var e={3282:function(e){function t(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function n(e,t){for(var n,r="",i=0,s=-1,a=0,o=0;o<=e.length;++o){if(o<e.length)n=e.charCodeAt(o);else{if(47===n)break;n=47}if(47===n){if(s===o-1||1===a);else if(s!==o-1&&2===a){if(r.length<2||2!==i||46!==r.charCodeAt(r.length-1)||46!==r.charCodeAt(r.length-2))if(r.length>2){var E=r.lastIndexOf("/");if(E!==r.length-1){-1===E?(r="",i=0):i=(r=r.slice(0,E)).length-1-r.lastIndexOf("/"),s=o,a=0;continue}}else if(2===r.length||1===r.length){r="",i=0,s=o,a=0;continue}t&&(r.length>0?r+="/..":r="..",i=2)}else r.length>0?r+="/"+e.slice(s+1,o):r=e.slice(s+1,o),i=o-s-1;s=o,a=0}else 46===n&&-1!==a?++a:a=-1}return r}var r={resolve:function(){for(var e,r="",i=!1,s=arguments.length-1;s>=-1&&!i;s--){var a;s>=0?a=arguments[s]:(void 0===e&&(e=process.cwd()),a=e),t(a),0!==a.length&&(r=a+"/"+r,i=47===a.charCodeAt(0))}return r=n(r,!i),i?r.length>0?"/"+r:"/":r.length>0?r:"."},normalize:function(e){if(t(e),0===e.length)return".";var r=47===e.charCodeAt(0),i=47===e.charCodeAt(e.length-1);return 0!==(e=n(e,!r)).length||r||(e="."),e.length>0&&i&&(e+="/"),r?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,n=0;n<arguments.length;++n){var i=arguments[n];t(i),i.length>0&&(void 0===e?e=i:e+="/"+i)}return void 0===e?".":r.normalize(e)},relative:function(e,n){if(t(e),t(n),e===n)return"";if((e=r.resolve(e))===(n=r.resolve(n)))return"";for(var i=1;i<e.length&&47===e.charCodeAt(i);++i);for(var s=e.length,a=s-i,o=1;o<n.length&&47===n.charCodeAt(o);++o);for(var E=n.length-o,l=a<E?a:E,u=-1,c=0;c<=l;++c){if(c===l){if(E>l){if(47===n.charCodeAt(o+c))return n.slice(o+c+1);if(0===c)return n.slice(o+c)}else a>l&&(47===e.charCodeAt(i+c)?u=c:0===c&&(u=0));break}var d=e.charCodeAt(i+c);if(d!==n.charCodeAt(o+c))break;47===d&&(u=c)}var _="";for(c=i+u+1;c<=s;++c)c!==s&&47!==e.charCodeAt(c)||(0===_.length?_+="..":_+="/..");return _.length>0?_+n.slice(o+u):(o+=u,47===n.charCodeAt(o)&&++o,n.slice(o))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var n=e.charCodeAt(0),r=47===n,i=-1,s=!0,a=e.length-1;a>=1;--a)if(47===(n=e.charCodeAt(a))){if(!s){i=a;break}}else s=!1;return-1===i?r?"/":".":r&&1===i?"//":e.slice(0,i)},basename:function(e,n){if(void 0!==n&&"string"!=typeof n)throw new TypeError('"ext" argument must be a string');t(e);var r,i=0,s=-1,a=!0;if(void 0!==n&&n.length>0&&n.length<=e.length){if(n.length===e.length&&n===e)return"";var o=n.length-1,E=-1;for(r=e.length-1;r>=0;--r){var l=e.charCodeAt(r);if(47===l){if(!a){i=r+1;break}}else-1===E&&(a=!1,E=r+1),o>=0&&(l===n.charCodeAt(o)?-1==--o&&(s=r):(o=-1,s=E))}return i===s?s=E:-1===s&&(s=e.length),e.slice(i,s)}for(r=e.length-1;r>=0;--r)if(47===e.charCodeAt(r)){if(!a){i=r+1;break}}else-1===s&&(a=!1,s=r+1);return-1===s?"":e.slice(i,s)},extname:function(e){t(e);for(var n=-1,r=0,i=-1,s=!0,a=0,o=e.length-1;o>=0;--o){var E=e.charCodeAt(o);if(47!==E)-1===i&&(s=!1,i=o+1),46===E?-1===n?n=o:1!==a&&(a=1):-1!==n&&(a=-1);else if(!s){r=o+1;break}}return-1===n||-1===i||0===a||1===a&&n===i-1&&n===r+1?"":e.slice(n,i)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var n=t.dir||t.root,r=t.base||(t.name||"")+(t.ext||"");return n?n===t.root?n+r:n+"/"+r:r}(0,e)},parse:function(e){t(e);var n={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return n;var r,i=e.charCodeAt(0),s=47===i;s?(n.root="/",r=1):r=0;for(var a=-1,o=0,E=-1,l=!0,u=e.length-1,c=0;u>=r;--u)if(47!==(i=e.charCodeAt(u)))-1===E&&(l=!1,E=u+1),46===i?-1===a?a=u:1!==c&&(c=1):-1!==a&&(c=-1);else if(!l){o=u+1;break}return-1===a||-1===E||0===c||1===c&&a===E-1&&a===o+1?-1!==E&&(n.base=n.name=0===o&&s?e.slice(1,E):e.slice(o,E)):(0===o&&s?(n.name=e.slice(1,a),n.base=e.slice(1,E)):(n.name=e.slice(o,a),n.base=e.slice(o,E)),n.ext=e.slice(a,E)),o>0?n.dir=e.slice(0,o-1):s&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};r.posix=r,e.exports=r},8571:function(e,t,n){var r;!function(i,s){var a="function",o="undefined",E="object",l="string",u="major",c="model",d="name",_="type",S="vendor",f="version",g="architecture",y="console",T="mobile",h="tablet",m="smarttv",R="wearable",I="embedded",p="Amazon",A="Apple",C="ASUS",D="BlackBerry",M="Browser",b="Chrome",w="Firefox",N="Google",O="Huawei",L="LG",v="Microsoft",P="Motorola",K="Opera",U="Samsung",k="Sharp",Y="Sony",x="Xiaomi",G="Zebra",H="Facebook",F="Chromium OS",B="Mac OS",V=function(e){for(var t={},n=0;n<e.length;n++)t[e[n].toUpperCase()]=e[n];return t},W=function(e,t){return typeof e===l&&-1!==q(t).indexOf(q(e))},q=function(e){return e.toLowerCase()},j=function(e,t){if(typeof e===l)return e=e.replace(/^\s\s*/,""),typeof t===o?e:e.substring(0,500)},z=function(e,t){for(var n,r,i,o,l,u,c=0;c<t.length&&!l;){var d=t[c],_=t[c+1];for(n=r=0;n<d.length&&!l&&d[n];)if(l=d[n++].exec(e))for(i=0;i<_.length;i++)u=l[++r],typeof(o=_[i])===E&&o.length>0?2===o.length?typeof o[1]==a?this[o[0]]=o[1].call(this,u):this[o[0]]=o[1]:3===o.length?typeof o[1]!==a||o[1].exec&&o[1].test?this[o[0]]=u?u.replace(o[1],o[2]):s:this[o[0]]=u?o[1].call(this,u,o[2]):s:4===o.length&&(this[o[0]]=u?o[3].call(this,u.replace(o[1],o[2])):s):this[o]=u||s;c+=2}},X=function(e,t){for(var n in t)if(typeof t[n]===E&&t[n].length>0){for(var r=0;r<t[n].length;r++)if(W(t[n][r],e))return"?"===n?s:n}else if(W(t[n],e))return"?"===n?s:n;return e},Q={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Z={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[f,[d,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[f,[d,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[d,f],[/opios[\/ ]+([\w\.]+)/i],[f,[d,K+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[f,[d,K+" GX"]],[/\bopr\/([\w\.]+)/i],[f,[d,K]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[f,[d,"Baidu"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim)\s?(?:browser)?[\/ ]?([\w\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[d,f],[/\bddg\/([\w\.]+)/i],[f,[d,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[f,[d,"UC"+M]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[f,[d,"WeChat"]],[/konqueror\/([\w\.]+)/i],[f,[d,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[f,[d,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[f,[d,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[f,[d,"Smart Lenovo "+M]],[/(avast|avg)\/([\w\.]+)/i],[[d,/(.+)/,"$1 Secure "+M],f],[/\bfocus\/([\w\.]+)/i],[f,[d,w+" Focus"]],[/\bopt\/([\w\.]+)/i],[f,[d,K+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[f,[d,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[f,[d,"Dolphin"]],[/coast\/([\w\.]+)/i],[f,[d,K+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[f,[d,"MIUI "+M]],[/fxios\/([-\w\.]+)/i],[f,[d,w]],[/\bqihu|(qi?ho?o?|360)browser/i],[[d,"360 "+M]],[/(oculus|sailfish|huawei|vivo)browser\/([\w\.]+)/i],[[d,/(.+)/,"$1 "+M],f],[/samsungbrowser\/([\w\.]+)/i],[f,[d,U+" Internet"]],[/(comodo_dragon)\/([\w\.]+)/i],[[d,/_/g," "],f],[/metasr[\/ ]?([\d\.]+)/i],[f,[d,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[d,"Sogou Mobile"],f],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345Explorer)[\/ ]?([\w\.]+)/i],[d,f],[/(lbbrowser)/i,/\[(linkedin)app\]/i],[d],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[d,H],f],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[d,f],[/\bgsa\/([\w\.]+) .*safari\//i],[f,[d,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[f,[d,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[f,[d,b+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[d,b+" WebView"],f],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[f,[d,"Android "+M]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[d,f],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[f,[d,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[f,d],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[d,[f,X,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[d,f],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[d,"Netscape"],f],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[f,[d,w+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[d,f],[/(cobalt)\/([\w\.]+)/i],[d,[f,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[g,"amd64"]],[/(ia32(?=;))/i],[[g,q]],[/((?:i[346]|x)86)[;\)]/i],[[g,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[g,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[g,"armhf"]],[/windows (ce|mobile); ppc;/i],[[g,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[g,/ower/,"",q]],[/(sun4\w)[;\)]/i],[[g,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[g,q]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[c,[S,U],[_,h]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[c,[S,U],[_,T]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[c,[S,A],[_,T]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[c,[S,A],[_,h]],[/(macintosh);/i],[c,[S,A]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[c,[S,k],[_,T]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[c,[S,O],[_,h]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[c,[S,O],[_,T]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[c,/_/g," "],[S,x],[_,T]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[c,/_/g," "],[S,x],[_,h]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[c,[S,"OPPO"],[_,T]],[/\b(opd2\d{3}a?) bui/i],[c,[S,"OPPO"],[_,h]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[c,[S,"Vivo"],[_,T]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[c,[S,"Realme"],[_,T]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[c,[S,P],[_,T]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[c,[S,P],[_,h]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[c,[S,L],[_,h]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[c,[S,L],[_,T]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[c,[S,"Lenovo"],[_,h]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[c,/_/g," "],[S,"Nokia"],[_,T]],[/(pixel c)\b/i],[c,[S,N],[_,h]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[c,[S,N],[_,T]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[c,[S,Y],[_,T]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[c,"Xperia Tablet"],[S,Y],[_,h]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[c,[S,"OnePlus"],[_,T]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[c,[S,p],[_,h]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[c,/(.+)/g,"Fire Phone $1"],[S,p],[_,T]],[/(playbook);[-\w\),; ]+(rim)/i],[c,S,[_,h]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[c,[S,D],[_,T]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[c,[S,C],[_,h]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[c,[S,C],[_,T]],[/(nexus 9)/i],[c,[S,"HTC"],[_,h]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[S,[c,/_/g," "],[_,T]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[c,[S,"Acer"],[_,h]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[c,[S,"Meizu"],[_,T]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[c,[S,"Ulefone"],[_,T]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[S,c,[_,T]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[S,c,[_,h]],[/(surface duo)/i],[c,[S,v],[_,h]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[c,[S,"Fairphone"],[_,T]],[/(u304aa)/i],[c,[S,"AT&T"],[_,T]],[/\bsie-(\w*)/i],[c,[S,"Siemens"],[_,T]],[/\b(rct\w+) b/i],[c,[S,"RCA"],[_,h]],[/\b(venue[\d ]{2,7}) b/i],[c,[S,"Dell"],[_,h]],[/\b(q(?:mv|ta)\w+) b/i],[c,[S,"Verizon"],[_,h]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[c,[S,"Barnes & Noble"],[_,h]],[/\b(tm\d{3}\w+) b/i],[c,[S,"NuVision"],[_,h]],[/\b(k88) b/i],[c,[S,"ZTE"],[_,h]],[/\b(nx\d{3}j) b/i],[c,[S,"ZTE"],[_,T]],[/\b(gen\d{3}) b.+49h/i],[c,[S,"Swiss"],[_,T]],[/\b(zur\d{3}) b/i],[c,[S,"Swiss"],[_,h]],[/\b((zeki)?tb.*\b) b/i],[c,[S,"Zeki"],[_,h]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[S,"Dragon Touch"],c,[_,h]],[/\b(ns-?\w{0,9}) b/i],[c,[S,"Insignia"],[_,h]],[/\b((nxa|next)-?\w{0,9}) b/i],[c,[S,"NextBook"],[_,h]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[S,"Voice"],c,[_,T]],[/\b(lvtel\-)?(v1[12]) b/i],[[S,"LvTel"],c,[_,T]],[/\b(ph-1) /i],[c,[S,"Essential"],[_,T]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[c,[S,"Envizen"],[_,h]],[/\b(trio[-\w\. ]+) b/i],[c,[S,"MachSpeed"],[_,h]],[/\btu_(1491) b/i],[c,[S,"Rotor"],[_,h]],[/(shield[\w ]+) b/i],[c,[S,"Nvidia"],[_,h]],[/(sprint) (\w+)/i],[S,c,[_,T]],[/(kin\.[onetw]{3})/i],[[c,/\./g," "],[S,v],[_,T]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[c,[S,G],[_,h]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[c,[S,G],[_,T]],[/smart-tv.+(samsung)/i],[S,[_,m]],[/hbbtv.+maple;(\d+)/i],[[c,/^/,"SmartTV"],[S,U],[_,m]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[S,L],[_,m]],[/(apple) ?tv/i],[S,[c,A+" TV"],[_,m]],[/crkey/i],[[c,b+"cast"],[S,N],[_,m]],[/droid.+aft(\w+)( bui|\))/i],[c,[S,p],[_,m]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[c,[S,k],[_,m]],[/(bravia[\w ]+)( bui|\))/i],[c,[S,Y],[_,m]],[/(mitv-\w{5}) bui/i],[c,[S,x],[_,m]],[/Hbbtv.*(technisat) (.*);/i],[S,c,[_,m]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[S,j],[c,j],[_,m]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[_,m]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[S,c,[_,y]],[/droid.+; (shield) bui/i],[c,[S,"Nvidia"],[_,y]],[/(playstation [345portablevi]+)/i],[c,[S,Y],[_,y]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[c,[S,v],[_,y]],[/((pebble))app/i],[S,c,[_,R]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[c,[S,A],[_,R]],[/droid.+; (glass) \d/i],[c,[S,N],[_,R]],[/droid.+; (wt63?0{2,3})\)/i],[c,[S,G],[_,R]],[/(quest( \d| pro)?)/i],[c,[S,H],[_,R]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[S,[_,I]],[/(aeobc)\b/i],[c,[S,p],[_,I]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[c,[_,T]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[c,[_,h]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[_,h]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[_,T]],[/(android[-\w\. ]{0,9});.+buil/i],[c,[S,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[f,[d,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[f,[d,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[d,f],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[f,d]],os:[[/microsoft (windows) (vista|xp)/i],[d,f],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[d,[f,X,Q]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[f,X,Q],[d,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[f,/_/g,"."],[d,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[d,B],[f,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[f,d],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[d,f],[/\(bb(10);/i],[f,[d,D]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[f,[d,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[f,[d,w+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[f,[d,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[f,[d,"watchOS"]],[/crkey\/([\d\.]+)/i],[f,[d,b+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[d,F],f],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[d,f],[/(sunos) ?([\w\.\d]*)/i],[[d,"Solaris"],f],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[d,f]]},$=function(e,t){if(typeof e===E&&(t=e,e=s),!(this instanceof $))return new $(e,t).getResult();var n=typeof i!==o&&i.navigator?i.navigator:s,r=e||(n&&n.userAgent?n.userAgent:""),y=n&&n.userAgentData?n.userAgentData:s,m=t?function(e,t){var n={};for(var r in e)t[r]&&t[r].length%2==0?n[r]=t[r].concat(e[r]):n[r]=e[r];return n}(Z,t):Z,R=n&&n.userAgent==r;return this.getBrowser=function(){var e,t={};return t[d]=s,t[f]=s,z.call(t,r,m.browser),t[u]=typeof(e=t[f])===l?e.replace(/[^\d\.]/g,"").split(".")[0]:s,R&&n&&n.brave&&typeof n.brave.isBrave==a&&(t[d]="Brave"),t},this.getCPU=function(){var e={};return e[g]=s,z.call(e,r,m.cpu),e},this.getDevice=function(){var e={};return e[S]=s,e[c]=s,e[_]=s,z.call(e,r,m.device),R&&!e[_]&&y&&y.mobile&&(e[_]=T),R&&"Macintosh"==e[c]&&n&&typeof n.standalone!==o&&n.maxTouchPoints&&n.maxTouchPoints>2&&(e[c]="iPad",e[_]=h),e},this.getEngine=function(){var e={};return e[d]=s,e[f]=s,z.call(e,r,m.engine),e},this.getOS=function(){var e={};return e[d]=s,e[f]=s,z.call(e,r,m.os),R&&!e[d]&&y&&y.platform&&"Unknown"!=y.platform&&(e[d]=y.platform.replace(/chrome os/i,F).replace(/macos/i,B)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return r},this.setUA=function(e){return r=typeof e===l&&e.length>500?j(e,500):e,this},this.setUA(r),this};$.VERSION="1.0.38",$.BROWSER=V([d,f,u]),$.CPU=V([g]),$.DEVICE=V([c,S,_,y,T,m,h,R,I]),$.ENGINE=$.OS=V([d,f]),typeof t!==o?(e.exports&&(t=e.exports=$),t.UAParser=$):n.amdO?(r=function(){return $}.call(t,n,t,e))===s||(e.exports=r):typeof i!==o&&(i.UAParser=$);var J=typeof i!==o&&(i.jQuery||i.Zepto);if(J&&!J.ua){var ee=new $;J.ua=ee.getResult(),J.ua.get=function(){return ee.getUA()},J.ua.set=function(e){ee.setUA(e);var t=ee.getResult();for(var n in t)J.ua[n]=t[n]}}}("object"==typeof window?window:this)},138:function(e,t,n){n.r(t);const r=function(){let e,t=[];const n={},r={};function i(e,n){for(const r in t){const i=t[r];if(i.context===e&&i.name===n)return i.instance}return null}function s(e,t){return t[e]}function a(e,t,n){e in n&&(n[e]=t)}function o(t,n,r){let i;const s=t.__dashjs_factory_name,a=n[s];if(a){let s=a.instance;if(!a.override)return s.apply({context:n,factory:e},r);i=t.apply({context:n},r),s=s.apply({context:n,factory:e,parent:i},r);for(const e in s)i.hasOwnProperty(e)&&(i[e]=s[e])}else i=t.apply({context:n},r);return i.getClassName=function(){return s},i}return e={extend:function(e,t,n,r){!r[e]&&t&&(r[e]={instance:t,override:n})},getSingletonInstance:i,setSingletonInstance:function(e,n,r){for(const i in t){const s=t[i];if(s.context===e&&s.name===n)return void(t[i].instance=r)}t.push({name:n,context:e,instance:r})},deleteSingletonInstances:function(e){t=t.filter((t=>t.context!==e))},getSingletonFactory:function(e){let r=s(e.__dashjs_factory_name,n);return r||(r=function(n){let r;return void 0===n&&(n={}),{getInstance:function(){return r||(r=i(n,e.__dashjs_factory_name)),r||(r=o(e,n,arguments),t.push({name:e.__dashjs_factory_name,context:n,instance:r})),r}}},n[e.__dashjs_factory_name]=r),r},getSingletonFactoryByName:function(e){return s(e,n)},updateSingletonFactory:function(e,t){a(e,t,n)},getClassFactory:function(e){let t=s(e.__dashjs_factory_name,r);return t||(t=function(t){return void 0===t&&(t={}),{create:function(){return o(e,t,arguments)}}},r[e.__dashjs_factory_name]=t),t},getClassFactoryByName:function(e){return s(e,r)},updateClassFactory:function(e,t){a(e,t,r)}},e}();t.default=r},7263:function(e,t,n){n.r(t);var r=n(3282),i=n(8571),s=n(5212);class a{static mixin(e,t,n){let r,i={};if(e)for(let s in t)t.hasOwnProperty(s)&&(r=t[s],s in e&&(e[s]===r||s in i&&i[s]===r)||("object"==typeof e[s]&&null!==e[s]?e[s]=a.mixin(e[s],r,n):e[s]=n(r)));return e}static clone(e){if(!e||"object"!=typeof e)return e;if(e instanceof RegExp)return new RegExp(e);let t;if(e instanceof Array){t=[];for(let n=0,r=e.length;n<r;++n)n in e&&t.push(a.clone(e[n]))}else t={};return a.mixin(t,e,a.clone)}static addAdditionalQueryParameterToUrl(e,t){try{if(!t||0===t.length)return e;let n=e;return t.forEach((e=>{let{key:t,value:r}=e;const i=n.includes("?")?"&":"?";n+=`${i}${encodeURIComponent(t)}=${encodeURIComponent(r)}`})),n}catch(t){return e}}static removeQueryParameterFromUrl(e,t){if(!e||!t)return e;const n=new URL(e),r=new URLSearchParams(n.search);if(!r||0===r.size||!r.has(t))return e;r.delete(t);const i=Array.from(r.entries()).map((e=>{let[t,n]=e;return`${t}=${n}`})).join("&"),s=`${n.origin}${n.pathname}`;return i?`${s}?${i}`:s}static parseHttpHeaders(e){let t={};if(!e)return t;let n=e.trim().split("\r\n");for(let e=0,r=n.length;e<r;e++){let r=n[e],i=r.indexOf(": ");i>0&&(t[r.substring(0,i)]=r.substring(i+2))}return t}static parseQueryParams(e){const t=[],n=new URLSearchParams(e);for(const[e,r]of n.entries())t.push({key:decodeURIComponent(e),value:decodeURIComponent(r)});return t}static generateUuid(){let e=(new Date).getTime();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){const n=(e+16*Math.random())%16|0;return e=Math.floor(e/16),("x"==t?n:3&n|8).toString(16)}))}static generateHashCode(e){let t=0;if(0===e.length)return t;for(let n=0;n<e.length;n++)t=(t<<5)-t+e.charCodeAt(n),t|=0;return t}static getRelativeUrl(e,t){try{const n=new URL(e),i=new URL(t);if(n.protocol=i.protocol,n.origin!==i.origin)return t;let s=r.relative(n.pathname.substr(0,n.pathname.lastIndexOf("/")),i.pathname.substr(0,i.pathname.lastIndexOf("/")));const a=0===s.length?1:0;return s+=i.pathname.substr(i.pathname.lastIndexOf("/")+a,i.pathname.length-1),i.pathname.length<s.length?i.pathname:s}catch(e){return t}}static getHostFromUrl(e){try{return new URL(e).host}catch(e){return null}}static parseUserAgent(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;try{const t=null===e&&"undefined"!=typeof navigator?navigator.userAgent.toLowerCase():"";return(0,i.UAParser)(t)}catch(e){return{}}}static stringHasProtocol(e){return/(http(s?)):\/\//i.test(e)}static bufferSourceToDataView(e){return a.toDataView(e,DataView)}static bufferSourceToInt8(e){return a.toDataView(e,Uint8Array)}static uint8ArrayToString(e){return new TextDecoder("utf-8").decode(e)}static bufferSourceToHex(e){const t=a.bufferSourceToInt8(e);let n="";for(let e of t)e=e.toString(16),1===e.length&&(e="0"+e),n+=e;return n}static toDataView(e,t){const n=a.getArrayBuffer(e);let r=1;"BYTES_PER_ELEMENT"in DataView&&(r=DataView.BYTES_PER_ELEMENT);const i=((e.byteOffset||0)+e.byteLength)/r,s=(e.byteOffset||0)/r,o=Math.floor(Math.max(0,Math.min(s,i)));return new t(n,o,Math.floor(Math.min(o+Math.max(1/0,0),i))-o)}static getArrayBuffer(e){return e instanceof ArrayBuffer?e:e.buffer}static getCodecFamily(e){const{base:t,profile:n}=a._getCodecParts(e);switch(t){case"mp4a":switch(n){case"69":case"6b":case"40.34":return s.default.CODEC_FAMILIES.MP3;case"66":case"67":case"68":case"40.2":case"40.02":case"40.5":case"40.05":case"40.29":case"40.42":return s.default.CODEC_FAMILIES.AAC;case"a5":return s.default.CODEC_FAMILIES.AC3;case"e6":return s.default.CODEC_FAMILIES.EC3;case"b2":return s.default.CODEC_FAMILIES.DTSX;case"a9":return s.default.CODEC_FAMILIES.DTSC}break;case"avc1":case"avc3":return s.default.CODEC_FAMILIES.AVC;case"hvc1":case"hvc3":return s.default.CODEC_FAMILIES.HEVC;default:return t}return t}static _getCodecParts(e){const[t,...n]=e.split(".");return{base:t,profile:n.join(".")}}}t.default=a},8748:function(e,t,n){n.r(t),t.default=class{extend(e,t){if(!e)return;let n=!!t&&t.override,r=!!t&&t.publicOnly;for(const t in e)!e.hasOwnProperty(t)||this[t]&&!n||r&&-1===e[t].indexOf("public_")||(this[t]=e[t])}}},7252:function(e,t,n){n.r(t),t.default=class{extend(e,t){if(!e)return;let n=!!t&&t.override,r=!!t&&t.publicOnly;for(const t in e)!e.hasOwnProperty(t)||this[t]&&!n||r&&-1===e[t].indexOf("public_")||(this[t]=e[t])}}},8854:function(e,t,n){n.r(t),t.default={ACCESSIBILITY:"Accessibility",ADAPTATION_SET:"AdaptationSet",ADAPTATION_SETS:"adaptationSets",ADAPTATION_SET_SWITCHING_SCHEME_ID_URI:"urn:mpeg:dash:adaptation-set-switching:2016",ADD:"add",ASSET_IDENTIFIER:"AssetIdentifier",AUDIO_CHANNEL_CONFIGURATION:"AudioChannelConfiguration",AUDIO_SAMPLING_RATE:"audioSamplingRate",AVAILABILITY_END_TIME:"availabilityEndTime",AVAILABILITY_START_TIME:"availabilityStartTime",AVAILABILITY_TIME_COMPLETE:"availabilityTimeComplete",AVAILABILITY_TIME_OFFSET:"availabilityTimeOffset",BANDWITH:"bandwidth",BASE_URL:"BaseURL",BITSTREAM_SWITCHING:"BitstreamSwitching",BITSTREAM_SWITCHING_MINUS:"bitstreamSwitching",BYTE_RANGE:"byteRange",CAPTION:"caption",CENC_DEFAULT_KID:"cenc:default_KID",CLIENT_DATA_REPORTING:"ClientDataReporting",CLIENT_REQUIREMENT:"clientRequirement",CMCD_PARAMETERS:"CMCDParameters",CODECS:"codecs",CODEC_PRIVATE_DATA:"codecPrivateData",CODING_DEPENDENCY:"codingDependency",CONTENT_COMPONENT:"ContentComponent",CONTENT_PROTECTION:"ContentProtection",CONTENT_STEERING:"ContentSteering",CONTENT_STEERING_RESPONSE:{VERSION:"VERSION",TTL:"TTL",RELOAD_URI:"RELOAD-URI",PATHWAY_PRIORITY:"PATHWAY-PRIORITY",PATHWAY_CLONES:"PATHWAY-CLONES",BASE_ID:"BASE-ID",ID:"ID",URI_REPLACEMENT:"URI-REPLACEMENT",HOST:"HOST",PARAMS:"PARAMS"},CONTENT_TYPE:"contentType",DEFAULT_SERVICE_LOCATION:"defaultServiceLocation",DEPENDENCY_ID:"dependencyId",DURATION:"duration",DVB_PRIORITY:"dvb:priority",DVB_WEIGHT:"dvb:weight",DVB_URL:"dvb:url",DVB_MIMETYPE:"dvb:mimeType",DVB_FONTFAMILY:"dvb:fontFamily",DYNAMIC:"dynamic",END_NUMBER:"endNumber",ESSENTIAL_PROPERTY:"EssentialProperty",EVENT:"Event",EVENT_STREAM:"EventStream",FORCED_SUBTITLE:"forced-subtitle",FRAMERATE:"frameRate",FRAME_PACKING:"FramePacking",GROUP_LABEL:"GroupLabel",HEIGHT:"height",ID:"id",INBAND:"inband",INBAND_EVENT_STREAM:"InbandEventStream",INDEX:"index",INDEX_RANGE:"indexRange",INITIALIZATION:"Initialization",INITIALIZATION_MINUS:"initialization",LA_URL:"Laurl",LA_URL_LOWER_CASE:"laurl",LABEL:"Label",LANG:"lang",LOCATION:"Location",MAIN:"main",MAXIMUM_SAP_PERIOD:"maximumSAPPeriod",MAX_PLAYOUT_RATE:"maxPlayoutRate",MAX_SEGMENT_DURATION:"maxSegmentDuration",MAX_SUBSEGMENT_DURATION:"maxSubsegmentDuration",MEDIA:"media",MEDIA_PRESENTATION_DURATION:"mediaPresentationDuration",MEDIA_RANGE:"mediaRange",MEDIA_STREAM_STRUCTURE_ID:"mediaStreamStructureId",METRICS:"Metrics",METRICS_MINUS:"metrics",MIME_TYPE:"mimeType",MINIMUM_UPDATE_PERIOD:"minimumUpdatePeriod",MIN_BUFFER_TIME:"minBufferTime",MP4_PROTECTION_SCHEME:"urn:mpeg:dash:mp4protection:2011",MPD:"MPD",MPD_TYPE:"mpd",MPD_PATCH_TYPE:"mpdpatch",ORIGINAL_MPD_ID:"mpdId",ORIGINAL_PUBLISH_TIME:"originalPublishTime",PATCH_LOCATION:"PatchLocation",PERIOD:"Period",PRESENTATION_TIME:"presentationTime",PRESENTATION_TIME_OFFSET:"presentationTimeOffset",PRO:"pro",PRODUCER_REFERENCE_TIME:"ProducerReferenceTime",PRODUCER_REFERENCE_TIME_TYPE:{ENCODER:"encoder",CAPTURED:"captured",APPLICATION:"application"},PROFILES:"profiles",PSSH:"pssh",PUBLISH_TIME:"publishTime",QUALITY_RANKING:"qualityRanking",QUERY_BEFORE_START:"queryBeforeStart",QUERY_PART:"$querypart$",RANGE:"range",RATING:"Rating",REF:"ref",REF_ID:"refId",REMOVE:"remove",REPLACE:"replace",REPORTING:"Reporting",REPRESENTATION:"Representation",REPRESENTATION_INDEX:"RepresentationIndex",ROBUSTNESS:"robustness",ROLE:"Role",S:"S",SAR:"sar",SCAN_TYPE:"scanType",SEGMENT_ALIGNMENT:"segmentAlignment",SEGMENT_BASE:"SegmentBase",SEGMENT_LIST:"SegmentList",SEGMENT_PROFILES:"segmentProfiles",SEGMENT_TEMPLATE:"SegmentTemplate",SEGMENT_TIMELINE:"SegmentTimeline",SEGMENT_TYPE:"segment",SEGMENT_URL:"SegmentURL",SERVICE_DESCRIPTION:"ServiceDescription",SERVICE_DESCRIPTION_LATENCY:"Latency",SERVICE_DESCRIPTION_OPERATING_BANDWIDTH:"OperatingBandwidth",SERVICE_DESCRIPTION_OPERATING_QUALITY:"OperatingQuality",SERVICE_DESCRIPTION_PLAYBACK_RATE:"PlaybackRate",SERVICE_DESCRIPTION_SCOPE:"Scope",SERVICE_LOCATION:"serviceLocation",SERVICE_LOCATIONS:"serviceLocations",SOURCE_URL:"sourceURL",START:"start",START_NUMBER:"startNumber",START_WITH_SAP:"startWithSAP",STATIC:"static",STEERING_TYPE:"steering",SUBSET:"Subset",SUBTITLE:"subtitle",SUB_REPRESENTATION:"SubRepresentation",SUB_SEGMENT_ALIGNMENT:"subsegmentAlignment",SUGGESTED_PRESENTATION_DELAY:"suggestedPresentationDelay",SUPPLEMENTAL_PROPERTY:"SupplementalProperty",SUPPLEMENTAL_CODECS:"scte214:supplementalCodecs",TIMESCALE:"timescale",TIMESHIFT_BUFFER_DEPTH:"timeShiftBufferDepth",TTL:"ttl",TYPE:"type",UTC_TIMING:"UTCTiming",VALUE:"value",VIEWPOINT:"Viewpoint",WALL_CLOCK_TIME:"wallClockTime",WIDTH:"width"}},5212:function(e,t,n){n.r(t),t.default={STREAM:"stream",VIDEO:"video",AUDIO:"audio",TEXT:"text",MUXED:"muxed",IMAGE:"image",STPP:"stpp",TTML:"ttml",VTT:"vtt",WVTT:"wvtt",CONTENT_STEERING:"contentSteering",LIVE_CATCHUP_MODE_DEFAULT:"liveCatchupModeDefault",LIVE_CATCHUP_MODE_LOLP:"liveCatchupModeLoLP",MOVING_AVERAGE_SLIDING_WINDOW:"slidingWindow",MOVING_AVERAGE_EWMA:"ewma",BAD_ARGUMENT_ERROR:"Invalid Arguments",MISSING_CONFIG_ERROR:"Missing config parameter(s)",TRACK_SWITCH_MODE_ALWAYS_REPLACE:"alwaysReplace",TRACK_SWITCH_MODE_NEVER_REPLACE:"neverReplace",TRACK_SELECTION_MODE_FIRST_TRACK:"firstTrack",TRACK_SELECTION_MODE_HIGHEST_BITRATE:"highestBitrate",TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY:"highestEfficiency",TRACK_SELECTION_MODE_WIDEST_RANGE:"widestRange",CMCD_QUERY_KEY:"CMCD",CMCD_MODE_QUERY:"query",CMCD_MODE_HEADER:"header",CMCD_AVAILABLE_KEYS:["br","d","ot","tb","bl","dl","mtp","nor","nrr","su","bs","rtp","cid","pr","sf","sid","st","v"],CMCD_V2_AVAILABLE_KEYS:["msd","ltc"],CMCD_AVAILABLE_REQUESTS:["segment","mpd","xlink","steering","other"],INITIALIZE:"initialize",TEXT_SHOWING:"showing",TEXT_HIDDEN:"hidden",TEXT_DISABLED:"disabled",ACCESSIBILITY_CEA608_SCHEME:"urn:scte:dash:cc:cea-608:2015",CC1:"CC1",CC3:"CC3",UTF8:"utf-8",SCHEME_ID_URI:"schemeIdUri",START_TIME:"starttime",SERVICE_DESCRIPTION_DVB_LL_SCHEME:"urn:dvb:dash:lowlatency:scope:2019",SUPPLEMENTAL_PROPERTY_DVB_LL_SCHEME:"urn:dvb:dash:lowlatency:critical:2019",CTA_5004_2023_SCHEME:"urn:mpeg:dash:cta-5004:2023",THUMBNAILS_SCHEME_ID_URIS:["http://dashif.org/thumbnail_tile","http://dashif.org/guidelines/thumbnail_tile"],FONT_DOWNLOAD_DVB_SCHEME:"urn:dvb:dash:fontdownload:2014",COLOUR_PRIMARIES_SCHEME_ID_URI:"urn:mpeg:mpegB:cicp:ColourPrimaries",URL_QUERY_INFO_SCHEME:"urn:mpeg:dash:urlparam:2014",EXT_URL_QUERY_INFO_SCHEME:"urn:mpeg:dash:urlparam:2016",MATRIX_COEFFICIENTS_SCHEME_ID_URI:"urn:mpeg:mpegB:cicp:MatrixCoefficients",TRANSFER_CHARACTERISTICS_SCHEME_ID_URI:"urn:mpeg:mpegB:cicp:TransferCharacteristics",HDR_METADATA_FORMAT_SCHEME_ID_URI:"urn:dvb:dash:hdr-dmi",HDR_METADATA_FORMAT_VALUES:{ST2094_10:"ST2094-10",SL_HDR2:"SL-HDR2",ST2094_40:"ST2094-40"},MEDIA_CAPABILITIES_API:{COLORGAMUT:{SRGB:"srgb",P3:"p3",REC2020:"rec2020"},TRANSFERFUNCTION:{SRGB:"srgb",PQ:"pq",HLG:"hlg"},HDR_METADATATYPE:{SMPTE_ST_2094_10:"smpteSt2094-10",SLHDR2:"slhdr2",SMPTE_ST_2094_40:"smpteSt2094-40"}},XML:"XML",ARRAY_BUFFER:"ArrayBuffer",DVB_REPORTING_URL:"dvb:reportingUrl",DVB_PROBABILITY:"dvb:probability",OFF_MIMETYPE:"application/font-sfnt",WOFF_MIMETYPE:"application/font-woff",VIDEO_ELEMENT_READY_STATES:{HAVE_NOTHING:0,HAVE_METADATA:1,HAVE_CURRENT_DATA:2,HAVE_FUTURE_DATA:3,HAVE_ENOUGH_DATA:4},FILE_LOADER_TYPES:{FETCH:"fetch_loader",XHR:"xhr_loader"},THROUGHPUT_TYPES:{LATENCY:"throughput_type_latency",BANDWIDTH:"throughput_type_bandwidth"},THROUGHPUT_CALCULATION_MODES:{EWMA:"throughputCalculationModeEwma",ZLEMA:"throughputCalculationModeZlema",ARITHMETIC_MEAN:"throughputCalculationModeArithmeticMean",BYTE_SIZE_WEIGHTED_ARITHMETIC_MEAN:"throughputCalculationModeByteSizeWeightedArithmeticMean",DATE_WEIGHTED_ARITHMETIC_MEAN:"throughputCalculationModeDateWeightedArithmeticMean",HARMONIC_MEAN:"throughputCalculationModeHarmonicMean",BYTE_SIZE_WEIGHTED_HARMONIC_MEAN:"throughputCalculationModeByteSizeWeightedHarmonicMean",DATE_WEIGHTED_HARMONIC_MEAN:"throughputCalculationModeDateWeightedHarmonicMean"},LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE:{MOOF_PARSING:"lowLatencyDownloadTimeCalculationModeMoofParsing",DOWNLOADED_DATA:"lowLatencyDownloadTimeCalculationModeDownloadedData",AAST:"lowLatencyDownloadTimeCalculationModeAast"},RULES_TYPES:{QUALITY_SWITCH_RULES:"qualitySwitchRules",ABANDON_FRAGMENT_RULES:"abandonFragmentRules"},QUALITY_SWITCH_RULES:{BOLA_RULE:"BolaRule",THROUGHPUT_RULE:"ThroughputRule",INSUFFICIENT_BUFFER_RULE:"InsufficientBufferRule",SWITCH_HISTORY_RULE:"SwitchHistoryRule",DROPPED_FRAMES_RULE:"DroppedFramesRule",LEARN_TO_ADAPT_RULE:"L2ARule",LOL_PLUS_RULE:"LoLPRule"},ABANDON_FRAGMENT_RULES:{ABANDON_REQUEST_RULE:"AbandonRequestsRule"},ID3_SCHEME_ID_URI:"https://aomedia.org/emsg/ID3",COMMON_ACCESS_TOKEN_HEADER:"common-access-token",DASH_ROLE_SCHEME_ID:"urn:mpeg:dash:role:2011",CODEC_FAMILIES:{MP3:"mp3",AAC:"aac",AC3:"ac3",EC3:"ec3",DTSX:"dtsx",DTSC:"dtsc",AVC:"avc",HEVC:"hevc"}}},2861:function(e,t,n){n.r(t),t.default={CLEARKEY_KEYSTEM_STRING:"org.w3.clearkey",WIDEVINE_KEYSTEM_STRING:"com.widevine.alpha",PLAYREADY_KEYSTEM_STRING:"com.microsoft.playready",PLAYREADY_RECOMMENDATION_KEYSTEM_STRING:"com.microsoft.playready.recommendation",WIDEVINE_UUID:"edef8ba9-79d6-4ace-a3c8-27dcd51d21ed",PLAYREADY_UUID:"9a04f079-9840-4286-ab92-e65be0885f95",CLEARKEY_UUID:"e2719d58-a985-b3c9-781a-b030af78d30e",W3C_CLEARKEY_UUID:"1077efec-c0b2-4d02-ace3-3c1e52e2fb4b",INITIALIZATION_DATA_TYPE_CENC:"cenc",INITIALIZATION_DATA_TYPE_KEYIDS:"keyids",INITIALIZATION_DATA_TYPE_WEBM:"webm",ENCRYPTION_SCHEME_CENC:"cenc",ENCRYPTION_SCHEME_CBCS:"cbcs",MEDIA_KEY_MESSAGE_TYPES:{LICENSE_REQUEST:"license-request",LICENSE_RENEWAL:"license-renewal",LICENSE_RELEASE:"license-release",INDIVIDUALIZATION_REQUEST:"individualization-request"},ROBUSTNESS_STRINGS:{WIDEVINE:{SW_SECURE_CRYPTO:"SW_SECURE_CRYPTO",SW_SECURE_DECODE:"SW_SECURE_DECODE",HW_SECURE_CRYPTO:"HW_SECURE_CRYPTO",HW_SECURE_DECODE:"HW_SECURE_DECODE",HW_SECURE_ALL:"HW_SECURE_ALL"}},MEDIA_KEY_STATUSES:{USABLE:"usable",EXPIRED:"expired",RELEASED:"released",OUTPUT_RESTRICTED:"output-restricted",OUTPUT_DOWNSCALED:"output-downscaled",STATUS_PENDING:"status-pending",INTERNAL_ERROR:"internal-error"}}},445:function(e,t,n){n.r(t);var r=n(7252);class i extends r.default{constructor(){super(),this.INTERNAL_KEY_MESSAGE="internalKeyMessage",this.INTERNAL_KEY_STATUSES_CHANGED="internalkeyStatusesChanged",this.KEY_ADDED="public_keyAdded",this.KEY_ERROR="public_keyError",this.KEY_MESSAGE="public_keyMessage",this.KEY_SESSION_CLOSED="public_keySessionClosed",this.KEY_SESSION_CREATED="public_keySessionCreated",this.KEY_SESSION_REMOVED="public_keySessionRemoved",this.KEY_STATUSES_CHANGED="public_keyStatusesChanged",this.KEY_STATUSES_MAP_UPDATED="keyStatusesMapUpdated",this.KEY_SYSTEM_ACCESS_COMPLETE="public_keySystemAccessComplete",this.KEY_SYSTEM_SELECTED="public_keySystemSelected",this.LICENSE_REQUEST_COMPLETE="public_licenseRequestComplete",this.LICENSE_REQUEST_SENDING="public_licenseRequestSending",this.NEED_KEY="needkey",this.PROTECTION_CREATED="public_protectioncreated",this.PROTECTION_DESTROYED="public_protectiondestroyed",this.SERVER_CERTIFICATE_UPDATED="serverCertificateUpdated",this.TEARDOWN_COMPLETE="protectionTeardownComplete",this.VIDEO_ELEMENT_SELECTED="videoElementSelected",this.KEY_SESSION_UPDATED="public_keySessionUpdated"}}let s=new i;t.default=s},1923:function(e,t,n){n.r(t);var r=n(8748);class i extends r.default{constructor(){super(),this.MEDIA_KEYERR_CODE=100,this.MEDIA_KEYERR_UNKNOWN_CODE=101,this.MEDIA_KEYERR_CLIENT_CODE=102,this.MEDIA_KEYERR_SERVICE_CODE=103,this.MEDIA_KEYERR_OUTPUT_CODE=104,this.MEDIA_KEYERR_HARDWARECHANGE_CODE=105,this.MEDIA_KEYERR_DOMAIN_CODE=106,this.MEDIA_KEY_MESSAGE_ERROR_CODE=107,this.MEDIA_KEY_MESSAGE_NO_CHALLENGE_ERROR_CODE=108,this.SERVER_CERTIFICATE_UPDATED_ERROR_CODE=109,this.KEY_STATUS_CHANGED_EXPIRED_ERROR_CODE=110,this.MEDIA_KEY_MESSAGE_NO_LICENSE_SERVER_URL_ERROR_CODE=111,this.KEY_SYSTEM_ACCESS_DENIED_ERROR_CODE=112,this.KEY_SESSION_CREATED_ERROR_CODE=113,this.MEDIA_KEY_MESSAGE_LICENSER_ERROR_CODE=114,this.MEDIA_KEYERR_UNKNOWN_MESSAGE="An unspecified error occurred. This value is used for errors that don't match any of the other codes.",this.MEDIA_KEYERR_CLIENT_MESSAGE="The Key System could not be installed or updated.",this.MEDIA_KEYERR_SERVICE_MESSAGE="The message passed into update indicated an error from the license service.",this.MEDIA_KEYERR_OUTPUT_MESSAGE="There is no available output device with the required characteristics for the content protection system.",this.MEDIA_KEYERR_HARDWARECHANGE_MESSAGE="A hardware configuration change caused a content protection error.",this.MEDIA_KEYERR_DOMAIN_MESSAGE="An error occurred in a multi-device domain licensing configuration. The most common error is a failure to join the domain.",this.MEDIA_KEY_MESSAGE_ERROR_MESSAGE="Multiple key sessions were creates with a user-agent that does not support sessionIDs!! Unpredictable behavior ahead!",this.MEDIA_KEY_MESSAGE_NO_CHALLENGE_ERROR_MESSAGE="DRM: Empty key message from CDM",this.SERVER_CERTIFICATE_UPDATED_ERROR_MESSAGE="Error updating server certificate -- ",this.KEY_STATUS_CHANGED_EXPIRED_ERROR_MESSAGE="DRM: KeyStatusChange error! -- License has expired",this.MEDIA_KEY_MESSAGE_NO_LICENSE_SERVER_URL_ERROR_MESSAGE="DRM: No license server URL specified!",this.KEY_SYSTEM_ACCESS_DENIED_ERROR_MESSAGE="DRM: KeySystem Access Denied! -- ",this.KEY_SESSION_CREATED_ERROR_MESSAGE="DRM: unable to create session! --",this.MEDIA_KEY_MESSAGE_LICENSER_ERROR_MESSAGE="DRM: licenser error! --"}}let s=new i;t.default=s},1944:function(e,t,n){n.r(t),t.default=class{constructor(e,t,n){this.code=e||null,this.message=t||null,this.data=n||null}}},7568:function(e,t,n){n.r(t),n.d(t,{HTTPRequest:function(){return r},HTTPRequestTrace:function(){return i}});class r{constructor(){this.tcpid=null,this.type=null,this.url=null,this.actualurl=null,this.range=null,this.trequest=null,this.tresponse=null,this.responsecode=null,this.interval=null,this.trace=[],this.cmsd=null,this._stream=null,this._tfinish=null,this._mediaduration=null,this._responseHeaders=null,this._serviceLocation=null,this._fileLoaderType=null,this._resourceTimingValues=null}}class i{constructor(){this.s=null,this.d=null,this.b=[]}}r.GET="GET",r.HEAD="HEAD",r.MPD_TYPE="MPD",r.XLINK_EXPANSION_TYPE="XLinkExpansion",r.INIT_SEGMENT_TYPE="InitializationSegment",r.INDEX_SEGMENT_TYPE="IndexSegment",r.MEDIA_SEGMENT_TYPE="MediaSegment",r.BITSTREAM_SWITCHING_SEGMENT_TYPE="BitstreamSwitchingSegment",r.MSS_FRAGMENT_INFO_SEGMENT_TYPE="FragmentInfoSegment",r.DVB_REPORTING_TYPE="DVBReporting",r.LICENSE="license",r.CONTENT_STEERING_TYPE="ContentSteering",r.OTHER_TYPE="other"}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var s=t[r]={exports:{}};return e[r].call(s.exports,s,s.exports,n),s.exports}n.amdO={},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};n.r(r),n.d(r,{default:function(){return fe}});var i=n(8854),s=n(2861);const a={prefixes:["clearkey","dashif","ck"]};class o{static findMp4ProtectionElement(e){let t=null;for(let n=0;n<e.length;++n){let r=e[n];r.schemeIdUri&&r.schemeIdUri.toLowerCase()===i.default.MP4_PROTECTION_SCHEME&&r.value&&(r.value.toLowerCase()===s.default.ENCRYPTION_SCHEME_CENC||r.value.toLowerCase()===s.default.ENCRYPTION_SCHEME_CBCS)&&(t=r)}return t}static getPSSHData(e){let t=8,n=new DataView(e),r=n.getUint8(t);return t+=20,r>0&&(t+=4+16*n.getUint32(t)),t+=4,e.slice(t)}static getPSSHForKeySystem(e,t){let n=o.parsePSSHList(t);return e&&n.hasOwnProperty(e.uuid.toLowerCase())?n[e.uuid.toLowerCase()]:null}static parseInitDataFromContentProtection(e,t){return"pssh"in e&&e.pssh?(e.pssh.__text=e.pssh.__text.replace(/\r?\n|\r/g,"").replace(/\s+/g,""),t.decodeArray(e.pssh.__text).buffer):null}static parsePSSHList(e){if(null==e)return[];let t=new DataView(e.buffer||e),n={},r=0;for(;;){let e,i,s,a,o,E,l=r;if(r>=t.buffer.byteLength)break;if(e=t.getUint32(r),i=r+e,r+=4,1886614376===t.getUint32(r))if(r+=4,s=t.getUint8(r),0===s||1===s){for(r++,r+=3,a="",o=0;o<4;o++)E=t.getUint8(r+o).toString(16),a+=1===E.length?"0"+E:E;for(r+=4,a+="-",o=0;o<2;o++)E=t.getUint8(r+o).toString(16),a+=1===E.length?"0"+E:E;for(r+=2,a+="-",o=0;o<2;o++)E=t.getUint8(r+o).toString(16),a+=1===E.length?"0"+E:E;for(r+=2,a+="-",o=0;o<2;o++)E=t.getUint8(r+o).toString(16),a+=1===E.length?"0"+E:E;for(r+=2,a+="-",o=0;o<6;o++)E=t.getUint8(r+o).toString(16),a+=1===E.length?"0"+E:E;r+=6,a=a.toLowerCase(),r+=4,n[a]=t.buffer.slice(l,i),r=i}else r=i;else r=i}return n}static getLicenseServerUrlFromMediaInfo(e,t){try{if(!e||0===e.length)return null;let n=0,r=null;for(;n<e.length&&!r;){const i=e[n];if(i&&i.contentProtection&&i.contentProtection.length>0){const e=i.contentProtection.filter((e=>e.schemeIdUri&&e.schemeIdUri===t));if(e&&e.length>0){let t=0;for(;t<e.length&&!r;){const n=e[t];n.laUrl&&n.laUrl.__prefix&&a.prefixes.includes(n.laUrl.__prefix)&&n.laUrl.__text&&(r=n.laUrl.__text),t+=1}}}n+=1}return r}catch(e){return null}}static hexKidToBufferSource(e){const t=e.replace(/-/g,"");return new Uint8Array(t.match(/[\da-f]{2}/gi).map((function(e){return parseInt(e,16)}))).buffer}}var E=o,l=class{constructor(e,t){this.contentType=e,this.robustness=t}},u=class{constructor(e,t,n,r,i,a){this.initDataTypes=a&&a.length>0?a:[s.default.INITIALIZATION_DATA_TYPE_CENC],e&&e.length&&(this.audioCapabilities=e),t&&t.length&&(this.videoCapabilities=t),this.distinctiveIdentifier=n,this.persistentState=r,this.sessionTypes=i}},c=n(1923),d=n(1944),_=class{constructor(e,t,n,r,i,s,a,o){this.url=e,this.method=t,this.responseType=n,this.headers=r,this.withCredentials=i,this.messageType=s,this.sessionId=a,this.data=o}},S=class{constructor(e,t,n){this.url=e,this.headers=t,this.data=n}},f=n(7568),g=n(7263),y=n(5212),T=n(138);function h(e){const t=(e=e||{}).BASE64,n=e.cmcdModel,r=e.constants,i=e.customParametersModel,a=e.debug,o=e.eventBus,T=e.events,h=e.protectionKeyController,m=e.settings;let R,I,p,A,C,D,M,b,w,N,O,L,v=e.protectionModel,P=[];function K(){if(!(o&&o.hasOwnProperty("on")&&h&&h.hasOwnProperty("getSupportedKeySystemMetadataFromContentProtection")))throw new Error("Missing config parameter(s)")}function U(e,n){O||A?O&&k():function(e,n){if(A)return;var r;A=!0;const i=function(e){const t=[];for(let n=0;n<e.length;n++){const r=Y(e[n]);t.push({ks:e[n].ks,configs:[r],protData:e[n].protData})}return t}(e=(r=e).sort(((e,t)=>(R&&R[e.ks.systemString]&&R[e.ks.systemString].priority>=0?R[e.ks.systemString].priority:r.length)-(R&&R[t.ks.systemString]&&R[t.ks.systemString].priority>=0?R[t.ks.systemString].priority:r.length))));let s;v.requestKeySystemAccess(i).then((e=>(s=e.data,function(e){let t=e&&e.selectedSystemString?e.selectedSystemString:e.keySystem.systemString;return M.info("DRM: KeySystem Access Granted for system string ("+t+")!  Selecting key system..."),v.selectKeySystem(e)}(s)))).then((e=>{!function(e,n){O=e,A=!1,o.trigger(T.KEY_SYSTEM_SELECTED,{data:n});const r=H(O);r&&r.serverCertificate&&r.serverCertificate.length>0&&v.setServerCertificate(t.decodeArray(r.serverCertificate).buffer),k()}(e,s)})).catch((e=>{!function(e,t){O=null,A=!1,t||o.trigger(T.KEY_SYSTEM_SELECTED,{data:null,error:new d.default(c.default.KEY_SYSTEM_ACCESS_DENIED_ERROR_CODE,c.default.KEY_SYSTEM_ACCESS_DENIED_ERROR_MESSAGE+"Error selecting key system! -- "+e.error)})}(e,n)}))}(e,n)}function k(){let e;for(let n=0;n<w.length;n++)for(e=0;e<w[n].length;e++)if(O===w[n][e].ks){t=w[n][e],h.isClearKey(O)&&function(e){if(e.protData&&e.protData.hasOwnProperty("clearkeys")&&0!==Object.keys(e.protData.clearkeys).length){const t={kids:Object.keys(e.protData.clearkeys)};e.initData=(new TextEncoder).encode(JSON.stringify(t))}}(t),t.sessionId?x(t):null!==t.initData&&G(t);break}var t;w=[]}function Y(e){const t=e.protData,n=[],i=[],a=t&&t.initDataTypes&&t.initDataTypes.length>0?t.initDataTypes:[s.default.INITIALIZATION_DATA_TYPE_CENC],o=t&&t.audioRobustness&&t.audioRobustness.length>0?t.audioRobustness:N,E=t&&t.videoRobustness&&t.videoRobustness.length>0?t.videoRobustness:N,c=e.sessionType,d=t&&t.distinctiveIdentifier?t.distinctiveIdentifier:"optional",_=t&&t.persistentState?t.persistentState:"temporary"===c?"optional":"required";return b.forEach((e=>{e.type===r.AUDIO?n.push(new l(e.codec,o)):e.type===r.VIDEO&&i.push(new l(e.codec,E))})),new u(n,i,d,_,[c],a)}function x(e){K(),v.loadKeySession(e)}function G(e){if(e&&function(e){if(!e)return!1;try{const t=v.getSessionTokens();for(let n=0;n<t.length;n++)if(t[n].getKeyId()===e)return!0;return!1}catch(e){return!1}}(e.keyId))return;const t=E.getPSSHForKeySystem(O,e?e.initData:null);if(t){if(F(t))return;try{e.initData=t,v.createKeySession(e)}catch(e){o.trigger(T.KEY_SESSION_CREATED,{data:null,error:new d.default(c.default.KEY_SESSION_CREATED_ERROR_CODE,c.default.KEY_SESSION_CREATED_ERROR_MESSAGE+e.message)})}}else e&&e.initData?v.createKeySession(e):o.trigger(T.KEY_SESSION_CREATED,{data:null,error:new d.default(c.default.KEY_SESSION_CREATED_ERROR_CODE,c.default.KEY_SESSION_CREATED_ERROR_MESSAGE+"Selected key system is "+(O?O.systemString:null)+".  needkey/encrypted event contains no initData corresponding to that key system!")})}function H(e){if(e){const t=e.systemString;if(R)return t in R?R[t]:null}return null}function F(e){if(!e)return!1;try{const t=v.getAllInitData();for(let n=0;n<t.length;n++)if(h.initDataEquals(e,t[n]))return M.debug("DRM: Ignoring initData because we have already seen it!"),!0;return!1}catch(e){return!1}}function B(e){K(),e?(v.setMediaElement(e),o.on(T.NEED_KEY,Z,I)):null===e&&(v.setMediaElement(e),o.off(T.NEED_KEY,Z,I))}function V(e){M.debug("DRM: onKeyMessage");const t=e.data;o.trigger(T.KEY_MESSAGE,{data:t});const n=t.messageType?t.messageType:s.default.MEDIA_KEY_MESSAGE_TYPES.LICENSE_REQUEST,r=t.message,a=t.sessionToken,l=H(O),u=h.getLicenseServerModelInstance(O,l,n),y={sessionToken:a,messageType:n};if(r&&0!==r.byteLength){if(!u)return M.debug("DRM: License server request not required for this message (type = "+e.data.messageType+").  Session ID = "+a.getSessionId()),void W(y);if(h.isClearKey(O)){const e=h.processClearKeyLicenseRequest(O,l,r);if(e&&e.keyPairs&&e.keyPairs.length>0)return M.debug("DRM: ClearKey license request handled by application!"),W(y),void v.updateKeySession(a,e)}!function(e,t,n){const r=e.sessionToken,a=e.messageType?e.messageType:s.default.MEDIA_KEY_MESSAGE_TYPES.LICENSE_REQUEST,o={sessionToken:r,messageType:a},l=O?O.systemString:null;let u=function(e,t,n,r,i){let s=null;const a=r.message;if(e&&e.serverURL){const n=e.serverURL;"string"==typeof n&&""!==n?s=n:"object"==typeof n&&n.hasOwnProperty(t)&&(s=n[t])}else if(e&&e.laURL&&""!==e.laURL)s=e.laURL;else if(s=E.getLicenseServerUrlFromMediaInfo(b,O.schemeIdURI),!s&&!h.isClearKey(O)){const e=E.getPSSHData(n.initData);s=O.getLicenseServerURLFromInitData(e),s||(s=r.laURL)}return s=i.getServerURLFromMessage(s,a,t),s}(n,a,r,e,t);if(!u)return void W(o,new d.default(c.default.MEDIA_KEY_MESSAGE_NO_LICENSE_SERVER_URL_ERROR_CODE,c.default.MEDIA_KEY_MESSAGE_NO_LICENSE_SERVER_URL_ERROR_MESSAGE));const y={};let T=!1;n&&z(y,n.httpRequestHeaders);const R=e.message;z(y,O.getRequestHeadersFromMessage(R)),Object.keys(y).forEach((e=>{"authorization"===e.toLowerCase()&&(T=!0)})),n&&"boolean"==typeof n.withCredentials&&(T=n.withCredentials);const I=function(e){if(v)if(e.status>=200&&e.status<=299){const n=g.default.parseHttpHeaders(e.getAllResponseHeaders?e.getAllResponseHeaders():null);let s=new S(e.responseURL,n,e.response);Q(i.getLicenseResponseFilters(),s).then((()=>{const n=t.getLicenseMessage(s.data,l,a);null!==n?(W(o),v.updateKeySession(r,n)):X(e,o,l,a,t)}))}else X(e,o,l,a,t)},p=function(e){W(o,new d.default(c.default.MEDIA_KEY_MESSAGE_LICENSER_ERROR_CODE,c.default.MEDIA_KEY_MESSAGE_LICENSER_ERROR_MESSAGE+l+' update, XHR aborted. status is "'+e.statusText+'" ('+e.status+"), readyState is "+e.readyState))},A=function(e){W(o,new d.default(c.default.MEDIA_KEY_MESSAGE_LICENSER_ERROR_CODE,c.default.MEDIA_KEY_MESSAGE_LICENSER_ERROR_MESSAGE+l+' update, XHR error. status is "'+e.statusText+'" ('+e.status+"), readyState is "+e.readyState))},C=O.getLicenseRequestFromMessage(R),D=t.getHTTPMethod(a),M=t.getResponseType(l,a),w=n&&!isNaN(n.httpTimeout)?n.httpTimeout:8e3,N=r.getSessionId()||null;let L=new _(u,D,M,y,T,a,N,C);const P=isNaN(m.get().streaming.retryAttempts[f.HTTPRequest.LICENSE])?3:m.get().streaming.retryAttempts[f.HTTPRequest.LICENSE];Q(i.getLicenseRequestFilters(),L).then((()=>{q(L,P,w,I,p,A)}))}(t,u,l)}else W(y,new d.default(c.default.MEDIA_KEY_MESSAGE_NO_CHALLENGE_ERROR_CODE,c.default.MEDIA_KEY_MESSAGE_NO_CHALLENGE_ERROR_MESSAGE))}function W(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;o.trigger(T.LICENSE_REQUEST_COMPLETE,{data:e,error:t})}function q(e,t,r,i,s,a){const E=new XMLHttpRequest,l=n.getCmcdParametersFromManifest();if(n.isCmcdEnabled()&&(l.mode?l.mode:m.get().streaming.cmcd.mode)===y.default.CMCD_MODE_QUERY){const t=n.getQueryParameter({url:e.url,type:f.HTTPRequest.LICENSE});t&&(e.url=g.default.addAdditionalQueryParameterToUrl(e.url,[t]))}E.open(e.method,e.url,!0),E.responseType=e.responseType,E.withCredentials=e.withCredentials,r>0&&(E.timeout=r);for(const t in e.headers)E.setRequestHeader(t,e.headers[t]);if(n.isCmcdEnabled()&&(l.mode?l.mode:m.get().streaming.cmcd.mode)===y.default.CMCD_MODE_HEADER){const t=n.getHeaderParameters({url:e.url,type:f.HTTPRequest.LICENSE});if(t)for(const e in t){let n=t[e];n&&E.setRequestHeader(e,n)}}const u=function(){t--;const n=isNaN(m.get().streaming.retryIntervals[f.HTTPRequest.LICENSE])?1e3:m.get().streaming.retryIntervals[f.HTTPRequest.LICENSE];C=setTimeout((function(){q(e,t,r,i,s,a)}),n)};E.onload=function(){D=null,this.status>=200&&this.status<=299||t<=0?i(this):(M.warn("License request failed ("+this.status+"). Retrying it... Pending retries: "+t),u())},E.ontimeout=E.onerror=function(){D=null,t<=0?a(this):(M.warn("License request network request failed . Retrying it... Pending retries: "+t),u())},E.onabort=function(){s(this)},o.trigger(T.LICENSE_REQUEST_SENDING,{url:e.url,headers:e.headers,payload:e.data,sessionId:e.sessionId}),D=E,E.send(e.data)}function j(){D&&(D.onloadend=D.onerror=D.onprogress=void 0,D.abort(),D=null),C&&(clearTimeout(C),C=null)}function z(e,t){if(t)for(const n in t)e[n]=t[n]}function X(e,t,n,r,i){let s="NONE",a=null;e.response&&(s=i.getErrorResponse(e.response,n,r),a={serverResponse:e.response||null,responseCode:e.status||null,responseText:e.statusText||null}),W(t,new d.default(c.default.MEDIA_KEY_MESSAGE_LICENSER_ERROR_CODE,c.default.MEDIA_KEY_MESSAGE_LICENSER_ERROR_MESSAGE+n+' update, XHR complete. status is "'+e.statusText+'" ('+e.status+"), readyState is "+e.readyState+".  Response is "+s,a))}function Q(e,t){return e?e.reduce(((e,n)=>e.then((()=>n(t)))),Promise.resolve()):Promise.resolve()}function Z(e,t){if(m.get().streaming.protection.ignoreEmeEncryptedEvent)return;if(M.debug("DRM: onNeedKey"),e.key.initDataType!==s.default.INITIALIZATION_DATA_TYPE_CENC)return void M.warn("DRM:  Only 'cenc' initData is supported!  Ignoring initData of type: "+e.key.initDataType);if(0===b.length&&(M.warn("DRM: onNeedKey called before initializeForMedia, wait until initialized"),(t=void 0===t?1:t+1)<5))return void P.push(setTimeout((()=>{Z(e,t)}),500));let n=e.key.initData;if(ArrayBuffer.isView(n)&&(n=n.buffer),O){const e=E.getPSSHForKeySystem(O,n);if(e&&F(e))return}M.debug("DRM: initData:",String.fromCharCode.apply(null,new Uint8Array(n)));const r=h.getSupportedKeySystemMetadataFromSegmentPssh(n,R,L);0!==r.length?function(e){w.push(e),U(e,!1)}(r):M.debug("DRM: Received needkey event with initData, but we don't support any of the key systems!")}function $(e,t){if(e.size<=0)return!1;if(t.size>0&&[...e].every((e=>{const n=t.get(e);return void 0!==n&&""!==n})))return!0;const n=v.getSessionTokens();if(n&&n.length>0){const t=n.filter((t=>[...e].includes(t.normalizedKeyId)));if(t.some((e=>!e.hasTriggeredKeyStatusMapUpdate))||0===t.length)return!1}return!m.get().streaming.protection.ignoreKeyStatuses&&e&&e.size>0&&t&&t.size>0}return I={areKeyIdsExpired:function(e){try{return!!$(e,p)&&[...e].every((e=>p.get(e)===s.default.MEDIA_KEY_STATUSES.EXPIRED))}catch(e){return M.error(e),!1}},areKeyIdsUsable:function(e){try{return!$(e,p)||[...e].some((e=>{const t=p.get(e);return t&&t!==s.default.MEDIA_KEY_STATUSES.INTERNAL_ERROR&&t!==s.default.MEDIA_KEY_STATUSES.OUTPUT_RESTRICTED}))}catch(e){return M.error(e),!0}},clearMediaInfoArray:function(){b=[]},closeKeySession:function(e){K(),v.closeKeySession(e)},createKeySession:G,getKeySystems:function(){return h?h.getKeySystems():[]},getSupportedKeySystemMetadataFromContentProtection:function(e){return K(),h.getSupportedKeySystemMetadataFromContentProtection(e,R,L)},handleKeySystemFromManifest:function(){if(!b||0===b.length)return;let e=[];b.forEach((t=>{const n=h.getSupportedKeySystemMetadataFromContentProtection(t.contentProtection,R,L);n.length>0&&(0===e.length&&(e=n),w.push(n))})),e&&e.length>0&&U(e,!0)},initializeForMedia:function(e){if(!e)throw new Error("mediaInfo can not be null or undefined");K(),b.push(e)},loadKeySession:x,removeKeySession:function(e){K(),v.removeKeySession(e)},reset:function(){o.off(T.INTERNAL_KEY_MESSAGE,V,I),K(),j(),B(null),O=null,A=!1,p=new Map,v&&(v.reset(),v=null),P.forEach((e=>clearTimeout(e))),P=[],b=[],w=[]},setKeySystems:function(e){h&&h.setKeySystems(e)},setMediaElement:B,setProtectionData:function(e){R=e,h.setProtectionData(e)},setRobustnessLevel:function(e){N=e},setServerCertificate:function(e){K(),v.setServerCertificate(e)},setSessionType:function(e){L=e},stop:function(){j(),v&&v.stop()},updateKeyStatusesMap:function(e){try{if(!e||!e.sessionToken||!e.parsedKeyStatuses)return;e.sessionToken.hasTriggeredKeyStatusMapUpdate=!0;const t=e.parsedKeyStatuses,n=g.default.parseUserAgent(),r=n&&n.browser&&n.browser.name&&"edge"===n.browser.name.toLowerCase();t.forEach((e=>{r&&O.uuid===s.default.PLAYREADY_UUID&&e.keyId&&16===e.keyId.byteLength&&function(e){const t=g.default.bufferSourceToDataView(e),n=t.getUint32(0,!0),r=t.getUint16(4,!0),i=t.getUint16(6,!0);t.setUint32(0,n,!1),t.setUint16(4,r,!1),t.setUint16(6,i,!1)}(e.keyId);const t=g.default.bufferSourceToHex(e.keyId).slice(0,32);t&&""!==t&&p.set(t,e.status)})),o.trigger(T.KEY_STATUSES_MAP_UPDATED,{keyStatusMap:p})}catch(e){M.error(e)}}},M=a.getLogger(I),w=[],b=[],L="temporary",N="",D=null,C=null,p=new Map,o.on(T.INTERNAL_KEY_MESSAGE,V,I),I}h.__dashjs_factory_name="ProtectionController";var m=T.default.getClassFactory(h),R=class{constructor(e,t){this.keyID=e,this.key=t}},I=class{constructor(e,t){if(t&&"persistent"!==t&&"temporary"!==t)throw new Error("Invalid ClearKey key set type!  Must be one of 'persistent' or 'temporary'");this.keyPairs=e,this.type=t}toJWK(){let e,t=this.keyPairs.length,n={keys:[]};for(e=0;e<t;e++){let t={kty:"oct",alg:"A128KW",kid:this.keyPairs[e].keyID,k:this.keyPairs[e].key};n.keys.push(t)}this.type&&(n.type=this.type);let r=JSON.stringify(n);const i=r.length;let s=new ArrayBuffer(i),a=new Uint8Array(s);for(e=0;e<i;e++)a[e]=r.charCodeAt(e);return s}};const p=s.default.CLEARKEY_UUID,A=s.default.CLEARKEY_KEYSTEM_STRING,C="urn:uuid:"+p;function D(e){let t;const n=(e=e||{}).BASE64;return t={uuid:p,schemeIdURI:C,systemString:A,getInitData:function(e,t){try{let r=E.parseInitDataFromContentProtection(e,n);if(!r&&t){const e={kids:[function(e){try{let t=e.replace(/-/g,"");return t=btoa(t.match(/\w{2}/g).map((e=>String.fromCharCode(parseInt(e,16)))).join("")),t.replace(/=/g,"").replace(/\//g,"_").replace(/\+/g,"-")}catch(e){return null}}(t.cencDefaultKid)]};r=(new TextEncoder).encode(JSON.stringify(e))}return r}catch(e){return null}},getRequestHeadersFromMessage:function(){return{"Content-Type":"application/json"}},getLicenseRequestFromMessage:function(e){return JSON.stringify(JSON.parse(String.fromCharCode.apply(null,new Uint8Array(e))))},getLicenseServerURLFromInitData:function(){return null},getCDMData:function(){return null},getClearKeysFromProtectionData:function(e,t){let n=null;if(e){const r=JSON.parse(String.fromCharCode.apply(null,new Uint8Array(t))),i=[];for(let t=0;t<r.kids.length;t++){const n=r.kids[t],s=e.clearkeys&&e.clearkeys.hasOwnProperty(n)?e.clearkeys[n]:null;if(!s)throw new Error("DRM: ClearKey keyID ("+n+") is not known!");i.push(new R(n,s))}n=new I(i)}return n}},t}D.__dashjs_factory_name="KeySystemClearKey";var M=T.default.getSingletonFactory(D);const b=s.default.W3C_CLEARKEY_UUID,w=s.default.CLEARKEY_KEYSTEM_STRING,N="urn:uuid:"+b;function O(e){let t;const n=e.BASE64,r=e.debug.getLogger(t);return t={uuid:b,schemeIdURI:N,systemString:w,getInitData:function(e){return E.parseInitDataFromContentProtection(e,n)},getRequestHeadersFromMessage:function(){return null},getLicenseRequestFromMessage:function(e){return new Uint8Array(e)},getLicenseServerURLFromInitData:function(){return null},getCDMData:function(){return null},getClearKeysFromProtectionData:function(e,t){let n=null;if(e){const i=JSON.parse(String.fromCharCode.apply(null,new Uint8Array(t))),s=[];for(let t=0;t<i.kids.length;t++){const n=i.kids[t],r=e.clearkeys&&e.clearkeys.hasOwnProperty(n)?e.clearkeys[n]:null;if(!r)throw new Error("DRM: ClearKey keyID ("+n+") is not known!");s.push(new R(n,r))}n=new I(s),r.warn("ClearKey schemeIdURI is using W3C Common PSSH systemID (1077efec-c0b2-4d02-ace3-3c1e52e2fb4b) in Content Protection. See DASH-IF IOP v4.1 section 7.6.2.4")}return n}},t}O.__dashjs_factory_name="KeySystemW3CClearKey";var L=T.default.getSingletonFactory(O);const v=s.default.WIDEVINE_UUID,P=s.default.WIDEVINE_KEYSTEM_STRING,K="urn:uuid:"+v;function U(e){let t;const n=(e=e||{}).BASE64;return t={uuid:v,schemeIdURI:K,systemString:P,getInitData:function(e){return E.parseInitDataFromContentProtection(e,n)},getRequestHeadersFromMessage:function(){return null},getLicenseRequestFromMessage:function(e){return new Uint8Array(e)},getLicenseServerURLFromInitData:function(){return null},getCDMData:function(){return null}},t}U.__dashjs_factory_name="KeySystemWidevine";var k=T.default.getSingletonFactory(U);const Y=s.default.PLAYREADY_UUID,x=s.default.PLAYREADY_KEYSTEM_STRING,G="urn:uuid:"+Y;function H(e){let t,n="utf-16";const r=(e=e||{}).BASE64,i=e.settings;function s(){if(!r||!r.hasOwnProperty("decodeArray")||!r.hasOwnProperty("decodeArray"))throw new Error("Missing config parameter(s)")}return t={uuid:Y,schemeIdURI:G,systemString:x,getInitData:function(e){const t=new Uint8Array([112,115,115,104,0,0,0,0]),n=new Uint8Array([154,4,240,121,152,64,66,134,171,146,230,91,224,136,95,149]);let i,a,o,l,u,c=0,d=null;if(s(),!e)return null;if("pssh"in e&&e.pssh)return E.parseInitDataFromContentProtection(e,r);if("pro"in e&&e.pro)d=r.decodeArray(e.pro.__text);else{if(!("prheader"in e)||!e.prheader)return null;d=r.decodeArray(e.prheader.__text)}return i=d.length,a=4+t.length+n.length+4+i,o=new ArrayBuffer(a),l=new Uint8Array(o),u=new DataView(o),u.setUint32(c,a),c+=4,l.set(t,c),c+=t.length,l.set(n,c),c+=n.length,u.setUint32(c,i),c+=4,l.set(d,c),c+=i,l.buffer},getRequestHeadersFromMessage:function(e){let t,r;const s={},a=new DOMParser;if(i&&i.get().streaming.protection.detectPlayreadyMessageFormat&&"utf-16"===n&&e&&e.byteLength%2==1)return s["Content-Type"]="text/xml; charset=utf-8",s;const o="utf-16"===n?new Uint16Array(e):new Uint8Array(e);t=String.fromCharCode.apply(null,o),r=a.parseFromString(t,"application/xml");const E=r.getElementsByTagName("name"),l=r.getElementsByTagName("value");for(let e=0;e<E.length;e++)s[E[e].childNodes[0].nodeValue]=l[e].childNodes[0].nodeValue;return s.hasOwnProperty("Content")&&(s["Content-Type"]=s.Content,delete s.Content),s.hasOwnProperty("Content-Type")||(s["Content-Type"]="text/xml; charset=utf-8"),s},getLicenseRequestFromMessage:function(e){let t=null;const a=new DOMParser;if(i&&i.get().streaming.protection.detectPlayreadyMessageFormat&&"utf-16"===n&&e&&e.byteLength%2==1)return e;const o="utf-16"===n?new Uint16Array(e):new Uint8Array(e);s();const E=String.fromCharCode.apply(null,o),l=a.parseFromString(E,"application/xml");if(!l.getElementsByTagName("PlayReadyKeyMessage")[0])return e;{const e=l.getElementsByTagName("Challenge")[0].childNodes[0].nodeValue;e&&(t=r.decode(e))}return t},getLicenseServerURLFromInitData:function(e){if(e){const t=new DataView(e),n=t.getUint16(4,!0);let r=6;const i=new DOMParser;for(let s=0;s<n;s++){const n=t.getUint16(r,!0);r+=2;const s=t.getUint16(r,!0);if(r+=2,1!==n){r+=s;continue}const a=e.slice(r,r+s),o=String.fromCharCode.apply(null,new Uint16Array(a)),E=i.parseFromString(o,"application/xml");if(E.getElementsByTagName("LA_URL")[0]){const e=E.getElementsByTagName("LA_URL")[0].childNodes[0].nodeValue;if(e)return e}if(E.getElementsByTagName("LUI_URL")[0]){const e=E.getElementsByTagName("LUI_URL")[0].childNodes[0].nodeValue;if(e)return e}}}return null},getCDMData:function(e){let t,n,i,a;if(s(),!e)return null;for(t=[],a=0;a<e.length;++a)t.push(e.charCodeAt(a)),t.push(0);for(t=String.fromCharCode.apply(null,t),t=r.encode(t),n='<PlayReadyCDMData type="LicenseAcquisition"><LicenseAcquisition version="1.0" Proactive="false"><CustomData encoding="base64encoded">%CUSTOMDATA%</CustomData></LicenseAcquisition></PlayReadyCDMData>'.replace("%CUSTOMDATA%",t),i=[],a=0;a<n.length;++a)i.push(n.charCodeAt(a)),i.push(0);return new Uint8Array(i).buffer},setPlayReadyMessageFormat:function(e){if("utf-8"!==e&&"utf-16"!==e)throw new Error('Specified message format is not one of "utf-8" or "utf-16"');n=e}},t}H.__dashjs_factory_name="KeySystemPlayReady";var F=T.default.getSingletonFactory(H);function B(e){const t=(e=e||{}).BASE64,n={};let r;return n[s.default.WIDEVINE_KEYSTEM_STRING]={responseType:"json",getLicenseMessage:function(e){return t.decodeArray(e.license)},getErrorResponse:function(e){return e}},n[s.default.PLAYREADY_KEYSTEM_STRING]={responseType:"arraybuffer",getLicenseMessage:function(e){return e},getErrorResponse:function(e){return String.fromCharCode.apply(null,new Uint8Array(e))}},r={getServerURLFromMessage:function(e){return e},getHTTPMethod:function(){return"POST"},getResponseType:function(e){return n[e].responseType},getLicenseMessage:function(e,r){return function(){if(!t||!t.hasOwnProperty("decodeArray"))throw new Error("Missing config parameter(s)")}(),n[r].getLicenseMessage(e)},getErrorResponse:function(e,t){return n[t].getErrorResponse(e)}},r}B.__dashjs_factory_name="DRMToday";var V=T.default.getSingletonFactory(B);function W(){let e;const t="http://schemas.xmlsoap.org/soap/envelope/";function n(e){const t=String.fromCharCode.apply(null,new Uint8Array(e));return decodeURIComponent(escape(t))}function r(e){if(window.DOMParser){const r=n(e),i=(new window.DOMParser).parseFromString(r,"text/xml"),s=i?i.getElementsByTagNameNS(t,"Envelope")[0]:null,a=s?s.getElementsByTagNameNS(t,"Body")[0]:null;if(a&&a.getElementsByTagNameNS(t,"Fault")[0])return null}return e}function i(e){let r="",i="",s="",a=-1,o=-1;if(window.DOMParser){const E=n(e),l=(new window.DOMParser).parseFromString(E,"text/xml"),u=l?l.getElementsByTagNameNS(t,"Envelope")[0]:null,c=u?u.getElementsByTagNameNS(t,"Body")[0]:null,d=c?c.getElementsByTagNameNS(t,"Fault")[0]:null,_=d?d.getElementsByTagName("detail")[0]:null,S=_?_.getElementsByTagName("Exception")[0]:null;let f=null;if(null===d)return E;f=d.getElementsByTagName("faultstring")[0].firstChild,r=f?f.nodeValue:null,null!==S&&(f=S.getElementsByTagName("StatusCode")[0],i=f?f.firstChild.nodeValue:null,f=S.getElementsByTagName("Message")[0],s=f?f.firstChild.nodeValue:null,a=s?s.lastIndexOf("[")+1:-1,o=s?s.indexOf("]"):-1,s=s?s.substring(a,o):"")}let E=`code: ${i}, name: ${r}`;return s&&(E+=`, message: ${s}`),E}return e={getServerURLFromMessage:function(e){return e},getHTTPMethod:function(){return"POST"},getResponseType:function(){return"arraybuffer"},getLicenseMessage:function(e){return r.call(this,e)},getErrorResponse:function(e){return i.call(this,e)}},e}W.__dashjs_factory_name="PlayReady";var q=T.default.getSingletonFactory(W);function j(){let e;return e={getServerURLFromMessage:function(e){return e},getHTTPMethod:function(){return"POST"},getResponseType:function(){return"arraybuffer"},getLicenseMessage:function(e){return e},getErrorResponse:function(e){return String.fromCharCode.apply(null,new Uint8Array(e))}},e}j.__dashjs_factory_name="Widevine";var z=T.default.getSingletonFactory(j);function X(){let e;return e={getServerURLFromMessage:function(e){return e},getHTTPMethod:function(){return"POST"},getResponseType:function(){return"json"},getLicenseMessage:function(e){if(!e.hasOwnProperty("keys"))return null;let t=[];for(let n=0;n<e.keys.length;n++){let r=e.keys[n],i=r.kid.replace(/=/g,""),s=r.k.replace(/=/g,"");t.push(new R(i,s))}return new I(t)},getErrorResponse:function(e){return String.fromCharCode.apply(null,new Uint8Array(e))}},e}X.__dashjs_factory_name="ClearKey";var Q=T.default.getSingletonFactory(X),Z=class{constructor(e){this.ks=e.ks,this.keyId=e.keyId,this.initData=e.initData,this.protData=e.protData,this.cdmData=e.cdmData,this.sessionId=e.sessionId,this.sessionType=e.sessionType}};function $(){let e,t,n,r,i,a,o,l,u=this.context;function c(e,t){return t&&e in t?t[e]:null}function d(e,t){return e&&e.sessionId?e.sessionId:t&&t.sessionId?t.sessionId:null}function _(e,t){return e&&e.sessionType?e.sessionType:t}return e={getKeySystemBySystemString:function(e){for(let t=0;t<r.length;t++)if(r[t].systemString===e)return r[t];return null},getKeySystems:function(){return r},getLicenseServerModelInstance:function(e,t,n){if(n===s.default.MEDIA_KEY_MESSAGE_TYPES.LICENSE_RELEASE||n===s.default.MEDIA_KEY_MESSAGE_TYPES.INDIVIDUALIZATION_REQUEST)return null;let r=null;return t&&t.hasOwnProperty("drmtoday")?r=V(u).getInstance({BASE64:i}):e.systemString===s.default.WIDEVINE_KEYSTEM_STRING?r=z(u).getInstance():e.systemString===s.default.PLAYREADY_KEYSTEM_STRING?r=q(u).getInstance():e.systemString===s.default.CLEARKEY_KEYSTEM_STRING&&(r=Q(u).getInstance()),r},getSupportedKeySystemMetadataFromContentProtection:function(e,t,n){let i,s,a,o,l=[];if(!e||!e.length)return l;const u=E.findMp4ProtectionElement(e);for(a=0;a<r.length;a++){s=r[a];const E=c(s.systemString,t);for(o=0;o<e.length;o++)if(i=e[o],i.schemeIdUri.toLowerCase()===s.schemeIdURI){let e=s.getInitData(i,u);const t=new Z({ks:r[a],keyId:i.keyId,initData:e,protData:E,cdmData:s.getCDMData(E?E.cdmData:null),sessionId:d(E,i),sessionType:_(E,n)});E?l.unshift(t):l.push(t)}}return l},getSupportedKeySystemMetadataFromSegmentPssh:function(e,t,n){let i,s,a=[],o=E.parsePSSHList(e);for(let e=0;e<r.length;++e){i=r[e],s=i.systemString;const E=c(s,t);i.uuid in o&&a.push({ks:i,initData:o[i.uuid],protData:E,cdmData:i.getCDMData(E?E.cdmData:null),sessionId:d(E),sessionType:_(E,n)})}return a},initDataEquals:function(e,t){if(e.byteLength===t.byteLength){let n=new Uint8Array(e),r=new Uint8Array(t);for(let e=0;e<n.length;e++)if(n[e]!==r[e])return!1;return!0}return!1},initialize:function(){let e;r=[],e=F(u).getInstance({BASE64:i,settings:a}),r.push(e),e=k(u).getInstance({BASE64:i}),r.push(e),e=M(u).getInstance({BASE64:i}),r.push(e),o=e,e=L(u).getInstance({BASE64:i,debug:t}),r.push(e),l=e},isClearKey:function(e){return e===o||e===l},processClearKeyLicenseRequest:function(e,t,r){try{return e.getClearKeysFromProtectionData(t,r)}catch(e){return n.error("Failed to retrieve clearkeys from ProtectionData"),null}},setConfig:function(r){r&&(r.debug&&(t=r.debug,n=t.getLogger(e)),r.BASE64&&(i=r.BASE64),r.settings&&(a=r.settings))},setKeySystems:function(e){r=e},setProtectionData:function(e){for(var t,n,i=0;i<r.length;i++){var s=r[i];s.hasOwnProperty("init")&&s.init((t=s.systemString,n=void 0,n=null,e&&(n=t in e?e[t]:null),n))}}},e}$.__dashjs_factory_name="ProtectionKeyController";var J=T.default.getSingletonFactory($),ee=n(445),te=class{constructor(e,t){this.initData=e,this.initDataType=t}},ne=class{constructor(e,t,n,r){this.sessionToken=e,this.message=t,this.defaultURL=n,this.messageType=r||s.default.MEDIA_KEY_MESSAGE_TYPES.LICENSE_REQUEST}},re=class{constructor(e,t){this.keySystem=e,this.ksConfiguration=t,this.nativeMediaKeySystemAccessObject=null,this.selectedSystemString=null}};const ie={};function se(e){e=e||{};const t=this.context,n=e.eventBus,r=e.events,i=e.debug;let a,o,E,l,u,_,S,f;function g(e,t,i,s){if(void 0===navigator.requestMediaKeySystemAccess||"function"!=typeof navigator.requestMediaKeySystemAccess){const e="Insecure origins are not allowed";return n.trigger(r.KEY_SYSTEM_ACCESS_COMPLETE,{error:e}),void s({error:e})}const a=e[t].protData&&e[t].protData.systemStringPriority?e[t].protData.systemStringPriority:null,o=e[t].configs,E=e[t].ks;let l=E.systemString;(function(e,t){return new Promise(((n,r)=>{y(e,t,0,n,r)}))})(a||(ie[l]?ie[l]:[l]),o).then((e=>{const t=e&&e.nativeMediaKeySystemAccessObject&&"function"==typeof e.nativeMediaKeySystemAccessObject.getConfiguration?e.nativeMediaKeySystemAccessObject.getConfiguration():null,s=new re(E,t);s.selectedSystemString=e.selectedSystemString,s.nativeMediaKeySystemAccessObject=e.nativeMediaKeySystemAccessObject,n.trigger(r.KEY_SYSTEM_ACCESS_COMPLETE,{data:s}),i({data:s})})).catch((a=>{if(t+1<e.length)g(e,t+1,i,s);else{const e="Key system access denied! ";n.trigger(r.KEY_SYSTEM_ACCESS_COMPLETE,{error:e+a.message}),s({error:e+a.message})}}))}function y(e,t,n,r,i){const s=e[n];o.debug(`Requesting key system access for system string ${s}`),navigator.requestMediaKeySystemAccess(s,t).then((e=>{r({nativeMediaKeySystemAccessObject:e,selectedSystemString:s})})).catch((s=>{n+1<e.length?y(e,t,n+1,r,i):i(s)}))}function T(e){if(!e||!e.session)return Promise.resolve;const t=e.session;return t.removeEventListener("keystatuseschange",e),t.removeEventListener("message",e),t.close()}function h(e){for(let t=0;t<_.length;t++)if(_[t]===e){_.splice(t,1);break}}function m(e,t){const i={session:e,keyId:t.keyId,normalizedKeyId:t&&t.keyId&&"string"==typeof t.keyId?t.keyId.replace(/-/g,"").toLowerCase():"",initData:t.initData,sessionId:t.sessionId,sessionType:t.sessionType,hasTriggeredKeyStatusMapUpdate:!1,handleEvent:function(e){switch(e.type){case"keystatuseschange":this._onKeyStatusesChange(e);break;case"message":this._onKeyMessage(e)}},_onKeyStatusesChange:function(e){n.trigger(r.KEY_STATUSES_CHANGED,{data:this});const t=[];e.target.keyStatuses.forEach((function(){t.push(R(arguments))})),n.trigger(r.INTERNAL_KEY_STATUSES_CHANGED,{parsedKeyStatuses:t,sessionToken:i})},_onKeyMessage:function(e){let t=ArrayBuffer.isView(e.message)?e.message.buffer:e.message;n.trigger(r.INTERNAL_KEY_MESSAGE,{data:new ne(this,t,void 0,e.messageType)})},getKeyId:function(){return this.keyId},getSessionId:function(){return e.sessionId},getSessionType:function(){return this.sessionType},getExpirationTime:function(){return e.expiration},getKeyStatuses:function(){return e.keyStatuses},getUsable:function(){let t=!1;return e.keyStatuses.forEach((function(){R(arguments).status===s.default.MEDIA_KEY_STATUSES.USABLE&&(t=!0)})),t}};return e.addEventListener("keystatuseschange",i),e.addEventListener("message",i),e.closed.then((()=>{h(i),o.debug("DRM: Session closed.  SessionID = "+i.getSessionId()),n.trigger(r.KEY_SESSION_CLOSED,{data:i.getSessionId()})})),_.push(i),i}function R(e){let t,n;return e&&e.length>0&&(e[0]&&("string"==typeof e[0]?t=e[0]:n=e[0]),e[1]&&("string"==typeof e[1]?t=e[1]:n=e[1])),{status:t,keyId:n}}return a={closeKeySession:function(e){T(e).catch((function(t){h(e),n.trigger(r.KEY_SESSION_CLOSED,{data:null,error:"Error closing session ("+e.getSessionId()+") "+t.name})}))},createKeySession:function(e){if(!E||!u)throw new Error("Can not create sessions until you have selected a key system");const t=u.createSession(e.sessionType),i=m(t,e),a=E.systemString===s.default.CLEARKEY_KEYSTEM_STRING&&(e.initData||e.protData&&e.protData.clearkeys)?s.default.INITIALIZATION_DATA_TYPE_KEYIDS:s.default.INITIALIZATION_DATA_TYPE_CENC;t.generateRequest(a,e.initData).then((function(){o.debug("DRM: Session created.  SessionID = "+i.getSessionId()),n.trigger(r.KEY_SESSION_CREATED,{data:i})})).catch((function(e){h(i),n.trigger(r.KEY_SESSION_CREATED,{data:null,error:new d.default(c.default.KEY_SESSION_CREATED_ERROR_CODE,c.default.KEY_SESSION_CREATED_ERROR_MESSAGE+"Error generating key request -- "+e.name)})}))},getAllInitData:function(){const e=[];for(let t=0;t<_.length;t++)_[t].initData&&e.push(_[t].initData);return e},getSessionTokens:function(){return _},loadKeySession:function(e){if(!E||!u)throw new Error("Can not load sessions until you have selected a key system");const t=e.sessionId;for(let e=0;e<_.length;e++)if(t===_[e].sessionId)return void o.warn("DRM: Ignoring session ID because we have already seen it!");const i=u.createSession(e.sessionType),s=m(i,e);s.hasTriggeredKeyStatusMapUpdate=!0,i.load(t).then((function(e){e?(o.debug("DRM: Session loaded.  SessionID = "+s.getSessionId()),n.trigger(r.KEY_SESSION_CREATED,{data:s})):(h(s),n.trigger(r.KEY_SESSION_CREATED,{data:null,error:new d.default(c.default.KEY_SESSION_CREATED_ERROR_CODE,c.default.KEY_SESSION_CREATED_ERROR_MESSAGE+"Could not load session! Invalid Session ID ("+t+")")}))})).catch((function(e){h(s),n.trigger(r.KEY_SESSION_CREATED,{data:null,error:new d.default(c.default.KEY_SESSION_CREATED_ERROR_CODE,c.default.KEY_SESSION_CREATED_ERROR_MESSAGE+"Could not load session ("+t+")! "+e.name)})}))},removeKeySession:function(e){e.session.remove().then((function(){o.debug("DRM: Session removed.  SessionID = "+e.getSessionId()),n.trigger(r.KEY_SESSION_REMOVED,{data:e.getSessionId()})}),(function(t){n.trigger(r.KEY_SESSION_REMOVED,{data:null,error:"Error removing session ("+e.getSessionId()+"). "+t.name})}))},requestKeySystemAccess:function(e){return new Promise(((t,n)=>{g(e,0,t,n)}))},reset:function(){const e=_.length;let t;if(0!==e){const s=function(e){h(e),0===_.length&&(l?(l.removeEventListener("encrypted",S),l.setMediaKeys(null).then((function(){n.trigger(r.TEARDOWN_COMPLETE)}))):n.trigger(r.TEARDOWN_COMPLETE))};for(let n=0;n<e;n++)t=_[n],i=t,T(t),s(i)}else n.trigger(r.TEARDOWN_COMPLETE);var i},selectKeySystem:function(e){return new Promise(((t,n)=>{e.nativeMediaKeySystemAccessObject.createMediaKeys().then((t=>(E=e.keySystem,u=t,l?l.setMediaKeys(u):Promise.resolve()))).then((()=>{t(E)})).catch((function(){n({error:"Error selecting keys system ("+e.keySystem.systemString+")! Could not create MediaKeys -- TODO"})}))}))},setMediaElement:function(e){l!==e&&(l&&(l.removeEventListener("encrypted",S),l.setMediaKeys&&l.setMediaKeys(null)),l=e,l&&(l.addEventListener("encrypted",S),l.setMediaKeys&&u&&l.setMediaKeys(u)))},setServerCertificate:function(e){return new Promise(((t,i)=>{u.setServerCertificate(e).then((function(){o.info("DRM: License server certificate successfully updated."),n.trigger(r.SERVER_CERTIFICATE_UPDATED),t()})).catch((e=>{i(e),n.trigger(r.SERVER_CERTIFICATE_UPDATED,{error:new d.default(c.default.SERVER_CERTIFICATE_UPDATED_ERROR_CODE,c.default.SERVER_CERTIFICATE_UPDATED_ERROR_MESSAGE+e.name)})}))}))},stop:function(){let e;for(let t=0;t<_.length;t++)e=_[t],e.getUsable()||(T(e),h(e))},updateKeySession:function(e,t){const i=e.session;f.isClearKey(E)&&(t=t.toJWK()),i.update(t).then((()=>{n.trigger(r.KEY_SESSION_UPDATED)})).catch((function(t){n.trigger(r.KEY_ERROR,{error:new d.default(c.default.MEDIA_KEYERR_CODE,"Error sending update() message! "+t.name,e)})}))}},o=i.getLogger(a),E=null,l=null,u=null,_=[],f=J(t).getInstance(),S={handleEvent:function(e){if("encrypted"===e.type&&e.initData){let t=ArrayBuffer.isView(e.initData)?e.initData.buffer:e.initData;n.trigger(r.NEED_KEY,{key:new te(t,e.initDataType)})}}},a}ie[s.default.PLAYREADY_KEYSTEM_STRING]=[s.default.PLAYREADY_KEYSTEM_STRING,s.default.PLAYREADY_RECOMMENDATION_KEYSTEM_STRING],ie[s.default.WIDEVINE_KEYSTEM_STRING]=[s.default.WIDEVINE_KEYSTEM_STRING],ie[s.default.CLEARKEY_KEYSTEM_STRING]=[s.default.CLEARKEY_KEYSTEM_STRING],se.__dashjs_factory_name="DefaultProtectionModel";var ae=T.default.getClassFactory(se);function oe(e){e=e||{};const t=this.context,n=e.eventBus,r=e.events,i=e.debug,a=e.api;let o,E,l,_,S,f,g,y,T;function h(){try{for(let e=0;e<g.length;e++)m(g[e]);l&&l.removeEventListener(a.needkey,y),n.trigger(r.TEARDOWN_COMPLETE)}catch(e){n.trigger(r.TEARDOWN_COMPLETE,{error:"Error tearing down key sessions and MediaKeys! -- "+e.message})}}function m(e){const t=e.session;t.removeEventListener(a.error,e),t.removeEventListener(a.message,e),t.removeEventListener(a.ready,e),t.removeEventListener(a.close,e);for(let t=0;t<g.length;t++)if(g[t]===e){g.splice(t,1);break}t[a.release]()}function R(){let e=null;const t=function(){l.removeEventListener("loadedmetadata",e),l[a.setMediaKeys](S),n.trigger(r.VIDEO_ELEMENT_SELECTED)};l.readyState>=1?t():(e=t.bind(this),l.addEventListener("loadedmetadata",e))}return o={getAllInitData:function(){const e=[];for(let t=0;t<g.length;t++)e.push(g[t].initData);return e},getSessionTokens:function(){return g},requestKeySystemAccess:function(e){return new Promise(((t,i)=>{let s=!1;for(let i=0;i<e.length;i++){const o=e[i].ks.systemString,E=e[i].configs;let l=null,c=null;for(let e=0;e<E.length;e++){const i=E[e].audioCapabilities,d=E[e].videoCapabilities;if(i&&0!==i.length){l=[];for(let e=0;e<i.length;e++)window[a.MediaKeys].isTypeSupported(o,i[e].contentType)&&l.push(i[e])}if(d&&0!==d.length){c=[];for(let e=0;e<d.length;e++)window[a.MediaKeys].isTypeSupported(o,d[e].contentType)&&c.push(d[e])}if(!l&&!c||l&&0===l.length||c&&0===c.length)continue;s=!0;const _=new u(l,c),S=T.getKeySystemBySystemString(o),f=new re(S,_);n.trigger(r.KEY_SYSTEM_ACCESS_COMPLETE,{data:f}),t({data:f});break}}if(!s){const e="Key system access denied! -- No valid audio/video content configurations detected!";n.trigger(r.KEY_SYSTEM_ACCESS_COMPLETE,{error:e}),i({error:e})}}))},selectKeySystem:function(e){return new Promise(((t,n)=>{try{S=e.mediaKeys=new window[a.MediaKeys](e.keySystem.systemString),_=e.keySystem,f=e,l&&R(),t(_)}catch(e){n({error:"Error selecting keys system ("+_.systemString+")! Could not create MediaKeys -- TODO"})}}))},setMediaElement:function(e){l!==e&&(l&&l.removeEventListener(a.needkey,y),l=e,l&&(l.addEventListener(a.needkey,y),S&&R()))},createKeySession:function(e){if(!_||!S||!f)throw new Error("Can not create sessions until you have selected a key system");let t=null;if(f.ksConfiguration.videoCapabilities&&f.ksConfiguration.videoCapabilities.length>0&&(t=f.ksConfiguration.videoCapabilities[0]),null===t&&f.ksConfiguration.audioCapabilities&&f.ksConfiguration.audioCapabilities.length>0&&(t=f.ksConfiguration.audioCapabilities[0]),null===t)throw new Error("Can not create sessions for unknown content types.");const i=t.contentType,s=S.createSession(i,new Uint8Array(e.initData),e.cdmData?new Uint8Array(e.cdmData):null),o=function(e,t){return{session:e,keyId:t.keyId,normalizedKeyId:t&&t.keyId&&"string"==typeof t.keyId?t.keyId.replace(/-/g,"").toLowerCase():"",initData:t.initData,hasTriggeredKeyStatusMapUpdate:!1,getKeyId:function(){return this.keyId},getSessionId:function(){return this.session.sessionId},getExpirationTime:function(){return NaN},getSessionType:function(){return"temporary"},getKeyStatuses:function(){return{size:0,has:()=>!1,get:()=>{}}},handleEvent:function(e){switch(e.type){case a.error:let t="KeyError";n.trigger(r.KEY_ERROR,{error:new d.default(c.default.MEDIA_KEYERR_CODE,t,this)});break;case a.message:let i=ArrayBuffer.isView(e.message)?e.message.buffer:e.message;n.trigger(r.INTERNAL_KEY_MESSAGE,{data:new ne(this,i,e.destinationURL)});break;case a.ready:E.debug("DRM: Key added."),n.trigger(r.KEY_ADDED);break;case a.close:E.debug("DRM: Session closed.  SessionID = "+this.getSessionId()),n.trigger(r.KEY_SESSION_CLOSED,{data:this.getSessionId()})}}}}(s,e);s.addEventListener(a.error,o),s.addEventListener(a.message,o),s.addEventListener(a.ready,o),s.addEventListener(a.close,o),g.push(o),E.debug("DRM: Session created.  SessionID = "+o.getSessionId()),n.trigger(r.KEY_SESSION_CREATED,{data:o})},updateKeySession:function(e,t){const i=e.session;T.isClearKey(_)?i.update(new Uint8Array(t.toJWK())):i.update(new Uint8Array(t)),n.trigger(r.KEY_SESSION_UPDATED)},closeKeySession:m,setServerCertificate:function(){},loadKeySession:function(){},removeKeySession:function(){},stop:h,reset:h},E=i.getLogger(o),l=null,_=null,S=null,f=null,g=[],T=J(t).getInstance(),y={handleEvent:function(e){if(e.type===a.needkey&&e.initData){const t=ArrayBuffer.isView(e.initData)?e.initData.buffer:e.initData;n.trigger(r.NEED_KEY,{key:new te(t,s.default.INITIALIZATION_DATA_TYPE_CENC)})}}},o}oe.__dashjs_factory_name="ProtectionModel_3Feb2014";var Ee=T.default.getClassFactory(oe);function le(e){e=e||{};const t=this.context,n=e.eventBus,r=e.events,i=e.debug,a=e.api,o=e.errHandler;let E,l,_,S,f,g,y,T,h;function m(){_&&p();for(let e=0;e<y.length;e++)R(y[e]);n.trigger(r.TEARDOWN_COMPLETE)}function R(e){try{_[a.cancelKeyRequest](S.systemString,e.sessionId)}catch(t){n.trigger(r.KEY_SESSION_CLOSED,{data:null,error:"Error closing session ("+e.sessionId+") "+t.message})}}function I(e,t){if(t&&e){const n=e.length;for(let r=0;r<n;r++)if(e[r].sessionId==t)return e[r];return null}return null}function p(){_.removeEventListener(a.keyerror,h),_.removeEventListener(a.needkey,h),_.removeEventListener(a.keymessage,h),_.removeEventListener(a.keyadded,h)}return E={getAllInitData:function(){const e=[];for(let t=0;t<g.length;t++)e.push(g[t].initData);for(let t=0;t<y.length;t++)e.push(y[t].initData);return e},getSessionTokens:function(){return y.concat(g)},requestKeySystemAccess:function(e){return new Promise(((t,i)=>{let s=_;s||(s=document.createElement("video"));let a=!1;for(let i=0;i<e.length;i++){const o=e[i].ks.systemString,E=e[i].configs;let l=null,c=null;for(let e=0;e<E.length;e++){const i=E[e].videoCapabilities;if(i&&0!==i.length){c=[];for(let e=0;e<i.length;e++)""!==s.canPlayType(i[e].contentType,o)&&c.push(i[e])}if(!l&&!c||l&&0===l.length||c&&0===c.length)continue;a=!0;const d=new u(l,c),_=f.getKeySystemBySystemString(o),S=new re(_,d);n.trigger(r.KEY_SYSTEM_ACCESS_COMPLETE,{data:S}),t({data:S});break}}if(!a){const e="Key system access denied! -- No valid audio/video content configurations detected!";n.trigger(r.KEY_SYSTEM_ACCESS_COMPLETE,{error:e}),i({error:e})}}))},selectKeySystem:function(e){return S=e.keySystem,Promise.resolve(S)},setMediaElement:function(e){if(_!==e){if(_){p();for(var t=0;t<y.length;t++)R(y[t]);y=[]}_=e,_&&(_.addEventListener(a.keyerror,h),_.addEventListener(a.needkey,h),_.addEventListener(a.keymessage,h),_.addEventListener(a.keyadded,h),n.trigger(r.VIDEO_ELEMENT_SELECTED))}},createKeySession:function(e){if(!S)throw new Error("Can not create sessions until you have selected a key system");if(T||0===y.length){const t={sessionId:null,keyId:e.keyId,normalizedKeyId:e&&e.keyId&&"string"==typeof e.keyId?e.keyId.replace(/-/g,"").toLowerCase():"",initData:e.initData,hasTriggeredKeyStatusMapUpdate:!1,getKeyId:function(){return this.keyId},getSessionId:function(){return this.sessionId},getExpirationTime:function(){return NaN},getSessionType:function(){return"temporary"},getKeyStatuses:function(){return{size:0,has:()=>!1,get:()=>{}}}};return g.push(t),_[a.generateKeyRequest](S.systemString,new Uint8Array(e.initData)),t}throw new Error("Multiple sessions not allowed!")},updateKeySession:function(e,t){const i=e.sessionId;if(f.isClearKey(S))for(let e=0;e<t.keyPairs.length;e++)_[a.addKey](S.systemString,t.keyPairs[e].key,t.keyPairs[e].keyID,i);else _[a.addKey](S.systemString,new Uint8Array(t),new Uint8Array(e.initData),i);n.trigger(r.KEY_SESSION_UPDATED)},closeKeySession:R,setServerCertificate:function(){},loadKeySession:function(){},removeKeySession:function(){},stop:m,reset:m},l=i.getLogger(E),_=null,S=null,g=[],y=[],f=J(t).getInstance(),h={handleEvent:function(e){let t=null;switch(e.type){case a.needkey:let i=ArrayBuffer.isView(e.initData)?e.initData.buffer:e.initData;n.trigger(r.NEED_KEY,{key:new te(i,s.default.INITIALIZATION_DATA_TYPE_CENC)});break;case a.keyerror:if(t=I(y,e.sessionId),t||(t=I(g,e.sessionId)),t){let i=c.default.MEDIA_KEYERR_CODE,s="";switch(e.errorCode.code){case 1:i=c.default.MEDIA_KEYERR_UNKNOWN_CODE,s+="MEDIA_KEYERR_UNKNOWN - "+c.default.MEDIA_KEYERR_UNKNOWN_MESSAGE;break;case 2:i=c.default.MEDIA_KEYERR_CLIENT_CODE,s+="MEDIA_KEYERR_CLIENT - "+c.default.MEDIA_KEYERR_CLIENT_MESSAGE;break;case 3:i=c.default.MEDIA_KEYERR_SERVICE_CODE,s+="MEDIA_KEYERR_SERVICE - "+c.default.MEDIA_KEYERR_SERVICE_MESSAGE;break;case 4:i=c.default.MEDIA_KEYERR_OUTPUT_CODE,s+="MEDIA_KEYERR_OUTPUT - "+c.default.MEDIA_KEYERR_OUTPUT_MESSAGE;break;case 5:i=c.default.MEDIA_KEYERR_HARDWARECHANGE_CODE,s+="MEDIA_KEYERR_HARDWARECHANGE - "+c.default.MEDIA_KEYERR_HARDWARECHANGE_MESSAGE;break;case 6:i=c.default.MEDIA_KEYERR_DOMAIN_CODE,s+="MEDIA_KEYERR_DOMAIN - "+c.default.MEDIA_KEYERR_DOMAIN_MESSAGE}s+="  System Code = "+e.systemCode,n.trigger(r.KEY_ERROR,{error:new d.default(i,s,t)})}else l.error("No session token found for key error");break;case a.keyadded:t=I(y,e.sessionId),t||(t=I(g,e.sessionId)),t?(l.debug("DRM: Key added."),n.trigger(r.KEY_ADDED,{data:t})):l.debug("No session token found for key added");break;case a.keymessage:if(T=null!==e.sessionId&&void 0!==e.sessionId,T?(t=I(y,e.sessionId),!t&&g.length>0&&(t=g.shift(),y.push(t),t.sessionId=e.sessionId,n.trigger(r.KEY_SESSION_CREATED,{data:t}))):g.length>0&&(t=g.shift(),y.push(t),0!==g.length&&o.error(new d.default(c.default.MEDIA_KEY_MESSAGE_ERROR_CODE,c.default.MEDIA_KEY_MESSAGE_ERROR_MESSAGE))),t){let i=ArrayBuffer.isView(e.message)?e.message.buffer:e.message;t.keyMessage=i,n.trigger(r.INTERNAL_KEY_MESSAGE,{data:new ne(t,i,e.defaultURL)})}else l.warn("No session token found for key message")}}},E}le.__dashjs_factory_name="ProtectionModel_01b";var ue=T.default.getClassFactory(le);const ce=[{generateKeyRequest:"generateKeyRequest",addKey:"addKey",cancelKeyRequest:"cancelKeyRequest",needkey:"needkey",keyerror:"keyerror",keyadded:"keyadded",keymessage:"keymessage"},{generateKeyRequest:"webkitGenerateKeyRequest",addKey:"webkitAddKey",cancelKeyRequest:"webkitCancelKeyRequest",needkey:"webkitneedkey",keyerror:"webkitkeyerror",keyadded:"webkitkeyadded",keymessage:"webkitkeymessage"}],de=[{setMediaKeys:"setMediaKeys",MediaKeys:"MediaKeys",release:"close",needkey:"needkey",error:"keyerror",message:"keymessage",ready:"keyadded",close:"keyclose"},{setMediaKeys:"msSetMediaKeys",MediaKeys:"MSMediaKeys",release:"close",needkey:"msneedkey",error:"mskeyerror",message:"mskeymessage",ready:"mskeyadded",close:"mskeyclose"}];function _e(){let e;const t=this.context;function n(e,t){for(let n=0;n<t.length;n++){const r=t[n];if("function"==typeof e[r[Object.keys(r)[0]]])return r}return null}return e={createProtectionSystem:function(r){let i=null;const s=J(t).getInstance();s.setConfig({debug:r.debug,BASE64:r.BASE64,settings:r.settings}),s.initialize();let a=function(r){const i=r.debug,s=i.getLogger(e),a=r.eventBus,o=r.errHandler,E=r.videoModel?r.videoModel.getElement():null;return E&&void 0===E.onencrypted||E&&void 0===E.mediaKeys?n(E,de)?(s.info("EME detected on this user agent! (ProtectionModel_3Feb2014)"),Ee(t).create({debug:i,eventBus:a,events:r.events,api:n(E,de)})):n(E,ce)?(s.info("EME detected on this user agent! (ProtectionModel_01b)"),ue(t).create({debug:i,eventBus:a,errHandler:o,events:r.events,api:n(E,ce)})):(s.warn("No supported version of EME detected on this user agent! - Attempts to play encrypted content will fail!"),null):(s.info("EME detected on this user agent! (DefaultProtectionModel"),ae(t).create({debug:i,eventBus:a,events:r.events}))}(r);return a&&(i=m(t).create({BASE64:r.BASE64,cmcdModel:r.cmcdModel,constants:r.constants,customParametersModel:r.customParametersModel,debug:r.debug,eventBus:r.eventBus,events:r.events,protectionKeyController:s,protectionModel:a,settings:r.settings}),r.capabilities.setEncryptedMediaSupported(!0)),i}},e}_e.__dashjs_factory_name="Protection";const Se=dashjs.FactoryMaker.getClassFactory(_e);Se.events=ee.default,Se.errors=c.default,dashjs.FactoryMaker.updateClassFactory(_e.__dashjs_factory_name,Se);var fe=Se,ge=(r=r.default).default;export{ge as default};
//# sourceMappingURL=dash.protection.min.js.map