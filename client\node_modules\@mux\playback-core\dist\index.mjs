import ue from"mux-embed";import Ze from"hls.js";var g=Ze;var C={VIDEO:"video",THUMBNAIL:"thumbnail",STORYBOARD:"storyboard",DRM:"drm"},D={NOT_AN_ERROR:0,NETWORK_OFFLINE:2000002,NETWORK_UNKNOWN_ERROR:2e6,NETWORK_NO_STATUS:2000001,NETWORK_INVALID_URL:24e5,NETWORK_NOT_FOUND:2404e3,NETWORK_NOT_READY:2412e3,NETWORK_GENERIC_SERVER_FAIL:25e5,NETWORK_TOKEN_MISSING:2403201,NETWORK_TOKEN_MALFORMED:2412202,NETWORK_TOKEN_EXPIRED:2403210,NETWORK_TOKEN_AUD_MISSING:2403221,NETWORK_TOKEN_AUD_MISMATCH:2403222,NETWORK_TOKEN_SUB_MISMATCH:2403232,ENCRYPTED_ERROR:5e6,ENCRYPTED_UNSUPPORTED_KEY_SYSTEM:5000001,ENCRYPTED_GENERATE_REQUEST_FAILED:5000002,ENCRYPTED_UPDATE_LICENSE_FAILED:5000003,ENCRYPTED_UPDATE_SERVER_CERT_FAILED:5000004,ENCRYPTED_CDM_ERROR:5000005,ENCRYPTED_OUTPUT_RESTRICTED:5000006,ENCRYPTED_MISSING_TOKEN:5000002},V=e=>e===C.VIDEO?"playback":e,L=class L extends Error{constructor(t,r=L.MEDIA_ERR_CUSTOM,n,o){var a;super(t),this.name="MediaError",this.code=r,this.context=o,this.fatal=n!=null?n:r>=L.MEDIA_ERR_NETWORK&&r<=L.MEDIA_ERR_ENCRYPTED,this.message||(this.message=(a=L.defaultMessages[this.code])!=null?a:"")}};L.MEDIA_ERR_ABORTED=1,L.MEDIA_ERR_NETWORK=2,L.MEDIA_ERR_DECODE=3,L.MEDIA_ERR_SRC_NOT_SUPPORTED=4,L.MEDIA_ERR_ENCRYPTED=5,L.MEDIA_ERR_CUSTOM=100,L.defaultMessages={1:"You aborted the media playback",2:"A network error caused the media download to fail.",3:"A media error caused playback to be aborted. The media could be corrupt or your browser does not support this format.",4:"An unsupported error occurred. The server or network failed, or your browser does not support this format.",5:"The media is encrypted and there are no keys to decrypt it."};var f=L;var et=e=>e==null,O=(e,t)=>et(t)?!1:e in t,K={ANY:"any",MUTED:"muted"},_={ON_DEMAND:"on-demand",LIVE:"live",UNKNOWN:"unknown"},X={MSE:"mse",NATIVE:"native"},S={HEADER:"header",QUERY:"query",NONE:"none"},jt=Object.values(S),A={M3U8:"application/vnd.apple.mpegurl",MP4:"video/mp4"},W={HLS:A.M3U8},Jt=Object.keys(W),qt=[...Object.values(A),"hls","HLS"],Gt={upTo720p:"720p",upTo1080p:"1080p",upTo1440p:"1440p",upTo2160p:"2160p"},Xt={noLessThan480p:"480p",noLessThan540p:"540p",noLessThan720p:"720p",noLessThan1080p:"1080p",noLessThan1440p:"1440p",noLessThan2160p:"2160p"},zt={DESCENDING:"desc"};var tt="en",Y={code:tt};var v=(e,t,r,n,o=e)=>{o.addEventListener(t,r,n),e.addEventListener("teardown",()=>{o.removeEventListener(t,r)},{once:!0})};function fe(e,t,r){t&&r>t&&(r=t);for(let n=0;n<e.length;n++)if(e.start(n)<=r&&e.end(n)>=r)return!0;return!1}var F=e=>{let t=e.indexOf("?");if(t<0)return[e];let r=e.slice(0,t),n=e.slice(t);return[r,n]},U=e=>{let{type:t}=e;if(t){let r=t.toUpperCase();return O(r,W)?W[r]:t}return rt(e)},Q=e=>e==="VOD"?_.ON_DEMAND:_.LIVE,Z=e=>e==="EVENT"?Number.POSITIVE_INFINITY:e==="VOD"?Number.NaN:0,rt=e=>{let{src:t}=e;if(!t)return"";let r="";try{r=new URL(t).pathname}catch{console.error("invalid url")}let n=r.lastIndexOf(".");if(n<0)return ot(e)?A.M3U8:"";let a=r.slice(n+1).toUpperCase();return O(a,A)?A[a]:""},nt="mux.com",ot=({src:e,customDomain:t=nt})=>{let r;try{r=new URL(`${e}`)}catch{return!1}let n=r.protocol==="https:",o=r.hostname===`stream.${t}`.toLowerCase(),a=r.pathname.split("/"),i=a.length===2,c=!(a!=null&&a[1].includes("."));return n&&o&&i&&c},ee=e=>{let t=(e!=null?e:"").split(".")[1];if(t)try{let r=t.replace(/-/g,"+").replace(/_/g,"/"),n=decodeURIComponent(atob(r).split("").map(function(o){return"%"+("00"+o.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(n)}catch{return}},Te=({exp:e},t=Date.now())=>!e||e*1e3<t,ye=({sub:e},t)=>e!==t,me=({aud:e},t)=>!e,Ee=({aud:e},t)=>e!==t,ge="en";function x(e,t=!0){var o,a;let r=t&&(a=(o=Y)==null?void 0:o[e])!=null?a:e,n=t?Y.code:ge;return new z(r,n)}var z=class{constructor(t,r=(n=>(n=Y)!=null?n:ge)()){this.message=t,this.locale=r}format(t){return this.message.replace(/\{(\w+)\}/g,(r,n)=>{var o;return(o=t[n])!=null?o:""})}toString(){return this.message}};var at=Object.values(K),Me=e=>typeof e=="boolean"||typeof e=="string"&&at.includes(e),xe=(e,t,r)=>{let{autoplay:n}=e,o=!1,a=!1,i=Me(n)?n:!!n,c=()=>{o||v(t,"playing",()=>{o=!0},{once:!0})};if(c(),v(t,"loadstart",()=>{o=!1,c(),te(t,i)},{once:!0}),v(t,"loadstart",()=>{r||(e.streamType&&e.streamType!==_.UNKNOWN?a=e.streamType===_.LIVE:a=!Number.isFinite(t.duration)),te(t,i)},{once:!0}),r&&r.once(g.Events.LEVEL_LOADED,(u,s)=>{var p;e.streamType&&e.streamType!==_.UNKNOWN?a=e.streamType===_.LIVE:a=(p=s.details.live)!=null?p:!1}),!i){let u=()=>{!a||Number.isFinite(e.startTime)||(r!=null&&r.liveSyncPosition?t.currentTime=r.liveSyncPosition:Number.isFinite(t.seekable.end(0))&&(t.currentTime=t.seekable.end(0)))};r&&v(t,"play",()=>{t.preload==="metadata"?r.once(g.Events.LEVEL_UPDATED,u):u()},{once:!0})}return u=>{o||(i=Me(u)?u:!!u,te(t,i))}},te=(e,t)=>{if(!t)return;let r=e.muted,n=()=>e.muted=r;switch(t){case K.ANY:e.play().catch(()=>{e.muted=!0,e.play().catch(n)});break;case K.MUTED:e.muted=!0,e.play().catch(n);break;default:e.play().catch(()=>{});break}};var Re=({preload:e,src:t},r,n)=>{let o=p=>{p!=null&&["","none","metadata","auto"].includes(p)?r.setAttribute("preload",p):r.removeAttribute("preload")};if(!n)return o(e),o;let a=!1,i=!1,c=n.config.maxBufferLength,d=n.config.maxBufferSize,u=p=>{o(p);let l=p!=null?p:r.preload;i||l==="none"||(l==="metadata"?(n.config.maxBufferLength=1,n.config.maxBufferSize=1):(n.config.maxBufferLength=c,n.config.maxBufferSize=d),s())},s=()=>{!a&&t&&(a=!0,n.loadSource(t))};return v(r,"play",()=>{i=!0,n.config.maxBufferLength=c,n.config.maxBufferSize=d,s()},{once:!0}),u(e),u};function De(e,t){var c;if(!("videoTracks"in e))return;let r=new WeakMap;t.on(g.Events.MANIFEST_PARSED,function(d,u){i();let s=e.addVideoTrack("main");s.selected=!0;for(let[p,l]of u.levels.entries()){let T=s.addRendition(l.url[0],l.width,l.height,l.videoCodec,l.bitrate);r.set(l,`${p}`),T.id=`${p}`}}),t.on(g.Events.AUDIO_TRACKS_UPDATED,function(d,u){a();for(let s of u.audioTracks){let p=s.default?"main":"alternative",l=e.addAudioTrack(p,s.name,s.lang);l.id=`${s.id}`,s.default&&(l.enabled=!0)}}),e.audioTracks.addEventListener("change",()=>{var s;let d=+((s=[...e.audioTracks].find(p=>p.enabled))==null?void 0:s.id),u=t.audioTracks.map(p=>p.id);d!=t.audioTrack&&u.includes(d)&&(t.audioTrack=d)}),t.on(g.Events.LEVELS_UPDATED,function(d,u){var l;let s=e.videoTracks[(l=e.videoTracks.selectedIndex)!=null?l:0];if(!s)return;let p=u.levels.map(T=>r.get(T));for(let T of e.videoRenditions)T.id&&!p.includes(T.id)&&s.removeRendition(T)});let n=d=>{let u=d.target.selectedIndex;u!=t.nextLevel&&(t.nextLevel=u)};(c=e.videoRenditions)==null||c.addEventListener("change",n);let o=()=>{for(let d of e.videoTracks)e.removeVideoTrack(d)},a=()=>{for(let d of e.audioTracks)e.removeAudioTrack(d)},i=()=>{o(),a()};t.once(g.Events.DESTROYING,i)}var re=e=>"time"in e?e.time:e.startTime;function be(e,t){t.on(g.Events.NON_NATIVE_TEXT_TRACKS_FOUND,(o,{tracks:a})=>{a.forEach(i=>{var s,p;let c=(s=i.subtitleTrack)!=null?s:i.closedCaptions,d=t.subtitleTracks.findIndex(({lang:l,name:T,type:m})=>l==(c==null?void 0:c.lang)&&T===i.label&&m.toLowerCase()===i.kind),u=((p=i._id)!=null?p:i.default)?"default":`${i.kind}${d}`;ne(e,i.kind,i.label,c==null?void 0:c.lang,u,i.default)})});let r=()=>{if(!t.subtitleTracks.length)return;let o=Array.from(e.textTracks).find(c=>c.id&&c.mode==="showing"&&["subtitles","captions"].includes(c.kind));if(!o)return;let a=t.subtitleTracks[t.subtitleTrack],i=a?a.default?"default":`${t.subtitleTracks[t.subtitleTrack].type.toLowerCase()}${t.subtitleTrack}`:void 0;if(t.subtitleTrack<0||(o==null?void 0:o.id)!==i){let c=t.subtitleTracks.findIndex(({lang:d,name:u,type:s,default:p})=>o.id==="default"&&p||d==o.language&&u===o.label&&s.toLowerCase()===o.kind);t.subtitleTrack=c}(o==null?void 0:o.id)===i&&o.cues&&Array.from(o.cues).forEach(c=>{o.addCue(c)})};e.textTracks.addEventListener("change",r),t.on(g.Events.CUES_PARSED,(o,{track:a,cues:i})=>{let c=e.textTracks.getTrackById(a);if(!c)return;let d=c.mode==="disabled";d&&(c.mode="hidden"),i.forEach(u=>{var s;(s=c.cues)!=null&&s.getCueById(u.id)||c.addCue(u)}),d&&(c.mode="disabled")}),t.once(g.Events.DESTROYING,()=>{e.textTracks.removeEventListener("change",r),e.querySelectorAll("track[data-removeondestroy]").forEach(a=>{a.remove()})});let n=()=>{Array.from(e.textTracks).forEach(o=>{var a,i;if(!["subtitles","caption"].includes(o.kind)&&(o.label==="thumbnails"||o.kind==="chapters")){if(!((a=o.cues)!=null&&a.length)){let c="track";o.kind&&(c+=`[kind="${o.kind}"]`),o.label&&(c+=`[label="${o.label}"]`);let d=e.querySelector(c),u=(i=d==null?void 0:d.getAttribute("src"))!=null?i:"";d==null||d.removeAttribute("src"),setTimeout(()=>{d==null||d.setAttribute("src",u)},0)}o.mode!=="hidden"&&(o.mode="hidden")}})};t.once(g.Events.MANIFEST_LOADED,n),t.once(g.Events.MEDIA_ATTACHED,n)}function ne(e,t,r,n,o,a){let i=document.createElement("track");return i.kind=t,i.label=r,n&&(i.srclang=n),o&&(i.id=o),a&&(i.default=!0),i.track.mode=["subtitles","captions"].includes(t)?"disabled":"hidden",i.setAttribute("data-removeondestroy",""),e.append(i),i.track}function st(e,t){let r=Array.prototype.find.call(e.querySelectorAll("track"),n=>n.track===t);r==null||r.remove()}function w(e,t,r){var n;return(n=Array.from(e.querySelectorAll("track")).find(o=>o.track.label===t&&o.track.kind===r))==null?void 0:n.track}async function Ce(e,t,r,n){let o=w(e,r,n);return o||(o=ne(e,n,r),o.mode="hidden",await new Promise(a=>setTimeout(()=>a(void 0),0))),o.mode!=="hidden"&&(o.mode="hidden"),[...t].sort((a,i)=>re(i)-re(a)).forEach(a=>{var d,u;let i=a.value,c=re(a);if("endTime"in a&&a.endTime!=null)o==null||o.addCue(new VTTCue(c,a.endTime,n==="chapters"?i:JSON.stringify(i!=null?i:null)));else{let s=Array.prototype.findIndex.call(o==null?void 0:o.cues,m=>m.startTime>=c),p=(d=o==null?void 0:o.cues)==null?void 0:d[s],l=p?p.startTime:Number.isFinite(e.duration)?e.duration:Number.MAX_SAFE_INTEGER,T=(u=o==null?void 0:o.cues)==null?void 0:u[s-1];T&&(T.endTime=c),o==null||o.addCue(new VTTCue(c,l,n==="chapters"?i:JSON.stringify(i!=null?i:null)))}}),e.textTracks.dispatchEvent(new Event("change",{bubbles:!0,composed:!0})),o}var oe="cuepoints",ve=Object.freeze({label:oe});async function Pe(e,t,r=ve){return Ce(e,t,r.label,"metadata")}var $=e=>({time:e.startTime,value:JSON.parse(e.text)});function it(e,t={label:oe}){let r=w(e,t.label,"metadata");return r!=null&&r.cues?Array.from(r.cues,n=>$(n)):[]}function _e(e,t={label:oe}){var a,i;let r=w(e,t.label,"metadata");if(!((a=r==null?void 0:r.activeCues)!=null&&a.length))return;if(r.activeCues.length===1)return $(r.activeCues[0]);let{currentTime:n}=e,o=Array.prototype.find.call((i=r.activeCues)!=null?i:[],({startTime:c,endTime:d})=>c<=n&&d>n);return $(o||r.activeCues[0])}async function ke(e,t=ve){return new Promise(r=>{v(e,"loadstart",async()=>{let n=await Pe(e,[],t);v(e,"cuechange",()=>{let o=_e(e);if(o){let a=new CustomEvent("cuepointchange",{composed:!0,bubbles:!0,detail:o});e.dispatchEvent(a)}},{},n),r(n)})})}var ae="chapters",he=Object.freeze({label:ae}),B=e=>({startTime:e.startTime,endTime:e.endTime,value:e.text});async function Le(e,t,r=he){return Ce(e,t,r.label,"chapters")}function ct(e,t={label:ae}){var n;let r=w(e,t.label,"chapters");return(n=r==null?void 0:r.cues)!=null&&n.length?Array.from(r.cues,o=>B(o)):[]}function Ne(e,t={label:ae}){var a,i;let r=w(e,t.label,"chapters");if(!((a=r==null?void 0:r.activeCues)!=null&&a.length))return;if(r.activeCues.length===1)return B(r.activeCues[0]);let{currentTime:n}=e,o=Array.prototype.find.call((i=r.activeCues)!=null?i:[],({startTime:c,endTime:d})=>c<=n&&d>n);return B(o||r.activeCues[0])}async function Ae(e,t=he){return new Promise(r=>{v(e,"loadstart",async()=>{let n=await Le(e,[],t);v(e,"cuechange",()=>{let o=Ne(e);if(o){let a=new CustomEvent("chapterchange",{composed:!0,bubbles:!0,detail:o});e.dispatchEvent(a)}},{},n),r(n)})})}function ut(e,t){if(t){let r=t.playingDate;if(r!=null)return new Date(r.getTime()-e.currentTime*1e3)}return typeof e.getStartDate=="function"?e.getStartDate():new Date(NaN)}function dt(e,t){if(t&&t.playingDate)return t.playingDate;if(typeof e.getStartDate=="function"){let r=e.getStartDate();return new Date(r.getTime()+e.currentTime*1e3)}return new Date(NaN)}var se={VIDEO:"v",THUMBNAIL:"t",STORYBOARD:"s",DRM:"d"},lt=e=>{if(e===C.VIDEO)return se.VIDEO;if(e===C.DRM)return se.DRM},pt=(e,t)=>{var o,a;let r=V(e),n=`${r}Token`;return(o=t.tokens)!=null&&o[r]?(a=t.tokens)==null?void 0:a[r]:O(n,t)?t[n]:void 0},H=(e,t,r,n,o=!1,a=!(i=>(i=globalThis.navigator)==null?void 0:i.onLine)())=>{var M,h;if(a){let E=x("Your device appears to be offline",o),b=void 0,y=f.MEDIA_ERR_NETWORK,k=new f(E,y,!1,b);return k.errorCategory=t,k.muxCode=D.NETWORK_OFFLINE,k.data=e,k}let c="status"in e?e.status:e.code,d=Date.now(),u=f.MEDIA_ERR_NETWORK;if(c===200)return;let s=V(t),p=pt(t,r),l=lt(t),[T]=F((M=r.playbackId)!=null?M:"");if(!c||!T)return;let m=ee(p);if(p&&!m){let E=x("The {tokenNamePrefix}-token provided is invalid or malformed.",o).format({tokenNamePrefix:s}),b=x("Compact JWT string: {token}",o).format({token:p}),y=new f(E,u,!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_TOKEN_MALFORMED,y.data=e,y}if(c>=500){let E=new f("",u,n!=null?n:!0);return E.errorCategory=t,E.muxCode=D.NETWORK_UNKNOWN_ERROR,E}if(c===403)if(m){if(Te(m,d)){let E={timeStyle:"medium",dateStyle:"medium"},b=x("The video\u2019s secured {tokenNamePrefix}-token has expired.",o).format({tokenNamePrefix:s}),y=x("Expired at: {expiredDate}. Current time: {currentDate}.",o).format({expiredDate:new Intl.DateTimeFormat("en",E).format((h=m.exp)!=null?h:0*1e3),currentDate:new Intl.DateTimeFormat("en",E).format(d)}),k=new f(b,u,!0,y);return k.errorCategory=t,k.muxCode=D.NETWORK_TOKEN_EXPIRED,k.data=e,k}if(ye(m,T)){let E=x("The video\u2019s playback ID does not match the one encoded in the {tokenNamePrefix}-token.",o).format({tokenNamePrefix:s}),b=x("Specified playback ID: {playbackId} and the playback ID encoded in the {tokenNamePrefix}-token: {tokenPlaybackId}",o).format({tokenNamePrefix:s,playbackId:T,tokenPlaybackId:m.sub}),y=new f(E,u,!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_TOKEN_SUB_MISMATCH,y.data=e,y}if(me(m,l)){let E=x("The {tokenNamePrefix}-token is formatted with incorrect information.",o).format({tokenNamePrefix:s}),b=x("The {tokenNamePrefix}-token has no aud value. aud value should be {expectedAud}.",o).format({tokenNamePrefix:s,expectedAud:l}),y=new f(E,u,!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_TOKEN_AUD_MISSING,y.data=e,y}if(Ee(m,l)){let E=x("The {tokenNamePrefix}-token is formatted with incorrect information.",o).format({tokenNamePrefix:s}),b=x("The {tokenNamePrefix}-token has an incorrect aud value: {aud}. aud value should be {expectedAud}.",o).format({tokenNamePrefix:s,expectedAud:l,aud:m.aud}),y=new f(E,u,!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_TOKEN_AUD_MISMATCH,y.data=e,y}}else{let E=x("Authorization error trying to access this {category} URL. If this is a signed URL, you might need to provide a {tokenNamePrefix}-token.",o).format({tokenNamePrefix:s,category:t}),b=x("Specified playback ID: {playbackId}",o).format({playbackId:T}),y=new f(E,u,n!=null?n:!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_TOKEN_MISSING,y.data=e,y}if(c===412){let E=x("This playback-id may belong to a live stream that is not currently active or an asset that is not ready.",o),b=x("Specified playback ID: {playbackId}",o).format({playbackId:T}),y=new f(E,u,n!=null?n:!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_NOT_READY,y.streamType=r.streamType===_.LIVE?"live":r.streamType===_.ON_DEMAND?"on-demand":"unknown",y.data=e,y}if(c===404){let E=x("This URL or playback-id does not exist. You may have used an Asset ID or an ID from a different resource.",o),b=x("Specified playback ID: {playbackId}",o).format({playbackId:T}),y=new f(E,u,n!=null?n:!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_NOT_FOUND,y.data=e,y}if(c===400){let E=x("The URL or playback-id was invalid. You may have used an invalid value as a playback-id."),b=x("Specified playback ID: {playbackId}",o).format({playbackId:T}),y=new f(E,u,n!=null?n:!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_INVALID_URL,y.data=e,y}let R=new f("",u,n!=null?n:!0);return R.errorCategory=t,R.muxCode=D.NETWORK_UNKNOWN_ERROR,R.data=e,R};var Ie=g.DefaultConfig.capLevelController,j=class j extends Ie{constructor(t){super(t)}get levels(){var t;return(t=this.hls.levels)!=null?t:[]}getValidLevels(t){return this.levels.filter((r,n)=>this.isLevelAllowed(r)&&n<=t)}getMaxLevel(t){let r=super.getMaxLevel(t),n=this.getValidLevels(t);if(!n[r])return r;let o=Math.min(n[r].width,n[r].height),a=j.minMaxResolution;return o>=a?r:Ie.getMaxLevelByMediaSize(n,a*(16/9),a)}};j.minMaxResolution=720;var ie=j,Se=ie;var J={FAIRPLAY:"fairplay",PLAYREADY:"playready",WIDEVINE:"widevine"},ft=e=>{if(e.includes("fps"))return J.FAIRPLAY;if(e.includes("playready"))return J.PLAYREADY;if(e.includes("widevine"))return J.WIDEVINE},Tt=e=>{let t=e.split(`
`).find((r,n,o)=>n&&o[n-1].startsWith("#EXT-X-STREAM-INF"));return fetch(t).then(r=>r.status!==200?Promise.reject(r):r.text())},yt=e=>{let t=e.split(`
`).filter(n=>n.startsWith("#EXT-X-SESSION-DATA"));if(!t.length)return{};let r={};for(let n of t){let o=Et(n),a=o["DATA-ID"];a&&(r[a]={...o})}return{sessionData:r}},mt=/([A-Z0-9-]+)="?(.*?)"?(?:,|$)/g;function Et(e){let t=[...e.matchAll(mt)];return Object.fromEntries(t.map(([,r,n])=>[r,n]))}var gt=e=>{var c,d,u;let t=e.split(`
`),n=(d=((c=t.find(s=>s.startsWith("#EXT-X-PLAYLIST-TYPE")))!=null?c:"").split(":")[1])==null?void 0:d.trim(),o=Q(n),a=Z(n),i;if(o===_.LIVE){let s=t.find(l=>l.startsWith("#EXT-X-PART-INF"));if(!!s)i=+s.split(":")[1].split("=")[1]*2;else{let l=t.find(R=>R.startsWith("#EXT-X-TARGETDURATION")),T=(u=l==null?void 0:l.split(":"))==null?void 0:u[1];i=+(T!=null?T:6)*3}}return{streamType:o,targetLiveWindow:a,liveEdgeStartOffset:i}},Mt=async(e,t)=>{if(t===A.MP4)return{streamType:_.ON_DEMAND,targetLiveWindow:Number.NaN,liveEdgeStartOffset:void 0,sessionData:void 0};if(t===A.M3U8){let r=await fetch(e);if(!r.ok)return Promise.reject(r);let n=await r.text(),o=await Tt(n);return{...yt(n),...gt(o)}}return console.error(`Media type ${t} is an unrecognized or unsupported type for src ${e}.`),{streamType:void 0,targetLiveWindow:void 0,liveEdgeStartOffset:void 0,sessionData:void 0}},xt=async(e,t,r=U({src:e}))=>{var d,u,s,p;let{streamType:n,targetLiveWindow:o,liveEdgeStartOffset:a,sessionData:i}=await Mt(e,r),c=i==null?void 0:i["com.apple.hls.chapters"];(c!=null&&c.URI||c!=null&&c.VALUE.toLocaleLowerCase().startsWith("http"))&&de((d=c.URI)!=null?d:c.VALUE,t),((u=P.get(t))!=null?u:{}).liveEdgeStartOffset=a,((s=P.get(t))!=null?s:{}).targetLiveWindow=o,t.dispatchEvent(new CustomEvent("targetlivewindowchange",{composed:!0,bubbles:!0})),((p=P.get(t))!=null?p:{}).streamType=n,t.dispatchEvent(new CustomEvent("streamtypechange",{composed:!0,bubbles:!0}))},de=async(e,t)=>{var r,n;try{let o=await fetch(e);if(!o.ok)throw new Error(`Failed to fetch Mux metadata: ${o.status} ${o.statusText}`);let a=await o.json(),i={};if(!((r=a==null?void 0:a[0])!=null&&r.metadata))return;for(let d of a[0].metadata)d.key&&d.value&&(i[d.key]=d.value);((n=P.get(t))!=null?n:{}).metadata=i;let c=new CustomEvent("muxmetadata");t.dispatchEvent(c)}catch(o){console.error(o)}},Rt=e=>{var i;let t=e.type,r=Q(t),n=Z(t),o,a=!!((i=e.partList)!=null&&i.length);return r===_.LIVE&&(o=a?e.partTarget*2:e.targetduration*3),{streamType:r,targetLiveWindow:n,liveEdgeStartOffset:o,lowLatency:a}},Dt=(e,t,r)=>{var c,d,u,s,p,l,T,m;let{streamType:n,targetLiveWindow:o,liveEdgeStartOffset:a,lowLatency:i}=Rt(e);if(n===_.LIVE){i?(r.config.backBufferLength=(c=r.userConfig.backBufferLength)!=null?c:4,r.config.maxFragLookUpTolerance=(d=r.userConfig.maxFragLookUpTolerance)!=null?d:.001,r.config.abrBandWidthUpFactor=(u=r.userConfig.abrBandWidthUpFactor)!=null?u:r.config.abrBandWidthFactor):r.config.backBufferLength=(s=r.userConfig.backBufferLength)!=null?s:8;let R=Object.freeze({get length(){return t.seekable.length},start(M){return t.seekable.start(M)},end(M){var h;return M>this.length||M<0||Number.isFinite(t.duration)?t.seekable.end(M):(h=r.liveSyncPosition)!=null?h:t.seekable.end(M)}});((p=P.get(t))!=null?p:{}).seekable=R}((l=P.get(t))!=null?l:{}).liveEdgeStartOffset=a,((T=P.get(t))!=null?T:{}).targetLiveWindow=o,t.dispatchEvent(new CustomEvent("targetlivewindowchange",{composed:!0,bubbles:!0})),((m=P.get(t))!=null?m:{}).streamType=n,t.dispatchEvent(new CustomEvent("streamtypechange",{composed:!0,bubbles:!0}))},Oe,Ue,bt=(Ue=(Oe=globalThis==null?void 0:globalThis.navigator)==null?void 0:Oe.userAgent)!=null?Ue:"",He,Ve,Ke,Ct=(Ke=(Ve=(He=globalThis==null?void 0:globalThis.navigator)==null?void 0:He.userAgentData)==null?void 0:Ve.platform)!=null?Ke:"",vt=bt.toLowerCase().includes("android")||["x11","android"].some(e=>Ct.toLowerCase().includes(e)),P=new WeakMap,I="mux.com",We,Ye,Fe=(Ye=(We=g).isSupported)==null?void 0:Ye.call(We),Pt=vt,Wr=()=>ue.utils.now(),_t=ue.utils.generateUUID,Yr=({playbackId:e,customDomain:t=I,maxResolution:r,minResolution:n,renditionOrder:o,programStartTime:a,programEndTime:i,assetStartTime:c,assetEndTime:d,playbackToken:u,tokens:{playback:s=u}={},extraSourceParams:p={}}={})=>{if(!e)return;let[l,T=""]=F(e),m=new URL(`https://stream.${t}/${l}.m3u8${T}`);return s||m.searchParams.has("token")?(m.searchParams.forEach((R,M)=>{M!="token"&&m.searchParams.delete(M)}),s&&m.searchParams.set("token",s)):(r&&m.searchParams.set("max_resolution",r),n&&(m.searchParams.set("min_resolution",n),r&&+r.slice(0,-1)<+n.slice(0,-1)&&console.error("minResolution must be <= maxResolution","minResolution",n,"maxResolution",r)),o&&m.searchParams.set("rendition_order",o),a&&m.searchParams.set("program_start_time",`${a}`),i&&m.searchParams.set("program_end_time",`${i}`),c&&m.searchParams.set("asset_start_time",`${c}`),d&&m.searchParams.set("asset_end_time",`${d}`),Object.entries(p).forEach(([R,M])=>{M!=null&&m.searchParams.set(R,M)})),m.toString()},G=e=>{if(!e)return;let[t]=e.split("?");return t||void 0},$e=e=>{if(!e||!e.startsWith("https://stream."))return;let[t]=new URL(e).pathname.slice(1).split(/\.m3u8|\//);return t||void 0},kt=e=>{var t,r,n;return(t=e==null?void 0:e.metadata)!=null&&t.video_id?e.metadata.video_id:Xe(e)&&(n=(r=G(e.playbackId))!=null?r:$e(e.src))!=null?n:e.src},ht=e=>{var t;return(t=P.get(e))==null?void 0:t.error},Fr=e=>{var t;return(t=P.get(e))==null?void 0:t.metadata},we=e=>{var t,r;return(r=(t=P.get(e))==null?void 0:t.streamType)!=null?r:_.UNKNOWN},$r=e=>{var t,r;return(r=(t=P.get(e))==null?void 0:t.targetLiveWindow)!=null?r:Number.NaN},Be=e=>{var t,r;return(r=(t=P.get(e))==null?void 0:t.seekable)!=null?r:e.seekable},Br=e=>{var n;let t=(n=P.get(e))==null?void 0:n.liveEdgeStartOffset;if(typeof t!="number")return Number.NaN;let r=Be(e);return r.length?r.end(r.length-1)-t:Number.NaN},le=.034,Lt=(e,t,r=le)=>Math.abs(e-t)<=r,je=(e,t,r=le)=>e>t||Lt(e,t,r),Nt=(e,t=le)=>e.paused&&je(e.currentTime,e.duration,t),Je=(e,t)=>{var u,s,p;if(!t||!e.buffered.length)return;if(e.readyState>2)return!1;let r=t.currentLevel>=0?(s=(u=t.levels)==null?void 0:u[t.currentLevel])==null?void 0:s.details:(p=t.levels.find(l=>!!l.details))==null?void 0:p.details;if(!r||r.live)return;let{fragments:n}=r;if(!(n!=null&&n.length))return;if(e.currentTime<e.duration-(r.targetduration+.5))return!1;let o=n[n.length-1];if(e.currentTime<=o.start)return!1;let a=o.start+o.duration/2,i=e.buffered.start(e.buffered.length-1),c=e.buffered.end(e.buffered.length-1);return a>i&&a<c},At=(e,t)=>e.ended||e.loop?e.ended:t&&Je(e,t)?!0:Nt(e),jr=(e,t,r)=>{It(t,r,e);let{metadata:n={}}=e,{view_session_id:o=_t()}=n,a=kt(e);n.view_session_id=o,n.video_id=a,e.metadata=n;let i=s=>{var p;(p=t.mux)==null||p.emit("hb",{view_drm_type:s})};e.drmTypeCb=i,P.set(t,{retryCount:0});let c=St(e,t),d=Re(e,t,c);e!=null&&e.muxDataKeepSession&&(t!=null&&t.mux)&&!t.mux.deleted?c&&t.mux.addHLSJS({hlsjs:c,Hls:c?g:void 0}):Kt(e,t,c),Wt(e,t,c),ke(t),Ae(t);let u=xe(e,t,c);return{engine:c,setAutoplay:u,setPreload:d}},It=(e,t,r)=>{let n=t==null?void 0:t.engine;e!=null&&e.mux&&!e.mux.deleted&&(r!=null&&r.muxDataKeepSession?n&&e.mux.removeHLSJS():(e.mux.destroy(),delete e.mux)),n&&(n.detachMedia(),n.destroy()),e&&(e.hasAttribute("src")&&(e.removeAttribute("src"),e.load()),e.removeEventListener("error",Qe),e.removeEventListener("error",ce),e.removeEventListener("durationchange",ze),P.delete(e),e.dispatchEvent(new Event("teardown")))};function qe(e,t){var u;let r=U(e);if(!(r===A.M3U8))return!0;let o=!r||((u=t.canPlayType(r))!=null?u:!0),{preferPlayback:a}=e,i=a===X.MSE,c=a===X.NATIVE;return o&&(c||!(Fe&&(i||Pt)))}var St=(e,t)=>{let{debug:r,streamType:n,startTime:o=-1,metadata:a,preferCmcd:i,_hlsConfig:c={}}=e,u=U(e)===A.M3U8,s=qe(e,t);if(u&&!s&&Fe){let p={backBufferLength:30,renderTextTracksNatively:!1,liveDurationInfinity:!0,capLevelToPlayerSize:!0,capLevelOnFPSDrop:!0},l=wt(n),T=Ot(e),m=[S.QUERY,S.HEADER].includes(i)?{useHeaders:i===S.HEADER,sessionId:a==null?void 0:a.view_session_id,contentId:a==null?void 0:a.video_id}:void 0,R=new g({debug:r,startPosition:o,cmcd:m,xhrSetup:(M,h)=>{var y,k;if(i&&i!==S.QUERY)return;let E=new URL(h);if(!E.searchParams.has("CMCD"))return;let b=((k=(y=E.searchParams.get("CMCD"))==null?void 0:y.split(","))!=null?k:[]).filter(pe=>pe.startsWith("sid")||pe.startsWith("cid")).join(",");E.searchParams.set("CMCD",b),M.open("GET",E)},capLevelController:Se,...p,...l,...T,...c});return R.on(g.Events.MANIFEST_PARSED,async function(M,h){var b,y;let E=(b=h.sessionData)==null?void 0:b["com.apple.hls.chapters"];(E!=null&&E.URI||E!=null&&E.VALUE.toLocaleLowerCase().startsWith("http"))&&de((y=E==null?void 0:E.URI)!=null?y:E==null?void 0:E.VALUE,t)}),R}},wt=e=>e===_.LIVE?{backBufferLength:8}:{},Ot=e=>{let{tokens:{drm:t}={},playbackId:r,drmTypeCb:n}=e,o=G(r);return!t||!o?{}:{emeEnabled:!0,drmSystems:{"com.apple.fps":{licenseUrl:q(e,"fairplay"),serverCertificateUrl:Ge(e,"fairplay")},"com.widevine.alpha":{licenseUrl:q(e,"widevine")},"com.microsoft.playready":{licenseUrl:q(e,"playready")}},requestMediaKeySystemAccessFunc:(a,i)=>(a==="com.widevine.alpha"&&(i=[...i.map(c=>{var u;let d=(u=c.videoCapabilities)==null?void 0:u.map(s=>({...s,robustness:"HW_SECURE_ALL"}));return{...c,videoCapabilities:d}}),...i]),navigator.requestMediaKeySystemAccess(a,i).then(c=>{let d=ft(a);return n==null||n(d),c}))}},Ut=async e=>{let t=await fetch(e);return t.status!==200?Promise.reject(t):await t.arrayBuffer()},Ht=async(e,t)=>{let r=await fetch(t,{method:"POST",headers:{"Content-type":"application/octet-stream"},body:e});if(r.status!==200)return Promise.reject(r);let n=await r.arrayBuffer();return new Uint8Array(n)},Vt=(e,t)=>{v(t,"encrypted",async n=>{try{let o=n.initDataType;if(o!=="skd"){console.error(`Received unexpected initialization data type "${o}"`);return}if(!t.mediaKeys){let u=await navigator.requestMediaKeySystemAccess("com.apple.fps",[{initDataTypes:[o],videoCapabilities:[{contentType:"application/vnd.apple.mpegurl",robustness:""}],distinctiveIdentifier:"not-allowed",persistentState:"not-allowed",sessionTypes:["temporary"]}]).then(p=>{var l;return(l=e.drmTypeCb)==null||l.call(e,J.FAIRPLAY),p}).catch(()=>{let p=x("Cannot play DRM-protected content with current security configuration on this browser. Try playing in another browser."),l=new f(p,f.MEDIA_ERR_ENCRYPTED,!0);l.errorCategory=C.DRM,l.muxCode=D.ENCRYPTED_UNSUPPORTED_KEY_SYSTEM,N(t,l)});if(!u)return;let s=await u.createMediaKeys();try{let p=await Ut(Ge(e,"fairplay")).catch(l=>{if(l instanceof Response){let T=H(l,C.DRM,e);return console.error("mediaError",T==null?void 0:T.message,T==null?void 0:T.context),T?Promise.reject(T):Promise.reject(new Error("Unexpected error in app cert request"))}return Promise.reject(l)});await s.setServerCertificate(p).catch(()=>{let l=x("Your server certificate failed when attempting to set it. This may be an issue with a no longer valid certificate."),T=new f(l,f.MEDIA_ERR_ENCRYPTED,!0);return T.errorCategory=C.DRM,T.muxCode=D.ENCRYPTED_UPDATE_SERVER_CERT_FAILED,Promise.reject(T)})}catch(p){N(t,p);return}await t.setMediaKeys(s)}let a=n.initData;if(a==null){console.error(`Could not start encrypted playback due to missing initData in ${n.type} event`);return}let i=t.mediaKeys.createSession();i.addEventListener("keystatuseschange",()=>{i.keyStatuses.forEach(u=>{let s;if(u==="internal-error"){let p=x("The DRM Content Decryption Module system had an internal failure. Try reloading the page, upading your browser, or playing in another browser.");s=new f(p,f.MEDIA_ERR_ENCRYPTED,!0),s.errorCategory=C.DRM,s.muxCode=D.ENCRYPTED_CDM_ERROR}else if(u==="output-restricted"||u==="output-downscaled"){let p=x("DRM playback is being attempted in an environment that is not sufficiently secure. User may see black screen.");s=new f(p,f.MEDIA_ERR_ENCRYPTED,!1),s.errorCategory=C.DRM,s.muxCode=D.ENCRYPTED_OUTPUT_RESTRICTED}s&&N(t,s)})});let c=await Promise.all([i.generateRequest(o,a).catch(()=>{let u=x("Failed to generate a DRM license request. This may be an issue with the player or your protected content."),s=new f(u,f.MEDIA_ERR_ENCRYPTED,!0);s.errorCategory=C.DRM,s.muxCode=D.ENCRYPTED_GENERATE_REQUEST_FAILED,N(t,s)}),new Promise(u=>{i.addEventListener("message",s=>{u(s.message)},{once:!0})})]).then(([,u])=>u),d=await Ht(c,q(e,"fairplay")).catch(u=>{if(u instanceof Response){let s=H(u,C.DRM,e);return console.error("mediaError",s==null?void 0:s.message,s==null?void 0:s.context),s?Promise.reject(s):Promise.reject(new Error("Unexpected error in license key request"))}return Promise.reject(u)});await i.update(d).catch(()=>{let u=x("Failed to update DRM license. This may be an issue with the player or your protected content."),s=new f(u,f.MEDIA_ERR_ENCRYPTED,!0);return s.errorCategory=C.DRM,s.muxCode=D.ENCRYPTED_UPDATE_LICENSE_FAILED,Promise.reject(s)})}catch(o){N(t,o);return}})},q=({playbackId:e,tokens:{drm:t}={},customDomain:r=I},n)=>{let o=G(e);return`https://license.${r.toLocaleLowerCase().endsWith(I)?r:I}/license/${n}/${o}?token=${t}`},Ge=({playbackId:e,tokens:{drm:t}={},customDomain:r=I},n)=>{let o=G(e);return`https://license.${r.toLocaleLowerCase().endsWith(I)?r:I}/appcert/${n}/${o}?token=${t}`},Xe=({playbackId:e,src:t,customDomain:r})=>{if(e)return!0;if(typeof t!="string")return!1;let n=window==null?void 0:window.location.href,o=new URL(t,n).hostname.toLocaleLowerCase();return o.includes(I)||!!r&&o.includes(r.toLocaleLowerCase())},Kt=(e,t,r)=>{var d;let{envKey:n,disableTracking:o,muxDataSDK:a=ue,muxDataSDKOptions:i={}}=e,c=Xe(e);if(!o&&(n||c)){let{playerInitTime:u,playerSoftwareName:s,playerSoftwareVersion:p,beaconCollectionDomain:l,debug:T,disableCookies:m}=e,R={...e.metadata,video_title:((d=e==null?void 0:e.metadata)==null?void 0:d.video_title)||void 0},M=h=>typeof h.player_error_code=="string"?!1:typeof e.errorTranslator=="function"?e.errorTranslator(h):h;a.monitor(t,{debug:T,beaconCollectionDomain:l,hlsjs:r,Hls:r?g:void 0,automaticErrorTracking:!1,errorTranslator:M,disableCookies:m,...i,data:{...n?{env_key:n}:{},player_software_name:s,player_software:s,player_software_version:p,player_init_time:u,...R}})}},Wt=(e,t,r)=>{var s,p;let n=qe(e,t),{src:o,customDomain:a=I}=e,i=()=>{t.ended||!At(t,r)||(Je(t,r)?t.currentTime=t.buffered.end(t.buffered.length-1):t.dispatchEvent(new Event("ended")))},c,d,u=()=>{let l=Be(t),T,m;l.length>0&&(T=l.start(0),m=l.end(0)),(d!==m||c!==T)&&t.dispatchEvent(new CustomEvent("seekablechange",{composed:!0})),c=T,d=m};if(v(t,"durationchange",u),t&&n){let l=U(e);if(typeof o=="string"){if(o.endsWith(".mp4")&&o.includes(a)){let R=$e(o),M=new URL(`https://stream.${a}/${R}/metadata.json`);de(M.toString(),t)}let T=()=>{if(we(t)!==_.LIVE||Number.isFinite(t.duration))return;let R=setInterval(u,1e3);t.addEventListener("teardown",()=>{clearInterval(R)},{once:!0}),v(t,"durationchange",()=>{Number.isFinite(t.duration)&&clearInterval(R)})},m=async()=>xt(o,t,l).then(T).catch(R=>{if(R instanceof Response){let M=H(R,C.VIDEO,e);if(M){N(t,M);return}}else R instanceof Error});if(t.preload==="none"){let R=()=>{m(),t.removeEventListener("loadedmetadata",M)},M=()=>{m(),t.removeEventListener("play",R)};v(t,"play",R,{once:!0}),v(t,"loadedmetadata",M,{once:!0})}else m();(s=e.tokens)!=null&&s.drm?Vt(e,t):v(t,"encrypted",()=>{let R=x("Attempting to play DRM-protected content without providing a DRM token."),M=new f(R,f.MEDIA_ERR_ENCRYPTED,!0);M.errorCategory=C.DRM,M.muxCode=D.ENCRYPTED_MISSING_TOKEN,N(t,M)},{once:!0}),t.setAttribute("src",o),e.startTime&&(((p=P.get(t))!=null?p:{}).startTime=e.startTime,t.addEventListener("durationchange",ze,{once:!0}))}else t.removeAttribute("src");t.addEventListener("error",Qe),t.addEventListener("error",ce),t.addEventListener("emptied",()=>{t.querySelectorAll("track[data-removeondestroy]").forEach(m=>{m.remove()})},{once:!0}),v(t,"pause",i),v(t,"seeked",i),v(t,"play",()=>{t.ended||je(t.currentTime,t.duration)&&(t.currentTime=t.seekable.length?t.seekable.start(0):0)})}else r&&o?(r.once(g.Events.LEVEL_LOADED,(l,T)=>{Dt(T.details,t,r),u(),we(t)===_.LIVE&&!Number.isFinite(t.duration)&&(r.on(g.Events.LEVEL_UPDATED,u),v(t,"durationchange",()=>{Number.isFinite(t.duration)&&r.off(g.Events.LEVELS_UPDATED,u)}))}),r.on(g.Events.ERROR,(l,T)=>{var R,M;let m=Yt(T,e);if(m.muxCode===D.NETWORK_NOT_READY){let E=(R=P.get(t))!=null?R:{},b=(M=E.retryCount)!=null?M:0;if(b<6){let y=b===0?5e3:6e4,k=new f(`Retrying in ${y/1e3} seconds...`,m.code,m.fatal);Object.assign(k,m),N(t,k),setTimeout(()=>{E.retryCount=b+1,T.details==="manifestLoadError"&&T.url&&r.loadSource(T.url)},y);return}else{E.retryCount=0;let y=new f('Try again later or <a href="#" onclick="window.location.reload(); return false;" style="color: #4a90e2;">click here to retry</a>',m.code,m.fatal);Object.assign(y,m),N(t,y);return}}N(t,m)}),r.on(g.Events.MANIFEST_LOADED,()=>{let l=P.get(t);l&&l.error&&(l.error=null,l.retryCount=0,t.dispatchEvent(new Event("emptied")),t.dispatchEvent(new Event("loadstart")))}),t.addEventListener("error",ce),v(t,"waiting",i),De(e,r),be(t,r),r.attachMedia(t)):console.error("It looks like the video you're trying to play will not work on this system! If possible, try upgrading to the newest versions of your browser or software.")};function ze(e){var n;let t=e.target,r=(n=P.get(t))==null?void 0:n.startTime;if(r&&fe(t.seekable,t.duration,r)){let o=t.preload==="auto";o&&(t.preload="none"),t.currentTime=r,o&&(t.preload="auto")}}async function Qe(e){if(!e.isTrusted)return;e.stopImmediatePropagation();let t=e.target;if(!(t!=null&&t.error))return;let{message:r,code:n}=t.error,o=new f(r,n);if(t.src&&n===f.MEDIA_ERR_SRC_NOT_SUPPORTED&&t.readyState===HTMLMediaElement.HAVE_NOTHING){setTimeout(()=>{var i;let a=(i=ht(t))!=null?i:t.error;(a==null?void 0:a.code)===f.MEDIA_ERR_SRC_NOT_SUPPORTED&&N(t,o)},500);return}if(t.src&&(n!==f.MEDIA_ERR_DECODE||n!==void 0))try{let{status:a}=await fetch(t.src);o.data={response:{code:a}}}catch{}N(t,o)}function N(e,t){var r;t.fatal&&(((r=P.get(e))!=null?r:{}).error=t,e.dispatchEvent(new CustomEvent("error",{detail:t})))}function ce(e){var n,o;if(!(e instanceof CustomEvent)||!(e.detail instanceof f))return;let t=e.target,r=e.detail;!r||!r.fatal||(((n=P.get(t))!=null?n:{}).error=r,(o=t.mux)==null||o.emit("error",{player_error_code:r.code,player_error_message:r.message,player_error_context:r.context}))}var Yt=(e,t)=>{var c,d,u;console.error("getErrorFromHlsErrorData()",e);let r={[g.ErrorTypes.NETWORK_ERROR]:f.MEDIA_ERR_NETWORK,[g.ErrorTypes.MEDIA_ERROR]:f.MEDIA_ERR_DECODE,[g.ErrorTypes.KEY_SYSTEM_ERROR]:f.MEDIA_ERR_ENCRYPTED},n=s=>[g.ErrorDetails.KEY_SYSTEM_LICENSE_REQUEST_FAILED,g.ErrorDetails.KEY_SYSTEM_SERVER_CERTIFICATE_REQUEST_FAILED].includes(s.details)?f.MEDIA_ERR_NETWORK:r[s.type],o=s=>{if(s.type===g.ErrorTypes.KEY_SYSTEM_ERROR)return C.DRM;if(s.type===g.ErrorTypes.NETWORK_ERROR)return C.VIDEO},a,i=n(e);if(i===f.MEDIA_ERR_NETWORK&&e.response){let s=(c=o(e))!=null?c:C.VIDEO;a=(d=H(e.response,s,t,e.fatal))!=null?d:new f("",i,e.fatal)}else if(i===f.MEDIA_ERR_ENCRYPTED)if(e.details===g.ErrorDetails.KEY_SYSTEM_NO_CONFIGURED_LICENSE){let s=x("Attempting to play DRM-protected content without providing a DRM token.");a=new f(s,f.MEDIA_ERR_ENCRYPTED,e.fatal),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_MISSING_TOKEN}else if(e.details===g.ErrorDetails.KEY_SYSTEM_NO_ACCESS){let s=x("Cannot play DRM-protected content with current security configuration on this browser. Try playing in another browser.");a=new f(s,f.MEDIA_ERR_ENCRYPTED,e.fatal),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_UNSUPPORTED_KEY_SYSTEM}else if(e.details===g.ErrorDetails.KEY_SYSTEM_NO_SESSION){let s=x("Failed to generate a DRM license request. This may be an issue with the player or your protected content.");a=new f(s,f.MEDIA_ERR_ENCRYPTED,!0),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_GENERATE_REQUEST_FAILED}else if(e.details===g.ErrorDetails.KEY_SYSTEM_SESSION_UPDATE_FAILED){let s=x("Failed to update DRM license. This may be an issue with the player or your protected content.");a=new f(s,f.MEDIA_ERR_ENCRYPTED,e.fatal),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_UPDATE_LICENSE_FAILED}else if(e.details===g.ErrorDetails.KEY_SYSTEM_SERVER_CERTIFICATE_UPDATE_FAILED){let s=x("Your server certificate failed when attempting to set it. This may be an issue with a no longer valid certificate.");a=new f(s,f.MEDIA_ERR_ENCRYPTED,e.fatal),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_UPDATE_SERVER_CERT_FAILED}else if(e.details===g.ErrorDetails.KEY_SYSTEM_STATUS_INTERNAL_ERROR){let s=x("The DRM Content Decryption Module system had an internal failure. Try reloading the page, upading your browser, or playing in another browser.");a=new f(s,f.MEDIA_ERR_ENCRYPTED,e.fatal),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_CDM_ERROR}else if(e.details===g.ErrorDetails.KEY_SYSTEM_STATUS_OUTPUT_RESTRICTED){let s=x("DRM playback is being attempted in an environment that is not sufficiently secure. User may see black screen.");a=new f(s,f.MEDIA_ERR_ENCRYPTED,!1),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_OUTPUT_RESTRICTED}else a=new f(e.error.message,f.MEDIA_ERR_ENCRYPTED,e.fatal),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_ERROR;else a=new f("",i,e.fatal);return a.context||(a.context=`${e.url?`url: ${e.url}
`:""}${e.response&&(e.response.code||e.response.text)?`response: ${e.response.code}, ${e.response.text}
`:""}${e.reason?`failure reason: ${e.reason}
`:""}${e.level?`level: ${e.level}
`:""}${e.parent?`parent stream controller: ${e.parent}
`:""}${e.buffer?`buffer length: ${e.buffer}
`:""}${e.error?`error: ${e.error}
`:""}${e.event?`event: ${e.event}
`:""}${e.err?`error message: ${(u=e.err)==null?void 0:u.message}
`:""}`),a.data=e,a};export{K as AutoplayTypes,jt as CmcdTypeValues,S as CmcdTypes,A as ExtensionMimeTypeMap,g as Hls,Gt as MaxResolution,f as MediaError,W as MimeTypeShorthandMap,Xt as MinResolution,C as MuxErrorCategory,D as MuxErrorCode,se as MuxJWTAud,X as PlaybackTypes,zt as RenditionOrder,_ as StreamTypes,Le as addChapters,Pe as addCuePoints,ne as addTextTrack,qt as allMediaTypes,V as errorCategoryToTokenNameOrPrefix,de as fetchAndDispatchMuxMetadata,Wr as generatePlayerInitTime,_t as generateUUID,Ne as getActiveChapter,_e as getActiveCuePoint,Ut as getAppCertificate,ct as getChapters,it as getCuePoints,dt as getCurrentPdt,Ot as getDRMConfig,At as getEnded,ht as getError,Ht as getLicenseKey,Br as getLiveEdgeStart,Tt as getMediaPlaylistFromMultivariantPlaylist,Fr as getMetadata,yt as getMultivariantPlaylistSessionData,Be as getSeekable,ut as getStartDate,Rt as getStreamInfoFromHlsjsLevelDetails,gt as getStreamInfoFromPlaylist,Mt as getStreamInfoFromSrcAndType,we as getStreamType,wt as getStreamTypeConfig,$r as getTargetLiveWindow,w as getTextTrack,x as i18n,jr as initialize,O as isKeyOf,Xe as isMuxVideoSrc,Nt as isPseudoEnded,Je as isStuckOnLastFragment,Wt as loadMedia,ue as mux,P as muxMediaState,ee as parseJwt,Et as parseTagAttributes,st as removeTextTrack,Ae as setupChapters,ke as setupCuePoints,St as setupHls,Kt as setupMux,Vt as setupNativeFairplayDRM,Jt as shorthandKeys,It as teardown,Ge as toAppCertURL,ft as toDRMTypeFromKeySystem,q as toLicenseKeyURL,Yr as toMuxVideoURL,$e as toPlaybackIdFromSrc,F as toPlaybackIdParts,Dt as updateStreamInfoFromHlsjsLevelDetails,xt as updateStreamInfoFromSrc};
//# sourceMappingURL=index.mjs.map
