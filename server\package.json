{"name": "freelancehub-server", "version": "1.0.0", "description": "Backend server for FreelanceHub marketplace", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'Server build complete'", "test": "jest", "test:watch": "jest --watch", "postinstall": "npm run build"}, "keywords": ["freelance", "marketplace", "nodejs", "express", "mongodb"], "author": "FreelanceHub Team", "license": "MIT", "dependencies": {"@google-cloud/text-to-speech": "^6.2.0", "@google-cloud/translate": "^9.2.0", "bcryptjs": "^2.4.3", "cloudinary": "^1.40.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "helmet": "^7.0.0", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^7.0.5", "openai": "^5.12.0", "perspective-api-client": "^3.1.0", "sharp": "^0.34.3", "socket.io": "^4.7.2", "stripe": "^13.5.0", "validator": "^13.11.0", "xss-clean": "^0.1.4"}, "devDependencies": {"@types/jest": "^29.5.5", "jest": "^29.6.4", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}