!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.dashjs=e():t.dashjs=e()}(self,(function(){return function(){"use strict";var t={9306:function(t,e,r){var n=r(4901),o=r(6823),i=TypeError;t.exports=function(t){if(n(t))return t;throw new i(o(t)+" is not a function")}},5548:function(t,e,r){var n=r(3517),o=r(6823),i=TypeError;t.exports=function(t){if(n(t))return t;throw new i(o(t)+" is not a constructor")}},3506:function(t,e,r){var n=r(3925),o=String,i=TypeError;t.exports=function(t){if(n(t))return t;throw new i("Can't set "+o(t)+" as a prototype")}},6469:function(t,e,r){var n=r(8227),o=r(2360),i=r(4913).f,a=n("unscopables"),u=Array.prototype;void 0===u[a]&&i(u,a,{configurable:!0,value:o(null)}),t.exports=function(t){u[a][t]=!0}},7829:function(t,e,r){var n=r(8183).charAt;t.exports=function(t,e,r){return e+(r?n(t,e).length:1)}},679:function(t,e,r){var n=r(1625),o=TypeError;t.exports=function(t,e){if(n(e,t))return t;throw new o("Incorrect invocation")}},8551:function(t,e,r){var n=r(34),o=String,i=TypeError;t.exports=function(t){if(n(t))return t;throw new i(o(t)+" is not an object")}},7811:function(t){t.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},7394:function(t,e,r){var n=r(4576),o=r(6706),i=r(2195),a=n.ArrayBuffer,u=n.TypeError;t.exports=a&&o(a.prototype,"byteLength","get")||function(t){if("ArrayBuffer"!==i(t))throw new u("ArrayBuffer expected");return t.byteLength}},3238:function(t,e,r){var n=r(4576),o=r(7476),i=r(7394),a=n.ArrayBuffer,u=a&&a.prototype,s=u&&o(u.slice);t.exports=function(t){if(0!==i(t))return!1;if(!s)return!1;try{return s(t,0,0),!1}catch(t){return!0}}},5169:function(t,e,r){var n=r(3238),o=TypeError;t.exports=function(t){if(n(t))throw new o("ArrayBuffer is detached");return t}},5636:function(t,e,r){var n=r(4576),o=r(9504),i=r(6706),a=r(7696),u=r(5169),s=r(7394),c=r(4483),f=r(1548),p=n.structuredClone,l=n.ArrayBuffer,d=n.DataView,h=Math.min,v=l.prototype,y=d.prototype,g=o(v.slice),m=i(v,"resizable","get"),b=i(v,"maxByteLength","get"),T=o(y.getInt8),E=o(y.setInt8);t.exports=(f||c)&&function(t,e,r){var n,o=s(t),i=void 0===e?o:a(e),v=!m||!m(t);if(u(t),f&&(t=p(t,{transfer:[t]}),o===i&&(r||v)))return t;if(o>=i&&(!r||v))n=g(t,0,i);else{var y=r&&!v&&b?{maxByteLength:b(t)}:void 0;n=new l(i,y);for(var A=new d(t),_=new d(n),w=h(i,o),x=0;x<w;x++)E(_,x,T(A,x))}return f||c(t),n}},4644:function(t,e,r){var n,o,i,a=r(7811),u=r(3724),s=r(4576),c=r(4901),f=r(34),p=r(9297),l=r(6955),d=r(6823),h=r(6699),v=r(6840),y=r(2106),g=r(1625),m=r(2787),b=r(2967),T=r(8227),E=r(3392),A=r(1181),_=A.enforce,w=A.get,x=s.Int8Array,S=x&&x.prototype,I=s.Uint8ClampedArray,O=I&&I.prototype,N=x&&m(x),M=S&&m(S),D=Object.prototype,P=s.TypeError,C=T("toStringTag"),R=E("TYPED_ARRAY_TAG"),F="TypedArrayConstructor",B=a&&!!b&&"Opera"!==l(s.opera),L=!1,j={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},U={BigInt64Array:8,BigUint64Array:8},k=function(t){var e=m(t);if(f(e)){var r=w(e);return r&&p(r,F)?r[F]:k(e)}},G=function(t){if(!f(t))return!1;var e=l(t);return p(j,e)||p(U,e)};for(n in j)(i=(o=s[n])&&o.prototype)?_(i)[F]=o:B=!1;for(n in U)(i=(o=s[n])&&o.prototype)&&(_(i)[F]=o);if((!B||!c(N)||N===Function.prototype)&&(N=function(){throw new P("Incorrect invocation")},B))for(n in j)s[n]&&b(s[n],N);if((!B||!M||M===D)&&(M=N.prototype,B))for(n in j)s[n]&&b(s[n].prototype,M);if(B&&m(O)!==M&&b(O,M),u&&!p(M,C))for(n in L=!0,y(M,C,{configurable:!0,get:function(){return f(this)?this[R]:void 0}}),j)s[n]&&h(s[n],R,n);t.exports={NATIVE_ARRAY_BUFFER_VIEWS:B,TYPED_ARRAY_TAG:L&&R,aTypedArray:function(t){if(G(t))return t;throw new P("Target is not a typed array")},aTypedArrayConstructor:function(t){if(c(t)&&(!b||g(N,t)))return t;throw new P(d(t)+" is not a typed array constructor")},exportTypedArrayMethod:function(t,e,r,n){if(u){if(r)for(var o in j){var i=s[o];if(i&&p(i.prototype,t))try{delete i.prototype[t]}catch(r){try{i.prototype[t]=e}catch(t){}}}M[t]&&!r||v(M,t,r?e:B&&S[t]||e,n)}},exportTypedArrayStaticMethod:function(t,e,r){var n,o;if(u){if(b){if(r)for(n in j)if((o=s[n])&&p(o,t))try{delete o[t]}catch(t){}if(N[t]&&!r)return;try{return v(N,t,r?e:B&&N[t]||e)}catch(t){}}for(n in j)!(o=s[n])||o[t]&&!r||v(o,t,e)}},getTypedArrayConstructor:k,isView:function(t){if(!f(t))return!1;var e=l(t);return"DataView"===e||p(j,e)||p(U,e)},isTypedArray:G,TypedArray:N,TypedArrayPrototype:M}},6346:function(t,e,r){var n=r(4576),o=r(9504),i=r(3724),a=r(7811),u=r(350),s=r(6699),c=r(2106),f=r(6279),p=r(9039),l=r(679),d=r(1291),h=r(8014),v=r(7696),y=r(5617),g=r(8490),m=r(2787),b=r(2967),T=r(4373),E=r(7680),A=r(3167),_=r(7740),w=r(687),x=r(1181),S=u.PROPER,I=u.CONFIGURABLE,O="ArrayBuffer",N="DataView",M="prototype",D="Wrong index",P=x.getterFor(O),C=x.getterFor(N),R=x.set,F=n[O],B=F,L=B&&B[M],j=n[N],U=j&&j[M],k=Object.prototype,G=n.Array,q=n.RangeError,V=o(T),Y=o([].reverse),H=g.pack,W=g.unpack,K=function(t){return[255&t]},z=function(t){return[255&t,t>>8&255]},$=function(t){return[255&t,t>>8&255,t>>16&255,t>>24&255]},Z=function(t){return t[3]<<24|t[2]<<16|t[1]<<8|t[0]},J=function(t){return H(y(t),23,4)},X=function(t){return H(t,52,8)},Q=function(t,e,r){c(t[M],e,{configurable:!0,get:function(){return r(this)[e]}})},tt=function(t,e,r,n){var o=C(t),i=v(r),a=!!n;if(i+e>o.byteLength)throw new q(D);var u=o.bytes,s=i+o.byteOffset,c=E(u,s,s+e);return a?c:Y(c)},et=function(t,e,r,n,o,i){var a=C(t),u=v(r),s=n(+o),c=!!i;if(u+e>a.byteLength)throw new q(D);for(var f=a.bytes,p=u+a.byteOffset,l=0;l<e;l++)f[p+l]=s[c?l:e-l-1]};if(a){var rt=S&&F.name!==O;p((function(){F(1)}))&&p((function(){new F(-1)}))&&!p((function(){return new F,new F(1.5),new F(NaN),1!==F.length||rt&&!I}))?rt&&I&&s(F,"name",O):((B=function(t){return l(this,L),A(new F(v(t)),this,B)})[M]=L,L.constructor=B,_(B,F)),b&&m(U)!==k&&b(U,k);var nt=new j(new B(2)),ot=o(U.setInt8);nt.setInt8(0,2147483648),nt.setInt8(1,2147483649),!nt.getInt8(0)&&nt.getInt8(1)||f(U,{setInt8:function(t,e){ot(this,t,e<<24>>24)},setUint8:function(t,e){ot(this,t,e<<24>>24)}},{unsafe:!0})}else L=(B=function(t){l(this,L);var e=v(t);R(this,{type:O,bytes:V(G(e),0),byteLength:e}),i||(this.byteLength=e,this.detached=!1)})[M],U=(j=function(t,e,r){l(this,U),l(t,L);var n=P(t),o=n.byteLength,a=d(e);if(a<0||a>o)throw new q("Wrong offset");if(a+(r=void 0===r?o-a:h(r))>o)throw new q("Wrong length");R(this,{type:N,buffer:t,byteLength:r,byteOffset:a,bytes:n.bytes}),i||(this.buffer=t,this.byteLength=r,this.byteOffset=a)})[M],i&&(Q(B,"byteLength",P),Q(j,"buffer",C),Q(j,"byteLength",C),Q(j,"byteOffset",C)),f(U,{getInt8:function(t){return tt(this,1,t)[0]<<24>>24},getUint8:function(t){return tt(this,1,t)[0]},getInt16:function(t){var e=tt(this,2,t,arguments.length>1&&arguments[1]);return(e[1]<<8|e[0])<<16>>16},getUint16:function(t){var e=tt(this,2,t,arguments.length>1&&arguments[1]);return e[1]<<8|e[0]},getInt32:function(t){return Z(tt(this,4,t,arguments.length>1&&arguments[1]))},getUint32:function(t){return Z(tt(this,4,t,arguments.length>1&&arguments[1]))>>>0},getFloat32:function(t){return W(tt(this,4,t,arguments.length>1&&arguments[1]),23)},getFloat64:function(t){return W(tt(this,8,t,arguments.length>1&&arguments[1]),52)},setInt8:function(t,e){et(this,1,t,K,e)},setUint8:function(t,e){et(this,1,t,K,e)},setInt16:function(t,e){et(this,2,t,z,e,arguments.length>2&&arguments[2])},setUint16:function(t,e){et(this,2,t,z,e,arguments.length>2&&arguments[2])},setInt32:function(t,e){et(this,4,t,$,e,arguments.length>2&&arguments[2])},setUint32:function(t,e){et(this,4,t,$,e,arguments.length>2&&arguments[2])},setFloat32:function(t,e){et(this,4,t,J,e,arguments.length>2&&arguments[2])},setFloat64:function(t,e){et(this,8,t,X,e,arguments.length>2&&arguments[2])}});w(B,O),w(j,N),t.exports={ArrayBuffer:B,DataView:j}},7029:function(t,e,r){var n=r(8981),o=r(5610),i=r(6198),a=r(4606),u=Math.min;t.exports=[].copyWithin||function(t,e){var r=n(this),s=i(r),c=o(t,s),f=o(e,s),p=arguments.length>2?arguments[2]:void 0,l=u((void 0===p?s:o(p,s))-f,s-c),d=1;for(f<c&&c<f+l&&(d=-1,f+=l-1,c+=l-1);l-- >0;)f in r?r[c]=r[f]:a(r,c),c+=d,f+=d;return r}},4373:function(t,e,r){var n=r(8981),o=r(5610),i=r(6198);t.exports=function(t){for(var e=n(this),r=i(e),a=arguments.length,u=o(a>1?arguments[1]:void 0,r),s=a>2?arguments[2]:void 0,c=void 0===s?r:o(s,r);c>u;)e[u++]=t;return e}},235:function(t,e,r){var n=r(9213).forEach,o=r(4598)("forEach");t.exports=o?[].forEach:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}},5370:function(t,e,r){var n=r(6198);t.exports=function(t,e,r){for(var o=0,i=arguments.length>2?r:n(e),a=new t(i);i>o;)a[o]=e[o++];return a}},9617:function(t,e,r){var n=r(5397),o=r(5610),i=r(6198),a=function(t){return function(e,r,a){var u=n(e),s=i(u);if(0===s)return!t&&-1;var c,f=o(a,s);if(t&&r!=r){for(;s>f;)if((c=u[f++])!=c)return!0}else for(;s>f;f++)if((t||f in u)&&u[f]===r)return t||f||0;return!t&&-1}};t.exports={includes:a(!0),indexOf:a(!1)}},3839:function(t,e,r){var n=r(6080),o=r(7055),i=r(8981),a=r(6198),u=function(t){var e=1===t;return function(r,u,s){for(var c,f=i(r),p=o(f),l=a(p),d=n(u,s);l-- >0;)if(d(c=p[l],l,f))switch(t){case 0:return c;case 1:return l}return e?-1:void 0}};t.exports={findLast:u(0),findLastIndex:u(1)}},9213:function(t,e,r){var n=r(6080),o=r(9504),i=r(7055),a=r(8981),u=r(6198),s=r(1469),c=o([].push),f=function(t){var e=1===t,r=2===t,o=3===t,f=4===t,p=6===t,l=7===t,d=5===t||p;return function(h,v,y,g){for(var m,b,T=a(h),E=i(T),A=u(E),_=n(v,y),w=0,x=g||s,S=e?x(h,A):r||l?x(h,0):void 0;A>w;w++)if((d||w in E)&&(b=_(m=E[w],w,T),t))if(e)S[w]=b;else if(b)switch(t){case 3:return!0;case 5:return m;case 6:return w;case 2:c(S,m)}else switch(t){case 4:return!1;case 7:c(S,m)}return p?-1:o||f?f:S}};t.exports={forEach:f(0),map:f(1),filter:f(2),some:f(3),every:f(4),find:f(5),findIndex:f(6),filterReject:f(7)}},8379:function(t,e,r){var n=r(8745),o=r(5397),i=r(1291),a=r(6198),u=r(4598),s=Math.min,c=[].lastIndexOf,f=!!c&&1/[1].lastIndexOf(1,-0)<0,p=u("lastIndexOf"),l=f||!p;t.exports=l?function(t){if(f)return n(c,this,arguments)||0;var e=o(this),r=a(e);if(0===r)return-1;var u=r-1;for(arguments.length>1&&(u=s(u,i(arguments[1]))),u<0&&(u=r+u);u>=0;u--)if(u in e&&e[u]===t)return u||0;return-1}:c},597:function(t,e,r){var n=r(9039),o=r(8227),i=r(9519),a=o("species");t.exports=function(t){return i>=51||!n((function(){var e=[];return(e.constructor={})[a]=function(){return{foo:1}},1!==e[t](Boolean).foo}))}},4598:function(t,e,r){var n=r(9039);t.exports=function(t,e){var r=[][t];return!!r&&n((function(){r.call(null,e||function(){return 1},1)}))}},926:function(t,e,r){var n=r(9306),o=r(8981),i=r(7055),a=r(6198),u=TypeError,s="Reduce of empty array with no initial value",c=function(t){return function(e,r,c,f){var p=o(e),l=i(p),d=a(p);if(n(r),0===d&&c<2)throw new u(s);var h=t?d-1:0,v=t?-1:1;if(c<2)for(;;){if(h in l){f=l[h],h+=v;break}if(h+=v,t?h<0:d<=h)throw new u(s)}for(;t?h>=0:d>h;h+=v)h in l&&(f=r(f,l[h],h,p));return f}};t.exports={left:c(!1),right:c(!0)}},4527:function(t,e,r){var n=r(3724),o=r(4376),i=TypeError,a=Object.getOwnPropertyDescriptor,u=n&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}();t.exports=u?function(t,e){if(o(t)&&!a(t,"length").writable)throw new i("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e}},7680:function(t,e,r){var n=r(9504);t.exports=n([].slice)},4488:function(t,e,r){var n=r(7680),o=Math.floor,i=function(t,e){var r=t.length;if(r<8)for(var a,u,s=1;s<r;){for(u=s,a=t[s];u&&e(t[u-1],a)>0;)t[u]=t[--u];u!==s++&&(t[u]=a)}else for(var c=o(r/2),f=i(n(t,0,c),e),p=i(n(t,c),e),l=f.length,d=p.length,h=0,v=0;h<l||v<d;)t[h+v]=h<l&&v<d?e(f[h],p[v])<=0?f[h++]:p[v++]:h<l?f[h++]:p[v++];return t};t.exports=i},7433:function(t,e,r){var n=r(4376),o=r(3517),i=r(34),a=r(8227)("species"),u=Array;t.exports=function(t){var e;return n(t)&&(e=t.constructor,(o(e)&&(e===u||n(e.prototype))||i(e)&&null===(e=e[a]))&&(e=void 0)),void 0===e?u:e}},1469:function(t,e,r){var n=r(7433);t.exports=function(t,e){return new(n(t))(0===e?0:e)}},7628:function(t,e,r){var n=r(6198);t.exports=function(t,e){for(var r=n(t),o=new e(r),i=0;i<r;i++)o[i]=t[r-i-1];return o}},9928:function(t,e,r){var n=r(6198),o=r(1291),i=RangeError;t.exports=function(t,e,r,a){var u=n(t),s=o(r),c=s<0?u+s:s;if(c>=u||c<0)throw new i("Incorrect index");for(var f=new e(u),p=0;p<u;p++)f[p]=p===c?a:t[p];return f}},4428:function(t,e,r){var n=r(8227)("iterator"),o=!1;try{var i=0,a={next:function(){return{done:!!i++}},return:function(){o=!0}};a[n]=function(){return this},Array.from(a,(function(){throw 2}))}catch(t){}t.exports=function(t,e){try{if(!e&&!o)return!1}catch(t){return!1}var r=!1;try{var i={};i[n]=function(){return{next:function(){return{done:r=!0}}}},t(i)}catch(t){}return r}},2195:function(t,e,r){var n=r(9504),o=n({}.toString),i=n("".slice);t.exports=function(t){return i(o(t),8,-1)}},6955:function(t,e,r){var n=r(2140),o=r(4901),i=r(2195),a=r(8227)("toStringTag"),u=Object,s="Arguments"===i(function(){return arguments}());t.exports=n?i:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=u(t),a))?r:s?i(e):"Object"===(n=i(e))&&o(e.callee)?"Arguments":n}},7740:function(t,e,r){var n=r(9297),o=r(5031),i=r(7347),a=r(4913);t.exports=function(t,e,r){for(var u=o(e),s=a.f,c=i.f,f=0;f<u.length;f++){var p=u[f];n(t,p)||r&&n(r,p)||s(t,p,c(e,p))}}},2211:function(t,e,r){var n=r(9039);t.exports=!n((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))},2529:function(t){t.exports=function(t,e){return{value:t,done:e}}},6699:function(t,e,r){var n=r(3724),o=r(4913),i=r(6980);t.exports=n?function(t,e,r){return o.f(t,e,i(1,r))}:function(t,e,r){return t[e]=r,t}},6980:function(t){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},4659:function(t,e,r){var n=r(3724),o=r(4913),i=r(6980);t.exports=function(t,e,r){n?o.f(t,e,i(0,r)):t[e]=r}},3640:function(t,e,r){var n=r(8551),o=r(4270),i=TypeError;t.exports=function(t){if(n(this),"string"===t||"default"===t)t="string";else if("number"!==t)throw new i("Incorrect hint");return o(this,t)}},2106:function(t,e,r){var n=r(283),o=r(4913);t.exports=function(t,e,r){return r.get&&n(r.get,e,{getter:!0}),r.set&&n(r.set,e,{setter:!0}),o.f(t,e,r)}},6840:function(t,e,r){var n=r(4901),o=r(4913),i=r(283),a=r(9433);t.exports=function(t,e,r,u){u||(u={});var s=u.enumerable,c=void 0!==u.name?u.name:e;if(n(r)&&i(r,c,u),u.global)s?t[e]=r:a(e,r);else{try{u.unsafe?t[e]&&(s=!0):delete t[e]}catch(t){}s?t[e]=r:o.f(t,e,{value:r,enumerable:!1,configurable:!u.nonConfigurable,writable:!u.nonWritable})}return t}},6279:function(t,e,r){var n=r(6840);t.exports=function(t,e,r){for(var o in e)n(t,o,e[o],r);return t}},9433:function(t,e,r){var n=r(4576),o=Object.defineProperty;t.exports=function(t,e){try{o(n,t,{value:e,configurable:!0,writable:!0})}catch(r){n[t]=e}return e}},4606:function(t,e,r){var n=r(6823),o=TypeError;t.exports=function(t,e){if(!delete t[e])throw new o("Cannot delete property "+n(e)+" of "+n(t))}},3724:function(t,e,r){var n=r(9039);t.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},4483:function(t,e,r){var n,o,i,a,u=r(4576),s=r(9429),c=r(1548),f=u.structuredClone,p=u.ArrayBuffer,l=u.MessageChannel,d=!1;if(c)d=function(t){f(t,{transfer:[t]})};else if(p)try{l||(n=s("worker_threads"))&&(l=n.MessageChannel),l&&(o=new l,i=new p(2),a=function(t){o.port1.postMessage(null,[t])},2===i.byteLength&&(a(i),0===i.byteLength&&(d=a)))}catch(t){}t.exports=d},4055:function(t,e,r){var n=r(4576),o=r(34),i=n.document,a=o(i)&&o(i.createElement);t.exports=function(t){return a?i.createElement(t):{}}},6837:function(t){var e=TypeError;t.exports=function(t){if(t>9007199254740991)throw e("Maximum allowed index exceeded");return t}},7400:function(t){t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},9296:function(t,e,r){var n=r(4055)("span").classList,o=n&&n.constructor&&n.constructor.prototype;t.exports=o===Object.prototype?void 0:o},8727:function(t){t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},3709:function(t,e,r){var n=r(2839).match(/firefox\/(\d+)/i);t.exports=!!n&&+n[1]},3763:function(t,e,r){var n=r(2839);t.exports=/MSIE|Trident/.test(n)},4265:function(t,e,r){var n=r(2839);t.exports=/ipad|iphone|ipod/i.test(n)&&"undefined"!=typeof Pebble},9544:function(t,e,r){var n=r(2839);t.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(n)},8574:function(t,e,r){var n=r(4215);t.exports="NODE"===n},7860:function(t,e,r){var n=r(2839);t.exports=/web0s(?!.*chrome)/i.test(n)},2839:function(t,e,r){var n=r(4576).navigator,o=n&&n.userAgent;t.exports=o?String(o):""},9519:function(t,e,r){var n,o,i=r(4576),a=r(2839),u=i.process,s=i.Deno,c=u&&u.versions||s&&s.version,f=c&&c.v8;f&&(o=(n=f.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!o&&a&&(!(n=a.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=a.match(/Chrome\/(\d+)/))&&(o=+n[1]),t.exports=o},3607:function(t,e,r){var n=r(2839).match(/AppleWebKit\/(\d+)\./);t.exports=!!n&&+n[1]},4215:function(t,e,r){var n=r(4576),o=r(2839),i=r(2195),a=function(t){return o.slice(0,t.length)===t};t.exports=a("Bun/")?"BUN":a("Cloudflare-Workers")?"CLOUDFLARE":a("Deno/")?"DENO":a("Node.js/")?"NODE":n.Bun&&"string"==typeof Bun.version?"BUN":n.Deno&&"object"==typeof Deno.version?"DENO":"process"===i(n.process)?"NODE":n.window&&n.document?"BROWSER":"REST"},6193:function(t,e,r){var n=r(9504),o=Error,i=n("".replace),a=String(new o("zxcasd").stack),u=/\n\s*at [^:]*:[^\n]*/,s=u.test(a);t.exports=function(t,e){if(s&&"string"==typeof t&&!o.prepareStackTrace)for(;e--;)t=i(t,u,"");return t}},747:function(t,e,r){var n=r(6699),o=r(6193),i=r(6249),a=Error.captureStackTrace;t.exports=function(t,e,r,u){i&&(a?a(t,e):n(t,"stack",o(r,u)))}},6249:function(t,e,r){var n=r(9039),o=r(6980);t.exports=!n((function(){var t=new Error("a");return!("stack"in t)||(Object.defineProperty(t,"stack",o(1,7)),7!==t.stack)}))},6518:function(t,e,r){var n=r(4576),o=r(7347).f,i=r(6699),a=r(6840),u=r(9433),s=r(7740),c=r(2796);t.exports=function(t,e){var r,f,p,l,d,h=t.target,v=t.global,y=t.stat;if(r=v?n:y?n[h]||u(h,{}):n[h]&&n[h].prototype)for(f in e){if(l=e[f],p=t.dontCallGetSet?(d=o(r,f))&&d.value:r[f],!c(v?f:h+(y?".":"#")+f,t.forced)&&void 0!==p){if(typeof l==typeof p)continue;s(l,p)}(t.sham||p&&p.sham)&&i(l,"sham",!0),a(r,f,l,t)}}},9039:function(t){t.exports=function(t){try{return!!t()}catch(t){return!0}}},9228:function(t,e,r){r(7495);var n=r(9565),o=r(6840),i=r(7323),a=r(9039),u=r(8227),s=r(6699),c=u("species"),f=RegExp.prototype;t.exports=function(t,e,r,p){var l=u(t),d=!a((function(){var e={};return e[l]=function(){return 7},7!==""[t](e)})),h=d&&!a((function(){var e=!1,r=/a/;return"split"===t&&((r={}).constructor={},r.constructor[c]=function(){return r},r.flags="",r[l]=/./[l]),r.exec=function(){return e=!0,null},r[l](""),!e}));if(!d||!h||r){var v=/./[l],y=e(l,""[t],(function(t,e,r,o,a){var u=e.exec;return u===i||u===f.exec?d&&!a?{done:!0,value:n(v,e,r,o)}:{done:!0,value:n(t,r,e,o)}:{done:!1}}));o(String.prototype,t,y[0]),o(f,l,y[1])}p&&s(f[l],"sham",!0)}},8745:function(t,e,r){var n=r(616),o=Function.prototype,i=o.apply,a=o.call;t.exports="object"==typeof Reflect&&Reflect.apply||(n?a.bind(i):function(){return a.apply(i,arguments)})},6080:function(t,e,r){var n=r(7476),o=r(9306),i=r(616),a=n(n.bind);t.exports=function(t,e){return o(t),void 0===e?t:i?a(t,e):function(){return t.apply(e,arguments)}}},616:function(t,e,r){var n=r(9039);t.exports=!n((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))},566:function(t,e,r){var n=r(9504),o=r(9306),i=r(34),a=r(9297),u=r(7680),s=r(616),c=Function,f=n([].concat),p=n([].join),l={};t.exports=s?c.bind:function(t){var e=o(this),r=e.prototype,n=u(arguments,1),s=function(){var r=f(n,u(arguments));return this instanceof s?function(t,e,r){if(!a(l,e)){for(var n=[],o=0;o<e;o++)n[o]="a["+o+"]";l[e]=c("C,a","return new C("+p(n,",")+")")}return l[e](t,r)}(e,r.length,r):e.apply(t,r)};return i(r)&&(s.prototype=r),s}},9565:function(t,e,r){var n=r(616),o=Function.prototype.call;t.exports=n?o.bind(o):function(){return o.apply(o,arguments)}},350:function(t,e,r){var n=r(3724),o=r(9297),i=Function.prototype,a=n&&Object.getOwnPropertyDescriptor,u=o(i,"name"),s=u&&"something"===function(){}.name,c=u&&(!n||n&&a(i,"name").configurable);t.exports={EXISTS:u,PROPER:s,CONFIGURABLE:c}},6706:function(t,e,r){var n=r(9504),o=r(9306);t.exports=function(t,e,r){try{return n(o(Object.getOwnPropertyDescriptor(t,e)[r]))}catch(t){}}},7476:function(t,e,r){var n=r(2195),o=r(9504);t.exports=function(t){if("Function"===n(t))return o(t)}},9504:function(t,e,r){var n=r(616),o=Function.prototype,i=o.call,a=n&&o.bind.bind(i,i);t.exports=n?a:function(t){return function(){return i.apply(t,arguments)}}},9429:function(t,e,r){var n=r(4576),o=r(8574);t.exports=function(t){if(o){try{return n.process.getBuiltinModule(t)}catch(t){}try{return Function('return require("'+t+'")')()}catch(t){}}}},7751:function(t,e,r){var n=r(4576),o=r(4901);t.exports=function(t,e){return arguments.length<2?(r=n[t],o(r)?r:void 0):n[t]&&n[t][e];var r}},851:function(t,e,r){var n=r(6955),o=r(5966),i=r(4117),a=r(6269),u=r(8227)("iterator");t.exports=function(t){if(!i(t))return o(t,u)||o(t,"@@iterator")||a[n(t)]}},81:function(t,e,r){var n=r(9565),o=r(9306),i=r(8551),a=r(6823),u=r(851),s=TypeError;t.exports=function(t,e){var r=arguments.length<2?u(t):e;if(o(r))return i(n(r,t));throw new s(a(t)+" is not iterable")}},6933:function(t,e,r){var n=r(9504),o=r(4376),i=r(4901),a=r(2195),u=r(655),s=n([].push);t.exports=function(t){if(i(t))return t;if(o(t)){for(var e=t.length,r=[],n=0;n<e;n++){var c=t[n];"string"==typeof c?s(r,c):"number"!=typeof c&&"Number"!==a(c)&&"String"!==a(c)||s(r,u(c))}var f=r.length,p=!0;return function(t,e){if(p)return p=!1,e;if(o(this))return e;for(var n=0;n<f;n++)if(r[n]===t)return e}}}},5966:function(t,e,r){var n=r(9306),o=r(4117);t.exports=function(t,e){var r=t[e];return o(r)?void 0:n(r)}},2478:function(t,e,r){var n=r(9504),o=r(8981),i=Math.floor,a=n("".charAt),u=n("".replace),s=n("".slice),c=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,f=/\$([$&'`]|\d{1,2})/g;t.exports=function(t,e,r,n,p,l){var d=r+t.length,h=n.length,v=f;return void 0!==p&&(p=o(p),v=c),u(l,v,(function(o,u){var c;switch(a(u,0)){case"$":return"$";case"&":return t;case"`":return s(e,0,r);case"'":return s(e,d);case"<":c=p[s(u,1,-1)];break;default:var f=+u;if(0===f)return o;if(f>h){var l=i(f/10);return 0===l?o:l<=h?void 0===n[l-1]?a(u,1):n[l-1]+a(u,1):o}c=n[f-1]}return void 0===c?"":c}))}},4576:function(t,e,r){var n=function(t){return t&&t.Math===Math&&t};t.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof r.g&&r.g)||n("object"==typeof this&&this)||function(){return this}()||Function("return this")()},9297:function(t,e,r){var n=r(9504),o=r(8981),i=n({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,e){return i(o(t),e)}},421:function(t){t.exports={}},3138:function(t){t.exports=function(t,e){try{1===arguments.length?console.error(t):console.error(t,e)}catch(t){}}},397:function(t,e,r){var n=r(7751);t.exports=n("document","documentElement")},5917:function(t,e,r){var n=r(3724),o=r(9039),i=r(4055);t.exports=!n&&!o((function(){return 7!==Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},8490:function(t){var e=Array,r=Math.abs,n=Math.pow,o=Math.floor,i=Math.log,a=Math.LN2;t.exports={pack:function(t,u,s){var c,f,p,l=e(s),d=8*s-u-1,h=(1<<d)-1,v=h>>1,y=23===u?n(2,-24)-n(2,-77):0,g=t<0||0===t&&1/t<0?1:0,m=0;for((t=r(t))!=t||t===1/0?(f=t!=t?1:0,c=h):(c=o(i(t)/a),t*(p=n(2,-c))<1&&(c--,p*=2),(t+=c+v>=1?y/p:y*n(2,1-v))*p>=2&&(c++,p/=2),c+v>=h?(f=0,c=h):c+v>=1?(f=(t*p-1)*n(2,u),c+=v):(f=t*n(2,v-1)*n(2,u),c=0));u>=8;)l[m++]=255&f,f/=256,u-=8;for(c=c<<u|f,d+=u;d>0;)l[m++]=255&c,c/=256,d-=8;return l[m-1]|=128*g,l},unpack:function(t,e){var r,o=t.length,i=8*o-e-1,a=(1<<i)-1,u=a>>1,s=i-7,c=o-1,f=t[c--],p=127&f;for(f>>=7;s>0;)p=256*p+t[c--],s-=8;for(r=p&(1<<-s)-1,p>>=-s,s+=e;s>0;)r=256*r+t[c--],s-=8;if(0===p)p=1-u;else{if(p===a)return r?NaN:f?-1/0:1/0;r+=n(2,e),p-=u}return(f?-1:1)*r*n(2,p-e)}}},7055:function(t,e,r){var n=r(9504),o=r(9039),i=r(2195),a=Object,u=n("".split);t.exports=o((function(){return!a("z").propertyIsEnumerable(0)}))?function(t){return"String"===i(t)?u(t,""):a(t)}:a},3167:function(t,e,r){var n=r(4901),o=r(34),i=r(2967);t.exports=function(t,e,r){var a,u;return i&&n(a=e.constructor)&&a!==r&&o(u=a.prototype)&&u!==r.prototype&&i(t,u),t}},3706:function(t,e,r){var n=r(9504),o=r(4901),i=r(7629),a=n(Function.toString);o(i.inspectSource)||(i.inspectSource=function(t){return a(t)}),t.exports=i.inspectSource},7584:function(t,e,r){var n=r(34),o=r(6699);t.exports=function(t,e){n(e)&&"cause"in e&&o(t,"cause",e.cause)}},1181:function(t,e,r){var n,o,i,a=r(8622),u=r(4576),s=r(34),c=r(6699),f=r(9297),p=r(7629),l=r(6119),d=r(421),h="Object already initialized",v=u.TypeError,y=u.WeakMap;if(a||p.state){var g=p.state||(p.state=new y);g.get=g.get,g.has=g.has,g.set=g.set,n=function(t,e){if(g.has(t))throw new v(h);return e.facade=t,g.set(t,e),e},o=function(t){return g.get(t)||{}},i=function(t){return g.has(t)}}else{var m=l("state");d[m]=!0,n=function(t,e){if(f(t,m))throw new v(h);return e.facade=t,c(t,m,e),e},o=function(t){return f(t,m)?t[m]:{}},i=function(t){return f(t,m)}}t.exports={set:n,get:o,has:i,enforce:function(t){return i(t)?o(t):n(t,{})},getterFor:function(t){return function(e){var r;if(!s(e)||(r=o(e)).type!==t)throw new v("Incompatible receiver, "+t+" required");return r}}}},4209:function(t,e,r){var n=r(8227),o=r(6269),i=n("iterator"),a=Array.prototype;t.exports=function(t){return void 0!==t&&(o.Array===t||a[i]===t)}},4376:function(t,e,r){var n=r(2195);t.exports=Array.isArray||function(t){return"Array"===n(t)}},1108:function(t,e,r){var n=r(6955);t.exports=function(t){var e=n(t);return"BigInt64Array"===e||"BigUint64Array"===e}},4901:function(t){var e="object"==typeof document&&document.all;t.exports=void 0===e&&void 0!==e?function(t){return"function"==typeof t||t===e}:function(t){return"function"==typeof t}},3517:function(t,e,r){var n=r(9504),o=r(9039),i=r(4901),a=r(6955),u=r(7751),s=r(3706),c=function(){},f=u("Reflect","construct"),p=/^\s*(?:class|function)\b/,l=n(p.exec),d=!p.test(c),h=function(t){if(!i(t))return!1;try{return f(c,[],t),!0}catch(t){return!1}},v=function(t){if(!i(t))return!1;switch(a(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return d||!!l(p,s(t))}catch(t){return!0}};v.sham=!0,t.exports=!f||o((function(){var t;return h(h.call)||!h(Object)||!h((function(){t=!0}))||t}))?v:h},2796:function(t,e,r){var n=r(9039),o=r(4901),i=/#|\.prototype\./,a=function(t,e){var r=s[u(t)];return r===f||r!==c&&(o(e)?n(e):!!e)},u=a.normalize=function(t){return String(t).replace(i,".").toLowerCase()},s=a.data={},c=a.NATIVE="N",f=a.POLYFILL="P";t.exports=a},2087:function(t,e,r){var n=r(34),o=Math.floor;t.exports=Number.isInteger||function(t){return!n(t)&&isFinite(t)&&o(t)===t}},4117:function(t){t.exports=function(t){return null==t}},34:function(t,e,r){var n=r(4901);t.exports=function(t){return"object"==typeof t?null!==t:n(t)}},3925:function(t,e,r){var n=r(34);t.exports=function(t){return n(t)||null===t}},6395:function(t){t.exports=!1},757:function(t,e,r){var n=r(7751),o=r(4901),i=r(1625),a=r(7040),u=Object;t.exports=a?function(t){return"symbol"==typeof t}:function(t){var e=n("Symbol");return o(e)&&i(e.prototype,u(t))}},2652:function(t,e,r){var n=r(6080),o=r(9565),i=r(8551),a=r(6823),u=r(4209),s=r(6198),c=r(1625),f=r(81),p=r(851),l=r(9539),d=TypeError,h=function(t,e){this.stopped=t,this.result=e},v=h.prototype;t.exports=function(t,e,r){var y,g,m,b,T,E,A,_=r&&r.that,w=!(!r||!r.AS_ENTRIES),x=!(!r||!r.IS_RECORD),S=!(!r||!r.IS_ITERATOR),I=!(!r||!r.INTERRUPTED),O=n(e,_),N=function(t){return y&&l(y,"normal",t),new h(!0,t)},M=function(t){return w?(i(t),I?O(t[0],t[1],N):O(t[0],t[1])):I?O(t,N):O(t)};if(x)y=t.iterator;else if(S)y=t;else{if(!(g=p(t)))throw new d(a(t)+" is not iterable");if(u(g)){for(m=0,b=s(t);b>m;m++)if((T=M(t[m]))&&c(v,T))return T;return new h(!1)}y=f(t,g)}for(E=x?t.next:y.next;!(A=o(E,y)).done;){try{T=M(A.value)}catch(t){l(y,"throw",t)}if("object"==typeof T&&T&&c(v,T))return T}return new h(!1)}},9539:function(t,e,r){var n=r(9565),o=r(8551),i=r(5966);t.exports=function(t,e,r){var a,u;o(t);try{if(!(a=i(t,"return"))){if("throw"===e)throw r;return r}a=n(a,t)}catch(t){u=!0,a=t}if("throw"===e)throw r;if(u)throw a;return o(a),r}},3994:function(t,e,r){var n=r(7657).IteratorPrototype,o=r(2360),i=r(6980),a=r(687),u=r(6269),s=function(){return this};t.exports=function(t,e,r,c){var f=e+" Iterator";return t.prototype=o(n,{next:i(+!c,r)}),a(t,f,!1,!0),u[f]=s,t}},1088:function(t,e,r){var n=r(6518),o=r(9565),i=r(6395),a=r(350),u=r(4901),s=r(3994),c=r(2787),f=r(2967),p=r(687),l=r(6699),d=r(6840),h=r(8227),v=r(6269),y=r(7657),g=a.PROPER,m=a.CONFIGURABLE,b=y.IteratorPrototype,T=y.BUGGY_SAFARI_ITERATORS,E=h("iterator"),A="keys",_="values",w="entries",x=function(){return this};t.exports=function(t,e,r,a,h,y,S){s(r,e,a);var I,O,N,M=function(t){if(t===h&&F)return F;if(!T&&t&&t in C)return C[t];switch(t){case A:case _:case w:return function(){return new r(this,t)}}return function(){return new r(this)}},D=e+" Iterator",P=!1,C=t.prototype,R=C[E]||C["@@iterator"]||h&&C[h],F=!T&&R||M(h),B="Array"===e&&C.entries||R;if(B&&(I=c(B.call(new t)))!==Object.prototype&&I.next&&(i||c(I)===b||(f?f(I,b):u(I[E])||d(I,E,x)),p(I,D,!0,!0),i&&(v[D]=x)),g&&h===_&&R&&R.name!==_&&(!i&&m?l(C,"name",_):(P=!0,F=function(){return o(R,this)})),h)if(O={values:M(_),keys:y?F:M(A),entries:M(w)},S)for(N in O)(T||P||!(N in C))&&d(C,N,O[N]);else n({target:e,proto:!0,forced:T||P},O);return i&&!S||C[E]===F||d(C,E,F,{name:h}),v[e]=F,O}},7657:function(t,e,r){var n,o,i,a=r(9039),u=r(4901),s=r(34),c=r(2360),f=r(2787),p=r(6840),l=r(8227),d=r(6395),h=l("iterator"),v=!1;[].keys&&("next"in(i=[].keys())?(o=f(f(i)))!==Object.prototype&&(n=o):v=!0),!s(n)||a((function(){var t={};return n[h].call(t)!==t}))?n={}:d&&(n=c(n)),u(n[h])||p(n,h,(function(){return this})),t.exports={IteratorPrototype:n,BUGGY_SAFARI_ITERATORS:v}},6269:function(t){t.exports={}},6198:function(t,e,r){var n=r(8014);t.exports=function(t){return n(t.length)}},283:function(t,e,r){var n=r(9504),o=r(9039),i=r(4901),a=r(9297),u=r(3724),s=r(350).CONFIGURABLE,c=r(3706),f=r(1181),p=f.enforce,l=f.get,d=String,h=Object.defineProperty,v=n("".slice),y=n("".replace),g=n([].join),m=u&&!o((function(){return 8!==h((function(){}),"length",{value:8}).length})),b=String(String).split("String"),T=t.exports=function(t,e,r){"Symbol("===v(d(e),0,7)&&(e="["+y(d(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(e="get "+e),r&&r.setter&&(e="set "+e),(!a(t,"name")||s&&t.name!==e)&&(u?h(t,"name",{value:e,configurable:!0}):t.name=e),m&&r&&a(r,"arity")&&t.length!==r.arity&&h(t,"length",{value:r.arity});try{r&&a(r,"constructor")&&r.constructor?u&&h(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var n=p(t);return a(n,"source")||(n.source=g(b,"string"==typeof e?e:"")),t};Function.prototype.toString=T((function(){return i(this)&&l(this).source||c(this)}),"toString")},3164:function(t,e,r){var n=r(7782),o=Math.abs,i=2220446049250313e-31,a=1/i;t.exports=function(t,e,r,u){var s=+t,c=o(s),f=n(s);if(c<u)return f*function(t){return t+a-a}(c/u/e)*u*e;var p=(1+e/i)*c,l=p-(p-c);return l>r||l!=l?f*(1/0):f*l}},5617:function(t,e,r){var n=r(3164);t.exports=Math.fround||function(t){return n(t,1.1920928955078125e-7,34028234663852886e22,11754943508222875e-54)}},7782:function(t){t.exports=Math.sign||function(t){var e=+t;return 0===e||e!=e?e:e<0?-1:1}},741:function(t){var e=Math.ceil,r=Math.floor;t.exports=Math.trunc||function(t){var n=+t;return(n>0?r:e)(n)}},1955:function(t,e,r){var n,o,i,a,u,s=r(4576),c=r(3389),f=r(6080),p=r(9225).set,l=r(8265),d=r(9544),h=r(4265),v=r(7860),y=r(8574),g=s.MutationObserver||s.WebKitMutationObserver,m=s.document,b=s.process,T=s.Promise,E=c("queueMicrotask");if(!E){var A=new l,_=function(){var t,e;for(y&&(t=b.domain)&&t.exit();e=A.get();)try{e()}catch(t){throw A.head&&n(),t}t&&t.enter()};d||y||v||!g||!m?!h&&T&&T.resolve?((a=T.resolve(void 0)).constructor=T,u=f(a.then,a),n=function(){u(_)}):y?n=function(){b.nextTick(_)}:(p=f(p,s),n=function(){p(_)}):(o=!0,i=m.createTextNode(""),new g(_).observe(i,{characterData:!0}),n=function(){i.data=o=!o}),E=function(t){A.head||n(),A.add(t)}}t.exports=E},6043:function(t,e,r){var n=r(9306),o=TypeError,i=function(t){var e,r;this.promise=new t((function(t,n){if(void 0!==e||void 0!==r)throw new o("Bad Promise constructor");e=t,r=n})),this.resolve=n(e),this.reject=n(r)};t.exports.f=function(t){return new i(t)}},2603:function(t,e,r){var n=r(655);t.exports=function(t,e){return void 0===t?arguments.length<2?"":e:n(t)}},2360:function(t,e,r){var n,o=r(8551),i=r(6801),a=r(8727),u=r(421),s=r(397),c=r(4055),f=r(6119),p="prototype",l="script",d=f("IE_PROTO"),h=function(){},v=function(t){return"<"+l+">"+t+"</"+l+">"},y=function(t){t.write(v("")),t.close();var e=t.parentWindow.Object;return t=null,e},g=function(){try{n=new ActiveXObject("htmlfile")}catch(t){}var t,e,r;g="undefined"!=typeof document?document.domain&&n?y(n):(e=c("iframe"),r="java"+l+":",e.style.display="none",s.appendChild(e),e.src=String(r),(t=e.contentWindow.document).open(),t.write(v("document.F=Object")),t.close(),t.F):y(n);for(var o=a.length;o--;)delete g[p][a[o]];return g()};u[d]=!0,t.exports=Object.create||function(t,e){var r;return null!==t?(h[p]=o(t),r=new h,h[p]=null,r[d]=t):r=g(),void 0===e?r:i.f(r,e)}},6801:function(t,e,r){var n=r(3724),o=r(8686),i=r(4913),a=r(8551),u=r(5397),s=r(1072);e.f=n&&!o?Object.defineProperties:function(t,e){a(t);for(var r,n=u(e),o=s(e),c=o.length,f=0;c>f;)i.f(t,r=o[f++],n[r]);return t}},4913:function(t,e,r){var n=r(3724),o=r(5917),i=r(8686),a=r(8551),u=r(6969),s=TypeError,c=Object.defineProperty,f=Object.getOwnPropertyDescriptor,p="enumerable",l="configurable",d="writable";e.f=n?i?function(t,e,r){if(a(t),e=u(e),a(r),"function"==typeof t&&"prototype"===e&&"value"in r&&d in r&&!r[d]){var n=f(t,e);n&&n[d]&&(t[e]=r.value,r={configurable:l in r?r[l]:n[l],enumerable:p in r?r[p]:n[p],writable:!1})}return c(t,e,r)}:c:function(t,e,r){if(a(t),e=u(e),a(r),o)try{return c(t,e,r)}catch(t){}if("get"in r||"set"in r)throw new s("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},7347:function(t,e,r){var n=r(3724),o=r(9565),i=r(8773),a=r(6980),u=r(5397),s=r(6969),c=r(9297),f=r(5917),p=Object.getOwnPropertyDescriptor;e.f=n?p:function(t,e){if(t=u(t),e=s(e),f)try{return p(t,e)}catch(t){}if(c(t,e))return a(!o(i.f,t,e),t[e])}},298:function(t,e,r){var n=r(2195),o=r(5397),i=r(8480).f,a=r(7680),u="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];t.exports.f=function(t){return u&&"Window"===n(t)?function(t){try{return i(t)}catch(t){return a(u)}}(t):i(o(t))}},8480:function(t,e,r){var n=r(1828),o=r(8727).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return n(t,o)}},3717:function(t,e){e.f=Object.getOwnPropertySymbols},2787:function(t,e,r){var n=r(9297),o=r(4901),i=r(8981),a=r(6119),u=r(2211),s=a("IE_PROTO"),c=Object,f=c.prototype;t.exports=u?c.getPrototypeOf:function(t){var e=i(t);if(n(e,s))return e[s];var r=e.constructor;return o(r)&&e instanceof r?r.prototype:e instanceof c?f:null}},1625:function(t,e,r){var n=r(9504);t.exports=n({}.isPrototypeOf)},1828:function(t,e,r){var n=r(9504),o=r(9297),i=r(5397),a=r(9617).indexOf,u=r(421),s=n([].push);t.exports=function(t,e){var r,n=i(t),c=0,f=[];for(r in n)!o(u,r)&&o(n,r)&&s(f,r);for(;e.length>c;)o(n,r=e[c++])&&(~a(f,r)||s(f,r));return f}},1072:function(t,e,r){var n=r(1828),o=r(8727);t.exports=Object.keys||function(t){return n(t,o)}},8773:function(t,e){var r={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,o=n&&!r.call({1:2},1);e.f=o?function(t){var e=n(this,t);return!!e&&e.enumerable}:r},2967:function(t,e,r){var n=r(6706),o=r(34),i=r(7750),a=r(3506);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=n(Object.prototype,"__proto__","set"))(r,[]),e=r instanceof Array}catch(t){}return function(r,n){return i(r),a(n),o(r)?(e?t(r,n):r.__proto__=n,r):r}}():void 0)},3179:function(t,e,r){var n=r(2140),o=r(6955);t.exports=n?{}.toString:function(){return"[object "+o(this)+"]"}},4270:function(t,e,r){var n=r(9565),o=r(4901),i=r(34),a=TypeError;t.exports=function(t,e){var r,u;if("string"===e&&o(r=t.toString)&&!i(u=n(r,t)))return u;if(o(r=t.valueOf)&&!i(u=n(r,t)))return u;if("string"!==e&&o(r=t.toString)&&!i(u=n(r,t)))return u;throw new a("Can't convert object to primitive value")}},5031:function(t,e,r){var n=r(7751),o=r(9504),i=r(8480),a=r(3717),u=r(8551),s=o([].concat);t.exports=n("Reflect","ownKeys")||function(t){var e=i.f(u(t)),r=a.f;return r?s(e,r(t)):e}},9167:function(t,e,r){var n=r(4576);t.exports=n},1103:function(t){t.exports=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}}},916:function(t,e,r){var n=r(4576),o=r(550),i=r(4901),a=r(2796),u=r(3706),s=r(8227),c=r(4215),f=r(6395),p=r(9519),l=o&&o.prototype,d=s("species"),h=!1,v=i(n.PromiseRejectionEvent),y=a("Promise",(function(){var t=u(o),e=t!==String(o);if(!e&&66===p)return!0;if(f&&(!l.catch||!l.finally))return!0;if(!p||p<51||!/native code/.test(t)){var r=new o((function(t){t(1)})),n=function(t){t((function(){}),(function(){}))};if((r.constructor={})[d]=n,!(h=r.then((function(){}))instanceof n))return!0}return!(e||"BROWSER"!==c&&"DENO"!==c||v)}));t.exports={CONSTRUCTOR:y,REJECTION_EVENT:v,SUBCLASSING:h}},550:function(t,e,r){var n=r(4576);t.exports=n.Promise},3438:function(t,e,r){var n=r(8551),o=r(34),i=r(6043);t.exports=function(t,e){if(n(t),o(e)&&e.constructor===t)return e;var r=i.f(t);return(0,r.resolve)(e),r.promise}},537:function(t,e,r){var n=r(550),o=r(4428),i=r(916).CONSTRUCTOR;t.exports=i||!o((function(t){n.all(t).then(void 0,(function(){}))}))},1056:function(t,e,r){var n=r(4913).f;t.exports=function(t,e,r){r in t||n(t,r,{configurable:!0,get:function(){return e[r]},set:function(t){e[r]=t}})}},8265:function(t){var e=function(){this.head=null,this.tail=null};e.prototype={add:function(t){var e={item:t,next:null},r=this.tail;r?r.next=e:this.head=e,this.tail=e},get:function(){var t=this.head;if(t)return null===(this.head=t.next)&&(this.tail=null),t.item}},t.exports=e},6682:function(t,e,r){var n=r(9565),o=r(8551),i=r(4901),a=r(2195),u=r(7323),s=TypeError;t.exports=function(t,e){var r=t.exec;if(i(r)){var c=n(r,t,e);return null!==c&&o(c),c}if("RegExp"===a(t))return n(u,t,e);throw new s("RegExp#exec called on incompatible receiver")}},7323:function(t,e,r){var n,o,i=r(9565),a=r(9504),u=r(655),s=r(7979),c=r(8429),f=r(5745),p=r(2360),l=r(1181).get,d=r(3635),h=r(8814),v=f("native-string-replace",String.prototype.replace),y=RegExp.prototype.exec,g=y,m=a("".charAt),b=a("".indexOf),T=a("".replace),E=a("".slice),A=(o=/b*/g,i(y,n=/a/,"a"),i(y,o,"a"),0!==n.lastIndex||0!==o.lastIndex),_=c.BROKEN_CARET,w=void 0!==/()??/.exec("")[1];(A||w||_||d||h)&&(g=function(t){var e,r,n,o,a,c,f,d=this,h=l(d),x=u(t),S=h.raw;if(S)return S.lastIndex=d.lastIndex,e=i(g,S,x),d.lastIndex=S.lastIndex,e;var I=h.groups,O=_&&d.sticky,N=i(s,d),M=d.source,D=0,P=x;if(O&&(N=T(N,"y",""),-1===b(N,"g")&&(N+="g"),P=E(x,d.lastIndex),d.lastIndex>0&&(!d.multiline||d.multiline&&"\n"!==m(x,d.lastIndex-1))&&(M="(?: "+M+")",P=" "+P,D++),r=new RegExp("^(?:"+M+")",N)),w&&(r=new RegExp("^"+M+"$(?!\\s)",N)),A&&(n=d.lastIndex),o=i(y,O?r:d,P),O?o?(o.input=E(o.input,D),o[0]=E(o[0],D),o.index=d.lastIndex,d.lastIndex+=o[0].length):d.lastIndex=0:A&&o&&(d.lastIndex=d.global?o.index+o[0].length:n),w&&o&&o.length>1&&i(v,o[0],r,(function(){for(a=1;a<arguments.length-2;a++)void 0===arguments[a]&&(o[a]=void 0)})),o&&I)for(o.groups=c=p(null),a=0;a<I.length;a++)c[(f=I[a])[0]]=o[f[1]];return o}),t.exports=g},7979:function(t,e,r){var n=r(8551);t.exports=function(){var t=n(this),e="";return t.hasIndices&&(e+="d"),t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.unicodeSets&&(e+="v"),t.sticky&&(e+="y"),e}},1034:function(t,e,r){var n=r(9565),o=r(9297),i=r(1625),a=r(7979),u=RegExp.prototype;t.exports=function(t){var e=t.flags;return void 0!==e||"flags"in u||o(t,"flags")||!i(u,t)?e:n(a,t)}},8429:function(t,e,r){var n=r(9039),o=r(4576).RegExp,i=n((function(){var t=o("a","y");return t.lastIndex=2,null!==t.exec("abcd")})),a=i||n((function(){return!o("a","y").sticky})),u=i||n((function(){var t=o("^r","gy");return t.lastIndex=2,null!==t.exec("str")}));t.exports={BROKEN_CARET:u,MISSED_STICKY:a,UNSUPPORTED_Y:i}},3635:function(t,e,r){var n=r(9039),o=r(4576).RegExp;t.exports=n((function(){var t=o(".","s");return!(t.dotAll&&t.test("\n")&&"s"===t.flags)}))},8814:function(t,e,r){var n=r(9039),o=r(4576).RegExp;t.exports=n((function(){var t=o("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")}))},7750:function(t,e,r){var n=r(4117),o=TypeError;t.exports=function(t){if(n(t))throw new o("Can't call method on "+t);return t}},3389:function(t,e,r){var n=r(4576),o=r(3724),i=Object.getOwnPropertyDescriptor;t.exports=function(t){if(!o)return n[t];var e=i(n,t);return e&&e.value}},7633:function(t,e,r){var n=r(7751),o=r(2106),i=r(8227),a=r(3724),u=i("species");t.exports=function(t){var e=n(t);a&&e&&!e[u]&&o(e,u,{configurable:!0,get:function(){return this}})}},687:function(t,e,r){var n=r(4913).f,o=r(9297),i=r(8227)("toStringTag");t.exports=function(t,e,r){t&&!r&&(t=t.prototype),t&&!o(t,i)&&n(t,i,{configurable:!0,value:e})}},6119:function(t,e,r){var n=r(5745),o=r(3392),i=n("keys");t.exports=function(t){return i[t]||(i[t]=o(t))}},7629:function(t,e,r){var n=r(6395),o=r(4576),i=r(9433),a="__core-js_shared__",u=t.exports=o[a]||i(a,{});(u.versions||(u.versions=[])).push({version:"3.39.0",mode:n?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.39.0/LICENSE",source:"https://github.com/zloirock/core-js"})},5745:function(t,e,r){var n=r(7629);t.exports=function(t,e){return n[t]||(n[t]=e||{})}},2293:function(t,e,r){var n=r(8551),o=r(5548),i=r(4117),a=r(8227)("species");t.exports=function(t,e){var r,u=n(t).constructor;return void 0===u||i(r=n(u)[a])?e:o(r)}},8183:function(t,e,r){var n=r(9504),o=r(1291),i=r(655),a=r(7750),u=n("".charAt),s=n("".charCodeAt),c=n("".slice),f=function(t){return function(e,r){var n,f,p=i(a(e)),l=o(r),d=p.length;return l<0||l>=d?t?"":void 0:(n=s(p,l))<55296||n>56319||l+1===d||(f=s(p,l+1))<56320||f>57343?t?u(p,l):n:t?c(p,l,l+2):f-56320+(n-55296<<10)+65536}};t.exports={codeAt:f(!1),charAt:f(!0)}},3802:function(t,e,r){var n=r(9504),o=r(7750),i=r(655),a=r(7452),u=n("".replace),s=RegExp("^["+a+"]+"),c=RegExp("(^|[^"+a+"])["+a+"]+$"),f=function(t){return function(e){var r=i(o(e));return 1&t&&(r=u(r,s,"")),2&t&&(r=u(r,c,"$1")),r}};t.exports={start:f(1),end:f(2),trim:f(3)}},1548:function(t,e,r){var n=r(4576),o=r(9039),i=r(9519),a=r(4215),u=n.structuredClone;t.exports=!!u&&!o((function(){if("DENO"===a&&i>92||"NODE"===a&&i>94||"BROWSER"===a&&i>97)return!1;var t=new ArrayBuffer(8),e=u(t,{transfer:[t]});return 0!==t.byteLength||8!==e.byteLength}))},4495:function(t,e,r){var n=r(9519),o=r(9039),i=r(4576).String;t.exports=!!Object.getOwnPropertySymbols&&!o((function(){var t=Symbol("symbol detection");return!i(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},8242:function(t,e,r){var n=r(9565),o=r(7751),i=r(8227),a=r(6840);t.exports=function(){var t=o("Symbol"),e=t&&t.prototype,r=e&&e.valueOf,u=i("toPrimitive");e&&!e[u]&&a(e,u,(function(t){return n(r,this)}),{arity:1})}},1296:function(t,e,r){var n=r(4495);t.exports=n&&!!Symbol.for&&!!Symbol.keyFor},9225:function(t,e,r){var n,o,i,a,u=r(4576),s=r(8745),c=r(6080),f=r(4901),p=r(9297),l=r(9039),d=r(397),h=r(7680),v=r(4055),y=r(2812),g=r(9544),m=r(8574),b=u.setImmediate,T=u.clearImmediate,E=u.process,A=u.Dispatch,_=u.Function,w=u.MessageChannel,x=u.String,S=0,I={},O="onreadystatechange";l((function(){n=u.location}));var N=function(t){if(p(I,t)){var e=I[t];delete I[t],e()}},M=function(t){return function(){N(t)}},D=function(t){N(t.data)},P=function(t){u.postMessage(x(t),n.protocol+"//"+n.host)};b&&T||(b=function(t){y(arguments.length,1);var e=f(t)?t:_(t),r=h(arguments,1);return I[++S]=function(){s(e,void 0,r)},o(S),S},T=function(t){delete I[t]},m?o=function(t){E.nextTick(M(t))}:A&&A.now?o=function(t){A.now(M(t))}:w&&!g?(a=(i=new w).port2,i.port1.onmessage=D,o=c(a.postMessage,a)):u.addEventListener&&f(u.postMessage)&&!u.importScripts&&n&&"file:"!==n.protocol&&!l(P)?(o=P,u.addEventListener("message",D,!1)):o=O in v("script")?function(t){d.appendChild(v("script"))[O]=function(){d.removeChild(this),N(t)}}:function(t){setTimeout(M(t),0)}),t.exports={set:b,clear:T}},1240:function(t,e,r){var n=r(9504);t.exports=n(1..valueOf)},5610:function(t,e,r){var n=r(1291),o=Math.max,i=Math.min;t.exports=function(t,e){var r=n(t);return r<0?o(r+e,0):i(r,e)}},5854:function(t,e,r){var n=r(2777),o=TypeError;t.exports=function(t){var e=n(t,"number");if("number"==typeof e)throw new o("Can't convert number to bigint");return BigInt(e)}},7696:function(t,e,r){var n=r(1291),o=r(8014),i=RangeError;t.exports=function(t){if(void 0===t)return 0;var e=n(t),r=o(e);if(e!==r)throw new i("Wrong length or index");return r}},5397:function(t,e,r){var n=r(7055),o=r(7750);t.exports=function(t){return n(o(t))}},1291:function(t,e,r){var n=r(741);t.exports=function(t){var e=+t;return e!=e||0===e?0:n(e)}},8014:function(t,e,r){var n=r(1291),o=Math.min;t.exports=function(t){var e=n(t);return e>0?o(e,9007199254740991):0}},8981:function(t,e,r){var n=r(7750),o=Object;t.exports=function(t){return o(n(t))}},8229:function(t,e,r){var n=r(9590),o=RangeError;t.exports=function(t,e){var r=n(t);if(r%e)throw new o("Wrong offset");return r}},9590:function(t,e,r){var n=r(1291),o=RangeError;t.exports=function(t){var e=n(t);if(e<0)throw new o("The argument can't be less than 0");return e}},2777:function(t,e,r){var n=r(9565),o=r(34),i=r(757),a=r(5966),u=r(4270),s=r(8227),c=TypeError,f=s("toPrimitive");t.exports=function(t,e){if(!o(t)||i(t))return t;var r,s=a(t,f);if(s){if(void 0===e&&(e="default"),r=n(s,t,e),!o(r)||i(r))return r;throw new c("Can't convert object to primitive value")}return void 0===e&&(e="number"),u(t,e)}},6969:function(t,e,r){var n=r(2777),o=r(757);t.exports=function(t){var e=n(t,"string");return o(e)?e:e+""}},2140:function(t,e,r){var n={};n[r(8227)("toStringTag")]="z",t.exports="[object z]"===String(n)},655:function(t,e,r){var n=r(6955),o=String;t.exports=function(t){if("Symbol"===n(t))throw new TypeError("Cannot convert a Symbol value to a string");return o(t)}},8319:function(t){var e=Math.round;t.exports=function(t){var r=e(t);return r<0?0:r>255?255:255&r}},6823:function(t){var e=String;t.exports=function(t){try{return e(t)}catch(t){return"Object"}}},5823:function(t,e,r){var n=r(6518),o=r(4576),i=r(9565),a=r(3724),u=r(2805),s=r(4644),c=r(6346),f=r(679),p=r(6980),l=r(6699),d=r(2087),h=r(8014),v=r(7696),y=r(8229),g=r(8319),m=r(6969),b=r(9297),T=r(6955),E=r(34),A=r(757),_=r(2360),w=r(1625),x=r(2967),S=r(8480).f,I=r(3251),O=r(9213).forEach,N=r(7633),M=r(2106),D=r(4913),P=r(7347),C=r(5370),R=r(1181),F=r(3167),B=R.get,L=R.set,j=R.enforce,U=D.f,k=P.f,G=o.RangeError,q=c.ArrayBuffer,V=q.prototype,Y=c.DataView,H=s.NATIVE_ARRAY_BUFFER_VIEWS,W=s.TYPED_ARRAY_TAG,K=s.TypedArray,z=s.TypedArrayPrototype,$=s.isTypedArray,Z="BYTES_PER_ELEMENT",J="Wrong length",X=function(t,e){M(t,e,{configurable:!0,get:function(){return B(this)[e]}})},Q=function(t){var e;return w(V,t)||"ArrayBuffer"===(e=T(t))||"SharedArrayBuffer"===e},tt=function(t,e){return $(t)&&!A(e)&&e in t&&d(+e)&&e>=0},et=function(t,e){return e=m(e),tt(t,e)?p(2,t[e]):k(t,e)},rt=function(t,e,r){return e=m(e),!(tt(t,e)&&E(r)&&b(r,"value"))||b(r,"get")||b(r,"set")||r.configurable||b(r,"writable")&&!r.writable||b(r,"enumerable")&&!r.enumerable?U(t,e,r):(t[e]=r.value,t)};a?(H||(P.f=et,D.f=rt,X(z,"buffer"),X(z,"byteOffset"),X(z,"byteLength"),X(z,"length")),n({target:"Object",stat:!0,forced:!H},{getOwnPropertyDescriptor:et,defineProperty:rt}),t.exports=function(t,e,r){var a=t.match(/\d+/)[0]/8,s=t+(r?"Clamped":"")+"Array",c="get"+t,p="set"+t,d=o[s],m=d,b=m&&m.prototype,T={},A=function(t,e){U(t,e,{get:function(){return function(t,e){var r=B(t);return r.view[c](e*a+r.byteOffset,!0)}(this,e)},set:function(t){return function(t,e,n){var o=B(t);o.view[p](e*a+o.byteOffset,r?g(n):n,!0)}(this,e,t)},enumerable:!0})};H?u&&(m=e((function(t,e,r,n){return f(t,b),F(E(e)?Q(e)?void 0!==n?new d(e,y(r,a),n):void 0!==r?new d(e,y(r,a)):new d(e):$(e)?C(m,e):i(I,m,e):new d(v(e)),t,m)})),x&&x(m,K),O(S(d),(function(t){t in m||l(m,t,d[t])})),m.prototype=b):(m=e((function(t,e,r,n){f(t,b);var o,u,s,c=0,p=0;if(E(e)){if(!Q(e))return $(e)?C(m,e):i(I,m,e);o=e,p=y(r,a);var l=e.byteLength;if(void 0===n){if(l%a)throw new G(J);if((u=l-p)<0)throw new G(J)}else if((u=h(n)*a)+p>l)throw new G(J);s=u/a}else s=v(e),o=new q(u=s*a);for(L(t,{buffer:o,byteOffset:p,byteLength:u,length:s,view:new Y(o)});c<s;)A(t,c++)})),x&&x(m,K),b=m.prototype=_(z)),b.constructor!==m&&l(b,"constructor",m),j(b).TypedArrayConstructor=m,W&&l(b,W,s);var w=m!==d;T[s]=m,n({global:!0,constructor:!0,forced:w,sham:!H},T),Z in m||l(m,Z,a),Z in b||l(b,Z,a),N(s)}):t.exports=function(){}},2805:function(t,e,r){var n=r(4576),o=r(9039),i=r(4428),a=r(4644).NATIVE_ARRAY_BUFFER_VIEWS,u=n.ArrayBuffer,s=n.Int8Array;t.exports=!a||!o((function(){s(1)}))||!o((function(){new s(-1)}))||!i((function(t){new s,new s(null),new s(1.5),new s(t)}),!0)||o((function(){return 1!==new s(new u(2),1,void 0).length}))},9948:function(t,e,r){var n=r(5370),o=r(4644).getTypedArrayConstructor;t.exports=function(t,e){return n(o(t),e)}},3251:function(t,e,r){var n=r(6080),o=r(9565),i=r(5548),a=r(8981),u=r(6198),s=r(81),c=r(851),f=r(4209),p=r(1108),l=r(4644).aTypedArrayConstructor,d=r(5854);t.exports=function(t){var e,r,h,v,y,g,m,b,T=i(this),E=a(t),A=arguments.length,_=A>1?arguments[1]:void 0,w=void 0!==_,x=c(E);if(x&&!f(x))for(b=(m=s(E,x)).next,E=[];!(g=o(b,m)).done;)E.push(g.value);for(w&&A>2&&(_=n(_,arguments[2])),r=u(E),h=new(l(T))(r),v=p(h),e=0;r>e;e++)y=w?_(E[e],e):E[e],h[e]=v?d(y):+y;return h}},3392:function(t,e,r){var n=r(9504),o=0,i=Math.random(),a=n(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+a(++o+i,36)}},7040:function(t,e,r){var n=r(4495);t.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},8686:function(t,e,r){var n=r(3724),o=r(9039);t.exports=n&&o((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},2812:function(t){var e=TypeError;t.exports=function(t,r){if(t<r)throw new e("Not enough arguments");return t}},8622:function(t,e,r){var n=r(4576),o=r(4901),i=n.WeakMap;t.exports=o(i)&&/native code/.test(String(i))},511:function(t,e,r){var n=r(9167),o=r(9297),i=r(1951),a=r(4913).f;t.exports=function(t){var e=n.Symbol||(n.Symbol={});o(e,t)||a(e,t,{value:i.f(t)})}},1951:function(t,e,r){var n=r(8227);e.f=n},8227:function(t,e,r){var n=r(4576),o=r(5745),i=r(9297),a=r(3392),u=r(4495),s=r(7040),c=n.Symbol,f=o("wks"),p=s?c.for||c:c&&c.withoutSetter||a;t.exports=function(t){return i(f,t)||(f[t]=u&&i(c,t)?c[t]:p("Symbol."+t)),f[t]}},7452:function(t){t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},4601:function(t,e,r){var n=r(7751),o=r(9297),i=r(6699),a=r(1625),u=r(2967),s=r(7740),c=r(1056),f=r(3167),p=r(2603),l=r(7584),d=r(747),h=r(3724),v=r(6395);t.exports=function(t,e,r,y){var g="stackTraceLimit",m=y?2:1,b=t.split("."),T=b[b.length-1],E=n.apply(null,b);if(E){var A=E.prototype;if(!v&&o(A,"cause")&&delete A.cause,!r)return E;var _=n("Error"),w=e((function(t,e){var r=p(y?e:t,void 0),n=y?new E(t):new E;return void 0!==r&&i(n,"message",r),d(n,w,n.stack,2),this&&a(A,this)&&f(n,this,w),arguments.length>m&&l(n,arguments[m]),n}));if(w.prototype=A,"Error"!==T?u?u(w,_):s(w,_,{name:!0}):h&&g in E&&(c(w,E,g),c(w,E,"prepareStackTrace")),s(w,E),!v)try{A.name!==T&&i(A,"name",T),A.constructor=w}catch(t){}return w}}},4743:function(t,e,r){var n=r(6518),o=r(4576),i=r(6346),a=r(7633),u="ArrayBuffer",s=i[u];n({global:!0,constructor:!0,forced:o[u]!==s},{ArrayBuffer:s}),a(u)},6573:function(t,e,r){var n=r(3724),o=r(2106),i=r(3238),a=ArrayBuffer.prototype;n&&!("detached"in a)&&o(a,"detached",{configurable:!0,get:function(){return i(this)}})},7936:function(t,e,r){var n=r(6518),o=r(5636);o&&n({target:"ArrayBuffer",proto:!0},{transferToFixedLength:function(){return o(this,arguments.length?arguments[0]:void 0,!1)}})},8100:function(t,e,r){var n=r(6518),o=r(5636);o&&n({target:"ArrayBuffer",proto:!0},{transfer:function(){return o(this,arguments.length?arguments[0]:void 0,!0)}})},8706:function(t,e,r){var n=r(6518),o=r(9039),i=r(4376),a=r(34),u=r(8981),s=r(6198),c=r(6837),f=r(4659),p=r(1469),l=r(597),d=r(8227),h=r(9519),v=d("isConcatSpreadable"),y=h>=51||!o((function(){var t=[];return t[v]=!1,t.concat()[0]!==t})),g=function(t){if(!a(t))return!1;var e=t[v];return void 0!==e?!!e:i(t)};n({target:"Array",proto:!0,arity:1,forced:!y||!l("concat")},{concat:function(t){var e,r,n,o,i,a=u(this),l=p(a,0),d=0;for(e=-1,n=arguments.length;e<n;e++)if(g(i=-1===e?a:arguments[e]))for(o=s(i),c(d+o),r=0;r<o;r++,d++)r in i&&f(l,d,i[r]);else c(d+1),f(l,d++,i);return l.length=d,l}})},2008:function(t,e,r){var n=r(6518),o=r(9213).filter;n({target:"Array",proto:!0,forced:!r(597)("filter")},{filter:function(t){return o(this,t,arguments.length>1?arguments[1]:void 0)}})},3792:function(t,e,r){var n=r(5397),o=r(6469),i=r(6269),a=r(1181),u=r(4913).f,s=r(1088),c=r(2529),f=r(6395),p=r(3724),l="Array Iterator",d=a.set,h=a.getterFor(l);t.exports=s(Array,"Array",(function(t,e){d(this,{type:l,target:n(t),index:0,kind:e})}),(function(){var t=h(this),e=t.target,r=t.index++;if(!e||r>=e.length)return t.target=null,c(void 0,!0);switch(t.kind){case"keys":return c(r,!1);case"values":return c(e[r],!1)}return c([r,e[r]],!1)}),"values");var v=i.Arguments=i.Array;if(o("keys"),o("values"),o("entries"),!f&&p&&"values"!==v.name)try{u(v,"name",{value:"values"})}catch(t){}},8598:function(t,e,r){var n=r(6518),o=r(9504),i=r(7055),a=r(5397),u=r(4598),s=o([].join);n({target:"Array",proto:!0,forced:i!==Object||!u("join",",")},{join:function(t){return s(a(this),void 0===t?",":t)}})},2062:function(t,e,r){var n=r(6518),o=r(9213).map;n({target:"Array",proto:!0,forced:!r(597)("map")},{map:function(t){return o(this,t,arguments.length>1?arguments[1]:void 0)}})},4114:function(t,e,r){var n=r(6518),o=r(8981),i=r(6198),a=r(4527),u=r(6837);n({target:"Array",proto:!0,arity:1,forced:r(9039)((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(t){return t instanceof TypeError}}()},{push:function(t){var e=o(this),r=i(e),n=arguments.length;u(r+n);for(var s=0;s<n;s++)e[r]=arguments[s],r++;return a(e,r),r}})},4782:function(t,e,r){var n=r(6518),o=r(4376),i=r(3517),a=r(34),u=r(5610),s=r(6198),c=r(5397),f=r(4659),p=r(8227),l=r(597),d=r(7680),h=l("slice"),v=p("species"),y=Array,g=Math.max;n({target:"Array",proto:!0,forced:!h},{slice:function(t,e){var r,n,p,l=c(this),h=s(l),m=u(t,h),b=u(void 0===e?h:e,h);if(o(l)&&(r=l.constructor,(i(r)&&(r===y||o(r.prototype))||a(r)&&null===(r=r[v]))&&(r=void 0),r===y||void 0===r))return d(l,m,b);for(n=new(void 0===r?y:r)(g(b-m,0)),p=0;m<b;m++,p++)m in l&&f(n,p,l[m]);return n.length=p,n}})},4554:function(t,e,r){var n=r(6518),o=r(8981),i=r(5610),a=r(1291),u=r(6198),s=r(4527),c=r(6837),f=r(1469),p=r(4659),l=r(4606),d=r(597)("splice"),h=Math.max,v=Math.min;n({target:"Array",proto:!0,forced:!d},{splice:function(t,e){var r,n,d,y,g,m,b=o(this),T=u(b),E=i(t,T),A=arguments.length;for(0===A?r=n=0:1===A?(r=0,n=T-E):(r=A-2,n=v(h(a(e),0),T-E)),c(T+r-n),d=f(b,n),y=0;y<n;y++)(g=E+y)in b&&p(d,y,b[g]);if(d.length=n,r<n){for(y=E;y<T-n;y++)m=y+r,(g=y+n)in b?b[m]=b[g]:l(b,m);for(y=T;y>T-n+r;y--)l(b,y-1)}else if(r>n)for(y=T-n;y>E;y--)m=y+r-1,(g=y+n-1)in b?b[m]=b[g]:l(b,m);for(y=0;y<r;y++)b[y+E]=arguments[y+2];return s(b,T-n+r),d}})},9572:function(t,e,r){var n=r(9297),o=r(6840),i=r(3640),a=r(8227)("toPrimitive"),u=Date.prototype;n(u,a)||o(u,a,i)},6280:function(t,e,r){var n=r(6518),o=r(4576),i=r(8745),a=r(4601),u="WebAssembly",s=o[u],c=7!==new Error("e",{cause:7}).cause,f=function(t,e){var r={};r[t]=a(t,e,c),n({global:!0,constructor:!0,arity:1,forced:c},r)},p=function(t,e){if(s&&s[t]){var r={};r[t]=a(u+"."+t,e,c),n({target:u,stat:!0,constructor:!0,arity:1,forced:c},r)}};f("Error",(function(t){return function(e){return i(t,this,arguments)}})),f("EvalError",(function(t){return function(e){return i(t,this,arguments)}})),f("RangeError",(function(t){return function(e){return i(t,this,arguments)}})),f("ReferenceError",(function(t){return function(e){return i(t,this,arguments)}})),f("SyntaxError",(function(t){return function(e){return i(t,this,arguments)}})),f("TypeError",(function(t){return function(e){return i(t,this,arguments)}})),f("URIError",(function(t){return function(e){return i(t,this,arguments)}})),p("CompileError",(function(t){return function(e){return i(t,this,arguments)}})),p("LinkError",(function(t){return function(e){return i(t,this,arguments)}})),p("RuntimeError",(function(t){return function(e){return i(t,this,arguments)}}))},2010:function(t,e,r){var n=r(3724),o=r(350).EXISTS,i=r(9504),a=r(2106),u=Function.prototype,s=i(u.toString),c=/function\b(?:\s|\/\*[\S\s]*?\*\/|\/\/[^\n\r]*[\n\r]+)*([^\s(/]*)/,f=i(c.exec);n&&!o&&a(u,"name",{configurable:!0,get:function(){try{return f(c,s(this))[1]}catch(t){return""}}})},3110:function(t,e,r){var n=r(6518),o=r(7751),i=r(8745),a=r(9565),u=r(9504),s=r(9039),c=r(4901),f=r(757),p=r(7680),l=r(6933),d=r(4495),h=String,v=o("JSON","stringify"),y=u(/./.exec),g=u("".charAt),m=u("".charCodeAt),b=u("".replace),T=u(1..toString),E=/[\uD800-\uDFFF]/g,A=/^[\uD800-\uDBFF]$/,_=/^[\uDC00-\uDFFF]$/,w=!d||s((function(){var t=o("Symbol")("stringify detection");return"[null]"!==v([t])||"{}"!==v({a:t})||"{}"!==v(Object(t))})),x=s((function(){return'"\\udf06\\ud834"'!==v("\udf06\ud834")||'"\\udead"'!==v("\udead")})),S=function(t,e){var r=p(arguments),n=l(e);if(c(n)||void 0!==t&&!f(t))return r[1]=function(t,e){if(c(n)&&(e=a(n,this,h(t),e)),!f(e))return e},i(v,null,r)},I=function(t,e,r){var n=g(r,e-1),o=g(r,e+1);return y(A,t)&&!y(_,o)||y(_,t)&&!y(A,n)?"\\u"+T(m(t,0),16):t};v&&n({target:"JSON",stat:!0,arity:3,forced:w||x},{stringify:function(t,e,r){var n=p(arguments),o=i(w?S:v,null,n);return x&&"string"==typeof o?b(o,E,I):o}})},2892:function(t,e,r){var n=r(6518),o=r(6395),i=r(3724),a=r(4576),u=r(9167),s=r(9504),c=r(2796),f=r(9297),p=r(3167),l=r(1625),d=r(757),h=r(2777),v=r(9039),y=r(8480).f,g=r(7347).f,m=r(4913).f,b=r(1240),T=r(3802).trim,E="Number",A=a[E],_=u[E],w=A.prototype,x=a.TypeError,S=s("".slice),I=s("".charCodeAt),O=c(E,!A(" 0o1")||!A("0b1")||A("+0x1")),N=function(t){var e,r=arguments.length<1?0:A(function(t){var e=h(t,"number");return"bigint"==typeof e?e:function(t){var e,r,n,o,i,a,u,s,c=h(t,"number");if(d(c))throw new x("Cannot convert a Symbol value to a number");if("string"==typeof c&&c.length>2)if(c=T(c),43===(e=I(c,0))||45===e){if(88===(r=I(c,2))||120===r)return NaN}else if(48===e){switch(I(c,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+c}for(a=(i=S(c,2)).length,u=0;u<a;u++)if((s=I(i,u))<48||s>o)return NaN;return parseInt(i,n)}return+c}(e)}(t));return l(w,e=this)&&v((function(){b(e)}))?p(Object(r),this,N):r};N.prototype=w,O&&!o&&(w.constructor=N),n({global:!0,constructor:!0,wrap:!0,forced:O},{Number:N});var M=function(t,e){for(var r,n=i?y(e):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),o=0;n.length>o;o++)f(e,r=n[o])&&!f(t,r)&&m(t,r,g(e,r))};o&&_&&M(u[E],_),(O||o)&&M(u[E],A)},6982:function(t,e,r){r(6518)({target:"Number",stat:!0,nonConfigurable:!0,nonWritable:!0},{MAX_SAFE_INTEGER:9007199254740991})},9773:function(t,e,r){var n=r(6518),o=r(4495),i=r(9039),a=r(3717),u=r(8981);n({target:"Object",stat:!0,forced:!o||i((function(){a.f(1)}))},{getOwnPropertySymbols:function(t){var e=a.f;return e?e(u(t)):[]}})},875:function(t,e,r){var n=r(6518),o=r(9039),i=r(8981),a=r(2787),u=r(2211);n({target:"Object",stat:!0,forced:o((function(){a(1)})),sham:!u},{getPrototypeOf:function(t){return a(i(t))}})},6099:function(t,e,r){var n=r(2140),o=r(6840),i=r(3179);n||o(Object.prototype,"toString",i,{unsafe:!0})},6499:function(t,e,r){var n=r(6518),o=r(9565),i=r(9306),a=r(6043),u=r(1103),s=r(2652);n({target:"Promise",stat:!0,forced:r(537)},{all:function(t){var e=this,r=a.f(e),n=r.resolve,c=r.reject,f=u((function(){var r=i(e.resolve),a=[],u=0,f=1;s(t,(function(t){var i=u++,s=!1;f++,o(r,e,t).then((function(t){s||(s=!0,a[i]=t,--f||n(a))}),c)})),--f||n(a)}));return f.error&&c(f.value),r.promise}})},2003:function(t,e,r){var n=r(6518),o=r(6395),i=r(916).CONSTRUCTOR,a=r(550),u=r(7751),s=r(4901),c=r(6840),f=a&&a.prototype;if(n({target:"Promise",proto:!0,forced:i,real:!0},{catch:function(t){return this.then(void 0,t)}}),!o&&s(a)){var p=u("Promise").prototype.catch;f.catch!==p&&c(f,"catch",p,{unsafe:!0})}},436:function(t,e,r){var n,o,i,a=r(6518),u=r(6395),s=r(8574),c=r(4576),f=r(9565),p=r(6840),l=r(2967),d=r(687),h=r(7633),v=r(9306),y=r(4901),g=r(34),m=r(679),b=r(2293),T=r(9225).set,E=r(1955),A=r(3138),_=r(1103),w=r(8265),x=r(1181),S=r(550),I=r(916),O=r(6043),N="Promise",M=I.CONSTRUCTOR,D=I.REJECTION_EVENT,P=I.SUBCLASSING,C=x.getterFor(N),R=x.set,F=S&&S.prototype,B=S,L=F,j=c.TypeError,U=c.document,k=c.process,G=O.f,q=G,V=!!(U&&U.createEvent&&c.dispatchEvent),Y="unhandledrejection",H=function(t){var e;return!(!g(t)||!y(e=t.then))&&e},W=function(t,e){var r,n,o,i=e.value,a=1===e.state,u=a?t.ok:t.fail,s=t.resolve,c=t.reject,p=t.domain;try{u?(a||(2===e.rejection&&J(e),e.rejection=1),!0===u?r=i:(p&&p.enter(),r=u(i),p&&(p.exit(),o=!0)),r===t.promise?c(new j("Promise-chain cycle")):(n=H(r))?f(n,r,s,c):s(r)):c(i)}catch(t){p&&!o&&p.exit(),c(t)}},K=function(t,e){t.notified||(t.notified=!0,E((function(){for(var r,n=t.reactions;r=n.get();)W(r,t);t.notified=!1,e&&!t.rejection&&$(t)})))},z=function(t,e,r){var n,o;V?((n=U.createEvent("Event")).promise=e,n.reason=r,n.initEvent(t,!1,!0),c.dispatchEvent(n)):n={promise:e,reason:r},!D&&(o=c["on"+t])?o(n):t===Y&&A("Unhandled promise rejection",r)},$=function(t){f(T,c,(function(){var e,r=t.facade,n=t.value;if(Z(t)&&(e=_((function(){s?k.emit("unhandledRejection",n,r):z(Y,r,n)})),t.rejection=s||Z(t)?2:1,e.error))throw e.value}))},Z=function(t){return 1!==t.rejection&&!t.parent},J=function(t){f(T,c,(function(){var e=t.facade;s?k.emit("rejectionHandled",e):z("rejectionhandled",e,t.value)}))},X=function(t,e,r){return function(n){t(e,n,r)}},Q=function(t,e,r){t.done||(t.done=!0,r&&(t=r),t.value=e,t.state=2,K(t,!0))},tt=function(t,e,r){if(!t.done){t.done=!0,r&&(t=r);try{if(t.facade===e)throw new j("Promise can't be resolved itself");var n=H(e);n?E((function(){var r={done:!1};try{f(n,e,X(tt,r,t),X(Q,r,t))}catch(e){Q(r,e,t)}})):(t.value=e,t.state=1,K(t,!1))}catch(e){Q({done:!1},e,t)}}};if(M&&(L=(B=function(t){m(this,L),v(t),f(n,this);var e=C(this);try{t(X(tt,e),X(Q,e))}catch(t){Q(e,t)}}).prototype,(n=function(t){R(this,{type:N,done:!1,notified:!1,parent:!1,reactions:new w,rejection:!1,state:0,value:null})}).prototype=p(L,"then",(function(t,e){var r=C(this),n=G(b(this,B));return r.parent=!0,n.ok=!y(t)||t,n.fail=y(e)&&e,n.domain=s?k.domain:void 0,0===r.state?r.reactions.add(n):E((function(){W(n,r)})),n.promise})),o=function(){var t=new n,e=C(t);this.promise=t,this.resolve=X(tt,e),this.reject=X(Q,e)},O.f=G=function(t){return t===B||void 0===t?new o(t):q(t)},!u&&y(S)&&F!==Object.prototype)){i=F.then,P||p(F,"then",(function(t,e){var r=this;return new B((function(t,e){f(i,r,t,e)})).then(t,e)}),{unsafe:!0});try{delete F.constructor}catch(t){}l&&l(F,L)}a({global:!0,constructor:!0,wrap:!0,forced:M},{Promise:B}),d(B,N,!1,!0),h(N)},3362:function(t,e,r){r(436),r(6499),r(2003),r(7743),r(1481),r(280)},7743:function(t,e,r){var n=r(6518),o=r(9565),i=r(9306),a=r(6043),u=r(1103),s=r(2652);n({target:"Promise",stat:!0,forced:r(537)},{race:function(t){var e=this,r=a.f(e),n=r.reject,c=u((function(){var a=i(e.resolve);s(t,(function(t){o(a,e,t).then(r.resolve,n)}))}));return c.error&&n(c.value),r.promise}})},1481:function(t,e,r){var n=r(6518),o=r(6043);n({target:"Promise",stat:!0,forced:r(916).CONSTRUCTOR},{reject:function(t){var e=o.f(this);return(0,e.reject)(t),e.promise}})},280:function(t,e,r){var n=r(6518),o=r(7751),i=r(6395),a=r(550),u=r(916).CONSTRUCTOR,s=r(3438),c=o("Promise"),f=i&&!u;n({target:"Promise",stat:!0,forced:i||u},{resolve:function(t){return s(f&&this===c?a:this,t)}})},825:function(t,e,r){var n=r(6518),o=r(7751),i=r(8745),a=r(566),u=r(5548),s=r(8551),c=r(34),f=r(2360),p=r(9039),l=o("Reflect","construct"),d=Object.prototype,h=[].push,v=p((function(){function t(){}return!(l((function(){}),[],t)instanceof t)})),y=!p((function(){l((function(){}))})),g=v||y;n({target:"Reflect",stat:!0,forced:g,sham:g},{construct:function(t,e){u(t),s(e);var r=arguments.length<3?t:u(arguments[2]);if(y&&!v)return l(t,e,r);if(t===r){switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3])}var n=[null];return i(h,n,e),new(i(a,t,n))}var o=r.prototype,p=f(c(o)?o:d),g=i(t,p,e);return c(g)?g:p}})},7495:function(t,e,r){var n=r(6518),o=r(7323);n({target:"RegExp",proto:!0,forced:/./.exec!==o},{exec:o})},9479:function(t,e,r){var n=r(4576),o=r(3724),i=r(2106),a=r(7979),u=r(9039),s=n.RegExp,c=s.prototype;o&&u((function(){var t=!0;try{s(".","d")}catch(e){t=!1}var e={},r="",n=t?"dgimsy":"gimsy",o=function(t,n){Object.defineProperty(e,t,{get:function(){return r+=n,!0}})},i={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};for(var a in t&&(i.hasIndices="d"),i)o(a,i[a]);return Object.getOwnPropertyDescriptor(c,"flags").get.call(e)!==n||r!==n}))&&i(c,"flags",{configurable:!0,get:a})},906:function(t,e,r){r(7495);var n,o,i=r(6518),a=r(9565),u=r(4901),s=r(8551),c=r(655),f=(n=!1,(o=/[ac]/).exec=function(){return n=!0,/./.exec.apply(this,arguments)},!0===o.test("abc")&&n),p=/./.test;i({target:"RegExp",proto:!0,forced:!f},{test:function(t){var e=s(this),r=c(t),n=e.exec;if(!u(n))return a(p,e,r);var o=a(n,e,r);return null!==o&&(s(o),!0)}})},8781:function(t,e,r){var n=r(350).PROPER,o=r(6840),i=r(8551),a=r(655),u=r(9039),s=r(1034),c="toString",f=RegExp.prototype,p=f[c],l=u((function(){return"/a/b"!==p.call({source:"a",flags:"b"})})),d=n&&p.name!==c;(l||d)&&o(f,c,(function(){var t=i(this);return"/"+a(t.source)+"/"+a(s(t))}),{unsafe:!0})},7764:function(t,e,r){var n=r(8183).charAt,o=r(655),i=r(1181),a=r(1088),u=r(2529),s="String Iterator",c=i.set,f=i.getterFor(s);a(String,"String",(function(t){c(this,{type:s,string:o(t),index:0})}),(function(){var t,e=f(this),r=e.string,o=e.index;return o>=r.length?u(void 0,!0):(t=n(r,o),e.index+=t.length,u(t,!1))}))},5440:function(t,e,r){var n=r(8745),o=r(9565),i=r(9504),a=r(9228),u=r(9039),s=r(8551),c=r(4901),f=r(4117),p=r(1291),l=r(8014),d=r(655),h=r(7750),v=r(7829),y=r(5966),g=r(2478),m=r(6682),b=r(8227)("replace"),T=Math.max,E=Math.min,A=i([].concat),_=i([].push),w=i("".indexOf),x=i("".slice),S="$0"==="a".replace(/./,"$0"),I=!!/./[b]&&""===/./[b]("a","$0");a("replace",(function(t,e,r){var i=I?"$":"$0";return[function(t,r){var n=h(this),i=f(t)?void 0:y(t,b);return i?o(i,t,n,r):o(e,d(n),t,r)},function(t,o){var a=s(this),u=d(t);if("string"==typeof o&&-1===w(o,i)&&-1===w(o,"$<")){var f=r(e,a,u,o);if(f.done)return f.value}var h=c(o);h||(o=d(o));var y,b=a.global;b&&(y=a.unicode,a.lastIndex=0);for(var S,I=[];null!==(S=m(a,u))&&(_(I,S),b);)""===d(S[0])&&(a.lastIndex=v(u,l(a.lastIndex),y));for(var O,N="",M=0,D=0;D<I.length;D++){for(var P,C=d((S=I[D])[0]),R=T(E(p(S.index),u.length),0),F=[],B=1;B<S.length;B++)_(F,void 0===(O=S[B])?O:String(O));var L=S.groups;if(h){var j=A([C],F,R,u);void 0!==L&&_(j,L),P=d(n(o,void 0,j))}else P=g(C,u,R,F,L,o);R>=M&&(N+=x(u,M,R)+P,M=R+C.length)}return N+x(u,M)}]}),!!u((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!S||I)},744:function(t,e,r){var n=r(9565),o=r(9504),i=r(9228),a=r(8551),u=r(4117),s=r(7750),c=r(2293),f=r(7829),p=r(8014),l=r(655),d=r(5966),h=r(6682),v=r(8429),y=r(9039),g=v.UNSUPPORTED_Y,m=Math.min,b=o([].push),T=o("".slice),E=!y((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var r="ab".split(t);return 2!==r.length||"a"!==r[0]||"b"!==r[1]})),A="c"==="abbc".split(/(b)*/)[1]||4!=="test".split(/(?:)/,-1).length||2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length;i("split",(function(t,e,r){var o="0".split(void 0,0).length?function(t,r){return void 0===t&&0===r?[]:n(e,this,t,r)}:e;return[function(e,r){var i=s(this),a=u(e)?void 0:d(e,t);return a?n(a,e,i,r):n(o,l(i),e,r)},function(t,n){var i=a(this),u=l(t);if(!A){var s=r(o,i,u,n,o!==e);if(s.done)return s.value}var d=c(i,RegExp),v=i.unicode,y=(i.ignoreCase?"i":"")+(i.multiline?"m":"")+(i.unicode?"u":"")+(g?"g":"y"),E=new d(g?"^(?:"+i.source+")":i,y),_=void 0===n?4294967295:n>>>0;if(0===_)return[];if(0===u.length)return null===h(E,u)?[u]:[];for(var w=0,x=0,S=[];x<u.length;){E.lastIndex=g?0:x;var I,O=h(E,g?T(u,x):u);if(null===O||(I=m(p(E.lastIndex+(g?x:0)),u.length))===w)x=f(u,x,v);else{if(b(S,T(u,w,x)),S.length===_)return S;for(var N=1;N<=O.length-1;N++)if(b(S,O[N]),S.length===_)return S;x=w=I}}return b(S,T(u,w)),S}]}),A||!E,g)},6761:function(t,e,r){var n=r(6518),o=r(4576),i=r(9565),a=r(9504),u=r(6395),s=r(3724),c=r(4495),f=r(9039),p=r(9297),l=r(1625),d=r(8551),h=r(5397),v=r(6969),y=r(655),g=r(6980),m=r(2360),b=r(1072),T=r(8480),E=r(298),A=r(3717),_=r(7347),w=r(4913),x=r(6801),S=r(8773),I=r(6840),O=r(2106),N=r(5745),M=r(6119),D=r(421),P=r(3392),C=r(8227),R=r(1951),F=r(511),B=r(8242),L=r(687),j=r(1181),U=r(9213).forEach,k=M("hidden"),G="Symbol",q="prototype",V=j.set,Y=j.getterFor(G),H=Object[q],W=o.Symbol,K=W&&W[q],z=o.RangeError,$=o.TypeError,Z=o.QObject,J=_.f,X=w.f,Q=E.f,tt=S.f,et=a([].push),rt=N("symbols"),nt=N("op-symbols"),ot=N("wks"),it=!Z||!Z[q]||!Z[q].findChild,at=function(t,e,r){var n=J(H,e);n&&delete H[e],X(t,e,r),n&&t!==H&&X(H,e,n)},ut=s&&f((function(){return 7!==m(X({},"a",{get:function(){return X(this,"a",{value:7}).a}})).a}))?at:X,st=function(t,e){var r=rt[t]=m(K);return V(r,{type:G,tag:t,description:e}),s||(r.description=e),r},ct=function(t,e,r){t===H&&ct(nt,e,r),d(t);var n=v(e);return d(r),p(rt,n)?(r.enumerable?(p(t,k)&&t[k][n]&&(t[k][n]=!1),r=m(r,{enumerable:g(0,!1)})):(p(t,k)||X(t,k,g(1,m(null))),t[k][n]=!0),ut(t,n,r)):X(t,n,r)},ft=function(t,e){d(t);var r=h(e),n=b(r).concat(ht(r));return U(n,(function(e){s&&!i(pt,r,e)||ct(t,e,r[e])})),t},pt=function(t){var e=v(t),r=i(tt,this,e);return!(this===H&&p(rt,e)&&!p(nt,e))&&(!(r||!p(this,e)||!p(rt,e)||p(this,k)&&this[k][e])||r)},lt=function(t,e){var r=h(t),n=v(e);if(r!==H||!p(rt,n)||p(nt,n)){var o=J(r,n);return!o||!p(rt,n)||p(r,k)&&r[k][n]||(o.enumerable=!0),o}},dt=function(t){var e=Q(h(t)),r=[];return U(e,(function(t){p(rt,t)||p(D,t)||et(r,t)})),r},ht=function(t){var e=t===H,r=Q(e?nt:h(t)),n=[];return U(r,(function(t){!p(rt,t)||e&&!p(H,t)||et(n,rt[t])})),n};c||(W=function(){if(l(K,this))throw new $("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?y(arguments[0]):void 0,e=P(t),r=function(t){var n=void 0===this?o:this;n===H&&i(r,nt,t),p(n,k)&&p(n[k],e)&&(n[k][e]=!1);var a=g(1,t);try{ut(n,e,a)}catch(t){if(!(t instanceof z))throw t;at(n,e,a)}};return s&&it&&ut(H,e,{configurable:!0,set:r}),st(e,t)},I(K=W[q],"toString",(function(){return Y(this).tag})),I(W,"withoutSetter",(function(t){return st(P(t),t)})),S.f=pt,w.f=ct,x.f=ft,_.f=lt,T.f=E.f=dt,A.f=ht,R.f=function(t){return st(C(t),t)},s&&(O(K,"description",{configurable:!0,get:function(){return Y(this).description}}),u||I(H,"propertyIsEnumerable",pt,{unsafe:!0}))),n({global:!0,constructor:!0,wrap:!0,forced:!c,sham:!c},{Symbol:W}),U(b(ot),(function(t){F(t)})),n({target:G,stat:!0,forced:!c},{useSetter:function(){it=!0},useSimple:function(){it=!1}}),n({target:"Object",stat:!0,forced:!c,sham:!s},{create:function(t,e){return void 0===e?m(t):ft(m(t),e)},defineProperty:ct,defineProperties:ft,getOwnPropertyDescriptor:lt}),n({target:"Object",stat:!0,forced:!c},{getOwnPropertyNames:dt}),B(),L(W,G),D[k]=!0},9463:function(t,e,r){var n=r(6518),o=r(3724),i=r(4576),a=r(9504),u=r(9297),s=r(4901),c=r(1625),f=r(655),p=r(2106),l=r(7740),d=i.Symbol,h=d&&d.prototype;if(o&&s(d)&&(!("description"in h)||void 0!==d().description)){var v={},y=function(){var t=arguments.length<1||void 0===arguments[0]?void 0:f(arguments[0]),e=c(h,this)?new d(t):void 0===t?d():d(t);return""===t&&(v[e]=!0),e};l(y,d),y.prototype=h,h.constructor=y;var g="Symbol(description detection)"===String(d("description detection")),m=a(h.valueOf),b=a(h.toString),T=/^Symbol\((.*)\)[^)]+$/,E=a("".replace),A=a("".slice);p(h,"description",{configurable:!0,get:function(){var t=m(this);if(u(v,t))return"";var e=b(t),r=g?A(e,7,-1):E(e,T,"$1");return""===r?void 0:r}}),n({global:!0,constructor:!0,forced:!0},{Symbol:y})}},1510:function(t,e,r){var n=r(6518),o=r(7751),i=r(9297),a=r(655),u=r(5745),s=r(1296),c=u("string-to-symbol-registry"),f=u("symbol-to-string-registry");n({target:"Symbol",stat:!0,forced:!s},{for:function(t){var e=a(t);if(i(c,e))return c[e];var r=o("Symbol")(e);return c[e]=r,f[r]=e,r}})},2259:function(t,e,r){r(511)("iterator")},2675:function(t,e,r){r(6761),r(1510),r(7812),r(3110),r(9773)},7812:function(t,e,r){var n=r(6518),o=r(9297),i=r(757),a=r(6823),u=r(5745),s=r(1296),c=u("symbol-to-string-registry");n({target:"Symbol",stat:!0,forced:!s},{keyFor:function(t){if(!i(t))throw new TypeError(a(t)+" is not a symbol");if(o(c,t))return c[t]}})},5700:function(t,e,r){var n=r(511),o=r(8242);n("toPrimitive"),o()},8140:function(t,e,r){var n=r(4644),o=r(6198),i=r(1291),a=n.aTypedArray;(0,n.exportTypedArrayMethod)("at",(function(t){var e=a(this),r=o(e),n=i(t),u=n>=0?n:r+n;return u<0||u>=r?void 0:e[u]}))},1630:function(t,e,r){var n=r(9504),o=r(4644),i=n(r(7029)),a=o.aTypedArray;(0,o.exportTypedArrayMethod)("copyWithin",(function(t,e){return i(a(this),t,e,arguments.length>2?arguments[2]:void 0)}))},2170:function(t,e,r){var n=r(4644),o=r(9213).every,i=n.aTypedArray;(0,n.exportTypedArrayMethod)("every",(function(t){return o(i(this),t,arguments.length>1?arguments[1]:void 0)}))},5044:function(t,e,r){var n=r(4644),o=r(4373),i=r(5854),a=r(6955),u=r(9565),s=r(9504),c=r(9039),f=n.aTypedArray,p=n.exportTypedArrayMethod,l=s("".slice);p("fill",(function(t){var e=arguments.length;f(this);var r="Big"===l(a(this),0,3)?i(t):+t;return u(o,this,r,e>1?arguments[1]:void 0,e>2?arguments[2]:void 0)}),c((function(){var t=0;return new Int8Array(2).fill({valueOf:function(){return t++}}),1!==t})))},1920:function(t,e,r){var n=r(4644),o=r(9213).filter,i=r(9948),a=n.aTypedArray;(0,n.exportTypedArrayMethod)("filter",(function(t){var e=o(a(this),t,arguments.length>1?arguments[1]:void 0);return i(this,e)}))},9955:function(t,e,r){var n=r(4644),o=r(9213).findIndex,i=n.aTypedArray;(0,n.exportTypedArrayMethod)("findIndex",(function(t){return o(i(this),t,arguments.length>1?arguments[1]:void 0)}))},1134:function(t,e,r){var n=r(4644),o=r(3839).findLastIndex,i=n.aTypedArray;(0,n.exportTypedArrayMethod)("findLastIndex",(function(t){return o(i(this),t,arguments.length>1?arguments[1]:void 0)}))},1903:function(t,e,r){var n=r(4644),o=r(3839).findLast,i=n.aTypedArray;(0,n.exportTypedArrayMethod)("findLast",(function(t){return o(i(this),t,arguments.length>1?arguments[1]:void 0)}))},1694:function(t,e,r){var n=r(4644),o=r(9213).find,i=n.aTypedArray;(0,n.exportTypedArrayMethod)("find",(function(t){return o(i(this),t,arguments.length>1?arguments[1]:void 0)}))},3206:function(t,e,r){var n=r(4644),o=r(9213).forEach,i=n.aTypedArray;(0,n.exportTypedArrayMethod)("forEach",(function(t){o(i(this),t,arguments.length>1?arguments[1]:void 0)}))},4496:function(t,e,r){var n=r(4644),o=r(9617).includes,i=n.aTypedArray;(0,n.exportTypedArrayMethod)("includes",(function(t){return o(i(this),t,arguments.length>1?arguments[1]:void 0)}))},6651:function(t,e,r){var n=r(4644),o=r(9617).indexOf,i=n.aTypedArray;(0,n.exportTypedArrayMethod)("indexOf",(function(t){return o(i(this),t,arguments.length>1?arguments[1]:void 0)}))},2887:function(t,e,r){var n=r(4576),o=r(9039),i=r(9504),a=r(4644),u=r(3792),s=r(8227)("iterator"),c=n.Uint8Array,f=i(u.values),p=i(u.keys),l=i(u.entries),d=a.aTypedArray,h=a.exportTypedArrayMethod,v=c&&c.prototype,y=!o((function(){v[s].call([1])})),g=!!v&&v.values&&v[s]===v.values&&"values"===v.values.name,m=function(){return f(d(this))};h("entries",(function(){return l(d(this))}),y),h("keys",(function(){return p(d(this))}),y),h("values",m,y||!g,{name:"values"}),h(s,m,y||!g,{name:"values"})},9369:function(t,e,r){var n=r(4644),o=r(9504),i=n.aTypedArray,a=n.exportTypedArrayMethod,u=o([].join);a("join",(function(t){return u(i(this),t)}))},6812:function(t,e,r){var n=r(4644),o=r(8745),i=r(8379),a=n.aTypedArray;(0,n.exportTypedArrayMethod)("lastIndexOf",(function(t){var e=arguments.length;return o(i,a(this),e>1?[t,arguments[1]]:[t])}))},8995:function(t,e,r){var n=r(4644),o=r(9213).map,i=n.aTypedArray,a=n.getTypedArrayConstructor;(0,n.exportTypedArrayMethod)("map",(function(t){return o(i(this),t,arguments.length>1?arguments[1]:void 0,(function(t,e){return new(a(t))(e)}))}))},6072:function(t,e,r){var n=r(4644),o=r(926).right,i=n.aTypedArray;(0,n.exportTypedArrayMethod)("reduceRight",(function(t){var e=arguments.length;return o(i(this),t,e,e>1?arguments[1]:void 0)}))},1575:function(t,e,r){var n=r(4644),o=r(926).left,i=n.aTypedArray;(0,n.exportTypedArrayMethod)("reduce",(function(t){var e=arguments.length;return o(i(this),t,e,e>1?arguments[1]:void 0)}))},8747:function(t,e,r){var n=r(4644),o=n.aTypedArray,i=n.exportTypedArrayMethod,a=Math.floor;i("reverse",(function(){for(var t,e=this,r=o(e).length,n=a(r/2),i=0;i<n;)t=e[i],e[i++]=e[--r],e[r]=t;return e}))},8845:function(t,e,r){var n=r(4576),o=r(9565),i=r(4644),a=r(6198),u=r(8229),s=r(8981),c=r(9039),f=n.RangeError,p=n.Int8Array,l=p&&p.prototype,d=l&&l.set,h=i.aTypedArray,v=i.exportTypedArrayMethod,y=!c((function(){var t=new Uint8ClampedArray(2);return o(d,t,{length:1,0:3},1),3!==t[1]})),g=y&&i.NATIVE_ARRAY_BUFFER_VIEWS&&c((function(){var t=new p(2);return t.set(1),t.set("2",1),0!==t[0]||2!==t[1]}));v("set",(function(t){h(this);var e=u(arguments.length>1?arguments[1]:void 0,1),r=s(t);if(y)return o(d,this,r,e);var n=this.length,i=a(r),c=0;if(i+e>n)throw new f("Wrong length");for(;c<i;)this[e+c]=r[c++]}),!y||g)},9423:function(t,e,r){var n=r(4644),o=r(9039),i=r(7680),a=n.aTypedArray,u=n.getTypedArrayConstructor;(0,n.exportTypedArrayMethod)("slice",(function(t,e){for(var r=i(a(this),t,e),n=u(this),o=0,s=r.length,c=new n(s);s>o;)c[o]=r[o++];return c}),o((function(){new Int8Array(1).slice()})))},7301:function(t,e,r){var n=r(4644),o=r(9213).some,i=n.aTypedArray;(0,n.exportTypedArrayMethod)("some",(function(t){return o(i(this),t,arguments.length>1?arguments[1]:void 0)}))},373:function(t,e,r){var n=r(4576),o=r(7476),i=r(9039),a=r(9306),u=r(4488),s=r(4644),c=r(3709),f=r(3763),p=r(9519),l=r(3607),d=s.aTypedArray,h=s.exportTypedArrayMethod,v=n.Uint16Array,y=v&&o(v.prototype.sort),g=!(!y||i((function(){y(new v(2),null)}))&&i((function(){y(new v(2),{})}))),m=!!y&&!i((function(){if(p)return p<74;if(c)return c<67;if(f)return!0;if(l)return l<602;var t,e,r=new v(516),n=Array(516);for(t=0;t<516;t++)e=t%4,r[t]=515-t,n[t]=t-2*e+3;for(y(r,(function(t,e){return(t/4|0)-(e/4|0)})),t=0;t<516;t++)if(r[t]!==n[t])return!0}));h("sort",(function(t){return void 0!==t&&a(t),m?y(this,t):u(d(this),function(t){return function(e,r){return void 0!==t?+t(e,r)||0:r!=r?-1:e!=e?1:0===e&&0===r?1/e>0&&1/r<0?1:-1:e>r}}(t))}),!m||g)},6614:function(t,e,r){var n=r(4644),o=r(8014),i=r(5610),a=n.aTypedArray,u=n.getTypedArrayConstructor;(0,n.exportTypedArrayMethod)("subarray",(function(t,e){var r=a(this),n=r.length,s=i(t,n);return new(u(r))(r.buffer,r.byteOffset+s*r.BYTES_PER_ELEMENT,o((void 0===e?n:i(e,n))-s))}))},1405:function(t,e,r){var n=r(4576),o=r(8745),i=r(4644),a=r(9039),u=r(7680),s=n.Int8Array,c=i.aTypedArray,f=i.exportTypedArrayMethod,p=[].toLocaleString,l=!!s&&a((function(){p.call(new s(1))}));f("toLocaleString",(function(){return o(p,l?u(c(this)):c(this),u(arguments))}),a((function(){return[1,2].toLocaleString()!==new s([1,2]).toLocaleString()}))||!a((function(){s.prototype.toLocaleString.call([1,2])})))},7467:function(t,e,r){var n=r(7628),o=r(4644),i=o.aTypedArray,a=o.exportTypedArrayMethod,u=o.getTypedArrayConstructor;a("toReversed",(function(){return n(i(this),u(this))}))},4732:function(t,e,r){var n=r(4644),o=r(9504),i=r(9306),a=r(5370),u=n.aTypedArray,s=n.getTypedArrayConstructor,c=n.exportTypedArrayMethod,f=o(n.TypedArrayPrototype.sort);c("toSorted",(function(t){void 0!==t&&i(t);var e=u(this),r=a(s(e),e);return f(r,t)}))},3684:function(t,e,r){var n=r(4644).exportTypedArrayMethod,o=r(9039),i=r(4576),a=r(9504),u=i.Uint8Array,s=u&&u.prototype||{},c=[].toString,f=a([].join);o((function(){c.call({})}))&&(c=function(){return f(this)});var p=s.toString!==c;n("toString",c,p)},3690:function(t,e,r){r(5823)("Uint16",(function(t){return function(e,r,n){return t(this,e,r,n)}}))},1489:function(t,e,r){r(5823)("Uint8",(function(t){return function(e,r,n){return t(this,e,r,n)}}))},9577:function(t,e,r){var n=r(9928),o=r(4644),i=r(1108),a=r(1291),u=r(5854),s=o.aTypedArray,c=o.getTypedArrayConstructor,f=o.exportTypedArrayMethod,p=!!function(){try{new Int8Array(1).with(2,{valueOf:function(){throw 8}})}catch(t){return 8===t}}();f("with",{with:function(t,e){var r=s(this),o=a(t),f=i(r)?u(e):+e;return n(r,c(r),o,f)}}.with,!p)},3500:function(t,e,r){var n=r(4576),o=r(7400),i=r(9296),a=r(235),u=r(6699),s=function(t){if(t&&t.forEach!==a)try{u(t,"forEach",a)}catch(e){t.forEach=a}};for(var c in o)o[c]&&s(n[c]&&n[c].prototype);s(i)},2953:function(t,e,r){var n=r(4576),o=r(7400),i=r(9296),a=r(3792),u=r(6699),s=r(687),c=r(8227)("iterator"),f=a.values,p=function(t,e){if(t){if(t[c]!==f)try{u(t,c,f)}catch(e){t[c]=f}if(s(t,e,!0),o[e])for(var r in a)if(t[r]!==a[r])try{u(t,r,a[r])}catch(e){t[r]=a[r]}}};for(var l in o)p(n[l]&&n[l].prototype,l);p(i,"DOMTokenList")}},e={};function r(n){var o=e[n];if(void 0!==o)return o.exports;var i=e[n]={exports:{}};return t[n].call(i.exports,i,i.exports,r),i.exports}r.d=function(t,e){for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var n={};function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function i(t){var e=function(t){if("object"!=o(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==o(e)?e:e+""}function a(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,i(n.key),n)}}function u(t,e,r){return e&&a(t.prototype,e),r&&a(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}r.d(n,{default:function(){return z}}),r(2008),r(4114),r(6099),r(7495),r(5440),r(3500),r(2675),r(9463),r(2259),r(3792),r(7764),r(2953),r(5700),r(6280),r(9572),r(2892);var c=u((function t(){s(this,t),this.streamId=null,this.segmentType=null,this.index=NaN,this.bytes=null,this.start=NaN,this.end=NaN,this.duration=NaN,this.representation=null,this.endFragment=null})),f=u((function t(){s(this,t),this.tcpid=null,this.type=null,this.url=null,this.actualurl=null,this.range=null,this.trequest=null,this.tresponse=null,this.responsecode=null,this.interval=null,this.trace=[],this.cmsd=null,this._stream=null,this._tfinish=null,this._mediaduration=null,this._responseHeaders=null,this._serviceLocation=null,this._fileLoaderType=null,this._resourceTimingValues=null}));f.GET="GET",f.HEAD="HEAD",f.MPD_TYPE="MPD",f.XLINK_EXPANSION_TYPE="XLinkExpansion",f.INIT_SEGMENT_TYPE="InitializationSegment",f.INDEX_SEGMENT_TYPE="IndexSegment",f.MEDIA_SEGMENT_TYPE="MediaSegment",f.BITSTREAM_SWITCHING_SEGMENT_TYPE="BitstreamSwitchingSegment",f.MSS_FRAGMENT_INFO_SEGMENT_TYPE="FragmentInfoSegment",f.DVB_REPORTING_TYPE="DVBReporting",f.LICENSE="license",f.CONTENT_STEERING_TYPE="ContentSteering",f.OTHER_TYPE="other";var p=function(){return u((function t(e){s(this,t),this.action=t.ACTION_DOWNLOAD,this.availabilityEndTime=null,this.availabilityStartTime=null,this.bandwidth=NaN,this.bytesLoaded=NaN,this.bytesTotal=NaN,this.delayLoadingTime=NaN,this.duration=NaN,this.endDate=null,this.firstByteDate=null,this.index=NaN,this.mediaStartTime=NaN,this.mediaType=null,this.range=null,this.representation=null,this.responseType="arraybuffer",this.retryAttempts=0,this.serviceLocation=null,this.startDate=null,this.startTime=NaN,this.timescale=NaN,this.type=null,this.url=e||null,this.wallStartTime=null}),[{key:"isInitializationRequest",value:function(){return this.type&&this.type===f.INIT_SEGMENT_TYPE}},{key:"setInfo",value:function(t){this.type=t&&t.init?f.INIT_SEGMENT_TYPE:f.MEDIA_SEGMENT_TYPE,this.url=t&&t.url?t.url:null,this.range=t&&t.range?t.range.start+"-"+t.range.end:null,this.mediaType=t&&t.mediaType?t.mediaType:null,this.representation=t&&t.representation?t.representation:null}}])}();p.ACTION_DOWNLOAD="download",p.ACTION_COMPLETE="complete";var l=p,d=(r(2010),function(){var t,e=[],r={},n={};function o(t,r){for(var n in e){var o=e[n];if(o.context===t&&o.name===r)return o.instance}return null}function i(t,e){return e[t]}function a(t,e,r){t in r&&(r[t]=e)}function u(e,r,n){var o,i=e.__dashjs_factory_name,a=r[i];if(a){var u=a.instance;if(!a.override)return u.apply({context:r,factory:t},n);for(var s in o=e.apply({context:r},n),u=u.apply({context:r,factory:t,parent:o},n))o.hasOwnProperty(s)&&(o[s]=u[s])}else o=e.apply({context:r},n);return o.getClassName=function(){return i},o}return t={extend:function(t,e,r,n){!n[t]&&e&&(n[t]={instance:e,override:r})},getSingletonInstance:o,setSingletonInstance:function(t,r,n){for(var o in e){var i=e[o];if(i.context===t&&i.name===r)return void(e[o].instance=n)}e.push({name:r,context:t,instance:n})},deleteSingletonInstances:function(t){e=e.filter((function(e){return e.context!==t}))},getSingletonFactory:function(t){var n=i(t.__dashjs_factory_name,r);return n||(n=function(r){var n;return void 0===r&&(r={}),{getInstance:function(){return n||(n=o(r,t.__dashjs_factory_name)),n||(n=u(t,r,arguments),e.push({name:t.__dashjs_factory_name,context:r,instance:n})),n}}},r[t.__dashjs_factory_name]=n),n},getSingletonFactoryByName:function(t){return i(t,r)},updateSingletonFactory:function(t,e){a(t,e,r)},getClassFactory:function(t){var e=i(t.__dashjs_factory_name,n);return e||(e=function(e){return void 0===e&&(e={}),{create:function(){return u(t,e,arguments)}}},n[t.__dashjs_factory_name]=e),e},getClassFactoryByName:function(t){return i(t,n)},updateClassFactory:function(t,e){a(t,e,n)}},t}()),h=d;function v(t){var e,r,n,o,i,a,u,s,c,p=(t=t||{}).streamProcessor,d=t.baseURLController,h=t.debug;function v(){o&&(r.debug("Stop"),clearTimeout(a),o=!1,u=null,s=null)}function y(){if(o){var t=p.getRepresentationController().getCurrentRepresentation(),e=t.adaptation.period.mpd.manifest.Period[t.adaptation.period.index].AdaptationSet[t.adaptation.index],r=e.SegmentTemplate.SegmentTimeline.S,n=function(t,e,r){var n=t.SegmentTemplate.timescale,o=new l;return o.mediaType=i,o.type=f.MSS_FRAGMENT_INFO_SEGMENT_TYPE,o.startTime=r.t/n,o.duration=r.d/n,o.timescale=n,o.bandwidth=e.bandwidth,o.index=c++,o.adaptationIndex=e.adaptation.index,o.representation=e,o.url=d.resolve(e.path).url+t.SegmentTemplate.media,o.url=o.url.replace("$Bandwidth$",e.bandwidth),o.url=o.url.replace("$Time$",r.tManifest?r.tManifest:r.t),o.url=o.url.replace("/Fragments(","/FragmentInfo("),o}(e,t,r[r.length-1]);g.call(this,n)}}function g(t){if(p.getFragmentModel().isFragmentLoadedOrPending(t))return r.debug("End of timeline"),void v();n.executeRequest(t)}return e={initialize:function(){i=p.getType(),n=p.getFragmentModel(),o=!1,u=null,s=null},controllerType:"MssFragmentInfoController",start:function(){o||(r.debug("Start"),o=!0,c=0,y())},fragmentInfoLoaded:function(t){if(o){var e,n,i,c=t.request;t.response?(null===u&&(u=(new Date).getTime()),s||(s=c.startTime),n=((new Date).getTime()-u)/1e3,e=c.startTime+c.duration-s,i=Math.max(0,e-n),clearTimeout(a),a=setTimeout((function(){a=null,y()}),1e3*i)):r.error("Load error",c.url)}},getType:function(){return i},reset:function(){v()}},r=h.getLogger(e),e}v.__dashjs_factory_name="MssFragmentInfoController";var y=h.getClassFactory(v),g=(r(9479),r(4554),u((function t(e,r,n){s(this,t),this.code=e||null,this.message=r||null,this.data=n||null})));function m(t,e){if(e&&("object"==o(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function b(t){return b=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},b(t)}function T(t,e){return T=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},T(t,e)}function E(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&T(t,e)}function A(t,e,r){return e=b(e),m(t,_()?Reflect.construct(e,r||[],b(t).constructor):e.apply(t,r))}function _(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(_=function(){return!!t})()}r(825),r(875);var w=new(function(t){function e(){var t;return s(this,e),(t=A(this,e)).MSS_NO_TFRF_CODE=200,t.MSS_UNSUPPORTED_CODEC_CODE=201,t.MSS_NO_TFRF_MESSAGE="Missing tfrf in live media segment",t.MSS_UNSUPPORTED_CODEC_MESSAGE="Unsupported codec",t}return E(e,t),u(e)}(function(){return u((function t(){s(this,t)}),[{key:"extend",value:function(t,e){if(t){var r=!!e&&e.override,n=!!e&&e.publicOnly;for(var o in t)!t.hasOwnProperty(o)||this[o]&&!r||n&&-1===t[o].indexOf("public_")||(this[o]=t[o])}}}])}()));function x(t,e,r){return e=b(e),m(t,S()?Reflect.construct(e,r||[],b(t).constructor):e.apply(t,r))}function S(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(S=function(){return!!t})()}var I=new(function(t){function e(){var t;return s(this,e),(t=x(this,e)).AST_IN_FUTURE="astInFuture",t.BASE_URLS_UPDATED="baseUrlsUpdated",t.BUFFER_EMPTY="bufferStalled",t.BUFFER_LOADED="bufferLoaded",t.BUFFER_LEVEL_STATE_CHANGED="bufferStateChanged",t.BUFFER_LEVEL_UPDATED="bufferLevelUpdated",t.DVB_FONT_DOWNLOAD_ADDED="dvbFontDownloadAdded",t.DVB_FONT_DOWNLOAD_COMPLETE="dvbFontDownloadComplete",t.DVB_FONT_DOWNLOAD_FAILED="dvbFontDownloadFailed",t.DYNAMIC_TO_STATIC="dynamicToStatic",t.ERROR="error",t.FRAGMENT_LOADING_COMPLETED="fragmentLoadingCompleted",t.FRAGMENT_LOADING_PROGRESS="fragmentLoadingProgress",t.FRAGMENT_LOADING_STARTED="fragmentLoadingStarted",t.FRAGMENT_LOADING_ABANDONED="fragmentLoadingAbandoned",t.LOG="log",t.MANIFEST_LOADING_STARTED="manifestLoadingStarted",t.MANIFEST_LOADING_FINISHED="manifestLoadingFinished",t.MANIFEST_LOADED="manifestLoaded",t.METRICS_CHANGED="metricsChanged",t.METRIC_CHANGED="metricChanged",t.METRIC_ADDED="metricAdded",t.METRIC_UPDATED="metricUpdated",t.PERIOD_SWITCH_STARTED="periodSwitchStarted",t.PERIOD_SWITCH_COMPLETED="periodSwitchCompleted",t.QUALITY_CHANGE_REQUESTED="qualityChangeRequested",t.QUALITY_CHANGE_RENDERED="qualityChangeRendered",t.NEW_TRACK_SELECTED="newTrackSelected",t.TRACK_CHANGE_RENDERED="trackChangeRendered",t.STREAM_INITIALIZING="streamInitializing",t.STREAM_UPDATED="streamUpdated",t.STREAM_ACTIVATED="streamActivated",t.STREAM_DEACTIVATED="streamDeactivated",t.STREAM_INITIALIZED="streamInitialized",t.STREAM_TEARDOWN_COMPLETE="streamTeardownComplete",t.TEXT_TRACKS_ADDED="allTextTracksAdded",t.TEXT_TRACK_ADDED="textTrackAdded",t.CUE_ENTER="cueEnter",t.CUE_EXIT="cueExit",t.THROUGHPUT_MEASUREMENT_STORED="throughputMeasurementStored",t.TTML_PARSED="ttmlParsed",t.TTML_TO_PARSE="ttmlToParse",t.CAPTION_RENDERED="captionRendered",t.CAPTION_CONTAINER_RESIZE="captionContainerResize",t.CAN_PLAY="canPlay",t.CAN_PLAY_THROUGH="canPlayThrough",t.PLAYBACK_ENDED="playbackEnded",t.PLAYBACK_ERROR="playbackError",t.PLAYBACK_INITIALIZED="playbackInitialized",t.PLAYBACK_NOT_ALLOWED="playbackNotAllowed",t.PLAYBACK_METADATA_LOADED="playbackMetaDataLoaded",t.PLAYBACK_LOADED_DATA="playbackLoadedData",t.PLAYBACK_PAUSED="playbackPaused",t.PLAYBACK_PLAYING="playbackPlaying",t.PLAYBACK_PROGRESS="playbackProgress",t.PLAYBACK_RATE_CHANGED="playbackRateChanged",t.PLAYBACK_SEEKED="playbackSeeked",t.PLAYBACK_SEEKING="playbackSeeking",t.PLAYBACK_STALLED="playbackStalled",t.PLAYBACK_STARTED="playbackStarted",t.PLAYBACK_TIME_UPDATED="playbackTimeUpdated",t.PLAYBACK_VOLUME_CHANGED="playbackVolumeChanged",t.PLAYBACK_WAITING="playbackWaiting",t.MANIFEST_VALIDITY_CHANGED="manifestValidityChanged",t.EVENT_MODE_ON_START="eventModeOnStart",t.EVENT_MODE_ON_RECEIVE="eventModeOnReceive",t.CONFORMANCE_VIOLATION="conformanceViolation",t.REPRESENTATION_SWITCH="representationSwitch",t.ADAPTATION_SET_REMOVED_NO_CAPABILITIES="adaptationSetRemovedNoCapabilities",t.CONTENT_STEERING_REQUEST_COMPLETED="contentSteeringRequestCompleted",t.INBAND_PRFT="inbandPrft",t.MANAGED_MEDIA_SOURCE_START_STREAMING="managedMediaSourceStartStreaming",t.MANAGED_MEDIA_SOURCE_END_STREAMING="managedMediaSourceEndStreaming",t}return E(e,t),u(e)}(function(){return u((function t(){s(this,t)}),[{key:"extend",value:function(t,e){if(t){var r=!!e&&e.override,n=!!e&&e.publicOnly;for(var o in t)!t.hasOwnProperty(o)||this[o]&&!r||n&&-1===t[o].indexOf("public_")||(this[o]=t[o])}}}])}()));function O(t){var e,r,n,o=(t=t||{}).dashMetrics,i=t.playbackController,a=t.errHandler,u=t.eventBus,s=t.ISOBoxer,c=t.debug;function f(t,e,o,s){var c=s.getRepresentationController().getCurrentRepresentation(),f=c.adaptation.period.mpd.manifest,l=f.Period[c.adaptation.period.index].AdaptationSet[c.adaptation.index],d=l.SegmentTemplate.timescale;if(r=s.getType(),"dynamic"===f.type||f.timeShiftBufferDepth)if(e){var h,v,y,m,b=l.SegmentTemplate.SegmentTimeline.S,T=e.entry,E=null,A=null;if(0!==T.length&&(h=T[0],!("static"===f.type&&(v=b[0].tManifest?parseFloat(b[0].tManifest):b[0].t,h.fragment_absolute_time>v+f.timeShiftBufferDepth*d)))){if(v=b[b.length-1].tManifest?parseFloat(b[b.length-1].tManifest):b[b.length-1].t,h.fragment_absolute_time<=v)return y={start:b[0].t/d,end:o.baseMediaDecodeTime/d+t.duration},void p(t.mediaType,y,s.getStreamInfo().manifestInfo);(E={}).t=h.fragment_absolute_time,E.d=h.fragment_duration,b[0].tManifest&&(E.t-=parseFloat(b[0].tManifest)-b[0].t,E.tManifest=h.fragment_absolute_time);var _=b[b.length-1];if(_.t+_.d!==E.t&&(n.debug("Patch segment duration - t = ",_.t+", d = "+_.d+" => "+(E.t-_.t)),_.d=E.t-_.t),b.push(E),"static"!==f.type){if(f.timeShiftBufferDepth&&f.timeShiftBufferDepth>0)for(A=((E=b[b.length-1]).t-f.timeShiftBufferDepth*d)/d,m=((E=b[0]).t+E.d)/d;m<A&&(i.isPaused()||!(i.getTime()<m));)b.splice(0,1),m=((E=b[0]).t+E.d)/d;y={start:b[0].t/d,end:o.baseMediaDecodeTime/d+t.duration},p(r,y,s.getStreamInfo().manifestInfo)}else"video"===r&&(m=((E=b[b.length-1]).t+E.d)/d)>c.adaptation.period.duration&&u.trigger(I.MANIFEST_VALIDITY_CHANGED,{sender:this,newDuration:m})}}else a.error(new g(w.MSS_NO_TFRF_CODE,w.MSS_NO_TFRF_MESSAGE))}function p(t,e,r){if("video"===t||"audio"===t){var a=o.getCurrentDVRInfo(t);(!a||e.end>a.range.end)&&(n.debug("Update DVR range: ["+e.start+" - "+e.end+"]"),o.addDVRInfo(t,i.getTime(),r,e),i.updateCurrentTime(t))}}function l(t,e){var r=8,n=0;for(n=0;n<t.boxes.length;n++){if(t.boxes[n].type===e)return r;r+=t.boxes[n].size}return r}return e={convertFragment:function(t,e){var r,n=s.parseBuffer(t.response),o=n.fetch("tfhd");o.track_ID=t.request.representation.mediaInfo.index+1;var i=n.fetch("tfdt"),a=n.fetch("traf");null===i&&((i=s.createFullBox("tfdt",a,o)).version=1,i.flags=0,i.baseMediaDecodeTime=Math.floor(t.request.startTime*t.request.timescale));var u=n.fetch("trun"),c=n.fetch("tfxd");c&&(c._parent.boxes.splice(c._parent.boxes.indexOf(c),1),c=null);var p=n.fetch("tfrf");f(t.request,p,i,e),p&&(p._parent.boxes.splice(p._parent.boxes.indexOf(p),1),p=null);var d=n.fetch("sepiff");if(null!==d){d.type="senc",d.usertype=void 0;var h=n.fetch("saio");if(null===h){(h=s.createFullBox("saio",a)).version=0,h.flags=0,h.entry_count=1,h.offset=[0];var v=s.createFullBox("saiz",a);if(v.version=0,v.flags=0,v.sample_count=d.sample_count,v.default_sample_info_size=0,v.sample_info_size=[],2&d.flags)for(r=0;r<d.sample_count;r+=1)v.sample_info_size[r]=10+6*d.entry[r].NumberOfEntries;else v.default_sample_info_size=8}}o.flags&=16777214,o.flags|=131072,u.flags|=1;var y=n.fetch("moof"),g=y.getLength();u.data_offset=g+8;var m=n.fetch("saio");if(null!==m){var b=l(y,"traf"),T=l(a,"senc");m.offset[0]=b+T+16}t.response=n.write()},updateSegmentList:function(t,e){if(!t.response)throw new Error("e.response parameter is missing");var r=s.parseBuffer(t.response),n=r.fetch("tfhd");n.track_ID=t.request.representation.mediaInfo.index+1;var o=r.fetch("tfdt"),i=r.fetch("traf");null===o&&((o=s.createFullBox("tfdt",i,n)).version=1,o.flags=0,o.baseMediaDecodeTime=Math.floor(t.request.startTime*t.request.timescale));var a=r.fetch("tfrf");f(t.request,a,o,e),a&&(a._parent.boxes.splice(a._parent.boxes.indexOf(a),1),a=null)},getType:function(){return r}},n=c.getLogger(e),r="",e}O.__dashjs_factory_name="MssFragmentMoofProcessor";var N=h.getClassFactory(O);function M(t){var e,r,n,o,i,a,u=(t=t||{}).constants,s=t.ISOBoxer,c=t.protectionController;function f(t,e){s.createBox("frma",t).data_format=function(t){var e,r=0;for(e=0;e<t.length;e+=1)r|=t.charCodeAt(e)<<8*(t.length-e-1);return r}(e)}function p(t){var e=s.createFullBox("schm",t);e.flags=0,e.version=0,e.scheme_type=1667591779,e.scheme_version=65536}function l(t){var e,r;e=s.createBox("schi",t),(r=s.createFullBox("tenc",e)).flags=0,r.version=0,r.default_IsEncrypted=1,r.default_IV_size=8,r.default_KID=o&&o.length>0&&o[0]["cenc:default_KID"]?o[0]["cenc:default_KID"]:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}function d(t){var e,r=new Uint8Array(t.length/2);for(e=0;e<t.length/2;e+=1)r[e]=parseInt(""+t[2*e]+t[2*e+1],16);return r}return{generateMoov:function(t){var h;if(t&&t.adaptation)return r=(n=t).adaptation,e=r.period,a=r.index+1,o=e.mpd.manifest.Period[e.index].AdaptationSet[r.index].ContentProtection,i=e.mpd.manifest.Period[e.index].AdaptationSet[r.index].SegmentTemplate.timescale,function(t){var e=s.createBox("ftyp",t);e.major_brand="iso6",e.minor_version=1,e.compatible_brands=[],e.compatible_brands[0]="isom",e.compatible_brands[1]="iso6",e.compatible_brands[2]="msdh"}(h=s.createFile()),function(t){var h=s.createBox("moov",t);!function(t){var r=s.createFullBox("mvhd",t);r.version=1,r.creation_time=0,r.modification_time=0,r.timescale=i,r.duration=e.duration===1/0?0x10000000000000000:Math.round(e.duration*i),r.rate=1,r.volume=1,r.reserved1=0,r.reserved2=[0,0],r.matrix=[1,0,0,0,1,0,0,0,16384],r.pre_defined=[0,0,0,0,0,0],r.next_track_ID=a+1}(h);var v=s.createBox("trak",h);!function(t){var r=s.createFullBox("tkhd",t);r.version=1,r.flags=7,r.creation_time=0,r.modification_time=0,r.track_ID=a,r.reserved1=0,r.duration=e.duration===1/0?0x10000000000000000:Math.round(e.duration*i),r.reserved2=[0,0],r.layer=0,r.alternate_group=0,r.volume=1,r.reserved3=0,r.matrix=[1,0,0,0,1,0,0,0,16384],r.width=n.width,r.height=n.height}(v);var y=s.createBox("mdia",v);!function(t){var n=s.createFullBox("mdhd",t);n.version=1,n.creation_time=0,n.modification_time=0,n.timescale=i,n.duration=e.duration===1/0?0x10000000000000000:Math.round(e.duration*i),n.language=r.lang||"und",n.pre_defined=0}(y),function(t){var e=s.createFullBox("hdlr",t);switch(e.pre_defined=0,r.type){case u.VIDEO:e.handler_type="vide";break;case u.AUDIO:e.handler_type="soun";break;default:e.handler_type="meta"}e.name=n.id,e.reserved=[0,0,0]}(y);var g=s.createBox("minf",y);switch(r.type){case u.VIDEO:!function(t){var e=s.createFullBox("vmhd",t);e.flags=1,e.graphicsmode=0,e.opcolor=[0,0,0]}(g);break;case u.AUDIO:!function(t){var e=s.createFullBox("smhd",t);e.flags=1,e.balance=0,e.reserved=0}(g)}!function(t){var e=s.createFullBox("dref",t);e.entry_count=1,e.entries=[];var r=s.createFullBox("url ",e,!1);r.location="",r.flags=1,e.entries.push(r)}(s.createBox("dinf",g));var m=s.createBox("stbl",g);s.createFullBox("stts",m)._data=[0,0,0,0,0,0,0,0],s.createFullBox("stsc",m)._data=[0,0,0,0,0,0,0,0],s.createFullBox("stco",m)._data=[0,0,0,0,0,0,0,0],s.createFullBox("stsz",m)._data=[0,0,0,0,0,0,0,0,0,0,0,0],function(t){var e=s.createFullBox("stsd",t);switch(e.entries=[],r.type){case u.VIDEO:case u.AUDIO:e.entries.push(function(t){var e=n.codecs.substring(0,n.codecs.indexOf("."));switch(e){case"avc1":return function(t,e){var r;if((r=o?s.createBox("encv",t,!1):s.createBox("avc1",t,!1)).reserved1=[0,0,0,0,0,0],r.data_reference_index=1,r.pre_defined1=0,r.reserved2=0,r.pre_defined2=[0,0,0],r.height=n.height,r.width=n.width,r.horizresolution=72,r.vertresolution=72,r.reserved3=0,r.frame_count=1,r.compressorname=[10,65,86,67,32,67,111,100,105,110,103,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],r.depth=24,r.pre_defined3=65535,r.config=function(){for(var t,e=null,r=15,o=[],i=[],a=0,u=0,s=0,c=n.codecPrivateData.split("00000001").slice(1),f=0;f<c.length;f++)switch(31&(t=d(c[f]))[0]){case 7:o.push(t),r+=t.length+2;break;case 8:i.push(t),r+=t.length+2}o.length>0&&(a=o[0][1],s=o[0][2],u=o[0][3]);var p=0;(e=new Uint8Array(r))[p++]=(4278190080&r)>>24,e[p++]=(16711680&r)>>16,e[p++]=(65280&r)>>8,e[p++]=255&r,e.set([97,118,99,67],p),p+=4,e[p++]=1,e[p++]=a,e[p++]=s,e[p++]=u,e[p++]=255,e[p++]=224|o.length;for(var l=0;l<o.length;l++)e[p++]=(65280&o[l].length)>>8,e[p++]=255&o[l].length,e.set(o[l],p),p+=o[l].length;e[p++]=i.length;for(var h=0;h<i.length;h++)e[p++]=(65280&i[h].length)>>8,e[p++]=255&i[h].length,e.set(i[h],p),p+=i[h].length;return e}(),o){var i=s.createBox("sinf",r);f(i,e),p(i),l(i)}return r}(t,e);case"mp4a":return function(t,e){var r,i,u,c,h;if((r=o?s.createBox("enca",t,!1):s.createBox("mp4a",t,!1)).reserved1=[0,0,0,0,0,0],r.data_reference_index=1,r.reserved2=[0,0],r.channelcount=n.audioChannels,r.samplesize=16,r.pre_defined=0,r.reserved_3=0,r.samplerate=n.audioSamplingRate<<16,r.esds=(u=34+(i=d(n.codecPrivateData)).length,h=0,(c=new Uint8Array(u))[h++]=(4278190080&u)>>24,c[h++]=(16711680&u)>>16,c[h++]=(65280&u)>>8,c[h++]=255&u,c.set([101,115,100,115],h),h+=4,c.set([0,0,0,0],h),h+=4,c[h++]=3,c[h++]=20+i.length,c[h++]=(65280&a)>>8,c[h++]=255&a,c[h++]=0,c[h++]=4,c[h++]=15+i.length,c[h++]=64,c[h]=20,c[h]|=0,c[h++]|=1,c[h++]=255,c[h++]=255,c[h++]=255,c[h++]=(4278190080&n.bandwidth)>>24,c[h++]=(16711680&n.bandwidth)>>16,c[h++]=(65280&n.bandwidth)>>8,c[h++]=255&n.bandwidth,c[h++]=(4278190080&n.bandwidth)>>24,c[h++]=(16711680&n.bandwidth)>>16,c[h++]=(65280&n.bandwidth)>>8,c[h++]=255&n.bandwidth,c[h++]=5,c[h++]=i.length,c.set(i,h),c),o){var v=s.createBox("sinf",r);f(v,e),p(v),l(v)}return r}(t,e);default:throw{code:w.MSS_UNSUPPORTED_CODEC_CODE,message:w.MSS_UNSUPPORTED_CODEC_MESSAGE,data:{codec:e}}}}(e))}e.entry_count=e.entries.length}(m),function(t){var e=s.createFullBox("trex",t);e.track_ID=a,e.default_sample_description_index=1,e.default_sample_duration=0,e.default_sample_size=0,e.default_sample_flags=0}(s.createBox("mvex",h)),o&&c&&function(t,e){var r,n,o;for(o=0;o<e.length;o+=1)(r=e[o].initData)&&(n=s.parseBuffer(r).fetch("pssh"))&&s.Utils.appendBox(t,n)}(h,c.getSupportedKeySystemMetadataFromContentProtection(o))}(h),h.write()}}}r(4782),r(4743),r(6573),r(8100),r(7936),r(1489),r(8140),r(1630),r(2170),r(5044),r(1920),r(1694),r(9955),r(1903),r(1134),r(3206),r(4496),r(6651),r(2887),r(9369),r(6812),r(8995),r(1575),r(6072),r(8747),r(8845),r(9423),r(7301),r(373),r(6614),r(1405),r(7467),r(4732),r(3684),r(9577),M.__dashjs_factory_name="MssFragmentMoovProcessor";var D=h.getClassFactory(M);function P(t,e){return t.length===e.length&&t.every((function(t,r){return t===e[r]}))}function C(){this._procFullBox(),1&this.flags&&(this._procField("aux_info_type","uint",32),this._procField("aux_info_type_parameter","uint",32)),this._procField("entry_count","uint",32),this._procFieldArray("offset",this.entry_count,"uint",1===this.version?64:32)}function R(){this._procFullBox(),1&this.flags&&(this._procField("aux_info_type","uint",32),this._procField("aux_info_type_parameter","uint",32)),this._procField("default_sample_info_size","uint",8),this._procField("sample_count","uint",32),0===this.default_sample_info_size&&this._procFieldArray("sample_info_size",this.sample_count,"uint",8)}function F(){this._procFullBox(),this._procField("sample_count","uint",32),1&this.flags&&this._procField("IV_size","uint",8),this._procEntries("entry",this.sample_count,(function(t){this._procEntryField(t,"InitializationVector","data",8),2&this.flags&&(this._procEntryField(t,"NumberOfEntries","uint",16),this._procSubEntries(t,"clearAndCryptedData",t.NumberOfEntries,(function(t){this._procEntryField(t,"BytesOfClearData","uint",16),this._procEntryField(t,"BytesOfEncryptedData","uint",32)})))}))}function B(){P(this.usertype,[109,29,155,5,66,213,68,230,128,226,20,29,175,247,87,178])&&(this._procFullBox(),this._parsing&&(this.type="tfxd"),this._procField("fragment_absolute_time","uint",1===this.version?64:32),this._procField("fragment_duration","uint",1===this.version?64:32)),P(this.usertype,[212,128,126,242,202,57,70,149,142,84,38,203,158,70,167,159])&&(this._procFullBox(),this._parsing&&(this.type="tfrf"),this._procField("fragment_count","uint",8),this._procEntries("entry",this.fragment_count,(function(t){this._procEntryField(t,"fragment_absolute_time","uint",1===this.version?64:32),this._procEntryField(t,"fragment_duration","uint",1===this.version?64:32)}))),P(this.usertype,[162,57,79,82,90,155,79,20,162,68,108,66,124,100,141,244])&&(this._parsing&&(this.type="sepiff"),F.call(this))}function L(t){t=t||{};var e,r,n,o=this.context,i=t.dashMetrics,a=t.playbackController,u=t.eventBus,s=t.protectionController,c=t.ISOBoxer,p=t.debug;return n={generateMoov:function(t){return e.generateMoov(t)},processFragment:function(t,e){if(!t||!t.request||!t.response)throw new Error("e parameter is missing or malformed");"MediaSegment"===t.request.type?r.convertFragment(t,e):t.request.type===f.MSS_FRAGMENT_INFO_SEGMENT_TYPE&&(r.updateSegmentList(t,e),t.sender=null)}},c.addBoxProcessor("uuid",B),c.addBoxProcessor("saio",C),c.addBoxProcessor("saiz",R),c.addBoxProcessor("senc",F),e=D(o).create({protectionController:s,constants:t.constants,ISOBoxer:c}),r=N(o).create({dashMetrics:i,playbackController:a,ISOBoxer:c,eventBus:u,debug:p,errHandler:t.errHandler}),n}L.__dashjs_factory_name="MssFragmentProcessor";var j=h.getClassFactory(L),U=(r(6982),r(8781),r(3690),r(8706),r(8598),r(2062),r(3362),r(906),r(744),function(t){var e=1e7,r=7,n=9007199254740992,o=l(n),i="0123456789abcdefghijklmnopqrstuvwxyz",a="function"==typeof BigInt;function u(t,e,r,n){return void 0===t?u[0]:void 0===e||10==+e&&!r?J(t):W(t,e,r,n)}function s(t,e){this.value=t,this.sign=e,this.isSmall=!1}function c(t){this.value=t,this.sign=t<0,this.isSmall=!0}function f(t){this.value=t}function p(t){return-n<t&&t<n}function l(t){return t<1e7?[t]:t<1e14?[t%1e7,Math.floor(t/1e7)]:[t%1e7,Math.floor(t/1e7)%1e7,Math.floor(t/1e14)]}function d(t){h(t);var r=t.length;if(r<4&&M(t,o)<0)switch(r){case 0:return 0;case 1:return t[0];case 2:return t[0]+t[1]*e;default:return t[0]+(t[1]+t[2]*e)*e}return t}function h(t){for(var e=t.length;0===t[--e];);t.length=e+1}function v(t){for(var e=new Array(t),r=-1;++r<t;)e[r]=0;return e}function y(t){return t>0?Math.floor(t):Math.ceil(t)}function g(t,r){var n,o,i=t.length,a=r.length,u=new Array(i),s=0,c=e;for(o=0;o<a;o++)s=(n=t[o]+r[o]+s)>=c?1:0,u[o]=n-s*c;for(;o<i;)s=(n=t[o]+s)===c?1:0,u[o++]=n-s*c;return s>0&&u.push(s),u}function m(t,e){return t.length>=e.length?g(t,e):g(e,t)}function b(t,r){var n,o,i=t.length,a=new Array(i),u=e;for(o=0;o<i;o++)n=t[o]-u+r,r=Math.floor(n/u),a[o]=n-r*u,r+=1;for(;r>0;)a[o++]=r%u,r=Math.floor(r/u);return a}function T(t,r){var n,o,i=t.length,a=r.length,u=new Array(i),s=0,c=e;for(n=0;n<a;n++)(o=t[n]-s-r[n])<0?(o+=c,s=1):s=0,u[n]=o;for(n=a;n<i;n++){if(!((o=t[n]-s)<0)){u[n++]=o;break}o+=c,u[n]=o}for(;n<i;n++)u[n]=t[n];return h(u),u}function E(t,r,n){var o,i,a=t.length,u=new Array(a),f=-r,p=e;for(o=0;o<a;o++)i=t[o]+f,f=Math.floor(i/p),i%=p,u[o]=i<0?i+p:i;return"number"==typeof(u=d(u))?(n&&(u=-u),new c(u)):new s(u,n)}function A(t,r){var n,o,i,a,u=t.length,s=r.length,c=v(u+s),f=e;for(i=0;i<u;++i){a=t[i];for(var p=0;p<s;++p)n=a*r[p]+c[i+p],o=Math.floor(n/f),c[i+p]=n-o*f,c[i+p+1]+=o}return h(c),c}function _(t,r){var n,o,i=t.length,a=new Array(i),u=e,s=0;for(o=0;o<i;o++)n=t[o]*r+s,s=Math.floor(n/u),a[o]=n-s*u;for(;s>0;)a[o++]=s%u,s=Math.floor(s/u);return a}function w(t,e){for(var r=[];e-- >0;)r.push(0);return r.concat(t)}function x(t,e){var r=Math.max(t.length,e.length);if(r<=30)return A(t,e);r=Math.ceil(r/2);var n=t.slice(r),o=t.slice(0,r),i=e.slice(r),a=e.slice(0,r),u=x(o,a),s=x(n,i),c=x(m(o,n),m(a,i)),f=m(m(u,w(T(T(c,u),s),r)),w(s,2*r));return h(f),f}function S(t,r,n){return new s(t<e?_(r,t):A(r,l(t)),n)}function I(t){var r,n,o,i,a=t.length,u=v(a+a),s=e;for(o=0;o<a;o++){n=0-(i=t[o])*i;for(var c=o;c<a;c++)r=i*t[c]*2+u[o+c]+n,n=Math.floor(r/s),u[o+c]=r-n*s;u[o+a]=n}return h(u),u}function O(t,r){var n,o,i,a,u=t.length,s=v(u),c=e;for(i=0,n=u-1;n>=0;--n)i=(a=i*c+t[n])-(o=y(a/r))*r,s[n]=0|o;return[s,0|i]}function N(t,r){var n,o=J(r);if(a)return[new f(t.value/o.value),new f(t.value%o.value)];var i,p=t.value,g=o.value;if(0===g)throw new Error("Cannot divide by zero");if(t.isSmall)return o.isSmall?[new c(y(p/g)),new c(p%g)]:[u[0],t];if(o.isSmall){if(1===g)return[t,u[0]];if(-1==g)return[t.negate(),u[0]];var m=Math.abs(g);if(m<e){i=d((n=O(p,m))[0]);var b=n[1];return t.sign&&(b=-b),"number"==typeof i?(t.sign!==o.sign&&(i=-i),[new c(i),new c(b)]):[new s(i,t.sign!==o.sign),new c(b)]}g=l(m)}var E=M(p,g);if(-1===E)return[u[0],t];if(0===E)return[u[t.sign===o.sign?1:-1],u[0]];n=p.length+g.length<=200?function(t,r){var n,o,i,a,u,s,c,f=t.length,p=r.length,l=e,h=v(r.length),y=r[p-1],g=Math.ceil(l/(2*y)),m=_(t,g),b=_(r,g);for(m.length<=f&&m.push(0),b.push(0),y=b[p-1],o=f-p;o>=0;o--){for(n=l-1,m[o+p]!==y&&(n=Math.floor((m[o+p]*l+m[o+p-1])/y)),i=0,a=0,s=b.length,u=0;u<s;u++)i+=n*b[u],c=Math.floor(i/l),a+=m[o+u]-(i-c*l),i=c,a<0?(m[o+u]=a+l,a=-1):(m[o+u]=a,a=0);for(;0!==a;){for(n-=1,i=0,u=0;u<s;u++)(i+=m[o+u]-l+b[u])<0?(m[o+u]=i+l,i=0):(m[o+u]=i,i=1);a+=i}h[o]=n}return m=O(m,g)[0],[d(h),d(m)]}(p,g):function(t,r){for(var n,o,i,a,u,s=t.length,c=r.length,f=[],p=[],l=e;s;)if(p.unshift(t[--s]),h(p),M(p,r)<0)f.push(0);else{i=p[(o=p.length)-1]*l+p[o-2],a=r[c-1]*l+r[c-2],o>c&&(i=(i+1)*l),n=Math.ceil(i/a);do{if(M(u=_(r,n),p)<=0)break;n--}while(n);f.push(n),p=T(p,u)}return f.reverse(),[d(f),d(p)]}(p,g),i=n[0];var A=t.sign!==o.sign,w=n[1],x=t.sign;return"number"==typeof i?(A&&(i=-i),i=new c(i)):i=new s(i,A),"number"==typeof w?(x&&(w=-w),w=new c(w)):w=new s(w,x),[i,w]}function M(t,e){if(t.length!==e.length)return t.length>e.length?1:-1;for(var r=t.length-1;r>=0;r--)if(t[r]!==e[r])return t[r]>e[r]?1:-1;return 0}function D(t){var e=t.abs();return!e.isUnit()&&(!!(e.equals(2)||e.equals(3)||e.equals(5))||!(e.isEven()||e.isDivisibleBy(3)||e.isDivisibleBy(5))&&(!!e.lesser(49)||void 0))}function P(t,e){for(var r,n,o,i=t.prev(),a=i,u=0;a.isEven();)a=a.divide(2),u++;t:for(n=0;n<e.length;n++)if(!t.lesser(e[n])&&!(o=U(e[n]).modPow(a,t)).isUnit()&&!o.equals(i)){for(r=u-1;0!=r;r--){if((o=o.square().mod(t)).isUnit())return!1;if(o.equals(i))continue t}return!1}return!0}s.prototype=Object.create(u.prototype),c.prototype=Object.create(u.prototype),f.prototype=Object.create(u.prototype),s.prototype.add=function(t){var e=J(t);if(this.sign!==e.sign)return this.subtract(e.negate());var r=this.value,n=e.value;return e.isSmall?new s(b(r,Math.abs(n)),this.sign):new s(m(r,n),this.sign)},s.prototype.plus=s.prototype.add,c.prototype.add=function(t){var e=J(t),r=this.value;if(r<0!==e.sign)return this.subtract(e.negate());var n=e.value;if(e.isSmall){if(p(r+n))return new c(r+n);n=l(Math.abs(n))}return new s(b(n,Math.abs(r)),r<0)},c.prototype.plus=c.prototype.add,f.prototype.add=function(t){return new f(this.value+J(t).value)},f.prototype.plus=f.prototype.add,s.prototype.subtract=function(t){var e=J(t);if(this.sign!==e.sign)return this.add(e.negate());var r=this.value,n=e.value;return e.isSmall?E(r,Math.abs(n),this.sign):function(t,e,r){var n;return M(t,e)>=0?n=T(t,e):(n=T(e,t),r=!r),"number"==typeof(n=d(n))?(r&&(n=-n),new c(n)):new s(n,r)}(r,n,this.sign)},s.prototype.minus=s.prototype.subtract,c.prototype.subtract=function(t){var e=J(t),r=this.value;if(r<0!==e.sign)return this.add(e.negate());var n=e.value;return e.isSmall?new c(r-n):E(n,Math.abs(r),r>=0)},c.prototype.minus=c.prototype.subtract,f.prototype.subtract=function(t){return new f(this.value-J(t).value)},f.prototype.minus=f.prototype.subtract,s.prototype.negate=function(){return new s(this.value,!this.sign)},c.prototype.negate=function(){var t=this.sign,e=new c(-this.value);return e.sign=!t,e},f.prototype.negate=function(){return new f(-this.value)},s.prototype.abs=function(){return new s(this.value,!1)},c.prototype.abs=function(){return new c(Math.abs(this.value))},f.prototype.abs=function(){return new f(this.value>=0?this.value:-this.value)},s.prototype.multiply=function(t){var r,n,o,i=J(t),a=this.value,c=i.value,f=this.sign!==i.sign;if(i.isSmall){if(0===c)return u[0];if(1===c)return this;if(-1===c)return this.negate();if((r=Math.abs(c))<e)return new s(_(a,r),f);c=l(r)}return new s(-.012*(n=a.length)-.012*(o=c.length)+15e-6*n*o>0?x(a,c):A(a,c),f)},s.prototype.times=s.prototype.multiply,c.prototype._multiplyBySmall=function(t){return p(t.value*this.value)?new c(t.value*this.value):S(Math.abs(t.value),l(Math.abs(this.value)),this.sign!==t.sign)},s.prototype._multiplyBySmall=function(t){return 0===t.value?u[0]:1===t.value?this:-1===t.value?this.negate():S(Math.abs(t.value),this.value,this.sign!==t.sign)},c.prototype.multiply=function(t){return J(t)._multiplyBySmall(this)},c.prototype.times=c.prototype.multiply,f.prototype.multiply=function(t){return new f(this.value*J(t).value)},f.prototype.times=f.prototype.multiply,s.prototype.square=function(){return new s(I(this.value),!1)},c.prototype.square=function(){var t=this.value*this.value;return p(t)?new c(t):new s(I(l(Math.abs(this.value))),!1)},f.prototype.square=function(t){return new f(this.value*this.value)},s.prototype.divmod=function(t){var e=N(this,t);return{quotient:e[0],remainder:e[1]}},f.prototype.divmod=c.prototype.divmod=s.prototype.divmod,s.prototype.divide=function(t){return N(this,t)[0]},f.prototype.over=f.prototype.divide=function(t){return new f(this.value/J(t).value)},c.prototype.over=c.prototype.divide=s.prototype.over=s.prototype.divide,s.prototype.mod=function(t){return N(this,t)[1]},f.prototype.mod=f.prototype.remainder=function(t){return new f(this.value%J(t).value)},c.prototype.remainder=c.prototype.mod=s.prototype.remainder=s.prototype.mod,s.prototype.pow=function(t){var e,r,n,o=J(t),i=this.value,a=o.value;if(0===a)return u[1];if(0===i)return u[0];if(1===i)return u[1];if(-1===i)return o.isEven()?u[1]:u[-1];if(o.sign)return u[0];if(!o.isSmall)throw new Error("The exponent "+o.toString()+" is too large.");if(this.isSmall&&p(e=Math.pow(i,a)))return new c(y(e));for(r=this,n=u[1];!0&a&&(n=n.times(r),--a),0!==a;)a/=2,r=r.square();return n},c.prototype.pow=s.prototype.pow,f.prototype.pow=function(t){var e=J(t),r=this.value,n=e.value,o=BigInt(0),i=BigInt(1),a=BigInt(2);if(n===o)return u[1];if(r===o)return u[0];if(r===i)return u[1];if(r===BigInt(-1))return e.isEven()?u[1]:u[-1];if(e.isNegative())return new f(o);for(var s=this,c=u[1];(n&i)===i&&(c=c.times(s),--n),n!==o;)n/=a,s=s.square();return c},s.prototype.modPow=function(t,e){if(t=J(t),(e=J(e)).isZero())throw new Error("Cannot take modPow with modulus 0");for(var r=u[1],n=this.mod(e);t.isPositive();){if(n.isZero())return u[0];t.isOdd()&&(r=r.multiply(n).mod(e)),t=t.divide(2),n=n.square().mod(e)}return r},f.prototype.modPow=c.prototype.modPow=s.prototype.modPow,s.prototype.compareAbs=function(t){var e=J(t),r=this.value,n=e.value;return e.isSmall?1:M(r,n)},c.prototype.compareAbs=function(t){var e=J(t),r=Math.abs(this.value),n=e.value;return e.isSmall?r===(n=Math.abs(n))?0:r>n?1:-1:-1},f.prototype.compareAbs=function(t){var e=this.value,r=J(t).value;return(e=e>=0?e:-e)===(r=r>=0?r:-r)?0:e>r?1:-1},s.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var e=J(t),r=this.value,n=e.value;return this.sign!==e.sign?e.sign?1:-1:e.isSmall?this.sign?-1:1:M(r,n)*(this.sign?-1:1)},s.prototype.compareTo=s.prototype.compare,c.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var e=J(t),r=this.value,n=e.value;return e.isSmall?r==n?0:r>n?1:-1:r<0!==e.sign?r<0?-1:1:r<0?1:-1},c.prototype.compareTo=c.prototype.compare,f.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var e=this.value,r=J(t).value;return e===r?0:e>r?1:-1},f.prototype.compareTo=f.prototype.compare,s.prototype.equals=function(t){return 0===this.compare(t)},f.prototype.eq=f.prototype.equals=c.prototype.eq=c.prototype.equals=s.prototype.eq=s.prototype.equals,s.prototype.notEquals=function(t){return 0!==this.compare(t)},f.prototype.neq=f.prototype.notEquals=c.prototype.neq=c.prototype.notEquals=s.prototype.neq=s.prototype.notEquals,s.prototype.greater=function(t){return this.compare(t)>0},f.prototype.gt=f.prototype.greater=c.prototype.gt=c.prototype.greater=s.prototype.gt=s.prototype.greater,s.prototype.lesser=function(t){return this.compare(t)<0},f.prototype.lt=f.prototype.lesser=c.prototype.lt=c.prototype.lesser=s.prototype.lt=s.prototype.lesser,s.prototype.greaterOrEquals=function(t){return this.compare(t)>=0},f.prototype.geq=f.prototype.greaterOrEquals=c.prototype.geq=c.prototype.greaterOrEquals=s.prototype.geq=s.prototype.greaterOrEquals,s.prototype.lesserOrEquals=function(t){return this.compare(t)<=0},f.prototype.leq=f.prototype.lesserOrEquals=c.prototype.leq=c.prototype.lesserOrEquals=s.prototype.leq=s.prototype.lesserOrEquals,s.prototype.isEven=function(){return!(1&this.value[0])},c.prototype.isEven=function(){return!(1&this.value)},f.prototype.isEven=function(){return(this.value&BigInt(1))===BigInt(0)},s.prototype.isOdd=function(){return!(1&~this.value[0])},c.prototype.isOdd=function(){return!(1&~this.value)},f.prototype.isOdd=function(){return(this.value&BigInt(1))===BigInt(1)},s.prototype.isPositive=function(){return!this.sign},c.prototype.isPositive=function(){return this.value>0},f.prototype.isPositive=c.prototype.isPositive,s.prototype.isNegative=function(){return this.sign},c.prototype.isNegative=function(){return this.value<0},f.prototype.isNegative=c.prototype.isNegative,s.prototype.isUnit=function(){return!1},c.prototype.isUnit=function(){return 1===Math.abs(this.value)},f.prototype.isUnit=function(){return this.abs().value===BigInt(1)},s.prototype.isZero=function(){return!1},c.prototype.isZero=function(){return 0===this.value},f.prototype.isZero=function(){return this.value===BigInt(0)},s.prototype.isDivisibleBy=function(t){var e=J(t);return!e.isZero()&&(!!e.isUnit()||(0===e.compareAbs(2)?this.isEven():this.mod(e).isZero()))},f.prototype.isDivisibleBy=c.prototype.isDivisibleBy=s.prototype.isDivisibleBy,s.prototype.isPrime=function(e){var r=D(this);if(r!==t)return r;var n=this.abs(),o=n.bitLength();if(o<=64)return P(n,[2,3,5,7,11,13,17,19,23,29,31,37]);for(var i=Math.log(2)*o.toJSNumber(),a=Math.ceil(!0===e?2*Math.pow(i,2):i),u=[],s=0;s<a;s++)u.push(U(s+2));return P(n,u)},f.prototype.isPrime=c.prototype.isPrime=s.prototype.isPrime,s.prototype.isProbablePrime=function(e){var r=D(this);if(r!==t)return r;for(var n=this.abs(),o=e===t?5:e,i=[],a=0;a<o;a++)i.push(U.randBetween(2,n.minus(2)));return P(n,i)},f.prototype.isProbablePrime=c.prototype.isProbablePrime=s.prototype.isProbablePrime,s.prototype.modInv=function(t){for(var e,r,n,o=U.zero,i=U.one,a=J(t),u=this.abs();!u.isZero();)e=a.divide(u),r=o,n=a,o=i,a=u,i=r.subtract(e.multiply(i)),u=n.subtract(e.multiply(u));if(!a.isUnit())throw new Error(this.toString()+" and "+t.toString()+" are not co-prime");return-1===o.compare(0)&&(o=o.add(t)),this.isNegative()?o.negate():o},f.prototype.modInv=c.prototype.modInv=s.prototype.modInv,s.prototype.next=function(){var t=this.value;return this.sign?E(t,1,this.sign):new s(b(t,1),this.sign)},c.prototype.next=function(){var t=this.value;return t+1<n?new c(t+1):new s(o,!1)},f.prototype.next=function(){return new f(this.value+BigInt(1))},s.prototype.prev=function(){var t=this.value;return this.sign?new s(b(t,1),!0):E(t,1,this.sign)},c.prototype.prev=function(){var t=this.value;return t-1>-n?new c(t-1):new s(o,!0)},f.prototype.prev=function(){return new f(this.value-BigInt(1))};for(var C=[1];2*C[C.length-1]<=e;)C.push(2*C[C.length-1]);var R=C.length,F=C[R-1];function B(t){return Math.abs(t)<=e}function L(t,e,r){e=J(e);for(var n=t.isNegative(),o=e.isNegative(),i=n?t.not():t,a=o?e.not():e,u=0,s=0,c=null,f=null,p=[];!i.isZero()||!a.isZero();)u=(c=N(i,F))[1].toJSNumber(),n&&(u=F-1-u),s=(f=N(a,F))[1].toJSNumber(),o&&(s=F-1-s),i=c[0],a=f[0],p.push(r(u,s));for(var l=0!==r(n?1:0,o?1:0)?U(-1):U(0),d=p.length-1;d>=0;d-=1)l=l.multiply(F).add(U(p[d]));return l}s.prototype.shiftLeft=function(t){var e=J(t).toJSNumber();if(!B(e))throw new Error(String(e)+" is too large for shifting.");if(e<0)return this.shiftRight(-e);var r=this;if(r.isZero())return r;for(;e>=R;)r=r.multiply(F),e-=R-1;return r.multiply(C[e])},f.prototype.shiftLeft=c.prototype.shiftLeft=s.prototype.shiftLeft,s.prototype.shiftRight=function(t){var e,r=J(t).toJSNumber();if(!B(r))throw new Error(String(r)+" is too large for shifting.");if(r<0)return this.shiftLeft(-r);for(var n=this;r>=R;){if(n.isZero()||n.isNegative()&&n.isUnit())return n;n=(e=N(n,F))[1].isNegative()?e[0].prev():e[0],r-=R-1}return(e=N(n,C[r]))[1].isNegative()?e[0].prev():e[0]},f.prototype.shiftRight=c.prototype.shiftRight=s.prototype.shiftRight,s.prototype.not=function(){return this.negate().prev()},f.prototype.not=c.prototype.not=s.prototype.not,s.prototype.and=function(t){return L(this,t,(function(t,e){return t&e}))},f.prototype.and=c.prototype.and=s.prototype.and,s.prototype.or=function(t){return L(this,t,(function(t,e){return t|e}))},f.prototype.or=c.prototype.or=s.prototype.or,s.prototype.xor=function(t){return L(this,t,(function(t,e){return t^e}))},f.prototype.xor=c.prototype.xor=s.prototype.xor;var j=1<<30,k=(e&-e)*(e&-e)|j;function G(t){var r=t.value,n="number"==typeof r?r|j:"bigint"==typeof r?r|BigInt(j):r[0]+r[1]*e|k;return n&-n}function q(t,e){if(e.compareTo(t)<=0){var r=q(t,e.square(e)),n=r.p,o=r.e,i=n.multiply(e);return i.compareTo(t)<=0?{p:i,e:2*o+1}:{p:n,e:2*o}}return{p:U(1),e:0}}function V(t,e){return t=J(t),e=J(e),t.greater(e)?t:e}function Y(t,e){return t=J(t),e=J(e),t.lesser(e)?t:e}function H(t,e){if(t=J(t).abs(),e=J(e).abs(),t.equals(e))return t;if(t.isZero())return e;if(e.isZero())return t;for(var r,n,o=u[1];t.isEven()&&e.isEven();)r=Y(G(t),G(e)),t=t.divide(r),e=e.divide(r),o=o.multiply(r);for(;t.isEven();)t=t.divide(G(t));do{for(;e.isEven();)e=e.divide(G(e));t.greater(e)&&(n=e,e=t,t=n),e=e.subtract(t)}while(!e.isZero());return o.isUnit()?t:t.multiply(o)}s.prototype.bitLength=function(){var t=this;return t.compareTo(U(0))<0&&(t=t.negate().subtract(U(1))),0===t.compareTo(U(0))?U(0):U(q(t,U(2)).e).add(U(1))},f.prototype.bitLength=c.prototype.bitLength=s.prototype.bitLength;var W=function(t,e,r,n){r=r||i,t=String(t),n||(t=t.toLowerCase(),r=r.toLowerCase());var o,a=t.length,u=Math.abs(e),s={};for(o=0;o<r.length;o++)s[r[o]]=o;for(o=0;o<a;o++)if("-"!==(p=t[o])&&p in s&&s[p]>=u){if("1"===p&&1===u)continue;throw new Error(p+" is not a valid digit in base "+e+".")}e=J(e);var c=[],f="-"===t[0];for(o=f?1:0;o<t.length;o++){var p;if((p=t[o])in s)c.push(J(s[p]));else{if("<"!==p)throw new Error(p+" is not a valid character");var l=o;do{o++}while(">"!==t[o]&&o<t.length);c.push(J(t.slice(l+1,o)))}}return K(c,e,f)};function K(t,e,r){var n,o=u[0],i=u[1];for(n=t.length-1;n>=0;n--)o=o.add(t[n].times(i)),i=i.times(e);return r?o.negate():o}function z(t,e){if((e=U(e)).isZero()){if(t.isZero())return{value:[0],isNegative:!1};throw new Error("Cannot convert nonzero numbers to base 0.")}if(e.equals(-1)){if(t.isZero())return{value:[0],isNegative:!1};if(t.isNegative())return{value:[].concat.apply([],Array.apply(null,Array(-t.toJSNumber())).map(Array.prototype.valueOf,[1,0])),isNegative:!1};var r=Array.apply(null,Array(t.toJSNumber()-1)).map(Array.prototype.valueOf,[0,1]);return r.unshift([1]),{value:[].concat.apply([],r),isNegative:!1}}var n=!1;if(t.isNegative()&&e.isPositive()&&(n=!0,t=t.abs()),e.isUnit())return t.isZero()?{value:[0],isNegative:!1}:{value:Array.apply(null,Array(t.toJSNumber())).map(Number.prototype.valueOf,1),isNegative:n};for(var o,i=[],a=t;a.isNegative()||a.compareAbs(e)>=0;){o=a.divmod(e),a=o.quotient;var u=o.remainder;u.isNegative()&&(u=e.minus(u).abs(),a=a.next()),i.push(u.toJSNumber())}return i.push(a.toJSNumber()),{value:i.reverse(),isNegative:n}}function $(t,e,r){var n=z(t,e);return(n.isNegative?"-":"")+n.value.map((function(t){return function(t,e){return t<(e=e||i).length?e[t]:"<"+t+">"}(t,r)})).join("")}function Z(t){if(p(+t)){var e=+t;if(e===y(e))return a?new f(BigInt(e)):new c(e);throw new Error("Invalid integer: "+t)}var n="-"===t[0];n&&(t=t.slice(1));var o=t.split(/e/i);if(o.length>2)throw new Error("Invalid integer: "+o.join("e"));if(2===o.length){var i=o[1];if("+"===i[0]&&(i=i.slice(1)),(i=+i)!==y(i)||!p(i))throw new Error("Invalid integer: "+i+" is not a valid exponent.");var u=o[0],l=u.indexOf(".");if(l>=0&&(i-=u.length-l-1,u=u.slice(0,l)+u.slice(l+1)),i<0)throw new Error("Cannot include negative exponent part for integers");t=u+=new Array(i+1).join("0")}if(!/^([0-9][0-9]*)$/.test(t))throw new Error("Invalid integer: "+t);if(a)return new f(BigInt(n?"-"+t:t));for(var d=[],v=t.length,g=r,m=v-g;v>0;)d.push(+t.slice(m,v)),(m-=g)<0&&(m=0),v-=g;return h(d),new s(d,n)}function J(t){return"number"==typeof t?function(t){if(a)return new f(BigInt(t));if(p(t)){if(t!==y(t))throw new Error(t+" is not an integer.");return new c(t)}return Z(t.toString())}(t):"string"==typeof t?Z(t):"bigint"==typeof t?new f(t):t}s.prototype.toArray=function(t){return z(this,t)},c.prototype.toArray=function(t){return z(this,t)},f.prototype.toArray=function(t){return z(this,t)},s.prototype.toString=function(e,r){if(e===t&&(e=10),10!==e)return $(this,e,r);for(var n,o=this.value,i=o.length,a=String(o[--i]);--i>=0;)n=String(o[i]),a+="0000000".slice(n.length)+n;return(this.sign?"-":"")+a},c.prototype.toString=function(e,r){return e===t&&(e=10),10!=e?$(this,e,r):String(this.value)},f.prototype.toString=c.prototype.toString,f.prototype.toJSON=s.prototype.toJSON=c.prototype.toJSON=function(){return this.toString()},s.prototype.valueOf=function(){return parseInt(this.toString(),10)},s.prototype.toJSNumber=s.prototype.valueOf,c.prototype.valueOf=function(){return this.value},c.prototype.toJSNumber=c.prototype.valueOf,f.prototype.valueOf=f.prototype.toJSNumber=function(){return parseInt(this.toString(),10)};for(var X=0;X<1e3;X++)u[X]=J(X),X>0&&(u[-X]=J(-X));return u.one=u[1],u.zero=u[0],u.minusOne=u[-1],u.max=V,u.min=Y,u.gcd=H,u.lcm=function(t,e){return t=J(t).abs(),e=J(e).abs(),t.divide(H(t,e)).multiply(e)},u.isInstance=function(t){return t instanceof s||t instanceof c||t instanceof f},u.randBetween=function(t,r){var n=Y(t=J(t),r=J(r)),o=V(t,r).subtract(n).add(1);if(o.isSmall)return n.add(Math.floor(Math.random()*o));for(var i=z(o,e).value,a=[],s=!0,c=0;c<i.length;c++){var f=s?i[c]:e,p=y(Math.random()*f);a.push(p),p<f&&(s=!1)}return n.add(u.fromArray(a,e,!1))},u.fromArray=function(t,e,r){return K(t.map(J),J(e||10),r)},u}());"function"==typeof define&&define.amd&&define("big-integer",[],(function(){return U}));var k=U;function G(t){var e,r,n,o=(t=t||{}).BASE64,i=t.debug,a=t.constants,u=t.manifestModel,s=t.settings,c=["AAC","AACL","AACH","AACP","AVC1","H264","TTML","DFXP"],f={CAPT:"main",SUBT:"alternate",DESC:"main"},p={DESC:"2"},l={96e3:0,88200:1,64e3:2,48e3:3,44100:4,32e3:5,24e3:6,22050:7,16e3:8,12e3:9,11025:10,8e3:11,7350:12},d={video:"video/mp4",audio:"audio/mp4",text:"application/mp4"};function h(t,e){var r=t.getAttribute(e);return!!r&&"true"===r.toLowerCase()}function v(t,e){var r,n,o,i,a,u={},s=[],c=t.getAttribute("Name"),l=t.getAttribute("Type"),h=t.getAttribute("Language"),v=h?l+"_"+h:l;for(u.id=c||v,u.contentType=l,u.lang=h||"und",u.mimeType=d[l],u.subType=t.getAttribute("Subtype"),u.maxWidth=t.getAttribute("MaxWidth"),u.maxHeight=t.getAttribute("MaxHeight"),u.subType&&(f[u.subType]&&(u.Role=[{schemeIdUri:"urn:mpeg:dash:role:2011",value:f[u.subType]}]),p[u.subType]&&(u.Accessibility=[{schemeIdUri:"urn:tva:metadata:cs:AudioPurposeCS:2007",value:p[u.subType]}])),r=function(t,e){var r,n,o,i={};return r=(r=(o=t.getAttribute("Url"))?o.replace("{bitrate}","$Bandwidth$"):null)?r.replace("{start time}","$Time$"):null,n=(n=t.getAttribute("TimeScale"))?parseFloat(n):e,i.media=r,i.timescale=n,i.SegmentTimeline=function(t,e){var r,n,o,i,a,u,s={},c=t.getElementsByTagName("c"),f=[],p=0;for(i=0;i<c.length;i++)if(r={},(o=c[i].getAttribute("t"))&&k(o).greater(k(Number.MAX_SAFE_INTEGER))&&(r.tManifest=o),r.t=parseFloat(o),r.d=parseFloat(c[i].getAttribute("d")),0!==i||r.t||(r.t=0),i>0&&((n=f[f.length-1]).d||(n.tManifest?n.d=k(o).subtract(k(n.tManifest)).toJSNumber():n.d=r.t-n.t,p+=n.d),r.t||(n.tManifest?(r.tManifest=k(n.tManifest).add(k(n.d)).toString(),r.t=parseFloat(r.tManifest)):r.t=n.t+n.d)),r.d&&(p+=r.d),f.push(r),u=parseFloat(c[i].getAttribute("r")))for(a=0;a<u-1;a++)n=f[f.length-1],(r={}).t=n.t+n.d,r.d=n.d,n.tManifest&&(r.tManifest=k(n.tManifest).add(k(n.d)).toString()),p+=r.d,f.push(r);return s.S=f,s.duration=p/e,s}(t,i.timescale),i.availabilityTimeOffset="INF",i}(t,e),n=t.getElementsByTagName("QualityLevel"),i=0;i<n.length;i++)n[i].BaseURL=u.BaseURL,n[i].mimeType=u.mimeType,a=n[i].getAttribute("Index"),n[i].Id=u.id+(null!==a?"_"+a:""),null!==(o=y(n[i],t))&&(o.SegmentTemplate=r,s.push(o));return 0===s.length?null:(u.Representation=s,u.SegmentTemplate=r,u)}function y(t,e){var n,o,i={},u=e.getAttribute("Type"),s=null;if(i.id=t.Id,i.bandwidth=parseInt(t.getAttribute("Bitrate"),10),i.mimeType=t.mimeType,n=parseInt(t.getAttribute("MaxWidth"),10),o=parseInt(t.getAttribute("MaxHeight"),10),isNaN(n)||(i.width=n),isNaN(o)||(i.height=o),null!==(s=t.getAttribute("FourCC"))&&""!==s||(s=e.getAttribute("FourCC")),null===s||""===s)if(u===a.AUDIO)s="AAC";else if(u===a.VIDEO)return r.debug('FourCC is not defined whereas it is required for a QualityLevel element for a StreamIndex of type "video"'),null;return-1===c.indexOf(s.toUpperCase())?(r.warn("Codec not supported: "+s),null):("H264"===s||"AVC1"===s?i.codecs=function(t){var e,r=t.getAttribute("CodecPrivateData").toString();return"avc1."+((e=/00000001[0-9]7/.exec(r))&&e[0]?r.substr(r.indexOf(e[0])+10,6):void 0)}(t):s.indexOf("AAC")>=0?(i.codecs=function(t,e){var r,n,o,i,a=parseInt(t.getAttribute("SamplingRate"),10),u=t.getAttribute("CodecPrivateData").toString(),s=0;return"AACH"===e&&(s=5),void 0===u||""===u?(s=2,o=l[a],"AACH"===e?(s=5,u=new Uint8Array(4),i=l[2*a],u[0]=s<<3|o>>1,u[1]=o<<7|t.Channels<<3|i>>1,u[2]=i<<7|8,u[3]=0,(n=new Uint16Array(2))[0]=(u[0]<<8)+u[1],n[1]=(u[2]<<8)+u[3],r=n[0].toString(16),r=n[0].toString(16)+n[1].toString(16)):((u=new Uint8Array(2))[0]=s<<3|o>>1,u[1]=o<<7|parseInt(t.getAttribute("Channels"),10)<<3,(n=new Uint16Array(1))[0]=(u[0]<<8)+u[1],r=n[0].toString(16)),u=(u=""+r).toUpperCase(),t.setAttribute("CodecPrivateData",u)):0===s&&(s=(248&parseInt(u.substr(0,2),16))>>3),"mp4a.40."+s}(t,s),i.audioSamplingRate=parseInt(t.getAttribute("SamplingRate"),10),i.audioChannels=parseInt(t.getAttribute("Channels"),10)):(s.indexOf("TTML")||s.indexOf("DFXP"))&&(i.codecs=a.STPP),i.codecPrivateData=""+t.getAttribute("CodecPrivateData"),i.BaseURL=t.BaseURL,i)}function g(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function m(t){var e,r,i,c,f,p,l,d,y,m,b,T={},E=[],A=t.getElementsByTagName("SmoothStreamingMedia")[0],_=t.getElementsByTagName("Protection")[0],w=null;T.protocol="MSS",T.profiles="urn:mpeg:dash:profile:isoff-live:2011",T.type=h(A,"IsLive")?"dynamic":"static",d=A.getAttribute("TimeScale"),T.timescale=d?parseFloat(d):1e7;var x=parseFloat(A.getAttribute("DVRWindowLength"));"dynamic"!==T.type||0!==x&&!isNaN(x)||(x=1/0),0===x&&h(A,"CanSeek")&&(x=1/0),x>0&&(T.timeShiftBufferDepth=x/T.timescale);var S=parseFloat(A.getAttribute("Duration"));for(T.mediaPresentationDuration=0===S?1/0:S/T.timescale,T.minBufferTime=2,T.ttmlTimeIsRelative=!0,"dynamic"===T.type&&S>0&&(T.type="static",T.timeShiftBufferDepth=S/T.timescale),"dynamic"===T.type&&(T.refreshManifestOnSwitchTrack=!0,T.doNotUpdateDVRWindowOnBufferUpdated=!0,T.ignorePostponeTimePeriod=!0,T.availabilityStartTime=new Date(null)),e=function(t,e){var r,n,o={AdaptationSet:[]};r=t.getElementsByTagName("StreamIndex");for(var i=0;i<r.length;i++)null!==(n=v(r[i],e))&&o.AdaptationSet.push(n);return o}(A,T.timescale),T.Period=[e],e.start=0,void 0!==_&&((w=t.getElementsByTagName("ProtectionHeader")[0]).firstChild.data=w.firstChild.data.replace(/\n|\r/g,""),c=function(t){var e,r,n;return(e=function(t){var e,r,n,o=0;for(t[o+3],t[o+2],t[o+1],t[o],t[(o+=4)+1],t[o],o+=2;o<t.length;)if(e=(t[o+1]<<8)+t[o],o+=2,1===e)return r=(t[o+1]<<8)+t[o],o+=2,(n=new Uint8Array(r)).set(t.subarray(o,o+r)),n;return null}(o.decodeArray(t.firstChild.data)))&&(e=new Uint16Array(e.buffer),e=String.fromCharCode.apply(null,e),r=(new DOMParser).parseFromString(e,"application/xml").querySelector("KID").textContent,g(n=r=o.decodeArray(r),0,3),g(n,1,2),g(n,4,5),g(n,6,7)),r}(w),i=function(t){return{schemeIdUri:"urn:uuid:9a04f079-9840-4286-ab92-e65be0885f95",value:"com.microsoft.playready",pro:{__text:t.firstChild.data,__prefix:"mspr"}}}(w),i["cenc:default_KID"]=c,E.push(i),i=function(t){var e={schemeIdUri:"urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed",value:"com.widevine.alpha"};if(!t)return e;var r=new Uint8Array(2+t.length);r[0]=18,r[1]=16,r.set(t,2);var n=32+r.length,i=new Uint8Array(n),a=0;return i[a++]=(4278190080&n)>>24,i[a++]=(16711680&n)>>16,i[a++]=(65280&n)>>8,i[a++]=255&n,i.set([112,115,115,104,0,0,0,0],a),a+=8,i.set([237,239,139,169,121,214,74,206,163,200,39,220,213,29,33,237],a),a+=16,i[a++]=(4278190080&r.length)>>24,i[a++]=(16711680&r.length)>>16,i[a++]=(65280&r.length)>>8,i[a++]=255&r.length,i.set(r,a),i=String.fromCharCode.apply(null,i),i=o.encodeASCII(i),e.pssh={__text:i},e}(c),i["cenc:default_KID"]=c,E.push(i),T.ContentProtection=E),r=e.AdaptationSet,m=0;m<r.length;m+=1)r[m].SegmentTemplate.initialization="$Bandwidth$",void 0!==T.ContentProtection&&(r[m].ContentProtection=T.ContentProtection,r[m].ContentProtection=T.ContentProtection),"video"===r[m].contentType&&(y=r[m].SegmentTemplate.SegmentTimeline.S[0].d/r[m].SegmentTemplate.timescale,T.minBufferTime=y,"dynamic"===T.type&&T.timeShiftBufferDepth>0&&T.timeShiftBufferDepth!==1/0&&T.timeShiftBufferDepth>r[m].SegmentTemplate.SegmentTimeline.duration&&(T.timeShiftBufferDepth=r[m].SegmentTemplate.SegmentTimeline.duration));if(T.minBufferTime=Math.min(T.minBufferTime,T.timeShiftBufferDepth?T.timeShiftBufferDepth:1/0),"dynamic"===T.type){var I=s.get().streaming.delay.liveDelay;I||(I=y*(null===s.get().streaming.delay.liveDelayFragmentCount||isNaN(s.get().streaming.delay.liveDelayFragmentCount)?4:s.get().streaming.delay.liveDelayFragmentCount));var O=Math.max(T.timeShiftBufferDepth-10,T.timeShiftBufferDepth/2),N=Math.min(O,I),M=N-1.5*y;n={streaming:{buffer:{bufferTimeDefault:s.get().streaming.buffer.bufferTimeDefault,bufferTimeAtTopQuality:s.get().streaming.buffer.bufferTimeAtTopQuality,bufferTimeAtTopQualityLongForm:s.get().streaming.buffer.bufferTimeAtTopQualityLongForm},timeShiftBuffer:{calcFromSegmentTimeline:s.get().streaming.timeShiftBuffer.calcFromSegmentTimeline},delay:{liveDelay:s.get().streaming.delay.liveDelay}}},s.update({streaming:{buffer:{bufferTimeDefault:M,bufferTimeAtTopQuality:M,bufferTimeAtTopQualityLongForm:M},timeShiftBuffer:{calcFromSegmentTimeline:!0},delay:{liveDelay:N}}})}if(delete T.ContentProtection,"static"===T.type){var D=u.getValue();if(D&&D.timestampOffset)f=D.timestampOffset;else for(m=0;m<r.length;m++)r[m].contentType!==a.AUDIO&&r[m].contentType!==a.VIDEO||(p=(l=r[m].SegmentTemplate.SegmentTimeline.S)[0].t,void 0===f&&(f=p),f=Math.min(f,p),T.mediaPresentationDuration=Math.min(T.mediaPresentationDuration,r[m].SegmentTemplate.SegmentTimeline.duration));if(f>0){for(T.timestampOffset=f,m=0;m<r.length;m++){for(l=r[m].SegmentTemplate.SegmentTimeline.S,b=0;b<l.length;b++)l[b].tManifest||(l[b].tManifest=l[b].t.toString()),l[b].t-=f;r[m].contentType!==a.AUDIO&&r[m].contentType!==a.VIDEO||(e.start=Math.max(l[0].t,e.start),r[m].SegmentTemplate.presentationTimeOffset=e.start)}e.start/=T.timescale}}return T.mediaPresentationDuration=Math.floor(1e3*T.mediaPresentationDuration)/1e3,e.duration=T.mediaPresentationDuration,T}return e={parse:function(t){var e,n,o=window.performance.now();e=function(t){var e=null;if(window.DOMParser&&(e=(new window.DOMParser).parseFromString(t,"text/xml")).getElementsByTagName("parsererror").length>0)throw new Error("parsing the manifest failed");return e}(t);var i=window.performance.now();if(null===e)return null;n=m(e,new Date);var a=window.performance.now();return r.info("Parsing complete: (xmlParsing: "+(i-o).toPrecision(3)+"ms, mss2dash: "+(a-i).toPrecision(3)+"ms, total: "+((a-o)/1e3).toPrecision(3)+"s)"),n},getIron:function(){return null},reset:function(){n&&s.update(n)}},r=i.getLogger(e),e}G.__dashjs_factory_name="MssParser";var q=h.getClassFactory(G);function V(t){t=t||{};var e,r,n,o,i=this.context,a=t.eventBus,u=t.events,s=t.constants,p=t.initSegmentType,d=t.playbackController,h=t.streamController;function v(t){return h.getActiveStreamProcessors().filter((function(e){return e.getType()===t}))[0]}function m(t){return n.filter((function(e){return e.getType()===t}))[0]}function b(){h.getActiveStreamProcessors().forEach((function(e){if(e.getType()===s.VIDEO||e.getType()===s.AUDIO||e.getType()===s.TEXT){var r=m(e.getType());r||((r=y(i).create({streamProcessor:e,baseURLController:t.baseURLController,debug:t.debug})).initialize(),n.push(r)),r.start()}}))}function T(e){var n=v(e.mediaType);if(n){var o=n.getRepresentationController().getCurrentRepresentation(),i=n.getMediaInfo(),s=new l;s.mediaType=o.adaptation.type,s.type=p,s.range=o.range,s.bandwidth=o.bandwidth,s.representation=o;var f=function(t,e,r){var n=new c;return n.streamId=e,n.segmentType=t.type,n.start=t.startTime,n.duration=t.duration,n.end=n.start+n.duration,n.index=t.index,n.bandwidth=t.bandwidth,n.representation=t.representation,n.endFragment=r,n}(s,i.streamInfo.id,e.type!==u.FRAGMENT_LOADING_PROGRESS);try{f.bytes=r.generateMoov(o),a.trigger(u.INIT_FRAGMENT_LOADED,{chunk:f},{streamId:i.streamInfo.id,mediaType:o.adaptation.type})}catch(e){t.errHandler.error(new g(e.code,e.message,e.data))}e.sender=null}}function E(t){if(!t.error){var e=v(t.request.mediaType);if(e){if(r.processFragment(t,e),t.request.type===f.MSS_FRAGMENT_INFO_SEGMENT_TYPE){var n=m(t.request.mediaType);n&&n.fragmentInfoLoaded(t)}var o=t.request.representation.mediaInfo.streamInfo.manifestInfo;o.isDynamic||o.dvrWindowSize===1/0||b()}}}function A(){d.getIsDynamic()&&0!==d.getTime()&&b()}function _(){d.getIsDynamic()&&0!==d.getTime()&&b()}function w(t){t&&t.data&&(t.data=t.data.replace(/http:\/\/www.w3.org\/2006\/10\/ttaf1/gi,"http://www.w3.org/ns/ttml"))}return o={reset:function(){e&&(e.reset(),e=void 0),a.off(u.INIT_FRAGMENT_NEEDED,T,this),a.off(u.PLAYBACK_PAUSED,A,this),a.off(u.PLAYBACK_SEEKING,_,this),a.off(u.FRAGMENT_LOADING_COMPLETED,E,this),a.off(u.TTML_TO_PARSE,w,this),n.forEach((function(t){t.reset()})),n=[]},createMssParser:function(){return e=q(i).create(t)},createMssFragmentProcessor:function(){r=j(i).create(t)},registerEvents:function(){a.on(u.INIT_FRAGMENT_NEEDED,T,o,{priority:dashjs.FactoryMaker.getSingletonFactoryByName(a.getClassName()).EVENT_PRIORITY_HIGH}),a.on(u.PLAYBACK_PAUSED,A,o,{priority:dashjs.FactoryMaker.getSingletonFactoryByName(a.getClassName()).EVENT_PRIORITY_HIGH}),a.on(u.PLAYBACK_SEEKING,_,o,{priority:dashjs.FactoryMaker.getSingletonFactoryByName(a.getClassName()).EVENT_PRIORITY_HIGH}),a.on(u.FRAGMENT_LOADING_COMPLETED,E,o,{priority:dashjs.FactoryMaker.getSingletonFactoryByName(a.getClassName()).EVENT_PRIORITY_HIGH}),a.on(u.TTML_TO_PARSE,w,o)}},n=[],o}V.__dashjs_factory_name="MssHandler";var Y=dashjs.FactoryMaker.getClassFactory(V);Y.errors=w,dashjs.FactoryMaker.updateClassFactory(V.__dashjs_factory_name,Y);var H=Y,W="undefined"!=typeof window&&window||global,K=W.dashjs;K||(K=W.dashjs={}),K.MssHandler=H;var z=K;return n.default}()}));
//# sourceMappingURL=dash.mss.min.js.map