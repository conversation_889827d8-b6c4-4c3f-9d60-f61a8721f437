import*as e from"@react-pdf/primitives";export*from"@react-pdf/primitives";import t from"fs";import{Buffer as r}from"buffer";import o from"@react-pdf/font";import n from"@react-pdf/render";import i from"@react-pdf/pdfkit";import a from"@react-pdf/layout";import{upperFirst as d}from"@react-pdf/fns";import c from"@react-pdf/reconciler";const s=(e,t)=>{let{style:r,children:o,...n}=t;return{type:e,box:{},style:r||{},props:n||{},children:[]}},l=e=>({type:"TEXT_INSTANCE",value:e}),p=(e,t)=>{const r="TEXT"===e.type||"LINK"===e.type||"TSPAN"===e.type||"NOTE"===e.type;"TEXT_INSTANCE"!==t.type||r?e.children.push(t):console.warn(`Invalid '${t.value}' string child outside <Text> component`)},u=(e,t)=>{"ROOT"===e.type?e.document=t:p(e,t)},f=(e,t,r)=>{var o;const n=null===(o=e.children)||void 0===o?void 0:o.indexOf(r);void 0!==n&&-1!==n&&t&&e.children.splice(n,0,t)},m=(e,t)=>{var r;const o=null===(r=e.children)||void 0===r?void 0:r.indexOf(t);void 0!==o&&-1!==o&&e.children.splice(o,1)},v=(e,t)=>{var r;const o=null===(r=e.children)||void 0===r?void 0:r.indexOf(t);void 0!==o&&-1!==o&&e.children.splice(o,1)},y=(e,t,r)=>{e.value=r},h=(e,t,r,o,n)=>{const{style:i,...a}=n;e.props=a,e.style=i},w=e=>{let{onChange:t=(()=>{})}=e;return c({appendChild:p,appendChildToContainer:u,commitTextUpdate:y,commitUpdate:h,createInstance:s,createTextInstance:l,insertBefore:f,removeChild:m,removeChildFromContainer:v,resetAfterCommit:t})};var T={version:"4.3.0"};const{version:g}=T,b=new o;let P;const _={},D=e=>{const t={type:"ROOT",document:null};P=P||w({onChange:()=>{var e;const t=(null===(e=_.change)||void 0===e?void 0:e.slice())||[];for(let e=0;e<t.length;e+=1)t[e]()}});const r=P.createContainer(t),o=(e,t)=>{P.updateContainer(e,r,null,t)};e&&o(e);const c=async function(e){void 0===e&&(e=!0);const r=t.document.props||{},{pdfVersion:o,language:c,pageLayout:s,pageMode:l,title:p,author:u,subject:f,keyboards:m,creator:v="react-pdf",producer:y="react-pdf",creationDate:h=new Date,modificationDate:w}=r,T=new i({compress:e,pdfVersion:o,lang:c,displayTitle:!0,autoFirstPage:!1,info:(g={Title:p,Author:u,Subject:f,Keywords:m,Creator:v,Producer:y,CreationDate:h,ModificationDate:w},Object.fromEntries(Object.entries(g).filter((e=>{let[,t]=e;return void 0!==t}))))});var g;s&&(T._root.data.PageLayout=d(s)),l&&(T._root.data.PageMode=d(l));const P=await a(t.document,b);return{layout:P,fileStream:n(T,P)}},s=function(e){void 0===e&&(e={}),t.document.props.onRender&&t.document.props.onRender(e)};return{on:(e,t)=>{_[e]||(_[e]=[]),_[e].push(t)},container:t,toBlob:async()=>{const e=[],{layout:t,fileStream:r}=await c();return new Promise(((o,n)=>{r.on("data",(t=>{e.push(t instanceof Uint8Array?t:new Uint8Array(t))})),r.on("end",(()=>{try{const r=new Blob(e,{type:"application/pdf"});s({blob:r,_INTERNAL__LAYOUT__DATA_:t}),o(r)}catch(e){n(e)}}))}))},toBuffer:async()=>{const{layout:e,fileStream:t}=await c();return s({_INTERNAL__LAYOUT__DATA_:e}),t},toString:async()=>{"development"===process.env.NODE_ENV&&console.warn("`toString` is deprecated and will be removed in next major release");let e="";const{fileStream:t}=await c(!1);return new Promise(((r,o)=>{try{t.on("data",(t=>{e+=t})),t.on("end",(()=>{s(),r(e)}))}catch(e){o(e)}}))},removeListener:(e,t)=>{if(!_[e])return;const r=_[e].indexOf(t);r>-1&&_[e].splice(r,1)},updateContainer:o}},S=b,A={create:e=>e},C=async e=>{const t=D(e);return await t.toBuffer()},N=async(e,r,o)=>{const n=await C(e),i=t.createWriteStream(r);return n.pipe(i),new Promise(((e,t)=>{i.on("finish",(()=>{o&&o(n,r),e(n)})),i.on("error",t)}))},O=e=>C(e).then((e=>new Promise(((t,o)=>{const n=[];e.on("data",(e=>n.push(e))),e.on("end",(()=>t(r.concat(n)))),e.on("error",(e=>o(e)))})))),E=e=>("development"===process.env.NODE_ENV&&console.warn("`renderToString` is deprecated and will be removed in next major release, use `renderToBuffer` instead"),O(e).then((e=>e.toString()))),x=e=>{throw new Error(`${e} is a web specific API. You're either using this component on Node, or your bundler is not loading react-pdf from the appropriate web build.`)},F=()=>{x("usePDF")},L=()=>{x("PDFViewer")},I=()=>{x("PDFDownloadLink")},B=()=>{x("BlobProvider")},j=N;var R={pdf:D,Font:S,version:g,StyleSheet:A,usePDF:F,PDFViewer:L,BlobProvider:B,PDFDownloadLink:I,renderToStream:C,renderToString:E,renderToFile:N,render:j,...e};export{B as BlobProvider,S as Font,I as PDFDownloadLink,L as PDFViewer,A as StyleSheet,w as createRenderer,R as default,D as pdf,j as render,O as renderToBuffer,N as renderToFile,C as renderToStream,E as renderToString,F as usePDF,g as version};
