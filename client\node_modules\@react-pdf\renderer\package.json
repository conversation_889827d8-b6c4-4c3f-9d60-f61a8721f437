{"name": "@react-pdf/renderer", "version": "4.3.0", "license": "MIT", "description": "Create PDF files on the browser and server", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/diegomura/react-pdf#readme", "type": "module", "main": "./lib/react-pdf.js", "browser": {"./lib/react-pdf.js": "./lib/react-pdf.browser.js"}, "repository": {"type": "git", "url": "https://github.com/diegomura/react-pdf.git", "directory": "packages/renderer"}, "scripts": {"build": "rimraf ./lib && rollup -c", "watch": "rimraf ./lib && rollup -c -w", "size": "size-limit", "lint": "eslint src", "test": "vitest && vitest --config vitest.browser.config.js"}, "dependencies": {"@babel/runtime": "^7.20.13", "@react-pdf/fns": "3.1.2", "@react-pdf/font": "^4.0.2", "@react-pdf/layout": "^4.4.0", "@react-pdf/pdfkit": "^4.0.3", "@react-pdf/primitives": "^4.1.1", "@react-pdf/reconciler": "^1.1.4", "@react-pdf/render": "^4.3.0", "@react-pdf/types": "^2.9.0", "events": "^3.3.0", "object-assign": "^4.1.1", "prop-types": "^15.6.2", "queue": "^6.0.1"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "lint-staged": {"*.js": ["yarn lint", "prettier --write"]}, "files": ["lib", "index.d.ts"], "collective": {"type": "opencollective", "url": "https://opencollective.com/react-pdf", "logo": "https://opencollective.com/opencollective/logo.txt"}, "devDependencies": {"@size-limit/preset-big-lib": "^11.0.1", "assert": "^2.0.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "process": "^0.11.10", "size-limit": "^11.0.1", "util": "^0.12.4"}}