{"version": 3, "file": "dash.mss.debug.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;;;;;;;;;;;ACVA,IAAIA,MAAM,GAAG,UAAUC,SAAS,EAAE;EAC9B,YAAY;;EACZ,IAAIC,IAAI,GAAG,GAAG;IAAEC,QAAQ,GAAG,CAAC;IAAEC,OAAO,GAAG,gBAAgB;IAAEC,WAAW,GAAGC,YAAY,CAACF,OAAO,CAAC;IACzFG,gBAAgB,GAAG,sCAAsC;EAC7D,IAAIC,oBAAoB,GAAG,OAAOC,MAAM,KAAK,UAAU;EAEvD,SAASC,OAAOA,CAACC,CAAC,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAE;IAChD,IAAI,OAAOH,CAAC,KAAK,WAAW,EAAE,OAAOD,OAAO,CAAC,CAAC,CAAC;IAC/C,IAAI,OAAOE,KAAK,KAAK,WAAW,EAAE,OAAO,CAACA,KAAK,KAAK,EAAE,IAAI,CAACC,QAAQ,GAAGE,UAAU,CAACJ,CAAC,CAAC,GAAGK,SAAS,CAACL,CAAC,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,CAAC;IAClI,OAAOC,UAAU,CAACJ,CAAC,CAAC;EACxB;EAEA,SAASM,UAAUA,CAACC,KAAK,EAAEC,IAAI,EAAE;IAC7B,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,OAAO,GAAG,KAAK;EACxB;EAEAH,UAAU,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACb,OAAO,CAACW,SAAS,CAAC;EAEvD,SAASG,YAAYA,CAACN,KAAK,EAAE;IACzB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,IAAI,GAAGD,KAAK,GAAG,CAAC;IACrB,IAAI,CAACE,OAAO,GAAG,IAAI;EACvB;EAEAI,YAAY,CAACH,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACb,OAAO,CAACW,SAAS,CAAC;EAEzD,SAASI,YAAYA,CAACP,KAAK,EAAE;IACzB,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;EAEAO,YAAY,CAACJ,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACb,OAAO,CAACW,SAAS,CAAC;EAEzD,SAASK,SAASA,CAACC,CAAC,EAAE;IAClB,OAAO,CAACvB,OAAO,GAAGuB,CAAC,IAAIA,CAAC,GAAGvB,OAAO;EACtC;EAEA,SAASE,YAAYA,CAACqB,CAAC,EAAE;IACrB,IAAIA,CAAC,GAAG,GAAG,EAAE,OAAO,CAACA,CAAC,CAAC;IACvB,IAAIA,CAAC,GAAG,IAAI,EAAE,OAAO,CAACA,CAAC,GAAG,GAAG,EAAEC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,GAAG,CAAC,CAAC;IACnD,OAAO,CAACA,CAAC,GAAG,GAAG,EAAEC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,EAAEC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,IAAI,CAAC,CAAC;EACrE;EAEA,SAASG,YAAYA,CAACC,GAAG,EAAE;IACvBC,IAAI,CAACD,GAAG,CAAC;IACT,IAAIE,MAAM,GAAGF,GAAG,CAACE,MAAM;IACvB,IAAIA,MAAM,GAAG,CAAC,IAAIC,UAAU,CAACH,GAAG,EAAE1B,WAAW,CAAC,GAAG,CAAC,EAAE;MAChD,QAAQ4B,MAAM;QACV,KAAK,CAAC;UACF,OAAO,CAAC;QACZ,KAAK,CAAC;UACF,OAAOF,GAAG,CAAC,CAAC,CAAC;QACjB,KAAK,CAAC;UACF,OAAOA,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG7B,IAAI;QACjC;UACI,OAAO6B,GAAG,CAAC,CAAC,CAAC,GAAG,CAACA,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG7B,IAAI,IAAIA,IAAI;MACvD;IACJ;IACA,OAAO6B,GAAG;EACd;EAEA,SAASC,IAAIA,CAACrB,CAAC,EAAE;IACb,IAAIwB,CAAC,GAAGxB,CAAC,CAACsB,MAAM;IAChB,OAAOtB,CAAC,CAAC,EAAEwB,CAAC,CAAC,KAAK,CAAC,CAAE;IACrBxB,CAAC,CAACsB,MAAM,GAAGE,CAAC,GAAG,CAAC;EACpB;EAEA,SAASC,WAAWA,CAACH,MAAM,EAAE;IACzB,IAAII,CAAC,GAAG,IAAIC,KAAK,CAACL,MAAM,CAAC;IACzB,IAAIE,CAAC,GAAG,CAAC,CAAC;IACV,OAAO,EAAEA,CAAC,GAAGF,MAAM,EAAE;MACjBI,CAAC,CAACF,CAAC,CAAC,GAAG,CAAC;IACZ;IACA,OAAOE,CAAC;EACZ;EAEA,SAASE,QAAQA,CAACZ,CAAC,EAAE;IACjB,IAAIA,CAAC,GAAG,CAAC,EAAE,OAAOC,IAAI,CAACC,KAAK,CAACF,CAAC,CAAC;IAC/B,OAAOC,IAAI,CAACY,IAAI,CAACb,CAAC,CAAC;EACvB;EAEA,SAASc,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACf,IAAIC,GAAG,GAAGF,CAAC,CAACT,MAAM;MAAEY,GAAG,GAAGF,CAAC,CAACV,MAAM;MAAEa,CAAC,GAAG,IAAIR,KAAK,CAACM,GAAG,CAAC;MAAEG,KAAK,GAAG,CAAC;MAAEC,IAAI,GAAG9C,IAAI;MAAE+C,GAAG;MAAEd,CAAC;IACtF,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,GAAG,EAAEV,CAAC,EAAE,EAAE;MACtBc,GAAG,GAAGP,CAAC,CAACP,CAAC,CAAC,GAAGQ,CAAC,CAACR,CAAC,CAAC,GAAGY,KAAK;MACzBA,KAAK,GAAGE,GAAG,IAAID,IAAI,GAAG,CAAC,GAAG,CAAC;MAC3BF,CAAC,CAACX,CAAC,CAAC,GAAGc,GAAG,GAAGF,KAAK,GAAGC,IAAI;IAC7B;IACA,OAAOb,CAAC,GAAGS,GAAG,EAAE;MACZK,GAAG,GAAGP,CAAC,CAACP,CAAC,CAAC,GAAGY,KAAK;MAClBA,KAAK,GAAGE,GAAG,KAAKD,IAAI,GAAG,CAAC,GAAG,CAAC;MAC5BF,CAAC,CAACX,CAAC,EAAE,CAAC,GAAGc,GAAG,GAAGF,KAAK,GAAGC,IAAI;IAC/B;IACA,IAAID,KAAK,GAAG,CAAC,EAAED,CAAC,CAACI,IAAI,CAACH,KAAK,CAAC;IAC5B,OAAOD,CAAC;EACZ;EAEA,SAASK,MAAMA,CAACT,CAAC,EAAEC,CAAC,EAAE;IAClB,IAAID,CAAC,CAACT,MAAM,IAAIU,CAAC,CAACV,MAAM,EAAE,OAAOQ,GAAG,CAACC,CAAC,EAAEC,CAAC,CAAC;IAC1C,OAAOF,GAAG,CAACE,CAAC,EAAED,CAAC,CAAC;EACpB;EAEA,SAASU,QAAQA,CAACV,CAAC,EAAEK,KAAK,EAAE;IACxB,IAAIM,CAAC,GAAGX,CAAC,CAACT,MAAM;MAAEa,CAAC,GAAG,IAAIR,KAAK,CAACe,CAAC,CAAC;MAAEL,IAAI,GAAG9C,IAAI;MAAE+C,GAAG;MAAEd,CAAC;IACvD,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkB,CAAC,EAAElB,CAAC,EAAE,EAAE;MACpBc,GAAG,GAAGP,CAAC,CAACP,CAAC,CAAC,GAAGa,IAAI,GAAGD,KAAK;MACzBA,KAAK,GAAGnB,IAAI,CAACC,KAAK,CAACoB,GAAG,GAAGD,IAAI,CAAC;MAC9BF,CAAC,CAACX,CAAC,CAAC,GAAGc,GAAG,GAAGF,KAAK,GAAGC,IAAI;MACzBD,KAAK,IAAI,CAAC;IACd;IACA,OAAOA,KAAK,GAAG,CAAC,EAAE;MACdD,CAAC,CAACX,CAAC,EAAE,CAAC,GAAGY,KAAK,GAAGC,IAAI;MACrBD,KAAK,GAAGnB,IAAI,CAACC,KAAK,CAACkB,KAAK,GAAGC,IAAI,CAAC;IACpC;IACA,OAAOF,CAAC;EACZ;EAEA7B,UAAU,CAACI,SAAS,CAACoB,GAAG,GAAG,UAAU9B,CAAC,EAAE;IACpC,IAAIgB,CAAC,GAAGZ,UAAU,CAACJ,CAAC,CAAC;IACrB,IAAI,IAAI,CAACQ,IAAI,KAAKQ,CAAC,CAACR,IAAI,EAAE;MACtB,OAAO,IAAI,CAACmC,QAAQ,CAAC3B,CAAC,CAAC4B,MAAM,CAAC,CAAC,CAAC;IACpC;IACA,IAAIb,CAAC,GAAG,IAAI,CAACxB,KAAK;MAAEyB,CAAC,GAAGhB,CAAC,CAACT,KAAK;IAC/B,IAAIS,CAAC,CAACP,OAAO,EAAE;MACX,OAAO,IAAIH,UAAU,CAACmC,QAAQ,CAACV,CAAC,EAAEd,IAAI,CAAC4B,GAAG,CAACb,CAAC,CAAC,CAAC,EAAE,IAAI,CAACxB,IAAI,CAAC;IAC9D;IACA,OAAO,IAAIF,UAAU,CAACkC,MAAM,CAACT,CAAC,EAAEC,CAAC,CAAC,EAAE,IAAI,CAACxB,IAAI,CAAC;EAClD,CAAC;EACDF,UAAU,CAACI,SAAS,CAACoC,IAAI,GAAGxC,UAAU,CAACI,SAAS,CAACoB,GAAG;EACpDjB,YAAY,CAACH,SAAS,CAACoB,GAAG,GAAG,UAAU9B,CAAC,EAAE;IACtC,IAAIgB,CAAC,GAAGZ,UAAU,CAACJ,CAAC,CAAC;IACrB,IAAI+B,CAAC,GAAG,IAAI,CAACxB,KAAK;IAClB,IAAIwB,CAAC,GAAG,CAAC,KAAKf,CAAC,CAACR,IAAI,EAAE;MAClB,OAAO,IAAI,CAACmC,QAAQ,CAAC3B,CAAC,CAAC4B,MAAM,CAAC,CAAC,CAAC;IACpC;IACA,IAAIZ,CAAC,GAAGhB,CAAC,CAACT,KAAK;IACf,IAAIS,CAAC,CAACP,OAAO,EAAE;MACX,IAAIM,SAAS,CAACgB,CAAC,GAAGC,CAAC,CAAC,EAAE,OAAO,IAAInB,YAAY,CAACkB,CAAC,GAAGC,CAAC,CAAC;MACpDA,CAAC,GAAGrC,YAAY,CAACsB,IAAI,CAAC4B,GAAG,CAACb,CAAC,CAAC,CAAC;IACjC;IACA,OAAO,IAAI1B,UAAU,CAACmC,QAAQ,CAACT,CAAC,EAAEf,IAAI,CAAC4B,GAAG,CAACd,CAAC,CAAC,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;EAC1D,CAAC;EACDlB,YAAY,CAACH,SAAS,CAACoC,IAAI,GAAGjC,YAAY,CAACH,SAAS,CAACoB,GAAG;EACxDhB,YAAY,CAACJ,SAAS,CAACoB,GAAG,GAAG,UAAU9B,CAAC,EAAE;IACtC,OAAO,IAAIc,YAAY,CAAC,IAAI,CAACP,KAAK,GAAGH,UAAU,CAACJ,CAAC,CAAC,CAACO,KAAK,CAAC;EAC7D,CAAC;EACDO,YAAY,CAACJ,SAAS,CAACoC,IAAI,GAAGhC,YAAY,CAACJ,SAAS,CAACoB,GAAG;EAExD,SAASa,QAAQA,CAACZ,CAAC,EAAEC,CAAC,EAAE;IACpB,IAAIe,GAAG,GAAGhB,CAAC,CAACT,MAAM;MAAE0B,GAAG,GAAGhB,CAAC,CAACV,MAAM;MAAEa,CAAC,GAAG,IAAIR,KAAK,CAACoB,GAAG,CAAC;MAAEE,MAAM,GAAG,CAAC;MAAEZ,IAAI,GAAG9C,IAAI;MAAEiC,CAAC;MAAE0B,UAAU;IAC9F,KAAK1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,GAAG,EAAExB,CAAC,EAAE,EAAE;MACtB0B,UAAU,GAAGnB,CAAC,CAACP,CAAC,CAAC,GAAGyB,MAAM,GAAGjB,CAAC,CAACR,CAAC,CAAC;MACjC,IAAI0B,UAAU,GAAG,CAAC,EAAE;QAChBA,UAAU,IAAIb,IAAI;QAClBY,MAAM,GAAG,CAAC;MACd,CAAC,MAAMA,MAAM,GAAG,CAAC;MACjBd,CAAC,CAACX,CAAC,CAAC,GAAG0B,UAAU;IACrB;IACA,KAAK1B,CAAC,GAAGwB,GAAG,EAAExB,CAAC,GAAGuB,GAAG,EAAEvB,CAAC,EAAE,EAAE;MACxB0B,UAAU,GAAGnB,CAAC,CAACP,CAAC,CAAC,GAAGyB,MAAM;MAC1B,IAAIC,UAAU,GAAG,CAAC,EAAEA,UAAU,IAAIb,IAAI,CAAC,KAAM;QACzCF,CAAC,CAACX,CAAC,EAAE,CAAC,GAAG0B,UAAU;QACnB;MACJ;MACAf,CAAC,CAACX,CAAC,CAAC,GAAG0B,UAAU;IACrB;IACA,OAAO1B,CAAC,GAAGuB,GAAG,EAAEvB,CAAC,EAAE,EAAE;MACjBW,CAAC,CAACX,CAAC,CAAC,GAAGO,CAAC,CAACP,CAAC,CAAC;IACf;IACAH,IAAI,CAACc,CAAC,CAAC;IACP,OAAOA,CAAC;EACZ;EAEA,SAASgB,WAAWA,CAACpB,CAAC,EAAEC,CAAC,EAAExB,IAAI,EAAE;IAC7B,IAAID,KAAK;IACT,IAAIgB,UAAU,CAACQ,CAAC,EAAEC,CAAC,CAAC,IAAI,CAAC,EAAE;MACvBzB,KAAK,GAAGoC,QAAQ,CAACZ,CAAC,EAAEC,CAAC,CAAC;IAC1B,CAAC,MAAM;MACHzB,KAAK,GAAGoC,QAAQ,CAACX,CAAC,EAAED,CAAC,CAAC;MACtBvB,IAAI,GAAG,CAACA,IAAI;IAChB;IACAD,KAAK,GAAGY,YAAY,CAACZ,KAAK,CAAC;IAC3B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B,IAAIC,IAAI,EAAED,KAAK,GAAG,CAACA,KAAK;MACxB,OAAO,IAAIM,YAAY,CAACN,KAAK,CAAC;IAClC;IACA,OAAO,IAAID,UAAU,CAACC,KAAK,EAAEC,IAAI,CAAC;EACtC;EAEA,SAAS4C,aAAaA,CAACrB,CAAC,EAAEC,CAAC,EAAExB,IAAI,EAAE;IAC/B,IAAIkC,CAAC,GAAGX,CAAC,CAACT,MAAM;MAAEa,CAAC,GAAG,IAAIR,KAAK,CAACe,CAAC,CAAC;MAAEN,KAAK,GAAG,CAACJ,CAAC;MAAEK,IAAI,GAAG9C,IAAI;MAAEiC,CAAC;MAAE0B,UAAU;IAC1E,KAAK1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkB,CAAC,EAAElB,CAAC,EAAE,EAAE;MACpB0B,UAAU,GAAGnB,CAAC,CAACP,CAAC,CAAC,GAAGY,KAAK;MACzBA,KAAK,GAAGnB,IAAI,CAACC,KAAK,CAACgC,UAAU,GAAGb,IAAI,CAAC;MACrCa,UAAU,IAAIb,IAAI;MAClBF,CAAC,CAACX,CAAC,CAAC,GAAG0B,UAAU,GAAG,CAAC,GAAGA,UAAU,GAAGb,IAAI,GAAGa,UAAU;IAC1D;IACAf,CAAC,GAAGhB,YAAY,CAACgB,CAAC,CAAC;IACnB,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;MACvB,IAAI3B,IAAI,EAAE2B,CAAC,GAAG,CAACA,CAAC;MAChB,OAAO,IAAItB,YAAY,CAACsB,CAAC,CAAC;IAC9B;IACA,OAAO,IAAI7B,UAAU,CAAC6B,CAAC,EAAE3B,IAAI,CAAC;EAClC;EAEAF,UAAU,CAACI,SAAS,CAACiC,QAAQ,GAAG,UAAU3C,CAAC,EAAE;IACzC,IAAIgB,CAAC,GAAGZ,UAAU,CAACJ,CAAC,CAAC;IACrB,IAAI,IAAI,CAACQ,IAAI,KAAKQ,CAAC,CAACR,IAAI,EAAE;MACtB,OAAO,IAAI,CAACsB,GAAG,CAACd,CAAC,CAAC4B,MAAM,CAAC,CAAC,CAAC;IAC/B;IACA,IAAIb,CAAC,GAAG,IAAI,CAACxB,KAAK;MAAEyB,CAAC,GAAGhB,CAAC,CAACT,KAAK;IAC/B,IAAIS,CAAC,CAACP,OAAO,EAAE,OAAO2C,aAAa,CAACrB,CAAC,EAAEd,IAAI,CAAC4B,GAAG,CAACb,CAAC,CAAC,EAAE,IAAI,CAACxB,IAAI,CAAC;IAC9D,OAAO2C,WAAW,CAACpB,CAAC,EAAEC,CAAC,EAAE,IAAI,CAACxB,IAAI,CAAC;EACvC,CAAC;EACDF,UAAU,CAACI,SAAS,CAAC2C,KAAK,GAAG/C,UAAU,CAACI,SAAS,CAACiC,QAAQ;EAC1D9B,YAAY,CAACH,SAAS,CAACiC,QAAQ,GAAG,UAAU3C,CAAC,EAAE;IAC3C,IAAIgB,CAAC,GAAGZ,UAAU,CAACJ,CAAC,CAAC;IACrB,IAAI+B,CAAC,GAAG,IAAI,CAACxB,KAAK;IAClB,IAAIwB,CAAC,GAAG,CAAC,KAAKf,CAAC,CAACR,IAAI,EAAE;MAClB,OAAO,IAAI,CAACsB,GAAG,CAACd,CAAC,CAAC4B,MAAM,CAAC,CAAC,CAAC;IAC/B;IACA,IAAIZ,CAAC,GAAGhB,CAAC,CAACT,KAAK;IACf,IAAIS,CAAC,CAACP,OAAO,EAAE;MACX,OAAO,IAAII,YAAY,CAACkB,CAAC,GAAGC,CAAC,CAAC;IAClC;IACA,OAAOoB,aAAa,CAACpB,CAAC,EAAEf,IAAI,CAAC4B,GAAG,CAACd,CAAC,CAAC,EAAEA,CAAC,IAAI,CAAC,CAAC;EAChD,CAAC;EACDlB,YAAY,CAACH,SAAS,CAAC2C,KAAK,GAAGxC,YAAY,CAACH,SAAS,CAACiC,QAAQ;EAC9D7B,YAAY,CAACJ,SAAS,CAACiC,QAAQ,GAAG,UAAU3C,CAAC,EAAE;IAC3C,OAAO,IAAIc,YAAY,CAAC,IAAI,CAACP,KAAK,GAAGH,UAAU,CAACJ,CAAC,CAAC,CAACO,KAAK,CAAC;EAC7D,CAAC;EACDO,YAAY,CAACJ,SAAS,CAAC2C,KAAK,GAAGvC,YAAY,CAACJ,SAAS,CAACiC,QAAQ;EAC9DrC,UAAU,CAACI,SAAS,CAACkC,MAAM,GAAG,YAAY;IACtC,OAAO,IAAItC,UAAU,CAAC,IAAI,CAACC,KAAK,EAAE,CAAC,IAAI,CAACC,IAAI,CAAC;EACjD,CAAC;EACDK,YAAY,CAACH,SAAS,CAACkC,MAAM,GAAG,YAAY;IACxC,IAAIpC,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAI8C,KAAK,GAAG,IAAIzC,YAAY,CAAC,CAAC,IAAI,CAACN,KAAK,CAAC;IACzC+C,KAAK,CAAC9C,IAAI,GAAG,CAACA,IAAI;IAClB,OAAO8C,KAAK;EAChB,CAAC;EACDxC,YAAY,CAACJ,SAAS,CAACkC,MAAM,GAAG,YAAY;IACxC,OAAO,IAAI9B,YAAY,CAAC,CAAC,IAAI,CAACP,KAAK,CAAC;EACxC,CAAC;EACDD,UAAU,CAACI,SAAS,CAACmC,GAAG,GAAG,YAAY;IACnC,OAAO,IAAIvC,UAAU,CAAC,IAAI,CAACC,KAAK,EAAE,KAAK,CAAC;EAC5C,CAAC;EACDM,YAAY,CAACH,SAAS,CAACmC,GAAG,GAAG,YAAY;IACrC,OAAO,IAAIhC,YAAY,CAACI,IAAI,CAAC4B,GAAG,CAAC,IAAI,CAACtC,KAAK,CAAC,CAAC;EACjD,CAAC;EACDO,YAAY,CAACJ,SAAS,CAACmC,GAAG,GAAG,YAAY;IACrC,OAAO,IAAI/B,YAAY,CAAC,IAAI,CAACP,KAAK,IAAI,CAAC,GAAG,IAAI,CAACA,KAAK,GAAG,CAAC,IAAI,CAACA,KAAK,CAAC;EACvE,CAAC;EAED,SAASgD,YAAYA,CAACxB,CAAC,EAAEC,CAAC,EAAE;IACxB,IAAIe,GAAG,GAAGhB,CAAC,CAACT,MAAM;MAAE0B,GAAG,GAAGhB,CAAC,CAACV,MAAM;MAAEoB,CAAC,GAAGK,GAAG,GAAGC,GAAG;MAAEb,CAAC,GAAGV,WAAW,CAACiB,CAAC,CAAC;MAAEL,IAAI,GAAG9C,IAAI;MAAEiE,OAAO;MAAEpB,KAAK;MAAEZ,CAAC;MAAEiC,GAAG;MAAEC,GAAG;IAC/G,KAAKlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,GAAG,EAAE,EAAEvB,CAAC,EAAE;MACtBiC,GAAG,GAAG1B,CAAC,CAACP,CAAC,CAAC;MACV,KAAK,IAAImC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,GAAG,EAAE,EAAEW,CAAC,EAAE;QAC1BD,GAAG,GAAG1B,CAAC,CAAC2B,CAAC,CAAC;QACVH,OAAO,GAAGC,GAAG,GAAGC,GAAG,GAAGvB,CAAC,CAACX,CAAC,GAAGmC,CAAC,CAAC;QAC9BvB,KAAK,GAAGnB,IAAI,CAACC,KAAK,CAACsC,OAAO,GAAGnB,IAAI,CAAC;QAClCF,CAAC,CAACX,CAAC,GAAGmC,CAAC,CAAC,GAAGH,OAAO,GAAGpB,KAAK,GAAGC,IAAI;QACjCF,CAAC,CAACX,CAAC,GAAGmC,CAAC,GAAG,CAAC,CAAC,IAAIvB,KAAK;MACzB;IACJ;IACAf,IAAI,CAACc,CAAC,CAAC;IACP,OAAOA,CAAC;EACZ;EAEA,SAASyB,aAAaA,CAAC7B,CAAC,EAAEC,CAAC,EAAE;IACzB,IAAIU,CAAC,GAAGX,CAAC,CAACT,MAAM;MAAEa,CAAC,GAAG,IAAIR,KAAK,CAACe,CAAC,CAAC;MAAEL,IAAI,GAAG9C,IAAI;MAAE6C,KAAK,GAAG,CAAC;MAAEoB,OAAO;MAAEhC,CAAC;IACtE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkB,CAAC,EAAElB,CAAC,EAAE,EAAE;MACpBgC,OAAO,GAAGzB,CAAC,CAACP,CAAC,CAAC,GAAGQ,CAAC,GAAGI,KAAK;MAC1BA,KAAK,GAAGnB,IAAI,CAACC,KAAK,CAACsC,OAAO,GAAGnB,IAAI,CAAC;MAClCF,CAAC,CAACX,CAAC,CAAC,GAAGgC,OAAO,GAAGpB,KAAK,GAAGC,IAAI;IACjC;IACA,OAAOD,KAAK,GAAG,CAAC,EAAE;MACdD,CAAC,CAACX,CAAC,EAAE,CAAC,GAAGY,KAAK,GAAGC,IAAI;MACrBD,KAAK,GAAGnB,IAAI,CAACC,KAAK,CAACkB,KAAK,GAAGC,IAAI,CAAC;IACpC;IACA,OAAOF,CAAC;EACZ;EAEA,SAAS0B,SAASA,CAACnC,CAAC,EAAEV,CAAC,EAAE;IACrB,IAAImB,CAAC,GAAG,EAAE;IACV,OAAOnB,CAAC,EAAE,GAAG,CAAC,EAAEmB,CAAC,CAACI,IAAI,CAAC,CAAC,CAAC;IACzB,OAAOJ,CAAC,CAAC2B,MAAM,CAACpC,CAAC,CAAC;EACtB;EAEA,SAASqC,iBAAiBA,CAACrC,CAAC,EAAEsC,CAAC,EAAE;IAC7B,IAAIhD,CAAC,GAAGC,IAAI,CAACgD,GAAG,CAACvC,CAAC,CAACJ,MAAM,EAAE0C,CAAC,CAAC1C,MAAM,CAAC;IACpC,IAAIN,CAAC,IAAI,EAAE,EAAE,OAAOuC,YAAY,CAAC7B,CAAC,EAAEsC,CAAC,CAAC;IACtChD,CAAC,GAAGC,IAAI,CAACY,IAAI,CAACb,CAAC,GAAG,CAAC,CAAC;IACpB,IAAIgB,CAAC,GAAGN,CAAC,CAACwC,KAAK,CAAClD,CAAC,CAAC;MAAEe,CAAC,GAAGL,CAAC,CAACwC,KAAK,CAAC,CAAC,EAAElD,CAAC,CAAC;MAAEmD,CAAC,GAAGH,CAAC,CAACE,KAAK,CAAClD,CAAC,CAAC;MAAEoD,CAAC,GAAGJ,CAAC,CAACE,KAAK,CAAC,CAAC,EAAElD,CAAC,CAAC;IACxE,IAAIqD,EAAE,GAAGN,iBAAiB,CAAChC,CAAC,EAAEqC,CAAC,CAAC;MAAEE,EAAE,GAAGP,iBAAiB,CAAC/B,CAAC,EAAEmC,CAAC,CAAC;MAC1DI,IAAI,GAAGR,iBAAiB,CAACvB,MAAM,CAACT,CAAC,EAAEC,CAAC,CAAC,EAAEQ,MAAM,CAAC4B,CAAC,EAAED,CAAC,CAAC,CAAC;IACxD,IAAIX,OAAO,GAAGhB,MAAM,CAACA,MAAM,CAAC6B,EAAE,EAAER,SAAS,CAAClB,QAAQ,CAACA,QAAQ,CAAC4B,IAAI,EAAEF,EAAE,CAAC,EAAEC,EAAE,CAAC,EAAEtD,CAAC,CAAC,CAAC,EAAE6C,SAAS,CAACS,EAAE,EAAE,CAAC,GAAGtD,CAAC,CAAC,CAAC;IACtGK,IAAI,CAACmC,OAAO,CAAC;IACb,OAAOA,OAAO;EAClB;EAEA,SAASgB,YAAYA,CAACC,EAAE,EAAEC,EAAE,EAAE;IAC1B,OAAO,CAAC,IAAI,GAAGD,EAAE,GAAG,IAAI,GAAGC,EAAE,GAAG,KAAK,GAAGD,EAAE,GAAGC,EAAE,GAAG,CAAC;EACvD;EAEApE,UAAU,CAACI,SAAS,CAACiE,QAAQ,GAAG,UAAU3E,CAAC,EAAE;IACzC,IAAIgB,CAAC,GAAGZ,UAAU,CAACJ,CAAC,CAAC;MAAE+B,CAAC,GAAG,IAAI,CAACxB,KAAK;MAAEyB,CAAC,GAAGhB,CAAC,CAACT,KAAK;MAAEC,IAAI,GAAG,IAAI,CAACA,IAAI,KAAKQ,CAAC,CAACR,IAAI;MAAEqC,GAAG;IACpF,IAAI7B,CAAC,CAACP,OAAO,EAAE;MACX,IAAIuB,CAAC,KAAK,CAAC,EAAE,OAAOjC,OAAO,CAAC,CAAC,CAAC;MAC9B,IAAIiC,CAAC,KAAK,CAAC,EAAE,OAAO,IAAI;MACxB,IAAIA,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,IAAI,CAACY,MAAM,CAAC,CAAC;MAClCC,GAAG,GAAG5B,IAAI,CAAC4B,GAAG,CAACb,CAAC,CAAC;MACjB,IAAIa,GAAG,GAAGtD,IAAI,EAAE;QACZ,OAAO,IAAIe,UAAU,CAACsD,aAAa,CAAC7B,CAAC,EAAEc,GAAG,CAAC,EAAErC,IAAI,CAAC;MACtD;MACAwB,CAAC,GAAGrC,YAAY,CAACkD,GAAG,CAAC;IACzB;IACA,IAAI2B,YAAY,CAACzC,CAAC,CAACT,MAAM,EAAEU,CAAC,CAACV,MAAM,CAAC,EAAE,OAAO,IAAIhB,UAAU,CAACyD,iBAAiB,CAAChC,CAAC,EAAEC,CAAC,CAAC,EAAExB,IAAI,CAAC;IAC1F,OAAO,IAAIF,UAAU,CAACiD,YAAY,CAACxB,CAAC,EAAEC,CAAC,CAAC,EAAExB,IAAI,CAAC;EACnD,CAAC;EACDF,UAAU,CAACI,SAAS,CAACkE,KAAK,GAAGtE,UAAU,CAACI,SAAS,CAACiE,QAAQ;EAE1D,SAASE,qBAAqBA,CAAC9C,CAAC,EAAEC,CAAC,EAAExB,IAAI,EAAE;IACvC,IAAIuB,CAAC,GAAGxC,IAAI,EAAE;MACV,OAAO,IAAIe,UAAU,CAACsD,aAAa,CAAC5B,CAAC,EAAED,CAAC,CAAC,EAAEvB,IAAI,CAAC;IACpD;IACA,OAAO,IAAIF,UAAU,CAACiD,YAAY,CAACvB,CAAC,EAAErC,YAAY,CAACoC,CAAC,CAAC,CAAC,EAAEvB,IAAI,CAAC;EACjE;EAEAK,YAAY,CAACH,SAAS,CAACoE,gBAAgB,GAAG,UAAU/C,CAAC,EAAE;IACnD,IAAIhB,SAAS,CAACgB,CAAC,CAACxB,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,EAAE;MACjC,OAAO,IAAIM,YAAY,CAACkB,CAAC,CAACxB,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC;IACjD;IACA,OAAOsE,qBAAqB,CAAC5D,IAAI,CAAC4B,GAAG,CAACd,CAAC,CAACxB,KAAK,CAAC,EAAEZ,YAAY,CAACsB,IAAI,CAAC4B,GAAG,CAAC,IAAI,CAACtC,KAAK,CAAC,CAAC,EAAE,IAAI,CAACC,IAAI,KAAKuB,CAAC,CAACvB,IAAI,CAAC;EAC7G,CAAC;EACDF,UAAU,CAACI,SAAS,CAACoE,gBAAgB,GAAG,UAAU/C,CAAC,EAAE;IACjD,IAAIA,CAAC,CAACxB,KAAK,KAAK,CAAC,EAAE,OAAOR,OAAO,CAAC,CAAC,CAAC;IACpC,IAAIgC,CAAC,CAACxB,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAC9B,IAAIwB,CAAC,CAACxB,KAAK,KAAK,CAAC,CAAC,EAAE,OAAO,IAAI,CAACqC,MAAM,CAAC,CAAC;IACxC,OAAOiC,qBAAqB,CAAC5D,IAAI,CAAC4B,GAAG,CAACd,CAAC,CAACxB,KAAK,CAAC,EAAE,IAAI,CAACA,KAAK,EAAE,IAAI,CAACC,IAAI,KAAKuB,CAAC,CAACvB,IAAI,CAAC;EACrF,CAAC;EACDK,YAAY,CAACH,SAAS,CAACiE,QAAQ,GAAG,UAAU3E,CAAC,EAAE;IAC3C,OAAOI,UAAU,CAACJ,CAAC,CAAC,CAAC8E,gBAAgB,CAAC,IAAI,CAAC;EAC/C,CAAC;EACDjE,YAAY,CAACH,SAAS,CAACkE,KAAK,GAAG/D,YAAY,CAACH,SAAS,CAACiE,QAAQ;EAC9D7D,YAAY,CAACJ,SAAS,CAACiE,QAAQ,GAAG,UAAU3E,CAAC,EAAE;IAC3C,OAAO,IAAIc,YAAY,CAAC,IAAI,CAACP,KAAK,GAAGH,UAAU,CAACJ,CAAC,CAAC,CAACO,KAAK,CAAC;EAC7D,CAAC;EACDO,YAAY,CAACJ,SAAS,CAACkE,KAAK,GAAG9D,YAAY,CAACJ,SAAS,CAACiE,QAAQ;EAE9D,SAASI,MAAMA,CAAChD,CAAC,EAAE;IACf,IAAIW,CAAC,GAAGX,CAAC,CAACT,MAAM;MAAEa,CAAC,GAAGV,WAAW,CAACiB,CAAC,GAAGA,CAAC,CAAC;MAAEL,IAAI,GAAG9C,IAAI;MAAEiE,OAAO;MAAEpB,KAAK;MAAEZ,CAAC;MAAEiC,GAAG;MAAEuB,GAAG;IAClF,KAAKxD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkB,CAAC,EAAElB,CAAC,EAAE,EAAE;MACpBiC,GAAG,GAAG1B,CAAC,CAACP,CAAC,CAAC;MACVY,KAAK,GAAG,CAAC,GAAGqB,GAAG,GAAGA,GAAG;MACrB,KAAK,IAAIE,CAAC,GAAGnC,CAAC,EAAEmC,CAAC,GAAGjB,CAAC,EAAEiB,CAAC,EAAE,EAAE;QACxBqB,GAAG,GAAGjD,CAAC,CAAC4B,CAAC,CAAC;QACVH,OAAO,GAAG,CAAC,IAAIC,GAAG,GAAGuB,GAAG,CAAC,GAAG7C,CAAC,CAACX,CAAC,GAAGmC,CAAC,CAAC,GAAGvB,KAAK;QAC5CA,KAAK,GAAGnB,IAAI,CAACC,KAAK,CAACsC,OAAO,GAAGnB,IAAI,CAAC;QAClCF,CAAC,CAACX,CAAC,GAAGmC,CAAC,CAAC,GAAGH,OAAO,GAAGpB,KAAK,GAAGC,IAAI;MACrC;MACAF,CAAC,CAACX,CAAC,GAAGkB,CAAC,CAAC,GAAGN,KAAK;IACpB;IACAf,IAAI,CAACc,CAAC,CAAC;IACP,OAAOA,CAAC;EACZ;EAEA7B,UAAU,CAACI,SAAS,CAACqE,MAAM,GAAG,YAAY;IACtC,OAAO,IAAIzE,UAAU,CAACyE,MAAM,CAAC,IAAI,CAACxE,KAAK,CAAC,EAAE,KAAK,CAAC;EACpD,CAAC;EACDM,YAAY,CAACH,SAAS,CAACqE,MAAM,GAAG,YAAY;IACxC,IAAIxE,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,IAAI,CAACA,KAAK;IACnC,IAAIQ,SAAS,CAACR,KAAK,CAAC,EAAE,OAAO,IAAIM,YAAY,CAACN,KAAK,CAAC;IACpD,OAAO,IAAID,UAAU,CAACyE,MAAM,CAACpF,YAAY,CAACsB,IAAI,CAAC4B,GAAG,CAAC,IAAI,CAACtC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;EAC5E,CAAC;EACDO,YAAY,CAACJ,SAAS,CAACqE,MAAM,GAAG,UAAU/E,CAAC,EAAE;IACzC,OAAO,IAAIc,YAAY,CAAC,IAAI,CAACP,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC;EACpD,CAAC;EAED,SAAS0E,OAAOA,CAAClD,CAAC,EAAEC,CAAC,EAAE;IACnB,IAAIe,GAAG,GAAGhB,CAAC,CAACT,MAAM;MAAE0B,GAAG,GAAGhB,CAAC,CAACV,MAAM;MAAEe,IAAI,GAAG9C,IAAI;MAAE2F,MAAM,GAAGzD,WAAW,CAACO,CAAC,CAACV,MAAM,CAAC;MAC3E6D,2BAA2B,GAAGnD,CAAC,CAACgB,GAAG,GAAG,CAAC,CAAC;MAAEoC,MAAM,GAAGnE,IAAI,CAACY,IAAI,CAACQ,IAAI,IAAI,CAAC,GAAG8C,2BAA2B,CAAC,CAAC;MACtGE,SAAS,GAAGzB,aAAa,CAAC7B,CAAC,EAAEqD,MAAM,CAAC;MAAEE,OAAO,GAAG1B,aAAa,CAAC5B,CAAC,EAAEoD,MAAM,CAAC;MAAEG,aAAa;MAAEC,KAAK;MAAEpD,KAAK;MACrGa,MAAM;MAAEzB,CAAC;MAAEkB,CAAC;MAAE+C,CAAC;IACnB,IAAIJ,SAAS,CAAC/D,MAAM,IAAIyB,GAAG,EAAEsC,SAAS,CAAC9C,IAAI,CAAC,CAAC,CAAC;IAC9C+C,OAAO,CAAC/C,IAAI,CAAC,CAAC,CAAC;IACf4C,2BAA2B,GAAGG,OAAO,CAACtC,GAAG,GAAG,CAAC,CAAC;IAC9C,KAAKwC,KAAK,GAAGzC,GAAG,GAAGC,GAAG,EAAEwC,KAAK,IAAI,CAAC,EAAEA,KAAK,EAAE,EAAE;MACzCD,aAAa,GAAGlD,IAAI,GAAG,CAAC;MACxB,IAAIgD,SAAS,CAACG,KAAK,GAAGxC,GAAG,CAAC,KAAKmC,2BAA2B,EAAE;QACxDI,aAAa,GAAGtE,IAAI,CAACC,KAAK,CAAC,CAACmE,SAAS,CAACG,KAAK,GAAGxC,GAAG,CAAC,GAAGX,IAAI,GAAGgD,SAAS,CAACG,KAAK,GAAGxC,GAAG,GAAG,CAAC,CAAC,IAAImC,2BAA2B,CAAC;MAC1H;MACA/C,KAAK,GAAG,CAAC;MACTa,MAAM,GAAG,CAAC;MACVP,CAAC,GAAG4C,OAAO,CAAChE,MAAM;MAClB,KAAKE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkB,CAAC,EAAElB,CAAC,EAAE,EAAE;QACpBY,KAAK,IAAImD,aAAa,GAAGD,OAAO,CAAC9D,CAAC,CAAC;QACnCiE,CAAC,GAAGxE,IAAI,CAACC,KAAK,CAACkB,KAAK,GAAGC,IAAI,CAAC;QAC5BY,MAAM,IAAIoC,SAAS,CAACG,KAAK,GAAGhE,CAAC,CAAC,IAAIY,KAAK,GAAGqD,CAAC,GAAGpD,IAAI,CAAC;QACnDD,KAAK,GAAGqD,CAAC;QACT,IAAIxC,MAAM,GAAG,CAAC,EAAE;UACZoC,SAAS,CAACG,KAAK,GAAGhE,CAAC,CAAC,GAAGyB,MAAM,GAAGZ,IAAI;UACpCY,MAAM,GAAG,CAAC,CAAC;QACf,CAAC,MAAM;UACHoC,SAAS,CAACG,KAAK,GAAGhE,CAAC,CAAC,GAAGyB,MAAM;UAC7BA,MAAM,GAAG,CAAC;QACd;MACJ;MACA,OAAOA,MAAM,KAAK,CAAC,EAAE;QACjBsC,aAAa,IAAI,CAAC;QAClBnD,KAAK,GAAG,CAAC;QACT,KAAKZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkB,CAAC,EAAElB,CAAC,EAAE,EAAE;UACpBY,KAAK,IAAIiD,SAAS,CAACG,KAAK,GAAGhE,CAAC,CAAC,GAAGa,IAAI,GAAGiD,OAAO,CAAC9D,CAAC,CAAC;UACjD,IAAIY,KAAK,GAAG,CAAC,EAAE;YACXiD,SAAS,CAACG,KAAK,GAAGhE,CAAC,CAAC,GAAGY,KAAK,GAAGC,IAAI;YACnCD,KAAK,GAAG,CAAC;UACb,CAAC,MAAM;YACHiD,SAAS,CAACG,KAAK,GAAGhE,CAAC,CAAC,GAAGY,KAAK;YAC5BA,KAAK,GAAG,CAAC;UACb;QACJ;QACAa,MAAM,IAAIb,KAAK;MACnB;MACA8C,MAAM,CAACM,KAAK,CAAC,GAAGD,aAAa;IACjC;IACAF,SAAS,GAAGK,WAAW,CAACL,SAAS,EAAED,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7C,OAAO,CAACjE,YAAY,CAAC+D,MAAM,CAAC,EAAE/D,YAAY,CAACkE,SAAS,CAAC,CAAC;EAC1D;EAEA,SAASM,OAAOA,CAAC5D,CAAC,EAAEC,CAAC,EAAE;IACnB,IAAIe,GAAG,GAAGhB,CAAC,CAACT,MAAM;MAAE0B,GAAG,GAAGhB,CAAC,CAACV,MAAM;MAAE4D,MAAM,GAAG,EAAE;MAAEU,IAAI,GAAG,EAAE;MAAEvD,IAAI,GAAG9C,IAAI;MAAEsG,KAAK;MAAEC,IAAI;MAAEC,KAAK;MAAEC,KAAK;MAAEC,KAAK;IACzG,OAAOlD,GAAG,EAAE;MACR6C,IAAI,CAACM,OAAO,CAACnE,CAAC,CAAC,EAAEgB,GAAG,CAAC,CAAC;MACtB1B,IAAI,CAACuE,IAAI,CAAC;MACV,IAAIrE,UAAU,CAACqE,IAAI,EAAE5D,CAAC,CAAC,GAAG,CAAC,EAAE;QACzBkD,MAAM,CAAC3C,IAAI,CAAC,CAAC,CAAC;QACd;MACJ;MACAuD,IAAI,GAAGF,IAAI,CAACtE,MAAM;MAClByE,KAAK,GAAGH,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGzD,IAAI,GAAGuD,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC;MAC9CE,KAAK,GAAGhE,CAAC,CAACgB,GAAG,GAAG,CAAC,CAAC,GAAGX,IAAI,GAAGL,CAAC,CAACgB,GAAG,GAAG,CAAC,CAAC;MACtC,IAAI8C,IAAI,GAAG9C,GAAG,EAAE;QACZ+C,KAAK,GAAG,CAACA,KAAK,GAAG,CAAC,IAAI1D,IAAI;MAC9B;MACAwD,KAAK,GAAG5E,IAAI,CAACY,IAAI,CAACkE,KAAK,GAAGC,KAAK,CAAC;MAChC,GAAG;QACCC,KAAK,GAAGrC,aAAa,CAAC5B,CAAC,EAAE6D,KAAK,CAAC;QAC/B,IAAItE,UAAU,CAAC0E,KAAK,EAAEL,IAAI,CAAC,IAAI,CAAC,EAAE;QAClCC,KAAK,EAAE;MACX,CAAC,QAAQA,KAAK;MACdX,MAAM,CAAC3C,IAAI,CAACsD,KAAK,CAAC;MAClBD,IAAI,GAAGjD,QAAQ,CAACiD,IAAI,EAAEK,KAAK,CAAC;IAChC;IACAf,MAAM,CAACiB,OAAO,CAAC,CAAC;IAChB,OAAO,CAAChF,YAAY,CAAC+D,MAAM,CAAC,EAAE/D,YAAY,CAACyE,IAAI,CAAC,CAAC;EACrD;EAEA,SAASF,WAAWA,CAACnF,KAAK,EAAE6E,MAAM,EAAE;IAChC,IAAI9D,MAAM,GAAGf,KAAK,CAACe,MAAM;MAAE8E,QAAQ,GAAG3E,WAAW,CAACH,MAAM,CAAC;MAAEe,IAAI,GAAG9C,IAAI;MAAEiC,CAAC;MAAEiE,CAAC;MAAEJ,SAAS;MAAEC,OAAO;IAChGD,SAAS,GAAG,CAAC;IACb,KAAK7D,CAAC,GAAGF,MAAM,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MAC9B8D,OAAO,GAAGD,SAAS,GAAGhD,IAAI,GAAG9B,KAAK,CAACiB,CAAC,CAAC;MACrCiE,CAAC,GAAG7D,QAAQ,CAAC0D,OAAO,GAAGF,MAAM,CAAC;MAC9BC,SAAS,GAAGC,OAAO,GAAGG,CAAC,GAAGL,MAAM;MAChCgB,QAAQ,CAAC5E,CAAC,CAAC,GAAGiE,CAAC,GAAG,CAAC;IACvB;IACA,OAAO,CAACW,QAAQ,EAAEf,SAAS,GAAG,CAAC,CAAC;EACpC;EAEA,SAASgB,SAASA,CAACC,IAAI,EAAEtG,CAAC,EAAE;IACxB,IAAIO,KAAK;MAAES,CAAC,GAAGZ,UAAU,CAACJ,CAAC,CAAC;IAC5B,IAAIH,oBAAoB,EAAE;MACtB,OAAO,CAAC,IAAIiB,YAAY,CAACwF,IAAI,CAAC/F,KAAK,GAAGS,CAAC,CAACT,KAAK,CAAC,EAAE,IAAIO,YAAY,CAACwF,IAAI,CAAC/F,KAAK,GAAGS,CAAC,CAACT,KAAK,CAAC,CAAC;IAC3F;IACA,IAAIwB,CAAC,GAAGuE,IAAI,CAAC/F,KAAK;MAAEyB,CAAC,GAAGhB,CAAC,CAACT,KAAK;IAC/B,IAAI6F,QAAQ;IACZ,IAAIpE,CAAC,KAAK,CAAC,EAAE,MAAM,IAAIuE,KAAK,CAAC,uBAAuB,CAAC;IACrD,IAAID,IAAI,CAAC7F,OAAO,EAAE;MACd,IAAIO,CAAC,CAACP,OAAO,EAAE;QACX,OAAO,CAAC,IAAII,YAAY,CAACe,QAAQ,CAACG,CAAC,GAAGC,CAAC,CAAC,CAAC,EAAE,IAAInB,YAAY,CAACkB,CAAC,GAAGC,CAAC,CAAC,CAAC;MACvE;MACA,OAAO,CAACjC,OAAO,CAAC,CAAC,CAAC,EAAEuG,IAAI,CAAC;IAC7B;IACA,IAAItF,CAAC,CAACP,OAAO,EAAE;MACX,IAAIuB,CAAC,KAAK,CAAC,EAAE,OAAO,CAACsE,IAAI,EAAEvG,OAAO,CAAC,CAAC,CAAC,CAAC;MACtC,IAAIiC,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,CAACsE,IAAI,CAAC1D,MAAM,CAAC,CAAC,EAAE7C,OAAO,CAAC,CAAC,CAAC,CAAC;MAC/C,IAAI8C,GAAG,GAAG5B,IAAI,CAAC4B,GAAG,CAACb,CAAC,CAAC;MACrB,IAAIa,GAAG,GAAGtD,IAAI,EAAE;QACZgB,KAAK,GAAGmF,WAAW,CAAC3D,CAAC,EAAEc,GAAG,CAAC;QAC3BuD,QAAQ,GAAGjF,YAAY,CAACZ,KAAK,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI8E,SAAS,GAAG9E,KAAK,CAAC,CAAC,CAAC;QACxB,IAAI+F,IAAI,CAAC9F,IAAI,EAAE6E,SAAS,GAAG,CAACA,SAAS;QACrC,IAAI,OAAOe,QAAQ,KAAK,QAAQ,EAAE;UAC9B,IAAIE,IAAI,CAAC9F,IAAI,KAAKQ,CAAC,CAACR,IAAI,EAAE4F,QAAQ,GAAG,CAACA,QAAQ;UAC9C,OAAO,CAAC,IAAIvF,YAAY,CAACuF,QAAQ,CAAC,EAAE,IAAIvF,YAAY,CAACwE,SAAS,CAAC,CAAC;QACpE;QACA,OAAO,CAAC,IAAI/E,UAAU,CAAC8F,QAAQ,EAAEE,IAAI,CAAC9F,IAAI,KAAKQ,CAAC,CAACR,IAAI,CAAC,EAAE,IAAIK,YAAY,CAACwE,SAAS,CAAC,CAAC;MACxF;MACArD,CAAC,GAAGrC,YAAY,CAACkD,GAAG,CAAC;IACzB;IACA,IAAI2D,UAAU,GAAGjF,UAAU,CAACQ,CAAC,EAAEC,CAAC,CAAC;IACjC,IAAIwE,UAAU,KAAK,CAAC,CAAC,EAAE,OAAO,CAACzG,OAAO,CAAC,CAAC,CAAC,EAAEuG,IAAI,CAAC;IAChD,IAAIE,UAAU,KAAK,CAAC,EAAE,OAAO,CAACzG,OAAO,CAACuG,IAAI,CAAC9F,IAAI,KAAKQ,CAAC,CAACR,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAET,OAAO,CAAC,CAAC,CAAC,CAAC;IACjF,IAAIgC,CAAC,CAACT,MAAM,GAAGU,CAAC,CAACV,MAAM,IAAI,GAAG,EAAEf,KAAK,GAAG0E,OAAO,CAAClD,CAAC,EAAEC,CAAC,CAAC,CAAC,KAAMzB,KAAK,GAAGoF,OAAO,CAAC5D,CAAC,EAAEC,CAAC,CAAC;IACjFoE,QAAQ,GAAG7F,KAAK,CAAC,CAAC,CAAC;IACnB,IAAIkG,KAAK,GAAGH,IAAI,CAAC9F,IAAI,KAAKQ,CAAC,CAACR,IAAI;MAAEkG,GAAG,GAAGnG,KAAK,CAAC,CAAC,CAAC;MAAEoG,KAAK,GAAGL,IAAI,CAAC9F,IAAI;IACnE,IAAI,OAAO4F,QAAQ,KAAK,QAAQ,EAAE;MAC9B,IAAIK,KAAK,EAAEL,QAAQ,GAAG,CAACA,QAAQ;MAC/BA,QAAQ,GAAG,IAAIvF,YAAY,CAACuF,QAAQ,CAAC;IACzC,CAAC,MAAMA,QAAQ,GAAG,IAAI9F,UAAU,CAAC8F,QAAQ,EAAEK,KAAK,CAAC;IACjD,IAAI,OAAOC,GAAG,KAAK,QAAQ,EAAE;MACzB,IAAIC,KAAK,EAAED,GAAG,GAAG,CAACA,GAAG;MACrBA,GAAG,GAAG,IAAI7F,YAAY,CAAC6F,GAAG,CAAC;IAC/B,CAAC,MAAMA,GAAG,GAAG,IAAIpG,UAAU,CAACoG,GAAG,EAAEC,KAAK,CAAC;IACvC,OAAO,CAACP,QAAQ,EAAEM,GAAG,CAAC;EAC1B;EAEApG,UAAU,CAACI,SAAS,CAACkG,MAAM,GAAG,UAAU5G,CAAC,EAAE;IACvC,IAAIkF,MAAM,GAAGmB,SAAS,CAAC,IAAI,EAAErG,CAAC,CAAC;IAC/B,OAAO;MAAEoG,QAAQ,EAAElB,MAAM,CAAC,CAAC,CAAC;MAAEG,SAAS,EAAEH,MAAM,CAAC,CAAC;IAAE,CAAC;EACxD,CAAC;EACDpE,YAAY,CAACJ,SAAS,CAACkG,MAAM,GAAG/F,YAAY,CAACH,SAAS,CAACkG,MAAM,GAAGtG,UAAU,CAACI,SAAS,CAACkG,MAAM;EAC3FtG,UAAU,CAACI,SAAS,CAACmG,MAAM,GAAG,UAAU7G,CAAC,EAAE;IACvC,OAAOqG,SAAS,CAAC,IAAI,EAAErG,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC;EACDc,YAAY,CAACJ,SAAS,CAACoG,IAAI,GAAGhG,YAAY,CAACJ,SAAS,CAACmG,MAAM,GAAG,UAAU7G,CAAC,EAAE;IACvE,OAAO,IAAIc,YAAY,CAAC,IAAI,CAACP,KAAK,GAAGH,UAAU,CAACJ,CAAC,CAAC,CAACO,KAAK,CAAC;EAC7D,CAAC;EACDM,YAAY,CAACH,SAAS,CAACoG,IAAI,GAAGjG,YAAY,CAACH,SAAS,CAACmG,MAAM,GAAGvG,UAAU,CAACI,SAAS,CAACoG,IAAI,GAAGxG,UAAU,CAACI,SAAS,CAACmG,MAAM;EACrHvG,UAAU,CAACI,SAAS,CAACgG,GAAG,GAAG,UAAU1G,CAAC,EAAE;IACpC,OAAOqG,SAAS,CAAC,IAAI,EAAErG,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC;EACDc,YAAY,CAACJ,SAAS,CAACgG,GAAG,GAAG5F,YAAY,CAACJ,SAAS,CAAC2E,SAAS,GAAG,UAAUrF,CAAC,EAAE;IACzE,OAAO,IAAIc,YAAY,CAAC,IAAI,CAACP,KAAK,GAAGH,UAAU,CAACJ,CAAC,CAAC,CAACO,KAAK,CAAC;EAC7D,CAAC;EACDM,YAAY,CAACH,SAAS,CAAC2E,SAAS,GAAGxE,YAAY,CAACH,SAAS,CAACgG,GAAG,GAAGpG,UAAU,CAACI,SAAS,CAAC2E,SAAS,GAAG/E,UAAU,CAACI,SAAS,CAACgG,GAAG;EACzHpG,UAAU,CAACI,SAAS,CAACqG,GAAG,GAAG,UAAU/G,CAAC,EAAE;IACpC,IAAIgB,CAAC,GAAGZ,UAAU,CAACJ,CAAC,CAAC;MAAE+B,CAAC,GAAG,IAAI,CAACxB,KAAK;MAAEyB,CAAC,GAAGhB,CAAC,CAACT,KAAK;MAAEA,KAAK;MAAEmB,CAAC;MAAEsC,CAAC;IAC/D,IAAIhC,CAAC,KAAK,CAAC,EAAE,OAAOjC,OAAO,CAAC,CAAC,CAAC;IAC9B,IAAIgC,CAAC,KAAK,CAAC,EAAE,OAAOhC,OAAO,CAAC,CAAC,CAAC;IAC9B,IAAIgC,CAAC,KAAK,CAAC,EAAE,OAAOhC,OAAO,CAAC,CAAC,CAAC;IAC9B,IAAIgC,CAAC,KAAK,CAAC,CAAC,EAAE,OAAOf,CAAC,CAACgG,MAAM,CAAC,CAAC,GAAGjH,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,CAAC;IAC1D,IAAIiB,CAAC,CAACR,IAAI,EAAE;MACR,OAAOT,OAAO,CAAC,CAAC,CAAC;IACrB;IACA,IAAI,CAACiB,CAAC,CAACP,OAAO,EAAE,MAAM,IAAI8F,KAAK,CAAC,eAAe,GAAGvF,CAAC,CAACiG,QAAQ,CAAC,CAAC,GAAG,gBAAgB,CAAC;IAClF,IAAI,IAAI,CAACxG,OAAO,EAAE;MACd,IAAIM,SAAS,CAACR,KAAK,GAAGU,IAAI,CAAC8F,GAAG,CAAChF,CAAC,EAAEC,CAAC,CAAC,CAAC,EAAE,OAAO,IAAInB,YAAY,CAACe,QAAQ,CAACrB,KAAK,CAAC,CAAC;IACnF;IACAmB,CAAC,GAAG,IAAI;IACRsC,CAAC,GAAGjE,OAAO,CAAC,CAAC,CAAC;IACd,OAAO,IAAI,EAAE;MACT,IAAIiC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;QACbgC,CAAC,GAAGA,CAAC,CAACY,KAAK,CAAClD,CAAC,CAAC;QACd,EAAEM,CAAC;MACP;MACA,IAAIA,CAAC,KAAK,CAAC,EAAE;MACbA,CAAC,IAAI,CAAC;MACNN,CAAC,GAAGA,CAAC,CAACqD,MAAM,CAAC,CAAC;IAClB;IACA,OAAOf,CAAC;EACZ,CAAC;EACDnD,YAAY,CAACH,SAAS,CAACqG,GAAG,GAAGzG,UAAU,CAACI,SAAS,CAACqG,GAAG;EACrDjG,YAAY,CAACJ,SAAS,CAACqG,GAAG,GAAG,UAAU/G,CAAC,EAAE;IACtC,IAAIgB,CAAC,GAAGZ,UAAU,CAACJ,CAAC,CAAC;IACrB,IAAI+B,CAAC,GAAG,IAAI,CAACxB,KAAK;MAAEyB,CAAC,GAAGhB,CAAC,CAACT,KAAK;IAC/B,IAAI2G,EAAE,GAAGpH,MAAM,CAAC,CAAC,CAAC;MAAEqH,EAAE,GAAGrH,MAAM,CAAC,CAAC,CAAC;MAAEsH,EAAE,GAAGtH,MAAM,CAAC,CAAC,CAAC;IAClD,IAAIkC,CAAC,KAAKkF,EAAE,EAAE,OAAOnH,OAAO,CAAC,CAAC,CAAC;IAC/B,IAAIgC,CAAC,KAAKmF,EAAE,EAAE,OAAOnH,OAAO,CAAC,CAAC,CAAC;IAC/B,IAAIgC,CAAC,KAAKoF,EAAE,EAAE,OAAOpH,OAAO,CAAC,CAAC,CAAC;IAC/B,IAAIgC,CAAC,KAAKjC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,OAAOkB,CAAC,CAACgG,MAAM,CAAC,CAAC,GAAGjH,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,CAAC;IAClE,IAAIiB,CAAC,CAACqG,UAAU,CAAC,CAAC,EAAE,OAAO,IAAIvG,YAAY,CAACoG,EAAE,CAAC;IAC/C,IAAIxF,CAAC,GAAG,IAAI;IACZ,IAAIsC,CAAC,GAAGjE,OAAO,CAAC,CAAC,CAAC;IAClB,OAAO,IAAI,EAAE;MACT,IAAI,CAACiC,CAAC,GAAGmF,EAAE,MAAMA,EAAE,EAAE;QACjBnD,CAAC,GAAGA,CAAC,CAACY,KAAK,CAAClD,CAAC,CAAC;QACd,EAAEM,CAAC;MACP;MACA,IAAIA,CAAC,KAAKkF,EAAE,EAAE;MACdlF,CAAC,IAAIoF,EAAE;MACP1F,CAAC,GAAGA,CAAC,CAACqD,MAAM,CAAC,CAAC;IAClB;IACA,OAAOf,CAAC;EACZ,CAAC;EACD1D,UAAU,CAACI,SAAS,CAAC4G,MAAM,GAAG,UAAUC,GAAG,EAAEb,GAAG,EAAE;IAC9Ca,GAAG,GAAGnH,UAAU,CAACmH,GAAG,CAAC;IACrBb,GAAG,GAAGtG,UAAU,CAACsG,GAAG,CAAC;IACrB,IAAIA,GAAG,CAACc,MAAM,CAAC,CAAC,EAAE,MAAM,IAAIjB,KAAK,CAAC,mCAAmC,CAAC;IACtE,IAAIpE,CAAC,GAAGpC,OAAO,CAAC,CAAC,CAAC;MAAEsC,IAAI,GAAG,IAAI,CAACqE,GAAG,CAACA,GAAG,CAAC;IACxC,OAAOa,GAAG,CAACE,UAAU,CAAC,CAAC,EAAE;MACrB,IAAIpF,IAAI,CAACmF,MAAM,CAAC,CAAC,EAAE,OAAOzH,OAAO,CAAC,CAAC,CAAC;MACpC,IAAIwH,GAAG,CAACG,KAAK,CAAC,CAAC,EAAEvF,CAAC,GAAGA,CAAC,CAACwC,QAAQ,CAACtC,IAAI,CAAC,CAACqE,GAAG,CAACA,GAAG,CAAC;MAC9Ca,GAAG,GAAGA,GAAG,CAACV,MAAM,CAAC,CAAC,CAAC;MACnBxE,IAAI,GAAGA,IAAI,CAAC0C,MAAM,CAAC,CAAC,CAAC2B,GAAG,CAACA,GAAG,CAAC;IACjC;IACA,OAAOvE,CAAC;EACZ,CAAC;EACDrB,YAAY,CAACJ,SAAS,CAAC4G,MAAM,GAAGzG,YAAY,CAACH,SAAS,CAAC4G,MAAM,GAAGhH,UAAU,CAACI,SAAS,CAAC4G,MAAM;EAE3F,SAAS/F,UAAUA,CAACQ,CAAC,EAAEC,CAAC,EAAE;IACtB,IAAID,CAAC,CAACT,MAAM,KAAKU,CAAC,CAACV,MAAM,EAAE;MACvB,OAAOS,CAAC,CAACT,MAAM,GAAGU,CAAC,CAACV,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;IACvC;IACA,KAAK,IAAIE,CAAC,GAAGO,CAAC,CAACT,MAAM,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpC,IAAIO,CAAC,CAACP,CAAC,CAAC,KAAKQ,CAAC,CAACR,CAAC,CAAC,EAAE,OAAOO,CAAC,CAACP,CAAC,CAAC,GAAGQ,CAAC,CAACR,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAClD;IACA,OAAO,CAAC;EACZ;EAEAlB,UAAU,CAACI,SAAS,CAACa,UAAU,GAAG,UAAUvB,CAAC,EAAE;IAC3C,IAAIgB,CAAC,GAAGZ,UAAU,CAACJ,CAAC,CAAC;MAAE+B,CAAC,GAAG,IAAI,CAACxB,KAAK;MAAEyB,CAAC,GAAGhB,CAAC,CAACT,KAAK;IAClD,IAAIS,CAAC,CAACP,OAAO,EAAE,OAAO,CAAC;IACvB,OAAOc,UAAU,CAACQ,CAAC,EAAEC,CAAC,CAAC;EAC3B,CAAC;EACDnB,YAAY,CAACH,SAAS,CAACa,UAAU,GAAG,UAAUvB,CAAC,EAAE;IAC7C,IAAIgB,CAAC,GAAGZ,UAAU,CAACJ,CAAC,CAAC;MAAE+B,CAAC,GAAGd,IAAI,CAAC4B,GAAG,CAAC,IAAI,CAACtC,KAAK,CAAC;MAAEyB,CAAC,GAAGhB,CAAC,CAACT,KAAK;IAC5D,IAAIS,CAAC,CAACP,OAAO,EAAE;MACXuB,CAAC,GAAGf,IAAI,CAAC4B,GAAG,CAACb,CAAC,CAAC;MACf,OAAOD,CAAC,KAAKC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACvC;IACA,OAAO,CAAC,CAAC;EACb,CAAC;EACDlB,YAAY,CAACJ,SAAS,CAACa,UAAU,GAAG,UAAUvB,CAAC,EAAE;IAC7C,IAAI+B,CAAC,GAAG,IAAI,CAACxB,KAAK;IAClB,IAAIyB,CAAC,GAAG5B,UAAU,CAACJ,CAAC,CAAC,CAACO,KAAK;IAC3BwB,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAGA,CAAC,GAAG,CAACA,CAAC;IACnBC,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAGA,CAAC,GAAG,CAACA,CAAC;IACnB,OAAOD,CAAC,KAAKC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACvC,CAAC;EACD1B,UAAU,CAACI,SAAS,CAACiH,OAAO,GAAG,UAAU3H,CAAC,EAAE;IACxC,IAAIA,CAAC,KAAK4H,QAAQ,EAAE;MAChB,OAAO,CAAC,CAAC;IACb;IACA,IAAI5H,CAAC,KAAK,CAAC4H,QAAQ,EAAE;MACjB,OAAO,CAAC;IACZ;IACA,IAAI5G,CAAC,GAAGZ,UAAU,CAACJ,CAAC,CAAC;MAAE+B,CAAC,GAAG,IAAI,CAACxB,KAAK;MAAEyB,CAAC,GAAGhB,CAAC,CAACT,KAAK;IAClD,IAAI,IAAI,CAACC,IAAI,KAAKQ,CAAC,CAACR,IAAI,EAAE;MACtB,OAAOQ,CAAC,CAACR,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAC1B;IACA,IAAIQ,CAAC,CAACP,OAAO,EAAE;MACX,OAAO,IAAI,CAACD,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;IAC7B;IACA,OAAOe,UAAU,CAACQ,CAAC,EAAEC,CAAC,CAAC,IAAI,IAAI,CAACxB,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EAClD,CAAC;EACDF,UAAU,CAACI,SAAS,CAACmH,SAAS,GAAGvH,UAAU,CAACI,SAAS,CAACiH,OAAO;EAC7D9G,YAAY,CAACH,SAAS,CAACiH,OAAO,GAAG,UAAU3H,CAAC,EAAE;IAC1C,IAAIA,CAAC,KAAK4H,QAAQ,EAAE;MAChB,OAAO,CAAC,CAAC;IACb;IACA,IAAI5H,CAAC,KAAK,CAAC4H,QAAQ,EAAE;MACjB,OAAO,CAAC;IACZ;IACA,IAAI5G,CAAC,GAAGZ,UAAU,CAACJ,CAAC,CAAC;MAAE+B,CAAC,GAAG,IAAI,CAACxB,KAAK;MAAEyB,CAAC,GAAGhB,CAAC,CAACT,KAAK;IAClD,IAAIS,CAAC,CAACP,OAAO,EAAE;MACX,OAAOsB,CAAC,IAAIC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACtC;IACA,IAAID,CAAC,GAAG,CAAC,KAAKf,CAAC,CAACR,IAAI,EAAE;MAClB,OAAOuB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACzB;IACA,OAAOA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACzB,CAAC;EACDlB,YAAY,CAACH,SAAS,CAACmH,SAAS,GAAGhH,YAAY,CAACH,SAAS,CAACiH,OAAO;EACjE7G,YAAY,CAACJ,SAAS,CAACiH,OAAO,GAAG,UAAU3H,CAAC,EAAE;IAC1C,IAAIA,CAAC,KAAK4H,QAAQ,EAAE;MAChB,OAAO,CAAC,CAAC;IACb;IACA,IAAI5H,CAAC,KAAK,CAAC4H,QAAQ,EAAE;MACjB,OAAO,CAAC;IACZ;IACA,IAAI7F,CAAC,GAAG,IAAI,CAACxB,KAAK;IAClB,IAAIyB,CAAC,GAAG5B,UAAU,CAACJ,CAAC,CAAC,CAACO,KAAK;IAC3B,OAAOwB,CAAC,KAAKC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACvC,CAAC;EACDlB,YAAY,CAACJ,SAAS,CAACmH,SAAS,GAAG/G,YAAY,CAACJ,SAAS,CAACiH,OAAO;EACjErH,UAAU,CAACI,SAAS,CAACoH,MAAM,GAAG,UAAU9H,CAAC,EAAE;IACvC,OAAO,IAAI,CAAC2H,OAAO,CAAC3H,CAAC,CAAC,KAAK,CAAC;EAChC,CAAC;EACDc,YAAY,CAACJ,SAAS,CAACqH,EAAE,GAAGjH,YAAY,CAACJ,SAAS,CAACoH,MAAM,GAAGjH,YAAY,CAACH,SAAS,CAACqH,EAAE,GAAGlH,YAAY,CAACH,SAAS,CAACoH,MAAM,GAAGxH,UAAU,CAACI,SAAS,CAACqH,EAAE,GAAGzH,UAAU,CAACI,SAAS,CAACoH,MAAM;EAC7KxH,UAAU,CAACI,SAAS,CAACsH,SAAS,GAAG,UAAUhI,CAAC,EAAE;IAC1C,OAAO,IAAI,CAAC2H,OAAO,CAAC3H,CAAC,CAAC,KAAK,CAAC;EAChC,CAAC;EACDc,YAAY,CAACJ,SAAS,CAACuH,GAAG,GAAGnH,YAAY,CAACJ,SAAS,CAACsH,SAAS,GAAGnH,YAAY,CAACH,SAAS,CAACuH,GAAG,GAAGpH,YAAY,CAACH,SAAS,CAACsH,SAAS,GAAG1H,UAAU,CAACI,SAAS,CAACuH,GAAG,GAAG3H,UAAU,CAACI,SAAS,CAACsH,SAAS;EACzL1H,UAAU,CAACI,SAAS,CAACwH,OAAO,GAAG,UAAUlI,CAAC,EAAE;IACxC,OAAO,IAAI,CAAC2H,OAAO,CAAC3H,CAAC,CAAC,GAAG,CAAC;EAC9B,CAAC;EACDc,YAAY,CAACJ,SAAS,CAACyH,EAAE,GAAGrH,YAAY,CAACJ,SAAS,CAACwH,OAAO,GAAGrH,YAAY,CAACH,SAAS,CAACyH,EAAE,GAAGtH,YAAY,CAACH,SAAS,CAACwH,OAAO,GAAG5H,UAAU,CAACI,SAAS,CAACyH,EAAE,GAAG7H,UAAU,CAACI,SAAS,CAACwH,OAAO;EAChL5H,UAAU,CAACI,SAAS,CAAC0H,MAAM,GAAG,UAAUpI,CAAC,EAAE;IACvC,OAAO,IAAI,CAAC2H,OAAO,CAAC3H,CAAC,CAAC,GAAG,CAAC;EAC9B,CAAC;EACDc,YAAY,CAACJ,SAAS,CAAC2H,EAAE,GAAGvH,YAAY,CAACJ,SAAS,CAAC0H,MAAM,GAAGvH,YAAY,CAACH,SAAS,CAAC2H,EAAE,GAAGxH,YAAY,CAACH,SAAS,CAAC0H,MAAM,GAAG9H,UAAU,CAACI,SAAS,CAAC2H,EAAE,GAAG/H,UAAU,CAACI,SAAS,CAAC0H,MAAM;EAC7K9H,UAAU,CAACI,SAAS,CAAC4H,eAAe,GAAG,UAAUtI,CAAC,EAAE;IAChD,OAAO,IAAI,CAAC2H,OAAO,CAAC3H,CAAC,CAAC,IAAI,CAAC;EAC/B,CAAC;EACDc,YAAY,CAACJ,SAAS,CAAC6H,GAAG,GAAGzH,YAAY,CAACJ,SAAS,CAAC4H,eAAe,GAAGzH,YAAY,CAACH,SAAS,CAAC6H,GAAG,GAAG1H,YAAY,CAACH,SAAS,CAAC4H,eAAe,GAAGhI,UAAU,CAACI,SAAS,CAAC6H,GAAG,GAAGjI,UAAU,CAACI,SAAS,CAAC4H,eAAe;EAC3MhI,UAAU,CAACI,SAAS,CAAC8H,cAAc,GAAG,UAAUxI,CAAC,EAAE;IAC/C,OAAO,IAAI,CAAC2H,OAAO,CAAC3H,CAAC,CAAC,IAAI,CAAC;EAC/B,CAAC;EACDc,YAAY,CAACJ,SAAS,CAAC+H,GAAG,GAAG3H,YAAY,CAACJ,SAAS,CAAC8H,cAAc,GAAG3H,YAAY,CAACH,SAAS,CAAC+H,GAAG,GAAG5H,YAAY,CAACH,SAAS,CAAC8H,cAAc,GAAGlI,UAAU,CAACI,SAAS,CAAC+H,GAAG,GAAGnI,UAAU,CAACI,SAAS,CAAC8H,cAAc;EACxMlI,UAAU,CAACI,SAAS,CAACsG,MAAM,GAAG,YAAY;IACtC,OAAO,CAAC,IAAI,CAACzG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;EACpC,CAAC;EACDM,YAAY,CAACH,SAAS,CAACsG,MAAM,GAAG,YAAY;IACxC,OAAO,CAAC,IAAI,CAACzG,KAAK,GAAG,CAAC,MAAM,CAAC;EACjC,CAAC;EACDO,YAAY,CAACJ,SAAS,CAACsG,MAAM,GAAG,YAAY;IACxC,OAAO,CAAC,IAAI,CAACzG,KAAK,GAAGT,MAAM,CAAC,CAAC,CAAC,MAAMA,MAAM,CAAC,CAAC,CAAC;EACjD,CAAC;EACDQ,UAAU,CAACI,SAAS,CAACgH,KAAK,GAAG,YAAY;IACrC,OAAO,CAAC,IAAI,CAACnH,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;EACpC,CAAC;EACDM,YAAY,CAACH,SAAS,CAACgH,KAAK,GAAG,YAAY;IACvC,OAAO,CAAC,IAAI,CAACnH,KAAK,GAAG,CAAC,MAAM,CAAC;EACjC,CAAC;EACDO,YAAY,CAACJ,SAAS,CAACgH,KAAK,GAAG,YAAY;IACvC,OAAO,CAAC,IAAI,CAACnH,KAAK,GAAGT,MAAM,CAAC,CAAC,CAAC,MAAMA,MAAM,CAAC,CAAC,CAAC;EACjD,CAAC;EACDQ,UAAU,CAACI,SAAS,CAAC+G,UAAU,GAAG,YAAY;IAC1C,OAAO,CAAC,IAAI,CAACjH,IAAI;EACrB,CAAC;EACDK,YAAY,CAACH,SAAS,CAAC+G,UAAU,GAAG,YAAY;IAC5C,OAAO,IAAI,CAAClH,KAAK,GAAG,CAAC;EACzB,CAAC;EACDO,YAAY,CAACJ,SAAS,CAAC+G,UAAU,GAAG5G,YAAY,CAACH,SAAS,CAAC+G,UAAU;EACrEnH,UAAU,CAACI,SAAS,CAAC2G,UAAU,GAAG,YAAY;IAC1C,OAAO,IAAI,CAAC7G,IAAI;EACpB,CAAC;EACDK,YAAY,CAACH,SAAS,CAAC2G,UAAU,GAAG,YAAY;IAC5C,OAAO,IAAI,CAAC9G,KAAK,GAAG,CAAC;EACzB,CAAC;EACDO,YAAY,CAACJ,SAAS,CAAC2G,UAAU,GAAGxG,YAAY,CAACH,SAAS,CAAC2G,UAAU;EACrE/G,UAAU,CAACI,SAAS,CAACgI,MAAM,GAAG,YAAY;IACtC,OAAO,KAAK;EAChB,CAAC;EACD7H,YAAY,CAACH,SAAS,CAACgI,MAAM,GAAG,YAAY;IACxC,OAAOzH,IAAI,CAAC4B,GAAG,CAAC,IAAI,CAACtC,KAAK,CAAC,KAAK,CAAC;EACrC,CAAC;EACDO,YAAY,CAACJ,SAAS,CAACgI,MAAM,GAAG,YAAY;IACxC,OAAO,IAAI,CAAC7F,GAAG,CAAC,CAAC,CAACtC,KAAK,KAAKT,MAAM,CAAC,CAAC,CAAC;EACzC,CAAC;EACDQ,UAAU,CAACI,SAAS,CAAC8G,MAAM,GAAG,YAAY;IACtC,OAAO,KAAK;EAChB,CAAC;EACD3G,YAAY,CAACH,SAAS,CAAC8G,MAAM,GAAG,YAAY;IACxC,OAAO,IAAI,CAACjH,KAAK,KAAK,CAAC;EAC3B,CAAC;EACDO,YAAY,CAACJ,SAAS,CAAC8G,MAAM,GAAG,YAAY;IACxC,OAAO,IAAI,CAACjH,KAAK,KAAKT,MAAM,CAAC,CAAC,CAAC;EACnC,CAAC;EACDQ,UAAU,CAACI,SAAS,CAACiI,aAAa,GAAG,UAAU3I,CAAC,EAAE;IAC9C,IAAIgB,CAAC,GAAGZ,UAAU,CAACJ,CAAC,CAAC;IACrB,IAAIgB,CAAC,CAACwG,MAAM,CAAC,CAAC,EAAE,OAAO,KAAK;IAC5B,IAAIxG,CAAC,CAAC0H,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI;IAC3B,IAAI1H,CAAC,CAACO,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,OAAO,IAAI,CAACyF,MAAM,CAAC,CAAC;IAC/C,OAAO,IAAI,CAACN,GAAG,CAAC1F,CAAC,CAAC,CAACwG,MAAM,CAAC,CAAC;EAC/B,CAAC;EACD1G,YAAY,CAACJ,SAAS,CAACiI,aAAa,GAAG9H,YAAY,CAACH,SAAS,CAACiI,aAAa,GAAGrI,UAAU,CAACI,SAAS,CAACiI,aAAa;EAEhH,SAASC,YAAYA,CAAC5I,CAAC,EAAE;IACrB,IAAIgB,CAAC,GAAGhB,CAAC,CAAC6C,GAAG,CAAC,CAAC;IACf,IAAI7B,CAAC,CAAC0H,MAAM,CAAC,CAAC,EAAE,OAAO,KAAK;IAC5B,IAAI1H,CAAC,CAAC8G,MAAM,CAAC,CAAC,CAAC,IAAI9G,CAAC,CAAC8G,MAAM,CAAC,CAAC,CAAC,IAAI9G,CAAC,CAAC8G,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI;IAC1D,IAAI9G,CAAC,CAACgG,MAAM,CAAC,CAAC,IAAIhG,CAAC,CAAC2H,aAAa,CAAC,CAAC,CAAC,IAAI3H,CAAC,CAAC2H,aAAa,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;IACxE,IAAI3H,CAAC,CAACoH,MAAM,CAAC,EAAE,CAAC,EAAE,OAAO,IAAI;EACjC;EAEA,SAASS,eAAeA,CAAC7H,CAAC,EAAEe,CAAC,EAAE;IAC3B,IAAI+G,KAAK,GAAG9H,CAAC,CAAC+H,IAAI,CAAC,CAAC;MAAE/G,CAAC,GAAG8G,KAAK;MAAE3G,CAAC,GAAG,CAAC;MAAEgC,CAAC;MAAE6E,CAAC;MAAExH,CAAC;MAAEE,CAAC;IAClD,OAAOM,CAAC,CAACgF,MAAM,CAAC,CAAC,EAAEhF,CAAC,GAAGA,CAAC,CAAC6E,MAAM,CAAC,CAAC,CAAC,EAAE1E,CAAC,EAAE;IACvC8G,IAAI,EAAC,KAAKzH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,CAAC,CAACT,MAAM,EAAEE,CAAC,EAAE,EAAE;MAChC,IAAIR,CAAC,CAACoH,MAAM,CAACrG,CAAC,CAACP,CAAC,CAAC,CAAC,EAAE;MACpBE,CAAC,GAAGrC,MAAM,CAAC0C,CAAC,CAACP,CAAC,CAAC,CAAC,CAAC8F,MAAM,CAACtF,CAAC,EAAEhB,CAAC,CAAC;MAC7B,IAAIU,CAAC,CAACgH,MAAM,CAAC,CAAC,IAAIhH,CAAC,CAACoG,MAAM,CAACgB,KAAK,CAAC,EAAE;MACnC,KAAK3E,CAAC,GAAGhC,CAAC,GAAG,CAAC,EAAEgC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACzBzC,CAAC,GAAGA,CAAC,CAACqD,MAAM,CAAC,CAAC,CAAC2B,GAAG,CAAC1F,CAAC,CAAC;QACrB,IAAIU,CAAC,CAACgH,MAAM,CAAC,CAAC,EAAE,OAAO,KAAK;QAC5B,IAAIhH,CAAC,CAACoG,MAAM,CAACgB,KAAK,CAAC,EAAE,SAASG,IAAI;MACtC;MACA,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;EAEA3I,UAAU,CAACI,SAAS,CAACwI,OAAO,GAAG,UAAUC,MAAM,EAAE;IAC7C,IAAID,OAAO,GAAGN,YAAY,CAAC,IAAI,CAAC;IAChC,IAAIM,OAAO,KAAK5J,SAAS,EAAE,OAAO4J,OAAO;IACzC,IAAIlI,CAAC,GAAG,IAAI,CAAC6B,GAAG,CAAC,CAAC;IAClB,IAAIuG,IAAI,GAAGpI,CAAC,CAACqI,SAAS,CAAC,CAAC;IACxB,IAAID,IAAI,IAAI,EAAE,EAAE,OAAOP,eAAe,CAAC7H,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IACvF,IAAIsI,IAAI,GAAGrI,IAAI,CAACsI,GAAG,CAAC,CAAC,CAAC,GAAGH,IAAI,CAACI,UAAU,CAAC,CAAC;IAC1C,IAAIR,CAAC,GAAG/H,IAAI,CAACY,IAAI,CAACsH,MAAM,KAAK,IAAI,GAAG,CAAC,GAAGlI,IAAI,CAAC8F,GAAG,CAACuC,IAAI,EAAE,CAAC,CAAC,GAAGA,IAAI,CAAC;IACjE,KAAK,IAAIvH,CAAC,GAAG,EAAE,EAAEP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwH,CAAC,EAAExH,CAAC,EAAE,EAAE;MAChCO,CAAC,CAACQ,IAAI,CAAClD,MAAM,CAACmC,CAAC,GAAG,CAAC,CAAC,CAAC;IACzB;IACA,OAAOqH,eAAe,CAAC7H,CAAC,EAAEe,CAAC,CAAC;EAChC,CAAC;EACDjB,YAAY,CAACJ,SAAS,CAACwI,OAAO,GAAGrI,YAAY,CAACH,SAAS,CAACwI,OAAO,GAAG5I,UAAU,CAACI,SAAS,CAACwI,OAAO;EAC9F5I,UAAU,CAACI,SAAS,CAAC+I,eAAe,GAAG,UAAUC,UAAU,EAAE;IACzD,IAAIR,OAAO,GAAGN,YAAY,CAAC,IAAI,CAAC;IAChC,IAAIM,OAAO,KAAK5J,SAAS,EAAE,OAAO4J,OAAO;IACzC,IAAIlI,CAAC,GAAG,IAAI,CAAC6B,GAAG,CAAC,CAAC;IAClB,IAAImG,CAAC,GAAGU,UAAU,KAAKpK,SAAS,GAAG,CAAC,GAAGoK,UAAU;IACjD,KAAK,IAAI3H,CAAC,GAAG,EAAE,EAAEP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwH,CAAC,EAAExH,CAAC,EAAE,EAAE;MAChCO,CAAC,CAACQ,IAAI,CAAClD,MAAM,CAACsK,WAAW,CAAC,CAAC,EAAE3I,CAAC,CAACqC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C;IACA,OAAOwF,eAAe,CAAC7H,CAAC,EAAEe,CAAC,CAAC;EAChC,CAAC;EACDjB,YAAY,CAACJ,SAAS,CAAC+I,eAAe,GAAG5I,YAAY,CAACH,SAAS,CAAC+I,eAAe,GAAGnJ,UAAU,CAACI,SAAS,CAAC+I,eAAe;EACtHnJ,UAAU,CAACI,SAAS,CAACkJ,MAAM,GAAG,UAAU5I,CAAC,EAAE;IACvC,IAAIgI,CAAC,GAAG3J,MAAM,CAACwK,IAAI;MAAEC,IAAI,GAAGzK,MAAM,CAAC0K,GAAG;MAAE5H,CAAC,GAAG/B,UAAU,CAACY,CAAC,CAAC;MAAEgJ,IAAI,GAAG,IAAI,CAACnH,GAAG,CAAC,CAAC;MAAE4C,CAAC;MAAEwE,KAAK;MAAEC,KAAK;IAC7F,OAAO,CAACF,IAAI,CAACxC,MAAM,CAAC,CAAC,EAAE;MACnB/B,CAAC,GAAGtD,CAAC,CAAC0E,MAAM,CAACmD,IAAI,CAAC;MAClBC,KAAK,GAAGjB,CAAC;MACTkB,KAAK,GAAG/H,CAAC;MACT6G,CAAC,GAAGc,IAAI;MACR3H,CAAC,GAAG6H,IAAI;MACRF,IAAI,GAAGG,KAAK,CAACtH,QAAQ,CAAC8C,CAAC,CAACd,QAAQ,CAACmF,IAAI,CAAC,CAAC;MACvCE,IAAI,GAAGE,KAAK,CAACvH,QAAQ,CAAC8C,CAAC,CAACd,QAAQ,CAACqF,IAAI,CAAC,CAAC;IAC3C;IACA,IAAI,CAAC7H,CAAC,CAACuG,MAAM,CAAC,CAAC,EAAE,MAAM,IAAInC,KAAK,CAAC,IAAI,CAACU,QAAQ,CAAC,CAAC,GAAG,OAAO,GAAGjG,CAAC,CAACiG,QAAQ,CAAC,CAAC,GAAG,mBAAmB,CAAC;IAChG,IAAI+B,CAAC,CAACrB,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;MACrBqB,CAAC,GAAGA,CAAC,CAAClH,GAAG,CAACd,CAAC,CAAC;IAChB;IACA,IAAI,IAAI,CAACqG,UAAU,CAAC,CAAC,EAAE;MACnB,OAAO2B,CAAC,CAACpG,MAAM,CAAC,CAAC;IACrB;IACA,OAAOoG,CAAC;EACZ,CAAC;EACDlI,YAAY,CAACJ,SAAS,CAACkJ,MAAM,GAAG/I,YAAY,CAACH,SAAS,CAACkJ,MAAM,GAAGtJ,UAAU,CAACI,SAAS,CAACkJ,MAAM;EAC3FtJ,UAAU,CAACI,SAAS,CAACuI,IAAI,GAAG,YAAY;IACpC,IAAI1I,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI,IAAI,CAACC,IAAI,EAAE;MACX,OAAO4C,aAAa,CAAC7C,KAAK,EAAE,CAAC,EAAE,IAAI,CAACC,IAAI,CAAC;IAC7C;IACA,OAAO,IAAIF,UAAU,CAACmC,QAAQ,CAAClC,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,CAACC,IAAI,CAAC;EACxD,CAAC;EACDK,YAAY,CAACH,SAAS,CAACuI,IAAI,GAAG,YAAY;IACtC,IAAI1I,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIA,KAAK,GAAG,CAAC,GAAGd,OAAO,EAAE,OAAO,IAAIoB,YAAY,CAACN,KAAK,GAAG,CAAC,CAAC;IAC3D,OAAO,IAAID,UAAU,CAACZ,WAAW,EAAE,KAAK,CAAC;EAC7C,CAAC;EACDoB,YAAY,CAACJ,SAAS,CAACuI,IAAI,GAAG,YAAY;IACtC,OAAO,IAAInI,YAAY,CAAC,IAAI,CAACP,KAAK,GAAGT,MAAM,CAAC,CAAC,CAAC,CAAC;EACnD,CAAC;EACDQ,UAAU,CAACI,SAAS,CAACqI,IAAI,GAAG,YAAY;IACpC,IAAIxI,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI,IAAI,CAACC,IAAI,EAAE;MACX,OAAO,IAAIF,UAAU,CAACmC,QAAQ,CAAClC,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;IACnD;IACA,OAAO6C,aAAa,CAAC7C,KAAK,EAAE,CAAC,EAAE,IAAI,CAACC,IAAI,CAAC;EAC7C,CAAC;EACDK,YAAY,CAACH,SAAS,CAACqI,IAAI,GAAG,YAAY;IACtC,IAAIxI,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIA,KAAK,GAAG,CAAC,GAAG,CAACd,OAAO,EAAE,OAAO,IAAIoB,YAAY,CAACN,KAAK,GAAG,CAAC,CAAC;IAC5D,OAAO,IAAID,UAAU,CAACZ,WAAW,EAAE,IAAI,CAAC;EAC5C,CAAC;EACDoB,YAAY,CAACJ,SAAS,CAACqI,IAAI,GAAG,YAAY;IACtC,OAAO,IAAIjI,YAAY,CAAC,IAAI,CAACP,KAAK,GAAGT,MAAM,CAAC,CAAC,CAAC,CAAC;EACnD,CAAC;EACD,IAAIqK,WAAW,GAAG,CAAC,CAAC,CAAC;EACrB,OAAO,CAAC,GAAGA,WAAW,CAACA,WAAW,CAAC7I,MAAM,GAAG,CAAC,CAAC,IAAI/B,IAAI,EAAE4K,WAAW,CAAC5H,IAAI,CAAC,CAAC,GAAG4H,WAAW,CAACA,WAAW,CAAC7I,MAAM,GAAG,CAAC,CAAC,CAAC;EACjH,IAAI8I,aAAa,GAAGD,WAAW,CAAC7I,MAAM;IAAE+I,aAAa,GAAGF,WAAW,CAACC,aAAa,GAAG,CAAC,CAAC;EAEtF,SAASE,aAAaA,CAACtJ,CAAC,EAAE;IACtB,OAAOC,IAAI,CAAC4B,GAAG,CAAC7B,CAAC,CAAC,IAAIzB,IAAI;EAC9B;EAEAe,UAAU,CAACI,SAAS,CAACmD,SAAS,GAAG,UAAU7D,CAAC,EAAE;IAC1C,IAAIgB,CAAC,GAAGZ,UAAU,CAACJ,CAAC,CAAC,CAACwJ,UAAU,CAAC,CAAC;IAClC,IAAI,CAACc,aAAa,CAACtJ,CAAC,CAAC,EAAE;MACnB,MAAM,IAAIuF,KAAK,CAACgE,MAAM,CAACvJ,CAAC,CAAC,GAAG,6BAA6B,CAAC;IAC9D;IACA,IAAIA,CAAC,GAAG,CAAC,EAAE,OAAO,IAAI,CAACwJ,UAAU,CAAC,CAACxJ,CAAC,CAAC;IACrC,IAAIkE,MAAM,GAAG,IAAI;IACjB,IAAIA,MAAM,CAACsC,MAAM,CAAC,CAAC,EAAE,OAAOtC,MAAM;IAClC,OAAOlE,CAAC,IAAIoJ,aAAa,EAAE;MACvBlF,MAAM,GAAGA,MAAM,CAACP,QAAQ,CAAC0F,aAAa,CAAC;MACvCrJ,CAAC,IAAIoJ,aAAa,GAAG,CAAC;IAC1B;IACA,OAAOlF,MAAM,CAACP,QAAQ,CAACwF,WAAW,CAACnJ,CAAC,CAAC,CAAC;EAC1C,CAAC;EACDF,YAAY,CAACJ,SAAS,CAACmD,SAAS,GAAGhD,YAAY,CAACH,SAAS,CAACmD,SAAS,GAAGvD,UAAU,CAACI,SAAS,CAACmD,SAAS;EACpGvD,UAAU,CAACI,SAAS,CAAC8J,UAAU,GAAG,UAAUxK,CAAC,EAAE;IAC3C,IAAIyK,MAAM;IACV,IAAIzJ,CAAC,GAAGZ,UAAU,CAACJ,CAAC,CAAC,CAACwJ,UAAU,CAAC,CAAC;IAClC,IAAI,CAACc,aAAa,CAACtJ,CAAC,CAAC,EAAE;MACnB,MAAM,IAAIuF,KAAK,CAACgE,MAAM,CAACvJ,CAAC,CAAC,GAAG,6BAA6B,CAAC;IAC9D;IACA,IAAIA,CAAC,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC6C,SAAS,CAAC,CAAC7C,CAAC,CAAC;IACpC,IAAIkE,MAAM,GAAG,IAAI;IACjB,OAAOlE,CAAC,IAAIoJ,aAAa,EAAE;MACvB,IAAIlF,MAAM,CAACsC,MAAM,CAAC,CAAC,IAAItC,MAAM,CAACmC,UAAU,CAAC,CAAC,IAAInC,MAAM,CAACwD,MAAM,CAAC,CAAC,EAAE,OAAOxD,MAAM;MAC5EuF,MAAM,GAAGpE,SAAS,CAACnB,MAAM,EAAEmF,aAAa,CAAC;MACzCnF,MAAM,GAAGuF,MAAM,CAAC,CAAC,CAAC,CAACpD,UAAU,CAAC,CAAC,GAAGoD,MAAM,CAAC,CAAC,CAAC,CAAC1B,IAAI,CAAC,CAAC,GAAG0B,MAAM,CAAC,CAAC,CAAC;MAC9DzJ,CAAC,IAAIoJ,aAAa,GAAG,CAAC;IAC1B;IACAK,MAAM,GAAGpE,SAAS,CAACnB,MAAM,EAAEiF,WAAW,CAACnJ,CAAC,CAAC,CAAC;IAC1C,OAAOyJ,MAAM,CAAC,CAAC,CAAC,CAACpD,UAAU,CAAC,CAAC,GAAGoD,MAAM,CAAC,CAAC,CAAC,CAAC1B,IAAI,CAAC,CAAC,GAAG0B,MAAM,CAAC,CAAC,CAAC;EAChE,CAAC;EACD3J,YAAY,CAACJ,SAAS,CAAC8J,UAAU,GAAG3J,YAAY,CAACH,SAAS,CAAC8J,UAAU,GAAGlK,UAAU,CAACI,SAAS,CAAC8J,UAAU;EAEvG,SAASE,OAAOA,CAAChJ,CAAC,EAAEsC,CAAC,EAAE2G,EAAE,EAAE;IACvB3G,CAAC,GAAG5D,UAAU,CAAC4D,CAAC,CAAC;IACjB,IAAI4G,KAAK,GAAGlJ,CAAC,CAAC2F,UAAU,CAAC,CAAC;MAAEwD,KAAK,GAAG7G,CAAC,CAACqD,UAAU,CAAC,CAAC;IAClD,IAAIyD,IAAI,GAAGF,KAAK,GAAGlJ,CAAC,CAACqJ,GAAG,CAAC,CAAC,GAAGrJ,CAAC;MAAEsJ,IAAI,GAAGH,KAAK,GAAG7G,CAAC,CAAC+G,GAAG,CAAC,CAAC,GAAG/G,CAAC;IAC1D,IAAIiH,MAAM,GAAG,CAAC;MAAEC,MAAM,GAAG,CAAC;IAC1B,IAAIC,OAAO,GAAG,IAAI;MAAEC,OAAO,GAAG,IAAI;IAClC,IAAIlG,MAAM,GAAG,EAAE;IACf,OAAO,CAAC4F,IAAI,CAACtD,MAAM,CAAC,CAAC,IAAI,CAACwD,IAAI,CAACxD,MAAM,CAAC,CAAC,EAAE;MACrC2D,OAAO,GAAG9E,SAAS,CAACyE,IAAI,EAAET,aAAa,CAAC;MACxCY,MAAM,GAAGE,OAAO,CAAC,CAAC,CAAC,CAAC3B,UAAU,CAAC,CAAC;MAChC,IAAIoB,KAAK,EAAE;QACPK,MAAM,GAAGZ,aAAa,GAAG,CAAC,GAAGY,MAAM;MACvC;MACAG,OAAO,GAAG/E,SAAS,CAAC2E,IAAI,EAAEX,aAAa,CAAC;MACxCa,MAAM,GAAGE,OAAO,CAAC,CAAC,CAAC,CAAC5B,UAAU,CAAC,CAAC;MAChC,IAAIqB,KAAK,EAAE;QACPK,MAAM,GAAGb,aAAa,GAAG,CAAC,GAAGa,MAAM;MACvC;MACAJ,IAAI,GAAGK,OAAO,CAAC,CAAC,CAAC;MACjBH,IAAI,GAAGI,OAAO,CAAC,CAAC,CAAC;MACjBlG,MAAM,CAAC3C,IAAI,CAACoI,EAAE,CAACM,MAAM,EAAEC,MAAM,CAAC,CAAC;IACnC;IACA,IAAI5I,GAAG,GAAGqI,EAAE,CAACC,KAAK,GAAG,CAAC,GAAG,CAAC,EAAEC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAGxL,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;IACzE,KAAK,IAAImC,CAAC,GAAG0D,MAAM,CAAC5D,MAAM,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MAC5Cc,GAAG,GAAGA,GAAG,CAACqC,QAAQ,CAAC0F,aAAa,CAAC,CAACvI,GAAG,CAACzC,MAAM,CAAC6F,MAAM,CAAC1D,CAAC,CAAC,CAAC,CAAC;IAC5D;IACA,OAAOc,GAAG;EACd;EAEAhC,UAAU,CAACI,SAAS,CAACqK,GAAG,GAAG,YAAY;IACnC,OAAO,IAAI,CAACnI,MAAM,CAAC,CAAC,CAACmG,IAAI,CAAC,CAAC;EAC/B,CAAC;EACDjI,YAAY,CAACJ,SAAS,CAACqK,GAAG,GAAGlK,YAAY,CAACH,SAAS,CAACqK,GAAG,GAAGzK,UAAU,CAACI,SAAS,CAACqK,GAAG;EAClFzK,UAAU,CAACI,SAAS,CAAC2K,GAAG,GAAG,UAAUrK,CAAC,EAAE;IACpC,OAAO0J,OAAO,CAAC,IAAI,EAAE1J,CAAC,EAAE,UAAUe,CAAC,EAAEC,CAAC,EAAE;MACpC,OAAOD,CAAC,GAAGC,CAAC;IAChB,CAAC,CAAC;EACN,CAAC;EACDlB,YAAY,CAACJ,SAAS,CAAC2K,GAAG,GAAGxK,YAAY,CAACH,SAAS,CAAC2K,GAAG,GAAG/K,UAAU,CAACI,SAAS,CAAC2K,GAAG;EAClF/K,UAAU,CAACI,SAAS,CAAC4K,EAAE,GAAG,UAAUtK,CAAC,EAAE;IACnC,OAAO0J,OAAO,CAAC,IAAI,EAAE1J,CAAC,EAAE,UAAUe,CAAC,EAAEC,CAAC,EAAE;MACpC,OAAOD,CAAC,GAAGC,CAAC;IAChB,CAAC,CAAC;EACN,CAAC;EACDlB,YAAY,CAACJ,SAAS,CAAC4K,EAAE,GAAGzK,YAAY,CAACH,SAAS,CAAC4K,EAAE,GAAGhL,UAAU,CAACI,SAAS,CAAC4K,EAAE;EAC/EhL,UAAU,CAACI,SAAS,CAAC6K,GAAG,GAAG,UAAUvK,CAAC,EAAE;IACpC,OAAO0J,OAAO,CAAC,IAAI,EAAE1J,CAAC,EAAE,UAAUe,CAAC,EAAEC,CAAC,EAAE;MACpC,OAAOD,CAAC,GAAGC,CAAC;IAChB,CAAC,CAAC;EACN,CAAC;EACDlB,YAAY,CAACJ,SAAS,CAAC6K,GAAG,GAAG1K,YAAY,CAACH,SAAS,CAAC6K,GAAG,GAAGjL,UAAU,CAACI,SAAS,CAAC6K,GAAG;EAClF,IAAIC,SAAS,GAAG,CAAC,IAAI,EAAE;IAAEC,UAAU,GAAG,CAAClM,IAAI,GAAG,CAACA,IAAI,KAAKA,IAAI,GAAG,CAACA,IAAI,CAAC,GAAGiM,SAAS;EAEjF,SAASE,QAAQA,CAAC1K,CAAC,EAAE;IACjB,IAAIhB,CAAC,GAAGgB,CAAC,CAACT,KAAK;MACXmB,CAAC,GAAG,OAAO1B,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAGwL,SAAS,GAAG,OAAOxL,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAGF,MAAM,CAAC0L,SAAS,CAAC,GAAGxL,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGT,IAAI,GAAGkM,UAAU;IAC/H,OAAO/J,CAAC,GAAG,CAACA,CAAC;EACjB;EAEA,SAASiK,gBAAgBA,CAACpL,KAAK,EAAE8B,IAAI,EAAE;IACnC,IAAIA,IAAI,CAACwF,SAAS,CAACtH,KAAK,CAAC,IAAI,CAAC,EAAE;MAC5B,IAAIqL,GAAG,GAAGD,gBAAgB,CAACpL,KAAK,EAAE8B,IAAI,CAAC0C,MAAM,CAAC1C,IAAI,CAAC,CAAC;MACpD,IAAIwJ,CAAC,GAAGD,GAAG,CAACC,CAAC;MACb,IAAIC,CAAC,GAAGF,GAAG,CAACE,CAAC;MACb,IAAI9C,CAAC,GAAG6C,CAAC,CAAClH,QAAQ,CAACtC,IAAI,CAAC;MACxB,OAAO2G,CAAC,CAACnB,SAAS,CAACtH,KAAK,CAAC,IAAI,CAAC,GAAG;QAAEsL,CAAC,EAAE7C,CAAC;QAAE8C,CAAC,EAAEA,CAAC,GAAG,CAAC,GAAG;MAAE,CAAC,GAAG;QAAED,CAAC,EAAEA,CAAC;QAAEC,CAAC,EAAEA,CAAC,GAAG;MAAE,CAAC;IAChF;IACA,OAAO;MAAED,CAAC,EAAExM,MAAM,CAAC,CAAC,CAAC;MAAEyM,CAAC,EAAE;IAAE,CAAC;EACjC;EAEAxL,UAAU,CAACI,SAAS,CAAC2I,SAAS,GAAG,YAAY;IACzC,IAAIrI,CAAC,GAAG,IAAI;IACZ,IAAIA,CAAC,CAAC6G,SAAS,CAACxI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MAC5B2B,CAAC,GAAGA,CAAC,CAAC4B,MAAM,CAAC,CAAC,CAACD,QAAQ,CAACtD,MAAM,CAAC,CAAC,CAAC,CAAC;IACtC;IACA,IAAI2B,CAAC,CAAC6G,SAAS,CAACxI,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;MAC9B,OAAOA,MAAM,CAAC,CAAC,CAAC;IACpB;IACA,OAAOA,MAAM,CAACsM,gBAAgB,CAAC3K,CAAC,EAAE3B,MAAM,CAAC,CAAC,CAAC,CAAC,CAACyM,CAAC,CAAC,CAAChK,GAAG,CAACzC,MAAM,CAAC,CAAC,CAAC,CAAC;EAClE,CAAC;EACDyB,YAAY,CAACJ,SAAS,CAAC2I,SAAS,GAAGxI,YAAY,CAACH,SAAS,CAAC2I,SAAS,GAAG/I,UAAU,CAACI,SAAS,CAAC2I,SAAS;EAEpG,SAASpF,GAAGA,CAAClC,CAAC,EAAEC,CAAC,EAAE;IACfD,CAAC,GAAG3B,UAAU,CAAC2B,CAAC,CAAC;IACjBC,CAAC,GAAG5B,UAAU,CAAC4B,CAAC,CAAC;IACjB,OAAOD,CAAC,CAACmG,OAAO,CAAClG,CAAC,CAAC,GAAGD,CAAC,GAAGC,CAAC;EAC/B;EAEA,SAAS+J,GAAGA,CAAChK,CAAC,EAAEC,CAAC,EAAE;IACfD,CAAC,GAAG3B,UAAU,CAAC2B,CAAC,CAAC;IACjBC,CAAC,GAAG5B,UAAU,CAAC4B,CAAC,CAAC;IACjB,OAAOD,CAAC,CAACqG,MAAM,CAACpG,CAAC,CAAC,GAAGD,CAAC,GAAGC,CAAC;EAC9B;EAEA,SAASgK,GAAGA,CAACjK,CAAC,EAAEC,CAAC,EAAE;IACfD,CAAC,GAAG3B,UAAU,CAAC2B,CAAC,CAAC,CAACc,GAAG,CAAC,CAAC;IACvBb,CAAC,GAAG5B,UAAU,CAAC4B,CAAC,CAAC,CAACa,GAAG,CAAC,CAAC;IACvB,IAAId,CAAC,CAAC+F,MAAM,CAAC9F,CAAC,CAAC,EAAE,OAAOD,CAAC;IACzB,IAAIA,CAAC,CAACyF,MAAM,CAAC,CAAC,EAAE,OAAOxF,CAAC;IACxB,IAAIA,CAAC,CAACwF,MAAM,CAAC,CAAC,EAAE,OAAOzF,CAAC;IACxB,IAAIqC,CAAC,GAAGrE,OAAO,CAAC,CAAC,CAAC;MAAEoE,CAAC;MAAE6E,CAAC;IACxB,OAAOjH,CAAC,CAACiF,MAAM,CAAC,CAAC,IAAIhF,CAAC,CAACgF,MAAM,CAAC,CAAC,EAAE;MAC7B7C,CAAC,GAAG4H,GAAG,CAACL,QAAQ,CAAC3J,CAAC,CAAC,EAAE2J,QAAQ,CAAC1J,CAAC,CAAC,CAAC;MACjCD,CAAC,GAAGA,CAAC,CAAC8E,MAAM,CAAC1C,CAAC,CAAC;MACfnC,CAAC,GAAGA,CAAC,CAAC6E,MAAM,CAAC1C,CAAC,CAAC;MACfC,CAAC,GAAGA,CAAC,CAACO,QAAQ,CAACR,CAAC,CAAC;IACrB;IACA,OAAOpC,CAAC,CAACiF,MAAM,CAAC,CAAC,EAAE;MACfjF,CAAC,GAAGA,CAAC,CAAC8E,MAAM,CAAC6E,QAAQ,CAAC3J,CAAC,CAAC,CAAC;IAC7B;IACA,GAAG;MACC,OAAOC,CAAC,CAACgF,MAAM,CAAC,CAAC,EAAE;QACfhF,CAAC,GAAGA,CAAC,CAAC6E,MAAM,CAAC6E,QAAQ,CAAC1J,CAAC,CAAC,CAAC;MAC7B;MACA,IAAID,CAAC,CAACmG,OAAO,CAAClG,CAAC,CAAC,EAAE;QACdgH,CAAC,GAAGhH,CAAC;QACLA,CAAC,GAAGD,CAAC;QACLA,CAAC,GAAGiH,CAAC;MACT;MACAhH,CAAC,GAAGA,CAAC,CAACW,QAAQ,CAACZ,CAAC,CAAC;IACrB,CAAC,QAAQ,CAACC,CAAC,CAACwF,MAAM,CAAC,CAAC;IACpB,OAAOpD,CAAC,CAACsE,MAAM,CAAC,CAAC,GAAG3G,CAAC,GAAGA,CAAC,CAAC4C,QAAQ,CAACP,CAAC,CAAC;EACzC;EAEA,SAAS6H,GAAGA,CAAClK,CAAC,EAAEC,CAAC,EAAE;IACfD,CAAC,GAAG3B,UAAU,CAAC2B,CAAC,CAAC,CAACc,GAAG,CAAC,CAAC;IACvBb,CAAC,GAAG5B,UAAU,CAAC4B,CAAC,CAAC,CAACa,GAAG,CAAC,CAAC;IACvB,OAAOd,CAAC,CAAC8E,MAAM,CAACmF,GAAG,CAACjK,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC2C,QAAQ,CAAC3C,CAAC,CAAC;EAC1C;EAEA,SAAS2H,WAAWA,CAAC5H,CAAC,EAAEC,CAAC,EAAE;IACvBD,CAAC,GAAG3B,UAAU,CAAC2B,CAAC,CAAC;IACjBC,CAAC,GAAG5B,UAAU,CAAC4B,CAAC,CAAC;IACjB,IAAIkK,GAAG,GAAGH,GAAG,CAAChK,CAAC,EAAEC,CAAC,CAAC;MAAEmK,IAAI,GAAGlI,GAAG,CAAClC,CAAC,EAAEC,CAAC,CAAC;IACrC,IAAIoK,KAAK,GAAGD,IAAI,CAACxJ,QAAQ,CAACuJ,GAAG,CAAC,CAACpK,GAAG,CAAC,CAAC,CAAC;IACrC,IAAIsK,KAAK,CAAC3L,OAAO,EAAE,OAAOyL,GAAG,CAACpK,GAAG,CAACb,IAAI,CAACC,KAAK,CAACD,IAAI,CAACoL,MAAM,CAAC,CAAC,GAAGD,KAAK,CAAC,CAAC;IACpE,IAAIE,MAAM,GAAGC,MAAM,CAACH,KAAK,EAAE7M,IAAI,CAAC,CAACgB,KAAK;IACtC,IAAI2E,MAAM,GAAG,EAAE;MAAEsH,UAAU,GAAG,IAAI;IAClC,KAAK,IAAIhL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8K,MAAM,CAAChL,MAAM,EAAEE,CAAC,EAAE,EAAE;MACpC,IAAIiL,GAAG,GAAGD,UAAU,GAAGF,MAAM,CAAC9K,CAAC,CAAC,GAAGjC,IAAI;MACvC,IAAImN,KAAK,GAAG9K,QAAQ,CAACX,IAAI,CAACoL,MAAM,CAAC,CAAC,GAAGI,GAAG,CAAC;MACzCvH,MAAM,CAAC3C,IAAI,CAACmK,KAAK,CAAC;MAClB,IAAIA,KAAK,GAAGD,GAAG,EAAED,UAAU,GAAG,KAAK;IACvC;IACA,OAAON,GAAG,CAACpK,GAAG,CAAC/B,OAAO,CAAC4M,SAAS,CAACzH,MAAM,EAAE3F,IAAI,EAAE,KAAK,CAAC,CAAC;EAC1D;EAEA,IAAIc,SAAS,GAAG,SAAAA,CAAUuM,IAAI,EAAEvK,IAAI,EAAEnC,QAAQ,EAAEC,aAAa,EAAE;IAC3DD,QAAQ,GAAGA,QAAQ,IAAIN,gBAAgB;IACvCgN,IAAI,GAAGrC,MAAM,CAACqC,IAAI,CAAC;IACnB,IAAI,CAACzM,aAAa,EAAE;MAChByM,IAAI,GAAGA,IAAI,CAACC,WAAW,CAAC,CAAC;MACzB3M,QAAQ,GAAGA,QAAQ,CAAC2M,WAAW,CAAC,CAAC;IACrC;IACA,IAAIvL,MAAM,GAAGsL,IAAI,CAACtL,MAAM;IACxB,IAAIE,CAAC;IACL,IAAIsL,OAAO,GAAG7L,IAAI,CAAC4B,GAAG,CAACR,IAAI,CAAC;IAC5B,IAAI0K,cAAc,GAAG,CAAC,CAAC;IACvB,KAAKvL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,QAAQ,CAACoB,MAAM,EAAEE,CAAC,EAAE,EAAE;MAClCuL,cAAc,CAAC7M,QAAQ,CAACsB,CAAC,CAAC,CAAC,GAAGA,CAAC;IACnC;IACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,EAAEE,CAAC,EAAE,EAAE;MACzB,IAAI4C,CAAC,GAAGwI,IAAI,CAACpL,CAAC,CAAC;MACf,IAAI4C,CAAC,KAAK,GAAG,EAAE;MACf,IAAIA,CAAC,IAAI2I,cAAc,EAAE;QACrB,IAAIA,cAAc,CAAC3I,CAAC,CAAC,IAAI0I,OAAO,EAAE;UAC9B,IAAI1I,CAAC,KAAK,GAAG,IAAI0I,OAAO,KAAK,CAAC,EAAE;UAChC,MAAM,IAAIvG,KAAK,CAACnC,CAAC,GAAG,gCAAgC,GAAG/B,IAAI,GAAG,GAAG,CAAC;QACtE;MACJ;IACJ;IACAA,IAAI,GAAGjC,UAAU,CAACiC,IAAI,CAAC;IACvB,IAAIiK,MAAM,GAAG,EAAE;IACf,IAAIjF,UAAU,GAAGuF,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG;IAChC,KAAKpL,CAAC,GAAG6F,UAAU,GAAG,CAAC,GAAG,CAAC,EAAE7F,CAAC,GAAGoL,IAAI,CAACtL,MAAM,EAAEE,CAAC,EAAE,EAAE;MAC/C,IAAI4C,CAAC,GAAGwI,IAAI,CAACpL,CAAC,CAAC;MACf,IAAI4C,CAAC,IAAI2I,cAAc,EAAET,MAAM,CAAC/J,IAAI,CAACnC,UAAU,CAAC2M,cAAc,CAAC3I,CAAC,CAAC,CAAC,CAAC,CAAC,KAAM,IAAIA,CAAC,KAAK,GAAG,EAAE;QACrF,IAAI4I,KAAK,GAAGxL,CAAC;QACb,GAAG;UACCA,CAAC,EAAE;QACP,CAAC,QAAQoL,IAAI,CAACpL,CAAC,CAAC,KAAK,GAAG,IAAIA,CAAC,GAAGoL,IAAI,CAACtL,MAAM;QAC3CgL,MAAM,CAAC/J,IAAI,CAACnC,UAAU,CAACwM,IAAI,CAAC1I,KAAK,CAAC8I,KAAK,GAAG,CAAC,EAAExL,CAAC,CAAC,CAAC,CAAC;MACrD,CAAC,MAAM,MAAM,IAAI+E,KAAK,CAACnC,CAAC,GAAG,2BAA2B,CAAC;IAC3D;IACA,OAAO6I,kBAAkB,CAACX,MAAM,EAAEjK,IAAI,EAAEgF,UAAU,CAAC;EACvD,CAAC;EAED,SAAS4F,kBAAkBA,CAACX,MAAM,EAAEjK,IAAI,EAAEgF,UAAU,EAAE;IAClD,IAAI6F,GAAG,GAAGnN,OAAO,CAAC,CAAC,CAAC;MAAEgH,GAAG,GAAGhH,OAAO,CAAC,CAAC,CAAC;MAAEyB,CAAC;IACzC,KAAKA,CAAC,GAAG8K,MAAM,CAAChL,MAAM,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACrC0L,GAAG,GAAGA,GAAG,CAACpL,GAAG,CAACwK,MAAM,CAAC9K,CAAC,CAAC,CAACoD,KAAK,CAACmC,GAAG,CAAC,CAAC;MACnCA,GAAG,GAAGA,GAAG,CAACnC,KAAK,CAACvC,IAAI,CAAC;IACzB;IACA,OAAOgF,UAAU,GAAG6F,GAAG,CAACtK,MAAM,CAAC,CAAC,GAAGsK,GAAG;EAC1C;EAEA,SAASC,SAASA,CAACT,KAAK,EAAExM,QAAQ,EAAE;IAChCA,QAAQ,GAAGA,QAAQ,IAAIN,gBAAgB;IACvC,IAAI8M,KAAK,GAAGxM,QAAQ,CAACoB,MAAM,EAAE;MACzB,OAAOpB,QAAQ,CAACwM,KAAK,CAAC;IAC1B;IACA,OAAO,GAAG,GAAGA,KAAK,GAAG,GAAG;EAC5B;EAEA,SAASH,MAAMA,CAACvL,CAAC,EAAEqB,IAAI,EAAE;IACrBA,IAAI,GAAGhD,MAAM,CAACgD,IAAI,CAAC;IACnB,IAAIA,IAAI,CAACmF,MAAM,CAAC,CAAC,EAAE;MACf,IAAIxG,CAAC,CAACwG,MAAM,CAAC,CAAC,EAAE,OAAO;QAAEjH,KAAK,EAAE,CAAC,CAAC,CAAC;QAAE8G,UAAU,EAAE;MAAM,CAAC;MACxD,MAAM,IAAId,KAAK,CAAC,2CAA2C,CAAC;IAChE;IACA,IAAIlE,IAAI,CAACyF,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,IAAI9G,CAAC,CAACwG,MAAM,CAAC,CAAC,EAAE,OAAO;QAAEjH,KAAK,EAAE,CAAC,CAAC,CAAC;QAAE8G,UAAU,EAAE;MAAM,CAAC;MACxD,IAAIrG,CAAC,CAACqG,UAAU,CAAC,CAAC,EAAE,OAAO;QACvB9G,KAAK,EAAE,EAAE,CAACuD,MAAM,CAACsJ,KAAK,CAAC,EAAE,EAAEzL,KAAK,CAACyL,KAAK,CAAC,IAAI,EAAEzL,KAAK,CAAC,CAACX,CAAC,CAACwI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC6D,GAAG,CAAC1L,KAAK,CAACjB,SAAS,CAAC4M,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1GjG,UAAU,EAAE;MAChB,CAAC;MACD,IAAIjG,GAAG,GAAGO,KAAK,CAACyL,KAAK,CAAC,IAAI,EAAEzL,KAAK,CAACX,CAAC,CAACwI,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC6D,GAAG,CAAC1L,KAAK,CAACjB,SAAS,CAAC4M,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3FlM,GAAG,CAAC8E,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,OAAO;QAAE3F,KAAK,EAAE,EAAE,CAACuD,MAAM,CAACsJ,KAAK,CAAC,EAAE,EAAEhM,GAAG,CAAC;QAAEiG,UAAU,EAAE;MAAM,CAAC;IACjE;IACA,IAAIkG,GAAG,GAAG,KAAK;IACf,IAAIvM,CAAC,CAACqG,UAAU,CAAC,CAAC,IAAIhF,IAAI,CAACoF,UAAU,CAAC,CAAC,EAAE;MACrC8F,GAAG,GAAG,IAAI;MACVvM,CAAC,GAAGA,CAAC,CAAC6B,GAAG,CAAC,CAAC;IACf;IACA,IAAIR,IAAI,CAACqG,MAAM,CAAC,CAAC,EAAE;MACf,IAAI1H,CAAC,CAACwG,MAAM,CAAC,CAAC,EAAE,OAAO;QAAEjH,KAAK,EAAE,CAAC,CAAC,CAAC;QAAE8G,UAAU,EAAE;MAAM,CAAC;MACxD,OAAO;QAAE9G,KAAK,EAAEoB,KAAK,CAACyL,KAAK,CAAC,IAAI,EAAEzL,KAAK,CAACX,CAAC,CAACwI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC6D,GAAG,CAACG,MAAM,CAAC9M,SAAS,CAAC4M,OAAO,EAAE,CAAC,CAAC;QAAEjG,UAAU,EAAEkG;MAAI,CAAC;IAChH;IACA,IAAIE,GAAG,GAAG,EAAE;IACZ,IAAIC,IAAI,GAAG1M,CAAC;MAAE4F,MAAM;IACpB,OAAO8G,IAAI,CAACrG,UAAU,CAAC,CAAC,IAAIqG,IAAI,CAACnM,UAAU,CAACc,IAAI,CAAC,IAAI,CAAC,EAAE;MACpDuE,MAAM,GAAG8G,IAAI,CAAC9G,MAAM,CAACvE,IAAI,CAAC;MAC1BqL,IAAI,GAAG9G,MAAM,CAACR,QAAQ;MACtB,IAAIsG,KAAK,GAAG9F,MAAM,CAACvB,SAAS;MAC5B,IAAIqH,KAAK,CAACrF,UAAU,CAAC,CAAC,EAAE;QACpBqF,KAAK,GAAGrK,IAAI,CAACgB,KAAK,CAACqJ,KAAK,CAAC,CAAC7J,GAAG,CAAC,CAAC;QAC/B6K,IAAI,GAAGA,IAAI,CAACzE,IAAI,CAAC,CAAC;MACtB;MACAwE,GAAG,CAAClL,IAAI,CAACmK,KAAK,CAAClD,UAAU,CAAC,CAAC,CAAC;IAChC;IACAiE,GAAG,CAAClL,IAAI,CAACmL,IAAI,CAAClE,UAAU,CAAC,CAAC,CAAC;IAC3B,OAAO;MAAEjJ,KAAK,EAAEkN,GAAG,CAACtH,OAAO,CAAC,CAAC;MAAEkB,UAAU,EAAEkG;IAAI,CAAC;EACpD;EAEA,SAASI,YAAYA,CAAC3M,CAAC,EAAEqB,IAAI,EAAEnC,QAAQ,EAAE;IACrC,IAAIkB,GAAG,GAAGmL,MAAM,CAACvL,CAAC,EAAEqB,IAAI,CAAC;IACzB,OAAO,CAACjB,GAAG,CAACiG,UAAU,GAAG,GAAG,GAAG,EAAE,IAAIjG,GAAG,CAACb,KAAK,CAAC8M,GAAG,CAAC,UAAU3L,CAAC,EAAE;MAC5D,OAAOyL,SAAS,CAACzL,CAAC,EAAExB,QAAQ,CAAC;IACjC,CAAC,CAAC,CAAC0N,IAAI,CAAC,EAAE,CAAC;EACf;EAEAtN,UAAU,CAACI,SAAS,CAACmN,OAAO,GAAG,UAAU5N,KAAK,EAAE;IAC5C,OAAOsM,MAAM,CAAC,IAAI,EAAEtM,KAAK,CAAC;EAC9B,CAAC;EACDY,YAAY,CAACH,SAAS,CAACmN,OAAO,GAAG,UAAU5N,KAAK,EAAE;IAC9C,OAAOsM,MAAM,CAAC,IAAI,EAAEtM,KAAK,CAAC;EAC9B,CAAC;EACDa,YAAY,CAACJ,SAAS,CAACmN,OAAO,GAAG,UAAU5N,KAAK,EAAE;IAC9C,OAAOsM,MAAM,CAAC,IAAI,EAAEtM,KAAK,CAAC;EAC9B,CAAC;EACDK,UAAU,CAACI,SAAS,CAACuG,QAAQ,GAAG,UAAUhH,KAAK,EAAEC,QAAQ,EAAE;IACvD,IAAID,KAAK,KAAKX,SAAS,EAAEW,KAAK,GAAG,EAAE;IACnC,IAAIA,KAAK,KAAK,EAAE,EAAE,OAAO0N,YAAY,CAAC,IAAI,EAAE1N,KAAK,EAAEC,QAAQ,CAAC;IAC5D,IAAIF,CAAC,GAAG,IAAI,CAACO,KAAK;MAAEmC,CAAC,GAAG1C,CAAC,CAACsB,MAAM;MAAEwM,GAAG,GAAGvD,MAAM,CAACvK,CAAC,CAAC,EAAE0C,CAAC,CAAC,CAAC;MAAEqL,KAAK,GAAG,SAAS;MAAErB,KAAK;IAChF,OAAO,EAAEhK,CAAC,IAAI,CAAC,EAAE;MACbgK,KAAK,GAAGnC,MAAM,CAACvK,CAAC,CAAC0C,CAAC,CAAC,CAAC;MACpBoL,GAAG,IAAIC,KAAK,CAAC7J,KAAK,CAACwI,KAAK,CAACpL,MAAM,CAAC,GAAGoL,KAAK;IAC5C;IACA,IAAIlM,IAAI,GAAG,IAAI,CAACA,IAAI,GAAG,GAAG,GAAG,EAAE;IAC/B,OAAOA,IAAI,GAAGsN,GAAG;EACrB,CAAC;EACDjN,YAAY,CAACH,SAAS,CAACuG,QAAQ,GAAG,UAAUhH,KAAK,EAAEC,QAAQ,EAAE;IACzD,IAAID,KAAK,KAAKX,SAAS,EAAEW,KAAK,GAAG,EAAE;IACnC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO0N,YAAY,CAAC,IAAI,EAAE1N,KAAK,EAAEC,QAAQ,CAAC;IAC3D,OAAOqK,MAAM,CAAC,IAAI,CAAChK,KAAK,CAAC;EAC7B,CAAC;EACDO,YAAY,CAACJ,SAAS,CAACuG,QAAQ,GAAGpG,YAAY,CAACH,SAAS,CAACuG,QAAQ;EACjEnG,YAAY,CAACJ,SAAS,CAACsN,MAAM,GAAG1N,UAAU,CAACI,SAAS,CAACsN,MAAM,GAAGnN,YAAY,CAACH,SAAS,CAACsN,MAAM,GAAG,YAAY;IACtG,OAAO,IAAI,CAAC/G,QAAQ,CAAC,CAAC;EAC1B,CAAC;EACD3G,UAAU,CAACI,SAAS,CAAC4M,OAAO,GAAG,YAAY;IACvC,OAAOW,QAAQ,CAAC,IAAI,CAAChH,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC;EACxC,CAAC;EACD3G,UAAU,CAACI,SAAS,CAAC8I,UAAU,GAAGlJ,UAAU,CAACI,SAAS,CAAC4M,OAAO;EAC9DzM,YAAY,CAACH,SAAS,CAAC4M,OAAO,GAAG,YAAY;IACzC,OAAO,IAAI,CAAC/M,KAAK;EACrB,CAAC;EACDM,YAAY,CAACH,SAAS,CAAC8I,UAAU,GAAG3I,YAAY,CAACH,SAAS,CAAC4M,OAAO;EAClExM,YAAY,CAACJ,SAAS,CAAC4M,OAAO,GAAGxM,YAAY,CAACJ,SAAS,CAAC8I,UAAU,GAAG,YAAY;IAC7E,OAAOyE,QAAQ,CAAC,IAAI,CAAChH,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC;EACxC,CAAC;EAED,SAASiH,gBAAgBA,CAAClO,CAAC,EAAE;IACzB,IAAIe,SAAS,CAAC,CAACf,CAAC,CAAC,EAAE;MACf,IAAI0B,CAAC,GAAG,CAAC1B,CAAC;MACV,IAAI0B,CAAC,KAAKE,QAAQ,CAACF,CAAC,CAAC,EAAE,OAAO7B,oBAAoB,GAAG,IAAIiB,YAAY,CAAChB,MAAM,CAAC4B,CAAC,CAAC,CAAC,GAAG,IAAIb,YAAY,CAACa,CAAC,CAAC;MACtG,MAAM,IAAI6E,KAAK,CAAC,mBAAmB,GAAGvG,CAAC,CAAC;IAC5C;IACA,IAAIQ,IAAI,GAAGR,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG;IACvB,IAAIQ,IAAI,EAAER,CAAC,GAAGA,CAAC,CAACkE,KAAK,CAAC,CAAC,CAAC;IACxB,IAAIiK,KAAK,GAAGnO,CAAC,CAACmO,KAAK,CAAC,IAAI,CAAC;IACzB,IAAIA,KAAK,CAAC7M,MAAM,GAAG,CAAC,EAAE,MAAM,IAAIiF,KAAK,CAAC,mBAAmB,GAAG4H,KAAK,CAACP,IAAI,CAAC,GAAG,CAAC,CAAC;IAC5E,IAAIO,KAAK,CAAC7M,MAAM,KAAK,CAAC,EAAE;MACpB,IAAIiG,GAAG,GAAG4G,KAAK,CAAC,CAAC,CAAC;MAClB,IAAI5G,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAEA,GAAG,GAAGA,GAAG,CAACrD,KAAK,CAAC,CAAC,CAAC;MACtCqD,GAAG,GAAG,CAACA,GAAG;MACV,IAAIA,GAAG,KAAK3F,QAAQ,CAAC2F,GAAG,CAAC,IAAI,CAACxG,SAAS,CAACwG,GAAG,CAAC,EAAE,MAAM,IAAIhB,KAAK,CAAC,mBAAmB,GAAGgB,GAAG,GAAG,2BAA2B,CAAC;MACtH,IAAIqF,IAAI,GAAGuB,KAAK,CAAC,CAAC,CAAC;MACnB,IAAIC,YAAY,GAAGxB,IAAI,CAACyB,OAAO,CAAC,GAAG,CAAC;MACpC,IAAID,YAAY,IAAI,CAAC,EAAE;QACnB7G,GAAG,IAAIqF,IAAI,CAACtL,MAAM,GAAG8M,YAAY,GAAG,CAAC;QACrCxB,IAAI,GAAGA,IAAI,CAAC1I,KAAK,CAAC,CAAC,EAAEkK,YAAY,CAAC,GAAGxB,IAAI,CAAC1I,KAAK,CAACkK,YAAY,GAAG,CAAC,CAAC;MACrE;MACA,IAAI7G,GAAG,GAAG,CAAC,EAAE,MAAM,IAAIhB,KAAK,CAAC,oDAAoD,CAAC;MAClFqG,IAAI,IAAI,IAAIjL,KAAK,CAAC4F,GAAG,GAAG,CAAC,CAAC,CAACqG,IAAI,CAAC,GAAG,CAAC;MACpC5N,CAAC,GAAG4M,IAAI;IACZ;IACA,IAAI0B,OAAO,GAAG,iBAAiB,CAACC,IAAI,CAACvO,CAAC,CAAC;IACvC,IAAI,CAACsO,OAAO,EAAE,MAAM,IAAI/H,KAAK,CAAC,mBAAmB,GAAGvG,CAAC,CAAC;IACtD,IAAIH,oBAAoB,EAAE;MACtB,OAAO,IAAIiB,YAAY,CAAChB,MAAM,CAACU,IAAI,GAAG,GAAG,GAAGR,CAAC,GAAGA,CAAC,CAAC,CAAC;IACvD;IACA,IAAImC,CAAC,GAAG,EAAE;MAAE8B,GAAG,GAAGjE,CAAC,CAACsB,MAAM;MAAEoB,CAAC,GAAGlD,QAAQ;MAAEuM,GAAG,GAAG9H,GAAG,GAAGvB,CAAC;IACvD,OAAOuB,GAAG,GAAG,CAAC,EAAE;MACZ9B,CAAC,CAACI,IAAI,CAAC,CAACvC,CAAC,CAACkE,KAAK,CAAC6H,GAAG,EAAE9H,GAAG,CAAC,CAAC;MAC1B8H,GAAG,IAAIrJ,CAAC;MACR,IAAIqJ,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC;MACpB9H,GAAG,IAAIvB,CAAC;IACZ;IACArB,IAAI,CAACc,CAAC,CAAC;IACP,OAAO,IAAI7B,UAAU,CAAC6B,CAAC,EAAE3B,IAAI,CAAC;EAClC;EAEA,SAASgO,gBAAgBA,CAACxO,CAAC,EAAE;IACzB,IAAIH,oBAAoB,EAAE;MACtB,OAAO,IAAIiB,YAAY,CAAChB,MAAM,CAACE,CAAC,CAAC,CAAC;IACtC;IACA,IAAIe,SAAS,CAACf,CAAC,CAAC,EAAE;MACd,IAAIA,CAAC,KAAK4B,QAAQ,CAAC5B,CAAC,CAAC,EAAE,MAAM,IAAIuG,KAAK,CAACvG,CAAC,GAAG,qBAAqB,CAAC;MACjE,OAAO,IAAIa,YAAY,CAACb,CAAC,CAAC;IAC9B;IACA,OAAOkO,gBAAgB,CAAClO,CAAC,CAACiH,QAAQ,CAAC,CAAC,CAAC;EACzC;EAEA,SAAS7G,UAAUA,CAACJ,CAAC,EAAE;IACnB,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;MACvB,OAAOwO,gBAAgB,CAACxO,CAAC,CAAC;IAC9B;IACA,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;MACvB,OAAOkO,gBAAgB,CAAClO,CAAC,CAAC;IAC9B;IACA,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;MACvB,OAAO,IAAIc,YAAY,CAACd,CAAC,CAAC;IAC9B;IACA,OAAOA,CAAC;EACZ;EAEA,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;IAC1BzB,OAAO,CAACyB,CAAC,CAAC,GAAGpB,UAAU,CAACoB,CAAC,CAAC;IAC1B,IAAIA,CAAC,GAAG,CAAC,EAAEzB,OAAO,CAAC,CAACyB,CAAC,CAAC,GAAGpB,UAAU,CAAC,CAACoB,CAAC,CAAC;EAC3C;EACAzB,OAAO,CAACgK,GAAG,GAAGhK,OAAO,CAAC,CAAC,CAAC;EACxBA,OAAO,CAAC8J,IAAI,GAAG9J,OAAO,CAAC,CAAC,CAAC;EACzBA,OAAO,CAAC0O,QAAQ,GAAG1O,OAAO,CAAC,CAAC,CAAC,CAAC;EAC9BA,OAAO,CAACkE,GAAG,GAAGA,GAAG;EACjBlE,OAAO,CAACgM,GAAG,GAAGA,GAAG;EACjBhM,OAAO,CAACiM,GAAG,GAAGA,GAAG;EACjBjM,OAAO,CAACkM,GAAG,GAAGA,GAAG;EACjBlM,OAAO,CAAC2O,UAAU,GAAG,UAAUhN,CAAC,EAAE;IAC9B,OAAOA,CAAC,YAAYpB,UAAU,IAAIoB,CAAC,YAAYb,YAAY,IAAIa,CAAC,YAAYZ,YAAY;EAC5F,CAAC;EACDf,OAAO,CAAC4J,WAAW,GAAGA,WAAW;EACjC5J,OAAO,CAAC4M,SAAS,GAAG,UAAUL,MAAM,EAAEjK,IAAI,EAAEgF,UAAU,EAAE;IACpD,OAAO4F,kBAAkB,CAACX,MAAM,CAACe,GAAG,CAACjN,UAAU,CAAC,EAAEA,UAAU,CAACiC,IAAI,IAAI,EAAE,CAAC,EAAEgF,UAAU,CAAC;EACzF,CAAC;EACD,OAAOtH,OAAO;AAClB,CAAC,CAAC,CAAC;AAEH,IAAI,OAAO4O,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;EAC5CD,MAAM,CAAC,aAAa,EAAE,EAAE,EAAE,YAAY;IAClC,OAAOtP,MAAM;EACjB,CAAC,CAAC;AACN;AAEA,+DAAeA,MAAM;;;;;;;;;;;ACzwCrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwP,YAAY,GAAI,YAAY;EAE9B,IAAIC,QAAQ;EACZ,IAAIC,iBAAiB,GAAG,EAAE;EAC1B,MAAMC,kBAAkB,GAAG,CAAC,CAAC;EAC7B,MAAMC,cAAc,GAAG,CAAC,CAAC;EAEzB,SAASC,MAAMA,CAACC,IAAI,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACpD,IAAI,CAACA,OAAO,CAACH,IAAI,CAAC,IAAIC,aAAa,EAAE;MACjCE,OAAO,CAACH,IAAI,CAAC,GAAG;QACZL,QAAQ,EAAEM,aAAa;QACvBC,QAAQ,EAAEA;MACd,CAAC;IACL;EACJ;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASE,oBAAoBA,CAACD,OAAO,EAAEE,SAAS,EAAE;IAC9C,KAAK,MAAMhO,CAAC,IAAIuN,iBAAiB,EAAE;MAC/B,MAAMU,GAAG,GAAGV,iBAAiB,CAACvN,CAAC,CAAC;MAChC,IAAIiO,GAAG,CAACH,OAAO,KAAKA,OAAO,IAAIG,GAAG,CAACN,IAAI,KAAKK,SAAS,EAAE;QACnD,OAAOC,GAAG,CAACX,QAAQ;MACvB;IACJ;IACA,OAAO,IAAI;EACf;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASY,oBAAoBA,CAACJ,OAAO,EAAEE,SAAS,EAAEV,QAAQ,EAAE;IACxD,KAAK,MAAMtN,CAAC,IAAIuN,iBAAiB,EAAE;MAC/B,MAAMU,GAAG,GAAGV,iBAAiB,CAACvN,CAAC,CAAC;MAChC,IAAIiO,GAAG,CAACH,OAAO,KAAKA,OAAO,IAAIG,GAAG,CAACN,IAAI,KAAKK,SAAS,EAAE;QACnDT,iBAAiB,CAACvN,CAAC,CAAC,CAACsN,QAAQ,GAAGA,QAAQ;QACxC;MACJ;IACJ;IACAC,iBAAiB,CAACxM,IAAI,CAAC;MACnB4M,IAAI,EAAEK,SAAS;MACfF,OAAO,EAAEA,OAAO;MAChBR,QAAQ,EAAEA;IACd,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASa,wBAAwBA,CAACL,OAAO,EAAE;IACvCP,iBAAiB,GAAGA,iBAAiB,CAACa,MAAM,CAAClO,CAAC,IAAIA,CAAC,CAAC4N,OAAO,KAAKA,OAAO,CAAC;EAC5E;;EAEA;;EAEA;;EAEA;;EAEA,SAASO,gBAAgBA,CAACV,IAAI,EAAEW,cAAc,EAAE;IAC5C,OAAOA,cAAc,CAACX,IAAI,CAAC;EAC/B;EAEA,SAASY,aAAaA,CAACZ,IAAI,EAAEa,OAAO,EAAEF,cAAc,EAAE;IAClD,IAAIX,IAAI,IAAIW,cAAc,EAAE;MACxBA,cAAc,CAACX,IAAI,CAAC,GAAGa,OAAO;IAClC;EACJ;;EAEA;;EAEA;;EAEA;;EAEA,SAASC,kBAAkBA,CAACd,IAAI,EAAEa,OAAO,EAAE;IACvCD,aAAa,CAACZ,IAAI,EAAEa,OAAO,EAAEf,cAAc,CAAC;EAChD;EAEA,SAASiB,qBAAqBA,CAACf,IAAI,EAAE;IACjC,OAAOU,gBAAgB,CAACV,IAAI,EAAEF,cAAc,CAAC;EACjD;EAEA,SAASkB,eAAeA,CAACC,gBAAgB,EAAE;IACvC,IAAIJ,OAAO,GAAGH,gBAAgB,CAACO,gBAAgB,CAACC,qBAAqB,EAAEpB,cAAc,CAAC;IAEtF,IAAI,CAACe,OAAO,EAAE;MACVA,OAAO,GAAG,SAAAA,CAAUV,OAAO,EAAE;QACzB,IAAIA,OAAO,KAAKhQ,SAAS,EAAE;UACvBgQ,OAAO,GAAG,CAAC,CAAC;QAChB;QACA,OAAO;UACH1O,MAAM,EAAE,SAAAA,CAAA,EAAY;YAChB,OAAO0P,KAAK,CAACF,gBAAgB,EAAEd,OAAO,EAAEiB,SAAS,CAAC;UACtD;QACJ,CAAC;MACL,CAAC;MAEDtB,cAAc,CAACmB,gBAAgB,CAACC,qBAAqB,CAAC,GAAGL,OAAO,CAAC,CAAC;IACtE;IACA,OAAOA,OAAO;EAClB;;EAEA;;EAEA;;EAEA;;EAEA,SAASQ,sBAAsBA,CAACrB,IAAI,EAAEa,OAAO,EAAE;IAC3CD,aAAa,CAACZ,IAAI,EAAEa,OAAO,EAAEhB,kBAAkB,CAAC;EACpD;EAEA,SAASyB,yBAAyBA,CAACtB,IAAI,EAAE;IACrC,OAAOU,gBAAgB,CAACV,IAAI,EAAEH,kBAAkB,CAAC;EACrD;EAEA,SAAS0B,mBAAmBA,CAACN,gBAAgB,EAAE;IAC3C,IAAIJ,OAAO,GAAGH,gBAAgB,CAACO,gBAAgB,CAACC,qBAAqB,EAAErB,kBAAkB,CAAC;IAC1F,IAAI,CAACgB,OAAO,EAAE;MACVA,OAAO,GAAG,SAAAA,CAAUV,OAAO,EAAE;QACzB,IAAIR,QAAQ;QACZ,IAAIQ,OAAO,KAAKhQ,SAAS,EAAE;UACvBgQ,OAAO,GAAG,CAAC,CAAC;QAChB;QACA,OAAO;UACHqB,WAAW,EAAE,SAAAA,CAAA,EAAY;YACrB;YACA,IAAI,CAAC7B,QAAQ,EAAE;cACXA,QAAQ,GAAGS,oBAAoB,CAACD,OAAO,EAAEc,gBAAgB,CAACC,qBAAqB,CAAC;YACpF;YACA;YACA,IAAI,CAACvB,QAAQ,EAAE;cACXA,QAAQ,GAAGwB,KAAK,CAACF,gBAAgB,EAAEd,OAAO,EAAEiB,SAAS,CAAC;cACtDxB,iBAAiB,CAACxM,IAAI,CAAC;gBACnB4M,IAAI,EAAEiB,gBAAgB,CAACC,qBAAqB;gBAC5Cf,OAAO,EAAEA,OAAO;gBAChBR,QAAQ,EAAEA;cACd,CAAC,CAAC;YACN;YACA,OAAOA,QAAQ;UACnB;QACJ,CAAC;MACL,CAAC;MACDE,kBAAkB,CAACoB,gBAAgB,CAACC,qBAAqB,CAAC,GAAGL,OAAO,CAAC,CAAC;IAC1E;IAEA,OAAOA,OAAO;EAClB;EAEA,SAASM,KAAKA,CAACF,gBAAgB,EAAEd,OAAO,EAAEsB,IAAI,EAAE;IAE5C,IAAIC,aAAa;IACjB,MAAMrB,SAAS,GAAGY,gBAAgB,CAACC,qBAAqB;IACxD,MAAMS,eAAe,GAAGxB,OAAO,CAACE,SAAS,CAAC;IAE1C,IAAIsB,eAAe,EAAE;MAEjB,IAAIC,SAAS,GAAGD,eAAe,CAAChC,QAAQ;MAExC,IAAIgC,eAAe,CAACzB,QAAQ,EAAE;QAAE;;QAE5BwB,aAAa,GAAGT,gBAAgB,CAAChD,KAAK,CAAC;UAACkC;QAAO,CAAC,EAAEsB,IAAI,CAAC;QACvDG,SAAS,GAAGA,SAAS,CAAC3D,KAAK,CAAC;UACxBkC,OAAO;UACPU,OAAO,EAAElB,QAAQ;UACjBkC,MAAM,EAAEH;QACZ,CAAC,EAAED,IAAI,CAAC;QAER,KAAK,MAAMK,IAAI,IAAIF,SAAS,EAAE;UAC1B,IAAIF,aAAa,CAACK,cAAc,CAACD,IAAI,CAAC,EAAE;YACpCJ,aAAa,CAACI,IAAI,CAAC,GAAGF,SAAS,CAACE,IAAI,CAAC;UACzC;QACJ;MAEJ,CAAC,MAAM;QAAE;;QAEL,OAAOF,SAAS,CAAC3D,KAAK,CAAC;UACnBkC,OAAO;UACPU,OAAO,EAAElB;QACb,CAAC,EAAE8B,IAAI,CAAC;MAEZ;IACJ,CAAC,MAAM;MACH;MACAC,aAAa,GAAGT,gBAAgB,CAAChD,KAAK,CAAC;QAACkC;MAAO,CAAC,EAAEsB,IAAI,CAAC;IAC3D;;IAEA;IACAC,aAAa,CAACM,YAAY,GAAG,YAAY;MAAC,OAAO3B,SAAS;IAAC,CAAC;IAE5D,OAAOqB,aAAa;EACxB;EAEA/B,QAAQ,GAAG;IACPI,MAAM,EAAEA,MAAM;IACdK,oBAAoB,EAAEA,oBAAoB;IAC1CG,oBAAoB,EAAEA,oBAAoB;IAC1CC,wBAAwB,EAAEA,wBAAwB;IAClDe,mBAAmB,EAAEA,mBAAmB;IACxCD,yBAAyB,EAAEA,yBAAyB;IACpDD,sBAAsB,EAAEA,sBAAsB;IAC9CL,eAAe,EAAEA,eAAe;IAChCD,qBAAqB,EAAEA,qBAAqB;IAC5CD,kBAAkB,EAAEA;EACxB,CAAC;EAED,OAAOnB,QAAQ;AAEnB,CAAC,CAAC,CAAE;AAEJ,+DAAeD,YAAY;;;;;;;;;;;ACzQ3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuC,UAAU,CAAC;EACblC,MAAMA,CAACmC,MAAM,EAAEC,MAAM,EAAE;IACnB,IAAI,CAACD,MAAM,EAAE;MACT;IACJ;IAEA,IAAIhC,QAAQ,GAAGiC,MAAM,GAAGA,MAAM,CAACjC,QAAQ,GAAG,KAAK;IAC/C,IAAIkC,UAAU,GAAGD,MAAM,GAAGA,MAAM,CAACC,UAAU,GAAG,KAAK;IAGnD,KAAK,MAAMC,GAAG,IAAIH,MAAM,EAAE;MACtB,IAAI,CAACA,MAAM,CAACH,cAAc,CAACM,GAAG,CAAC,IAAK,IAAI,CAACA,GAAG,CAAC,IAAI,CAACnC,QAAS,EAAE;QACzD;MACJ;MACA,IAAIkC,UAAU,IAAIF,MAAM,CAACG,GAAG,CAAC,CAACnD,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;QACrD;MACJ;MACA,IAAI,CAACmD,GAAG,CAAC,GAAGH,MAAM,CAACG,GAAG,CAAC;IAE3B;EACJ;AACJ;AAEA,+DAAeJ,UAAU;;;;;;;;;;;ACzDzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,UAAU,CAAC;EACbvC,MAAMA,CAACwC,MAAM,EAAEJ,MAAM,EAAE;IACnB,IAAI,CAACI,MAAM,EAAE;MACT;IACJ;IAEA,IAAIrC,QAAQ,GAAGiC,MAAM,GAAGA,MAAM,CAACjC,QAAQ,GAAG,KAAK;IAC/C,IAAIkC,UAAU,GAAGD,MAAM,GAAGA,MAAM,CAACC,UAAU,GAAG,KAAK;IAGnD,KAAK,MAAMI,GAAG,IAAID,MAAM,EAAE;MACtB,IAAI,CAACA,MAAM,CAACR,cAAc,CAACS,GAAG,CAAC,IAAK,IAAI,CAACA,GAAG,CAAC,IAAI,CAACtC,QAAS,EAAE;QACzD;MACJ;MACA,IAAIkC,UAAU,IAAIG,MAAM,CAACC,GAAG,CAAC,CAACtD,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;QACrD;MACJ;MACA,IAAI,CAACsD,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAE3B;EACJ;AACJ;AAEA,+DAAeF,UAAU;;;;;;;;;;;;;;ACzDzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEiE;AACE;AAChB;AAEnD,SAASK,yBAAyBA,CAACR,MAAM,EAAE;EAEvCA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EAErB,IAAIxC,QAAQ,EACRiD,MAAM,EACNC,aAAa,EACbC,OAAO,EACPC,IAAI,EACJC,mBAAmB,EACnBC,SAAS,EACTC,iBAAiB,EACjBC,KAAK;EAET,MAAMC,eAAe,GAAGjB,MAAM,CAACiB,eAAe;EAC9C,MAAMC,iBAAiB,GAAGlB,MAAM,CAACkB,iBAAiB;EAClD,MAAMC,KAAK,GAAGnB,MAAM,CAACmB,KAAK;EAC1B,MAAMC,cAAc,GAAG,2BAA2B;EAElD,SAASC,KAAKA,CAAA,EAAG;IACbZ,MAAM,GAAGU,KAAK,CAACG,SAAS,CAAC9D,QAAQ,CAAC;EACtC;EAEA,SAAS+D,UAAUA,CAAA,EAAG;IAClBX,IAAI,GAAGK,eAAe,CAACO,OAAO,CAAC,CAAC;IAChCd,aAAa,GAAGO,eAAe,CAACQ,gBAAgB,CAAC,CAAC;IAElDd,OAAO,GAAG,KAAK;IACfG,SAAS,GAAG,IAAI;IAChBC,iBAAiB,GAAG,IAAI;EAC5B;EAEA,SAASrF,KAAKA,CAAA,EAAG;IACb,IAAIiF,OAAO,EAAE;MACT;IACJ;IAEAF,MAAM,CAACU,KAAK,CAAC,OAAO,CAAC;IAErBR,OAAO,GAAG,IAAI;IACdK,KAAK,GAAG,CAAC;IAETU,oBAAoB,CAAC,CAAC;EAC1B;EAEA,SAASC,IAAIA,CAAA,EAAG;IACZ,IAAI,CAAChB,OAAO,EAAE;MACV;IACJ;IAEAF,MAAM,CAACU,KAAK,CAAC,MAAM,CAAC;IAEpBS,YAAY,CAACf,mBAAmB,CAAC;IACjCF,OAAO,GAAG,KAAK;IACfG,SAAS,GAAG,IAAI;IAChBC,iBAAiB,GAAG,IAAI;EAC5B;EAEA,SAASc,KAAKA,CAAA,EAAG;IACbF,IAAI,CAAC,CAAC;EACV;EAEA,SAASD,oBAAoBA,CAAA,EAAG;IAC5B,IAAI,CAACf,OAAO,EAAE;MACV;IACJ;;IAEA;IACA,MAAMmB,cAAc,GAAGC,wBAAwB,CAAC,CAAC;IACjD,MAAMC,QAAQ,GAAGF,cAAc,CAACG,UAAU,CAACC,MAAM,CAACC,GAAG,CAACH,QAAQ;IAC9D,MAAMC,UAAU,GAAGD,QAAQ,CAACI,MAAM,CAACN,cAAc,CAACG,UAAU,CAACC,MAAM,CAAClB,KAAK,CAAC,CAACqB,aAAa,CAACP,cAAc,CAACG,UAAU,CAACjB,KAAK,CAAC;IACzH,MAAMsB,QAAQ,GAAGL,UAAU,CAACM,eAAe,CAACC,eAAe,CAACC,CAAC;IAC7D,MAAMC,OAAO,GAAGJ,QAAQ,CAACA,QAAQ,CAACtS,MAAM,GAAG,CAAC,CAAC;;IAE7C;;IAEA;IACA,MAAM2S,OAAO,GAAGC,oBAAoB,CAACX,UAAU,EAAEH,cAAc,EAAEY,OAAO,CAAC;;IAEzE;IACAG,eAAe,CAACC,IAAI,CAAC,IAAI,EAAEH,OAAO,CAAC;EACvC;EAEA,SAASC,oBAAoBA,CAACX,UAAU,EAAEH,cAAc,EAAEY,OAAO,EAAE;IAC/D,IAAIK,SAAS,GAAGd,UAAU,CAACM,eAAe,CAACQ,SAAS;IACpD,IAAIJ,OAAO,GAAG,IAAIrC,wEAAe,CAAC,CAAC;IAEnCqC,OAAO,CAACK,SAAS,GAAGpC,IAAI;IACxB+B,OAAO,CAAC/B,IAAI,GAAGL,6EAAW,CAAC0C,8BAA8B;IACzD;IACAN,OAAO,CAAC7B,SAAS,GAAG4B,OAAO,CAAChL,CAAC,GAAGqL,SAAS;IACzCJ,OAAO,CAACO,QAAQ,GAAGR,OAAO,CAAC7P,CAAC,GAAGkQ,SAAS;IACxCJ,OAAO,CAACI,SAAS,GAAGA,SAAS;IAC7B;IACA;IACA;IACAJ,OAAO,CAACQ,SAAS,GAAGrB,cAAc,CAACqB,SAAS;IAC5CR,OAAO,CAAC3B,KAAK,GAAGA,KAAK,EAAE;IACvB2B,OAAO,CAACS,eAAe,GAAGtB,cAAc,CAACG,UAAU,CAACjB,KAAK;IACzD2B,OAAO,CAACb,cAAc,GAAGA,cAAc;IACvCa,OAAO,CAACU,GAAG,GAAGnC,iBAAiB,CAACoC,OAAO,CAACxB,cAAc,CAACyB,IAAI,CAAC,CAACF,GAAG,GAAGpB,UAAU,CAACM,eAAe,CAACiB,KAAK;IACnGb,OAAO,CAACU,GAAG,GAAGV,OAAO,CAACU,GAAG,CAACI,OAAO,CAAC,aAAa,EAAE3B,cAAc,CAACqB,SAAS,CAAC;IAC1ER,OAAO,CAACU,GAAG,GAAGV,OAAO,CAACU,GAAG,CAACI,OAAO,CAAC,QAAQ,EAAEf,OAAO,CAACgB,SAAS,GAAGhB,OAAO,CAACgB,SAAS,GAAGhB,OAAO,CAAChL,CAAC,CAAC;IAC9FiL,OAAO,CAACU,GAAG,GAAGV,OAAO,CAACU,GAAG,CAACI,OAAO,CAAC,aAAa,EAAE,gBAAgB,CAAC;IAElE,OAAOd,OAAO;EAClB;EAEA,SAASZ,wBAAwBA,CAAA,EAAG;IAChC,MAAM4B,wBAAwB,GAAG1C,eAAe,CAAC2C,2BAA2B,CAAC,CAAC;IAC9E,MAAM9B,cAAc,GAAG6B,wBAAwB,CAAC5B,wBAAwB,CAAC,CAAC;IAC1E,OAAOD,cAAc;EACzB;EAEA,SAASe,eAAeA,CAACF,OAAO,EAAE;IAC9B;IACA,IAAI1B,eAAe,CAACQ,gBAAgB,CAAC,CAAC,CAACoC,yBAAyB,CAAClB,OAAO,CAAC,EAAE;MACvE;MACAlC,MAAM,CAACU,KAAK,CAAC,iBAAiB,CAAC;MAC/BQ,IAAI,CAAC,CAAC;MACN;IACJ;IAEAjB,aAAa,CAACoD,cAAc,CAACnB,OAAO,CAAC;EACzC;EAEA,SAASoB,kBAAkBA,CAACvJ,CAAC,EAAE;IAC3B,IAAI,CAACmG,OAAO,EAAE;MACV;IACJ;IAEA,MAAMgC,OAAO,GAAGnI,CAAC,CAACmI,OAAO;IACzB,IAAI,CAACnI,CAAC,CAACwJ,QAAQ,EAAE;MACbvD,MAAM,CAACwD,KAAK,CAAC,YAAY,EAAEtB,OAAO,CAACU,GAAG,CAAC;MACvC;IACJ;IAEA,IAAIa,iBAAiB,EACjBC,SAAS,EACTC,KAAK;;IAET;;IAEA,IAAItD,SAAS,KAAK,IAAI,EAAE;MACpBA,SAAS,GAAG,IAAIuD,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACpC;IAEA,IAAI,CAACvD,iBAAiB,EAAE;MACpBA,iBAAiB,GAAG4B,OAAO,CAAC7B,SAAS;IACzC;;IAEA;IACAqD,SAAS,GAAG,CAAC,IAAIE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAGxD,SAAS,IAAI,IAAI;IACrDoD,iBAAiB,GAAIvB,OAAO,CAAC7B,SAAS,GAAG6B,OAAO,CAACO,QAAQ,GAAInC,iBAAiB;IAC9EqD,KAAK,GAAGzU,IAAI,CAACgD,GAAG,CAAC,CAAC,EAAGuR,iBAAiB,GAAGC,SAAU,CAAC;;IAEpD;IACAvC,YAAY,CAACf,mBAAmB,CAAC;IACjCA,mBAAmB,GAAG0D,UAAU,CAAC,YAAY;MACzC1D,mBAAmB,GAAG,IAAI;MAC1Ba,oBAAoB,CAAC,CAAC;IAC1B,CAAC,EAAE0C,KAAK,GAAG,IAAI,CAAC;EACpB;EAEA,SAAS5C,OAAOA,CAAA,EAAG;IACf,OAAOZ,IAAI;EACf;EAEApD,QAAQ,GAAG;IACP+D,UAAU,EAAEA,UAAU;IACtBH,cAAc,EAAEA,cAAc;IAC9B1F,KAAK,EAAEA,KAAK;IACZqI,kBAAkB,EAAEA,kBAAkB;IACtCvC,OAAO,EAAEA,OAAO;IAChBK,KAAK,EAAEA;EACX,CAAC;EAEDR,KAAK,CAAC,CAAC;EAEP,OAAO7D,QAAQ;AACnB;AAEAgD,yBAAyB,CAACzB,qBAAqB,GAAG,2BAA2B;AAC7E,+DAAexB,6DAAY,CAACsB,eAAe,CAAC2B,yBAAyB,CAAC;;;;;;;;;;;;;;;AC1NtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACyD;AACX;AAES;AACJ;;AAEnD;AACA;AACA;AACA;AACA;AACA,SAASmE,wBAAwBA,CAAC3E,MAAM,EAAE;EAEtCA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACrB,IAAIxC,QAAQ,EACRoD,IAAI,EACJH,MAAM;EACV,MAAMmE,WAAW,GAAG5E,MAAM,CAAC4E,WAAW;EACtC,MAAMC,kBAAkB,GAAG7E,MAAM,CAAC6E,kBAAkB;EACpD,MAAMC,YAAY,GAAG9E,MAAM,CAAC+E,UAAU;EACtC,MAAMC,QAAQ,GAAGhF,MAAM,CAACgF,QAAQ;EAChC,MAAMC,QAAQ,GAAGjF,MAAM,CAACiF,QAAQ;EAChC,MAAM9D,KAAK,GAAGnB,MAAM,CAACmB,KAAK;EAE1B,SAASE,KAAKA,CAAA,EAAG;IACbZ,MAAM,GAAGU,KAAK,CAACG,SAAS,CAAC9D,QAAQ,CAAC;IAClCoD,IAAI,GAAG,EAAE;EACb;EAEA,SAASsE,WAAWA,CAACvC,OAAO,EAAEwC,IAAI,EAAEC,IAAI,EAAEnE,eAAe,EAAE;IACvD,MAAM0C,wBAAwB,GAAG1C,eAAe,CAAC2C,2BAA2B,CAAC,CAAC;IAC9E,MAAM9B,cAAc,GAAG6B,wBAAwB,CAAC5B,wBAAwB,CAAC,CAAC;IAE1E,MAAMC,QAAQ,GAAGF,cAAc,CAACG,UAAU,CAACC,MAAM,CAACC,GAAG,CAACH,QAAQ;IAC9D,MAAMC,UAAU,GAAGD,QAAQ,CAACI,MAAM,CAACN,cAAc,CAACG,UAAU,CAACC,MAAM,CAAClB,KAAK,CAAC,CAACqB,aAAa,CAACP,cAAc,CAACG,UAAU,CAACjB,KAAK,CAAC;IACzH,MAAM+B,SAAS,GAAGd,UAAU,CAACM,eAAe,CAACQ,SAAS;IAEtDnC,IAAI,GAAGK,eAAe,CAACO,OAAO,CAAC,CAAC;;IAEhC;IACA,IAAIQ,QAAQ,CAACpB,IAAI,KAAK,SAAS,IAAI,CAACoB,QAAQ,CAACqD,oBAAoB,EAAE;MAC/D;IACJ;IAEA,IAAI,CAACF,IAAI,EAAE;MACPL,YAAY,CAACb,KAAK,CAAC,IAAIO,oEAAW,CAACC,4DAAS,CAACa,gBAAgB,EAAEb,4DAAS,CAACc,mBAAmB,CAAC,CAAC;MAC9F;IACJ;;IAEA;IACA,MAAMjD,QAAQ,GAAGL,UAAU,CAACM,eAAe,CAACC,eAAe,CAACC,CAAC;IAC7D,MAAM+C,OAAO,GAAGL,IAAI,CAACM,KAAK;IAC1B,IAAIA,KAAK,EACLC,WAAW,EACX5K,KAAK;IACT,IAAI4H,OAAO,GAAG,IAAI;IAClB,IAAIhL,CAAC,GAAG,CAAC;IACT,IAAIiO,OAAO;IACX,IAAIC,qBAAqB,GAAG,IAAI;IAEhC,IAAIJ,OAAO,CAACxV,MAAM,KAAK,CAAC,EAAE;MACtB;IACJ;;IAEA;IACAyV,KAAK,GAAGD,OAAO,CAAC,CAAC,CAAC;;IAElB;IACA;IACA,IAAIxD,QAAQ,CAACpB,IAAI,KAAK,QAAQ,EAAE;MAC5B;MACA8E,WAAW,GAAGpD,QAAQ,CAAC,CAAC,CAAC,CAACoB,SAAS,GAAGmC,UAAU,CAACvD,QAAQ,CAAC,CAAC,CAAC,CAACoB,SAAS,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC,CAAC5K,CAAC;MACvF,IAAI+N,KAAK,CAACK,sBAAsB,GAAIJ,WAAW,GAAI1D,QAAQ,CAACqD,oBAAoB,GAAGtC,SAAW,EAAE;QAC5F;MACJ;IACJ;;IAEA;;IAEA;IACA2C,WAAW,GAAGpD,QAAQ,CAACA,QAAQ,CAACtS,MAAM,GAAG,CAAC,CAAC,CAAC0T,SAAS,GAAGmC,UAAU,CAACvD,QAAQ,CAACA,QAAQ,CAACtS,MAAM,GAAG,CAAC,CAAC,CAAC0T,SAAS,CAAC,GAAGpB,QAAQ,CAACA,QAAQ,CAACtS,MAAM,GAAG,CAAC,CAAC,CAAC0H,CAAC;IAC7I;;IAEA;IACA,IAAI+N,KAAK,CAACK,sBAAsB,IAAIJ,WAAW,EAAE;MAC7C;MACA5K,KAAK,GAAG;QACJY,KAAK,EAAE4G,QAAQ,CAAC,CAAC,CAAC,CAAC5K,CAAC,GAAGqL,SAAS;QAChCgD,GAAG,EAAGX,IAAI,CAACY,mBAAmB,GAAGjD,SAAS,GAAIJ,OAAO,CAACO;MAC1D,CAAC;MAED+C,SAAS,CAACtD,OAAO,CAACK,SAAS,EAAElI,KAAK,EAAEmG,eAAe,CAACiF,aAAa,CAAC,CAAC,CAACC,YAAY,CAAC;MACjF;IACJ;;IAEA;IACAzD,OAAO,GAAG,CAAC,CAAC;IACZA,OAAO,CAAChL,CAAC,GAAG+N,KAAK,CAACK,sBAAsB;IACxCpD,OAAO,CAAC7P,CAAC,GAAG4S,KAAK,CAACW,iBAAiB;IACnC;IACA,IAAI9D,QAAQ,CAAC,CAAC,CAAC,CAACoB,SAAS,EAAE;MACvBhB,OAAO,CAAChL,CAAC,IAAImO,UAAU,CAACvD,QAAQ,CAAC,CAAC,CAAC,CAACoB,SAAS,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC,CAAC5K,CAAC;MAC9DgL,OAAO,CAACgB,SAAS,GAAG+B,KAAK,CAACK,sBAAsB;IACpD;;IAEA;IACA,IAAIO,WAAW,GAAG/D,QAAQ,CAACA,QAAQ,CAACtS,MAAM,GAAG,CAAC,CAAC;IAC/C,IAAIqW,WAAW,CAAC3O,CAAC,GAAG2O,WAAW,CAACxT,CAAC,KAAK6P,OAAO,CAAChL,CAAC,EAAE;MAC7C+I,MAAM,CAACU,KAAK,CAAC,+BAA+B,EAAEkF,WAAW,CAAC3O,CAAC,GAAG,QAAQ,GAAG2O,WAAW,CAACxT,CAAC,GAAG,MAAM,IAAI6P,OAAO,CAAChL,CAAC,GAAG2O,WAAW,CAAC3O,CAAC,CAAC,CAAC;MAC9H2O,WAAW,CAACxT,CAAC,GAAG6P,OAAO,CAAChL,CAAC,GAAG2O,WAAW,CAAC3O,CAAC;IAC7C;IAEA4K,QAAQ,CAACrR,IAAI,CAACyR,OAAO,CAAC;;IAEtB;IACA,IAAIV,QAAQ,CAACpB,IAAI,KAAK,QAAQ,EAAE;MAC5B,IAAIA,IAAI,KAAK,OAAO,EAAE;QAClB8B,OAAO,GAAGJ,QAAQ,CAACA,QAAQ,CAACtS,MAAM,GAAG,CAAC,CAAC;QACvC2V,OAAO,GAAG,CAACjD,OAAO,CAAChL,CAAC,GAAGgL,OAAO,CAAC7P,CAAC,IAAIkQ,SAAS;QAC7C,IAAI4C,OAAO,GAAG7D,cAAc,CAACG,UAAU,CAACC,MAAM,CAACgB,QAAQ,EAAE;UACrD8B,QAAQ,CAACsB,OAAO,CAAC5B,uEAAM,CAAC6B,yBAAyB,EAAE;YAAEC,MAAM,EAAE,IAAI;YAAEC,WAAW,EAAEd;UAAQ,CAAC,CAAC;QAC9F;MACJ;MACA;IACJ,CAAC,MAAM;MACH;MACA,IAAI3D,QAAQ,CAACqD,oBAAoB,IAAIrD,QAAQ,CAACqD,oBAAoB,GAAG,CAAC,EAAE;QACpE;QACA3C,OAAO,GAAGJ,QAAQ,CAACA,QAAQ,CAACtS,MAAM,GAAG,CAAC,CAAC;QACvC0H,CAAC,GAAGgL,OAAO,CAAChL,CAAC;;QAEb;QACAkO,qBAAqB,GAAG,CAAClO,CAAC,GAAIsK,QAAQ,CAACqD,oBAAoB,GAAGtC,SAAU,IAAIA,SAAS;;QAErF;QACAL,OAAO,GAAGJ,QAAQ,CAAC,CAAC,CAAC;QACrBqD,OAAO,GAAG,CAACjD,OAAO,CAAChL,CAAC,GAAGgL,OAAO,CAAC7P,CAAC,IAAIkQ,SAAS;QAC7C,OAAO4C,OAAO,GAAGC,qBAAqB,EAAE;UACpC;UACA,IAAI,CAACf,kBAAkB,CAAC6B,QAAQ,CAAC,CAAC,IAAI7B,kBAAkB,CAACP,OAAO,CAAC,CAAC,GAAGqB,OAAO,EAAE;YAC1E;UACJ;UACA;UACArD,QAAQ,CAACqE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;UACrBjE,OAAO,GAAGJ,QAAQ,CAAC,CAAC,CAAC;UACrBqD,OAAO,GAAG,CAACjD,OAAO,CAAChL,CAAC,GAAGgL,OAAO,CAAC7P,CAAC,IAAIkQ,SAAS;QACjD;MACJ;;MAEA;MACAjI,KAAK,GAAG;QACJY,KAAK,EAAE4G,QAAQ,CAAC,CAAC,CAAC,CAAC5K,CAAC,GAAGqL,SAAS;QAChCgD,GAAG,EAAGX,IAAI,CAACY,mBAAmB,GAAGjD,SAAS,GAAIJ,OAAO,CAACO;MAC1D,CAAC;MAED+C,SAAS,CAACrF,IAAI,EAAE9F,KAAK,EAAEmG,eAAe,CAACiF,aAAa,CAAC,CAAC,CAACC,YAAY,CAAC;IACxE;EAEJ;EAEA,SAASF,SAASA,CAACrF,IAAI,EAAE9F,KAAK,EAAEqL,YAAY,EAAE;IAC1C,IAAIvF,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAO,EAAE;MACtC;IACJ;IACA,MAAMgG,QAAQ,GAAGhC,WAAW,CAACiC,iBAAiB,CAACjG,IAAI,CAAC;IACpD,IAAI,CAACgG,QAAQ,IAAK9L,KAAK,CAACiL,GAAG,GAAGa,QAAQ,CAAC9L,KAAK,CAACiL,GAAI,EAAE;MAC/CtF,MAAM,CAACU,KAAK,CAAC,qBAAqB,GAAGrG,KAAK,CAACY,KAAK,GAAG,KAAK,GAAGZ,KAAK,CAACiL,GAAG,GAAG,GAAG,CAAC;MAC3EnB,WAAW,CAACkC,UAAU,CAAClG,IAAI,EAAEiE,kBAAkB,CAACP,OAAO,CAAC,CAAC,EAAE6B,YAAY,EAAErL,KAAK,CAAC;MAC/E+J,kBAAkB,CAACkC,iBAAiB,CAACnG,IAAI,CAAC;IAC9C;EACJ;;EAEA;EACA,SAASoG,YAAYA,CAACtH,MAAM,EAAEkB,IAAI,EAAE;IAChC,IAAIqG,MAAM,GAAG,CAAC;IACd,IAAI/W,CAAC,GAAG,CAAC;IAET,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwP,MAAM,CAACwH,KAAK,CAAClX,MAAM,EAAEE,CAAC,EAAE,EAAE;MACtC,IAAIwP,MAAM,CAACwH,KAAK,CAAChX,CAAC,CAAC,CAAC0Q,IAAI,KAAKA,IAAI,EAAE;QAC/B,OAAOqG,MAAM;MACjB;MACAA,MAAM,IAAIvH,MAAM,CAACwH,KAAK,CAAChX,CAAC,CAAC,CAACiX,IAAI;IAClC;IACA,OAAOF,MAAM;EACjB;EAEA,SAASG,eAAeA,CAAC5M,CAAC,EAAEyG,eAAe,EAAE;IACzC,IAAI/Q,CAAC;;IAEL;IACA;IACA,MAAMmX,OAAO,GAAGpC,QAAQ,CAACqC,WAAW,CAAC9M,CAAC,CAACwJ,QAAQ,CAAC;IAChD;IACA,MAAMuD,IAAI,GAAGF,OAAO,CAACG,KAAK,CAAC,MAAM,CAAC;IAClCD,IAAI,CAACE,QAAQ,GAAGjN,CAAC,CAACmI,OAAO,CAACb,cAAc,CAAC4F,SAAS,CAAC1G,KAAK,GAAG,CAAC;;IAE5D;IACA,IAAIoE,IAAI,GAAGiC,OAAO,CAACG,KAAK,CAAC,MAAM,CAAC;IAChC,MAAMG,IAAI,GAAGN,OAAO,CAACG,KAAK,CAAC,MAAM,CAAC;IAClC,IAAIpC,IAAI,KAAK,IAAI,EAAE;MACfA,IAAI,GAAGH,QAAQ,CAAC2C,aAAa,CAAC,MAAM,EAAED,IAAI,EAAEJ,IAAI,CAAC;MACjDnC,IAAI,CAACyC,OAAO,GAAG,CAAC;MAChBzC,IAAI,CAAC0C,KAAK,GAAG,CAAC;MACd1C,IAAI,CAACY,mBAAmB,GAAGrW,IAAI,CAACC,KAAK,CAAC4K,CAAC,CAACmI,OAAO,CAAC7B,SAAS,GAAGtG,CAAC,CAACmI,OAAO,CAACI,SAAS,CAAC;IACpF;IAEA,MAAMgF,IAAI,GAAGV,OAAO,CAACG,KAAK,CAAC,MAAM,CAAC;;IAElC;IACA;IACA,IAAIQ,IAAI,GAAGX,OAAO,CAACG,KAAK,CAAC,MAAM,CAAC;IAChC,IAAIQ,IAAI,EAAE;MACNA,IAAI,CAACC,OAAO,CAACf,KAAK,CAACP,MAAM,CAACqB,IAAI,CAACC,OAAO,CAACf,KAAK,CAACnK,OAAO,CAACiL,IAAI,CAAC,EAAE,CAAC,CAAC;MAC9DA,IAAI,GAAG,IAAI;IACf;IACA,IAAI7C,IAAI,GAAGkC,OAAO,CAACG,KAAK,CAAC,MAAM,CAAC;IAChCtC,WAAW,CAAC1K,CAAC,CAACmI,OAAO,EAAEwC,IAAI,EAAEC,IAAI,EAAEnE,eAAe,CAAC;IACnD,IAAIkE,IAAI,EAAE;MACNA,IAAI,CAAC8C,OAAO,CAACf,KAAK,CAACP,MAAM,CAACxB,IAAI,CAAC8C,OAAO,CAACf,KAAK,CAACnK,OAAO,CAACoI,IAAI,CAAC,EAAE,CAAC,CAAC;MAC9DA,IAAI,GAAG,IAAI;IACf;;IAEA;IACA;IACA;IACA,MAAM+C,MAAM,GAAGb,OAAO,CAACG,KAAK,CAAC,QAAQ,CAAC;IACtC,IAAIU,MAAM,KAAK,IAAI,EAAE;MACjBA,MAAM,CAACtH,IAAI,GAAG,MAAM;MACpBsH,MAAM,CAACC,QAAQ,GAAGna,SAAS;MAE3B,IAAIoa,IAAI,GAAGf,OAAO,CAACG,KAAK,CAAC,MAAM,CAAC;MAChC,IAAIY,IAAI,KAAK,IAAI,EAAE;QACf;QACAA,IAAI,GAAGnD,QAAQ,CAAC2C,aAAa,CAAC,MAAM,EAAED,IAAI,CAAC;QAC3CS,IAAI,CAACP,OAAO,GAAG,CAAC;QAChBO,IAAI,CAACN,KAAK,GAAG,CAAC;QACdM,IAAI,CAACC,WAAW,GAAG,CAAC;QACpBD,IAAI,CAACnB,MAAM,GAAG,CAAC,CAAC,CAAC;QAEjB,MAAMqB,IAAI,GAAGrD,QAAQ,CAAC2C,aAAa,CAAC,MAAM,EAAED,IAAI,CAAC;QACjDW,IAAI,CAACT,OAAO,GAAG,CAAC;QAChBS,IAAI,CAACR,KAAK,GAAG,CAAC;QACdQ,IAAI,CAACC,YAAY,GAAGL,MAAM,CAACK,YAAY;QACvCD,IAAI,CAACE,wBAAwB,GAAG,CAAC;QACjCF,IAAI,CAACG,gBAAgB,GAAG,EAAE;QAE1B,IAAIP,MAAM,CAACJ,KAAK,GAAG,IAAI,EAAE;UACrB;UACA,KAAK5X,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgY,MAAM,CAACK,YAAY,EAAErY,CAAC,IAAI,CAAC,EAAE;YACzC;YACA;YACAoY,IAAI,CAACG,gBAAgB,CAACvY,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC,GAAGgY,MAAM,CAACzC,KAAK,CAACvV,CAAC,CAAC,CAACwY,eAAgB;UACzE;QACJ,CAAC,MAAM;UACH;UACAJ,IAAI,CAACE,wBAAwB,GAAG,CAAC;QACrC;MACJ;IACJ;IAEAjB,IAAI,CAACO,KAAK,IAAI,QAAQ,CAAC,CAAC;IACxBP,IAAI,CAACO,KAAK,IAAI,QAAQ,CAAC,CAAC;IACxBC,IAAI,CAACD,KAAK,IAAI,QAAQ,CAAC,CAAC;;IAExB;IACA,MAAMa,IAAI,GAAGtB,OAAO,CAACG,KAAK,CAAC,MAAM,CAAC;IAClC,IAAIxX,MAAM,GAAG2Y,IAAI,CAACC,SAAS,CAAC,CAAC;IAC7Bb,IAAI,CAACc,WAAW,GAAG7Y,MAAM,GAAG,CAAC;;IAE7B;IACA,IAAIoY,IAAI,GAAGf,OAAO,CAACG,KAAK,CAAC,MAAM,CAAC;IAChC,IAAIY,IAAI,KAAK,IAAI,EAAE;MACf,IAAIU,aAAa,GAAG9B,YAAY,CAAC2B,IAAI,EAAE,MAAM,CAAC;MAC9C,IAAII,aAAa,GAAG/B,YAAY,CAACW,IAAI,EAAE,MAAM,CAAC;MAC9C;MACAS,IAAI,CAACnB,MAAM,CAAC,CAAC,CAAC,GAAG6B,aAAa,GAAGC,aAAa,GAAG,EAAE,CAAC,CAAC;IACzD;;IAEA;IACAvO,CAAC,CAACwJ,QAAQ,GAAGqD,OAAO,CAAC2B,KAAK,CAAC,CAAC;EAChC;EAEA,SAASC,iBAAiBA,CAACzO,CAAC,EAAEyG,eAAe,EAAE;IAC3C;IACA;IACA,IAAI,CAACzG,CAAC,CAACwJ,QAAQ,EAAE;MACb,MAAM,IAAI/O,KAAK,CAAC,iCAAiC,CAAC;IACtD;IAEA,MAAMoS,OAAO,GAAGpC,QAAQ,CAACqC,WAAW,CAAC9M,CAAC,CAACwJ,QAAQ,CAAC;IAChD;IACA,MAAMuD,IAAI,GAAGF,OAAO,CAACG,KAAK,CAAC,MAAM,CAAC;IAClCD,IAAI,CAACE,QAAQ,GAAGjN,CAAC,CAACmI,OAAO,CAACb,cAAc,CAAC4F,SAAS,CAAC1G,KAAK,GAAG,CAAC;;IAE5D;IACA,IAAIoE,IAAI,GAAGiC,OAAO,CAACG,KAAK,CAAC,MAAM,CAAC;IAChC,IAAIG,IAAI,GAAGN,OAAO,CAACG,KAAK,CAAC,MAAM,CAAC;IAChC,IAAIpC,IAAI,KAAK,IAAI,EAAE;MACfA,IAAI,GAAGH,QAAQ,CAAC2C,aAAa,CAAC,MAAM,EAAED,IAAI,EAAEJ,IAAI,CAAC;MACjDnC,IAAI,CAACyC,OAAO,GAAG,CAAC;MAChBzC,IAAI,CAAC0C,KAAK,GAAG,CAAC;MACd1C,IAAI,CAACY,mBAAmB,GAAGrW,IAAI,CAACC,KAAK,CAAC4K,CAAC,CAACmI,OAAO,CAAC7B,SAAS,GAAGtG,CAAC,CAACmI,OAAO,CAACI,SAAS,CAAC;IACpF;IAEA,IAAIoC,IAAI,GAAGkC,OAAO,CAACG,KAAK,CAAC,MAAM,CAAC;IAChCtC,WAAW,CAAC1K,CAAC,CAACmI,OAAO,EAAEwC,IAAI,EAAEC,IAAI,EAAEnE,eAAe,CAAC;IACnD,IAAIkE,IAAI,EAAE;MACNA,IAAI,CAAC8C,OAAO,CAACf,KAAK,CAACP,MAAM,CAACxB,IAAI,CAAC8C,OAAO,CAACf,KAAK,CAACnK,OAAO,CAACoI,IAAI,CAAC,EAAE,CAAC,CAAC;MAC9DA,IAAI,GAAG,IAAI;IACf;EACJ;EAEA,SAAS3D,OAAOA,CAAA,EAAG;IACf,OAAOZ,IAAI;EACf;EAEApD,QAAQ,GAAG;IACP4J,eAAe;IACf6B,iBAAiB;IACjBzH;EACJ,CAAC;EAEDH,KAAK,CAAC,CAAC;EACP,OAAO7D,QAAQ;AACnB;AAEAmH,wBAAwB,CAAC5F,qBAAqB,GAAG,0BAA0B;AAC3E,+DAAexB,6DAAY,CAACsB,eAAe,CAAC8F,wBAAwB,CAAC;;;;;;;;;;;;;ACrWrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAC8C;AACK;;AAEnD;AACA;AACA;AACA;AACA;AACA,SAASuE,wBAAwBA,CAAClJ,MAAM,EAAE;EACtCA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACrB,MAAMmJ,YAAY,GAAG,CAAC;EACtB,MAAMC,YAAY,GAAG,CAAC;EACtB,MAAMC,SAAS,GAAGrJ,MAAM,CAACqJ,SAAS;EAClC,MAAMpE,QAAQ,GAAGjF,MAAM,CAACiF,QAAQ;EAEhC,IAAIqE,oBAAoB,GAAGtJ,MAAM,CAACsJ,oBAAoB;EACtD,IAAI9L,QAAQ,EACR0E,MAAM,EACNqH,aAAa,EACbzH,cAAc,EACd0H,iBAAiB,EACjBzG,SAAS,EACT0G,OAAO;EAEX,SAASC,aAAaA,CAACrC,OAAO,EAAE;IAC5B,IAAIsC,IAAI,GAAG1E,QAAQ,CAAC2E,SAAS,CAAC,MAAM,EAAEvC,OAAO,CAAC;IAC9CsC,IAAI,CAACE,WAAW,GAAG,MAAM;IACzBF,IAAI,CAACG,aAAa,GAAG,CAAC,CAAC,CAAC;IACxBH,IAAI,CAACI,iBAAiB,GAAG,EAAE,CAAC,CAAC;IAC7BJ,IAAI,CAACI,iBAAiB,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;IACpCJ,IAAI,CAACI,iBAAiB,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;IACpCJ,IAAI,CAACI,iBAAiB,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;;IAEpC,OAAOJ,IAAI;EACf;EAEA,SAASK,aAAaA,CAAC3C,OAAO,EAAE;IAE5B;IACA,IAAI4C,IAAI,GAAGhF,QAAQ,CAAC2E,SAAS,CAAC,MAAM,EAAEvC,OAAO,CAAC;;IAE9C;IACA6C,aAAa,CAACD,IAAI,CAAC;;IAEnB;IACA,IAAIE,IAAI,GAAGlF,QAAQ,CAAC2E,SAAS,CAAC,MAAM,EAAEK,IAAI,CAAC;;IAE3C;IACAG,aAAa,CAACD,IAAI,CAAC;;IAEnB;IACA,IAAIE,IAAI,GAAGpF,QAAQ,CAAC2E,SAAS,CAAC,MAAM,EAAEO,IAAI,CAAC;;IAE3C;IACAG,aAAa,CAACD,IAAI,CAAC;;IAEnB;IACAE,aAAa,CAACF,IAAI,CAAC;;IAEnB;IACA,IAAIG,IAAI,GAAGvF,QAAQ,CAAC2E,SAAS,CAAC,MAAM,EAAES,IAAI,CAAC;IAE3C,QAAQd,aAAa,CAAC3I,IAAI;MACtB,KAAKyI,SAAS,CAACoB,KAAK;QAChB;QACAC,aAAa,CAACF,IAAI,CAAC;QACnB;MACJ,KAAKnB,SAAS,CAACsB,KAAK;QAChB;QACAC,aAAa,CAACJ,IAAI,CAAC;QACnB;MACJ;QACI;IACR;;IAEA;IACA,IAAIK,IAAI,GAAG5F,QAAQ,CAAC2E,SAAS,CAAC,MAAM,EAAEY,IAAI,CAAC;;IAE3C;IACAM,aAAa,CAACD,IAAI,CAAC;;IAEnB;IACA,IAAIE,IAAI,GAAG9F,QAAQ,CAAC2E,SAAS,CAAC,MAAM,EAAEY,IAAI,CAAC;;IAE3C;IACA;;IAEA;IACA,IAAIQ,IAAI,GAAG/F,QAAQ,CAAC2C,aAAa,CAAC,MAAM,EAAEmD,IAAI,CAAC;IAC/CC,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEvC;IACA,IAAIC,IAAI,GAAGjG,QAAQ,CAAC2C,aAAa,CAAC,MAAM,EAAEmD,IAAI,CAAC;IAC/CG,IAAI,CAACD,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEvC;IACA,IAAIE,IAAI,GAAGlG,QAAQ,CAAC2C,aAAa,CAAC,MAAM,EAAEmD,IAAI,CAAC;IAC/CI,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEvC;IACA,IAAIG,IAAI,GAAGnG,QAAQ,CAAC2C,aAAa,CAAC,MAAM,EAAEmD,IAAI,CAAC;IAC/CK,IAAI,CAACH,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEnD;IACAI,aAAa,CAACN,IAAI,CAAC;;IAEnB;IACA,IAAIO,IAAI,GAAGrG,QAAQ,CAAC2E,SAAS,CAAC,MAAM,EAAEK,IAAI,CAAC;;IAE3C;IACAsB,aAAa,CAACD,IAAI,CAAC;IAEnB,IAAI9B,iBAAiB,IAAIF,oBAAoB,EAAE;MAC3C,IAAIkC,WAAW,GAAGlC,oBAAoB,CAACmC,kDAAkD,CAACjC,iBAAiB,CAAC;MAC5GkC,uCAAuC,CAACzB,IAAI,EAAEuB,WAAW,CAAC;IAC9D;EACJ;EAEA,SAAStB,aAAaA,CAACD,IAAI,EAAE;IAEzB,IAAI0B,IAAI,GAAG1G,QAAQ,CAAC2C,aAAa,CAAC,MAAM,EAAEqC,IAAI,CAAC;IAE/C0B,IAAI,CAAC9D,OAAO,GAAG,CAAC,CAAC,CAAC;;IAElB8D,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC,CAAC;IACxBD,IAAI,CAACE,iBAAiB,GAAG,CAAC,CAAC,CAAC;IAC5BF,IAAI,CAAC5I,SAAS,GAAGA,SAAS,CAAC,CAAC;IAC5B4I,IAAI,CAACzI,QAAQ,GAAGhB,MAAM,CAACgB,QAAQ,KAAK5M,QAAQ,GAAG,kBAAkB,GAAG3G,IAAI,CAACmc,KAAK,CAAC5J,MAAM,CAACgB,QAAQ,GAAGH,SAAS,CAAC,CAAC,CAAC;IAC7G4I,IAAI,CAACI,IAAI,GAAG,GAAG,CAAC,CAAC;IACjBJ,IAAI,CAACK,MAAM,GAAG,GAAG,CAAC,CAAC;IACnBL,IAAI,CAACM,SAAS,GAAG,CAAC;IAClBN,IAAI,CAACO,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;IAC3BP,IAAI,CAACQ,MAAM,GAAG,CACV,CAAC,EAAE,CAAC,EAAE,CAAC;IAAE;IACT,CAAC,EAAE,CAAC,EAAE,CAAC;IAAE;IACT,CAAC,EAAE,CAAC,EAAE,KAAK,CACd;IACDR,IAAI,CAACS,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrCT,IAAI,CAACU,aAAa,GAAG5C,OAAO,GAAG,CAAC,CAAC,CAAC;;IAElC,OAAOkC,IAAI;EACf;EAEA,SAASvB,aAAaA,CAACD,IAAI,EAAE;IAEzB,IAAImC,IAAI,GAAGrH,QAAQ,CAAC2C,aAAa,CAAC,MAAM,EAAEuC,IAAI,CAAC;IAE/CmC,IAAI,CAACzE,OAAO,GAAG,CAAC,CAAC,CAAC;IAClByE,IAAI,CAACxE,KAAK,GAAG,GAAG;IAAG;IACf,GAAG;IAAG;IACN,GAAG,CAAC,CAAC;;IAETwE,IAAI,CAACV,aAAa,GAAG,CAAC,CAAC,CAAC;IACxBU,IAAI,CAACT,iBAAiB,GAAG,CAAC,CAAC,CAAC;IAC5BS,IAAI,CAAC7E,QAAQ,GAAGgC,OAAO,CAAC,CAAC;IACzB6C,IAAI,CAACL,SAAS,GAAG,CAAC;IAClBK,IAAI,CAACpJ,QAAQ,GAAGhB,MAAM,CAACgB,QAAQ,KAAK5M,QAAQ,GAAG,kBAAkB,GAAG3G,IAAI,CAACmc,KAAK,CAAC5J,MAAM,CAACgB,QAAQ,GAAGH,SAAS,CAAC,CAAC,CAAC;IAC7GuJ,IAAI,CAACJ,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;IAC3BI,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC,CAAC;IAChBD,IAAI,CAACE,eAAe,GAAG,CAAC,CAAC,CAAC;IAC1BF,IAAI,CAACN,MAAM,GAAG,GAAG,CAAC,CAAC;IACnBM,IAAI,CAACG,SAAS,GAAG,CAAC;IAClBH,IAAI,CAACH,MAAM,GAAG,CACV,CAAC,EAAE,CAAC,EAAE,CAAC;IAAE;IACT,CAAC,EAAE,CAAC,EAAE,CAAC;IAAE;IACT,CAAC,EAAE,CAAC,EAAE,KAAK,CACd;IACDG,IAAI,CAACI,KAAK,GAAG5K,cAAc,CAAC4K,KAAK,CAAC,CAAC;IACnCJ,IAAI,CAACK,MAAM,GAAG7K,cAAc,CAAC6K,MAAM,CAAC,CAAC;;IAErC,OAAOL,IAAI;EACf;EAEA,SAAShC,aAAaA,CAACD,IAAI,EAAE;IAEzB,IAAIuC,IAAI,GAAG3H,QAAQ,CAAC2C,aAAa,CAAC,MAAM,EAAEyC,IAAI,CAAC;IAE/CuC,IAAI,CAAC/E,OAAO,GAAG,CAAC,CAAC,CAAC;;IAElB+E,IAAI,CAAChB,aAAa,GAAG,CAAC,CAAC,CAAC;IACxBgB,IAAI,CAACf,iBAAiB,GAAG,CAAC,CAAC,CAAC;IAC5Be,IAAI,CAAC7J,SAAS,GAAGA,SAAS,CAAC,CAAC;IAC5B6J,IAAI,CAAC1J,QAAQ,GAAGhB,MAAM,CAACgB,QAAQ,KAAK5M,QAAQ,GAAG,kBAAkB,GAAG3G,IAAI,CAACmc,KAAK,CAAC5J,MAAM,CAACgB,QAAQ,GAAGH,SAAS,CAAC,CAAC,CAAC;IAC7G6J,IAAI,CAACC,QAAQ,GAAGtD,aAAa,CAACuD,IAAI,IAAI,KAAK,CAAC,CAAC;IAC7CF,IAAI,CAACR,WAAW,GAAG,CAAC;IAEpB,OAAOQ,IAAI;EACf;EAEA,SAASrC,aAAaA,CAACF,IAAI,EAAE;IAEzB,IAAI0C,IAAI,GAAG9H,QAAQ,CAAC2C,aAAa,CAAC,MAAM,EAAEyC,IAAI,CAAC;IAE/C0C,IAAI,CAACX,WAAW,GAAG,CAAC;IACpB,QAAQ7C,aAAa,CAAC3I,IAAI;MACtB,KAAKyI,SAAS,CAACoB,KAAK;QAChBsC,IAAI,CAACC,YAAY,GAAG,MAAM;QAC1B;MACJ,KAAK3D,SAAS,CAACsB,KAAK;QAChBoC,IAAI,CAACC,YAAY,GAAG,MAAM;QAC1B;MACJ;QACID,IAAI,CAACC,YAAY,GAAG,MAAM;QAC1B;IACR;IACAD,IAAI,CAAClP,IAAI,GAAGiE,cAAc,CAACmL,EAAE;IAC7BF,IAAI,CAACG,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAEzB,OAAOH,IAAI;EACf;EAEA,SAASrC,aAAaA,CAACF,IAAI,EAAE;IAEzB,IAAI2C,IAAI,GAAGlI,QAAQ,CAAC2C,aAAa,CAAC,MAAM,EAAE4C,IAAI,CAAC;IAE/C2C,IAAI,CAACrF,KAAK,GAAG,CAAC;IAEdqF,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC,CAAC;IACvBD,IAAI,CAACE,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAE1B,OAAOF,IAAI;EACf;EAEA,SAASvC,aAAaA,CAACJ,IAAI,EAAE;IAEzB,IAAI8C,IAAI,GAAGrI,QAAQ,CAAC2C,aAAa,CAAC,MAAM,EAAE4C,IAAI,CAAC;IAE/C8C,IAAI,CAACxF,KAAK,GAAG,CAAC;IAEdwF,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC,CAAC;IAClBD,IAAI,CAACJ,QAAQ,GAAG,CAAC;IAEjB,OAAOI,IAAI;EACf;EAEA,SAASxC,aAAaA,CAACD,IAAI,EAAE;IAEzB,IAAI2C,IAAI,GAAGvI,QAAQ,CAAC2C,aAAa,CAAC,MAAM,EAAEiD,IAAI,CAAC;IAE/C2C,IAAI,CAACnF,WAAW,GAAG,CAAC;IACpBmF,IAAI,CAAChI,OAAO,GAAG,EAAE;IAEjB,IAAInC,GAAG,GAAG4B,QAAQ,CAAC2C,aAAa,CAAC,MAAM,EAAE4F,IAAI,EAAE,KAAK,CAAC;IACrDnK,GAAG,CAACoK,QAAQ,GAAG,EAAE;IACjBpK,GAAG,CAACyE,KAAK,GAAG,CAAC;IAEb0F,IAAI,CAAChI,OAAO,CAACvU,IAAI,CAACoS,GAAG,CAAC;IAEtB,OAAOmK,IAAI;EACf;EAEA,SAASnC,aAAaA,CAACN,IAAI,EAAE;IAEzB,IAAI2C,IAAI,GAAGzI,QAAQ,CAAC2C,aAAa,CAAC,MAAM,EAAEmD,IAAI,CAAC;IAE/C2C,IAAI,CAAClI,OAAO,GAAG,EAAE;IACjB,QAAQ+D,aAAa,CAAC3I,IAAI;MACtB,KAAKyI,SAAS,CAACoB,KAAK;MACpB,KAAKpB,SAAS,CAACsB,KAAK;QAChB+C,IAAI,CAAClI,OAAO,CAACvU,IAAI,CAAC0c,iBAAiB,CAACD,IAAI,CAAC,CAAC;QAC1C;MACJ;QACI;IACR;IAEAA,IAAI,CAACrF,WAAW,GAAGqF,IAAI,CAAClI,OAAO,CAACxV,MAAM,CAAC,CAAC;IACxC,OAAO0d,IAAI;EACf;EAEA,SAASC,iBAAiBA,CAACD,IAAI,EAAE;IAC7B,IAAIE,KAAK,GAAG9L,cAAc,CAAC+L,MAAM,CAACC,SAAS,CAAC,CAAC,EAAEhM,cAAc,CAAC+L,MAAM,CAAC9Q,OAAO,CAAC,GAAG,CAAC,CAAC;IAElF,QAAQ6Q,KAAK;MACT,KAAK,MAAM;QACP,OAAOG,0BAA0B,CAACL,IAAI,EAAEE,KAAK,CAAC;MAClD,KAAK,MAAM;QACP,OAAOI,yBAAyB,CAACN,IAAI,EAAEE,KAAK,CAAC;MACjD;QACI,MAAM;UACFK,IAAI,EAAExJ,4DAAS,CAACyJ,0BAA0B;UAC1CC,OAAO,EAAE1J,4DAAS,CAAC2J,6BAA6B;UAChDC,IAAI,EAAE;YACFT,KAAK,EAAEA;UACX;QACJ,CAAC;IACT;EACJ;EAEA,SAASG,0BAA0BA,CAACL,IAAI,EAAEE,KAAK,EAAE;IAC7C,IAAIU,IAAI;IAER,IAAI9E,iBAAiB,EAAE;MACnB8E,IAAI,GAAGrJ,QAAQ,CAAC2E,SAAS,CAAC,MAAM,EAAE8D,IAAI,EAAE,KAAK,CAAC;IAClD,CAAC,MAAM;MACHY,IAAI,GAAGrJ,QAAQ,CAAC2E,SAAS,CAAC,MAAM,EAAE8D,IAAI,EAAE,KAAK,CAAC;IAClD;;IAEA;IACAY,IAAI,CAACrC,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC/CqC,IAAI,CAACC,oBAAoB,GAAG,CAAC;;IAE7B;IACAD,IAAI,CAACE,YAAY,GAAG,CAAC;IACrBF,IAAI,CAACpC,SAAS,GAAG,CAAC;IAClBoC,IAAI,CAACG,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7BH,IAAI,CAAC3B,MAAM,GAAG7K,cAAc,CAAC6K,MAAM;IACnC2B,IAAI,CAAC5B,KAAK,GAAG5K,cAAc,CAAC4K,KAAK;IACjC4B,IAAI,CAACI,eAAe,GAAG,EAAE,CAAC,CAAC;IAC3BJ,IAAI,CAACK,cAAc,GAAG,EAAE,CAAC,CAAC;IAC1BL,IAAI,CAAC7B,SAAS,GAAG,CAAC;IAClB6B,IAAI,CAACM,WAAW,GAAG,CAAC,CAAC,CAAC;IACtBN,IAAI,CAACO,cAAc,GAAG,CAClB,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;IAAE;IAChD,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9C,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC9C,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CACjD;IACDP,IAAI,CAACQ,KAAK,GAAG,MAAM,CAAC,CAAC;IACrBR,IAAI,CAACS,YAAY,GAAG,KAAK;IACzBT,IAAI,CAACtO,MAAM,GAAGgP,6BAA6B,CAAC,CAAC;IAC7C,IAAIxF,iBAAiB,EAAE;MACnB;MACA,IAAIyF,IAAI,GAAGhK,QAAQ,CAAC2E,SAAS,CAAC,MAAM,EAAE0E,IAAI,CAAC;;MAE3C;MACAY,uBAAuB,CAACD,IAAI,EAAErB,KAAK,CAAC;;MAEpC;MACAuB,mBAAmB,CAACF,IAAI,CAAC;;MAEzB;MACAG,0BAA0B,CAACH,IAAI,CAAC;IACpC;IAEA,OAAOX,IAAI;EACf;EAEA,SAASU,6BAA6BA,CAAA,EAAG;IAErC,IAAIK,IAAI,GAAG,IAAI;IACf,IAAIC,UAAU,GAAG,EAAE,CAAC,CAAC;;IAErB;IACA,IAAIC,GAAG,GAAG,EAAE;IACZ,IAAIC,GAAG,GAAG,EAAE;IACZ,IAAIC,oBAAoB,GAAG,CAAC;IAC5B,IAAIC,kBAAkB,GAAG,CAAC;IAC1B,IAAIC,qBAAqB,GAAG,CAAC;IAE7B,IAAIC,KAAK,GAAG9N,cAAc,CAAC+N,gBAAgB,CAAChT,KAAK,CAAC,UAAU,CAAC,CAACjK,KAAK,CAAC,CAAC,CAAC;IACtE,IAAIkd,SAAS,EAAEC,QAAQ;IAEvB,KAAK,IAAI7f,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0f,KAAK,CAAC5f,MAAM,EAAEE,CAAC,EAAE,EAAE;MACnC4f,SAAS,GAAGE,iBAAiB,CAACJ,KAAK,CAAC1f,CAAC,CAAC,CAAC;MAEvC6f,QAAQ,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;MAE9B,QAAQC,QAAQ;QACZ,KAAK5G,YAAY;UACboG,GAAG,CAACte,IAAI,CAAC6e,SAAS,CAAC;UACnBR,UAAU,IAAIQ,SAAS,CAAC9f,MAAM,GAAG,CAAC,CAAC,CAAC;UACpC;QACJ,KAAKoZ,YAAY;UACboG,GAAG,CAACve,IAAI,CAAC6e,SAAS,CAAC;UACnBR,UAAU,IAAIQ,SAAS,CAAC9f,MAAM,GAAG,CAAC,CAAC,CAAC;UACpC;QACJ;UACI;MACR;IACJ;;IAEA;IACA,IAAIuf,GAAG,CAACvf,MAAM,GAAG,CAAC,EAAE;MAChByf,oBAAoB,GAAGF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChCI,qBAAqB,GAAGJ,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjCG,kBAAkB,GAAGH,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC;;IAEA;IACAF,IAAI,GAAG,IAAIY,UAAU,CAACX,UAAU,CAAC;IAEjC,IAAIpf,CAAC,GAAG,CAAC;IACT;IACAmf,IAAI,CAACnf,CAAC,EAAE,CAAC,GAAG,CAACof,UAAU,GAAG,UAAU,KAAK,EAAE;IAC3CD,IAAI,CAACnf,CAAC,EAAE,CAAC,GAAG,CAACof,UAAU,GAAG,UAAU,KAAK,EAAE;IAC3CD,IAAI,CAACnf,CAAC,EAAE,CAAC,GAAG,CAACof,UAAU,GAAG,UAAU,KAAK,CAAC;IAC1CD,IAAI,CAACnf,CAAC,EAAE,CAAC,GAAIof,UAAU,GAAG,UAAW;IACrCD,IAAI,CAACa,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAEhgB,CAAC,CAAC,CAAC,CAAC;IACvCA,CAAC,IAAI,CAAC;IACNmf,IAAI,CAACnf,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACfmf,IAAI,CAACnf,CAAC,EAAE,CAAC,GAAGuf,oBAAoB;IAChCJ,IAAI,CAACnf,CAAC,EAAE,CAAC,GAAGyf,qBAAqB;IACjCN,IAAI,CAACnf,CAAC,EAAE,CAAC,GAAGwf,kBAAkB;IAC9BL,IAAI,CAACnf,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAClBmf,IAAI,CAACnf,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGqf,GAAG,CAACvf,MAAM,CAAC,CAAC;IAC/B,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6f,GAAG,CAACvf,MAAM,EAAEN,CAAC,EAAE,EAAE;MACjC2f,IAAI,CAACnf,CAAC,EAAE,CAAC,GAAG,CAACqf,GAAG,CAAC7f,CAAC,CAAC,CAACM,MAAM,GAAG,MAAM,KAAK,CAAC;MACzCqf,IAAI,CAACnf,CAAC,EAAE,CAAC,GAAIqf,GAAG,CAAC7f,CAAC,CAAC,CAACM,MAAM,GAAG,MAAO;MACpCqf,IAAI,CAACa,GAAG,CAACX,GAAG,CAAC7f,CAAC,CAAC,EAAEQ,CAAC,CAAC;MACnBA,CAAC,IAAIqf,GAAG,CAAC7f,CAAC,CAAC,CAACM,MAAM;IACtB;IACAqf,IAAI,CAACnf,CAAC,EAAE,CAAC,GAAGsf,GAAG,CAACxf,MAAM,CAAC,CAAC;IACxB,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8f,GAAG,CAACxf,MAAM,EAAEN,CAAC,EAAE,EAAE;MACjC2f,IAAI,CAACnf,CAAC,EAAE,CAAC,GAAG,CAACsf,GAAG,CAAC9f,CAAC,CAAC,CAACM,MAAM,GAAG,MAAM,KAAK,CAAC;MACzCqf,IAAI,CAACnf,CAAC,EAAE,CAAC,GAAIsf,GAAG,CAAC9f,CAAC,CAAC,CAACM,MAAM,GAAG,MAAO;MACpCqf,IAAI,CAACa,GAAG,CAACV,GAAG,CAAC9f,CAAC,CAAC,EAAEQ,CAAC,CAAC;MACnBA,CAAC,IAAIsf,GAAG,CAAC9f,CAAC,CAAC,CAACM,MAAM;IACtB;IAEA,OAAOqf,IAAI;EACf;EAEA,SAASrB,yBAAyBA,CAACN,IAAI,EAAEE,KAAK,EAAE;IAC5C,IAAIuC,IAAI;IAER,IAAI3G,iBAAiB,EAAE;MACnB2G,IAAI,GAAGlL,QAAQ,CAAC2E,SAAS,CAAC,MAAM,EAAE8D,IAAI,EAAE,KAAK,CAAC;IAClD,CAAC,MAAM;MACHyC,IAAI,GAAGlL,QAAQ,CAAC2E,SAAS,CAAC,MAAM,EAAE8D,IAAI,EAAE,KAAK,CAAC;IAClD;;IAEA;IACAyC,IAAI,CAAClE,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC/CkE,IAAI,CAAC5B,oBAAoB,GAAG,CAAC;;IAE7B;IACA4B,IAAI,CAACjE,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;IAC3BiE,IAAI,CAACC,YAAY,GAAGtO,cAAc,CAACuO,aAAa;IAChDF,IAAI,CAACG,UAAU,GAAG,EAAE;IACpBH,IAAI,CAAC/D,WAAW,GAAG,CAAC;IACpB+D,IAAI,CAACI,UAAU,GAAG,CAAC;IACnBJ,IAAI,CAACK,UAAU,GAAG1O,cAAc,CAAC2O,iBAAiB,IAAI,EAAE;IAExDN,IAAI,CAACO,IAAI,GAAGC,0BAA0B,CAAC,CAAC;IAExC,IAAInH,iBAAiB,EAAE;MACnB;MACA,IAAIyF,IAAI,GAAGhK,QAAQ,CAAC2E,SAAS,CAAC,MAAM,EAAEuG,IAAI,CAAC;;MAE3C;MACAjB,uBAAuB,CAACD,IAAI,EAAErB,KAAK,CAAC;;MAEpC;MACAuB,mBAAmB,CAACF,IAAI,CAAC;;MAEzB;MACAG,0BAA0B,CAACH,IAAI,CAAC;IACpC;IAEA,OAAOkB,IAAI;EACf;EAEA,SAASQ,0BAA0BA,CAAA,EAAG;IAElC;IACA,IAAIC,mBAAmB,GAAGZ,iBAAiB,CAAClO,cAAc,CAAC+N,gBAAgB,CAAC;;IAE5E;IACA;IACA;IACA;IACA;IACA,IAAIgB,UAAU,GAAG,EAAE,GAAGD,mBAAmB,CAAC5gB,MAAM;IAChD,IAAI0gB,IAAI,GAAG,IAAIT,UAAU,CAACY,UAAU,CAAC;IAErC,IAAI3gB,CAAC,GAAG,CAAC;IACT;IACAwgB,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG,CAAC2gB,UAAU,GAAG,UAAU,KAAK,EAAE,CAAC,CAAC;IAC7CH,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG,CAAC2gB,UAAU,GAAG,UAAU,KAAK,EAAE,CAAC,CAAC;IAC7CH,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG,CAAC2gB,UAAU,GAAG,UAAU,KAAK,CAAC,CAAC,CAAC;IAC5CH,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAI2gB,UAAU,GAAG,UAAW,CAAC,CAAC;IACvCH,IAAI,CAACR,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAEhgB,CAAC,CAAC,CAAC,CAAC;IACvCA,CAAC,IAAI,CAAC;IACNwgB,IAAI,CAACR,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEhgB,CAAC,CAAC,CAAC,CAAC;IAC3BA,CAAC,IAAI,CAAC;IACN;IACAwgB,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAClBwgB,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG0gB,mBAAmB,CAAC5gB,MAAM,CAAC,CAAC;IAC7C0gB,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG,CAACuZ,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;IACrCiH,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAIuZ,OAAO,GAAG,MAAO,CAAC,CAAC;IAChCiH,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;;IAEf;IACAwgB,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAClBwgB,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG0gB,mBAAmB,CAAC5gB,MAAM,CAAC,CAAC;IAC7C0gB,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAClBwgB,IAAI,CAACxgB,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;IACrBwgB,IAAI,CAACxgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACnBwgB,IAAI,CAACxgB,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAChBwgB,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAClBwgB,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAClBwgB,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAClBwgB,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG,CAAC4R,cAAc,CAACqB,SAAS,GAAG,UAAU,KAAK,EAAE,CAAC,CAAC;IAC3DuN,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG,CAAC4R,cAAc,CAACqB,SAAS,GAAG,UAAU,KAAK,EAAE,CAAC,CAAC;IAC3DuN,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG,CAAC4R,cAAc,CAACqB,SAAS,GAAG,UAAU,KAAK,CAAC,CAAC,CAAC;IAC1DuN,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAI4R,cAAc,CAACqB,SAAS,GAAG,UAAW,CAAC,CAAC;IACrDuN,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG,CAAC4R,cAAc,CAACqB,SAAS,GAAG,UAAU,KAAK,EAAE,CAAC,CAAC;IAC3DuN,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG,CAAC4R,cAAc,CAACqB,SAAS,GAAG,UAAU,KAAK,EAAE,CAAC,CAAC;IAC3DuN,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG,CAAC4R,cAAc,CAACqB,SAAS,GAAG,UAAU,KAAK,CAAC,CAAC,CAAC;IAC1DuN,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAI4R,cAAc,CAACqB,SAAS,GAAG,UAAW,CAAC,CAAC;;IAErD;IACAuN,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IAClBwgB,IAAI,CAACxgB,CAAC,EAAE,CAAC,GAAG0gB,mBAAmB,CAAC5gB,MAAM,CAAC,CAAC;IACxC0gB,IAAI,CAACR,GAAG,CAACU,mBAAmB,EAAE1gB,CAAC,CAAC,CAAC,CAAC;;IAElC,OAAOwgB,IAAI;EACf;EAEA,SAASxB,uBAAuBA,CAACD,IAAI,EAAErB,KAAK,EAAE;IAC1C,IAAIkD,IAAI,GAAG7L,QAAQ,CAAC2E,SAAS,CAAC,MAAM,EAAEqF,IAAI,CAAC;IAC3C6B,IAAI,CAACC,WAAW,GAAGC,gBAAgB,CAACpD,KAAK,CAAC;EAC9C;EAEA,SAASuB,mBAAmBA,CAACF,IAAI,EAAE;IAC/B,IAAIgC,IAAI,GAAGhM,QAAQ,CAAC2C,aAAa,CAAC,MAAM,EAAEqH,IAAI,CAAC;IAE/CgC,IAAI,CAACnJ,KAAK,GAAG,CAAC;IACdmJ,IAAI,CAACpJ,OAAO,GAAG,CAAC;IAChBoJ,IAAI,CAACC,WAAW,GAAG,UAAU,CAAC,CAAC;IAC/BD,IAAI,CAACE,cAAc,GAAG,UAAU,CAAC,CAAC;EACtC;EAEA,SAAS/B,0BAA0BA,CAACH,IAAI,EAAE;IACtC,IAAImC,IAAI,GAAGnM,QAAQ,CAAC2E,SAAS,CAAC,MAAM,EAAEqF,IAAI,CAAC;;IAE3C;IACAoC,wBAAwB,CAACD,IAAI,CAAC;EAClC;EAEA,SAAS1F,uCAAuCA,CAACzB,IAAI,EAAEqH,UAAU,EAAE;IAC/D,IAAIC,UAAU,EACVC,IAAI,EACJthB,CAAC,EACDuhB,YAAY;IAEhB,KAAKvhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGohB,UAAU,CAACthB,MAAM,EAAEE,CAAC,IAAI,CAAC,EAAE;MACvCqhB,UAAU,GAAGD,UAAU,CAACphB,CAAC,CAAC,CAACwhB,QAAQ;MACnC,IAAIH,UAAU,EAAE;QACZE,YAAY,GAAGxM,QAAQ,CAACqC,WAAW,CAACiK,UAAU,CAAC;QAC/CC,IAAI,GAAGC,YAAY,CAACjK,KAAK,CAAC,MAAM,CAAC;QACjC,IAAIgK,IAAI,EAAE;UACNvM,QAAQ,CAAC0M,KAAK,CAACC,SAAS,CAAC3H,IAAI,EAAEuH,IAAI,CAAC;QACxC;MACJ;IACJ;EACJ;EAEA,SAASH,wBAAwBA,CAACD,IAAI,EAAE;IACpC,IAAIS,IAAI,GAAG5M,QAAQ,CAAC2C,aAAa,CAAC,MAAM,EAAEwJ,IAAI,CAAC;IAE/CS,IAAI,CAAC/J,KAAK,GAAG,CAAC;IACd+J,IAAI,CAAChK,OAAO,GAAG,CAAC;IAEhBgK,IAAI,CAACC,mBAAmB,GAAG,GAAG;IAC9BD,IAAI,CAACE,eAAe,GAAG,CAAC;IACxBF,IAAI,CAACG,WAAW,GAAIxI,iBAAiB,IAAKA,iBAAiB,CAACxZ,MAAM,GAAI,CAAC,IAAIwZ,iBAAiB,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,GAC/GA,iBAAiB,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnI;EAEA,SAAS+B,aAAaA,CAACtB,IAAI,EAAE;IACzB,IAAIgI,IAAI,GAAGhN,QAAQ,CAAC2C,aAAa,CAAC,MAAM,EAAEqC,IAAI,CAAC;IAE/CgI,IAAI,CAACxK,QAAQ,GAAGgC,OAAO;IACvBwI,IAAI,CAACC,gCAAgC,GAAG,CAAC;IACzCD,IAAI,CAACE,uBAAuB,GAAG,CAAC;IAChCF,IAAI,CAACG,mBAAmB,GAAG,CAAC;IAC5BH,IAAI,CAACI,oBAAoB,GAAG,CAAC;IAE7B,OAAOJ,IAAI;EACf;EAEA,SAASjC,iBAAiBA,CAACxT,GAAG,EAAE;IAC5B,IAAI8V,GAAG,GAAG,IAAIrC,UAAU,CAACzT,GAAG,CAACxM,MAAM,GAAG,CAAC,CAAC;IACxC,IAAIE,CAAC;IAEL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsM,GAAG,CAACxM,MAAM,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAE;MACpCoiB,GAAG,CAACpiB,CAAC,CAAC,GAAGyM,QAAQ,CAAC,EAAE,GAAGH,GAAG,CAACtM,CAAC,GAAG,CAAC,CAAC,GAAGsM,GAAG,CAACtM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;IAC3D;IACA,OAAOoiB,GAAG;EACd;EAEA,SAAStB,gBAAgBA,CAACxU,GAAG,EAAE;IAC3B,IAAIyR,IAAI,GAAG,CAAC;IACZ,IAAI/d,CAAC;IAEL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsM,GAAG,CAACxM,MAAM,EAAEE,CAAC,IAAI,CAAC,EAAE;MAChC+d,IAAI,IAAIzR,GAAG,CAAC+V,UAAU,CAACriB,CAAC,CAAC,IAAK,CAACsM,GAAG,CAACxM,MAAM,GAAGE,CAAC,GAAG,CAAC,IAAI,CAAE;IAC3D;IACA,OAAO+d,IAAI;EACf;EAEA,SAASuE,YAAYA,CAACC,GAAG,EAAE;IACvB,IAAI,CAACA,GAAG,IAAI,CAACA,GAAG,CAACxQ,UAAU,EAAE;MACzB;IACJ;IAEA,IAAIoF,OAAO,EACPqL,WAAW;IAEf5Q,cAAc,GAAG2Q,GAAG;IACpBlJ,aAAa,GAAGzH,cAAc,CAACG,UAAU;IAEzCC,MAAM,GAAGqH,aAAa,CAACrH,MAAM;IAC7BuH,OAAO,GAAGF,aAAa,CAACvI,KAAK,GAAG,CAAC;IACjCwI,iBAAiB,GAAGtH,MAAM,CAACC,GAAG,CAACH,QAAQ,CAACI,MAAM,CAACF,MAAM,CAAClB,KAAK,CAAC,CAACqB,aAAa,CAACkH,aAAa,CAACvI,KAAK,CAAC,CAAC2R,iBAAiB;IAEjH5P,SAAS,GAAGb,MAAM,CAACC,GAAG,CAACH,QAAQ,CAACI,MAAM,CAACF,MAAM,CAAClB,KAAK,CAAC,CAACqB,aAAa,CAACkH,aAAa,CAACvI,KAAK,CAAC,CAACuB,eAAe,CAACQ,SAAS;IAEjHsE,OAAO,GAAGpC,QAAQ,CAAC2N,UAAU,CAAC,CAAC;IAC/BlJ,aAAa,CAACrC,OAAO,CAAC;IACtB2C,aAAa,CAAC3C,OAAO,CAAC;IAEtBqL,WAAW,GAAGrL,OAAO,CAAC2B,KAAK,CAAC,CAAC;IAE7B,OAAO0J,WAAW;EACtB;EAEAlV,QAAQ,GAAG;IACPgV,YAAY,EAAEA;EAClB,CAAC;EAED,OAAOhV,QAAQ;AACnB;AAEA0L,wBAAwB,CAACnK,qBAAqB,GAAG,0BAA0B;AAC3E,+DAAexB,6DAAY,CAACsB,eAAe,CAACqK,wBAAwB,CAAC;;;;;;;;;;;;;;;AChpBrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEqE;AACA;AACF;AAChB;;AAGnD;;AAEA,SAAS2J,UAAUA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC5B,OAAQD,IAAI,CAAC9iB,MAAM,KAAK+iB,IAAI,CAAC/iB,MAAM,IAAK8iB,IAAI,CAACE,KAAK,CAAC,UAAUC,OAAO,EAAEjS,KAAK,EAAE;IACzE,OAAOiS,OAAO,KAAKF,IAAI,CAAC/R,KAAK,CAAC;EAClC,CAAC,CAAC;AACN;AAEA,SAASkS,aAAaA,CAAA,EAAG;EACrB,IAAI,CAACC,YAAY,CAAC,CAAC;EACnB,IAAI,IAAI,CAACrL,KAAK,GAAG,CAAC,EAAE;IAChB,IAAI,CAACsL,UAAU,CAAC,eAAe,EAAE,MAAM,EAAE,EAAE,CAAC;IAC5C,IAAI,CAACA,UAAU,CAAC,yBAAyB,EAAE,MAAM,EAAE,EAAE,CAAC;EAC1D;EACA,IAAI,CAACA,UAAU,CAAC,aAAa,EAAE,MAAM,EAAE,EAAE,CAAC;EAC1C,IAAI,CAACC,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAChL,WAAW,EAAE,MAAM,EAAG,IAAI,CAACR,OAAO,KAAK,CAAC,GAAI,EAAE,GAAG,EAAE,CAAC;AAC5F;AAEA,SAASyL,aAAaA,CAAA,EAAG;EACrB,IAAI,CAACH,YAAY,CAAC,CAAC;EACnB,IAAI,IAAI,CAACrL,KAAK,GAAG,CAAC,EAAE;IAChB,IAAI,CAACsL,UAAU,CAAC,eAAe,EAAE,MAAM,EAAE,EAAE,CAAC;IAC5C,IAAI,CAACA,UAAU,CAAC,yBAAyB,EAAE,MAAM,EAAE,EAAE,CAAC;EAC1D;EACA,IAAI,CAACA,UAAU,CAAC,0BAA0B,EAAE,MAAM,EAAE,CAAC,CAAC;EACtD,IAAI,CAACA,UAAU,CAAC,cAAc,EAAE,MAAM,EAAE,EAAE,CAAC;EAC3C,IAAI,IAAI,CAAC5K,wBAAwB,KAAK,CAAC,EAAE;IACrC,IAAI,CAAC6K,eAAe,CAAC,kBAAkB,EAAE,IAAI,CAAC9K,YAAY,EAAE,MAAM,EAAE,CAAC,CAAC;EAC1E;AACJ;AAEA,SAASgL,aAAaA,CAAA,EAAG;EACrB,IAAI,CAACJ,YAAY,CAAC,CAAC;EACnB,IAAI,CAACC,UAAU,CAAC,cAAc,EAAE,MAAM,EAAE,EAAE,CAAC;EAC3C,IAAI,IAAI,CAACtL,KAAK,GAAG,CAAC,EAAE;IAChB,IAAI,CAACsL,UAAU,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC;EACzC;EACA,IAAI,CAACI,YAAY,CAAC,OAAO,EAAE,IAAI,CAACjL,YAAY,EAAE,UAAU9C,KAAK,EAAE;IAC3D,IAAI,CAACgO,eAAe,CAAChO,KAAK,EAAE,sBAAsB,EAAE,MAAM,EAAE,CAAC,CAAC;IAC9D,IAAI,IAAI,CAACqC,KAAK,GAAG,CAAC,EAAE;MAChB,IAAI,CAAC2L,eAAe,CAAChO,KAAK,EAAE,iBAAiB,EAAE,MAAM,EAAE,EAAE,CAAC;MAC1D,IAAI,CAACiO,eAAe,CAACjO,KAAK,EAAE,qBAAqB,EAAEA,KAAK,CAACiD,eAAe,EAAE,UAAUiL,mBAAmB,EAAE;QACrG,IAAI,CAACF,eAAe,CAACE,mBAAmB,EAAE,kBAAkB,EAAE,MAAM,EAAE,EAAE,CAAC;QACzE,IAAI,CAACF,eAAe,CAACE,mBAAmB,EAAE,sBAAsB,EAAE,MAAM,EAAE,EAAE,CAAC;MACjF,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;AACN;AAEA,SAASC,aAAaA,CAAA,EAAG;EACrB,IAAIC,YAAY,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACnH,IAAIC,YAAY,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACnH,IAAIC,cAAc,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAErH,IAAIlB,UAAU,CAAC,IAAI,CAAC1K,QAAQ,EAAE0L,YAAY,CAAC,EAAE;IACzC,IAAI,CAACV,YAAY,CAAC,CAAC;IACnB,IAAI,IAAI,CAACa,QAAQ,EAAE;MACf,IAAI,CAACpT,IAAI,GAAG,MAAM;IACtB;IACA,IAAI,CAACwS,UAAU,CAAC,wBAAwB,EAAE,MAAM,EAAG,IAAI,CAACvL,OAAO,KAAK,CAAC,GAAI,EAAE,GAAG,EAAE,CAAC;IACjF,IAAI,CAACuL,UAAU,CAAC,mBAAmB,EAAE,MAAM,EAAG,IAAI,CAACvL,OAAO,KAAK,CAAC,GAAI,EAAE,GAAG,EAAE,CAAC;EAChF;EAEA,IAAIgL,UAAU,CAAC,IAAI,CAAC1K,QAAQ,EAAE2L,YAAY,CAAC,EAAE;IACzC,IAAI,CAACX,YAAY,CAAC,CAAC;IACnB,IAAI,IAAI,CAACa,QAAQ,EAAE;MACf,IAAI,CAACpT,IAAI,GAAG,MAAM;IACtB;IACA,IAAI,CAACwS,UAAU,CAAC,gBAAgB,EAAE,MAAM,EAAE,CAAC,CAAC;IAC5C,IAAI,CAACI,YAAY,CAAC,OAAO,EAAE,IAAI,CAACS,cAAc,EAAE,UAAUxO,KAAK,EAAE;MAC7D,IAAI,CAACgO,eAAe,CAAChO,KAAK,EAAE,wBAAwB,EAAE,MAAM,EAAG,IAAI,CAACoC,OAAO,KAAK,CAAC,GAAI,EAAE,GAAG,EAAE,CAAC;MAC7F,IAAI,CAAC4L,eAAe,CAAChO,KAAK,EAAE,mBAAmB,EAAE,MAAM,EAAG,IAAI,CAACoC,OAAO,KAAK,CAAC,GAAI,EAAE,GAAG,EAAE,CAAC;IAC5F,CAAC,CAAC;EACN;EAEA,IAAIgL,UAAU,CAAC,IAAI,CAAC1K,QAAQ,EAAE4L,cAAc,CAAC,EAAE;IAC3C,IAAI,IAAI,CAACC,QAAQ,EAAE;MACf,IAAI,CAACpT,IAAI,GAAG,QAAQ;IACxB;IACA2S,aAAa,CAACzQ,IAAI,CAAC,IAAI,CAAC;EAC5B;AACJ;AAEA,SAASoR,oBAAoBA,CAAClU,MAAM,EAAE;EAElCA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACrB,MAAMhC,OAAO,GAAG,IAAI,CAACA,OAAO;EAC5B,MAAM4G,WAAW,GAAG5E,MAAM,CAAC4E,WAAW;EACtC,MAAMC,kBAAkB,GAAG7E,MAAM,CAAC6E,kBAAkB;EACpD,MAAMG,QAAQ,GAAGhF,MAAM,CAACgF,QAAQ;EAChC,MAAMsE,oBAAoB,GAAGtJ,MAAM,CAACsJ,oBAAoB;EACxD,MAAMrE,QAAQ,GAAGjF,MAAM,CAACiF,QAAQ;EAChC,MAAM9D,KAAK,GAAGnB,MAAM,CAACmB,KAAK;EAC1B,IAAIgT,wBAAwB,EACxBC,wBAAwB,EACxB5W,QAAQ;EAEZ,SAAS6D,KAAKA,CAAA,EAAG;IACb4D,QAAQ,CAACoP,eAAe,CAAC,MAAM,EAAET,aAAa,CAAC;IAC/C3O,QAAQ,CAACoP,eAAe,CAAC,MAAM,EAAEnB,aAAa,CAAC;IAC/CjO,QAAQ,CAACoP,eAAe,CAAC,MAAM,EAAEf,aAAa,CAAC;IAC/CrO,QAAQ,CAACoP,eAAe,CAAC,MAAM,EAAEd,aAAa,CAAC;IAE/CY,wBAAwB,GAAGjL,wEAAwB,CAAClL,OAAO,CAAC,CAAC1O,MAAM,CAAC;MAChEga,oBAAoB,EAAEA,oBAAoB;MAC1CD,SAAS,EAAErJ,MAAM,CAACqJ,SAAS;MAC3BpE,QAAQ,EAAEA;IAAQ,CAAC,CAAC;IAExBmP,wBAAwB,GAAGzP,wEAAwB,CAAC3G,OAAO,CAAC,CAAC1O,MAAM,CAAC;MAChEsV,WAAW,EAAEA,WAAW;MACxBC,kBAAkB,EAAEA,kBAAkB;MACtCI,QAAQ,EAAEA,QAAQ;MAClBD,QAAQ,EAAEA,QAAQ;MAClB7D,KAAK,EAAEA,KAAK;MACZ4D,UAAU,EAAE/E,MAAM,CAAC+E;IACvB,CAAC,CAAC;EACN;EAEA,SAASyN,YAAYA,CAACC,GAAG,EAAE;IACvB,OAAO0B,wBAAwB,CAAC3B,YAAY,CAACC,GAAG,CAAC;EACrD;EAEA,SAAS6B,eAAeA,CAAC9Z,CAAC,EAAEyG,eAAe,EAAE;IACzC,IAAI,CAACzG,CAAC,IAAI,CAACA,CAAC,CAACmI,OAAO,IAAI,CAACnI,CAAC,CAACwJ,QAAQ,EAAE;MACjC,MAAM,IAAI/O,KAAK,CAAC,qCAAqC,CAAC;IAC1D;IAEA,IAAIuF,CAAC,CAACmI,OAAO,CAAC/B,IAAI,KAAK,cAAc,EAAE;MACnC;MACAwT,wBAAwB,CAAChN,eAAe,CAAC5M,CAAC,EAAEyG,eAAe,CAAC;IAEhE,CAAC,MAAM,IAAIzG,CAAC,CAACmI,OAAO,CAAC/B,IAAI,KAAKL,6EAAW,CAAC0C,8BAA8B,EAAE;MACtE;MACAmR,wBAAwB,CAACnL,iBAAiB,CAACzO,CAAC,EAAEyG,eAAe,CAAC;;MAE9D;MACAzG,CAAC,CAACgM,MAAM,GAAG,IAAI;IACnB;EACJ;EAEAhJ,QAAQ,GAAG;IACPgV,YAAY;IACZ8B;EACJ,CAAC;EAEDjT,KAAK,CAAC,CAAC;EAEP,OAAO7D,QAAQ;AACnB;AAEA0W,oBAAoB,CAACnV,qBAAqB,GAAG,sBAAsB;AACnE,+DAAexB,6DAAY,CAACsB,eAAe,CAACqV,oBAAoB,CAAC;;;;;;;;;;;;;;;;;;;AC5LjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEqD;AACY;AACM;AACV;AACf;AACA;AACW;AACU;AAEnE,SAASO,UAAUA,CAACzU,MAAM,EAAE;EAExBA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACrB,MAAMhC,OAAO,GAAG,IAAI,CAACA,OAAO;EAC5B,MAAMgH,QAAQ,GAAGhF,MAAM,CAACgF,QAAQ;EAChC,MAAM5E,MAAM,GAAGJ,MAAM,CAACI,MAAM;EAC5B,MAAMiJ,SAAS,GAAGrJ,MAAM,CAACqJ,SAAS;EAClC,MAAMqL,eAAe,GAAG1U,MAAM,CAAC0U,eAAe;EAC9C,MAAM7P,kBAAkB,GAAG7E,MAAM,CAAC6E,kBAAkB;EACpD,MAAM8P,gBAAgB,GAAG3U,MAAM,CAAC2U,gBAAgB;EAChD,IAAIC,SAAS,EACTC,oBAAoB,EACpBC,uBAAuB,EACvBtX,QAAQ;EAEZ,SAAS6D,KAAKA,CAAA,EAAG;IACbyT,uBAAuB,GAAG,EAAE;EAChC;EAEA,SAASC,0BAA0BA,CAAA,EAAG;IAClCF,oBAAoB,GAAGX,oEAAoB,CAAClW,OAAO,CAAC,CAAC1O,MAAM,CAAC0Q,MAAM,CAAC;EACvE;EAEA,SAASgV,kBAAkBA,CAACpU,IAAI,EAAE;IAC9B,OAAO+T,gBAAgB,CAACM,yBAAyB,CAAC,CAAC,CAAC3W,MAAM,CAAC4W,SAAS,IAAI;MACpE,OAAOA,SAAS,CAAC1T,OAAO,CAAC,CAAC,KAAKZ,IAAI;IACvC,CAAC,CAAC,CAAC,CAAC,CAAC;EACT;EAEA,SAASuU,yBAAyBA,CAACvU,IAAI,EAAE;IACrC,OAAOkU,uBAAuB,CAACxW,MAAM,CAAC8W,UAAU,IAAI;MAChD,OAAQA,UAAU,CAAC5T,OAAO,CAAC,CAAC,KAAKZ,IAAI;IACzC,CAAC,CAAC,CAAC,CAAC,CAAC;EACT;EAEA,SAASyU,eAAeA,CAAC1S,OAAO,EAAE2S,QAAQ,EAAEC,WAAW,EAAE;IACrD,MAAMC,KAAK,GAAG,IAAIjB,kEAAS,CAAC,CAAC;IAE7BiB,KAAK,CAACF,QAAQ,GAAGA,QAAQ;IACzBE,KAAK,CAACC,WAAW,GAAG9S,OAAO,CAAC/B,IAAI;IAChC4U,KAAK,CAAC9Z,KAAK,GAAGiH,OAAO,CAAC7B,SAAS;IAC/B0U,KAAK,CAACtS,QAAQ,GAAGP,OAAO,CAACO,QAAQ;IACjCsS,KAAK,CAACzP,GAAG,GAAGyP,KAAK,CAAC9Z,KAAK,GAAG8Z,KAAK,CAACtS,QAAQ;IACxCsS,KAAK,CAACxU,KAAK,GAAG2B,OAAO,CAAC3B,KAAK;IAC3BwU,KAAK,CAACrS,SAAS,GAAGR,OAAO,CAACQ,SAAS;IACnCqS,KAAK,CAAC1T,cAAc,GAAGa,OAAO,CAACb,cAAc;IAC7C0T,KAAK,CAACD,WAAW,GAAGA,WAAW;IAE/B,OAAOC,KAAK;EAChB;EAEA,SAASE,4BAA4BA,CAAA,EAAG;IAEpC;IACA,IAAIC,UAAU,GAAGhB,gBAAgB,CAACM,yBAAyB,CAAC,CAAC;IAC7DU,UAAU,CAACC,OAAO,CAAC,UAAUV,SAAS,EAAE;MACpC,IAAIA,SAAS,CAAC1T,OAAO,CAAC,CAAC,KAAK6H,SAAS,CAACoB,KAAK,IACvCyK,SAAS,CAAC1T,OAAO,CAAC,CAAC,KAAK6H,SAAS,CAACsB,KAAK,IACvCuK,SAAS,CAAC1T,OAAO,CAAC,CAAC,KAAK6H,SAAS,CAACwM,IAAI,EAAE;QAExC,IAAIC,sBAAsB,GAAGX,yBAAyB,CAACD,SAAS,CAAC1T,OAAO,CAAC,CAAC,CAAC;QAC3E,IAAI,CAACsU,sBAAsB,EAAE;UACzBA,sBAAsB,GAAGtV,yEAAyB,CAACxC,OAAO,CAAC,CAAC1O,MAAM,CAAC;YAC/D2R,eAAe,EAAEiU,SAAS;YAC1BhU,iBAAiB,EAAElB,MAAM,CAACkB,iBAAiB;YAC3CC,KAAK,EAAEnB,MAAM,CAACmB;UAClB,CAAC,CAAC;UACF2U,sBAAsB,CAACvU,UAAU,CAAC,CAAC;UACnCuT,uBAAuB,CAAC7jB,IAAI,CAAC6kB,sBAAsB,CAAC;QACxD;QACAA,sBAAsB,CAACpa,KAAK,CAAC,CAAC;MAClC;IACJ,CAAC,CAAC;EACN;EAEA,SAASqa,2BAA2BA,CAAA,EAAG;IACnCjB,uBAAuB,CAACc,OAAO,CAAC9iB,CAAC,IAAI;MACjCA,CAAC,CAAC+O,KAAK,CAAC,CAAC;IACb,CAAC,CAAC;IACFiT,uBAAuB,GAAG,EAAE;EAChC;EAEA,SAASkB,oBAAoBA,CAACxb,CAAC,EAAE;IAC7B,IAAIyG,eAAe,GAAG+T,kBAAkB,CAACxa,CAAC,CAACwI,SAAS,CAAC;IACrD,IAAI,CAAC/B,eAAe,EAAE;MAClB;IACJ;;IAEA;IACA,IAAI0C,wBAAwB,GAAG1C,eAAe,CAAC2C,2BAA2B,CAAC,CAAC;IAC5E,IAAI9B,cAAc,GAAG6B,wBAAwB,CAAC5B,wBAAwB,CAAC,CAAC;IACxE,IAAI2F,SAAS,GAAGzG,eAAe,CAACgV,YAAY,CAAC,CAAC;IAE9C,IAAItT,OAAO,GAAG,IAAIrC,wEAAe,CAAC,CAAC;IACnCqC,OAAO,CAACK,SAAS,GAAGlB,cAAc,CAACG,UAAU,CAACrB,IAAI;IAClD+B,OAAO,CAAC/B,IAAI,GAAG8T,eAAe;IAC9B/R,OAAO,CAAC7H,KAAK,GAAGgH,cAAc,CAAChH,KAAK;IACpC6H,OAAO,CAACQ,SAAS,GAAGrB,cAAc,CAACqB,SAAS;IAC5CR,OAAO,CAACb,cAAc,GAAGA,cAAc;IAEvC,MAAM0T,KAAK,GAAGH,eAAe,CAAC1S,OAAO,EAAE+E,SAAS,CAACwO,UAAU,CAACjJ,EAAE,EAAEzS,CAAC,CAACoG,IAAI,KAAKR,MAAM,CAAC+V,yBAAyB,CAAC;IAE5G,IAAI;MACA;MACAX,KAAK,CAACY,KAAK,GAAGvB,oBAAoB,CAACrC,YAAY,CAAC1Q,cAAc,CAAC;;MAE/D;MACAkD,QAAQ,CAACsB,OAAO,CAAClG,MAAM,CAACiW,oBAAoB,EACxC;QAAEb,KAAK,EAAEA;MAAM,CAAC,EAChB;QAAEF,QAAQ,EAAE5N,SAAS,CAACwO,UAAU,CAACjJ,EAAE;QAAEjK,SAAS,EAAElB,cAAc,CAACG,UAAU,CAACrB;MAAK,CACnF,CAAC;IACL,CAAC,CAAC,OAAOpG,CAAC,EAAE;MACRwF,MAAM,CAAC+E,UAAU,CAACd,KAAK,CAAC,IAAIO,oEAAW,CAAChK,CAAC,CAACyT,IAAI,EAAEzT,CAAC,CAAC2T,OAAO,EAAE3T,CAAC,CAAC6T,IAAI,CAAC,CAAC;IACvE;;IAEA;IACA7T,CAAC,CAACgM,MAAM,GAAG,IAAI;EACnB;EAEA,SAAS8P,oBAAoBA,CAAC9b,CAAC,EAAE;IAC7B,IAAIA,CAAC,CAACyJ,KAAK,EAAE;MACT;IACJ;IAEA,IAAIhD,eAAe,GAAG+T,kBAAkB,CAACxa,CAAC,CAACmI,OAAO,CAACK,SAAS,CAAC;IAC7D,IAAI,CAAC/B,eAAe,EAAE;MAClB;IACJ;;IAEA;IACA4T,oBAAoB,CAACP,eAAe,CAAC9Z,CAAC,EAAEyG,eAAe,CAAC;IAExD,IAAIzG,CAAC,CAACmI,OAAO,CAAC/B,IAAI,KAAKL,6EAAW,CAAC0C,8BAA8B,EAAE;MAC/D;MACA,IAAI6S,sBAAsB,GAAGX,yBAAyB,CAAC3a,CAAC,CAACmI,OAAO,CAACK,SAAS,CAAC;MAC3E,IAAI8S,sBAAsB,EAAE;QACxBA,sBAAsB,CAAC/R,kBAAkB,CAACvJ,CAAC,CAAC;MAChD;IACJ;;IAEA;IACA,IAAI2L,YAAY,GAAG3L,CAAC,CAACmI,OAAO,CAACb,cAAc,CAAC4F,SAAS,CAACwO,UAAU,CAAC/P,YAAY;IAC7E,IAAI,CAACA,YAAY,CAACoQ,SAAS,IAAIpQ,YAAY,CAACqQ,aAAa,KAAKlgB,QAAQ,EAAE;MACpEof,4BAA4B,CAAC,CAAC;IAClC;EACJ;EAEA,SAASe,gBAAgBA,CAAA,EAAG;IACxB,IAAI5R,kBAAkB,CAAC6R,YAAY,CAAC,CAAC,IAAI7R,kBAAkB,CAACP,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE;MACzEoR,4BAA4B,CAAC,CAAC;IAClC;EACJ;EAEA,SAASiB,iBAAiBA,CAAA,EAAG;IACzB,IAAI9R,kBAAkB,CAAC6R,YAAY,CAAC,CAAC,IAAI7R,kBAAkB,CAACP,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE;MACzEoR,4BAA4B,CAAC,CAAC;IAClC;EACJ;EAEA,SAASkB,gBAAgBA,CAACC,aAAa,EAAE;IACrC,IAAI,CAACA,aAAa,IAAI,CAACA,aAAa,CAACxI,IAAI,EAAE;MACvC;IACJ;IAEAwI,aAAa,CAACxI,IAAI,GAAGwI,aAAa,CAACxI,IAAI,CAAC5K,OAAO,CAAC,wCAAwC,EAAE,2BAA2B,CAAC;EAC1H;EAEA,SAASqT,cAAcA,CAAA,EAAG;IACtB9R,QAAQ,CAAC+R,EAAE,CAAC3W,MAAM,CAAC4W,oBAAoB,EAAEhB,oBAAoB,EAAExY,QAAQ,EAAE;MAAEyZ,QAAQ,EAAEC,MAAM,CAAC3Z,YAAY,CAAC4B,yBAAyB,CAAC6F,QAAQ,CAACnF,YAAY,CAAC,CAAC,CAAC,CAACsX;IAAoB,CAAC,CAAC;IAClLnS,QAAQ,CAAC+R,EAAE,CAAC3W,MAAM,CAACgX,eAAe,EAAEX,gBAAgB,EAAEjZ,QAAQ,EAAE;MAAEyZ,QAAQ,EAAEC,MAAM,CAAC3Z,YAAY,CAAC4B,yBAAyB,CAAC6F,QAAQ,CAACnF,YAAY,CAAC,CAAC,CAAC,CAACsX;IAAoB,CAAC,CAAC;IACzKnS,QAAQ,CAAC+R,EAAE,CAAC3W,MAAM,CAACiX,gBAAgB,EAAEV,iBAAiB,EAAEnZ,QAAQ,EAAE;MAAEyZ,QAAQ,EAAEC,MAAM,CAAC3Z,YAAY,CAAC4B,yBAAyB,CAAC6F,QAAQ,CAACnF,YAAY,CAAC,CAAC,CAAC,CAACsX;IAAoB,CAAC,CAAC;IAC3KnS,QAAQ,CAAC+R,EAAE,CAAC3W,MAAM,CAACkX,0BAA0B,EAAEhB,oBAAoB,EAAE9Y,QAAQ,EAAE;MAAEyZ,QAAQ,EAAEC,MAAM,CAAC3Z,YAAY,CAAC4B,yBAAyB,CAAC6F,QAAQ,CAACnF,YAAY,CAAC,CAAC,CAAC,CAACsX;IAAoB,CAAC,CAAC;IACxLnS,QAAQ,CAAC+R,EAAE,CAAC3W,MAAM,CAACmX,aAAa,EAAEX,gBAAgB,EAAEpZ,QAAQ,CAAC;EACjE;EAEA,SAASqE,KAAKA,CAAA,EAAG;IACb,IAAI+S,SAAS,EAAE;MACXA,SAAS,CAAC/S,KAAK,CAAC,CAAC;MACjB+S,SAAS,GAAG5mB,SAAS;IACzB;IAEAgX,QAAQ,CAACwS,GAAG,CAACpX,MAAM,CAAC4W,oBAAoB,EAAEhB,oBAAoB,EAAE,IAAI,CAAC;IACrEhR,QAAQ,CAACwS,GAAG,CAACpX,MAAM,CAACgX,eAAe,EAAEX,gBAAgB,EAAE,IAAI,CAAC;IAC5DzR,QAAQ,CAACwS,GAAG,CAACpX,MAAM,CAACiX,gBAAgB,EAAEV,iBAAiB,EAAE,IAAI,CAAC;IAC9D3R,QAAQ,CAACwS,GAAG,CAACpX,MAAM,CAACkX,0BAA0B,EAAEhB,oBAAoB,EAAE,IAAI,CAAC;IAC3EtR,QAAQ,CAACwS,GAAG,CAACpX,MAAM,CAACmX,aAAa,EAAEX,gBAAgB,EAAE,IAAI,CAAC;;IAE1D;IACAb,2BAA2B,CAAC,CAAC;EACjC;EAEA,SAAS0B,eAAeA,CAAA,EAAG;IACvB7C,SAAS,GAAGJ,gEAAS,CAACxW,OAAO,CAAC,CAAC1O,MAAM,CAAC0Q,MAAM,CAAC;IAC7C,OAAO4U,SAAS;EACpB;EAEApX,QAAQ,GAAG;IACPqE,KAAK;IACL4V,eAAe;IACf1C,0BAA0B;IAC1B+B;EACJ,CAAC;EAEDzV,KAAK,CAAC,CAAC;EAEP,OAAO7D,QAAQ;AACnB;AAEAiX,UAAU,CAAC1V,qBAAqB,GAAG,YAAY;AAC/C,MAAML,OAAO,GAAGwY,MAAM,CAAC3Z,YAAY,CAACsB,eAAe,CAAC4V,UAAU,CAAC;AAC/D/V,OAAO,CAACqB,MAAM,GAAG0E,4DAAS;AAC1ByS,MAAM,CAAC3Z,YAAY,CAACoB,kBAAkB,CAAC8V,UAAU,CAAC1V,qBAAqB,EAAEL,OAAO,CAAC;AACjF,+DAAeA,OAAO;;;;;;;;;;;;AC5PtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACyD;;AAEzD;AACA;AACA;AACA;AACA,MAAM+F,SAAS,SAAS3E,kEAAU,CAAC;EAC/B4X,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP;AACR;AACA;IACQ,IAAI,CAACpS,gBAAgB,GAAG,GAAG;;IAE3B;AACR;AACA;IACQ,IAAI,CAAC4I,0BAA0B,GAAG,GAAG;IAErC,IAAI,CAAC3I,mBAAmB,GAAG,oCAAoC;IAC/D,IAAI,CAAC6I,6BAA6B,GAAG,mBAAmB;EAC5D;AACJ;AAEA,IAAIuJ,SAAS,GAAG,IAAIlT,SAAS,CAAC,CAAC;AAC/B,+DAAekT,SAAS;;;;;;;;;;;;;;ACvDxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEsD;AACA;AAC6B;AAEnF,SAASnD,SAASA,CAACxU,MAAM,EAAE;EACvBA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACrB,MAAM6X,MAAM,GAAG7X,MAAM,CAAC6X,MAAM;EAC5B,MAAM1W,KAAK,GAAGnB,MAAM,CAACmB,KAAK;EAC1B,MAAMkI,SAAS,GAAGrJ,MAAM,CAACqJ,SAAS;EAClC,MAAMyO,aAAa,GAAG9X,MAAM,CAAC8X,aAAa;EAC1C,MAAMC,QAAQ,GAAG/X,MAAM,CAAC+X,QAAQ;EAEhC,MAAMC,kBAAkB,GAAG,UAAU;EACrC,MAAMC,gBAAgB,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EACxF;EACA,MAAMC,IAAI,GAAG;IACT,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,WAAW;IACnB,MAAM,EAAE;EACZ,CAAC;EACD,MAAMC,aAAa,GAAG;IAClB,MAAM,EAAE;EACZ,CAAC;EACD,MAAMC,sBAAsB,GAAG;IAC3B,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,IAAI,EAAE,GAAG;IACT,IAAI,EAAE;EACV,CAAC;EACD,MAAMC,WAAW,GAAG;IAChB,OAAO,EAAE,WAAW;IACpB,OAAO,EAAE,WAAW;IACpB,MAAM,EAAE;EACZ,CAAC;EAED,IAAI7a,QAAQ,EACRiD,MAAM,EACN6X,qBAAqB;EAGzB,SAASjX,KAAKA,CAAA,EAAG;IACbZ,MAAM,GAAGU,KAAK,CAACG,SAAS,CAAC9D,QAAQ,CAAC;EACtC;EAEA,SAAS+a,qBAAqBA,CAACC,IAAI,EAAEC,QAAQ,EAAE;IAC3C,MAAMxpB,KAAK,GAAGupB,IAAI,CAACE,YAAY,CAACD,QAAQ,CAAC;IACzC,IAAI,CAACxpB,KAAK,EAAE;MACR,OAAO,KAAK;IAChB;IACA,OAAOA,KAAK,CAACsM,WAAW,CAAC,CAAC,KAAK,MAAM;EACzC;EAEA,SAASod,SAASA,CAACC,oBAAoB,EAAE7V,SAAS,EAAE;IAChD,MAAMb,MAAM,GAAG,CAAC,CAAC;IACjB,IAAI2W,OAAO,EACP5W,UAAU;;IAEd;IACAC,MAAM,CAACG,aAAa,GAAG,EAAE;IACzBwW,OAAO,GAAGD,oBAAoB,CAACE,oBAAoB,CAAC,aAAa,CAAC;IAClE,KAAK,IAAI5oB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2oB,OAAO,CAAC7oB,MAAM,EAAEE,CAAC,EAAE,EAAE;MACrC+R,UAAU,GAAG8W,gBAAgB,CAACF,OAAO,CAAC3oB,CAAC,CAAC,EAAE6S,SAAS,CAAC;MACpD,IAAId,UAAU,KAAK,IAAI,EAAE;QACrBC,MAAM,CAACG,aAAa,CAACpR,IAAI,CAACgR,UAAU,CAAC;MACzC;IACJ;IAEA,OAAOC,MAAM;EACjB;EAEA,SAAS6W,gBAAgBA,CAACC,WAAW,EAAEjW,SAAS,EAAE;IAC9C,MAAMwG,aAAa,GAAG,CAAC,CAAC;IACxB,MAAM0P,eAAe,GAAG,EAAE;IAC1B,IAAIC,eAAe;IACnB,IAAIC,aAAa,EACbrX,cAAc,EACd5R,CAAC,EACD8Q,KAAK;IAET,MAAMnD,IAAI,GAAGmb,WAAW,CAACN,YAAY,CAAC,MAAM,CAAC;IAC7C,MAAM9X,IAAI,GAAGoY,WAAW,CAACN,YAAY,CAAC,MAAM,CAAC;IAC7C,MAAM5L,IAAI,GAAGkM,WAAW,CAACN,YAAY,CAAC,UAAU,CAAC;IACjD,MAAMU,UAAU,GAAGtM,IAAI,GAAGlM,IAAI,GAAG,GAAG,GAAGkM,IAAI,GAAGlM,IAAI;IAElD2I,aAAa,CAAC0D,EAAE,GAAGpP,IAAI,IAAIub,UAAU;IACrC7P,aAAa,CAAC8P,WAAW,GAAGzY,IAAI;IAChC2I,aAAa,CAACuD,IAAI,GAAGA,IAAI,IAAI,KAAK;IAClCvD,aAAa,CAAC+P,QAAQ,GAAGjB,WAAW,CAACzX,IAAI,CAAC;IAC1C2I,aAAa,CAACgQ,OAAO,GAAGP,WAAW,CAACN,YAAY,CAAC,SAAS,CAAC;IAC3DnP,aAAa,CAACiQ,QAAQ,GAAGR,WAAW,CAACN,YAAY,CAAC,UAAU,CAAC;IAC7DnP,aAAa,CAACkQ,SAAS,GAAGT,WAAW,CAACN,YAAY,CAAC,WAAW,CAAC;;IAE/D;IACA,IAAInP,aAAa,CAACgQ,OAAO,EAAE;MACvB,IAAIrB,IAAI,CAAC3O,aAAa,CAACgQ,OAAO,CAAC,EAAE;QAC7BhQ,aAAa,CAACmQ,IAAI,GAAG,CAAC;UAClBC,WAAW,EAAE,yBAAyB;UACtC1qB,KAAK,EAAEipB,IAAI,CAAC3O,aAAa,CAACgQ,OAAO;QACrC,CAAC,CAAC;MACN;MACA,IAAIpB,aAAa,CAAC5O,aAAa,CAACgQ,OAAO,CAAC,EAAE;QACtChQ,aAAa,CAACqQ,aAAa,GAAG,CAAC;UAC3BD,WAAW,EAAE,yCAAyC;UACtD1qB,KAAK,EAAEkpB,aAAa,CAAC5O,aAAa,CAACgQ,OAAO;QAC9C,CAAC,CAAC;MACN;IACJ;;IAEA;IACAL,eAAe,GAAGW,kBAAkB,CAACb,WAAW,EAAEjW,SAAS,CAAC;IAE5DoW,aAAa,GAAGH,WAAW,CAACF,oBAAoB,CAAC,cAAc,CAAC;IAChE;IACA,KAAK5oB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGipB,aAAa,CAACnpB,MAAM,EAAEE,CAAC,EAAE,EAAE;MACvC;MACAipB,aAAa,CAACjpB,CAAC,CAAC,CAAC4pB,OAAO,GAAGvQ,aAAa,CAACuQ,OAAO;MAChDX,aAAa,CAACjpB,CAAC,CAAC,CAACopB,QAAQ,GAAG/P,aAAa,CAAC+P,QAAQ;;MAElD;MACAtY,KAAK,GAAGmY,aAAa,CAACjpB,CAAC,CAAC,CAACwoB,YAAY,CAAC,OAAO,CAAC;MAC9CS,aAAa,CAACjpB,CAAC,CAAC,CAAC6pB,EAAE,GAAGxQ,aAAa,CAAC0D,EAAE,IAAKjM,KAAK,KAAK,IAAI,GAAK,GAAG,GAAGA,KAAK,GAAI,EAAE,CAAC;;MAEhF;MACAc,cAAc,GAAGkY,iBAAiB,CAACb,aAAa,CAACjpB,CAAC,CAAC,EAAE8oB,WAAW,CAAC;MAEjE,IAAIlX,cAAc,KAAK,IAAI,EAAE;QACzB;QACAA,cAAc,CAACS,eAAe,GAAG2W,eAAe;QAEhDD,eAAe,CAAChoB,IAAI,CAAC6Q,cAAc,CAAC;MACxC;IACJ;IAEA,IAAImX,eAAe,CAACjpB,MAAM,KAAK,CAAC,EAAE;MAC9B,OAAO,IAAI;IACf;IAEAuZ,aAAa,CAAC0Q,cAAc,GAAGhB,eAAe;;IAE9C;IACA1P,aAAa,CAAChH,eAAe,GAAG2W,eAAe;IAE/C,OAAO3P,aAAa;EACxB;EAEA,SAASyQ,iBAAiBA,CAACE,YAAY,EAAElB,WAAW,EAAE;IAClD,MAAMlX,cAAc,GAAG,CAAC,CAAC;IACzB,MAAMlB,IAAI,GAAGoY,WAAW,CAACN,YAAY,CAAC,MAAM,CAAC;IAC7C,IAAIyB,WAAW,GAAG,IAAI;IACtB,IAAIzN,KAAK,GAAG,IAAI;IAChB,IAAIC,MAAM,GAAG,IAAI;IAEjB7K,cAAc,CAACmL,EAAE,GAAGiN,YAAY,CAACH,EAAE;IACnCjY,cAAc,CAACqB,SAAS,GAAGxG,QAAQ,CAACud,YAAY,CAACxB,YAAY,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;IAC7E5W,cAAc,CAACwX,QAAQ,GAAGY,YAAY,CAACZ,QAAQ;IAE/C5M,KAAK,GAAG/P,QAAQ,CAACud,YAAY,CAACxB,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC;IAC3D/L,MAAM,GAAGhQ,QAAQ,CAACud,YAAY,CAACxB,YAAY,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC;IAC7D,IAAI,CAAC0B,KAAK,CAAC1N,KAAK,CAAC,EAAE;MACf5K,cAAc,CAAC4K,KAAK,GAAGA,KAAK;IAChC;IACA,IAAI,CAAC0N,KAAK,CAACzN,MAAM,CAAC,EAAE;MAChB7K,cAAc,CAAC6K,MAAM,GAAGA,MAAM;IAClC;IAGAwN,WAAW,GAAGD,YAAY,CAACxB,YAAY,CAAC,QAAQ,CAAC;;IAEjD;IACA,IAAIyB,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,EAAE,EAAE;MAC5CA,WAAW,GAAGnB,WAAW,CAACN,YAAY,CAAC,QAAQ,CAAC;IACpD;;IAEA;IACA;IACA,IAAIyB,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,EAAE,EAAE;MAC5C,IAAIvZ,IAAI,KAAKyI,SAAS,CAACsB,KAAK,EAAE;QAC1BwP,WAAW,GAAG,KAAK;MACvB,CAAC,MAAM,IAAIvZ,IAAI,KAAKyI,SAAS,CAACoB,KAAK,EAAE;QACjChK,MAAM,CAACU,KAAK,CAAC,2GAA2G,CAAC;QACzH,OAAO,IAAI;MACf;IACJ;;IAEA;IACA,IAAI8W,gBAAgB,CAAClb,OAAO,CAACod,WAAW,CAACE,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;MAC5D;MACA5Z,MAAM,CAAC6Z,IAAI,CAAC,uBAAuB,GAAGH,WAAW,CAAC;MAClD,OAAO,IAAI;IACf;;IAEA;IACA,IAAIA,WAAW,KAAK,MAAM,IAAIA,WAAW,KAAK,MAAM,EAAE;MAClDrY,cAAc,CAAC+L,MAAM,GAAG0M,YAAY,CAACL,YAAY,CAAC;IACtD,CAAC,MAAM,IAAIC,WAAW,CAACpd,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;MACxC+E,cAAc,CAAC+L,MAAM,GAAG2M,WAAW,CAACN,YAAY,EAAEC,WAAW,CAAC;MAC9DrY,cAAc,CAAC2O,iBAAiB,GAAG9T,QAAQ,CAACud,YAAY,CAACxB,YAAY,CAAC,cAAc,CAAC,EAAE,EAAE,CAAC;MAC1F5W,cAAc,CAACuO,aAAa,GAAG1T,QAAQ,CAACud,YAAY,CAACxB,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC;IACtF,CAAC,MAAM,IAAIyB,WAAW,CAACpd,OAAO,CAAC,MAAM,CAAC,IAAIod,WAAW,CAACpd,OAAO,CAAC,MAAM,CAAC,EAAE;MACnE+E,cAAc,CAAC+L,MAAM,GAAGxE,SAAS,CAACoR,IAAI;IAC1C;IAEA3Y,cAAc,CAAC+N,gBAAgB,GAAG,EAAE,GAAGqK,YAAY,CAACxB,YAAY,CAAC,kBAAkB,CAAC;IACpF5W,cAAc,CAACgY,OAAO,GAAGI,YAAY,CAACJ,OAAO;IAE7C,OAAOhY,cAAc;EACzB;EAEA,SAASyY,YAAYA,CAACL,YAAY,EAAE;IAChC,IAAIrK,gBAAgB,GAAGqK,YAAY,CAACxB,YAAY,CAAC,kBAAkB,CAAC,CAAC/iB,QAAQ,CAAC,CAAC;IAC/E,IAAI+kB,SAAS,EACTC,MAAM;;IAGV;IACA;IACA;IACAD,SAAS,GAAG,gBAAgB,CAACE,IAAI,CAAC/K,gBAAgB,CAAC;IACnD;IACA8K,MAAM,GAAGD,SAAS,IAAIA,SAAS,CAAC,CAAC,CAAC,GAAI7K,gBAAgB,CAACgL,MAAM,CAAChL,gBAAgB,CAAC9S,OAAO,CAAC2d,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAI1sB,SAAS;IAE1H,OAAO,OAAO,GAAG2sB,MAAM;EAC3B;EAEA,SAASH,WAAWA,CAACN,YAAY,EAAEC,WAAW,EAAE;IAC5C,MAAMW,YAAY,GAAGne,QAAQ,CAACud,YAAY,CAACxB,YAAY,CAAC,cAAc,CAAC,EAAE,EAAE,CAAC;IAC5E,IAAI7I,gBAAgB,GAAGqK,YAAY,CAACxB,YAAY,CAAC,kBAAkB,CAAC,CAAC/iB,QAAQ,CAAC,CAAC;IAC/E,IAAIolB,UAAU,GAAG,CAAC;IAClB,IAAIC,mBAAmB,EACnBC,KAAK,EACLC,SAAS,EACTC,+BAA+B;;IAEnC;IACA;IACA,IAAIhB,WAAW,KAAK,MAAM,EAAE;MACxBY,UAAU,GAAG,IAAI;IACrB;IACA;IACA,IAAIlL,gBAAgB,KAAK7hB,SAAS,IAAI6hB,gBAAgB,KAAK,EAAE,EAAE;MAC3DkL,UAAU,GAAG,IAAI,CAAC,CAAC;MACnBG,SAAS,GAAG9C,sBAAsB,CAAC0C,YAAY,CAAC;MAChD,IAAIX,WAAW,KAAK,MAAM,EAAE;QACxB;QACA;QACAY,UAAU,GAAG,IAAI,CAAC,CAAC;QACnBlL,gBAAgB,GAAG,IAAII,UAAU,CAAC,CAAC,CAAC;QACpCkL,+BAA+B,GAAG/C,sBAAsB,CAAC0C,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5E;QACA;QACAjL,gBAAgB,CAAC,CAAC,CAAC,GAAIkL,UAAU,IAAI,CAAC,GAAKG,SAAS,IAAI,CAAE;QAC1DrL,gBAAgB,CAAC,CAAC,CAAC,GAAIqL,SAAS,IAAI,CAAC,GAAKhB,YAAY,CAACkB,QAAQ,IAAI,CAAE,GAAID,+BAA+B,IAAI,CAAE;QAC9GtL,gBAAgB,CAAC,CAAC,CAAC,GAAIsL,+BAA+B,IAAI,CAAC,GAAK,IAAI,IAAI,CAAE,CAAC,CAAC;QAC5EtL,gBAAgB,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;;QAE3BoL,KAAK,GAAG,IAAII,WAAW,CAAC,CAAC,CAAC;QAC1BJ,KAAK,CAAC,CAAC,CAAC,GAAG,CAACpL,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAIA,gBAAgB,CAAC,CAAC,CAAC;QAC3DoL,KAAK,CAAC,CAAC,CAAC,GAAG,CAACpL,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAIA,gBAAgB,CAAC,CAAC,CAAC;QAC3D;QACAmL,mBAAmB,GAAGC,KAAK,CAAC,CAAC,CAAC,CAACtlB,QAAQ,CAAC,EAAE,CAAC;QAC3CqlB,mBAAmB,GAAGC,KAAK,CAAC,CAAC,CAAC,CAACtlB,QAAQ,CAAC,EAAE,CAAC,GAAGslB,KAAK,CAAC,CAAC,CAAC,CAACtlB,QAAQ,CAAC,EAAE,CAAC;MAEvE,CAAC,MAAM;QACH;QACA;QACAka,gBAAgB,GAAG,IAAII,UAAU,CAAC,CAAC,CAAC;QACpC;QACAJ,gBAAgB,CAAC,CAAC,CAAC,GAAIkL,UAAU,IAAI,CAAC,GAAKG,SAAS,IAAI,CAAE;QAC1DrL,gBAAgB,CAAC,CAAC,CAAC,GAAIqL,SAAS,IAAI,CAAC,GAAKve,QAAQ,CAACud,YAAY,CAACxB,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC,IAAI,CAAE;QACnG;QACAuC,KAAK,GAAG,IAAII,WAAW,CAAC,CAAC,CAAC;QAC1BJ,KAAK,CAAC,CAAC,CAAC,GAAG,CAACpL,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAIA,gBAAgB,CAAC,CAAC,CAAC;QAC3D;QACAmL,mBAAmB,GAAGC,KAAK,CAAC,CAAC,CAAC,CAACtlB,QAAQ,CAAC,EAAE,CAAC;MAC/C;MAEAka,gBAAgB,GAAG,EAAE,GAAGmL,mBAAmB;MAC3CnL,gBAAgB,GAAGA,gBAAgB,CAACwK,WAAW,CAAC,CAAC;MACjDH,YAAY,CAACoB,YAAY,CAAC,kBAAkB,EAAEzL,gBAAgB,CAAC;IACnE,CAAC,MAAM,IAAIkL,UAAU,KAAK,CAAC,EAAE;MACzBA,UAAU,GAAG,CAACpe,QAAQ,CAACkT,gBAAgB,CAACgL,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,KAAK,CAAC;IAC1E;IAEA,OAAO,UAAU,GAAGE,UAAU;EAClC;EAEA,SAASlB,kBAAkBA,CAACb,WAAW,EAAEjW,SAAS,EAAE;IAChD,MAAMmW,eAAe,GAAG,CAAC,CAAC;IAC1B,IAAIqC,QAAQ,EACRC,oBAAoB,EACpBnY,GAAG;IAEPA,GAAG,GAAG2V,WAAW,CAACN,YAAY,CAAC,KAAK,CAAC;IACrC6C,QAAQ,GAAGlY,GAAG,GAAGA,GAAG,CAACI,OAAO,CAAC,WAAW,EAAE,aAAa,CAAC,GAAG,IAAI;IAC/D8X,QAAQ,GAAGA,QAAQ,GAAGA,QAAQ,CAAC9X,OAAO,CAAC,cAAc,EAAE,QAAQ,CAAC,GAAG,IAAI;IAEvE+X,oBAAoB,GAAGxC,WAAW,CAACN,YAAY,CAAC,WAAW,CAAC;IAC5D8C,oBAAoB,GAAGA,oBAAoB,GAAG3V,UAAU,CAAC2V,oBAAoB,CAAC,GAAGzY,SAAS;IAE1FmW,eAAe,CAAC1V,KAAK,GAAG+X,QAAQ;IAChCrC,eAAe,CAACnW,SAAS,GAAGyY,oBAAoB;IAEhDtC,eAAe,CAAC1W,eAAe,GAAGiZ,kBAAkB,CAACzC,WAAW,EAAEE,eAAe,CAACnW,SAAS,CAAC;;IAE5F;IACAmW,eAAe,CAACwC,sBAAsB,GAAG,KAAK;IAE9C,OAAOxC,eAAe;EAC1B;EAEA,SAASuC,kBAAkBA,CAACzC,WAAW,EAAEjW,SAAS,EAAE;IAChD,MAAM4Y,eAAe,GAAG,CAAC,CAAC;IAC1B,MAAMC,MAAM,GAAG5C,WAAW,CAACF,oBAAoB,CAAC,GAAG,CAAC;IACpD,MAAMxW,QAAQ,GAAG,EAAE;IACnB,IAAII,OAAO,EACPmZ,WAAW,EACXnY,SAAS,EACTxT,CAAC,EAAEmC,CAAC,EAAExB,CAAC;IACX,IAAIqS,QAAQ,GAAG,CAAC;IAEhB,KAAKhT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0rB,MAAM,CAAC5rB,MAAM,EAAEE,CAAC,EAAE,EAAE;MAChCwS,OAAO,GAAG,CAAC,CAAC;;MAEZ;MACAgB,SAAS,GAAGkY,MAAM,CAAC1rB,CAAC,CAAC,CAACwoB,YAAY,CAAC,GAAG,CAAC;;MAEvC;MACA;MACA,IAAIhV,SAAS,IAAIlV,oEAAM,CAACkV,SAAS,CAAC,CAAC9M,OAAO,CAACpI,oEAAM,CAAC0N,MAAM,CAAC4f,gBAAgB,CAAC,CAAC,EAAE;QACzEpZ,OAAO,CAACgB,SAAS,GAAGA,SAAS;MACjC;MACAhB,OAAO,CAAChL,CAAC,GAAGmO,UAAU,CAACnC,SAAS,CAAC;;MAEjC;MACAhB,OAAO,CAAC7P,CAAC,GAAGgT,UAAU,CAAC+V,MAAM,CAAC1rB,CAAC,CAAC,CAACwoB,YAAY,CAAC,GAAG,CAAC,CAAC;;MAEnD;MACA,IAAKxoB,CAAC,KAAK,CAAC,IAAK,CAACwS,OAAO,CAAChL,CAAC,EAAE;QACzBgL,OAAO,CAAChL,CAAC,GAAG,CAAC;MACjB;MAEA,IAAIxH,CAAC,GAAG,CAAC,EAAE;QACP2rB,WAAW,GAAGvZ,QAAQ,CAACA,QAAQ,CAACtS,MAAM,GAAG,CAAC,CAAC;QAC3C;QACA,IAAI,CAAC6rB,WAAW,CAAChpB,CAAC,EAAE;UAChB,IAAIgpB,WAAW,CAACnY,SAAS,EAAE;YACvBmY,WAAW,CAAChpB,CAAC,GAAGrE,oEAAM,CAACkV,SAAS,CAAC,CAACrS,QAAQ,CAAC7C,oEAAM,CAACqtB,WAAW,CAACnY,SAAS,CAAC,CAAC,CAACxL,UAAU,CAAC,CAAC;UAC1F,CAAC,MAAM;YACH2jB,WAAW,CAAChpB,CAAC,GAAG6P,OAAO,CAAChL,CAAC,GAAGmkB,WAAW,CAACnkB,CAAC;UAC7C;UACAwL,QAAQ,IAAI2Y,WAAW,CAAChpB,CAAC;QAC7B;QACA;QACA,IAAI,CAAC6P,OAAO,CAAChL,CAAC,EAAE;UACZ,IAAImkB,WAAW,CAACnY,SAAS,EAAE;YACvBhB,OAAO,CAACgB,SAAS,GAAGlV,oEAAM,CAACqtB,WAAW,CAACnY,SAAS,CAAC,CAAClT,GAAG,CAAChC,oEAAM,CAACqtB,WAAW,CAAChpB,CAAC,CAAC,CAAC,CAAC8C,QAAQ,CAAC,CAAC;YACvF+M,OAAO,CAAChL,CAAC,GAAGmO,UAAU,CAACnD,OAAO,CAACgB,SAAS,CAAC;UAC7C,CAAC,MAAM;YACHhB,OAAO,CAAChL,CAAC,GAAGmkB,WAAW,CAACnkB,CAAC,GAAGmkB,WAAW,CAAChpB,CAAC;UAC7C;QACJ;MACJ;MAEA,IAAI6P,OAAO,CAAC7P,CAAC,EAAE;QACXqQ,QAAQ,IAAIR,OAAO,CAAC7P,CAAC;MACzB;;MAEA;MACAyP,QAAQ,CAACrR,IAAI,CAACyR,OAAO,CAAC;;MAEtB;MACA7R,CAAC,GAAGgV,UAAU,CAAC+V,MAAM,CAAC1rB,CAAC,CAAC,CAACwoB,YAAY,CAAC,GAAG,CAAC,CAAC;MAC3C,IAAI7nB,CAAC,EAAE;QAEH,KAAKwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAIxB,CAAC,GAAG,CAAE,EAAEwB,CAAC,EAAE,EAAE;UAC1BwpB,WAAW,GAAGvZ,QAAQ,CAACA,QAAQ,CAACtS,MAAM,GAAG,CAAC,CAAC;UAC3C0S,OAAO,GAAG,CAAC,CAAC;UACZA,OAAO,CAAChL,CAAC,GAAGmkB,WAAW,CAACnkB,CAAC,GAAGmkB,WAAW,CAAChpB,CAAC;UACzC6P,OAAO,CAAC7P,CAAC,GAAGgpB,WAAW,CAAChpB,CAAC;UACzB,IAAIgpB,WAAW,CAACnY,SAAS,EAAE;YACvBhB,OAAO,CAACgB,SAAS,GAAGlV,oEAAM,CAACqtB,WAAW,CAACnY,SAAS,CAAC,CAAClT,GAAG,CAAChC,oEAAM,CAACqtB,WAAW,CAAChpB,CAAC,CAAC,CAAC,CAAC8C,QAAQ,CAAC,CAAC;UAC3F;UACAuN,QAAQ,IAAIR,OAAO,CAAC7P,CAAC;UACrByP,QAAQ,CAACrR,IAAI,CAACyR,OAAO,CAAC;QAC1B;MACJ;IACJ;IAEAiZ,eAAe,CAAClZ,CAAC,GAAGH,QAAQ;IAC5BqZ,eAAe,CAACzY,QAAQ,GAAGA,QAAQ,GAAGH,SAAS;IAE/C,OAAO4Y,eAAe;EAC1B;EAEA,SAASI,0BAA0BA,CAACC,gBAAgB,EAAE;IAClD,IAAIC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,GAAG;;IAEP;IACAH,QAAQ,GAAGpE,MAAM,CAACwE,WAAW,CAACL,gBAAgB,CAACM,UAAU,CAACjO,IAAI,CAAC;;IAE/D;IACA6N,SAAS,GAAGK,wBAAwB,CAACN,QAAQ,CAAC;IAE9C,IAAIC,SAAS,EAAE;MACX;MACAA,SAAS,GAAG,IAAIb,WAAW,CAACa,SAAS,CAACM,MAAM,CAAC;;MAE7C;MACAN,SAAS,GAAGjjB,MAAM,CAACwjB,YAAY,CAAC3gB,KAAK,CAAC,IAAI,EAAEogB,SAAS,CAAC;;MAEtD;MACAC,SAAS,GAAI,IAAIO,SAAS,CAAC,CAAC,CAAEC,eAAe,CAACT,SAAS,EAAE,iBAAiB,CAAC;MAC3EE,GAAG,GAAGD,SAAS,CAACS,aAAa,CAAC,KAAK,CAAC,CAACC,WAAW;;MAEhD;MACAT,GAAG,GAAGvE,MAAM,CAACwE,WAAW,CAACD,GAAG,CAAC;;MAE7B;MACAU,qBAAqB,CAACV,GAAG,CAAC;IAC9B;IAEA,OAAOA,GAAG;EACd;EAEA,SAASG,wBAAwBA,CAACN,QAAQ,EAAE;IACxC,IAAIjsB,MAAM,EACN+sB,WAAW,EACXC,UAAU,EACVC,YAAY,EACZC,WAAW;IACf,IAAIhtB,CAAC,GAAG,CAAC;;IAET;;IAEA;IACAF,MAAM,GAAG,CAACisB,QAAQ,CAAC/rB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK+rB,QAAQ,CAAC/rB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI+rB,QAAQ,CAAC/rB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG+rB,QAAQ,CAAC/rB,CAAC,CAAC,CAAC,CAAC;IACnGA,CAAC,IAAI,CAAC;;IAEN;IACA6sB,WAAW,GAAG,CAACd,QAAQ,CAAC/rB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI+rB,QAAQ,CAAC/rB,CAAC,CAAC,CAAC,CAAC;IACpDA,CAAC,IAAI,CAAC;;IAEN;IACA,OAAOA,CAAC,GAAG+rB,QAAQ,CAACjsB,MAAM,EAAE;MACxB;MACAgtB,UAAU,GAAG,CAACf,QAAQ,CAAC/rB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI+rB,QAAQ,CAAC/rB,CAAC,CAAC;MACjDA,CAAC,IAAI,CAAC;;MAEN;MACA,IAAI8sB,UAAU,KAAK,IAAI,EAAE;QAErB;QACAC,YAAY,GAAG,CAAChB,QAAQ,CAAC/rB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI+rB,QAAQ,CAAC/rB,CAAC,CAAC;QACnDA,CAAC,IAAI,CAAC;;QAEN;QACAgtB,WAAW,GAAG,IAAIjN,UAAU,CAACgN,YAAY,CAAC;QAC1CC,WAAW,CAAChN,GAAG,CAAC+L,QAAQ,CAACkB,QAAQ,CAACjtB,CAAC,EAAEA,CAAC,GAAG+sB,YAAY,CAAC,CAAC;QACvD,OAAOC,WAAW;MACtB;IACJ;IAEA,OAAO,IAAI;EACf;EAEA,SAASJ,qBAAqBA,CAACM,IAAI,EAAE;IACjCC,SAAS,CAACD,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;IACrBC,SAAS,CAACD,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;IACrBC,SAAS,CAACD,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;IACrBC,SAAS,CAACD,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;EACzB;EAEA,SAASC,SAASA,CAACjH,KAAK,EAAEkH,IAAI,EAAEC,IAAI,EAAE;IAClC,MAAMC,IAAI,GAAGpH,KAAK,CAACkH,IAAI,CAAC;IACxBlH,KAAK,CAACkH,IAAI,CAAC,GAAGlH,KAAK,CAACmH,IAAI,CAAC;IACzBnH,KAAK,CAACmH,IAAI,CAAC,GAAGC,IAAI;EACtB;EAGA,SAASC,yBAAyBA,CAACzB,gBAAgB,EAAE;IACjD,IAAI0B,GAAG,GAAG;MACNC,MAAM,EAAE3B,gBAAgB,CAACM,UAAU,CAACjO,IAAI;MACxCuP,QAAQ,EAAE;IACd,CAAC;IACD,OAAO;MACHjE,WAAW,EAAE,WAAW,GAAG/B,mFAAmB,CAACiG,cAAc;MAC7D5uB,KAAK,EAAE2oB,mFAAmB,CAACkG,wBAAwB;MACnDJ,GAAG,EAAEA;IACT,CAAC;EACL;EAEA,SAASK,+BAA+BA,CAAC3B,GAAG,EAAE;IAC1C,IAAI4B,UAAU,GAAG;MACbrE,WAAW,EAAE,WAAW,GAAG/B,mFAAmB,CAACqG,aAAa;MAC5DhvB,KAAK,EAAE2oB,mFAAmB,CAACsG;IAC/B,CAAC;IACD,IAAI,CAAC9B,GAAG,EAAE;MACN,OAAO4B,UAAU;IACrB;IACA;IACA,MAAMG,YAAY,GAAG,IAAIlO,UAAU,CAAC,CAAC,GAAGmM,GAAG,CAACpsB,MAAM,CAAC;IACnDmuB,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI;IACtBA,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI;IACtBA,YAAY,CAACjO,GAAG,CAACkM,GAAG,EAAE,CAAC,CAAC;;IAExB;IACA,MAAMpsB,MAAM,GAAG,EAAE,CAAC,4CAA4C,EAAE,CAAC,iBAAiB,CAAC,CAAC,oBAAoBmuB,YAAY,CAACnuB,MAAM;IAC3H,IAAIwhB,IAAI,GAAG,IAAIvB,UAAU,CAACjgB,MAAM,CAAC;IACjC,IAAIE,CAAC,GAAG,CAAC;;IAET;IACAshB,IAAI,CAACthB,CAAC,EAAE,CAAC,GAAG,CAACF,MAAM,GAAG,UAAU,KAAK,EAAE;IACvCwhB,IAAI,CAACthB,CAAC,EAAE,CAAC,GAAG,CAACF,MAAM,GAAG,UAAU,KAAK,EAAE;IACvCwhB,IAAI,CAACthB,CAAC,EAAE,CAAC,GAAG,CAACF,MAAM,GAAG,UAAU,KAAK,CAAC;IACtCwhB,IAAI,CAACthB,CAAC,EAAE,CAAC,GAAIF,MAAM,GAAG,UAAW;;IAEjC;IACAwhB,IAAI,CAACtB,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAEhgB,CAAC,CAAC;IAC7DA,CAAC,IAAI,CAAC;;IAEN;IACAshB,IAAI,CAACtB,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAEhgB,CAAC,CAAC;IAC7GA,CAAC,IAAI,EAAE;;IAEP;IACAshB,IAAI,CAACthB,CAAC,EAAE,CAAC,GAAG,CAACiuB,YAAY,CAACnuB,MAAM,GAAG,UAAU,KAAK,EAAE;IACpDwhB,IAAI,CAACthB,CAAC,EAAE,CAAC,GAAG,CAACiuB,YAAY,CAACnuB,MAAM,GAAG,UAAU,KAAK,EAAE;IACpDwhB,IAAI,CAACthB,CAAC,EAAE,CAAC,GAAG,CAACiuB,YAAY,CAACnuB,MAAM,GAAG,UAAU,KAAK,CAAC;IACnDwhB,IAAI,CAACthB,CAAC,EAAE,CAAC,GAAIiuB,YAAY,CAACnuB,MAAM,GAAG,UAAW;;IAE9C;IACAwhB,IAAI,CAACtB,GAAG,CAACiO,YAAY,EAAEjuB,CAAC,CAAC;;IAEzB;IACAshB,IAAI,GAAGvY,MAAM,CAACwjB,YAAY,CAAC3gB,KAAK,CAAC,IAAI,EAAE0V,IAAI,CAAC;IAC5CA,IAAI,GAAGqG,MAAM,CAACuG,WAAW,CAAC5M,IAAI,CAAC;IAE/BwM,UAAU,CAACxM,IAAI,GAAG;MAAEmM,MAAM,EAAEnM;IAAK,CAAC;IAElC,OAAOwM,UAAU;EACrB;EAEA,SAASK,eAAeA,CAACC,MAAM,EAAE;IAC7B,MAAMtc,QAAQ,GAAG,CAAC,CAAC;IACnB,MAAMuc,kBAAkB,GAAG,EAAE;IAC7B,MAAM3F,oBAAoB,GAAG0F,MAAM,CAACxF,oBAAoB,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;IACnF,MAAM0F,UAAU,GAAGF,MAAM,CAACxF,oBAAoB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IAC/D,IAAIkD,gBAAgB,GAAG,IAAI;IAC3B,IAAI9Z,MAAM,EACNuc,WAAW,EACXjV,iBAAiB,EACjB4S,GAAG,EACHsC,eAAe,EACf5d,SAAS,EACTwB,QAAQ,EACRS,SAAS,EACT4b,eAAe,EACfzuB,CAAC,EAAEmC,CAAC;;IAER;IACA2P,QAAQ,CAAC4c,QAAQ,GAAG,KAAK;IACzB5c,QAAQ,CAAC6c,QAAQ,GAAG,uCAAuC;IAC3D7c,QAAQ,CAACpB,IAAI,GAAG2X,qBAAqB,CAACK,oBAAoB,EAAE,QAAQ,CAAC,GAAG,SAAS,GAAG,QAAQ;IAC5F7V,SAAS,GAAG6V,oBAAoB,CAACF,YAAY,CAAC,WAAW,CAAC;IAC1D1W,QAAQ,CAACe,SAAS,GAAGA,SAAS,GAAG8C,UAAU,CAAC9C,SAAS,CAAC,GAAGiV,kBAAkB;IAC3E,IAAI8G,eAAe,GAAGjZ,UAAU,CAAC+S,oBAAoB,CAACF,YAAY,CAAC,iBAAiB,CAAC,CAAC;IACtF;IACA,IAAI1W,QAAQ,CAACpB,IAAI,KAAK,SAAS,KAAKke,eAAe,KAAK,CAAC,IAAI1E,KAAK,CAAC0E,eAAe,CAAC,CAAC,EAAE;MAClFA,eAAe,GAAGxoB,QAAQ;IAC9B;IACA;IACA,IAAIwoB,eAAe,KAAK,CAAC,IAAIvG,qBAAqB,CAACK,oBAAoB,EAAE,SAAS,CAAC,EAAE;MACjFkG,eAAe,GAAGxoB,QAAQ;IAC9B;IAEA,IAAIwoB,eAAe,GAAG,CAAC,EAAE;MACrB9c,QAAQ,CAACqD,oBAAoB,GAAGyZ,eAAe,GAAG9c,QAAQ,CAACe,SAAS;IACxE;IAEA,IAAIG,QAAQ,GAAG2C,UAAU,CAAC+S,oBAAoB,CAACF,YAAY,CAAC,UAAU,CAAC,CAAC;IACxE1W,QAAQ,CAAC+c,yBAAyB,GAAI7b,QAAQ,KAAK,CAAC,GAAI5M,QAAQ,GAAG4M,QAAQ,GAAGlB,QAAQ,CAACe,SAAS;IAChG;IACAf,QAAQ,CAACgd,aAAa,GAAG,CAAC;IAC1Bhd,QAAQ,CAACid,kBAAkB,GAAG,IAAI;;IAElC;IACA,IAAIjd,QAAQ,CAACpB,IAAI,KAAK,SAAS,IAAIsC,QAAQ,GAAG,CAAC,EAAE;MAC7ClB,QAAQ,CAACpB,IAAI,GAAG,QAAQ;MACxB;MACAoB,QAAQ,CAACqD,oBAAoB,GAAGnC,QAAQ,GAAGlB,QAAQ,CAACe,SAAS;MAC7D;IACJ;IAEA,IAAIf,QAAQ,CAACpB,IAAI,KAAK,SAAS,EAAE;MAC7BoB,QAAQ,CAACkd,4BAA4B,GAAG,IAAI,CAAC,CAAC;MAC9Cld,QAAQ,CAACmd,mCAAmC,GAAG,IAAI,CAAC,CAAC;MACrDnd,QAAQ,CAACod,wBAAwB,GAAG,IAAI,CAAC,CAAC;MAC1Cpd,QAAQ,CAAC4D,qBAAqB,GAAG,IAAIvB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACrD;;IAEA;IACAnC,MAAM,GAAGyW,SAAS,CAACC,oBAAoB,EAAE5W,QAAQ,CAACe,SAAS,CAAC;IAC5Df,QAAQ,CAACI,MAAM,GAAG,CAACF,MAAM,CAAC;;IAE1B;IACAA,MAAM,CAACxG,KAAK,GAAG,CAAC;;IAEhB;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA,IAAI8iB,UAAU,KAAKxwB,SAAS,EAAE;MAC1BguB,gBAAgB,GAAGsC,MAAM,CAACxF,oBAAoB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;;MAErE;MACA;MACAkD,gBAAgB,CAACM,UAAU,CAACjO,IAAI,GAAG2N,gBAAgB,CAACM,UAAU,CAACjO,IAAI,CAAC5K,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;;MAEzF;MACA2Y,GAAG,GAAGL,0BAA0B,CAACC,gBAAgB,CAAC;;MAElD;MACAxS,iBAAiB,GAAGiU,yBAAyB,CAACzB,gBAAgB,CAAC;MAC/DxS,iBAAiB,CAAC,kBAAkB,CAAC,GAAG4S,GAAG;MAC3CmC,kBAAkB,CAACttB,IAAI,CAACuY,iBAAiB,CAAC;;MAE1C;MACAA,iBAAiB,GAAGuU,+BAA+B,CAAC3B,GAAG,CAAC;MACxD5S,iBAAiB,CAAC,kBAAkB,CAAC,GAAG4S,GAAG;MAC3CmC,kBAAkB,CAACttB,IAAI,CAACuY,iBAAiB,CAAC;MAE1CxH,QAAQ,CAAC2Q,iBAAiB,GAAG4L,kBAAkB;IACnD;IAEAE,WAAW,GAAGvc,MAAM,CAACG,aAAa;IAElC,KAAKnS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuuB,WAAW,CAACzuB,MAAM,EAAEE,CAAC,IAAI,CAAC,EAAE;MACxCuuB,WAAW,CAACvuB,CAAC,CAAC,CAACqS,eAAe,CAAC8c,cAAc,GAAG,aAAa;MAC7D;MACA,IAAIrd,QAAQ,CAAC2Q,iBAAiB,KAAK3kB,SAAS,EAAE;QAC1CywB,WAAW,CAACvuB,CAAC,CAAC,CAACyiB,iBAAiB,GAAG3Q,QAAQ,CAAC2Q,iBAAiB;QAC7D8L,WAAW,CAACvuB,CAAC,CAAC,CAACyiB,iBAAiB,GAAG3Q,QAAQ,CAAC2Q,iBAAiB;MACjE;MAEA,IAAI8L,WAAW,CAACvuB,CAAC,CAAC,CAACmpB,WAAW,KAAK,OAAO,EAAE;QACxC;QACAsF,eAAe,GAAGF,WAAW,CAACvuB,CAAC,CAAC,CAACqS,eAAe,CAACC,eAAe,CAACC,CAAC,CAAC,CAAC,CAAC,CAAC5P,CAAC,GAAG4rB,WAAW,CAACvuB,CAAC,CAAC,CAACqS,eAAe,CAACQ,SAAS;QAClH;QACAf,QAAQ,CAACgd,aAAa,GAAGL,eAAe;QAExC,IAAI3c,QAAQ,CAACpB,IAAI,KAAK,SAAS,EAAE;UAC7B;UACA,IAAIoB,QAAQ,CAACqD,oBAAoB,GAAG,CAAC,IACjCrD,QAAQ,CAACqD,oBAAoB,KAAK/O,QAAQ,IAC1C0L,QAAQ,CAACqD,oBAAoB,GAAGoZ,WAAW,CAACvuB,CAAC,CAAC,CAACqS,eAAe,CAACC,eAAe,CAACU,QAAQ,EAAE;YACzFlB,QAAQ,CAACqD,oBAAoB,GAAGoZ,WAAW,CAACvuB,CAAC,CAAC,CAACqS,eAAe,CAACC,eAAe,CAACU,QAAQ;UAC3F;QACJ;MACJ;IACJ;;IAEA;IACAlB,QAAQ,CAACgd,aAAa,GAAGrvB,IAAI,CAAC8K,GAAG,CAACuH,QAAQ,CAACgd,aAAa,EAAGhd,QAAQ,CAACqD,oBAAoB,GAAGrD,QAAQ,CAACqD,oBAAoB,GAAG/O,QAAS,CAAC;;IAErI;IACA;IACA;IACA;IACA,IAAI0L,QAAQ,CAACpB,IAAI,KAAK,SAAS,EAAE;MAC7B,IAAI0e,eAAe,GAAGvH,QAAQ,CAACwH,GAAG,CAAC,CAAC,CAACC,SAAS,CAACpb,KAAK,CAACqb,SAAS;MAC9D,IAAI,CAACH,eAAe,EAAE;QAClB,MAAMI,sBAAsB,GAAG3H,QAAQ,CAACwH,GAAG,CAAC,CAAC,CAACC,SAAS,CAACpb,KAAK,CAACsb,sBAAsB,KAAK,IAAI,IAAI,CAACtF,KAAK,CAACrC,QAAQ,CAACwH,GAAG,CAAC,CAAC,CAACC,SAAS,CAACpb,KAAK,CAACsb,sBAAsB,CAAC,GAAG3H,QAAQ,CAACwH,GAAG,CAAC,CAAC,CAACC,SAAS,CAACpb,KAAK,CAACsb,sBAAsB,GAAG,CAAC;QAC1NJ,eAAe,GAAGX,eAAe,GAAGe,sBAAsB;MAC9D;MACA,IAAIC,kBAAkB,GAAGhwB,IAAI,CAACgD,GAAG,CAACqP,QAAQ,CAACqD,oBAAoB,GAAG,EAAE,8BAA6BrD,QAAQ,CAACqD,oBAAoB,GAAG,CAAC,CAAC;MACnI,IAAIoa,SAAS,GAAG9vB,IAAI,CAAC8K,GAAG,CAACklB,kBAAkB,EAAEL,eAAe,CAAC;MAC7D;MACA,IAAIM,UAAU,GAAGH,SAAS,GAAId,eAAe,GAAG,GAAI;;MAEpD;MACArG,qBAAqB,GAAG;QACpB,WAAW,EAAE;UACT,QAAQ,EAAE;YACN,mBAAmB,EAAEP,QAAQ,CAACwH,GAAG,CAAC,CAAC,CAACC,SAAS,CAAChD,MAAM,CAACqD,iBAAiB;YACtE,wBAAwB,EAAE9H,QAAQ,CAACwH,GAAG,CAAC,CAAC,CAACC,SAAS,CAAChD,MAAM,CAACsD,sBAAsB;YAChF,gCAAgC,EAAE/H,QAAQ,CAACwH,GAAG,CAAC,CAAC,CAACC,SAAS,CAAChD,MAAM,CAACuD;UACtE,CAAC;UACD,iBAAiB,EAAE;YACfC,uBAAuB,EAAEjI,QAAQ,CAACwH,GAAG,CAAC,CAAC,CAACC,SAAS,CAACS,eAAe,CAACD;UACtE,CAAC;UACD,OAAO,EAAE;YACL,WAAW,EAAEjI,QAAQ,CAACwH,GAAG,CAAC,CAAC,CAACC,SAAS,CAACpb,KAAK,CAACqb;UAChD;QACJ;MACJ,CAAC;MAED1H,QAAQ,CAACmI,MAAM,CAAC;QACZ,WAAW,EAAE;UACT,QAAQ,EAAE;YACN,mBAAmB,EAAEN,UAAU;YAC/B,wBAAwB,EAAEA,UAAU;YACpC,gCAAgC,EAAEA;UACtC,CAAC;UACD,iBAAiB,EAAE;YACfI,uBAAuB,EAAE;UAC7B,CAAC;UACD,OAAO,EAAE;YACL,WAAW,EAAEP;UACjB;QACJ;MACJ,CAAC,CAAC;IACN;;IAEA;IACA,OAAOzd,QAAQ,CAAC2Q,iBAAiB;;IAEjC;IACA;IACA;IACA,IAAI3Q,QAAQ,CAACpB,IAAI,KAAK,QAAQ,EAAE;MAC5B;MACA;MACA,IAAIuf,YAAY,GAAGrI,aAAa,CAACsI,QAAQ,CAAC,CAAC;MAC3C,IAAID,YAAY,IAAIA,YAAY,CAACzB,eAAe,EAAE;QAC9CA,eAAe,GAAGyB,YAAY,CAACzB,eAAe;MAClD,CAAC,MAAM;QACH,KAAKxuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuuB,WAAW,CAACzuB,MAAM,EAAEE,CAAC,EAAE,EAAE;UACrC,IAAIuuB,WAAW,CAACvuB,CAAC,CAAC,CAACmpB,WAAW,KAAKhQ,SAAS,CAACsB,KAAK,IAAI8T,WAAW,CAACvuB,CAAC,CAAC,CAACmpB,WAAW,KAAKhQ,SAAS,CAACoB,KAAK,EAAE;YAClGnI,QAAQ,GAAGmc,WAAW,CAACvuB,CAAC,CAAC,CAACqS,eAAe,CAACC,eAAe,CAACC,CAAC;YAC3D3B,SAAS,GAAGwB,QAAQ,CAAC,CAAC,CAAC,CAAC5K,CAAC;YACzB,IAAIgnB,eAAe,KAAK1wB,SAAS,EAAE;cAC/B0wB,eAAe,GAAG5d,SAAS;YAC/B;YACA4d,eAAe,GAAG/uB,IAAI,CAAC8K,GAAG,CAACikB,eAAe,EAAE5d,SAAS,CAAC;YACtD;YACA;YACAkB,QAAQ,CAAC+c,yBAAyB,GAAGpvB,IAAI,CAAC8K,GAAG,CAACuH,QAAQ,CAAC+c,yBAAyB,EAAEN,WAAW,CAACvuB,CAAC,CAAC,CAACqS,eAAe,CAACC,eAAe,CAACU,QAAQ,CAAC;UAC9I;QACJ;MACJ;MACA,IAAIwb,eAAe,GAAG,CAAC,EAAE;QACrB;QACA1c,QAAQ,CAAC0c,eAAe,GAAGA,eAAe;QAC1C,KAAKxuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuuB,WAAW,CAACzuB,MAAM,EAAEE,CAAC,EAAE,EAAE;UACrCoS,QAAQ,GAAGmc,WAAW,CAACvuB,CAAC,CAAC,CAACqS,eAAe,CAACC,eAAe,CAACC,CAAC;UAC3D,KAAKpQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiQ,QAAQ,CAACtS,MAAM,EAAEqC,CAAC,EAAE,EAAE;YAClC,IAAI,CAACiQ,QAAQ,CAACjQ,CAAC,CAAC,CAACqR,SAAS,EAAE;cACxBpB,QAAQ,CAACjQ,CAAC,CAAC,CAACqR,SAAS,GAAGpB,QAAQ,CAACjQ,CAAC,CAAC,CAACqF,CAAC,CAAC/B,QAAQ,CAAC,CAAC;YACpD;YACA2M,QAAQ,CAACjQ,CAAC,CAAC,CAACqF,CAAC,IAAIgnB,eAAe;UACpC;UACA,IAAID,WAAW,CAACvuB,CAAC,CAAC,CAACmpB,WAAW,KAAKhQ,SAAS,CAACsB,KAAK,IAAI8T,WAAW,CAACvuB,CAAC,CAAC,CAACmpB,WAAW,KAAKhQ,SAAS,CAACoB,KAAK,EAAE;YAClGvI,MAAM,CAACxG,KAAK,GAAG/L,IAAI,CAACgD,GAAG,CAAC2P,QAAQ,CAAC,CAAC,CAAC,CAAC5K,CAAC,EAAEwK,MAAM,CAACxG,KAAK,CAAC;YACpD+iB,WAAW,CAACvuB,CAAC,CAAC,CAACqS,eAAe,CAAC8d,sBAAsB,GAAGne,MAAM,CAACxG,KAAK;UACxE;QACJ;QACAwG,MAAM,CAACxG,KAAK,IAAIsG,QAAQ,CAACe,SAAS;MACtC;IACJ;;IAEA;IACA;IACAf,QAAQ,CAAC+c,yBAAyB,GAAGpvB,IAAI,CAACC,KAAK,CAACoS,QAAQ,CAAC+c,yBAAyB,GAAG,IAAI,CAAC,GAAG,IAAI;IACjG7c,MAAM,CAACgB,QAAQ,GAAGlB,QAAQ,CAAC+c,yBAAyB;IAEpD,OAAO/c,QAAQ;EACnB;EAEA,SAASse,QAAQA,CAACjS,IAAI,EAAE;IACpB,IAAIiQ,MAAM,GAAG,IAAI;IAEjB,IAAIiC,MAAM,CAAC7D,SAAS,EAAE;MAClB,MAAM8D,MAAM,GAAG,IAAID,MAAM,CAAC7D,SAAS,CAAC,CAAC;MAErC4B,MAAM,GAAGkC,MAAM,CAAC7D,eAAe,CAACtO,IAAI,EAAE,UAAU,CAAC;MACjD,IAAIiQ,MAAM,CAACxF,oBAAoB,CAAC,aAAa,CAAC,CAAC9oB,MAAM,GAAG,CAAC,EAAE;QACvD,MAAM,IAAIiF,KAAK,CAAC,6BAA6B,CAAC;MAClD;IACJ;IAEA,OAAOqpB,MAAM;EACjB;EAEA,SAASmC,OAAOA,CAAA,EAAG;IACf,OAAO,IAAI;EACf;EAEA,SAASC,aAAaA,CAACrS,IAAI,EAAE;IACzB,IAAIiQ,MAAM,GAAG,IAAI;IACjB,IAAItc,QAAQ,GAAG,IAAI;IAEnB,MAAMlB,SAAS,GAAGyf,MAAM,CAACI,WAAW,CAACC,GAAG,CAAC,CAAC;;IAE1C;IACAtC,MAAM,GAAGgC,QAAQ,CAACjS,IAAI,CAAC;IAEvB,MAAMwS,YAAY,GAAGN,MAAM,CAACI,WAAW,CAACC,GAAG,CAAC,CAAC;IAE7C,IAAItC,MAAM,KAAK,IAAI,EAAE;MACjB,OAAO,IAAI;IACf;;IAEA;IACAtc,QAAQ,GAAGqc,eAAe,CAACC,MAAM,EAAE,IAAIja,IAAI,CAAC,CAAC,CAAC;IAE9C,MAAMyc,YAAY,GAAGP,MAAM,CAACI,WAAW,CAACC,GAAG,CAAC,CAAC;IAE7CngB,MAAM,CAACsgB,IAAI,CAAC,iCAAiC,GAAG,CAACF,YAAY,GAAG/f,SAAS,EAAEkgB,WAAW,CAAC,CAAC,CAAC,GAAG,gBAAgB,GAAG,CAACF,YAAY,GAAGD,YAAY,EAAEG,WAAW,CAAC,CAAC,CAAC,GAAG,aAAa,GAAG,CAAC,CAACF,YAAY,GAAGhgB,SAAS,IAAI,IAAI,EAAEkgB,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAExO,OAAOhf,QAAQ;EACnB;EAEA,SAASH,KAAKA,CAAA,EAAG;IACb;IACA,IAAIyW,qBAAqB,EAAE;MACvBP,QAAQ,CAACmI,MAAM,CAAC5H,qBAAqB,CAAC;IAC1C;EACJ;EAEA9a,QAAQ,GAAG;IACPyjB,KAAK,EAAEP,aAAa;IACpBD,OAAO,EAAEA,OAAO;IAChB5e,KAAK,EAAEA;EACX,CAAC;EAEDR,KAAK,CAAC,CAAC;EAEP,OAAO7D,QAAQ;AACnB;AAEAgX,SAAS,CAACzV,qBAAqB,GAAG,WAAW;AAC7C,+DAAexB,6DAAY,CAACsB,eAAe,CAAC2V,SAAS,CAAC;;;;;;;;;;;;ACp3BtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACsD;;AAEtD;AACA;AACA;AACA;AACA,MAAM0M,iBAAiB,SAAS/gB,kEAAU,CAAC;EAEvC;AACJ;AACA;EACIuX,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACyJ,aAAa,GAAG,aAAa;;IAElC;AACR;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,iBAAiB;;IAE1C;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,eAAe;;IAEnC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,cAAc;;IAEnC;AACR;AACA;AACA;IACQ,IAAI,CAACC,0BAA0B,GAAG,oBAAoB;;IAEtD;AACR;AACA;AACA;IACQ,IAAI,CAACC,oBAAoB,GAAG,oBAAoB;;IAEhD;AACR;AACA;AACA;IACQ,IAAI,CAACC,uBAAuB,GAAG,sBAAsB;;IAErD;AACR;AACA;AACA;IACQ,IAAI,CAACC,0BAA0B,GAAG,yBAAyB;;IAE3D;AACR;AACA;AACA;IACQ,IAAI,CAACC,wBAAwB,GAAG,uBAAuB;;IAEvD;AACR;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,iBAAiB;;IAE1C;AACR;AACA;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,OAAO;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACvK,0BAA0B,GAAG,0BAA0B;;IAE5D;AACR;AACA;AACA;IACQ,IAAI,CAACnB,yBAAyB,GAAG,yBAAyB;IAC1D;AACR;AACA;AACA;IACQ,IAAI,CAAC2L,wBAAwB,GAAG,wBAAwB;;IAExD;AACR;AACA;AACA;IACQ,IAAI,CAACC,0BAA0B,GAAG,0BAA0B;;IAE5D;AACR;AACA;AACA;IACQ,IAAI,CAACC,GAAG,GAAG,KAAK;;IAEhB;AACR;AACA;AACA;IACQ,IAAI,CAACC,wBAAwB,GAAG,wBAAwB;;IAExD;AACR;AACA;AACA;IACQ,IAAI,CAACC,yBAAyB,GAAG,yBAAyB;;IAE1D;AACR;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,gBAAgB;;IAEvC;AACR;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,gBAAgB;;IAEvC;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,eAAe;;IAErC;AACR;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,aAAa;;IAEjC;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,eAAe;;IAErC;AACR;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,qBAAqB;;IAElD;AACR;AACA;AACA;IACQ,IAAI,CAACC,uBAAuB,GAAG,uBAAuB;;IAEtD;AACR;AACA;AACA;IACQ,IAAI,CAACC,wBAAwB,GAAG,wBAAwB;;IAExD;AACR;AACA;AACA;IACQ,IAAI,CAACC,uBAAuB,GAAG,uBAAuB;;IAEtD;AACR;AACA;AACA;IACQ,IAAI,CAACC,kBAAkB,GAAG,kBAAkB;;IAE5C;AACR;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,qBAAqB;;IAElD;AACR;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,oBAAoB;;IAE/C;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,eAAe;;IAErC;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,iBAAiB;;IAEzC;AACR;AACA;AACA;IACQ,IAAI,CAACC,kBAAkB,GAAG,mBAAmB;;IAE7C;AACR;AACA;AACA;IACQ,IAAI,CAACC,kBAAkB,GAAG,mBAAmB;;IAE7C;AACR;AACA;AACA;IACQ,IAAI,CAACC,wBAAwB,GAAG,wBAAwB;;IAExD;AACR;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,oBAAoB;;IAE7C;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,gBAAgB;;IAExC;AACR;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,UAAU;;IAE3B;AACR;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,SAAS;;IAEzB;AACR;AACA;AACA;IACQ,IAAI,CAACC,6BAA6B,GAAG,6BAA6B;;IAElE;AACR;AACA;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,YAAY;;IAE/B;AACR;AACA;AACA;IACQ,IAAI,CAAClM,aAAa,GAAG,aAAa;;IAElC;AACR;AACA;AACA;IACQ,IAAI,CAACmM,gBAAgB,GAAG,iBAAiB;;IAEzC;AACR;AACA;AACA;IACQ,IAAI,CAACC,wBAAwB,GAAG,wBAAwB;;IAExD;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,SAAS;;IAEzB;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,gBAAgB;;IAExC;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,eAAe;;IAErC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,eAAe;;IAErC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,oBAAoB,GAAG,qBAAqB;;IAEjD;AACR;AACA;AACA;IACQ,IAAI,CAACC,oBAAoB,GAAG,oBAAoB;;IAEhD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,wBAAwB,GAAG,wBAAwB;;IAExD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,oBAAoB,GAAG,oBAAoB;;IAEhD;AACR;AACA;AACA;IACQ,IAAI,CAAC/M,eAAe,GAAG,gBAAgB;;IAEvC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACgN,gBAAgB,GAAG,iBAAiB;;IAEzC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,kBAAkB;;IAE3C;AACR;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,qBAAqB;;IAElD;AACR;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,gBAAgB;;IAEvC;AACR;AACA;AACA;IACQ,IAAI,CAAClN,gBAAgB,GAAG,iBAAiB;;IAEzC;AACR;AACA;AACA;IACQ,IAAI,CAACmN,gBAAgB,GAAG,iBAAiB;;IAEzC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,iBAAiB;;IAEzC;AACR;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,qBAAqB;;IAElD;AACR;AACA;AACA;IACQ,IAAI,CAACC,uBAAuB,GAAG,uBAAuB;;IAEtD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,iBAAiB;;IAEzC;AACR;AACA;AACA;IACQ,IAAI,CAACre,yBAAyB,GAAG,yBAAyB;;IAE1D;AACR;AACA;AACA;IACQ,IAAI,CAACse,mBAAmB,GAAG,kBAAkB;;IAE7C;AACR;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,oBAAoB;;IAEjD;AACR;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,sBAAsB;;IAEnD;AACR;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,sBAAsB;;IAEnD;AACR;AACA;AACA;IACQ,IAAI,CAACC,sCAAsC,GAAG,oCAAoC;;IAElF;AACR;AACA;AACA;IACQ,IAAI,CAACC,kCAAkC,GAAG,iCAAiC;;IAE3E;AACR;AACA;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,YAAY;;IAE/B;AACR;AACA;AACA;IACQ,IAAI,CAACC,oCAAoC,GAAG,kCAAkC;;IAE9E;AACR;AACA;AACA;IACQ,IAAI,CAACC,kCAAkC,GAAG,gCAAgC;EAC9E;AACJ;AAEA,IAAIC,iBAAiB,GAAG,IAAIpE,iBAAiB,CAAC,CAAC;AAC/C,+DAAeoE,iBAAiB;;;;;;;;;;;ACnfhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,+DAAe;EACXC,uBAAuB,EAAE,iBAAiB;EAC1CrH,uBAAuB,EAAE,oBAAoB;EAC7CJ,wBAAwB,EAAE,yBAAyB;EACnD0H,uCAAuC,EAAE,wCAAwC;EACjFvH,aAAa,EAAE,sCAAsC;EACrDJ,cAAc,EAAE,sCAAsC;EACtD4H,aAAa,EAAE,sCAAsC;EACrDC,iBAAiB,EAAE,sCAAsC;EACzDC,6BAA6B,EAAE,MAAM;EACrCC,+BAA+B,EAAE,QAAQ;EACzCC,6BAA6B,EAAE,MAAM;EACrCC,sBAAsB,EAAE,MAAM;EAC9BC,sBAAsB,EAAE,MAAM;EAC9BC,uBAAuB,EAAE;IACrBC,eAAe,EAAE,iBAAiB;IAClCC,eAAe,EAAE,iBAAiB;IAClCC,eAAe,EAAE,iBAAiB;IAClCC,yBAAyB,EAAE;EAC/B,CAAC;EACDC,kBAAkB,EAAE;IAChBC,QAAQ,EAAE;MACNC,gBAAgB,EAAE,kBAAkB;MACpCC,gBAAgB,EAAE,kBAAkB;MACpCC,gBAAgB,EAAE,kBAAkB;MACpCC,gBAAgB,EAAE,kBAAkB;MACpCC,aAAa,EAAE;IACnB;EACJ,CAAC;EACDC,kBAAkB,EAAE;IAChBC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,UAAU;IACpBC,iBAAiB,EAAE,mBAAmB;IACtCC,iBAAiB,EAAE,mBAAmB;IACtCC,cAAc,EAAE,gBAAgB;IAChCC,cAAc,EAAE;EACpB;AACJ,CAAC;;;;;;;;;;;ACzED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM3iB,WAAW,CAAC;EACdkT,WAAWA,CAACzJ,IAAI,EAAEE,OAAO,EAAEE,IAAI,EAAE;IAC7B,IAAI,CAACJ,IAAI,GAAGA,IAAI,IAAI,IAAI;IACxB,IAAI,CAACE,OAAO,GAAGA,OAAO,IAAI,IAAI;IAC9B,IAAI,CAACE,IAAI,GAAGA,IAAI,IAAI,IAAI;EAC5B;AACJ;AAEA,+DAAe7J,WAAW;;;;;;;;;;;AC1C1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM+P,SAAS,CAAC;EACZ;EACAmD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACpC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACG,WAAW,GAAG,IAAI;IACvB,IAAI,CAACzU,KAAK,GAAGomB,GAAG;IAChB,IAAI,CAAChR,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC1a,KAAK,GAAG0rB,GAAG;IAChB,IAAI,CAACrhB,GAAG,GAAGqhB,GAAG;IACd,IAAI,CAAClkB,QAAQ,GAAGkkB,GAAG;IACnB,IAAI,CAACtlB,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACyT,WAAW,GAAG,IAAI;EAC3B;AACJ;AAEA,+DAAehB,SAAS;;;;;;;;;;;;AClDxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEqD;;AAErD;AACA;AACA;AACA;AACA,MAAMjU,eAAe,CAAC;EAClBoX,WAAWA,CAACrU,GAAG,EAAE;IACb,IAAI,CAACgkB,MAAM,GAAG/mB,eAAe,CAACgnB,eAAe;IAC7C,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC3hB,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACzC,SAAS,GAAGikB,GAAG;IACpB,IAAI,CAACI,WAAW,GAAGJ,GAAG;IACtB,IAAI,CAACK,UAAU,GAAGL,GAAG;IACrB,IAAI,CAACM,gBAAgB,GAAGN,GAAG;IAC3B,IAAI,CAAClkB,QAAQ,GAAGkkB,GAAG;IACnB,IAAI,CAACO,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC5mB,KAAK,GAAGomB,GAAG;IAChB,IAAI,CAACS,cAAc,GAAGT,GAAG;IACzB,IAAI,CAACpkB,SAAS,GAAG,IAAI;IACrB,IAAI,CAAClI,KAAK,GAAG,IAAI;IACjB,IAAI,CAACgH,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACgmB,YAAY,GAAG,aAAa;IACjC,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACnnB,SAAS,GAAGsmB,GAAG;IACpB,IAAI,CAACrkB,SAAS,GAAGqkB,GAAG;IACpB,IAAI,CAACxmB,IAAI,GAAG,IAAI;IAChB,IAAI,CAACyC,GAAG,GAAGA,GAAG,IAAI,IAAI;IACtB,IAAI,CAAC6kB,aAAa,GAAG,IAAI;EAC7B;EAEAC,uBAAuBA,CAAA,EAAG;IACtB,OAAQ,IAAI,CAACvnB,IAAI,IAAI,IAAI,CAACA,IAAI,KAAKL,gEAAW,CAAC6nB,iBAAiB;EACpE;EAEAC,OAAOA,CAACtH,IAAI,EAAE;IACV,IAAI,CAACngB,IAAI,GAAGmgB,IAAI,IAAIA,IAAI,CAACuH,IAAI,GAAG/nB,gEAAW,CAAC6nB,iBAAiB,GAAG7nB,gEAAW,CAACgoB,kBAAkB;IAC9F,IAAI,CAACllB,GAAG,GAAG0d,IAAI,IAAIA,IAAI,CAAC1d,GAAG,GAAG0d,IAAI,CAAC1d,GAAG,GAAG,IAAI;IAC7C,IAAI,CAACvI,KAAK,GAAGimB,IAAI,IAAIA,IAAI,CAACjmB,KAAK,GAAGimB,IAAI,CAACjmB,KAAK,CAACY,KAAK,GAAG,GAAG,GAAGqlB,IAAI,CAACjmB,KAAK,CAACiL,GAAG,GAAG,IAAI;IAChF,IAAI,CAAC/C,SAAS,GAAG+d,IAAI,IAAIA,IAAI,CAAC/d,SAAS,GAAG+d,IAAI,CAAC/d,SAAS,GAAG,IAAI;IAC/D,IAAI,CAAClB,cAAc,GAAGif,IAAI,IAAIA,IAAI,CAACjf,cAAc,GAAGif,IAAI,CAACjf,cAAc,GAAG,IAAI;EAClF;AACJ;AAEAxB,eAAe,CAACgnB,eAAe,GAAG,UAAU;AAC5ChnB,eAAe,CAACkoB,eAAe,GAAG,UAAU;AAE5C,+DAAeloB,eAAe;;;;;;;;;;;;;;;ACjF9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACd;AACJ;AACA;EACImX,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;IACQ,IAAI,CAAC+Q,KAAK,GAAG,IAAI;IACjB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC7nB,IAAI,GAAG,IAAI;IAChB;AACR;AACA;AACA;IACQ,IAAI,CAACyC,GAAG,GAAG,IAAI;IACf;AACR;AACA;AACA;IACQ,IAAI,CAACqlB,SAAS,GAAG,IAAI;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAAC5tB,KAAK,GAAG,IAAI;IACjB;AACR;AACA;AACA;IACQ,IAAI,CAAC6tB,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB;AACR;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,EAAE;IACf;AACR;AACA;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,IAAI;;IAEhB;AACR;AACA;AACA;IACQ,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB;AACR;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B;AACR;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B;AACR;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,IAAI;EACrC;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnB;AACJ;AACA;EACI9R,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;IACQ,IAAI,CAAC+R,CAAC,GAAG,IAAI;IACb;AACR;AACA;AACA;IACQ,IAAI,CAAC52B,CAAC,GAAG,IAAI;IACb;AACR;AACA;AACA;IACQ,IAAI,CAACnC,CAAC,GAAG,EAAE;EACf;AACJ;AAEA6P,WAAW,CAACmpB,GAAG,GAAG,KAAK;AACvBnpB,WAAW,CAACopB,IAAI,GAAG,MAAM;AACzBppB,WAAW,CAACqpB,QAAQ,GAAG,KAAK;AAC5BrpB,WAAW,CAACspB,oBAAoB,GAAG,gBAAgB;AACnDtpB,WAAW,CAAC6nB,iBAAiB,GAAG,uBAAuB;AACvD7nB,WAAW,CAACupB,kBAAkB,GAAG,cAAc;AAC/CvpB,WAAW,CAACgoB,kBAAkB,GAAG,cAAc;AAC/ChoB,WAAW,CAACwpB,gCAAgC,GAAG,2BAA2B;AAC1ExpB,WAAW,CAAC0C,8BAA8B,GAAG,qBAAqB;AAClE1C,WAAW,CAACypB,kBAAkB,GAAG,cAAc;AAC/CzpB,WAAW,CAAC0pB,OAAO,GAAG,SAAS;AAC/B1pB,WAAW,CAAC2pB,qBAAqB,GAAG,iBAAiB;AACrD3pB,WAAW,CAAC4pB,UAAU,GAAG,OAAO;;;;;;;UCpLhC;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA,8CAA8C;;;;;WCA9C;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEyC;;AAEzC;AACA,IAAInsB,OAAO,GAAI,OAAOuiB,MAAM,KAAK,WAAW,IAAIA,MAAM,IAAK6J,MAAM;AAEjE,IAAIlT,UAAM,GAAGlZ,OAAO,CAACkZ,MAAM;AAC3B,IAAI,CAACA,UAAM,EAAE;EACTA,UAAM,GAAGlZ,OAAO,CAACkZ,MAAM,GAAG,CAAC,CAAC;AAChC;AAEAA,UAAM,CAACzC,UAAU,GAAGA,sDAAU;AAE9B,+DAAeyC,UAAM,EAAC", "sources": ["webpack://dashjs/webpack/universalModuleDefinition", "webpack://dashjs/./externals/BigInteger.js", "webpack://dashjs/./src/core/FactoryMaker.js", "webpack://dashjs/./src/core/errors/ErrorsBase.js", "webpack://dashjs/./src/core/events/EventsBase.js", "webpack://dashjs/./src/mss/MssFragmentInfoController.js", "webpack://dashjs/./src/mss/MssFragmentMoofProcessor.js", "webpack://dashjs/./src/mss/MssFragmentMoovProcessor.js", "webpack://dashjs/./src/mss/MssFragmentProcessor.js", "webpack://dashjs/./src/mss/MssHandler.js", "webpack://dashjs/./src/mss/errors/MssErrors.js", "webpack://dashjs/./src/mss/parser/MssParser.js", "webpack://dashjs/./src/streaming/MediaPlayerEvents.js", "webpack://dashjs/./src/streaming/constants/ProtectionConstants.js", "webpack://dashjs/./src/streaming/vo/DashJSError.js", "webpack://dashjs/./src/streaming/vo/DataChunk.js", "webpack://dashjs/./src/streaming/vo/FragmentRequest.js", "webpack://dashjs/./src/streaming/vo/metrics/HTTPRequest.js", "webpack://dashjs/webpack/bootstrap", "webpack://dashjs/webpack/runtime/define property getters", "webpack://dashjs/webpack/runtime/hasOwnProperty shorthand", "webpack://dashjs/webpack/runtime/make namespace object", "webpack://dashjs/./src/mss/index.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"dashjs\"] = factory();\n\telse\n\t\troot[\"dashjs\"] = factory();\n})(self, function() {\nreturn ", "var bigInt = function (undefined) {\n    'use strict';\n    var BASE = 1e7, LOG_BASE = 7, MAX_INT = 9007199254740992, MAX_INT_ARR = smallToArray(MAX_INT),\n        DEFAULT_ALPHABET = '0123456789abcdefghijklmnopqrstuvwxyz';\n    var supportsNativeBigInt = typeof BigInt === 'function';\n\n    function Integer(v, radix, alphabet, caseSensitive) {\n        if (typeof v === 'undefined') return Integer[0];\n        if (typeof radix !== 'undefined') return +radix === 10 && !alphabet ? parseValue(v) : parseBase(v, radix, alphabet, caseSensitive);\n        return parseValue(v)\n    }\n\n    function BigInteger(value, sign) {\n        this.value = value;\n        this.sign = sign;\n        this.isSmall = false\n    }\n\n    BigInteger.prototype = Object.create(Integer.prototype);\n\n    function SmallInteger(value) {\n        this.value = value;\n        this.sign = value < 0;\n        this.isSmall = true\n    }\n\n    SmallInteger.prototype = Object.create(Integer.prototype);\n\n    function NativeBigInt(value) {\n        this.value = value\n    }\n\n    NativeBigInt.prototype = Object.create(Integer.prototype);\n\n    function isPrecise(n) {\n        return -MAX_INT < n && n < MAX_INT\n    }\n\n    function smallToArray(n) {\n        if (n < 1e7) return [n];\n        if (n < 1e14) return [n % 1e7, Math.floor(n / 1e7)];\n        return [n % 1e7, Math.floor(n / 1e7) % 1e7, Math.floor(n / 1e14)]\n    }\n\n    function arrayToSmall(arr) {\n        trim(arr);\n        var length = arr.length;\n        if (length < 4 && compareAbs(arr, MAX_INT_ARR) < 0) {\n            switch (length) {\n                case 0:\n                    return 0;\n                case 1:\n                    return arr[0];\n                case 2:\n                    return arr[0] + arr[1] * BASE;\n                default:\n                    return arr[0] + (arr[1] + arr[2] * BASE) * BASE\n            }\n        }\n        return arr\n    }\n\n    function trim(v) {\n        var i = v.length;\n        while (v[--i] === 0) ;\n        v.length = i + 1\n    }\n\n    function createArray(length) {\n        var x = new Array(length);\n        var i = -1;\n        while (++i < length) {\n            x[i] = 0\n        }\n        return x\n    }\n\n    function truncate(n) {\n        if (n > 0) return Math.floor(n);\n        return Math.ceil(n)\n    }\n\n    function add(a, b) {\n        var l_a = a.length, l_b = b.length, r = new Array(l_a), carry = 0, base = BASE, sum, i;\n        for (i = 0; i < l_b; i++) {\n            sum = a[i] + b[i] + carry;\n            carry = sum >= base ? 1 : 0;\n            r[i] = sum - carry * base\n        }\n        while (i < l_a) {\n            sum = a[i] + carry;\n            carry = sum === base ? 1 : 0;\n            r[i++] = sum - carry * base\n        }\n        if (carry > 0) r.push(carry);\n        return r\n    }\n\n    function addAny(a, b) {\n        if (a.length >= b.length) return add(a, b);\n        return add(b, a)\n    }\n\n    function addSmall(a, carry) {\n        var l = a.length, r = new Array(l), base = BASE, sum, i;\n        for (i = 0; i < l; i++) {\n            sum = a[i] - base + carry;\n            carry = Math.floor(sum / base);\n            r[i] = sum - carry * base;\n            carry += 1\n        }\n        while (carry > 0) {\n            r[i++] = carry % base;\n            carry = Math.floor(carry / base)\n        }\n        return r\n    }\n\n    BigInteger.prototype.add = function (v) {\n        var n = parseValue(v);\n        if (this.sign !== n.sign) {\n            return this.subtract(n.negate())\n        }\n        var a = this.value, b = n.value;\n        if (n.isSmall) {\n            return new BigInteger(addSmall(a, Math.abs(b)), this.sign)\n        }\n        return new BigInteger(addAny(a, b), this.sign)\n    };\n    BigInteger.prototype.plus = BigInteger.prototype.add;\n    SmallInteger.prototype.add = function (v) {\n        var n = parseValue(v);\n        var a = this.value;\n        if (a < 0 !== n.sign) {\n            return this.subtract(n.negate())\n        }\n        var b = n.value;\n        if (n.isSmall) {\n            if (isPrecise(a + b)) return new SmallInteger(a + b);\n            b = smallToArray(Math.abs(b))\n        }\n        return new BigInteger(addSmall(b, Math.abs(a)), a < 0)\n    };\n    SmallInteger.prototype.plus = SmallInteger.prototype.add;\n    NativeBigInt.prototype.add = function (v) {\n        return new NativeBigInt(this.value + parseValue(v).value)\n    };\n    NativeBigInt.prototype.plus = NativeBigInt.prototype.add;\n\n    function subtract(a, b) {\n        var a_l = a.length, b_l = b.length, r = new Array(a_l), borrow = 0, base = BASE, i, difference;\n        for (i = 0; i < b_l; i++) {\n            difference = a[i] - borrow - b[i];\n            if (difference < 0) {\n                difference += base;\n                borrow = 1\n            } else borrow = 0;\n            r[i] = difference\n        }\n        for (i = b_l; i < a_l; i++) {\n            difference = a[i] - borrow;\n            if (difference < 0) difference += base; else {\n                r[i++] = difference;\n                break\n            }\n            r[i] = difference\n        }\n        for (; i < a_l; i++) {\n            r[i] = a[i]\n        }\n        trim(r);\n        return r\n    }\n\n    function subtractAny(a, b, sign) {\n        var value;\n        if (compareAbs(a, b) >= 0) {\n            value = subtract(a, b)\n        } else {\n            value = subtract(b, a);\n            sign = !sign\n        }\n        value = arrayToSmall(value);\n        if (typeof value === 'number') {\n            if (sign) value = -value;\n            return new SmallInteger(value)\n        }\n        return new BigInteger(value, sign)\n    }\n\n    function subtractSmall(a, b, sign) {\n        var l = a.length, r = new Array(l), carry = -b, base = BASE, i, difference;\n        for (i = 0; i < l; i++) {\n            difference = a[i] + carry;\n            carry = Math.floor(difference / base);\n            difference %= base;\n            r[i] = difference < 0 ? difference + base : difference\n        }\n        r = arrayToSmall(r);\n        if (typeof r === 'number') {\n            if (sign) r = -r;\n            return new SmallInteger(r)\n        }\n        return new BigInteger(r, sign)\n    }\n\n    BigInteger.prototype.subtract = function (v) {\n        var n = parseValue(v);\n        if (this.sign !== n.sign) {\n            return this.add(n.negate())\n        }\n        var a = this.value, b = n.value;\n        if (n.isSmall) return subtractSmall(a, Math.abs(b), this.sign);\n        return subtractAny(a, b, this.sign)\n    };\n    BigInteger.prototype.minus = BigInteger.prototype.subtract;\n    SmallInteger.prototype.subtract = function (v) {\n        var n = parseValue(v);\n        var a = this.value;\n        if (a < 0 !== n.sign) {\n            return this.add(n.negate())\n        }\n        var b = n.value;\n        if (n.isSmall) {\n            return new SmallInteger(a - b)\n        }\n        return subtractSmall(b, Math.abs(a), a >= 0)\n    };\n    SmallInteger.prototype.minus = SmallInteger.prototype.subtract;\n    NativeBigInt.prototype.subtract = function (v) {\n        return new NativeBigInt(this.value - parseValue(v).value)\n    };\n    NativeBigInt.prototype.minus = NativeBigInt.prototype.subtract;\n    BigInteger.prototype.negate = function () {\n        return new BigInteger(this.value, !this.sign)\n    };\n    SmallInteger.prototype.negate = function () {\n        var sign = this.sign;\n        var small = new SmallInteger(-this.value);\n        small.sign = !sign;\n        return small\n    };\n    NativeBigInt.prototype.negate = function () {\n        return new NativeBigInt(-this.value)\n    };\n    BigInteger.prototype.abs = function () {\n        return new BigInteger(this.value, false)\n    };\n    SmallInteger.prototype.abs = function () {\n        return new SmallInteger(Math.abs(this.value))\n    };\n    NativeBigInt.prototype.abs = function () {\n        return new NativeBigInt(this.value >= 0 ? this.value : -this.value)\n    };\n\n    function multiplyLong(a, b) {\n        var a_l = a.length, b_l = b.length, l = a_l + b_l, r = createArray(l), base = BASE, product, carry, i, a_i, b_j;\n        for (i = 0; i < a_l; ++i) {\n            a_i = a[i];\n            for (var j = 0; j < b_l; ++j) {\n                b_j = b[j];\n                product = a_i * b_j + r[i + j];\n                carry = Math.floor(product / base);\n                r[i + j] = product - carry * base;\n                r[i + j + 1] += carry\n            }\n        }\n        trim(r);\n        return r\n    }\n\n    function multiplySmall(a, b) {\n        var l = a.length, r = new Array(l), base = BASE, carry = 0, product, i;\n        for (i = 0; i < l; i++) {\n            product = a[i] * b + carry;\n            carry = Math.floor(product / base);\n            r[i] = product - carry * base\n        }\n        while (carry > 0) {\n            r[i++] = carry % base;\n            carry = Math.floor(carry / base)\n        }\n        return r\n    }\n\n    function shiftLeft(x, n) {\n        var r = [];\n        while (n-- > 0) r.push(0);\n        return r.concat(x)\n    }\n\n    function multiplyKaratsuba(x, y) {\n        var n = Math.max(x.length, y.length);\n        if (n <= 30) return multiplyLong(x, y);\n        n = Math.ceil(n / 2);\n        var b = x.slice(n), a = x.slice(0, n), d = y.slice(n), c = y.slice(0, n);\n        var ac = multiplyKaratsuba(a, c), bd = multiplyKaratsuba(b, d),\n            abcd = multiplyKaratsuba(addAny(a, b), addAny(c, d));\n        var product = addAny(addAny(ac, shiftLeft(subtract(subtract(abcd, ac), bd), n)), shiftLeft(bd, 2 * n));\n        trim(product);\n        return product\n    }\n\n    function useKaratsuba(l1, l2) {\n        return -.012 * l1 - .012 * l2 + 15e-6 * l1 * l2 > 0\n    }\n\n    BigInteger.prototype.multiply = function (v) {\n        var n = parseValue(v), a = this.value, b = n.value, sign = this.sign !== n.sign, abs;\n        if (n.isSmall) {\n            if (b === 0) return Integer[0];\n            if (b === 1) return this;\n            if (b === -1) return this.negate();\n            abs = Math.abs(b);\n            if (abs < BASE) {\n                return new BigInteger(multiplySmall(a, abs), sign)\n            }\n            b = smallToArray(abs)\n        }\n        if (useKaratsuba(a.length, b.length)) return new BigInteger(multiplyKaratsuba(a, b), sign);\n        return new BigInteger(multiplyLong(a, b), sign)\n    };\n    BigInteger.prototype.times = BigInteger.prototype.multiply;\n\n    function multiplySmallAndArray(a, b, sign) {\n        if (a < BASE) {\n            return new BigInteger(multiplySmall(b, a), sign)\n        }\n        return new BigInteger(multiplyLong(b, smallToArray(a)), sign)\n    }\n\n    SmallInteger.prototype._multiplyBySmall = function (a) {\n        if (isPrecise(a.value * this.value)) {\n            return new SmallInteger(a.value * this.value)\n        }\n        return multiplySmallAndArray(Math.abs(a.value), smallToArray(Math.abs(this.value)), this.sign !== a.sign)\n    };\n    BigInteger.prototype._multiplyBySmall = function (a) {\n        if (a.value === 0) return Integer[0];\n        if (a.value === 1) return this;\n        if (a.value === -1) return this.negate();\n        return multiplySmallAndArray(Math.abs(a.value), this.value, this.sign !== a.sign)\n    };\n    SmallInteger.prototype.multiply = function (v) {\n        return parseValue(v)._multiplyBySmall(this)\n    };\n    SmallInteger.prototype.times = SmallInteger.prototype.multiply;\n    NativeBigInt.prototype.multiply = function (v) {\n        return new NativeBigInt(this.value * parseValue(v).value)\n    };\n    NativeBigInt.prototype.times = NativeBigInt.prototype.multiply;\n\n    function square(a) {\n        var l = a.length, r = createArray(l + l), base = BASE, product, carry, i, a_i, a_j;\n        for (i = 0; i < l; i++) {\n            a_i = a[i];\n            carry = 0 - a_i * a_i;\n            for (var j = i; j < l; j++) {\n                a_j = a[j];\n                product = 2 * (a_i * a_j) + r[i + j] + carry;\n                carry = Math.floor(product / base);\n                r[i + j] = product - carry * base\n            }\n            r[i + l] = carry\n        }\n        trim(r);\n        return r\n    }\n\n    BigInteger.prototype.square = function () {\n        return new BigInteger(square(this.value), false)\n    };\n    SmallInteger.prototype.square = function () {\n        var value = this.value * this.value;\n        if (isPrecise(value)) return new SmallInteger(value);\n        return new BigInteger(square(smallToArray(Math.abs(this.value))), false)\n    };\n    NativeBigInt.prototype.square = function (v) {\n        return new NativeBigInt(this.value * this.value)\n    };\n\n    function divMod1(a, b) {\n        var a_l = a.length, b_l = b.length, base = BASE, result = createArray(b.length),\n            divisorMostSignificantDigit = b[b_l - 1], lambda = Math.ceil(base / (2 * divisorMostSignificantDigit)),\n            remainder = multiplySmall(a, lambda), divisor = multiplySmall(b, lambda), quotientDigit, shift, carry,\n            borrow, i, l, q;\n        if (remainder.length <= a_l) remainder.push(0);\n        divisor.push(0);\n        divisorMostSignificantDigit = divisor[b_l - 1];\n        for (shift = a_l - b_l; shift >= 0; shift--) {\n            quotientDigit = base - 1;\n            if (remainder[shift + b_l] !== divisorMostSignificantDigit) {\n                quotientDigit = Math.floor((remainder[shift + b_l] * base + remainder[shift + b_l - 1]) / divisorMostSignificantDigit)\n            }\n            carry = 0;\n            borrow = 0;\n            l = divisor.length;\n            for (i = 0; i < l; i++) {\n                carry += quotientDigit * divisor[i];\n                q = Math.floor(carry / base);\n                borrow += remainder[shift + i] - (carry - q * base);\n                carry = q;\n                if (borrow < 0) {\n                    remainder[shift + i] = borrow + base;\n                    borrow = -1\n                } else {\n                    remainder[shift + i] = borrow;\n                    borrow = 0\n                }\n            }\n            while (borrow !== 0) {\n                quotientDigit -= 1;\n                carry = 0;\n                for (i = 0; i < l; i++) {\n                    carry += remainder[shift + i] - base + divisor[i];\n                    if (carry < 0) {\n                        remainder[shift + i] = carry + base;\n                        carry = 0\n                    } else {\n                        remainder[shift + i] = carry;\n                        carry = 1\n                    }\n                }\n                borrow += carry\n            }\n            result[shift] = quotientDigit\n        }\n        remainder = divModSmall(remainder, lambda)[0];\n        return [arrayToSmall(result), arrayToSmall(remainder)]\n    }\n\n    function divMod2(a, b) {\n        var a_l = a.length, b_l = b.length, result = [], part = [], base = BASE, guess, xlen, highx, highy, check;\n        while (a_l) {\n            part.unshift(a[--a_l]);\n            trim(part);\n            if (compareAbs(part, b) < 0) {\n                result.push(0);\n                continue\n            }\n            xlen = part.length;\n            highx = part[xlen - 1] * base + part[xlen - 2];\n            highy = b[b_l - 1] * base + b[b_l - 2];\n            if (xlen > b_l) {\n                highx = (highx + 1) * base\n            }\n            guess = Math.ceil(highx / highy);\n            do {\n                check = multiplySmall(b, guess);\n                if (compareAbs(check, part) <= 0) break;\n                guess--\n            } while (guess);\n            result.push(guess);\n            part = subtract(part, check)\n        }\n        result.reverse();\n        return [arrayToSmall(result), arrayToSmall(part)]\n    }\n\n    function divModSmall(value, lambda) {\n        var length = value.length, quotient = createArray(length), base = BASE, i, q, remainder, divisor;\n        remainder = 0;\n        for (i = length - 1; i >= 0; --i) {\n            divisor = remainder * base + value[i];\n            q = truncate(divisor / lambda);\n            remainder = divisor - q * lambda;\n            quotient[i] = q | 0\n        }\n        return [quotient, remainder | 0]\n    }\n\n    function divModAny(self, v) {\n        var value, n = parseValue(v);\n        if (supportsNativeBigInt) {\n            return [new NativeBigInt(self.value / n.value), new NativeBigInt(self.value % n.value)]\n        }\n        var a = self.value, b = n.value;\n        var quotient;\n        if (b === 0) throw new Error('Cannot divide by zero');\n        if (self.isSmall) {\n            if (n.isSmall) {\n                return [new SmallInteger(truncate(a / b)), new SmallInteger(a % b)]\n            }\n            return [Integer[0], self]\n        }\n        if (n.isSmall) {\n            if (b === 1) return [self, Integer[0]];\n            if (b == -1) return [self.negate(), Integer[0]];\n            var abs = Math.abs(b);\n            if (abs < BASE) {\n                value = divModSmall(a, abs);\n                quotient = arrayToSmall(value[0]);\n                var remainder = value[1];\n                if (self.sign) remainder = -remainder;\n                if (typeof quotient === 'number') {\n                    if (self.sign !== n.sign) quotient = -quotient;\n                    return [new SmallInteger(quotient), new SmallInteger(remainder)]\n                }\n                return [new BigInteger(quotient, self.sign !== n.sign), new SmallInteger(remainder)]\n            }\n            b = smallToArray(abs)\n        }\n        var comparison = compareAbs(a, b);\n        if (comparison === -1) return [Integer[0], self];\n        if (comparison === 0) return [Integer[self.sign === n.sign ? 1 : -1], Integer[0]];\n        if (a.length + b.length <= 200) value = divMod1(a, b); else value = divMod2(a, b);\n        quotient = value[0];\n        var qSign = self.sign !== n.sign, mod = value[1], mSign = self.sign;\n        if (typeof quotient === 'number') {\n            if (qSign) quotient = -quotient;\n            quotient = new SmallInteger(quotient)\n        } else quotient = new BigInteger(quotient, qSign);\n        if (typeof mod === 'number') {\n            if (mSign) mod = -mod;\n            mod = new SmallInteger(mod)\n        } else mod = new BigInteger(mod, mSign);\n        return [quotient, mod]\n    }\n\n    BigInteger.prototype.divmod = function (v) {\n        var result = divModAny(this, v);\n        return { quotient: result[0], remainder: result[1] }\n    };\n    NativeBigInt.prototype.divmod = SmallInteger.prototype.divmod = BigInteger.prototype.divmod;\n    BigInteger.prototype.divide = function (v) {\n        return divModAny(this, v)[0]\n    };\n    NativeBigInt.prototype.over = NativeBigInt.prototype.divide = function (v) {\n        return new NativeBigInt(this.value / parseValue(v).value)\n    };\n    SmallInteger.prototype.over = SmallInteger.prototype.divide = BigInteger.prototype.over = BigInteger.prototype.divide;\n    BigInteger.prototype.mod = function (v) {\n        return divModAny(this, v)[1]\n    };\n    NativeBigInt.prototype.mod = NativeBigInt.prototype.remainder = function (v) {\n        return new NativeBigInt(this.value % parseValue(v).value)\n    };\n    SmallInteger.prototype.remainder = SmallInteger.prototype.mod = BigInteger.prototype.remainder = BigInteger.prototype.mod;\n    BigInteger.prototype.pow = function (v) {\n        var n = parseValue(v), a = this.value, b = n.value, value, x, y;\n        if (b === 0) return Integer[1];\n        if (a === 0) return Integer[0];\n        if (a === 1) return Integer[1];\n        if (a === -1) return n.isEven() ? Integer[1] : Integer[-1];\n        if (n.sign) {\n            return Integer[0]\n        }\n        if (!n.isSmall) throw new Error('The exponent ' + n.toString() + ' is too large.');\n        if (this.isSmall) {\n            if (isPrecise(value = Math.pow(a, b))) return new SmallInteger(truncate(value))\n        }\n        x = this;\n        y = Integer[1];\n        while (true) {\n            if (b & 1 === 1) {\n                y = y.times(x);\n                --b\n            }\n            if (b === 0) break;\n            b /= 2;\n            x = x.square()\n        }\n        return y\n    };\n    SmallInteger.prototype.pow = BigInteger.prototype.pow;\n    NativeBigInt.prototype.pow = function (v) {\n        var n = parseValue(v);\n        var a = this.value, b = n.value;\n        var _0 = BigInt(0), _1 = BigInt(1), _2 = BigInt(2);\n        if (b === _0) return Integer[1];\n        if (a === _0) return Integer[0];\n        if (a === _1) return Integer[1];\n        if (a === BigInt(-1)) return n.isEven() ? Integer[1] : Integer[-1];\n        if (n.isNegative()) return new NativeBigInt(_0);\n        var x = this;\n        var y = Integer[1];\n        while (true) {\n            if ((b & _1) === _1) {\n                y = y.times(x);\n                --b\n            }\n            if (b === _0) break;\n            b /= _2;\n            x = x.square()\n        }\n        return y\n    };\n    BigInteger.prototype.modPow = function (exp, mod) {\n        exp = parseValue(exp);\n        mod = parseValue(mod);\n        if (mod.isZero()) throw new Error('Cannot take modPow with modulus 0');\n        var r = Integer[1], base = this.mod(mod);\n        while (exp.isPositive()) {\n            if (base.isZero()) return Integer[0];\n            if (exp.isOdd()) r = r.multiply(base).mod(mod);\n            exp = exp.divide(2);\n            base = base.square().mod(mod)\n        }\n        return r\n    };\n    NativeBigInt.prototype.modPow = SmallInteger.prototype.modPow = BigInteger.prototype.modPow;\n\n    function compareAbs(a, b) {\n        if (a.length !== b.length) {\n            return a.length > b.length ? 1 : -1\n        }\n        for (var i = a.length - 1; i >= 0; i--) {\n            if (a[i] !== b[i]) return a[i] > b[i] ? 1 : -1\n        }\n        return 0\n    }\n\n    BigInteger.prototype.compareAbs = function (v) {\n        var n = parseValue(v), a = this.value, b = n.value;\n        if (n.isSmall) return 1;\n        return compareAbs(a, b)\n    };\n    SmallInteger.prototype.compareAbs = function (v) {\n        var n = parseValue(v), a = Math.abs(this.value), b = n.value;\n        if (n.isSmall) {\n            b = Math.abs(b);\n            return a === b ? 0 : a > b ? 1 : -1\n        }\n        return -1\n    };\n    NativeBigInt.prototype.compareAbs = function (v) {\n        var a = this.value;\n        var b = parseValue(v).value;\n        a = a >= 0 ? a : -a;\n        b = b >= 0 ? b : -b;\n        return a === b ? 0 : a > b ? 1 : -1\n    };\n    BigInteger.prototype.compare = function (v) {\n        if (v === Infinity) {\n            return -1\n        }\n        if (v === -Infinity) {\n            return 1\n        }\n        var n = parseValue(v), a = this.value, b = n.value;\n        if (this.sign !== n.sign) {\n            return n.sign ? 1 : -1\n        }\n        if (n.isSmall) {\n            return this.sign ? -1 : 1\n        }\n        return compareAbs(a, b) * (this.sign ? -1 : 1)\n    };\n    BigInteger.prototype.compareTo = BigInteger.prototype.compare;\n    SmallInteger.prototype.compare = function (v) {\n        if (v === Infinity) {\n            return -1\n        }\n        if (v === -Infinity) {\n            return 1\n        }\n        var n = parseValue(v), a = this.value, b = n.value;\n        if (n.isSmall) {\n            return a == b ? 0 : a > b ? 1 : -1\n        }\n        if (a < 0 !== n.sign) {\n            return a < 0 ? -1 : 1\n        }\n        return a < 0 ? 1 : -1\n    };\n    SmallInteger.prototype.compareTo = SmallInteger.prototype.compare;\n    NativeBigInt.prototype.compare = function (v) {\n        if (v === Infinity) {\n            return -1\n        }\n        if (v === -Infinity) {\n            return 1\n        }\n        var a = this.value;\n        var b = parseValue(v).value;\n        return a === b ? 0 : a > b ? 1 : -1\n    };\n    NativeBigInt.prototype.compareTo = NativeBigInt.prototype.compare;\n    BigInteger.prototype.equals = function (v) {\n        return this.compare(v) === 0\n    };\n    NativeBigInt.prototype.eq = NativeBigInt.prototype.equals = SmallInteger.prototype.eq = SmallInteger.prototype.equals = BigInteger.prototype.eq = BigInteger.prototype.equals;\n    BigInteger.prototype.notEquals = function (v) {\n        return this.compare(v) !== 0\n    };\n    NativeBigInt.prototype.neq = NativeBigInt.prototype.notEquals = SmallInteger.prototype.neq = SmallInteger.prototype.notEquals = BigInteger.prototype.neq = BigInteger.prototype.notEquals;\n    BigInteger.prototype.greater = function (v) {\n        return this.compare(v) > 0\n    };\n    NativeBigInt.prototype.gt = NativeBigInt.prototype.greater = SmallInteger.prototype.gt = SmallInteger.prototype.greater = BigInteger.prototype.gt = BigInteger.prototype.greater;\n    BigInteger.prototype.lesser = function (v) {\n        return this.compare(v) < 0\n    };\n    NativeBigInt.prototype.lt = NativeBigInt.prototype.lesser = SmallInteger.prototype.lt = SmallInteger.prototype.lesser = BigInteger.prototype.lt = BigInteger.prototype.lesser;\n    BigInteger.prototype.greaterOrEquals = function (v) {\n        return this.compare(v) >= 0\n    };\n    NativeBigInt.prototype.geq = NativeBigInt.prototype.greaterOrEquals = SmallInteger.prototype.geq = SmallInteger.prototype.greaterOrEquals = BigInteger.prototype.geq = BigInteger.prototype.greaterOrEquals;\n    BigInteger.prototype.lesserOrEquals = function (v) {\n        return this.compare(v) <= 0\n    };\n    NativeBigInt.prototype.leq = NativeBigInt.prototype.lesserOrEquals = SmallInteger.prototype.leq = SmallInteger.prototype.lesserOrEquals = BigInteger.prototype.leq = BigInteger.prototype.lesserOrEquals;\n    BigInteger.prototype.isEven = function () {\n        return (this.value[0] & 1) === 0\n    };\n    SmallInteger.prototype.isEven = function () {\n        return (this.value & 1) === 0\n    };\n    NativeBigInt.prototype.isEven = function () {\n        return (this.value & BigInt(1)) === BigInt(0)\n    };\n    BigInteger.prototype.isOdd = function () {\n        return (this.value[0] & 1) === 1\n    };\n    SmallInteger.prototype.isOdd = function () {\n        return (this.value & 1) === 1\n    };\n    NativeBigInt.prototype.isOdd = function () {\n        return (this.value & BigInt(1)) === BigInt(1)\n    };\n    BigInteger.prototype.isPositive = function () {\n        return !this.sign\n    };\n    SmallInteger.prototype.isPositive = function () {\n        return this.value > 0\n    };\n    NativeBigInt.prototype.isPositive = SmallInteger.prototype.isPositive;\n    BigInteger.prototype.isNegative = function () {\n        return this.sign\n    };\n    SmallInteger.prototype.isNegative = function () {\n        return this.value < 0\n    };\n    NativeBigInt.prototype.isNegative = SmallInteger.prototype.isNegative;\n    BigInteger.prototype.isUnit = function () {\n        return false\n    };\n    SmallInteger.prototype.isUnit = function () {\n        return Math.abs(this.value) === 1\n    };\n    NativeBigInt.prototype.isUnit = function () {\n        return this.abs().value === BigInt(1)\n    };\n    BigInteger.prototype.isZero = function () {\n        return false\n    };\n    SmallInteger.prototype.isZero = function () {\n        return this.value === 0\n    };\n    NativeBigInt.prototype.isZero = function () {\n        return this.value === BigInt(0)\n    };\n    BigInteger.prototype.isDivisibleBy = function (v) {\n        var n = parseValue(v);\n        if (n.isZero()) return false;\n        if (n.isUnit()) return true;\n        if (n.compareAbs(2) === 0) return this.isEven();\n        return this.mod(n).isZero()\n    };\n    NativeBigInt.prototype.isDivisibleBy = SmallInteger.prototype.isDivisibleBy = BigInteger.prototype.isDivisibleBy;\n\n    function isBasicPrime(v) {\n        var n = v.abs();\n        if (n.isUnit()) return false;\n        if (n.equals(2) || n.equals(3) || n.equals(5)) return true;\n        if (n.isEven() || n.isDivisibleBy(3) || n.isDivisibleBy(5)) return false;\n        if (n.lesser(49)) return true\n    }\n\n    function millerRabinTest(n, a) {\n        var nPrev = n.prev(), b = nPrev, r = 0, d, t, i, x;\n        while (b.isEven()) b = b.divide(2), r++;\n        next:for (i = 0; i < a.length; i++) {\n            if (n.lesser(a[i])) continue;\n            x = bigInt(a[i]).modPow(b, n);\n            if (x.isUnit() || x.equals(nPrev)) continue;\n            for (d = r - 1; d != 0; d--) {\n                x = x.square().mod(n);\n                if (x.isUnit()) return false;\n                if (x.equals(nPrev)) continue next\n            }\n            return false\n        }\n        return true\n    }\n\n    BigInteger.prototype.isPrime = function (strict) {\n        var isPrime = isBasicPrime(this);\n        if (isPrime !== undefined) return isPrime;\n        var n = this.abs();\n        var bits = n.bitLength();\n        if (bits <= 64) return millerRabinTest(n, [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37]);\n        var logN = Math.log(2) * bits.toJSNumber();\n        var t = Math.ceil(strict === true ? 2 * Math.pow(logN, 2) : logN);\n        for (var a = [], i = 0; i < t; i++) {\n            a.push(bigInt(i + 2))\n        }\n        return millerRabinTest(n, a)\n    };\n    NativeBigInt.prototype.isPrime = SmallInteger.prototype.isPrime = BigInteger.prototype.isPrime;\n    BigInteger.prototype.isProbablePrime = function (iterations) {\n        var isPrime = isBasicPrime(this);\n        if (isPrime !== undefined) return isPrime;\n        var n = this.abs();\n        var t = iterations === undefined ? 5 : iterations;\n        for (var a = [], i = 0; i < t; i++) {\n            a.push(bigInt.randBetween(2, n.minus(2)))\n        }\n        return millerRabinTest(n, a)\n    };\n    NativeBigInt.prototype.isProbablePrime = SmallInteger.prototype.isProbablePrime = BigInteger.prototype.isProbablePrime;\n    BigInteger.prototype.modInv = function (n) {\n        var t = bigInt.zero, newT = bigInt.one, r = parseValue(n), newR = this.abs(), q, lastT, lastR;\n        while (!newR.isZero()) {\n            q = r.divide(newR);\n            lastT = t;\n            lastR = r;\n            t = newT;\n            r = newR;\n            newT = lastT.subtract(q.multiply(newT));\n            newR = lastR.subtract(q.multiply(newR))\n        }\n        if (!r.isUnit()) throw new Error(this.toString() + ' and ' + n.toString() + ' are not co-prime');\n        if (t.compare(0) === -1) {\n            t = t.add(n)\n        }\n        if (this.isNegative()) {\n            return t.negate()\n        }\n        return t\n    };\n    NativeBigInt.prototype.modInv = SmallInteger.prototype.modInv = BigInteger.prototype.modInv;\n    BigInteger.prototype.next = function () {\n        var value = this.value;\n        if (this.sign) {\n            return subtractSmall(value, 1, this.sign)\n        }\n        return new BigInteger(addSmall(value, 1), this.sign)\n    };\n    SmallInteger.prototype.next = function () {\n        var value = this.value;\n        if (value + 1 < MAX_INT) return new SmallInteger(value + 1);\n        return new BigInteger(MAX_INT_ARR, false)\n    };\n    NativeBigInt.prototype.next = function () {\n        return new NativeBigInt(this.value + BigInt(1))\n    };\n    BigInteger.prototype.prev = function () {\n        var value = this.value;\n        if (this.sign) {\n            return new BigInteger(addSmall(value, 1), true)\n        }\n        return subtractSmall(value, 1, this.sign)\n    };\n    SmallInteger.prototype.prev = function () {\n        var value = this.value;\n        if (value - 1 > -MAX_INT) return new SmallInteger(value - 1);\n        return new BigInteger(MAX_INT_ARR, true)\n    };\n    NativeBigInt.prototype.prev = function () {\n        return new NativeBigInt(this.value - BigInt(1))\n    };\n    var powersOfTwo = [1];\n    while (2 * powersOfTwo[powersOfTwo.length - 1] <= BASE) powersOfTwo.push(2 * powersOfTwo[powersOfTwo.length - 1]);\n    var powers2Length = powersOfTwo.length, highestPower2 = powersOfTwo[powers2Length - 1];\n\n    function shift_isSmall(n) {\n        return Math.abs(n) <= BASE\n    }\n\n    BigInteger.prototype.shiftLeft = function (v) {\n        var n = parseValue(v).toJSNumber();\n        if (!shift_isSmall(n)) {\n            throw new Error(String(n) + ' is too large for shifting.')\n        }\n        if (n < 0) return this.shiftRight(-n);\n        var result = this;\n        if (result.isZero()) return result;\n        while (n >= powers2Length) {\n            result = result.multiply(highestPower2);\n            n -= powers2Length - 1\n        }\n        return result.multiply(powersOfTwo[n])\n    };\n    NativeBigInt.prototype.shiftLeft = SmallInteger.prototype.shiftLeft = BigInteger.prototype.shiftLeft;\n    BigInteger.prototype.shiftRight = function (v) {\n        var remQuo;\n        var n = parseValue(v).toJSNumber();\n        if (!shift_isSmall(n)) {\n            throw new Error(String(n) + ' is too large for shifting.')\n        }\n        if (n < 0) return this.shiftLeft(-n);\n        var result = this;\n        while (n >= powers2Length) {\n            if (result.isZero() || result.isNegative() && result.isUnit()) return result;\n            remQuo = divModAny(result, highestPower2);\n            result = remQuo[1].isNegative() ? remQuo[0].prev() : remQuo[0];\n            n -= powers2Length - 1\n        }\n        remQuo = divModAny(result, powersOfTwo[n]);\n        return remQuo[1].isNegative() ? remQuo[0].prev() : remQuo[0]\n    };\n    NativeBigInt.prototype.shiftRight = SmallInteger.prototype.shiftRight = BigInteger.prototype.shiftRight;\n\n    function bitwise(x, y, fn) {\n        y = parseValue(y);\n        var xSign = x.isNegative(), ySign = y.isNegative();\n        var xRem = xSign ? x.not() : x, yRem = ySign ? y.not() : y;\n        var xDigit = 0, yDigit = 0;\n        var xDivMod = null, yDivMod = null;\n        var result = [];\n        while (!xRem.isZero() || !yRem.isZero()) {\n            xDivMod = divModAny(xRem, highestPower2);\n            xDigit = xDivMod[1].toJSNumber();\n            if (xSign) {\n                xDigit = highestPower2 - 1 - xDigit\n            }\n            yDivMod = divModAny(yRem, highestPower2);\n            yDigit = yDivMod[1].toJSNumber();\n            if (ySign) {\n                yDigit = highestPower2 - 1 - yDigit\n            }\n            xRem = xDivMod[0];\n            yRem = yDivMod[0];\n            result.push(fn(xDigit, yDigit))\n        }\n        var sum = fn(xSign ? 1 : 0, ySign ? 1 : 0) !== 0 ? bigInt(-1) : bigInt(0);\n        for (var i = result.length - 1; i >= 0; i -= 1) {\n            sum = sum.multiply(highestPower2).add(bigInt(result[i]))\n        }\n        return sum\n    }\n\n    BigInteger.prototype.not = function () {\n        return this.negate().prev()\n    };\n    NativeBigInt.prototype.not = SmallInteger.prototype.not = BigInteger.prototype.not;\n    BigInteger.prototype.and = function (n) {\n        return bitwise(this, n, function (a, b) {\n            return a & b\n        })\n    };\n    NativeBigInt.prototype.and = SmallInteger.prototype.and = BigInteger.prototype.and;\n    BigInteger.prototype.or = function (n) {\n        return bitwise(this, n, function (a, b) {\n            return a | b\n        })\n    };\n    NativeBigInt.prototype.or = SmallInteger.prototype.or = BigInteger.prototype.or;\n    BigInteger.prototype.xor = function (n) {\n        return bitwise(this, n, function (a, b) {\n            return a ^ b\n        })\n    };\n    NativeBigInt.prototype.xor = SmallInteger.prototype.xor = BigInteger.prototype.xor;\n    var LOBMASK_I = 1 << 30, LOBMASK_BI = (BASE & -BASE) * (BASE & -BASE) | LOBMASK_I;\n\n    function roughLOB(n) {\n        var v = n.value,\n            x = typeof v === 'number' ? v | LOBMASK_I : typeof v === 'bigint' ? v | BigInt(LOBMASK_I) : v[0] + v[1] * BASE | LOBMASK_BI;\n        return x & -x\n    }\n\n    function integerLogarithm(value, base) {\n        if (base.compareTo(value) <= 0) {\n            var tmp = integerLogarithm(value, base.square(base));\n            var p = tmp.p;\n            var e = tmp.e;\n            var t = p.multiply(base);\n            return t.compareTo(value) <= 0 ? { p: t, e: e * 2 + 1 } : { p: p, e: e * 2 }\n        }\n        return { p: bigInt(1), e: 0 }\n    }\n\n    BigInteger.prototype.bitLength = function () {\n        var n = this;\n        if (n.compareTo(bigInt(0)) < 0) {\n            n = n.negate().subtract(bigInt(1))\n        }\n        if (n.compareTo(bigInt(0)) === 0) {\n            return bigInt(0)\n        }\n        return bigInt(integerLogarithm(n, bigInt(2)).e).add(bigInt(1))\n    };\n    NativeBigInt.prototype.bitLength = SmallInteger.prototype.bitLength = BigInteger.prototype.bitLength;\n\n    function max(a, b) {\n        a = parseValue(a);\n        b = parseValue(b);\n        return a.greater(b) ? a : b\n    }\n\n    function min(a, b) {\n        a = parseValue(a);\n        b = parseValue(b);\n        return a.lesser(b) ? a : b\n    }\n\n    function gcd(a, b) {\n        a = parseValue(a).abs();\n        b = parseValue(b).abs();\n        if (a.equals(b)) return a;\n        if (a.isZero()) return b;\n        if (b.isZero()) return a;\n        var c = Integer[1], d, t;\n        while (a.isEven() && b.isEven()) {\n            d = min(roughLOB(a), roughLOB(b));\n            a = a.divide(d);\n            b = b.divide(d);\n            c = c.multiply(d)\n        }\n        while (a.isEven()) {\n            a = a.divide(roughLOB(a))\n        }\n        do {\n            while (b.isEven()) {\n                b = b.divide(roughLOB(b))\n            }\n            if (a.greater(b)) {\n                t = b;\n                b = a;\n                a = t\n            }\n            b = b.subtract(a)\n        } while (!b.isZero());\n        return c.isUnit() ? a : a.multiply(c)\n    }\n\n    function lcm(a, b) {\n        a = parseValue(a).abs();\n        b = parseValue(b).abs();\n        return a.divide(gcd(a, b)).multiply(b)\n    }\n\n    function randBetween(a, b) {\n        a = parseValue(a);\n        b = parseValue(b);\n        var low = min(a, b), high = max(a, b);\n        var range = high.subtract(low).add(1);\n        if (range.isSmall) return low.add(Math.floor(Math.random() * range));\n        var digits = toBase(range, BASE).value;\n        var result = [], restricted = true;\n        for (var i = 0; i < digits.length; i++) {\n            var top = restricted ? digits[i] : BASE;\n            var digit = truncate(Math.random() * top);\n            result.push(digit);\n            if (digit < top) restricted = false\n        }\n        return low.add(Integer.fromArray(result, BASE, false))\n    }\n\n    var parseBase = function (text, base, alphabet, caseSensitive) {\n        alphabet = alphabet || DEFAULT_ALPHABET;\n        text = String(text);\n        if (!caseSensitive) {\n            text = text.toLowerCase();\n            alphabet = alphabet.toLowerCase()\n        }\n        var length = text.length;\n        var i;\n        var absBase = Math.abs(base);\n        var alphabetValues = {};\n        for (i = 0; i < alphabet.length; i++) {\n            alphabetValues[alphabet[i]] = i\n        }\n        for (i = 0; i < length; i++) {\n            var c = text[i];\n            if (c === '-') continue;\n            if (c in alphabetValues) {\n                if (alphabetValues[c] >= absBase) {\n                    if (c === '1' && absBase === 1) continue;\n                    throw new Error(c + ' is not a valid digit in base ' + base + '.')\n                }\n            }\n        }\n        base = parseValue(base);\n        var digits = [];\n        var isNegative = text[0] === '-';\n        for (i = isNegative ? 1 : 0; i < text.length; i++) {\n            var c = text[i];\n            if (c in alphabetValues) digits.push(parseValue(alphabetValues[c])); else if (c === '<') {\n                var start = i;\n                do {\n                    i++\n                } while (text[i] !== '>' && i < text.length);\n                digits.push(parseValue(text.slice(start + 1, i)))\n            } else throw new Error(c + ' is not a valid character')\n        }\n        return parseBaseFromArray(digits, base, isNegative)\n    };\n\n    function parseBaseFromArray(digits, base, isNegative) {\n        var val = Integer[0], pow = Integer[1], i;\n        for (i = digits.length - 1; i >= 0; i--) {\n            val = val.add(digits[i].times(pow));\n            pow = pow.times(base)\n        }\n        return isNegative ? val.negate() : val\n    }\n\n    function stringify(digit, alphabet) {\n        alphabet = alphabet || DEFAULT_ALPHABET;\n        if (digit < alphabet.length) {\n            return alphabet[digit]\n        }\n        return '<' + digit + '>'\n    }\n\n    function toBase(n, base) {\n        base = bigInt(base);\n        if (base.isZero()) {\n            if (n.isZero()) return { value: [0], isNegative: false };\n            throw new Error('Cannot convert nonzero numbers to base 0.')\n        }\n        if (base.equals(-1)) {\n            if (n.isZero()) return { value: [0], isNegative: false };\n            if (n.isNegative()) return {\n                value: [].concat.apply([], Array.apply(null, Array(-n.toJSNumber())).map(Array.prototype.valueOf, [1, 0])),\n                isNegative: false\n            };\n            var arr = Array.apply(null, Array(n.toJSNumber() - 1)).map(Array.prototype.valueOf, [0, 1]);\n            arr.unshift([1]);\n            return { value: [].concat.apply([], arr), isNegative: false }\n        }\n        var neg = false;\n        if (n.isNegative() && base.isPositive()) {\n            neg = true;\n            n = n.abs()\n        }\n        if (base.isUnit()) {\n            if (n.isZero()) return { value: [0], isNegative: false };\n            return { value: Array.apply(null, Array(n.toJSNumber())).map(Number.prototype.valueOf, 1), isNegative: neg }\n        }\n        var out = [];\n        var left = n, divmod;\n        while (left.isNegative() || left.compareAbs(base) >= 0) {\n            divmod = left.divmod(base);\n            left = divmod.quotient;\n            var digit = divmod.remainder;\n            if (digit.isNegative()) {\n                digit = base.minus(digit).abs();\n                left = left.next()\n            }\n            out.push(digit.toJSNumber())\n        }\n        out.push(left.toJSNumber());\n        return { value: out.reverse(), isNegative: neg }\n    }\n\n    function toBaseString(n, base, alphabet) {\n        var arr = toBase(n, base);\n        return (arr.isNegative ? '-' : '') + arr.value.map(function (x) {\n            return stringify(x, alphabet)\n        }).join('')\n    }\n\n    BigInteger.prototype.toArray = function (radix) {\n        return toBase(this, radix)\n    };\n    SmallInteger.prototype.toArray = function (radix) {\n        return toBase(this, radix)\n    };\n    NativeBigInt.prototype.toArray = function (radix) {\n        return toBase(this, radix)\n    };\n    BigInteger.prototype.toString = function (radix, alphabet) {\n        if (radix === undefined) radix = 10;\n        if (radix !== 10) return toBaseString(this, radix, alphabet);\n        var v = this.value, l = v.length, str = String(v[--l]), zeros = '0000000', digit;\n        while (--l >= 0) {\n            digit = String(v[l]);\n            str += zeros.slice(digit.length) + digit\n        }\n        var sign = this.sign ? '-' : '';\n        return sign + str\n    };\n    SmallInteger.prototype.toString = function (radix, alphabet) {\n        if (radix === undefined) radix = 10;\n        if (radix != 10) return toBaseString(this, radix, alphabet);\n        return String(this.value)\n    };\n    NativeBigInt.prototype.toString = SmallInteger.prototype.toString;\n    NativeBigInt.prototype.toJSON = BigInteger.prototype.toJSON = SmallInteger.prototype.toJSON = function () {\n        return this.toString()\n    };\n    BigInteger.prototype.valueOf = function () {\n        return parseInt(this.toString(), 10)\n    };\n    BigInteger.prototype.toJSNumber = BigInteger.prototype.valueOf;\n    SmallInteger.prototype.valueOf = function () {\n        return this.value\n    };\n    SmallInteger.prototype.toJSNumber = SmallInteger.prototype.valueOf;\n    NativeBigInt.prototype.valueOf = NativeBigInt.prototype.toJSNumber = function () {\n        return parseInt(this.toString(), 10)\n    };\n\n    function parseStringValue(v) {\n        if (isPrecise(+v)) {\n            var x = +v;\n            if (x === truncate(x)) return supportsNativeBigInt ? new NativeBigInt(BigInt(x)) : new SmallInteger(x);\n            throw new Error('Invalid integer: ' + v)\n        }\n        var sign = v[0] === '-';\n        if (sign) v = v.slice(1);\n        var split = v.split(/e/i);\n        if (split.length > 2) throw new Error('Invalid integer: ' + split.join('e'));\n        if (split.length === 2) {\n            var exp = split[1];\n            if (exp[0] === '+') exp = exp.slice(1);\n            exp = +exp;\n            if (exp !== truncate(exp) || !isPrecise(exp)) throw new Error('Invalid integer: ' + exp + ' is not a valid exponent.');\n            var text = split[0];\n            var decimalPlace = text.indexOf('.');\n            if (decimalPlace >= 0) {\n                exp -= text.length - decimalPlace - 1;\n                text = text.slice(0, decimalPlace) + text.slice(decimalPlace + 1)\n            }\n            if (exp < 0) throw new Error('Cannot include negative exponent part for integers');\n            text += new Array(exp + 1).join('0');\n            v = text\n        }\n        var isValid = /^([0-9][0-9]*)$/.test(v);\n        if (!isValid) throw new Error('Invalid integer: ' + v);\n        if (supportsNativeBigInt) {\n            return new NativeBigInt(BigInt(sign ? '-' + v : v))\n        }\n        var r = [], max = v.length, l = LOG_BASE, min = max - l;\n        while (max > 0) {\n            r.push(+v.slice(min, max));\n            min -= l;\n            if (min < 0) min = 0;\n            max -= l\n        }\n        trim(r);\n        return new BigInteger(r, sign)\n    }\n\n    function parseNumberValue(v) {\n        if (supportsNativeBigInt) {\n            return new NativeBigInt(BigInt(v))\n        }\n        if (isPrecise(v)) {\n            if (v !== truncate(v)) throw new Error(v + ' is not an integer.');\n            return new SmallInteger(v)\n        }\n        return parseStringValue(v.toString())\n    }\n\n    function parseValue(v) {\n        if (typeof v === 'number') {\n            return parseNumberValue(v)\n        }\n        if (typeof v === 'string') {\n            return parseStringValue(v)\n        }\n        if (typeof v === 'bigint') {\n            return new NativeBigInt(v)\n        }\n        return v\n    }\n\n    for (var i = 0; i < 1e3; i++) {\n        Integer[i] = parseValue(i);\n        if (i > 0) Integer[-i] = parseValue(-i)\n    }\n    Integer.one = Integer[1];\n    Integer.zero = Integer[0];\n    Integer.minusOne = Integer[-1];\n    Integer.max = max;\n    Integer.min = min;\n    Integer.gcd = gcd;\n    Integer.lcm = lcm;\n    Integer.isInstance = function (x) {\n        return x instanceof BigInteger || x instanceof SmallInteger || x instanceof NativeBigInt\n    };\n    Integer.randBetween = randBetween;\n    Integer.fromArray = function (digits, base, isNegative) {\n        return parseBaseFromArray(digits.map(parseValue), parseValue(base || 10), isNegative)\n    };\n    return Integer\n}();\n\nif (typeof define === 'function' && define.amd) {\n    define('big-integer', [], function () {\n        return bigInt\n    })\n}\n\nexport default bigInt\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @module FactoryMaker\n * @ignore\n */\nconst FactoryMaker = (function () {\n\n    let instance;\n    let singletonContexts = [];\n    const singletonFactories = {};\n    const classFactories = {};\n\n    function extend(name, childInstance, override, context) {\n        if (!context[name] && childInstance) {\n            context[name] = {\n                instance: childInstance,\n                override: override\n            };\n        }\n    }\n\n    /**\n     * Use this method from your extended object.  this.factory is injected into your object.\n     * this.factory.getSingletonInstance(this.context, 'VideoModel')\n     * will return the video model for use in the extended object.\n     *\n     * @param {Object} context - injected into extended object as this.context\n     * @param {string} className - string name found in all dash.js objects\n     * with name __dashjs_factory_name Will be at the bottom. Will be the same as the object's name.\n     * @returns {*} Context aware instance of specified singleton name.\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function getSingletonInstance(context, className) {\n        for (const i in singletonContexts) {\n            const obj = singletonContexts[i];\n            if (obj.context === context && obj.name === className) {\n                return obj.instance;\n            }\n        }\n        return null;\n    }\n\n    /**\n     * Use this method to add an singleton instance to the system.  Useful for unit testing to mock objects etc.\n     *\n     * @param {Object} context\n     * @param {string} className\n     * @param {Object} instance\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function setSingletonInstance(context, className, instance) {\n        for (const i in singletonContexts) {\n            const obj = singletonContexts[i];\n            if (obj.context === context && obj.name === className) {\n                singletonContexts[i].instance = instance;\n                return;\n            }\n        }\n        singletonContexts.push({\n            name: className,\n            context: context,\n            instance: instance\n        });\n    }\n\n    /**\n     * Use this method to remove all singleton instances associated with a particular context.\n     *\n     * @param {Object} context\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function deleteSingletonInstances(context) {\n        singletonContexts = singletonContexts.filter(x => x.context !== context);\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Factories storage Management\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function getFactoryByName(name, factoriesArray) {\n        return factoriesArray[name];\n    }\n\n    function updateFactory(name, factory, factoriesArray) {\n        if (name in factoriesArray) {\n            factoriesArray[name] = factory;\n        }\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Class Factories Management\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function updateClassFactory(name, factory) {\n        updateFactory(name, factory, classFactories);\n    }\n\n    function getClassFactoryByName(name) {\n        return getFactoryByName(name, classFactories);\n    }\n\n    function getClassFactory(classConstructor) {\n        let factory = getFactoryByName(classConstructor.__dashjs_factory_name, classFactories);\n\n        if (!factory) {\n            factory = function (context) {\n                if (context === undefined) {\n                    context = {};\n                }\n                return {\n                    create: function () {\n                        return merge(classConstructor, context, arguments);\n                    }\n                };\n            };\n\n            classFactories[classConstructor.__dashjs_factory_name] = factory; // store factory\n        }\n        return factory;\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Singleton Factory MAangement\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function updateSingletonFactory(name, factory) {\n        updateFactory(name, factory, singletonFactories);\n    }\n\n    function getSingletonFactoryByName(name) {\n        return getFactoryByName(name, singletonFactories);\n    }\n\n    function getSingletonFactory(classConstructor) {\n        let factory = getFactoryByName(classConstructor.__dashjs_factory_name, singletonFactories);\n        if (!factory) {\n            factory = function (context) {\n                let instance;\n                if (context === undefined) {\n                    context = {};\n                }\n                return {\n                    getInstance: function () {\n                        // If we don't have an instance yet check for one on the context\n                        if (!instance) {\n                            instance = getSingletonInstance(context, classConstructor.__dashjs_factory_name);\n                        }\n                        // If there's no instance on the context then create one\n                        if (!instance) {\n                            instance = merge(classConstructor, context, arguments);\n                            singletonContexts.push({\n                                name: classConstructor.__dashjs_factory_name,\n                                context: context,\n                                instance: instance\n                            });\n                        }\n                        return instance;\n                    }\n                };\n            };\n            singletonFactories[classConstructor.__dashjs_factory_name] = factory; // store factory\n        }\n\n        return factory;\n    }\n\n    function merge(classConstructor, context, args) {\n\n        let classInstance;\n        const className = classConstructor.__dashjs_factory_name;\n        const extensionObject = context[className];\n\n        if (extensionObject) {\n\n            let extension = extensionObject.instance;\n\n            if (extensionObject.override) { //Override public methods in parent but keep parent.\n\n                classInstance = classConstructor.apply({context}, args);\n                extension = extension.apply({\n                    context,\n                    factory: instance,\n                    parent: classInstance\n                }, args);\n\n                for (const prop in extension) {\n                    if (classInstance.hasOwnProperty(prop)) {\n                        classInstance[prop] = extension[prop];\n                    }\n                }\n\n            } else { //replace parent object completely with new object. Same as dijon.\n\n                return extension.apply({\n                    context,\n                    factory: instance\n                }, args);\n\n            }\n        } else {\n            // Create new instance of the class\n            classInstance = classConstructor.apply({context}, args);\n        }\n\n        // Add getClassName function to class instance prototype (used by Debug)\n        classInstance.getClassName = function () {return className;};\n\n        return classInstance;\n    }\n\n    instance = {\n        extend: extend,\n        getSingletonInstance: getSingletonInstance,\n        setSingletonInstance: setSingletonInstance,\n        deleteSingletonInstances: deleteSingletonInstances,\n        getSingletonFactory: getSingletonFactory,\n        getSingletonFactoryByName: getSingletonFactoryByName,\n        updateSingletonFactory: updateSingletonFactory,\n        getClassFactory: getClassFactory,\n        getClassFactoryByName: getClassFactoryByName,\n        updateClassFactory: updateClassFactory\n    };\n\n    return instance;\n\n}());\n\nexport default FactoryMaker;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass ErrorsBase {\n    extend(errors, config) {\n        if (!errors) {\n            return;\n        }\n\n        let override = config ? config.override : false;\n        let publicOnly = config ? config.publicOnly : false;\n\n\n        for (const err in errors) {\n            if (!errors.hasOwnProperty(err) || (this[err] && !override)) {\n                continue;\n            }\n            if (publicOnly && errors[err].indexOf('public_') === -1) {\n                continue;\n            }\n            this[err] = errors[err];\n\n        }\n    }\n}\n\nexport default ErrorsBase;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass EventsBase {\n    extend(events, config) {\n        if (!events) {\n            return;\n        }\n\n        let override = config ? config.override : false;\n        let publicOnly = config ? config.publicOnly : false;\n\n\n        for (const evt in events) {\n            if (!events.hasOwnProperty(evt) || (this[evt] && !override)) {\n                continue;\n            }\n            if (publicOnly && events[evt].indexOf('public_') === -1) {\n                continue;\n            }\n            this[evt] = events[evt];\n\n        }\n    }\n}\n\nexport default EventsBase;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport FragmentRequest from '../streaming/vo/FragmentRequest.js';\nimport {HTTPRequest} from '../streaming/vo/metrics/HTTPRequest.js';\nimport FactoryMaker from '../core/FactoryMaker.js';\n\nfunction MssFragmentInfoController(config) {\n\n    config = config || {};\n\n    let instance,\n        logger,\n        fragmentModel,\n        started,\n        type,\n        loadFragmentTimeout,\n        startTime,\n        startFragmentTime,\n        index;\n\n    const streamProcessor = config.streamProcessor;\n    const baseURLController = config.baseURLController;\n    const debug = config.debug;\n    const controllerType = 'MssFragmentInfoController';\n\n    function setup() {\n        logger = debug.getLogger(instance);\n    }\n\n    function initialize() {\n        type = streamProcessor.getType();\n        fragmentModel = streamProcessor.getFragmentModel();\n\n        started = false;\n        startTime = null;\n        startFragmentTime = null;\n    }\n\n    function start() {\n        if (started) {\n            return;\n        }\n\n        logger.debug('Start');\n\n        started = true;\n        index = 0;\n\n        loadNextFragmentInfo();\n    }\n\n    function stop() {\n        if (!started) {\n            return;\n        }\n\n        logger.debug('Stop');\n\n        clearTimeout(loadFragmentTimeout);\n        started = false;\n        startTime = null;\n        startFragmentTime = null;\n    }\n\n    function reset() {\n        stop();\n    }\n\n    function loadNextFragmentInfo() {\n        if (!started) {\n            return;\n        }\n\n        // Get last segment from SegmentTimeline\n        const representation = getCurrentRepresentation();\n        const manifest = representation.adaptation.period.mpd.manifest;\n        const adaptation = manifest.Period[representation.adaptation.period.index].AdaptationSet[representation.adaptation.index];\n        const segments = adaptation.SegmentTemplate.SegmentTimeline.S;\n        const segment = segments[segments.length - 1];\n\n        // logger.debug('Last fragment time: ' + (segment.t / adaptation.SegmentTemplate.timescale));\n\n        // Generate segment request\n        const request = getRequestForSegment(adaptation, representation, segment);\n\n        // Send segment request\n        requestFragment.call(this, request);\n    }\n\n    function getRequestForSegment(adaptation, representation, segment) {\n        let timescale = adaptation.SegmentTemplate.timescale;\n        let request = new FragmentRequest();\n\n        request.mediaType = type;\n        request.type = HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE;\n        // request.range = segment.mediaRange;\n        request.startTime = segment.t / timescale;\n        request.duration = segment.d / timescale;\n        request.timescale = timescale;\n        // request.availabilityStartTime = segment.availabilityStartTime;\n        // request.availabilityEndTime = segment.availabilityEndTime;\n        // request.wallStartTime = segment.wallStartTime;\n        request.bandwidth = representation.bandwidth;\n        request.index = index++;\n        request.adaptationIndex = representation.adaptation.index;\n        request.representation = representation;\n        request.url = baseURLController.resolve(representation.path).url + adaptation.SegmentTemplate.media;\n        request.url = request.url.replace('$Bandwidth$', representation.bandwidth);\n        request.url = request.url.replace('$Time$', segment.tManifest ? segment.tManifest : segment.t);\n        request.url = request.url.replace('/Fragments(', '/FragmentInfo(');\n\n        return request;\n    }\n\n    function getCurrentRepresentation() {\n        const representationController = streamProcessor.getRepresentationController();\n        const representation = representationController.getCurrentRepresentation();\n        return representation;\n    }\n\n    function requestFragment(request) {\n        // logger.debug('Load FragmentInfo for time: ' + request.startTime);\n        if (streamProcessor.getFragmentModel().isFragmentLoadedOrPending(request)) {\n            // We may have reached end of timeline in case of start-over streams\n            logger.debug('End of timeline');\n            stop();\n            return;\n        }\n\n        fragmentModel.executeRequest(request);\n    }\n\n    function fragmentInfoLoaded(e) {\n        if (!started) {\n            return;\n        }\n\n        const request = e.request;\n        if (!e.response) {\n            logger.error('Load error', request.url);\n            return;\n        }\n\n        let deltaFragmentTime,\n            deltaTime,\n            delay;\n\n        // logger.debug('FragmentInfo loaded: ', request.url);\n\n        if (startTime === null) {\n            startTime = new Date().getTime();\n        }\n\n        if (!startFragmentTime) {\n            startFragmentTime = request.startTime;\n        }\n\n        // Determine delay before requesting next FragmentInfo\n        deltaTime = (new Date().getTime() - startTime) / 1000;\n        deltaFragmentTime = (request.startTime + request.duration) - startFragmentTime;\n        delay = Math.max(0, (deltaFragmentTime - deltaTime));\n\n        // Set timeout for requesting next FragmentInfo\n        clearTimeout(loadFragmentTimeout);\n        loadFragmentTimeout = setTimeout(function () {\n            loadFragmentTimeout = null;\n            loadNextFragmentInfo();\n        }, delay * 1000);\n    }\n\n    function getType() {\n        return type;\n    }\n\n    instance = {\n        initialize: initialize,\n        controllerType: controllerType,\n        start: start,\n        fragmentInfoLoaded: fragmentInfoLoaded,\n        getType: getType,\n        reset: reset\n    };\n\n    setup();\n\n    return instance;\n}\n\nMssFragmentInfoController.__dashjs_factory_name = 'MssFragmentInfoController';\nexport default FactoryMaker.getClassFactory(MssFragmentInfoController);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport DashJSError from '../streaming/vo/DashJSError.js';\nimport MssErrors from './errors/MssErrors.js';\n\nimport Events from '../streaming/MediaPlayerEvents.js';\nimport FactoryMaker from '../core/FactoryMaker.js';\n\n/**\n * @module MssFragmentMoofProcessor\n * @ignore\n * @param {Object} config object\n */\nfunction MssFragmentMoofProcessor(config) {\n\n    config = config || {};\n    let instance,\n        type,\n        logger;\n    const dashMetrics = config.dashMetrics;\n    const playbackController = config.playbackController;\n    const errorHandler = config.errHandler;\n    const eventBus = config.eventBus;\n    const ISOBoxer = config.ISOBoxer;\n    const debug = config.debug;\n\n    function setup() {\n        logger = debug.getLogger(instance);\n        type = '';\n    }\n\n    function processTfrf(request, tfrf, tfdt, streamProcessor) {\n        const representationController = streamProcessor.getRepresentationController();\n        const representation = representationController.getCurrentRepresentation();\n\n        const manifest = representation.adaptation.period.mpd.manifest;\n        const adaptation = manifest.Period[representation.adaptation.period.index].AdaptationSet[representation.adaptation.index];\n        const timescale = adaptation.SegmentTemplate.timescale;\n\n        type = streamProcessor.getType();\n\n        // Process tfrf only for live streams or start-over static streams (timeShiftBufferDepth > 0)\n        if (manifest.type !== 'dynamic' && !manifest.timeShiftBufferDepth) {\n            return;\n        }\n\n        if (!tfrf) {\n            errorHandler.error(new DashJSError(MssErrors.MSS_NO_TFRF_CODE, MssErrors.MSS_NO_TFRF_MESSAGE));\n            return;\n        }\n\n        // Get adaptation's segment timeline (always a SegmentTimeline in Smooth Streaming use case)\n        const segments = adaptation.SegmentTemplate.SegmentTimeline.S;\n        const entries = tfrf.entry;\n        let entry,\n            segmentTime,\n            range;\n        let segment = null;\n        let t = 0;\n        let endTime;\n        let availabilityStartTime = null;\n\n        if (entries.length === 0) {\n            return;\n        }\n\n        // Consider only first tfrf entry (to avoid pre-condition failure on fragment info requests)\n        entry = entries[0];\n\n        // In case of start-over streams, check if we have reached end of original manifest duration (set in timeShiftBufferDepth)\n        // => then do not update anymore timeline\n        if (manifest.type === 'static') {\n            // Get first segment time\n            segmentTime = segments[0].tManifest ? parseFloat(segments[0].tManifest) : segments[0].t;\n            if (entry.fragment_absolute_time > (segmentTime + (manifest.timeShiftBufferDepth * timescale))) {\n                return;\n            }\n        }\n\n        // logger.debug('entry - t = ', (entry.fragment_absolute_time / timescale));\n\n        // Get last segment time\n        segmentTime = segments[segments.length - 1].tManifest ? parseFloat(segments[segments.length - 1].tManifest) : segments[segments.length - 1].t;\n        // logger.debug('Last segment - t = ', (segmentTime / timescale));\n\n        // Check if we have to append new segment to timeline\n        if (entry.fragment_absolute_time <= segmentTime) {\n            // Update DVR window range => set range end to end time of current segment\n            range = {\n                start: segments[0].t / timescale,\n                end: (tfdt.baseMediaDecodeTime / timescale) + request.duration\n            };\n\n            updateDVR(request.mediaType, range, streamProcessor.getStreamInfo().manifestInfo);\n            return;\n        }\n\n        // logger.debug('Add new segment - t = ', (entry.fragment_absolute_time / timescale));\n        segment = {};\n        segment.t = entry.fragment_absolute_time;\n        segment.d = entry.fragment_duration;\n        // If timestamps starts at 0 relative to 1st segment (dynamic to static) then update segment time\n        if (segments[0].tManifest) {\n            segment.t -= parseFloat(segments[0].tManifest) - segments[0].t;\n            segment.tManifest = entry.fragment_absolute_time;\n        }\n\n        // Patch previous segment duration\n        let lastSegment = segments[segments.length - 1];\n        if (lastSegment.t + lastSegment.d !== segment.t) {\n            logger.debug('Patch segment duration - t = ', lastSegment.t + ', d = ' + lastSegment.d + ' => ' + (segment.t - lastSegment.t));\n            lastSegment.d = segment.t - lastSegment.t;\n        }\n\n        segments.push(segment);\n\n        // In case of static start-over streams, update content duration\n        if (manifest.type === 'static') {\n            if (type === 'video') {\n                segment = segments[segments.length - 1];\n                endTime = (segment.t + segment.d) / timescale;\n                if (endTime > representation.adaptation.period.duration) {\n                    eventBus.trigger(Events.MANIFEST_VALIDITY_CHANGED, { sender: this, newDuration: endTime });\n                }\n            }\n            return;\n        } else {\n            // In case of live streams, update segment timeline according to DVR window\n            if (manifest.timeShiftBufferDepth && manifest.timeShiftBufferDepth > 0) {\n                // Get timestamp of the last segment\n                segment = segments[segments.length - 1];\n                t = segment.t;\n\n                // Determine the segments' availability start time\n                availabilityStartTime = (t - (manifest.timeShiftBufferDepth * timescale)) / timescale;\n\n                // Remove segments prior to availability start time\n                segment = segments[0];\n                endTime = (segment.t + segment.d) / timescale;\n                while (endTime < availabilityStartTime) {\n                    // Check if not currently playing the segment to be removed\n                    if (!playbackController.isPaused() && playbackController.getTime() < endTime) {\n                        break;\n                    }\n                    // logger.debug('Remove segment  - t = ' + (segment.t / timescale));\n                    segments.splice(0, 1);\n                    segment = segments[0];\n                    endTime = (segment.t + segment.d) / timescale;\n                }\n            }\n\n            // Update DVR window range => set range end to end time of current segment\n            range = {\n                start: segments[0].t / timescale,\n                end: (tfdt.baseMediaDecodeTime / timescale) + request.duration\n            };\n\n            updateDVR(type, range, streamProcessor.getStreamInfo().manifestInfo);\n        }\n\n    }\n\n    function updateDVR(type, range, manifestInfo) {\n        if (type !== 'video' && type !== 'audio') {\n            return;\n        }\n        const dvrInfos = dashMetrics.getCurrentDVRInfo(type);\n        if (!dvrInfos || (range.end > dvrInfos.range.end)) {\n            logger.debug('Update DVR range: [' + range.start + ' - ' + range.end + ']');\n            dashMetrics.addDVRInfo(type, playbackController.getTime(), manifestInfo, range);\n            playbackController.updateCurrentTime(type);\n        }\n    }\n\n    // This function returns the offset of the 1st byte of a child box within a container box\n    function getBoxOffset(parent, type) {\n        let offset = 8;\n        let i = 0;\n\n        for (i = 0; i < parent.boxes.length; i++) {\n            if (parent.boxes[i].type === type) {\n                return offset;\n            }\n            offset += parent.boxes[i].size;\n        }\n        return offset;\n    }\n\n    function convertFragment(e, streamProcessor) {\n        let i;\n\n        // e.request contains request description object\n        // e.response contains fragment bytes\n        const isoFile = ISOBoxer.parseBuffer(e.response);\n        // Update track_Id in tfhd box\n        const tfhd = isoFile.fetch('tfhd');\n        tfhd.track_ID = e.request.representation.mediaInfo.index + 1;\n\n        // Add tfdt box\n        let tfdt = isoFile.fetch('tfdt');\n        const traf = isoFile.fetch('traf');\n        if (tfdt === null) {\n            tfdt = ISOBoxer.createFullBox('tfdt', traf, tfhd);\n            tfdt.version = 1;\n            tfdt.flags = 0;\n            tfdt.baseMediaDecodeTime = Math.floor(e.request.startTime * e.request.timescale);\n        }\n\n        const trun = isoFile.fetch('trun');\n\n        // Process tfxd boxes\n        // This box provide absolute timestamp but we take the segment start time for tfdt\n        let tfxd = isoFile.fetch('tfxd');\n        if (tfxd) {\n            tfxd._parent.boxes.splice(tfxd._parent.boxes.indexOf(tfxd), 1);\n            tfxd = null;\n        }\n        let tfrf = isoFile.fetch('tfrf');\n        processTfrf(e.request, tfrf, tfdt, streamProcessor);\n        if (tfrf) {\n            tfrf._parent.boxes.splice(tfrf._parent.boxes.indexOf(tfrf), 1);\n            tfrf = null;\n        }\n\n        // If protected content in PIFF1.1 format (sepiff box = Sample Encryption PIFF)\n        // => convert sepiff box it into a senc box\n        // => create saio and saiz boxes (if not already present)\n        const sepiff = isoFile.fetch('sepiff');\n        if (sepiff !== null) {\n            sepiff.type = 'senc';\n            sepiff.usertype = undefined;\n\n            let saio = isoFile.fetch('saio');\n            if (saio === null) {\n                // Create Sample Auxiliary Information Offsets Box box (saio)\n                saio = ISOBoxer.createFullBox('saio', traf);\n                saio.version = 0;\n                saio.flags = 0;\n                saio.entry_count = 1;\n                saio.offset = [0];\n\n                const saiz = ISOBoxer.createFullBox('saiz', traf);\n                saiz.version = 0;\n                saiz.flags = 0;\n                saiz.sample_count = sepiff.sample_count;\n                saiz.default_sample_info_size = 0;\n                saiz.sample_info_size = [];\n\n                if (sepiff.flags & 0x02) {\n                    // Sub-sample encryption => set sample_info_size for each sample\n                    for (i = 0; i < sepiff.sample_count; i += 1) {\n                        // 10 = 8 (InitializationVector field size) + 2 (subsample_count field size)\n                        // 6 = 2 (BytesOfClearData field size) + 4 (BytesOfEncryptedData field size)\n                        saiz.sample_info_size[i] = 10 + (6 * sepiff.entry[i].NumberOfEntries);\n                    }\n                } else {\n                    // No sub-sample encryption => set default sample_info_size = InitializationVector field size (8)\n                    saiz.default_sample_info_size = 8;\n                }\n            }\n        }\n\n        tfhd.flags &= 0xFFFFFE; // set tfhd.base-data-offset-present to false\n        tfhd.flags |= 0x020000; // set tfhd.default-base-is-moof to true\n        trun.flags |= 0x000001; // set trun.data-offset-present to true\n\n        // Update trun.data_offset field that corresponds to first data byte (inside mdat box)\n        const moof = isoFile.fetch('moof');\n        let length = moof.getLength();\n        trun.data_offset = length + 8;\n\n        // Update saio box offset field according to new senc box offset\n        let saio = isoFile.fetch('saio');\n        if (saio !== null) {\n            let trafPosInMoof = getBoxOffset(moof, 'traf');\n            let sencPosInTraf = getBoxOffset(traf, 'senc');\n            // Set offset from begin fragment to the first IV field in senc box\n            saio.offset[0] = trafPosInMoof + sencPosInTraf + 16; // 16 = box header (12) + sample_count field size (4)\n        }\n\n        // Write transformed/processed fragment into request reponse data\n        e.response = isoFile.write();\n    }\n\n    function updateSegmentList(e, streamProcessor) {\n        // e.request contains request description object\n        // e.response contains fragment bytes\n        if (!e.response) {\n            throw new Error('e.response parameter is missing');\n        }\n\n        const isoFile = ISOBoxer.parseBuffer(e.response);\n        // Update track_Id in tfhd box\n        const tfhd = isoFile.fetch('tfhd');\n        tfhd.track_ID = e.request.representation.mediaInfo.index + 1;\n\n        // Add tfdt box\n        let tfdt = isoFile.fetch('tfdt');\n        let traf = isoFile.fetch('traf');\n        if (tfdt === null) {\n            tfdt = ISOBoxer.createFullBox('tfdt', traf, tfhd);\n            tfdt.version = 1;\n            tfdt.flags = 0;\n            tfdt.baseMediaDecodeTime = Math.floor(e.request.startTime * e.request.timescale);\n        }\n\n        let tfrf = isoFile.fetch('tfrf');\n        processTfrf(e.request, tfrf, tfdt, streamProcessor);\n        if (tfrf) {\n            tfrf._parent.boxes.splice(tfrf._parent.boxes.indexOf(tfrf), 1);\n            tfrf = null;\n        }\n    }\n\n    function getType() {\n        return type;\n    }\n\n    instance = {\n        convertFragment,\n        updateSegmentList,\n        getType\n    };\n\n    setup();\n    return instance;\n}\n\nMssFragmentMoofProcessor.__dashjs_factory_name = 'MssFragmentMoofProcessor';\nexport default FactoryMaker.getClassFactory(MssFragmentMoofProcessor);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport MssErrors from './errors/MssErrors.js';\nimport FactoryMaker from '../core/FactoryMaker.js';\n\n/**\n * @module MssFragmentMoovProcessor\n * @ignore\n * @param {Object} config object\n */\nfunction MssFragmentMoovProcessor(config) {\n    config = config || {};\n    const NALUTYPE_SPS = 7;\n    const NALUTYPE_PPS = 8;\n    const constants = config.constants;\n    const ISOBoxer = config.ISOBoxer;\n\n    let protectionController = config.protectionController;\n    let instance,\n        period,\n        adaptationSet,\n        representation,\n        contentProtection,\n        timescale,\n        trackId;\n\n    function createFtypBox(isoFile) {\n        let ftyp = ISOBoxer.createBox('ftyp', isoFile);\n        ftyp.major_brand = 'iso6';\n        ftyp.minor_version = 1; // is an informative integer for the minor version of the major brand\n        ftyp.compatible_brands = []; //is a list, to the end of the box, of brands isom, iso6 and msdh\n        ftyp.compatible_brands[0] = 'isom'; // => decimal ASCII value for isom\n        ftyp.compatible_brands[1] = 'iso6'; // => decimal ASCII value for iso6\n        ftyp.compatible_brands[2] = 'msdh'; // => decimal ASCII value for msdh\n\n        return ftyp;\n    }\n\n    function createMoovBox(isoFile) {\n\n        // moov box\n        let moov = ISOBoxer.createBox('moov', isoFile);\n\n        // moov/mvhd\n        createMvhdBox(moov);\n\n        // moov/trak\n        let trak = ISOBoxer.createBox('trak', moov);\n\n        // moov/trak/tkhd\n        createTkhdBox(trak);\n\n        // moov/trak/mdia\n        let mdia = ISOBoxer.createBox('mdia', trak);\n\n        // moov/trak/mdia/mdhd\n        createMdhdBox(mdia);\n\n        // moov/trak/mdia/hdlr\n        createHdlrBox(mdia);\n\n        // moov/trak/mdia/minf\n        let minf = ISOBoxer.createBox('minf', mdia);\n\n        switch (adaptationSet.type) {\n            case constants.VIDEO:\n                // moov/trak/mdia/minf/vmhd\n                createVmhdBox(minf);\n                break;\n            case constants.AUDIO:\n                // moov/trak/mdia/minf/smhd\n                createSmhdBox(minf);\n                break;\n            default:\n                break;\n        }\n\n        // moov/trak/mdia/minf/dinf\n        let dinf = ISOBoxer.createBox('dinf', minf);\n\n        // moov/trak/mdia/minf/dinf/dref\n        createDrefBox(dinf);\n\n        // moov/trak/mdia/minf/stbl\n        let stbl = ISOBoxer.createBox('stbl', minf);\n\n        // Create empty stts, stsc, stco and stsz boxes\n        // Use data field as for codem-isoboxer unknown boxes for setting fields value\n\n        // moov/trak/mdia/minf/stbl/stts\n        let stts = ISOBoxer.createFullBox('stts', stbl);\n        stts._data = [0, 0, 0, 0, 0, 0, 0, 0]; // version = 0, flags = 0, entry_count = 0\n\n        // moov/trak/mdia/minf/stbl/stsc\n        let stsc = ISOBoxer.createFullBox('stsc', stbl);\n        stsc._data = [0, 0, 0, 0, 0, 0, 0, 0]; // version = 0, flags = 0, entry_count = 0\n\n        // moov/trak/mdia/minf/stbl/stco\n        let stco = ISOBoxer.createFullBox('stco', stbl);\n        stco._data = [0, 0, 0, 0, 0, 0, 0, 0]; // version = 0, flags = 0, entry_count = 0\n\n        // moov/trak/mdia/minf/stbl/stsz\n        let stsz = ISOBoxer.createFullBox('stsz', stbl);\n        stsz._data = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]; // version = 0, flags = 0, sample_size = 0, sample_count = 0\n\n        // moov/trak/mdia/minf/stbl/stsd\n        createStsdBox(stbl);\n\n        // moov/mvex\n        let mvex = ISOBoxer.createBox('mvex', moov);\n\n        // moov/mvex/trex\n        createTrexBox(mvex);\n\n        if (contentProtection && protectionController) {\n            let supportedKS = protectionController.getSupportedKeySystemMetadataFromContentProtection(contentProtection);\n            createProtectionSystemSpecificHeaderBox(moov, supportedKS);\n        }\n    }\n\n    function createMvhdBox(moov) {\n\n        let mvhd = ISOBoxer.createFullBox('mvhd', moov);\n\n        mvhd.version = 1; // version = 1  in order to have 64bits duration value\n\n        mvhd.creation_time = 0; // the creation time of the presentation => ignore (set to 0)\n        mvhd.modification_time = 0; // the most recent time the presentation was modified => ignore (set to 0)\n        mvhd.timescale = timescale; // the time-scale for the entire presentation => 10000000 for MSS\n        mvhd.duration = period.duration === Infinity ? 0xFFFFFFFFFFFFFFFF : Math.round(period.duration * timescale); // the length of the presentation (in the indicated timescale) =>  take duration of period\n        mvhd.rate = 1.0; // 16.16 number, '1.0' = normal playback\n        mvhd.volume = 1.0; // 8.8 number, '1.0' = full volume\n        mvhd.reserved1 = 0;\n        mvhd.reserved2 = [0x0, 0x0];\n        mvhd.matrix = [\n            1, 0, 0, // provides a transformation matrix for the video;\n            0, 1, 0, // (u,v,w) are restricted here to (0,0,1)\n            0, 0, 16384\n        ];\n        mvhd.pre_defined = [0, 0, 0, 0, 0, 0];\n        mvhd.next_track_ID = trackId + 1; // indicates a value to use for the track ID of the next track to be added to this presentation\n\n        return mvhd;\n    }\n\n    function createTkhdBox(trak) {\n\n        let tkhd = ISOBoxer.createFullBox('tkhd', trak);\n\n        tkhd.version = 1; // version = 1  in order to have 64bits duration value\n        tkhd.flags = 0x1 | // Track_enabled (0x000001): Indicates that the track is enabled\n            0x2 | // Track_in_movie (0x000002):  Indicates that the track is used in the presentation\n            0x4; // Track_in_preview (0x000004):  Indicates that the track is used when previewing the presentation\n\n        tkhd.creation_time = 0; // the creation time of the presentation => ignore (set to 0)\n        tkhd.modification_time = 0; // the most recent time the presentation was modified => ignore (set to 0)\n        tkhd.track_ID = trackId; // uniquely identifies this track over the entire life-time of this presentation\n        tkhd.reserved1 = 0;\n        tkhd.duration = period.duration === Infinity ? 0xFFFFFFFFFFFFFFFF : Math.round(period.duration * timescale); // the duration of this track (in the timescale indicated in the Movie Header Box) =>  take duration of period\n        tkhd.reserved2 = [0x0, 0x0];\n        tkhd.layer = 0; // specifies the front-to-back ordering of video tracks; tracks with lower numbers are closer to the viewer => 0 since only one video track\n        tkhd.alternate_group = 0; // specifies a group or collection of tracks => ignore\n        tkhd.volume = 1.0; // '1.0' = full volume\n        tkhd.reserved3 = 0;\n        tkhd.matrix = [\n            1, 0, 0, // provides a transformation matrix for the video;\n            0, 1, 0, // (u,v,w) are restricted here to (0,0,1)\n            0, 0, 16384\n        ];\n        tkhd.width = representation.width; // visual presentation width\n        tkhd.height = representation.height; // visual presentation height\n\n        return tkhd;\n    }\n\n    function createMdhdBox(mdia) {\n\n        let mdhd = ISOBoxer.createFullBox('mdhd', mdia);\n\n        mdhd.version = 1; // version = 1  in order to have 64bits duration value\n\n        mdhd.creation_time = 0; // the creation time of the presentation => ignore (set to 0)\n        mdhd.modification_time = 0; // the most recent time the presentation was modified => ignore (set to 0)\n        mdhd.timescale = timescale; // the time-scale for the entire presentation\n        mdhd.duration = period.duration === Infinity ? 0xFFFFFFFFFFFFFFFF : Math.round(period.duration * timescale); // the duration of this media (in the scale of the timescale). If the duration cannot be determined then duration is set to all 1s.\n        mdhd.language = adaptationSet.lang || 'und'; // declares the language code for this media\n        mdhd.pre_defined = 0;\n\n        return mdhd;\n    }\n\n    function createHdlrBox(mdia) {\n\n        let hdlr = ISOBoxer.createFullBox('hdlr', mdia);\n\n        hdlr.pre_defined = 0;\n        switch (adaptationSet.type) {\n            case constants.VIDEO:\n                hdlr.handler_type = 'vide';\n                break;\n            case constants.AUDIO:\n                hdlr.handler_type = 'soun';\n                break;\n            default:\n                hdlr.handler_type = 'meta';\n                break;\n        }\n        hdlr.name = representation.id;\n        hdlr.reserved = [0, 0, 0];\n\n        return hdlr;\n    }\n\n    function createVmhdBox(minf) {\n\n        let vmhd = ISOBoxer.createFullBox('vmhd', minf);\n\n        vmhd.flags = 1;\n\n        vmhd.graphicsmode = 0; // specifies a composition mode for this video track, from the following enumerated set, which may be extended by derived specifications: copy = 0 copy over the existing image\n        vmhd.opcolor = [0, 0, 0]; // is a set of 3 colour values (red, green, blue) available for use by graphics modes\n\n        return vmhd;\n    }\n\n    function createSmhdBox(minf) {\n\n        let smhd = ISOBoxer.createFullBox('smhd', minf);\n\n        smhd.flags = 1;\n\n        smhd.balance = 0; // is a fixed-point 8.8 number that places mono audio tracks in a stereo space; 0 is centre (the normal value); full left is -1.0 and full right is 1.0.\n        smhd.reserved = 0;\n\n        return smhd;\n    }\n\n    function createDrefBox(dinf) {\n\n        let dref = ISOBoxer.createFullBox('dref', dinf);\n\n        dref.entry_count = 1;\n        dref.entries = [];\n\n        let url = ISOBoxer.createFullBox('url ', dref, false);\n        url.location = '';\n        url.flags = 1;\n\n        dref.entries.push(url);\n\n        return dref;\n    }\n\n    function createStsdBox(stbl) {\n\n        let stsd = ISOBoxer.createFullBox('stsd', stbl);\n\n        stsd.entries = [];\n        switch (adaptationSet.type) {\n            case constants.VIDEO:\n            case constants.AUDIO:\n                stsd.entries.push(createSampleEntry(stsd));\n                break;\n            default:\n                break;\n        }\n\n        stsd.entry_count = stsd.entries.length; // is an integer that counts the actual entries\n        return stsd;\n    }\n\n    function createSampleEntry(stsd) {\n        let codec = representation.codecs.substring(0, representation.codecs.indexOf('.'));\n\n        switch (codec) {\n            case 'avc1':\n                return createAVCVisualSampleEntry(stsd, codec);\n            case 'mp4a':\n                return createMP4AudioSampleEntry(stsd, codec);\n            default:\n                throw {\n                    code: MssErrors.MSS_UNSUPPORTED_CODEC_CODE,\n                    message: MssErrors.MSS_UNSUPPORTED_CODEC_MESSAGE,\n                    data: {\n                        codec: codec\n                    }\n                };\n        }\n    }\n\n    function createAVCVisualSampleEntry(stsd, codec) {\n        let avc1;\n\n        if (contentProtection) {\n            avc1 = ISOBoxer.createBox('encv', stsd, false);\n        } else {\n            avc1 = ISOBoxer.createBox('avc1', stsd, false);\n        }\n\n        // SampleEntry fields\n        avc1.reserved1 = [0x0, 0x0, 0x0, 0x0, 0x0, 0x0];\n        avc1.data_reference_index = 1;\n\n        // VisualSampleEntry fields\n        avc1.pre_defined1 = 0;\n        avc1.reserved2 = 0;\n        avc1.pre_defined2 = [0, 0, 0];\n        avc1.height = representation.height;\n        avc1.width = representation.width;\n        avc1.horizresolution = 72; // 72 dpi\n        avc1.vertresolution = 72; // 72 dpi\n        avc1.reserved3 = 0;\n        avc1.frame_count = 1; // 1 compressed video frame per sample\n        avc1.compressorname = [\n            0x0A, 0x41, 0x56, 0x43, 0x20, 0x43, 0x6F, 0x64, // = 'AVC Coding';\n            0x69, 0x6E, 0x67, 0x00, 0x00, 0x00, 0x00, 0x00,\n            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,\n            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00\n        ];\n        avc1.depth = 0x0018; // 0x0018 – images are in colour with no alpha.\n        avc1.pre_defined3 = 65535;\n        avc1.config = createAVC1ConfigurationRecord();\n        if (contentProtection) {\n            // Create and add Protection Scheme Info Box\n            let sinf = ISOBoxer.createBox('sinf', avc1);\n\n            // Create and add Original Format Box => indicate codec type of the encrypted content\n            createOriginalFormatBox(sinf, codec);\n\n            // Create and add Scheme Type box\n            createSchemeTypeBox(sinf);\n\n            // Create and add Scheme Information Box\n            createSchemeInformationBox(sinf);\n        }\n\n        return avc1;\n    }\n\n    function createAVC1ConfigurationRecord() {\n\n        let avcC = null;\n        let avcCLength = 15; // length = 15 by default (0 SPS and 0 PPS)\n\n        // First get all SPS and PPS from codecPrivateData\n        let sps = [];\n        let pps = [];\n        let AVCProfileIndication = 0;\n        let AVCLevelIndication = 0;\n        let profile_compatibility = 0;\n\n        let nalus = representation.codecPrivateData.split('00000001').slice(1);\n        let naluBytes, naluType;\n\n        for (let i = 0; i < nalus.length; i++) {\n            naluBytes = hexStringtoBuffer(nalus[i]);\n\n            naluType = naluBytes[0] & 0x1F;\n\n            switch (naluType) {\n                case NALUTYPE_SPS:\n                    sps.push(naluBytes);\n                    avcCLength += naluBytes.length + 2; // 2 = sequenceParameterSetLength field length\n                    break;\n                case NALUTYPE_PPS:\n                    pps.push(naluBytes);\n                    avcCLength += naluBytes.length + 2; // 2 = pictureParameterSetLength field length\n                    break;\n                default:\n                    break;\n            }\n        }\n\n        // Get profile and level from SPS\n        if (sps.length > 0) {\n            AVCProfileIndication = sps[0][1];\n            profile_compatibility = sps[0][2];\n            AVCLevelIndication = sps[0][3];\n        }\n\n        // Generate avcC buffer\n        avcC = new Uint8Array(avcCLength);\n\n        let i = 0;\n        // length\n        avcC[i++] = (avcCLength & 0xFF000000) >> 24;\n        avcC[i++] = (avcCLength & 0x00FF0000) >> 16;\n        avcC[i++] = (avcCLength & 0x0000FF00) >> 8;\n        avcC[i++] = (avcCLength & 0x000000FF);\n        avcC.set([0x61, 0x76, 0x63, 0x43], i); // type = 'avcC'\n        i += 4;\n        avcC[i++] = 1; // configurationVersion = 1\n        avcC[i++] = AVCProfileIndication;\n        avcC[i++] = profile_compatibility;\n        avcC[i++] = AVCLevelIndication;\n        avcC[i++] = 0xFF; // '11111' + lengthSizeMinusOne = 3\n        avcC[i++] = 0xE0 | sps.length; // '111' + numOfSequenceParameterSets\n        for (let n = 0; n < sps.length; n++) {\n            avcC[i++] = (sps[n].length & 0xFF00) >> 8;\n            avcC[i++] = (sps[n].length & 0x00FF);\n            avcC.set(sps[n], i);\n            i += sps[n].length;\n        }\n        avcC[i++] = pps.length; // numOfPictureParameterSets\n        for (let n = 0; n < pps.length; n++) {\n            avcC[i++] = (pps[n].length & 0xFF00) >> 8;\n            avcC[i++] = (pps[n].length & 0x00FF);\n            avcC.set(pps[n], i);\n            i += pps[n].length;\n        }\n\n        return avcC;\n    }\n\n    function createMP4AudioSampleEntry(stsd, codec) {\n        let mp4a;\n\n        if (contentProtection) {\n            mp4a = ISOBoxer.createBox('enca', stsd, false);\n        } else {\n            mp4a = ISOBoxer.createBox('mp4a', stsd, false);\n        }\n\n        // SampleEntry fields\n        mp4a.reserved1 = [0x0, 0x0, 0x0, 0x0, 0x0, 0x0];\n        mp4a.data_reference_index = 1;\n\n        // AudioSampleEntry fields\n        mp4a.reserved2 = [0x0, 0x0];\n        mp4a.channelcount = representation.audioChannels;\n        mp4a.samplesize = 16;\n        mp4a.pre_defined = 0;\n        mp4a.reserved_3 = 0;\n        mp4a.samplerate = representation.audioSamplingRate << 16;\n\n        mp4a.esds = createMPEG4AACESDescriptor();\n\n        if (contentProtection) {\n            // Create and add Protection Scheme Info Box\n            let sinf = ISOBoxer.createBox('sinf', mp4a);\n\n            // Create and add Original Format Box => indicate codec type of the encrypted content\n            createOriginalFormatBox(sinf, codec);\n\n            // Create and add Scheme Type box\n            createSchemeTypeBox(sinf);\n\n            // Create and add Scheme Information Box\n            createSchemeInformationBox(sinf);\n        }\n\n        return mp4a;\n    }\n\n    function createMPEG4AACESDescriptor() {\n\n        // AudioSpecificConfig (see ISO/IEC 14496-3, subpart 1) => corresponds to hex bytes contained in 'codecPrivateData' field\n        let audioSpecificConfig = hexStringtoBuffer(representation.codecPrivateData);\n\n        // ESDS length = esds box header length (= 12) +\n        //               ES_Descriptor header length (= 5) +\n        //               DecoderConfigDescriptor header length (= 15) +\n        //               decoderSpecificInfo header length (= 2) +\n        //               AudioSpecificConfig length (= codecPrivateData length)\n        let esdsLength = 34 + audioSpecificConfig.length;\n        let esds = new Uint8Array(esdsLength);\n\n        let i = 0;\n        // esds box\n        esds[i++] = (esdsLength & 0xFF000000) >> 24; // esds box length\n        esds[i++] = (esdsLength & 0x00FF0000) >> 16; // ''\n        esds[i++] = (esdsLength & 0x0000FF00) >> 8; // ''\n        esds[i++] = (esdsLength & 0x000000FF); // ''\n        esds.set([0x65, 0x73, 0x64, 0x73], i); // type = 'esds'\n        i += 4;\n        esds.set([0, 0, 0, 0], i); // version = 0, flags = 0\n        i += 4;\n        // ES_Descriptor (see ISO/IEC 14496-1 (Systems))\n        esds[i++] = 0x03; // tag = 0x03 (ES_DescrTag)\n        esds[i++] = 20 + audioSpecificConfig.length; // size\n        esds[i++] = (trackId & 0xFF00) >> 8; // ES_ID = track_id\n        esds[i++] = (trackId & 0x00FF); // ''\n        esds[i++] = 0; // flags and streamPriority\n\n        // DecoderConfigDescriptor (see ISO/IEC 14496-1 (Systems))\n        esds[i++] = 0x04; // tag = 0x04 (DecoderConfigDescrTag)\n        esds[i++] = 15 + audioSpecificConfig.length; // size\n        esds[i++] = 0x40; // objectTypeIndication = 0x40 (MPEG-4 AAC)\n        esds[i] = 0x05 << 2; // streamType = 0x05 (Audiostream)\n        esds[i] |= 0 << 1; // upStream = 0\n        esds[i++] |= 1; // reserved = 1\n        esds[i++] = 0xFF; // buffersizeDB = undefined\n        esds[i++] = 0xFF; // ''\n        esds[i++] = 0xFF; // ''\n        esds[i++] = (representation.bandwidth & 0xFF000000) >> 24; // maxBitrate\n        esds[i++] = (representation.bandwidth & 0x00FF0000) >> 16; // ''\n        esds[i++] = (representation.bandwidth & 0x0000FF00) >> 8; // ''\n        esds[i++] = (representation.bandwidth & 0x000000FF); // ''\n        esds[i++] = (representation.bandwidth & 0xFF000000) >> 24; // avgbitrate\n        esds[i++] = (representation.bandwidth & 0x00FF0000) >> 16; // ''\n        esds[i++] = (representation.bandwidth & 0x0000FF00) >> 8; // ''\n        esds[i++] = (representation.bandwidth & 0x000000FF); // ''\n\n        // DecoderSpecificInfo (see ISO/IEC 14496-1 (Systems))\n        esds[i++] = 0x05; // tag = 0x05 (DecSpecificInfoTag)\n        esds[i++] = audioSpecificConfig.length; // size\n        esds.set(audioSpecificConfig, i); // AudioSpecificConfig bytes\n\n        return esds;\n    }\n\n    function createOriginalFormatBox(sinf, codec) {\n        let frma = ISOBoxer.createBox('frma', sinf);\n        frma.data_format = stringToCharCode(codec);\n    }\n\n    function createSchemeTypeBox(sinf) {\n        let schm = ISOBoxer.createFullBox('schm', sinf);\n\n        schm.flags = 0;\n        schm.version = 0;\n        schm.scheme_type = 0x63656E63; // 'cenc' => common encryption\n        schm.scheme_version = 0x00010000; // version set to 0x00010000 (Major version 1, Minor version 0)\n    }\n\n    function createSchemeInformationBox(sinf) {\n        let schi = ISOBoxer.createBox('schi', sinf);\n\n        // Create and add Track Encryption Box\n        createTrackEncryptionBox(schi);\n    }\n\n    function createProtectionSystemSpecificHeaderBox(moov, keySystems) {\n        let pssh_bytes,\n            pssh,\n            i,\n            parsedBuffer;\n\n        for (i = 0; i < keySystems.length; i += 1) {\n            pssh_bytes = keySystems[i].initData;\n            if (pssh_bytes) {\n                parsedBuffer = ISOBoxer.parseBuffer(pssh_bytes);\n                pssh = parsedBuffer.fetch('pssh');\n                if (pssh) {\n                    ISOBoxer.Utils.appendBox(moov, pssh);\n                }\n            }\n        }\n    }\n\n    function createTrackEncryptionBox(schi) {\n        let tenc = ISOBoxer.createFullBox('tenc', schi);\n\n        tenc.flags = 0;\n        tenc.version = 0;\n\n        tenc.default_IsEncrypted = 0x1;\n        tenc.default_IV_size = 8;\n        tenc.default_KID = (contentProtection && (contentProtection.length) > 0 && contentProtection[0]['cenc:default_KID']) ?\n            contentProtection[0]['cenc:default_KID'] : [0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0];\n    }\n\n    function createTrexBox(moov) {\n        let trex = ISOBoxer.createFullBox('trex', moov);\n\n        trex.track_ID = trackId;\n        trex.default_sample_description_index = 1;\n        trex.default_sample_duration = 0;\n        trex.default_sample_size = 0;\n        trex.default_sample_flags = 0;\n\n        return trex;\n    }\n\n    function hexStringtoBuffer(str) {\n        let buf = new Uint8Array(str.length / 2);\n        let i;\n\n        for (i = 0; i < str.length / 2; i += 1) {\n            buf[i] = parseInt('' + str[i * 2] + str[i * 2 + 1], 16);\n        }\n        return buf;\n    }\n\n    function stringToCharCode(str) {\n        let code = 0;\n        let i;\n\n        for (i = 0; i < str.length; i += 1) {\n            code |= str.charCodeAt(i) << ((str.length - i - 1) * 8);\n        }\n        return code;\n    }\n\n    function generateMoov(rep) {\n        if (!rep || !rep.adaptation) {\n            return;\n        }\n\n        let isoFile,\n            arrayBuffer;\n\n        representation = rep;\n        adaptationSet = representation.adaptation;\n\n        period = adaptationSet.period;\n        trackId = adaptationSet.index + 1;\n        contentProtection = period.mpd.manifest.Period[period.index].AdaptationSet[adaptationSet.index].ContentProtection;\n\n        timescale = period.mpd.manifest.Period[period.index].AdaptationSet[adaptationSet.index].SegmentTemplate.timescale;\n\n        isoFile = ISOBoxer.createFile();\n        createFtypBox(isoFile);\n        createMoovBox(isoFile);\n\n        arrayBuffer = isoFile.write();\n\n        return arrayBuffer;\n    }\n\n    instance = {\n        generateMoov: generateMoov\n    };\n\n    return instance;\n}\n\nMssFragmentMoovProcessor.__dashjs_factory_name = 'MssFragmentMoovProcessor';\nexport default FactoryMaker.getClassFactory(MssFragmentMoovProcessor);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport MssFragmentMoofProcessor from './MssFragmentMoofProcessor.js';\nimport MssFragmentMoovProcessor from './MssFragmentMoovProcessor.js';\nimport {HTTPRequest} from '../streaming/vo/metrics/HTTPRequest.js';\nimport FactoryMaker from '../core/FactoryMaker.js';\n\n\n// Add specific box processors not provided by codem-isoboxer library\n\nfunction arrayEqual(arr1, arr2) {\n    return (arr1.length === arr2.length) && arr1.every(function (element, index) {\n        return element === arr2[index];\n    });\n}\n\nfunction saioProcessor() {\n    this._procFullBox();\n    if (this.flags & 1) {\n        this._procField('aux_info_type', 'uint', 32);\n        this._procField('aux_info_type_parameter', 'uint', 32);\n    }\n    this._procField('entry_count', 'uint', 32);\n    this._procFieldArray('offset', this.entry_count, 'uint', (this.version === 1) ? 64 : 32);\n}\n\nfunction saizProcessor() {\n    this._procFullBox();\n    if (this.flags & 1) {\n        this._procField('aux_info_type', 'uint', 32);\n        this._procField('aux_info_type_parameter', 'uint', 32);\n    }\n    this._procField('default_sample_info_size', 'uint', 8);\n    this._procField('sample_count', 'uint', 32);\n    if (this.default_sample_info_size === 0) {\n        this._procFieldArray('sample_info_size', this.sample_count, 'uint', 8);\n    }\n}\n\nfunction sencProcessor() {\n    this._procFullBox();\n    this._procField('sample_count', 'uint', 32);\n    if (this.flags & 1) {\n        this._procField('IV_size', 'uint', 8);\n    }\n    this._procEntries('entry', this.sample_count, function (entry) {\n        this._procEntryField(entry, 'InitializationVector', 'data', 8);\n        if (this.flags & 2) {\n            this._procEntryField(entry, 'NumberOfEntries', 'uint', 16);\n            this._procSubEntries(entry, 'clearAndCryptedData', entry.NumberOfEntries, function (clearAndCryptedData) {\n                this._procEntryField(clearAndCryptedData, 'BytesOfClearData', 'uint', 16);\n                this._procEntryField(clearAndCryptedData, 'BytesOfEncryptedData', 'uint', 32);\n            });\n        }\n    });\n}\n\nfunction uuidProcessor() {\n    let tfxdUserType = [0x6D, 0x1D, 0x9B, 0x05, 0x42, 0xD5, 0x44, 0xE6, 0x80, 0xE2, 0x14, 0x1D, 0xAF, 0xF7, 0x57, 0xB2];\n    let tfrfUserType = [0xD4, 0x80, 0x7E, 0xF2, 0xCA, 0x39, 0x46, 0x95, 0x8E, 0x54, 0x26, 0xCB, 0x9E, 0x46, 0xA7, 0x9F];\n    let sepiffUserType = [0xA2, 0x39, 0x4F, 0x52, 0x5A, 0x9B, 0x4f, 0x14, 0xA2, 0x44, 0x6C, 0x42, 0x7C, 0x64, 0x8D, 0xF4];\n\n    if (arrayEqual(this.usertype, tfxdUserType)) {\n        this._procFullBox();\n        if (this._parsing) {\n            this.type = 'tfxd';\n        }\n        this._procField('fragment_absolute_time', 'uint', (this.version === 1) ? 64 : 32);\n        this._procField('fragment_duration', 'uint', (this.version === 1) ? 64 : 32);\n    }\n\n    if (arrayEqual(this.usertype, tfrfUserType)) {\n        this._procFullBox();\n        if (this._parsing) {\n            this.type = 'tfrf';\n        }\n        this._procField('fragment_count', 'uint', 8);\n        this._procEntries('entry', this.fragment_count, function (entry) {\n            this._procEntryField(entry, 'fragment_absolute_time', 'uint', (this.version === 1) ? 64 : 32);\n            this._procEntryField(entry, 'fragment_duration', 'uint', (this.version === 1) ? 64 : 32);\n        });\n    }\n\n    if (arrayEqual(this.usertype, sepiffUserType)) {\n        if (this._parsing) {\n            this.type = 'sepiff';\n        }\n        sencProcessor.call(this);\n    }\n}\n\nfunction MssFragmentProcessor(config) {\n\n    config = config || {};\n    const context = this.context;\n    const dashMetrics = config.dashMetrics;\n    const playbackController = config.playbackController;\n    const eventBus = config.eventBus;\n    const protectionController = config.protectionController;\n    const ISOBoxer = config.ISOBoxer;\n    const debug = config.debug;\n    let mssFragmentMoovProcessor,\n        mssFragmentMoofProcessor,\n        instance;\n\n    function setup() {\n        ISOBoxer.addBoxProcessor('uuid', uuidProcessor);\n        ISOBoxer.addBoxProcessor('saio', saioProcessor);\n        ISOBoxer.addBoxProcessor('saiz', saizProcessor);\n        ISOBoxer.addBoxProcessor('senc', sencProcessor);\n\n        mssFragmentMoovProcessor = MssFragmentMoovProcessor(context).create({\n            protectionController: protectionController,\n            constants: config.constants,\n            ISOBoxer: ISOBoxer});\n\n        mssFragmentMoofProcessor = MssFragmentMoofProcessor(context).create({\n            dashMetrics: dashMetrics,\n            playbackController: playbackController,\n            ISOBoxer: ISOBoxer,\n            eventBus: eventBus,\n            debug: debug,\n            errHandler: config.errHandler\n        });\n    }\n\n    function generateMoov(rep) {\n        return mssFragmentMoovProcessor.generateMoov(rep);\n    }\n\n    function processFragment(e, streamProcessor) {\n        if (!e || !e.request || !e.response) {\n            throw new Error('e parameter is missing or malformed');\n        }\n\n        if (e.request.type === 'MediaSegment') {\n            // MediaSegment => convert to Smooth Streaming moof format\n            mssFragmentMoofProcessor.convertFragment(e, streamProcessor);\n\n        } else if (e.request.type === HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE) {\n            // FragmentInfo (live) => update segments list\n            mssFragmentMoofProcessor.updateSegmentList(e, streamProcessor);\n\n            // Stop event propagation (FragmentInfo must not be added to buffer)\n            e.sender = null;\n        }\n    }\n\n    instance = {\n        generateMoov,\n        processFragment\n    };\n\n    setup();\n\n    return instance;\n}\n\nMssFragmentProcessor.__dashjs_factory_name = 'MssFragmentProcessor';\nexport default FactoryMaker.getClassFactory(MssFragmentProcessor);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport DataChunk from '../streaming/vo/DataChunk.js';\nimport FragmentRequest from '../streaming/vo/FragmentRequest.js';\nimport MssFragmentInfoController from './MssFragmentInfoController.js';\nimport MssFragmentProcessor from './MssFragmentProcessor.js';\nimport MssParser from './parser/MssParser.js';\nimport MssErrors from './errors/MssErrors.js';\nimport DashJSError from '../streaming/vo/DashJSError.js';\nimport {HTTPRequest} from '../streaming/vo/metrics/HTTPRequest.js';\n\nfunction MssHandler(config) {\n\n    config = config || {};\n    const context = this.context;\n    const eventBus = config.eventBus;\n    const events = config.events;\n    const constants = config.constants;\n    const initSegmentType = config.initSegmentType;\n    const playbackController = config.playbackController;\n    const streamController = config.streamController;\n    let mssParser,\n        mssFragmentProcessor,\n        fragmentInfoControllers,\n        instance;\n\n    function setup() {\n        fragmentInfoControllers = [];\n    }\n\n    function createMssFragmentProcessor() {\n        mssFragmentProcessor = MssFragmentProcessor(context).create(config);\n    }\n\n    function getStreamProcessor(type) {\n        return streamController.getActiveStreamProcessors().filter(processor => {\n            return processor.getType() === type;\n        })[0];\n    }\n\n    function getFragmentInfoController(type) {\n        return fragmentInfoControllers.filter(controller => {\n            return (controller.getType() === type);\n        })[0];\n    }\n\n    function createDataChunk(request, streamId, endFragment) {\n        const chunk = new DataChunk();\n\n        chunk.streamId = streamId;\n        chunk.segmentType = request.type;\n        chunk.start = request.startTime;\n        chunk.duration = request.duration;\n        chunk.end = chunk.start + chunk.duration;\n        chunk.index = request.index;\n        chunk.bandwidth = request.bandwidth;\n        chunk.representation = request.representation;\n        chunk.endFragment = endFragment;\n\n        return chunk;\n    }\n\n    function startFragmentInfoControllers() {\n\n        // Create MssFragmentInfoControllers for each StreamProcessor of active stream (only for audio, video or text)\n        let processors = streamController.getActiveStreamProcessors();\n        processors.forEach(function (processor) {\n            if (processor.getType() === constants.VIDEO ||\n                processor.getType() === constants.AUDIO ||\n                processor.getType() === constants.TEXT) {\n\n                let fragmentInfoController = getFragmentInfoController(processor.getType());\n                if (!fragmentInfoController) {\n                    fragmentInfoController = MssFragmentInfoController(context).create({\n                        streamProcessor: processor,\n                        baseURLController: config.baseURLController,\n                        debug: config.debug\n                    });\n                    fragmentInfoController.initialize();\n                    fragmentInfoControllers.push(fragmentInfoController);\n                }\n                fragmentInfoController.start();\n            }\n        });\n    }\n\n    function stopFragmentInfoControllers() {\n        fragmentInfoControllers.forEach(c => {\n            c.reset();\n        });\n        fragmentInfoControllers = [];\n    }\n\n    function onInitFragmentNeeded(e) {\n        let streamProcessor = getStreamProcessor(e.mediaType);\n        if (!streamProcessor) {\n            return;\n        }\n\n        // Create init segment request\n        let representationController = streamProcessor.getRepresentationController();\n        let representation = representationController.getCurrentRepresentation();\n        let mediaInfo = streamProcessor.getMediaInfo();\n\n        let request = new FragmentRequest();\n        request.mediaType = representation.adaptation.type;\n        request.type = initSegmentType;\n        request.range = representation.range;\n        request.bandwidth = representation.bandwidth;\n        request.representation = representation;\n\n        const chunk = createDataChunk(request, mediaInfo.streamInfo.id, e.type !== events.FRAGMENT_LOADING_PROGRESS);\n\n        try {\n            // Generate init segment (moov)\n            chunk.bytes = mssFragmentProcessor.generateMoov(representation);\n\n            // Notify init segment has been loaded\n            eventBus.trigger(events.INIT_FRAGMENT_LOADED,\n                { chunk: chunk },\n                { streamId: mediaInfo.streamInfo.id, mediaType: representation.adaptation.type }\n            );\n        } catch (e) {\n            config.errHandler.error(new DashJSError(e.code, e.message, e.data));\n        }\n\n        // Change the sender value to stop event to be propagated\n        e.sender = null;\n    }\n\n    function onSegmentMediaLoaded(e) {\n        if (e.error) {\n            return;\n        }\n\n        let streamProcessor = getStreamProcessor(e.request.mediaType);\n        if (!streamProcessor) {\n            return;\n        }\n\n        // Process moof to transcode it from MSS to DASH (or to update segment timeline for SegmentInfo fragments)\n        mssFragmentProcessor.processFragment(e, streamProcessor);\n\n        if (e.request.type === HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE) {\n            // If FragmentInfo loaded, then notify corresponding MssFragmentInfoController\n            let fragmentInfoController = getFragmentInfoController(e.request.mediaType);\n            if (fragmentInfoController) {\n                fragmentInfoController.fragmentInfoLoaded(e);\n            }\n        }\n\n        // Start MssFragmentInfoControllers in case of start-over streams\n        let manifestInfo = e.request.representation.mediaInfo.streamInfo.manifestInfo;\n        if (!manifestInfo.isDynamic && manifestInfo.dvrWindowSize !== Infinity) {\n            startFragmentInfoControllers();\n        }\n    }\n\n    function onPlaybackPaused() {\n        if (playbackController.getIsDynamic() && playbackController.getTime() !== 0) {\n            startFragmentInfoControllers();\n        }\n    }\n\n    function onPlaybackSeeking() {\n        if (playbackController.getIsDynamic() && playbackController.getTime() !== 0) {\n            startFragmentInfoControllers();\n        }\n    }\n\n    function onTTMLPreProcess(ttmlSubtitles) {\n        if (!ttmlSubtitles || !ttmlSubtitles.data) {\n            return;\n        }\n\n        ttmlSubtitles.data = ttmlSubtitles.data.replace(/http:\\/\\/www.w3.org\\/2006\\/10\\/ttaf1/gi, 'http://www.w3.org/ns/ttml');\n    }\n\n    function registerEvents() {\n        eventBus.on(events.INIT_FRAGMENT_NEEDED, onInitFragmentNeeded, instance, { priority: dashjs.FactoryMaker.getSingletonFactoryByName(eventBus.getClassName()).EVENT_PRIORITY_HIGH });\n        eventBus.on(events.PLAYBACK_PAUSED, onPlaybackPaused, instance, { priority: dashjs.FactoryMaker.getSingletonFactoryByName(eventBus.getClassName()).EVENT_PRIORITY_HIGH });\n        eventBus.on(events.PLAYBACK_SEEKING, onPlaybackSeeking, instance, { priority: dashjs.FactoryMaker.getSingletonFactoryByName(eventBus.getClassName()).EVENT_PRIORITY_HIGH });\n        eventBus.on(events.FRAGMENT_LOADING_COMPLETED, onSegmentMediaLoaded, instance, { priority: dashjs.FactoryMaker.getSingletonFactoryByName(eventBus.getClassName()).EVENT_PRIORITY_HIGH });\n        eventBus.on(events.TTML_TO_PARSE, onTTMLPreProcess, instance);\n    }\n\n    function reset() {\n        if (mssParser) {\n            mssParser.reset();\n            mssParser = undefined;\n        }\n\n        eventBus.off(events.INIT_FRAGMENT_NEEDED, onInitFragmentNeeded, this);\n        eventBus.off(events.PLAYBACK_PAUSED, onPlaybackPaused, this);\n        eventBus.off(events.PLAYBACK_SEEKING, onPlaybackSeeking, this);\n        eventBus.off(events.FRAGMENT_LOADING_COMPLETED, onSegmentMediaLoaded, this);\n        eventBus.off(events.TTML_TO_PARSE, onTTMLPreProcess, this);\n\n        // Reset FragmentInfoControllers\n        stopFragmentInfoControllers();\n    }\n\n    function createMssParser() {\n        mssParser = MssParser(context).create(config);\n        return mssParser;\n    }\n\n    instance = {\n        reset,\n        createMssParser,\n        createMssFragmentProcessor,\n        registerEvents\n    };\n\n    setup();\n\n    return instance;\n}\n\nMssHandler.__dashjs_factory_name = 'MssHandler';\nconst factory = dashjs.FactoryMaker.getClassFactory(MssHandler);\nfactory.errors = MssErrors;\ndashjs.FactoryMaker.updateClassFactory(MssHandler.__dashjs_factory_name, factory);\nexport default factory;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport ErrorsBase from '../../core/errors/ErrorsBase.js';\n\n/**\n * @class\n *\n */\nclass MssErrors extends ErrorsBase {\n    constructor() {\n        super();\n        /**\n         * Error code returned when no tfrf box is detected in MSS live stream\n         */\n        this.MSS_NO_TFRF_CODE = 200;\n\n        /**\n         * Error code returned when one of the codecs defined in the manifest is not supported\n         */\n        this.MSS_UNSUPPORTED_CODEC_CODE = 201;\n\n        this.MSS_NO_TFRF_MESSAGE = 'Missing tfrf in live media segment';\n        this.MSS_UNSUPPORTED_CODEC_MESSAGE = 'Unsupported codec';\n    }\n}\n\nlet mssErrors = new MssErrors();\nexport default mssErrors;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * @module MssParser\n * @ignore\n * @param {Object} config object\n */\n\nimport BigInt from '../../../externals/BigInteger.js';\nimport FactoryMaker from '../../core/FactoryMaker.js';\nimport ProtectionConstants from '../../streaming/constants/ProtectionConstants.js';\n\nfunction MssParser(config) {\n    config = config || {};\n    const BASE64 = config.BASE64;\n    const debug = config.debug;\n    const constants = config.constants;\n    const manifestModel = config.manifestModel;\n    const settings = config.settings;\n\n    const DEFAULT_TIME_SCALE = 10000000.0;\n    const SUPPORTED_CODECS = ['AAC', 'AACL', 'AACH', 'AACP', 'AVC1', 'H264', 'TTML', 'DFXP'];\n    // MPEG-DASH Role and accessibility mapping for text tracks according to ETSI TS 103 285 v1.1.1 (section 7.1.2)\n    const ROLE = {\n        'CAPT': 'main',\n        'SUBT': 'alternate',\n        'DESC': 'main'\n    };\n    const ACCESSIBILITY = {\n        'DESC': '2'\n    };\n    const samplingFrequencyIndex = {\n        96000: 0x0,\n        88200: 0x1,\n        64000: 0x2,\n        48000: 0x3,\n        44100: 0x4,\n        32000: 0x5,\n        24000: 0x6,\n        22050: 0x7,\n        16000: 0x8,\n        12000: 0x9,\n        11025: 0xA,\n        8000: 0xB,\n        7350: 0xC\n    };\n    const mimeTypeMap = {\n        'video': 'video/mp4',\n        'audio': 'audio/mp4',\n        'text': 'application/mp4'\n    };\n\n    let instance,\n        logger,\n        initialBufferSettings;\n\n\n    function setup() {\n        logger = debug.getLogger(instance);\n    }\n\n    function getAttributeAsBoolean(node, attrName) {\n        const value = node.getAttribute(attrName);\n        if (!value) {\n            return false;\n        }\n        return value.toLowerCase() === 'true';\n    }\n\n    function mapPeriod(smoothStreamingMedia, timescale) {\n        const period = {};\n        let streams,\n            adaptation;\n\n        // For each StreamIndex node, create an AdaptationSet element\n        period.AdaptationSet = [];\n        streams = smoothStreamingMedia.getElementsByTagName('StreamIndex');\n        for (let i = 0; i < streams.length; i++) {\n            adaptation = mapAdaptationSet(streams[i], timescale);\n            if (adaptation !== null) {\n                period.AdaptationSet.push(adaptation);\n            }\n        }\n\n        return period;\n    }\n\n    function mapAdaptationSet(streamIndex, timescale) {\n        const adaptationSet = {};\n        const representations = [];\n        let segmentTemplate;\n        let qualityLevels,\n            representation,\n            i,\n            index;\n\n        const name = streamIndex.getAttribute('Name');\n        const type = streamIndex.getAttribute('Type');\n        const lang = streamIndex.getAttribute('Language');\n        const fallBackId = lang ? type + '_' + lang : type;\n\n        adaptationSet.id = name || fallBackId;\n        adaptationSet.contentType = type;\n        adaptationSet.lang = lang || 'und';\n        adaptationSet.mimeType = mimeTypeMap[type];\n        adaptationSet.subType = streamIndex.getAttribute('Subtype');\n        adaptationSet.maxWidth = streamIndex.getAttribute('MaxWidth');\n        adaptationSet.maxHeight = streamIndex.getAttribute('MaxHeight');\n\n        // Map text tracks subTypes to MPEG-DASH AdaptationSet role and accessibility (see ETSI TS 103 285 v1.1.1, section 7.1.2)\n        if (adaptationSet.subType) {\n            if (ROLE[adaptationSet.subType]) {\n                adaptationSet.Role = [{\n                    schemeIdUri: 'urn:mpeg:dash:role:2011',\n                    value: ROLE[adaptationSet.subType]\n                }];\n            }\n            if (ACCESSIBILITY[adaptationSet.subType]) {\n                adaptationSet.Accessibility = [{\n                    schemeIdUri: 'urn:tva:metadata:cs:AudioPurposeCS:2007',\n                    value: ACCESSIBILITY[adaptationSet.subType]\n                }];\n            }\n        }\n\n        // Create a SegmentTemplate with a SegmentTimeline\n        segmentTemplate = mapSegmentTemplate(streamIndex, timescale);\n\n        qualityLevels = streamIndex.getElementsByTagName('QualityLevel');\n        // For each QualityLevel node, create a Representation element\n        for (i = 0; i < qualityLevels.length; i++) {\n            // Propagate BaseURL and mimeType\n            qualityLevels[i].BaseURL = adaptationSet.BaseURL;\n            qualityLevels[i].mimeType = adaptationSet.mimeType;\n\n            // Set quality level id\n            index = qualityLevels[i].getAttribute('Index');\n            qualityLevels[i].Id = adaptationSet.id + ((index !== null) ? ('_' + index) : '');\n\n            // Map Representation to QualityLevel\n            representation = mapRepresentation(qualityLevels[i], streamIndex);\n\n            if (representation !== null) {\n                // Copy SegmentTemplate into Representation\n                representation.SegmentTemplate = segmentTemplate;\n\n                representations.push(representation);\n            }\n        }\n\n        if (representations.length === 0) {\n            return null;\n        }\n\n        adaptationSet.Representation = representations;\n\n        // Set SegmentTemplate\n        adaptationSet.SegmentTemplate = segmentTemplate;\n\n        return adaptationSet;\n    }\n\n    function mapRepresentation(qualityLevel, streamIndex) {\n        const representation = {};\n        const type = streamIndex.getAttribute('Type');\n        let fourCCValue = null;\n        let width = null;\n        let height = null;\n\n        representation.id = qualityLevel.Id;\n        representation.bandwidth = parseInt(qualityLevel.getAttribute('Bitrate'), 10);\n        representation.mimeType = qualityLevel.mimeType;\n\n        width = parseInt(qualityLevel.getAttribute('MaxWidth'), 10);\n        height = parseInt(qualityLevel.getAttribute('MaxHeight'), 10);\n        if (!isNaN(width)) {\n            representation.width = width;\n        }\n        if (!isNaN(height)) {\n            representation.height = height;\n        }\n\n\n        fourCCValue = qualityLevel.getAttribute('FourCC');\n\n        // If FourCC not defined at QualityLevel level, then get it from StreamIndex level\n        if (fourCCValue === null || fourCCValue === '') {\n            fourCCValue = streamIndex.getAttribute('FourCC');\n        }\n\n        // If still not defined (optionnal for audio stream, see https://msdn.microsoft.com/en-us/library/ff728116%28v=vs.95%29.aspx),\n        // then we consider the stream is an audio AAC stream\n        if (fourCCValue === null || fourCCValue === '') {\n            if (type === constants.AUDIO) {\n                fourCCValue = 'AAC';\n            } else if (type === constants.VIDEO) {\n                logger.debug('FourCC is not defined whereas it is required for a QualityLevel element for a StreamIndex of type \"video\"');\n                return null;\n            }\n        }\n\n        // Check if codec is supported\n        if (SUPPORTED_CODECS.indexOf(fourCCValue.toUpperCase()) === -1) {\n            // Do not send warning\n            logger.warn('Codec not supported: ' + fourCCValue);\n            return null;\n        }\n\n        // Get codecs value according to FourCC field\n        if (fourCCValue === 'H264' || fourCCValue === 'AVC1') {\n            representation.codecs = getH264Codec(qualityLevel);\n        } else if (fourCCValue.indexOf('AAC') >= 0) {\n            representation.codecs = getAACCodec(qualityLevel, fourCCValue);\n            representation.audioSamplingRate = parseInt(qualityLevel.getAttribute('SamplingRate'), 10);\n            representation.audioChannels = parseInt(qualityLevel.getAttribute('Channels'), 10);\n        } else if (fourCCValue.indexOf('TTML') || fourCCValue.indexOf('DFXP')) {\n            representation.codecs = constants.STPP;\n        }\n\n        representation.codecPrivateData = '' + qualityLevel.getAttribute('CodecPrivateData');\n        representation.BaseURL = qualityLevel.BaseURL;\n\n        return representation;\n    }\n\n    function getH264Codec(qualityLevel) {\n        let codecPrivateData = qualityLevel.getAttribute('CodecPrivateData').toString();\n        let nalHeader,\n            avcoti;\n\n\n        // Extract from the CodecPrivateData field the hexadecimal representation of the following\n        // three bytes in the sequence parameter set NAL unit.\n        // => Find the SPS nal header\n        nalHeader = /00000001[0-9]7/.exec(codecPrivateData);\n        // => Find the 6 characters after the SPS nalHeader (if it exists)\n        avcoti = nalHeader && nalHeader[0] ? (codecPrivateData.substr(codecPrivateData.indexOf(nalHeader[0]) + 10, 6)) : undefined;\n\n        return 'avc1.' + avcoti;\n    }\n\n    function getAACCodec(qualityLevel, fourCCValue) {\n        const samplingRate = parseInt(qualityLevel.getAttribute('SamplingRate'), 10);\n        let codecPrivateData = qualityLevel.getAttribute('CodecPrivateData').toString();\n        let objectType = 0;\n        let codecPrivateDataHex,\n            arr16,\n            indexFreq,\n            extensionSamplingFrequencyIndex;\n\n        //chrome problem, in implicit AAC HE definition, so when AACH is detected in FourCC\n        //set objectType to 5 => strange, it should be 2\n        if (fourCCValue === 'AACH') {\n            objectType = 0x05;\n        }\n        //if codecPrivateData is empty, build it :\n        if (codecPrivateData === undefined || codecPrivateData === '') {\n            objectType = 0x02; //AAC Main Low Complexity => object Type = 2\n            indexFreq = samplingFrequencyIndex[samplingRate];\n            if (fourCCValue === 'AACH') {\n                // 4 bytes :     XXXXX         XXXX          XXXX             XXXX                  XXXXX      XXX   XXXXXXX\n                //           ' ObjectType' 'Freq Index' 'Channels value'   'Extens Sampl Freq'  'ObjectType'  'GAS' 'alignment = 0'\n                objectType = 0x05; // High Efficiency AAC Profile = object Type = 5 SBR\n                codecPrivateData = new Uint8Array(4);\n                extensionSamplingFrequencyIndex = samplingFrequencyIndex[samplingRate * 2]; // in HE AAC Extension Sampling frequence\n                // equals to SamplingRate*2\n                //Freq Index is present for 3 bits in the first byte, last bit is in the second\n                codecPrivateData[0] = (objectType << 3) | (indexFreq >> 1);\n                codecPrivateData[1] = (indexFreq << 7) | (qualityLevel.Channels << 3) | (extensionSamplingFrequencyIndex >> 1);\n                codecPrivateData[2] = (extensionSamplingFrequencyIndex << 7) | (0x02 << 2); // origin object type equals to 2 => AAC Main Low Complexity\n                codecPrivateData[3] = 0x0; //alignment bits\n\n                arr16 = new Uint16Array(2);\n                arr16[0] = (codecPrivateData[0] << 8) + codecPrivateData[1];\n                arr16[1] = (codecPrivateData[2] << 8) + codecPrivateData[3];\n                //convert decimal to hex value\n                codecPrivateDataHex = arr16[0].toString(16);\n                codecPrivateDataHex = arr16[0].toString(16) + arr16[1].toString(16);\n\n            } else {\n                // 2 bytes :     XXXXX         XXXX          XXXX              XXX\n                //           ' ObjectType' 'Freq Index' 'Channels value'   'GAS = 000'\n                codecPrivateData = new Uint8Array(2);\n                //Freq Index is present for 3 bits in the first byte, last bit is in the second\n                codecPrivateData[0] = (objectType << 3) | (indexFreq >> 1);\n                codecPrivateData[1] = (indexFreq << 7) | (parseInt(qualityLevel.getAttribute('Channels'), 10) << 3);\n                // put the 2 bytes in an 16 bits array\n                arr16 = new Uint16Array(1);\n                arr16[0] = (codecPrivateData[0] << 8) + codecPrivateData[1];\n                //convert decimal to hex value\n                codecPrivateDataHex = arr16[0].toString(16);\n            }\n\n            codecPrivateData = '' + codecPrivateDataHex;\n            codecPrivateData = codecPrivateData.toUpperCase();\n            qualityLevel.setAttribute('CodecPrivateData', codecPrivateData);\n        } else if (objectType === 0) {\n            objectType = (parseInt(codecPrivateData.substr(0, 2), 16) & 0xF8) >> 3;\n        }\n\n        return 'mp4a.40.' + objectType;\n    }\n\n    function mapSegmentTemplate(streamIndex, timescale) {\n        const segmentTemplate = {};\n        let mediaUrl,\n            streamIndexTimeScale,\n            url;\n\n        url = streamIndex.getAttribute('Url');\n        mediaUrl = url ? url.replace('{bitrate}', '$Bandwidth$') : null;\n        mediaUrl = mediaUrl ? mediaUrl.replace('{start time}', '$Time$') : null;\n\n        streamIndexTimeScale = streamIndex.getAttribute('TimeScale');\n        streamIndexTimeScale = streamIndexTimeScale ? parseFloat(streamIndexTimeScale) : timescale;\n\n        segmentTemplate.media = mediaUrl;\n        segmentTemplate.timescale = streamIndexTimeScale;\n\n        segmentTemplate.SegmentTimeline = mapSegmentTimeline(streamIndex, segmentTemplate.timescale);\n\n        // Patch: set availabilityTimeOffset to Infinity since segments are available as long as they are present in timeline\n        segmentTemplate.availabilityTimeOffset = 'INF';\n\n        return segmentTemplate;\n    }\n\n    function mapSegmentTimeline(streamIndex, timescale) {\n        const segmentTimeline = {};\n        const chunks = streamIndex.getElementsByTagName('c');\n        const segments = [];\n        let segment,\n            prevSegment,\n            tManifest,\n            i, j, r;\n        let duration = 0;\n\n        for (i = 0; i < chunks.length; i++) {\n            segment = {};\n\n            // Get time 't' attribute value\n            tManifest = chunks[i].getAttribute('t');\n\n            // => segment.tManifest = original timestamp value as a string (for constructing the fragment request url, see DashHandler)\n            // => segment.t = number value of timestamp (maybe rounded value, but only for 0.1 microsecond)\n            if (tManifest && BigInt(tManifest).greater(BigInt(Number.MAX_SAFE_INTEGER))) {\n                segment.tManifest = tManifest;\n            }\n            segment.t = parseFloat(tManifest);\n\n            // Get duration 'd' attribute value\n            segment.d = parseFloat(chunks[i].getAttribute('d'));\n\n            // If 't' not defined for first segment then t=0\n            if ((i === 0) && !segment.t) {\n                segment.t = 0;\n            }\n\n            if (i > 0) {\n                prevSegment = segments[segments.length - 1];\n                // Update previous segment duration if not defined\n                if (!prevSegment.d) {\n                    if (prevSegment.tManifest) {\n                        prevSegment.d = BigInt(tManifest).subtract(BigInt(prevSegment.tManifest)).toJSNumber();\n                    } else {\n                        prevSegment.d = segment.t - prevSegment.t;\n                    }\n                    duration += prevSegment.d;\n                }\n                // Set segment absolute timestamp if not set in manifest\n                if (!segment.t) {\n                    if (prevSegment.tManifest) {\n                        segment.tManifest = BigInt(prevSegment.tManifest).add(BigInt(prevSegment.d)).toString();\n                        segment.t = parseFloat(segment.tManifest);\n                    } else {\n                        segment.t = prevSegment.t + prevSegment.d;\n                    }\n                }\n            }\n\n            if (segment.d) {\n                duration += segment.d;\n            }\n\n            // Create new segment\n            segments.push(segment);\n\n            // Support for 'r' attribute (i.e. \"repeat\" as in MPEG-DASH)\n            r = parseFloat(chunks[i].getAttribute('r'));\n            if (r) {\n\n                for (j = 0; j < (r - 1); j++) {\n                    prevSegment = segments[segments.length - 1];\n                    segment = {};\n                    segment.t = prevSegment.t + prevSegment.d;\n                    segment.d = prevSegment.d;\n                    if (prevSegment.tManifest) {\n                        segment.tManifest = BigInt(prevSegment.tManifest).add(BigInt(prevSegment.d)).toString();\n                    }\n                    duration += segment.d;\n                    segments.push(segment);\n                }\n            }\n        }\n\n        segmentTimeline.S = segments;\n        segmentTimeline.duration = duration / timescale;\n\n        return segmentTimeline;\n    }\n\n    function getKIDFromProtectionHeader(protectionHeader) {\n        let prHeader,\n            wrmHeader,\n            xmlReader,\n            KID;\n\n        // Get PlayReady header as byte array (base64 decoded)\n        prHeader = BASE64.decodeArray(protectionHeader.firstChild.data);\n\n        // Get Right Management header (WRMHEADER) from PlayReady header\n        wrmHeader = getWRMHeaderFromPRHeader(prHeader);\n\n        if (wrmHeader) {\n            // Convert from multi-byte to unicode\n            wrmHeader = new Uint16Array(wrmHeader.buffer);\n\n            // Convert to string\n            wrmHeader = String.fromCharCode.apply(null, wrmHeader);\n\n            // Parse <WRMHeader> to get KID field value\n            xmlReader = (new DOMParser()).parseFromString(wrmHeader, 'application/xml');\n            KID = xmlReader.querySelector('KID').textContent;\n\n            // Get KID (base64 decoded) as byte array\n            KID = BASE64.decodeArray(KID);\n\n            // Convert UUID from little-endian to big-endian\n            convertUuidEndianness(KID);\n        }\n\n        return KID;\n    }\n\n    function getWRMHeaderFromPRHeader(prHeader) {\n        let length,\n            recordCount,\n            recordType,\n            recordLength,\n            recordValue;\n        let i = 0;\n\n        // Parse PlayReady header\n\n        // Length - 32 bits (LE format)\n        length = (prHeader[i + 3] << 24) + (prHeader[i + 2] << 16) + (prHeader[i + 1] << 8) + prHeader[i]; // eslint-disable-line\n        i += 4;\n\n        // Record count - 16 bits (LE format)\n        recordCount = (prHeader[i + 1] << 8) + prHeader[i]; // eslint-disable-line\n        i += 2;\n\n        // Parse records\n        while (i < prHeader.length) {\n            // Record type - 16 bits (LE format)\n            recordType = (prHeader[i + 1] << 8) + prHeader[i];\n            i += 2;\n\n            // Check if Rights Management header (record type = 0x01)\n            if (recordType === 0x01) {\n\n                // Record length - 16 bits (LE format)\n                recordLength = (prHeader[i + 1] << 8) + prHeader[i];\n                i += 2;\n\n                // Record value => contains <WRMHEADER>\n                recordValue = new Uint8Array(recordLength);\n                recordValue.set(prHeader.subarray(i, i + recordLength));\n                return recordValue;\n            }\n        }\n\n        return null;\n    }\n\n    function convertUuidEndianness(uuid) {\n        swapBytes(uuid, 0, 3);\n        swapBytes(uuid, 1, 2);\n        swapBytes(uuid, 4, 5);\n        swapBytes(uuid, 6, 7);\n    }\n\n    function swapBytes(bytes, pos1, pos2) {\n        const temp = bytes[pos1];\n        bytes[pos1] = bytes[pos2];\n        bytes[pos2] = temp;\n    }\n\n\n    function createPRContentProtection(protectionHeader) {\n        let pro = {\n            __text: protectionHeader.firstChild.data,\n            __prefix: 'mspr'\n        };\n        return {\n            schemeIdUri: 'urn:uuid:' + ProtectionConstants.PLAYREADY_UUID,\n            value: ProtectionConstants.PLAYREADY_KEYSTEM_STRING,\n            pro: pro\n        };\n    }\n\n    function createWidevineContentProtection(KID) {\n        let widevineCP = {\n            schemeIdUri: 'urn:uuid:' + ProtectionConstants.WIDEVINE_UUID,\n            value: ProtectionConstants.WIDEVINE_KEYSTEM_STRING\n        };\n        if (!KID) {\n            return widevineCP;\n        }\n        // Create Widevine CENC header (Protocol Buffer) with KID value\n        const wvCencHeader = new Uint8Array(2 + KID.length);\n        wvCencHeader[0] = 0x12;\n        wvCencHeader[1] = 0x10;\n        wvCencHeader.set(KID, 2);\n\n        // Create a pssh box\n        const length = 12 /* box length, type, version and flags */ + 16 /* SystemID */ + 4 /* data length */ + wvCencHeader.length;\n        let pssh = new Uint8Array(length);\n        let i = 0;\n\n        // Set box length value\n        pssh[i++] = (length & 0xFF000000) >> 24;\n        pssh[i++] = (length & 0x00FF0000) >> 16;\n        pssh[i++] = (length & 0x0000FF00) >> 8;\n        pssh[i++] = (length & 0x000000FF);\n\n        // Set type ('pssh'), version (0) and flags (0)\n        pssh.set([0x70, 0x73, 0x73, 0x68, 0x00, 0x00, 0x00, 0x00], i);\n        i += 8;\n\n        // Set SystemID ('edef8ba9-79d6-4ace-a3c8-27dcd51d21ed')\n        pssh.set([0xed, 0xef, 0x8b, 0xa9, 0x79, 0xd6, 0x4a, 0xce, 0xa3, 0xc8, 0x27, 0xdc, 0xd5, 0x1d, 0x21, 0xed], i);\n        i += 16;\n\n        // Set data length value\n        pssh[i++] = (wvCencHeader.length & 0xFF000000) >> 24;\n        pssh[i++] = (wvCencHeader.length & 0x00FF0000) >> 16;\n        pssh[i++] = (wvCencHeader.length & 0x0000FF00) >> 8;\n        pssh[i++] = (wvCencHeader.length & 0x000000FF);\n\n        // Copy Widevine CENC header\n        pssh.set(wvCencHeader, i);\n\n        // Convert to BASE64 string\n        pssh = String.fromCharCode.apply(null, pssh);\n        pssh = BASE64.encodeASCII(pssh);\n\n        widevineCP.pssh = { __text: pssh };\n\n        return widevineCP;\n    }\n\n    function processManifest(xmlDoc) {\n        const manifest = {};\n        const contentProtections = [];\n        const smoothStreamingMedia = xmlDoc.getElementsByTagName('SmoothStreamingMedia')[0];\n        const protection = xmlDoc.getElementsByTagName('Protection')[0];\n        let protectionHeader = null;\n        let period,\n            adaptations,\n            contentProtection,\n            KID,\n            timestampOffset,\n            startTime,\n            segments,\n            timescale,\n            segmentDuration,\n            i, j;\n\n        // Set manifest node properties\n        manifest.protocol = 'MSS';\n        manifest.profiles = 'urn:mpeg:dash:profile:isoff-live:2011';\n        manifest.type = getAttributeAsBoolean(smoothStreamingMedia, 'IsLive') ? 'dynamic' : 'static';\n        timescale = smoothStreamingMedia.getAttribute('TimeScale');\n        manifest.timescale = timescale ? parseFloat(timescale) : DEFAULT_TIME_SCALE;\n        let dvrWindowLength = parseFloat(smoothStreamingMedia.getAttribute('DVRWindowLength'));\n        // If the DVRWindowLength field is omitted for a live presentation or set to 0, the DVR window is effectively infinite\n        if (manifest.type === 'dynamic' && (dvrWindowLength === 0 || isNaN(dvrWindowLength))) {\n            dvrWindowLength = Infinity;\n        }\n        // Star-over\n        if (dvrWindowLength === 0 && getAttributeAsBoolean(smoothStreamingMedia, 'CanSeek')) {\n            dvrWindowLength = Infinity;\n        }\n\n        if (dvrWindowLength > 0) {\n            manifest.timeShiftBufferDepth = dvrWindowLength / manifest.timescale;\n        }\n\n        let duration = parseFloat(smoothStreamingMedia.getAttribute('Duration'));\n        manifest.mediaPresentationDuration = (duration === 0) ? Infinity : duration / manifest.timescale;\n        // By default, set minBufferTime to 2 sec. (but set below according to video segment duration)\n        manifest.minBufferTime = 2;\n        manifest.ttmlTimeIsRelative = true;\n\n        // Live manifest with Duration = start-over\n        if (manifest.type === 'dynamic' && duration > 0) {\n            manifest.type = 'static';\n            // We set timeShiftBufferDepth to initial duration, to be used by MssFragmentController to update segment timeline\n            manifest.timeShiftBufferDepth = duration / manifest.timescale;\n            // Duration will be set according to current segment timeline duration (see below)\n        }\n\n        if (manifest.type === 'dynamic') {\n            manifest.refreshManifestOnSwitchTrack = true; // Refresh manifest when switching tracks\n            manifest.doNotUpdateDVRWindowOnBufferUpdated = true; // DVRWindow is update by MssFragmentMoofPocessor based on tfrf boxes\n            manifest.ignorePostponeTimePeriod = true; // Never update manifest\n            manifest.availabilityStartTime = new Date(null); // Returns 1970\n        }\n\n        // Map period node to manifest root node\n        period = mapPeriod(smoothStreamingMedia, manifest.timescale);\n        manifest.Period = [period];\n\n        // Initialize period start time\n        period.start = 0;\n\n        // Uncomment to test live to static manifests\n        // if (manifest.type !== 'static') {\n        //     manifest.type = 'static';\n        //     manifest.mediaPresentationDuration = manifest.timeShiftBufferDepth;\n        //     manifest.timeShiftBufferDepth = null;\n        // }\n\n        // ContentProtection node\n        if (protection !== undefined) {\n            protectionHeader = xmlDoc.getElementsByTagName('ProtectionHeader')[0];\n\n            // Some packagers put newlines into the ProtectionHeader base64 string, which is not good\n            // because this cannot be correctly parsed. Let's just filter out any newlines found in there.\n            protectionHeader.firstChild.data = protectionHeader.firstChild.data.replace(/\\n|\\r/g, '');\n\n            // Get KID (in CENC format) from protection header\n            KID = getKIDFromProtectionHeader(protectionHeader);\n\n            // Create ContentProtection for PlayReady\n            contentProtection = createPRContentProtection(protectionHeader);\n            contentProtection['cenc:default_KID'] = KID;\n            contentProtections.push(contentProtection);\n\n            // Create ContentProtection for Widevine (as a CENC protection)\n            contentProtection = createWidevineContentProtection(KID);\n            contentProtection['cenc:default_KID'] = KID;\n            contentProtections.push(contentProtection);\n\n            manifest.ContentProtection = contentProtections;\n        }\n\n        adaptations = period.AdaptationSet;\n\n        for (i = 0; i < adaptations.length; i += 1) {\n            adaptations[i].SegmentTemplate.initialization = '$Bandwidth$';\n            // Propagate content protection information into each adaptation\n            if (manifest.ContentProtection !== undefined) {\n                adaptations[i].ContentProtection = manifest.ContentProtection;\n                adaptations[i].ContentProtection = manifest.ContentProtection;\n            }\n\n            if (adaptations[i].contentType === 'video') {\n                // Get video segment duration\n                segmentDuration = adaptations[i].SegmentTemplate.SegmentTimeline.S[0].d / adaptations[i].SegmentTemplate.timescale;\n                // Set minBufferTime to one segment duration\n                manifest.minBufferTime = segmentDuration;\n\n                if (manifest.type === 'dynamic') {\n                    // Match timeShiftBufferDepth to video segment timeline duration\n                    if (manifest.timeShiftBufferDepth > 0 &&\n                        manifest.timeShiftBufferDepth !== Infinity &&\n                        manifest.timeShiftBufferDepth > adaptations[i].SegmentTemplate.SegmentTimeline.duration) {\n                        manifest.timeShiftBufferDepth = adaptations[i].SegmentTemplate.SegmentTimeline.duration;\n                    }\n                }\n            }\n        }\n\n        // Cap minBufferTime to timeShiftBufferDepth\n        manifest.minBufferTime = Math.min(manifest.minBufferTime, (manifest.timeShiftBufferDepth ? manifest.timeShiftBufferDepth : Infinity));\n\n        // In case of live streams:\n        // 1- configure player buffering properties according to target live delay\n        // 2- adapt live delay and then buffers length in case timeShiftBufferDepth is too small compared to target live delay (see PlaybackController.computeLiveDelay())\n        // 3- Set retry attempts and intervals for FragmentInfo requests\n        if (manifest.type === 'dynamic') {\n            let targetLiveDelay = settings.get().streaming.delay.liveDelay;\n            if (!targetLiveDelay) {\n                const liveDelayFragmentCount = settings.get().streaming.delay.liveDelayFragmentCount !== null && !isNaN(settings.get().streaming.delay.liveDelayFragmentCount) ? settings.get().streaming.delay.liveDelayFragmentCount : 4;\n                targetLiveDelay = segmentDuration * liveDelayFragmentCount;\n            }\n            let targetDelayCapping = Math.max(manifest.timeShiftBufferDepth - 10/*END_OF_PLAYLIST_PADDING*/, manifest.timeShiftBufferDepth / 2);\n            let liveDelay = Math.min(targetDelayCapping, targetLiveDelay);\n            // Consider a margin of more than one segment in order to avoid Precondition Failed errors (412), for example if audio and video are not correctly synchronized\n            let bufferTime = liveDelay - (segmentDuration * 1.5);\n\n            // Store initial buffer settings\n            initialBufferSettings = {\n                'streaming': {\n                    'buffer': {\n                        'bufferTimeDefault': settings.get().streaming.buffer.bufferTimeDefault,\n                        'bufferTimeAtTopQuality': settings.get().streaming.buffer.bufferTimeAtTopQuality,\n                        'bufferTimeAtTopQualityLongForm': settings.get().streaming.buffer.bufferTimeAtTopQualityLongForm\n                    },\n                    'timeShiftBuffer': {\n                        calcFromSegmentTimeline: settings.get().streaming.timeShiftBuffer.calcFromSegmentTimeline\n                    },\n                    'delay': {\n                        'liveDelay': settings.get().streaming.delay.liveDelay\n                    }\n                }\n            };\n\n            settings.update({\n                'streaming': {\n                    'buffer': {\n                        'bufferTimeDefault': bufferTime,\n                        'bufferTimeAtTopQuality': bufferTime,\n                        'bufferTimeAtTopQualityLongForm': bufferTime\n                    },\n                    'timeShiftBuffer': {\n                        calcFromSegmentTimeline: true\n                    },\n                    'delay': {\n                        'liveDelay': liveDelay\n                    }\n                }\n            });\n        }\n\n        // Delete Content Protection under root manifest node\n        delete manifest.ContentProtection;\n\n        // In case of VOD streams, check if start time is greater than 0\n        // Then determine timestamp offset according to higher audio/video start time\n        // (use case = live stream delinearization)\n        if (manifest.type === 'static') {\n            // In case of start-over stream and manifest reloading (due to track switch)\n            // we consider previous timestampOffset to keep timelines synchronized\n            var prevManifest = manifestModel.getValue();\n            if (prevManifest && prevManifest.timestampOffset) {\n                timestampOffset = prevManifest.timestampOffset;\n            } else {\n                for (i = 0; i < adaptations.length; i++) {\n                    if (adaptations[i].contentType === constants.AUDIO || adaptations[i].contentType === constants.VIDEO) {\n                        segments = adaptations[i].SegmentTemplate.SegmentTimeline.S;\n                        startTime = segments[0].t;\n                        if (timestampOffset === undefined) {\n                            timestampOffset = startTime;\n                        }\n                        timestampOffset = Math.min(timestampOffset, startTime);\n                        // Correct content duration according to minimum adaptation's segment timeline duration\n                        // in order to force <video> element sending 'ended' event\n                        manifest.mediaPresentationDuration = Math.min(manifest.mediaPresentationDuration, adaptations[i].SegmentTemplate.SegmentTimeline.duration);\n                    }\n                }\n            }\n            if (timestampOffset > 0) {\n                // Patch segment templates timestamps and determine period start time (since audio/video should not be aligned to 0)\n                manifest.timestampOffset = timestampOffset;\n                for (i = 0; i < adaptations.length; i++) {\n                    segments = adaptations[i].SegmentTemplate.SegmentTimeline.S;\n                    for (j = 0; j < segments.length; j++) {\n                        if (!segments[j].tManifest) {\n                            segments[j].tManifest = segments[j].t.toString();\n                        }\n                        segments[j].t -= timestampOffset;\n                    }\n                    if (adaptations[i].contentType === constants.AUDIO || adaptations[i].contentType === constants.VIDEO) {\n                        period.start = Math.max(segments[0].t, period.start);\n                        adaptations[i].SegmentTemplate.presentationTimeOffset = period.start;\n                    }\n                }\n                period.start /= manifest.timescale;\n            }\n        }\n\n        // Floor the duration to get around precision differences between segments timestamps and MSE buffer timestamps\n        // and then avoid 'ended' event not being raised\n        manifest.mediaPresentationDuration = Math.floor(manifest.mediaPresentationDuration * 1000) / 1000;\n        period.duration = manifest.mediaPresentationDuration;\n\n        return manifest;\n    }\n\n    function parseDOM(data) {\n        let xmlDoc = null;\n\n        if (window.DOMParser) {\n            const parser = new window.DOMParser();\n\n            xmlDoc = parser.parseFromString(data, 'text/xml');\n            if (xmlDoc.getElementsByTagName('parsererror').length > 0) {\n                throw new Error('parsing the manifest failed');\n            }\n        }\n\n        return xmlDoc;\n    }\n\n    function getIron() {\n        return null;\n    }\n\n    function internalParse(data) {\n        let xmlDoc = null;\n        let manifest = null;\n\n        const startTime = window.performance.now();\n\n        // Parse the MSS XML manifest\n        xmlDoc = parseDOM(data);\n\n        const xmlParseTime = window.performance.now();\n\n        if (xmlDoc === null) {\n            return null;\n        }\n\n        // Convert MSS manifest into DASH manifest\n        manifest = processManifest(xmlDoc, new Date());\n\n        const mss2dashTime = window.performance.now();\n\n        logger.info('Parsing complete: (xmlParsing: ' + (xmlParseTime - startTime).toPrecision(3) + 'ms, mss2dash: ' + (mss2dashTime - xmlParseTime).toPrecision(3) + 'ms, total: ' + ((mss2dashTime - startTime) / 1000).toPrecision(3) + 's)');\n\n        return manifest;\n    }\n\n    function reset() {\n        // Restore initial buffer settings\n        if (initialBufferSettings) {\n            settings.update(initialBufferSettings);\n        }\n    }\n\n    instance = {\n        parse: internalParse,\n        getIron: getIron,\n        reset: reset\n    };\n\n    setup();\n\n    return instance;\n}\n\nMssParser.__dashjs_factory_name = 'MssParser';\nexport default FactoryMaker.getClassFactory(MssParser);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport EventsBase from '../core/events/EventsBase.js';\n\n/**\n * @class\n * @implements EventsBase\n */\nclass MediaPlayerEvents extends EventsBase {\n\n    /**\n     * @description Public facing external events to be used when developing a player that implements dash.js.\n     */\n    constructor() {\n        super();\n        /**\n         * Triggered when playback will not start yet\n         * as the MPD's availabilityStartTime is in the future.\n         * Check delay property in payload to determine time before playback will start.\n         * @event MediaPlayerEvents#AST_IN_FUTURE\n         */\n        this.AST_IN_FUTURE = 'astInFuture';\n\n        /**\n         * Triggered when the BaseURLs have been updated.\n         * @event MediaPlayerEvents#BASE_URLS_UPDATED\n         */\n        this.BASE_URLS_UPDATED = 'baseUrlsUpdated';\n\n        /**\n         * Triggered when the video element's buffer state changes to stalled.\n         * Check mediaType in payload to determine type (Video, Audio, FragmentedText).\n         * @event MediaPlayerEvents#BUFFER_EMPTY\n         */\n        this.BUFFER_EMPTY = 'bufferStalled';\n\n        /**\n         * Triggered when the video element's buffer state changes to loaded.\n         * Check mediaType in payload to determine type (Video, Audio, FragmentedText).\n         * @event MediaPlayerEvents#BUFFER_LOADED\n         */\n        this.BUFFER_LOADED = 'bufferLoaded';\n\n        /**\n         * Triggered when the video element's buffer state changes, either stalled or loaded. Check payload for state.\n         * @event MediaPlayerEvents#BUFFER_LEVEL_STATE_CHANGED\n         */\n        this.BUFFER_LEVEL_STATE_CHANGED = 'bufferStateChanged';\n\n        /**\n         * Triggered when the buffer level of a media type has been updated\n         * @event MediaPlayerEvents#BUFFER_LEVEL_UPDATED\n         */\n        this.BUFFER_LEVEL_UPDATED = 'bufferLevelUpdated';\n\n        /**\n         * Triggered when a font signalled by a DVB Font Download has been added to the document FontFaceSet interface.\n         * @event MediaPlayerEvents#DVB_FONT_DOWNLOAD_ADDED\n         */\n        this.DVB_FONT_DOWNLOAD_ADDED = 'dvbFontDownloadAdded';\n\n        /**\n         * Triggered when a font signalled by a DVB Font Download has successfully downloaded and the FontFace can be used.\n         * @event MediaPlayerEvents#DVB_FONT_DOWNLOAD_COMPLETE\n         */\n        this.DVB_FONT_DOWNLOAD_COMPLETE = 'dvbFontDownloadComplete';\n\n        /**\n         * Triggered when a font signalled by a DVB Font Download could not be successfully downloaded, so the FontFace will not be used.\n         * @event MediaPlayerEvents#DVB_FONT_DOWNLOAD_FAILED\n         */\n        this.DVB_FONT_DOWNLOAD_FAILED = 'dvbFontDownloadFailed';\n\n        /**\n         * Triggered when a dynamic stream changed to static (transition phase between Live and On-Demand).\n         * @event MediaPlayerEvents#DYNAMIC_TO_STATIC\n         */\n        this.DYNAMIC_TO_STATIC = 'dynamicToStatic';\n\n        /**\n         * Triggered when there is an error from the element or MSE source buffer.\n         * @event MediaPlayerEvents#ERROR\n         */\n        this.ERROR = 'error';\n        /**\n         * Triggered when a fragment download has completed.\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_COMPLETED\n         */\n        this.FRAGMENT_LOADING_COMPLETED = 'fragmentLoadingCompleted';\n\n        /**\n         * Triggered when a partial fragment download has completed.\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_PROGRESS\n         */\n        this.FRAGMENT_LOADING_PROGRESS = 'fragmentLoadingProgress';\n        /**\n         * Triggered when a fragment download has started.\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_STARTED\n         */\n        this.FRAGMENT_LOADING_STARTED = 'fragmentLoadingStarted';\n\n        /**\n         * Triggered when a fragment download is abandoned due to detection of slow download base on the ABR abandon rule..\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_ABANDONED\n         */\n        this.FRAGMENT_LOADING_ABANDONED = 'fragmentLoadingAbandoned';\n\n        /**\n         * Triggered when {@link module:Debug} logger methods are called.\n         * @event MediaPlayerEvents#LOG\n         */\n        this.LOG = 'log';\n\n        /**\n         * Triggered when the manifest load is started\n         * @event MediaPlayerEvents#MANIFEST_LOADING_STARTED\n         */\n        this.MANIFEST_LOADING_STARTED = 'manifestLoadingStarted';\n\n        /**\n         * Triggered when the manifest loading is finished, providing the request object information\n         * @event MediaPlayerEvents#MANIFEST_LOADING_FINISHED\n         */\n        this.MANIFEST_LOADING_FINISHED = 'manifestLoadingFinished';\n\n        /**\n         * Triggered when the manifest load is complete, providing the payload\n         * @event MediaPlayerEvents#MANIFEST_LOADED\n         */\n        this.MANIFEST_LOADED = 'manifestLoaded';\n\n        /**\n         * Triggered anytime there is a change to the overall metrics.\n         * @event MediaPlayerEvents#METRICS_CHANGED\n         */\n        this.METRICS_CHANGED = 'metricsChanged';\n\n        /**\n         * Triggered when an individual metric is added, updated or cleared.\n         * @event MediaPlayerEvents#METRIC_CHANGED\n         */\n        this.METRIC_CHANGED = 'metricChanged';\n\n        /**\n         * Triggered every time a new metric is added.\n         * @event MediaPlayerEvents#METRIC_ADDED\n         */\n        this.METRIC_ADDED = 'metricAdded';\n\n        /**\n         * Triggered every time a metric is updated.\n         * @event MediaPlayerEvents#METRIC_UPDATED\n         */\n        this.METRIC_UPDATED = 'metricUpdated';\n\n        /**\n         * Triggered when a new stream (period) starts.\n         * @event MediaPlayerEvents#PERIOD_SWITCH_STARTED\n         */\n        this.PERIOD_SWITCH_STARTED = 'periodSwitchStarted';\n\n        /**\n         * Triggered at the stream end of a period.\n         * @event MediaPlayerEvents#PERIOD_SWITCH_COMPLETED\n         */\n        this.PERIOD_SWITCH_COMPLETED = 'periodSwitchCompleted';\n\n        /**\n         * Triggered when an ABR up /down switch is initiated; either by user in manual mode or auto mode via ABR rules.\n         * @event MediaPlayerEvents#QUALITY_CHANGE_REQUESTED\n         */\n        this.QUALITY_CHANGE_REQUESTED = 'qualityChangeRequested';\n\n        /**\n         * Triggered when the new ABR quality is being rendered on-screen.\n         * @event MediaPlayerEvents#QUALITY_CHANGE_RENDERED\n         */\n        this.QUALITY_CHANGE_RENDERED = 'qualityChangeRendered';\n\n        /**\n         * Triggered when the new track is being selected\n         * @event MediaPlayerEvents#NEW_TRACK_SELECTED\n         */\n        this.NEW_TRACK_SELECTED = 'newTrackSelected';\n\n        /**\n         * Triggered when the new track is being rendered.\n         * @event MediaPlayerEvents#TRACK_CHANGE_RENDERED\n         */\n        this.TRACK_CHANGE_RENDERED = 'trackChangeRendered';\n\n        /**\n         * Triggered when a stream (period) is being loaded\n         * @event MediaPlayerEvents#STREAM_INITIALIZING\n         */\n        this.STREAM_INITIALIZING = 'streamInitializing';\n\n        /**\n         * Triggered when a stream (period) is loaded\n         * @event MediaPlayerEvents#STREAM_UPDATED\n         */\n        this.STREAM_UPDATED = 'streamUpdated';\n\n        /**\n         * Triggered when a stream (period) is activated\n         * @event MediaPlayerEvents#STREAM_ACTIVATED\n         */\n        this.STREAM_ACTIVATED = 'streamActivated';\n\n        /**\n         * Triggered when a stream (period) is deactivated\n         * @event MediaPlayerEvents#STREAM_DEACTIVATED\n         */\n        this.STREAM_DEACTIVATED = 'streamDeactivated';\n\n        /**\n         * Triggered when a stream (period) is activated\n         * @event MediaPlayerEvents#STREAM_INITIALIZED\n         */\n        this.STREAM_INITIALIZED = 'streamInitialized';\n\n        /**\n         * Triggered when the player has been reset.\n         * @event MediaPlayerEvents#STREAM_TEARDOWN_COMPLETE\n         */\n        this.STREAM_TEARDOWN_COMPLETE = 'streamTeardownComplete';\n\n        /**\n         * Triggered once all text tracks detected in the MPD are added to the video element.\n         * @event MediaPlayerEvents#TEXT_TRACKS_ADDED\n         */\n        this.TEXT_TRACKS_ADDED = 'allTextTracksAdded';\n\n        /**\n         * Triggered when a text track is added to the video element's TextTrackList\n         * @event MediaPlayerEvents#TEXT_TRACK_ADDED\n         */\n        this.TEXT_TRACK_ADDED = 'textTrackAdded';\n\n        /**\n         * Triggered when a text track should be shown\n         * @event MediaPlayerEvents#CUE_ENTER\n         */\n        this.CUE_ENTER = 'cueEnter'\n\n        /**\n         * Triggered when a text track should be hidden\n         * @event MediaPlayerEvents#CUE_ENTER\n         */\n        this.CUE_EXIT = 'cueExit'\n\n        /**\n         * Triggered when a throughput measurement based on the last segment request has been stored\n         * @event MediaPlayerEvents#THROUGHPUT_MEASUREMENT_STORED\n         */\n        this.THROUGHPUT_MEASUREMENT_STORED = 'throughputMeasurementStored';\n\n        /**\n         * Triggered when a ttml chunk is parsed.\n         * @event MediaPlayerEvents#TTML_PARSED\n         */\n        this.TTML_PARSED = 'ttmlParsed';\n\n        /**\n         * Triggered when a ttml chunk has to be parsed.\n         * @event MediaPlayerEvents#TTML_TO_PARSE\n         */\n        this.TTML_TO_PARSE = 'ttmlToParse';\n\n        /**\n         * Triggered when a caption is rendered.\n         * @event MediaPlayerEvents#CAPTION_RENDERED\n         */\n        this.CAPTION_RENDERED = 'captionRendered';\n\n        /**\n         * Triggered when the caption container is resized.\n         * @event MediaPlayerEvents#CAPTION_CONTAINER_RESIZE\n         */\n        this.CAPTION_CONTAINER_RESIZE = 'captionContainerResize';\n\n        /**\n         * Sent when enough data is available that the media can be played,\n         * at least for a couple of frames.  This corresponds to the\n         * HAVE_ENOUGH_DATA readyState.\n         * @event MediaPlayerEvents#CAN_PLAY\n         */\n        this.CAN_PLAY = 'canPlay';\n\n        /**\n         * This corresponds to the CAN_PLAY_THROUGH readyState.\n         * @event MediaPlayerEvents#CAN_PLAY_THROUGH\n         */\n        this.CAN_PLAY_THROUGH = 'canPlayThrough';\n\n        /**\n         * Sent when playback completes.\n         * @event MediaPlayerEvents#PLAYBACK_ENDED\n         */\n        this.PLAYBACK_ENDED = 'playbackEnded';\n\n        /**\n         * Sent when an error occurs.  The element's error\n         * attribute contains more information.\n         * @event MediaPlayerEvents#PLAYBACK_ERROR\n         */\n        this.PLAYBACK_ERROR = 'playbackError';\n\n        /**\n         * This event is fired once the playback has been initialized by MediaPlayer.js.\n         * After that event methods such as setTextTrack() can be used.\n         * @event MediaPlayerEvents#PLAYBACK_INITIALIZED\n         */\n        this.PLAYBACK_INITIALIZED = 'playbackInitialized';\n\n        /**\n         * Sent when playback is not allowed (for example if user gesture is needed).\n         * @event MediaPlayerEvents#PLAYBACK_NOT_ALLOWED\n         */\n        this.PLAYBACK_NOT_ALLOWED = 'playbackNotAllowed';\n\n        /**\n         * The media's metadata has finished loading; all attributes now\n         * contain as much useful information as they're going to.\n         * @event MediaPlayerEvents#PLAYBACK_METADATA_LOADED\n         */\n        this.PLAYBACK_METADATA_LOADED = 'playbackMetaDataLoaded';\n\n        /**\n         * The event is fired when the frame at the current playback position of the media has finished loading;\n         * often the first frame\n         * @event MediaPlayerEvents#PLAYBACK_LOADED_DATA\n         */\n        this.PLAYBACK_LOADED_DATA = 'playbackLoadedData';\n\n        /**\n         * Sent when playback is paused.\n         * @event MediaPlayerEvents#PLAYBACK_PAUSED\n         */\n        this.PLAYBACK_PAUSED = 'playbackPaused';\n\n        /**\n         * Sent when the media begins to play (either for the first time, after having been paused,\n         * or after ending and then restarting).\n         *\n         * @event MediaPlayerEvents#PLAYBACK_PLAYING\n         */\n        this.PLAYBACK_PLAYING = 'playbackPlaying';\n\n        /**\n         * Sent periodically to inform interested parties of progress downloading\n         * the media. Information about the current amount of the media that has\n         * been downloaded is available in the media element's buffered attribute.\n         * @event MediaPlayerEvents#PLAYBACK_PROGRESS\n         */\n        this.PLAYBACK_PROGRESS = 'playbackProgress';\n\n        /**\n         * Sent when the playback speed changes.\n         * @event MediaPlayerEvents#PLAYBACK_RATE_CHANGED\n         */\n        this.PLAYBACK_RATE_CHANGED = 'playbackRateChanged';\n\n        /**\n         * Sent when a seek operation completes.\n         * @event MediaPlayerEvents#PLAYBACK_SEEKED\n         */\n        this.PLAYBACK_SEEKED = 'playbackSeeked';\n\n        /**\n         * Sent when a seek operation begins.\n         * @event MediaPlayerEvents#PLAYBACK_SEEKING\n         */\n        this.PLAYBACK_SEEKING = 'playbackSeeking';\n\n        /**\n         * Sent when the video element reports stalled\n         * @event MediaPlayerEvents#PLAYBACK_STALLED\n         */\n        this.PLAYBACK_STALLED = 'playbackStalled';\n\n        /**\n         * Sent when playback of the media starts after having been paused;\n         * that is, when playback is resumed after a prior pause event.\n         *\n         * @event MediaPlayerEvents#PLAYBACK_STARTED\n         */\n        this.PLAYBACK_STARTED = 'playbackStarted';\n\n        /**\n         * The time indicated by the element's currentTime attribute has changed.\n         * @event MediaPlayerEvents#PLAYBACK_TIME_UPDATED\n         */\n        this.PLAYBACK_TIME_UPDATED = 'playbackTimeUpdated';\n\n        /**\n         * Sent when the video element reports that the volume has changed\n         * @event MediaPlayerEvents#PLAYBACK_VOLUME_CHANGED\n         */\n        this.PLAYBACK_VOLUME_CHANGED = 'playbackVolumeChanged';\n\n        /**\n         * Sent when the media playback has stopped because of a temporary lack of data.\n         *\n         * @event MediaPlayerEvents#PLAYBACK_WAITING\n         */\n        this.PLAYBACK_WAITING = 'playbackWaiting';\n\n        /**\n         * Manifest validity changed - As a result of an MPD validity expiration event.\n         * @event MediaPlayerEvents#MANIFEST_VALIDITY_CHANGED\n         */\n        this.MANIFEST_VALIDITY_CHANGED = 'manifestValidityChanged';\n\n        /**\n         * Dash events are triggered at their respective start points on the timeline.\n         * @event MediaPlayerEvents#EVENT_MODE_ON_START\n         */\n        this.EVENT_MODE_ON_START = 'eventModeOnStart';\n\n        /**\n         * Dash events are triggered as soon as they were parsed.\n         * @event MediaPlayerEvents#EVENT_MODE_ON_RECEIVE\n         */\n        this.EVENT_MODE_ON_RECEIVE = 'eventModeOnReceive';\n\n        /**\n         * Event that is dispatched whenever the player encounters a potential conformance validation that might lead to unexpected/not optimal behavior\n         * @event MediaPlayerEvents#CONFORMANCE_VIOLATION\n         */\n        this.CONFORMANCE_VIOLATION = 'conformanceViolation';\n\n        /**\n         * Event that is dispatched whenever the player switches to a different representation\n         * @event MediaPlayerEvents#REPRESENTATION_SWITCH\n         */\n        this.REPRESENTATION_SWITCH = 'representationSwitch';\n\n        /**\n         * Event that is dispatched whenever an adaptation set is removed due to all representations not being supported.\n         * @event MediaPlayerEvents#ADAPTATION_SET_REMOVED_NO_CAPABILITIES\n         */\n        this.ADAPTATION_SET_REMOVED_NO_CAPABILITIES = 'adaptationSetRemovedNoCapabilities';\n\n        /**\n         * Triggered when a content steering request has completed.\n         * @event MediaPlayerEvents#CONTENT_STEERING_REQUEST_COMPLETED\n         */\n        this.CONTENT_STEERING_REQUEST_COMPLETED = 'contentSteeringRequestCompleted';\n\n        /**\n         * Triggered when an inband prft (ProducerReferenceTime) boxes has been received.\n         * @event MediaPlayerEvents#INBAND_PRFT\n         */\n        this.INBAND_PRFT = 'inbandPrft';\n\n        /**\n         * The streaming attribute of the Managed Media Source is true\n         * @type {string}\n         */\n        this.MANAGED_MEDIA_SOURCE_START_STREAMING = 'managedMediaSourceStartStreaming';\n\n        /**\n         * The streaming attribute of the Managed Media Source is false\n         * @type {string}\n         */\n        this.MANAGED_MEDIA_SOURCE_END_STREAMING = 'managedMediaSourceEndStreaming';\n    }\n}\n\nlet mediaPlayerEvents = new MediaPlayerEvents();\nexport default mediaPlayerEvents;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES, LOSS OF USE, DATA, OR\n *  PROFITS, OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * Protection Constants declaration\n * @ignore\n */\nexport default {\n    CLEARKEY_KEYSTEM_STRING: 'org.w3.clearkey',\n    WIDEVINE_KEYSTEM_STRING: 'com.widevine.alpha',\n    PLAYREADY_KEYSTEM_STRING: 'com.microsoft.playready',\n    PLAYREADY_RECOMMENDATION_KEYSTEM_STRING: 'com.microsoft.playready.recommendation',\n    WIDEVINE_UUID: 'edef8ba9-79d6-4ace-a3c8-27dcd51d21ed',\n    PLAYREADY_UUID: '9a04f079-9840-4286-ab92-e65be0885f95',\n    CLEARKEY_UUID: 'e2719d58-a985-b3c9-781a-b030af78d30e',\n    W3C_CLEARKEY_UUID: '1077efec-c0b2-4d02-ace3-3c1e52e2fb4b',\n    INITIALIZATION_DATA_TYPE_CENC: 'cenc',\n    INITIALIZATION_DATA_TYPE_KEYIDS: 'keyids',\n    INITIALIZATION_DATA_TYPE_WEBM: 'webm',\n    ENCRYPTION_SCHEME_CENC: 'cenc',\n    ENCRYPTION_SCHEME_CBCS: 'cbcs',\n    MEDIA_KEY_MESSAGE_TYPES: {\n        LICENSE_REQUEST: 'license-request',\n        LICENSE_RENEWAL: 'license-renewal',\n        LICENSE_RELEASE: 'license-release',\n        INDIVIDUALIZATION_REQUEST: 'individualization-request',\n    },\n    ROBUSTNESS_STRINGS: {\n        WIDEVINE: {\n            SW_SECURE_CRYPTO: 'SW_SECURE_CRYPTO',\n            SW_SECURE_DECODE: 'SW_SECURE_DECODE',\n            HW_SECURE_CRYPTO: 'HW_SECURE_CRYPTO',\n            HW_SECURE_DECODE: 'HW_SECURE_DECODE',\n            HW_SECURE_ALL: 'HW_SECURE_ALL'\n        }\n    },\n    MEDIA_KEY_STATUSES: {\n        USABLE: 'usable',\n        EXPIRED: 'expired',\n        RELEASED: 'released',\n        OUTPUT_RESTRICTED: 'output-restricted',\n        OUTPUT_DOWNSCALED: 'output-downscaled',\n        STATUS_PENDING: 'status-pending',\n        INTERNAL_ERROR: 'internal-error',\n    }\n}\n\n\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass DashJSError {\n    constructor(code, message, data) {\n        this.code = code || null;\n        this.message = message || null;\n        this.data = data || null;\n    }\n}\n\nexport default DashJSError;", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * @class\n * @ignore\n */\nclass DataChunk {\n    //Represents a data structure that keep all the necessary info about a single init/media segment\n    constructor() {\n        this.streamId = null;\n        this.segmentType = null;\n        this.index = NaN;\n        this.bytes = null;\n        this.start = NaN;\n        this.end = NaN;\n        this.duration = NaN;\n        this.representation = null;\n        this.endFragment = null;\n    }\n}\n\nexport default DataChunk;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport {HTTPRequest} from './metrics/HTTPRequest.js';\n\n/**\n * @class\n * @ignore\n */\nclass FragmentRequest {\n    constructor(url) {\n        this.action = FragmentRequest.ACTION_DOWNLOAD;\n        this.availabilityEndTime = null;\n        this.availabilityStartTime = null;\n        this.bandwidth = NaN;\n        this.bytesLoaded = NaN;\n        this.bytesTotal = NaN;\n        this.delayLoadingTime = NaN;\n        this.duration = NaN;\n        this.endDate = null;\n        this.firstByteDate = null;\n        this.index = NaN;\n        this.mediaStartTime = NaN;\n        this.mediaType = null;\n        this.range = null;\n        this.representation = null;\n        this.responseType = 'arraybuffer';\n        this.retryAttempts = 0;\n        this.serviceLocation = null;\n        this.startDate = null;\n        this.startTime = NaN;\n        this.timescale = NaN;\n        this.type = null;\n        this.url = url || null;\n        this.wallStartTime = null;\n    }\n\n    isInitializationRequest() {\n        return (this.type && this.type === HTTPRequest.INIT_SEGMENT_TYPE);\n    }\n\n    setInfo(info) {\n        this.type = info && info.init ? HTTPRequest.INIT_SEGMENT_TYPE : HTTPRequest.MEDIA_SEGMENT_TYPE;\n        this.url = info && info.url ? info.url : null;\n        this.range = info && info.range ? info.range.start + '-' + info.range.end : null;\n        this.mediaType = info && info.mediaType ? info.mediaType : null;\n        this.representation = info && info.representation ? info.representation : null;\n    }\n}\n\nFragmentRequest.ACTION_DOWNLOAD = 'download';\nFragmentRequest.ACTION_COMPLETE = 'complete';\n\nexport default FragmentRequest;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @classdesc This Object holds reference to the HTTPRequest for manifest, fragment and xlink loading.\n * Members which are not defined in ISO23009-1 Annex D should be prefixed by a _ so that they are ignored\n * by Metrics Reporting code.\n * @ignore\n */\nclass HTTPRequest {\n    /**\n     * @class\n     */\n    constructor() {\n        /**\n         * Identifier of the TCP connection on which the HTTP request was sent.\n         * @public\n         */\n        this.tcpid = null;\n        /**\n         * This is an optional parameter and should not be included in HTTP request/response transactions for progressive download.\n         * The type of the request:\n         * - MPD\n         * - XLink expansion\n         * - Initialization Fragment\n         * - Index Fragment\n         * - Media Fragment\n         * - Bitstream Switching Fragment\n         * - other\n         * @public\n         */\n        this.type = null;\n        /**\n         * The original URL (before any redirects or failures)\n         * @public\n         */\n        this.url = null;\n        /**\n         * The actual URL requested, if different from above\n         * @public\n         */\n        this.actualurl = null;\n        /**\n         * The contents of the byte-range-spec part of the HTTP Range header.\n         * @public\n         */\n        this.range = null;\n        /**\n         * Real-Time | The real time at which the request was sent.\n         * @public\n         */\n        this.trequest = null;\n        /**\n         * Real-Time | The real time at which the first byte of the response was received.\n         * @public\n         */\n        this.tresponse = null;\n        /**\n         * The HTTP response code.\n         * @public\n         */\n        this.responsecode = null;\n        /**\n         * The duration of the throughput trace intervals (ms), for successful requests only.\n         * @public\n         */\n        this.interval = null;\n        /**\n         * Throughput traces, for successful requests only.\n         * @public\n         */\n        this.trace = [];\n        /**\n         * The CMSD static and dynamic values retrieved from CMSD response headers.\n         * @public\n         */\n        this.cmsd = null;\n\n        /**\n         * Type of stream (\"audio\" | \"video\" etc..)\n         * @public\n         */\n        this._stream = null;\n        /**\n         * Real-Time | The real time at which the request finished.\n         * @public\n         */\n        this._tfinish = null;\n        /**\n         * The duration of the media requests, if available, in seconds.\n         * @public\n         */\n        this._mediaduration = null;\n        /**\n         * all the response headers from request.\n         * @public\n         */\n        this._responseHeaders = null;\n        /**\n         * The selected service location for the request. string.\n         * @public\n         */\n        this._serviceLocation = null;\n        /**\n         * The type of the loader that was used. Distinguish between fetch loader and xhr loader\n         */\n        this._fileLoaderType = null;\n        /**\n         * The values derived from the ResourceTimingAPI.\n         */\n        this._resourceTimingValues = null;\n    }\n}\n\n/**\n * @classdesc This Object holds reference to the progress of the HTTPRequest.\n * @ignore\n */\nclass HTTPRequestTrace {\n    /**\n     * @class\n     */\n    constructor() {\n        /**\n         * Real-Time | Measurement stream start.\n         * @public\n         */\n        this.s = null;\n        /**\n         * Measurement stream duration (ms).\n         * @public\n         */\n        this.d = null;\n        /**\n         * List of integers counting the bytes received in each trace interval within the measurement stream.\n         * @public\n         */\n        this.b = [];\n    }\n}\n\nHTTPRequest.GET = 'GET';\nHTTPRequest.HEAD = 'HEAD';\nHTTPRequest.MPD_TYPE = 'MPD';\nHTTPRequest.XLINK_EXPANSION_TYPE = 'XLinkExpansion';\nHTTPRequest.INIT_SEGMENT_TYPE = 'InitializationSegment';\nHTTPRequest.INDEX_SEGMENT_TYPE = 'IndexSegment';\nHTTPRequest.MEDIA_SEGMENT_TYPE = 'MediaSegment';\nHTTPRequest.BITSTREAM_SWITCHING_SEGMENT_TYPE = 'BitstreamSwitchingSegment';\nHTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE = 'FragmentInfoSegment';\nHTTPRequest.DVB_REPORTING_TYPE = 'DVBReporting';\nHTTPRequest.LICENSE = 'license';\nHTTPRequest.CONTENT_STEERING_TYPE = 'ContentSteering';\nHTTPRequest.OTHER_TYPE = 'other';\n\nexport {HTTPRequest, HTTPRequestTrace};\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport MssHandler from './MssHandler.js';\n\n// Shove both of these into the global scope\nvar context = (typeof window !== 'undefined' && window) || global;\n\nvar dashjs = context.dashjs;\nif (!dashjs) {\n    dashjs = context.dashjs = {};\n}\n\ndashjs.MssHandler = MssHandler;\n\nexport default dashjs;\nexport { MssHandler };\n"], "names": ["bigInt", "undefined", "BASE", "LOG_BASE", "MAX_INT", "MAX_INT_ARR", "smallToArray", "DEFAULT_ALPHABET", "supportsNativeBigInt", "BigInt", "Integer", "v", "radix", "alphabet", "caseSensitive", "parseValue", "parseBase", "BigInteger", "value", "sign", "isSmall", "prototype", "Object", "create", "SmallInteger", "NativeBigInt", "isPrecise", "n", "Math", "floor", "arrayToSmall", "arr", "trim", "length", "compareAbs", "i", "createArray", "x", "Array", "truncate", "ceil", "add", "a", "b", "l_a", "l_b", "r", "carry", "base", "sum", "push", "addAny", "addSmall", "l", "subtract", "negate", "abs", "plus", "a_l", "b_l", "borrow", "difference", "subtractAny", "subtractSmall", "minus", "small", "multiplyLong", "product", "a_i", "b_j", "j", "multiplySmall", "shiftLeft", "concat", "multiplyK<PERSON><PERSON><PERSON>", "y", "max", "slice", "d", "c", "ac", "bd", "abcd", "useKaratsuba", "l1", "l2", "multiply", "times", "multiplySmallAndArray", "_multiplyBySmall", "square", "a_j", "divMod1", "result", "divisorMostSignificantDigit", "lambda", "remainder", "divisor", "quotientDigit", "shift", "q", "divModSmall", "divMod2", "part", "guess", "xlen", "highx", "highy", "check", "unshift", "reverse", "quotient", "divModAny", "self", "Error", "comparison", "qSign", "mod", "mSign", "divmod", "divide", "over", "pow", "isEven", "toString", "_0", "_1", "_2", "isNegative", "modPow", "exp", "isZero", "isPositive", "isOdd", "compare", "Infinity", "compareTo", "equals", "eq", "notEquals", "neq", "greater", "gt", "lesser", "lt", "greaterOrEquals", "geq", "lesserOrEquals", "leq", "isUnit", "isDivisibleBy", "isBasicPrime", "millerRabinTest", "nPrev", "prev", "t", "next", "isPrime", "strict", "bits", "bitLength", "logN", "log", "toJSNumber", "isProbablePrime", "iterations", "randBetween", "modInv", "zero", "newT", "one", "newR", "lastT", "lastR", "powersOfTwo", "powers2Length", "highestPower2", "shift_isSmall", "String", "shiftRight", "remQuo", "bitwise", "fn", "xSign", "ySign", "xRem", "not", "yRem", "xDigit", "yDigit", "xDivMod", "yDivMod", "and", "or", "xor", "LOBMASK_I", "LOBMASK_BI", "roughLOB", "integerLogarithm", "tmp", "p", "e", "min", "gcd", "lcm", "low", "high", "range", "random", "digits", "toBase", "restricted", "top", "digit", "fromArray", "text", "toLowerCase", "absBase", "alphabetValues", "start", "parseBaseFromArray", "val", "stringify", "apply", "map", "valueOf", "neg", "Number", "out", "left", "toBaseString", "join", "toArray", "str", "zeros", "toJSON", "parseInt", "parseStringValue", "split", "decimalPlace", "indexOf", "<PERSON><PERSON><PERSON><PERSON>", "test", "parseNumberValue", "minusOne", "isInstance", "define", "amd", "FactoryMaker", "instance", "singletonContexts", "singletonFactories", "classFactories", "extend", "name", "childInstance", "override", "context", "getSingletonInstance", "className", "obj", "setSingletonInstance", "deleteSingletonInstances", "filter", "getFactoryByName", "factoriesArray", "updateFactory", "factory", "updateClassFactory", "getClassFactoryByName", "getClassFactory", "classConstructor", "__dashjs_factory_name", "merge", "arguments", "updateSingletonFactory", "getSingletonFactoryByName", "getSingletonFactory", "getInstance", "args", "classInstance", "extensionObject", "extension", "parent", "prop", "hasOwnProperty", "getClassName", "ErrorsBase", "errors", "config", "publicOnly", "err", "EventsBase", "events", "evt", "FragmentRequest", "HTTPRequest", "MssFragmentInfoController", "logger", "fragmentModel", "started", "type", "loadFragmentTimeout", "startTime", "startFragmentTime", "index", "streamProcessor", "baseURLController", "debug", "controllerType", "setup", "<PERSON><PERSON><PERSON><PERSON>", "initialize", "getType", "getFragmentModel", "loadNextFragmentInfo", "stop", "clearTimeout", "reset", "representation", "getCurrentRepresentation", "manifest", "adaptation", "period", "mpd", "Period", "AdaptationSet", "segments", "SegmentTemplate", "SegmentTimeline", "S", "segment", "request", "getRequestForSegment", "requestFragment", "call", "timescale", "mediaType", "MSS_FRAGMENT_INFO_SEGMENT_TYPE", "duration", "bandwidth", "adaptationIndex", "url", "resolve", "path", "media", "replace", "tManifest", "representationController", "getRepresentationController", "isFragmentLoadedOrPending", "executeRequest", "fragmentInfoLoaded", "response", "error", "deltaFragmentTime", "deltaTime", "delay", "Date", "getTime", "setTimeout", "DashJSError", "MssErrors", "Events", "MssFragmentMoofProcessor", "dashMetrics", "playbackController", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eventBus", "ISOBoxer", "processTfrf", "tfrf", "tfdt", "timeShiftBufferDepth", "MSS_NO_TFRF_CODE", "MSS_NO_TFRF_MESSAGE", "entries", "entry", "segmentTime", "endTime", "availabilityStartTime", "parseFloat", "fragment_absolute_time", "end", "baseMediaDecodeTime", "updateDVR", "getStreamInfo", "manifestInfo", "fragment_duration", "lastSegment", "trigger", "MANIFEST_VALIDITY_CHANGED", "sender", "newDuration", "isPaused", "splice", "dvrInfos", "getCurrentDVRInfo", "addDVRInfo", "updateCurrentTime", "getBoxOffset", "offset", "boxes", "size", "convertFragment", "isoFile", "parse<PERSON><PERSON>er", "tfhd", "fetch", "track_ID", "mediaInfo", "traf", "createFullBox", "version", "flags", "trun", "tfxd", "_parent", "sepiff", "usertype", "saio", "entry_count", "saiz", "sample_count", "default_sample_info_size", "sample_info_size", "NumberOfEntries", "moof", "<PERSON><PERSON><PERSON><PERSON>", "data_offset", "trafPosInMoof", "sencPosInTraf", "write", "updateSegmentList", "MssFragmentMoovProcessor", "NALUTYPE_SPS", "NALUTYPE_PPS", "constants", "protectionController", "adaptationSet", "contentProtection", "trackId", "createFtypBox", "ftyp", "createBox", "major_brand", "minor_version", "compatible_brands", "createMoovBox", "moov", "createMvhdBox", "trak", "createTkhdBox", "mdia", "createMdhdBox", "createHdlrBox", "minf", "VIDEO", "createVmhdBox", "AUDIO", "createSmhdBox", "dinf", "createDrefBox", "stbl", "stts", "_data", "stsc", "stco", "stsz", "createStsdBox", "mvex", "createTrexBox", "supportedKS", "getSupportedKeySystemMetadataFromContentProtection", "createProtectionSystemSpecificHeaderBox", "mvhd", "creation_time", "modification_time", "round", "rate", "volume", "reserved1", "reserved2", "matrix", "pre_defined", "next_track_ID", "tkhd", "layer", "alternate_group", "reserved3", "width", "height", "mdhd", "language", "lang", "hdlr", "handler_type", "id", "reserved", "vmhd", "graphicsmode", "opcolor", "smhd", "balance", "dref", "location", "stsd", "createSampleEntry", "codec", "codecs", "substring", "createAVCVisualSampleEntry", "createMP4AudioSampleEntry", "code", "MSS_UNSUPPORTED_CODEC_CODE", "message", "MSS_UNSUPPORTED_CODEC_MESSAGE", "data", "avc1", "data_reference_index", "pre_defined1", "pre_defined2", "horizresolution", "vertresolution", "frame_count", "compressorname", "depth", "pre_defined3", "createAVC1ConfigurationRecord", "sinf", "createOriginalFormatBox", "createSchemeTypeBox", "createSchemeInformationBox", "avcC", "avc<PERSON><PERSON>th", "sps", "pps", "AVCProfileIndication", "AVCLevelIndication", "profile_compatibility", "nalus", "codecPrivateData", "naluBytes", "naluType", "hexStringto<PERSON>uffer", "Uint8Array", "set", "mp4a", "channelcount", "audioChannels", "samplesize", "reserved_3", "samplerate", "audioSamplingRate", "esds", "createMPEG4AACESDescriptor", "audioSpecificConfig", "esdsLength", "frma", "data_format", "stringToCharCode", "schm", "scheme_type", "scheme_version", "schi", "createTrackEncryptionBox", "keySystems", "pssh_bytes", "pssh", "parsed<PERSON><PERSON><PERSON>", "initData", "Utils", "appendBox", "tenc", "default_IsEncrypted", "default_IV_size", "default_KID", "trex", "default_sample_description_index", "default_sample_duration", "default_sample_size", "default_sample_flags", "buf", "charCodeAt", "generateMoov", "rep", "arrayBuffer", "ContentProtection", "createFile", "arrayEqual", "arr1", "arr2", "every", "element", "saioProcessor", "_procFullBox", "_procField", "_procFieldArray", "saizProcessor", "sencProcessor", "_procEntries", "_procEntryField", "_procSubEntries", "clearAndCryptedData", "uuidProcessor", "tfxdUserType", "tfrfUserType", "sepiffUserType", "_parsing", "fragment_count", "MssFragmentProcessor", "mssFragmentMoovProcessor", "mssFragmentMoofProcessor", "addBoxProcessor", "processFragment", "DataChunk", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initSegmentType", "streamController", "mss<PERSON><PERSON><PERSON>", "mssFragmentProcessor", "fragmentInfoControllers", "createMssFragmentProcessor", "getStreamProcessor", "getActiveStreamProcessors", "processor", "getFragmentInfoController", "controller", "createDataChunk", "streamId", "endFragment", "chunk", "segmentType", "startFragmentInfoControllers", "processors", "for<PERSON>ach", "TEXT", "fragmentInfoController", "stopFragmentInfoControllers", "onInitFragmentNeeded", "getMediaInfo", "streamInfo", "FRAGMENT_LOADING_PROGRESS", "bytes", "INIT_FRAGMENT_LOADED", "onSegmentMediaLoaded", "isDynamic", "dvrWindowSize", "onPlaybackPaused", "getIsDynamic", "onPlaybackSeeking", "onTTMLPreProcess", "ttmlSubtitles", "registerEvents", "on", "INIT_FRAGMENT_NEEDED", "priority", "dashjs", "EVENT_PRIORITY_HIGH", "PLAYBACK_PAUSED", "PLAYBACK_SEEKING", "FRAGMENT_LOADING_COMPLETED", "TTML_TO_PARSE", "off", "createMssParser", "constructor", "mssErrors", "ProtectionConstants", "BASE64", "manifestModel", "settings", "DEFAULT_TIME_SCALE", "SUPPORTED_CODECS", "ROLE", "ACCESSIBILITY", "samplingFrequencyIndex", "mimeTypeMap", "initialBufferSettings", "getAttributeAsBoolean", "node", "attrName", "getAttribute", "mapPeriod", "smoothStreamingMedia", "streams", "getElementsByTagName", "mapAdaptationSet", "streamIndex", "representations", "segmentTemplate", "qualityLevels", "fallBackId", "contentType", "mimeType", "subType", "max<PERSON><PERSON><PERSON>", "maxHeight", "Role", "schemeIdUri", "Accessibility", "mapSegmentTemplate", "BaseURL", "Id", "mapRepresentation", "Representation", "qualityLevel", "fourCCValue", "isNaN", "toUpperCase", "warn", "getH264Codec", "getAACCodec", "STPP", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avcoti", "exec", "substr", "samplingRate", "objectType", "codecPrivateDataHex", "arr16", "indexFreq", "extensionSamplingFrequencyIndex", "Channels", "Uint16Array", "setAttribute", "mediaUrl", "streamIndexTimeScale", "mapSegmentTimeline", "availabilityTimeOffset", "segmentTimeline", "chunks", "prevSegment", "MAX_SAFE_INTEGER", "getKIDFromProtectionHeader", "protectionHeader", "pr<PERSON><PERSON><PERSON>", "wrm<PERSON><PERSON>er", "xmlReader", "KID", "decodeArray", "<PERSON><PERSON><PERSON><PERSON>", "getWRMHeaderFromPRHeader", "buffer", "fromCharCode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "querySelector", "textContent", "convertUuidEndianness", "recordCount", "recordType", "recordLength", "recordValue", "subarray", "uuid", "swapBytes", "pos1", "pos2", "temp", "createPRContentProtection", "pro", "__text", "__prefix", "PLAYREADY_UUID", "PLAYREADY_KEYSTEM_STRING", "createWidevineContentProtection", "widevineCP", "WIDEVINE_UUID", "WIDEVINE_KEYSTEM_STRING", "wv<PERSON>en<PERSON><PERSON>eader", "encodeASCII", "processManifest", "xmlDoc", "contentProtections", "protection", "adaptations", "timestampOffset", "segmentDuration", "protocol", "profiles", "dvr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mediaPresentationDuration", "minBufferTime", "ttmlTimeIsRelative", "refreshManifestOnSwitchTrack", "doNotUpdateDVRWindowOnBufferUpdated", "ignorePostponeTimePeriod", "initialization", "targetLiveDelay", "get", "streaming", "liveDelay", "liveDelayFragmentCount", "targetDelayCapping", "bufferTime", "bufferTimeDefault", "bufferTimeAtTopQuality", "bufferTimeAtTopQualityLongForm", "calcFromSegmentTimeline", "timeShiftBuffer", "update", "prevManifest", "getValue", "presentationTimeOffset", "parseDOM", "window", "parser", "getIron", "internalParse", "performance", "now", "xmlParseTime", "mss2dashTime", "info", "toPrecision", "parse", "MediaPlayerEvents", "AST_IN_FUTURE", "BASE_URLS_UPDATED", "BUFFER_EMPTY", "BUFFER_LOADED", "BUFFER_LEVEL_STATE_CHANGED", "BUFFER_LEVEL_UPDATED", "DVB_FONT_DOWNLOAD_ADDED", "DVB_FONT_DOWNLOAD_COMPLETE", "DVB_FONT_DOWNLOAD_FAILED", "DYNAMIC_TO_STATIC", "ERROR", "FRAGMENT_LOADING_STARTED", "FRAGMENT_LOADING_ABANDONED", "LOG", "MANIFEST_LOADING_STARTED", "MANIFEST_LOADING_FINISHED", "MANIFEST_LOADED", "METRICS_CHANGED", "METRIC_CHANGED", "METRIC_ADDED", "METRIC_UPDATED", "PERIOD_SWITCH_STARTED", "PERIOD_SWITCH_COMPLETED", "QUALITY_CHANGE_REQUESTED", "QUALITY_CHANGE_RENDERED", "NEW_TRACK_SELECTED", "TRACK_CHANGE_RENDERED", "STREAM_INITIALIZING", "STREAM_UPDATED", "STREAM_ACTIVATED", "STREAM_DEACTIVATED", "STREAM_INITIALIZED", "STREAM_TEARDOWN_COMPLETE", "TEXT_TRACKS_ADDED", "TEXT_TRACK_ADDED", "CUE_ENTER", "CUE_EXIT", "THROUGHPUT_MEASUREMENT_STORED", "TTML_PARSED", "CAPTION_RENDERED", "CAPTION_CONTAINER_RESIZE", "CAN_PLAY", "CAN_PLAY_THROUGH", "PLAYBACK_ENDED", "PLAYBACK_ERROR", "PLAYBACK_INITIALIZED", "PLAYBACK_NOT_ALLOWED", "PLAYBACK_METADATA_LOADED", "PLAYBACK_LOADED_DATA", "PLAYBACK_PLAYING", "PLAYBACK_PROGRESS", "PLAYBACK_RATE_CHANGED", "PLAYBACK_SEEKED", "PLAYBACK_STALLED", "PLAYBACK_STARTED", "PLAYBACK_TIME_UPDATED", "PLAYBACK_VOLUME_CHANGED", "PLAYBACK_WAITING", "EVENT_MODE_ON_START", "EVENT_MODE_ON_RECEIVE", "CONFORMANCE_VIOLATION", "REPRESENTATION_SWITCH", "ADAPTATION_SET_REMOVED_NO_CAPABILITIES", "CONTENT_STEERING_REQUEST_COMPLETED", "INBAND_PRFT", "MANAGED_MEDIA_SOURCE_START_STREAMING", "MANAGED_MEDIA_SOURCE_END_STREAMING", "mediaPlayerEvents", "CLEARKEY_KEYSTEM_STRING", "PLAYREADY_RECOMMENDATION_KEYSTEM_STRING", "CLEARKEY_UUID", "W3C_CLEARKEY_UUID", "INITIALIZATION_DATA_TYPE_CENC", "INITIALIZATION_DATA_TYPE_KEYIDS", "INITIALIZATION_DATA_TYPE_WEBM", "ENCRYPTION_SCHEME_CENC", "ENCRYPTION_SCHEME_CBCS", "MEDIA_KEY_MESSAGE_TYPES", "LICENSE_REQUEST", "LICENSE_RENEWAL", "LICENSE_RELEASE", "INDIVIDUALIZATION_REQUEST", "ROBUSTNESS_STRINGS", "WIDEVINE", "SW_SECURE_CRYPTO", "SW_SECURE_DECODE", "HW_SECURE_CRYPTO", "HW_SECURE_DECODE", "HW_SECURE_ALL", "MEDIA_KEY_STATUSES", "USABLE", "EXPIRED", "RELEASED", "OUTPUT_RESTRICTED", "OUTPUT_DOWNSCALED", "STATUS_PENDING", "INTERNAL_ERROR", "NaN", "action", "ACTION_DOWNLOAD", "availabilityEndTime", "bytesLoaded", "bytesTotal", "delayLoadingTime", "endDate", "firstByteDate", "mediaStartTime", "responseType", "retryAttempts", "serviceLocation", "startDate", "wallStartTime", "isInitializationRequest", "INIT_SEGMENT_TYPE", "setInfo", "init", "MEDIA_SEGMENT_TYPE", "ACTION_COMPLETE", "tcpid", "<PERSON><PERSON><PERSON>", "trequest", "tresponse", "responsecode", "interval", "trace", "cmsd", "_stream", "_tfinish", "_mediaduration", "_responseHeaders", "_serviceLocation", "_fileLoaderType", "_resourceTimingValues", "HTTPRequestTrace", "s", "GET", "HEAD", "MPD_TYPE", "XLINK_EXPANSION_TYPE", "INDEX_SEGMENT_TYPE", "BITSTREAM_SWITCHING_SEGMENT_TYPE", "DVB_REPORTING_TYPE", "LICENSE", "CONTENT_STEERING_TYPE", "OTHER_TYPE", "global"], "sourceRoot": ""}