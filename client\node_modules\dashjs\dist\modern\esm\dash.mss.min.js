var t={d:function(e,r){for(var n in r)t.o(r,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},o:function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r:function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{MssHandler:function(){return O},default:function(){return x}});var r=class{constructor(){this.streamId=null,this.segmentType=null,this.index=NaN,this.bytes=null,this.start=NaN,this.end=NaN,this.duration=NaN,this.representation=null,this.endFragment=null}};class n{constructor(){this.tcpid=null,this.type=null,this.url=null,this.actualurl=null,this.range=null,this.trequest=null,this.tresponse=null,this.responsecode=null,this.interval=null,this.trace=[],this.cmsd=null,this._stream=null,this._tfinish=null,this._mediaduration=null,this._responseHeaders=null,this._serviceLocation=null,this._fileLoaderType=null,this._resourceTimingValues=null}}n.GET="GET",n.HEAD="HEAD",n.MPD_TYPE="MPD",n.XLINK_EXPANSION_TYPE="XLinkExpansion",n.INIT_SEGMENT_TYPE="InitializationSegment",n.INDEX_SEGMENT_TYPE="IndexSegment",n.MEDIA_SEGMENT_TYPE="MediaSegment",n.BITSTREAM_SWITCHING_SEGMENT_TYPE="BitstreamSwitchingSegment",n.MSS_FRAGMENT_INFO_SEGMENT_TYPE="FragmentInfoSegment",n.DVB_REPORTING_TYPE="DVBReporting",n.LICENSE="license",n.CONTENT_STEERING_TYPE="ContentSteering",n.OTHER_TYPE="other";class i{constructor(t){this.action=i.ACTION_DOWNLOAD,this.availabilityEndTime=null,this.availabilityStartTime=null,this.bandwidth=NaN,this.bytesLoaded=NaN,this.bytesTotal=NaN,this.delayLoadingTime=NaN,this.duration=NaN,this.endDate=null,this.firstByteDate=null,this.index=NaN,this.mediaStartTime=NaN,this.mediaType=null,this.range=null,this.representation=null,this.responseType="arraybuffer",this.retryAttempts=0,this.serviceLocation=null,this.startDate=null,this.startTime=NaN,this.timescale=NaN,this.type=null,this.url=t||null,this.wallStartTime=null}isInitializationRequest(){return this.type&&this.type===n.INIT_SEGMENT_TYPE}setInfo(t){this.type=t&&t.init?n.INIT_SEGMENT_TYPE:n.MEDIA_SEGMENT_TYPE,this.url=t&&t.url?t.url:null,this.range=t&&t.range?t.range.start+"-"+t.range.end:null,this.mediaType=t&&t.mediaType?t.mediaType:null,this.representation=t&&t.representation?t.representation:null}}i.ACTION_DOWNLOAD="download",i.ACTION_COMPLETE="complete";var o=i;const a=function(){let t,e=[];const r={},n={};function i(t,r){for(const n in e){const i=e[n];if(i.context===t&&i.name===r)return i.instance}return null}function o(t,e){return e[t]}function a(t,e,r){t in r&&(r[t]=e)}function s(e,r,n){let i;const o=e.__dashjs_factory_name,a=r[o];if(a){let o=a.instance;if(!a.override)return o.apply({context:r,factory:t},n);i=e.apply({context:r},n),o=o.apply({context:r,factory:t,parent:i},n);for(const t in o)i.hasOwnProperty(t)&&(i[t]=o[t])}else i=e.apply({context:r},n);return i.getClassName=function(){return o},i}return t={extend:function(t,e,r,n){!n[t]&&e&&(n[t]={instance:e,override:r})},getSingletonInstance:i,setSingletonInstance:function(t,r,n){for(const i in e){const o=e[i];if(o.context===t&&o.name===r)return void(e[i].instance=n)}e.push({name:r,context:t,instance:n})},deleteSingletonInstances:function(t){e=e.filter((e=>e.context!==t))},getSingletonFactory:function(t){let n=o(t.__dashjs_factory_name,r);return n||(n=function(r){let n;return void 0===r&&(r={}),{getInstance:function(){return n||(n=i(r,t.__dashjs_factory_name)),n||(n=s(t,r,arguments),e.push({name:t.__dashjs_factory_name,context:r,instance:n})),n}}},r[t.__dashjs_factory_name]=n),n},getSingletonFactoryByName:function(t){return o(t,r)},updateSingletonFactory:function(t,e){a(t,e,r)},getClassFactory:function(t){let e=o(t.__dashjs_factory_name,n);return e||(e=function(e){return void 0===e&&(e={}),{create:function(){return s(t,e,arguments)}}},n[t.__dashjs_factory_name]=e),e},getClassFactoryByName:function(t){return o(t,n)},updateClassFactory:function(t,e){a(t,e,n)}},t}();var s=a;function u(t){let e,r,i,a,s,u,l,p,f;const c=(t=t||{}).streamProcessor,d=t.baseURLController,h=t.debug;function m(){a&&(r.debug("Stop"),clearTimeout(u),a=!1,l=null,p=null)}function g(){if(!a)return;const t=c.getRepresentationController().getCurrentRepresentation(),e=t.adaptation.period.mpd.manifest.Period[t.adaptation.period.index].AdaptationSet[t.adaptation.index],r=e.SegmentTemplate.SegmentTimeline.S,i=function(t,e,r){let i=t.SegmentTemplate.timescale,a=new o;return a.mediaType=s,a.type=n.MSS_FRAGMENT_INFO_SEGMENT_TYPE,a.startTime=r.t/i,a.duration=r.d/i,a.timescale=i,a.bandwidth=e.bandwidth,a.index=f++,a.adaptationIndex=e.adaptation.index,a.representation=e,a.url=d.resolve(e.path).url+t.SegmentTemplate.media,a.url=a.url.replace("$Bandwidth$",e.bandwidth),a.url=a.url.replace("$Time$",r.tManifest?r.tManifest:r.t),a.url=a.url.replace("/Fragments(","/FragmentInfo("),a}(e,t,r[r.length-1]);y.call(this,i)}function y(t){if(c.getFragmentModel().isFragmentLoadedOrPending(t))return r.debug("End of timeline"),void m();i.executeRequest(t)}return e={initialize:function(){s=c.getType(),i=c.getFragmentModel(),a=!1,l=null,p=null},controllerType:"MssFragmentInfoController",start:function(){a||(r.debug("Start"),a=!0,f=0,g())},fragmentInfoLoaded:function(t){if(!a)return;const e=t.request;if(!t.response)return void r.error("Load error",e.url);let n,i,o;null===l&&(l=(new Date).getTime()),p||(p=e.startTime),i=((new Date).getTime()-l)/1e3,n=e.startTime+e.duration-p,o=Math.max(0,n-i),clearTimeout(u),u=setTimeout((function(){u=null,g()}),1e3*o)},getType:function(){return s},reset:function(){m()}},r=h.getLogger(e),e}u.__dashjs_factory_name="MssFragmentInfoController";var l=s.getClassFactory(u),p=class{constructor(t,e,r){this.code=t||null,this.message=e||null,this.data=r||null}},f=class{extend(t,e){if(!t)return;let r=!!e&&e.override,n=!!e&&e.publicOnly;for(const e in t)!t.hasOwnProperty(e)||this[e]&&!r||n&&-1===t[e].indexOf("public_")||(this[e]=t[e])}},c=new class extends f{constructor(){super(),this.MSS_NO_TFRF_CODE=200,this.MSS_UNSUPPORTED_CODEC_CODE=201,this.MSS_NO_TFRF_MESSAGE="Missing tfrf in live media segment",this.MSS_UNSUPPORTED_CODEC_MESSAGE="Unsupported codec"}},d=class{extend(t,e){if(!t)return;let r=!!e&&e.override,n=!!e&&e.publicOnly;for(const e in t)!t.hasOwnProperty(e)||this[e]&&!r||n&&-1===t[e].indexOf("public_")||(this[e]=t[e])}},h=new class extends d{constructor(){super(),this.AST_IN_FUTURE="astInFuture",this.BASE_URLS_UPDATED="baseUrlsUpdated",this.BUFFER_EMPTY="bufferStalled",this.BUFFER_LOADED="bufferLoaded",this.BUFFER_LEVEL_STATE_CHANGED="bufferStateChanged",this.BUFFER_LEVEL_UPDATED="bufferLevelUpdated",this.DVB_FONT_DOWNLOAD_ADDED="dvbFontDownloadAdded",this.DVB_FONT_DOWNLOAD_COMPLETE="dvbFontDownloadComplete",this.DVB_FONT_DOWNLOAD_FAILED="dvbFontDownloadFailed",this.DYNAMIC_TO_STATIC="dynamicToStatic",this.ERROR="error",this.FRAGMENT_LOADING_COMPLETED="fragmentLoadingCompleted",this.FRAGMENT_LOADING_PROGRESS="fragmentLoadingProgress",this.FRAGMENT_LOADING_STARTED="fragmentLoadingStarted",this.FRAGMENT_LOADING_ABANDONED="fragmentLoadingAbandoned",this.LOG="log",this.MANIFEST_LOADING_STARTED="manifestLoadingStarted",this.MANIFEST_LOADING_FINISHED="manifestLoadingFinished",this.MANIFEST_LOADED="manifestLoaded",this.METRICS_CHANGED="metricsChanged",this.METRIC_CHANGED="metricChanged",this.METRIC_ADDED="metricAdded",this.METRIC_UPDATED="metricUpdated",this.PERIOD_SWITCH_STARTED="periodSwitchStarted",this.PERIOD_SWITCH_COMPLETED="periodSwitchCompleted",this.QUALITY_CHANGE_REQUESTED="qualityChangeRequested",this.QUALITY_CHANGE_RENDERED="qualityChangeRendered",this.NEW_TRACK_SELECTED="newTrackSelected",this.TRACK_CHANGE_RENDERED="trackChangeRendered",this.STREAM_INITIALIZING="streamInitializing",this.STREAM_UPDATED="streamUpdated",this.STREAM_ACTIVATED="streamActivated",this.STREAM_DEACTIVATED="streamDeactivated",this.STREAM_INITIALIZED="streamInitialized",this.STREAM_TEARDOWN_COMPLETE="streamTeardownComplete",this.TEXT_TRACKS_ADDED="allTextTracksAdded",this.TEXT_TRACK_ADDED="textTrackAdded",this.CUE_ENTER="cueEnter",this.CUE_EXIT="cueExit",this.THROUGHPUT_MEASUREMENT_STORED="throughputMeasurementStored",this.TTML_PARSED="ttmlParsed",this.TTML_TO_PARSE="ttmlToParse",this.CAPTION_RENDERED="captionRendered",this.CAPTION_CONTAINER_RESIZE="captionContainerResize",this.CAN_PLAY="canPlay",this.CAN_PLAY_THROUGH="canPlayThrough",this.PLAYBACK_ENDED="playbackEnded",this.PLAYBACK_ERROR="playbackError",this.PLAYBACK_INITIALIZED="playbackInitialized",this.PLAYBACK_NOT_ALLOWED="playbackNotAllowed",this.PLAYBACK_METADATA_LOADED="playbackMetaDataLoaded",this.PLAYBACK_LOADED_DATA="playbackLoadedData",this.PLAYBACK_PAUSED="playbackPaused",this.PLAYBACK_PLAYING="playbackPlaying",this.PLAYBACK_PROGRESS="playbackProgress",this.PLAYBACK_RATE_CHANGED="playbackRateChanged",this.PLAYBACK_SEEKED="playbackSeeked",this.PLAYBACK_SEEKING="playbackSeeking",this.PLAYBACK_STALLED="playbackStalled",this.PLAYBACK_STARTED="playbackStarted",this.PLAYBACK_TIME_UPDATED="playbackTimeUpdated",this.PLAYBACK_VOLUME_CHANGED="playbackVolumeChanged",this.PLAYBACK_WAITING="playbackWaiting",this.MANIFEST_VALIDITY_CHANGED="manifestValidityChanged",this.EVENT_MODE_ON_START="eventModeOnStart",this.EVENT_MODE_ON_RECEIVE="eventModeOnReceive",this.CONFORMANCE_VIOLATION="conformanceViolation",this.REPRESENTATION_SWITCH="representationSwitch",this.ADAPTATION_SET_REMOVED_NO_CAPABILITIES="adaptationSetRemovedNoCapabilities",this.CONTENT_STEERING_REQUEST_COMPLETED="contentSteeringRequestCompleted",this.INBAND_PRFT="inbandPrft",this.MANAGED_MEDIA_SOURCE_START_STREAMING="managedMediaSourceStartStreaming",this.MANAGED_MEDIA_SOURCE_END_STREAMING="managedMediaSourceEndStreaming"}};function m(t){let e,r,n;const i=(t=t||{}).dashMetrics,o=t.playbackController,a=t.errHandler,s=t.eventBus,u=t.ISOBoxer,l=t.debug;function f(t,e,i,u){const l=u.getRepresentationController().getCurrentRepresentation(),f=l.adaptation.period.mpd.manifest,m=f.Period[l.adaptation.period.index].AdaptationSet[l.adaptation.index],g=m.SegmentTemplate.timescale;if(r=u.getType(),"dynamic"!==f.type&&!f.timeShiftBufferDepth)return;if(!e)return void a.error(new p(c.MSS_NO_TFRF_CODE,c.MSS_NO_TFRF_MESSAGE));const y=m.SegmentTemplate.SegmentTimeline.S,_=e.entry;let v,T,E,S,A=null,b=0,D=null;if(0===_.length)return;if(v=_[0],"static"===f.type&&(T=y[0].tManifest?parseFloat(y[0].tManifest):y[0].t,v.fragment_absolute_time>T+f.timeShiftBufferDepth*g))return;if(T=y[y.length-1].tManifest?parseFloat(y[y.length-1].tManifest):y[y.length-1].t,v.fragment_absolute_time<=T)return E={start:y[0].t/g,end:i.baseMediaDecodeTime/g+t.duration},void d(t.mediaType,E,u.getStreamInfo().manifestInfo);A={},A.t=v.fragment_absolute_time,A.d=v.fragment_duration,y[0].tManifest&&(A.t-=parseFloat(y[0].tManifest)-y[0].t,A.tManifest=v.fragment_absolute_time);let N=y[y.length-1];if(N.t+N.d!==A.t&&(n.debug("Patch segment duration - t = ",N.t+", d = "+N.d+" => "+(A.t-N.t)),N.d=A.t-N.t),y.push(A),"static"!==f.type){if(f.timeShiftBufferDepth&&f.timeShiftBufferDepth>0)for(A=y[y.length-1],b=A.t,D=(b-f.timeShiftBufferDepth*g)/g,A=y[0],S=(A.t+A.d)/g;S<D&&(o.isPaused()||!(o.getTime()<S));)y.splice(0,1),A=y[0],S=(A.t+A.d)/g;E={start:y[0].t/g,end:i.baseMediaDecodeTime/g+t.duration},d(r,E,u.getStreamInfo().manifestInfo)}else"video"===r&&(A=y[y.length-1],S=(A.t+A.d)/g,S>l.adaptation.period.duration&&s.trigger(h.MANIFEST_VALIDITY_CHANGED,{sender:this,newDuration:S}))}function d(t,e,r){if("video"!==t&&"audio"!==t)return;const a=i.getCurrentDVRInfo(t);(!a||e.end>a.range.end)&&(n.debug("Update DVR range: ["+e.start+" - "+e.end+"]"),i.addDVRInfo(t,o.getTime(),r,e),o.updateCurrentTime(t))}function m(t,e){let r=8,n=0;for(n=0;n<t.boxes.length;n++){if(t.boxes[n].type===e)return r;r+=t.boxes[n].size}return r}return e={convertFragment:function(t,e){let r;const n=u.parseBuffer(t.response),i=n.fetch("tfhd");i.track_ID=t.request.representation.mediaInfo.index+1;let o=n.fetch("tfdt");const a=n.fetch("traf");null===o&&(o=u.createFullBox("tfdt",a,i),o.version=1,o.flags=0,o.baseMediaDecodeTime=Math.floor(t.request.startTime*t.request.timescale));const s=n.fetch("trun");let l=n.fetch("tfxd");l&&(l._parent.boxes.splice(l._parent.boxes.indexOf(l),1),l=null);let p=n.fetch("tfrf");f(t.request,p,o,e),p&&(p._parent.boxes.splice(p._parent.boxes.indexOf(p),1),p=null);const c=n.fetch("sepiff");if(null!==c){c.type="senc",c.usertype=void 0;let t=n.fetch("saio");if(null===t){t=u.createFullBox("saio",a),t.version=0,t.flags=0,t.entry_count=1,t.offset=[0];const e=u.createFullBox("saiz",a);if(e.version=0,e.flags=0,e.sample_count=c.sample_count,e.default_sample_info_size=0,e.sample_info_size=[],2&c.flags)for(r=0;r<c.sample_count;r+=1)e.sample_info_size[r]=10+6*c.entry[r].NumberOfEntries;else e.default_sample_info_size=8}}i.flags&=16777214,i.flags|=131072,s.flags|=1;const d=n.fetch("moof");let h=d.getLength();s.data_offset=h+8;let g=n.fetch("saio");if(null!==g){let t=m(d,"traf"),e=m(a,"senc");g.offset[0]=t+e+16}t.response=n.write()},updateSegmentList:function(t,e){if(!t.response)throw new Error("e.response parameter is missing");const r=u.parseBuffer(t.response),n=r.fetch("tfhd");n.track_ID=t.request.representation.mediaInfo.index+1;let i=r.fetch("tfdt"),o=r.fetch("traf");null===i&&(i=u.createFullBox("tfdt",o,n),i.version=1,i.flags=0,i.baseMediaDecodeTime=Math.floor(t.request.startTime*t.request.timescale));let a=r.fetch("tfrf");f(t.request,a,i,e),a&&(a._parent.boxes.splice(a._parent.boxes.indexOf(a),1),a=null)},getType:function(){return r}},n=l.getLogger(e),r="",e}m.__dashjs_factory_name="MssFragmentMoofProcessor";var g=s.getClassFactory(m);function y(t){const e=(t=t||{}).constants,r=t.ISOBoxer;let n,i,o,a,s,u,l,p=t.protectionController;function f(t,e){r.createBox("frma",t).data_format=function(t){let e,r=0;for(e=0;e<t.length;e+=1)r|=t.charCodeAt(e)<<8*(t.length-e-1);return r}(e)}function d(t){let e=r.createFullBox("schm",t);e.flags=0,e.version=0,e.scheme_type=1667591779,e.scheme_version=65536}function h(t){!function(t){let e=r.createFullBox("tenc",t);e.flags=0,e.version=0,e.default_IsEncrypted=1,e.default_IV_size=8,e.default_KID=s&&s.length>0&&s[0]["cenc:default_KID"]?s[0]["cenc:default_KID"]:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}(r.createBox("schi",t))}function m(t){let e,r=new Uint8Array(t.length/2);for(e=0;e<t.length/2;e+=1)r[e]=parseInt(""+t[2*e]+t[2*e+1],16);return r}return n={generateMoov:function(t){if(!t||!t.adaptation)return;let n,g;return a=t,o=a.adaptation,i=o.period,l=o.index+1,s=i.mpd.manifest.Period[i.index].AdaptationSet[o.index].ContentProtection,u=i.mpd.manifest.Period[i.index].AdaptationSet[o.index].SegmentTemplate.timescale,n=r.createFile(),function(t){let e=r.createBox("ftyp",t);e.major_brand="iso6",e.minor_version=1,e.compatible_brands=[],e.compatible_brands[0]="isom",e.compatible_brands[1]="iso6",e.compatible_brands[2]="msdh"}(n),function(t){let n=r.createBox("moov",t);!function(t){let e=r.createFullBox("mvhd",t);e.version=1,e.creation_time=0,e.modification_time=0,e.timescale=u,e.duration=i.duration===1/0?0x10000000000000000:Math.round(i.duration*u),e.rate=1,e.volume=1,e.reserved1=0,e.reserved2=[0,0],e.matrix=[1,0,0,0,1,0,0,0,16384],e.pre_defined=[0,0,0,0,0,0],e.next_track_ID=l+1}(n);let g=r.createBox("trak",n);!function(t){let e=r.createFullBox("tkhd",t);e.version=1,e.flags=7,e.creation_time=0,e.modification_time=0,e.track_ID=l,e.reserved1=0,e.duration=i.duration===1/0?0x10000000000000000:Math.round(i.duration*u),e.reserved2=[0,0],e.layer=0,e.alternate_group=0,e.volume=1,e.reserved3=0,e.matrix=[1,0,0,0,1,0,0,0,16384],e.width=a.width,e.height=a.height}(g);let y=r.createBox("mdia",g);!function(t){let e=r.createFullBox("mdhd",t);e.version=1,e.creation_time=0,e.modification_time=0,e.timescale=u,e.duration=i.duration===1/0?0x10000000000000000:Math.round(i.duration*u),e.language=o.lang||"und",e.pre_defined=0}(y),function(t){let n=r.createFullBox("hdlr",t);switch(n.pre_defined=0,o.type){case e.VIDEO:n.handler_type="vide";break;case e.AUDIO:n.handler_type="soun";break;default:n.handler_type="meta"}n.name=a.id,n.reserved=[0,0,0]}(y);let _=r.createBox("minf",y);switch(o.type){case e.VIDEO:!function(t){let e=r.createFullBox("vmhd",t);e.flags=1,e.graphicsmode=0,e.opcolor=[0,0,0]}(_);break;case e.AUDIO:!function(t){let e=r.createFullBox("smhd",t);e.flags=1,e.balance=0,e.reserved=0}(_)}!function(t){let e=r.createFullBox("dref",t);e.entry_count=1,e.entries=[];let n=r.createFullBox("url ",e,!1);n.location="",n.flags=1,e.entries.push(n)}(r.createBox("dinf",_));let v=r.createBox("stbl",_);r.createFullBox("stts",v)._data=[0,0,0,0,0,0,0,0],r.createFullBox("stsc",v)._data=[0,0,0,0,0,0,0,0],r.createFullBox("stco",v)._data=[0,0,0,0,0,0,0,0],r.createFullBox("stsz",v)._data=[0,0,0,0,0,0,0,0,0,0,0,0],function(t){let n=r.createFullBox("stsd",t);switch(n.entries=[],o.type){case e.VIDEO:case e.AUDIO:n.entries.push(function(t){let e=a.codecs.substring(0,a.codecs.indexOf("."));switch(e){case"avc1":return function(t,e){let n;if(n=s?r.createBox("encv",t,!1):r.createBox("avc1",t,!1),n.reserved1=[0,0,0,0,0,0],n.data_reference_index=1,n.pre_defined1=0,n.reserved2=0,n.pre_defined2=[0,0,0],n.height=a.height,n.width=a.width,n.horizresolution=72,n.vertresolution=72,n.reserved3=0,n.frame_count=1,n.compressorname=[10,65,86,67,32,67,111,100,105,110,103,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],n.depth=24,n.pre_defined3=65535,n.config=function(){let t,e,r=null,n=15,i=[],o=[],s=0,u=0,l=0,p=a.codecPrivateData.split("00000001").slice(1);for(let r=0;r<p.length;r++)switch(t=m(p[r]),e=31&t[0],e){case 7:i.push(t),n+=t.length+2;break;case 8:o.push(t),n+=t.length+2}i.length>0&&(s=i[0][1],l=i[0][2],u=i[0][3]),r=new Uint8Array(n);let f=0;r[f++]=(4278190080&n)>>24,r[f++]=(16711680&n)>>16,r[f++]=(65280&n)>>8,r[f++]=255&n,r.set([97,118,99,67],f),f+=4,r[f++]=1,r[f++]=s,r[f++]=l,r[f++]=u,r[f++]=255,r[f++]=224|i.length;for(let t=0;t<i.length;t++)r[f++]=(65280&i[t].length)>>8,r[f++]=255&i[t].length,r.set(i[t],f),f+=i[t].length;r[f++]=o.length;for(let t=0;t<o.length;t++)r[f++]=(65280&o[t].length)>>8,r[f++]=255&o[t].length,r.set(o[t],f),f+=o[t].length;return r}(),s){let t=r.createBox("sinf",n);f(t,e),d(t),h(t)}return n}(t,e);case"mp4a":return function(t,e){let n;if(n=s?r.createBox("enca",t,!1):r.createBox("mp4a",t,!1),n.reserved1=[0,0,0,0,0,0],n.data_reference_index=1,n.reserved2=[0,0],n.channelcount=a.audioChannels,n.samplesize=16,n.pre_defined=0,n.reserved_3=0,n.samplerate=a.audioSamplingRate<<16,n.esds=function(){let t=m(a.codecPrivateData),e=34+t.length,r=new Uint8Array(e),n=0;return r[n++]=(4278190080&e)>>24,r[n++]=(16711680&e)>>16,r[n++]=(65280&e)>>8,r[n++]=255&e,r.set([101,115,100,115],n),n+=4,r.set([0,0,0,0],n),n+=4,r[n++]=3,r[n++]=20+t.length,r[n++]=(65280&l)>>8,r[n++]=255&l,r[n++]=0,r[n++]=4,r[n++]=15+t.length,r[n++]=64,r[n]=20,r[n]|=0,r[n++]|=1,r[n++]=255,r[n++]=255,r[n++]=255,r[n++]=(4278190080&a.bandwidth)>>24,r[n++]=(16711680&a.bandwidth)>>16,r[n++]=(65280&a.bandwidth)>>8,r[n++]=255&a.bandwidth,r[n++]=(4278190080&a.bandwidth)>>24,r[n++]=(16711680&a.bandwidth)>>16,r[n++]=(65280&a.bandwidth)>>8,r[n++]=255&a.bandwidth,r[n++]=5,r[n++]=t.length,r.set(t,n),r}(),s){let t=r.createBox("sinf",n);f(t,e),d(t),h(t)}return n}(t,e);default:throw{code:c.MSS_UNSUPPORTED_CODEC_CODE,message:c.MSS_UNSUPPORTED_CODEC_MESSAGE,data:{codec:e}}}}(n))}n.entry_count=n.entries.length}(v),function(t){let e=r.createFullBox("trex",t);e.track_ID=l,e.default_sample_description_index=1,e.default_sample_duration=0,e.default_sample_size=0,e.default_sample_flags=0}(r.createBox("mvex",n)),s&&p&&function(t,e){let n,i,o,a;for(o=0;o<e.length;o+=1)n=e[o].initData,n&&(a=r.parseBuffer(n),i=a.fetch("pssh"),i&&r.Utils.appendBox(t,i))}(n,p.getSupportedKeySystemMetadataFromContentProtection(s))}(n),g=n.write(),g}},n}y.__dashjs_factory_name="MssFragmentMoovProcessor";var _=s.getClassFactory(y);function v(t,e){return t.length===e.length&&t.every((function(t,r){return t===e[r]}))}function T(){this._procFullBox(),1&this.flags&&(this._procField("aux_info_type","uint",32),this._procField("aux_info_type_parameter","uint",32)),this._procField("entry_count","uint",32),this._procFieldArray("offset",this.entry_count,"uint",1===this.version?64:32)}function E(){this._procFullBox(),1&this.flags&&(this._procField("aux_info_type","uint",32),this._procField("aux_info_type_parameter","uint",32)),this._procField("default_sample_info_size","uint",8),this._procField("sample_count","uint",32),0===this.default_sample_info_size&&this._procFieldArray("sample_info_size",this.sample_count,"uint",8)}function S(){this._procFullBox(),this._procField("sample_count","uint",32),1&this.flags&&this._procField("IV_size","uint",8),this._procEntries("entry",this.sample_count,(function(t){this._procEntryField(t,"InitializationVector","data",8),2&this.flags&&(this._procEntryField(t,"NumberOfEntries","uint",16),this._procSubEntries(t,"clearAndCryptedData",t.NumberOfEntries,(function(t){this._procEntryField(t,"BytesOfClearData","uint",16),this._procEntryField(t,"BytesOfEncryptedData","uint",32)})))}))}function A(){v(this.usertype,[109,29,155,5,66,213,68,230,128,226,20,29,175,247,87,178])&&(this._procFullBox(),this._parsing&&(this.type="tfxd"),this._procField("fragment_absolute_time","uint",1===this.version?64:32),this._procField("fragment_duration","uint",1===this.version?64:32)),v(this.usertype,[212,128,126,242,202,57,70,149,142,84,38,203,158,70,167,159])&&(this._procFullBox(),this._parsing&&(this.type="tfrf"),this._procField("fragment_count","uint",8),this._procEntries("entry",this.fragment_count,(function(t){this._procEntryField(t,"fragment_absolute_time","uint",1===this.version?64:32),this._procEntryField(t,"fragment_duration","uint",1===this.version?64:32)}))),v(this.usertype,[162,57,79,82,90,155,79,20,162,68,108,66,124,100,141,244])&&(this._parsing&&(this.type="sepiff"),S.call(this))}function b(t){t=t||{};const e=this.context,r=t.dashMetrics,i=t.playbackController,o=t.eventBus,a=t.protectionController,s=t.ISOBoxer,u=t.debug;let l,p,f;return f={generateMoov:function(t){return l.generateMoov(t)},processFragment:function(t,e){if(!t||!t.request||!t.response)throw new Error("e parameter is missing or malformed");"MediaSegment"===t.request.type?p.convertFragment(t,e):t.request.type===n.MSS_FRAGMENT_INFO_SEGMENT_TYPE&&(p.updateSegmentList(t,e),t.sender=null)}},s.addBoxProcessor("uuid",A),s.addBoxProcessor("saio",T),s.addBoxProcessor("saiz",E),s.addBoxProcessor("senc",S),l=_(e).create({protectionController:a,constants:t.constants,ISOBoxer:s}),p=g(e).create({dashMetrics:r,playbackController:i,ISOBoxer:s,eventBus:o,debug:u,errHandler:t.errHandler}),f}b.__dashjs_factory_name="MssFragmentProcessor";var D=s.getClassFactory(b),N=function(t){var e=1e7,r=7,n=9007199254740992,i=c(n),o="0123456789abcdefghijklmnopqrstuvwxyz",a="function"==typeof BigInt;function s(t,e,r,n){return void 0===t?s[0]:void 0===e||10==+e&&!r?Q(t):j(t,e,r,n)}function u(t,e){this.value=t,this.sign=e,this.isSmall=!1}function l(t){this.value=t,this.sign=t<0,this.isSmall=!0}function p(t){this.value=t}function f(t){return-n<t&&t<n}function c(t){return t<1e7?[t]:t<1e14?[t%1e7,Math.floor(t/1e7)]:[t%1e7,Math.floor(t/1e7)%1e7,Math.floor(t/1e14)]}function d(t){h(t);var r=t.length;if(r<4&&P(t,i)<0)switch(r){case 0:return 0;case 1:return t[0];case 2:return t[0]+t[1]*e;default:return t[0]+(t[1]+t[2]*e)*e}return t}function h(t){for(var e=t.length;0===t[--e];);t.length=e+1}function m(t){for(var e=new Array(t),r=-1;++r<t;)e[r]=0;return e}function g(t){return t>0?Math.floor(t):Math.ceil(t)}function y(t,r){var n,i,o=t.length,a=r.length,s=new Array(o),u=0,l=e;for(i=0;i<a;i++)u=(n=t[i]+r[i]+u)>=l?1:0,s[i]=n-u*l;for(;i<o;)u=(n=t[i]+u)===l?1:0,s[i++]=n-u*l;return u>0&&s.push(u),s}function _(t,e){return t.length>=e.length?y(t,e):y(e,t)}function v(t,r){var n,i,o=t.length,a=new Array(o),s=e;for(i=0;i<o;i++)n=t[i]-s+r,r=Math.floor(n/s),a[i]=n-r*s,r+=1;for(;r>0;)a[i++]=r%s,r=Math.floor(r/s);return a}function T(t,r){var n,i,o=t.length,a=r.length,s=new Array(o),u=0,l=e;for(n=0;n<a;n++)(i=t[n]-u-r[n])<0?(i+=l,u=1):u=0,s[n]=i;for(n=a;n<o;n++){if(!((i=t[n]-u)<0)){s[n++]=i;break}i+=l,s[n]=i}for(;n<o;n++)s[n]=t[n];return h(s),s}function E(t,r,n){var i,o,a=t.length,s=new Array(a),p=-r,f=e;for(i=0;i<a;i++)o=t[i]+p,p=Math.floor(o/f),o%=f,s[i]=o<0?o+f:o;return"number"==typeof(s=d(s))?(n&&(s=-s),new l(s)):new u(s,n)}function S(t,r){var n,i,o,a,s=t.length,u=r.length,l=m(s+u),p=e;for(o=0;o<s;++o){a=t[o];for(var f=0;f<u;++f)n=a*r[f]+l[o+f],i=Math.floor(n/p),l[o+f]=n-i*p,l[o+f+1]+=i}return h(l),l}function A(t,r){var n,i,o=t.length,a=new Array(o),s=e,u=0;for(i=0;i<o;i++)n=t[i]*r+u,u=Math.floor(n/s),a[i]=n-u*s;for(;u>0;)a[i++]=u%s,u=Math.floor(u/s);return a}function b(t,e){for(var r=[];e-- >0;)r.push(0);return r.concat(t)}function D(t,e){var r=Math.max(t.length,e.length);if(r<=30)return S(t,e);r=Math.ceil(r/2);var n=t.slice(r),i=t.slice(0,r),o=e.slice(r),a=e.slice(0,r),s=D(i,a),u=D(n,o),l=D(_(i,n),_(a,o)),p=_(_(s,b(T(T(l,s),u),r)),b(u,2*r));return h(p),p}function I(t,r,n){return new u(t<e?A(r,t):S(r,c(t)),n)}function w(t){var r,n,i,o,a=t.length,s=m(a+a),u=e;for(i=0;i<a;i++){n=0-(o=t[i])*o;for(var l=i;l<a;l++)r=o*t[l]*2+s[i+l]+n,n=Math.floor(r/u),s[i+l]=r-n*u;s[i+a]=n}return h(s),s}function M(t,r){var n,i,o,a,s=t.length,u=m(s),l=e;for(o=0,n=s-1;n>=0;--n)o=(a=o*l+t[n])-(i=g(a/r))*r,u[n]=0|i;return[u,0|o]}function C(t,r){var n,i=Q(r);if(a)return[new p(t.value/i.value),new p(t.value%i.value)];var o,f=t.value,y=i.value;if(0===y)throw new Error("Cannot divide by zero");if(t.isSmall)return i.isSmall?[new l(g(f/y)),new l(f%y)]:[s[0],t];if(i.isSmall){if(1===y)return[t,s[0]];if(-1==y)return[t.negate(),s[0]];var _=Math.abs(y);if(_<e){o=d((n=M(f,_))[0]);var v=n[1];return t.sign&&(v=-v),"number"==typeof o?(t.sign!==i.sign&&(o=-o),[new l(o),new l(v)]):[new u(o,t.sign!==i.sign),new l(v)]}y=c(_)}var E=P(f,y);if(-1===E)return[s[0],t];if(0===E)return[s[t.sign===i.sign?1:-1],s[0]];n=f.length+y.length<=200?function(t,r){var n,i,o,a,s,u,l,p=t.length,f=r.length,c=e,h=m(r.length),g=r[f-1],y=Math.ceil(c/(2*g)),_=A(t,y),v=A(r,y);for(_.length<=p&&_.push(0),v.push(0),g=v[f-1],i=p-f;i>=0;i--){for(n=c-1,_[i+f]!==g&&(n=Math.floor((_[i+f]*c+_[i+f-1])/g)),o=0,a=0,u=v.length,s=0;s<u;s++)o+=n*v[s],l=Math.floor(o/c),a+=_[i+s]-(o-l*c),o=l,a<0?(_[i+s]=a+c,a=-1):(_[i+s]=a,a=0);for(;0!==a;){for(n-=1,o=0,s=0;s<u;s++)(o+=_[i+s]-c+v[s])<0?(_[i+s]=o+c,o=0):(_[i+s]=o,o=1);a+=o}h[i]=n}return _=M(_,y)[0],[d(h),d(_)]}(f,y):function(t,r){for(var n,i,o,a,s,u=t.length,l=r.length,p=[],f=[],c=e;u;)if(f.unshift(t[--u]),h(f),P(f,r)<0)p.push(0);else{o=f[(i=f.length)-1]*c+f[i-2],a=r[l-1]*c+r[l-2],i>l&&(o=(o+1)*c),n=Math.ceil(o/a);do{if(P(s=A(r,n),f)<=0)break;n--}while(n);p.push(n),f=T(f,s)}return p.reverse(),[d(p),d(f)]}(f,y),o=n[0];var S=t.sign!==i.sign,b=n[1],D=t.sign;return"number"==typeof o?(S&&(o=-o),o=new l(o)):o=new u(o,S),"number"==typeof b?(D&&(b=-b),b=new l(b)):b=new u(b,D),[o,b]}function P(t,e){if(t.length!==e.length)return t.length>e.length?1:-1;for(var r=t.length-1;r>=0;r--)if(t[r]!==e[r])return t[r]>e[r]?1:-1;return 0}function O(t){var e=t.abs();return!e.isUnit()&&(!!(e.equals(2)||e.equals(3)||e.equals(5))||!(e.isEven()||e.isDivisibleBy(3)||e.isDivisibleBy(5))&&(!!e.lesser(49)||void 0))}function B(t,e){for(var r,n,i,o=t.prev(),a=o,s=0;a.isEven();)a=a.divide(2),s++;t:for(n=0;n<e.length;n++)if(!t.lesser(e[n])&&!(i=N(e[n]).modPow(a,t)).isUnit()&&!i.equals(o)){for(r=s-1;0!=r;r--){if((i=i.square().mod(t)).isUnit())return!1;if(i.equals(o))continue t}return!1}return!0}u.prototype=Object.create(s.prototype),l.prototype=Object.create(s.prototype),p.prototype=Object.create(s.prototype),u.prototype.add=function(t){var e=Q(t);if(this.sign!==e.sign)return this.subtract(e.negate());var r=this.value,n=e.value;return e.isSmall?new u(v(r,Math.abs(n)),this.sign):new u(_(r,n),this.sign)},u.prototype.plus=u.prototype.add,l.prototype.add=function(t){var e=Q(t),r=this.value;if(r<0!==e.sign)return this.subtract(e.negate());var n=e.value;if(e.isSmall){if(f(r+n))return new l(r+n);n=c(Math.abs(n))}return new u(v(n,Math.abs(r)),r<0)},l.prototype.plus=l.prototype.add,p.prototype.add=function(t){return new p(this.value+Q(t).value)},p.prototype.plus=p.prototype.add,u.prototype.subtract=function(t){var e=Q(t);if(this.sign!==e.sign)return this.add(e.negate());var r=this.value,n=e.value;return e.isSmall?E(r,Math.abs(n),this.sign):function(t,e,r){var n;return P(t,e)>=0?n=T(t,e):(n=T(e,t),r=!r),"number"==typeof(n=d(n))?(r&&(n=-n),new l(n)):new u(n,r)}(r,n,this.sign)},u.prototype.minus=u.prototype.subtract,l.prototype.subtract=function(t){var e=Q(t),r=this.value;if(r<0!==e.sign)return this.add(e.negate());var n=e.value;return e.isSmall?new l(r-n):E(n,Math.abs(r),r>=0)},l.prototype.minus=l.prototype.subtract,p.prototype.subtract=function(t){return new p(this.value-Q(t).value)},p.prototype.minus=p.prototype.subtract,u.prototype.negate=function(){return new u(this.value,!this.sign)},l.prototype.negate=function(){var t=this.sign,e=new l(-this.value);return e.sign=!t,e},p.prototype.negate=function(){return new p(-this.value)},u.prototype.abs=function(){return new u(this.value,!1)},l.prototype.abs=function(){return new l(Math.abs(this.value))},p.prototype.abs=function(){return new p(this.value>=0?this.value:-this.value)},u.prototype.multiply=function(t){var r,n,i,o=Q(t),a=this.value,l=o.value,p=this.sign!==o.sign;if(o.isSmall){if(0===l)return s[0];if(1===l)return this;if(-1===l)return this.negate();if((r=Math.abs(l))<e)return new u(A(a,r),p);l=c(r)}return new u(-.012*(n=a.length)-.012*(i=l.length)+15e-6*n*i>0?D(a,l):S(a,l),p)},u.prototype.times=u.prototype.multiply,l.prototype._multiplyBySmall=function(t){return f(t.value*this.value)?new l(t.value*this.value):I(Math.abs(t.value),c(Math.abs(this.value)),this.sign!==t.sign)},u.prototype._multiplyBySmall=function(t){return 0===t.value?s[0]:1===t.value?this:-1===t.value?this.negate():I(Math.abs(t.value),this.value,this.sign!==t.sign)},l.prototype.multiply=function(t){return Q(t)._multiplyBySmall(this)},l.prototype.times=l.prototype.multiply,p.prototype.multiply=function(t){return new p(this.value*Q(t).value)},p.prototype.times=p.prototype.multiply,u.prototype.square=function(){return new u(w(this.value),!1)},l.prototype.square=function(){var t=this.value*this.value;return f(t)?new l(t):new u(w(c(Math.abs(this.value))),!1)},p.prototype.square=function(t){return new p(this.value*this.value)},u.prototype.divmod=function(t){var e=C(this,t);return{quotient:e[0],remainder:e[1]}},p.prototype.divmod=l.prototype.divmod=u.prototype.divmod,u.prototype.divide=function(t){return C(this,t)[0]},p.prototype.over=p.prototype.divide=function(t){return new p(this.value/Q(t).value)},l.prototype.over=l.prototype.divide=u.prototype.over=u.prototype.divide,u.prototype.mod=function(t){return C(this,t)[1]},p.prototype.mod=p.prototype.remainder=function(t){return new p(this.value%Q(t).value)},l.prototype.remainder=l.prototype.mod=u.prototype.remainder=u.prototype.mod,u.prototype.pow=function(t){var e,r,n,i=Q(t),o=this.value,a=i.value;if(0===a)return s[1];if(0===o)return s[0];if(1===o)return s[1];if(-1===o)return i.isEven()?s[1]:s[-1];if(i.sign)return s[0];if(!i.isSmall)throw new Error("The exponent "+i.toString()+" is too large.");if(this.isSmall&&f(e=Math.pow(o,a)))return new l(g(e));for(r=this,n=s[1];!0&a&&(n=n.times(r),--a),0!==a;)a/=2,r=r.square();return n},l.prototype.pow=u.prototype.pow,p.prototype.pow=function(t){var e=Q(t),r=this.value,n=e.value,i=BigInt(0),o=BigInt(1),a=BigInt(2);if(n===i)return s[1];if(r===i)return s[0];if(r===o)return s[1];if(r===BigInt(-1))return e.isEven()?s[1]:s[-1];if(e.isNegative())return new p(i);for(var u=this,l=s[1];(n&o)===o&&(l=l.times(u),--n),n!==i;)n/=a,u=u.square();return l},u.prototype.modPow=function(t,e){if(t=Q(t),(e=Q(e)).isZero())throw new Error("Cannot take modPow with modulus 0");for(var r=s[1],n=this.mod(e);t.isPositive();){if(n.isZero())return s[0];t.isOdd()&&(r=r.multiply(n).mod(e)),t=t.divide(2),n=n.square().mod(e)}return r},p.prototype.modPow=l.prototype.modPow=u.prototype.modPow,u.prototype.compareAbs=function(t){var e=Q(t),r=this.value,n=e.value;return e.isSmall?1:P(r,n)},l.prototype.compareAbs=function(t){var e=Q(t),r=Math.abs(this.value),n=e.value;return e.isSmall?r===(n=Math.abs(n))?0:r>n?1:-1:-1},p.prototype.compareAbs=function(t){var e=this.value,r=Q(t).value;return(e=e>=0?e:-e)===(r=r>=0?r:-r)?0:e>r?1:-1},u.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var e=Q(t),r=this.value,n=e.value;return this.sign!==e.sign?e.sign?1:-1:e.isSmall?this.sign?-1:1:P(r,n)*(this.sign?-1:1)},u.prototype.compareTo=u.prototype.compare,l.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var e=Q(t),r=this.value,n=e.value;return e.isSmall?r==n?0:r>n?1:-1:r<0!==e.sign?r<0?-1:1:r<0?1:-1},l.prototype.compareTo=l.prototype.compare,p.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var e=this.value,r=Q(t).value;return e===r?0:e>r?1:-1},p.prototype.compareTo=p.prototype.compare,u.prototype.equals=function(t){return 0===this.compare(t)},p.prototype.eq=p.prototype.equals=l.prototype.eq=l.prototype.equals=u.prototype.eq=u.prototype.equals,u.prototype.notEquals=function(t){return 0!==this.compare(t)},p.prototype.neq=p.prototype.notEquals=l.prototype.neq=l.prototype.notEquals=u.prototype.neq=u.prototype.notEquals,u.prototype.greater=function(t){return this.compare(t)>0},p.prototype.gt=p.prototype.greater=l.prototype.gt=l.prototype.greater=u.prototype.gt=u.prototype.greater,u.prototype.lesser=function(t){return this.compare(t)<0},p.prototype.lt=p.prototype.lesser=l.prototype.lt=l.prototype.lesser=u.prototype.lt=u.prototype.lesser,u.prototype.greaterOrEquals=function(t){return this.compare(t)>=0},p.prototype.geq=p.prototype.greaterOrEquals=l.prototype.geq=l.prototype.greaterOrEquals=u.prototype.geq=u.prototype.greaterOrEquals,u.prototype.lesserOrEquals=function(t){return this.compare(t)<=0},p.prototype.leq=p.prototype.lesserOrEquals=l.prototype.leq=l.prototype.lesserOrEquals=u.prototype.leq=u.prototype.lesserOrEquals,u.prototype.isEven=function(){return!(1&this.value[0])},l.prototype.isEven=function(){return!(1&this.value)},p.prototype.isEven=function(){return(this.value&BigInt(1))===BigInt(0)},u.prototype.isOdd=function(){return!(1&~this.value[0])},l.prototype.isOdd=function(){return!(1&~this.value)},p.prototype.isOdd=function(){return(this.value&BigInt(1))===BigInt(1)},u.prototype.isPositive=function(){return!this.sign},l.prototype.isPositive=function(){return this.value>0},p.prototype.isPositive=l.prototype.isPositive,u.prototype.isNegative=function(){return this.sign},l.prototype.isNegative=function(){return this.value<0},p.prototype.isNegative=l.prototype.isNegative,u.prototype.isUnit=function(){return!1},l.prototype.isUnit=function(){return 1===Math.abs(this.value)},p.prototype.isUnit=function(){return this.abs().value===BigInt(1)},u.prototype.isZero=function(){return!1},l.prototype.isZero=function(){return 0===this.value},p.prototype.isZero=function(){return this.value===BigInt(0)},u.prototype.isDivisibleBy=function(t){var e=Q(t);return!e.isZero()&&(!!e.isUnit()||(0===e.compareAbs(2)?this.isEven():this.mod(e).isZero()))},p.prototype.isDivisibleBy=l.prototype.isDivisibleBy=u.prototype.isDivisibleBy,u.prototype.isPrime=function(e){var r=O(this);if(r!==t)return r;var n=this.abs(),i=n.bitLength();if(i<=64)return B(n,[2,3,5,7,11,13,17,19,23,29,31,37]);for(var o=Math.log(2)*i.toJSNumber(),a=Math.ceil(!0===e?2*Math.pow(o,2):o),s=[],u=0;u<a;u++)s.push(N(u+2));return B(n,s)},p.prototype.isPrime=l.prototype.isPrime=u.prototype.isPrime,u.prototype.isProbablePrime=function(e){var r=O(this);if(r!==t)return r;for(var n=this.abs(),i=e===t?5:e,o=[],a=0;a<i;a++)o.push(N.randBetween(2,n.minus(2)));return B(n,o)},p.prototype.isProbablePrime=l.prototype.isProbablePrime=u.prototype.isProbablePrime,u.prototype.modInv=function(t){for(var e,r,n,i=N.zero,o=N.one,a=Q(t),s=this.abs();!s.isZero();)e=a.divide(s),r=i,n=a,i=o,a=s,o=r.subtract(e.multiply(o)),s=n.subtract(e.multiply(s));if(!a.isUnit())throw new Error(this.toString()+" and "+t.toString()+" are not co-prime");return-1===i.compare(0)&&(i=i.add(t)),this.isNegative()?i.negate():i},p.prototype.modInv=l.prototype.modInv=u.prototype.modInv,u.prototype.next=function(){var t=this.value;return this.sign?E(t,1,this.sign):new u(v(t,1),this.sign)},l.prototype.next=function(){var t=this.value;return t+1<n?new l(t+1):new u(i,!1)},p.prototype.next=function(){return new p(this.value+BigInt(1))},u.prototype.prev=function(){var t=this.value;return this.sign?new u(v(t,1),!0):E(t,1,this.sign)},l.prototype.prev=function(){var t=this.value;return t-1>-n?new l(t-1):new u(i,!0)},p.prototype.prev=function(){return new p(this.value-BigInt(1))};for(var F=[1];2*F[F.length-1]<=e;)F.push(2*F[F.length-1]);var x=F.length,R=F[x-1];function L(t){return Math.abs(t)<=e}function U(t,e,r){e=Q(e);for(var n=t.isNegative(),i=e.isNegative(),o=n?t.not():t,a=i?e.not():e,s=0,u=0,l=null,p=null,f=[];!o.isZero()||!a.isZero();)s=(l=C(o,R))[1].toJSNumber(),n&&(s=R-1-s),u=(p=C(a,R))[1].toJSNumber(),i&&(u=R-1-u),o=l[0],a=p[0],f.push(r(s,u));for(var c=0!==r(n?1:0,i?1:0)?N(-1):N(0),d=f.length-1;d>=0;d-=1)c=c.multiply(R).add(N(f[d]));return c}u.prototype.shiftLeft=function(t){var e=Q(t).toJSNumber();if(!L(e))throw new Error(String(e)+" is too large for shifting.");if(e<0)return this.shiftRight(-e);var r=this;if(r.isZero())return r;for(;e>=x;)r=r.multiply(R),e-=x-1;return r.multiply(F[e])},p.prototype.shiftLeft=l.prototype.shiftLeft=u.prototype.shiftLeft,u.prototype.shiftRight=function(t){var e,r=Q(t).toJSNumber();if(!L(r))throw new Error(String(r)+" is too large for shifting.");if(r<0)return this.shiftLeft(-r);for(var n=this;r>=x;){if(n.isZero()||n.isNegative()&&n.isUnit())return n;n=(e=C(n,R))[1].isNegative()?e[0].prev():e[0],r-=x-1}return(e=C(n,F[r]))[1].isNegative()?e[0].prev():e[0]},p.prototype.shiftRight=l.prototype.shiftRight=u.prototype.shiftRight,u.prototype.not=function(){return this.negate().prev()},p.prototype.not=l.prototype.not=u.prototype.not,u.prototype.and=function(t){return U(this,t,(function(t,e){return t&e}))},p.prototype.and=l.prototype.and=u.prototype.and,u.prototype.or=function(t){return U(this,t,(function(t,e){return t|e}))},p.prototype.or=l.prototype.or=u.prototype.or,u.prototype.xor=function(t){return U(this,t,(function(t,e){return t^e}))},p.prototype.xor=l.prototype.xor=u.prototype.xor;var G=1<<30,q=(e&-e)*(e&-e)|G;function k(t){var r=t.value,n="number"==typeof r?r|G:"bigint"==typeof r?r|BigInt(G):r[0]+r[1]*e|q;return n&-n}function Y(t,e){if(e.compareTo(t)<=0){var r=Y(t,e.square(e)),n=r.p,i=r.e,o=n.multiply(e);return o.compareTo(t)<=0?{p:o,e:2*i+1}:{p:n,e:2*i}}return{p:N(1),e:0}}function H(t,e){return t=Q(t),e=Q(e),t.greater(e)?t:e}function V(t,e){return t=Q(t),e=Q(e),t.lesser(e)?t:e}function K(t,e){if(t=Q(t).abs(),e=Q(e).abs(),t.equals(e))return t;if(t.isZero())return e;if(e.isZero())return t;for(var r,n,i=s[1];t.isEven()&&e.isEven();)r=V(k(t),k(e)),t=t.divide(r),e=e.divide(r),i=i.multiply(r);for(;t.isEven();)t=t.divide(k(t));do{for(;e.isEven();)e=e.divide(k(e));t.greater(e)&&(n=e,e=t,t=n),e=e.subtract(t)}while(!e.isZero());return i.isUnit()?t:t.multiply(i)}u.prototype.bitLength=function(){var t=this;return t.compareTo(N(0))<0&&(t=t.negate().subtract(N(1))),0===t.compareTo(N(0))?N(0):N(Y(t,N(2)).e).add(N(1))},p.prototype.bitLength=l.prototype.bitLength=u.prototype.bitLength;var j=function(t,e,r,n){r=r||o,t=String(t),n||(t=t.toLowerCase(),r=r.toLowerCase());var i,a=t.length,s=Math.abs(e),u={};for(i=0;i<r.length;i++)u[r[i]]=i;for(i=0;i<a;i++)if("-"!==(f=t[i])&&f in u&&u[f]>=s){if("1"===f&&1===s)continue;throw new Error(f+" is not a valid digit in base "+e+".")}e=Q(e);var l=[],p="-"===t[0];for(i=p?1:0;i<t.length;i++){var f;if((f=t[i])in u)l.push(Q(u[f]));else{if("<"!==f)throw new Error(f+" is not a valid character");var c=i;do{i++}while(">"!==t[i]&&i<t.length);l.push(Q(t.slice(c+1,i)))}}return z(l,e,p)};function z(t,e,r){var n,i=s[0],o=s[1];for(n=t.length-1;n>=0;n--)i=i.add(t[n].times(o)),o=o.times(e);return r?i.negate():i}function Z(t,e){if((e=N(e)).isZero()){if(t.isZero())return{value:[0],isNegative:!1};throw new Error("Cannot convert nonzero numbers to base 0.")}if(e.equals(-1)){if(t.isZero())return{value:[0],isNegative:!1};if(t.isNegative())return{value:[].concat.apply([],Array.apply(null,Array(-t.toJSNumber())).map(Array.prototype.valueOf,[1,0])),isNegative:!1};var r=Array.apply(null,Array(t.toJSNumber()-1)).map(Array.prototype.valueOf,[0,1]);return r.unshift([1]),{value:[].concat.apply([],r),isNegative:!1}}var n=!1;if(t.isNegative()&&e.isPositive()&&(n=!0,t=t.abs()),e.isUnit())return t.isZero()?{value:[0],isNegative:!1}:{value:Array.apply(null,Array(t.toJSNumber())).map(Number.prototype.valueOf,1),isNegative:n};for(var i,o=[],a=t;a.isNegative()||a.compareAbs(e)>=0;){i=a.divmod(e),a=i.quotient;var s=i.remainder;s.isNegative()&&(s=e.minus(s).abs(),a=a.next()),o.push(s.toJSNumber())}return o.push(a.toJSNumber()),{value:o.reverse(),isNegative:n}}function W(t,e,r){var n=Z(t,e);return(n.isNegative?"-":"")+n.value.map((function(t){return function(t,e){return t<(e=e||o).length?e[t]:"<"+t+">"}(t,r)})).join("")}function J(t){if(f(+t)){var e=+t;if(e===g(e))return a?new p(BigInt(e)):new l(e);throw new Error("Invalid integer: "+t)}var n="-"===t[0];n&&(t=t.slice(1));var i=t.split(/e/i);if(i.length>2)throw new Error("Invalid integer: "+i.join("e"));if(2===i.length){var o=i[1];if("+"===o[0]&&(o=o.slice(1)),(o=+o)!==g(o)||!f(o))throw new Error("Invalid integer: "+o+" is not a valid exponent.");var s=i[0],c=s.indexOf(".");if(c>=0&&(o-=s.length-c-1,s=s.slice(0,c)+s.slice(c+1)),o<0)throw new Error("Cannot include negative exponent part for integers");t=s+=new Array(o+1).join("0")}if(!/^([0-9][0-9]*)$/.test(t))throw new Error("Invalid integer: "+t);if(a)return new p(BigInt(n?"-"+t:t));for(var d=[],m=t.length,y=r,_=m-y;m>0;)d.push(+t.slice(_,m)),(_-=y)<0&&(_=0),m-=y;return h(d),new u(d,n)}function Q(t){return"number"==typeof t?function(t){if(a)return new p(BigInt(t));if(f(t)){if(t!==g(t))throw new Error(t+" is not an integer.");return new l(t)}return J(t.toString())}(t):"string"==typeof t?J(t):"bigint"==typeof t?new p(t):t}u.prototype.toArray=function(t){return Z(this,t)},l.prototype.toArray=function(t){return Z(this,t)},p.prototype.toArray=function(t){return Z(this,t)},u.prototype.toString=function(e,r){if(e===t&&(e=10),10!==e)return W(this,e,r);for(var n,i=this.value,o=i.length,a=String(i[--o]);--o>=0;)n=String(i[o]),a+="0000000".slice(n.length)+n;return(this.sign?"-":"")+a},l.prototype.toString=function(e,r){return e===t&&(e=10),10!=e?W(this,e,r):String(this.value)},p.prototype.toString=l.prototype.toString,p.prototype.toJSON=u.prototype.toJSON=l.prototype.toJSON=function(){return this.toString()},u.prototype.valueOf=function(){return parseInt(this.toString(),10)},u.prototype.toJSNumber=u.prototype.valueOf,l.prototype.valueOf=function(){return this.value},l.prototype.toJSNumber=l.prototype.valueOf,p.prototype.valueOf=p.prototype.toJSNumber=function(){return parseInt(this.toString(),10)};for(var X=0;X<1e3;X++)s[X]=Q(X),X>0&&(s[-X]=Q(-X));return s.one=s[1],s.zero=s[0],s.minusOne=s[-1],s.max=H,s.min=V,s.gcd=K,s.lcm=function(t,e){return t=Q(t).abs(),e=Q(e).abs(),t.divide(K(t,e)).multiply(e)},s.isInstance=function(t){return t instanceof u||t instanceof l||t instanceof p},s.randBetween=function(t,r){var n=V(t=Q(t),r=Q(r)),i=H(t,r).subtract(n).add(1);if(i.isSmall)return n.add(Math.floor(Math.random()*i));for(var o=Z(i,e).value,a=[],u=!0,l=0;l<o.length;l++){var p=u?o[l]:e,f=g(Math.random()*p);a.push(f),f<p&&(u=!1)}return n.add(s.fromArray(a,e,!1))},s.fromArray=function(t,e,r){return z(t.map(Q),Q(e||10),r)},s}();"function"==typeof define&&define.amd&&define("big-integer",[],(function(){return N}));var I=N;function w(t){const e=(t=t||{}).BASE64,r=t.debug,n=t.constants,i=t.manifestModel,o=t.settings,a=["AAC","AACL","AACH","AACP","AVC1","H264","TTML","DFXP"],s={CAPT:"main",SUBT:"alternate",DESC:"main"},u={DESC:"2"},l={96e3:0,88200:1,64e3:2,48e3:3,44100:4,32e3:5,24e3:6,22050:7,16e3:8,12e3:9,11025:10,8e3:11,7350:12},p={video:"video/mp4",audio:"audio/mp4",text:"application/mp4"};let f,c,d;function h(t,e){const r=t.getAttribute(e);return!!r&&"true"===r.toLowerCase()}function m(t,e){const r={},n=[];let i,o,a,l,f;const c=t.getAttribute("Name"),d=t.getAttribute("Type"),h=t.getAttribute("Language"),m=h?d+"_"+h:d;for(r.id=c||m,r.contentType=d,r.lang=h||"und",r.mimeType=p[d],r.subType=t.getAttribute("Subtype"),r.maxWidth=t.getAttribute("MaxWidth"),r.maxHeight=t.getAttribute("MaxHeight"),r.subType&&(s[r.subType]&&(r.Role=[{schemeIdUri:"urn:mpeg:dash:role:2011",value:s[r.subType]}]),u[r.subType]&&(r.Accessibility=[{schemeIdUri:"urn:tva:metadata:cs:AudioPurposeCS:2007",value:u[r.subType]}])),i=function(t,e){const r={};let n,i,o;return o=t.getAttribute("Url"),n=o?o.replace("{bitrate}","$Bandwidth$"):null,n=n?n.replace("{start time}","$Time$"):null,i=t.getAttribute("TimeScale"),i=i?parseFloat(i):e,r.media=n,r.timescale=i,r.SegmentTimeline=function(t,e){const r={},n=t.getElementsByTagName("c"),i=[];let o,a,s,u,l,p,f=0;for(u=0;u<n.length;u++)if(o={},s=n[u].getAttribute("t"),s&&I(s).greater(I(Number.MAX_SAFE_INTEGER))&&(o.tManifest=s),o.t=parseFloat(s),o.d=parseFloat(n[u].getAttribute("d")),0!==u||o.t||(o.t=0),u>0&&(a=i[i.length-1],a.d||(a.tManifest?a.d=I(s).subtract(I(a.tManifest)).toJSNumber():a.d=o.t-a.t,f+=a.d),o.t||(a.tManifest?(o.tManifest=I(a.tManifest).add(I(a.d)).toString(),o.t=parseFloat(o.tManifest)):o.t=a.t+a.d)),o.d&&(f+=o.d),i.push(o),p=parseFloat(n[u].getAttribute("r")),p)for(l=0;l<p-1;l++)a=i[i.length-1],o={},o.t=a.t+a.d,o.d=a.d,a.tManifest&&(o.tManifest=I(a.tManifest).add(I(a.d)).toString()),f+=o.d,i.push(o);return r.S=i,r.duration=f/e,r}(t,r.timescale),r.availabilityTimeOffset="INF",r}(t,e),o=t.getElementsByTagName("QualityLevel"),l=0;l<o.length;l++)o[l].BaseURL=r.BaseURL,o[l].mimeType=r.mimeType,f=o[l].getAttribute("Index"),o[l].Id=r.id+(null!==f?"_"+f:""),a=g(o[l],t),null!==a&&(a.SegmentTemplate=i,n.push(a));return 0===n.length?null:(r.Representation=n,r.SegmentTemplate=i,r)}function g(t,e){const r={},i=e.getAttribute("Type");let o=null,s=null,u=null;if(r.id=t.Id,r.bandwidth=parseInt(t.getAttribute("Bitrate"),10),r.mimeType=t.mimeType,s=parseInt(t.getAttribute("MaxWidth"),10),u=parseInt(t.getAttribute("MaxHeight"),10),isNaN(s)||(r.width=s),isNaN(u)||(r.height=u),o=t.getAttribute("FourCC"),null!==o&&""!==o||(o=e.getAttribute("FourCC")),null===o||""===o)if(i===n.AUDIO)o="AAC";else if(i===n.VIDEO)return c.debug('FourCC is not defined whereas it is required for a QualityLevel element for a StreamIndex of type "video"'),null;return-1===a.indexOf(o.toUpperCase())?(c.warn("Codec not supported: "+o),null):("H264"===o||"AVC1"===o?r.codecs=function(t){let e,r,n=t.getAttribute("CodecPrivateData").toString();return e=/00000001[0-9]7/.exec(n),r=e&&e[0]?n.substr(n.indexOf(e[0])+10,6):void 0,"avc1."+r}(t):o.indexOf("AAC")>=0?(r.codecs=function(t,e){const r=parseInt(t.getAttribute("SamplingRate"),10);let n,i,o,a,s=t.getAttribute("CodecPrivateData").toString(),u=0;return"AACH"===e&&(u=5),void 0===s||""===s?(u=2,o=l[r],"AACH"===e?(u=5,s=new Uint8Array(4),a=l[2*r],s[0]=u<<3|o>>1,s[1]=o<<7|t.Channels<<3|a>>1,s[2]=a<<7|8,s[3]=0,i=new Uint16Array(2),i[0]=(s[0]<<8)+s[1],i[1]=(s[2]<<8)+s[3],n=i[0].toString(16),n=i[0].toString(16)+i[1].toString(16)):(s=new Uint8Array(2),s[0]=u<<3|o>>1,s[1]=o<<7|parseInt(t.getAttribute("Channels"),10)<<3,i=new Uint16Array(1),i[0]=(s[0]<<8)+s[1],n=i[0].toString(16)),s=""+n,s=s.toUpperCase(),t.setAttribute("CodecPrivateData",s)):0===u&&(u=(248&parseInt(s.substr(0,2),16))>>3),"mp4a.40."+u}(t,o),r.audioSamplingRate=parseInt(t.getAttribute("SamplingRate"),10),r.audioChannels=parseInt(t.getAttribute("Channels"),10)):(o.indexOf("TTML")||o.indexOf("DFXP"))&&(r.codecs=n.STPP),r.codecPrivateData=""+t.getAttribute("CodecPrivateData"),r.BaseURL=t.BaseURL,r)}function y(t,e,r){const n=t[e];t[e]=t[r],t[r]=n}function _(t){const r={},a=[],s=t.getElementsByTagName("SmoothStreamingMedia")[0],u=t.getElementsByTagName("Protection")[0];let l,p,f,c,g,_,v,T,E,S,A,b=null;r.protocol="MSS",r.profiles="urn:mpeg:dash:profile:isoff-live:2011",r.type=h(s,"IsLive")?"dynamic":"static",T=s.getAttribute("TimeScale"),r.timescale=T?parseFloat(T):1e7;let D=parseFloat(s.getAttribute("DVRWindowLength"));"dynamic"!==r.type||0!==D&&!isNaN(D)||(D=1/0),0===D&&h(s,"CanSeek")&&(D=1/0),D>0&&(r.timeShiftBufferDepth=D/r.timescale);let N=parseFloat(s.getAttribute("Duration"));for(r.mediaPresentationDuration=0===N?1/0:N/r.timescale,r.minBufferTime=2,r.ttmlTimeIsRelative=!0,"dynamic"===r.type&&N>0&&(r.type="static",r.timeShiftBufferDepth=N/r.timescale),"dynamic"===r.type&&(r.refreshManifestOnSwitchTrack=!0,r.doNotUpdateDVRWindowOnBufferUpdated=!0,r.ignorePostponeTimePeriod=!0,r.availabilityStartTime=new Date(null)),l=function(t,e){const r={};let n,i;r.AdaptationSet=[],n=t.getElementsByTagName("StreamIndex");for(let t=0;t<n.length;t++)i=m(n[t],e),null!==i&&r.AdaptationSet.push(i);return r}(s,r.timescale),r.Period=[l],l.start=0,void 0!==u&&(b=t.getElementsByTagName("ProtectionHeader")[0],b.firstChild.data=b.firstChild.data.replace(/\n|\r/g,""),c=function(t){let r,n,i,o;var a;return r=e.decodeArray(t.firstChild.data),n=function(t){let e,r,n,i,o,a=0;for(e=(t[a+3]<<24)+(t[a+2]<<16)+(t[a+1]<<8)+t[a],a+=4,r=(t[a+1]<<8)+t[a],a+=2;a<t.length;)if(n=(t[a+1]<<8)+t[a],a+=2,1===n)return i=(t[a+1]<<8)+t[a],a+=2,o=new Uint8Array(i),o.set(t.subarray(a,a+i)),o;return null}(r),n&&(n=new Uint16Array(n.buffer),n=String.fromCharCode.apply(null,n),i=(new DOMParser).parseFromString(n,"application/xml"),o=i.querySelector("KID").textContent,o=e.decodeArray(o),y(a=o,0,3),y(a,1,2),y(a,4,5),y(a,6,7)),o}(b),f=function(t){return{schemeIdUri:"urn:uuid:9a04f079-9840-4286-ab92-e65be0885f95",value:"com.microsoft.playready",pro:{__text:t.firstChild.data,__prefix:"mspr"}}}(b),f["cenc:default_KID"]=c,a.push(f),f=function(t){let r={schemeIdUri:"urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed",value:"com.widevine.alpha"};if(!t)return r;const n=new Uint8Array(2+t.length);n[0]=18,n[1]=16,n.set(t,2);const i=32+n.length;let o=new Uint8Array(i),a=0;return o[a++]=(4278190080&i)>>24,o[a++]=(16711680&i)>>16,o[a++]=(65280&i)>>8,o[a++]=255&i,o.set([112,115,115,104,0,0,0,0],a),a+=8,o.set([237,239,139,169,121,214,74,206,163,200,39,220,213,29,33,237],a),a+=16,o[a++]=(4278190080&n.length)>>24,o[a++]=(16711680&n.length)>>16,o[a++]=(65280&n.length)>>8,o[a++]=255&n.length,o.set(n,a),o=String.fromCharCode.apply(null,o),o=e.encodeASCII(o),r.pssh={__text:o},r}(c),f["cenc:default_KID"]=c,a.push(f),r.ContentProtection=a),p=l.AdaptationSet,S=0;S<p.length;S+=1)p[S].SegmentTemplate.initialization="$Bandwidth$",void 0!==r.ContentProtection&&(p[S].ContentProtection=r.ContentProtection,p[S].ContentProtection=r.ContentProtection),"video"===p[S].contentType&&(E=p[S].SegmentTemplate.SegmentTimeline.S[0].d/p[S].SegmentTemplate.timescale,r.minBufferTime=E,"dynamic"===r.type&&r.timeShiftBufferDepth>0&&r.timeShiftBufferDepth!==1/0&&r.timeShiftBufferDepth>p[S].SegmentTemplate.SegmentTimeline.duration&&(r.timeShiftBufferDepth=p[S].SegmentTemplate.SegmentTimeline.duration));if(r.minBufferTime=Math.min(r.minBufferTime,r.timeShiftBufferDepth?r.timeShiftBufferDepth:1/0),"dynamic"===r.type){let t=o.get().streaming.delay.liveDelay;t||(t=E*(null===o.get().streaming.delay.liveDelayFragmentCount||isNaN(o.get().streaming.delay.liveDelayFragmentCount)?4:o.get().streaming.delay.liveDelayFragmentCount));let e=Math.max(r.timeShiftBufferDepth-10,r.timeShiftBufferDepth/2),n=Math.min(e,t),i=n-1.5*E;d={streaming:{buffer:{bufferTimeDefault:o.get().streaming.buffer.bufferTimeDefault,bufferTimeAtTopQuality:o.get().streaming.buffer.bufferTimeAtTopQuality,bufferTimeAtTopQualityLongForm:o.get().streaming.buffer.bufferTimeAtTopQualityLongForm},timeShiftBuffer:{calcFromSegmentTimeline:o.get().streaming.timeShiftBuffer.calcFromSegmentTimeline},delay:{liveDelay:o.get().streaming.delay.liveDelay}}},o.update({streaming:{buffer:{bufferTimeDefault:i,bufferTimeAtTopQuality:i,bufferTimeAtTopQualityLongForm:i},timeShiftBuffer:{calcFromSegmentTimeline:!0},delay:{liveDelay:n}}})}if(delete r.ContentProtection,"static"===r.type){var I=i.getValue();if(I&&I.timestampOffset)g=I.timestampOffset;else for(S=0;S<p.length;S++)p[S].contentType!==n.AUDIO&&p[S].contentType!==n.VIDEO||(v=p[S].SegmentTemplate.SegmentTimeline.S,_=v[0].t,void 0===g&&(g=_),g=Math.min(g,_),r.mediaPresentationDuration=Math.min(r.mediaPresentationDuration,p[S].SegmentTemplate.SegmentTimeline.duration));if(g>0){for(r.timestampOffset=g,S=0;S<p.length;S++){for(v=p[S].SegmentTemplate.SegmentTimeline.S,A=0;A<v.length;A++)v[A].tManifest||(v[A].tManifest=v[A].t.toString()),v[A].t-=g;p[S].contentType!==n.AUDIO&&p[S].contentType!==n.VIDEO||(l.start=Math.max(v[0].t,l.start),p[S].SegmentTemplate.presentationTimeOffset=l.start)}l.start/=r.timescale}}return r.mediaPresentationDuration=Math.floor(1e3*r.mediaPresentationDuration)/1e3,l.duration=r.mediaPresentationDuration,r}return f={parse:function(t){let e=null,r=null;const n=window.performance.now();e=function(t){let e=null;if(window.DOMParser&&(e=(new window.DOMParser).parseFromString(t,"text/xml"),e.getElementsByTagName("parsererror").length>0))throw new Error("parsing the manifest failed");return e}(t);const i=window.performance.now();if(null===e)return null;r=_(e,new Date);const o=window.performance.now();return c.info("Parsing complete: (xmlParsing: "+(i-n).toPrecision(3)+"ms, mss2dash: "+(o-i).toPrecision(3)+"ms, total: "+((o-n)/1e3).toPrecision(3)+"s)"),r},getIron:function(){return null},reset:function(){d&&o.update(d)}},c=r.getLogger(f),f}w.__dashjs_factory_name="MssParser";var M=s.getClassFactory(w);function C(t){t=t||{};const e=this.context,i=t.eventBus,a=t.events,s=t.constants,u=t.initSegmentType,f=t.playbackController,c=t.streamController;let d,h,m,g;function y(t){return c.getActiveStreamProcessors().filter((e=>e.getType()===t))[0]}function _(t){return m.filter((e=>e.getType()===t))[0]}function v(){c.getActiveStreamProcessors().forEach((function(r){if(r.getType()===s.VIDEO||r.getType()===s.AUDIO||r.getType()===s.TEXT){let n=_(r.getType());n||(n=l(e).create({streamProcessor:r,baseURLController:t.baseURLController,debug:t.debug}),n.initialize(),m.push(n)),n.start()}}))}function T(e){let n=y(e.mediaType);if(!n)return;let s=n.getRepresentationController().getCurrentRepresentation(),l=n.getMediaInfo(),f=new o;f.mediaType=s.adaptation.type,f.type=u,f.range=s.range,f.bandwidth=s.bandwidth,f.representation=s;const c=function(t,e,n){const i=new r;return i.streamId=e,i.segmentType=t.type,i.start=t.startTime,i.duration=t.duration,i.end=i.start+i.duration,i.index=t.index,i.bandwidth=t.bandwidth,i.representation=t.representation,i.endFragment=n,i}(f,l.streamInfo.id,e.type!==a.FRAGMENT_LOADING_PROGRESS);try{c.bytes=h.generateMoov(s),i.trigger(a.INIT_FRAGMENT_LOADED,{chunk:c},{streamId:l.streamInfo.id,mediaType:s.adaptation.type})}catch(e){t.errHandler.error(new p(e.code,e.message,e.data))}e.sender=null}function E(t){if(t.error)return;let e=y(t.request.mediaType);if(!e)return;if(h.processFragment(t,e),t.request.type===n.MSS_FRAGMENT_INFO_SEGMENT_TYPE){let e=_(t.request.mediaType);e&&e.fragmentInfoLoaded(t)}let r=t.request.representation.mediaInfo.streamInfo.manifestInfo;r.isDynamic||r.dvrWindowSize===1/0||v()}function S(){f.getIsDynamic()&&0!==f.getTime()&&v()}function A(){f.getIsDynamic()&&0!==f.getTime()&&v()}function b(t){t&&t.data&&(t.data=t.data.replace(/http:\/\/www.w3.org\/2006\/10\/ttaf1/gi,"http://www.w3.org/ns/ttml"))}return g={reset:function(){d&&(d.reset(),d=void 0),i.off(a.INIT_FRAGMENT_NEEDED,T,this),i.off(a.PLAYBACK_PAUSED,S,this),i.off(a.PLAYBACK_SEEKING,A,this),i.off(a.FRAGMENT_LOADING_COMPLETED,E,this),i.off(a.TTML_TO_PARSE,b,this),m.forEach((t=>{t.reset()})),m=[]},createMssParser:function(){return d=M(e).create(t),d},createMssFragmentProcessor:function(){h=D(e).create(t)},registerEvents:function(){i.on(a.INIT_FRAGMENT_NEEDED,T,g,{priority:dashjs.FactoryMaker.getSingletonFactoryByName(i.getClassName()).EVENT_PRIORITY_HIGH}),i.on(a.PLAYBACK_PAUSED,S,g,{priority:dashjs.FactoryMaker.getSingletonFactoryByName(i.getClassName()).EVENT_PRIORITY_HIGH}),i.on(a.PLAYBACK_SEEKING,A,g,{priority:dashjs.FactoryMaker.getSingletonFactoryByName(i.getClassName()).EVENT_PRIORITY_HIGH}),i.on(a.FRAGMENT_LOADING_COMPLETED,E,g,{priority:dashjs.FactoryMaker.getSingletonFactoryByName(i.getClassName()).EVENT_PRIORITY_HIGH}),i.on(a.TTML_TO_PARSE,b,g)}},m=[],g}C.__dashjs_factory_name="MssHandler";const P=dashjs.FactoryMaker.getClassFactory(C);P.errors=c,dashjs.FactoryMaker.updateClassFactory(C.__dashjs_factory_name,P);var O=P,B="undefined"!=typeof window&&window||global,F=B.dashjs;F||(F=B.dashjs={}),F.MssHandler=O;var x=F,R=(e=e.default).MssHandler,L=e.default;export{R as MssHandler,L as default};
//# sourceMappingURL=dash.mss.min.js.map