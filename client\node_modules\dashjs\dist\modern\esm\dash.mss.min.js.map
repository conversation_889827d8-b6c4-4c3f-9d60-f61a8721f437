{"version": 3, "file": "dash.mss.min.js", "mappings": "AACA,IAAIA,EAAsB,CCA1BA,EAAwB,SAASC,EAASC,GACzC,IAAI,IAAIC,KAAOD,EACXF,EAAoBI,EAAEF,EAAYC,KAASH,EAAoBI,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAG3E,ECPAH,EAAwB,SAASS,EAAKC,GAAQ,OAAOL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,EAAO,ECCtGV,EAAwB,SAASC,GACX,oBAAXa,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeL,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeL,EAAS,aAAc,CAAEe,OAAO,GACvD,G,kFC4CA,MAfA,MAEIC,WAAAA,GACIC,KAAKC,SAAW,KAChBD,KAAKE,YAAc,KACnBF,KAAKG,MAAQC,IACbJ,KAAKK,MAAQ,KACbL,KAAKM,MAAQF,IACbJ,KAAKO,IAAMH,IACXJ,KAAKQ,SAAWJ,IAChBJ,KAAKS,eAAiB,KACtBT,KAAKU,YAAc,IACvB,GCXJ,MAAMC,EAIFZ,WAAAA,GAKIC,KAAKY,MAAQ,KAabZ,KAAKa,KAAO,KAKZb,KAAKc,IAAM,KAKXd,KAAKe,UAAY,KAKjBf,KAAKgB,MAAQ,KAKbhB,KAAKiB,SAAW,KAKhBjB,KAAKkB,UAAY,KAKjBlB,KAAKmB,aAAe,KAKpBnB,KAAKoB,SAAW,KAKhBpB,KAAKqB,MAAQ,GAKbrB,KAAKsB,KAAO,KAMZtB,KAAKuB,QAAU,KAKfvB,KAAKwB,SAAW,KAKhBxB,KAAKyB,eAAiB,KAKtBzB,KAAK0B,iBAAmB,KAKxB1B,KAAK2B,iBAAmB,KAIxB3B,KAAK4B,gBAAkB,KAIvB5B,KAAK6B,sBAAwB,IACjC,EA8BJlB,EAAYmB,IAAM,MAClBnB,EAAYoB,KAAO,OACnBpB,EAAYqB,SAAW,MACvBrB,EAAYsB,qBAAuB,iBACnCtB,EAAYuB,kBAAoB,wBAChCvB,EAAYwB,mBAAqB,eACjCxB,EAAYyB,mBAAqB,eACjCzB,EAAY0B,iCAAmC,4BAC/C1B,EAAY2B,+BAAiC,sBAC7C3B,EAAY4B,mBAAqB,eACjC5B,EAAY6B,QAAU,UACtB7B,EAAY8B,sBAAwB,kBACpC9B,EAAY+B,WAAa,QC/IzB,MAAMC,EACF5C,WAAAA,CAAYe,GACRd,KAAK4C,OAASD,EAAgBE,gBAC9B7C,KAAK8C,oBAAsB,KAC3B9C,KAAK+C,sBAAwB,KAC7B/C,KAAKgD,UAAY5C,IACjBJ,KAAKiD,YAAc7C,IACnBJ,KAAKkD,WAAa9C,IAClBJ,KAAKmD,iBAAmB/C,IACxBJ,KAAKQ,SAAWJ,IAChBJ,KAAKoD,QAAU,KACfpD,KAAKqD,cAAgB,KACrBrD,KAAKG,MAAQC,IACbJ,KAAKsD,eAAiBlD,IACtBJ,KAAKuD,UAAY,KACjBvD,KAAKgB,MAAQ,KACbhB,KAAKS,eAAiB,KACtBT,KAAKwD,aAAe,cACpBxD,KAAKyD,cAAgB,EACrBzD,KAAK0D,gBAAkB,KACvB1D,KAAK2D,UAAY,KACjB3D,KAAK4D,UAAYxD,IACjBJ,KAAK6D,UAAYzD,IACjBJ,KAAKa,KAAO,KACZb,KAAKc,IAAMA,GAAO,KAClBd,KAAK8D,cAAgB,IACzB,CAEAC,uBAAAA,GACI,OAAQ/D,KAAKa,MAAQb,KAAKa,OAASF,EAAYuB,iBACnD,CAEA8B,OAAAA,CAAQC,GACJjE,KAAKa,KAAOoD,GAAQA,EAAKC,KAAOvD,EAAYuB,kBAAoBvB,EAAYyB,mBAC5EpC,KAAKc,IAAMmD,GAAQA,EAAKnD,IAAMmD,EAAKnD,IAAM,KACzCd,KAAKgB,MAAQiD,GAAQA,EAAKjD,MAAQiD,EAAKjD,MAAMV,MAAQ,IAAM2D,EAAKjD,MAAMT,IAAM,KAC5EP,KAAKuD,UAAYU,GAAQA,EAAKV,UAAYU,EAAKV,UAAY,KAC3DvD,KAAKS,eAAiBwD,GAAQA,EAAKxD,eAAiBwD,EAAKxD,eAAiB,IAC9E,EAGJkC,EAAgBE,gBAAkB,WAClCF,EAAgBwB,gBAAkB,WAElC,QC/CA,MAAMC,EAAgB,WAElB,IAAIC,EACAC,EAAoB,GACxB,MAAMC,EAAqB,CAAC,EACtBC,EAAiB,CAAC,EAuBxB,SAASC,EAAqBC,EAASC,GACnC,IAAK,MAAMC,KAAKN,EAAmB,CAC/B,MAAM/E,EAAM+E,EAAkBM,GAC9B,GAAIrF,EAAImF,UAAYA,GAAWnF,EAAIsF,OAASF,EACxC,OAAOpF,EAAI8E,QAEnB,CACA,OAAO,IACX,CA2CA,SAASS,EAAiBD,EAAME,GAC5B,OAAOA,EAAeF,EAC1B,CAEA,SAASG,EAAcH,EAAMI,EAASF,GAC9BF,KAAQE,IACRA,EAAeF,GAAQI,EAE/B,CAmFA,SAASC,EAAMC,EAAkBT,EAASU,GAEtC,IAAIC,EACJ,MAAMV,EAAYQ,EAAiBG,sBAC7BC,EAAkBb,EAAQC,GAEhC,GAAIY,EAAiB,CAEjB,IAAIC,EAAYD,EAAgBlB,SAEhC,IAAIkB,EAAgBE,SAiBhB,OAAOD,EAAUE,MAAM,CACnBhB,UACAO,QAASZ,GACVe,GAlBHC,EAAgBF,EAAiBO,MAAM,CAAChB,WAAUU,GAClDI,EAAYA,EAAUE,MAAM,CACxBhB,UACAO,QAASZ,EACTsB,OAAQN,GACTD,GAEH,IAAK,MAAM5F,KAAQgG,EACXH,EAAc3F,eAAeF,KAC7B6F,EAAc7F,GAAQgG,EAAUhG,GAYhD,MAEI6F,EAAgBF,EAAiBO,MAAM,CAAChB,WAAUU,GAMtD,OAFAC,EAAcO,aAAe,WAAa,OAAOjB,CAAU,EAEpDU,CACX,CAeA,OAbAhB,EAAW,CACPwB,OAhNJ,SAAgBhB,EAAMiB,EAAeL,EAAUf,IACtCA,EAAQG,IAASiB,IAClBpB,EAAQG,GAAQ,CACZR,SAAUyB,EACVL,SAAUA,GAGtB,EA0MIhB,qBAAsBA,EACtBsB,qBA1KJ,SAA8BrB,EAASC,EAAWN,GAC9C,IAAK,MAAMO,KAAKN,EAAmB,CAC/B,MAAM/E,EAAM+E,EAAkBM,GAC9B,GAAIrF,EAAImF,UAAYA,GAAWnF,EAAIsF,OAASF,EAExC,YADAL,EAAkBM,GAAGP,SAAWA,EAGxC,CACAC,EAAkB0B,KAAK,CACnBnB,KAAMF,EACND,QAASA,EACTL,SAAUA,GAElB,EA8JI4B,yBArJJ,SAAkCvB,GAC9BJ,EAAoBA,EAAkB4B,QAAOC,GAAKA,EAAEzB,UAAYA,GACpE,EAoJI0B,oBAlFJ,SAA6BjB,GACzB,IAAIF,EAAUH,EAAiBK,EAAiBG,sBAAuBf,GA6BvE,OA5BKU,IACDA,EAAU,SAAUP,GAChB,IAAIL,EAIJ,YAHgBgC,IAAZ3B,IACAA,EAAU,CAAC,GAER,CACH4B,YAAa,WAcT,OAZKjC,IACDA,EAAWI,EAAqBC,EAASS,EAAiBG,wBAGzDjB,IACDA,EAAWa,EAAMC,EAAkBT,EAAS6B,WAC5CjC,EAAkB0B,KAAK,CACnBnB,KAAMM,EAAiBG,sBACvBZ,QAASA,EACTL,SAAUA,KAGXA,CACX,EAER,EACAE,EAAmBY,EAAiBG,uBAAyBL,GAG1DA,CACX,EAoDIuB,0BAvFJ,SAAmC3B,GAC/B,OAAOC,EAAiBD,EAAMN,EAClC,EAsFIkC,uBA5FJ,SAAgC5B,EAAMI,GAClCD,EAAcH,EAAMI,EAASV,EACjC,EA2FImC,gBAvHJ,SAAyBvB,GACrB,IAAIF,EAAUH,EAAiBK,EAAiBG,sBAAuBd,GAgBvE,OAdKS,IACDA,EAAU,SAAUP,GAIhB,YAHgB2B,IAAZ3B,IACAA,EAAU,CAAC,GAER,CACHiC,OAAQ,WACJ,OAAOzB,EAAMC,EAAkBT,EAAS6B,UAC5C,EAER,EAEA/B,EAAeW,EAAiBG,uBAAyBL,GAEtDA,CACX,EAsGI2B,sBA5HJ,SAA+B/B,GAC3B,OAAOC,EAAiBD,EAAML,EAClC,EA2HIqC,mBAjIJ,SAA4BhC,EAAMI,GAC9BD,EAAcH,EAAMI,EAAST,EACjC,GAkIOH,CAEX,CArOsB,GAuOtB,QCtOA,SAASyC,EAA0BC,GAI/B,IAAI1C,EACA2C,EACAC,EACAC,EACArG,EACAsG,EACAvD,EACAwD,EACAjH,EAEJ,MAAMkH,GAZNN,EAASA,GAAU,CAAC,GAYWM,gBACzBC,EAAoBP,EAAOO,kBAC3BC,EAAQR,EAAOQ,MA6BrB,SAASC,IACAN,IAILF,EAAOO,MAAM,QAEbE,aAAaN,GACbD,GAAU,EACVtD,EAAY,KACZwD,EAAoB,KACxB,CAMA,SAASM,IACL,IAAKR,EACD,OAIJ,MAAMzG,EAyC2B4G,EAAgBM,8BACDC,2BAxC1CC,EADWpH,EAAeoH,WAAWC,OAAOC,IAAIC,SAC1BC,OAAOxH,EAAeoH,WAAWC,OAAO3H,OAAO+H,cAAczH,EAAeoH,WAAW1H,OAC7GgI,EAAWN,EAAWO,gBAAgBC,gBAAgBC,EAMtDC,EAMV,SAA8BV,EAAYpH,EAAgB+H,GACtD,IAAI3E,EAAYgE,EAAWO,gBAAgBvE,UACvC0E,EAAU,IAAI5F,EAoBlB,OAlBA4F,EAAQhF,UAAY1C,EACpB0H,EAAQ1H,KAAOF,EAAY2B,+BAE3BiG,EAAQ3E,UAAY4E,EAAQC,EAAI5E,EAChC0E,EAAQ/H,SAAWgI,EAAQE,EAAI7E,EAC/B0E,EAAQ1E,UAAYA,EAIpB0E,EAAQvF,UAAYvC,EAAeuC,UACnCuF,EAAQpI,MAAQA,IAChBoI,EAAQI,gBAAkBlI,EAAeoH,WAAW1H,MACpDoI,EAAQ9H,eAAiBA,EACzB8H,EAAQzH,IAAMwG,EAAkBsB,QAAQnI,EAAeoI,MAAM/H,IAAM+G,EAAWO,gBAAgBU,MAC9FP,EAAQzH,IAAMyH,EAAQzH,IAAIiI,QAAQ,cAAetI,EAAeuC,WAChEuF,EAAQzH,IAAMyH,EAAQzH,IAAIiI,QAAQ,SAAUP,EAAQQ,UAAYR,EAAQQ,UAAYR,EAAQC,GAC5FF,EAAQzH,IAAMyH,EAAQzH,IAAIiI,QAAQ,cAAe,kBAE1CR,CACX,CA7BoBU,CAAqBpB,EAAYpH,EALjC0H,EAASA,EAASe,OAAS,IAQ3CC,EAAgBxJ,KAAKK,KAAMuI,EAC/B,CAiCA,SAASY,EAAgBZ,GAErB,GAAIlB,EAAgB+B,mBAAmBC,0BAA0Bd,GAI7D,OAFAvB,EAAOO,MAAM,wBACbC,IAIJP,EAAcqC,eAAef,EACjC,CAuDA,OAXAlE,EAAW,CACPkF,WAlJJ,WACI1I,EAAOwG,EAAgBmC,UACvBvC,EAAgBI,EAAgB+B,mBAEhClC,GAAU,EACVtD,EAAY,KACZwD,EAAoB,IACxB,EA4IIqC,eAzJmB,4BA0JnBnJ,MA3IJ,WACQ4G,IAIJF,EAAOO,MAAM,SAEbL,GAAU,EACV/G,EAAQ,EAERuH,IACJ,EAiIIgC,mBA9CJ,SAA4BC,GACxB,IAAKzC,EACD,OAGJ,MAAMqB,EAAUoB,EAAEpB,QAClB,IAAKoB,EAAEC,SAEH,YADA5C,EAAO6C,MAAM,aAActB,EAAQzH,KAIvC,IAAIgJ,EACAC,EACAC,EAIc,OAAdpG,IACAA,GAAY,IAAIqG,MAAOC,WAGtB9C,IACDA,EAAoBmB,EAAQ3E,WAIhCmG,IAAa,IAAIE,MAAOC,UAAYtG,GAAa,IACjDkG,EAAqBvB,EAAQ3E,UAAY2E,EAAQ/H,SAAY4G,EAC7D4C,EAAQG,KAAKC,IAAI,EAAIN,EAAoBC,GAGzCtC,aAAaN,GACbA,EAAsBkD,YAAW,WAC7BlD,EAAsB,KACtBO,GACJ,GAAW,IAARsC,EACP,EAWIR,QATJ,WACI,OAAO3I,CACX,EAQIyJ,MApHJ,WACI9C,GACJ,GAxCIR,EAASO,EAAMgD,UAAUlG,GA+JtBA,CACX,CAEAyC,EAA0BxB,sBAAwB,4BAClD,MAAelB,EAAasC,gBAAgBI,GChL5C,EARA,MACI/G,WAAAA,CAAYyK,EAAMC,EAASC,GACvB1K,KAAKwK,KAAOA,GAAQ,KACpBxK,KAAKyK,QAAUA,GAAW,KAC1BzK,KAAK0K,KAAOA,GAAQ,IACxB,GCkBJ,EAvBA,MACI7E,MAAAA,CAAO8E,EAAQ5D,GACX,IAAK4D,EACD,OAGJ,IAAIlF,IAAWsB,GAASA,EAAOtB,SAC3BmF,IAAa7D,GAASA,EAAO6D,WAGjC,IAAK,MAAMC,KAAOF,GACTA,EAAOjL,eAAemL,IAAS7K,KAAK6K,KAASpF,GAG9CmF,IAAkD,IAApCD,EAAOE,GAAKC,QAAQ,aAGtC9K,KAAK6K,GAAOF,EAAOE,GAG3B,GCCJ,EADgB,IAlBhB,cAAwBE,EACpBhL,WAAAA,GACIiL,QAIAhL,KAAKiL,iBAAmB,IAKxBjL,KAAKkL,2BAA6B,IAElClL,KAAKmL,oBAAsB,qCAC3BnL,KAAKoL,8BAAgC,mBACzC,GCMJ,EAvBA,MACIvF,MAAAA,CAAOwF,EAAQtE,GACX,IAAKsE,EACD,OAGJ,IAAI5F,IAAWsB,GAASA,EAAOtB,SAC3BmF,IAAa7D,GAASA,EAAO6D,WAGjC,IAAK,MAAMU,KAAOD,GACTA,EAAO3L,eAAe4L,IAAStL,KAAKsL,KAAS7F,GAG9CmF,IAAkD,IAApCS,EAAOC,GAAKR,QAAQ,aAGtC9K,KAAKsL,GAAOD,EAAOC,GAG3B,GC6bJ,EADwB,IA9cxB,cAAgCC,EAK5BxL,WAAAA,GACIiL,QAOAhL,KAAKwL,cAAgB,cAMrBxL,KAAKyL,kBAAoB,kBAOzBzL,KAAK0L,aAAe,gBAOpB1L,KAAK2L,cAAgB,eAMrB3L,KAAK4L,2BAA6B,qBAMlC5L,KAAK6L,qBAAuB,qBAM5B7L,KAAK8L,wBAA0B,uBAM/B9L,KAAK+L,2BAA6B,0BAMlC/L,KAAKgM,yBAA2B,wBAMhChM,KAAKiM,kBAAoB,kBAMzBjM,KAAKkM,MAAQ,QAKblM,KAAKmM,2BAA6B,2BAMlCnM,KAAKoM,0BAA4B,0BAKjCpM,KAAKqM,yBAA2B,yBAMhCrM,KAAKsM,2BAA6B,2BAMlCtM,KAAKuM,IAAM,MAMXvM,KAAKwM,yBAA2B,yBAMhCxM,KAAKyM,0BAA4B,0BAMjCzM,KAAK0M,gBAAkB,iBAMvB1M,KAAK2M,gBAAkB,iBAMvB3M,KAAK4M,eAAiB,gBAMtB5M,KAAK6M,aAAe,cAMpB7M,KAAK8M,eAAiB,gBAMtB9M,KAAK+M,sBAAwB,sBAM7B/M,KAAKgN,wBAA0B,wBAM/BhN,KAAKiN,yBAA2B,yBAMhCjN,KAAKkN,wBAA0B,wBAM/BlN,KAAKmN,mBAAqB,mBAM1BnN,KAAKoN,sBAAwB,sBAM7BpN,KAAKqN,oBAAsB,qBAM3BrN,KAAKsN,eAAiB,gBAMtBtN,KAAKuN,iBAAmB,kBAMxBvN,KAAKwN,mBAAqB,oBAM1BxN,KAAKyN,mBAAqB,oBAM1BzN,KAAK0N,yBAA2B,yBAMhC1N,KAAK2N,kBAAoB,qBAMzB3N,KAAK4N,iBAAmB,iBAMxB5N,KAAK6N,UAAY,WAMjB7N,KAAK8N,SAAW,UAMhB9N,KAAK+N,8BAAgC,8BAMrC/N,KAAKgO,YAAc,aAMnBhO,KAAKiO,cAAgB,cAMrBjO,KAAKkO,iBAAmB,kBAMxBlO,KAAKmO,yBAA2B,yBAQhCnO,KAAKoO,SAAW,UAMhBpO,KAAKqO,iBAAmB,iBAMxBrO,KAAKsO,eAAiB,gBAOtBtO,KAAKuO,eAAiB,gBAOtBvO,KAAKwO,qBAAuB,sBAM5BxO,KAAKyO,qBAAuB,qBAO5BzO,KAAK0O,yBAA2B,yBAOhC1O,KAAK2O,qBAAuB,qBAM5B3O,KAAK4O,gBAAkB,iBAQvB5O,KAAK6O,iBAAmB,kBAQxB7O,KAAK8O,kBAAoB,mBAMzB9O,KAAK+O,sBAAwB,sBAM7B/O,KAAKgP,gBAAkB,iBAMvBhP,KAAKiP,iBAAmB,kBAMxBjP,KAAKkP,iBAAmB,kBAQxBlP,KAAKmP,iBAAmB,kBAMxBnP,KAAKoP,sBAAwB,sBAM7BpP,KAAKqP,wBAA0B,wBAO/BrP,KAAKsP,iBAAmB,kBAMxBtP,KAAKuP,0BAA4B,0BAMjCvP,KAAKwP,oBAAsB,mBAM3BxP,KAAKyP,sBAAwB,qBAM7BzP,KAAK0P,sBAAwB,uBAM7B1P,KAAK2P,sBAAwB,uBAM7B3P,KAAK4P,uCAAyC,qCAM9C5P,KAAK6P,mCAAqC,kCAM1C7P,KAAK8P,YAAc,aAMnB9P,KAAK+P,qCAAuC,mCAM5C/P,KAAKgQ,mCAAqC,gCAC9C,GCtcJ,SAASC,EAAyBlJ,GAG9B,IAAI1C,EACAxD,EACAmG,EACJ,MAAMkJ,GAJNnJ,EAASA,GAAU,CAAC,GAIOmJ,YACrBC,EAAqBpJ,EAAOoJ,mBAC5BC,EAAerJ,EAAOsJ,WACtBC,EAAWvJ,EAAOuJ,SAClBC,EAAWxJ,EAAOwJ,SAClBhJ,EAAQR,EAAOQ,MAOrB,SAASiJ,EAAYjI,EAASkI,EAAMC,EAAMrJ,GACtC,MACM5G,EAD2B4G,EAAgBM,8BACDC,2BAE1CI,EAAWvH,EAAeoH,WAAWC,OAAOC,IAAIC,SAChDH,EAAaG,EAASC,OAAOxH,EAAeoH,WAAWC,OAAO3H,OAAO+H,cAAczH,EAAeoH,WAAW1H,OAC7G0D,EAAYgE,EAAWO,gBAAgBvE,UAK7C,GAHAhD,EAAOwG,EAAgBmC,UAGD,YAAlBxB,EAASnH,OAAuBmH,EAAS2I,qBACzC,OAGJ,IAAKF,EAED,YADAL,EAAavG,MAAM,IAAI+G,EAAYC,EAAU5F,iBAAkB4F,EAAU1F,sBAK7E,MAAMhD,EAAWN,EAAWO,gBAAgBC,gBAAgBC,EACtDwI,EAAUL,EAAKM,MACrB,IAAIA,EACAC,EACAhQ,EAGAiQ,EAFAzI,EAAU,KACVC,EAAI,EAEJ1F,EAAwB,KAE5B,GAAuB,IAAnB+N,EAAQ5H,OACR,OAQJ,GAJA6H,EAAQD,EAAQ,GAIM,WAAlB9I,EAASnH,OAETmQ,EAAc7I,EAAS,GAAGa,UAAYkI,WAAW/I,EAAS,GAAGa,WAAab,EAAS,GAAGM,EAClFsI,EAAMI,uBAA0BH,EAAehJ,EAAS2I,qBAAuB9M,GAC/E,OAWR,GAJAmN,EAAc7I,EAASA,EAASe,OAAS,GAAGF,UAAYkI,WAAW/I,EAASA,EAASe,OAAS,GAAGF,WAAab,EAASA,EAASe,OAAS,GAAGT,EAIxIsI,EAAMI,wBAA0BH,EAQhC,OANAhQ,EAAQ,CACJV,MAAO6H,EAAS,GAAGM,EAAI5E,EACvBtD,IAAMmQ,EAAKU,oBAAsBvN,EAAa0E,EAAQ/H,eAG1D6Q,EAAU9I,EAAQhF,UAAWvC,EAAOqG,EAAgBiK,gBAAgBC,cAKxE/I,EAAU,CAAC,EACXA,EAAQC,EAAIsI,EAAMI,uBAClB3I,EAAQE,EAAIqI,EAAMS,kBAEdrJ,EAAS,GAAGa,YACZR,EAAQC,GAAKyI,WAAW/I,EAAS,GAAGa,WAAab,EAAS,GAAGM,EAC7DD,EAAQQ,UAAY+H,EAAMI,wBAI9B,IAAIM,EAActJ,EAASA,EAASe,OAAS,GAS7C,GARIuI,EAAYhJ,EAAIgJ,EAAY/I,IAAMF,EAAQC,IAC1CzB,EAAOO,MAAM,gCAAiCkK,EAAYhJ,EAAI,SAAWgJ,EAAY/I,EAAI,QAAUF,EAAQC,EAAIgJ,EAAYhJ,IAC3HgJ,EAAY/I,EAAIF,EAAQC,EAAIgJ,EAAYhJ,GAG5CN,EAASnC,KAAKwC,GAGQ,WAAlBR,EAASnH,KAAb,CAWI,GAAImH,EAAS2I,sBAAwB3I,EAAS2I,qBAAuB,EAWjE,IATAnI,EAAUL,EAASA,EAASe,OAAS,GACrCT,EAAID,EAAQC,EAGZ1F,GAAyB0F,EAAKT,EAAS2I,qBAAuB9M,GAAcA,EAG5E2E,EAAUL,EAAS,GACnB8I,GAAWzI,EAAQC,EAAID,EAAQE,GAAK7E,EAC7BoN,EAAUlO,IAERoN,EAAmBuB,cAAcvB,EAAmBjG,UAAY+G,KAIrE9I,EAASwJ,OAAO,EAAG,GACnBnJ,EAAUL,EAAS,GACnB8I,GAAWzI,EAAQC,EAAID,EAAQE,GAAK7E,EAK5C7C,EAAQ,CACJV,MAAO6H,EAAS,GAAGM,EAAI5E,EACvBtD,IAAMmQ,EAAKU,oBAAsBvN,EAAa0E,EAAQ/H,UAG1D6Q,EAAUxQ,EAAMG,EAAOqG,EAAgBiK,gBAAgBC,aAC3D,KAxCiB,UAAT1Q,IACA2H,EAAUL,EAASA,EAASe,OAAS,GACrC+H,GAAWzI,EAAQC,EAAID,EAAQE,GAAK7E,EAChCoN,EAAUxQ,EAAeoH,WAAWC,OAAOtH,UAC3C8P,EAASsB,QAAQC,EAAOtC,0BAA2B,CAAEuC,OAAQ9R,KAAM+R,YAAad,IAsChG,CAEA,SAASI,EAAUxQ,EAAMG,EAAOuQ,GAC5B,GAAa,UAAT1Q,GAA6B,UAATA,EACpB,OAEJ,MAAMmR,EAAW9B,EAAY+B,kBAAkBpR,KAC1CmR,GAAahR,EAAMT,IAAMyR,EAAShR,MAAMT,OACzCyG,EAAOO,MAAM,sBAAwBvG,EAAMV,MAAQ,MAAQU,EAAMT,IAAM,KACvE2P,EAAYgC,WAAWrR,EAAMsP,EAAmBjG,UAAWqH,EAAcvQ,GACzEmP,EAAmBgC,kBAAkBtR,GAE7C,CAGA,SAASuR,EAAazM,EAAQ9E,GAC1B,IAAIwR,EAAS,EACTzN,EAAI,EAER,IAAKA,EAAI,EAAGA,EAAIe,EAAO2M,MAAMpJ,OAAQtE,IAAK,CACtC,GAAIe,EAAO2M,MAAM1N,GAAG/D,OAASA,EACzB,OAAOwR,EAEXA,GAAU1M,EAAO2M,MAAM1N,GAAG2N,IAC9B,CACA,OAAOF,CACX,CA2IA,OAPAhO,EAAW,CACPmO,gBAnIJ,SAAyB7I,EAAGtC,GACxB,IAAIzC,EAIJ,MAAM6N,EAAUlC,EAASmC,YAAY/I,EAAEC,UAEjC+I,EAAOF,EAAQG,MAAM,QAC3BD,EAAKE,SAAWlJ,EAAEpB,QAAQ9H,eAAeqS,UAAU3S,MAAQ,EAG3D,IAAIuQ,EAAO+B,EAAQG,MAAM,QACzB,MAAMG,EAAON,EAAQG,MAAM,QACd,OAATlC,IACAA,EAAOH,EAASyC,cAAc,OAAQD,EAAMJ,GAC5CjC,EAAKuC,QAAU,EACfvC,EAAKwC,MAAQ,EACbxC,EAAKU,oBAAsBjH,KAAKgJ,MAAMxJ,EAAEpB,QAAQ3E,UAAY+F,EAAEpB,QAAQ1E,YAG1E,MAAMuP,EAAOX,EAAQG,MAAM,QAI3B,IAAIS,EAAOZ,EAAQG,MAAM,QACrBS,IACAA,EAAKC,QAAQhB,MAAMX,OAAO0B,EAAKC,QAAQhB,MAAMxH,QAAQuI,GAAO,GAC5DA,EAAO,MAEX,IAAI5C,EAAOgC,EAAQG,MAAM,QACzBpC,EAAY7G,EAAEpB,QAASkI,EAAMC,EAAMrJ,GAC/BoJ,IACAA,EAAK6C,QAAQhB,MAAMX,OAAOlB,EAAK6C,QAAQhB,MAAMxH,QAAQ2F,GAAO,GAC5DA,EAAO,MAMX,MAAM8C,EAASd,EAAQG,MAAM,UAC7B,GAAe,OAAXW,EAAiB,CACjBA,EAAO1S,KAAO,OACd0S,EAAOC,cAAWnN,EAElB,IAAIoN,EAAOhB,EAAQG,MAAM,QACzB,GAAa,OAATa,EAAe,CAEfA,EAAOlD,EAASyC,cAAc,OAAQD,GACtCU,EAAKR,QAAU,EACfQ,EAAKP,MAAQ,EACbO,EAAKC,YAAc,EACnBD,EAAKpB,OAAS,CAAC,GAEf,MAAMsB,EAAOpD,EAASyC,cAAc,OAAQD,GAO5C,GANAY,EAAKV,QAAU,EACfU,EAAKT,MAAQ,EACbS,EAAKC,aAAeL,EAAOK,aAC3BD,EAAKE,yBAA2B,EAChCF,EAAKG,iBAAmB,GAEL,EAAfP,EAAOL,MAEP,IAAKtO,EAAI,EAAGA,EAAI2O,EAAOK,aAAchP,GAAK,EAGtC+O,EAAKG,iBAAiBlP,GAAK,GAAM,EAAI2O,EAAOxC,MAAMnM,GAAGmP,qBAIzDJ,EAAKE,yBAA2B,CAExC,CACJ,CAEAlB,EAAKO,OAAS,SACdP,EAAKO,OAAS,OACdE,EAAKF,OAAS,EAGd,MAAMc,EAAOvB,EAAQG,MAAM,QAC3B,IAAI1J,EAAS8K,EAAKC,YAClBb,EAAKc,YAAchL,EAAS,EAG5B,IAAIuK,EAAOhB,EAAQG,MAAM,QACzB,GAAa,OAATa,EAAe,CACf,IAAIU,EAAgB/B,EAAa4B,EAAM,QACnCI,EAAgBhC,EAAaW,EAAM,QAEvCU,EAAKpB,OAAO,GAAK8B,EAAgBC,EAAgB,EACrD,CAGAzK,EAAEC,SAAW6I,EAAQ4B,OACzB,EAsCIC,kBApCJ,SAA2B3K,EAAGtC,GAG1B,IAAKsC,EAAEC,SACH,MAAM,IAAI2K,MAAM,mCAGpB,MAAM9B,EAAUlC,EAASmC,YAAY/I,EAAEC,UAEjC+I,EAAOF,EAAQG,MAAM,QAC3BD,EAAKE,SAAWlJ,EAAEpB,QAAQ9H,eAAeqS,UAAU3S,MAAQ,EAG3D,IAAIuQ,EAAO+B,EAAQG,MAAM,QACrBG,EAAON,EAAQG,MAAM,QACZ,OAATlC,IACAA,EAAOH,EAASyC,cAAc,OAAQD,EAAMJ,GAC5CjC,EAAKuC,QAAU,EACfvC,EAAKwC,MAAQ,EACbxC,EAAKU,oBAAsBjH,KAAKgJ,MAAMxJ,EAAEpB,QAAQ3E,UAAY+F,EAAEpB,QAAQ1E,YAG1E,IAAI4M,EAAOgC,EAAQG,MAAM,QACzBpC,EAAY7G,EAAEpB,QAASkI,EAAMC,EAAMrJ,GAC/BoJ,IACAA,EAAK6C,QAAQhB,MAAMX,OAAOlB,EAAK6C,QAAQhB,MAAMxH,QAAQ2F,GAAO,GAC5DA,EAAO,KAEf,EASIjH,QAPJ,WACI,OAAO3I,CACX,GAjSImG,EAASO,EAAMgD,UAAUlG,GACzBxD,EAAO,GAySJwD,CACX,CAEA4L,EAAyB3K,sBAAwB,2BACjD,MAAelB,EAAasC,gBAAgBuJ,GC/T5C,SAASuE,EAAyBzN,GAE9B,MAEM0N,GAHN1N,EAASA,GAAU,CAAC,GAGK0N,UACnBlE,EAAWxJ,EAAOwJ,SAExB,IACIlM,EACAyD,EACA4M,EACAjU,EACAkU,EACA9Q,EACA+Q,EAPAC,EAAuB9N,EAAO8N,qBA8elC,SAASC,EAAwBC,EAAMC,GACxBzE,EAAS0E,UAAU,OAAQF,GACjCG,YAuET,SAA0BC,GACtB,IACIvQ,EADA4F,EAAO,EAGX,IAAK5F,EAAI,EAAGA,EAAIuQ,EAAIjM,OAAQtE,GAAK,EAC7B4F,GAAQ2K,EAAIC,WAAWxQ,IAA8B,GAAtBuQ,EAAIjM,OAAStE,EAAI,GAEpD,OAAO4F,CACX,CA/EuB6K,CAAiBL,EACxC,CAEA,SAASM,EAAoBP,GACzB,IAAIQ,EAAOhF,EAASyC,cAAc,OAAQ+B,GAE1CQ,EAAKrC,MAAQ,EACbqC,EAAKtC,QAAU,EACfsC,EAAKC,YAAc,WACnBD,EAAKE,eAAiB,KAC1B,CAEA,SAASC,EAA2BX,IAyBpC,SAAkCY,GAC9B,IAAIC,EAAOrF,EAASyC,cAAc,OAAQ2C,GAE1CC,EAAK1C,MAAQ,EACb0C,EAAK3C,QAAU,EAEf2C,EAAKC,oBAAsB,EAC3BD,EAAKE,gBAAkB,EACvBF,EAAKG,YAAepB,GAAsBA,EAAkBzL,OAAU,GAAKyL,EAAkB,GAAG,oBAC5FA,EAAkB,GAAG,oBAAsB,CAAC,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAC/H,CA/BIqB,CAHWzF,EAAS0E,UAAU,OAAQF,GAI1C,CA4CA,SAASkB,EAAkBd,GACvB,IACIvQ,EADAsR,EAAM,IAAIC,WAAWhB,EAAIjM,OAAS,GAGtC,IAAKtE,EAAI,EAAGA,EAAIuQ,EAAIjM,OAAS,EAAGtE,GAAK,EACjCsR,EAAItR,GAAKwR,SAAS,GAAKjB,EAAQ,EAAJvQ,GAASuQ,EAAQ,EAAJvQ,EAAQ,GAAI,IAExD,OAAOsR,CACX,CA0CA,OAJA7R,EAAW,CACPgS,aA3BJ,SAAsBC,GAClB,IAAKA,IAAQA,EAAIzO,WACb,OAGJ,IAAI4K,EACA8D,EAiBJ,OAfA9V,EAAiB6V,EACjB5B,EAAgBjU,EAAeoH,WAE/BC,EAAS4M,EAAc5M,OACvB8M,EAAUF,EAAcvU,MAAQ,EAChCwU,EAAoB7M,EAAOC,IAAIC,SAASC,OAAOH,EAAO3H,OAAO+H,cAAcwM,EAAcvU,OAAOqW,kBAEhG3S,EAAYiE,EAAOC,IAAIC,SAASC,OAAOH,EAAO3H,OAAO+H,cAAcwM,EAAcvU,OAAOiI,gBAAgBvE,UAExG4O,EAAUlC,EAASkG,aAzkBvB,SAAuBhE,GACnB,IAAIiE,EAAOnG,EAAS0E,UAAU,OAAQxC,GACtCiE,EAAKC,YAAc,OACnBD,EAAKE,cAAgB,EACrBF,EAAKG,kBAAoB,GACzBH,EAAKG,kBAAkB,GAAK,OAC5BH,EAAKG,kBAAkB,GAAK,OAC5BH,EAAKG,kBAAkB,GAAK,MAGhC,CAgkBIC,CAAcrE,GA9jBlB,SAAuBA,GAGnB,IAAIsE,EAAOxG,EAAS0E,UAAU,OAAQxC,IA+E1C,SAAuBsE,GAEnB,IAAIC,EAAOzG,EAASyC,cAAc,OAAQ+D,GAE1CC,EAAK/D,QAAU,EAEf+D,EAAKC,cAAgB,EACrBD,EAAKE,kBAAoB,EACzBF,EAAKnT,UAAYA,EACjBmT,EAAKxW,SAAWsH,EAAOtH,WAAa2W,IAAW,oBAAqBhN,KAAKiN,MAAMtP,EAAOtH,SAAWqD,GACjGmT,EAAKK,KAAO,EACZL,EAAKM,OAAS,EACdN,EAAKO,UAAY,EACjBP,EAAKQ,UAAY,CAAC,EAAK,GACvBR,EAAKS,OAAS,CACV,EAAG,EAAG,EACN,EAAG,EAAG,EACN,EAAG,EAAG,OAEVT,EAAKU,YAAc,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,GACnCV,EAAKW,cAAgB/C,EAAU,CAGnC,CAnGIgD,CAAcb,GAGd,IAAIc,EAAOtH,EAAS0E,UAAU,OAAQ8B,IAkG1C,SAAuBc,GAEnB,IAAIC,EAAOvH,EAASyC,cAAc,OAAQ6E,GAE1CC,EAAK7E,QAAU,EACf6E,EAAK5E,MAAQ,EAIb4E,EAAKb,cAAgB,EACrBa,EAAKZ,kBAAoB,EACzBY,EAAKjF,SAAW+B,EAChBkD,EAAKP,UAAY,EACjBO,EAAKtX,SAAWsH,EAAOtH,WAAa2W,IAAW,oBAAqBhN,KAAKiN,MAAMtP,EAAOtH,SAAWqD,GACjGiU,EAAKN,UAAY,CAAC,EAAK,GACvBM,EAAKC,MAAQ,EACbD,EAAKE,gBAAkB,EACvBF,EAAKR,OAAS,EACdQ,EAAKG,UAAY,EACjBH,EAAKL,OAAS,CACV,EAAG,EAAG,EACN,EAAG,EAAG,EACN,EAAG,EAAG,OAEVK,EAAKI,MAAQzX,EAAeyX,MAC5BJ,EAAKK,OAAS1X,EAAe0X,MAGjC,CA3HIC,CAAcP,GAGd,IAAIQ,EAAO9H,EAAS0E,UAAU,OAAQ4C,IA0H1C,SAAuBQ,GAEnB,IAAIC,EAAO/H,EAASyC,cAAc,OAAQqF,GAE1CC,EAAKrF,QAAU,EAEfqF,EAAKrB,cAAgB,EACrBqB,EAAKpB,kBAAoB,EACzBoB,EAAKzU,UAAYA,EACjByU,EAAK9X,SAAWsH,EAAOtH,WAAa2W,IAAW,oBAAqBhN,KAAKiN,MAAMtP,EAAOtH,SAAWqD,GACjGyU,EAAKC,SAAW7D,EAAc8D,MAAQ,MACtCF,EAAKZ,YAAc,CAGvB,CArIIe,CAAcJ,GAuIlB,SAAuBA,GAEnB,IAAIK,EAAOnI,EAASyC,cAAc,OAAQqF,GAG1C,OADAK,EAAKhB,YAAc,EACXhD,EAAc7T,MAClB,KAAK4T,EAAUkE,MACXD,EAAKE,aAAe,OACpB,MACJ,KAAKnE,EAAUoE,MACXH,EAAKE,aAAe,OACpB,MACJ,QACIF,EAAKE,aAAe,OAG5BF,EAAK7T,KAAOpE,EAAeqY,GAC3BJ,EAAKK,SAAW,CAAC,EAAG,EAAG,EAG3B,CAxJIC,CAAcX,GAGd,IAAIY,EAAO1I,EAAS0E,UAAU,OAAQoD,GAEtC,OAAQ3D,EAAc7T,MAClB,KAAK4T,EAAUkE,OAoJvB,SAAuBM,GAEnB,IAAIC,EAAO3I,EAASyC,cAAc,OAAQiG,GAE1CC,EAAKhG,MAAQ,EAEbgG,EAAKC,aAAe,EACpBD,EAAKE,QAAU,CAAC,EAAG,EAAG,EAG1B,CA5JYC,CAAcJ,GACd,MACJ,KAAKxE,EAAUoE,OA4JvB,SAAuBI,GAEnB,IAAIK,EAAO/I,EAASyC,cAAc,OAAQiG,GAE1CK,EAAKpG,MAAQ,EAEboG,EAAKC,QAAU,EACfD,EAAKP,SAAW,CAGpB,CApKYS,CAAcP,IAsK1B,SAAuBQ,GAEnB,IAAIC,EAAOnJ,EAASyC,cAAc,OAAQyG,GAE1CC,EAAKhG,YAAc,EACnBgG,EAAK5I,QAAU,GAEf,IAAIhQ,EAAMyP,EAASyC,cAAc,OAAQ0G,GAAM,GAC/C5Y,EAAI6Y,SAAW,GACf7Y,EAAIoS,MAAQ,EAEZwG,EAAK5I,QAAQ9K,KAAKlF,EAGtB,CA1KI8Y,CAHWrJ,EAAS0E,UAAU,OAAQgE,IAMtC,IAAIY,EAAOtJ,EAAS0E,UAAU,OAAQgE,GAM3B1I,EAASyC,cAAc,OAAQ6G,GACrCC,MAAQ,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAGxBvJ,EAASyC,cAAc,OAAQ6G,GACrCC,MAAQ,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAGxBvJ,EAASyC,cAAc,OAAQ6G,GACrCC,MAAQ,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAGxBvJ,EAASyC,cAAc,OAAQ6G,GACrCC,MAAQ,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAsJnD,SAAuBD,GAEnB,IAAIE,EAAOxJ,EAASyC,cAAc,OAAQ6G,GAG1C,OADAE,EAAKjJ,QAAU,GACP4D,EAAc7T,MAClB,KAAK4T,EAAUkE,MACf,KAAKlE,EAAUoE,MACXkB,EAAKjJ,QAAQ9K,KAUzB,SAA2B+T,GACvB,IAAI/E,EAAQvU,EAAeuZ,OAAOC,UAAU,EAAGxZ,EAAeuZ,OAAOlP,QAAQ,MAE7E,OAAQkK,GACJ,IAAK,OACD,OAcZ,SAAoC+E,EAAM/E,GACtC,IAAIkF,EA+BJ,GA5BIA,EADAvF,EACOpE,EAAS0E,UAAU,OAAQ8E,GAAM,GAEjCxJ,EAAS0E,UAAU,OAAQ8E,GAAM,GAI5CG,EAAK3C,UAAY,CAAC,EAAK,EAAK,EAAK,EAAK,EAAK,GAC3C2C,EAAKC,qBAAuB,EAG5BD,EAAKE,aAAe,EACpBF,EAAK1C,UAAY,EACjB0C,EAAKG,aAAe,CAAC,EAAG,EAAG,GAC3BH,EAAK/B,OAAS1X,EAAe0X,OAC7B+B,EAAKhC,MAAQzX,EAAeyX,MAC5BgC,EAAKI,gBAAkB,GACvBJ,EAAKK,eAAiB,GACtBL,EAAKjC,UAAY,EACjBiC,EAAKM,YAAc,EACnBN,EAAKO,eAAiB,CAClB,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,IAAM,IAC1C,IAAM,IAAM,IAAM,EAAM,EAAM,EAAM,EAAM,EAC1C,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAC1C,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,GAE9CP,EAAKQ,MAAQ,GACbR,EAAKS,aAAe,MACpBT,EAAKnT,OAkBT,WAEI,IAWI6T,EAAWC,EAXXC,EAAO,KACPC,EAAa,GAGbC,EAAM,GACNC,EAAM,GACNC,EAAuB,EACvBC,EAAqB,EACrBC,EAAwB,EAExBC,EAAQ5a,EAAe6a,iBAAiBC,MAAM,YAAYC,MAAM,GAGpE,IAAK,IAAI5W,EAAI,EAAGA,EAAIyW,EAAMnS,OAAQtE,IAK9B,OAJAgW,EAAY3E,EAAkBoF,EAAMzW,IAEpCiW,EAA0B,GAAfD,EAAU,GAEbC,GACJ,KA5VS,EA6VLG,EAAIhV,KAAK4U,GACTG,GAAcH,EAAU1R,OAAS,EACjC,MACJ,KA/VS,EAgWL+R,EAAIjV,KAAK4U,GACTG,GAAcH,EAAU1R,OAAS,EAQzC8R,EAAI9R,OAAS,IACbgS,EAAuBF,EAAI,GAAG,GAC9BI,EAAwBJ,EAAI,GAAG,GAC/BG,EAAqBH,EAAI,GAAG,IAIhCF,EAAO,IAAI3E,WAAW4E,GAEtB,IAAInW,EAAI,EAERkW,EAAKlW,MAAqB,WAAbmW,IAA4B,GACzCD,EAAKlW,MAAqB,SAAbmW,IAA4B,GACzCD,EAAKlW,MAAqB,MAAbmW,IAA4B,EACzCD,EAAKlW,KAAqB,IAAbmW,EACbD,EAAKW,IAAI,CAAC,GAAM,IAAM,GAAM,IAAO7W,GACnCA,GAAK,EACLkW,EAAKlW,KAAO,EACZkW,EAAKlW,KAAOsW,EACZJ,EAAKlW,KAAOwW,EACZN,EAAKlW,KAAOuW,EACZL,EAAKlW,KAAO,IACZkW,EAAKlW,KAAO,IAAOoW,EAAI9R,OACvB,IAAK,IAAIwS,EAAI,EAAGA,EAAIV,EAAI9R,OAAQwS,IAC5BZ,EAAKlW,MAAwB,MAAhBoW,EAAIU,GAAGxS,SAAoB,EACxC4R,EAAKlW,KAAwB,IAAhBoW,EAAIU,GAAGxS,OACpB4R,EAAKW,IAAIT,EAAIU,GAAI9W,GACjBA,GAAKoW,EAAIU,GAAGxS,OAEhB4R,EAAKlW,KAAOqW,EAAI/R,OAChB,IAAK,IAAIwS,EAAI,EAAGA,EAAIT,EAAI/R,OAAQwS,IAC5BZ,EAAKlW,MAAwB,MAAhBqW,EAAIS,GAAGxS,SAAoB,EACxC4R,EAAKlW,KAAwB,IAAhBqW,EAAIS,GAAGxS,OACpB4R,EAAKW,IAAIR,EAAIS,GAAI9W,GACjBA,GAAKqW,EAAIS,GAAGxS,OAGhB,OAAO4R,CACX,CA3FkBa,GACVhH,EAAmB,CAEnB,IAAII,EAAOxE,EAAS0E,UAAU,OAAQiF,GAGtCpF,EAAwBC,EAAMC,GAG9BM,EAAoBP,GAGpBW,EAA2BX,EAC/B,CAEA,OAAOmF,CACX,CA7DmB0B,CAA2B7B,EAAM/E,GAC5C,IAAK,OACD,OAwIZ,SAAmC+E,EAAM/E,GACrC,IAAI6G,EAsBJ,GAnBIA,EADAlH,EACOpE,EAAS0E,UAAU,OAAQ8E,GAAM,GAEjCxJ,EAAS0E,UAAU,OAAQ8E,GAAM,GAI5C8B,EAAKtE,UAAY,CAAC,EAAK,EAAK,EAAK,EAAK,EAAK,GAC3CsE,EAAK1B,qBAAuB,EAG5B0B,EAAKrE,UAAY,CAAC,EAAK,GACvBqE,EAAKC,aAAerb,EAAesb,cACnCF,EAAKG,WAAa,GAClBH,EAAKnE,YAAc,EACnBmE,EAAKI,WAAa,EAClBJ,EAAKK,WAAazb,EAAe0b,mBAAqB,GAEtDN,EAAKO,KAmBT,WAGI,IAAIC,EAAsBpG,EAAkBxV,EAAe6a,kBAOvDgB,EAAa,GAAKD,EAAoBnT,OACtCkT,EAAO,IAAIjG,WAAWmG,GAEtB1X,EAAI,EAyCR,OAvCAwX,EAAKxX,MAAqB,WAAb0X,IAA4B,GACzCF,EAAKxX,MAAqB,SAAb0X,IAA4B,GACzCF,EAAKxX,MAAqB,MAAb0X,IAA4B,EACzCF,EAAKxX,KAAqB,IAAb0X,EACbF,EAAKX,IAAI,CAAC,IAAM,IAAM,IAAM,KAAO7W,GACnCA,GAAK,EACLwX,EAAKX,IAAI,CAAC,EAAG,EAAG,EAAG,GAAI7W,GACvBA,GAAK,EAELwX,EAAKxX,KAAO,EACZwX,EAAKxX,KAAO,GAAKyX,EAAoBnT,OACrCkT,EAAKxX,MAAkB,MAAVgQ,IAAqB,EAClCwH,EAAKxX,KAAkB,IAAVgQ,EACbwH,EAAKxX,KAAO,EAGZwX,EAAKxX,KAAO,EACZwX,EAAKxX,KAAO,GAAKyX,EAAoBnT,OACrCkT,EAAKxX,KAAO,GACZwX,EAAKxX,GAAK,GACVwX,EAAKxX,IAAM,EACXwX,EAAKxX,MAAQ,EACbwX,EAAKxX,KAAO,IACZwX,EAAKxX,KAAO,IACZwX,EAAKxX,KAAO,IACZwX,EAAKxX,MAAmC,WAA3BnE,EAAeuC,YAA2B,GACvDoZ,EAAKxX,MAAmC,SAA3BnE,EAAeuC,YAA2B,GACvDoZ,EAAKxX,MAAmC,MAA3BnE,EAAeuC,YAA2B,EACvDoZ,EAAKxX,KAAmC,IAA3BnE,EAAeuC,UAC5BoZ,EAAKxX,MAAmC,WAA3BnE,EAAeuC,YAA2B,GACvDoZ,EAAKxX,MAAmC,SAA3BnE,EAAeuC,YAA2B,GACvDoZ,EAAKxX,MAAmC,MAA3BnE,EAAeuC,YAA2B,EACvDoZ,EAAKxX,KAAmC,IAA3BnE,EAAeuC,UAG5BoZ,EAAKxX,KAAO,EACZwX,EAAKxX,KAAOyX,EAAoBnT,OAChCkT,EAAKX,IAAIY,EAAqBzX,GAEvBwX,CACX,CA1EgBG,GAER5H,EAAmB,CAEnB,IAAII,EAAOxE,EAAS0E,UAAU,OAAQ4G,GAGtC/G,EAAwBC,EAAMC,GAG9BM,EAAoBP,GAGpBW,EAA2BX,EAC/B,CAEA,OAAO8G,CACX,CA9KmBW,CAA0BzC,EAAM/E,GAC3C,QACI,KAAM,CACFxK,KAAMqG,EAAU3F,2BAChBT,QAASoG,EAAUzF,8BACnBV,KAAM,CACFsK,MAAOA,IAI3B,CA3B8ByH,CAAkB1C,IAM5CA,EAAKrG,YAAcqG,EAAKjJ,QAAQ5H,MAEpC,CAnKIwT,CAAc7C,GAwclB,SAAuB9C,GACnB,IAAI4F,EAAOpM,EAASyC,cAAc,OAAQ+D,GAE1C4F,EAAK9J,SAAW+B,EAChB+H,EAAKC,iCAAmC,EACxCD,EAAKE,wBAA0B,EAC/BF,EAAKG,oBAAsB,EAC3BH,EAAKI,qBAAuB,CAGhC,CA5cIC,CAHWzM,EAAS0E,UAAU,OAAQ8B,IAKlCpC,GAAqBE,GAka7B,SAAiDkC,EAAMkG,GACnD,IAAIC,EACAC,EACAvY,EACAwY,EAEJ,IAAKxY,EAAI,EAAGA,EAAIqY,EAAW/T,OAAQtE,GAAK,EACpCsY,EAAaD,EAAWrY,GAAGyY,SACvBH,IACAE,EAAe7M,EAASmC,YAAYwK,GACpCC,EAAOC,EAAaxK,MAAM,QACtBuK,GACA5M,EAAS+M,MAAMC,UAAUxG,EAAMoG,GAI/C,CAhbQK,CAAwCzG,EADtBlC,EAAqB4I,mDAAmD9I,GAGlG,CA+eI+I,CAAcjL,GAEd8D,EAAc9D,EAAQ4B,QAEfkC,CACX,GAMOlS,CACX,CAEAmQ,EAAyBlP,sBAAwB,2BACjD,MAAelB,EAAasC,gBAAgB8N,GCzmB5C,SAASmJ,EAAWC,EAAMC,GACtB,OAAQD,EAAK1U,SAAW2U,EAAK3U,QAAW0U,EAAKE,OAAM,SAAUC,EAAS5d,GAClE,OAAO4d,IAAYF,EAAK1d,EAC5B,GACJ,CAEA,SAAS6d,IACLhe,KAAKie,eACY,EAAbje,KAAKkT,QACLlT,KAAKke,WAAW,gBAAiB,OAAQ,IACzCle,KAAKke,WAAW,0BAA2B,OAAQ,KAEvDle,KAAKke,WAAW,cAAe,OAAQ,IACvCle,KAAKme,gBAAgB,SAAUne,KAAK0T,YAAa,OAA0B,IAAjB1T,KAAKiT,QAAiB,GAAK,GACzF,CAEA,SAASmL,IACLpe,KAAKie,eACY,EAAbje,KAAKkT,QACLlT,KAAKke,WAAW,gBAAiB,OAAQ,IACzCle,KAAKke,WAAW,0BAA2B,OAAQ,KAEvDle,KAAKke,WAAW,2BAA4B,OAAQ,GACpDle,KAAKke,WAAW,eAAgB,OAAQ,IACF,IAAlCle,KAAK6T,0BACL7T,KAAKme,gBAAgB,mBAAoBne,KAAK4T,aAAc,OAAQ,EAE5E,CAEA,SAASyK,IACLre,KAAKie,eACLje,KAAKke,WAAW,eAAgB,OAAQ,IACvB,EAAble,KAAKkT,OACLlT,KAAKke,WAAW,UAAW,OAAQ,GAEvCle,KAAKse,aAAa,QAASte,KAAK4T,cAAc,SAAU7C,GACpD/Q,KAAKue,gBAAgBxN,EAAO,uBAAwB,OAAQ,GAC3C,EAAb/Q,KAAKkT,QACLlT,KAAKue,gBAAgBxN,EAAO,kBAAmB,OAAQ,IACvD/Q,KAAKwe,gBAAgBzN,EAAO,sBAAuBA,EAAMgD,iBAAiB,SAAU0K,GAChFze,KAAKue,gBAAgBE,EAAqB,mBAAoB,OAAQ,IACtEze,KAAKue,gBAAgBE,EAAqB,uBAAwB,OAAQ,GAC9E,IAER,GACJ,CAEA,SAASC,IAKDf,EAAW3d,KAAKwT,SAJD,CAAC,IAAM,GAAM,IAAM,EAAM,GAAM,IAAM,GAAM,IAAM,IAAM,IAAM,GAAM,GAAM,IAAM,IAAM,GAAM,QAK1GxT,KAAKie,eACDje,KAAK2e,WACL3e,KAAKa,KAAO,QAEhBb,KAAKke,WAAW,yBAA0B,OAA0B,IAAjBle,KAAKiT,QAAiB,GAAK,IAC9EjT,KAAKke,WAAW,oBAAqB,OAA0B,IAAjBle,KAAKiT,QAAiB,GAAK,KAGzE0K,EAAW3d,KAAKwT,SAZD,CAAC,IAAM,IAAM,IAAM,IAAM,IAAM,GAAM,GAAM,IAAM,IAAM,GAAM,GAAM,IAAM,IAAM,GAAM,IAAM,QAa1GxT,KAAKie,eACDje,KAAK2e,WACL3e,KAAKa,KAAO,QAEhBb,KAAKke,WAAW,iBAAkB,OAAQ,GAC1Cle,KAAKse,aAAa,QAASte,KAAK4e,gBAAgB,SAAU7N,GACtD/Q,KAAKue,gBAAgBxN,EAAO,yBAA0B,OAA0B,IAAjB/Q,KAAKiT,QAAiB,GAAK,IAC1FjT,KAAKue,gBAAgBxN,EAAO,oBAAqB,OAA0B,IAAjB/Q,KAAKiT,QAAiB,GAAK,GACzF,KAGA0K,EAAW3d,KAAKwT,SAvBC,CAAC,IAAM,GAAM,GAAM,GAAM,GAAM,IAAM,GAAM,GAAM,IAAM,GAAM,IAAM,GAAM,IAAM,IAAM,IAAM,QAwBxGxT,KAAK2e,WACL3e,KAAKa,KAAO,UAEhBwd,EAAc1e,KAAKK,MAE3B,CAEA,SAAS6e,EAAqB9X,GAE1BA,EAASA,GAAU,CAAC,EACpB,MAAMrC,EAAU1E,KAAK0E,QACfwL,EAAcnJ,EAAOmJ,YACrBC,EAAqBpJ,EAAOoJ,mBAC5BG,EAAWvJ,EAAOuJ,SAClBuE,EAAuB9N,EAAO8N,qBAC9BtE,EAAWxJ,EAAOwJ,SAClBhJ,EAAQR,EAAOQ,MACrB,IAAIuX,EACAC,EACA1a,EAoDJ,OAPAA,EAAW,CACPgS,aAvBJ,SAAsBC,GAClB,OAAOwI,EAAyBzI,aAAaC,EACjD,EAsBI0I,gBApBJ,SAAyBrV,EAAGtC,GACxB,IAAKsC,IAAMA,EAAEpB,UAAYoB,EAAEC,SACvB,MAAM,IAAI2K,MAAM,uCAGG,iBAAnB5K,EAAEpB,QAAQ1H,KAEVke,EAAyBvM,gBAAgB7I,EAAGtC,GAErCsC,EAAEpB,QAAQ1H,OAASF,EAAY2B,iCAEtCyc,EAAyBzK,kBAAkB3K,EAAGtC,GAG9CsC,EAAEmI,OAAS,KAEnB,GAxCIvB,EAAS0O,gBAAgB,OAAQP,GACjCnO,EAAS0O,gBAAgB,OAAQjB,GACjCzN,EAAS0O,gBAAgB,OAAQb,GACjC7N,EAAS0O,gBAAgB,OAAQZ,GAEjCS,EAA2BtK,EAAyB9P,GAASiC,OAAO,CAChEkO,qBAAsBA,EACtBJ,UAAW1N,EAAO0N,UAClBlE,SAAUA,IAEdwO,EAA2B9O,EAAyBvL,GAASiC,OAAO,CAChEuJ,YAAaA,EACbC,mBAAoBA,EACpBI,SAAUA,EACVD,SAAUA,EACV/I,MAAOA,EACP8I,WAAYtJ,EAAOsJ,aAiCpBhM,CACX,CAEAwa,EAAqBvZ,sBAAwB,uBAC7C,MAAelB,EAAasC,gBAAgBmY,GC5LxCK,EAAS,SAAU7Y,GAEnB,IAAI8Y,EAAO,IAAKC,EAAW,EAAGC,EAAU,iBAAkBC,EAAcC,EAAaF,GACjFG,EAAmB,uCACnBC,EAAyC,mBAAXC,OAElC,SAASC,EAAQC,EAAGC,EAAOC,EAAUC,GACjC,YAAiB,IAANH,EAA0BD,EAAQ,QACxB,IAAVE,GAAyC,KAAVA,IAAiBC,EACpDE,EAAWJ,GADoEK,EAAUL,EAAGC,EAAOC,EAAUC,EAExH,CAEA,SAASG,EAAWpgB,EAAOqgB,GACvBngB,KAAKF,MAAQA,EACbE,KAAKmgB,KAAOA,EACZngB,KAAKogB,SAAU,CACnB,CAIA,SAASC,EAAavgB,GAClBE,KAAKF,MAAQA,EACbE,KAAKmgB,KAAOrgB,EAAQ,EACpBE,KAAKogB,SAAU,CACnB,CAIA,SAASE,EAAaxgB,GAClBE,KAAKF,MAAQA,CACjB,CAIA,SAASygB,EAAU7E,GACf,OAAQ2D,EAAU3D,GAAKA,EAAI2D,CAC/B,CAEA,SAASE,EAAa7D,GAClB,OAAIA,EAAI,IAAY,CAACA,GACjBA,EAAI,KAAa,CAACA,EAAI,IAAKvR,KAAKgJ,MAAMuI,EAAI,MACvC,CAACA,EAAI,IAAKvR,KAAKgJ,MAAMuI,EAAI,KAAO,IAAKvR,KAAKgJ,MAAMuI,EAAI,MAC/D,CAEA,SAAS8E,EAAaC,GAClBC,EAAKD,GACL,IAAIvX,EAASuX,EAAIvX,OACjB,GAAIA,EAAS,GAAKyX,EAAWF,EAAKnB,GAAe,EAC7C,OAAQpW,GACJ,KAAK,EACD,OAAO,EACX,KAAK,EACD,OAAOuX,EAAI,GACf,KAAK,EACD,OAAOA,EAAI,GAAKA,EAAI,GAAKtB,EAC7B,QACI,OAAOsB,EAAI,IAAMA,EAAI,GAAKA,EAAI,GAAKtB,GAAQA,EAGvD,OAAOsB,CACX,CAEA,SAASC,EAAKd,GAEV,IADA,IAAIhb,EAAIgb,EAAE1W,OACQ,IAAX0W,IAAIhb,KACXgb,EAAE1W,OAAStE,EAAI,CACnB,CAEA,SAASgc,EAAY1X,GAGjB,IAFA,IAAI/C,EAAI,IAAI0a,MAAM3X,GACdtE,GAAK,IACAA,EAAIsE,GACT/C,EAAEvB,GAAK,EAEX,OAAOuB,CACX,CAEA,SAAS2a,EAASpF,GACd,OAAIA,EAAI,EAAUvR,KAAKgJ,MAAMuI,GACtBvR,KAAK4W,KAAKrF,EACrB,CAEA,SAASsF,EAAIC,EAAGC,GACZ,IAAgFC,EAAKvc,EAAjFwc,EAAMH,EAAE/X,OAAQmY,EAAMH,EAAEhY,OAAQoY,EAAI,IAAIT,MAAMO,GAAMG,EAAQ,EAAGC,EAAOrC,EAC1E,IAAKva,EAAI,EAAGA,EAAIyc,EAAKzc,IAEjB2c,GADAJ,EAAMF,EAAErc,GAAKsc,EAAEtc,GAAK2c,IACLC,EAAO,EAAI,EAC1BF,EAAE1c,GAAKuc,EAAMI,EAAQC,EAEzB,KAAO5c,EAAIwc,GAEPG,GADAJ,EAAMF,EAAErc,GAAK2c,KACGC,EAAO,EAAI,EAC3BF,EAAE1c,KAAOuc,EAAMI,EAAQC,EAG3B,OADID,EAAQ,GAAGD,EAAEtb,KAAKub,GACfD,CACX,CAEA,SAASG,EAAOR,EAAGC,GACf,OAAID,EAAE/X,QAAUgY,EAAEhY,OAAe8X,EAAIC,EAAGC,GACjCF,EAAIE,EAAGD,EAClB,CAEA,SAASS,EAAST,EAAGM,GACjB,IAAiDJ,EAAKvc,EAAlD+c,EAAIV,EAAE/X,OAAQoY,EAAI,IAAIT,MAAMc,GAAIH,EAAOrC,EAC3C,IAAKva,EAAI,EAAGA,EAAI+c,EAAG/c,IACfuc,EAAMF,EAAErc,GAAK4c,EAAOD,EACpBA,EAAQpX,KAAKgJ,MAAMgO,EAAMK,GACzBF,EAAE1c,GAAKuc,EAAMI,EAAQC,EACrBD,GAAS,EAEb,KAAOA,EAAQ,GACXD,EAAE1c,KAAO2c,EAAQC,EACjBD,EAAQpX,KAAKgJ,MAAMoO,EAAQC,GAE/B,OAAOF,CACX,CAiCA,SAASM,EAASX,EAAGC,GACjB,IAAiFtc,EAAGid,EAAhFC,EAAMb,EAAE/X,OAAQ6Y,EAAMb,EAAEhY,OAAQoY,EAAI,IAAIT,MAAMiB,GAAME,EAAS,EAAGR,EAAOrC,EAC3E,IAAKva,EAAI,EAAGA,EAAImd,EAAKnd,KACjBid,EAAaZ,EAAErc,GAAKod,EAASd,EAAEtc,IACd,GACbid,GAAcL,EACdQ,EAAS,GACNA,EAAS,EAChBV,EAAE1c,GAAKid,EAEX,IAAKjd,EAAImd,EAAKnd,EAAIkd,EAAKld,IAAK,CAExB,MADAid,EAAaZ,EAAErc,GAAKod,GACH,GAA4B,CACzCV,EAAE1c,KAAOid,EACT,KACJ,CAHoBA,GAAcL,EAIlCF,EAAE1c,GAAKid,CACX,CACA,KAAOjd,EAAIkd,EAAKld,IACZ0c,EAAE1c,GAAKqc,EAAErc,GAGb,OADA8b,EAAKY,GACEA,CACX,CAkBA,SAASW,EAAchB,EAAGC,EAAGf,GACzB,IAA6Dvb,EAAGid,EAA5DF,EAAIV,EAAE/X,OAAQoY,EAAI,IAAIT,MAAMc,GAAIJ,GAASL,EAAGM,EAAOrC,EACvD,IAAKva,EAAI,EAAGA,EAAI+c,EAAG/c,IACfid,EAAaZ,EAAErc,GAAK2c,EACpBA,EAAQpX,KAAKgJ,MAAM0O,EAAaL,GAChCK,GAAcL,EACdF,EAAE1c,GAAKid,EAAa,EAAIA,EAAaL,EAAOK,EAGhD,MAAiB,iBADjBP,EAAId,EAAac,KAETnB,IAAMmB,GAAKA,GACR,IAAIjB,EAAaiB,IAErB,IAAIpB,EAAWoB,EAAGnB,EAC7B,CAmDA,SAAS+B,EAAajB,EAAGC,GACrB,IAAoFiB,EAASZ,EAAO3c,EAAGwd,EAAnGN,EAAMb,EAAE/X,OAAQ6Y,EAAMb,EAAEhY,OAAuBoY,EAAIV,EAAfkB,EAAMC,GAAyBP,EAAOrC,EAC9E,IAAKva,EAAI,EAAGA,EAAIkd,IAAOld,EAAG,CACtBwd,EAAMnB,EAAErc,GACR,IAAK,IAAIyd,EAAI,EAAGA,EAAIN,IAAOM,EAEvBF,EAAUC,EADJlB,EAAEmB,GACcf,EAAE1c,EAAIyd,GAC5Bd,EAAQpX,KAAKgJ,MAAMgP,EAAUX,GAC7BF,EAAE1c,EAAIyd,GAAKF,EAAUZ,EAAQC,EAC7BF,EAAE1c,EAAIyd,EAAI,IAAMd,CAExB,CAEA,OADAb,EAAKY,GACEA,CACX,CAEA,SAASgB,EAAcrB,EAAGC,GACtB,IAA4DiB,EAASvd,EAAjE+c,EAAIV,EAAE/X,OAAQoY,EAAI,IAAIT,MAAMc,GAAIH,EAAOrC,EAAMoC,EAAQ,EACzD,IAAK3c,EAAI,EAAGA,EAAI+c,EAAG/c,IACfud,EAAUlB,EAAErc,GAAKsc,EAAIK,EACrBA,EAAQpX,KAAKgJ,MAAMgP,EAAUX,GAC7BF,EAAE1c,GAAKud,EAAUZ,EAAQC,EAE7B,KAAOD,EAAQ,GACXD,EAAE1c,KAAO2c,EAAQC,EACjBD,EAAQpX,KAAKgJ,MAAMoO,EAAQC,GAE/B,OAAOF,CACX,CAEA,SAASiB,EAAUpc,EAAGuV,GAElB,IADA,IAAI4F,EAAI,GACD5F,KAAM,GAAG4F,EAAEtb,KAAK,GACvB,OAAOsb,EAAEkB,OAAOrc,EACpB,CAEA,SAASsc,EAAkBtc,EAAGuc,GAC1B,IAAIhH,EAAIvR,KAAKC,IAAIjE,EAAE+C,OAAQwZ,EAAExZ,QAC7B,GAAIwS,GAAK,GAAI,OAAOwG,EAAa/b,EAAGuc,GACpChH,EAAIvR,KAAK4W,KAAKrF,EAAI,GAClB,IAAIwF,EAAI/a,EAAEqV,MAAME,GAAIuF,EAAI9a,EAAEqV,MAAM,EAAGE,GAAIhT,EAAIga,EAAElH,MAAME,GAAIiH,EAAID,EAAElH,MAAM,EAAGE,GAClEkH,EAAKH,EAAkBxB,EAAG0B,GAAIE,EAAKJ,EAAkBvB,EAAGxY,GACxDoa,EAAOL,EAAkBhB,EAAOR,EAAGC,GAAIO,EAAOkB,EAAGja,IACjDyZ,EAAUV,EAAOA,EAAOmB,EAAIL,EAAUX,EAASA,EAASkB,EAAMF,GAAKC,GAAKnH,IAAK6G,EAAUM,EAAI,EAAInH,IAEnG,OADAgF,EAAKyB,GACEA,CACX,CAuBA,SAASY,EAAsB9B,EAAGC,EAAGf,GACjC,OACW,IAAID,EADXe,EAAI9B,EACkBmD,EAAcpB,EAAGD,GAErBiB,EAAahB,EAAG3B,EAAa0B,IAFJd,EAGnD,CAuBA,SAAS6C,EAAO/B,GACZ,IAAuDkB,EAASZ,EAAO3c,EAAGwd,EAAtET,EAAIV,EAAE/X,OAAQoY,EAAIV,EAAYe,EAAIA,GAAIH,EAAOrC,EACjD,IAAKva,EAAI,EAAGA,EAAI+c,EAAG/c,IAAK,CAEpB2c,EAAQ,GADRa,EAAMnB,EAAErc,IACUwd,EAClB,IAAK,IAAIC,EAAIzd,EAAGyd,EAAIV,EAAGU,IAEnBF,EAAeC,EADTnB,EAAEoB,GACE,EAAkBf,EAAE1c,EAAIyd,GAAKd,EACvCA,EAAQpX,KAAKgJ,MAAMgP,EAAUX,GAC7BF,EAAE1c,EAAIyd,GAAKF,EAAUZ,EAAQC,EAEjCF,EAAE1c,EAAI+c,GAAKJ,CACf,CAEA,OADAb,EAAKY,GACEA,CACX,CA4FA,SAAS2B,EAAYnjB,EAAOojB,GACxB,IAAwEte,EAAGue,EAAGC,EAAWC,EAArFna,EAASpJ,EAAMoJ,OAAQoa,EAAW1C,EAAY1X,GAASsY,EAAOrC,EAElE,IADAiE,EAAY,EACPxe,EAAIsE,EAAS,EAAGtE,GAAK,IAAKA,EAG3Bwe,GAFAC,EAAUD,EAAY5B,EAAO1hB,EAAM8E,KACnCue,EAAIrC,EAASuC,EAAUH,IACGA,EAC1BI,EAAS1e,GAAS,EAAJue,EAElB,MAAO,CAACG,EAAsB,EAAZF,EACtB,CAEA,SAASG,EAAUC,EAAM5D,GACrB,IAAI9f,EAAO4b,EAAIsE,EAAWJ,GAC1B,GAAIH,EACA,MAAO,CAAC,IAAIa,EAAakD,EAAK1jB,MAAQ4b,EAAE5b,OAAQ,IAAIwgB,EAAakD,EAAK1jB,MAAQ4b,EAAE5b,QAEpF,IACIwjB,EADArC,EAAIuC,EAAK1jB,MAAOohB,EAAIxF,EAAE5b,MAE1B,GAAU,IAANohB,EAAS,MAAM,IAAI3M,MAAM,yBAC7B,GAAIiP,EAAKpD,QACL,OAAI1E,EAAE0E,QACK,CAAC,IAAIC,EAAaS,EAASG,EAAIC,IAAK,IAAIb,EAAaY,EAAIC,IAE7D,CAACvB,EAAQ,GAAI6D,GAExB,GAAI9H,EAAE0E,QAAS,CACX,GAAU,IAANc,EAAS,MAAO,CAACsC,EAAM7D,EAAQ,IACnC,IAAU,GAANuB,EAAS,MAAO,CAACsC,EAAKC,SAAU9D,EAAQ,IAC5C,IAAI+D,EAAMvZ,KAAKuZ,IAAIxC,GACnB,GAAIwC,EAAMvE,EAAM,CAEZmE,EAAW9C,GADX1gB,EAAQmjB,EAAYhC,EAAGyC,IACO,IAC9B,IAAIN,EAAYtjB,EAAM,GAEtB,OADI0jB,EAAKrD,OAAMiD,GAAaA,GACJ,iBAAbE,GACHE,EAAKrD,OAASzE,EAAEyE,OAAMmD,GAAYA,GAC/B,CAAC,IAAIjD,EAAaiD,GAAW,IAAIjD,EAAa+C,KAElD,CAAC,IAAIlD,EAAWoD,EAAUE,EAAKrD,OAASzE,EAAEyE,MAAO,IAAIE,EAAa+C,GAC7E,CACAlC,EAAI3B,EAAamE,EACrB,CACA,IAAIC,EAAahD,EAAWM,EAAGC,GAC/B,IAAoB,IAAhByC,EAAmB,MAAO,CAAChE,EAAQ,GAAI6D,GAC3C,GAAmB,IAAfG,EAAkB,MAAO,CAAChE,EAAQ6D,EAAKrD,OAASzE,EAAEyE,KAAO,GAAK,GAAIR,EAAQ,IAC9C7f,EAA5BmhB,EAAE/X,OAASgY,EAAEhY,QAAU,IA5H/B,SAAiB+X,EAAGC,GAChB,IAE8E0C,EAAeC,EAAOtC,EAChGS,EAAQpd,EAAG+c,EAAGwB,EAHdrB,EAAMb,EAAE/X,OAAQ6Y,EAAMb,EAAEhY,OAAQsY,EAAOrC,EAAM2E,EAASlD,EAAYM,EAAEhY,QACpE6a,EAA8B7C,EAAEa,EAAM,GAAImB,EAAS/Y,KAAK4W,KAAKS,GAAQ,EAAIuC,IACzEX,EAAYd,EAAcrB,EAAGiC,GAASG,EAAUf,EAAcpB,EAAGgC,GAKrE,IAHIE,EAAUla,QAAU4Y,GAAKsB,EAAUpd,KAAK,GAC5Cqd,EAAQrd,KAAK,GACb+d,EAA8BV,EAAQtB,EAAM,GACvC8B,EAAQ/B,EAAMC,EAAK8B,GAAS,EAAGA,IAAS,CAQzC,IAPAD,EAAgBpC,EAAO,EACnB4B,EAAUS,EAAQ9B,KAASgC,IAC3BH,EAAgBzZ,KAAKgJ,OAAOiQ,EAAUS,EAAQ9B,GAAOP,EAAO4B,EAAUS,EAAQ9B,EAAM,IAAMgC,IAE9FxC,EAAQ,EACRS,EAAS,EACTL,EAAI0B,EAAQna,OACPtE,EAAI,EAAGA,EAAI+c,EAAG/c,IACf2c,GAASqC,EAAgBP,EAAQze,GACjCue,EAAIhZ,KAAKgJ,MAAMoO,EAAQC,GACvBQ,GAAUoB,EAAUS,EAAQjf,IAAM2c,EAAQ4B,EAAI3B,GAC9CD,EAAQ4B,EACJnB,EAAS,GACToB,EAAUS,EAAQjf,GAAKod,EAASR,EAChCQ,GAAU,IAEVoB,EAAUS,EAAQjf,GAAKod,EACvBA,EAAS,GAGjB,KAAkB,IAAXA,GAAc,CAGjB,IAFA4B,GAAiB,EACjBrC,EAAQ,EACH3c,EAAI,EAAGA,EAAI+c,EAAG/c,KACf2c,GAAS6B,EAAUS,EAAQjf,GAAK4c,EAAO6B,EAAQze,IACnC,GACRwe,EAAUS,EAAQjf,GAAK2c,EAAQC,EAC/BD,EAAQ,IAER6B,EAAUS,EAAQjf,GAAK2c,EACvBA,EAAQ,GAGhBS,GAAUT,CACd,CACAuC,EAAOD,GAASD,CACpB,CAEA,OADAR,EAAYH,EAAYG,EAAWF,GAAQ,GACpC,CAAC1C,EAAasD,GAAStD,EAAa4C,GAC/C,CA4E4CY,CAAQ/C,EAAGC,GA1EvD,SAAiBD,EAAGC,GAEhB,IADA,IAAyE+C,EAAOC,EAAMC,EAAOC,EAAOC,EAAhGvC,EAAMb,EAAE/X,OAAQ6Y,EAAMb,EAAEhY,OAAQ4a,EAAS,GAAIQ,EAAO,GAAI9C,EAAOrC,EAC5D2C,GAGH,GAFAwC,EAAKC,QAAQtD,IAAIa,IACjBpB,EAAK4D,GACD3D,EAAW2D,EAAMpD,GAAK,EACtB4C,EAAO9d,KAAK,OADhB,CAKAme,EAAQG,GADRJ,EAAOI,EAAKpb,QACQ,GAAKsY,EAAO8C,EAAKJ,EAAO,GAC5CE,EAAQlD,EAAEa,EAAM,GAAKP,EAAON,EAAEa,EAAM,GAChCmC,EAAOnC,IACPoC,GAASA,EAAQ,GAAK3C,GAE1ByC,EAAQ9Z,KAAK4W,KAAKoD,EAAQC,GAC1B,EAAG,CAEC,GAAIzD,EADJ0D,EAAQ/B,EAAcpB,EAAG+C,GACHK,IAAS,EAAG,MAClCL,GACJ,OAASA,GACTH,EAAO9d,KAAKie,GACZK,EAAO1C,EAAS0C,EAAMD,EAdtB,CAiBJ,OADAP,EAAOU,UACA,CAAChE,EAAasD,GAAStD,EAAa8D,GAC/C,CAgDwEG,CAAQxD,EAAGC,GAC/EoC,EAAWxjB,EAAM,GACjB,IAAI4kB,EAAQlB,EAAKrD,OAASzE,EAAEyE,KAAMwE,EAAM7kB,EAAM,GAAI8kB,EAAQpB,EAAKrD,KAS/D,MARwB,iBAAbmD,GACHoB,IAAOpB,GAAYA,GACvBA,EAAW,IAAIjD,EAAaiD,IACzBA,EAAW,IAAIpD,EAAWoD,EAAUoB,GACxB,iBAARC,GACHC,IAAOD,GAAOA,GAClBA,EAAM,IAAItE,EAAasE,IACpBA,EAAM,IAAIzE,EAAWyE,EAAKC,GAC1B,CAACtB,EAAUqB,EACtB,CAqFA,SAAShE,EAAWM,EAAGC,GACnB,GAAID,EAAE/X,SAAWgY,EAAEhY,OACf,OAAO+X,EAAE/X,OAASgY,EAAEhY,OAAS,GAAK,EAEtC,IAAK,IAAItE,EAAIqc,EAAE/X,OAAS,EAAGtE,GAAK,EAAGA,IAC/B,GAAIqc,EAAErc,KAAOsc,EAAEtc,GAAI,OAAOqc,EAAErc,GAAKsc,EAAEtc,GAAK,GAAK,EAEjD,OAAO,CACX,CAuJA,SAASigB,EAAajF,GAClB,IAAIlE,EAAIkE,EAAE8D,MACV,OAAIhI,EAAEoJ,cACFpJ,EAAEqJ,OAAO,IAAMrJ,EAAEqJ,OAAO,IAAMrJ,EAAEqJ,OAAO,OACvCrJ,EAAEsJ,UAAYtJ,EAAEuJ,cAAc,IAAMvJ,EAAEuJ,cAAc,QACpDvJ,EAAEwJ,OAAO,UAAb,GACJ,CAEA,SAASC,EAAgBzJ,EAAGuF,GAExB,IADA,IAAwCvY,EAAM9D,EAAGuB,EAA7Cif,EAAQ1J,EAAE2J,OAAQnE,EAAIkE,EAAO9D,EAAI,EAC9BJ,EAAE8D,UAAU9D,EAAIA,EAAEoE,OAAO,GAAIhE,IACpCiE,EAAK,IAAK3gB,EAAI,EAAGA,EAAIqc,EAAE/X,OAAQtE,IAC3B,IAAI8W,EAAEwJ,OAAOjE,EAAErc,OACfuB,EAAI+Y,EAAO+B,EAAErc,IAAI4gB,OAAOtE,EAAGxF,IACrBoJ,WAAY3e,EAAE4e,OAAOK,GAA3B,CACA,IAAK1c,EAAI4Y,EAAI,EAAQ,GAAL5Y,EAAQA,IAAK,CAEzB,IADAvC,EAAIA,EAAE6c,SAAS2B,IAAIjJ,IACboJ,SAAU,OAAO,EACvB,GAAI3e,EAAE4e,OAAOK,GAAQ,SAASG,CAClC,CACA,OAAO,CANoC,CAQ/C,OAAO,CACX,CA9vBArF,EAAWzgB,UAAYN,OAAOwH,OAAOgZ,EAAQlgB,WAQ7C4gB,EAAa5gB,UAAYN,OAAOwH,OAAOgZ,EAAQlgB,WAM/C6gB,EAAa7gB,UAAYN,OAAOwH,OAAOgZ,EAAQlgB,WAsF/CygB,EAAWzgB,UAAUuhB,IAAM,SAAUpB,GACjC,IAAIlE,EAAIsE,EAAWJ,GACnB,GAAI5f,KAAKmgB,OAASzE,EAAEyE,KAChB,OAAOngB,KAAK4hB,SAASlG,EAAE+H,UAE3B,IAAIxC,EAAIjhB,KAAKF,MAAOohB,EAAIxF,EAAE5b,MAC1B,OAAI4b,EAAE0E,QACK,IAAIF,EAAWwB,EAAST,EAAG9W,KAAKuZ,IAAIxC,IAAKlhB,KAAKmgB,MAElD,IAAID,EAAWuB,EAAOR,EAAGC,GAAIlhB,KAAKmgB,KAC7C,EACAD,EAAWzgB,UAAUgmB,KAAOvF,EAAWzgB,UAAUuhB,IACjDX,EAAa5gB,UAAUuhB,IAAM,SAAUpB,GACnC,IAAIlE,EAAIsE,EAAWJ,GACfqB,EAAIjhB,KAAKF,MACb,GAAImhB,EAAI,IAAMvF,EAAEyE,KACZ,OAAOngB,KAAK4hB,SAASlG,EAAE+H,UAE3B,IAAIvC,EAAIxF,EAAE5b,MACV,GAAI4b,EAAE0E,QAAS,CACX,GAAIG,EAAUU,EAAIC,GAAI,OAAO,IAAIb,EAAaY,EAAIC,GAClDA,EAAI3B,EAAapV,KAAKuZ,IAAIxC,GAC9B,CACA,OAAO,IAAIhB,EAAWwB,EAASR,EAAG/W,KAAKuZ,IAAIzC,IAAKA,EAAI,EACxD,EACAZ,EAAa5gB,UAAUgmB,KAAOpF,EAAa5gB,UAAUuhB,IACrDV,EAAa7gB,UAAUuhB,IAAM,SAAUpB,GACnC,OAAO,IAAIU,EAAatgB,KAAKF,MAAQkgB,EAAWJ,GAAG9f,MACvD,EACAwgB,EAAa7gB,UAAUgmB,KAAOnF,EAAa7gB,UAAUuhB,IA2DrDd,EAAWzgB,UAAUmiB,SAAW,SAAUhC,GACtC,IAAIlE,EAAIsE,EAAWJ,GACnB,GAAI5f,KAAKmgB,OAASzE,EAAEyE,KAChB,OAAOngB,KAAKghB,IAAItF,EAAE+H,UAEtB,IAAIxC,EAAIjhB,KAAKF,MAAOohB,EAAIxF,EAAE5b,MAC1B,OAAI4b,EAAE0E,QAAgB6B,EAAchB,EAAG9W,KAAKuZ,IAAIxC,GAAIlhB,KAAKmgB,MAtC7D,SAAqBc,EAAGC,EAAGf,GACvB,IAAIrgB,EAQJ,OAPI6gB,EAAWM,EAAGC,IAAM,EACpBphB,EAAQ8hB,EAASX,EAAGC,IAEpBphB,EAAQ8hB,EAASV,EAAGD,GACpBd,GAAQA,GAGS,iBADrBrgB,EAAQ0gB,EAAa1gB,KAEbqgB,IAAMrgB,GAASA,GACZ,IAAIugB,EAAavgB,IAErB,IAAIogB,EAAWpgB,EAAOqgB,EACjC,CAyBWuF,CAAYzE,EAAGC,EAAGlhB,KAAKmgB,KAClC,EACAD,EAAWzgB,UAAUkmB,MAAQzF,EAAWzgB,UAAUmiB,SAClDvB,EAAa5gB,UAAUmiB,SAAW,SAAUhC,GACxC,IAAIlE,EAAIsE,EAAWJ,GACfqB,EAAIjhB,KAAKF,MACb,GAAImhB,EAAI,IAAMvF,EAAEyE,KACZ,OAAOngB,KAAKghB,IAAItF,EAAE+H,UAEtB,IAAIvC,EAAIxF,EAAE5b,MACV,OAAI4b,EAAE0E,QACK,IAAIC,EAAaY,EAAIC,GAEzBe,EAAcf,EAAG/W,KAAKuZ,IAAIzC,GAAIA,GAAK,EAC9C,EACAZ,EAAa5gB,UAAUkmB,MAAQtF,EAAa5gB,UAAUmiB,SACtDtB,EAAa7gB,UAAUmiB,SAAW,SAAUhC,GACxC,OAAO,IAAIU,EAAatgB,KAAKF,MAAQkgB,EAAWJ,GAAG9f,MACvD,EACAwgB,EAAa7gB,UAAUkmB,MAAQrF,EAAa7gB,UAAUmiB,SACtD1B,EAAWzgB,UAAUgkB,OAAS,WAC1B,OAAO,IAAIvD,EAAWlgB,KAAKF,OAAQE,KAAKmgB,KAC5C,EACAE,EAAa5gB,UAAUgkB,OAAS,WAC5B,IAAItD,EAAOngB,KAAKmgB,KACZyF,EAAQ,IAAIvF,GAAcrgB,KAAKF,OAEnC,OADA8lB,EAAMzF,MAAQA,EACPyF,CACX,EACAtF,EAAa7gB,UAAUgkB,OAAS,WAC5B,OAAO,IAAInD,GAActgB,KAAKF,MAClC,EACAogB,EAAWzgB,UAAUikB,IAAM,WACvB,OAAO,IAAIxD,EAAWlgB,KAAKF,OAAO,EACtC,EACAugB,EAAa5gB,UAAUikB,IAAM,WACzB,OAAO,IAAIrD,EAAalW,KAAKuZ,IAAI1jB,KAAKF,OAC1C,EACAwgB,EAAa7gB,UAAUikB,IAAM,WACzB,OAAO,IAAIpD,EAAatgB,KAAKF,OAAS,EAAIE,KAAKF,OAASE,KAAKF,MACjE,EAsDAogB,EAAWzgB,UAAUomB,SAAW,SAAUjG,GACtC,IAAiF8D,EAL/DoC,EAAIC,EAKlBrK,EAAIsE,EAAWJ,GAAIqB,EAAIjhB,KAAKF,MAAOohB,EAAIxF,EAAE5b,MAAOqgB,EAAOngB,KAAKmgB,OAASzE,EAAEyE,KAC3E,GAAIzE,EAAE0E,QAAS,CACX,GAAU,IAANc,EAAS,OAAOvB,EAAQ,GAC5B,GAAU,IAANuB,EAAS,OAAOlhB,KACpB,IAAW,IAAPkhB,EAAU,OAAOlhB,KAAKyjB,SAE1B,IADAC,EAAMvZ,KAAKuZ,IAAIxC,IACL/B,EACN,OAAO,IAAIe,EAAWoC,EAAcrB,EAAGyC,GAAMvD,GAEjDe,EAAI3B,EAAamE,EACrB,CACA,OAA6C,IAAIxD,GAfzC,MADU4F,EAgBD7E,EAAE/X,QAfC,MADE6c,EAgBK7E,EAAEhY,QAfG,MAAQ4c,EAAKC,EAAK,EAeUtD,EAAkBxB,EAAGC,GAC3DgB,EAAajB,EAAGC,GAD+Cf,EAEzF,EACAD,EAAWzgB,UAAUumB,MAAQ9F,EAAWzgB,UAAUomB,SASlDxF,EAAa5gB,UAAUwmB,iBAAmB,SAAUhF,GAChD,OAAIV,EAAUU,EAAEnhB,MAAQE,KAAKF,OAClB,IAAIugB,EAAaY,EAAEnhB,MAAQE,KAAKF,OAEpCijB,EAAsB5Y,KAAKuZ,IAAIzC,EAAEnhB,OAAQyf,EAAapV,KAAKuZ,IAAI1jB,KAAKF,QAASE,KAAKmgB,OAASc,EAAEd,KACxG,EACAD,EAAWzgB,UAAUwmB,iBAAmB,SAAUhF,GAC9C,OAAgB,IAAZA,EAAEnhB,MAAoB6f,EAAQ,GAClB,IAAZsB,EAAEnhB,MAAoBE,MACT,IAAbihB,EAAEnhB,MAAqBE,KAAKyjB,SACzBV,EAAsB5Y,KAAKuZ,IAAIzC,EAAEnhB,OAAQE,KAAKF,MAAOE,KAAKmgB,OAASc,EAAEd,KAChF,EACAE,EAAa5gB,UAAUomB,SAAW,SAAUjG,GACxC,OAAOI,EAAWJ,GAAGqG,iBAAiBjmB,KAC1C,EACAqgB,EAAa5gB,UAAUumB,MAAQ3F,EAAa5gB,UAAUomB,SACtDvF,EAAa7gB,UAAUomB,SAAW,SAAUjG,GACxC,OAAO,IAAIU,EAAatgB,KAAKF,MAAQkgB,EAAWJ,GAAG9f,MACvD,EACAwgB,EAAa7gB,UAAUumB,MAAQ1F,EAAa7gB,UAAUomB,SAmBtD3F,EAAWzgB,UAAUujB,OAAS,WAC1B,OAAO,IAAI9C,EAAW8C,EAAOhjB,KAAKF,QAAQ,EAC9C,EACAugB,EAAa5gB,UAAUujB,OAAS,WAC5B,IAAIljB,EAAQE,KAAKF,MAAQE,KAAKF,MAC9B,OAAIygB,EAAUzgB,GAAe,IAAIugB,EAAavgB,GACvC,IAAIogB,EAAW8C,EAAOzD,EAAapV,KAAKuZ,IAAI1jB,KAAKF,UAAU,EACtE,EACAwgB,EAAa7gB,UAAUujB,OAAS,SAAUpD,GACtC,OAAO,IAAIU,EAAatgB,KAAKF,MAAQE,KAAKF,MAC9C,EA4IAogB,EAAWzgB,UAAUymB,OAAS,SAAUtG,GACpC,IAAIkE,EAASP,EAAUvjB,KAAM4f,GAC7B,MAAO,CAAE0D,SAAUQ,EAAO,GAAIV,UAAWU,EAAO,GACpD,EACAxD,EAAa7gB,UAAUymB,OAAS7F,EAAa5gB,UAAUymB,OAAShG,EAAWzgB,UAAUymB,OACrFhG,EAAWzgB,UAAU6lB,OAAS,SAAU1F,GACpC,OAAO2D,EAAUvjB,KAAM4f,GAAG,EAC9B,EACAU,EAAa7gB,UAAU0mB,KAAO7F,EAAa7gB,UAAU6lB,OAAS,SAAU1F,GACpE,OAAO,IAAIU,EAAatgB,KAAKF,MAAQkgB,EAAWJ,GAAG9f,MACvD,EACAugB,EAAa5gB,UAAU0mB,KAAO9F,EAAa5gB,UAAU6lB,OAASpF,EAAWzgB,UAAU0mB,KAAOjG,EAAWzgB,UAAU6lB,OAC/GpF,EAAWzgB,UAAUklB,IAAM,SAAU/E,GACjC,OAAO2D,EAAUvjB,KAAM4f,GAAG,EAC9B,EACAU,EAAa7gB,UAAUklB,IAAMrE,EAAa7gB,UAAU2jB,UAAY,SAAUxD,GACtE,OAAO,IAAIU,EAAatgB,KAAKF,MAAQkgB,EAAWJ,GAAG9f,MACvD,EACAugB,EAAa5gB,UAAU2jB,UAAY/C,EAAa5gB,UAAUklB,IAAMzE,EAAWzgB,UAAU2jB,UAAYlD,EAAWzgB,UAAUklB,IACtHzE,EAAWzgB,UAAU2mB,IAAM,SAAUxG,GACjC,IAAoD9f,EAAOqG,EAAGuc,EAA1DhH,EAAIsE,EAAWJ,GAAIqB,EAAIjhB,KAAKF,MAAOohB,EAAIxF,EAAE5b,MAC7C,GAAU,IAANohB,EAAS,OAAOvB,EAAQ,GAC5B,GAAU,IAANsB,EAAS,OAAOtB,EAAQ,GAC5B,GAAU,IAANsB,EAAS,OAAOtB,EAAQ,GAC5B,IAAW,IAAPsB,EAAU,OAAOvF,EAAEsJ,SAAWrF,EAAQ,GAAKA,GAAS,GACxD,GAAIjE,EAAEyE,KACF,OAAOR,EAAQ,GAEnB,IAAKjE,EAAE0E,QAAS,MAAM,IAAI7L,MAAM,gBAAkBmH,EAAE2K,WAAa,kBACjE,GAAIrmB,KAAKogB,SACDG,EAAUzgB,EAAQqK,KAAKic,IAAInF,EAAGC,IAAK,OAAO,IAAIb,EAAaS,EAAShhB,IAI5E,IAFAqG,EAAInG,KACJ0iB,EAAI/C,EAAQ,IAEA,EAAJuB,IACAwB,EAAIA,EAAEsD,MAAM7f,KACV+a,GAEI,IAANA,GACJA,GAAK,EACL/a,EAAIA,EAAE6c,SAEV,OAAON,CACX,EACArC,EAAa5gB,UAAU2mB,IAAMlG,EAAWzgB,UAAU2mB,IAClD9F,EAAa7gB,UAAU2mB,IAAM,SAAUxG,GACnC,IAAIlE,EAAIsE,EAAWJ,GACfqB,EAAIjhB,KAAKF,MAAOohB,EAAIxF,EAAE5b,MACtBwmB,EAAK5G,OAAO,GAAI6G,EAAK7G,OAAO,GAAI8G,EAAK9G,OAAO,GAChD,GAAIwB,IAAMoF,EAAI,OAAO3G,EAAQ,GAC7B,GAAIsB,IAAMqF,EAAI,OAAO3G,EAAQ,GAC7B,GAAIsB,IAAMsF,EAAI,OAAO5G,EAAQ,GAC7B,GAAIsB,IAAMvB,QAAQ,GAAI,OAAOhE,EAAEsJ,SAAWrF,EAAQ,GAAKA,GAAS,GAChE,GAAIjE,EAAE+K,aAAc,OAAO,IAAInG,EAAagG,GAG5C,IAFA,IAAIngB,EAAInG,KACJ0iB,EAAI/C,EAAQ,IAEPuB,EAAIqF,KAAQA,IACb7D,EAAIA,EAAEsD,MAAM7f,KACV+a,GAEFA,IAAMoF,GACVpF,GAAKsF,EACLrgB,EAAIA,EAAE6c,SAEV,OAAON,CACX,EACAxC,EAAWzgB,UAAU+lB,OAAS,SAAUkB,EAAK/B,GAGzC,GAFA+B,EAAM1G,EAAW0G,IACjB/B,EAAM3E,EAAW2E,IACTgC,SAAU,MAAM,IAAIpS,MAAM,qCAElC,IADA,IAAI+M,EAAI3B,EAAQ,GAAI6B,EAAOxhB,KAAK2kB,IAAIA,GAC7B+B,EAAIE,cAAc,CACrB,GAAIpF,EAAKmF,SAAU,OAAOhH,EAAQ,GAC9B+G,EAAIG,UAASvF,EAAIA,EAAEuE,SAASrE,GAAMmD,IAAIA,IAC1C+B,EAAMA,EAAIpB,OAAO,GACjB9D,EAAOA,EAAKwB,SAAS2B,IAAIA,EAC7B,CACA,OAAOrD,CACX,EACAhB,EAAa7gB,UAAU+lB,OAASnF,EAAa5gB,UAAU+lB,OAAStF,EAAWzgB,UAAU+lB,OAYrFtF,EAAWzgB,UAAUkhB,WAAa,SAAUf,GACxC,IAAIlE,EAAIsE,EAAWJ,GAAIqB,EAAIjhB,KAAKF,MAAOohB,EAAIxF,EAAE5b,MAC7C,OAAI4b,EAAE0E,QAAgB,EACfO,EAAWM,EAAGC,EACzB,EACAb,EAAa5gB,UAAUkhB,WAAa,SAAUf,GAC1C,IAAIlE,EAAIsE,EAAWJ,GAAIqB,EAAI9W,KAAKuZ,IAAI1jB,KAAKF,OAAQohB,EAAIxF,EAAE5b,MACvD,OAAI4b,EAAE0E,QAEKa,KADPC,EAAI/W,KAAKuZ,IAAIxC,IACI,EAAID,EAAIC,EAAI,GAAK,GAE9B,CACZ,EACAZ,EAAa7gB,UAAUkhB,WAAa,SAAUf,GAC1C,IAAIqB,EAAIjhB,KAAKF,MACTohB,EAAIlB,EAAWJ,GAAG9f,MAGtB,OAFAmhB,EAAIA,GAAK,EAAIA,GAAKA,MAClBC,EAAIA,GAAK,EAAIA,GAAKA,GACD,EAAID,EAAIC,EAAI,GAAK,CACtC,EACAhB,EAAWzgB,UAAUqnB,QAAU,SAAUlH,GACrC,GAAIA,IAAMzI,IACN,OAAQ,EAEZ,GAAIyI,KAAM,IACN,OAAO,EAEX,IAAIlE,EAAIsE,EAAWJ,GAAIqB,EAAIjhB,KAAKF,MAAOohB,EAAIxF,EAAE5b,MAC7C,OAAIE,KAAKmgB,OAASzE,EAAEyE,KACTzE,EAAEyE,KAAO,GAAK,EAErBzE,EAAE0E,QACKpgB,KAAKmgB,MAAQ,EAAI,EAErBQ,EAAWM,EAAGC,IAAMlhB,KAAKmgB,MAAQ,EAAI,EAChD,EACAD,EAAWzgB,UAAUsnB,UAAY7G,EAAWzgB,UAAUqnB,QACtDzG,EAAa5gB,UAAUqnB,QAAU,SAAUlH,GACvC,GAAIA,IAAMzI,IACN,OAAQ,EAEZ,GAAIyI,KAAM,IACN,OAAO,EAEX,IAAIlE,EAAIsE,EAAWJ,GAAIqB,EAAIjhB,KAAKF,MAAOohB,EAAIxF,EAAE5b,MAC7C,OAAI4b,EAAE0E,QACKa,GAAKC,EAAI,EAAID,EAAIC,EAAI,GAAK,EAEjCD,EAAI,IAAMvF,EAAEyE,KACLc,EAAI,GAAK,EAAI,EAEjBA,EAAI,EAAI,GAAK,CACxB,EACAZ,EAAa5gB,UAAUsnB,UAAY1G,EAAa5gB,UAAUqnB,QAC1DxG,EAAa7gB,UAAUqnB,QAAU,SAAUlH,GACvC,GAAIA,IAAMzI,IACN,OAAQ,EAEZ,GAAIyI,KAAM,IACN,OAAO,EAEX,IAAIqB,EAAIjhB,KAAKF,MACTohB,EAAIlB,EAAWJ,GAAG9f,MACtB,OAAOmhB,IAAMC,EAAI,EAAID,EAAIC,EAAI,GAAK,CACtC,EACAZ,EAAa7gB,UAAUsnB,UAAYzG,EAAa7gB,UAAUqnB,QAC1D5G,EAAWzgB,UAAUslB,OAAS,SAAUnF,GACpC,OAA2B,IAApB5f,KAAK8mB,QAAQlH,EACxB,EACAU,EAAa7gB,UAAUunB,GAAK1G,EAAa7gB,UAAUslB,OAAS1E,EAAa5gB,UAAUunB,GAAK3G,EAAa5gB,UAAUslB,OAAS7E,EAAWzgB,UAAUunB,GAAK9G,EAAWzgB,UAAUslB,OACvK7E,EAAWzgB,UAAUwnB,UAAY,SAAUrH,GACvC,OAA2B,IAApB5f,KAAK8mB,QAAQlH,EACxB,EACAU,EAAa7gB,UAAUynB,IAAM5G,EAAa7gB,UAAUwnB,UAAY5G,EAAa5gB,UAAUynB,IAAM7G,EAAa5gB,UAAUwnB,UAAY/G,EAAWzgB,UAAUynB,IAAMhH,EAAWzgB,UAAUwnB,UAChL/G,EAAWzgB,UAAU0nB,QAAU,SAAUvH,GACrC,OAAO5f,KAAK8mB,QAAQlH,GAAK,CAC7B,EACAU,EAAa7gB,UAAU2nB,GAAK9G,EAAa7gB,UAAU0nB,QAAU9G,EAAa5gB,UAAU2nB,GAAK/G,EAAa5gB,UAAU0nB,QAAUjH,EAAWzgB,UAAU2nB,GAAKlH,EAAWzgB,UAAU0nB,QACzKjH,EAAWzgB,UAAUylB,OAAS,SAAUtF,GACpC,OAAO5f,KAAK8mB,QAAQlH,GAAK,CAC7B,EACAU,EAAa7gB,UAAU4nB,GAAK/G,EAAa7gB,UAAUylB,OAAS7E,EAAa5gB,UAAU4nB,GAAKhH,EAAa5gB,UAAUylB,OAAShF,EAAWzgB,UAAU4nB,GAAKnH,EAAWzgB,UAAUylB,OACvKhF,EAAWzgB,UAAU6nB,gBAAkB,SAAU1H,GAC7C,OAAO5f,KAAK8mB,QAAQlH,IAAM,CAC9B,EACAU,EAAa7gB,UAAU8nB,IAAMjH,EAAa7gB,UAAU6nB,gBAAkBjH,EAAa5gB,UAAU8nB,IAAMlH,EAAa5gB,UAAU6nB,gBAAkBpH,EAAWzgB,UAAU8nB,IAAMrH,EAAWzgB,UAAU6nB,gBAC5LpH,EAAWzgB,UAAU+nB,eAAiB,SAAU5H,GAC5C,OAAO5f,KAAK8mB,QAAQlH,IAAM,CAC9B,EACAU,EAAa7gB,UAAUgoB,IAAMnH,EAAa7gB,UAAU+nB,eAAiBnH,EAAa5gB,UAAUgoB,IAAMpH,EAAa5gB,UAAU+nB,eAAiBtH,EAAWzgB,UAAUgoB,IAAMvH,EAAWzgB,UAAU+nB,eAC1LtH,EAAWzgB,UAAUulB,OAAS,WAC1B,QAAwB,EAAhBhlB,KAAKF,MAAM,GACvB,EACAugB,EAAa5gB,UAAUulB,OAAS,WAC5B,QAAqB,EAAbhlB,KAAKF,MACjB,EACAwgB,EAAa7gB,UAAUulB,OAAS,WAC5B,OAAQhlB,KAAKF,MAAQ4f,OAAO,MAAQA,OAAO,EAC/C,EACAQ,EAAWzgB,UAAUonB,MAAQ,WACzB,QAA+B,GAAvB7mB,KAAKF,MAAM,GACvB,EACAugB,EAAa5gB,UAAUonB,MAAQ,WAC3B,QAA4B,GAApB7mB,KAAKF,MACjB,EACAwgB,EAAa7gB,UAAUonB,MAAQ,WAC3B,OAAQ7mB,KAAKF,MAAQ4f,OAAO,MAAQA,OAAO,EAC/C,EACAQ,EAAWzgB,UAAUmnB,WAAa,WAC9B,OAAQ5mB,KAAKmgB,IACjB,EACAE,EAAa5gB,UAAUmnB,WAAa,WAChC,OAAO5mB,KAAKF,MAAQ,CACxB,EACAwgB,EAAa7gB,UAAUmnB,WAAavG,EAAa5gB,UAAUmnB,WAC3D1G,EAAWzgB,UAAUgnB,WAAa,WAC9B,OAAOzmB,KAAKmgB,IAChB,EACAE,EAAa5gB,UAAUgnB,WAAa,WAChC,OAAOzmB,KAAKF,MAAQ,CACxB,EACAwgB,EAAa7gB,UAAUgnB,WAAapG,EAAa5gB,UAAUgnB,WAC3DvG,EAAWzgB,UAAUqlB,OAAS,WAC1B,OAAO,CACX,EACAzE,EAAa5gB,UAAUqlB,OAAS,WAC5B,OAAgC,IAAzB3a,KAAKuZ,IAAI1jB,KAAKF,MACzB,EACAwgB,EAAa7gB,UAAUqlB,OAAS,WAC5B,OAAO9kB,KAAK0jB,MAAM5jB,QAAU4f,OAAO,EACvC,EACAQ,EAAWzgB,UAAUknB,OAAS,WAC1B,OAAO,CACX,EACAtG,EAAa5gB,UAAUknB,OAAS,WAC5B,OAAsB,IAAf3mB,KAAKF,KAChB,EACAwgB,EAAa7gB,UAAUknB,OAAS,WAC5B,OAAO3mB,KAAKF,QAAU4f,OAAO,EACjC,EACAQ,EAAWzgB,UAAUwlB,cAAgB,SAAUrF,GAC3C,IAAIlE,EAAIsE,EAAWJ,GACnB,OAAIlE,EAAEiL,aACFjL,EAAEoJ,WACkB,IAApBpJ,EAAEiF,WAAW,GAAiB3gB,KAAKglB,SAChChlB,KAAK2kB,IAAIjJ,GAAGiL,UACvB,EACArG,EAAa7gB,UAAUwlB,cAAgB5E,EAAa5gB,UAAUwlB,cAAgB/E,EAAWzgB,UAAUwlB,cA2BnG/E,EAAWzgB,UAAUioB,QAAU,SAAUC,GACrC,IAAID,EAAU7C,EAAa7kB,MAC3B,GAAI0nB,IAAYrhB,EAAW,OAAOqhB,EAClC,IAAIhM,EAAI1b,KAAK0jB,MACTkE,EAAOlM,EAAEmM,YACb,GAAID,GAAQ,GAAI,OAAOzC,EAAgBzJ,EAAG,CAAC,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,KAGnF,IAFA,IAAIoM,EAAO3d,KAAK4d,IAAI,GAAKH,EAAKI,aAC1Bvf,EAAI0B,KAAK4W,MAAgB,IAAX4G,EAAkB,EAAIxd,KAAKic,IAAI0B,EAAM,GAAKA,GACnD7G,EAAI,GAAIrc,EAAI,EAAGA,EAAI6D,EAAG7D,IAC3Bqc,EAAEjb,KAAKkZ,EAAOta,EAAI,IAEtB,OAAOugB,EAAgBzJ,EAAGuF,EAC9B,EACAX,EAAa7gB,UAAUioB,QAAUrH,EAAa5gB,UAAUioB,QAAUxH,EAAWzgB,UAAUioB,QACvFxH,EAAWzgB,UAAUwoB,gBAAkB,SAAUC,GAC7C,IAAIR,EAAU7C,EAAa7kB,MAC3B,GAAI0nB,IAAYrhB,EAAW,OAAOqhB,EAGlC,IAFA,IAAIhM,EAAI1b,KAAK0jB,MACTjb,EAAIyf,IAAe7hB,EAAY,EAAI6hB,EAC9BjH,EAAI,GAAIrc,EAAI,EAAGA,EAAI6D,EAAG7D,IAC3Bqc,EAAEjb,KAAKkZ,EAAOiJ,YAAY,EAAGzM,EAAEiK,MAAM,KAEzC,OAAOR,EAAgBzJ,EAAGuF,EAC9B,EACAX,EAAa7gB,UAAUwoB,gBAAkB5H,EAAa5gB,UAAUwoB,gBAAkB/H,EAAWzgB,UAAUwoB,gBACvG/H,EAAWzgB,UAAU2oB,OAAS,SAAU1M,GAEpC,IADA,IAA8EyH,EAAGkF,EAAOC,EAApF7f,EAAIyW,EAAOqJ,KAAMC,EAAOtJ,EAAOuJ,IAAKnH,EAAItB,EAAWtE,GAAIgN,EAAO1oB,KAAK0jB,OAC/DgF,EAAK/B,UACTxD,EAAI7B,EAAEgE,OAAOoD,GACbL,EAAQ5f,EACR6f,EAAQhH,EACR7Y,EAAI+f,EACJlH,EAAIoH,EACJF,EAAOH,EAAMzG,SAASuB,EAAE0C,SAAS2C,IACjCE,EAAOJ,EAAM1G,SAASuB,EAAE0C,SAAS6C,IAErC,IAAKpH,EAAEwD,SAAU,MAAM,IAAIvQ,MAAMvU,KAAKqmB,WAAa,QAAU3K,EAAE2K,WAAa,qBAI5E,OAHsB,IAAlB5d,EAAEqe,QAAQ,KACVre,EAAIA,EAAEuY,IAAItF,IAEV1b,KAAKymB,aACEhe,EAAEgb,SAENhb,CACX,EACA6X,EAAa7gB,UAAU2oB,OAAS/H,EAAa5gB,UAAU2oB,OAASlI,EAAWzgB,UAAU2oB,OACrFlI,EAAWzgB,UAAU8lB,KAAO,WACxB,IAAIzlB,EAAQE,KAAKF,MACjB,OAAIE,KAAKmgB,KACE8B,EAAcniB,EAAO,EAAGE,KAAKmgB,MAEjC,IAAID,EAAWwB,EAAS5hB,EAAO,GAAIE,KAAKmgB,KACnD,EACAE,EAAa5gB,UAAU8lB,KAAO,WAC1B,IAAIzlB,EAAQE,KAAKF,MACjB,OAAIA,EAAQ,EAAIuf,EAAgB,IAAIgB,EAAavgB,EAAQ,GAClD,IAAIogB,EAAWZ,GAAa,EACvC,EACAgB,EAAa7gB,UAAU8lB,KAAO,WAC1B,OAAO,IAAIjF,EAAatgB,KAAKF,MAAQ4f,OAAO,GAChD,EACAQ,EAAWzgB,UAAU4lB,KAAO,WACxB,IAAIvlB,EAAQE,KAAKF,MACjB,OAAIE,KAAKmgB,KACE,IAAID,EAAWwB,EAAS5hB,EAAO,IAAI,GAEvCmiB,EAAcniB,EAAO,EAAGE,KAAKmgB,KACxC,EACAE,EAAa5gB,UAAU4lB,KAAO,WAC1B,IAAIvlB,EAAQE,KAAKF,MACjB,OAAIA,EAAQ,GAAKuf,EAAgB,IAAIgB,EAAavgB,EAAQ,GACnD,IAAIogB,EAAWZ,GAAa,EACvC,EACAgB,EAAa7gB,UAAU4lB,KAAO,WAC1B,OAAO,IAAI/E,EAAatgB,KAAKF,MAAQ4f,OAAO,GAChD,EAEA,IADA,IAAIiJ,EAAc,CAAC,GACZ,EAAIA,EAAYA,EAAYzf,OAAS,IAAMiW,GAAMwJ,EAAY3iB,KAAK,EAAI2iB,EAAYA,EAAYzf,OAAS,IAC9G,IAAI0f,EAAgBD,EAAYzf,OAAQ2f,EAAgBF,EAAYC,EAAgB,GAEpF,SAASE,EAAcpN,GACnB,OAAOvR,KAAKuZ,IAAIhI,IAAMyD,CAC1B,CAoCA,SAAS4J,EAAQ5iB,EAAGuc,EAAGsG,GACnBtG,EAAI1C,EAAW0C,GAMf,IALA,IAAIuG,EAAQ9iB,EAAEsgB,aAAcyC,EAAQxG,EAAE+D,aAClC0C,EAAOF,EAAQ9iB,EAAEijB,MAAQjjB,EAAGkjB,EAAOH,EAAQxG,EAAE0G,MAAQ1G,EACrD4G,EAAS,EAAGC,EAAS,EACrBC,EAAU,KAAMC,EAAU,KAC1B3F,EAAS,IACLqF,EAAKxC,WAAa0C,EAAK1C,UAE3B2C,GADAE,EAAUjG,EAAU4F,EAAMN,IACT,GAAGb,aAChBiB,IACAK,EAAST,EAAgB,EAAIS,GAGjCC,GADAE,EAAUlG,EAAU8F,EAAMR,IACT,GAAGb,aAChBkB,IACAK,EAASV,EAAgB,EAAIU,GAEjCJ,EAAOK,EAAQ,GACfH,EAAOI,EAAQ,GACf3F,EAAO9d,KAAKgjB,EAAGM,EAAQC,IAG3B,IADA,IAAIpI,EAA2C,IAArC6H,EAAGC,EAAQ,EAAI,EAAGC,EAAQ,EAAI,GAAWhK,GAAQ,GAAKA,EAAO,GAC9Dta,EAAIkf,EAAO5a,OAAS,EAAGtE,GAAK,EAAGA,GAAK,EACzCuc,EAAMA,EAAI0E,SAASgD,GAAe7H,IAAI9B,EAAO4E,EAAOlf,KAExD,OAAOuc,CACX,CA7DAjB,EAAWzgB,UAAU8iB,UAAY,SAAU3C,GACvC,IAAIlE,EAAIsE,EAAWJ,GAAGoI,aACtB,IAAKc,EAAcpN,GACf,MAAM,IAAInH,MAAMmV,OAAOhO,GAAK,+BAEhC,GAAIA,EAAI,EAAG,OAAO1b,KAAK2pB,YAAYjO,GACnC,IAAIoI,EAAS9jB,KACb,GAAI8jB,EAAO6C,SAAU,OAAO7C,EAC5B,KAAOpI,GAAKkN,GACR9E,EAASA,EAAO+B,SAASgD,GACzBnN,GAAKkN,EAAgB,EAEzB,OAAO9E,EAAO+B,SAAS8C,EAAYjN,GACvC,EACA4E,EAAa7gB,UAAU8iB,UAAYlC,EAAa5gB,UAAU8iB,UAAYrC,EAAWzgB,UAAU8iB,UAC3FrC,EAAWzgB,UAAUkqB,WAAa,SAAU/J,GACxC,IAAIgK,EACAlO,EAAIsE,EAAWJ,GAAGoI,aACtB,IAAKc,EAAcpN,GACf,MAAM,IAAInH,MAAMmV,OAAOhO,GAAK,+BAEhC,GAAIA,EAAI,EAAG,OAAO1b,KAAKuiB,WAAW7G,GAElC,IADA,IAAIoI,EAAS9jB,KACN0b,GAAKkN,GAAe,CACvB,GAAI9E,EAAO6C,UAAY7C,EAAO2C,cAAgB3C,EAAOgB,SAAU,OAAOhB,EAEtEA,GADA8F,EAASrG,EAAUO,EAAQ+E,IACX,GAAGpC,aAAemD,EAAO,GAAGvE,OAASuE,EAAO,GAC5DlO,GAAKkN,EAAgB,CACzB,CAEA,OADAgB,EAASrG,EAAUO,EAAQ6E,EAAYjN,KACzB,GAAG+K,aAAemD,EAAO,GAAGvE,OAASuE,EAAO,EAC9D,EACAtJ,EAAa7gB,UAAUkqB,WAAatJ,EAAa5gB,UAAUkqB,WAAazJ,EAAWzgB,UAAUkqB,WA+B7FzJ,EAAWzgB,UAAU2pB,IAAM,WACvB,OAAOppB,KAAKyjB,SAAS4B,MACzB,EACA/E,EAAa7gB,UAAU2pB,IAAM/I,EAAa5gB,UAAU2pB,IAAMlJ,EAAWzgB,UAAU2pB,IAC/ElJ,EAAWzgB,UAAUoqB,IAAM,SAAUnO,GACjC,OAAOqN,EAAQ/oB,KAAM0b,GAAG,SAAUuF,EAAGC,GACjC,OAAOD,EAAIC,CACf,GACJ,EACAZ,EAAa7gB,UAAUoqB,IAAMxJ,EAAa5gB,UAAUoqB,IAAM3J,EAAWzgB,UAAUoqB,IAC/E3J,EAAWzgB,UAAUqqB,GAAK,SAAUpO,GAChC,OAAOqN,EAAQ/oB,KAAM0b,GAAG,SAAUuF,EAAGC,GACjC,OAAOD,EAAIC,CACf,GACJ,EACAZ,EAAa7gB,UAAUqqB,GAAKzJ,EAAa5gB,UAAUqqB,GAAK5J,EAAWzgB,UAAUqqB,GAC7E5J,EAAWzgB,UAAUsqB,IAAM,SAAUrO,GACjC,OAAOqN,EAAQ/oB,KAAM0b,GAAG,SAAUuF,EAAGC,GACjC,OAAOD,EAAIC,CACf,GACJ,EACAZ,EAAa7gB,UAAUsqB,IAAM1J,EAAa5gB,UAAUsqB,IAAM7J,EAAWzgB,UAAUsqB,IAC/E,IAAIC,EAAY,GAAK,GAAIC,GAAc9K,GAAQA,IAASA,GAAQA,GAAQ6K,EAExE,SAASE,EAASxO,GACd,IAAIkE,EAAIlE,EAAE5b,MACNqG,EAAiB,iBAANyZ,EAAiBA,EAAIoK,EAAyB,iBAANpK,EAAiBA,EAAIF,OAAOsK,GAAapK,EAAE,GAAKA,EAAE,GAAKT,EAAO8K,EACrH,OAAO9jB,GAAKA,CAChB,CAEA,SAASgkB,EAAiBrqB,EAAO0hB,GAC7B,GAAIA,EAAKuF,UAAUjnB,IAAU,EAAG,CAC5B,IAAIsqB,EAAMD,EAAiBrqB,EAAO0hB,EAAKwB,OAAOxB,IAC1C6I,EAAID,EAAIC,EACR1gB,EAAIygB,EAAIzgB,EACRlB,EAAI4hB,EAAExE,SAASrE,GACnB,OAAO/Y,EAAEse,UAAUjnB,IAAU,EAAI,CAAEuqB,EAAG5hB,EAAGkB,EAAO,EAAJA,EAAQ,GAAM,CAAE0gB,EAAGA,EAAG1gB,EAAO,EAAJA,EACzE,CACA,MAAO,CAAE0gB,EAAGnL,EAAO,GAAIvV,EAAG,EAC9B,CAcA,SAASS,EAAI6W,EAAGC,GAGZ,OAFAD,EAAIjB,EAAWiB,GACfC,EAAIlB,EAAWkB,GACRD,EAAEkG,QAAQjG,GAAKD,EAAIC,CAC9B,CAEA,SAASoJ,EAAIrJ,EAAGC,GAGZ,OAFAD,EAAIjB,EAAWiB,GACfC,EAAIlB,EAAWkB,GACRD,EAAEiE,OAAOhE,GAAKD,EAAIC,CAC7B,CAEA,SAASqJ,EAAItJ,EAAGC,GAGZ,GAFAD,EAAIjB,EAAWiB,GAAGyC,MAClBxC,EAAIlB,EAAWkB,GAAGwC,MACdzC,EAAE8D,OAAO7D,GAAI,OAAOD,EACxB,GAAIA,EAAE0F,SAAU,OAAOzF,EACvB,GAAIA,EAAEyF,SAAU,OAAO1F,EAEvB,IADA,IAAoBvY,EAAGD,EAAnBka,EAAIhD,EAAQ,GACTsB,EAAE+D,UAAY9D,EAAE8D,UACnBtc,EAAI4hB,EAAIJ,EAASjJ,GAAIiJ,EAAShJ,IAC9BD,EAAIA,EAAEqE,OAAO5c,GACbwY,EAAIA,EAAEoE,OAAO5c,GACbia,EAAIA,EAAEkD,SAASnd,GAEnB,KAAOuY,EAAE+D,UACL/D,EAAIA,EAAEqE,OAAO4E,EAASjJ,IAE1B,EAAG,CACC,KAAOC,EAAE8D,UACL9D,EAAIA,EAAEoE,OAAO4E,EAAShJ,IAEtBD,EAAEkG,QAAQjG,KACVzY,EAAIyY,EACJA,EAAID,EACJA,EAAIxY,GAERyY,EAAIA,EAAEU,SAASX,EACnB,QAAUC,EAAEyF,UACZ,OAAOhE,EAAEmC,SAAW7D,EAAIA,EAAE4E,SAASlD,EACvC,CApDAzC,EAAWzgB,UAAUooB,UAAY,WAC7B,IAAInM,EAAI1b,KAIR,OAHI0b,EAAEqL,UAAU7H,EAAO,IAAM,IACzBxD,EAAIA,EAAE+H,SAAS7B,SAAS1C,EAAO,KAEJ,IAA3BxD,EAAEqL,UAAU7H,EAAO,IACZA,EAAO,GAEXA,EAAOiL,EAAiBzO,EAAGwD,EAAO,IAAIvV,GAAGqX,IAAI9B,EAAO,GAC/D,EACAoB,EAAa7gB,UAAUooB,UAAYxH,EAAa5gB,UAAUooB,UAAY3H,EAAWzgB,UAAUooB,UAmE3F,IAAI5H,EAAY,SAAUuK,EAAMhJ,EAAM1B,EAAUC,GAC5CD,EAAWA,GAAYN,EACvBgL,EAAOd,OAAOc,GACTzK,IACDyK,EAAOA,EAAKC,cACZ3K,EAAWA,EAAS2K,eAExB,IACI7lB,EADAsE,EAASshB,EAAKthB,OAEdwhB,EAAUvgB,KAAKuZ,IAAIlC,GACnBmJ,EAAiB,CAAC,EACtB,IAAK/lB,EAAI,EAAGA,EAAIkb,EAAS5W,OAAQtE,IAC7B+lB,EAAe7K,EAASlb,IAAMA,EAElC,IAAKA,EAAI,EAAGA,EAAIsE,EAAQtE,IAEpB,GAAU,OADN+d,EAAI6H,EAAK5lB,KAET+d,KAAKgI,GACDA,EAAehI,IAAM+H,EAAS,CAC9B,GAAU,MAAN/H,GAAyB,IAAZ+H,EAAe,SAChC,MAAM,IAAInW,MAAMoO,EAAI,iCAAmCnB,EAAO,IAClE,CAGRA,EAAOxB,EAAWwB,GAClB,IAAIoJ,EAAS,GACTnE,EAAyB,MAAZ+D,EAAK,GACtB,IAAK5lB,EAAI6hB,EAAa,EAAI,EAAG7hB,EAAI4lB,EAAKthB,OAAQtE,IAAK,CAC/C,IAAI+d,EACJ,IADIA,EAAI6H,EAAK5lB,MACJ+lB,EAAgBC,EAAO5kB,KAAKga,EAAW2K,EAAehI,SAAW,IAAU,MAANA,EAMvE,MAAM,IAAIpO,MAAMoO,EAAI,6BALvB,IAAIriB,EAAQsE,EACZ,GACIA,UACiB,MAAZ4lB,EAAK5lB,IAAcA,EAAI4lB,EAAKthB,QACrC0hB,EAAO5kB,KAAKga,EAAWwK,EAAKhP,MAAMlb,EAAQ,EAAGsE,IACM,CAC3D,CACA,OAAOimB,EAAmBD,EAAQpJ,EAAMiF,EAC5C,EAEA,SAASoE,EAAmBD,EAAQpJ,EAAMiF,GACtC,IAAwC7hB,EAApCkmB,EAAMnL,EAAQ,GAAIyG,EAAMzG,EAAQ,GACpC,IAAK/a,EAAIgmB,EAAO1hB,OAAS,EAAGtE,GAAK,EAAGA,IAChCkmB,EAAMA,EAAI9J,IAAI4J,EAAOhmB,GAAGohB,MAAMI,IAC9BA,EAAMA,EAAIJ,MAAMxE,GAEpB,OAAOiF,EAAaqE,EAAIrH,SAAWqH,CACvC,CAUA,SAASC,EAAOrP,EAAG8F,GAEf,IADAA,EAAOtC,EAAOsC,IACLmF,SAAU,CACf,GAAIjL,EAAEiL,SAAU,MAAO,CAAE7mB,MAAO,CAAC,GAAI2mB,YAAY,GACjD,MAAM,IAAIlS,MAAM,4CACpB,CACA,GAAIiN,EAAKuD,QAAQ,GAAI,CACjB,GAAIrJ,EAAEiL,SAAU,MAAO,CAAE7mB,MAAO,CAAC,GAAI2mB,YAAY,GACjD,GAAI/K,EAAE+K,aAAc,MAAO,CACvB3mB,MAAO,GAAG0iB,OAAO9c,MAAM,GAAImb,MAAMnb,MAAM,KAAMmb,OAAOnF,EAAEsM,eAAegD,IAAInK,MAAMphB,UAAUwrB,QAAS,CAAC,EAAG,KACtGxE,YAAY,GAEhB,IAAIhG,EAAMI,MAAMnb,MAAM,KAAMmb,MAAMnF,EAAEsM,aAAe,IAAIgD,IAAInK,MAAMphB,UAAUwrB,QAAS,CAAC,EAAG,IAExF,OADAxK,EAAI8D,QAAQ,CAAC,IACN,CAAEzkB,MAAO,GAAG0iB,OAAO9c,MAAM,GAAI+a,GAAMgG,YAAY,EAC1D,CACA,IAAIyE,GAAM,EAKV,GAJIxP,EAAE+K,cAAgBjF,EAAKoF,eACvBsE,GAAM,EACNxP,EAAIA,EAAEgI,OAENlC,EAAKsD,SACL,OAAIpJ,EAAEiL,SAAiB,CAAE7mB,MAAO,CAAC,GAAI2mB,YAAY,GAC1C,CAAE3mB,MAAO+gB,MAAMnb,MAAM,KAAMmb,MAAMnF,EAAEsM,eAAegD,IAAIG,OAAO1rB,UAAUwrB,QAAS,GAAIxE,WAAYyE,GAI3G,IAFA,IACchF,EADVkF,EAAM,GACNC,EAAO3P,EACJ2P,EAAK5E,cAAgB4E,EAAK1K,WAAWa,IAAS,GAAG,CACpD0E,EAASmF,EAAKnF,OAAO1E,GACrB6J,EAAOnF,EAAO5C,SACd,IAAIgI,EAAQpF,EAAO9C,UACfkI,EAAM7E,eACN6E,EAAQ9J,EAAKmE,MAAM2F,GAAO5H,MAC1B2H,EAAOA,EAAK9F,QAEhB6F,EAAIplB,KAAKslB,EAAMtD,aACnB,CAEA,OADAoD,EAAIplB,KAAKqlB,EAAKrD,cACP,CAAEloB,MAAOsrB,EAAI5G,UAAWiC,WAAYyE,EAC/C,CAEA,SAASK,EAAa7P,EAAG8F,EAAM1B,GAC3B,IAAIW,EAAMsK,EAAOrP,EAAG8F,GACpB,OAAQf,EAAIgG,WAAa,IAAM,IAAMhG,EAAI3gB,MAAMkrB,KAAI,SAAU7kB,GACzD,OApDR,SAAmBmlB,EAAOxL,GAEtB,OAAIwL,GADJxL,EAAWA,GAAYN,GACFtW,OACV4W,EAASwL,GAEb,IAAMA,EAAQ,GACzB,CA8CeE,CAAUrlB,EAAG2Z,EACxB,IAAG2L,KAAK,GACZ,CA2CA,SAASC,EAAiB9L,GACtB,GAAIW,GAAWX,GAAI,CACf,IAAIzZ,GAAKyZ,EACT,GAAIzZ,IAAM2a,EAAS3a,GAAI,OAAOsZ,EAAuB,IAAIa,EAAaZ,OAAOvZ,IAAM,IAAIka,EAAala,GACpG,MAAM,IAAIoO,MAAM,oBAAsBqL,EAC1C,CACA,IAAIO,EAAgB,MAATP,EAAE,GACTO,IAAMP,EAAIA,EAAEpE,MAAM,IACtB,IAAID,EAAQqE,EAAErE,MAAM,MACpB,GAAIA,EAAMrS,OAAS,EAAG,MAAM,IAAIqL,MAAM,oBAAsBgH,EAAMkQ,KAAK,MACvE,GAAqB,IAAjBlQ,EAAMrS,OAAc,CACpB,IAAIwd,EAAMnL,EAAM,GAGhB,GAFe,MAAXmL,EAAI,KAAYA,EAAMA,EAAIlL,MAAM,KACpCkL,GAAOA,KACK5F,EAAS4F,KAASnG,EAAUmG,GAAM,MAAM,IAAInS,MAAM,oBAAsBmS,EAAM,6BAC1F,IAAI8D,EAAOjP,EAAM,GACboQ,EAAenB,EAAK1f,QAAQ,KAKhC,GAJI6gB,GAAgB,IAChBjF,GAAO8D,EAAKthB,OAASyiB,EAAe,EACpCnB,EAAOA,EAAKhP,MAAM,EAAGmQ,GAAgBnB,EAAKhP,MAAMmQ,EAAe,IAE/DjF,EAAM,EAAG,MAAM,IAAInS,MAAM,sDAE7BqL,EADA4K,GAAQ,IAAI3J,MAAM6F,EAAM,GAAG+E,KAAK,IAEpC,CAEA,IADc,kBAAkBG,KAAKhM,GACvB,MAAM,IAAIrL,MAAM,oBAAsBqL,GACpD,GAAIH,EACA,OAAO,IAAIa,EAAaZ,OAAOS,EAAO,IAAMP,EAAIA,IAGpD,IADA,IAAI0B,EAAI,GAAIlX,EAAMwV,EAAE1W,OAAQyY,EAAIvC,EAAUkL,EAAMlgB,EAAMuX,EAC/CvX,EAAM,GACTkX,EAAEtb,MAAM4Z,EAAEpE,MAAM8O,EAAKlgB,KACrBkgB,GAAO3I,GACG,IAAG2I,EAAM,GACnBlgB,GAAOuX,EAGX,OADAjB,EAAKY,GACE,IAAIpB,EAAWoB,EAAGnB,EAC7B,CAaA,SAASH,EAAWJ,GAChB,MAAiB,iBAANA,EAZf,SAA0BA,GACtB,GAAIH,EACA,OAAO,IAAIa,EAAaZ,OAAOE,IAEnC,GAAIW,EAAUX,GAAI,CACd,GAAIA,IAAMkB,EAASlB,GAAI,MAAM,IAAIrL,MAAMqL,EAAI,uBAC3C,OAAO,IAAIS,EAAaT,EAC5B,CACA,OAAO8L,EAAiB9L,EAAEyG,WAC9B,CAIewF,CAAiBjM,GAEX,iBAANA,EACA8L,EAAiB9L,GAEX,iBAANA,EACA,IAAIU,EAAaV,GAErBA,CACX,CAxGAM,EAAWzgB,UAAUqsB,QAAU,SAAUjM,GACrC,OAAOkL,EAAO/qB,KAAM6f,EACxB,EACAQ,EAAa5gB,UAAUqsB,QAAU,SAAUjM,GACvC,OAAOkL,EAAO/qB,KAAM6f,EACxB,EACAS,EAAa7gB,UAAUqsB,QAAU,SAAUjM,GACvC,OAAOkL,EAAO/qB,KAAM6f,EACxB,EACAK,EAAWzgB,UAAU4mB,SAAW,SAAUxG,EAAOC,GAE7C,GADID,IAAUxZ,IAAWwZ,EAAQ,IACnB,KAAVA,EAAc,OAAO0L,EAAavrB,KAAM6f,EAAOC,GAEnD,IADA,IAA2EwL,EAAvE1L,EAAI5f,KAAKF,MAAO6hB,EAAI/B,EAAE1W,OAAQiM,EAAMuU,OAAO9J,IAAI+B,MAC1CA,GAAK,GACV2J,EAAQ5B,OAAO9J,EAAE+B,IACjBxM,GAH4D,UAG/CqG,MAAM8P,EAAMpiB,QAAUoiB,EAGvC,OADWtrB,KAAKmgB,KAAO,IAAM,IACfhL,CAClB,EACAkL,EAAa5gB,UAAU4mB,SAAW,SAAUxG,EAAOC,GAE/C,OADID,IAAUxZ,IAAWwZ,EAAQ,IACpB,IAATA,EAAoB0L,EAAavrB,KAAM6f,EAAOC,GAC3C4J,OAAO1pB,KAAKF,MACvB,EACAwgB,EAAa7gB,UAAU4mB,SAAWhG,EAAa5gB,UAAU4mB,SACzD/F,EAAa7gB,UAAUssB,OAAS7L,EAAWzgB,UAAUssB,OAAS1L,EAAa5gB,UAAUssB,OAAS,WAC1F,OAAO/rB,KAAKqmB,UAChB,EACAnG,EAAWzgB,UAAUwrB,QAAU,WAC3B,OAAO7U,SAASpW,KAAKqmB,WAAY,GACrC,EACAnG,EAAWzgB,UAAUuoB,WAAa9H,EAAWzgB,UAAUwrB,QACvD5K,EAAa5gB,UAAUwrB,QAAU,WAC7B,OAAOjrB,KAAKF,KAChB,EACAugB,EAAa5gB,UAAUuoB,WAAa3H,EAAa5gB,UAAUwrB,QAC3D3K,EAAa7gB,UAAUwrB,QAAU3K,EAAa7gB,UAAUuoB,WAAa,WACjE,OAAO5R,SAASpW,KAAKqmB,WAAY,GACrC,EAmEA,IAAK,IAAIzhB,EAAI,EAAGA,EAAI,IAAKA,IACrB+a,EAAQ/a,GAAKob,EAAWpb,GACpBA,EAAI,IAAG+a,GAAS/a,GAAKob,GAAYpb,IAgBzC,OAdA+a,EAAQ8I,IAAM9I,EAAQ,GACtBA,EAAQ4I,KAAO5I,EAAQ,GACvBA,EAAQqM,SAAWrM,GAAS,GAC5BA,EAAQvV,IAAMA,EACduV,EAAQ2K,IAAMA,EACd3K,EAAQ4K,IAAMA,EACd5K,EAAQsM,IApPR,SAAahL,EAAGC,GAGZ,OAFAD,EAAIjB,EAAWiB,GAAGyC,MAClBxC,EAAIlB,EAAWkB,GAAGwC,MACXzC,EAAEqE,OAAOiF,EAAItJ,EAAGC,IAAI2E,SAAS3E,EACxC,EAiPAvB,EAAQuM,WAAa,SAAU/lB,GAC3B,OAAOA,aAAa+Z,GAAc/Z,aAAaka,GAAgBla,aAAama,CAChF,EACAX,EAAQwI,YAlPR,SAAqBlH,EAAGC,GAGpB,IAAIiL,EAAM7B,EAFVrJ,EAAIjB,EAAWiB,GACfC,EAAIlB,EAAWkB,IAEXlgB,EADwBoJ,EAAI6W,EAAGC,GAClBU,SAASuK,GAAKnL,IAAI,GACnC,GAAIhgB,EAAMof,QAAS,OAAO+L,EAAInL,IAAI7W,KAAKgJ,MAAMhJ,KAAKiiB,SAAWprB,IAG7D,IAFA,IAAI4pB,EAASG,EAAO/pB,EAAOme,GAAMrf,MAC7BgkB,EAAS,GAAIuI,GAAa,EACrBznB,EAAI,EAAGA,EAAIgmB,EAAO1hB,OAAQtE,IAAK,CACpC,IAAI0nB,EAAMD,EAAazB,EAAOhmB,GAAKua,EAC/BmM,EAAQxK,EAAS3W,KAAKiiB,SAAWE,GACrCxI,EAAO9d,KAAKslB,GACRA,EAAQgB,IAAKD,GAAa,EAClC,CACA,OAAOF,EAAInL,IAAIrB,EAAQ4M,UAAUzI,EAAQ3E,GAAM,GACnD,EAoOAQ,EAAQ4M,UAAY,SAAU3B,EAAQpJ,EAAMiF,GACxC,OAAOoE,EAAmBD,EAAOI,IAAIhL,GAAaA,EAAWwB,GAAQ,IAAKiF,EAC9E,EACO9G,CACX,CAjwCa,GAmwCS,mBAAX6M,QAAyBA,OAAOC,KACvCD,OAAO,cAAe,IAAI,WACtB,OAAOtN,CACX,IAGJ,QChuCA,SAASwN,EAAU3lB,GAEf,MAAM4lB,GADN5lB,EAASA,GAAU,CAAC,GACE4lB,OAChBplB,EAAQR,EAAOQ,MACfkN,EAAY1N,EAAO0N,UACnBmY,EAAgB7lB,EAAO6lB,cACvBC,EAAW9lB,EAAO8lB,SAGlBC,EAAmB,CAAC,MAAO,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QAE3EC,EAAO,CACT,KAAQ,OACR,KAAQ,YACR,KAAQ,QAENC,EAAgB,CAClB,KAAQ,KAENC,EAAyB,CAC3B,KAAO,EACP,MAAO,EACP,KAAO,EACP,KAAO,EACP,MAAO,EACP,KAAO,EACP,KAAO,EACP,MAAO,EACP,KAAO,EACP,KAAO,EACP,MAAO,GACP,IAAM,GACN,KAAM,IAEJC,EAAc,CAChB,MAAS,YACT,MAAS,YACT,KAAQ,mBAGZ,IAAI7oB,EACA2C,EACAmmB,EAOJ,SAASC,EAAsBC,EAAMC,GACjC,MAAMxtB,EAAQutB,EAAKE,aAAaD,GAChC,QAAKxtB,GAG0B,SAAxBA,EAAM2qB,aACjB,CAoBA,SAAS+C,EAAiBC,EAAa5pB,GACnC,MAAM6Q,EAAgB,CAAC,EACjBgZ,EAAkB,GACxB,IAAIC,EACAC,EACAntB,EACAmE,EACAzE,EAEJ,MAAM0E,EAAO4oB,EAAYF,aAAa,QAChC1sB,EAAO4sB,EAAYF,aAAa,QAChC/U,EAAOiV,EAAYF,aAAa,YAChCM,EAAarV,EAAO3X,EAAO,IAAM2X,EAAO3X,EA+B9C,IA7BA6T,EAAcoE,GAAKjU,GAAQgpB,EAC3BnZ,EAAcoZ,YAAcjtB,EAC5B6T,EAAc8D,KAAOA,GAAQ,MAC7B9D,EAAcqZ,SAAWb,EAAYrsB,GACrC6T,EAAcsZ,QAAUP,EAAYF,aAAa,WACjD7Y,EAAcuZ,SAAWR,EAAYF,aAAa,YAClD7Y,EAAcwZ,UAAYT,EAAYF,aAAa,aAG/C7Y,EAAcsZ,UACVjB,EAAKrY,EAAcsZ,WACnBtZ,EAAcyZ,KAAO,CAAC,CAClBC,YAAa,0BACbtuB,MAAOitB,EAAKrY,EAAcsZ,YAG9BhB,EAActY,EAAcsZ,WAC5BtZ,EAAc2Z,cAAgB,CAAC,CAC3BD,YAAa,0CACbtuB,MAAOktB,EAActY,EAAcsZ,aAM/CL,EAiLJ,SAA4BF,EAAa5pB,GACrC,MAAM8pB,EAAkB,CAAC,EACzB,IAAIW,EACAC,EACAztB,EAiBJ,OAfAA,EAAM2sB,EAAYF,aAAa,OAC/Be,EAAWxtB,EAAMA,EAAIiI,QAAQ,YAAa,eAAiB,KAC3DulB,EAAWA,EAAWA,EAASvlB,QAAQ,eAAgB,UAAY,KAEnEwlB,EAAuBd,EAAYF,aAAa,aAChDgB,EAAuBA,EAAuBrd,WAAWqd,GAAwB1qB,EAEjF8pB,EAAgB7kB,MAAQwlB,EACxBX,EAAgB9pB,UAAY0qB,EAE5BZ,EAAgBtlB,gBAQpB,SAA4BolB,EAAa5pB,GACrC,MAAM2qB,EAAkB,CAAC,EACnBC,EAAShB,EAAYiB,qBAAqB,KAC1CvmB,EAAW,GACjB,IAAIK,EACAmmB,EACA3lB,EACApE,EAAGyd,EAAGf,EACN9gB,EAAW,EAEf,IAAKoE,EAAI,EAAGA,EAAI6pB,EAAOvlB,OAAQtE,IAoD3B,GAnDA4D,EAAU,CAAC,EAGXQ,EAAYylB,EAAO7pB,GAAG2oB,aAAa,KAI/BvkB,GAAa0W,EAAO1W,GAAWme,QAAQzH,EAAOyL,OAAOyD,qBACrDpmB,EAAQQ,UAAYA,GAExBR,EAAQC,EAAIyI,WAAWlI,GAGvBR,EAAQE,EAAIwI,WAAWud,EAAO7pB,GAAG2oB,aAAa,MAGnC,IAAN3oB,GAAa4D,EAAQC,IACtBD,EAAQC,EAAI,GAGZ7D,EAAI,IACJ+pB,EAAcxmB,EAASA,EAASe,OAAS,GAEpCylB,EAAYjmB,IACTimB,EAAY3lB,UACZ2lB,EAAYjmB,EAAIgX,EAAO1W,GAAW4Y,SAASlC,EAAOiP,EAAY3lB,YAAYgf,aAE1E2G,EAAYjmB,EAAIF,EAAQC,EAAIkmB,EAAYlmB,EAE5CjI,GAAYmuB,EAAYjmB,GAGvBF,EAAQC,IACLkmB,EAAY3lB,WACZR,EAAQQ,UAAY0W,EAAOiP,EAAY3lB,WAAWgY,IAAItB,EAAOiP,EAAYjmB,IAAI2d,WAC7E7d,EAAQC,EAAIyI,WAAW1I,EAAQQ,YAE/BR,EAAQC,EAAIkmB,EAAYlmB,EAAIkmB,EAAYjmB,IAKhDF,EAAQE,IACRlI,GAAYgI,EAAQE,GAIxBP,EAASnC,KAAKwC,GAGd8Y,EAAIpQ,WAAWud,EAAO7pB,GAAG2oB,aAAa,MAClCjM,EAEA,IAAKe,EAAI,EAAGA,EAAKf,EAAI,EAAIe,IACrBsM,EAAcxmB,EAASA,EAASe,OAAS,GACzCV,EAAU,CAAC,EACXA,EAAQC,EAAIkmB,EAAYlmB,EAAIkmB,EAAYjmB,EACxCF,EAAQE,EAAIimB,EAAYjmB,EACpBimB,EAAY3lB,YACZR,EAAQQ,UAAY0W,EAAOiP,EAAY3lB,WAAWgY,IAAItB,EAAOiP,EAAYjmB,IAAI2d,YAEjF7lB,GAAYgI,EAAQE,EACpBP,EAASnC,KAAKwC,GAQ1B,OAHAgmB,EAAgBlmB,EAAIH,EACpBqmB,EAAgBhuB,SAAWA,EAAWqD,EAE/B2qB,CACX,CA1FsCK,CAAmBpB,EAAaE,EAAgB9pB,WAGlF8pB,EAAgBmB,uBAAyB,MAElCnB,CACX,CAvMsBoB,CAAmBtB,EAAa5pB,GAElD+pB,EAAgBH,EAAYiB,qBAAqB,gBAE5C9pB,EAAI,EAAGA,EAAIgpB,EAAc1kB,OAAQtE,IAElCgpB,EAAchpB,GAAGoqB,QAAUta,EAAcsa,QACzCpB,EAAchpB,GAAGmpB,SAAWrZ,EAAcqZ,SAG1C5tB,EAAQytB,EAAchpB,GAAG2oB,aAAa,SACtCK,EAAchpB,GAAGqqB,GAAKva,EAAcoE,IAAiB,OAAV3Y,EAAmB,IAAMA,EAAS,IAG7EM,EAAiByuB,EAAkBtB,EAAchpB,GAAI6oB,GAE9B,OAAnBhtB,IAEAA,EAAe2H,gBAAkBulB,EAEjCD,EAAgB1nB,KAAKvF,IAI7B,OAA+B,IAA3BitB,EAAgBxkB,OACT,MAGXwL,EAAcya,eAAiBzB,EAG/BhZ,EAActM,gBAAkBulB,EAEzBjZ,EACX,CAEA,SAASwa,EAAkBE,EAAc3B,GACrC,MAAMhtB,EAAiB,CAAC,EAClBI,EAAO4sB,EAAYF,aAAa,QACtC,IAAI8B,EAAc,KACdnX,EAAQ,KACRC,EAAS,KAyBb,GAvBA1X,EAAeqY,GAAKsW,EAAaH,GACjCxuB,EAAeuC,UAAYoT,SAASgZ,EAAa7B,aAAa,WAAY,IAC1E9sB,EAAestB,SAAWqB,EAAarB,SAEvC7V,EAAQ9B,SAASgZ,EAAa7B,aAAa,YAAa,IACxDpV,EAAS/B,SAASgZ,EAAa7B,aAAa,aAAc,IACrD+B,MAAMpX,KACPzX,EAAeyX,MAAQA,GAEtBoX,MAAMnX,KACP1X,EAAe0X,OAASA,GAI5BkX,EAAcD,EAAa7B,aAAa,UAGpB,OAAhB8B,GAAwC,KAAhBA,IACxBA,EAAc5B,EAAYF,aAAa,WAKvB,OAAhB8B,GAAwC,KAAhBA,EACxB,GAAIxuB,IAAS4T,EAAUoE,MACnBwW,EAAc,WACX,GAAIxuB,IAAS4T,EAAUkE,MAE1B,OADA3R,EAAOO,MAAM,6GACN,KAKf,OAA6D,IAAzDulB,EAAiBhiB,QAAQukB,EAAYE,gBAErCvoB,EAAOwoB,KAAK,wBAA0BH,GAC/B,OAIS,SAAhBA,GAA0C,SAAhBA,EAC1B5uB,EAAeuZ,OAevB,SAAsBoV,GAClB,IACIK,EACAC,EAFApU,EAAmB8T,EAAa7B,aAAa,oBAAoBlH,WAYrE,OAJAoJ,EAAY,iBAAiBE,KAAKrU,GAElCoU,EAASD,GAAaA,EAAU,GAAMnU,EAAiBsU,OAAOtU,EAAiBxQ,QAAQ2kB,EAAU,IAAM,GAAI,QAAMppB,EAE1G,QAAUqpB,CACrB,CA7BgCG,CAAaT,GAC9BC,EAAYvkB,QAAQ,QAAU,GACrCrK,EAAeuZ,OA6BvB,SAAqBoV,EAAcC,GAC/B,MAAMS,EAAe1Z,SAASgZ,EAAa7B,aAAa,gBAAiB,IACzE,IAEIwC,EACAC,EACAC,EACAC,EALA5U,EAAmB8T,EAAa7B,aAAa,oBAAoBlH,WACjE8J,EAAa,EAwDjB,MAhDoB,SAAhBd,IACAc,EAAa,QAGQ9pB,IAArBiV,GAAuD,KAArBA,GAClC6U,EAAa,EACbF,EAAYhD,EAAuB6C,GACf,SAAhBT,GAGAc,EAAa,EACb7U,EAAmB,IAAInF,WAAW,GAClC+Z,EAAkCjD,EAAsC,EAAf6C,GAGzDxU,EAAiB,GAAM6U,GAAc,EAAMF,GAAa,EACxD3U,EAAiB,GAAM2U,GAAa,EAAMb,EAAagB,UAAY,EAAMF,GAAmC,EAC5G5U,EAAiB,GAAM4U,GAAmC,EAAM,EAChE5U,EAAiB,GAAK,EAEtB0U,EAAQ,IAAIK,YAAY,GACxBL,EAAM,IAAM1U,EAAiB,IAAM,GAAKA,EAAiB,GACzD0U,EAAM,IAAM1U,EAAiB,IAAM,GAAKA,EAAiB,GAEzDyU,EAAsBC,EAAM,GAAG3J,SAAS,IACxC0J,EAAsBC,EAAM,GAAG3J,SAAS,IAAM2J,EAAM,GAAG3J,SAAS,MAKhE/K,EAAmB,IAAInF,WAAW,GAElCmF,EAAiB,GAAM6U,GAAc,EAAMF,GAAa,EACxD3U,EAAiB,GAAM2U,GAAa,EAAM7Z,SAASgZ,EAAa7B,aAAa,YAAa,KAAO,EAEjGyC,EAAQ,IAAIK,YAAY,GACxBL,EAAM,IAAM1U,EAAiB,IAAM,GAAKA,EAAiB,GAEzDyU,EAAsBC,EAAM,GAAG3J,SAAS,KAG5C/K,EAAmB,GAAKyU,EACxBzU,EAAmBA,EAAiBiU,cACpCH,EAAakB,aAAa,mBAAoBhV,IACxB,IAAf6U,IACPA,GAA4D,IAA9C/Z,SAASkF,EAAiBsU,OAAO,EAAG,GAAI,MAAe,GAGlE,WAAaO,CACxB,CAzFgCI,CAAYnB,EAAcC,GAClD5uB,EAAe0b,kBAAoB/F,SAASgZ,EAAa7B,aAAa,gBAAiB,IACvF9sB,EAAesb,cAAgB3F,SAASgZ,EAAa7B,aAAa,YAAa,MACxE8B,EAAYvkB,QAAQ,SAAWukB,EAAYvkB,QAAQ,WAC1DrK,EAAeuZ,OAASvF,EAAU+b,MAGtC/vB,EAAe6a,iBAAmB,GAAK8T,EAAa7B,aAAa,oBACjE9sB,EAAeuuB,QAAUI,EAAaJ,QAE/BvuB,EACX,CA6QA,SAASgwB,EAAUpwB,EAAOqwB,EAAMC,GAC5B,MAAMC,EAAOvwB,EAAMqwB,GACnBrwB,EAAMqwB,GAAQrwB,EAAMswB,GACpBtwB,EAAMswB,GAAQC,CAClB,CAkEA,SAASC,EAAgBC,GACrB,MAAM9oB,EAAW,CAAC,EACZ+oB,EAAqB,GACrBC,EAAuBF,EAAOpC,qBAAqB,wBAAwB,GAC3EuC,EAAaH,EAAOpC,qBAAqB,cAAc,GAC7D,IACI5mB,EACAopB,EACAvc,EACAwc,EACAC,EACAxtB,EACAuE,EACAtE,EACAwtB,EACAzsB,EAAGyd,EAVHiP,EAAmB,KAavBtpB,EAASupB,SAAW,MACpBvpB,EAASwpB,SAAW,wCACpBxpB,EAASnH,KAAOusB,EAAsB4D,EAAsB,UAAY,UAAY,SACpFntB,EAAYmtB,EAAqBzD,aAAa,aAC9CvlB,EAASnE,UAAYA,EAAYqN,WAAWrN,GApjBrB,IAqjBvB,IAAI4tB,EAAkBvgB,WAAW8f,EAAqBzD,aAAa,oBAE7C,YAAlBvlB,EAASnH,MAA2C,IAApB4wB,IAAyBnC,MAAMmC,KAC/DA,EAAkBta,KAGE,IAApBsa,GAAyBrE,EAAsB4D,EAAsB,aACrES,EAAkBta,KAGlBsa,EAAkB,IAClBzpB,EAAS2I,qBAAuB8gB,EAAkBzpB,EAASnE,WAG/D,IAAIrD,EAAW0Q,WAAW8f,EAAqBzD,aAAa,aA6D5D,IA5DAvlB,EAAS0pB,0BAA0C,IAAblxB,EAAkB2W,IAAW3W,EAAWwH,EAASnE,UAEvFmE,EAAS2pB,cAAgB,EACzB3pB,EAAS4pB,oBAAqB,EAGR,YAAlB5pB,EAASnH,MAAsBL,EAAW,IAC1CwH,EAASnH,KAAO,SAEhBmH,EAAS2I,qBAAuBnQ,EAAWwH,EAASnE,WAIlC,YAAlBmE,EAASnH,OACTmH,EAAS6pB,8BAA+B,EACxC7pB,EAAS8pB,qCAAsC,EAC/C9pB,EAAS+pB,0BAA2B,EACpC/pB,EAASjF,sBAAwB,IAAIkH,KAAK,OAI9CnC,EAxiBJ,SAAmBkpB,EAAsBntB,GACrC,MAAMiE,EAAS,CAAC,EAChB,IAAIkqB,EACAnqB,EAGJC,EAAOI,cAAgB,GACvB8pB,EAAUhB,EAAqBtC,qBAAqB,eACpD,IAAK,IAAI9pB,EAAI,EAAGA,EAAIotB,EAAQ9oB,OAAQtE,IAChCiD,EAAa2lB,EAAiBwE,EAAQptB,GAAIf,GACvB,OAAfgE,GACAC,EAAOI,cAAclC,KAAK6B,GAIlC,OAAOC,CACX,CAwhBamqB,CAAUjB,EAAsBhpB,EAASnE,WAClDmE,EAASC,OAAS,CAACH,GAGnBA,EAAOxH,MAAQ,OAUI+F,IAAf4qB,IACAK,EAAmBR,EAAOpC,qBAAqB,oBAAoB,GAInE4C,EAAiBY,WAAWxnB,KAAO4mB,EAAiBY,WAAWxnB,KAAK3B,QAAQ,SAAU,IAGtFooB,EAxOR,SAAoCG,GAChC,IAAIa,EACAC,EACAC,EACAlB,EAsER,IAA+BmB,EA5C3B,OAvBAH,EAAWxF,EAAO4F,YAAYjB,EAAiBY,WAAWxnB,MAG1D0nB,EAuBJ,SAAkCD,GAC9B,IAAIjpB,EACAspB,EACAC,EACAC,EACAC,EACA/tB,EAAI,EAaR,IARAsE,GAAUipB,EAASvtB,EAAI,IAAM,KAAOutB,EAASvtB,EAAI,IAAM,KAAOutB,EAASvtB,EAAI,IAAM,GAAKutB,EAASvtB,GAC/FA,GAAK,EAGL4tB,GAAeL,EAASvtB,EAAI,IAAM,GAAKutB,EAASvtB,GAChDA,GAAK,EAGEA,EAAIutB,EAASjpB,QAMhB,GAJAupB,GAAcN,EAASvtB,EAAI,IAAM,GAAKutB,EAASvtB,GAC/CA,GAAK,EAGc,IAAf6tB,EASA,OANAC,GAAgBP,EAASvtB,EAAI,IAAM,GAAKutB,EAASvtB,GACjDA,GAAK,EAGL+tB,EAAc,IAAIxc,WAAWuc,GAC7BC,EAAYlX,IAAI0W,EAASS,SAAShuB,EAAGA,EAAI8tB,IAClCC,EAIf,OAAO,IACX,CA9DgBE,CAAyBV,GAEjCC,IAEAA,EAAY,IAAI/B,YAAY+B,EAAUU,QAGtCV,EAAY1I,OAAOqJ,aAAartB,MAAM,KAAM0sB,GAG5CC,GAAa,IAAIW,WAAaC,gBAAgBb,EAAW,mBACzDjB,EAAMkB,EAAUa,cAAc,OAAOC,YAGrChC,EAAMxE,EAAO4F,YAAYpB,GAmD7BV,EAD2B6B,EA/CDnB,EAgDV,EAAG,GACnBV,EAAU6B,EAAM,EAAG,GACnB7B,EAAU6B,EAAM,EAAG,GACnB7B,EAAU6B,EAAM,EAAG,IAhDZnB,CACX,CAyMciC,CAA2B9B,GAGjC3c,EAnJR,SAAmC2c,GAK/B,MAAO,CACHlD,YAAa,gDACbtuB,MCjfkB,0BDkflBuzB,IAPM,CACNC,OAAQhC,EAAiBY,WAAWxnB,KACpC6oB,SAAU,QAOlB,CAyI4BC,CAA0BlC,GAC9C3c,EAAkB,oBAAsBwc,EACxCJ,EAAmB/qB,KAAK2O,GAGxBA,EA5IR,SAAyCwc,GACrC,IAAIsC,EAAa,CACbrF,YAAa,gDACbtuB,MC1fiB,sBD4frB,IAAKqxB,EACD,OAAOsC,EAGX,MAAMC,EAAe,IAAIvd,WAAW,EAAIgb,EAAIjoB,QAC5CwqB,EAAa,GAAK,GAClBA,EAAa,GAAK,GAClBA,EAAajY,IAAI0V,EAAK,GAGtB,MAAMjoB,EAAS,GAAyFwqB,EAAaxqB,OACrH,IAAIiU,EAAO,IAAIhH,WAAWjN,GACtBtE,EAAI,EA+BR,OA5BAuY,EAAKvY,MAAiB,WAATsE,IAAwB,GACrCiU,EAAKvY,MAAiB,SAATsE,IAAwB,GACrCiU,EAAKvY,MAAiB,MAATsE,IAAwB,EACrCiU,EAAKvY,KAAiB,IAATsE,EAGbiU,EAAK1B,IAAI,CAAC,IAAM,IAAM,IAAM,IAAM,EAAM,EAAM,EAAM,GAAO7W,GAC3DA,GAAK,EAGLuY,EAAK1B,IAAI,CAAC,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,GAAM,IAAM,IAAM,IAAM,GAAM,IAAM,IAAM,GAAM,GAAM,KAAO7W,GAC3GA,GAAK,GAGLuY,EAAKvY,MAA8B,WAAtB8uB,EAAaxqB,SAAwB,GAClDiU,EAAKvY,MAA8B,SAAtB8uB,EAAaxqB,SAAwB,GAClDiU,EAAKvY,MAA8B,MAAtB8uB,EAAaxqB,SAAwB,EAClDiU,EAAKvY,KAA8B,IAAtB8uB,EAAaxqB,OAG1BiU,EAAK1B,IAAIiY,EAAc9uB,GAGvBuY,EAAOuM,OAAOqJ,aAAartB,MAAM,KAAMyX,GACvCA,EAAOwP,EAAOgH,YAAYxW,GAE1BsW,EAAWtW,KAAO,CAAEmW,OAAQnW,GAErBsW,CACX,CA2F4BG,CAAgCzC,GACpDxc,EAAkB,oBAAsBwc,EACxCJ,EAAmB/qB,KAAK2O,GAExB3M,EAASwO,kBAAoBua,GAGjCG,EAAcppB,EAAOI,cAEhBtD,EAAI,EAAGA,EAAIssB,EAAYhoB,OAAQtE,GAAK,EACrCssB,EAAYtsB,GAAGwD,gBAAgByrB,eAAiB,mBAEbxtB,IAA/B2B,EAASwO,oBACT0a,EAAYtsB,GAAG4R,kBAAoBxO,EAASwO,kBAC5C0a,EAAYtsB,GAAG4R,kBAAoBxO,EAASwO,mBAGb,UAA/B0a,EAAYtsB,GAAGkpB,cAEfuD,EAAkBH,EAAYtsB,GAAGwD,gBAAgBC,gBAAgBC,EAAE,GAAGI,EAAIwoB,EAAYtsB,GAAGwD,gBAAgBvE,UAEzGmE,EAAS2pB,cAAgBN,EAEH,YAAlBrpB,EAASnH,MAELmH,EAAS2I,qBAAuB,GAChC3I,EAAS2I,uBAAyBwG,KAClCnP,EAAS2I,qBAAuBugB,EAAYtsB,GAAGwD,gBAAgBC,gBAAgB7H,WAC/EwH,EAAS2I,qBAAuBugB,EAAYtsB,GAAGwD,gBAAgBC,gBAAgB7H,WAa/F,GANAwH,EAAS2pB,cAAgBxnB,KAAKmgB,IAAItiB,EAAS2pB,cAAgB3pB,EAAS2I,qBAAuB3I,EAAS2I,qBAAuBwG,KAMrG,YAAlBnP,EAASnH,KAAoB,CAC7B,IAAIizB,EAAkBjH,EAASvtB,MAAMy0B,UAAU/pB,MAAMgqB,UAChDF,IAEDA,EAAkBzC,GADuE,OAA1DxE,EAASvtB,MAAMy0B,UAAU/pB,MAAMiqB,wBAAoC3E,MAAMzC,EAASvtB,MAAMy0B,UAAU/pB,MAAMiqB,wBAAkF,EAAxDpH,EAASvtB,MAAMy0B,UAAU/pB,MAAMiqB,yBAGpM,IAAIC,EAAqB/pB,KAAKC,IAAIpC,EAAS2I,qBAAuB,GAA+B3I,EAAS2I,qBAAuB,GAC7HqjB,EAAY7pB,KAAKmgB,IAAI4J,EAAoBJ,GAEzCK,EAAaH,EAA+B,IAAlB3C,EAG9BlE,EAAwB,CACpB,UAAa,CACT,OAAU,CACN,kBAAqBN,EAASvtB,MAAMy0B,UAAUjB,OAAOsB,kBACrD,uBAA0BvH,EAASvtB,MAAMy0B,UAAUjB,OAAOuB,uBAC1D,+BAAkCxH,EAASvtB,MAAMy0B,UAAUjB,OAAOwB,gCAEtE,gBAAmB,CACfC,wBAAyB1H,EAASvtB,MAAMy0B,UAAUS,gBAAgBD,yBAEtE,MAAS,CACL,UAAa1H,EAASvtB,MAAMy0B,UAAU/pB,MAAMgqB,aAKxDnH,EAAS4H,OAAO,CACZ,UAAa,CACT,OAAU,CACN,kBAAqBN,EACrB,uBAA0BA,EAC1B,+BAAkCA,GAEtC,gBAAmB,CACfI,yBAAyB,GAE7B,MAAS,CACL,UAAaP,KAI7B,CAQA,UALOhsB,EAASwO,kBAKM,WAAlBxO,EAASnH,KAAmB,CAG5B,IAAI6zB,EAAe9H,EAAc+H,WACjC,GAAID,GAAgBA,EAAatD,gBAC7BA,EAAkBsD,EAAatD,qBAE/B,IAAKxsB,EAAI,EAAGA,EAAIssB,EAAYhoB,OAAQtE,IAC5BssB,EAAYtsB,GAAGkpB,cAAgBrZ,EAAUoE,OAASqY,EAAYtsB,GAAGkpB,cAAgBrZ,EAAUkE,QAC3FxQ,EAAW+oB,EAAYtsB,GAAGwD,gBAAgBC,gBAAgBC,EAC1D1E,EAAYuE,EAAS,GAAGM,OACApC,IAApB+qB,IACAA,EAAkBxtB,GAEtBwtB,EAAkBjnB,KAAKmgB,IAAI8G,EAAiBxtB,GAG5CoE,EAAS0pB,0BAA4BvnB,KAAKmgB,IAAItiB,EAAS0pB,0BAA2BR,EAAYtsB,GAAGwD,gBAAgBC,gBAAgB7H,WAI7I,GAAI4wB,EAAkB,EAAG,CAGrB,IADAppB,EAASopB,gBAAkBA,EACtBxsB,EAAI,EAAGA,EAAIssB,EAAYhoB,OAAQtE,IAAK,CAErC,IADAuD,EAAW+oB,EAAYtsB,GAAGwD,gBAAgBC,gBAAgBC,EACrD+Z,EAAI,EAAGA,EAAIla,EAASe,OAAQmZ,IACxBla,EAASka,GAAGrZ,YACbb,EAASka,GAAGrZ,UAAYb,EAASka,GAAG5Z,EAAE4d,YAE1Cle,EAASka,GAAG5Z,GAAK2oB,EAEjBF,EAAYtsB,GAAGkpB,cAAgBrZ,EAAUoE,OAASqY,EAAYtsB,GAAGkpB,cAAgBrZ,EAAUkE,QAC3F7Q,EAAOxH,MAAQ6J,KAAKC,IAAIjC,EAAS,GAAGM,EAAGX,EAAOxH,OAC9C4wB,EAAYtsB,GAAGwD,gBAAgBwsB,uBAAyB9sB,EAAOxH,MAEvE,CACAwH,EAAOxH,OAAS0H,EAASnE,SAC7B,CACJ,CAOA,OAHAmE,EAAS0pB,0BAA4BvnB,KAAKgJ,MAA2C,IAArCnL,EAAS0pB,2BAAoC,IAC7F5pB,EAAOtH,SAAWwH,EAAS0pB,0BAEpB1pB,CACX,CA6DA,OARA3D,EAAW,CACPwwB,MAjCJ,SAAuBnqB,GACnB,IAAIomB,EAAS,KACT9oB,EAAW,KAEf,MAAMpE,EAAYkxB,OAAOC,YAAYC,MAGrClE,EA1BJ,SAAkBpmB,GACd,IAAIomB,EAAS,KAEb,GAAIgE,OAAO9B,YAGPlC,GAFe,IAAIgE,OAAO9B,WAEVC,gBAAgBvoB,EAAM,YAClComB,EAAOpC,qBAAqB,eAAexlB,OAAS,GACpD,MAAM,IAAIqL,MAAM,+BAIxB,OAAOuc,CACX,CAaamE,CAASvqB,GAElB,MAAMwqB,EAAeJ,OAAOC,YAAYC,MAExC,GAAe,OAAXlE,EACA,OAAO,KAIX9oB,EAAW6oB,EAAgBC,EAAQ,IAAI7mB,MAEvC,MAAMkrB,EAAeL,OAAOC,YAAYC,MAIxC,OAFAhuB,EAAO/C,KAAK,mCAAqCixB,EAAetxB,GAAWwxB,YAAY,GAAK,kBAAoBD,EAAeD,GAAcE,YAAY,GAAK,gBAAkBD,EAAevxB,GAAa,KAAMwxB,YAAY,GAAK,MAE5NptB,CACX,EAWIqtB,QAtCJ,WACI,OAAO,IACX,EAqCI/qB,MAVJ,WAEQ6iB,GACAN,EAAS4H,OAAOtH,EAExB,GA/wBInmB,EAASO,EAAMgD,UAAUlG,GAyxBtBA,CACX,CAEAqoB,EAAUpnB,sBAAwB,YAClC,MAAelB,EAAasC,gBAAgBgmB,GE50B5C,SAAS4I,EAAWvuB,GAEhBA,EAASA,GAAU,CAAC,EACpB,MAAMrC,EAAU1E,KAAK0E,QACf4L,EAAWvJ,EAAOuJ,SAClBjF,EAAStE,EAAOsE,OAChBoJ,EAAY1N,EAAO0N,UACnB8gB,EAAkBxuB,EAAOwuB,gBACzBplB,EAAqBpJ,EAAOoJ,mBAC5BqlB,EAAmBzuB,EAAOyuB,iBAChC,IAAIC,EACAC,EACAC,EACAtxB,EAUJ,SAASuxB,EAAmB/0B,GACxB,OAAO20B,EAAiBK,4BAA4B3vB,QAAO4vB,GAChDA,EAAUtsB,YAAc3I,IAChC,EACP,CAEA,SAASk1B,EAA0Bl1B,GAC/B,OAAO80B,EAAwBzvB,QAAO8vB,GAC1BA,EAAWxsB,YAAc3I,IAClC,EACP,CAkBA,SAASo1B,IAGYT,EAAiBK,4BACvBK,SAAQ,SAAUJ,GACzB,GAAIA,EAAUtsB,YAAciL,EAAUkE,OAClCmd,EAAUtsB,YAAciL,EAAUoE,OAClCid,EAAUtsB,YAAciL,EAAU0hB,KAAM,CAExC,IAAIC,EAAyBL,EAA0BD,EAAUtsB,WAC5D4sB,IACDA,EAAyBtvB,EAA0BpC,GAASiC,OAAO,CAC/DU,gBAAiByuB,EACjBxuB,kBAAmBP,EAAOO,kBAC1BC,MAAOR,EAAOQ,QAElB6uB,EAAuB7sB,aACvBosB,EAAwB3vB,KAAKowB,IAEjCA,EAAuB91B,OAC3B,CACJ,GACJ,CASA,SAAS+1B,EAAqB1sB,GAC1B,IAAItC,EAAkBuuB,EAAmBjsB,EAAEpG,WAC3C,IAAK8D,EACD,OAIJ,IACI5G,EAD2B4G,EAAgBM,8BACDC,2BAC1CkL,EAAYzL,EAAgBivB,eAE5B/tB,EAAU,IAAI5F,EAClB4F,EAAQhF,UAAY9C,EAAeoH,WAAWhH,KAC9C0H,EAAQ1H,KAAO00B,EACfhtB,EAAQvH,MAAQP,EAAeO,MAC/BuH,EAAQvF,UAAYvC,EAAeuC,UACnCuF,EAAQ9H,eAAiBA,EAEzB,MAAM81B,EAjEV,SAAyBhuB,EAAStI,EAAUS,GACxC,MAAM61B,EAAQ,IAAIC,EAYlB,OAVAD,EAAMt2B,SAAWA,EACjBs2B,EAAMr2B,YAAcqI,EAAQ1H,KAC5B01B,EAAMj2B,MAAQiI,EAAQ3E,UACtB2yB,EAAM/1B,SAAW+H,EAAQ/H,SACzB+1B,EAAMh2B,IAAMg2B,EAAMj2B,MAAQi2B,EAAM/1B,SAChC+1B,EAAMp2B,MAAQoI,EAAQpI,MACtBo2B,EAAMvzB,UAAYuF,EAAQvF,UAC1BuzB,EAAM91B,eAAiB8H,EAAQ9H,eAC/B81B,EAAM71B,YAAcA,EAEb61B,CACX,CAmDkBE,CAAgBluB,EAASuK,EAAU4jB,WAAW5d,GAAInP,EAAE9I,OAASwK,EAAOe,2BAElF,IAEImqB,EAAMl2B,MAAQq1B,EAAqBrf,aAAa5V,GAGhD6P,EAASsB,QAAQvG,EAAOsrB,qBACpB,CAAEJ,MAAOA,GACT,CAAEt2B,SAAU6S,EAAU4jB,WAAW5d,GAAIvV,UAAW9C,EAAeoH,WAAWhH,MAElF,CAAE,MAAO8I,GACL5C,EAAOsJ,WAAWxG,MAAM,IAAI+G,EAAYjH,EAAEa,KAAMb,EAAEc,QAASd,EAAEe,MACjE,CAGAf,EAAEmI,OAAS,IACf,CAEA,SAAS8kB,EAAqBjtB,GAC1B,GAAIA,EAAEE,MACF,OAGJ,IAAIxC,EAAkBuuB,EAAmBjsB,EAAEpB,QAAQhF,WACnD,IAAK8D,EACD,OAMJ,GAFAquB,EAAqB1W,gBAAgBrV,EAAGtC,GAEpCsC,EAAEpB,QAAQ1H,OAASF,EAAY2B,+BAAgC,CAE/D,IAAI8zB,EAAyBL,EAA0BpsB,EAAEpB,QAAQhF,WAC7D6yB,GACAA,EAAuB1sB,mBAAmBC,EAElD,CAGA,IAAI4H,EAAe5H,EAAEpB,QAAQ9H,eAAeqS,UAAU4jB,WAAWnlB,aAC5DA,EAAaslB,WAAatlB,EAAaulB,gBAAkB3f,KAC1D8e,GAER,CAEA,SAASc,IACD5mB,EAAmB6mB,gBAAmD,IAAjC7mB,EAAmBjG,WACxD+rB,GAER,CAEA,SAASgB,IACD9mB,EAAmB6mB,gBAAmD,IAAjC7mB,EAAmBjG,WACxD+rB,GAER,CAEA,SAASiB,EAAiBC,GACjBA,GAAkBA,EAAczsB,OAIrCysB,EAAczsB,KAAOysB,EAAczsB,KAAK3B,QAAQ,yCAA0C,6BAC9F,CAwCA,OATA1E,EAAW,CACPiG,MAtBJ,WACQmrB,IACAA,EAAUnrB,QACVmrB,OAAYpvB,GAGhBiK,EAAS8mB,IAAI/rB,EAAOgsB,qBAAsBhB,EAAsBr2B,MAChEsQ,EAAS8mB,IAAI/rB,EAAOuD,gBAAiBmoB,EAAkB/2B,MACvDsQ,EAAS8mB,IAAI/rB,EAAO4D,iBAAkBgoB,EAAmBj3B,MACzDsQ,EAAS8mB,IAAI/rB,EAAOc,2BAA4ByqB,EAAsB52B,MACtEsQ,EAAS8mB,IAAI/rB,EAAO4C,cAAeipB,EAAkBl3B,MA7GrD21B,EAAwBO,SAAQvT,IAC5BA,EAAErY,OAAO,IAEbqrB,EAA0B,EA8G9B,EASI2B,gBAPJ,WAEI,OADA7B,EAAY/I,EAAUhoB,GAASiC,OAAOI,GAC/B0uB,CACX,EAKI8B,2BApLJ,WACI7B,EAAuB7W,EAAqBna,GAASiC,OAAOI,EAChE,EAmLIywB,eAjCJ,WACIlnB,EAASmnB,GAAGpsB,EAAOgsB,qBAAsBhB,EAAsBhyB,EAAU,CAAEqzB,SAAUC,OAAOvzB,aAAaoC,0BAA0B8J,EAAS1K,gBAAgBgyB,sBAC5JtnB,EAASmnB,GAAGpsB,EAAOuD,gBAAiBmoB,EAAkB1yB,EAAU,CAAEqzB,SAAUC,OAAOvzB,aAAaoC,0BAA0B8J,EAAS1K,gBAAgBgyB,sBACnJtnB,EAASmnB,GAAGpsB,EAAO4D,iBAAkBgoB,EAAmB5yB,EAAU,CAAEqzB,SAAUC,OAAOvzB,aAAaoC,0BAA0B8J,EAAS1K,gBAAgBgyB,sBACrJtnB,EAASmnB,GAAGpsB,EAAOc,2BAA4ByqB,EAAsBvyB,EAAU,CAAEqzB,SAAUC,OAAOvzB,aAAaoC,0BAA0B8J,EAAS1K,gBAAgBgyB,sBAClKtnB,EAASmnB,GAAGpsB,EAAO4C,cAAeipB,EAAkB7yB,EACxD,GA7JIsxB,EAA0B,GA6LvBtxB,CACX,CAEAixB,EAAWhwB,sBAAwB,aACnC,MAAML,EAAU0yB,OAAOvzB,aAAasC,gBAAgB4uB,GACpDrwB,EAAQ0F,OAASkG,EACjB8mB,OAAOvzB,aAAayC,mBAAmByuB,EAAWhwB,sBAAuBL,GACzE,QC1NIP,EAA6B,oBAAXowB,QAA0BA,QAAW+C,OAEvDF,EAASjzB,EAAQizB,OAChBA,IACDA,EAASjzB,EAAQizB,OAAS,CAAC,GAG/BA,EAAOrC,WAAaA,EAEpB,Q", "sources": ["webpack://dashjs/webpack/bootstrap", "webpack://dashjs/webpack/runtime/define property getters", "webpack://dashjs/webpack/runtime/hasOwnProperty shorthand", "webpack://dashjs/webpack/runtime/make namespace object", "webpack://dashjs/./src/streaming/vo/DataChunk.js", "webpack://dashjs/./src/streaming/vo/metrics/HTTPRequest.js", "webpack://dashjs/./src/streaming/vo/FragmentRequest.js", "webpack://dashjs/./src/core/FactoryMaker.js", "webpack://dashjs/./src/mss/MssFragmentInfoController.js", "webpack://dashjs/./src/streaming/vo/DashJSError.js", "webpack://dashjs/./src/core/errors/ErrorsBase.js", "webpack://dashjs/./src/mss/errors/MssErrors.js", "webpack://dashjs/./src/core/events/EventsBase.js", "webpack://dashjs/./src/streaming/MediaPlayerEvents.js", "webpack://dashjs/./src/mss/MssFragmentMoofProcessor.js", "webpack://dashjs/./src/mss/MssFragmentMoovProcessor.js", "webpack://dashjs/./src/mss/MssFragmentProcessor.js", "webpack://dashjs/./externals/BigInteger.js", "webpack://dashjs/./src/mss/parser/MssParser.js", "webpack://dashjs/./src/streaming/constants/ProtectionConstants.js", "webpack://dashjs/./src/mss/MssHandler.js", "webpack://dashjs/./src/mss/index.js"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * @class\n * @ignore\n */\nclass DataChunk {\n    //Represents a data structure that keep all the necessary info about a single init/media segment\n    constructor() {\n        this.streamId = null;\n        this.segmentType = null;\n        this.index = NaN;\n        this.bytes = null;\n        this.start = NaN;\n        this.end = NaN;\n        this.duration = NaN;\n        this.representation = null;\n        this.endFragment = null;\n    }\n}\n\nexport default DataChunk;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @classdesc This Object holds reference to the HTTPRequest for manifest, fragment and xlink loading.\n * Members which are not defined in ISO23009-1 Annex D should be prefixed by a _ so that they are ignored\n * by Metrics Reporting code.\n * @ignore\n */\nclass HTTPRequest {\n    /**\n     * @class\n     */\n    constructor() {\n        /**\n         * Identifier of the TCP connection on which the HTTP request was sent.\n         * @public\n         */\n        this.tcpid = null;\n        /**\n         * This is an optional parameter and should not be included in HTTP request/response transactions for progressive download.\n         * The type of the request:\n         * - MPD\n         * - XLink expansion\n         * - Initialization Fragment\n         * - Index Fragment\n         * - Media Fragment\n         * - Bitstream Switching Fragment\n         * - other\n         * @public\n         */\n        this.type = null;\n        /**\n         * The original URL (before any redirects or failures)\n         * @public\n         */\n        this.url = null;\n        /**\n         * The actual URL requested, if different from above\n         * @public\n         */\n        this.actualurl = null;\n        /**\n         * The contents of the byte-range-spec part of the HTTP Range header.\n         * @public\n         */\n        this.range = null;\n        /**\n         * Real-Time | The real time at which the request was sent.\n         * @public\n         */\n        this.trequest = null;\n        /**\n         * Real-Time | The real time at which the first byte of the response was received.\n         * @public\n         */\n        this.tresponse = null;\n        /**\n         * The HTTP response code.\n         * @public\n         */\n        this.responsecode = null;\n        /**\n         * The duration of the throughput trace intervals (ms), for successful requests only.\n         * @public\n         */\n        this.interval = null;\n        /**\n         * Throughput traces, for successful requests only.\n         * @public\n         */\n        this.trace = [];\n        /**\n         * The CMSD static and dynamic values retrieved from CMSD response headers.\n         * @public\n         */\n        this.cmsd = null;\n\n        /**\n         * Type of stream (\"audio\" | \"video\" etc..)\n         * @public\n         */\n        this._stream = null;\n        /**\n         * Real-Time | The real time at which the request finished.\n         * @public\n         */\n        this._tfinish = null;\n        /**\n         * The duration of the media requests, if available, in seconds.\n         * @public\n         */\n        this._mediaduration = null;\n        /**\n         * all the response headers from request.\n         * @public\n         */\n        this._responseHeaders = null;\n        /**\n         * The selected service location for the request. string.\n         * @public\n         */\n        this._serviceLocation = null;\n        /**\n         * The type of the loader that was used. Distinguish between fetch loader and xhr loader\n         */\n        this._fileLoaderType = null;\n        /**\n         * The values derived from the ResourceTimingAPI.\n         */\n        this._resourceTimingValues = null;\n    }\n}\n\n/**\n * @classdesc This Object holds reference to the progress of the HTTPRequest.\n * @ignore\n */\nclass HTTPRequestTrace {\n    /**\n     * @class\n     */\n    constructor() {\n        /**\n         * Real-Time | Measurement stream start.\n         * @public\n         */\n        this.s = null;\n        /**\n         * Measurement stream duration (ms).\n         * @public\n         */\n        this.d = null;\n        /**\n         * List of integers counting the bytes received in each trace interval within the measurement stream.\n         * @public\n         */\n        this.b = [];\n    }\n}\n\nHTTPRequest.GET = 'GET';\nHTTPRequest.HEAD = 'HEAD';\nHTTPRequest.MPD_TYPE = 'MPD';\nHTTPRequest.XLINK_EXPANSION_TYPE = 'XLinkExpansion';\nHTTPRequest.INIT_SEGMENT_TYPE = 'InitializationSegment';\nHTTPRequest.INDEX_SEGMENT_TYPE = 'IndexSegment';\nHTTPRequest.MEDIA_SEGMENT_TYPE = 'MediaSegment';\nHTTPRequest.BITSTREAM_SWITCHING_SEGMENT_TYPE = 'BitstreamSwitchingSegment';\nHTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE = 'FragmentInfoSegment';\nHTTPRequest.DVB_REPORTING_TYPE = 'DVBReporting';\nHTTPRequest.LICENSE = 'license';\nHTTPRequest.CONTENT_STEERING_TYPE = 'ContentSteering';\nHTTPRequest.OTHER_TYPE = 'other';\n\nexport {HTTPRequest, HTTPRequestTrace};\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport {HTTPRequest} from './metrics/HTTPRequest.js';\n\n/**\n * @class\n * @ignore\n */\nclass FragmentRequest {\n    constructor(url) {\n        this.action = FragmentRequest.ACTION_DOWNLOAD;\n        this.availabilityEndTime = null;\n        this.availabilityStartTime = null;\n        this.bandwidth = NaN;\n        this.bytesLoaded = NaN;\n        this.bytesTotal = NaN;\n        this.delayLoadingTime = NaN;\n        this.duration = NaN;\n        this.endDate = null;\n        this.firstByteDate = null;\n        this.index = NaN;\n        this.mediaStartTime = NaN;\n        this.mediaType = null;\n        this.range = null;\n        this.representation = null;\n        this.responseType = 'arraybuffer';\n        this.retryAttempts = 0;\n        this.serviceLocation = null;\n        this.startDate = null;\n        this.startTime = NaN;\n        this.timescale = NaN;\n        this.type = null;\n        this.url = url || null;\n        this.wallStartTime = null;\n    }\n\n    isInitializationRequest() {\n        return (this.type && this.type === HTTPRequest.INIT_SEGMENT_TYPE);\n    }\n\n    setInfo(info) {\n        this.type = info && info.init ? HTTPRequest.INIT_SEGMENT_TYPE : HTTPRequest.MEDIA_SEGMENT_TYPE;\n        this.url = info && info.url ? info.url : null;\n        this.range = info && info.range ? info.range.start + '-' + info.range.end : null;\n        this.mediaType = info && info.mediaType ? info.mediaType : null;\n        this.representation = info && info.representation ? info.representation : null;\n    }\n}\n\nFragmentRequest.ACTION_DOWNLOAD = 'download';\nFragmentRequest.ACTION_COMPLETE = 'complete';\n\nexport default FragmentRequest;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @module FactoryMaker\n * @ignore\n */\nconst FactoryMaker = (function () {\n\n    let instance;\n    let singletonContexts = [];\n    const singletonFactories = {};\n    const classFactories = {};\n\n    function extend(name, childInstance, override, context) {\n        if (!context[name] && childInstance) {\n            context[name] = {\n                instance: childInstance,\n                override: override\n            };\n        }\n    }\n\n    /**\n     * Use this method from your extended object.  this.factory is injected into your object.\n     * this.factory.getSingletonInstance(this.context, 'VideoModel')\n     * will return the video model for use in the extended object.\n     *\n     * @param {Object} context - injected into extended object as this.context\n     * @param {string} className - string name found in all dash.js objects\n     * with name __dashjs_factory_name Will be at the bottom. Will be the same as the object's name.\n     * @returns {*} Context aware instance of specified singleton name.\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function getSingletonInstance(context, className) {\n        for (const i in singletonContexts) {\n            const obj = singletonContexts[i];\n            if (obj.context === context && obj.name === className) {\n                return obj.instance;\n            }\n        }\n        return null;\n    }\n\n    /**\n     * Use this method to add an singleton instance to the system.  Useful for unit testing to mock objects etc.\n     *\n     * @param {Object} context\n     * @param {string} className\n     * @param {Object} instance\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function setSingletonInstance(context, className, instance) {\n        for (const i in singletonContexts) {\n            const obj = singletonContexts[i];\n            if (obj.context === context && obj.name === className) {\n                singletonContexts[i].instance = instance;\n                return;\n            }\n        }\n        singletonContexts.push({\n            name: className,\n            context: context,\n            instance: instance\n        });\n    }\n\n    /**\n     * Use this method to remove all singleton instances associated with a particular context.\n     *\n     * @param {Object} context\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function deleteSingletonInstances(context) {\n        singletonContexts = singletonContexts.filter(x => x.context !== context);\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Factories storage Management\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function getFactoryByName(name, factoriesArray) {\n        return factoriesArray[name];\n    }\n\n    function updateFactory(name, factory, factoriesArray) {\n        if (name in factoriesArray) {\n            factoriesArray[name] = factory;\n        }\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Class Factories Management\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function updateClassFactory(name, factory) {\n        updateFactory(name, factory, classFactories);\n    }\n\n    function getClassFactoryByName(name) {\n        return getFactoryByName(name, classFactories);\n    }\n\n    function getClassFactory(classConstructor) {\n        let factory = getFactoryByName(classConstructor.__dashjs_factory_name, classFactories);\n\n        if (!factory) {\n            factory = function (context) {\n                if (context === undefined) {\n                    context = {};\n                }\n                return {\n                    create: function () {\n                        return merge(classConstructor, context, arguments);\n                    }\n                };\n            };\n\n            classFactories[classConstructor.__dashjs_factory_name] = factory; // store factory\n        }\n        return factory;\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Singleton Factory MAangement\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function updateSingletonFactory(name, factory) {\n        updateFactory(name, factory, singletonFactories);\n    }\n\n    function getSingletonFactoryByName(name) {\n        return getFactoryByName(name, singletonFactories);\n    }\n\n    function getSingletonFactory(classConstructor) {\n        let factory = getFactoryByName(classConstructor.__dashjs_factory_name, singletonFactories);\n        if (!factory) {\n            factory = function (context) {\n                let instance;\n                if (context === undefined) {\n                    context = {};\n                }\n                return {\n                    getInstance: function () {\n                        // If we don't have an instance yet check for one on the context\n                        if (!instance) {\n                            instance = getSingletonInstance(context, classConstructor.__dashjs_factory_name);\n                        }\n                        // If there's no instance on the context then create one\n                        if (!instance) {\n                            instance = merge(classConstructor, context, arguments);\n                            singletonContexts.push({\n                                name: classConstructor.__dashjs_factory_name,\n                                context: context,\n                                instance: instance\n                            });\n                        }\n                        return instance;\n                    }\n                };\n            };\n            singletonFactories[classConstructor.__dashjs_factory_name] = factory; // store factory\n        }\n\n        return factory;\n    }\n\n    function merge(classConstructor, context, args) {\n\n        let classInstance;\n        const className = classConstructor.__dashjs_factory_name;\n        const extensionObject = context[className];\n\n        if (extensionObject) {\n\n            let extension = extensionObject.instance;\n\n            if (extensionObject.override) { //Override public methods in parent but keep parent.\n\n                classInstance = classConstructor.apply({context}, args);\n                extension = extension.apply({\n                    context,\n                    factory: instance,\n                    parent: classInstance\n                }, args);\n\n                for (const prop in extension) {\n                    if (classInstance.hasOwnProperty(prop)) {\n                        classInstance[prop] = extension[prop];\n                    }\n                }\n\n            } else { //replace parent object completely with new object. Same as dijon.\n\n                return extension.apply({\n                    context,\n                    factory: instance\n                }, args);\n\n            }\n        } else {\n            // Create new instance of the class\n            classInstance = classConstructor.apply({context}, args);\n        }\n\n        // Add getClassName function to class instance prototype (used by Debug)\n        classInstance.getClassName = function () {return className;};\n\n        return classInstance;\n    }\n\n    instance = {\n        extend: extend,\n        getSingletonInstance: getSingletonInstance,\n        setSingletonInstance: setSingletonInstance,\n        deleteSingletonInstances: deleteSingletonInstances,\n        getSingletonFactory: getSingletonFactory,\n        getSingletonFactoryByName: getSingletonFactoryByName,\n        updateSingletonFactory: updateSingletonFactory,\n        getClassFactory: getClassFactory,\n        getClassFactoryByName: getClassFactoryByName,\n        updateClassFactory: updateClassFactory\n    };\n\n    return instance;\n\n}());\n\nexport default FactoryMaker;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport FragmentRequest from '../streaming/vo/FragmentRequest.js';\nimport {HTTPRequest} from '../streaming/vo/metrics/HTTPRequest.js';\nimport FactoryMaker from '../core/FactoryMaker.js';\n\nfunction MssFragmentInfoController(config) {\n\n    config = config || {};\n\n    let instance,\n        logger,\n        fragmentModel,\n        started,\n        type,\n        loadFragmentTimeout,\n        startTime,\n        startFragmentTime,\n        index;\n\n    const streamProcessor = config.streamProcessor;\n    const baseURLController = config.baseURLController;\n    const debug = config.debug;\n    const controllerType = 'MssFragmentInfoController';\n\n    function setup() {\n        logger = debug.getLogger(instance);\n    }\n\n    function initialize() {\n        type = streamProcessor.getType();\n        fragmentModel = streamProcessor.getFragmentModel();\n\n        started = false;\n        startTime = null;\n        startFragmentTime = null;\n    }\n\n    function start() {\n        if (started) {\n            return;\n        }\n\n        logger.debug('Start');\n\n        started = true;\n        index = 0;\n\n        loadNextFragmentInfo();\n    }\n\n    function stop() {\n        if (!started) {\n            return;\n        }\n\n        logger.debug('Stop');\n\n        clearTimeout(loadFragmentTimeout);\n        started = false;\n        startTime = null;\n        startFragmentTime = null;\n    }\n\n    function reset() {\n        stop();\n    }\n\n    function loadNextFragmentInfo() {\n        if (!started) {\n            return;\n        }\n\n        // Get last segment from SegmentTimeline\n        const representation = getCurrentRepresentation();\n        const manifest = representation.adaptation.period.mpd.manifest;\n        const adaptation = manifest.Period[representation.adaptation.period.index].AdaptationSet[representation.adaptation.index];\n        const segments = adaptation.SegmentTemplate.SegmentTimeline.S;\n        const segment = segments[segments.length - 1];\n\n        // logger.debug('Last fragment time: ' + (segment.t / adaptation.SegmentTemplate.timescale));\n\n        // Generate segment request\n        const request = getRequestForSegment(adaptation, representation, segment);\n\n        // Send segment request\n        requestFragment.call(this, request);\n    }\n\n    function getRequestForSegment(adaptation, representation, segment) {\n        let timescale = adaptation.SegmentTemplate.timescale;\n        let request = new FragmentRequest();\n\n        request.mediaType = type;\n        request.type = HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE;\n        // request.range = segment.mediaRange;\n        request.startTime = segment.t / timescale;\n        request.duration = segment.d / timescale;\n        request.timescale = timescale;\n        // request.availabilityStartTime = segment.availabilityStartTime;\n        // request.availabilityEndTime = segment.availabilityEndTime;\n        // request.wallStartTime = segment.wallStartTime;\n        request.bandwidth = representation.bandwidth;\n        request.index = index++;\n        request.adaptationIndex = representation.adaptation.index;\n        request.representation = representation;\n        request.url = baseURLController.resolve(representation.path).url + adaptation.SegmentTemplate.media;\n        request.url = request.url.replace('$Bandwidth$', representation.bandwidth);\n        request.url = request.url.replace('$Time$', segment.tManifest ? segment.tManifest : segment.t);\n        request.url = request.url.replace('/Fragments(', '/FragmentInfo(');\n\n        return request;\n    }\n\n    function getCurrentRepresentation() {\n        const representationController = streamProcessor.getRepresentationController();\n        const representation = representationController.getCurrentRepresentation();\n        return representation;\n    }\n\n    function requestFragment(request) {\n        // logger.debug('Load FragmentInfo for time: ' + request.startTime);\n        if (streamProcessor.getFragmentModel().isFragmentLoadedOrPending(request)) {\n            // We may have reached end of timeline in case of start-over streams\n            logger.debug('End of timeline');\n            stop();\n            return;\n        }\n\n        fragmentModel.executeRequest(request);\n    }\n\n    function fragmentInfoLoaded(e) {\n        if (!started) {\n            return;\n        }\n\n        const request = e.request;\n        if (!e.response) {\n            logger.error('Load error', request.url);\n            return;\n        }\n\n        let deltaFragmentTime,\n            deltaTime,\n            delay;\n\n        // logger.debug('FragmentInfo loaded: ', request.url);\n\n        if (startTime === null) {\n            startTime = new Date().getTime();\n        }\n\n        if (!startFragmentTime) {\n            startFragmentTime = request.startTime;\n        }\n\n        // Determine delay before requesting next FragmentInfo\n        deltaTime = (new Date().getTime() - startTime) / 1000;\n        deltaFragmentTime = (request.startTime + request.duration) - startFragmentTime;\n        delay = Math.max(0, (deltaFragmentTime - deltaTime));\n\n        // Set timeout for requesting next FragmentInfo\n        clearTimeout(loadFragmentTimeout);\n        loadFragmentTimeout = setTimeout(function () {\n            loadFragmentTimeout = null;\n            loadNextFragmentInfo();\n        }, delay * 1000);\n    }\n\n    function getType() {\n        return type;\n    }\n\n    instance = {\n        initialize: initialize,\n        controllerType: controllerType,\n        start: start,\n        fragmentInfoLoaded: fragmentInfoLoaded,\n        getType: getType,\n        reset: reset\n    };\n\n    setup();\n\n    return instance;\n}\n\nMssFragmentInfoController.__dashjs_factory_name = 'MssFragmentInfoController';\nexport default FactoryMaker.getClassFactory(MssFragmentInfoController);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass DashJSError {\n    constructor(code, message, data) {\n        this.code = code || null;\n        this.message = message || null;\n        this.data = data || null;\n    }\n}\n\nexport default DashJSError;", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass ErrorsBase {\n    extend(errors, config) {\n        if (!errors) {\n            return;\n        }\n\n        let override = config ? config.override : false;\n        let publicOnly = config ? config.publicOnly : false;\n\n\n        for (const err in errors) {\n            if (!errors.hasOwnProperty(err) || (this[err] && !override)) {\n                continue;\n            }\n            if (publicOnly && errors[err].indexOf('public_') === -1) {\n                continue;\n            }\n            this[err] = errors[err];\n\n        }\n    }\n}\n\nexport default ErrorsBase;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport ErrorsBase from '../../core/errors/ErrorsBase.js';\n\n/**\n * @class\n *\n */\nclass MssErrors extends ErrorsBase {\n    constructor() {\n        super();\n        /**\n         * Error code returned when no tfrf box is detected in MSS live stream\n         */\n        this.MSS_NO_TFRF_CODE = 200;\n\n        /**\n         * Error code returned when one of the codecs defined in the manifest is not supported\n         */\n        this.MSS_UNSUPPORTED_CODEC_CODE = 201;\n\n        this.MSS_NO_TFRF_MESSAGE = 'Missing tfrf in live media segment';\n        this.MSS_UNSUPPORTED_CODEC_MESSAGE = 'Unsupported codec';\n    }\n}\n\nlet mssErrors = new MssErrors();\nexport default mssErrors;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass EventsBase {\n    extend(events, config) {\n        if (!events) {\n            return;\n        }\n\n        let override = config ? config.override : false;\n        let publicOnly = config ? config.publicOnly : false;\n\n\n        for (const evt in events) {\n            if (!events.hasOwnProperty(evt) || (this[evt] && !override)) {\n                continue;\n            }\n            if (publicOnly && events[evt].indexOf('public_') === -1) {\n                continue;\n            }\n            this[evt] = events[evt];\n\n        }\n    }\n}\n\nexport default EventsBase;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport EventsBase from '../core/events/EventsBase.js';\n\n/**\n * @class\n * @implements EventsBase\n */\nclass MediaPlayerEvents extends EventsBase {\n\n    /**\n     * @description Public facing external events to be used when developing a player that implements dash.js.\n     */\n    constructor() {\n        super();\n        /**\n         * Triggered when playback will not start yet\n         * as the MPD's availabilityStartTime is in the future.\n         * Check delay property in payload to determine time before playback will start.\n         * @event MediaPlayerEvents#AST_IN_FUTURE\n         */\n        this.AST_IN_FUTURE = 'astInFuture';\n\n        /**\n         * Triggered when the BaseURLs have been updated.\n         * @event MediaPlayerEvents#BASE_URLS_UPDATED\n         */\n        this.BASE_URLS_UPDATED = 'baseUrlsUpdated';\n\n        /**\n         * Triggered when the video element's buffer state changes to stalled.\n         * Check mediaType in payload to determine type (Video, Audio, FragmentedText).\n         * @event MediaPlayerEvents#BUFFER_EMPTY\n         */\n        this.BUFFER_EMPTY = 'bufferStalled';\n\n        /**\n         * Triggered when the video element's buffer state changes to loaded.\n         * Check mediaType in payload to determine type (Video, Audio, FragmentedText).\n         * @event MediaPlayerEvents#BUFFER_LOADED\n         */\n        this.BUFFER_LOADED = 'bufferLoaded';\n\n        /**\n         * Triggered when the video element's buffer state changes, either stalled or loaded. Check payload for state.\n         * @event MediaPlayerEvents#BUFFER_LEVEL_STATE_CHANGED\n         */\n        this.BUFFER_LEVEL_STATE_CHANGED = 'bufferStateChanged';\n\n        /**\n         * Triggered when the buffer level of a media type has been updated\n         * @event MediaPlayerEvents#BUFFER_LEVEL_UPDATED\n         */\n        this.BUFFER_LEVEL_UPDATED = 'bufferLevelUpdated';\n\n        /**\n         * Triggered when a font signalled by a DVB Font Download has been added to the document FontFaceSet interface.\n         * @event MediaPlayerEvents#DVB_FONT_DOWNLOAD_ADDED\n         */\n        this.DVB_FONT_DOWNLOAD_ADDED = 'dvbFontDownloadAdded';\n\n        /**\n         * Triggered when a font signalled by a DVB Font Download has successfully downloaded and the FontFace can be used.\n         * @event MediaPlayerEvents#DVB_FONT_DOWNLOAD_COMPLETE\n         */\n        this.DVB_FONT_DOWNLOAD_COMPLETE = 'dvbFontDownloadComplete';\n\n        /**\n         * Triggered when a font signalled by a DVB Font Download could not be successfully downloaded, so the FontFace will not be used.\n         * @event MediaPlayerEvents#DVB_FONT_DOWNLOAD_FAILED\n         */\n        this.DVB_FONT_DOWNLOAD_FAILED = 'dvbFontDownloadFailed';\n\n        /**\n         * Triggered when a dynamic stream changed to static (transition phase between Live and On-Demand).\n         * @event MediaPlayerEvents#DYNAMIC_TO_STATIC\n         */\n        this.DYNAMIC_TO_STATIC = 'dynamicToStatic';\n\n        /**\n         * Triggered when there is an error from the element or MSE source buffer.\n         * @event MediaPlayerEvents#ERROR\n         */\n        this.ERROR = 'error';\n        /**\n         * Triggered when a fragment download has completed.\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_COMPLETED\n         */\n        this.FRAGMENT_LOADING_COMPLETED = 'fragmentLoadingCompleted';\n\n        /**\n         * Triggered when a partial fragment download has completed.\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_PROGRESS\n         */\n        this.FRAGMENT_LOADING_PROGRESS = 'fragmentLoadingProgress';\n        /**\n         * Triggered when a fragment download has started.\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_STARTED\n         */\n        this.FRAGMENT_LOADING_STARTED = 'fragmentLoadingStarted';\n\n        /**\n         * Triggered when a fragment download is abandoned due to detection of slow download base on the ABR abandon rule..\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_ABANDONED\n         */\n        this.FRAGMENT_LOADING_ABANDONED = 'fragmentLoadingAbandoned';\n\n        /**\n         * Triggered when {@link module:Debug} logger methods are called.\n         * @event MediaPlayerEvents#LOG\n         */\n        this.LOG = 'log';\n\n        /**\n         * Triggered when the manifest load is started\n         * @event MediaPlayerEvents#MANIFEST_LOADING_STARTED\n         */\n        this.MANIFEST_LOADING_STARTED = 'manifestLoadingStarted';\n\n        /**\n         * Triggered when the manifest loading is finished, providing the request object information\n         * @event MediaPlayerEvents#MANIFEST_LOADING_FINISHED\n         */\n        this.MANIFEST_LOADING_FINISHED = 'manifestLoadingFinished';\n\n        /**\n         * Triggered when the manifest load is complete, providing the payload\n         * @event MediaPlayerEvents#MANIFEST_LOADED\n         */\n        this.MANIFEST_LOADED = 'manifestLoaded';\n\n        /**\n         * Triggered anytime there is a change to the overall metrics.\n         * @event MediaPlayerEvents#METRICS_CHANGED\n         */\n        this.METRICS_CHANGED = 'metricsChanged';\n\n        /**\n         * Triggered when an individual metric is added, updated or cleared.\n         * @event MediaPlayerEvents#METRIC_CHANGED\n         */\n        this.METRIC_CHANGED = 'metricChanged';\n\n        /**\n         * Triggered every time a new metric is added.\n         * @event MediaPlayerEvents#METRIC_ADDED\n         */\n        this.METRIC_ADDED = 'metricAdded';\n\n        /**\n         * Triggered every time a metric is updated.\n         * @event MediaPlayerEvents#METRIC_UPDATED\n         */\n        this.METRIC_UPDATED = 'metricUpdated';\n\n        /**\n         * Triggered when a new stream (period) starts.\n         * @event MediaPlayerEvents#PERIOD_SWITCH_STARTED\n         */\n        this.PERIOD_SWITCH_STARTED = 'periodSwitchStarted';\n\n        /**\n         * Triggered at the stream end of a period.\n         * @event MediaPlayerEvents#PERIOD_SWITCH_COMPLETED\n         */\n        this.PERIOD_SWITCH_COMPLETED = 'periodSwitchCompleted';\n\n        /**\n         * Triggered when an ABR up /down switch is initiated; either by user in manual mode or auto mode via ABR rules.\n         * @event MediaPlayerEvents#QUALITY_CHANGE_REQUESTED\n         */\n        this.QUALITY_CHANGE_REQUESTED = 'qualityChangeRequested';\n\n        /**\n         * Triggered when the new ABR quality is being rendered on-screen.\n         * @event MediaPlayerEvents#QUALITY_CHANGE_RENDERED\n         */\n        this.QUALITY_CHANGE_RENDERED = 'qualityChangeRendered';\n\n        /**\n         * Triggered when the new track is being selected\n         * @event MediaPlayerEvents#NEW_TRACK_SELECTED\n         */\n        this.NEW_TRACK_SELECTED = 'newTrackSelected';\n\n        /**\n         * Triggered when the new track is being rendered.\n         * @event MediaPlayerEvents#TRACK_CHANGE_RENDERED\n         */\n        this.TRACK_CHANGE_RENDERED = 'trackChangeRendered';\n\n        /**\n         * Triggered when a stream (period) is being loaded\n         * @event MediaPlayerEvents#STREAM_INITIALIZING\n         */\n        this.STREAM_INITIALIZING = 'streamInitializing';\n\n        /**\n         * Triggered when a stream (period) is loaded\n         * @event MediaPlayerEvents#STREAM_UPDATED\n         */\n        this.STREAM_UPDATED = 'streamUpdated';\n\n        /**\n         * Triggered when a stream (period) is activated\n         * @event MediaPlayerEvents#STREAM_ACTIVATED\n         */\n        this.STREAM_ACTIVATED = 'streamActivated';\n\n        /**\n         * Triggered when a stream (period) is deactivated\n         * @event MediaPlayerEvents#STREAM_DEACTIVATED\n         */\n        this.STREAM_DEACTIVATED = 'streamDeactivated';\n\n        /**\n         * Triggered when a stream (period) is activated\n         * @event MediaPlayerEvents#STREAM_INITIALIZED\n         */\n        this.STREAM_INITIALIZED = 'streamInitialized';\n\n        /**\n         * Triggered when the player has been reset.\n         * @event MediaPlayerEvents#STREAM_TEARDOWN_COMPLETE\n         */\n        this.STREAM_TEARDOWN_COMPLETE = 'streamTeardownComplete';\n\n        /**\n         * Triggered once all text tracks detected in the MPD are added to the video element.\n         * @event MediaPlayerEvents#TEXT_TRACKS_ADDED\n         */\n        this.TEXT_TRACKS_ADDED = 'allTextTracksAdded';\n\n        /**\n         * Triggered when a text track is added to the video element's TextTrackList\n         * @event MediaPlayerEvents#TEXT_TRACK_ADDED\n         */\n        this.TEXT_TRACK_ADDED = 'textTrackAdded';\n\n        /**\n         * Triggered when a text track should be shown\n         * @event MediaPlayerEvents#CUE_ENTER\n         */\n        this.CUE_ENTER = 'cueEnter'\n\n        /**\n         * Triggered when a text track should be hidden\n         * @event MediaPlayerEvents#CUE_ENTER\n         */\n        this.CUE_EXIT = 'cueExit'\n\n        /**\n         * Triggered when a throughput measurement based on the last segment request has been stored\n         * @event MediaPlayerEvents#THROUGHPUT_MEASUREMENT_STORED\n         */\n        this.THROUGHPUT_MEASUREMENT_STORED = 'throughputMeasurementStored';\n\n        /**\n         * Triggered when a ttml chunk is parsed.\n         * @event MediaPlayerEvents#TTML_PARSED\n         */\n        this.TTML_PARSED = 'ttmlParsed';\n\n        /**\n         * Triggered when a ttml chunk has to be parsed.\n         * @event MediaPlayerEvents#TTML_TO_PARSE\n         */\n        this.TTML_TO_PARSE = 'ttmlToParse';\n\n        /**\n         * Triggered when a caption is rendered.\n         * @event MediaPlayerEvents#CAPTION_RENDERED\n         */\n        this.CAPTION_RENDERED = 'captionRendered';\n\n        /**\n         * Triggered when the caption container is resized.\n         * @event MediaPlayerEvents#CAPTION_CONTAINER_RESIZE\n         */\n        this.CAPTION_CONTAINER_RESIZE = 'captionContainerResize';\n\n        /**\n         * Sent when enough data is available that the media can be played,\n         * at least for a couple of frames.  This corresponds to the\n         * HAVE_ENOUGH_DATA readyState.\n         * @event MediaPlayerEvents#CAN_PLAY\n         */\n        this.CAN_PLAY = 'canPlay';\n\n        /**\n         * This corresponds to the CAN_PLAY_THROUGH readyState.\n         * @event MediaPlayerEvents#CAN_PLAY_THROUGH\n         */\n        this.CAN_PLAY_THROUGH = 'canPlayThrough';\n\n        /**\n         * Sent when playback completes.\n         * @event MediaPlayerEvents#PLAYBACK_ENDED\n         */\n        this.PLAYBACK_ENDED = 'playbackEnded';\n\n        /**\n         * Sent when an error occurs.  The element's error\n         * attribute contains more information.\n         * @event MediaPlayerEvents#PLAYBACK_ERROR\n         */\n        this.PLAYBACK_ERROR = 'playbackError';\n\n        /**\n         * This event is fired once the playback has been initialized by MediaPlayer.js.\n         * After that event methods such as setTextTrack() can be used.\n         * @event MediaPlayerEvents#PLAYBACK_INITIALIZED\n         */\n        this.PLAYBACK_INITIALIZED = 'playbackInitialized';\n\n        /**\n         * Sent when playback is not allowed (for example if user gesture is needed).\n         * @event MediaPlayerEvents#PLAYBACK_NOT_ALLOWED\n         */\n        this.PLAYBACK_NOT_ALLOWED = 'playbackNotAllowed';\n\n        /**\n         * The media's metadata has finished loading; all attributes now\n         * contain as much useful information as they're going to.\n         * @event MediaPlayerEvents#PLAYBACK_METADATA_LOADED\n         */\n        this.PLAYBACK_METADATA_LOADED = 'playbackMetaDataLoaded';\n\n        /**\n         * The event is fired when the frame at the current playback position of the media has finished loading;\n         * often the first frame\n         * @event MediaPlayerEvents#PLAYBACK_LOADED_DATA\n         */\n        this.PLAYBACK_LOADED_DATA = 'playbackLoadedData';\n\n        /**\n         * Sent when playback is paused.\n         * @event MediaPlayerEvents#PLAYBACK_PAUSED\n         */\n        this.PLAYBACK_PAUSED = 'playbackPaused';\n\n        /**\n         * Sent when the media begins to play (either for the first time, after having been paused,\n         * or after ending and then restarting).\n         *\n         * @event MediaPlayerEvents#PLAYBACK_PLAYING\n         */\n        this.PLAYBACK_PLAYING = 'playbackPlaying';\n\n        /**\n         * Sent periodically to inform interested parties of progress downloading\n         * the media. Information about the current amount of the media that has\n         * been downloaded is available in the media element's buffered attribute.\n         * @event MediaPlayerEvents#PLAYBACK_PROGRESS\n         */\n        this.PLAYBACK_PROGRESS = 'playbackProgress';\n\n        /**\n         * Sent when the playback speed changes.\n         * @event MediaPlayerEvents#PLAYBACK_RATE_CHANGED\n         */\n        this.PLAYBACK_RATE_CHANGED = 'playbackRateChanged';\n\n        /**\n         * Sent when a seek operation completes.\n         * @event MediaPlayerEvents#PLAYBACK_SEEKED\n         */\n        this.PLAYBACK_SEEKED = 'playbackSeeked';\n\n        /**\n         * Sent when a seek operation begins.\n         * @event MediaPlayerEvents#PLAYBACK_SEEKING\n         */\n        this.PLAYBACK_SEEKING = 'playbackSeeking';\n\n        /**\n         * Sent when the video element reports stalled\n         * @event MediaPlayerEvents#PLAYBACK_STALLED\n         */\n        this.PLAYBACK_STALLED = 'playbackStalled';\n\n        /**\n         * Sent when playback of the media starts after having been paused;\n         * that is, when playback is resumed after a prior pause event.\n         *\n         * @event MediaPlayerEvents#PLAYBACK_STARTED\n         */\n        this.PLAYBACK_STARTED = 'playbackStarted';\n\n        /**\n         * The time indicated by the element's currentTime attribute has changed.\n         * @event MediaPlayerEvents#PLAYBACK_TIME_UPDATED\n         */\n        this.PLAYBACK_TIME_UPDATED = 'playbackTimeUpdated';\n\n        /**\n         * Sent when the video element reports that the volume has changed\n         * @event MediaPlayerEvents#PLAYBACK_VOLUME_CHANGED\n         */\n        this.PLAYBACK_VOLUME_CHANGED = 'playbackVolumeChanged';\n\n        /**\n         * Sent when the media playback has stopped because of a temporary lack of data.\n         *\n         * @event MediaPlayerEvents#PLAYBACK_WAITING\n         */\n        this.PLAYBACK_WAITING = 'playbackWaiting';\n\n        /**\n         * Manifest validity changed - As a result of an MPD validity expiration event.\n         * @event MediaPlayerEvents#MANIFEST_VALIDITY_CHANGED\n         */\n        this.MANIFEST_VALIDITY_CHANGED = 'manifestValidityChanged';\n\n        /**\n         * Dash events are triggered at their respective start points on the timeline.\n         * @event MediaPlayerEvents#EVENT_MODE_ON_START\n         */\n        this.EVENT_MODE_ON_START = 'eventModeOnStart';\n\n        /**\n         * Dash events are triggered as soon as they were parsed.\n         * @event MediaPlayerEvents#EVENT_MODE_ON_RECEIVE\n         */\n        this.EVENT_MODE_ON_RECEIVE = 'eventModeOnReceive';\n\n        /**\n         * Event that is dispatched whenever the player encounters a potential conformance validation that might lead to unexpected/not optimal behavior\n         * @event MediaPlayerEvents#CONFORMANCE_VIOLATION\n         */\n        this.CONFORMANCE_VIOLATION = 'conformanceViolation';\n\n        /**\n         * Event that is dispatched whenever the player switches to a different representation\n         * @event MediaPlayerEvents#REPRESENTATION_SWITCH\n         */\n        this.REPRESENTATION_SWITCH = 'representationSwitch';\n\n        /**\n         * Event that is dispatched whenever an adaptation set is removed due to all representations not being supported.\n         * @event MediaPlayerEvents#ADAPTATION_SET_REMOVED_NO_CAPABILITIES\n         */\n        this.ADAPTATION_SET_REMOVED_NO_CAPABILITIES = 'adaptationSetRemovedNoCapabilities';\n\n        /**\n         * Triggered when a content steering request has completed.\n         * @event MediaPlayerEvents#CONTENT_STEERING_REQUEST_COMPLETED\n         */\n        this.CONTENT_STEERING_REQUEST_COMPLETED = 'contentSteeringRequestCompleted';\n\n        /**\n         * Triggered when an inband prft (ProducerReferenceTime) boxes has been received.\n         * @event MediaPlayerEvents#INBAND_PRFT\n         */\n        this.INBAND_PRFT = 'inbandPrft';\n\n        /**\n         * The streaming attribute of the Managed Media Source is true\n         * @type {string}\n         */\n        this.MANAGED_MEDIA_SOURCE_START_STREAMING = 'managedMediaSourceStartStreaming';\n\n        /**\n         * The streaming attribute of the Managed Media Source is false\n         * @type {string}\n         */\n        this.MANAGED_MEDIA_SOURCE_END_STREAMING = 'managedMediaSourceEndStreaming';\n    }\n}\n\nlet mediaPlayerEvents = new MediaPlayerEvents();\nexport default mediaPlayerEvents;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport DashJSError from '../streaming/vo/DashJSError.js';\nimport MssErrors from './errors/MssErrors.js';\n\nimport Events from '../streaming/MediaPlayerEvents.js';\nimport FactoryMaker from '../core/FactoryMaker.js';\n\n/**\n * @module MssFragmentMoofProcessor\n * @ignore\n * @param {Object} config object\n */\nfunction MssFragmentMoofProcessor(config) {\n\n    config = config || {};\n    let instance,\n        type,\n        logger;\n    const dashMetrics = config.dashMetrics;\n    const playbackController = config.playbackController;\n    const errorHandler = config.errHandler;\n    const eventBus = config.eventBus;\n    const ISOBoxer = config.ISOBoxer;\n    const debug = config.debug;\n\n    function setup() {\n        logger = debug.getLogger(instance);\n        type = '';\n    }\n\n    function processTfrf(request, tfrf, tfdt, streamProcessor) {\n        const representationController = streamProcessor.getRepresentationController();\n        const representation = representationController.getCurrentRepresentation();\n\n        const manifest = representation.adaptation.period.mpd.manifest;\n        const adaptation = manifest.Period[representation.adaptation.period.index].AdaptationSet[representation.adaptation.index];\n        const timescale = adaptation.SegmentTemplate.timescale;\n\n        type = streamProcessor.getType();\n\n        // Process tfrf only for live streams or start-over static streams (timeShiftBufferDepth > 0)\n        if (manifest.type !== 'dynamic' && !manifest.timeShiftBufferDepth) {\n            return;\n        }\n\n        if (!tfrf) {\n            errorHandler.error(new DashJSError(MssErrors.MSS_NO_TFRF_CODE, MssErrors.MSS_NO_TFRF_MESSAGE));\n            return;\n        }\n\n        // Get adaptation's segment timeline (always a SegmentTimeline in Smooth Streaming use case)\n        const segments = adaptation.SegmentTemplate.SegmentTimeline.S;\n        const entries = tfrf.entry;\n        let entry,\n            segmentTime,\n            range;\n        let segment = null;\n        let t = 0;\n        let endTime;\n        let availabilityStartTime = null;\n\n        if (entries.length === 0) {\n            return;\n        }\n\n        // Consider only first tfrf entry (to avoid pre-condition failure on fragment info requests)\n        entry = entries[0];\n\n        // In case of start-over streams, check if we have reached end of original manifest duration (set in timeShiftBufferDepth)\n        // => then do not update anymore timeline\n        if (manifest.type === 'static') {\n            // Get first segment time\n            segmentTime = segments[0].tManifest ? parseFloat(segments[0].tManifest) : segments[0].t;\n            if (entry.fragment_absolute_time > (segmentTime + (manifest.timeShiftBufferDepth * timescale))) {\n                return;\n            }\n        }\n\n        // logger.debug('entry - t = ', (entry.fragment_absolute_time / timescale));\n\n        // Get last segment time\n        segmentTime = segments[segments.length - 1].tManifest ? parseFloat(segments[segments.length - 1].tManifest) : segments[segments.length - 1].t;\n        // logger.debug('Last segment - t = ', (segmentTime / timescale));\n\n        // Check if we have to append new segment to timeline\n        if (entry.fragment_absolute_time <= segmentTime) {\n            // Update DVR window range => set range end to end time of current segment\n            range = {\n                start: segments[0].t / timescale,\n                end: (tfdt.baseMediaDecodeTime / timescale) + request.duration\n            };\n\n            updateDVR(request.mediaType, range, streamProcessor.getStreamInfo().manifestInfo);\n            return;\n        }\n\n        // logger.debug('Add new segment - t = ', (entry.fragment_absolute_time / timescale));\n        segment = {};\n        segment.t = entry.fragment_absolute_time;\n        segment.d = entry.fragment_duration;\n        // If timestamps starts at 0 relative to 1st segment (dynamic to static) then update segment time\n        if (segments[0].tManifest) {\n            segment.t -= parseFloat(segments[0].tManifest) - segments[0].t;\n            segment.tManifest = entry.fragment_absolute_time;\n        }\n\n        // Patch previous segment duration\n        let lastSegment = segments[segments.length - 1];\n        if (lastSegment.t + lastSegment.d !== segment.t) {\n            logger.debug('Patch segment duration - t = ', lastSegment.t + ', d = ' + lastSegment.d + ' => ' + (segment.t - lastSegment.t));\n            lastSegment.d = segment.t - lastSegment.t;\n        }\n\n        segments.push(segment);\n\n        // In case of static start-over streams, update content duration\n        if (manifest.type === 'static') {\n            if (type === 'video') {\n                segment = segments[segments.length - 1];\n                endTime = (segment.t + segment.d) / timescale;\n                if (endTime > representation.adaptation.period.duration) {\n                    eventBus.trigger(Events.MANIFEST_VALIDITY_CHANGED, { sender: this, newDuration: endTime });\n                }\n            }\n            return;\n        } else {\n            // In case of live streams, update segment timeline according to DVR window\n            if (manifest.timeShiftBufferDepth && manifest.timeShiftBufferDepth > 0) {\n                // Get timestamp of the last segment\n                segment = segments[segments.length - 1];\n                t = segment.t;\n\n                // Determine the segments' availability start time\n                availabilityStartTime = (t - (manifest.timeShiftBufferDepth * timescale)) / timescale;\n\n                // Remove segments prior to availability start time\n                segment = segments[0];\n                endTime = (segment.t + segment.d) / timescale;\n                while (endTime < availabilityStartTime) {\n                    // Check if not currently playing the segment to be removed\n                    if (!playbackController.isPaused() && playbackController.getTime() < endTime) {\n                        break;\n                    }\n                    // logger.debug('Remove segment  - t = ' + (segment.t / timescale));\n                    segments.splice(0, 1);\n                    segment = segments[0];\n                    endTime = (segment.t + segment.d) / timescale;\n                }\n            }\n\n            // Update DVR window range => set range end to end time of current segment\n            range = {\n                start: segments[0].t / timescale,\n                end: (tfdt.baseMediaDecodeTime / timescale) + request.duration\n            };\n\n            updateDVR(type, range, streamProcessor.getStreamInfo().manifestInfo);\n        }\n\n    }\n\n    function updateDVR(type, range, manifestInfo) {\n        if (type !== 'video' && type !== 'audio') {\n            return;\n        }\n        const dvrInfos = dashMetrics.getCurrentDVRInfo(type);\n        if (!dvrInfos || (range.end > dvrInfos.range.end)) {\n            logger.debug('Update DVR range: [' + range.start + ' - ' + range.end + ']');\n            dashMetrics.addDVRInfo(type, playbackController.getTime(), manifestInfo, range);\n            playbackController.updateCurrentTime(type);\n        }\n    }\n\n    // This function returns the offset of the 1st byte of a child box within a container box\n    function getBoxOffset(parent, type) {\n        let offset = 8;\n        let i = 0;\n\n        for (i = 0; i < parent.boxes.length; i++) {\n            if (parent.boxes[i].type === type) {\n                return offset;\n            }\n            offset += parent.boxes[i].size;\n        }\n        return offset;\n    }\n\n    function convertFragment(e, streamProcessor) {\n        let i;\n\n        // e.request contains request description object\n        // e.response contains fragment bytes\n        const isoFile = ISOBoxer.parseBuffer(e.response);\n        // Update track_Id in tfhd box\n        const tfhd = isoFile.fetch('tfhd');\n        tfhd.track_ID = e.request.representation.mediaInfo.index + 1;\n\n        // Add tfdt box\n        let tfdt = isoFile.fetch('tfdt');\n        const traf = isoFile.fetch('traf');\n        if (tfdt === null) {\n            tfdt = ISOBoxer.createFullBox('tfdt', traf, tfhd);\n            tfdt.version = 1;\n            tfdt.flags = 0;\n            tfdt.baseMediaDecodeTime = Math.floor(e.request.startTime * e.request.timescale);\n        }\n\n        const trun = isoFile.fetch('trun');\n\n        // Process tfxd boxes\n        // This box provide absolute timestamp but we take the segment start time for tfdt\n        let tfxd = isoFile.fetch('tfxd');\n        if (tfxd) {\n            tfxd._parent.boxes.splice(tfxd._parent.boxes.indexOf(tfxd), 1);\n            tfxd = null;\n        }\n        let tfrf = isoFile.fetch('tfrf');\n        processTfrf(e.request, tfrf, tfdt, streamProcessor);\n        if (tfrf) {\n            tfrf._parent.boxes.splice(tfrf._parent.boxes.indexOf(tfrf), 1);\n            tfrf = null;\n        }\n\n        // If protected content in PIFF1.1 format (sepiff box = Sample Encryption PIFF)\n        // => convert sepiff box it into a senc box\n        // => create saio and saiz boxes (if not already present)\n        const sepiff = isoFile.fetch('sepiff');\n        if (sepiff !== null) {\n            sepiff.type = 'senc';\n            sepiff.usertype = undefined;\n\n            let saio = isoFile.fetch('saio');\n            if (saio === null) {\n                // Create Sample Auxiliary Information Offsets Box box (saio)\n                saio = ISOBoxer.createFullBox('saio', traf);\n                saio.version = 0;\n                saio.flags = 0;\n                saio.entry_count = 1;\n                saio.offset = [0];\n\n                const saiz = ISOBoxer.createFullBox('saiz', traf);\n                saiz.version = 0;\n                saiz.flags = 0;\n                saiz.sample_count = sepiff.sample_count;\n                saiz.default_sample_info_size = 0;\n                saiz.sample_info_size = [];\n\n                if (sepiff.flags & 0x02) {\n                    // Sub-sample encryption => set sample_info_size for each sample\n                    for (i = 0; i < sepiff.sample_count; i += 1) {\n                        // 10 = 8 (InitializationVector field size) + 2 (subsample_count field size)\n                        // 6 = 2 (BytesOfClearData field size) + 4 (BytesOfEncryptedData field size)\n                        saiz.sample_info_size[i] = 10 + (6 * sepiff.entry[i].NumberOfEntries);\n                    }\n                } else {\n                    // No sub-sample encryption => set default sample_info_size = InitializationVector field size (8)\n                    saiz.default_sample_info_size = 8;\n                }\n            }\n        }\n\n        tfhd.flags &= 0xFFFFFE; // set tfhd.base-data-offset-present to false\n        tfhd.flags |= 0x020000; // set tfhd.default-base-is-moof to true\n        trun.flags |= 0x000001; // set trun.data-offset-present to true\n\n        // Update trun.data_offset field that corresponds to first data byte (inside mdat box)\n        const moof = isoFile.fetch('moof');\n        let length = moof.getLength();\n        trun.data_offset = length + 8;\n\n        // Update saio box offset field according to new senc box offset\n        let saio = isoFile.fetch('saio');\n        if (saio !== null) {\n            let trafPosInMoof = getBoxOffset(moof, 'traf');\n            let sencPosInTraf = getBoxOffset(traf, 'senc');\n            // Set offset from begin fragment to the first IV field in senc box\n            saio.offset[0] = trafPosInMoof + sencPosInTraf + 16; // 16 = box header (12) + sample_count field size (4)\n        }\n\n        // Write transformed/processed fragment into request reponse data\n        e.response = isoFile.write();\n    }\n\n    function updateSegmentList(e, streamProcessor) {\n        // e.request contains request description object\n        // e.response contains fragment bytes\n        if (!e.response) {\n            throw new Error('e.response parameter is missing');\n        }\n\n        const isoFile = ISOBoxer.parseBuffer(e.response);\n        // Update track_Id in tfhd box\n        const tfhd = isoFile.fetch('tfhd');\n        tfhd.track_ID = e.request.representation.mediaInfo.index + 1;\n\n        // Add tfdt box\n        let tfdt = isoFile.fetch('tfdt');\n        let traf = isoFile.fetch('traf');\n        if (tfdt === null) {\n            tfdt = ISOBoxer.createFullBox('tfdt', traf, tfhd);\n            tfdt.version = 1;\n            tfdt.flags = 0;\n            tfdt.baseMediaDecodeTime = Math.floor(e.request.startTime * e.request.timescale);\n        }\n\n        let tfrf = isoFile.fetch('tfrf');\n        processTfrf(e.request, tfrf, tfdt, streamProcessor);\n        if (tfrf) {\n            tfrf._parent.boxes.splice(tfrf._parent.boxes.indexOf(tfrf), 1);\n            tfrf = null;\n        }\n    }\n\n    function getType() {\n        return type;\n    }\n\n    instance = {\n        convertFragment,\n        updateSegmentList,\n        getType\n    };\n\n    setup();\n    return instance;\n}\n\nMssFragmentMoofProcessor.__dashjs_factory_name = 'MssFragmentMoofProcessor';\nexport default FactoryMaker.getClassFactory(MssFragmentMoofProcessor);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport MssErrors from './errors/MssErrors.js';\nimport FactoryMaker from '../core/FactoryMaker.js';\n\n/**\n * @module MssFragmentMoovProcessor\n * @ignore\n * @param {Object} config object\n */\nfunction MssFragmentMoovProcessor(config) {\n    config = config || {};\n    const NALUTYPE_SPS = 7;\n    const NALUTYPE_PPS = 8;\n    const constants = config.constants;\n    const ISOBoxer = config.ISOBoxer;\n\n    let protectionController = config.protectionController;\n    let instance,\n        period,\n        adaptationSet,\n        representation,\n        contentProtection,\n        timescale,\n        trackId;\n\n    function createFtypBox(isoFile) {\n        let ftyp = ISOBoxer.createBox('ftyp', isoFile);\n        ftyp.major_brand = 'iso6';\n        ftyp.minor_version = 1; // is an informative integer for the minor version of the major brand\n        ftyp.compatible_brands = []; //is a list, to the end of the box, of brands isom, iso6 and msdh\n        ftyp.compatible_brands[0] = 'isom'; // => decimal ASCII value for isom\n        ftyp.compatible_brands[1] = 'iso6'; // => decimal ASCII value for iso6\n        ftyp.compatible_brands[2] = 'msdh'; // => decimal ASCII value for msdh\n\n        return ftyp;\n    }\n\n    function createMoovBox(isoFile) {\n\n        // moov box\n        let moov = ISOBoxer.createBox('moov', isoFile);\n\n        // moov/mvhd\n        createMvhdBox(moov);\n\n        // moov/trak\n        let trak = ISOBoxer.createBox('trak', moov);\n\n        // moov/trak/tkhd\n        createTkhdBox(trak);\n\n        // moov/trak/mdia\n        let mdia = ISOBoxer.createBox('mdia', trak);\n\n        // moov/trak/mdia/mdhd\n        createMdhdBox(mdia);\n\n        // moov/trak/mdia/hdlr\n        createHdlrBox(mdia);\n\n        // moov/trak/mdia/minf\n        let minf = ISOBoxer.createBox('minf', mdia);\n\n        switch (adaptationSet.type) {\n            case constants.VIDEO:\n                // moov/trak/mdia/minf/vmhd\n                createVmhdBox(minf);\n                break;\n            case constants.AUDIO:\n                // moov/trak/mdia/minf/smhd\n                createSmhdBox(minf);\n                break;\n            default:\n                break;\n        }\n\n        // moov/trak/mdia/minf/dinf\n        let dinf = ISOBoxer.createBox('dinf', minf);\n\n        // moov/trak/mdia/minf/dinf/dref\n        createDrefBox(dinf);\n\n        // moov/trak/mdia/minf/stbl\n        let stbl = ISOBoxer.createBox('stbl', minf);\n\n        // Create empty stts, stsc, stco and stsz boxes\n        // Use data field as for codem-isoboxer unknown boxes for setting fields value\n\n        // moov/trak/mdia/minf/stbl/stts\n        let stts = ISOBoxer.createFullBox('stts', stbl);\n        stts._data = [0, 0, 0, 0, 0, 0, 0, 0]; // version = 0, flags = 0, entry_count = 0\n\n        // moov/trak/mdia/minf/stbl/stsc\n        let stsc = ISOBoxer.createFullBox('stsc', stbl);\n        stsc._data = [0, 0, 0, 0, 0, 0, 0, 0]; // version = 0, flags = 0, entry_count = 0\n\n        // moov/trak/mdia/minf/stbl/stco\n        let stco = ISOBoxer.createFullBox('stco', stbl);\n        stco._data = [0, 0, 0, 0, 0, 0, 0, 0]; // version = 0, flags = 0, entry_count = 0\n\n        // moov/trak/mdia/minf/stbl/stsz\n        let stsz = ISOBoxer.createFullBox('stsz', stbl);\n        stsz._data = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]; // version = 0, flags = 0, sample_size = 0, sample_count = 0\n\n        // moov/trak/mdia/minf/stbl/stsd\n        createStsdBox(stbl);\n\n        // moov/mvex\n        let mvex = ISOBoxer.createBox('mvex', moov);\n\n        // moov/mvex/trex\n        createTrexBox(mvex);\n\n        if (contentProtection && protectionController) {\n            let supportedKS = protectionController.getSupportedKeySystemMetadataFromContentProtection(contentProtection);\n            createProtectionSystemSpecificHeaderBox(moov, supportedKS);\n        }\n    }\n\n    function createMvhdBox(moov) {\n\n        let mvhd = ISOBoxer.createFullBox('mvhd', moov);\n\n        mvhd.version = 1; // version = 1  in order to have 64bits duration value\n\n        mvhd.creation_time = 0; // the creation time of the presentation => ignore (set to 0)\n        mvhd.modification_time = 0; // the most recent time the presentation was modified => ignore (set to 0)\n        mvhd.timescale = timescale; // the time-scale for the entire presentation => 10000000 for MSS\n        mvhd.duration = period.duration === Infinity ? 0xFFFFFFFFFFFFFFFF : Math.round(period.duration * timescale); // the length of the presentation (in the indicated timescale) =>  take duration of period\n        mvhd.rate = 1.0; // 16.16 number, '1.0' = normal playback\n        mvhd.volume = 1.0; // 8.8 number, '1.0' = full volume\n        mvhd.reserved1 = 0;\n        mvhd.reserved2 = [0x0, 0x0];\n        mvhd.matrix = [\n            1, 0, 0, // provides a transformation matrix for the video;\n            0, 1, 0, // (u,v,w) are restricted here to (0,0,1)\n            0, 0, 16384\n        ];\n        mvhd.pre_defined = [0, 0, 0, 0, 0, 0];\n        mvhd.next_track_ID = trackId + 1; // indicates a value to use for the track ID of the next track to be added to this presentation\n\n        return mvhd;\n    }\n\n    function createTkhdBox(trak) {\n\n        let tkhd = ISOBoxer.createFullBox('tkhd', trak);\n\n        tkhd.version = 1; // version = 1  in order to have 64bits duration value\n        tkhd.flags = 0x1 | // Track_enabled (0x000001): Indicates that the track is enabled\n            0x2 | // Track_in_movie (0x000002):  Indicates that the track is used in the presentation\n            0x4; // Track_in_preview (0x000004):  Indicates that the track is used when previewing the presentation\n\n        tkhd.creation_time = 0; // the creation time of the presentation => ignore (set to 0)\n        tkhd.modification_time = 0; // the most recent time the presentation was modified => ignore (set to 0)\n        tkhd.track_ID = trackId; // uniquely identifies this track over the entire life-time of this presentation\n        tkhd.reserved1 = 0;\n        tkhd.duration = period.duration === Infinity ? 0xFFFFFFFFFFFFFFFF : Math.round(period.duration * timescale); // the duration of this track (in the timescale indicated in the Movie Header Box) =>  take duration of period\n        tkhd.reserved2 = [0x0, 0x0];\n        tkhd.layer = 0; // specifies the front-to-back ordering of video tracks; tracks with lower numbers are closer to the viewer => 0 since only one video track\n        tkhd.alternate_group = 0; // specifies a group or collection of tracks => ignore\n        tkhd.volume = 1.0; // '1.0' = full volume\n        tkhd.reserved3 = 0;\n        tkhd.matrix = [\n            1, 0, 0, // provides a transformation matrix for the video;\n            0, 1, 0, // (u,v,w) are restricted here to (0,0,1)\n            0, 0, 16384\n        ];\n        tkhd.width = representation.width; // visual presentation width\n        tkhd.height = representation.height; // visual presentation height\n\n        return tkhd;\n    }\n\n    function createMdhdBox(mdia) {\n\n        let mdhd = ISOBoxer.createFullBox('mdhd', mdia);\n\n        mdhd.version = 1; // version = 1  in order to have 64bits duration value\n\n        mdhd.creation_time = 0; // the creation time of the presentation => ignore (set to 0)\n        mdhd.modification_time = 0; // the most recent time the presentation was modified => ignore (set to 0)\n        mdhd.timescale = timescale; // the time-scale for the entire presentation\n        mdhd.duration = period.duration === Infinity ? 0xFFFFFFFFFFFFFFFF : Math.round(period.duration * timescale); // the duration of this media (in the scale of the timescale). If the duration cannot be determined then duration is set to all 1s.\n        mdhd.language = adaptationSet.lang || 'und'; // declares the language code for this media\n        mdhd.pre_defined = 0;\n\n        return mdhd;\n    }\n\n    function createHdlrBox(mdia) {\n\n        let hdlr = ISOBoxer.createFullBox('hdlr', mdia);\n\n        hdlr.pre_defined = 0;\n        switch (adaptationSet.type) {\n            case constants.VIDEO:\n                hdlr.handler_type = 'vide';\n                break;\n            case constants.AUDIO:\n                hdlr.handler_type = 'soun';\n                break;\n            default:\n                hdlr.handler_type = 'meta';\n                break;\n        }\n        hdlr.name = representation.id;\n        hdlr.reserved = [0, 0, 0];\n\n        return hdlr;\n    }\n\n    function createVmhdBox(minf) {\n\n        let vmhd = ISOBoxer.createFullBox('vmhd', minf);\n\n        vmhd.flags = 1;\n\n        vmhd.graphicsmode = 0; // specifies a composition mode for this video track, from the following enumerated set, which may be extended by derived specifications: copy = 0 copy over the existing image\n        vmhd.opcolor = [0, 0, 0]; // is a set of 3 colour values (red, green, blue) available for use by graphics modes\n\n        return vmhd;\n    }\n\n    function createSmhdBox(minf) {\n\n        let smhd = ISOBoxer.createFullBox('smhd', minf);\n\n        smhd.flags = 1;\n\n        smhd.balance = 0; // is a fixed-point 8.8 number that places mono audio tracks in a stereo space; 0 is centre (the normal value); full left is -1.0 and full right is 1.0.\n        smhd.reserved = 0;\n\n        return smhd;\n    }\n\n    function createDrefBox(dinf) {\n\n        let dref = ISOBoxer.createFullBox('dref', dinf);\n\n        dref.entry_count = 1;\n        dref.entries = [];\n\n        let url = ISOBoxer.createFullBox('url ', dref, false);\n        url.location = '';\n        url.flags = 1;\n\n        dref.entries.push(url);\n\n        return dref;\n    }\n\n    function createStsdBox(stbl) {\n\n        let stsd = ISOBoxer.createFullBox('stsd', stbl);\n\n        stsd.entries = [];\n        switch (adaptationSet.type) {\n            case constants.VIDEO:\n            case constants.AUDIO:\n                stsd.entries.push(createSampleEntry(stsd));\n                break;\n            default:\n                break;\n        }\n\n        stsd.entry_count = stsd.entries.length; // is an integer that counts the actual entries\n        return stsd;\n    }\n\n    function createSampleEntry(stsd) {\n        let codec = representation.codecs.substring(0, representation.codecs.indexOf('.'));\n\n        switch (codec) {\n            case 'avc1':\n                return createAVCVisualSampleEntry(stsd, codec);\n            case 'mp4a':\n                return createMP4AudioSampleEntry(stsd, codec);\n            default:\n                throw {\n                    code: MssErrors.MSS_UNSUPPORTED_CODEC_CODE,\n                    message: MssErrors.MSS_UNSUPPORTED_CODEC_MESSAGE,\n                    data: {\n                        codec: codec\n                    }\n                };\n        }\n    }\n\n    function createAVCVisualSampleEntry(stsd, codec) {\n        let avc1;\n\n        if (contentProtection) {\n            avc1 = ISOBoxer.createBox('encv', stsd, false);\n        } else {\n            avc1 = ISOBoxer.createBox('avc1', stsd, false);\n        }\n\n        // SampleEntry fields\n        avc1.reserved1 = [0x0, 0x0, 0x0, 0x0, 0x0, 0x0];\n        avc1.data_reference_index = 1;\n\n        // VisualSampleEntry fields\n        avc1.pre_defined1 = 0;\n        avc1.reserved2 = 0;\n        avc1.pre_defined2 = [0, 0, 0];\n        avc1.height = representation.height;\n        avc1.width = representation.width;\n        avc1.horizresolution = 72; // 72 dpi\n        avc1.vertresolution = 72; // 72 dpi\n        avc1.reserved3 = 0;\n        avc1.frame_count = 1; // 1 compressed video frame per sample\n        avc1.compressorname = [\n            0x0A, 0x41, 0x56, 0x43, 0x20, 0x43, 0x6F, 0x64, // = 'AVC Coding';\n            0x69, 0x6E, 0x67, 0x00, 0x00, 0x00, 0x00, 0x00,\n            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,\n            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00\n        ];\n        avc1.depth = 0x0018; // 0x0018 – images are in colour with no alpha.\n        avc1.pre_defined3 = 65535;\n        avc1.config = createAVC1ConfigurationRecord();\n        if (contentProtection) {\n            // Create and add Protection Scheme Info Box\n            let sinf = ISOBoxer.createBox('sinf', avc1);\n\n            // Create and add Original Format Box => indicate codec type of the encrypted content\n            createOriginalFormatBox(sinf, codec);\n\n            // Create and add Scheme Type box\n            createSchemeTypeBox(sinf);\n\n            // Create and add Scheme Information Box\n            createSchemeInformationBox(sinf);\n        }\n\n        return avc1;\n    }\n\n    function createAVC1ConfigurationRecord() {\n\n        let avcC = null;\n        let avcCLength = 15; // length = 15 by default (0 SPS and 0 PPS)\n\n        // First get all SPS and PPS from codecPrivateData\n        let sps = [];\n        let pps = [];\n        let AVCProfileIndication = 0;\n        let AVCLevelIndication = 0;\n        let profile_compatibility = 0;\n\n        let nalus = representation.codecPrivateData.split('00000001').slice(1);\n        let naluBytes, naluType;\n\n        for (let i = 0; i < nalus.length; i++) {\n            naluBytes = hexStringtoBuffer(nalus[i]);\n\n            naluType = naluBytes[0] & 0x1F;\n\n            switch (naluType) {\n                case NALUTYPE_SPS:\n                    sps.push(naluBytes);\n                    avcCLength += naluBytes.length + 2; // 2 = sequenceParameterSetLength field length\n                    break;\n                case NALUTYPE_PPS:\n                    pps.push(naluBytes);\n                    avcCLength += naluBytes.length + 2; // 2 = pictureParameterSetLength field length\n                    break;\n                default:\n                    break;\n            }\n        }\n\n        // Get profile and level from SPS\n        if (sps.length > 0) {\n            AVCProfileIndication = sps[0][1];\n            profile_compatibility = sps[0][2];\n            AVCLevelIndication = sps[0][3];\n        }\n\n        // Generate avcC buffer\n        avcC = new Uint8Array(avcCLength);\n\n        let i = 0;\n        // length\n        avcC[i++] = (avcCLength & 0xFF000000) >> 24;\n        avcC[i++] = (avcCLength & 0x00FF0000) >> 16;\n        avcC[i++] = (avcCLength & 0x0000FF00) >> 8;\n        avcC[i++] = (avcCLength & 0x000000FF);\n        avcC.set([0x61, 0x76, 0x63, 0x43], i); // type = 'avcC'\n        i += 4;\n        avcC[i++] = 1; // configurationVersion = 1\n        avcC[i++] = AVCProfileIndication;\n        avcC[i++] = profile_compatibility;\n        avcC[i++] = AVCLevelIndication;\n        avcC[i++] = 0xFF; // '11111' + lengthSizeMinusOne = 3\n        avcC[i++] = 0xE0 | sps.length; // '111' + numOfSequenceParameterSets\n        for (let n = 0; n < sps.length; n++) {\n            avcC[i++] = (sps[n].length & 0xFF00) >> 8;\n            avcC[i++] = (sps[n].length & 0x00FF);\n            avcC.set(sps[n], i);\n            i += sps[n].length;\n        }\n        avcC[i++] = pps.length; // numOfPictureParameterSets\n        for (let n = 0; n < pps.length; n++) {\n            avcC[i++] = (pps[n].length & 0xFF00) >> 8;\n            avcC[i++] = (pps[n].length & 0x00FF);\n            avcC.set(pps[n], i);\n            i += pps[n].length;\n        }\n\n        return avcC;\n    }\n\n    function createMP4AudioSampleEntry(stsd, codec) {\n        let mp4a;\n\n        if (contentProtection) {\n            mp4a = ISOBoxer.createBox('enca', stsd, false);\n        } else {\n            mp4a = ISOBoxer.createBox('mp4a', stsd, false);\n        }\n\n        // SampleEntry fields\n        mp4a.reserved1 = [0x0, 0x0, 0x0, 0x0, 0x0, 0x0];\n        mp4a.data_reference_index = 1;\n\n        // AudioSampleEntry fields\n        mp4a.reserved2 = [0x0, 0x0];\n        mp4a.channelcount = representation.audioChannels;\n        mp4a.samplesize = 16;\n        mp4a.pre_defined = 0;\n        mp4a.reserved_3 = 0;\n        mp4a.samplerate = representation.audioSamplingRate << 16;\n\n        mp4a.esds = createMPEG4AACESDescriptor();\n\n        if (contentProtection) {\n            // Create and add Protection Scheme Info Box\n            let sinf = ISOBoxer.createBox('sinf', mp4a);\n\n            // Create and add Original Format Box => indicate codec type of the encrypted content\n            createOriginalFormatBox(sinf, codec);\n\n            // Create and add Scheme Type box\n            createSchemeTypeBox(sinf);\n\n            // Create and add Scheme Information Box\n            createSchemeInformationBox(sinf);\n        }\n\n        return mp4a;\n    }\n\n    function createMPEG4AACESDescriptor() {\n\n        // AudioSpecificConfig (see ISO/IEC 14496-3, subpart 1) => corresponds to hex bytes contained in 'codecPrivateData' field\n        let audioSpecificConfig = hexStringtoBuffer(representation.codecPrivateData);\n\n        // ESDS length = esds box header length (= 12) +\n        //               ES_Descriptor header length (= 5) +\n        //               DecoderConfigDescriptor header length (= 15) +\n        //               decoderSpecificInfo header length (= 2) +\n        //               AudioSpecificConfig length (= codecPrivateData length)\n        let esdsLength = 34 + audioSpecificConfig.length;\n        let esds = new Uint8Array(esdsLength);\n\n        let i = 0;\n        // esds box\n        esds[i++] = (esdsLength & 0xFF000000) >> 24; // esds box length\n        esds[i++] = (esdsLength & 0x00FF0000) >> 16; // ''\n        esds[i++] = (esdsLength & 0x0000FF00) >> 8; // ''\n        esds[i++] = (esdsLength & 0x000000FF); // ''\n        esds.set([0x65, 0x73, 0x64, 0x73], i); // type = 'esds'\n        i += 4;\n        esds.set([0, 0, 0, 0], i); // version = 0, flags = 0\n        i += 4;\n        // ES_Descriptor (see ISO/IEC 14496-1 (Systems))\n        esds[i++] = 0x03; // tag = 0x03 (ES_DescrTag)\n        esds[i++] = 20 + audioSpecificConfig.length; // size\n        esds[i++] = (trackId & 0xFF00) >> 8; // ES_ID = track_id\n        esds[i++] = (trackId & 0x00FF); // ''\n        esds[i++] = 0; // flags and streamPriority\n\n        // DecoderConfigDescriptor (see ISO/IEC 14496-1 (Systems))\n        esds[i++] = 0x04; // tag = 0x04 (DecoderConfigDescrTag)\n        esds[i++] = 15 + audioSpecificConfig.length; // size\n        esds[i++] = 0x40; // objectTypeIndication = 0x40 (MPEG-4 AAC)\n        esds[i] = 0x05 << 2; // streamType = 0x05 (Audiostream)\n        esds[i] |= 0 << 1; // upStream = 0\n        esds[i++] |= 1; // reserved = 1\n        esds[i++] = 0xFF; // buffersizeDB = undefined\n        esds[i++] = 0xFF; // ''\n        esds[i++] = 0xFF; // ''\n        esds[i++] = (representation.bandwidth & 0xFF000000) >> 24; // maxBitrate\n        esds[i++] = (representation.bandwidth & 0x00FF0000) >> 16; // ''\n        esds[i++] = (representation.bandwidth & 0x0000FF00) >> 8; // ''\n        esds[i++] = (representation.bandwidth & 0x000000FF); // ''\n        esds[i++] = (representation.bandwidth & 0xFF000000) >> 24; // avgbitrate\n        esds[i++] = (representation.bandwidth & 0x00FF0000) >> 16; // ''\n        esds[i++] = (representation.bandwidth & 0x0000FF00) >> 8; // ''\n        esds[i++] = (representation.bandwidth & 0x000000FF); // ''\n\n        // DecoderSpecificInfo (see ISO/IEC 14496-1 (Systems))\n        esds[i++] = 0x05; // tag = 0x05 (DecSpecificInfoTag)\n        esds[i++] = audioSpecificConfig.length; // size\n        esds.set(audioSpecificConfig, i); // AudioSpecificConfig bytes\n\n        return esds;\n    }\n\n    function createOriginalFormatBox(sinf, codec) {\n        let frma = ISOBoxer.createBox('frma', sinf);\n        frma.data_format = stringToCharCode(codec);\n    }\n\n    function createSchemeTypeBox(sinf) {\n        let schm = ISOBoxer.createFullBox('schm', sinf);\n\n        schm.flags = 0;\n        schm.version = 0;\n        schm.scheme_type = 0x63656E63; // 'cenc' => common encryption\n        schm.scheme_version = 0x00010000; // version set to 0x00010000 (Major version 1, Minor version 0)\n    }\n\n    function createSchemeInformationBox(sinf) {\n        let schi = ISOBoxer.createBox('schi', sinf);\n\n        // Create and add Track Encryption Box\n        createTrackEncryptionBox(schi);\n    }\n\n    function createProtectionSystemSpecificHeaderBox(moov, keySystems) {\n        let pssh_bytes,\n            pssh,\n            i,\n            parsedBuffer;\n\n        for (i = 0; i < keySystems.length; i += 1) {\n            pssh_bytes = keySystems[i].initData;\n            if (pssh_bytes) {\n                parsedBuffer = ISOBoxer.parseBuffer(pssh_bytes);\n                pssh = parsedBuffer.fetch('pssh');\n                if (pssh) {\n                    ISOBoxer.Utils.appendBox(moov, pssh);\n                }\n            }\n        }\n    }\n\n    function createTrackEncryptionBox(schi) {\n        let tenc = ISOBoxer.createFullBox('tenc', schi);\n\n        tenc.flags = 0;\n        tenc.version = 0;\n\n        tenc.default_IsEncrypted = 0x1;\n        tenc.default_IV_size = 8;\n        tenc.default_KID = (contentProtection && (contentProtection.length) > 0 && contentProtection[0]['cenc:default_KID']) ?\n            contentProtection[0]['cenc:default_KID'] : [0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0];\n    }\n\n    function createTrexBox(moov) {\n        let trex = ISOBoxer.createFullBox('trex', moov);\n\n        trex.track_ID = trackId;\n        trex.default_sample_description_index = 1;\n        trex.default_sample_duration = 0;\n        trex.default_sample_size = 0;\n        trex.default_sample_flags = 0;\n\n        return trex;\n    }\n\n    function hexStringtoBuffer(str) {\n        let buf = new Uint8Array(str.length / 2);\n        let i;\n\n        for (i = 0; i < str.length / 2; i += 1) {\n            buf[i] = parseInt('' + str[i * 2] + str[i * 2 + 1], 16);\n        }\n        return buf;\n    }\n\n    function stringToCharCode(str) {\n        let code = 0;\n        let i;\n\n        for (i = 0; i < str.length; i += 1) {\n            code |= str.charCodeAt(i) << ((str.length - i - 1) * 8);\n        }\n        return code;\n    }\n\n    function generateMoov(rep) {\n        if (!rep || !rep.adaptation) {\n            return;\n        }\n\n        let isoFile,\n            arrayBuffer;\n\n        representation = rep;\n        adaptationSet = representation.adaptation;\n\n        period = adaptationSet.period;\n        trackId = adaptationSet.index + 1;\n        contentProtection = period.mpd.manifest.Period[period.index].AdaptationSet[adaptationSet.index].ContentProtection;\n\n        timescale = period.mpd.manifest.Period[period.index].AdaptationSet[adaptationSet.index].SegmentTemplate.timescale;\n\n        isoFile = ISOBoxer.createFile();\n        createFtypBox(isoFile);\n        createMoovBox(isoFile);\n\n        arrayBuffer = isoFile.write();\n\n        return arrayBuffer;\n    }\n\n    instance = {\n        generateMoov: generateMoov\n    };\n\n    return instance;\n}\n\nMssFragmentMoovProcessor.__dashjs_factory_name = 'MssFragmentMoovProcessor';\nexport default FactoryMaker.getClassFactory(MssFragmentMoovProcessor);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport MssFragmentMoofProcessor from './MssFragmentMoofProcessor.js';\nimport MssFragmentMoovProcessor from './MssFragmentMoovProcessor.js';\nimport {HTTPRequest} from '../streaming/vo/metrics/HTTPRequest.js';\nimport FactoryMaker from '../core/FactoryMaker.js';\n\n\n// Add specific box processors not provided by codem-isoboxer library\n\nfunction arrayEqual(arr1, arr2) {\n    return (arr1.length === arr2.length) && arr1.every(function (element, index) {\n        return element === arr2[index];\n    });\n}\n\nfunction saioProcessor() {\n    this._procFullBox();\n    if (this.flags & 1) {\n        this._procField('aux_info_type', 'uint', 32);\n        this._procField('aux_info_type_parameter', 'uint', 32);\n    }\n    this._procField('entry_count', 'uint', 32);\n    this._procFieldArray('offset', this.entry_count, 'uint', (this.version === 1) ? 64 : 32);\n}\n\nfunction saizProcessor() {\n    this._procFullBox();\n    if (this.flags & 1) {\n        this._procField('aux_info_type', 'uint', 32);\n        this._procField('aux_info_type_parameter', 'uint', 32);\n    }\n    this._procField('default_sample_info_size', 'uint', 8);\n    this._procField('sample_count', 'uint', 32);\n    if (this.default_sample_info_size === 0) {\n        this._procFieldArray('sample_info_size', this.sample_count, 'uint', 8);\n    }\n}\n\nfunction sencProcessor() {\n    this._procFullBox();\n    this._procField('sample_count', 'uint', 32);\n    if (this.flags & 1) {\n        this._procField('IV_size', 'uint', 8);\n    }\n    this._procEntries('entry', this.sample_count, function (entry) {\n        this._procEntryField(entry, 'InitializationVector', 'data', 8);\n        if (this.flags & 2) {\n            this._procEntryField(entry, 'NumberOfEntries', 'uint', 16);\n            this._procSubEntries(entry, 'clearAndCryptedData', entry.NumberOfEntries, function (clearAndCryptedData) {\n                this._procEntryField(clearAndCryptedData, 'BytesOfClearData', 'uint', 16);\n                this._procEntryField(clearAndCryptedData, 'BytesOfEncryptedData', 'uint', 32);\n            });\n        }\n    });\n}\n\nfunction uuidProcessor() {\n    let tfxdUserType = [0x6D, 0x1D, 0x9B, 0x05, 0x42, 0xD5, 0x44, 0xE6, 0x80, 0xE2, 0x14, 0x1D, 0xAF, 0xF7, 0x57, 0xB2];\n    let tfrfUserType = [0xD4, 0x80, 0x7E, 0xF2, 0xCA, 0x39, 0x46, 0x95, 0x8E, 0x54, 0x26, 0xCB, 0x9E, 0x46, 0xA7, 0x9F];\n    let sepiffUserType = [0xA2, 0x39, 0x4F, 0x52, 0x5A, 0x9B, 0x4f, 0x14, 0xA2, 0x44, 0x6C, 0x42, 0x7C, 0x64, 0x8D, 0xF4];\n\n    if (arrayEqual(this.usertype, tfxdUserType)) {\n        this._procFullBox();\n        if (this._parsing) {\n            this.type = 'tfxd';\n        }\n        this._procField('fragment_absolute_time', 'uint', (this.version === 1) ? 64 : 32);\n        this._procField('fragment_duration', 'uint', (this.version === 1) ? 64 : 32);\n    }\n\n    if (arrayEqual(this.usertype, tfrfUserType)) {\n        this._procFullBox();\n        if (this._parsing) {\n            this.type = 'tfrf';\n        }\n        this._procField('fragment_count', 'uint', 8);\n        this._procEntries('entry', this.fragment_count, function (entry) {\n            this._procEntryField(entry, 'fragment_absolute_time', 'uint', (this.version === 1) ? 64 : 32);\n            this._procEntryField(entry, 'fragment_duration', 'uint', (this.version === 1) ? 64 : 32);\n        });\n    }\n\n    if (arrayEqual(this.usertype, sepiffUserType)) {\n        if (this._parsing) {\n            this.type = 'sepiff';\n        }\n        sencProcessor.call(this);\n    }\n}\n\nfunction MssFragmentProcessor(config) {\n\n    config = config || {};\n    const context = this.context;\n    const dashMetrics = config.dashMetrics;\n    const playbackController = config.playbackController;\n    const eventBus = config.eventBus;\n    const protectionController = config.protectionController;\n    const ISOBoxer = config.ISOBoxer;\n    const debug = config.debug;\n    let mssFragmentMoovProcessor,\n        mssFragmentMoofProcessor,\n        instance;\n\n    function setup() {\n        ISOBoxer.addBoxProcessor('uuid', uuidProcessor);\n        ISOBoxer.addBoxProcessor('saio', saioProcessor);\n        ISOBoxer.addBoxProcessor('saiz', saizProcessor);\n        ISOBoxer.addBoxProcessor('senc', sencProcessor);\n\n        mssFragmentMoovProcessor = MssFragmentMoovProcessor(context).create({\n            protectionController: protectionController,\n            constants: config.constants,\n            ISOBoxer: ISOBoxer});\n\n        mssFragmentMoofProcessor = MssFragmentMoofProcessor(context).create({\n            dashMetrics: dashMetrics,\n            playbackController: playbackController,\n            ISOBoxer: ISOBoxer,\n            eventBus: eventBus,\n            debug: debug,\n            errHandler: config.errHandler\n        });\n    }\n\n    function generateMoov(rep) {\n        return mssFragmentMoovProcessor.generateMoov(rep);\n    }\n\n    function processFragment(e, streamProcessor) {\n        if (!e || !e.request || !e.response) {\n            throw new Error('e parameter is missing or malformed');\n        }\n\n        if (e.request.type === 'MediaSegment') {\n            // MediaSegment => convert to Smooth Streaming moof format\n            mssFragmentMoofProcessor.convertFragment(e, streamProcessor);\n\n        } else if (e.request.type === HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE) {\n            // FragmentInfo (live) => update segments list\n            mssFragmentMoofProcessor.updateSegmentList(e, streamProcessor);\n\n            // Stop event propagation (FragmentInfo must not be added to buffer)\n            e.sender = null;\n        }\n    }\n\n    instance = {\n        generateMoov,\n        processFragment\n    };\n\n    setup();\n\n    return instance;\n}\n\nMssFragmentProcessor.__dashjs_factory_name = 'MssFragmentProcessor';\nexport default FactoryMaker.getClassFactory(MssFragmentProcessor);\n", "var bigInt = function (undefined) {\n    'use strict';\n    var BASE = 1e7, LOG_BASE = 7, MAX_INT = 9007199254740992, MAX_INT_ARR = smallToArray(MAX_INT),\n        DEFAULT_ALPHABET = '0123456789abcdefghijklmnopqrstuvwxyz';\n    var supportsNativeBigInt = typeof BigInt === 'function';\n\n    function Integer(v, radix, alphabet, caseSensitive) {\n        if (typeof v === 'undefined') return Integer[0];\n        if (typeof radix !== 'undefined') return +radix === 10 && !alphabet ? parseValue(v) : parseBase(v, radix, alphabet, caseSensitive);\n        return parseValue(v)\n    }\n\n    function BigInteger(value, sign) {\n        this.value = value;\n        this.sign = sign;\n        this.isSmall = false\n    }\n\n    BigInteger.prototype = Object.create(Integer.prototype);\n\n    function SmallInteger(value) {\n        this.value = value;\n        this.sign = value < 0;\n        this.isSmall = true\n    }\n\n    SmallInteger.prototype = Object.create(Integer.prototype);\n\n    function NativeBigInt(value) {\n        this.value = value\n    }\n\n    NativeBigInt.prototype = Object.create(Integer.prototype);\n\n    function isPrecise(n) {\n        return -MAX_INT < n && n < MAX_INT\n    }\n\n    function smallToArray(n) {\n        if (n < 1e7) return [n];\n        if (n < 1e14) return [n % 1e7, Math.floor(n / 1e7)];\n        return [n % 1e7, Math.floor(n / 1e7) % 1e7, Math.floor(n / 1e14)]\n    }\n\n    function arrayToSmall(arr) {\n        trim(arr);\n        var length = arr.length;\n        if (length < 4 && compareAbs(arr, MAX_INT_ARR) < 0) {\n            switch (length) {\n                case 0:\n                    return 0;\n                case 1:\n                    return arr[0];\n                case 2:\n                    return arr[0] + arr[1] * BASE;\n                default:\n                    return arr[0] + (arr[1] + arr[2] * BASE) * BASE\n            }\n        }\n        return arr\n    }\n\n    function trim(v) {\n        var i = v.length;\n        while (v[--i] === 0) ;\n        v.length = i + 1\n    }\n\n    function createArray(length) {\n        var x = new Array(length);\n        var i = -1;\n        while (++i < length) {\n            x[i] = 0\n        }\n        return x\n    }\n\n    function truncate(n) {\n        if (n > 0) return Math.floor(n);\n        return Math.ceil(n)\n    }\n\n    function add(a, b) {\n        var l_a = a.length, l_b = b.length, r = new Array(l_a), carry = 0, base = BASE, sum, i;\n        for (i = 0; i < l_b; i++) {\n            sum = a[i] + b[i] + carry;\n            carry = sum >= base ? 1 : 0;\n            r[i] = sum - carry * base\n        }\n        while (i < l_a) {\n            sum = a[i] + carry;\n            carry = sum === base ? 1 : 0;\n            r[i++] = sum - carry * base\n        }\n        if (carry > 0) r.push(carry);\n        return r\n    }\n\n    function addAny(a, b) {\n        if (a.length >= b.length) return add(a, b);\n        return add(b, a)\n    }\n\n    function addSmall(a, carry) {\n        var l = a.length, r = new Array(l), base = BASE, sum, i;\n        for (i = 0; i < l; i++) {\n            sum = a[i] - base + carry;\n            carry = Math.floor(sum / base);\n            r[i] = sum - carry * base;\n            carry += 1\n        }\n        while (carry > 0) {\n            r[i++] = carry % base;\n            carry = Math.floor(carry / base)\n        }\n        return r\n    }\n\n    BigInteger.prototype.add = function (v) {\n        var n = parseValue(v);\n        if (this.sign !== n.sign) {\n            return this.subtract(n.negate())\n        }\n        var a = this.value, b = n.value;\n        if (n.isSmall) {\n            return new BigInteger(addSmall(a, Math.abs(b)), this.sign)\n        }\n        return new BigInteger(addAny(a, b), this.sign)\n    };\n    BigInteger.prototype.plus = BigInteger.prototype.add;\n    SmallInteger.prototype.add = function (v) {\n        var n = parseValue(v);\n        var a = this.value;\n        if (a < 0 !== n.sign) {\n            return this.subtract(n.negate())\n        }\n        var b = n.value;\n        if (n.isSmall) {\n            if (isPrecise(a + b)) return new SmallInteger(a + b);\n            b = smallToArray(Math.abs(b))\n        }\n        return new BigInteger(addSmall(b, Math.abs(a)), a < 0)\n    };\n    SmallInteger.prototype.plus = SmallInteger.prototype.add;\n    NativeBigInt.prototype.add = function (v) {\n        return new NativeBigInt(this.value + parseValue(v).value)\n    };\n    NativeBigInt.prototype.plus = NativeBigInt.prototype.add;\n\n    function subtract(a, b) {\n        var a_l = a.length, b_l = b.length, r = new Array(a_l), borrow = 0, base = BASE, i, difference;\n        for (i = 0; i < b_l; i++) {\n            difference = a[i] - borrow - b[i];\n            if (difference < 0) {\n                difference += base;\n                borrow = 1\n            } else borrow = 0;\n            r[i] = difference\n        }\n        for (i = b_l; i < a_l; i++) {\n            difference = a[i] - borrow;\n            if (difference < 0) difference += base; else {\n                r[i++] = difference;\n                break\n            }\n            r[i] = difference\n        }\n        for (; i < a_l; i++) {\n            r[i] = a[i]\n        }\n        trim(r);\n        return r\n    }\n\n    function subtractAny(a, b, sign) {\n        var value;\n        if (compareAbs(a, b) >= 0) {\n            value = subtract(a, b)\n        } else {\n            value = subtract(b, a);\n            sign = !sign\n        }\n        value = arrayToSmall(value);\n        if (typeof value === 'number') {\n            if (sign) value = -value;\n            return new SmallInteger(value)\n        }\n        return new BigInteger(value, sign)\n    }\n\n    function subtractSmall(a, b, sign) {\n        var l = a.length, r = new Array(l), carry = -b, base = BASE, i, difference;\n        for (i = 0; i < l; i++) {\n            difference = a[i] + carry;\n            carry = Math.floor(difference / base);\n            difference %= base;\n            r[i] = difference < 0 ? difference + base : difference\n        }\n        r = arrayToSmall(r);\n        if (typeof r === 'number') {\n            if (sign) r = -r;\n            return new SmallInteger(r)\n        }\n        return new BigInteger(r, sign)\n    }\n\n    BigInteger.prototype.subtract = function (v) {\n        var n = parseValue(v);\n        if (this.sign !== n.sign) {\n            return this.add(n.negate())\n        }\n        var a = this.value, b = n.value;\n        if (n.isSmall) return subtractSmall(a, Math.abs(b), this.sign);\n        return subtractAny(a, b, this.sign)\n    };\n    BigInteger.prototype.minus = BigInteger.prototype.subtract;\n    SmallInteger.prototype.subtract = function (v) {\n        var n = parseValue(v);\n        var a = this.value;\n        if (a < 0 !== n.sign) {\n            return this.add(n.negate())\n        }\n        var b = n.value;\n        if (n.isSmall) {\n            return new SmallInteger(a - b)\n        }\n        return subtractSmall(b, Math.abs(a), a >= 0)\n    };\n    SmallInteger.prototype.minus = SmallInteger.prototype.subtract;\n    NativeBigInt.prototype.subtract = function (v) {\n        return new NativeBigInt(this.value - parseValue(v).value)\n    };\n    NativeBigInt.prototype.minus = NativeBigInt.prototype.subtract;\n    BigInteger.prototype.negate = function () {\n        return new BigInteger(this.value, !this.sign)\n    };\n    SmallInteger.prototype.negate = function () {\n        var sign = this.sign;\n        var small = new SmallInteger(-this.value);\n        small.sign = !sign;\n        return small\n    };\n    NativeBigInt.prototype.negate = function () {\n        return new NativeBigInt(-this.value)\n    };\n    BigInteger.prototype.abs = function () {\n        return new BigInteger(this.value, false)\n    };\n    SmallInteger.prototype.abs = function () {\n        return new SmallInteger(Math.abs(this.value))\n    };\n    NativeBigInt.prototype.abs = function () {\n        return new NativeBigInt(this.value >= 0 ? this.value : -this.value)\n    };\n\n    function multiplyLong(a, b) {\n        var a_l = a.length, b_l = b.length, l = a_l + b_l, r = createArray(l), base = BASE, product, carry, i, a_i, b_j;\n        for (i = 0; i < a_l; ++i) {\n            a_i = a[i];\n            for (var j = 0; j < b_l; ++j) {\n                b_j = b[j];\n                product = a_i * b_j + r[i + j];\n                carry = Math.floor(product / base);\n                r[i + j] = product - carry * base;\n                r[i + j + 1] += carry\n            }\n        }\n        trim(r);\n        return r\n    }\n\n    function multiplySmall(a, b) {\n        var l = a.length, r = new Array(l), base = BASE, carry = 0, product, i;\n        for (i = 0; i < l; i++) {\n            product = a[i] * b + carry;\n            carry = Math.floor(product / base);\n            r[i] = product - carry * base\n        }\n        while (carry > 0) {\n            r[i++] = carry % base;\n            carry = Math.floor(carry / base)\n        }\n        return r\n    }\n\n    function shiftLeft(x, n) {\n        var r = [];\n        while (n-- > 0) r.push(0);\n        return r.concat(x)\n    }\n\n    function multiplyKaratsuba(x, y) {\n        var n = Math.max(x.length, y.length);\n        if (n <= 30) return multiplyLong(x, y);\n        n = Math.ceil(n / 2);\n        var b = x.slice(n), a = x.slice(0, n), d = y.slice(n), c = y.slice(0, n);\n        var ac = multiplyKaratsuba(a, c), bd = multiplyKaratsuba(b, d),\n            abcd = multiplyKaratsuba(addAny(a, b), addAny(c, d));\n        var product = addAny(addAny(ac, shiftLeft(subtract(subtract(abcd, ac), bd), n)), shiftLeft(bd, 2 * n));\n        trim(product);\n        return product\n    }\n\n    function useKaratsuba(l1, l2) {\n        return -.012 * l1 - .012 * l2 + 15e-6 * l1 * l2 > 0\n    }\n\n    BigInteger.prototype.multiply = function (v) {\n        var n = parseValue(v), a = this.value, b = n.value, sign = this.sign !== n.sign, abs;\n        if (n.isSmall) {\n            if (b === 0) return Integer[0];\n            if (b === 1) return this;\n            if (b === -1) return this.negate();\n            abs = Math.abs(b);\n            if (abs < BASE) {\n                return new BigInteger(multiplySmall(a, abs), sign)\n            }\n            b = smallToArray(abs)\n        }\n        if (useKaratsuba(a.length, b.length)) return new BigInteger(multiplyKaratsuba(a, b), sign);\n        return new BigInteger(multiplyLong(a, b), sign)\n    };\n    BigInteger.prototype.times = BigInteger.prototype.multiply;\n\n    function multiplySmallAndArray(a, b, sign) {\n        if (a < BASE) {\n            return new BigInteger(multiplySmall(b, a), sign)\n        }\n        return new BigInteger(multiplyLong(b, smallToArray(a)), sign)\n    }\n\n    SmallInteger.prototype._multiplyBySmall = function (a) {\n        if (isPrecise(a.value * this.value)) {\n            return new SmallInteger(a.value * this.value)\n        }\n        return multiplySmallAndArray(Math.abs(a.value), smallToArray(Math.abs(this.value)), this.sign !== a.sign)\n    };\n    BigInteger.prototype._multiplyBySmall = function (a) {\n        if (a.value === 0) return Integer[0];\n        if (a.value === 1) return this;\n        if (a.value === -1) return this.negate();\n        return multiplySmallAndArray(Math.abs(a.value), this.value, this.sign !== a.sign)\n    };\n    SmallInteger.prototype.multiply = function (v) {\n        return parseValue(v)._multiplyBySmall(this)\n    };\n    SmallInteger.prototype.times = SmallInteger.prototype.multiply;\n    NativeBigInt.prototype.multiply = function (v) {\n        return new NativeBigInt(this.value * parseValue(v).value)\n    };\n    NativeBigInt.prototype.times = NativeBigInt.prototype.multiply;\n\n    function square(a) {\n        var l = a.length, r = createArray(l + l), base = BASE, product, carry, i, a_i, a_j;\n        for (i = 0; i < l; i++) {\n            a_i = a[i];\n            carry = 0 - a_i * a_i;\n            for (var j = i; j < l; j++) {\n                a_j = a[j];\n                product = 2 * (a_i * a_j) + r[i + j] + carry;\n                carry = Math.floor(product / base);\n                r[i + j] = product - carry * base\n            }\n            r[i + l] = carry\n        }\n        trim(r);\n        return r\n    }\n\n    BigInteger.prototype.square = function () {\n        return new BigInteger(square(this.value), false)\n    };\n    SmallInteger.prototype.square = function () {\n        var value = this.value * this.value;\n        if (isPrecise(value)) return new SmallInteger(value);\n        return new BigInteger(square(smallToArray(Math.abs(this.value))), false)\n    };\n    NativeBigInt.prototype.square = function (v) {\n        return new NativeBigInt(this.value * this.value)\n    };\n\n    function divMod1(a, b) {\n        var a_l = a.length, b_l = b.length, base = BASE, result = createArray(b.length),\n            divisorMostSignificantDigit = b[b_l - 1], lambda = Math.ceil(base / (2 * divisorMostSignificantDigit)),\n            remainder = multiplySmall(a, lambda), divisor = multiplySmall(b, lambda), quotientDigit, shift, carry,\n            borrow, i, l, q;\n        if (remainder.length <= a_l) remainder.push(0);\n        divisor.push(0);\n        divisorMostSignificantDigit = divisor[b_l - 1];\n        for (shift = a_l - b_l; shift >= 0; shift--) {\n            quotientDigit = base - 1;\n            if (remainder[shift + b_l] !== divisorMostSignificantDigit) {\n                quotientDigit = Math.floor((remainder[shift + b_l] * base + remainder[shift + b_l - 1]) / divisorMostSignificantDigit)\n            }\n            carry = 0;\n            borrow = 0;\n            l = divisor.length;\n            for (i = 0; i < l; i++) {\n                carry += quotientDigit * divisor[i];\n                q = Math.floor(carry / base);\n                borrow += remainder[shift + i] - (carry - q * base);\n                carry = q;\n                if (borrow < 0) {\n                    remainder[shift + i] = borrow + base;\n                    borrow = -1\n                } else {\n                    remainder[shift + i] = borrow;\n                    borrow = 0\n                }\n            }\n            while (borrow !== 0) {\n                quotientDigit -= 1;\n                carry = 0;\n                for (i = 0; i < l; i++) {\n                    carry += remainder[shift + i] - base + divisor[i];\n                    if (carry < 0) {\n                        remainder[shift + i] = carry + base;\n                        carry = 0\n                    } else {\n                        remainder[shift + i] = carry;\n                        carry = 1\n                    }\n                }\n                borrow += carry\n            }\n            result[shift] = quotientDigit\n        }\n        remainder = divModSmall(remainder, lambda)[0];\n        return [arrayToSmall(result), arrayToSmall(remainder)]\n    }\n\n    function divMod2(a, b) {\n        var a_l = a.length, b_l = b.length, result = [], part = [], base = BASE, guess, xlen, highx, highy, check;\n        while (a_l) {\n            part.unshift(a[--a_l]);\n            trim(part);\n            if (compareAbs(part, b) < 0) {\n                result.push(0);\n                continue\n            }\n            xlen = part.length;\n            highx = part[xlen - 1] * base + part[xlen - 2];\n            highy = b[b_l - 1] * base + b[b_l - 2];\n            if (xlen > b_l) {\n                highx = (highx + 1) * base\n            }\n            guess = Math.ceil(highx / highy);\n            do {\n                check = multiplySmall(b, guess);\n                if (compareAbs(check, part) <= 0) break;\n                guess--\n            } while (guess);\n            result.push(guess);\n            part = subtract(part, check)\n        }\n        result.reverse();\n        return [arrayToSmall(result), arrayToSmall(part)]\n    }\n\n    function divModSmall(value, lambda) {\n        var length = value.length, quotient = createArray(length), base = BASE, i, q, remainder, divisor;\n        remainder = 0;\n        for (i = length - 1; i >= 0; --i) {\n            divisor = remainder * base + value[i];\n            q = truncate(divisor / lambda);\n            remainder = divisor - q * lambda;\n            quotient[i] = q | 0\n        }\n        return [quotient, remainder | 0]\n    }\n\n    function divModAny(self, v) {\n        var value, n = parseValue(v);\n        if (supportsNativeBigInt) {\n            return [new NativeBigInt(self.value / n.value), new NativeBigInt(self.value % n.value)]\n        }\n        var a = self.value, b = n.value;\n        var quotient;\n        if (b === 0) throw new Error('Cannot divide by zero');\n        if (self.isSmall) {\n            if (n.isSmall) {\n                return [new SmallInteger(truncate(a / b)), new SmallInteger(a % b)]\n            }\n            return [Integer[0], self]\n        }\n        if (n.isSmall) {\n            if (b === 1) return [self, Integer[0]];\n            if (b == -1) return [self.negate(), Integer[0]];\n            var abs = Math.abs(b);\n            if (abs < BASE) {\n                value = divModSmall(a, abs);\n                quotient = arrayToSmall(value[0]);\n                var remainder = value[1];\n                if (self.sign) remainder = -remainder;\n                if (typeof quotient === 'number') {\n                    if (self.sign !== n.sign) quotient = -quotient;\n                    return [new SmallInteger(quotient), new SmallInteger(remainder)]\n                }\n                return [new BigInteger(quotient, self.sign !== n.sign), new SmallInteger(remainder)]\n            }\n            b = smallToArray(abs)\n        }\n        var comparison = compareAbs(a, b);\n        if (comparison === -1) return [Integer[0], self];\n        if (comparison === 0) return [Integer[self.sign === n.sign ? 1 : -1], Integer[0]];\n        if (a.length + b.length <= 200) value = divMod1(a, b); else value = divMod2(a, b);\n        quotient = value[0];\n        var qSign = self.sign !== n.sign, mod = value[1], mSign = self.sign;\n        if (typeof quotient === 'number') {\n            if (qSign) quotient = -quotient;\n            quotient = new SmallInteger(quotient)\n        } else quotient = new BigInteger(quotient, qSign);\n        if (typeof mod === 'number') {\n            if (mSign) mod = -mod;\n            mod = new SmallInteger(mod)\n        } else mod = new BigInteger(mod, mSign);\n        return [quotient, mod]\n    }\n\n    BigInteger.prototype.divmod = function (v) {\n        var result = divModAny(this, v);\n        return { quotient: result[0], remainder: result[1] }\n    };\n    NativeBigInt.prototype.divmod = SmallInteger.prototype.divmod = BigInteger.prototype.divmod;\n    BigInteger.prototype.divide = function (v) {\n        return divModAny(this, v)[0]\n    };\n    NativeBigInt.prototype.over = NativeBigInt.prototype.divide = function (v) {\n        return new NativeBigInt(this.value / parseValue(v).value)\n    };\n    SmallInteger.prototype.over = SmallInteger.prototype.divide = BigInteger.prototype.over = BigInteger.prototype.divide;\n    BigInteger.prototype.mod = function (v) {\n        return divModAny(this, v)[1]\n    };\n    NativeBigInt.prototype.mod = NativeBigInt.prototype.remainder = function (v) {\n        return new NativeBigInt(this.value % parseValue(v).value)\n    };\n    SmallInteger.prototype.remainder = SmallInteger.prototype.mod = BigInteger.prototype.remainder = BigInteger.prototype.mod;\n    BigInteger.prototype.pow = function (v) {\n        var n = parseValue(v), a = this.value, b = n.value, value, x, y;\n        if (b === 0) return Integer[1];\n        if (a === 0) return Integer[0];\n        if (a === 1) return Integer[1];\n        if (a === -1) return n.isEven() ? Integer[1] : Integer[-1];\n        if (n.sign) {\n            return Integer[0]\n        }\n        if (!n.isSmall) throw new Error('The exponent ' + n.toString() + ' is too large.');\n        if (this.isSmall) {\n            if (isPrecise(value = Math.pow(a, b))) return new SmallInteger(truncate(value))\n        }\n        x = this;\n        y = Integer[1];\n        while (true) {\n            if (b & 1 === 1) {\n                y = y.times(x);\n                --b\n            }\n            if (b === 0) break;\n            b /= 2;\n            x = x.square()\n        }\n        return y\n    };\n    SmallInteger.prototype.pow = BigInteger.prototype.pow;\n    NativeBigInt.prototype.pow = function (v) {\n        var n = parseValue(v);\n        var a = this.value, b = n.value;\n        var _0 = BigInt(0), _1 = BigInt(1), _2 = BigInt(2);\n        if (b === _0) return Integer[1];\n        if (a === _0) return Integer[0];\n        if (a === _1) return Integer[1];\n        if (a === BigInt(-1)) return n.isEven() ? Integer[1] : Integer[-1];\n        if (n.isNegative()) return new NativeBigInt(_0);\n        var x = this;\n        var y = Integer[1];\n        while (true) {\n            if ((b & _1) === _1) {\n                y = y.times(x);\n                --b\n            }\n            if (b === _0) break;\n            b /= _2;\n            x = x.square()\n        }\n        return y\n    };\n    BigInteger.prototype.modPow = function (exp, mod) {\n        exp = parseValue(exp);\n        mod = parseValue(mod);\n        if (mod.isZero()) throw new Error('Cannot take modPow with modulus 0');\n        var r = Integer[1], base = this.mod(mod);\n        while (exp.isPositive()) {\n            if (base.isZero()) return Integer[0];\n            if (exp.isOdd()) r = r.multiply(base).mod(mod);\n            exp = exp.divide(2);\n            base = base.square().mod(mod)\n        }\n        return r\n    };\n    NativeBigInt.prototype.modPow = SmallInteger.prototype.modPow = BigInteger.prototype.modPow;\n\n    function compareAbs(a, b) {\n        if (a.length !== b.length) {\n            return a.length > b.length ? 1 : -1\n        }\n        for (var i = a.length - 1; i >= 0; i--) {\n            if (a[i] !== b[i]) return a[i] > b[i] ? 1 : -1\n        }\n        return 0\n    }\n\n    BigInteger.prototype.compareAbs = function (v) {\n        var n = parseValue(v), a = this.value, b = n.value;\n        if (n.isSmall) return 1;\n        return compareAbs(a, b)\n    };\n    SmallInteger.prototype.compareAbs = function (v) {\n        var n = parseValue(v), a = Math.abs(this.value), b = n.value;\n        if (n.isSmall) {\n            b = Math.abs(b);\n            return a === b ? 0 : a > b ? 1 : -1\n        }\n        return -1\n    };\n    NativeBigInt.prototype.compareAbs = function (v) {\n        var a = this.value;\n        var b = parseValue(v).value;\n        a = a >= 0 ? a : -a;\n        b = b >= 0 ? b : -b;\n        return a === b ? 0 : a > b ? 1 : -1\n    };\n    BigInteger.prototype.compare = function (v) {\n        if (v === Infinity) {\n            return -1\n        }\n        if (v === -Infinity) {\n            return 1\n        }\n        var n = parseValue(v), a = this.value, b = n.value;\n        if (this.sign !== n.sign) {\n            return n.sign ? 1 : -1\n        }\n        if (n.isSmall) {\n            return this.sign ? -1 : 1\n        }\n        return compareAbs(a, b) * (this.sign ? -1 : 1)\n    };\n    BigInteger.prototype.compareTo = BigInteger.prototype.compare;\n    SmallInteger.prototype.compare = function (v) {\n        if (v === Infinity) {\n            return -1\n        }\n        if (v === -Infinity) {\n            return 1\n        }\n        var n = parseValue(v), a = this.value, b = n.value;\n        if (n.isSmall) {\n            return a == b ? 0 : a > b ? 1 : -1\n        }\n        if (a < 0 !== n.sign) {\n            return a < 0 ? -1 : 1\n        }\n        return a < 0 ? 1 : -1\n    };\n    SmallInteger.prototype.compareTo = SmallInteger.prototype.compare;\n    NativeBigInt.prototype.compare = function (v) {\n        if (v === Infinity) {\n            return -1\n        }\n        if (v === -Infinity) {\n            return 1\n        }\n        var a = this.value;\n        var b = parseValue(v).value;\n        return a === b ? 0 : a > b ? 1 : -1\n    };\n    NativeBigInt.prototype.compareTo = NativeBigInt.prototype.compare;\n    BigInteger.prototype.equals = function (v) {\n        return this.compare(v) === 0\n    };\n    NativeBigInt.prototype.eq = NativeBigInt.prototype.equals = SmallInteger.prototype.eq = SmallInteger.prototype.equals = BigInteger.prototype.eq = BigInteger.prototype.equals;\n    BigInteger.prototype.notEquals = function (v) {\n        return this.compare(v) !== 0\n    };\n    NativeBigInt.prototype.neq = NativeBigInt.prototype.notEquals = SmallInteger.prototype.neq = SmallInteger.prototype.notEquals = BigInteger.prototype.neq = BigInteger.prototype.notEquals;\n    BigInteger.prototype.greater = function (v) {\n        return this.compare(v) > 0\n    };\n    NativeBigInt.prototype.gt = NativeBigInt.prototype.greater = SmallInteger.prototype.gt = SmallInteger.prototype.greater = BigInteger.prototype.gt = BigInteger.prototype.greater;\n    BigInteger.prototype.lesser = function (v) {\n        return this.compare(v) < 0\n    };\n    NativeBigInt.prototype.lt = NativeBigInt.prototype.lesser = SmallInteger.prototype.lt = SmallInteger.prototype.lesser = BigInteger.prototype.lt = BigInteger.prototype.lesser;\n    BigInteger.prototype.greaterOrEquals = function (v) {\n        return this.compare(v) >= 0\n    };\n    NativeBigInt.prototype.geq = NativeBigInt.prototype.greaterOrEquals = SmallInteger.prototype.geq = SmallInteger.prototype.greaterOrEquals = BigInteger.prototype.geq = BigInteger.prototype.greaterOrEquals;\n    BigInteger.prototype.lesserOrEquals = function (v) {\n        return this.compare(v) <= 0\n    };\n    NativeBigInt.prototype.leq = NativeBigInt.prototype.lesserOrEquals = SmallInteger.prototype.leq = SmallInteger.prototype.lesserOrEquals = BigInteger.prototype.leq = BigInteger.prototype.lesserOrEquals;\n    BigInteger.prototype.isEven = function () {\n        return (this.value[0] & 1) === 0\n    };\n    SmallInteger.prototype.isEven = function () {\n        return (this.value & 1) === 0\n    };\n    NativeBigInt.prototype.isEven = function () {\n        return (this.value & BigInt(1)) === BigInt(0)\n    };\n    BigInteger.prototype.isOdd = function () {\n        return (this.value[0] & 1) === 1\n    };\n    SmallInteger.prototype.isOdd = function () {\n        return (this.value & 1) === 1\n    };\n    NativeBigInt.prototype.isOdd = function () {\n        return (this.value & BigInt(1)) === BigInt(1)\n    };\n    BigInteger.prototype.isPositive = function () {\n        return !this.sign\n    };\n    SmallInteger.prototype.isPositive = function () {\n        return this.value > 0\n    };\n    NativeBigInt.prototype.isPositive = SmallInteger.prototype.isPositive;\n    BigInteger.prototype.isNegative = function () {\n        return this.sign\n    };\n    SmallInteger.prototype.isNegative = function () {\n        return this.value < 0\n    };\n    NativeBigInt.prototype.isNegative = SmallInteger.prototype.isNegative;\n    BigInteger.prototype.isUnit = function () {\n        return false\n    };\n    SmallInteger.prototype.isUnit = function () {\n        return Math.abs(this.value) === 1\n    };\n    NativeBigInt.prototype.isUnit = function () {\n        return this.abs().value === BigInt(1)\n    };\n    BigInteger.prototype.isZero = function () {\n        return false\n    };\n    SmallInteger.prototype.isZero = function () {\n        return this.value === 0\n    };\n    NativeBigInt.prototype.isZero = function () {\n        return this.value === BigInt(0)\n    };\n    BigInteger.prototype.isDivisibleBy = function (v) {\n        var n = parseValue(v);\n        if (n.isZero()) return false;\n        if (n.isUnit()) return true;\n        if (n.compareAbs(2) === 0) return this.isEven();\n        return this.mod(n).isZero()\n    };\n    NativeBigInt.prototype.isDivisibleBy = SmallInteger.prototype.isDivisibleBy = BigInteger.prototype.isDivisibleBy;\n\n    function isBasicPrime(v) {\n        var n = v.abs();\n        if (n.isUnit()) return false;\n        if (n.equals(2) || n.equals(3) || n.equals(5)) return true;\n        if (n.isEven() || n.isDivisibleBy(3) || n.isDivisibleBy(5)) return false;\n        if (n.lesser(49)) return true\n    }\n\n    function millerRabinTest(n, a) {\n        var nPrev = n.prev(), b = nPrev, r = 0, d, t, i, x;\n        while (b.isEven()) b = b.divide(2), r++;\n        next:for (i = 0; i < a.length; i++) {\n            if (n.lesser(a[i])) continue;\n            x = bigInt(a[i]).modPow(b, n);\n            if (x.isUnit() || x.equals(nPrev)) continue;\n            for (d = r - 1; d != 0; d--) {\n                x = x.square().mod(n);\n                if (x.isUnit()) return false;\n                if (x.equals(nPrev)) continue next\n            }\n            return false\n        }\n        return true\n    }\n\n    BigInteger.prototype.isPrime = function (strict) {\n        var isPrime = isBasicPrime(this);\n        if (isPrime !== undefined) return isPrime;\n        var n = this.abs();\n        var bits = n.bitLength();\n        if (bits <= 64) return millerRabinTest(n, [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37]);\n        var logN = Math.log(2) * bits.toJSNumber();\n        var t = Math.ceil(strict === true ? 2 * Math.pow(logN, 2) : logN);\n        for (var a = [], i = 0; i < t; i++) {\n            a.push(bigInt(i + 2))\n        }\n        return millerRabinTest(n, a)\n    };\n    NativeBigInt.prototype.isPrime = SmallInteger.prototype.isPrime = BigInteger.prototype.isPrime;\n    BigInteger.prototype.isProbablePrime = function (iterations) {\n        var isPrime = isBasicPrime(this);\n        if (isPrime !== undefined) return isPrime;\n        var n = this.abs();\n        var t = iterations === undefined ? 5 : iterations;\n        for (var a = [], i = 0; i < t; i++) {\n            a.push(bigInt.randBetween(2, n.minus(2)))\n        }\n        return millerRabinTest(n, a)\n    };\n    NativeBigInt.prototype.isProbablePrime = SmallInteger.prototype.isProbablePrime = BigInteger.prototype.isProbablePrime;\n    BigInteger.prototype.modInv = function (n) {\n        var t = bigInt.zero, newT = bigInt.one, r = parseValue(n), newR = this.abs(), q, lastT, lastR;\n        while (!newR.isZero()) {\n            q = r.divide(newR);\n            lastT = t;\n            lastR = r;\n            t = newT;\n            r = newR;\n            newT = lastT.subtract(q.multiply(newT));\n            newR = lastR.subtract(q.multiply(newR))\n        }\n        if (!r.isUnit()) throw new Error(this.toString() + ' and ' + n.toString() + ' are not co-prime');\n        if (t.compare(0) === -1) {\n            t = t.add(n)\n        }\n        if (this.isNegative()) {\n            return t.negate()\n        }\n        return t\n    };\n    NativeBigInt.prototype.modInv = SmallInteger.prototype.modInv = BigInteger.prototype.modInv;\n    BigInteger.prototype.next = function () {\n        var value = this.value;\n        if (this.sign) {\n            return subtractSmall(value, 1, this.sign)\n        }\n        return new BigInteger(addSmall(value, 1), this.sign)\n    };\n    SmallInteger.prototype.next = function () {\n        var value = this.value;\n        if (value + 1 < MAX_INT) return new SmallInteger(value + 1);\n        return new BigInteger(MAX_INT_ARR, false)\n    };\n    NativeBigInt.prototype.next = function () {\n        return new NativeBigInt(this.value + BigInt(1))\n    };\n    BigInteger.prototype.prev = function () {\n        var value = this.value;\n        if (this.sign) {\n            return new BigInteger(addSmall(value, 1), true)\n        }\n        return subtractSmall(value, 1, this.sign)\n    };\n    SmallInteger.prototype.prev = function () {\n        var value = this.value;\n        if (value - 1 > -MAX_INT) return new SmallInteger(value - 1);\n        return new BigInteger(MAX_INT_ARR, true)\n    };\n    NativeBigInt.prototype.prev = function () {\n        return new NativeBigInt(this.value - BigInt(1))\n    };\n    var powersOfTwo = [1];\n    while (2 * powersOfTwo[powersOfTwo.length - 1] <= BASE) powersOfTwo.push(2 * powersOfTwo[powersOfTwo.length - 1]);\n    var powers2Length = powersOfTwo.length, highestPower2 = powersOfTwo[powers2Length - 1];\n\n    function shift_isSmall(n) {\n        return Math.abs(n) <= BASE\n    }\n\n    BigInteger.prototype.shiftLeft = function (v) {\n        var n = parseValue(v).toJSNumber();\n        if (!shift_isSmall(n)) {\n            throw new Error(String(n) + ' is too large for shifting.')\n        }\n        if (n < 0) return this.shiftRight(-n);\n        var result = this;\n        if (result.isZero()) return result;\n        while (n >= powers2Length) {\n            result = result.multiply(highestPower2);\n            n -= powers2Length - 1\n        }\n        return result.multiply(powersOfTwo[n])\n    };\n    NativeBigInt.prototype.shiftLeft = SmallInteger.prototype.shiftLeft = BigInteger.prototype.shiftLeft;\n    BigInteger.prototype.shiftRight = function (v) {\n        var remQuo;\n        var n = parseValue(v).toJSNumber();\n        if (!shift_isSmall(n)) {\n            throw new Error(String(n) + ' is too large for shifting.')\n        }\n        if (n < 0) return this.shiftLeft(-n);\n        var result = this;\n        while (n >= powers2Length) {\n            if (result.isZero() || result.isNegative() && result.isUnit()) return result;\n            remQuo = divModAny(result, highestPower2);\n            result = remQuo[1].isNegative() ? remQuo[0].prev() : remQuo[0];\n            n -= powers2Length - 1\n        }\n        remQuo = divModAny(result, powersOfTwo[n]);\n        return remQuo[1].isNegative() ? remQuo[0].prev() : remQuo[0]\n    };\n    NativeBigInt.prototype.shiftRight = SmallInteger.prototype.shiftRight = BigInteger.prototype.shiftRight;\n\n    function bitwise(x, y, fn) {\n        y = parseValue(y);\n        var xSign = x.isNegative(), ySign = y.isNegative();\n        var xRem = xSign ? x.not() : x, yRem = ySign ? y.not() : y;\n        var xDigit = 0, yDigit = 0;\n        var xDivMod = null, yDivMod = null;\n        var result = [];\n        while (!xRem.isZero() || !yRem.isZero()) {\n            xDivMod = divModAny(xRem, highestPower2);\n            xDigit = xDivMod[1].toJSNumber();\n            if (xSign) {\n                xDigit = highestPower2 - 1 - xDigit\n            }\n            yDivMod = divModAny(yRem, highestPower2);\n            yDigit = yDivMod[1].toJSNumber();\n            if (ySign) {\n                yDigit = highestPower2 - 1 - yDigit\n            }\n            xRem = xDivMod[0];\n            yRem = yDivMod[0];\n            result.push(fn(xDigit, yDigit))\n        }\n        var sum = fn(xSign ? 1 : 0, ySign ? 1 : 0) !== 0 ? bigInt(-1) : bigInt(0);\n        for (var i = result.length - 1; i >= 0; i -= 1) {\n            sum = sum.multiply(highestPower2).add(bigInt(result[i]))\n        }\n        return sum\n    }\n\n    BigInteger.prototype.not = function () {\n        return this.negate().prev()\n    };\n    NativeBigInt.prototype.not = SmallInteger.prototype.not = BigInteger.prototype.not;\n    BigInteger.prototype.and = function (n) {\n        return bitwise(this, n, function (a, b) {\n            return a & b\n        })\n    };\n    NativeBigInt.prototype.and = SmallInteger.prototype.and = BigInteger.prototype.and;\n    BigInteger.prototype.or = function (n) {\n        return bitwise(this, n, function (a, b) {\n            return a | b\n        })\n    };\n    NativeBigInt.prototype.or = SmallInteger.prototype.or = BigInteger.prototype.or;\n    BigInteger.prototype.xor = function (n) {\n        return bitwise(this, n, function (a, b) {\n            return a ^ b\n        })\n    };\n    NativeBigInt.prototype.xor = SmallInteger.prototype.xor = BigInteger.prototype.xor;\n    var LOBMASK_I = 1 << 30, LOBMASK_BI = (BASE & -BASE) * (BASE & -BASE) | LOBMASK_I;\n\n    function roughLOB(n) {\n        var v = n.value,\n            x = typeof v === 'number' ? v | LOBMASK_I : typeof v === 'bigint' ? v | BigInt(LOBMASK_I) : v[0] + v[1] * BASE | LOBMASK_BI;\n        return x & -x\n    }\n\n    function integerLogarithm(value, base) {\n        if (base.compareTo(value) <= 0) {\n            var tmp = integerLogarithm(value, base.square(base));\n            var p = tmp.p;\n            var e = tmp.e;\n            var t = p.multiply(base);\n            return t.compareTo(value) <= 0 ? { p: t, e: e * 2 + 1 } : { p: p, e: e * 2 }\n        }\n        return { p: bigInt(1), e: 0 }\n    }\n\n    BigInteger.prototype.bitLength = function () {\n        var n = this;\n        if (n.compareTo(bigInt(0)) < 0) {\n            n = n.negate().subtract(bigInt(1))\n        }\n        if (n.compareTo(bigInt(0)) === 0) {\n            return bigInt(0)\n        }\n        return bigInt(integerLogarithm(n, bigInt(2)).e).add(bigInt(1))\n    };\n    NativeBigInt.prototype.bitLength = SmallInteger.prototype.bitLength = BigInteger.prototype.bitLength;\n\n    function max(a, b) {\n        a = parseValue(a);\n        b = parseValue(b);\n        return a.greater(b) ? a : b\n    }\n\n    function min(a, b) {\n        a = parseValue(a);\n        b = parseValue(b);\n        return a.lesser(b) ? a : b\n    }\n\n    function gcd(a, b) {\n        a = parseValue(a).abs();\n        b = parseValue(b).abs();\n        if (a.equals(b)) return a;\n        if (a.isZero()) return b;\n        if (b.isZero()) return a;\n        var c = Integer[1], d, t;\n        while (a.isEven() && b.isEven()) {\n            d = min(roughLOB(a), roughLOB(b));\n            a = a.divide(d);\n            b = b.divide(d);\n            c = c.multiply(d)\n        }\n        while (a.isEven()) {\n            a = a.divide(roughLOB(a))\n        }\n        do {\n            while (b.isEven()) {\n                b = b.divide(roughLOB(b))\n            }\n            if (a.greater(b)) {\n                t = b;\n                b = a;\n                a = t\n            }\n            b = b.subtract(a)\n        } while (!b.isZero());\n        return c.isUnit() ? a : a.multiply(c)\n    }\n\n    function lcm(a, b) {\n        a = parseValue(a).abs();\n        b = parseValue(b).abs();\n        return a.divide(gcd(a, b)).multiply(b)\n    }\n\n    function randBetween(a, b) {\n        a = parseValue(a);\n        b = parseValue(b);\n        var low = min(a, b), high = max(a, b);\n        var range = high.subtract(low).add(1);\n        if (range.isSmall) return low.add(Math.floor(Math.random() * range));\n        var digits = toBase(range, BASE).value;\n        var result = [], restricted = true;\n        for (var i = 0; i < digits.length; i++) {\n            var top = restricted ? digits[i] : BASE;\n            var digit = truncate(Math.random() * top);\n            result.push(digit);\n            if (digit < top) restricted = false\n        }\n        return low.add(Integer.fromArray(result, BASE, false))\n    }\n\n    var parseBase = function (text, base, alphabet, caseSensitive) {\n        alphabet = alphabet || DEFAULT_ALPHABET;\n        text = String(text);\n        if (!caseSensitive) {\n            text = text.toLowerCase();\n            alphabet = alphabet.toLowerCase()\n        }\n        var length = text.length;\n        var i;\n        var absBase = Math.abs(base);\n        var alphabetValues = {};\n        for (i = 0; i < alphabet.length; i++) {\n            alphabetValues[alphabet[i]] = i\n        }\n        for (i = 0; i < length; i++) {\n            var c = text[i];\n            if (c === '-') continue;\n            if (c in alphabetValues) {\n                if (alphabetValues[c] >= absBase) {\n                    if (c === '1' && absBase === 1) continue;\n                    throw new Error(c + ' is not a valid digit in base ' + base + '.')\n                }\n            }\n        }\n        base = parseValue(base);\n        var digits = [];\n        var isNegative = text[0] === '-';\n        for (i = isNegative ? 1 : 0; i < text.length; i++) {\n            var c = text[i];\n            if (c in alphabetValues) digits.push(parseValue(alphabetValues[c])); else if (c === '<') {\n                var start = i;\n                do {\n                    i++\n                } while (text[i] !== '>' && i < text.length);\n                digits.push(parseValue(text.slice(start + 1, i)))\n            } else throw new Error(c + ' is not a valid character')\n        }\n        return parseBaseFromArray(digits, base, isNegative)\n    };\n\n    function parseBaseFromArray(digits, base, isNegative) {\n        var val = Integer[0], pow = Integer[1], i;\n        for (i = digits.length - 1; i >= 0; i--) {\n            val = val.add(digits[i].times(pow));\n            pow = pow.times(base)\n        }\n        return isNegative ? val.negate() : val\n    }\n\n    function stringify(digit, alphabet) {\n        alphabet = alphabet || DEFAULT_ALPHABET;\n        if (digit < alphabet.length) {\n            return alphabet[digit]\n        }\n        return '<' + digit + '>'\n    }\n\n    function toBase(n, base) {\n        base = bigInt(base);\n        if (base.isZero()) {\n            if (n.isZero()) return { value: [0], isNegative: false };\n            throw new Error('Cannot convert nonzero numbers to base 0.')\n        }\n        if (base.equals(-1)) {\n            if (n.isZero()) return { value: [0], isNegative: false };\n            if (n.isNegative()) return {\n                value: [].concat.apply([], Array.apply(null, Array(-n.toJSNumber())).map(Array.prototype.valueOf, [1, 0])),\n                isNegative: false\n            };\n            var arr = Array.apply(null, Array(n.toJSNumber() - 1)).map(Array.prototype.valueOf, [0, 1]);\n            arr.unshift([1]);\n            return { value: [].concat.apply([], arr), isNegative: false }\n        }\n        var neg = false;\n        if (n.isNegative() && base.isPositive()) {\n            neg = true;\n            n = n.abs()\n        }\n        if (base.isUnit()) {\n            if (n.isZero()) return { value: [0], isNegative: false };\n            return { value: Array.apply(null, Array(n.toJSNumber())).map(Number.prototype.valueOf, 1), isNegative: neg }\n        }\n        var out = [];\n        var left = n, divmod;\n        while (left.isNegative() || left.compareAbs(base) >= 0) {\n            divmod = left.divmod(base);\n            left = divmod.quotient;\n            var digit = divmod.remainder;\n            if (digit.isNegative()) {\n                digit = base.minus(digit).abs();\n                left = left.next()\n            }\n            out.push(digit.toJSNumber())\n        }\n        out.push(left.toJSNumber());\n        return { value: out.reverse(), isNegative: neg }\n    }\n\n    function toBaseString(n, base, alphabet) {\n        var arr = toBase(n, base);\n        return (arr.isNegative ? '-' : '') + arr.value.map(function (x) {\n            return stringify(x, alphabet)\n        }).join('')\n    }\n\n    BigInteger.prototype.toArray = function (radix) {\n        return toBase(this, radix)\n    };\n    SmallInteger.prototype.toArray = function (radix) {\n        return toBase(this, radix)\n    };\n    NativeBigInt.prototype.toArray = function (radix) {\n        return toBase(this, radix)\n    };\n    BigInteger.prototype.toString = function (radix, alphabet) {\n        if (radix === undefined) radix = 10;\n        if (radix !== 10) return toBaseString(this, radix, alphabet);\n        var v = this.value, l = v.length, str = String(v[--l]), zeros = '0000000', digit;\n        while (--l >= 0) {\n            digit = String(v[l]);\n            str += zeros.slice(digit.length) + digit\n        }\n        var sign = this.sign ? '-' : '';\n        return sign + str\n    };\n    SmallInteger.prototype.toString = function (radix, alphabet) {\n        if (radix === undefined) radix = 10;\n        if (radix != 10) return toBaseString(this, radix, alphabet);\n        return String(this.value)\n    };\n    NativeBigInt.prototype.toString = SmallInteger.prototype.toString;\n    NativeBigInt.prototype.toJSON = BigInteger.prototype.toJSON = SmallInteger.prototype.toJSON = function () {\n        return this.toString()\n    };\n    BigInteger.prototype.valueOf = function () {\n        return parseInt(this.toString(), 10)\n    };\n    BigInteger.prototype.toJSNumber = BigInteger.prototype.valueOf;\n    SmallInteger.prototype.valueOf = function () {\n        return this.value\n    };\n    SmallInteger.prototype.toJSNumber = SmallInteger.prototype.valueOf;\n    NativeBigInt.prototype.valueOf = NativeBigInt.prototype.toJSNumber = function () {\n        return parseInt(this.toString(), 10)\n    };\n\n    function parseStringValue(v) {\n        if (isPrecise(+v)) {\n            var x = +v;\n            if (x === truncate(x)) return supportsNativeBigInt ? new NativeBigInt(BigInt(x)) : new SmallInteger(x);\n            throw new Error('Invalid integer: ' + v)\n        }\n        var sign = v[0] === '-';\n        if (sign) v = v.slice(1);\n        var split = v.split(/e/i);\n        if (split.length > 2) throw new Error('Invalid integer: ' + split.join('e'));\n        if (split.length === 2) {\n            var exp = split[1];\n            if (exp[0] === '+') exp = exp.slice(1);\n            exp = +exp;\n            if (exp !== truncate(exp) || !isPrecise(exp)) throw new Error('Invalid integer: ' + exp + ' is not a valid exponent.');\n            var text = split[0];\n            var decimalPlace = text.indexOf('.');\n            if (decimalPlace >= 0) {\n                exp -= text.length - decimalPlace - 1;\n                text = text.slice(0, decimalPlace) + text.slice(decimalPlace + 1)\n            }\n            if (exp < 0) throw new Error('Cannot include negative exponent part for integers');\n            text += new Array(exp + 1).join('0');\n            v = text\n        }\n        var isValid = /^([0-9][0-9]*)$/.test(v);\n        if (!isValid) throw new Error('Invalid integer: ' + v);\n        if (supportsNativeBigInt) {\n            return new NativeBigInt(BigInt(sign ? '-' + v : v))\n        }\n        var r = [], max = v.length, l = LOG_BASE, min = max - l;\n        while (max > 0) {\n            r.push(+v.slice(min, max));\n            min -= l;\n            if (min < 0) min = 0;\n            max -= l\n        }\n        trim(r);\n        return new BigInteger(r, sign)\n    }\n\n    function parseNumberValue(v) {\n        if (supportsNativeBigInt) {\n            return new NativeBigInt(BigInt(v))\n        }\n        if (isPrecise(v)) {\n            if (v !== truncate(v)) throw new Error(v + ' is not an integer.');\n            return new SmallInteger(v)\n        }\n        return parseStringValue(v.toString())\n    }\n\n    function parseValue(v) {\n        if (typeof v === 'number') {\n            return parseNumberValue(v)\n        }\n        if (typeof v === 'string') {\n            return parseStringValue(v)\n        }\n        if (typeof v === 'bigint') {\n            return new NativeBigInt(v)\n        }\n        return v\n    }\n\n    for (var i = 0; i < 1e3; i++) {\n        Integer[i] = parseValue(i);\n        if (i > 0) Integer[-i] = parseValue(-i)\n    }\n    Integer.one = Integer[1];\n    Integer.zero = Integer[0];\n    Integer.minusOne = Integer[-1];\n    Integer.max = max;\n    Integer.min = min;\n    Integer.gcd = gcd;\n    Integer.lcm = lcm;\n    Integer.isInstance = function (x) {\n        return x instanceof BigInteger || x instanceof SmallInteger || x instanceof NativeBigInt\n    };\n    Integer.randBetween = randBetween;\n    Integer.fromArray = function (digits, base, isNegative) {\n        return parseBaseFromArray(digits.map(parseValue), parseValue(base || 10), isNegative)\n    };\n    return Integer\n}();\n\nif (typeof define === 'function' && define.amd) {\n    define('big-integer', [], function () {\n        return bigInt\n    })\n}\n\nexport default bigInt\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * @module MssParser\n * @ignore\n * @param {Object} config object\n */\n\nimport BigInt from '../../../externals/BigInteger.js';\nimport FactoryMaker from '../../core/FactoryMaker.js';\nimport ProtectionConstants from '../../streaming/constants/ProtectionConstants.js';\n\nfunction MssParser(config) {\n    config = config || {};\n    const BASE64 = config.BASE64;\n    const debug = config.debug;\n    const constants = config.constants;\n    const manifestModel = config.manifestModel;\n    const settings = config.settings;\n\n    const DEFAULT_TIME_SCALE = 10000000.0;\n    const SUPPORTED_CODECS = ['AAC', 'AACL', 'AACH', 'AACP', 'AVC1', 'H264', 'TTML', 'DFXP'];\n    // MPEG-DASH Role and accessibility mapping for text tracks according to ETSI TS 103 285 v1.1.1 (section 7.1.2)\n    const ROLE = {\n        'CAPT': 'main',\n        'SUBT': 'alternate',\n        'DESC': 'main'\n    };\n    const ACCESSIBILITY = {\n        'DESC': '2'\n    };\n    const samplingFrequencyIndex = {\n        96000: 0x0,\n        88200: 0x1,\n        64000: 0x2,\n        48000: 0x3,\n        44100: 0x4,\n        32000: 0x5,\n        24000: 0x6,\n        22050: 0x7,\n        16000: 0x8,\n        12000: 0x9,\n        11025: 0xA,\n        8000: 0xB,\n        7350: 0xC\n    };\n    const mimeTypeMap = {\n        'video': 'video/mp4',\n        'audio': 'audio/mp4',\n        'text': 'application/mp4'\n    };\n\n    let instance,\n        logger,\n        initialBufferSettings;\n\n\n    function setup() {\n        logger = debug.getLogger(instance);\n    }\n\n    function getAttributeAsBoolean(node, attrName) {\n        const value = node.getAttribute(attrName);\n        if (!value) {\n            return false;\n        }\n        return value.toLowerCase() === 'true';\n    }\n\n    function mapPeriod(smoothStreamingMedia, timescale) {\n        const period = {};\n        let streams,\n            adaptation;\n\n        // For each StreamIndex node, create an AdaptationSet element\n        period.AdaptationSet = [];\n        streams = smoothStreamingMedia.getElementsByTagName('StreamIndex');\n        for (let i = 0; i < streams.length; i++) {\n            adaptation = mapAdaptationSet(streams[i], timescale);\n            if (adaptation !== null) {\n                period.AdaptationSet.push(adaptation);\n            }\n        }\n\n        return period;\n    }\n\n    function mapAdaptationSet(streamIndex, timescale) {\n        const adaptationSet = {};\n        const representations = [];\n        let segmentTemplate;\n        let qualityLevels,\n            representation,\n            i,\n            index;\n\n        const name = streamIndex.getAttribute('Name');\n        const type = streamIndex.getAttribute('Type');\n        const lang = streamIndex.getAttribute('Language');\n        const fallBackId = lang ? type + '_' + lang : type;\n\n        adaptationSet.id = name || fallBackId;\n        adaptationSet.contentType = type;\n        adaptationSet.lang = lang || 'und';\n        adaptationSet.mimeType = mimeTypeMap[type];\n        adaptationSet.subType = streamIndex.getAttribute('Subtype');\n        adaptationSet.maxWidth = streamIndex.getAttribute('MaxWidth');\n        adaptationSet.maxHeight = streamIndex.getAttribute('MaxHeight');\n\n        // Map text tracks subTypes to MPEG-DASH AdaptationSet role and accessibility (see ETSI TS 103 285 v1.1.1, section 7.1.2)\n        if (adaptationSet.subType) {\n            if (ROLE[adaptationSet.subType]) {\n                adaptationSet.Role = [{\n                    schemeIdUri: 'urn:mpeg:dash:role:2011',\n                    value: ROLE[adaptationSet.subType]\n                }];\n            }\n            if (ACCESSIBILITY[adaptationSet.subType]) {\n                adaptationSet.Accessibility = [{\n                    schemeIdUri: 'urn:tva:metadata:cs:AudioPurposeCS:2007',\n                    value: ACCESSIBILITY[adaptationSet.subType]\n                }];\n            }\n        }\n\n        // Create a SegmentTemplate with a SegmentTimeline\n        segmentTemplate = mapSegmentTemplate(streamIndex, timescale);\n\n        qualityLevels = streamIndex.getElementsByTagName('QualityLevel');\n        // For each QualityLevel node, create a Representation element\n        for (i = 0; i < qualityLevels.length; i++) {\n            // Propagate BaseURL and mimeType\n            qualityLevels[i].BaseURL = adaptationSet.BaseURL;\n            qualityLevels[i].mimeType = adaptationSet.mimeType;\n\n            // Set quality level id\n            index = qualityLevels[i].getAttribute('Index');\n            qualityLevels[i].Id = adaptationSet.id + ((index !== null) ? ('_' + index) : '');\n\n            // Map Representation to QualityLevel\n            representation = mapRepresentation(qualityLevels[i], streamIndex);\n\n            if (representation !== null) {\n                // Copy SegmentTemplate into Representation\n                representation.SegmentTemplate = segmentTemplate;\n\n                representations.push(representation);\n            }\n        }\n\n        if (representations.length === 0) {\n            return null;\n        }\n\n        adaptationSet.Representation = representations;\n\n        // Set SegmentTemplate\n        adaptationSet.SegmentTemplate = segmentTemplate;\n\n        return adaptationSet;\n    }\n\n    function mapRepresentation(qualityLevel, streamIndex) {\n        const representation = {};\n        const type = streamIndex.getAttribute('Type');\n        let fourCCValue = null;\n        let width = null;\n        let height = null;\n\n        representation.id = qualityLevel.Id;\n        representation.bandwidth = parseInt(qualityLevel.getAttribute('Bitrate'), 10);\n        representation.mimeType = qualityLevel.mimeType;\n\n        width = parseInt(qualityLevel.getAttribute('MaxWidth'), 10);\n        height = parseInt(qualityLevel.getAttribute('MaxHeight'), 10);\n        if (!isNaN(width)) {\n            representation.width = width;\n        }\n        if (!isNaN(height)) {\n            representation.height = height;\n        }\n\n\n        fourCCValue = qualityLevel.getAttribute('FourCC');\n\n        // If FourCC not defined at QualityLevel level, then get it from StreamIndex level\n        if (fourCCValue === null || fourCCValue === '') {\n            fourCCValue = streamIndex.getAttribute('FourCC');\n        }\n\n        // If still not defined (optionnal for audio stream, see https://msdn.microsoft.com/en-us/library/ff728116%28v=vs.95%29.aspx),\n        // then we consider the stream is an audio AAC stream\n        if (fourCCValue === null || fourCCValue === '') {\n            if (type === constants.AUDIO) {\n                fourCCValue = 'AAC';\n            } else if (type === constants.VIDEO) {\n                logger.debug('FourCC is not defined whereas it is required for a QualityLevel element for a StreamIndex of type \"video\"');\n                return null;\n            }\n        }\n\n        // Check if codec is supported\n        if (SUPPORTED_CODECS.indexOf(fourCCValue.toUpperCase()) === -1) {\n            // Do not send warning\n            logger.warn('Codec not supported: ' + fourCCValue);\n            return null;\n        }\n\n        // Get codecs value according to FourCC field\n        if (fourCCValue === 'H264' || fourCCValue === 'AVC1') {\n            representation.codecs = getH264Codec(qualityLevel);\n        } else if (fourCCValue.indexOf('AAC') >= 0) {\n            representation.codecs = getAACCodec(qualityLevel, fourCCValue);\n            representation.audioSamplingRate = parseInt(qualityLevel.getAttribute('SamplingRate'), 10);\n            representation.audioChannels = parseInt(qualityLevel.getAttribute('Channels'), 10);\n        } else if (fourCCValue.indexOf('TTML') || fourCCValue.indexOf('DFXP')) {\n            representation.codecs = constants.STPP;\n        }\n\n        representation.codecPrivateData = '' + qualityLevel.getAttribute('CodecPrivateData');\n        representation.BaseURL = qualityLevel.BaseURL;\n\n        return representation;\n    }\n\n    function getH264Codec(qualityLevel) {\n        let codecPrivateData = qualityLevel.getAttribute('CodecPrivateData').toString();\n        let nalHeader,\n            avcoti;\n\n\n        // Extract from the CodecPrivateData field the hexadecimal representation of the following\n        // three bytes in the sequence parameter set NAL unit.\n        // => Find the SPS nal header\n        nalHeader = /00000001[0-9]7/.exec(codecPrivateData);\n        // => Find the 6 characters after the SPS nalHeader (if it exists)\n        avcoti = nalHeader && nalHeader[0] ? (codecPrivateData.substr(codecPrivateData.indexOf(nalHeader[0]) + 10, 6)) : undefined;\n\n        return 'avc1.' + avcoti;\n    }\n\n    function getAACCodec(qualityLevel, fourCCValue) {\n        const samplingRate = parseInt(qualityLevel.getAttribute('SamplingRate'), 10);\n        let codecPrivateData = qualityLevel.getAttribute('CodecPrivateData').toString();\n        let objectType = 0;\n        let codecPrivateDataHex,\n            arr16,\n            indexFreq,\n            extensionSamplingFrequencyIndex;\n\n        //chrome problem, in implicit AAC HE definition, so when AACH is detected in FourCC\n        //set objectType to 5 => strange, it should be 2\n        if (fourCCValue === 'AACH') {\n            objectType = 0x05;\n        }\n        //if codecPrivateData is empty, build it :\n        if (codecPrivateData === undefined || codecPrivateData === '') {\n            objectType = 0x02; //AAC Main Low Complexity => object Type = 2\n            indexFreq = samplingFrequencyIndex[samplingRate];\n            if (fourCCValue === 'AACH') {\n                // 4 bytes :     XXXXX         XXXX          XXXX             XXXX                  XXXXX      XXX   XXXXXXX\n                //           ' ObjectType' 'Freq Index' 'Channels value'   'Extens Sampl Freq'  'ObjectType'  'GAS' 'alignment = 0'\n                objectType = 0x05; // High Efficiency AAC Profile = object Type = 5 SBR\n                codecPrivateData = new Uint8Array(4);\n                extensionSamplingFrequencyIndex = samplingFrequencyIndex[samplingRate * 2]; // in HE AAC Extension Sampling frequence\n                // equals to SamplingRate*2\n                //Freq Index is present for 3 bits in the first byte, last bit is in the second\n                codecPrivateData[0] = (objectType << 3) | (indexFreq >> 1);\n                codecPrivateData[1] = (indexFreq << 7) | (qualityLevel.Channels << 3) | (extensionSamplingFrequencyIndex >> 1);\n                codecPrivateData[2] = (extensionSamplingFrequencyIndex << 7) | (0x02 << 2); // origin object type equals to 2 => AAC Main Low Complexity\n                codecPrivateData[3] = 0x0; //alignment bits\n\n                arr16 = new Uint16Array(2);\n                arr16[0] = (codecPrivateData[0] << 8) + codecPrivateData[1];\n                arr16[1] = (codecPrivateData[2] << 8) + codecPrivateData[3];\n                //convert decimal to hex value\n                codecPrivateDataHex = arr16[0].toString(16);\n                codecPrivateDataHex = arr16[0].toString(16) + arr16[1].toString(16);\n\n            } else {\n                // 2 bytes :     XXXXX         XXXX          XXXX              XXX\n                //           ' ObjectType' 'Freq Index' 'Channels value'   'GAS = 000'\n                codecPrivateData = new Uint8Array(2);\n                //Freq Index is present for 3 bits in the first byte, last bit is in the second\n                codecPrivateData[0] = (objectType << 3) | (indexFreq >> 1);\n                codecPrivateData[1] = (indexFreq << 7) | (parseInt(qualityLevel.getAttribute('Channels'), 10) << 3);\n                // put the 2 bytes in an 16 bits array\n                arr16 = new Uint16Array(1);\n                arr16[0] = (codecPrivateData[0] << 8) + codecPrivateData[1];\n                //convert decimal to hex value\n                codecPrivateDataHex = arr16[0].toString(16);\n            }\n\n            codecPrivateData = '' + codecPrivateDataHex;\n            codecPrivateData = codecPrivateData.toUpperCase();\n            qualityLevel.setAttribute('CodecPrivateData', codecPrivateData);\n        } else if (objectType === 0) {\n            objectType = (parseInt(codecPrivateData.substr(0, 2), 16) & 0xF8) >> 3;\n        }\n\n        return 'mp4a.40.' + objectType;\n    }\n\n    function mapSegmentTemplate(streamIndex, timescale) {\n        const segmentTemplate = {};\n        let mediaUrl,\n            streamIndexTimeScale,\n            url;\n\n        url = streamIndex.getAttribute('Url');\n        mediaUrl = url ? url.replace('{bitrate}', '$Bandwidth$') : null;\n        mediaUrl = mediaUrl ? mediaUrl.replace('{start time}', '$Time$') : null;\n\n        streamIndexTimeScale = streamIndex.getAttribute('TimeScale');\n        streamIndexTimeScale = streamIndexTimeScale ? parseFloat(streamIndexTimeScale) : timescale;\n\n        segmentTemplate.media = mediaUrl;\n        segmentTemplate.timescale = streamIndexTimeScale;\n\n        segmentTemplate.SegmentTimeline = mapSegmentTimeline(streamIndex, segmentTemplate.timescale);\n\n        // Patch: set availabilityTimeOffset to Infinity since segments are available as long as they are present in timeline\n        segmentTemplate.availabilityTimeOffset = 'INF';\n\n        return segmentTemplate;\n    }\n\n    function mapSegmentTimeline(streamIndex, timescale) {\n        const segmentTimeline = {};\n        const chunks = streamIndex.getElementsByTagName('c');\n        const segments = [];\n        let segment,\n            prevSegment,\n            tManifest,\n            i, j, r;\n        let duration = 0;\n\n        for (i = 0; i < chunks.length; i++) {\n            segment = {};\n\n            // Get time 't' attribute value\n            tManifest = chunks[i].getAttribute('t');\n\n            // => segment.tManifest = original timestamp value as a string (for constructing the fragment request url, see DashHandler)\n            // => segment.t = number value of timestamp (maybe rounded value, but only for 0.1 microsecond)\n            if (tManifest && BigInt(tManifest).greater(BigInt(Number.MAX_SAFE_INTEGER))) {\n                segment.tManifest = tManifest;\n            }\n            segment.t = parseFloat(tManifest);\n\n            // Get duration 'd' attribute value\n            segment.d = parseFloat(chunks[i].getAttribute('d'));\n\n            // If 't' not defined for first segment then t=0\n            if ((i === 0) && !segment.t) {\n                segment.t = 0;\n            }\n\n            if (i > 0) {\n                prevSegment = segments[segments.length - 1];\n                // Update previous segment duration if not defined\n                if (!prevSegment.d) {\n                    if (prevSegment.tManifest) {\n                        prevSegment.d = BigInt(tManifest).subtract(BigInt(prevSegment.tManifest)).toJSNumber();\n                    } else {\n                        prevSegment.d = segment.t - prevSegment.t;\n                    }\n                    duration += prevSegment.d;\n                }\n                // Set segment absolute timestamp if not set in manifest\n                if (!segment.t) {\n                    if (prevSegment.tManifest) {\n                        segment.tManifest = BigInt(prevSegment.tManifest).add(BigInt(prevSegment.d)).toString();\n                        segment.t = parseFloat(segment.tManifest);\n                    } else {\n                        segment.t = prevSegment.t + prevSegment.d;\n                    }\n                }\n            }\n\n            if (segment.d) {\n                duration += segment.d;\n            }\n\n            // Create new segment\n            segments.push(segment);\n\n            // Support for 'r' attribute (i.e. \"repeat\" as in MPEG-DASH)\n            r = parseFloat(chunks[i].getAttribute('r'));\n            if (r) {\n\n                for (j = 0; j < (r - 1); j++) {\n                    prevSegment = segments[segments.length - 1];\n                    segment = {};\n                    segment.t = prevSegment.t + prevSegment.d;\n                    segment.d = prevSegment.d;\n                    if (prevSegment.tManifest) {\n                        segment.tManifest = BigInt(prevSegment.tManifest).add(BigInt(prevSegment.d)).toString();\n                    }\n                    duration += segment.d;\n                    segments.push(segment);\n                }\n            }\n        }\n\n        segmentTimeline.S = segments;\n        segmentTimeline.duration = duration / timescale;\n\n        return segmentTimeline;\n    }\n\n    function getKIDFromProtectionHeader(protectionHeader) {\n        let prHeader,\n            wrmHeader,\n            xmlReader,\n            KID;\n\n        // Get PlayReady header as byte array (base64 decoded)\n        prHeader = BASE64.decodeArray(protectionHeader.firstChild.data);\n\n        // Get Right Management header (WRMHEADER) from PlayReady header\n        wrmHeader = getWRMHeaderFromPRHeader(prHeader);\n\n        if (wrmHeader) {\n            // Convert from multi-byte to unicode\n            wrmHeader = new Uint16Array(wrmHeader.buffer);\n\n            // Convert to string\n            wrmHeader = String.fromCharCode.apply(null, wrmHeader);\n\n            // Parse <WRMHeader> to get KID field value\n            xmlReader = (new DOMParser()).parseFromString(wrmHeader, 'application/xml');\n            KID = xmlReader.querySelector('KID').textContent;\n\n            // Get KID (base64 decoded) as byte array\n            KID = BASE64.decodeArray(KID);\n\n            // Convert UUID from little-endian to big-endian\n            convertUuidEndianness(KID);\n        }\n\n        return KID;\n    }\n\n    function getWRMHeaderFromPRHeader(prHeader) {\n        let length,\n            recordCount,\n            recordType,\n            recordLength,\n            recordValue;\n        let i = 0;\n\n        // Parse PlayReady header\n\n        // Length - 32 bits (LE format)\n        length = (prHeader[i + 3] << 24) + (prHeader[i + 2] << 16) + (prHeader[i + 1] << 8) + prHeader[i]; // eslint-disable-line\n        i += 4;\n\n        // Record count - 16 bits (LE format)\n        recordCount = (prHeader[i + 1] << 8) + prHeader[i]; // eslint-disable-line\n        i += 2;\n\n        // Parse records\n        while (i < prHeader.length) {\n            // Record type - 16 bits (LE format)\n            recordType = (prHeader[i + 1] << 8) + prHeader[i];\n            i += 2;\n\n            // Check if Rights Management header (record type = 0x01)\n            if (recordType === 0x01) {\n\n                // Record length - 16 bits (LE format)\n                recordLength = (prHeader[i + 1] << 8) + prHeader[i];\n                i += 2;\n\n                // Record value => contains <WRMHEADER>\n                recordValue = new Uint8Array(recordLength);\n                recordValue.set(prHeader.subarray(i, i + recordLength));\n                return recordValue;\n            }\n        }\n\n        return null;\n    }\n\n    function convertUuidEndianness(uuid) {\n        swapBytes(uuid, 0, 3);\n        swapBytes(uuid, 1, 2);\n        swapBytes(uuid, 4, 5);\n        swapBytes(uuid, 6, 7);\n    }\n\n    function swapBytes(bytes, pos1, pos2) {\n        const temp = bytes[pos1];\n        bytes[pos1] = bytes[pos2];\n        bytes[pos2] = temp;\n    }\n\n\n    function createPRContentProtection(protectionHeader) {\n        let pro = {\n            __text: protectionHeader.firstChild.data,\n            __prefix: 'mspr'\n        };\n        return {\n            schemeIdUri: 'urn:uuid:' + ProtectionConstants.PLAYREADY_UUID,\n            value: ProtectionConstants.PLAYREADY_KEYSTEM_STRING,\n            pro: pro\n        };\n    }\n\n    function createWidevineContentProtection(KID) {\n        let widevineCP = {\n            schemeIdUri: 'urn:uuid:' + ProtectionConstants.WIDEVINE_UUID,\n            value: ProtectionConstants.WIDEVINE_KEYSTEM_STRING\n        };\n        if (!KID) {\n            return widevineCP;\n        }\n        // Create Widevine CENC header (Protocol Buffer) with KID value\n        const wvCencHeader = new Uint8Array(2 + KID.length);\n        wvCencHeader[0] = 0x12;\n        wvCencHeader[1] = 0x10;\n        wvCencHeader.set(KID, 2);\n\n        // Create a pssh box\n        const length = 12 /* box length, type, version and flags */ + 16 /* SystemID */ + 4 /* data length */ + wvCencHeader.length;\n        let pssh = new Uint8Array(length);\n        let i = 0;\n\n        // Set box length value\n        pssh[i++] = (length & 0xFF000000) >> 24;\n        pssh[i++] = (length & 0x00FF0000) >> 16;\n        pssh[i++] = (length & 0x0000FF00) >> 8;\n        pssh[i++] = (length & 0x000000FF);\n\n        // Set type ('pssh'), version (0) and flags (0)\n        pssh.set([0x70, 0x73, 0x73, 0x68, 0x00, 0x00, 0x00, 0x00], i);\n        i += 8;\n\n        // Set SystemID ('edef8ba9-79d6-4ace-a3c8-27dcd51d21ed')\n        pssh.set([0xed, 0xef, 0x8b, 0xa9, 0x79, 0xd6, 0x4a, 0xce, 0xa3, 0xc8, 0x27, 0xdc, 0xd5, 0x1d, 0x21, 0xed], i);\n        i += 16;\n\n        // Set data length value\n        pssh[i++] = (wvCencHeader.length & 0xFF000000) >> 24;\n        pssh[i++] = (wvCencHeader.length & 0x00FF0000) >> 16;\n        pssh[i++] = (wvCencHeader.length & 0x0000FF00) >> 8;\n        pssh[i++] = (wvCencHeader.length & 0x000000FF);\n\n        // Copy Widevine CENC header\n        pssh.set(wvCencHeader, i);\n\n        // Convert to BASE64 string\n        pssh = String.fromCharCode.apply(null, pssh);\n        pssh = BASE64.encodeASCII(pssh);\n\n        widevineCP.pssh = { __text: pssh };\n\n        return widevineCP;\n    }\n\n    function processManifest(xmlDoc) {\n        const manifest = {};\n        const contentProtections = [];\n        const smoothStreamingMedia = xmlDoc.getElementsByTagName('SmoothStreamingMedia')[0];\n        const protection = xmlDoc.getElementsByTagName('Protection')[0];\n        let protectionHeader = null;\n        let period,\n            adaptations,\n            contentProtection,\n            KID,\n            timestampOffset,\n            startTime,\n            segments,\n            timescale,\n            segmentDuration,\n            i, j;\n\n        // Set manifest node properties\n        manifest.protocol = 'MSS';\n        manifest.profiles = 'urn:mpeg:dash:profile:isoff-live:2011';\n        manifest.type = getAttributeAsBoolean(smoothStreamingMedia, 'IsLive') ? 'dynamic' : 'static';\n        timescale = smoothStreamingMedia.getAttribute('TimeScale');\n        manifest.timescale = timescale ? parseFloat(timescale) : DEFAULT_TIME_SCALE;\n        let dvrWindowLength = parseFloat(smoothStreamingMedia.getAttribute('DVRWindowLength'));\n        // If the DVRWindowLength field is omitted for a live presentation or set to 0, the DVR window is effectively infinite\n        if (manifest.type === 'dynamic' && (dvrWindowLength === 0 || isNaN(dvrWindowLength))) {\n            dvrWindowLength = Infinity;\n        }\n        // Star-over\n        if (dvrWindowLength === 0 && getAttributeAsBoolean(smoothStreamingMedia, 'CanSeek')) {\n            dvrWindowLength = Infinity;\n        }\n\n        if (dvrWindowLength > 0) {\n            manifest.timeShiftBufferDepth = dvrWindowLength / manifest.timescale;\n        }\n\n        let duration = parseFloat(smoothStreamingMedia.getAttribute('Duration'));\n        manifest.mediaPresentationDuration = (duration === 0) ? Infinity : duration / manifest.timescale;\n        // By default, set minBufferTime to 2 sec. (but set below according to video segment duration)\n        manifest.minBufferTime = 2;\n        manifest.ttmlTimeIsRelative = true;\n\n        // Live manifest with Duration = start-over\n        if (manifest.type === 'dynamic' && duration > 0) {\n            manifest.type = 'static';\n            // We set timeShiftBufferDepth to initial duration, to be used by MssFragmentController to update segment timeline\n            manifest.timeShiftBufferDepth = duration / manifest.timescale;\n            // Duration will be set according to current segment timeline duration (see below)\n        }\n\n        if (manifest.type === 'dynamic') {\n            manifest.refreshManifestOnSwitchTrack = true; // Refresh manifest when switching tracks\n            manifest.doNotUpdateDVRWindowOnBufferUpdated = true; // DVRWindow is update by MssFragmentMoofPocessor based on tfrf boxes\n            manifest.ignorePostponeTimePeriod = true; // Never update manifest\n            manifest.availabilityStartTime = new Date(null); // Returns 1970\n        }\n\n        // Map period node to manifest root node\n        period = mapPeriod(smoothStreamingMedia, manifest.timescale);\n        manifest.Period = [period];\n\n        // Initialize period start time\n        period.start = 0;\n\n        // Uncomment to test live to static manifests\n        // if (manifest.type !== 'static') {\n        //     manifest.type = 'static';\n        //     manifest.mediaPresentationDuration = manifest.timeShiftBufferDepth;\n        //     manifest.timeShiftBufferDepth = null;\n        // }\n\n        // ContentProtection node\n        if (protection !== undefined) {\n            protectionHeader = xmlDoc.getElementsByTagName('ProtectionHeader')[0];\n\n            // Some packagers put newlines into the ProtectionHeader base64 string, which is not good\n            // because this cannot be correctly parsed. Let's just filter out any newlines found in there.\n            protectionHeader.firstChild.data = protectionHeader.firstChild.data.replace(/\\n|\\r/g, '');\n\n            // Get KID (in CENC format) from protection header\n            KID = getKIDFromProtectionHeader(protectionHeader);\n\n            // Create ContentProtection for PlayReady\n            contentProtection = createPRContentProtection(protectionHeader);\n            contentProtection['cenc:default_KID'] = KID;\n            contentProtections.push(contentProtection);\n\n            // Create ContentProtection for Widevine (as a CENC protection)\n            contentProtection = createWidevineContentProtection(KID);\n            contentProtection['cenc:default_KID'] = KID;\n            contentProtections.push(contentProtection);\n\n            manifest.ContentProtection = contentProtections;\n        }\n\n        adaptations = period.AdaptationSet;\n\n        for (i = 0; i < adaptations.length; i += 1) {\n            adaptations[i].SegmentTemplate.initialization = '$Bandwidth$';\n            // Propagate content protection information into each adaptation\n            if (manifest.ContentProtection !== undefined) {\n                adaptations[i].ContentProtection = manifest.ContentProtection;\n                adaptations[i].ContentProtection = manifest.ContentProtection;\n            }\n\n            if (adaptations[i].contentType === 'video') {\n                // Get video segment duration\n                segmentDuration = adaptations[i].SegmentTemplate.SegmentTimeline.S[0].d / adaptations[i].SegmentTemplate.timescale;\n                // Set minBufferTime to one segment duration\n                manifest.minBufferTime = segmentDuration;\n\n                if (manifest.type === 'dynamic') {\n                    // Match timeShiftBufferDepth to video segment timeline duration\n                    if (manifest.timeShiftBufferDepth > 0 &&\n                        manifest.timeShiftBufferDepth !== Infinity &&\n                        manifest.timeShiftBufferDepth > adaptations[i].SegmentTemplate.SegmentTimeline.duration) {\n                        manifest.timeShiftBufferDepth = adaptations[i].SegmentTemplate.SegmentTimeline.duration;\n                    }\n                }\n            }\n        }\n\n        // Cap minBufferTime to timeShiftBufferDepth\n        manifest.minBufferTime = Math.min(manifest.minBufferTime, (manifest.timeShiftBufferDepth ? manifest.timeShiftBufferDepth : Infinity));\n\n        // In case of live streams:\n        // 1- configure player buffering properties according to target live delay\n        // 2- adapt live delay and then buffers length in case timeShiftBufferDepth is too small compared to target live delay (see PlaybackController.computeLiveDelay())\n        // 3- Set retry attempts and intervals for FragmentInfo requests\n        if (manifest.type === 'dynamic') {\n            let targetLiveDelay = settings.get().streaming.delay.liveDelay;\n            if (!targetLiveDelay) {\n                const liveDelayFragmentCount = settings.get().streaming.delay.liveDelayFragmentCount !== null && !isNaN(settings.get().streaming.delay.liveDelayFragmentCount) ? settings.get().streaming.delay.liveDelayFragmentCount : 4;\n                targetLiveDelay = segmentDuration * liveDelayFragmentCount;\n            }\n            let targetDelayCapping = Math.max(manifest.timeShiftBufferDepth - 10/*END_OF_PLAYLIST_PADDING*/, manifest.timeShiftBufferDepth / 2);\n            let liveDelay = Math.min(targetDelayCapping, targetLiveDelay);\n            // Consider a margin of more than one segment in order to avoid Precondition Failed errors (412), for example if audio and video are not correctly synchronized\n            let bufferTime = liveDelay - (segmentDuration * 1.5);\n\n            // Store initial buffer settings\n            initialBufferSettings = {\n                'streaming': {\n                    'buffer': {\n                        'bufferTimeDefault': settings.get().streaming.buffer.bufferTimeDefault,\n                        'bufferTimeAtTopQuality': settings.get().streaming.buffer.bufferTimeAtTopQuality,\n                        'bufferTimeAtTopQualityLongForm': settings.get().streaming.buffer.bufferTimeAtTopQualityLongForm\n                    },\n                    'timeShiftBuffer': {\n                        calcFromSegmentTimeline: settings.get().streaming.timeShiftBuffer.calcFromSegmentTimeline\n                    },\n                    'delay': {\n                        'liveDelay': settings.get().streaming.delay.liveDelay\n                    }\n                }\n            };\n\n            settings.update({\n                'streaming': {\n                    'buffer': {\n                        'bufferTimeDefault': bufferTime,\n                        'bufferTimeAtTopQuality': bufferTime,\n                        'bufferTimeAtTopQualityLongForm': bufferTime\n                    },\n                    'timeShiftBuffer': {\n                        calcFromSegmentTimeline: true\n                    },\n                    'delay': {\n                        'liveDelay': liveDelay\n                    }\n                }\n            });\n        }\n\n        // Delete Content Protection under root manifest node\n        delete manifest.ContentProtection;\n\n        // In case of VOD streams, check if start time is greater than 0\n        // Then determine timestamp offset according to higher audio/video start time\n        // (use case = live stream delinearization)\n        if (manifest.type === 'static') {\n            // In case of start-over stream and manifest reloading (due to track switch)\n            // we consider previous timestampOffset to keep timelines synchronized\n            var prevManifest = manifestModel.getValue();\n            if (prevManifest && prevManifest.timestampOffset) {\n                timestampOffset = prevManifest.timestampOffset;\n            } else {\n                for (i = 0; i < adaptations.length; i++) {\n                    if (adaptations[i].contentType === constants.AUDIO || adaptations[i].contentType === constants.VIDEO) {\n                        segments = adaptations[i].SegmentTemplate.SegmentTimeline.S;\n                        startTime = segments[0].t;\n                        if (timestampOffset === undefined) {\n                            timestampOffset = startTime;\n                        }\n                        timestampOffset = Math.min(timestampOffset, startTime);\n                        // Correct content duration according to minimum adaptation's segment timeline duration\n                        // in order to force <video> element sending 'ended' event\n                        manifest.mediaPresentationDuration = Math.min(manifest.mediaPresentationDuration, adaptations[i].SegmentTemplate.SegmentTimeline.duration);\n                    }\n                }\n            }\n            if (timestampOffset > 0) {\n                // Patch segment templates timestamps and determine period start time (since audio/video should not be aligned to 0)\n                manifest.timestampOffset = timestampOffset;\n                for (i = 0; i < adaptations.length; i++) {\n                    segments = adaptations[i].SegmentTemplate.SegmentTimeline.S;\n                    for (j = 0; j < segments.length; j++) {\n                        if (!segments[j].tManifest) {\n                            segments[j].tManifest = segments[j].t.toString();\n                        }\n                        segments[j].t -= timestampOffset;\n                    }\n                    if (adaptations[i].contentType === constants.AUDIO || adaptations[i].contentType === constants.VIDEO) {\n                        period.start = Math.max(segments[0].t, period.start);\n                        adaptations[i].SegmentTemplate.presentationTimeOffset = period.start;\n                    }\n                }\n                period.start /= manifest.timescale;\n            }\n        }\n\n        // Floor the duration to get around precision differences between segments timestamps and MSE buffer timestamps\n        // and then avoid 'ended' event not being raised\n        manifest.mediaPresentationDuration = Math.floor(manifest.mediaPresentationDuration * 1000) / 1000;\n        period.duration = manifest.mediaPresentationDuration;\n\n        return manifest;\n    }\n\n    function parseDOM(data) {\n        let xmlDoc = null;\n\n        if (window.DOMParser) {\n            const parser = new window.DOMParser();\n\n            xmlDoc = parser.parseFromString(data, 'text/xml');\n            if (xmlDoc.getElementsByTagName('parsererror').length > 0) {\n                throw new Error('parsing the manifest failed');\n            }\n        }\n\n        return xmlDoc;\n    }\n\n    function getIron() {\n        return null;\n    }\n\n    function internalParse(data) {\n        let xmlDoc = null;\n        let manifest = null;\n\n        const startTime = window.performance.now();\n\n        // Parse the MSS XML manifest\n        xmlDoc = parseDOM(data);\n\n        const xmlParseTime = window.performance.now();\n\n        if (xmlDoc === null) {\n            return null;\n        }\n\n        // Convert MSS manifest into DASH manifest\n        manifest = processManifest(xmlDoc, new Date());\n\n        const mss2dashTime = window.performance.now();\n\n        logger.info('Parsing complete: (xmlParsing: ' + (xmlParseTime - startTime).toPrecision(3) + 'ms, mss2dash: ' + (mss2dashTime - xmlParseTime).toPrecision(3) + 'ms, total: ' + ((mss2dashTime - startTime) / 1000).toPrecision(3) + 's)');\n\n        return manifest;\n    }\n\n    function reset() {\n        // Restore initial buffer settings\n        if (initialBufferSettings) {\n            settings.update(initialBufferSettings);\n        }\n    }\n\n    instance = {\n        parse: internalParse,\n        getIron: getIron,\n        reset: reset\n    };\n\n    setup();\n\n    return instance;\n}\n\nMssParser.__dashjs_factory_name = 'MssParser';\nexport default FactoryMaker.getClassFactory(MssParser);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES, LOSS OF USE, DATA, OR\n *  PROFITS, OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * Protection Constants declaration\n * @ignore\n */\nexport default {\n    CLEARKEY_KEYSTEM_STRING: 'org.w3.clearkey',\n    WIDEVINE_KEYSTEM_STRING: 'com.widevine.alpha',\n    PLAYREADY_KEYSTEM_STRING: 'com.microsoft.playready',\n    PLAYREADY_RECOMMENDATION_KEYSTEM_STRING: 'com.microsoft.playready.recommendation',\n    WIDEVINE_UUID: 'edef8ba9-79d6-4ace-a3c8-27dcd51d21ed',\n    PLAYREADY_UUID: '9a04f079-9840-4286-ab92-e65be0885f95',\n    CLEARKEY_UUID: 'e2719d58-a985-b3c9-781a-b030af78d30e',\n    W3C_CLEARKEY_UUID: '1077efec-c0b2-4d02-ace3-3c1e52e2fb4b',\n    INITIALIZATION_DATA_TYPE_CENC: 'cenc',\n    INITIALIZATION_DATA_TYPE_KEYIDS: 'keyids',\n    INITIALIZATION_DATA_TYPE_WEBM: 'webm',\n    ENCRYPTION_SCHEME_CENC: 'cenc',\n    ENCRYPTION_SCHEME_CBCS: 'cbcs',\n    MEDIA_KEY_MESSAGE_TYPES: {\n        LICENSE_REQUEST: 'license-request',\n        LICENSE_RENEWAL: 'license-renewal',\n        LICENSE_RELEASE: 'license-release',\n        INDIVIDUALIZATION_REQUEST: 'individualization-request',\n    },\n    ROBUSTNESS_STRINGS: {\n        WIDEVINE: {\n            SW_SECURE_CRYPTO: 'SW_SECURE_CRYPTO',\n            SW_SECURE_DECODE: 'SW_SECURE_DECODE',\n            HW_SECURE_CRYPTO: 'HW_SECURE_CRYPTO',\n            HW_SECURE_DECODE: 'HW_SECURE_DECODE',\n            HW_SECURE_ALL: 'HW_SECURE_ALL'\n        }\n    },\n    MEDIA_KEY_STATUSES: {\n        USABLE: 'usable',\n        EXPIRED: 'expired',\n        RELEASED: 'released',\n        OUTPUT_RESTRICTED: 'output-restricted',\n        OUTPUT_DOWNSCALED: 'output-downscaled',\n        STATUS_PENDING: 'status-pending',\n        INTERNAL_ERROR: 'internal-error',\n    }\n}\n\n\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport DataChunk from '../streaming/vo/DataChunk.js';\nimport FragmentRequest from '../streaming/vo/FragmentRequest.js';\nimport MssFragmentInfoController from './MssFragmentInfoController.js';\nimport MssFragmentProcessor from './MssFragmentProcessor.js';\nimport MssParser from './parser/MssParser.js';\nimport MssErrors from './errors/MssErrors.js';\nimport DashJSError from '../streaming/vo/DashJSError.js';\nimport {HTTPRequest} from '../streaming/vo/metrics/HTTPRequest.js';\n\nfunction MssHandler(config) {\n\n    config = config || {};\n    const context = this.context;\n    const eventBus = config.eventBus;\n    const events = config.events;\n    const constants = config.constants;\n    const initSegmentType = config.initSegmentType;\n    const playbackController = config.playbackController;\n    const streamController = config.streamController;\n    let mssParser,\n        mssFragmentProcessor,\n        fragmentInfoControllers,\n        instance;\n\n    function setup() {\n        fragmentInfoControllers = [];\n    }\n\n    function createMssFragmentProcessor() {\n        mssFragmentProcessor = MssFragmentProcessor(context).create(config);\n    }\n\n    function getStreamProcessor(type) {\n        return streamController.getActiveStreamProcessors().filter(processor => {\n            return processor.getType() === type;\n        })[0];\n    }\n\n    function getFragmentInfoController(type) {\n        return fragmentInfoControllers.filter(controller => {\n            return (controller.getType() === type);\n        })[0];\n    }\n\n    function createDataChunk(request, streamId, endFragment) {\n        const chunk = new DataChunk();\n\n        chunk.streamId = streamId;\n        chunk.segmentType = request.type;\n        chunk.start = request.startTime;\n        chunk.duration = request.duration;\n        chunk.end = chunk.start + chunk.duration;\n        chunk.index = request.index;\n        chunk.bandwidth = request.bandwidth;\n        chunk.representation = request.representation;\n        chunk.endFragment = endFragment;\n\n        return chunk;\n    }\n\n    function startFragmentInfoControllers() {\n\n        // Create MssFragmentInfoControllers for each StreamProcessor of active stream (only for audio, video or text)\n        let processors = streamController.getActiveStreamProcessors();\n        processors.forEach(function (processor) {\n            if (processor.getType() === constants.VIDEO ||\n                processor.getType() === constants.AUDIO ||\n                processor.getType() === constants.TEXT) {\n\n                let fragmentInfoController = getFragmentInfoController(processor.getType());\n                if (!fragmentInfoController) {\n                    fragmentInfoController = MssFragmentInfoController(context).create({\n                        streamProcessor: processor,\n                        baseURLController: config.baseURLController,\n                        debug: config.debug\n                    });\n                    fragmentInfoController.initialize();\n                    fragmentInfoControllers.push(fragmentInfoController);\n                }\n                fragmentInfoController.start();\n            }\n        });\n    }\n\n    function stopFragmentInfoControllers() {\n        fragmentInfoControllers.forEach(c => {\n            c.reset();\n        });\n        fragmentInfoControllers = [];\n    }\n\n    function onInitFragmentNeeded(e) {\n        let streamProcessor = getStreamProcessor(e.mediaType);\n        if (!streamProcessor) {\n            return;\n        }\n\n        // Create init segment request\n        let representationController = streamProcessor.getRepresentationController();\n        let representation = representationController.getCurrentRepresentation();\n        let mediaInfo = streamProcessor.getMediaInfo();\n\n        let request = new FragmentRequest();\n        request.mediaType = representation.adaptation.type;\n        request.type = initSegmentType;\n        request.range = representation.range;\n        request.bandwidth = representation.bandwidth;\n        request.representation = representation;\n\n        const chunk = createDataChunk(request, mediaInfo.streamInfo.id, e.type !== events.FRAGMENT_LOADING_PROGRESS);\n\n        try {\n            // Generate init segment (moov)\n            chunk.bytes = mssFragmentProcessor.generateMoov(representation);\n\n            // Notify init segment has been loaded\n            eventBus.trigger(events.INIT_FRAGMENT_LOADED,\n                { chunk: chunk },\n                { streamId: mediaInfo.streamInfo.id, mediaType: representation.adaptation.type }\n            );\n        } catch (e) {\n            config.errHandler.error(new DashJSError(e.code, e.message, e.data));\n        }\n\n        // Change the sender value to stop event to be propagated\n        e.sender = null;\n    }\n\n    function onSegmentMediaLoaded(e) {\n        if (e.error) {\n            return;\n        }\n\n        let streamProcessor = getStreamProcessor(e.request.mediaType);\n        if (!streamProcessor) {\n            return;\n        }\n\n        // Process moof to transcode it from MSS to DASH (or to update segment timeline for SegmentInfo fragments)\n        mssFragmentProcessor.processFragment(e, streamProcessor);\n\n        if (e.request.type === HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE) {\n            // If FragmentInfo loaded, then notify corresponding MssFragmentInfoController\n            let fragmentInfoController = getFragmentInfoController(e.request.mediaType);\n            if (fragmentInfoController) {\n                fragmentInfoController.fragmentInfoLoaded(e);\n            }\n        }\n\n        // Start MssFragmentInfoControllers in case of start-over streams\n        let manifestInfo = e.request.representation.mediaInfo.streamInfo.manifestInfo;\n        if (!manifestInfo.isDynamic && manifestInfo.dvrWindowSize !== Infinity) {\n            startFragmentInfoControllers();\n        }\n    }\n\n    function onPlaybackPaused() {\n        if (playbackController.getIsDynamic() && playbackController.getTime() !== 0) {\n            startFragmentInfoControllers();\n        }\n    }\n\n    function onPlaybackSeeking() {\n        if (playbackController.getIsDynamic() && playbackController.getTime() !== 0) {\n            startFragmentInfoControllers();\n        }\n    }\n\n    function onTTMLPreProcess(ttmlSubtitles) {\n        if (!ttmlSubtitles || !ttmlSubtitles.data) {\n            return;\n        }\n\n        ttmlSubtitles.data = ttmlSubtitles.data.replace(/http:\\/\\/www.w3.org\\/2006\\/10\\/ttaf1/gi, 'http://www.w3.org/ns/ttml');\n    }\n\n    function registerEvents() {\n        eventBus.on(events.INIT_FRAGMENT_NEEDED, onInitFragmentNeeded, instance, { priority: dashjs.FactoryMaker.getSingletonFactoryByName(eventBus.getClassName()).EVENT_PRIORITY_HIGH });\n        eventBus.on(events.PLAYBACK_PAUSED, onPlaybackPaused, instance, { priority: dashjs.FactoryMaker.getSingletonFactoryByName(eventBus.getClassName()).EVENT_PRIORITY_HIGH });\n        eventBus.on(events.PLAYBACK_SEEKING, onPlaybackSeeking, instance, { priority: dashjs.FactoryMaker.getSingletonFactoryByName(eventBus.getClassName()).EVENT_PRIORITY_HIGH });\n        eventBus.on(events.FRAGMENT_LOADING_COMPLETED, onSegmentMediaLoaded, instance, { priority: dashjs.FactoryMaker.getSingletonFactoryByName(eventBus.getClassName()).EVENT_PRIORITY_HIGH });\n        eventBus.on(events.TTML_TO_PARSE, onTTMLPreProcess, instance);\n    }\n\n    function reset() {\n        if (mssParser) {\n            mssParser.reset();\n            mssParser = undefined;\n        }\n\n        eventBus.off(events.INIT_FRAGMENT_NEEDED, onInitFragmentNeeded, this);\n        eventBus.off(events.PLAYBACK_PAUSED, onPlaybackPaused, this);\n        eventBus.off(events.PLAYBACK_SEEKING, onPlaybackSeeking, this);\n        eventBus.off(events.FRAGMENT_LOADING_COMPLETED, onSegmentMediaLoaded, this);\n        eventBus.off(events.TTML_TO_PARSE, onTTMLPreProcess, this);\n\n        // Reset FragmentInfoControllers\n        stopFragmentInfoControllers();\n    }\n\n    function createMssParser() {\n        mssParser = MssParser(context).create(config);\n        return mssParser;\n    }\n\n    instance = {\n        reset,\n        createMssParser,\n        createMssFragmentProcessor,\n        registerEvents\n    };\n\n    setup();\n\n    return instance;\n}\n\nMssHandler.__dashjs_factory_name = 'MssHandler';\nconst factory = dashjs.FactoryMaker.getClassFactory(MssHandler);\nfactory.errors = MssErrors;\ndashjs.FactoryMaker.updateClassFactory(MssHandler.__dashjs_factory_name, factory);\nexport default factory;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport MssHandler from './MssHandler.js';\n\n// Shove both of these into the global scope\nvar context = (typeof window !== 'undefined' && window) || global;\n\nvar dashjs = context.dashjs;\nif (!dashjs) {\n    dashjs = context.dashjs = {};\n}\n\ndashjs.MssHandler = MssHandler;\n\nexport default dashjs;\nexport { MssHandler };\n"], "names": ["__webpack_require__", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "constructor", "this", "streamId", "segmentType", "index", "NaN", "bytes", "start", "end", "duration", "representation", "endFragment", "HTTPRequest", "tcpid", "type", "url", "<PERSON><PERSON><PERSON>", "range", "trequest", "tresponse", "responsecode", "interval", "trace", "cmsd", "_stream", "_tfinish", "_mediaduration", "_responseHeaders", "_serviceLocation", "_fileLoaderType", "_resourceTimingValues", "GET", "HEAD", "MPD_TYPE", "XLINK_EXPANSION_TYPE", "INIT_SEGMENT_TYPE", "INDEX_SEGMENT_TYPE", "MEDIA_SEGMENT_TYPE", "BITSTREAM_SWITCHING_SEGMENT_TYPE", "MSS_FRAGMENT_INFO_SEGMENT_TYPE", "DVB_REPORTING_TYPE", "LICENSE", "CONTENT_STEERING_TYPE", "OTHER_TYPE", "FragmentRequest", "action", "ACTION_DOWNLOAD", "availabilityEndTime", "availabilityStartTime", "bandwidth", "bytesLoaded", "bytesTotal", "delayLoadingTime", "endDate", "firstByteDate", "mediaStartTime", "mediaType", "responseType", "retryAttempts", "serviceLocation", "startDate", "startTime", "timescale", "wallStartTime", "isInitializationRequest", "setInfo", "info", "init", "ACTION_COMPLETE", "FactoryMaker", "instance", "singletonContexts", "singletonFactories", "classFactories", "getSingletonInstance", "context", "className", "i", "name", "getFactoryByName", "factoriesArray", "updateFactory", "factory", "merge", "classConstructor", "args", "classInstance", "__dashjs_factory_name", "extensionObject", "extension", "override", "apply", "parent", "getClassName", "extend", "childInstance", "setSingletonInstance", "push", "deleteSingletonInstances", "filter", "x", "getSingletonFactory", "undefined", "getInstance", "arguments", "getSingletonFactoryByName", "updateSingletonFactory", "getClassFactory", "create", "getClassFactoryByName", "updateClassFactory", "MssFragmentInfoController", "config", "logger", "fragmentModel", "started", "loadFragmentTimeout", "startFragmentTime", "streamProcessor", "baseURLController", "debug", "stop", "clearTimeout", "loadNextFragmentInfo", "getRepresentationController", "getCurrentRepresentation", "adaptation", "period", "mpd", "manifest", "Period", "AdaptationSet", "segments", "SegmentTemplate", "SegmentTimeline", "S", "request", "segment", "t", "d", "adaptationIndex", "resolve", "path", "media", "replace", "tManifest", "getRequestForSegment", "length", "requestFragment", "getFragmentModel", "isFragmentLoadedOrPending", "executeRequest", "initialize", "getType", "controllerType", "fragmentInfoLoaded", "e", "response", "error", "deltaFragmentTime", "deltaTime", "delay", "Date", "getTime", "Math", "max", "setTimeout", "reset", "<PERSON><PERSON><PERSON><PERSON>", "code", "message", "data", "errors", "publicOnly", "err", "indexOf", "ErrorsBase", "super", "MSS_NO_TFRF_CODE", "MSS_UNSUPPORTED_CODEC_CODE", "MSS_NO_TFRF_MESSAGE", "MSS_UNSUPPORTED_CODEC_MESSAGE", "events", "evt", "EventsBase", "AST_IN_FUTURE", "BASE_URLS_UPDATED", "BUFFER_EMPTY", "BUFFER_LOADED", "BUFFER_LEVEL_STATE_CHANGED", "BUFFER_LEVEL_UPDATED", "DVB_FONT_DOWNLOAD_ADDED", "DVB_FONT_DOWNLOAD_COMPLETE", "DVB_FONT_DOWNLOAD_FAILED", "DYNAMIC_TO_STATIC", "ERROR", "FRAGMENT_LOADING_COMPLETED", "FRAGMENT_LOADING_PROGRESS", "FRAGMENT_LOADING_STARTED", "FRAGMENT_LOADING_ABANDONED", "LOG", "MANIFEST_LOADING_STARTED", "MANIFEST_LOADING_FINISHED", "MANIFEST_LOADED", "METRICS_CHANGED", "METRIC_CHANGED", "METRIC_ADDED", "METRIC_UPDATED", "PERIOD_SWITCH_STARTED", "PERIOD_SWITCH_COMPLETED", "QUALITY_CHANGE_REQUESTED", "QUALITY_CHANGE_RENDERED", "NEW_TRACK_SELECTED", "TRACK_CHANGE_RENDERED", "STREAM_INITIALIZING", "STREAM_UPDATED", "STREAM_ACTIVATED", "STREAM_DEACTIVATED", "STREAM_INITIALIZED", "STREAM_TEARDOWN_COMPLETE", "TEXT_TRACKS_ADDED", "TEXT_TRACK_ADDED", "CUE_ENTER", "CUE_EXIT", "THROUGHPUT_MEASUREMENT_STORED", "TTML_PARSED", "TTML_TO_PARSE", "CAPTION_RENDERED", "CAPTION_CONTAINER_RESIZE", "CAN_PLAY", "CAN_PLAY_THROUGH", "PLAYBACK_ENDED", "PLAYBACK_ERROR", "PLAYBACK_INITIALIZED", "PLAYBACK_NOT_ALLOWED", "PLAYBACK_METADATA_LOADED", "PLAYBACK_LOADED_DATA", "PLAYBACK_PAUSED", "PLAYBACK_PLAYING", "PLAYBACK_PROGRESS", "PLAYBACK_RATE_CHANGED", "PLAYBACK_SEEKED", "PLAYBACK_SEEKING", "PLAYBACK_STALLED", "PLAYBACK_STARTED", "PLAYBACK_TIME_UPDATED", "PLAYBACK_VOLUME_CHANGED", "PLAYBACK_WAITING", "MANIFEST_VALIDITY_CHANGED", "EVENT_MODE_ON_START", "EVENT_MODE_ON_RECEIVE", "CONFORMANCE_VIOLATION", "REPRESENTATION_SWITCH", "ADAPTATION_SET_REMOVED_NO_CAPABILITIES", "CONTENT_STEERING_REQUEST_COMPLETED", "INBAND_PRFT", "MANAGED_MEDIA_SOURCE_START_STREAMING", "MANAGED_MEDIA_SOURCE_END_STREAMING", "MssFragmentMoofProcessor", "dashMetrics", "playbackController", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eventBus", "ISOBoxer", "processTfrf", "tfrf", "tfdt", "timeShiftBufferDepth", "DashJSError", "MssErrors", "entries", "entry", "segmentTime", "endTime", "parseFloat", "fragment_absolute_time", "baseMediaDecodeTime", "updateDVR", "getStreamInfo", "manifestInfo", "fragment_duration", "lastSegment", "isPaused", "splice", "trigger", "Events", "sender", "newDuration", "dvrInfos", "getCurrentDVRInfo", "addDVRInfo", "updateCurrentTime", "getBoxOffset", "offset", "boxes", "size", "convertFragment", "isoFile", "parse<PERSON><PERSON>er", "tfhd", "fetch", "track_ID", "mediaInfo", "traf", "createFullBox", "version", "flags", "floor", "trun", "tfxd", "_parent", "sepiff", "usertype", "saio", "entry_count", "saiz", "sample_count", "default_sample_info_size", "sample_info_size", "NumberOfEntries", "moof", "<PERSON><PERSON><PERSON><PERSON>", "data_offset", "trafPosInMoof", "sencPosInTraf", "write", "updateSegmentList", "Error", "MssFragmentMoovProcessor", "constants", "adaptationSet", "contentProtection", "trackId", "protectionController", "createOriginalFormatBox", "sinf", "codec", "createBox", "data_format", "str", "charCodeAt", "stringToCharCode", "createSchemeTypeBox", "schm", "scheme_type", "scheme_version", "createSchemeInformationBox", "schi", "tenc", "default_IsEncrypted", "default_IV_size", "default_KID", "createTrackEncryptionBox", "hexStringto<PERSON>uffer", "buf", "Uint8Array", "parseInt", "generateMoov", "rep", "arrayBuffer", "ContentProtection", "createFile", "ftyp", "major_brand", "minor_version", "compatible_brands", "createFtypBox", "moov", "mvhd", "creation_time", "modification_time", "Infinity", "round", "rate", "volume", "reserved1", "reserved2", "matrix", "pre_defined", "next_track_ID", "createMvhdBox", "trak", "tkhd", "layer", "alternate_group", "reserved3", "width", "height", "createTkhdBox", "mdia", "mdhd", "language", "lang", "createMdhdBox", "hdlr", "VIDEO", "handler_type", "AUDIO", "id", "reserved", "createHdlrBox", "minf", "vmhd", "graphicsmode", "opcolor", "createVmhdBox", "smhd", "balance", "createSmhdBox", "dinf", "dref", "location", "createDrefBox", "stbl", "_data", "stsd", "codecs", "substring", "avc1", "data_reference_index", "pre_defined1", "pre_defined2", "horizresolution", "vertresolution", "frame_count", "compressorname", "depth", "pre_defined3", "naluBytes", "naluType", "avcC", "avc<PERSON><PERSON>th", "sps", "pps", "AVCProfileIndication", "AVCLevelIndication", "profile_compatibility", "nalus", "codecPrivateData", "split", "slice", "set", "n", "createAVC1ConfigurationRecord", "createAVCVisualSampleEntry", "mp4a", "channelcount", "audioChannels", "samplesize", "reserved_3", "samplerate", "audioSamplingRate", "esds", "audioSpecificConfig", "esdsLength", "createMPEG4AACESDescriptor", "createMP4AudioSampleEntry", "createSampleEntry", "createStsdBox", "trex", "default_sample_description_index", "default_sample_duration", "default_sample_size", "default_sample_flags", "createTrexBox", "keySystems", "pssh_bytes", "pssh", "parsed<PERSON><PERSON><PERSON>", "initData", "Utils", "appendBox", "createProtectionSystemSpecificHeaderBox", "getSupportedKeySystemMetadataFromContentProtection", "createMoovBox", "arrayEqual", "arr1", "arr2", "every", "element", "saioProcessor", "_procFullBox", "_procField", "_procFieldArray", "saizProcessor", "sencProcessor", "_procEntries", "_procEntryField", "_procSubEntries", "clearAndCryptedData", "uuidProcessor", "_parsing", "fragment_count", "MssFragmentProcessor", "mssFragmentMoovProcessor", "mssFragmentMoofProcessor", "processFragment", "addBoxProcessor", "bigInt", "BASE", "LOG_BASE", "MAX_INT", "MAX_INT_ARR", "smallToArray", "DEFAULT_ALPHABET", "supportsNativeBigInt", "BigInt", "Integer", "v", "radix", "alphabet", "caseSensitive", "parseValue", "parseBase", "BigInteger", "sign", "isSmall", "SmallInteger", "NativeBigInt", "isPrecise", "arrayToSmall", "arr", "trim", "compareAbs", "createArray", "Array", "truncate", "ceil", "add", "a", "b", "sum", "l_a", "l_b", "r", "carry", "base", "addAny", "addSmall", "l", "subtract", "difference", "a_l", "b_l", "borrow", "subtractSmall", "multiplyLong", "product", "a_i", "j", "multiplySmall", "shiftLeft", "concat", "multiplyK<PERSON><PERSON><PERSON>", "y", "c", "ac", "bd", "abcd", "multiplySmallAndArray", "square", "divModSmall", "lambda", "q", "remainder", "divisor", "quotient", "divModAny", "self", "negate", "abs", "comparison", "quotientDigit", "shift", "result", "divisorMostSignificantDigit", "divMod1", "guess", "xlen", "highx", "highy", "check", "part", "unshift", "reverse", "divMod2", "qSign", "mod", "mSign", "isBasicPrime", "isUnit", "equals", "isEven", "isDivisibleBy", "lesser", "millerRabinTest", "nPrev", "prev", "divide", "next", "modPow", "plus", "subtractAny", "minus", "small", "multiply", "l1", "l2", "times", "_multiplyBySmall", "divmod", "over", "pow", "toString", "_0", "_1", "_2", "isNegative", "exp", "isZero", "isPositive", "isOdd", "compare", "compareTo", "eq", "notEquals", "neq", "greater", "gt", "lt", "greaterOrEquals", "geq", "lesserOrEquals", "leq", "isPrime", "strict", "bits", "bitLength", "logN", "log", "toJSNumber", "isProbablePrime", "iterations", "randBetween", "modInv", "lastT", "lastR", "zero", "newT", "one", "newR", "powersOfTwo", "powers2Length", "highestPower2", "shift_isSmall", "bitwise", "fn", "xSign", "ySign", "xRem", "not", "yRem", "xDigit", "yDigit", "xDivMod", "yDivMod", "String", "shiftRight", "remQuo", "and", "or", "xor", "LOBMASK_I", "LOBMASK_BI", "roughLOB", "integerLogarithm", "tmp", "p", "min", "gcd", "text", "toLowerCase", "absBase", "alphabetValues", "digits", "parseBaseFromArray", "val", "toBase", "map", "valueOf", "neg", "Number", "out", "left", "digit", "toBaseString", "stringify", "join", "parseStringValue", "decimalPlace", "test", "parseNumberValue", "toArray", "toJSON", "minusOne", "lcm", "isInstance", "low", "random", "restricted", "top", "fromArray", "define", "amd", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BASE64", "manifestModel", "settings", "SUPPORTED_CODECS", "ROLE", "ACCESSIBILITY", "samplingFrequencyIndex", "mimeTypeMap", "initialBufferSettings", "getAttributeAsBoolean", "node", "attrName", "getAttribute", "mapAdaptationSet", "streamIndex", "representations", "segmentTemplate", "qualityLevels", "fallBackId", "contentType", "mimeType", "subType", "max<PERSON><PERSON><PERSON>", "maxHeight", "Role", "schemeIdUri", "Accessibility", "mediaUrl", "streamIndexTimeScale", "segmentTimeline", "chunks", "getElementsByTagName", "prevSegment", "MAX_SAFE_INTEGER", "mapSegmentTimeline", "availabilityTimeOffset", "mapSegmentTemplate", "BaseURL", "Id", "mapRepresentation", "Representation", "qualityLevel", "fourCCValue", "isNaN", "toUpperCase", "warn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avcoti", "exec", "substr", "getH264Codec", "samplingRate", "codecPrivateDataHex", "arr16", "indexFreq", "extensionSamplingFrequencyIndex", "objectType", "Channels", "Uint16Array", "setAttribute", "getAACCodec", "STPP", "swapBytes", "pos1", "pos2", "temp", "processManifest", "xmlDoc", "contentProtections", "smoothStreamingMedia", "protection", "adaptations", "KID", "timestampOffset", "segmentDuration", "protectionHeader", "protocol", "profiles", "dvr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mediaPresentationDuration", "minBufferTime", "ttmlTimeIsRelative", "refreshManifestOnSwitchTrack", "doNotUpdateDVRWindowOnBufferUpdated", "ignorePostponeTimePeriod", "streams", "mapPeriod", "<PERSON><PERSON><PERSON><PERSON>", "pr<PERSON><PERSON><PERSON>", "wrm<PERSON><PERSON>er", "xmlReader", "uuid", "decodeArray", "recordCount", "recordType", "recordLength", "recordValue", "subarray", "getWRMHeaderFromPRHeader", "buffer", "fromCharCode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "querySelector", "textContent", "getKIDFromProtectionHeader", "pro", "__text", "__prefix", "createPRContentProtection", "widevineCP", "wv<PERSON>en<PERSON><PERSON>eader", "encodeASCII", "createWidevineContentProtection", "initialization", "targetLiveDelay", "streaming", "liveDelay", "liveDelayFragmentCount", "targetDelayCapping", "bufferTime", "bufferTimeDefault", "bufferTimeAtTopQuality", "bufferTimeAtTopQualityLongForm", "calcFromSegmentTimeline", "timeShiftBuffer", "update", "prevManifest", "getValue", "presentationTimeOffset", "parse", "window", "performance", "now", "parseDOM", "xmlParseTime", "mss2dashTime", "toPrecision", "getIron", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initSegmentType", "streamController", "mss<PERSON><PERSON><PERSON>", "mssFragmentProcessor", "fragmentInfoControllers", "getStreamProcessor", "getActiveStreamProcessors", "processor", "getFragmentInfoController", "controller", "startFragmentInfoControllers", "for<PERSON>ach", "TEXT", "fragmentInfoController", "onInitFragmentNeeded", "getMediaInfo", "chunk", "DataChunk", "createDataChunk", "streamInfo", "INIT_FRAGMENT_LOADED", "onSegmentMediaLoaded", "isDynamic", "dvrWindowSize", "onPlaybackPaused", "getIsDynamic", "onPlaybackSeeking", "onTTMLPreProcess", "ttmlSubtitles", "off", "INIT_FRAGMENT_NEEDED", "createMssParser", "createMssFragmentProcessor", "registerEvents", "on", "priority", "dashjs", "EVENT_PRIORITY_HIGH", "global"], "sourceRoot": ""}