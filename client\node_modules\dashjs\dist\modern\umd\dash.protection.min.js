!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.dashjs=t():e.dashjs=t()}(self,(function(){return function(){var e={3282:function(e){"use strict";function t(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function n(e,t){for(var n,r="",i=0,s=-1,o=0,a=0;a<=e.length;++a){if(a<e.length)n=e.charCodeAt(a);else{if(47===n)break;n=47}if(47===n){if(s===a-1||1===o);else if(s!==a-1&&2===o){if(r.length<2||2!==i||46!==r.charCodeAt(r.length-1)||46!==r.charCodeAt(r.length-2))if(r.length>2){var E=r.lastIndexOf("/");if(E!==r.length-1){-1===E?(r="",i=0):i=(r=r.slice(0,E)).length-1-r.lastIndexOf("/"),s=a,o=0;continue}}else if(2===r.length||1===r.length){r="",i=0,s=a,o=0;continue}t&&(r.length>0?r+="/..":r="..",i=2)}else r.length>0?r+="/"+e.slice(s+1,a):r=e.slice(s+1,a),i=a-s-1;s=a,o=0}else 46===n&&-1!==o?++o:o=-1}return r}var r={resolve:function(){for(var e,r="",i=!1,s=arguments.length-1;s>=-1&&!i;s--){var o;s>=0?o=arguments[s]:(void 0===e&&(e=process.cwd()),o=e),t(o),0!==o.length&&(r=o+"/"+r,i=47===o.charCodeAt(0))}return r=n(r,!i),i?r.length>0?"/"+r:"/":r.length>0?r:"."},normalize:function(e){if(t(e),0===e.length)return".";var r=47===e.charCodeAt(0),i=47===e.charCodeAt(e.length-1);return 0!==(e=n(e,!r)).length||r||(e="."),e.length>0&&i&&(e+="/"),r?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,n=0;n<arguments.length;++n){var i=arguments[n];t(i),i.length>0&&(void 0===e?e=i:e+="/"+i)}return void 0===e?".":r.normalize(e)},relative:function(e,n){if(t(e),t(n),e===n)return"";if((e=r.resolve(e))===(n=r.resolve(n)))return"";for(var i=1;i<e.length&&47===e.charCodeAt(i);++i);for(var s=e.length,o=s-i,a=1;a<n.length&&47===n.charCodeAt(a);++a);for(var E=n.length-a,c=o<E?o:E,l=-1,u=0;u<=c;++u){if(u===c){if(E>c){if(47===n.charCodeAt(a+u))return n.slice(a+u+1);if(0===u)return n.slice(a+u)}else o>c&&(47===e.charCodeAt(i+u)?l=u:0===u&&(l=0));break}var d=e.charCodeAt(i+u);if(d!==n.charCodeAt(a+u))break;47===d&&(l=u)}var _="";for(u=i+l+1;u<=s;++u)u!==s&&47!==e.charCodeAt(u)||(0===_.length?_+="..":_+="/..");return _.length>0?_+n.slice(a+l):(a+=l,47===n.charCodeAt(a)&&++a,n.slice(a))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var n=e.charCodeAt(0),r=47===n,i=-1,s=!0,o=e.length-1;o>=1;--o)if(47===(n=e.charCodeAt(o))){if(!s){i=o;break}}else s=!1;return-1===i?r?"/":".":r&&1===i?"//":e.slice(0,i)},basename:function(e,n){if(void 0!==n&&"string"!=typeof n)throw new TypeError('"ext" argument must be a string');t(e);var r,i=0,s=-1,o=!0;if(void 0!==n&&n.length>0&&n.length<=e.length){if(n.length===e.length&&n===e)return"";var a=n.length-1,E=-1;for(r=e.length-1;r>=0;--r){var c=e.charCodeAt(r);if(47===c){if(!o){i=r+1;break}}else-1===E&&(o=!1,E=r+1),a>=0&&(c===n.charCodeAt(a)?-1==--a&&(s=r):(a=-1,s=E))}return i===s?s=E:-1===s&&(s=e.length),e.slice(i,s)}for(r=e.length-1;r>=0;--r)if(47===e.charCodeAt(r)){if(!o){i=r+1;break}}else-1===s&&(o=!1,s=r+1);return-1===s?"":e.slice(i,s)},extname:function(e){t(e);for(var n=-1,r=0,i=-1,s=!0,o=0,a=e.length-1;a>=0;--a){var E=e.charCodeAt(a);if(47!==E)-1===i&&(s=!1,i=a+1),46===E?-1===n?n=a:1!==o&&(o=1):-1!==n&&(o=-1);else if(!s){r=a+1;break}}return-1===n||-1===i||0===o||1===o&&n===i-1&&n===r+1?"":e.slice(n,i)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var n=t.dir||t.root,r=t.base||(t.name||"")+(t.ext||"");return n?n===t.root?n+r:n+"/"+r:r}(0,e)},parse:function(e){t(e);var n={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return n;var r,i=e.charCodeAt(0),s=47===i;s?(n.root="/",r=1):r=0;for(var o=-1,a=0,E=-1,c=!0,l=e.length-1,u=0;l>=r;--l)if(47!==(i=e.charCodeAt(l)))-1===E&&(c=!1,E=l+1),46===i?-1===o?o=l:1!==u&&(u=1):-1!==o&&(u=-1);else if(!c){a=l+1;break}return-1===o||-1===E||0===u||1===u&&o===E-1&&o===a+1?-1!==E&&(n.base=n.name=0===a&&s?e.slice(1,E):e.slice(a,E)):(0===a&&s?(n.name=e.slice(1,o),n.base=e.slice(1,E)):(n.name=e.slice(a,o),n.base=e.slice(a,E)),n.ext=e.slice(o,E)),a>0?n.dir=e.slice(0,a-1):s&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};r.posix=r,e.exports=r},8571:function(e,t,n){var r;!function(i,s){"use strict";var o="function",a="undefined",E="object",c="string",l="major",u="model",d="name",_="type",S="vendor",g="version",f="architecture",A="console",y="mobile",h="tablet",T="smarttv",m="wearable",p="embedded",I="Amazon",R="Apple",C="ASUS",D="BlackBerry",M="Browser",b="Chrome",w="Firefox",N="Google",O="Huawei",L="LG",v="Microsoft",P="Motorola",K="Opera",U="Samsung",k="Sharp",Y="Sony",x="Xiaomi",G="Zebra",H="Facebook",F="Chromium OS",B="Mac OS",V=function(e){for(var t={},n=0;n<e.length;n++)t[e[n].toUpperCase()]=e[n];return t},W=function(e,t){return typeof e===c&&-1!==q(t).indexOf(q(e))},q=function(e){return e.toLowerCase()},j=function(e,t){if(typeof e===c)return e=e.replace(/^\s\s*/,""),typeof t===a?e:e.substring(0,500)},z=function(e,t){for(var n,r,i,a,c,l,u=0;u<t.length&&!c;){var d=t[u],_=t[u+1];for(n=r=0;n<d.length&&!c&&d[n];)if(c=d[n++].exec(e))for(i=0;i<_.length;i++)l=c[++r],typeof(a=_[i])===E&&a.length>0?2===a.length?typeof a[1]==o?this[a[0]]=a[1].call(this,l):this[a[0]]=a[1]:3===a.length?typeof a[1]!==o||a[1].exec&&a[1].test?this[a[0]]=l?l.replace(a[1],a[2]):s:this[a[0]]=l?a[1].call(this,l,a[2]):s:4===a.length&&(this[a[0]]=l?a[3].call(this,l.replace(a[1],a[2])):s):this[a]=l||s;u+=2}},X=function(e,t){for(var n in t)if(typeof t[n]===E&&t[n].length>0){for(var r=0;r<t[n].length;r++)if(W(t[n][r],e))return"?"===n?s:n}else if(W(t[n],e))return"?"===n?s:n;return e},Q={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Z={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[g,[d,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[g,[d,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[d,g],[/opios[\/ ]+([\w\.]+)/i],[g,[d,K+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[g,[d,K+" GX"]],[/\bopr\/([\w\.]+)/i],[g,[d,K]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[g,[d,"Baidu"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim)\s?(?:browser)?[\/ ]?([\w\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[d,g],[/\bddg\/([\w\.]+)/i],[g,[d,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[g,[d,"UC"+M]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[g,[d,"WeChat"]],[/konqueror\/([\w\.]+)/i],[g,[d,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[g,[d,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[g,[d,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[g,[d,"Smart Lenovo "+M]],[/(avast|avg)\/([\w\.]+)/i],[[d,/(.+)/,"$1 Secure "+M],g],[/\bfocus\/([\w\.]+)/i],[g,[d,w+" Focus"]],[/\bopt\/([\w\.]+)/i],[g,[d,K+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[g,[d,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[g,[d,"Dolphin"]],[/coast\/([\w\.]+)/i],[g,[d,K+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[g,[d,"MIUI "+M]],[/fxios\/([-\w\.]+)/i],[g,[d,w]],[/\bqihu|(qi?ho?o?|360)browser/i],[[d,"360 "+M]],[/(oculus|sailfish|huawei|vivo)browser\/([\w\.]+)/i],[[d,/(.+)/,"$1 "+M],g],[/samsungbrowser\/([\w\.]+)/i],[g,[d,U+" Internet"]],[/(comodo_dragon)\/([\w\.]+)/i],[[d,/_/g," "],g],[/metasr[\/ ]?([\d\.]+)/i],[g,[d,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[d,"Sogou Mobile"],g],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345Explorer)[\/ ]?([\w\.]+)/i],[d,g],[/(lbbrowser)/i,/\[(linkedin)app\]/i],[d],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[d,H],g],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[d,g],[/\bgsa\/([\w\.]+) .*safari\//i],[g,[d,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[g,[d,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[g,[d,b+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[d,b+" WebView"],g],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[g,[d,"Android "+M]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[d,g],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[g,[d,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[g,d],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[d,[g,X,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[d,g],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[d,"Netscape"],g],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[g,[d,w+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[d,g],[/(cobalt)\/([\w\.]+)/i],[d,[g,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[f,"amd64"]],[/(ia32(?=;))/i],[[f,q]],[/((?:i[346]|x)86)[;\)]/i],[[f,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[f,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[f,"armhf"]],[/windows (ce|mobile); ppc;/i],[[f,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[f,/ower/,"",q]],[/(sun4\w)[;\)]/i],[[f,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[f,q]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[u,[S,U],[_,h]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[u,[S,U],[_,y]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[u,[S,R],[_,y]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[u,[S,R],[_,h]],[/(macintosh);/i],[u,[S,R]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[u,[S,k],[_,y]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[u,[S,O],[_,h]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[u,[S,O],[_,y]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[u,/_/g," "],[S,x],[_,y]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[u,/_/g," "],[S,x],[_,h]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[u,[S,"OPPO"],[_,y]],[/\b(opd2\d{3}a?) bui/i],[u,[S,"OPPO"],[_,h]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[u,[S,"Vivo"],[_,y]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[u,[S,"Realme"],[_,y]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[u,[S,P],[_,y]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[u,[S,P],[_,h]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[u,[S,L],[_,h]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[u,[S,L],[_,y]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[u,[S,"Lenovo"],[_,h]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[u,/_/g," "],[S,"Nokia"],[_,y]],[/(pixel c)\b/i],[u,[S,N],[_,h]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[u,[S,N],[_,y]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[u,[S,Y],[_,y]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[u,"Xperia Tablet"],[S,Y],[_,h]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[u,[S,"OnePlus"],[_,y]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[u,[S,I],[_,h]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[u,/(.+)/g,"Fire Phone $1"],[S,I],[_,y]],[/(playbook);[-\w\),; ]+(rim)/i],[u,S,[_,h]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[u,[S,D],[_,y]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[u,[S,C],[_,h]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[u,[S,C],[_,y]],[/(nexus 9)/i],[u,[S,"HTC"],[_,h]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[S,[u,/_/g," "],[_,y]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[u,[S,"Acer"],[_,h]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[u,[S,"Meizu"],[_,y]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[u,[S,"Ulefone"],[_,y]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[S,u,[_,y]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[S,u,[_,h]],[/(surface duo)/i],[u,[S,v],[_,h]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[u,[S,"Fairphone"],[_,y]],[/(u304aa)/i],[u,[S,"AT&T"],[_,y]],[/\bsie-(\w*)/i],[u,[S,"Siemens"],[_,y]],[/\b(rct\w+) b/i],[u,[S,"RCA"],[_,h]],[/\b(venue[\d ]{2,7}) b/i],[u,[S,"Dell"],[_,h]],[/\b(q(?:mv|ta)\w+) b/i],[u,[S,"Verizon"],[_,h]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[u,[S,"Barnes & Noble"],[_,h]],[/\b(tm\d{3}\w+) b/i],[u,[S,"NuVision"],[_,h]],[/\b(k88) b/i],[u,[S,"ZTE"],[_,h]],[/\b(nx\d{3}j) b/i],[u,[S,"ZTE"],[_,y]],[/\b(gen\d{3}) b.+49h/i],[u,[S,"Swiss"],[_,y]],[/\b(zur\d{3}) b/i],[u,[S,"Swiss"],[_,h]],[/\b((zeki)?tb.*\b) b/i],[u,[S,"Zeki"],[_,h]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[S,"Dragon Touch"],u,[_,h]],[/\b(ns-?\w{0,9}) b/i],[u,[S,"Insignia"],[_,h]],[/\b((nxa|next)-?\w{0,9}) b/i],[u,[S,"NextBook"],[_,h]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[S,"Voice"],u,[_,y]],[/\b(lvtel\-)?(v1[12]) b/i],[[S,"LvTel"],u,[_,y]],[/\b(ph-1) /i],[u,[S,"Essential"],[_,y]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[u,[S,"Envizen"],[_,h]],[/\b(trio[-\w\. ]+) b/i],[u,[S,"MachSpeed"],[_,h]],[/\btu_(1491) b/i],[u,[S,"Rotor"],[_,h]],[/(shield[\w ]+) b/i],[u,[S,"Nvidia"],[_,h]],[/(sprint) (\w+)/i],[S,u,[_,y]],[/(kin\.[onetw]{3})/i],[[u,/\./g," "],[S,v],[_,y]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[u,[S,G],[_,h]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[u,[S,G],[_,y]],[/smart-tv.+(samsung)/i],[S,[_,T]],[/hbbtv.+maple;(\d+)/i],[[u,/^/,"SmartTV"],[S,U],[_,T]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[S,L],[_,T]],[/(apple) ?tv/i],[S,[u,R+" TV"],[_,T]],[/crkey/i],[[u,b+"cast"],[S,N],[_,T]],[/droid.+aft(\w+)( bui|\))/i],[u,[S,I],[_,T]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[u,[S,k],[_,T]],[/(bravia[\w ]+)( bui|\))/i],[u,[S,Y],[_,T]],[/(mitv-\w{5}) bui/i],[u,[S,x],[_,T]],[/Hbbtv.*(technisat) (.*);/i],[S,u,[_,T]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[S,j],[u,j],[_,T]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[_,T]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[S,u,[_,A]],[/droid.+; (shield) bui/i],[u,[S,"Nvidia"],[_,A]],[/(playstation [345portablevi]+)/i],[u,[S,Y],[_,A]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[u,[S,v],[_,A]],[/((pebble))app/i],[S,u,[_,m]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[u,[S,R],[_,m]],[/droid.+; (glass) \d/i],[u,[S,N],[_,m]],[/droid.+; (wt63?0{2,3})\)/i],[u,[S,G],[_,m]],[/(quest( \d| pro)?)/i],[u,[S,H],[_,m]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[S,[_,p]],[/(aeobc)\b/i],[u,[S,I],[_,p]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[u,[_,y]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[u,[_,h]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[_,h]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[_,y]],[/(android[-\w\. ]{0,9});.+buil/i],[u,[S,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[g,[d,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[g,[d,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[d,g],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[g,d]],os:[[/microsoft (windows) (vista|xp)/i],[d,g],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[d,[g,X,Q]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[g,X,Q],[d,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[g,/_/g,"."],[d,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[d,B],[g,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[g,d],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[d,g],[/\(bb(10);/i],[g,[d,D]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[g,[d,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[g,[d,w+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[g,[d,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[g,[d,"watchOS"]],[/crkey\/([\d\.]+)/i],[g,[d,b+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[d,F],g],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[d,g],[/(sunos) ?([\w\.\d]*)/i],[[d,"Solaris"],g],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[d,g]]},$=function(e,t){if(typeof e===E&&(t=e,e=s),!(this instanceof $))return new $(e,t).getResult();var n=typeof i!==a&&i.navigator?i.navigator:s,r=e||(n&&n.userAgent?n.userAgent:""),A=n&&n.userAgentData?n.userAgentData:s,T=t?function(e,t){var n={};for(var r in e)t[r]&&t[r].length%2==0?n[r]=t[r].concat(e[r]):n[r]=e[r];return n}(Z,t):Z,m=n&&n.userAgent==r;return this.getBrowser=function(){var e,t={};return t[d]=s,t[g]=s,z.call(t,r,T.browser),t[l]=typeof(e=t[g])===c?e.replace(/[^\d\.]/g,"").split(".")[0]:s,m&&n&&n.brave&&typeof n.brave.isBrave==o&&(t[d]="Brave"),t},this.getCPU=function(){var e={};return e[f]=s,z.call(e,r,T.cpu),e},this.getDevice=function(){var e={};return e[S]=s,e[u]=s,e[_]=s,z.call(e,r,T.device),m&&!e[_]&&A&&A.mobile&&(e[_]=y),m&&"Macintosh"==e[u]&&n&&typeof n.standalone!==a&&n.maxTouchPoints&&n.maxTouchPoints>2&&(e[u]="iPad",e[_]=h),e},this.getEngine=function(){var e={};return e[d]=s,e[g]=s,z.call(e,r,T.engine),e},this.getOS=function(){var e={};return e[d]=s,e[g]=s,z.call(e,r,T.os),m&&!e[d]&&A&&A.platform&&"Unknown"!=A.platform&&(e[d]=A.platform.replace(/chrome os/i,F).replace(/macos/i,B)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return r},this.setUA=function(e){return r=typeof e===c&&e.length>500?j(e,500):e,this},this.setUA(r),this};$.VERSION="1.0.38",$.BROWSER=V([d,g,l]),$.CPU=V([f]),$.DEVICE=V([u,S,_,A,y,T,h,m,p]),$.ENGINE=$.OS=V([d,g]),typeof t!==a?(e.exports&&(t=e.exports=$),t.UAParser=$):n.amdO?(r=function(){return $}.call(t,n,t,e))===s||(e.exports=r):typeof i!==a&&(i.UAParser=$);var J=typeof i!==a&&(i.jQuery||i.Zepto);if(J&&!J.ua){var ee=new $;J.ua=ee.getResult(),J.ua.get=function(){return ee.getUA()},J.ua.set=function(e){ee.setUA(e);var t=ee.getResult();for(var n in t)J.ua[n]=t[n]}}}("object"==typeof window?window:this)},138:function(e,t){"use strict";const n=function(){let e,t=[];const n={},r={};function i(e,n){for(const r in t){const i=t[r];if(i.context===e&&i.name===n)return i.instance}return null}function s(e,t){return t[e]}function o(e,t,n){e in n&&(n[e]=t)}function a(t,n,r){let i;const s=t.__dashjs_factory_name,o=n[s];if(o){let s=o.instance;if(!o.override)return s.apply({context:n,factory:e},r);i=t.apply({context:n},r),s=s.apply({context:n,factory:e,parent:i},r);for(const e in s)i.hasOwnProperty(e)&&(i[e]=s[e])}else i=t.apply({context:n},r);return i.getClassName=function(){return s},i}return e={extend:function(e,t,n,r){!r[e]&&t&&(r[e]={instance:t,override:n})},getSingletonInstance:i,setSingletonInstance:function(e,n,r){for(const i in t){const s=t[i];if(s.context===e&&s.name===n)return void(t[i].instance=r)}t.push({name:n,context:e,instance:r})},deleteSingletonInstances:function(e){t=t.filter((t=>t.context!==e))},getSingletonFactory:function(e){let r=s(e.__dashjs_factory_name,n);return r||(r=function(n){let r;return void 0===n&&(n={}),{getInstance:function(){return r||(r=i(n,e.__dashjs_factory_name)),r||(r=a(e,n,arguments),t.push({name:e.__dashjs_factory_name,context:n,instance:r})),r}}},n[e.__dashjs_factory_name]=r),r},getSingletonFactoryByName:function(e){return s(e,n)},updateSingletonFactory:function(e,t){o(e,t,n)},getClassFactory:function(e){let t=s(e.__dashjs_factory_name,r);return t||(t=function(t){return void 0===t&&(t={}),{create:function(){return a(e,t,arguments)}}},r[e.__dashjs_factory_name]=t),t},getClassFactoryByName:function(e){return s(e,r)},updateClassFactory:function(e,t){o(e,t,r)}},e}();t.A=n},7263:function(e,t,n){"use strict";var r=n(3282),i=n(8571),s=n(5212);class o{static mixin(e,t,n){let r,i={};if(e)for(let s in t)t.hasOwnProperty(s)&&(r=t[s],s in e&&(e[s]===r||s in i&&i[s]===r)||("object"==typeof e[s]&&null!==e[s]?e[s]=o.mixin(e[s],r,n):e[s]=n(r)));return e}static clone(e){if(!e||"object"!=typeof e)return e;if(e instanceof RegExp)return new RegExp(e);let t;if(e instanceof Array){t=[];for(let n=0,r=e.length;n<r;++n)n in e&&t.push(o.clone(e[n]))}else t={};return o.mixin(t,e,o.clone)}static addAdditionalQueryParameterToUrl(e,t){try{if(!t||0===t.length)return e;let n=e;return t.forEach((e=>{let{key:t,value:r}=e;const i=n.includes("?")?"&":"?";n+=`${i}${encodeURIComponent(t)}=${encodeURIComponent(r)}`})),n}catch(t){return e}}static removeQueryParameterFromUrl(e,t){if(!e||!t)return e;const n=new URL(e),r=new URLSearchParams(n.search);if(!r||0===r.size||!r.has(t))return e;r.delete(t);const i=Array.from(r.entries()).map((e=>{let[t,n]=e;return`${t}=${n}`})).join("&"),s=`${n.origin}${n.pathname}`;return i?`${s}?${i}`:s}static parseHttpHeaders(e){let t={};if(!e)return t;let n=e.trim().split("\r\n");for(let e=0,r=n.length;e<r;e++){let r=n[e],i=r.indexOf(": ");i>0&&(t[r.substring(0,i)]=r.substring(i+2))}return t}static parseQueryParams(e){const t=[],n=new URLSearchParams(e);for(const[e,r]of n.entries())t.push({key:decodeURIComponent(e),value:decodeURIComponent(r)});return t}static generateUuid(){let e=(new Date).getTime();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){const n=(e+16*Math.random())%16|0;return e=Math.floor(e/16),("x"==t?n:3&n|8).toString(16)}))}static generateHashCode(e){let t=0;if(0===e.length)return t;for(let n=0;n<e.length;n++)t=(t<<5)-t+e.charCodeAt(n),t|=0;return t}static getRelativeUrl(e,t){try{const n=new URL(e),i=new URL(t);if(n.protocol=i.protocol,n.origin!==i.origin)return t;let s=r.relative(n.pathname.substr(0,n.pathname.lastIndexOf("/")),i.pathname.substr(0,i.pathname.lastIndexOf("/")));const o=0===s.length?1:0;return s+=i.pathname.substr(i.pathname.lastIndexOf("/")+o,i.pathname.length-1),i.pathname.length<s.length?i.pathname:s}catch(e){return t}}static getHostFromUrl(e){try{return new URL(e).host}catch(e){return null}}static parseUserAgent(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;try{const t=null===e&&"undefined"!=typeof navigator?navigator.userAgent.toLowerCase():"";return(0,i.UAParser)(t)}catch(e){return{}}}static stringHasProtocol(e){return/(http(s?)):\/\//i.test(e)}static bufferSourceToDataView(e){return o.toDataView(e,DataView)}static bufferSourceToInt8(e){return o.toDataView(e,Uint8Array)}static uint8ArrayToString(e){return new TextDecoder("utf-8").decode(e)}static bufferSourceToHex(e){const t=o.bufferSourceToInt8(e);let n="";for(let e of t)e=e.toString(16),1===e.length&&(e="0"+e),n+=e;return n}static toDataView(e,t){const n=o.getArrayBuffer(e);let r=1;"BYTES_PER_ELEMENT"in DataView&&(r=DataView.BYTES_PER_ELEMENT);const i=((e.byteOffset||0)+e.byteLength)/r,s=(e.byteOffset||0)/r,a=Math.floor(Math.max(0,Math.min(s,i)));return new t(n,a,Math.floor(Math.min(a+Math.max(1/0,0),i))-a)}static getArrayBuffer(e){return e instanceof ArrayBuffer?e:e.buffer}static getCodecFamily(e){const{base:t,profile:n}=o._getCodecParts(e);switch(t){case"mp4a":switch(n){case"69":case"6b":case"40.34":return s.A.CODEC_FAMILIES.MP3;case"66":case"67":case"68":case"40.2":case"40.02":case"40.5":case"40.05":case"40.29":case"40.42":return s.A.CODEC_FAMILIES.AAC;case"a5":return s.A.CODEC_FAMILIES.AC3;case"e6":return s.A.CODEC_FAMILIES.EC3;case"b2":return s.A.CODEC_FAMILIES.DTSX;case"a9":return s.A.CODEC_FAMILIES.DTSC}break;case"avc1":case"avc3":return s.A.CODEC_FAMILIES.AVC;case"hvc1":case"hvc3":return s.A.CODEC_FAMILIES.HEVC;default:return t}return t}static _getCodecParts(e){const[t,...n]=e.split(".");return{base:t,profile:n.join(".")}}}t.A=o},8748:function(e,t){"use strict";t.A=class{extend(e,t){if(!e)return;let n=!!t&&t.override,r=!!t&&t.publicOnly;for(const t in e)!e.hasOwnProperty(t)||this[t]&&!n||r&&-1===e[t].indexOf("public_")||(this[t]=e[t])}}},7252:function(e,t){"use strict";t.A=class{extend(e,t){if(!e)return;let n=!!t&&t.override,r=!!t&&t.publicOnly;for(const t in e)!e.hasOwnProperty(t)||this[t]&&!n||r&&-1===e[t].indexOf("public_")||(this[t]=e[t])}}},8854:function(e,t){"use strict";t.A={ACCESSIBILITY:"Accessibility",ADAPTATION_SET:"AdaptationSet",ADAPTATION_SETS:"adaptationSets",ADAPTATION_SET_SWITCHING_SCHEME_ID_URI:"urn:mpeg:dash:adaptation-set-switching:2016",ADD:"add",ASSET_IDENTIFIER:"AssetIdentifier",AUDIO_CHANNEL_CONFIGURATION:"AudioChannelConfiguration",AUDIO_SAMPLING_RATE:"audioSamplingRate",AVAILABILITY_END_TIME:"availabilityEndTime",AVAILABILITY_START_TIME:"availabilityStartTime",AVAILABILITY_TIME_COMPLETE:"availabilityTimeComplete",AVAILABILITY_TIME_OFFSET:"availabilityTimeOffset",BANDWITH:"bandwidth",BASE_URL:"BaseURL",BITSTREAM_SWITCHING:"BitstreamSwitching",BITSTREAM_SWITCHING_MINUS:"bitstreamSwitching",BYTE_RANGE:"byteRange",CAPTION:"caption",CENC_DEFAULT_KID:"cenc:default_KID",CLIENT_DATA_REPORTING:"ClientDataReporting",CLIENT_REQUIREMENT:"clientRequirement",CMCD_PARAMETERS:"CMCDParameters",CODECS:"codecs",CODEC_PRIVATE_DATA:"codecPrivateData",CODING_DEPENDENCY:"codingDependency",CONTENT_COMPONENT:"ContentComponent",CONTENT_PROTECTION:"ContentProtection",CONTENT_STEERING:"ContentSteering",CONTENT_STEERING_RESPONSE:{VERSION:"VERSION",TTL:"TTL",RELOAD_URI:"RELOAD-URI",PATHWAY_PRIORITY:"PATHWAY-PRIORITY",PATHWAY_CLONES:"PATHWAY-CLONES",BASE_ID:"BASE-ID",ID:"ID",URI_REPLACEMENT:"URI-REPLACEMENT",HOST:"HOST",PARAMS:"PARAMS"},CONTENT_TYPE:"contentType",DEFAULT_SERVICE_LOCATION:"defaultServiceLocation",DEPENDENCY_ID:"dependencyId",DURATION:"duration",DVB_PRIORITY:"dvb:priority",DVB_WEIGHT:"dvb:weight",DVB_URL:"dvb:url",DVB_MIMETYPE:"dvb:mimeType",DVB_FONTFAMILY:"dvb:fontFamily",DYNAMIC:"dynamic",END_NUMBER:"endNumber",ESSENTIAL_PROPERTY:"EssentialProperty",EVENT:"Event",EVENT_STREAM:"EventStream",FORCED_SUBTITLE:"forced-subtitle",FRAMERATE:"frameRate",FRAME_PACKING:"FramePacking",GROUP_LABEL:"GroupLabel",HEIGHT:"height",ID:"id",INBAND:"inband",INBAND_EVENT_STREAM:"InbandEventStream",INDEX:"index",INDEX_RANGE:"indexRange",INITIALIZATION:"Initialization",INITIALIZATION_MINUS:"initialization",LA_URL:"Laurl",LA_URL_LOWER_CASE:"laurl",LABEL:"Label",LANG:"lang",LOCATION:"Location",MAIN:"main",MAXIMUM_SAP_PERIOD:"maximumSAPPeriod",MAX_PLAYOUT_RATE:"maxPlayoutRate",MAX_SEGMENT_DURATION:"maxSegmentDuration",MAX_SUBSEGMENT_DURATION:"maxSubsegmentDuration",MEDIA:"media",MEDIA_PRESENTATION_DURATION:"mediaPresentationDuration",MEDIA_RANGE:"mediaRange",MEDIA_STREAM_STRUCTURE_ID:"mediaStreamStructureId",METRICS:"Metrics",METRICS_MINUS:"metrics",MIME_TYPE:"mimeType",MINIMUM_UPDATE_PERIOD:"minimumUpdatePeriod",MIN_BUFFER_TIME:"minBufferTime",MP4_PROTECTION_SCHEME:"urn:mpeg:dash:mp4protection:2011",MPD:"MPD",MPD_TYPE:"mpd",MPD_PATCH_TYPE:"mpdpatch",ORIGINAL_MPD_ID:"mpdId",ORIGINAL_PUBLISH_TIME:"originalPublishTime",PATCH_LOCATION:"PatchLocation",PERIOD:"Period",PRESENTATION_TIME:"presentationTime",PRESENTATION_TIME_OFFSET:"presentationTimeOffset",PRO:"pro",PRODUCER_REFERENCE_TIME:"ProducerReferenceTime",PRODUCER_REFERENCE_TIME_TYPE:{ENCODER:"encoder",CAPTURED:"captured",APPLICATION:"application"},PROFILES:"profiles",PSSH:"pssh",PUBLISH_TIME:"publishTime",QUALITY_RANKING:"qualityRanking",QUERY_BEFORE_START:"queryBeforeStart",QUERY_PART:"$querypart$",RANGE:"range",RATING:"Rating",REF:"ref",REF_ID:"refId",REMOVE:"remove",REPLACE:"replace",REPORTING:"Reporting",REPRESENTATION:"Representation",REPRESENTATION_INDEX:"RepresentationIndex",ROBUSTNESS:"robustness",ROLE:"Role",S:"S",SAR:"sar",SCAN_TYPE:"scanType",SEGMENT_ALIGNMENT:"segmentAlignment",SEGMENT_BASE:"SegmentBase",SEGMENT_LIST:"SegmentList",SEGMENT_PROFILES:"segmentProfiles",SEGMENT_TEMPLATE:"SegmentTemplate",SEGMENT_TIMELINE:"SegmentTimeline",SEGMENT_TYPE:"segment",SEGMENT_URL:"SegmentURL",SERVICE_DESCRIPTION:"ServiceDescription",SERVICE_DESCRIPTION_LATENCY:"Latency",SERVICE_DESCRIPTION_OPERATING_BANDWIDTH:"OperatingBandwidth",SERVICE_DESCRIPTION_OPERATING_QUALITY:"OperatingQuality",SERVICE_DESCRIPTION_PLAYBACK_RATE:"PlaybackRate",SERVICE_DESCRIPTION_SCOPE:"Scope",SERVICE_LOCATION:"serviceLocation",SERVICE_LOCATIONS:"serviceLocations",SOURCE_URL:"sourceURL",START:"start",START_NUMBER:"startNumber",START_WITH_SAP:"startWithSAP",STATIC:"static",STEERING_TYPE:"steering",SUBSET:"Subset",SUBTITLE:"subtitle",SUB_REPRESENTATION:"SubRepresentation",SUB_SEGMENT_ALIGNMENT:"subsegmentAlignment",SUGGESTED_PRESENTATION_DELAY:"suggestedPresentationDelay",SUPPLEMENTAL_PROPERTY:"SupplementalProperty",SUPPLEMENTAL_CODECS:"scte214:supplementalCodecs",TIMESCALE:"timescale",TIMESHIFT_BUFFER_DEPTH:"timeShiftBufferDepth",TTL:"ttl",TYPE:"type",UTC_TIMING:"UTCTiming",VALUE:"value",VIEWPOINT:"Viewpoint",WALL_CLOCK_TIME:"wallClockTime",WIDTH:"width"}},5212:function(e,t){"use strict";t.A={STREAM:"stream",VIDEO:"video",AUDIO:"audio",TEXT:"text",MUXED:"muxed",IMAGE:"image",STPP:"stpp",TTML:"ttml",VTT:"vtt",WVTT:"wvtt",CONTENT_STEERING:"contentSteering",LIVE_CATCHUP_MODE_DEFAULT:"liveCatchupModeDefault",LIVE_CATCHUP_MODE_LOLP:"liveCatchupModeLoLP",MOVING_AVERAGE_SLIDING_WINDOW:"slidingWindow",MOVING_AVERAGE_EWMA:"ewma",BAD_ARGUMENT_ERROR:"Invalid Arguments",MISSING_CONFIG_ERROR:"Missing config parameter(s)",TRACK_SWITCH_MODE_ALWAYS_REPLACE:"alwaysReplace",TRACK_SWITCH_MODE_NEVER_REPLACE:"neverReplace",TRACK_SELECTION_MODE_FIRST_TRACK:"firstTrack",TRACK_SELECTION_MODE_HIGHEST_BITRATE:"highestBitrate",TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY:"highestEfficiency",TRACK_SELECTION_MODE_WIDEST_RANGE:"widestRange",CMCD_QUERY_KEY:"CMCD",CMCD_MODE_QUERY:"query",CMCD_MODE_HEADER:"header",CMCD_AVAILABLE_KEYS:["br","d","ot","tb","bl","dl","mtp","nor","nrr","su","bs","rtp","cid","pr","sf","sid","st","v"],CMCD_V2_AVAILABLE_KEYS:["msd","ltc"],CMCD_AVAILABLE_REQUESTS:["segment","mpd","xlink","steering","other"],INITIALIZE:"initialize",TEXT_SHOWING:"showing",TEXT_HIDDEN:"hidden",TEXT_DISABLED:"disabled",ACCESSIBILITY_CEA608_SCHEME:"urn:scte:dash:cc:cea-608:2015",CC1:"CC1",CC3:"CC3",UTF8:"utf-8",SCHEME_ID_URI:"schemeIdUri",START_TIME:"starttime",SERVICE_DESCRIPTION_DVB_LL_SCHEME:"urn:dvb:dash:lowlatency:scope:2019",SUPPLEMENTAL_PROPERTY_DVB_LL_SCHEME:"urn:dvb:dash:lowlatency:critical:2019",CTA_5004_2023_SCHEME:"urn:mpeg:dash:cta-5004:2023",THUMBNAILS_SCHEME_ID_URIS:["http://dashif.org/thumbnail_tile","http://dashif.org/guidelines/thumbnail_tile"],FONT_DOWNLOAD_DVB_SCHEME:"urn:dvb:dash:fontdownload:2014",COLOUR_PRIMARIES_SCHEME_ID_URI:"urn:mpeg:mpegB:cicp:ColourPrimaries",URL_QUERY_INFO_SCHEME:"urn:mpeg:dash:urlparam:2014",EXT_URL_QUERY_INFO_SCHEME:"urn:mpeg:dash:urlparam:2016",MATRIX_COEFFICIENTS_SCHEME_ID_URI:"urn:mpeg:mpegB:cicp:MatrixCoefficients",TRANSFER_CHARACTERISTICS_SCHEME_ID_URI:"urn:mpeg:mpegB:cicp:TransferCharacteristics",HDR_METADATA_FORMAT_SCHEME_ID_URI:"urn:dvb:dash:hdr-dmi",HDR_METADATA_FORMAT_VALUES:{ST2094_10:"ST2094-10",SL_HDR2:"SL-HDR2",ST2094_40:"ST2094-40"},MEDIA_CAPABILITIES_API:{COLORGAMUT:{SRGB:"srgb",P3:"p3",REC2020:"rec2020"},TRANSFERFUNCTION:{SRGB:"srgb",PQ:"pq",HLG:"hlg"},HDR_METADATATYPE:{SMPTE_ST_2094_10:"smpteSt2094-10",SLHDR2:"slhdr2",SMPTE_ST_2094_40:"smpteSt2094-40"}},XML:"XML",ARRAY_BUFFER:"ArrayBuffer",DVB_REPORTING_URL:"dvb:reportingUrl",DVB_PROBABILITY:"dvb:probability",OFF_MIMETYPE:"application/font-sfnt",WOFF_MIMETYPE:"application/font-woff",VIDEO_ELEMENT_READY_STATES:{HAVE_NOTHING:0,HAVE_METADATA:1,HAVE_CURRENT_DATA:2,HAVE_FUTURE_DATA:3,HAVE_ENOUGH_DATA:4},FILE_LOADER_TYPES:{FETCH:"fetch_loader",XHR:"xhr_loader"},THROUGHPUT_TYPES:{LATENCY:"throughput_type_latency",BANDWIDTH:"throughput_type_bandwidth"},THROUGHPUT_CALCULATION_MODES:{EWMA:"throughputCalculationModeEwma",ZLEMA:"throughputCalculationModeZlema",ARITHMETIC_MEAN:"throughputCalculationModeArithmeticMean",BYTE_SIZE_WEIGHTED_ARITHMETIC_MEAN:"throughputCalculationModeByteSizeWeightedArithmeticMean",DATE_WEIGHTED_ARITHMETIC_MEAN:"throughputCalculationModeDateWeightedArithmeticMean",HARMONIC_MEAN:"throughputCalculationModeHarmonicMean",BYTE_SIZE_WEIGHTED_HARMONIC_MEAN:"throughputCalculationModeByteSizeWeightedHarmonicMean",DATE_WEIGHTED_HARMONIC_MEAN:"throughputCalculationModeDateWeightedHarmonicMean"},LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE:{MOOF_PARSING:"lowLatencyDownloadTimeCalculationModeMoofParsing",DOWNLOADED_DATA:"lowLatencyDownloadTimeCalculationModeDownloadedData",AAST:"lowLatencyDownloadTimeCalculationModeAast"},RULES_TYPES:{QUALITY_SWITCH_RULES:"qualitySwitchRules",ABANDON_FRAGMENT_RULES:"abandonFragmentRules"},QUALITY_SWITCH_RULES:{BOLA_RULE:"BolaRule",THROUGHPUT_RULE:"ThroughputRule",INSUFFICIENT_BUFFER_RULE:"InsufficientBufferRule",SWITCH_HISTORY_RULE:"SwitchHistoryRule",DROPPED_FRAMES_RULE:"DroppedFramesRule",LEARN_TO_ADAPT_RULE:"L2ARule",LOL_PLUS_RULE:"LoLPRule"},ABANDON_FRAGMENT_RULES:{ABANDON_REQUEST_RULE:"AbandonRequestsRule"},ID3_SCHEME_ID_URI:"https://aomedia.org/emsg/ID3",COMMON_ACCESS_TOKEN_HEADER:"common-access-token",DASH_ROLE_SCHEME_ID:"urn:mpeg:dash:role:2011",CODEC_FAMILIES:{MP3:"mp3",AAC:"aac",AC3:"ac3",EC3:"ec3",DTSX:"dtsx",DTSC:"dtsc",AVC:"avc",HEVC:"hevc"}}},2861:function(e,t){"use strict";t.A={CLEARKEY_KEYSTEM_STRING:"org.w3.clearkey",WIDEVINE_KEYSTEM_STRING:"com.widevine.alpha",PLAYREADY_KEYSTEM_STRING:"com.microsoft.playready",PLAYREADY_RECOMMENDATION_KEYSTEM_STRING:"com.microsoft.playready.recommendation",WIDEVINE_UUID:"edef8ba9-79d6-4ace-a3c8-27dcd51d21ed",PLAYREADY_UUID:"9a04f079-9840-4286-ab92-e65be0885f95",CLEARKEY_UUID:"e2719d58-a985-b3c9-781a-b030af78d30e",W3C_CLEARKEY_UUID:"1077efec-c0b2-4d02-ace3-3c1e52e2fb4b",INITIALIZATION_DATA_TYPE_CENC:"cenc",INITIALIZATION_DATA_TYPE_KEYIDS:"keyids",INITIALIZATION_DATA_TYPE_WEBM:"webm",ENCRYPTION_SCHEME_CENC:"cenc",ENCRYPTION_SCHEME_CBCS:"cbcs",MEDIA_KEY_MESSAGE_TYPES:{LICENSE_REQUEST:"license-request",LICENSE_RENEWAL:"license-renewal",LICENSE_RELEASE:"license-release",INDIVIDUALIZATION_REQUEST:"individualization-request"},ROBUSTNESS_STRINGS:{WIDEVINE:{SW_SECURE_CRYPTO:"SW_SECURE_CRYPTO",SW_SECURE_DECODE:"SW_SECURE_DECODE",HW_SECURE_CRYPTO:"HW_SECURE_CRYPTO",HW_SECURE_DECODE:"HW_SECURE_DECODE",HW_SECURE_ALL:"HW_SECURE_ALL"}},MEDIA_KEY_STATUSES:{USABLE:"usable",EXPIRED:"expired",RELEASED:"released",OUTPUT_RESTRICTED:"output-restricted",OUTPUT_DOWNSCALED:"output-downscaled",STATUS_PENDING:"status-pending",INTERNAL_ERROR:"internal-error"}}},445:function(e,t,n){"use strict";var r=n(7252);class i extends r.A{constructor(){super(),this.INTERNAL_KEY_MESSAGE="internalKeyMessage",this.INTERNAL_KEY_STATUSES_CHANGED="internalkeyStatusesChanged",this.KEY_ADDED="public_keyAdded",this.KEY_ERROR="public_keyError",this.KEY_MESSAGE="public_keyMessage",this.KEY_SESSION_CLOSED="public_keySessionClosed",this.KEY_SESSION_CREATED="public_keySessionCreated",this.KEY_SESSION_REMOVED="public_keySessionRemoved",this.KEY_STATUSES_CHANGED="public_keyStatusesChanged",this.KEY_STATUSES_MAP_UPDATED="keyStatusesMapUpdated",this.KEY_SYSTEM_ACCESS_COMPLETE="public_keySystemAccessComplete",this.KEY_SYSTEM_SELECTED="public_keySystemSelected",this.LICENSE_REQUEST_COMPLETE="public_licenseRequestComplete",this.LICENSE_REQUEST_SENDING="public_licenseRequestSending",this.NEED_KEY="needkey",this.PROTECTION_CREATED="public_protectioncreated",this.PROTECTION_DESTROYED="public_protectiondestroyed",this.SERVER_CERTIFICATE_UPDATED="serverCertificateUpdated",this.TEARDOWN_COMPLETE="protectionTeardownComplete",this.VIDEO_ELEMENT_SELECTED="videoElementSelected",this.KEY_SESSION_UPDATED="public_keySessionUpdated"}}let s=new i;t.A=s},1923:function(e,t,n){"use strict";var r=n(8748);class i extends r.A{constructor(){super(),this.MEDIA_KEYERR_CODE=100,this.MEDIA_KEYERR_UNKNOWN_CODE=101,this.MEDIA_KEYERR_CLIENT_CODE=102,this.MEDIA_KEYERR_SERVICE_CODE=103,this.MEDIA_KEYERR_OUTPUT_CODE=104,this.MEDIA_KEYERR_HARDWARECHANGE_CODE=105,this.MEDIA_KEYERR_DOMAIN_CODE=106,this.MEDIA_KEY_MESSAGE_ERROR_CODE=107,this.MEDIA_KEY_MESSAGE_NO_CHALLENGE_ERROR_CODE=108,this.SERVER_CERTIFICATE_UPDATED_ERROR_CODE=109,this.KEY_STATUS_CHANGED_EXPIRED_ERROR_CODE=110,this.MEDIA_KEY_MESSAGE_NO_LICENSE_SERVER_URL_ERROR_CODE=111,this.KEY_SYSTEM_ACCESS_DENIED_ERROR_CODE=112,this.KEY_SESSION_CREATED_ERROR_CODE=113,this.MEDIA_KEY_MESSAGE_LICENSER_ERROR_CODE=114,this.MEDIA_KEYERR_UNKNOWN_MESSAGE="An unspecified error occurred. This value is used for errors that don't match any of the other codes.",this.MEDIA_KEYERR_CLIENT_MESSAGE="The Key System could not be installed or updated.",this.MEDIA_KEYERR_SERVICE_MESSAGE="The message passed into update indicated an error from the license service.",this.MEDIA_KEYERR_OUTPUT_MESSAGE="There is no available output device with the required characteristics for the content protection system.",this.MEDIA_KEYERR_HARDWARECHANGE_MESSAGE="A hardware configuration change caused a content protection error.",this.MEDIA_KEYERR_DOMAIN_MESSAGE="An error occurred in a multi-device domain licensing configuration. The most common error is a failure to join the domain.",this.MEDIA_KEY_MESSAGE_ERROR_MESSAGE="Multiple key sessions were creates with a user-agent that does not support sessionIDs!! Unpredictable behavior ahead!",this.MEDIA_KEY_MESSAGE_NO_CHALLENGE_ERROR_MESSAGE="DRM: Empty key message from CDM",this.SERVER_CERTIFICATE_UPDATED_ERROR_MESSAGE="Error updating server certificate -- ",this.KEY_STATUS_CHANGED_EXPIRED_ERROR_MESSAGE="DRM: KeyStatusChange error! -- License has expired",this.MEDIA_KEY_MESSAGE_NO_LICENSE_SERVER_URL_ERROR_MESSAGE="DRM: No license server URL specified!",this.KEY_SYSTEM_ACCESS_DENIED_ERROR_MESSAGE="DRM: KeySystem Access Denied! -- ",this.KEY_SESSION_CREATED_ERROR_MESSAGE="DRM: unable to create session! --",this.MEDIA_KEY_MESSAGE_LICENSER_ERROR_MESSAGE="DRM: licenser error! --"}}let s=new i;t.A=s},1944:function(e,t){"use strict";t.A=class{constructor(e,t,n){this.code=e||null,this.message=t||null,this.data=n||null}}},7568:function(e,t,n){"use strict";n.d(t,{G:function(){return r}});class r{constructor(){this.tcpid=null,this.type=null,this.url=null,this.actualurl=null,this.range=null,this.trequest=null,this.tresponse=null,this.responsecode=null,this.interval=null,this.trace=[],this.cmsd=null,this._stream=null,this._tfinish=null,this._mediaduration=null,this._responseHeaders=null,this._serviceLocation=null,this._fileLoaderType=null,this._resourceTimingValues=null}}r.GET="GET",r.HEAD="HEAD",r.MPD_TYPE="MPD",r.XLINK_EXPANSION_TYPE="XLinkExpansion",r.INIT_SEGMENT_TYPE="InitializationSegment",r.INDEX_SEGMENT_TYPE="IndexSegment",r.MEDIA_SEGMENT_TYPE="MediaSegment",r.BITSTREAM_SWITCHING_SEGMENT_TYPE="BitstreamSwitchingSegment",r.MSS_FRAGMENT_INFO_SEGMENT_TYPE="FragmentInfoSegment",r.DVB_REPORTING_TYPE="DVBReporting",r.LICENSE="license",r.CONTENT_STEERING_TYPE="ContentSteering",r.OTHER_TYPE="other"}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var s=t[r]={exports:{}};return e[r].call(s.exports,s,s.exports,n),s.exports}n.amdO={},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};var r={};return function(){"use strict";n.d(r,{default:function(){return _e}});var e=n(8854),t=n(2861);const i={prefixes:["clearkey","dashif","ck"]};class s{static findMp4ProtectionElement(n){let r=null;for(let i=0;i<n.length;++i){let s=n[i];s.schemeIdUri&&s.schemeIdUri.toLowerCase()===e.A.MP4_PROTECTION_SCHEME&&s.value&&(s.value.toLowerCase()===t.A.ENCRYPTION_SCHEME_CENC||s.value.toLowerCase()===t.A.ENCRYPTION_SCHEME_CBCS)&&(r=s)}return r}static getPSSHData(e){let t=8,n=new DataView(e),r=n.getUint8(t);return t+=20,r>0&&(t+=4+16*n.getUint32(t)),t+=4,e.slice(t)}static getPSSHForKeySystem(e,t){let n=s.parsePSSHList(t);return e&&n.hasOwnProperty(e.uuid.toLowerCase())?n[e.uuid.toLowerCase()]:null}static parseInitDataFromContentProtection(e,t){return"pssh"in e&&e.pssh?(e.pssh.__text=e.pssh.__text.replace(/\r?\n|\r/g,"").replace(/\s+/g,""),t.decodeArray(e.pssh.__text).buffer):null}static parsePSSHList(e){if(null==e)return[];let t=new DataView(e.buffer||e),n={},r=0;for(;;){let e,i,s,o,a,E,c=r;if(r>=t.buffer.byteLength)break;if(e=t.getUint32(r),i=r+e,r+=4,1886614376===t.getUint32(r))if(r+=4,s=t.getUint8(r),0===s||1===s){for(r++,r+=3,o="",a=0;a<4;a++)E=t.getUint8(r+a).toString(16),o+=1===E.length?"0"+E:E;for(r+=4,o+="-",a=0;a<2;a++)E=t.getUint8(r+a).toString(16),o+=1===E.length?"0"+E:E;for(r+=2,o+="-",a=0;a<2;a++)E=t.getUint8(r+a).toString(16),o+=1===E.length?"0"+E:E;for(r+=2,o+="-",a=0;a<2;a++)E=t.getUint8(r+a).toString(16),o+=1===E.length?"0"+E:E;for(r+=2,o+="-",a=0;a<6;a++)E=t.getUint8(r+a).toString(16),o+=1===E.length?"0"+E:E;r+=6,o=o.toLowerCase(),r+=4,n[o]=t.buffer.slice(c,i),r=i}else r=i;else r=i}return n}static getLicenseServerUrlFromMediaInfo(e,t){try{if(!e||0===e.length)return null;let n=0,r=null;for(;n<e.length&&!r;){const s=e[n];if(s&&s.contentProtection&&s.contentProtection.length>0){const e=s.contentProtection.filter((e=>e.schemeIdUri&&e.schemeIdUri===t));if(e&&e.length>0){let t=0;for(;t<e.length&&!r;){const n=e[t];n.laUrl&&n.laUrl.__prefix&&i.prefixes.includes(n.laUrl.__prefix)&&n.laUrl.__text&&(r=n.laUrl.__text),t+=1}}}n+=1}return r}catch(e){return null}}static hexKidToBufferSource(e){const t=e.replace(/-/g,"");return new Uint8Array(t.match(/[\da-f]{2}/gi).map((function(e){return parseInt(e,16)}))).buffer}}var o=s,a=class{constructor(e,t){this.contentType=e,this.robustness=t}},E=class{constructor(e,n,r,i,s,o){this.initDataTypes=o&&o.length>0?o:[t.A.INITIALIZATION_DATA_TYPE_CENC],e&&e.length&&(this.audioCapabilities=e),n&&n.length&&(this.videoCapabilities=n),this.distinctiveIdentifier=r,this.persistentState=i,this.sessionTypes=s}},c=n(1923),l=n(1944),u=class{constructor(e,t,n,r,i,s,o,a){this.url=e,this.method=t,this.responseType=n,this.headers=r,this.withCredentials=i,this.messageType=s,this.sessionId=o,this.data=a}},d=class{constructor(e,t,n){this.url=e,this.headers=t,this.data=n}},_=n(7568),S=n(7263),g=n(5212),f=n(138);function A(e){const n=(e=e||{}).BASE64,r=e.cmcdModel,i=e.constants,s=e.customParametersModel,f=e.debug,A=e.eventBus,y=e.events,h=e.protectionKeyController,T=e.settings;let m,p,I,R,C,D,M,b,w,N,O,L,v=e.protectionModel,P=[];function K(){if(!(A&&A.hasOwnProperty("on")&&h&&h.hasOwnProperty("getSupportedKeySystemMetadataFromContentProtection")))throw new Error("Missing config parameter(s)")}function U(e,t){O||R?O&&k():function(e,t){if(R)return;var r;R=!0;const i=function(e){const t=[];for(let n=0;n<e.length;n++){const r=Y(e[n]);t.push({ks:e[n].ks,configs:[r],protData:e[n].protData})}return t}(e=(r=e).sort(((e,t)=>(m&&m[e.ks.systemString]&&m[e.ks.systemString].priority>=0?m[e.ks.systemString].priority:r.length)-(m&&m[t.ks.systemString]&&m[t.ks.systemString].priority>=0?m[t.ks.systemString].priority:r.length))));let s;v.requestKeySystemAccess(i).then((e=>(s=e.data,function(e){let t=e&&e.selectedSystemString?e.selectedSystemString:e.keySystem.systemString;return M.info("DRM: KeySystem Access Granted for system string ("+t+")!  Selecting key system..."),v.selectKeySystem(e)}(s)))).then((e=>{!function(e,t){O=e,R=!1,A.trigger(y.KEY_SYSTEM_SELECTED,{data:t});const r=H(O);r&&r.serverCertificate&&r.serverCertificate.length>0&&v.setServerCertificate(n.decodeArray(r.serverCertificate).buffer),k()}(e,s)})).catch((e=>{!function(e,t){O=null,R=!1,t||A.trigger(y.KEY_SYSTEM_SELECTED,{data:null,error:new l.A(c.A.KEY_SYSTEM_ACCESS_DENIED_ERROR_CODE,c.A.KEY_SYSTEM_ACCESS_DENIED_ERROR_MESSAGE+"Error selecting key system! -- "+e.error)})}(e,t)}))}(e,t)}function k(){let e;for(let n=0;n<w.length;n++)for(e=0;e<w[n].length;e++)if(O===w[n][e].ks){t=w[n][e],h.isClearKey(O)&&function(e){if(e.protData&&e.protData.hasOwnProperty("clearkeys")&&0!==Object.keys(e.protData.clearkeys).length){const t={kids:Object.keys(e.protData.clearkeys)};e.initData=(new TextEncoder).encode(JSON.stringify(t))}}(t),t.sessionId?x(t):null!==t.initData&&G(t);break}var t;w=[]}function Y(e){const n=e.protData,r=[],s=[],o=n&&n.initDataTypes&&n.initDataTypes.length>0?n.initDataTypes:[t.A.INITIALIZATION_DATA_TYPE_CENC],c=n&&n.audioRobustness&&n.audioRobustness.length>0?n.audioRobustness:N,l=n&&n.videoRobustness&&n.videoRobustness.length>0?n.videoRobustness:N,u=e.sessionType,d=n&&n.distinctiveIdentifier?n.distinctiveIdentifier:"optional",_=n&&n.persistentState?n.persistentState:"temporary"===u?"optional":"required";return b.forEach((e=>{e.type===i.AUDIO?r.push(new a(e.codec,c)):e.type===i.VIDEO&&s.push(new a(e.codec,l))})),new E(r,s,d,_,[u],o)}function x(e){K(),v.loadKeySession(e)}function G(e){if(e&&function(e){if(!e)return!1;try{const t=v.getSessionTokens();for(let n=0;n<t.length;n++)if(t[n].getKeyId()===e)return!0;return!1}catch(e){return!1}}(e.keyId))return;const t=o.getPSSHForKeySystem(O,e?e.initData:null);if(t){if(F(t))return;try{e.initData=t,v.createKeySession(e)}catch(e){A.trigger(y.KEY_SESSION_CREATED,{data:null,error:new l.A(c.A.KEY_SESSION_CREATED_ERROR_CODE,c.A.KEY_SESSION_CREATED_ERROR_MESSAGE+e.message)})}}else e&&e.initData?v.createKeySession(e):A.trigger(y.KEY_SESSION_CREATED,{data:null,error:new l.A(c.A.KEY_SESSION_CREATED_ERROR_CODE,c.A.KEY_SESSION_CREATED_ERROR_MESSAGE+"Selected key system is "+(O?O.systemString:null)+".  needkey/encrypted event contains no initData corresponding to that key system!")})}function H(e){if(e){const t=e.systemString;if(m)return t in m?m[t]:null}return null}function F(e){if(!e)return!1;try{const t=v.getAllInitData();for(let n=0;n<t.length;n++)if(h.initDataEquals(e,t[n]))return M.debug("DRM: Ignoring initData because we have already seen it!"),!0;return!1}catch(e){return!1}}function B(e){K(),e?(v.setMediaElement(e),A.on(y.NEED_KEY,Z,p)):null===e&&(v.setMediaElement(e),A.off(y.NEED_KEY,Z,p))}function V(e){M.debug("DRM: onKeyMessage");const n=e.data;A.trigger(y.KEY_MESSAGE,{data:n});const r=n.messageType?n.messageType:t.A.MEDIA_KEY_MESSAGE_TYPES.LICENSE_REQUEST,i=n.message,a=n.sessionToken,E=H(O),g=h.getLicenseServerModelInstance(O,E,r),f={sessionToken:a,messageType:r};if(i&&0!==i.byteLength){if(!g)return M.debug("DRM: License server request not required for this message (type = "+e.data.messageType+").  Session ID = "+a.getSessionId()),void W(f);if(h.isClearKey(O)){const e=h.processClearKeyLicenseRequest(O,E,i);if(e&&e.keyPairs&&e.keyPairs.length>0)return M.debug("DRM: ClearKey license request handled by application!"),W(f),void v.updateKeySession(a,e)}!function(e,n,r){const i=e.sessionToken,a=e.messageType?e.messageType:t.A.MEDIA_KEY_MESSAGE_TYPES.LICENSE_REQUEST,E={sessionToken:i,messageType:a},g=O?O.systemString:null;let f=function(e,t,n,r,i){let s=null;const a=r.message;if(e&&e.serverURL){const n=e.serverURL;"string"==typeof n&&""!==n?s=n:"object"==typeof n&&n.hasOwnProperty(t)&&(s=n[t])}else if(e&&e.laURL&&""!==e.laURL)s=e.laURL;else if(s=o.getLicenseServerUrlFromMediaInfo(b,O.schemeIdURI),!s&&!h.isClearKey(O)){const e=o.getPSSHData(n.initData);s=O.getLicenseServerURLFromInitData(e),s||(s=r.laURL)}return s=i.getServerURLFromMessage(s,a,t),s}(r,a,i,e,n);if(!f)return void W(E,new l.A(c.A.MEDIA_KEY_MESSAGE_NO_LICENSE_SERVER_URL_ERROR_CODE,c.A.MEDIA_KEY_MESSAGE_NO_LICENSE_SERVER_URL_ERROR_MESSAGE));const A={};let y=!1;r&&z(A,r.httpRequestHeaders);const m=e.message;z(A,O.getRequestHeadersFromMessage(m)),Object.keys(A).forEach((e=>{"authorization"===e.toLowerCase()&&(y=!0)})),r&&"boolean"==typeof r.withCredentials&&(y=r.withCredentials);const p=function(e){if(v)if(e.status>=200&&e.status<=299){const t=S.A.parseHttpHeaders(e.getAllResponseHeaders?e.getAllResponseHeaders():null);let r=new d(e.responseURL,t,e.response);Q(s.getLicenseResponseFilters(),r).then((()=>{const t=n.getLicenseMessage(r.data,g,a);null!==t?(W(E),v.updateKeySession(i,t)):X(e,E,g,a,n)}))}else X(e,E,g,a,n)},I=function(e){W(E,new l.A(c.A.MEDIA_KEY_MESSAGE_LICENSER_ERROR_CODE,c.A.MEDIA_KEY_MESSAGE_LICENSER_ERROR_MESSAGE+g+' update, XHR aborted. status is "'+e.statusText+'" ('+e.status+"), readyState is "+e.readyState))},R=function(e){W(E,new l.A(c.A.MEDIA_KEY_MESSAGE_LICENSER_ERROR_CODE,c.A.MEDIA_KEY_MESSAGE_LICENSER_ERROR_MESSAGE+g+' update, XHR error. status is "'+e.statusText+'" ('+e.status+"), readyState is "+e.readyState))},C=O.getLicenseRequestFromMessage(m),D=n.getHTTPMethod(a),M=n.getResponseType(g,a),w=r&&!isNaN(r.httpTimeout)?r.httpTimeout:8e3,N=i.getSessionId()||null;let L=new u(f,D,M,A,y,a,N,C);const P=isNaN(T.get().streaming.retryAttempts[_.G.LICENSE])?3:T.get().streaming.retryAttempts[_.G.LICENSE];Q(s.getLicenseRequestFilters(),L).then((()=>{q(L,P,w,p,I,R)}))}(n,g,E)}else W(f,new l.A(c.A.MEDIA_KEY_MESSAGE_NO_CHALLENGE_ERROR_CODE,c.A.MEDIA_KEY_MESSAGE_NO_CHALLENGE_ERROR_MESSAGE))}function W(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;A.trigger(y.LICENSE_REQUEST_COMPLETE,{data:e,error:t})}function q(e,t,n,i,s,o){const a=new XMLHttpRequest,E=r.getCmcdParametersFromManifest();if(r.isCmcdEnabled()&&(E.mode?E.mode:T.get().streaming.cmcd.mode)===g.A.CMCD_MODE_QUERY){const t=r.getQueryParameter({url:e.url,type:_.G.LICENSE});t&&(e.url=S.A.addAdditionalQueryParameterToUrl(e.url,[t]))}a.open(e.method,e.url,!0),a.responseType=e.responseType,a.withCredentials=e.withCredentials,n>0&&(a.timeout=n);for(const t in e.headers)a.setRequestHeader(t,e.headers[t]);if(r.isCmcdEnabled()&&(E.mode?E.mode:T.get().streaming.cmcd.mode)===g.A.CMCD_MODE_HEADER){const t=r.getHeaderParameters({url:e.url,type:_.G.LICENSE});if(t)for(const e in t){let n=t[e];n&&a.setRequestHeader(e,n)}}const c=function(){t--;const r=isNaN(T.get().streaming.retryIntervals[_.G.LICENSE])?1e3:T.get().streaming.retryIntervals[_.G.LICENSE];C=setTimeout((function(){q(e,t,n,i,s,o)}),r)};a.onload=function(){D=null,this.status>=200&&this.status<=299||t<=0?i(this):(M.warn("License request failed ("+this.status+"). Retrying it... Pending retries: "+t),c())},a.ontimeout=a.onerror=function(){D=null,t<=0?o(this):(M.warn("License request network request failed . Retrying it... Pending retries: "+t),c())},a.onabort=function(){s(this)},A.trigger(y.LICENSE_REQUEST_SENDING,{url:e.url,headers:e.headers,payload:e.data,sessionId:e.sessionId}),D=a,a.send(e.data)}function j(){D&&(D.onloadend=D.onerror=D.onprogress=void 0,D.abort(),D=null),C&&(clearTimeout(C),C=null)}function z(e,t){if(t)for(const n in t)e[n]=t[n]}function X(e,t,n,r,i){let s="NONE",o=null;e.response&&(s=i.getErrorResponse(e.response,n,r),o={serverResponse:e.response||null,responseCode:e.status||null,responseText:e.statusText||null}),W(t,new l.A(c.A.MEDIA_KEY_MESSAGE_LICENSER_ERROR_CODE,c.A.MEDIA_KEY_MESSAGE_LICENSER_ERROR_MESSAGE+n+' update, XHR complete. status is "'+e.statusText+'" ('+e.status+"), readyState is "+e.readyState+".  Response is "+s,o))}function Q(e,t){return e?e.reduce(((e,n)=>e.then((()=>n(t)))),Promise.resolve()):Promise.resolve()}function Z(e,n){if(T.get().streaming.protection.ignoreEmeEncryptedEvent)return;if(M.debug("DRM: onNeedKey"),e.key.initDataType!==t.A.INITIALIZATION_DATA_TYPE_CENC)return void M.warn("DRM:  Only 'cenc' initData is supported!  Ignoring initData of type: "+e.key.initDataType);if(0===b.length&&(M.warn("DRM: onNeedKey called before initializeForMedia, wait until initialized"),(n=void 0===n?1:n+1)<5))return void P.push(setTimeout((()=>{Z(e,n)}),500));let r=e.key.initData;if(ArrayBuffer.isView(r)&&(r=r.buffer),O){const e=o.getPSSHForKeySystem(O,r);if(e&&F(e))return}M.debug("DRM: initData:",String.fromCharCode.apply(null,new Uint8Array(r)));const i=h.getSupportedKeySystemMetadataFromSegmentPssh(r,m,L);0!==i.length?function(e){w.push(e),U(e,!1)}(i):M.debug("DRM: Received needkey event with initData, but we don't support any of the key systems!")}function $(e,t){if(e.size<=0)return!1;if(t.size>0&&[...e].every((e=>{const n=t.get(e);return void 0!==n&&""!==n})))return!0;const n=v.getSessionTokens();if(n&&n.length>0){const t=n.filter((t=>[...e].includes(t.normalizedKeyId)));if(t.some((e=>!e.hasTriggeredKeyStatusMapUpdate))||0===t.length)return!1}return!T.get().streaming.protection.ignoreKeyStatuses&&e&&e.size>0&&t&&t.size>0}return p={areKeyIdsExpired:function(e){try{return!!$(e,I)&&[...e].every((e=>I.get(e)===t.A.MEDIA_KEY_STATUSES.EXPIRED))}catch(e){return M.error(e),!1}},areKeyIdsUsable:function(e){try{return!$(e,I)||[...e].some((e=>{const n=I.get(e);return n&&n!==t.A.MEDIA_KEY_STATUSES.INTERNAL_ERROR&&n!==t.A.MEDIA_KEY_STATUSES.OUTPUT_RESTRICTED}))}catch(e){return M.error(e),!0}},clearMediaInfoArray:function(){b=[]},closeKeySession:function(e){K(),v.closeKeySession(e)},createKeySession:G,getKeySystems:function(){return h?h.getKeySystems():[]},getSupportedKeySystemMetadataFromContentProtection:function(e){return K(),h.getSupportedKeySystemMetadataFromContentProtection(e,m,L)},handleKeySystemFromManifest:function(){if(!b||0===b.length)return;let e=[];b.forEach((t=>{const n=h.getSupportedKeySystemMetadataFromContentProtection(t.contentProtection,m,L);n.length>0&&(0===e.length&&(e=n),w.push(n))})),e&&e.length>0&&U(e,!0)},initializeForMedia:function(e){if(!e)throw new Error("mediaInfo can not be null or undefined");K(),b.push(e)},loadKeySession:x,removeKeySession:function(e){K(),v.removeKeySession(e)},reset:function(){A.off(y.INTERNAL_KEY_MESSAGE,V,p),K(),j(),B(null),O=null,R=!1,I=new Map,v&&(v.reset(),v=null),P.forEach((e=>clearTimeout(e))),P=[],b=[],w=[]},setKeySystems:function(e){h&&h.setKeySystems(e)},setMediaElement:B,setProtectionData:function(e){m=e,h.setProtectionData(e)},setRobustnessLevel:function(e){N=e},setServerCertificate:function(e){K(),v.setServerCertificate(e)},setSessionType:function(e){L=e},stop:function(){j(),v&&v.stop()},updateKeyStatusesMap:function(e){try{if(!e||!e.sessionToken||!e.parsedKeyStatuses)return;e.sessionToken.hasTriggeredKeyStatusMapUpdate=!0;const n=e.parsedKeyStatuses,r=S.A.parseUserAgent(),i=r&&r.browser&&r.browser.name&&"edge"===r.browser.name.toLowerCase();n.forEach((e=>{i&&O.uuid===t.A.PLAYREADY_UUID&&e.keyId&&16===e.keyId.byteLength&&function(e){const t=S.A.bufferSourceToDataView(e),n=t.getUint32(0,!0),r=t.getUint16(4,!0),i=t.getUint16(6,!0);t.setUint32(0,n,!1),t.setUint16(4,r,!1),t.setUint16(6,i,!1)}(e.keyId);const n=S.A.bufferSourceToHex(e.keyId).slice(0,32);n&&""!==n&&I.set(n,e.status)})),A.trigger(y.KEY_STATUSES_MAP_UPDATED,{keyStatusMap:I})}catch(e){M.error(e)}}},M=f.getLogger(p),w=[],b=[],L="temporary",N="",D=null,C=null,I=new Map,A.on(y.INTERNAL_KEY_MESSAGE,V,p),p}A.__dashjs_factory_name="ProtectionController";var y=f.A.getClassFactory(A),h=class{constructor(e,t){this.keyID=e,this.key=t}},T=class{constructor(e,t){if(t&&"persistent"!==t&&"temporary"!==t)throw new Error("Invalid ClearKey key set type!  Must be one of 'persistent' or 'temporary'");this.keyPairs=e,this.type=t}toJWK(){let e,t=this.keyPairs.length,n={keys:[]};for(e=0;e<t;e++){let t={kty:"oct",alg:"A128KW",kid:this.keyPairs[e].keyID,k:this.keyPairs[e].key};n.keys.push(t)}this.type&&(n.type=this.type);let r=JSON.stringify(n);const i=r.length;let s=new ArrayBuffer(i),o=new Uint8Array(s);for(e=0;e<i;e++)o[e]=r.charCodeAt(e);return s}};const m=t.A.CLEARKEY_UUID,p=t.A.CLEARKEY_KEYSTEM_STRING,I="urn:uuid:"+m;function R(e){let t;const n=(e=e||{}).BASE64;return t={uuid:m,schemeIdURI:I,systemString:p,getInitData:function(e,t){try{let r=o.parseInitDataFromContentProtection(e,n);if(!r&&t){const e={kids:[function(e){try{let t=e.replace(/-/g,"");return t=btoa(t.match(/\w{2}/g).map((e=>String.fromCharCode(parseInt(e,16)))).join("")),t.replace(/=/g,"").replace(/\//g,"_").replace(/\+/g,"-")}catch(e){return null}}(t.cencDefaultKid)]};r=(new TextEncoder).encode(JSON.stringify(e))}return r}catch(e){return null}},getRequestHeadersFromMessage:function(){return{"Content-Type":"application/json"}},getLicenseRequestFromMessage:function(e){return JSON.stringify(JSON.parse(String.fromCharCode.apply(null,new Uint8Array(e))))},getLicenseServerURLFromInitData:function(){return null},getCDMData:function(){return null},getClearKeysFromProtectionData:function(e,t){let n=null;if(e){const r=JSON.parse(String.fromCharCode.apply(null,new Uint8Array(t))),i=[];for(let t=0;t<r.kids.length;t++){const n=r.kids[t],s=e.clearkeys&&e.clearkeys.hasOwnProperty(n)?e.clearkeys[n]:null;if(!s)throw new Error("DRM: ClearKey keyID ("+n+") is not known!");i.push(new h(n,s))}n=new T(i)}return n}},t}R.__dashjs_factory_name="KeySystemClearKey";var C=f.A.getSingletonFactory(R);const D=t.A.W3C_CLEARKEY_UUID,M=t.A.CLEARKEY_KEYSTEM_STRING,b="urn:uuid:"+D;function w(e){let t;const n=e.BASE64,r=e.debug.getLogger(t);return t={uuid:D,schemeIdURI:b,systemString:M,getInitData:function(e){return o.parseInitDataFromContentProtection(e,n)},getRequestHeadersFromMessage:function(){return null},getLicenseRequestFromMessage:function(e){return new Uint8Array(e)},getLicenseServerURLFromInitData:function(){return null},getCDMData:function(){return null},getClearKeysFromProtectionData:function(e,t){let n=null;if(e){const i=JSON.parse(String.fromCharCode.apply(null,new Uint8Array(t))),s=[];for(let t=0;t<i.kids.length;t++){const n=i.kids[t],r=e.clearkeys&&e.clearkeys.hasOwnProperty(n)?e.clearkeys[n]:null;if(!r)throw new Error("DRM: ClearKey keyID ("+n+") is not known!");s.push(new h(n,r))}n=new T(s),r.warn("ClearKey schemeIdURI is using W3C Common PSSH systemID (1077efec-c0b2-4d02-ace3-3c1e52e2fb4b) in Content Protection. See DASH-IF IOP v4.1 section 7.6.2.4")}return n}},t}w.__dashjs_factory_name="KeySystemW3CClearKey";var N=f.A.getSingletonFactory(w);const O=t.A.WIDEVINE_UUID,L=t.A.WIDEVINE_KEYSTEM_STRING,v="urn:uuid:"+O;function P(e){let t;const n=(e=e||{}).BASE64;return t={uuid:O,schemeIdURI:v,systemString:L,getInitData:function(e){return o.parseInitDataFromContentProtection(e,n)},getRequestHeadersFromMessage:function(){return null},getLicenseRequestFromMessage:function(e){return new Uint8Array(e)},getLicenseServerURLFromInitData:function(){return null},getCDMData:function(){return null}},t}P.__dashjs_factory_name="KeySystemWidevine";var K=f.A.getSingletonFactory(P);const U=t.A.PLAYREADY_UUID,k=t.A.PLAYREADY_KEYSTEM_STRING,Y="urn:uuid:"+U;function x(e){let t,n="utf-16";const r=(e=e||{}).BASE64,i=e.settings;function s(){if(!r||!r.hasOwnProperty("decodeArray")||!r.hasOwnProperty("decodeArray"))throw new Error("Missing config parameter(s)")}return t={uuid:U,schemeIdURI:Y,systemString:k,getInitData:function(e){const t=new Uint8Array([112,115,115,104,0,0,0,0]),n=new Uint8Array([154,4,240,121,152,64,66,134,171,146,230,91,224,136,95,149]);let i,a,E,c,l,u=0,d=null;if(s(),!e)return null;if("pssh"in e&&e.pssh)return o.parseInitDataFromContentProtection(e,r);if("pro"in e&&e.pro)d=r.decodeArray(e.pro.__text);else{if(!("prheader"in e)||!e.prheader)return null;d=r.decodeArray(e.prheader.__text)}return i=d.length,a=4+t.length+n.length+4+i,E=new ArrayBuffer(a),c=new Uint8Array(E),l=new DataView(E),l.setUint32(u,a),u+=4,c.set(t,u),u+=t.length,c.set(n,u),u+=n.length,l.setUint32(u,i),u+=4,c.set(d,u),u+=i,c.buffer},getRequestHeadersFromMessage:function(e){let t,r;const s={},o=new DOMParser;if(i&&i.get().streaming.protection.detectPlayreadyMessageFormat&&"utf-16"===n&&e&&e.byteLength%2==1)return s["Content-Type"]="text/xml; charset=utf-8",s;const a="utf-16"===n?new Uint16Array(e):new Uint8Array(e);t=String.fromCharCode.apply(null,a),r=o.parseFromString(t,"application/xml");const E=r.getElementsByTagName("name"),c=r.getElementsByTagName("value");for(let e=0;e<E.length;e++)s[E[e].childNodes[0].nodeValue]=c[e].childNodes[0].nodeValue;return s.hasOwnProperty("Content")&&(s["Content-Type"]=s.Content,delete s.Content),s.hasOwnProperty("Content-Type")||(s["Content-Type"]="text/xml; charset=utf-8"),s},getLicenseRequestFromMessage:function(e){let t=null;const o=new DOMParser;if(i&&i.get().streaming.protection.detectPlayreadyMessageFormat&&"utf-16"===n&&e&&e.byteLength%2==1)return e;const a="utf-16"===n?new Uint16Array(e):new Uint8Array(e);s();const E=String.fromCharCode.apply(null,a),c=o.parseFromString(E,"application/xml");if(!c.getElementsByTagName("PlayReadyKeyMessage")[0])return e;{const e=c.getElementsByTagName("Challenge")[0].childNodes[0].nodeValue;e&&(t=r.decode(e))}return t},getLicenseServerURLFromInitData:function(e){if(e){const t=new DataView(e),n=t.getUint16(4,!0);let r=6;const i=new DOMParser;for(let s=0;s<n;s++){const n=t.getUint16(r,!0);r+=2;const s=t.getUint16(r,!0);if(r+=2,1!==n){r+=s;continue}const o=e.slice(r,r+s),a=String.fromCharCode.apply(null,new Uint16Array(o)),E=i.parseFromString(a,"application/xml");if(E.getElementsByTagName("LA_URL")[0]){const e=E.getElementsByTagName("LA_URL")[0].childNodes[0].nodeValue;if(e)return e}if(E.getElementsByTagName("LUI_URL")[0]){const e=E.getElementsByTagName("LUI_URL")[0].childNodes[0].nodeValue;if(e)return e}}}return null},getCDMData:function(e){let t,n,i,o;if(s(),!e)return null;for(t=[],o=0;o<e.length;++o)t.push(e.charCodeAt(o)),t.push(0);for(t=String.fromCharCode.apply(null,t),t=r.encode(t),n='<PlayReadyCDMData type="LicenseAcquisition"><LicenseAcquisition version="1.0" Proactive="false"><CustomData encoding="base64encoded">%CUSTOMDATA%</CustomData></LicenseAcquisition></PlayReadyCDMData>'.replace("%CUSTOMDATA%",t),i=[],o=0;o<n.length;++o)i.push(n.charCodeAt(o)),i.push(0);return new Uint8Array(i).buffer},setPlayReadyMessageFormat:function(e){if("utf-8"!==e&&"utf-16"!==e)throw new Error('Specified message format is not one of "utf-8" or "utf-16"');n=e}},t}x.__dashjs_factory_name="KeySystemPlayReady";var G=f.A.getSingletonFactory(x);function H(e){const n=(e=e||{}).BASE64,r={};let i;return r[t.A.WIDEVINE_KEYSTEM_STRING]={responseType:"json",getLicenseMessage:function(e){return n.decodeArray(e.license)},getErrorResponse:function(e){return e}},r[t.A.PLAYREADY_KEYSTEM_STRING]={responseType:"arraybuffer",getLicenseMessage:function(e){return e},getErrorResponse:function(e){return String.fromCharCode.apply(null,new Uint8Array(e))}},i={getServerURLFromMessage:function(e){return e},getHTTPMethod:function(){return"POST"},getResponseType:function(e){return r[e].responseType},getLicenseMessage:function(e,t){return function(){if(!n||!n.hasOwnProperty("decodeArray"))throw new Error("Missing config parameter(s)")}(),r[t].getLicenseMessage(e)},getErrorResponse:function(e,t){return r[t].getErrorResponse(e)}},i}H.__dashjs_factory_name="DRMToday";var F=f.A.getSingletonFactory(H);function B(){let e;const t="http://schemas.xmlsoap.org/soap/envelope/";function n(e){const t=String.fromCharCode.apply(null,new Uint8Array(e));return decodeURIComponent(escape(t))}function r(e){if(window.DOMParser){const r=n(e),i=(new window.DOMParser).parseFromString(r,"text/xml"),s=i?i.getElementsByTagNameNS(t,"Envelope")[0]:null,o=s?s.getElementsByTagNameNS(t,"Body")[0]:null;if(o&&o.getElementsByTagNameNS(t,"Fault")[0])return null}return e}function i(e){let r="",i="",s="",o=-1,a=-1;if(window.DOMParser){const E=n(e),c=(new window.DOMParser).parseFromString(E,"text/xml"),l=c?c.getElementsByTagNameNS(t,"Envelope")[0]:null,u=l?l.getElementsByTagNameNS(t,"Body")[0]:null,d=u?u.getElementsByTagNameNS(t,"Fault")[0]:null,_=d?d.getElementsByTagName("detail")[0]:null,S=_?_.getElementsByTagName("Exception")[0]:null;let g=null;if(null===d)return E;g=d.getElementsByTagName("faultstring")[0].firstChild,r=g?g.nodeValue:null,null!==S&&(g=S.getElementsByTagName("StatusCode")[0],i=g?g.firstChild.nodeValue:null,g=S.getElementsByTagName("Message")[0],s=g?g.firstChild.nodeValue:null,o=s?s.lastIndexOf("[")+1:-1,a=s?s.indexOf("]"):-1,s=s?s.substring(o,a):"")}let E=`code: ${i}, name: ${r}`;return s&&(E+=`, message: ${s}`),E}return e={getServerURLFromMessage:function(e){return e},getHTTPMethod:function(){return"POST"},getResponseType:function(){return"arraybuffer"},getLicenseMessage:function(e){return r.call(this,e)},getErrorResponse:function(e){return i.call(this,e)}},e}B.__dashjs_factory_name="PlayReady";var V=f.A.getSingletonFactory(B);function W(){let e;return e={getServerURLFromMessage:function(e){return e},getHTTPMethod:function(){return"POST"},getResponseType:function(){return"arraybuffer"},getLicenseMessage:function(e){return e},getErrorResponse:function(e){return String.fromCharCode.apply(null,new Uint8Array(e))}},e}W.__dashjs_factory_name="Widevine";var q=f.A.getSingletonFactory(W);function j(){let e;return e={getServerURLFromMessage:function(e){return e},getHTTPMethod:function(){return"POST"},getResponseType:function(){return"json"},getLicenseMessage:function(e){if(!e.hasOwnProperty("keys"))return null;let t=[];for(let n=0;n<e.keys.length;n++){let r=e.keys[n],i=r.kid.replace(/=/g,""),s=r.k.replace(/=/g,"");t.push(new h(i,s))}return new T(t)},getErrorResponse:function(e){return String.fromCharCode.apply(null,new Uint8Array(e))}},e}j.__dashjs_factory_name="ClearKey";var z=f.A.getSingletonFactory(j),X=class{constructor(e){this.ks=e.ks,this.keyId=e.keyId,this.initData=e.initData,this.protData=e.protData,this.cdmData=e.cdmData,this.sessionId=e.sessionId,this.sessionType=e.sessionType}};function Q(){let e,n,r,i,s,a,E,c,l=this.context;function u(e,t){return t&&e in t?t[e]:null}function d(e,t){return e&&e.sessionId?e.sessionId:t&&t.sessionId?t.sessionId:null}function _(e,t){return e&&e.sessionType?e.sessionType:t}return e={getKeySystemBySystemString:function(e){for(let t=0;t<i.length;t++)if(i[t].systemString===e)return i[t];return null},getKeySystems:function(){return i},getLicenseServerModelInstance:function(e,n,r){if(r===t.A.MEDIA_KEY_MESSAGE_TYPES.LICENSE_RELEASE||r===t.A.MEDIA_KEY_MESSAGE_TYPES.INDIVIDUALIZATION_REQUEST)return null;let i=null;return n&&n.hasOwnProperty("drmtoday")?i=F(l).getInstance({BASE64:s}):e.systemString===t.A.WIDEVINE_KEYSTEM_STRING?i=q(l).getInstance():e.systemString===t.A.PLAYREADY_KEYSTEM_STRING?i=V(l).getInstance():e.systemString===t.A.CLEARKEY_KEYSTEM_STRING&&(i=z(l).getInstance()),i},getSupportedKeySystemMetadataFromContentProtection:function(e,t,n){let r,s,a,E,c=[];if(!e||!e.length)return c;const l=o.findMp4ProtectionElement(e);for(a=0;a<i.length;a++){s=i[a];const o=u(s.systemString,t);for(E=0;E<e.length;E++)if(r=e[E],r.schemeIdUri.toLowerCase()===s.schemeIdURI){let e=s.getInitData(r,l);const t=new X({ks:i[a],keyId:r.keyId,initData:e,protData:o,cdmData:s.getCDMData(o?o.cdmData:null),sessionId:d(o,r),sessionType:_(o,n)});o?c.unshift(t):c.push(t)}}return c},getSupportedKeySystemMetadataFromSegmentPssh:function(e,t,n){let r,s,a=[],E=o.parsePSSHList(e);for(let e=0;e<i.length;++e){r=i[e],s=r.systemString;const o=u(s,t);r.uuid in E&&a.push({ks:r,initData:E[r.uuid],protData:o,cdmData:r.getCDMData(o?o.cdmData:null),sessionId:d(o),sessionType:_(o,n)})}return a},initDataEquals:function(e,t){if(e.byteLength===t.byteLength){let n=new Uint8Array(e),r=new Uint8Array(t);for(let e=0;e<n.length;e++)if(n[e]!==r[e])return!1;return!0}return!1},initialize:function(){let e;i=[],e=G(l).getInstance({BASE64:s,settings:a}),i.push(e),e=K(l).getInstance({BASE64:s}),i.push(e),e=C(l).getInstance({BASE64:s}),i.push(e),E=e,e=N(l).getInstance({BASE64:s,debug:n}),i.push(e),c=e},isClearKey:function(e){return e===E||e===c},processClearKeyLicenseRequest:function(e,t,n){try{return e.getClearKeysFromProtectionData(t,n)}catch(e){return r.error("Failed to retrieve clearkeys from ProtectionData"),null}},setConfig:function(t){t&&(t.debug&&(n=t.debug,r=n.getLogger(e)),t.BASE64&&(s=t.BASE64),t.settings&&(a=t.settings))},setKeySystems:function(e){i=e},setProtectionData:function(e){for(var t,n,r=0;r<i.length;r++){var s=i[r];s.hasOwnProperty("init")&&s.init((t=s.systemString,n=void 0,n=null,e&&(n=t in e?e[t]:null),n))}}},e}Q.__dashjs_factory_name="ProtectionKeyController";var Z=f.A.getSingletonFactory(Q),$=n(445),J=class{constructor(e,t){this.initData=e,this.initDataType=t}},ee=class{constructor(e,n,r,i){this.sessionToken=e,this.message=n,this.defaultURL=r,this.messageType=i||t.A.MEDIA_KEY_MESSAGE_TYPES.LICENSE_REQUEST}},te=class{constructor(e,t){this.keySystem=e,this.ksConfiguration=t,this.nativeMediaKeySystemAccessObject=null,this.selectedSystemString=null}};const ne={};function re(e){e=e||{};const n=this.context,r=e.eventBus,i=e.events,s=e.debug;let o,a,E,u,d,_,S,g;function f(e,t,n,s){if(void 0===navigator.requestMediaKeySystemAccess||"function"!=typeof navigator.requestMediaKeySystemAccess){const e="Insecure origins are not allowed";return r.trigger(i.KEY_SYSTEM_ACCESS_COMPLETE,{error:e}),void s({error:e})}const o=e[t].protData&&e[t].protData.systemStringPriority?e[t].protData.systemStringPriority:null,a=e[t].configs,E=e[t].ks;let c=E.systemString;(function(e,t){return new Promise(((n,r)=>{A(e,t,0,n,r)}))})(o||(ne[c]?ne[c]:[c]),a).then((e=>{const t=e&&e.nativeMediaKeySystemAccessObject&&"function"==typeof e.nativeMediaKeySystemAccessObject.getConfiguration?e.nativeMediaKeySystemAccessObject.getConfiguration():null,s=new te(E,t);s.selectedSystemString=e.selectedSystemString,s.nativeMediaKeySystemAccessObject=e.nativeMediaKeySystemAccessObject,r.trigger(i.KEY_SYSTEM_ACCESS_COMPLETE,{data:s}),n({data:s})})).catch((o=>{if(t+1<e.length)f(e,t+1,n,s);else{const e="Key system access denied! ";r.trigger(i.KEY_SYSTEM_ACCESS_COMPLETE,{error:e+o.message}),s({error:e+o.message})}}))}function A(e,t,n,r,i){const s=e[n];a.debug(`Requesting key system access for system string ${s}`),navigator.requestMediaKeySystemAccess(s,t).then((e=>{r({nativeMediaKeySystemAccessObject:e,selectedSystemString:s})})).catch((s=>{n+1<e.length?A(e,t,n+1,r,i):i(s)}))}function y(e){if(!e||!e.session)return Promise.resolve;const t=e.session;return t.removeEventListener("keystatuseschange",e),t.removeEventListener("message",e),t.close()}function h(e){for(let t=0;t<_.length;t++)if(_[t]===e){_.splice(t,1);break}}function T(e,n){const s={session:e,keyId:n.keyId,normalizedKeyId:n&&n.keyId&&"string"==typeof n.keyId?n.keyId.replace(/-/g,"").toLowerCase():"",initData:n.initData,sessionId:n.sessionId,sessionType:n.sessionType,hasTriggeredKeyStatusMapUpdate:!1,handleEvent:function(e){switch(e.type){case"keystatuseschange":this._onKeyStatusesChange(e);break;case"message":this._onKeyMessage(e)}},_onKeyStatusesChange:function(e){r.trigger(i.KEY_STATUSES_CHANGED,{data:this});const t=[];e.target.keyStatuses.forEach((function(){t.push(m(arguments))})),r.trigger(i.INTERNAL_KEY_STATUSES_CHANGED,{parsedKeyStatuses:t,sessionToken:s})},_onKeyMessage:function(e){let t=ArrayBuffer.isView(e.message)?e.message.buffer:e.message;r.trigger(i.INTERNAL_KEY_MESSAGE,{data:new ee(this,t,void 0,e.messageType)})},getKeyId:function(){return this.keyId},getSessionId:function(){return e.sessionId},getSessionType:function(){return this.sessionType},getExpirationTime:function(){return e.expiration},getKeyStatuses:function(){return e.keyStatuses},getUsable:function(){let n=!1;return e.keyStatuses.forEach((function(){m(arguments).status===t.A.MEDIA_KEY_STATUSES.USABLE&&(n=!0)})),n}};return e.addEventListener("keystatuseschange",s),e.addEventListener("message",s),e.closed.then((()=>{h(s),a.debug("DRM: Session closed.  SessionID = "+s.getSessionId()),r.trigger(i.KEY_SESSION_CLOSED,{data:s.getSessionId()})})),_.push(s),s}function m(e){let t,n;return e&&e.length>0&&(e[0]&&("string"==typeof e[0]?t=e[0]:n=e[0]),e[1]&&("string"==typeof e[1]?t=e[1]:n=e[1])),{status:t,keyId:n}}return o={closeKeySession:function(e){y(e).catch((function(t){h(e),r.trigger(i.KEY_SESSION_CLOSED,{data:null,error:"Error closing session ("+e.getSessionId()+") "+t.name})}))},createKeySession:function(e){if(!E||!d)throw new Error("Can not create sessions until you have selected a key system");const n=d.createSession(e.sessionType),s=T(n,e),o=E.systemString===t.A.CLEARKEY_KEYSTEM_STRING&&(e.initData||e.protData&&e.protData.clearkeys)?t.A.INITIALIZATION_DATA_TYPE_KEYIDS:t.A.INITIALIZATION_DATA_TYPE_CENC;n.generateRequest(o,e.initData).then((function(){a.debug("DRM: Session created.  SessionID = "+s.getSessionId()),r.trigger(i.KEY_SESSION_CREATED,{data:s})})).catch((function(e){h(s),r.trigger(i.KEY_SESSION_CREATED,{data:null,error:new l.A(c.A.KEY_SESSION_CREATED_ERROR_CODE,c.A.KEY_SESSION_CREATED_ERROR_MESSAGE+"Error generating key request -- "+e.name)})}))},getAllInitData:function(){const e=[];for(let t=0;t<_.length;t++)_[t].initData&&e.push(_[t].initData);return e},getSessionTokens:function(){return _},loadKeySession:function(e){if(!E||!d)throw new Error("Can not load sessions until you have selected a key system");const t=e.sessionId;for(let e=0;e<_.length;e++)if(t===_[e].sessionId)return void a.warn("DRM: Ignoring session ID because we have already seen it!");const n=d.createSession(e.sessionType),s=T(n,e);s.hasTriggeredKeyStatusMapUpdate=!0,n.load(t).then((function(e){e?(a.debug("DRM: Session loaded.  SessionID = "+s.getSessionId()),r.trigger(i.KEY_SESSION_CREATED,{data:s})):(h(s),r.trigger(i.KEY_SESSION_CREATED,{data:null,error:new l.A(c.A.KEY_SESSION_CREATED_ERROR_CODE,c.A.KEY_SESSION_CREATED_ERROR_MESSAGE+"Could not load session! Invalid Session ID ("+t+")")}))})).catch((function(e){h(s),r.trigger(i.KEY_SESSION_CREATED,{data:null,error:new l.A(c.A.KEY_SESSION_CREATED_ERROR_CODE,c.A.KEY_SESSION_CREATED_ERROR_MESSAGE+"Could not load session ("+t+")! "+e.name)})}))},removeKeySession:function(e){e.session.remove().then((function(){a.debug("DRM: Session removed.  SessionID = "+e.getSessionId()),r.trigger(i.KEY_SESSION_REMOVED,{data:e.getSessionId()})}),(function(t){r.trigger(i.KEY_SESSION_REMOVED,{data:null,error:"Error removing session ("+e.getSessionId()+"). "+t.name})}))},requestKeySystemAccess:function(e){return new Promise(((t,n)=>{f(e,0,t,n)}))},reset:function(){const e=_.length;let t;if(0!==e){const s=function(e){h(e),0===_.length&&(u?(u.removeEventListener("encrypted",S),u.setMediaKeys(null).then((function(){r.trigger(i.TEARDOWN_COMPLETE)}))):r.trigger(i.TEARDOWN_COMPLETE))};for(let r=0;r<e;r++)t=_[r],n=t,y(t),s(n)}else r.trigger(i.TEARDOWN_COMPLETE);var n},selectKeySystem:function(e){return new Promise(((t,n)=>{e.nativeMediaKeySystemAccessObject.createMediaKeys().then((t=>(E=e.keySystem,d=t,u?u.setMediaKeys(d):Promise.resolve()))).then((()=>{t(E)})).catch((function(){n({error:"Error selecting keys system ("+e.keySystem.systemString+")! Could not create MediaKeys -- TODO"})}))}))},setMediaElement:function(e){u!==e&&(u&&(u.removeEventListener("encrypted",S),u.setMediaKeys&&u.setMediaKeys(null)),u=e,u&&(u.addEventListener("encrypted",S),u.setMediaKeys&&d&&u.setMediaKeys(d)))},setServerCertificate:function(e){return new Promise(((t,n)=>{d.setServerCertificate(e).then((function(){a.info("DRM: License server certificate successfully updated."),r.trigger(i.SERVER_CERTIFICATE_UPDATED),t()})).catch((e=>{n(e),r.trigger(i.SERVER_CERTIFICATE_UPDATED,{error:new l.A(c.A.SERVER_CERTIFICATE_UPDATED_ERROR_CODE,c.A.SERVER_CERTIFICATE_UPDATED_ERROR_MESSAGE+e.name)})}))}))},stop:function(){let e;for(let t=0;t<_.length;t++)e=_[t],e.getUsable()||(y(e),h(e))},updateKeySession:function(e,t){const n=e.session;g.isClearKey(E)&&(t=t.toJWK()),n.update(t).then((()=>{r.trigger(i.KEY_SESSION_UPDATED)})).catch((function(t){r.trigger(i.KEY_ERROR,{error:new l.A(c.A.MEDIA_KEYERR_CODE,"Error sending update() message! "+t.name,e)})}))}},a=s.getLogger(o),E=null,u=null,d=null,_=[],g=Z(n).getInstance(),S={handleEvent:function(e){if("encrypted"===e.type&&e.initData){let t=ArrayBuffer.isView(e.initData)?e.initData.buffer:e.initData;r.trigger(i.NEED_KEY,{key:new J(t,e.initDataType)})}}},o}ne[t.A.PLAYREADY_KEYSTEM_STRING]=[t.A.PLAYREADY_KEYSTEM_STRING,t.A.PLAYREADY_RECOMMENDATION_KEYSTEM_STRING],ne[t.A.WIDEVINE_KEYSTEM_STRING]=[t.A.WIDEVINE_KEYSTEM_STRING],ne[t.A.CLEARKEY_KEYSTEM_STRING]=[t.A.CLEARKEY_KEYSTEM_STRING],re.__dashjs_factory_name="DefaultProtectionModel";var ie=f.A.getClassFactory(re);function se(e){e=e||{};const n=this.context,r=e.eventBus,i=e.events,s=e.debug,o=e.api;let a,u,d,_,S,g,f,A,y;function h(){try{for(let e=0;e<f.length;e++)T(f[e]);d&&d.removeEventListener(o.needkey,A),r.trigger(i.TEARDOWN_COMPLETE)}catch(e){r.trigger(i.TEARDOWN_COMPLETE,{error:"Error tearing down key sessions and MediaKeys! -- "+e.message})}}function T(e){const t=e.session;t.removeEventListener(o.error,e),t.removeEventListener(o.message,e),t.removeEventListener(o.ready,e),t.removeEventListener(o.close,e);for(let t=0;t<f.length;t++)if(f[t]===e){f.splice(t,1);break}t[o.release]()}function m(){let e=null;const t=function(){d.removeEventListener("loadedmetadata",e),d[o.setMediaKeys](S),r.trigger(i.VIDEO_ELEMENT_SELECTED)};d.readyState>=1?t():(e=t.bind(this),d.addEventListener("loadedmetadata",e))}return a={getAllInitData:function(){const e=[];for(let t=0;t<f.length;t++)e.push(f[t].initData);return e},getSessionTokens:function(){return f},requestKeySystemAccess:function(e){return new Promise(((t,n)=>{let s=!1;for(let n=0;n<e.length;n++){const a=e[n].ks.systemString,c=e[n].configs;let l=null,u=null;for(let e=0;e<c.length;e++){const n=c[e].audioCapabilities,d=c[e].videoCapabilities;if(n&&0!==n.length){l=[];for(let e=0;e<n.length;e++)window[o.MediaKeys].isTypeSupported(a,n[e].contentType)&&l.push(n[e])}if(d&&0!==d.length){u=[];for(let e=0;e<d.length;e++)window[o.MediaKeys].isTypeSupported(a,d[e].contentType)&&u.push(d[e])}if(!l&&!u||l&&0===l.length||u&&0===u.length)continue;s=!0;const _=new E(l,u),S=y.getKeySystemBySystemString(a),g=new te(S,_);r.trigger(i.KEY_SYSTEM_ACCESS_COMPLETE,{data:g}),t({data:g});break}}if(!s){const e="Key system access denied! -- No valid audio/video content configurations detected!";r.trigger(i.KEY_SYSTEM_ACCESS_COMPLETE,{error:e}),n({error:e})}}))},selectKeySystem:function(e){return new Promise(((t,n)=>{try{S=e.mediaKeys=new window[o.MediaKeys](e.keySystem.systemString),_=e.keySystem,g=e,d&&m(),t(_)}catch(e){n({error:"Error selecting keys system ("+_.systemString+")! Could not create MediaKeys -- TODO"})}}))},setMediaElement:function(e){d!==e&&(d&&d.removeEventListener(o.needkey,A),d=e,d&&(d.addEventListener(o.needkey,A),S&&m()))},createKeySession:function(e){if(!_||!S||!g)throw new Error("Can not create sessions until you have selected a key system");let t=null;if(g.ksConfiguration.videoCapabilities&&g.ksConfiguration.videoCapabilities.length>0&&(t=g.ksConfiguration.videoCapabilities[0]),null===t&&g.ksConfiguration.audioCapabilities&&g.ksConfiguration.audioCapabilities.length>0&&(t=g.ksConfiguration.audioCapabilities[0]),null===t)throw new Error("Can not create sessions for unknown content types.");const n=t.contentType,s=S.createSession(n,new Uint8Array(e.initData),e.cdmData?new Uint8Array(e.cdmData):null),a=function(e,t){return{session:e,keyId:t.keyId,normalizedKeyId:t&&t.keyId&&"string"==typeof t.keyId?t.keyId.replace(/-/g,"").toLowerCase():"",initData:t.initData,hasTriggeredKeyStatusMapUpdate:!1,getKeyId:function(){return this.keyId},getSessionId:function(){return this.session.sessionId},getExpirationTime:function(){return NaN},getSessionType:function(){return"temporary"},getKeyStatuses:function(){return{size:0,has:()=>!1,get:()=>{}}},handleEvent:function(e){switch(e.type){case o.error:let t="KeyError";r.trigger(i.KEY_ERROR,{error:new l.A(c.A.MEDIA_KEYERR_CODE,t,this)});break;case o.message:let n=ArrayBuffer.isView(e.message)?e.message.buffer:e.message;r.trigger(i.INTERNAL_KEY_MESSAGE,{data:new ee(this,n,e.destinationURL)});break;case o.ready:u.debug("DRM: Key added."),r.trigger(i.KEY_ADDED);break;case o.close:u.debug("DRM: Session closed.  SessionID = "+this.getSessionId()),r.trigger(i.KEY_SESSION_CLOSED,{data:this.getSessionId()})}}}}(s,e);s.addEventListener(o.error,a),s.addEventListener(o.message,a),s.addEventListener(o.ready,a),s.addEventListener(o.close,a),f.push(a),u.debug("DRM: Session created.  SessionID = "+a.getSessionId()),r.trigger(i.KEY_SESSION_CREATED,{data:a})},updateKeySession:function(e,t){const n=e.session;y.isClearKey(_)?n.update(new Uint8Array(t.toJWK())):n.update(new Uint8Array(t)),r.trigger(i.KEY_SESSION_UPDATED)},closeKeySession:T,setServerCertificate:function(){},loadKeySession:function(){},removeKeySession:function(){},stop:h,reset:h},u=s.getLogger(a),d=null,_=null,S=null,g=null,f=[],y=Z(n).getInstance(),A={handleEvent:function(e){if(e.type===o.needkey&&e.initData){const n=ArrayBuffer.isView(e.initData)?e.initData.buffer:e.initData;r.trigger(i.NEED_KEY,{key:new J(n,t.A.INITIALIZATION_DATA_TYPE_CENC)})}}},a}se.__dashjs_factory_name="ProtectionModel_3Feb2014";var oe=f.A.getClassFactory(se);function ae(e){e=e||{};const n=this.context,r=e.eventBus,i=e.events,s=e.debug,o=e.api,a=e.errHandler;let u,d,_,S,g,f,A,y,h;function T(){_&&I();for(let e=0;e<A.length;e++)m(A[e]);r.trigger(i.TEARDOWN_COMPLETE)}function m(e){try{_[o.cancelKeyRequest](S.systemString,e.sessionId)}catch(t){r.trigger(i.KEY_SESSION_CLOSED,{data:null,error:"Error closing session ("+e.sessionId+") "+t.message})}}function p(e,t){if(t&&e){const n=e.length;for(let r=0;r<n;r++)if(e[r].sessionId==t)return e[r];return null}return null}function I(){_.removeEventListener(o.keyerror,h),_.removeEventListener(o.needkey,h),_.removeEventListener(o.keymessage,h),_.removeEventListener(o.keyadded,h)}return u={getAllInitData:function(){const e=[];for(let t=0;t<f.length;t++)e.push(f[t].initData);for(let t=0;t<A.length;t++)e.push(A[t].initData);return e},getSessionTokens:function(){return A.concat(f)},requestKeySystemAccess:function(e){return new Promise(((t,n)=>{let s=_;s||(s=document.createElement("video"));let o=!1;for(let n=0;n<e.length;n++){const a=e[n].ks.systemString,c=e[n].configs;let l=null,u=null;for(let e=0;e<c.length;e++){const n=c[e].videoCapabilities;if(n&&0!==n.length){u=[];for(let e=0;e<n.length;e++)""!==s.canPlayType(n[e].contentType,a)&&u.push(n[e])}if(!l&&!u||l&&0===l.length||u&&0===u.length)continue;o=!0;const d=new E(l,u),_=g.getKeySystemBySystemString(a),S=new te(_,d);r.trigger(i.KEY_SYSTEM_ACCESS_COMPLETE,{data:S}),t({data:S});break}}if(!o){const e="Key system access denied! -- No valid audio/video content configurations detected!";r.trigger(i.KEY_SYSTEM_ACCESS_COMPLETE,{error:e}),n({error:e})}}))},selectKeySystem:function(e){return S=e.keySystem,Promise.resolve(S)},setMediaElement:function(e){if(_!==e){if(_){I();for(var t=0;t<A.length;t++)m(A[t]);A=[]}_=e,_&&(_.addEventListener(o.keyerror,h),_.addEventListener(o.needkey,h),_.addEventListener(o.keymessage,h),_.addEventListener(o.keyadded,h),r.trigger(i.VIDEO_ELEMENT_SELECTED))}},createKeySession:function(e){if(!S)throw new Error("Can not create sessions until you have selected a key system");if(y||0===A.length){const t={sessionId:null,keyId:e.keyId,normalizedKeyId:e&&e.keyId&&"string"==typeof e.keyId?e.keyId.replace(/-/g,"").toLowerCase():"",initData:e.initData,hasTriggeredKeyStatusMapUpdate:!1,getKeyId:function(){return this.keyId},getSessionId:function(){return this.sessionId},getExpirationTime:function(){return NaN},getSessionType:function(){return"temporary"},getKeyStatuses:function(){return{size:0,has:()=>!1,get:()=>{}}}};return f.push(t),_[o.generateKeyRequest](S.systemString,new Uint8Array(e.initData)),t}throw new Error("Multiple sessions not allowed!")},updateKeySession:function(e,t){const n=e.sessionId;if(g.isClearKey(S))for(let e=0;e<t.keyPairs.length;e++)_[o.addKey](S.systemString,t.keyPairs[e].key,t.keyPairs[e].keyID,n);else _[o.addKey](S.systemString,new Uint8Array(t),new Uint8Array(e.initData),n);r.trigger(i.KEY_SESSION_UPDATED)},closeKeySession:m,setServerCertificate:function(){},loadKeySession:function(){},removeKeySession:function(){},stop:T,reset:T},d=s.getLogger(u),_=null,S=null,f=[],A=[],g=Z(n).getInstance(),h={handleEvent:function(e){let n=null;switch(e.type){case o.needkey:let s=ArrayBuffer.isView(e.initData)?e.initData.buffer:e.initData;r.trigger(i.NEED_KEY,{key:new J(s,t.A.INITIALIZATION_DATA_TYPE_CENC)});break;case o.keyerror:if(n=p(A,e.sessionId),n||(n=p(f,e.sessionId)),n){let t=c.A.MEDIA_KEYERR_CODE,s="";switch(e.errorCode.code){case 1:t=c.A.MEDIA_KEYERR_UNKNOWN_CODE,s+="MEDIA_KEYERR_UNKNOWN - "+c.A.MEDIA_KEYERR_UNKNOWN_MESSAGE;break;case 2:t=c.A.MEDIA_KEYERR_CLIENT_CODE,s+="MEDIA_KEYERR_CLIENT - "+c.A.MEDIA_KEYERR_CLIENT_MESSAGE;break;case 3:t=c.A.MEDIA_KEYERR_SERVICE_CODE,s+="MEDIA_KEYERR_SERVICE - "+c.A.MEDIA_KEYERR_SERVICE_MESSAGE;break;case 4:t=c.A.MEDIA_KEYERR_OUTPUT_CODE,s+="MEDIA_KEYERR_OUTPUT - "+c.A.MEDIA_KEYERR_OUTPUT_MESSAGE;break;case 5:t=c.A.MEDIA_KEYERR_HARDWARECHANGE_CODE,s+="MEDIA_KEYERR_HARDWARECHANGE - "+c.A.MEDIA_KEYERR_HARDWARECHANGE_MESSAGE;break;case 6:t=c.A.MEDIA_KEYERR_DOMAIN_CODE,s+="MEDIA_KEYERR_DOMAIN - "+c.A.MEDIA_KEYERR_DOMAIN_MESSAGE}s+="  System Code = "+e.systemCode,r.trigger(i.KEY_ERROR,{error:new l.A(t,s,n)})}else d.error("No session token found for key error");break;case o.keyadded:n=p(A,e.sessionId),n||(n=p(f,e.sessionId)),n?(d.debug("DRM: Key added."),r.trigger(i.KEY_ADDED,{data:n})):d.debug("No session token found for key added");break;case o.keymessage:if(y=null!==e.sessionId&&void 0!==e.sessionId,y?(n=p(A,e.sessionId),!n&&f.length>0&&(n=f.shift(),A.push(n),n.sessionId=e.sessionId,r.trigger(i.KEY_SESSION_CREATED,{data:n}))):f.length>0&&(n=f.shift(),A.push(n),0!==f.length&&a.error(new l.A(c.A.MEDIA_KEY_MESSAGE_ERROR_CODE,c.A.MEDIA_KEY_MESSAGE_ERROR_MESSAGE))),n){let t=ArrayBuffer.isView(e.message)?e.message.buffer:e.message;n.keyMessage=t,r.trigger(i.INTERNAL_KEY_MESSAGE,{data:new ee(n,t,e.defaultURL)})}else d.warn("No session token found for key message")}}},u}ae.__dashjs_factory_name="ProtectionModel_01b";var Ee=f.A.getClassFactory(ae);const ce=[{generateKeyRequest:"generateKeyRequest",addKey:"addKey",cancelKeyRequest:"cancelKeyRequest",needkey:"needkey",keyerror:"keyerror",keyadded:"keyadded",keymessage:"keymessage"},{generateKeyRequest:"webkitGenerateKeyRequest",addKey:"webkitAddKey",cancelKeyRequest:"webkitCancelKeyRequest",needkey:"webkitneedkey",keyerror:"webkitkeyerror",keyadded:"webkitkeyadded",keymessage:"webkitkeymessage"}],le=[{setMediaKeys:"setMediaKeys",MediaKeys:"MediaKeys",release:"close",needkey:"needkey",error:"keyerror",message:"keymessage",ready:"keyadded",close:"keyclose"},{setMediaKeys:"msSetMediaKeys",MediaKeys:"MSMediaKeys",release:"close",needkey:"msneedkey",error:"mskeyerror",message:"mskeymessage",ready:"mskeyadded",close:"mskeyclose"}];function ue(){let e;const t=this.context;function n(e,t){for(let n=0;n<t.length;n++){const r=t[n];if("function"==typeof e[r[Object.keys(r)[0]]])return r}return null}return e={createProtectionSystem:function(r){let i=null;const s=Z(t).getInstance();s.setConfig({debug:r.debug,BASE64:r.BASE64,settings:r.settings}),s.initialize();let o=function(r){const i=r.debug,s=i.getLogger(e),o=r.eventBus,a=r.errHandler,E=r.videoModel?r.videoModel.getElement():null;return E&&void 0===E.onencrypted||E&&void 0===E.mediaKeys?n(E,le)?(s.info("EME detected on this user agent! (ProtectionModel_3Feb2014)"),oe(t).create({debug:i,eventBus:o,events:r.events,api:n(E,le)})):n(E,ce)?(s.info("EME detected on this user agent! (ProtectionModel_01b)"),Ee(t).create({debug:i,eventBus:o,errHandler:a,events:r.events,api:n(E,ce)})):(s.warn("No supported version of EME detected on this user agent! - Attempts to play encrypted content will fail!"),null):(s.info("EME detected on this user agent! (DefaultProtectionModel"),ie(t).create({debug:i,eventBus:o,events:r.events}))}(r);return o&&(i=y(t).create({BASE64:r.BASE64,cmcdModel:r.cmcdModel,constants:r.constants,customParametersModel:r.customParametersModel,debug:r.debug,eventBus:r.eventBus,events:r.events,protectionKeyController:s,protectionModel:o,settings:r.settings}),r.capabilities.setEncryptedMediaSupported(!0)),i}},e}ue.__dashjs_factory_name="Protection";const de=dashjs.FactoryMaker.getClassFactory(ue);de.events=$.A,de.errors=c.A,dashjs.FactoryMaker.updateClassFactory(ue.__dashjs_factory_name,de);var _e=de}(),r.default}()}));
//# sourceMappingURL=dash.protection.min.js.map