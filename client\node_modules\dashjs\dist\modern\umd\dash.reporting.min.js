!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.dashjs=t():e.dashjs=t()}(self,(function(){return function(){var e={3282:function(e){"use strict";function t(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function i(e,t){for(var i,n="",r=0,a=-1,o=0,s=0;s<=e.length;++s){if(s<e.length)i=e.charCodeAt(s);else{if(47===i)break;i=47}if(47===i){if(a===s-1||1===o);else if(a!==s-1&&2===o){if(n.length<2||2!==r||46!==n.charCodeAt(n.length-1)||46!==n.charCodeAt(n.length-2))if(n.length>2){var c=n.lastIndexOf("/");if(c!==n.length-1){-1===c?(n="",r=0):r=(n=n.slice(0,c)).length-1-n.lastIndexOf("/"),a=s,o=0;continue}}else if(2===n.length||1===n.length){n="",r=0,a=s,o=0;continue}t&&(n.length>0?n+="/..":n="..",r=2)}else n.length>0?n+="/"+e.slice(a+1,s):n=e.slice(a+1,s),r=s-a-1;a=s,o=0}else 46===i&&-1!==o?++o:o=-1}return n}var n={resolve:function(){for(var e,n="",r=!1,a=arguments.length-1;a>=-1&&!r;a--){var o;a>=0?o=arguments[a]:(void 0===e&&(e=process.cwd()),o=e),t(o),0!==o.length&&(n=o+"/"+n,r=47===o.charCodeAt(0))}return n=i(n,!r),r?n.length>0?"/"+n:"/":n.length>0?n:"."},normalize:function(e){if(t(e),0===e.length)return".";var n=47===e.charCodeAt(0),r=47===e.charCodeAt(e.length-1);return 0!==(e=i(e,!n)).length||n||(e="."),e.length>0&&r&&(e+="/"),n?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,i=0;i<arguments.length;++i){var r=arguments[i];t(r),r.length>0&&(void 0===e?e=r:e+="/"+r)}return void 0===e?".":n.normalize(e)},relative:function(e,i){if(t(e),t(i),e===i)return"";if((e=n.resolve(e))===(i=n.resolve(i)))return"";for(var r=1;r<e.length&&47===e.charCodeAt(r);++r);for(var a=e.length,o=a-r,s=1;s<i.length&&47===i.charCodeAt(s);++s);for(var c=i.length-s,l=o<c?o:c,u=-1,E=0;E<=l;++E){if(E===l){if(c>l){if(47===i.charCodeAt(s+E))return i.slice(s+E+1);if(0===E)return i.slice(s+E)}else o>l&&(47===e.charCodeAt(r+E)?u=E:0===E&&(u=0));break}var d=e.charCodeAt(r+E);if(d!==i.charCodeAt(s+E))break;47===d&&(u=E)}var _="";for(E=r+u+1;E<=a;++E)E!==a&&47!==e.charCodeAt(E)||(0===_.length?_+="..":_+="/..");return _.length>0?_+i.slice(s+u):(s+=u,47===i.charCodeAt(s)&&++s,i.slice(s))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var i=e.charCodeAt(0),n=47===i,r=-1,a=!0,o=e.length-1;o>=1;--o)if(47===(i=e.charCodeAt(o))){if(!a){r=o;break}}else a=!1;return-1===r?n?"/":".":n&&1===r?"//":e.slice(0,r)},basename:function(e,i){if(void 0!==i&&"string"!=typeof i)throw new TypeError('"ext" argument must be a string');t(e);var n,r=0,a=-1,o=!0;if(void 0!==i&&i.length>0&&i.length<=e.length){if(i.length===e.length&&i===e)return"";var s=i.length-1,c=-1;for(n=e.length-1;n>=0;--n){var l=e.charCodeAt(n);if(47===l){if(!o){r=n+1;break}}else-1===c&&(o=!1,c=n+1),s>=0&&(l===i.charCodeAt(s)?-1==--s&&(a=n):(s=-1,a=c))}return r===a?a=c:-1===a&&(a=e.length),e.slice(r,a)}for(n=e.length-1;n>=0;--n)if(47===e.charCodeAt(n)){if(!o){r=n+1;break}}else-1===a&&(o=!1,a=n+1);return-1===a?"":e.slice(r,a)},extname:function(e){t(e);for(var i=-1,n=0,r=-1,a=!0,o=0,s=e.length-1;s>=0;--s){var c=e.charCodeAt(s);if(47!==c)-1===r&&(a=!1,r=s+1),46===c?-1===i?i=s:1!==o&&(o=1):-1!==i&&(o=-1);else if(!a){n=s+1;break}}return-1===i||-1===r||0===o||1===o&&i===r-1&&i===n+1?"":e.slice(i,r)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var i=t.dir||t.root,n=t.base||(t.name||"")+(t.ext||"");return i?i===t.root?i+n:i+"/"+n:n}(0,e)},parse:function(e){t(e);var i={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return i;var n,r=e.charCodeAt(0),a=47===r;a?(i.root="/",n=1):n=0;for(var o=-1,s=0,c=-1,l=!0,u=e.length-1,E=0;u>=n;--u)if(47!==(r=e.charCodeAt(u)))-1===c&&(l=!1,c=u+1),46===r?-1===o?o=u:1!==E&&(E=1):-1!==o&&(E=-1);else if(!l){s=u+1;break}return-1===o||-1===c||0===E||1===E&&o===c-1&&o===s+1?-1!==c&&(i.base=i.name=0===s&&a?e.slice(1,c):e.slice(s,c)):(0===s&&a?(i.name=e.slice(1,o),i.base=e.slice(1,c)):(i.name=e.slice(s,o),i.base=e.slice(s,c)),i.ext=e.slice(o,c)),s>0?i.dir=e.slice(0,s-1):a&&(i.dir="/"),i},sep:"/",delimiter:":",win32:null,posix:null};n.posix=n,e.exports=n},8571:function(e,t,i){var n;!function(r,a){"use strict";var o="function",s="undefined",c="object",l="string",u="major",E="model",d="name",_="type",A="vendor",h="version",T="architecture",m="console",f="mobile",g="tablet",p="smarttv",R="wearable",I="embedded",C="Amazon",b="Apple",S="ASUS",D="BlackBerry",w="Browser",N="Chrome",y="Firefox",L="Google",M="Huawei",v="LG",O="Microsoft",P="Motorola",U="Opera",F="Samsung",x="Sharp",B="Sony",G="Xiaomi",k="Zebra",H="Facebook",V="Chromium OS",Y="Mac OS",j=function(e){for(var t={},i=0;i<e.length;i++)t[e[i].toUpperCase()]=e[i];return t},K=function(e,t){return typeof e===l&&-1!==W(t).indexOf(W(e))},W=function(e){return e.toLowerCase()},z=function(e,t){if(typeof e===l)return e=e.replace(/^\s\s*/,""),typeof t===s?e:e.substring(0,500)},q=function(e,t){for(var i,n,r,s,l,u,E=0;E<t.length&&!l;){var d=t[E],_=t[E+1];for(i=n=0;i<d.length&&!l&&d[i];)if(l=d[i++].exec(e))for(r=0;r<_.length;r++)u=l[++n],typeof(s=_[r])===c&&s.length>0?2===s.length?typeof s[1]==o?this[s[0]]=s[1].call(this,u):this[s[0]]=s[1]:3===s.length?typeof s[1]!==o||s[1].exec&&s[1].test?this[s[0]]=u?u.replace(s[1],s[2]):a:this[s[0]]=u?s[1].call(this,u,s[2]):a:4===s.length&&(this[s[0]]=u?s[3].call(this,u.replace(s[1],s[2])):a):this[s]=u||a;E+=2}},X=function(e,t){for(var i in t)if(typeof t[i]===c&&t[i].length>0){for(var n=0;n<t[i].length;n++)if(K(t[i][n],e))return"?"===i?a:i}else if(K(t[i],e))return"?"===i?a:i;return e},Q={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},$={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[h,[d,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[h,[d,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[d,h],[/opios[\/ ]+([\w\.]+)/i],[h,[d,U+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[h,[d,U+" GX"]],[/\bopr\/([\w\.]+)/i],[h,[d,U]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[h,[d,"Baidu"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim)\s?(?:browser)?[\/ ]?([\w\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[d,h],[/\bddg\/([\w\.]+)/i],[h,[d,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[h,[d,"UC"+w]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[h,[d,"WeChat"]],[/konqueror\/([\w\.]+)/i],[h,[d,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[h,[d,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[h,[d,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[h,[d,"Smart Lenovo "+w]],[/(avast|avg)\/([\w\.]+)/i],[[d,/(.+)/,"$1 Secure "+w],h],[/\bfocus\/([\w\.]+)/i],[h,[d,y+" Focus"]],[/\bopt\/([\w\.]+)/i],[h,[d,U+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[h,[d,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[h,[d,"Dolphin"]],[/coast\/([\w\.]+)/i],[h,[d,U+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[h,[d,"MIUI "+w]],[/fxios\/([-\w\.]+)/i],[h,[d,y]],[/\bqihu|(qi?ho?o?|360)browser/i],[[d,"360 "+w]],[/(oculus|sailfish|huawei|vivo)browser\/([\w\.]+)/i],[[d,/(.+)/,"$1 "+w],h],[/samsungbrowser\/([\w\.]+)/i],[h,[d,F+" Internet"]],[/(comodo_dragon)\/([\w\.]+)/i],[[d,/_/g," "],h],[/metasr[\/ ]?([\d\.]+)/i],[h,[d,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[d,"Sogou Mobile"],h],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345Explorer)[\/ ]?([\w\.]+)/i],[d,h],[/(lbbrowser)/i,/\[(linkedin)app\]/i],[d],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[d,H],h],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[d,h],[/\bgsa\/([\w\.]+) .*safari\//i],[h,[d,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[h,[d,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[h,[d,N+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[d,N+" WebView"],h],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[h,[d,"Android "+w]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[d,h],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[h,[d,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[h,d],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[d,[h,X,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[d,h],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[d,"Netscape"],h],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[h,[d,y+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[d,h],[/(cobalt)\/([\w\.]+)/i],[d,[h,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[T,"amd64"]],[/(ia32(?=;))/i],[[T,W]],[/((?:i[346]|x)86)[;\)]/i],[[T,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[T,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[T,"armhf"]],[/windows (ce|mobile); ppc;/i],[[T,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[T,/ower/,"",W]],[/(sun4\w)[;\)]/i],[[T,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[T,W]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[E,[A,F],[_,g]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[E,[A,F],[_,f]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[E,[A,b],[_,f]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[E,[A,b],[_,g]],[/(macintosh);/i],[E,[A,b]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[E,[A,x],[_,f]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[E,[A,M],[_,g]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[E,[A,M],[_,f]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[E,/_/g," "],[A,G],[_,f]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[E,/_/g," "],[A,G],[_,g]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[E,[A,"OPPO"],[_,f]],[/\b(opd2\d{3}a?) bui/i],[E,[A,"OPPO"],[_,g]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[E,[A,"Vivo"],[_,f]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[E,[A,"Realme"],[_,f]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[E,[A,P],[_,f]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[E,[A,P],[_,g]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[E,[A,v],[_,g]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[E,[A,v],[_,f]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[E,[A,"Lenovo"],[_,g]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[E,/_/g," "],[A,"Nokia"],[_,f]],[/(pixel c)\b/i],[E,[A,L],[_,g]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[E,[A,L],[_,f]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[E,[A,B],[_,f]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[E,"Xperia Tablet"],[A,B],[_,g]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[E,[A,"OnePlus"],[_,f]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[E,[A,C],[_,g]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[E,/(.+)/g,"Fire Phone $1"],[A,C],[_,f]],[/(playbook);[-\w\),; ]+(rim)/i],[E,A,[_,g]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[E,[A,D],[_,f]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[E,[A,S],[_,g]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[E,[A,S],[_,f]],[/(nexus 9)/i],[E,[A,"HTC"],[_,g]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[A,[E,/_/g," "],[_,f]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[E,[A,"Acer"],[_,g]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[E,[A,"Meizu"],[_,f]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[E,[A,"Ulefone"],[_,f]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[A,E,[_,f]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[A,E,[_,g]],[/(surface duo)/i],[E,[A,O],[_,g]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[E,[A,"Fairphone"],[_,f]],[/(u304aa)/i],[E,[A,"AT&T"],[_,f]],[/\bsie-(\w*)/i],[E,[A,"Siemens"],[_,f]],[/\b(rct\w+) b/i],[E,[A,"RCA"],[_,g]],[/\b(venue[\d ]{2,7}) b/i],[E,[A,"Dell"],[_,g]],[/\b(q(?:mv|ta)\w+) b/i],[E,[A,"Verizon"],[_,g]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[E,[A,"Barnes & Noble"],[_,g]],[/\b(tm\d{3}\w+) b/i],[E,[A,"NuVision"],[_,g]],[/\b(k88) b/i],[E,[A,"ZTE"],[_,g]],[/\b(nx\d{3}j) b/i],[E,[A,"ZTE"],[_,f]],[/\b(gen\d{3}) b.+49h/i],[E,[A,"Swiss"],[_,f]],[/\b(zur\d{3}) b/i],[E,[A,"Swiss"],[_,g]],[/\b((zeki)?tb.*\b) b/i],[E,[A,"Zeki"],[_,g]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[A,"Dragon Touch"],E,[_,g]],[/\b(ns-?\w{0,9}) b/i],[E,[A,"Insignia"],[_,g]],[/\b((nxa|next)-?\w{0,9}) b/i],[E,[A,"NextBook"],[_,g]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[A,"Voice"],E,[_,f]],[/\b(lvtel\-)?(v1[12]) b/i],[[A,"LvTel"],E,[_,f]],[/\b(ph-1) /i],[E,[A,"Essential"],[_,f]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[E,[A,"Envizen"],[_,g]],[/\b(trio[-\w\. ]+) b/i],[E,[A,"MachSpeed"],[_,g]],[/\btu_(1491) b/i],[E,[A,"Rotor"],[_,g]],[/(shield[\w ]+) b/i],[E,[A,"Nvidia"],[_,g]],[/(sprint) (\w+)/i],[A,E,[_,f]],[/(kin\.[onetw]{3})/i],[[E,/\./g," "],[A,O],[_,f]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[E,[A,k],[_,g]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[E,[A,k],[_,f]],[/smart-tv.+(samsung)/i],[A,[_,p]],[/hbbtv.+maple;(\d+)/i],[[E,/^/,"SmartTV"],[A,F],[_,p]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[A,v],[_,p]],[/(apple) ?tv/i],[A,[E,b+" TV"],[_,p]],[/crkey/i],[[E,N+"cast"],[A,L],[_,p]],[/droid.+aft(\w+)( bui|\))/i],[E,[A,C],[_,p]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[E,[A,x],[_,p]],[/(bravia[\w ]+)( bui|\))/i],[E,[A,B],[_,p]],[/(mitv-\w{5}) bui/i],[E,[A,G],[_,p]],[/Hbbtv.*(technisat) (.*);/i],[A,E,[_,p]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[A,z],[E,z],[_,p]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[_,p]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[A,E,[_,m]],[/droid.+; (shield) bui/i],[E,[A,"Nvidia"],[_,m]],[/(playstation [345portablevi]+)/i],[E,[A,B],[_,m]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[E,[A,O],[_,m]],[/((pebble))app/i],[A,E,[_,R]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[E,[A,b],[_,R]],[/droid.+; (glass) \d/i],[E,[A,L],[_,R]],[/droid.+; (wt63?0{2,3})\)/i],[E,[A,k],[_,R]],[/(quest( \d| pro)?)/i],[E,[A,H],[_,R]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[A,[_,I]],[/(aeobc)\b/i],[E,[A,C],[_,I]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[E,[_,f]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[E,[_,g]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[_,g]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[_,f]],[/(android[-\w\. ]{0,9});.+buil/i],[E,[A,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[h,[d,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[h,[d,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[d,h],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[h,d]],os:[[/microsoft (windows) (vista|xp)/i],[d,h],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[d,[h,X,Q]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[h,X,Q],[d,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[h,/_/g,"."],[d,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[d,Y],[h,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[h,d],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[d,h],[/\(bb(10);/i],[h,[d,D]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[h,[d,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[h,[d,y+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[h,[d,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[h,[d,"watchOS"]],[/crkey\/([\d\.]+)/i],[h,[d,N+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[d,V],h],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[d,h],[/(sunos) ?([\w\.\d]*)/i],[[d,"Solaris"],h],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[d,h]]},Z=function(e,t){if(typeof e===c&&(t=e,e=a),!(this instanceof Z))return new Z(e,t).getResult();var i=typeof r!==s&&r.navigator?r.navigator:a,n=e||(i&&i.userAgent?i.userAgent:""),m=i&&i.userAgentData?i.userAgentData:a,p=t?function(e,t){var i={};for(var n in e)t[n]&&t[n].length%2==0?i[n]=t[n].concat(e[n]):i[n]=e[n];return i}($,t):$,R=i&&i.userAgent==n;return this.getBrowser=function(){var e,t={};return t[d]=a,t[h]=a,q.call(t,n,p.browser),t[u]=typeof(e=t[h])===l?e.replace(/[^\d\.]/g,"").split(".")[0]:a,R&&i&&i.brave&&typeof i.brave.isBrave==o&&(t[d]="Brave"),t},this.getCPU=function(){var e={};return e[T]=a,q.call(e,n,p.cpu),e},this.getDevice=function(){var e={};return e[A]=a,e[E]=a,e[_]=a,q.call(e,n,p.device),R&&!e[_]&&m&&m.mobile&&(e[_]=f),R&&"Macintosh"==e[E]&&i&&typeof i.standalone!==s&&i.maxTouchPoints&&i.maxTouchPoints>2&&(e[E]="iPad",e[_]=g),e},this.getEngine=function(){var e={};return e[d]=a,e[h]=a,q.call(e,n,p.engine),e},this.getOS=function(){var e={};return e[d]=a,e[h]=a,q.call(e,n,p.os),R&&!e[d]&&m&&m.platform&&"Unknown"!=m.platform&&(e[d]=m.platform.replace(/chrome os/i,V).replace(/macos/i,Y)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===l&&e.length>500?z(e,500):e,this},this.setUA(n),this};Z.VERSION="1.0.38",Z.BROWSER=j([d,h,u]),Z.CPU=j([T]),Z.DEVICE=j([E,A,_,m,f,p,g,R,I]),Z.ENGINE=Z.OS=j([d,h]),typeof t!==s?(e.exports&&(t=e.exports=Z),t.UAParser=Z):i.amdO?(n=function(){return Z}.call(t,i,t,e))===a||(e.exports=n):typeof r!==s&&(r.UAParser=Z);var J=typeof r!==s&&(r.jQuery||r.Zepto);if(J&&!J.ua){var ee=new Z;J.ua=ee.getResult(),J.ua.get=function(){return ee.getUA()},J.ua.set=function(e){ee.setUA(e);var t=ee.getResult();for(var i in t)J.ua[i]=t[i]}}}("object"==typeof window?window:this)},649:function(e,t,i){"use strict";var n=i(8850),r=i(3621),a=i(138);function o(e){e=e||{};const t=this.context,i=(0,n.A)(t).getInstance(),a=e.settings,o=[];let s,c,l,u;function E(e){return e&&e.bind?e.bind(window.console):window.console.log.bind(window.console)}function d(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];m(1,this,...t)}function _(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];m(2,this,...t)}function A(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];m(3,this,...t)}function h(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];m(4,this,...t)}function T(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];m(5,this,...t)}function m(e,t){let n="",s=null;c&&(s=(new Date).getTime(),n+="["+(s-u)+"]"),l&&t&&t.getClassName&&(n+="["+t.getClassName()+"]",t.getType&&(n+="["+t.getType()+"]")),n.length>0&&(n+=" ");for(var E=arguments.length,d=new Array(E>2?E-2:0),_=2;_<E;_++)d[_-2]=arguments[_];Array.apply(null,d).forEach((function(e){n+=e+" "})),o[e]&&a&&a.get().debug.logLevel>=e&&o[e](n),a&&a.get().debug.dispatchEvent&&i.trigger(r.A.LOG,{message:n,level:e})}return s={getLogger:function(e){return{fatal:d.bind(e),error:_.bind(e),warn:A.bind(e),info:h.bind(e),debug:T.bind(e)}},setLogTimestampVisible:function(e){c=e},setCalleeNameVisible:function(e){l=e}},c=!0,l=!0,u=(new Date).getTime(),"undefined"!=typeof window&&window.console&&(o[1]=E(window.console.error),o[2]=E(window.console.error),o[3]=E(window.console.warn),o[4]=E(window.console.info),o[5]=E(window.console.debug)),s}o.__dashjs_factory_name="Debug";const s=a.A.getSingletonFactory(o);s.LOG_LEVEL_NONE=0,s.LOG_LEVEL_FATAL=1,s.LOG_LEVEL_ERROR=2,s.LOG_LEVEL_WARNING=3,s.LOG_LEVEL_INFO=4,s.LOG_LEVEL_DEBUG=5,a.A.updateSingletonFactory(o.__dashjs_factory_name,s),t.A=s},8850:function(e,t,i){"use strict";var n=i(138),r=i(1191);function a(){let e={};function t(t,i,r){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(!t)throw new Error("event type cannot be null or undefined");if(!i||"function"!=typeof i)throw new Error("listener must be a function: "+i);let s=a.priority||0;if(n(t,i,r)>=0)return;e[t]=e[t]||[];const c={callback:i,scope:r,priority:s,executeOnlyOnce:o};r&&r.getStreamId&&(c.streamId=r.getStreamId()),r&&r.getType&&(c.mediaType=r.getType()),a&&a.mode&&(c.mode=a.mode),e[t].some(((i,n)=>{if(i&&s>i.priority)return e[t].splice(n,0,c),!0}))||e[t].push(c)}function i(t,i,r){if(!t||!i||!e[t])return;const a=n(t,i,r);a<0||(e[t][a]=null)}function n(t,i,n){let r=-1;return e[t]?(e[t].some(((e,t)=>{if(e&&e.callback===i&&(!n||n===e.scope))return r=t,!0})),r):r}const a={on:function(e,i,n){t(e,i,n,arguments.length>3&&void 0!==arguments[3]?arguments[3]:{})},once:function(e,i,n){t(e,i,n,arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},!0)},off:i,trigger:function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!t||!e[t])return;if(n=n||{},n.hasOwnProperty("type"))throw new Error("'type' is a reserved word for event dispatching");n.type=t,a.streamId&&(n.streamId=a.streamId),a.mediaType&&(n.mediaType=a.mediaType);const o=[];e[t].filter((e=>!(!e||a.streamId&&e.streamId&&e.streamId!==a.streamId||a.mediaType&&e.mediaType&&e.mediaType!==a.mediaType||a.mode&&e.mode&&e.mode!==a.mode||!e.mode&&a.mode&&a.mode===r.A.EVENT_MODE_ON_RECEIVE))).forEach((e=>{e&&e.callback.call(e.scope,n),e.executeOnlyOnce&&o.push(e)})),o.forEach((e=>{i(t,e.callback,e.scope)}))},reset:function(){e={}}};return a}a.__dashjs_factory_name="EventBus";const o=n.A.getSingletonFactory(a);o.EVENT_PRIORITY_LOW=0,o.EVENT_PRIORITY_HIGH=5e3,n.A.updateSingletonFactory(a.__dashjs_factory_name,o),t.A=o},138:function(e,t){"use strict";const i=function(){let e,t=[];const i={},n={};function r(e,i){for(const n in t){const r=t[n];if(r.context===e&&r.name===i)return r.instance}return null}function a(e,t){return t[e]}function o(e,t,i){e in i&&(i[e]=t)}function s(t,i,n){let r;const a=t.__dashjs_factory_name,o=i[a];if(o){let a=o.instance;if(!o.override)return a.apply({context:i,factory:e},n);r=t.apply({context:i},n),a=a.apply({context:i,factory:e,parent:r},n);for(const e in a)r.hasOwnProperty(e)&&(r[e]=a[e])}else r=t.apply({context:i},n);return r.getClassName=function(){return a},r}return e={extend:function(e,t,i,n){!n[e]&&t&&(n[e]={instance:t,override:i})},getSingletonInstance:r,setSingletonInstance:function(e,i,n){for(const r in t){const a=t[r];if(a.context===e&&a.name===i)return void(t[r].instance=n)}t.push({name:i,context:e,instance:n})},deleteSingletonInstances:function(e){t=t.filter((t=>t.context!==e))},getSingletonFactory:function(e){let n=a(e.__dashjs_factory_name,i);return n||(n=function(i){let n;return void 0===i&&(i={}),{getInstance:function(){return n||(n=r(i,e.__dashjs_factory_name)),n||(n=s(e,i,arguments),t.push({name:e.__dashjs_factory_name,context:i,instance:n})),n}}},i[e.__dashjs_factory_name]=n),n},getSingletonFactoryByName:function(e){return a(e,i)},updateSingletonFactory:function(e,t){o(e,t,i)},getClassFactory:function(e){let t=a(e.__dashjs_factory_name,n);return t||(t=function(t){return void 0===t&&(t={}),{create:function(){return s(e,t,arguments)}}},n[e.__dashjs_factory_name]=t),t},getClassFactoryByName:function(e){return a(e,n)},updateClassFactory:function(e,t){o(e,t,n)}},e}();t.A=i},8261:function(e,t,i){"use strict";var n=i(138),r=i(7263),a=i(649),o=i(5212),s=i(7568),c=i(8850),l=i(3621);function u(){let e;const t=this.context,i=(0,c.A)(t).getInstance(),n={"streaming.delay.liveDelay":l.A.SETTING_UPDATED_LIVE_DELAY,"streaming.delay.liveDelayFragmentCount":l.A.SETTING_UPDATED_LIVE_DELAY_FRAGMENT_COUNT,"streaming.liveCatchup.enabled":l.A.SETTING_UPDATED_CATCHUP_ENABLED,"streaming.liveCatchup.playbackRate.min":l.A.SETTING_UPDATED_PLAYBACK_RATE_MIN,"streaming.liveCatchup.playbackRate.max":l.A.SETTING_UPDATED_PLAYBACK_RATE_MAX,"streaming.abr.rules.throughputRule.active":l.A.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.bolaRule.active":l.A.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.insufficientBufferRule.active":l.A.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.switchHistoryRule.active":l.A.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.droppedFramesRule.active":l.A.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.abandonRequestsRule.active":l.A.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.l2ARule.active":l.A.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.loLPRule.active":l.A.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.maxBitrate.video":l.A.SETTING_UPDATED_MAX_BITRATE,"streaming.abr.maxBitrate.audio":l.A.SETTING_UPDATED_MAX_BITRATE,"streaming.abr.minBitrate.video":l.A.SETTING_UPDATED_MIN_BITRATE,"streaming.abr.minBitrate.audio":l.A.SETTING_UPDATED_MIN_BITRATE},u={debug:{logLevel:a.A.LOG_LEVEL_WARNING,dispatchEvent:!1},streaming:{abandonLoadTimeout:1e4,wallclockTimeUpdateInterval:100,manifestUpdateRetryInterval:100,liveUpdateTimeThresholdInMilliseconds:0,cacheInitSegments:!1,applyServiceDescription:!0,applyProducerReferenceTime:!0,applyContentSteering:!0,enableManifestDurationMismatchFix:!0,parseInbandPrft:!1,enableManifestTimescaleMismatchFix:!1,capabilities:{filterUnsupportedEssentialProperties:!0,supportedEssentialProperties:[{schemeIdUri:o.A.FONT_DOWNLOAD_DVB_SCHEME},{schemeIdUri:o.A.COLOUR_PRIMARIES_SCHEME_ID_URI,value:/1|5|6|7/},{schemeIdUri:o.A.URL_QUERY_INFO_SCHEME},{schemeIdUri:o.A.EXT_URL_QUERY_INFO_SCHEME},{schemeIdUri:o.A.MATRIX_COEFFICIENTS_SCHEME_ID_URI,value:/0|1|5|6/},{schemeIdUri:o.A.TRANSFER_CHARACTERISTICS_SCHEME_ID_URI,value:/1|6|13|14|15/},...o.A.THUMBNAILS_SCHEME_ID_URIS.map((e=>({schemeIdUri:e})))],useMediaCapabilitiesApi:!0,filterVideoColorimetryEssentialProperties:!1,filterHDRMetadataFormatEssentialProperties:!1},events:{eventControllerRefreshDelay:100,deleteEventMessageDataTimeout:1e4},timeShiftBuffer:{calcFromSegmentTimeline:!1,fallbackToSegmentTimeline:!0},metrics:{maxListDepth:100},delay:{liveDelayFragmentCount:NaN,liveDelay:NaN,useSuggestedPresentationDelay:!0},protection:{keepProtectionMediaKeys:!1,ignoreEmeEncryptedEvent:!1,detectPlayreadyMessageFormat:!0,ignoreKeyStatuses:!1},buffer:{enableSeekDecorrelationFix:!1,fastSwitchEnabled:null,flushBufferAtTrackSwitch:!1,reuseExistingSourceBuffers:!0,bufferPruningInterval:10,bufferToKeep:20,bufferTimeAtTopQuality:30,bufferTimeAtTopQualityLongForm:60,initialBufferLevel:NaN,bufferTimeDefault:18,longFormContentDurationThreshold:600,stallThreshold:.3,lowLatencyStallThreshold:.3,useAppendWindow:!0,setStallState:!0,avoidCurrentTimeRangePruning:!1,useChangeType:!0,mediaSourceDurationInfinity:!0,resetSourceBuffersForTrackSwitch:!1,syntheticStallEvents:{enabled:!1,ignoreReadyState:!1}},gaps:{jumpGaps:!0,jumpLargeGaps:!0,smallGapLimit:1.5,threshold:.3,enableSeekFix:!0,enableStallFix:!1,stallSeek:.1},utcSynchronization:{enabled:!0,useManifestDateHeaderTimeSource:!0,backgroundAttempts:2,timeBetweenSyncAttempts:30,maximumTimeBetweenSyncAttempts:600,minimumTimeBetweenSyncAttempts:2,timeBetweenSyncAttemptsAdjustmentFactor:2,maximumAllowedDrift:100,enableBackgroundSyncAfterSegmentDownloadError:!0,defaultTimingSource:{scheme:"urn:mpeg:dash:utc:http-xsdate:2014",value:"https://time.akamai.com/?iso&ms"}},scheduling:{defaultTimeout:500,lowLatencyTimeout:0,scheduleWhilePaused:!0},text:{defaultEnabled:!0,dispatchForManualRendering:!1,extendSegmentedCues:!0,imsc:{displayForcedOnlyMode:!1,enableRollUp:!0},webvtt:{customRenderingEnabled:!1}},liveCatchup:{maxDrift:NaN,playbackRate:{min:NaN,max:NaN},playbackBufferMin:.5,enabled:null,mode:o.A.LIVE_CATCHUP_MODE_DEFAULT},lastBitrateCachingInfo:{enabled:!0,ttl:36e4},lastMediaSettingsCachingInfo:{enabled:!0,ttl:36e4},saveLastMediaSettingsForCurrentStreamingSession:!0,cacheLoadThresholds:{video:10,audio:5},trackSwitchMode:{audio:o.A.TRACK_SWITCH_MODE_ALWAYS_REPLACE,video:o.A.TRACK_SWITCH_MODE_NEVER_REPLACE},ignoreSelectionPriority:!1,prioritizeRoleMain:!0,assumeDefaultRoleAsMain:!0,selectionModeForInitialTrack:o.A.TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY,fragmentRequestTimeout:2e4,fragmentRequestProgressTimeout:-1,manifestRequestTimeout:1e4,retryIntervals:{[s.G.MPD_TYPE]:500,[s.G.XLINK_EXPANSION_TYPE]:500,[s.G.MEDIA_SEGMENT_TYPE]:1e3,[s.G.INIT_SEGMENT_TYPE]:1e3,[s.G.BITSTREAM_SWITCHING_SEGMENT_TYPE]:1e3,[s.G.INDEX_SEGMENT_TYPE]:1e3,[s.G.MSS_FRAGMENT_INFO_SEGMENT_TYPE]:1e3,[s.G.LICENSE]:1e3,[s.G.OTHER_TYPE]:1e3,lowLatencyReductionFactor:10},retryAttempts:{[s.G.MPD_TYPE]:3,[s.G.XLINK_EXPANSION_TYPE]:1,[s.G.MEDIA_SEGMENT_TYPE]:3,[s.G.INIT_SEGMENT_TYPE]:3,[s.G.BITSTREAM_SWITCHING_SEGMENT_TYPE]:3,[s.G.INDEX_SEGMENT_TYPE]:3,[s.G.MSS_FRAGMENT_INFO_SEGMENT_TYPE]:3,[s.G.LICENSE]:3,[s.G.OTHER_TYPE]:3,lowLatencyMultiplyFactor:5},abr:{limitBitrateByPortal:!1,usePixelRatioInLimitBitrateByPortal:!1,enableSupplementalPropertyAdaptationSetSwitching:!0,rules:{throughputRule:{active:!0},bolaRule:{active:!0},insufficientBufferRule:{active:!0,parameters:{throughputSafetyFactor:.7,segmentIgnoreCount:2}},switchHistoryRule:{active:!0,parameters:{sampleSize:8,switchPercentageThreshold:.075}},droppedFramesRule:{active:!1,parameters:{minimumSampleSize:375,droppedFramesPercentageThreshold:.15}},abandonRequestsRule:{active:!0,parameters:{abandonDurationMultiplier:1.8,minSegmentDownloadTimeThresholdInMs:500,minThroughputSamplesThreshold:6}},l2ARule:{active:!1},loLPRule:{active:!1}},throughput:{averageCalculationMode:o.A.THROUGHPUT_CALCULATION_MODES.EWMA,lowLatencyDownloadTimeCalculationMode:o.A.LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE.MOOF_PARSING,useResourceTimingApi:!0,useNetworkInformationApi:{xhr:!1,fetch:!1},useDeadTimeLatency:!0,bandwidthSafetyFactor:.9,sampleSettings:{live:3,vod:4,enableSampleSizeAdjustment:!0,decreaseScale:.7,increaseScale:1.3,maxMeasurementsToKeep:20,averageLatencySampleAmount:4},ewma:{throughputSlowHalfLifeSeconds:8,throughputFastHalfLifeSeconds:3,latencySlowHalfLifeCount:2,latencyFastHalfLifeCount:1,weightDownloadTimeMultiplicationFactor:.0015}},maxBitrate:{audio:-1,video:-1},minBitrate:{audio:-1,video:-1},initialBitrate:{audio:-1,video:-1},autoSwitchBitrate:{audio:!0,video:!0}},cmcd:{applyParametersFromMpd:!0,enabled:!1,sid:null,cid:null,rtp:null,rtpSafetyFactor:5,mode:o.A.CMCD_MODE_QUERY,enabledKeys:o.A.CMCD_AVAILABLE_KEYS,includeInRequests:["segment","mpd"],version:1},cmsd:{enabled:!1,abr:{applyMb:!1,etpWeightRatio:0}},defaultSchemeIdUri:{viewpoint:"",audioChannelConfiguration:"urn:mpeg:mpegB:cicp:ChannelConfiguration",role:"urn:mpeg:dash:role:2011",accessibility:"urn:mpeg:dash:role:2011"}},errors:{recoverAttempts:{mediaErrorDecode:5}}};let E=r.A.clone(u);function d(e,t,a){for(let o in e)e.hasOwnProperty(o)&&(t.hasOwnProperty(o)?"object"!=typeof e[o]||e[o]instanceof RegExp||e[o]instanceof Array||null===e[o]?(t[o]=r.A.clone(e[o]),n[a+o]&&i.trigger(n[a+o])):d(e[o],t[o],a.slice()+o+"."):console.error("Settings parameter "+a+o+" is not supported"))}return e={get:function(){return E},update:function(e){"object"==typeof e&&d(e,E,"")},reset:function(){E=r.A.clone(u)}},e}u.__dashjs_factory_name="Settings";let E=n.A.getSingletonFactory(u);t.A=E},7263:function(e,t,i){"use strict";var n=i(3282),r=i(8571),a=i(5212);class o{static mixin(e,t,i){let n,r={};if(e)for(let a in t)t.hasOwnProperty(a)&&(n=t[a],a in e&&(e[a]===n||a in r&&r[a]===n)||("object"==typeof e[a]&&null!==e[a]?e[a]=o.mixin(e[a],n,i):e[a]=i(n)));return e}static clone(e){if(!e||"object"!=typeof e)return e;if(e instanceof RegExp)return new RegExp(e);let t;if(e instanceof Array){t=[];for(let i=0,n=e.length;i<n;++i)i in e&&t.push(o.clone(e[i]))}else t={};return o.mixin(t,e,o.clone)}static addAdditionalQueryParameterToUrl(e,t){try{if(!t||0===t.length)return e;let i=e;return t.forEach((e=>{let{key:t,value:n}=e;const r=i.includes("?")?"&":"?";i+=`${r}${encodeURIComponent(t)}=${encodeURIComponent(n)}`})),i}catch(t){return e}}static removeQueryParameterFromUrl(e,t){if(!e||!t)return e;const i=new URL(e),n=new URLSearchParams(i.search);if(!n||0===n.size||!n.has(t))return e;n.delete(t);const r=Array.from(n.entries()).map((e=>{let[t,i]=e;return`${t}=${i}`})).join("&"),a=`${i.origin}${i.pathname}`;return r?`${a}?${r}`:a}static parseHttpHeaders(e){let t={};if(!e)return t;let i=e.trim().split("\r\n");for(let e=0,n=i.length;e<n;e++){let n=i[e],r=n.indexOf(": ");r>0&&(t[n.substring(0,r)]=n.substring(r+2))}return t}static parseQueryParams(e){const t=[],i=new URLSearchParams(e);for(const[e,n]of i.entries())t.push({key:decodeURIComponent(e),value:decodeURIComponent(n)});return t}static generateUuid(){let e=(new Date).getTime();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){const i=(e+16*Math.random())%16|0;return e=Math.floor(e/16),("x"==t?i:3&i|8).toString(16)}))}static generateHashCode(e){let t=0;if(0===e.length)return t;for(let i=0;i<e.length;i++)t=(t<<5)-t+e.charCodeAt(i),t|=0;return t}static getRelativeUrl(e,t){try{const i=new URL(e),r=new URL(t);if(i.protocol=r.protocol,i.origin!==r.origin)return t;let a=n.relative(i.pathname.substr(0,i.pathname.lastIndexOf("/")),r.pathname.substr(0,r.pathname.lastIndexOf("/")));const o=0===a.length?1:0;return a+=r.pathname.substr(r.pathname.lastIndexOf("/")+o,r.pathname.length-1),r.pathname.length<a.length?r.pathname:a}catch(e){return t}}static getHostFromUrl(e){try{return new URL(e).host}catch(e){return null}}static parseUserAgent(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;try{const t=null===e&&"undefined"!=typeof navigator?navigator.userAgent.toLowerCase():"";return(0,r.UAParser)(t)}catch(e){return{}}}static stringHasProtocol(e){return/(http(s?)):\/\//i.test(e)}static bufferSourceToDataView(e){return o.toDataView(e,DataView)}static bufferSourceToInt8(e){return o.toDataView(e,Uint8Array)}static uint8ArrayToString(e){return new TextDecoder("utf-8").decode(e)}static bufferSourceToHex(e){const t=o.bufferSourceToInt8(e);let i="";for(let e of t)e=e.toString(16),1===e.length&&(e="0"+e),i+=e;return i}static toDataView(e,t){const i=o.getArrayBuffer(e);let n=1;"BYTES_PER_ELEMENT"in DataView&&(n=DataView.BYTES_PER_ELEMENT);const r=((e.byteOffset||0)+e.byteLength)/n,a=(e.byteOffset||0)/n,s=Math.floor(Math.max(0,Math.min(a,r)));return new t(i,s,Math.floor(Math.min(s+Math.max(1/0,0),r))-s)}static getArrayBuffer(e){return e instanceof ArrayBuffer?e:e.buffer}static getCodecFamily(e){const{base:t,profile:i}=o._getCodecParts(e);switch(t){case"mp4a":switch(i){case"69":case"6b":case"40.34":return a.A.CODEC_FAMILIES.MP3;case"66":case"67":case"68":case"40.2":case"40.02":case"40.5":case"40.05":case"40.29":case"40.42":return a.A.CODEC_FAMILIES.AAC;case"a5":return a.A.CODEC_FAMILIES.AC3;case"e6":return a.A.CODEC_FAMILIES.EC3;case"b2":return a.A.CODEC_FAMILIES.DTSX;case"a9":return a.A.CODEC_FAMILIES.DTSC}break;case"avc1":case"avc3":return a.A.CODEC_FAMILIES.AVC;case"hvc1":case"hvc3":return a.A.CODEC_FAMILIES.HEVC;default:return t}return t}static _getCodecParts(e){const[t,...i]=e.split(".");return{base:t,profile:i.join(".")}}}t.A=o},5734:function(e,t,i){"use strict";var n=i(7252);class r extends n.A{constructor(){super(),this.ATTEMPT_BACKGROUND_SYNC="attemptBackgroundSync",this.BUFFERING_COMPLETED="bufferingCompleted",this.BUFFER_CLEARED="bufferCleared",this.BYTES_APPENDED_END_FRAGMENT="bytesAppendedEndFragment",this.BUFFER_REPLACEMENT_STARTED="bufferReplacementStarted",this.CHECK_FOR_EXISTENCE_COMPLETED="checkForExistenceCompleted",this.CMSD_STATIC_HEADER="cmsdStaticHeader",this.CURRENT_TRACK_CHANGED="currentTrackChanged",this.DATA_UPDATE_COMPLETED="dataUpdateCompleted",this.INBAND_EVENTS="inbandEvents",this.INITIAL_STREAM_SWITCH="initialStreamSwitch",this.INIT_FRAGMENT_LOADED="initFragmentLoaded",this.INIT_FRAGMENT_NEEDED="initFragmentNeeded",this.INTERNAL_MANIFEST_LOADED="internalManifestLoaded",this.ORIGINAL_MANIFEST_LOADED="originalManifestLoaded",this.LOADING_COMPLETED="loadingCompleted",this.LOADING_PROGRESS="loadingProgress",this.LOADING_DATA_PROGRESS="loadingDataProgress",this.LOADING_ABANDONED="loadingAborted",this.MANIFEST_UPDATED="manifestUpdated",this.MEDIA_FRAGMENT_LOADED="mediaFragmentLoaded",this.MEDIA_FRAGMENT_NEEDED="mediaFragmentNeeded",this.MEDIAINFO_UPDATED="mediaInfoUpdated",this.QUOTA_EXCEEDED="quotaExceeded",this.SEGMENT_LOCATION_BLACKLIST_ADD="segmentLocationBlacklistAdd",this.SEGMENT_LOCATION_BLACKLIST_CHANGED="segmentLocationBlacklistChanged",this.SERVICE_LOCATION_BASE_URL_BLACKLIST_ADD="serviceLocationBlacklistAdd",this.SERVICE_LOCATION_BASE_URL_BLACKLIST_CHANGED="serviceLocationBlacklistChanged",this.SERVICE_LOCATION_LOCATION_BLACKLIST_ADD="serviceLocationLocationBlacklistAdd",this.SERVICE_LOCATION_LOCATION_BLACKLIST_CHANGED="serviceLocationLocationBlacklistChanged",this.SET_FRAGMENTED_TEXT_AFTER_DISABLED="setFragmentedTextAfterDisabled",this.SET_NON_FRAGMENTED_TEXT="setNonFragmentedText",this.SOURCE_BUFFER_ERROR="sourceBufferError",this.STREAMS_COMPOSED="streamsComposed",this.STREAM_BUFFERING_COMPLETED="streamBufferingCompleted",this.STREAM_REQUESTING_COMPLETED="streamRequestingCompleted",this.TEXT_TRACKS_QUEUE_INITIALIZED="textTracksQueueInitialized",this.TIME_SYNCHRONIZATION_COMPLETED="timeSynchronizationComplete",this.UPDATE_TIME_SYNC_OFFSET="updateTimeSyncOffset",this.URL_RESOLUTION_FAILED="urlResolutionFailed",this.VIDEO_CHUNK_RECEIVED="videoChunkReceived",this.WALLCLOCK_TIME_UPDATED="wallclockTimeUpdated",this.XLINK_ELEMENT_LOADED="xlinkElementLoaded",this.XLINK_READY="xlinkReady",this.SEEK_TARGET="seekTarget",this.SETTING_UPDATED_LIVE_DELAY="settingUpdatedLiveDelay",this.SETTING_UPDATED_LIVE_DELAY_FRAGMENT_COUNT="settingUpdatedLiveDelayFragmentCount",this.SETTING_UPDATED_CATCHUP_ENABLED="settingUpdatedCatchupEnabled",this.SETTING_UPDATED_PLAYBACK_RATE_MIN="settingUpdatedPlaybackRateMin",this.SETTING_UPDATED_PLAYBACK_RATE_MAX="settingUpdatedPlaybackRateMax",this.SETTING_UPDATED_ABR_ACTIVE_RULES="settingUpdatedAbrActiveRules",this.SETTING_UPDATED_MAX_BITRATE="settingUpdatedMaxBitrate",this.SETTING_UPDATED_MIN_BITRATE="settingUpdatedMinBitrate"}}t.A=r},3621:function(e,t,i){"use strict";var n=i(5734);class r extends n.A{}let a=new r;t.A=a},7252:function(e,t){"use strict";t.A=class{extend(e,t){if(!e)return;let i=!!t&&t.override,n=!!t&&t.publicOnly;for(const t in e)!e.hasOwnProperty(t)||this[t]&&!i||n&&-1===e[t].indexOf("public_")||(this[t]=e[t])}}},5717:function(e,t){"use strict";t.A=class{constructor(){this.schemeIdUri="",this.value=""}}},1191:function(e,t,i){"use strict";var n=i(7252);class r extends n.A{constructor(){super(),this.AST_IN_FUTURE="astInFuture",this.BASE_URLS_UPDATED="baseUrlsUpdated",this.BUFFER_EMPTY="bufferStalled",this.BUFFER_LOADED="bufferLoaded",this.BUFFER_LEVEL_STATE_CHANGED="bufferStateChanged",this.BUFFER_LEVEL_UPDATED="bufferLevelUpdated",this.DVB_FONT_DOWNLOAD_ADDED="dvbFontDownloadAdded",this.DVB_FONT_DOWNLOAD_COMPLETE="dvbFontDownloadComplete",this.DVB_FONT_DOWNLOAD_FAILED="dvbFontDownloadFailed",this.DYNAMIC_TO_STATIC="dynamicToStatic",this.ERROR="error",this.FRAGMENT_LOADING_COMPLETED="fragmentLoadingCompleted",this.FRAGMENT_LOADING_PROGRESS="fragmentLoadingProgress",this.FRAGMENT_LOADING_STARTED="fragmentLoadingStarted",this.FRAGMENT_LOADING_ABANDONED="fragmentLoadingAbandoned",this.LOG="log",this.MANIFEST_LOADING_STARTED="manifestLoadingStarted",this.MANIFEST_LOADING_FINISHED="manifestLoadingFinished",this.MANIFEST_LOADED="manifestLoaded",this.METRICS_CHANGED="metricsChanged",this.METRIC_CHANGED="metricChanged",this.METRIC_ADDED="metricAdded",this.METRIC_UPDATED="metricUpdated",this.PERIOD_SWITCH_STARTED="periodSwitchStarted",this.PERIOD_SWITCH_COMPLETED="periodSwitchCompleted",this.QUALITY_CHANGE_REQUESTED="qualityChangeRequested",this.QUALITY_CHANGE_RENDERED="qualityChangeRendered",this.NEW_TRACK_SELECTED="newTrackSelected",this.TRACK_CHANGE_RENDERED="trackChangeRendered",this.STREAM_INITIALIZING="streamInitializing",this.STREAM_UPDATED="streamUpdated",this.STREAM_ACTIVATED="streamActivated",this.STREAM_DEACTIVATED="streamDeactivated",this.STREAM_INITIALIZED="streamInitialized",this.STREAM_TEARDOWN_COMPLETE="streamTeardownComplete",this.TEXT_TRACKS_ADDED="allTextTracksAdded",this.TEXT_TRACK_ADDED="textTrackAdded",this.CUE_ENTER="cueEnter",this.CUE_EXIT="cueExit",this.THROUGHPUT_MEASUREMENT_STORED="throughputMeasurementStored",this.TTML_PARSED="ttmlParsed",this.TTML_TO_PARSE="ttmlToParse",this.CAPTION_RENDERED="captionRendered",this.CAPTION_CONTAINER_RESIZE="captionContainerResize",this.CAN_PLAY="canPlay",this.CAN_PLAY_THROUGH="canPlayThrough",this.PLAYBACK_ENDED="playbackEnded",this.PLAYBACK_ERROR="playbackError",this.PLAYBACK_INITIALIZED="playbackInitialized",this.PLAYBACK_NOT_ALLOWED="playbackNotAllowed",this.PLAYBACK_METADATA_LOADED="playbackMetaDataLoaded",this.PLAYBACK_LOADED_DATA="playbackLoadedData",this.PLAYBACK_PAUSED="playbackPaused",this.PLAYBACK_PLAYING="playbackPlaying",this.PLAYBACK_PROGRESS="playbackProgress",this.PLAYBACK_RATE_CHANGED="playbackRateChanged",this.PLAYBACK_SEEKED="playbackSeeked",this.PLAYBACK_SEEKING="playbackSeeking",this.PLAYBACK_STALLED="playbackStalled",this.PLAYBACK_STARTED="playbackStarted",this.PLAYBACK_TIME_UPDATED="playbackTimeUpdated",this.PLAYBACK_VOLUME_CHANGED="playbackVolumeChanged",this.PLAYBACK_WAITING="playbackWaiting",this.MANIFEST_VALIDITY_CHANGED="manifestValidityChanged",this.EVENT_MODE_ON_START="eventModeOnStart",this.EVENT_MODE_ON_RECEIVE="eventModeOnReceive",this.CONFORMANCE_VIOLATION="conformanceViolation",this.REPRESENTATION_SWITCH="representationSwitch",this.ADAPTATION_SET_REMOVED_NO_CAPABILITIES="adaptationSetRemovedNoCapabilities",this.CONTENT_STEERING_REQUEST_COMPLETED="contentSteeringRequestCompleted",this.INBAND_PRFT="inbandPrft",this.MANAGED_MEDIA_SOURCE_START_STREAMING="managedMediaSourceStartStreaming",this.MANAGED_MEDIA_SOURCE_END_STREAMING="managedMediaSourceEndStreaming"}}let a=new r;t.A=a},5212:function(e,t){"use strict";t.A={STREAM:"stream",VIDEO:"video",AUDIO:"audio",TEXT:"text",MUXED:"muxed",IMAGE:"image",STPP:"stpp",TTML:"ttml",VTT:"vtt",WVTT:"wvtt",CONTENT_STEERING:"contentSteering",LIVE_CATCHUP_MODE_DEFAULT:"liveCatchupModeDefault",LIVE_CATCHUP_MODE_LOLP:"liveCatchupModeLoLP",MOVING_AVERAGE_SLIDING_WINDOW:"slidingWindow",MOVING_AVERAGE_EWMA:"ewma",BAD_ARGUMENT_ERROR:"Invalid Arguments",MISSING_CONFIG_ERROR:"Missing config parameter(s)",TRACK_SWITCH_MODE_ALWAYS_REPLACE:"alwaysReplace",TRACK_SWITCH_MODE_NEVER_REPLACE:"neverReplace",TRACK_SELECTION_MODE_FIRST_TRACK:"firstTrack",TRACK_SELECTION_MODE_HIGHEST_BITRATE:"highestBitrate",TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY:"highestEfficiency",TRACK_SELECTION_MODE_WIDEST_RANGE:"widestRange",CMCD_QUERY_KEY:"CMCD",CMCD_MODE_QUERY:"query",CMCD_MODE_HEADER:"header",CMCD_AVAILABLE_KEYS:["br","d","ot","tb","bl","dl","mtp","nor","nrr","su","bs","rtp","cid","pr","sf","sid","st","v"],CMCD_V2_AVAILABLE_KEYS:["msd","ltc"],CMCD_AVAILABLE_REQUESTS:["segment","mpd","xlink","steering","other"],INITIALIZE:"initialize",TEXT_SHOWING:"showing",TEXT_HIDDEN:"hidden",TEXT_DISABLED:"disabled",ACCESSIBILITY_CEA608_SCHEME:"urn:scte:dash:cc:cea-608:2015",CC1:"CC1",CC3:"CC3",UTF8:"utf-8",SCHEME_ID_URI:"schemeIdUri",START_TIME:"starttime",SERVICE_DESCRIPTION_DVB_LL_SCHEME:"urn:dvb:dash:lowlatency:scope:2019",SUPPLEMENTAL_PROPERTY_DVB_LL_SCHEME:"urn:dvb:dash:lowlatency:critical:2019",CTA_5004_2023_SCHEME:"urn:mpeg:dash:cta-5004:2023",THUMBNAILS_SCHEME_ID_URIS:["http://dashif.org/thumbnail_tile","http://dashif.org/guidelines/thumbnail_tile"],FONT_DOWNLOAD_DVB_SCHEME:"urn:dvb:dash:fontdownload:2014",COLOUR_PRIMARIES_SCHEME_ID_URI:"urn:mpeg:mpegB:cicp:ColourPrimaries",URL_QUERY_INFO_SCHEME:"urn:mpeg:dash:urlparam:2014",EXT_URL_QUERY_INFO_SCHEME:"urn:mpeg:dash:urlparam:2016",MATRIX_COEFFICIENTS_SCHEME_ID_URI:"urn:mpeg:mpegB:cicp:MatrixCoefficients",TRANSFER_CHARACTERISTICS_SCHEME_ID_URI:"urn:mpeg:mpegB:cicp:TransferCharacteristics",HDR_METADATA_FORMAT_SCHEME_ID_URI:"urn:dvb:dash:hdr-dmi",HDR_METADATA_FORMAT_VALUES:{ST2094_10:"ST2094-10",SL_HDR2:"SL-HDR2",ST2094_40:"ST2094-40"},MEDIA_CAPABILITIES_API:{COLORGAMUT:{SRGB:"srgb",P3:"p3",REC2020:"rec2020"},TRANSFERFUNCTION:{SRGB:"srgb",PQ:"pq",HLG:"hlg"},HDR_METADATATYPE:{SMPTE_ST_2094_10:"smpteSt2094-10",SLHDR2:"slhdr2",SMPTE_ST_2094_40:"smpteSt2094-40"}},XML:"XML",ARRAY_BUFFER:"ArrayBuffer",DVB_REPORTING_URL:"dvb:reportingUrl",DVB_PROBABILITY:"dvb:probability",OFF_MIMETYPE:"application/font-sfnt",WOFF_MIMETYPE:"application/font-woff",VIDEO_ELEMENT_READY_STATES:{HAVE_NOTHING:0,HAVE_METADATA:1,HAVE_CURRENT_DATA:2,HAVE_FUTURE_DATA:3,HAVE_ENOUGH_DATA:4},FILE_LOADER_TYPES:{FETCH:"fetch_loader",XHR:"xhr_loader"},THROUGHPUT_TYPES:{LATENCY:"throughput_type_latency",BANDWIDTH:"throughput_type_bandwidth"},THROUGHPUT_CALCULATION_MODES:{EWMA:"throughputCalculationModeEwma",ZLEMA:"throughputCalculationModeZlema",ARITHMETIC_MEAN:"throughputCalculationModeArithmeticMean",BYTE_SIZE_WEIGHTED_ARITHMETIC_MEAN:"throughputCalculationModeByteSizeWeightedArithmeticMean",DATE_WEIGHTED_ARITHMETIC_MEAN:"throughputCalculationModeDateWeightedArithmeticMean",HARMONIC_MEAN:"throughputCalculationModeHarmonicMean",BYTE_SIZE_WEIGHTED_HARMONIC_MEAN:"throughputCalculationModeByteSizeWeightedHarmonicMean",DATE_WEIGHTED_HARMONIC_MEAN:"throughputCalculationModeDateWeightedHarmonicMean"},LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE:{MOOF_PARSING:"lowLatencyDownloadTimeCalculationModeMoofParsing",DOWNLOADED_DATA:"lowLatencyDownloadTimeCalculationModeDownloadedData",AAST:"lowLatencyDownloadTimeCalculationModeAast"},RULES_TYPES:{QUALITY_SWITCH_RULES:"qualitySwitchRules",ABANDON_FRAGMENT_RULES:"abandonFragmentRules"},QUALITY_SWITCH_RULES:{BOLA_RULE:"BolaRule",THROUGHPUT_RULE:"ThroughputRule",INSUFFICIENT_BUFFER_RULE:"InsufficientBufferRule",SWITCH_HISTORY_RULE:"SwitchHistoryRule",DROPPED_FRAMES_RULE:"DroppedFramesRule",LEARN_TO_ADAPT_RULE:"L2ARule",LOL_PLUS_RULE:"LoLPRule"},ABANDON_FRAGMENT_RULES:{ABANDON_REQUEST_RULE:"AbandonRequestsRule"},ID3_SCHEME_ID_URI:"https://aomedia.org/emsg/ID3",COMMON_ACCESS_TOKEN_HEADER:"common-access-token",DASH_ROLE_SCHEME_ID:"urn:mpeg:dash:role:2011",CODEC_FAMILIES:{MP3:"mp3",AAC:"aac",AC3:"ac3",EC3:"ec3",DTSX:"dtsx",DTSC:"dtsc",AVC:"avc",HEVC:"hevc"}}},913:function(e,t,i){"use strict";var n=i(7252);class r extends n.A{constructor(){super(),this.METRICS_INITIALISATION_COMPLETE="internal_metricsReportingInitialized",this.BECAME_REPORTING_PLAYER="internal_becameReportingPlayer",this.CMCD_DATA_GENERATED="cmcdDataGenerated"}}let a=new r;t.A=a},1926:function(e,t,i){"use strict";var n=i(5717),r=i(138),a=i(8261),o=i(656),s=i(5212);function c(){let e,t,i,r,c,l,u,E,d,_;const A=this.context,h=(0,a.A)(A).getInstance();function T(){r=[],c=[],l=[],u=[],E=[],_=[],d=null,t=[]}function m(e,t){let i=-1;e.some(((e,n)=>{if(e===t)return i=n,!0})),i<0||e.splice(i,1)}function f(e){let t;for(t=0;t<_.length;t++)if(_[t].rulename===e)return t;return-1}function g(e,i){p(e,i);let r=new n.A;r.schemeIdUri=e,r.value=i,t.push(r)}function p(e,i){(0,o.sq)(e,"string"),(0,o.sq)(i,"string"),t.forEach((function(n,r){n.schemeIdUri===e&&n.value===i&&t.splice(r,1)}))}return e={addAbrCustomRule:function(e,t,i){if("string"!=typeof e||e!==s.A.RULES_TYPES.ABANDON_FRAGMENT_RULES&&e!==s.A.RULES_TYPES.QUALITY_SWITCH_RULES||"string"!=typeof t)throw s.A.BAD_ARGUMENT_ERROR;let n=f(t);-1===n?_.push({type:e,rulename:t,rule:i}):(_[n].type=e,_[n].rule=i)},addRequestInterceptor:function(e){r.push(e)},addResponseInterceptor:function(e){c.push(e)},addUTCTimingSource:g,clearDefaultUTCTimingSources:function(){t=[]},getAbrCustomRules:function(){return _},getCustomCapabilitiesFilters:function(){return E},getCustomInitialTrackSelectionFunction:function(){return d},getLicenseRequestFilters:function(){return l},getLicenseResponseFilters:function(){return u},getRequestInterceptors:function(){return r},getResponseInterceptors:function(){return c},getUTCTimingSources:function(){return t},getXHRWithCredentialsForType:function(e){const t=i[e];return void 0===t?i.default:t},registerCustomCapabilitiesFilter:function(e){E.push(e)},registerLicenseRequestFilter:function(e){l.push(e)},registerLicenseResponseFilter:function(e){u.push(e)},removeAbrCustomRule:function(e){if(e){let t=f(e);-1!==t&&_.splice(t,1)}else _=[]},removeAllAbrCustomRule:function(){_=[]},removeRequestInterceptor:function(e){m(r,e)},removeResponseInterceptor:function(e){m(c,e)},removeUTCTimingSource:p,reset:function(){T()},resetCustomInitialTrackSelectionFunction:function(){d=null},restoreDefaultUTCTimingSources:function(){let e=h.get().streaming.utcSynchronization.defaultTimingSource;g(e.scheme,e.value)},setConfig:function(){},setCustomInitialTrackSelectionFunction:function(e){d=e},setXHRWithCredentialsForType:function e(t,n){t?i[t]=!!n:Object.keys(i).forEach((t=>{e(t,n)}))},unregisterCustomCapabilitiesFilter:function(e){m(E,e)},unregisterLicenseRequestFilter:function(e){m(l,e)},unregisterLicenseResponseFilter:function(e){m(u,e)}},i={default:!1},T(),e}c.__dashjs_factory_name="CustomParametersModel",t.A=r.A.getSingletonFactory(c)},7377:function(e,t,i){"use strict";var n=i(138),r=i(656);function a(){return{customTimeRangeArray:[],length:0,add:function(e,t){let i;for(i=0;i<this.customTimeRangeArray.length&&e>this.customTimeRangeArray[i].start;i++);for(this.customTimeRangeArray.splice(i,0,{start:e,end:t}),i=0;i<this.customTimeRangeArray.length-1;i++)this.mergeRanges(i,i+1)&&i--;this.length=this.customTimeRangeArray.length},clear:function(){this.customTimeRangeArray=[],this.length=0},remove:function(e,t){for(let i=0;i<this.customTimeRangeArray.length;i++)if(e<=this.customTimeRangeArray[i].start&&t>=this.customTimeRangeArray[i].end)this.customTimeRangeArray.splice(i,1),i--;else{if(e>this.customTimeRangeArray[i].start&&t<this.customTimeRangeArray[i].end){this.customTimeRangeArray.splice(i+1,0,{start:t,end:this.customTimeRangeArray[i].end}),this.customTimeRangeArray[i].end=e;break}e>this.customTimeRangeArray[i].start&&e<this.customTimeRangeArray[i].end?this.customTimeRangeArray[i].end=e:t>this.customTimeRangeArray[i].start&&t<this.customTimeRangeArray[i].end&&(this.customTimeRangeArray[i].start=t)}this.length=this.customTimeRangeArray.length},mergeRanges:function(e,t){let i=this.customTimeRangeArray[e],n=this.customTimeRangeArray[t];return i.start<=n.start&&n.start<=i.end&&i.end<=n.end?(i.end=n.end,this.customTimeRangeArray.splice(t,1),!0):n.start<=i.start&&i.start<=n.end&&n.end<=i.end?(i.start=n.start,this.customTimeRangeArray.splice(t,1),!0):n.start<=i.start&&i.start<=n.end&&i.end<=n.end?(this.customTimeRangeArray.splice(e,1),!0):i.start<=n.start&&n.start<=i.end&&n.end<=i.end&&(this.customTimeRangeArray.splice(t,1),!0)},start:function(e){return(0,r.zQ)(e),e>=this.customTimeRangeArray.length||e<0?NaN:this.customTimeRangeArray[e].start},end:function(e){return(0,r.zQ)(e),e>=this.customTimeRangeArray.length||e<0?NaN:this.customTimeRangeArray[e].end}}}a.__dashjs_factory_name="CustomTimeRanges",t.A=n.A.getClassFactory(a)},656:function(e,t,i){"use strict";i.d(t,{sq:function(){return r},zQ:function(){return a}});var n=i(5212);function r(e,t){if(typeof e!==t)throw n.A.BAD_ARGUMENT_ERROR}function a(e){if(null===e||isNaN(e)||e%1!=0)throw n.A.BAD_ARGUMENT_ERROR+" : argument is not an integer"}},7568:function(e,t,i){"use strict";i.d(t,{G:function(){return n}});class n{constructor(){this.tcpid=null,this.type=null,this.url=null,this.actualurl=null,this.range=null,this.trequest=null,this.tresponse=null,this.responsecode=null,this.interval=null,this.trace=[],this.cmsd=null,this._stream=null,this._tfinish=null,this._mediaduration=null,this._responseHeaders=null,this._serviceLocation=null,this._fileLoaderType=null,this._resourceTimingValues=null}}n.GET="GET",n.HEAD="HEAD",n.MPD_TYPE="MPD",n.XLINK_EXPANSION_TYPE="XLinkExpansion",n.INIT_SEGMENT_TYPE="InitializationSegment",n.INDEX_SEGMENT_TYPE="IndexSegment",n.MEDIA_SEGMENT_TYPE="MediaSegment",n.BITSTREAM_SWITCHING_SEGMENT_TYPE="BitstreamSwitchingSegment",n.MSS_FRAGMENT_INFO_SEGMENT_TYPE="FragmentInfoSegment",n.DVB_REPORTING_TYPE="DVBReporting",n.LICENSE="license",n.CONTENT_STEERING_TYPE="ContentSteering",n.OTHER_TYPE="other"}},t={};function i(n){var r=t[n];if(void 0!==r)return r.exports;var a=t[n]={exports:{}};return e[n].call(a.exports,a,a.exports,i),a.exports}i.amdO={},i.d=function(e,t){for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};var n={};return function(){"use strict";i.d(n,{default:function(){return W}});class e{constructor(){this.mpdurl=null,this.errorcode=null,this.terror=null,this.url=null,this.ipaddress=null,this.servicelocation=null}}e.SSL_CONNECTION_FAILED_PREFIX="SSL",e.DNS_RESOLUTION_FAILED="C00",e.HOST_UNREACHABLE="C01",e.CONNECTION_REFUSED="C02",e.CONNECTION_ERROR="C03",e.CORRUPT_MEDIA_ISOBMFF="M00",e.CORRUPT_MEDIA_OTHER="M01",e.BASE_URL_CHANGED="F00",e.BECAME_REPORTER="S00";var t=e,r=i(913),a=i(138);function o(e){let i,n;const a=(e=e||{}).eventBus,o=e.dashMetrics,s=e.metricsConstants,c=e.events;function l(e){let i=new t;if(n){for(const t in e)e.hasOwnProperty(t)&&(i[t]=e[t]);i.mpdurl||(i.mpdurl=n.originalUrl||n.url),i.terror||(i.terror=new Date),o.addDVBErrors(i)}}function u(e){e.error||(n=e.manifest)}function E(e){l({errorcode:t.BASE_URL_CHANGED,servicelocation:e.entry})}function d(){l({errorcode:t.BECAME_REPORTER})}function _(e){var i;e.metric===s.HTTP_REQUEST&&(0===(i=e.value).responsecode||null==i.responsecode||i.responsecode>=400||i.responsecode<100||i.responsecode>=600)&&l({errorcode:i.responsecode||t.CONNECTION_ERROR,url:i.url,terror:i.tresponse,servicelocation:i._serviceLocation})}function A(e){let i;switch(e.error?e.error.code:0){case MediaError.MEDIA_ERR_NETWORK:i=t.CONNECTION_ERROR;break;case MediaError.MEDIA_ERR_DECODE:i=t.CORRUPT_MEDIA_OTHER;break;default:return}l({errorcode:i})}return i={initialize:function(){a.on(c.MANIFEST_UPDATED,u,i),a.on(c.SERVICE_LOCATION_BASE_URL_BLACKLIST_CHANGED,E,i),a.on(c.METRIC_ADDED,_,i),a.on(c.METRIC_UPDATED,_,i),a.on(c.PLAYBACK_ERROR,A,i),a.on(r.A.BECAME_REPORTING_PLAYER,d,i)},reset:function(){a.off(c.MANIFEST_UPDATED,u,i),a.off(c.SERVICE_LOCATION_BASE_URL_BLACKLIST_CHANGED,E,i),a.off(c.METRIC_ADDED,_,i),a.off(c.METRIC_UPDATED,_,i),a.off(c.PLAYBACK_ERROR,A,i),a.off(r.A.BECAME_REPORTING_PLAYER,d,i)}},i}o.__dashjs_factory_name="DVBErrorsTranslator";var s=a.A.getSingletonFactory(o),c=i(7377);function l(e){e=e||{};let t,i,n=!1,r=this.context,a=e.mediaElement;return t={initialize:function(e){e&&e.length&&(e.forEach((e=>{let t=e.starttime,n=t+e.duration;i.add(t,n)})),n=!!e[0]._useWallClockTime)},reset:function(){i.clear()},isEnabled:function(){let e,t=i.length;if(!t)return!0;e=n?(new Date).getTime()/1e3:a.currentTime;for(let n=0;n<t;n+=1){let t=i.start(n),r=i.end(n);if(t<=e&&e<r)return!0}return!1}},i=(0,c.A)(r).create(),t}l.__dashjs_factory_name="RangeController";var u=a.A.getClassFactory(l);function E(){return{serialise:function e(t){let i,n,r=[],a=[];for(i in t)if(t.hasOwnProperty(i)&&0!==i.indexOf("_")){if(n=t[i],null==n&&(n=""),Array.isArray(n)){if(!n.length)continue;a=[],n.forEach((function(t){let i="Object"!==Object.prototype.toString.call(t).slice(8,-1);a.push(i?t:e(t))})),n=a.map(encodeURIComponent).join(",")}else"string"==typeof n?n=encodeURIComponent(n):n instanceof Date?n=n.toISOString():"number"==typeof n&&(n=Math.round(n));r.push(i+"="+n)}return r.join("&")}}}E.__dashjs_factory_name="MetricSerialiser";var d=a.A.getSingletonFactory(E);function _(){let e,t,i,n=window.crypto||window.msCrypto,r=Uint32Array,a=Math.pow(2,8*r.BYTES_PER_ELEMENT)-1;function o(){n&&(e||(e=new r(10)),n.getRandomValues(e),t=0)}return i={random:function(i,r){let s;return i||(i=0),r||(r=1),n?(t===e.length&&o(),s=e[t]/a,t+=1):s=Math.random(),s*(r-i)+i}},o(),i}_.__dashjs_factory_name="RNG";var A=a.A.getSingletonFactory(_),h=i(1926);function T(e){let t;e=e||{};let i,n,r,a,o,s,c,l=this.context,u=[];const E=e.metricsConstants;function _(){a=!1,o=!1,s=null,c=null}return t={report:function(e,t){Array.isArray(t)||(t=[t]),o&&c.isEnabled()&&t.forEach((function(t){let r=i.serialise(t);e!==E.DVB_ERRORS&&(r=`metricname=${e}&${r}`),r=`${s}?${r}`,function(e,t,i){let r=new XMLHttpRequest;r.withCredentials=n.getXHRWithCredentialsForType(E.HTTP_REQUEST_DVB_REPORTING_TYPE);const a=function(){let e=u.indexOf(r);-1!==e&&(u.splice(e,1),!(r.status>=200&&r.status<300)&&(i&&i()))};u.push(r);try{r.open("GET",e),r.onloadend=a,r.onerror=a,r.send()}catch(e){r.onerror()}}(r,0,(function(){o=!1}))}))},initialize:function(e,t){let i;if(c=t,s=e.dvbReportingUrl,!s)throw new Error("required parameter missing (dvb:reportingUrl)");a||(i=e.dvbProbability,i&&(1e3===i||i/1e3>=r.random())&&(o=!0),a=!0)},reset:function(){_()}},i=d(l).getInstance(),r=A(l).getInstance(),n=(0,h.A)(l).getInstance(),_(),t}T.__dashjs_factory_name="DVBReporting";var m=a.A.getClassFactory(T);function f(e){e=e||{};const t={"urn:dvb:dash:reporting:2014":m},i=this.context;let n;const r=e.debug?e.debug.getLogger(n):{},a=e.metricsConstants,o=e.mediaPlayerModel||{};return n={create:function(e,n){let s;try{s=t[e.schemeIdUri](i).create({metricsConstants:a,mediaPlayerModel:o}),s.initialize(e,n)}catch(t){s=null,r.error(`ReportingFactory: could not create Reporting with schemeIdUri ${e.schemeIdUri} (${t.message})`)}return s},register:function(e,i){t[e]=i},unregister:function(e){delete t[e]}},n}f.__dashjs_factory_name="ReportingFactory";var g=a.A.getSingletonFactory(f);function p(e){let t,i=[];const n=g(this.context).getInstance(e);return t={initialize:function(e,t){e.some((e=>{let r=n.create(e,t);if(r)return i.push(r),!0}))},reset:function(){i.forEach((e=>e.reset())),i=[]},report:function(e,t){i.forEach((i=>i.report(e,t)))}},t}p.__dashjs_factory_name="ReportingController";var R=a.A.getClassFactory(p);function I(){return{reconstructFullMetricName:function(e,t,i){let n=e;return t&&(n+="("+t,i&&i.length&&(n+=","+i),n+=")"),n},validateN:function(e){if(!e)throw new Error("missing n");if(isNaN(e))throw new Error("n is NaN");if(e<0)throw new Error("n must be positive");return e}}}I.__dashjs_factory_name="HandlerHelpers";var C=a.A.getSingletonFactory(I);function b(e){let t,i,n,r,a,o;e=e||{};let s=this.context,c=C(s).getInstance(),l=[];const u=e.metricsConstants;function E(){let e=function(){try{return Object.keys(l).map((e=>l[e])).reduce(((e,t)=>e.level<t.level?e:t))}catch(e){return}}();e&&o!==e.t&&(o=e.t,i.report(r,e))}return t={initialize:function(e,t,o){t&&(n=c.validateN(o),i=t,r=c.reconstructFullMetricName(e,o),a=setInterval(E,n))},reset:function(){clearInterval(a),a=null,n=0,i=null,o=null},handleNewMetric:function(e,t,i){e===u.BUFFER_LEVEL&&(l[i]=t)}},t}b.__dashjs_factory_name="BufferLevelHandler";var S=a.A.getClassFactory(b),D=a.A.getClassFactory((function(e){let t,i,n=(e=e||{}).eventBus;const a=e.metricsConstants;function o(){n.off(r.A.METRICS_INITIALISATION_COMPLETE,o,this),n.trigger(r.A.BECAME_REPORTING_PLAYER)}return t={initialize:function(e,t){t&&(i=t,n.on(r.A.METRICS_INITIALISATION_COMPLETE,o,this))},reset:function(){i=null},handleNewMetric:function(e,t){e===a.DVB_ERRORS&&i&&i.report(e,t)}},t}));function w(e){let t,i,n,r,a,o;e=e||{};let s=[],c=C(this.context).getInstance();const l=e.metricsConstants;function u(){var e=s;e.length&&i&&i.report(a,e),s=[]}return t={initialize:function(e,t,s,l){t&&(n=c.validateN(s),i=t,l&&l.length&&(r=l),a=c.reconstructFullMetricName(e,s,l),o=setInterval(u,n))},reset:function(){clearInterval(o),o=null,n=null,r=null,s=[],i=null},handleNewMetric:function(e,t){e===l.HTTP_REQUEST&&(r&&r!==t.type||s.push(t))}},t}w.__dashjs_factory_name="HttpListHandler";var N=a.A.getClassFactory(w);function y(){let e,t,i;return e={initialize:function(e,n){t=e,i=n},reset:function(){i=null,t=void 0},handleNewMetric:function(e,n){e===t&&i&&i.report(t,n)}},e}y.__dashjs_factory_name="GenericMetricHandler";var L=a.A.getClassFactory(y);function M(e){let t;const i=(e=e||{}).debug?e.debug.getLogger(t):{};let n=/([a-zA-Z]*)(\(([0-9]*)(\,\s*([a-zA-Z]*))?\))?/;const r=this.context;let a={BufferLevel:S,DVBErrors:D,HttpList:N,PlayList:L,RepSwitchList:L,TcpList:L};return t={create:function(t,o){var s,c=t.match(n);if(c){try{(s=a[c[1]](r).create({eventBus:e.eventBus,metricsConstants:e.metricsConstants})).initialize(c[1],o,c[3],c[5])}catch(e){s=null,i.error(`MetricsHandlerFactory: Could not create handler for type ${c[1]} with args ${c[3]}, ${c[5]} (${e.message})`)}return s}},register:function(e,t){a[e]=t},unregister:function(e){delete a[e]}},t}M.__dashjs_factory_name="MetricsHandlerFactory";var v=a.A.getSingletonFactory(M);function O(e){e=e||{};let t,i=[];const n=this.context,r=e.eventBus,a=e.events;let o=v(n).getInstance({debug:e.debug,eventBus:e.eventBus,metricsConstants:e.metricsConstants});function s(e){i.forEach((t=>{t.handleNewMetric(e.metric,e.value,e.mediaType)}))}return t={initialize:function(e,n){e.split(",").forEach(((e,t,r)=>{let a;if(-1!==e.indexOf("(")&&-1===e.indexOf(")")){let i=r[t+1];i&&-1===i.indexOf("(")&&-1!==i.indexOf(")")&&(e+=","+i,delete r[t+1])}a=o.create(e,n),a&&i.push(a)})),r.on(a.METRIC_ADDED,s,t),r.on(a.METRIC_UPDATED,s,t)},reset:function(){r.off(a.METRIC_ADDED,s,t),r.off(a.METRIC_UPDATED,s,t),i.forEach((e=>e.reset())),i=[]}},t}O.__dashjs_factory_name="MetricsHandlersController";var P=a.A.getClassFactory(O);function U(e){let t,i,n,r;e=e||{};let a=this.context;function o(){t&&t.reset(),i&&i.reset(),n&&n.reset()}return r={initialize:function(r){try{n=u(a).create({mediaElement:e.mediaElement}),n.initialize(r.Range),i=R(a).create({debug:e.debug,metricsConstants:e.metricsConstants,mediaPlayerModel:e.mediaPlayerModel}),i.initialize(r.Reporting,n),t=P(a).create({debug:e.debug,eventBus:e.eventBus,metricsConstants:e.metricsConstants,events:e.events}),t.initialize(r.metrics,i)}catch(e){throw o(),e}},reset:o},r}U.__dashjs_factory_name="MetricsController";var F=a.A.getClassFactory(U),x=class{constructor(){this.metrics="",this.Range=[],this.Reporting=[]}},B=class{constructor(){this.starttime=0,this.duration=1/0,this._useWallClockTime=!1}},G=class{constructor(){this.schemeIdUri="",this.value="",this.dvbReportingUrl="",this.dvbProbability=1e3}};function k(e){let t,i=(e=e||{}).adapter;const n=e.constants;return t={getMetrics:function(e){let t=[];return e&&e.Metrics&&e.Metrics.forEach((r=>{var a=new x,o=i.getIsDynamic(e);r.hasOwnProperty("metrics")&&(a.metrics=r.metrics,r.Range&&r.Range.forEach((t=>{var r=new B;r.starttime=function(e,t,r){let a,o,s=0;return t?s=i.getAvailabilityStartTime(e)/1e3:(a=i.getRegularPeriods(e),a.length&&(s=a[0].start)),o=s,r&&r.hasOwnProperty(n.START_TIME)&&(o+=r.starttime),o}(e,o,t),t.hasOwnProperty("duration")?r.duration=t.duration:r.duration=i.getDuration(e),r._useWallClockTime=o,a.Range.push(r)})),r.Reporting&&(r.Reporting.forEach((e=>{var t=new G;e.hasOwnProperty(n.SCHEME_ID_URI)&&(t.schemeIdUri=e.schemeIdUri,e.hasOwnProperty("value")&&(t.value=e.value),e.hasOwnProperty(n.DVB_REPORTING_URL)&&(t.dvbReportingUrl=e[n.DVB_REPORTING_URL]),e.hasOwnProperty(n.DVB_PROBABILITY)&&(t.dvbProbability=e[n.DVB_PROBABILITY]),a.Reporting.push(t))})),t.push(a)))})),t}},t}k.__dashjs_factory_name="ManifestParsing";var H=a.A.getSingletonFactory(k);function V(e){let t;e=e||{};let i={},n=this.context,a=e.eventBus;const o=e.events;function s(t){if(t.error)return;let o=Object.keys(i);H(n).getInstance({adapter:e.adapter,constants:e.constants}).getMetrics(t.manifest).forEach((t=>{const r=JSON.stringify(t);if(i.hasOwnProperty(r))o.splice(r,1);else try{let a=F(n).create(e);a.initialize(t),i[r]=a}catch(e){}})),o.forEach((e=>{i[e].reset(),delete i[e]})),a.trigger(r.A.METRICS_INITIALISATION_COMPLETE)}function c(){Object.keys(i).forEach((e=>{i[e].reset()})),i={}}return t={reset:function(){a.off(o.MANIFEST_UPDATED,s,t),a.off(o.STREAM_TEARDOWN_COMPLETE,c,t)}},a.on(o.MANIFEST_UPDATED,s,t),a.on(o.STREAM_TEARDOWN_COMPLETE,c,t),t}V.__dashjs_factory_name="MetricsCollectionController";var Y=a.A.getClassFactory(V);function j(){let e,t,i=this.context;return e={createMetricsReporting:function(e){return t=s(i).getInstance({eventBus:e.eventBus,dashMetrics:e.dashMetrics,metricsConstants:e.metricsConstants,events:e.events}),t.initialize(),Y(i).create(e)},getReportingFactory:function(){return g(i).getInstance()},getMetricsHandlerFactory:function(){return v(i).getInstance()}},e}j.__dashjs_factory_name="MetricsReporting";const K=dashjs.FactoryMaker.getClassFactory(j);K.events=r.A,dashjs.FactoryMaker.updateClassFactory(j.__dashjs_factory_name,K);var W=K}(),n.default}()}));
//# sourceMappingURL=dash.reporting.min.js.map