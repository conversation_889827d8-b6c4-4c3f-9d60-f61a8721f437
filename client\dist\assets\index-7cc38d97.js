function Fg(e,t){for(var r=0;r<t.length;r++){const n=t[r];if(typeof n!="string"&&!Array.isArray(n)){for(const s in n)if(s!=="default"&&!(s in e)){const i=Object.getOwnPropertyDescriptor(n,s);i&&Object.defineProperty(e,s,i.get?i:{enumerable:!0,get:()=>n[s]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))n(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&n(l)}).observe(document,{childList:!0,subtree:!0});function r(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function n(s){if(s.ep)return;s.ep=!0;const i=r(s);fetch(s.href,i)}})();function nm(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var sm={exports:{}},Qa={},im={exports:{}},ie={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var di=Symbol.for("react.element"),Dg=Symbol.for("react.portal"),Ig=Symbol.for("react.fragment"),Mg=Symbol.for("react.strict_mode"),$g=Symbol.for("react.profiler"),zg=Symbol.for("react.provider"),Ug=Symbol.for("react.context"),Bg=Symbol.for("react.forward_ref"),Vg=Symbol.for("react.suspense"),Wg=Symbol.for("react.memo"),Hg=Symbol.for("react.lazy"),Uu=Symbol.iterator;function Zg(e){return e===null||typeof e!="object"?null:(e=Uu&&e[Uu]||e["@@iterator"],typeof e=="function"?e:null)}var am={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},lm=Object.assign,om={};function ns(e,t,r){this.props=e,this.context=t,this.refs=om,this.updater=r||am}ns.prototype.isReactComponent={};ns.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};ns.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function cm(){}cm.prototype=ns.prototype;function vc(e,t,r){this.props=e,this.context=t,this.refs=om,this.updater=r||am}var wc=vc.prototype=new cm;wc.constructor=vc;lm(wc,ns.prototype);wc.isPureReactComponent=!0;var Bu=Array.isArray,um=Object.prototype.hasOwnProperty,bc={current:null},dm={key:!0,ref:!0,__self:!0,__source:!0};function fm(e,t,r){var n,s={},i=null,l=null;if(t!=null)for(n in t.ref!==void 0&&(l=t.ref),t.key!==void 0&&(i=""+t.key),t)um.call(t,n)&&!dm.hasOwnProperty(n)&&(s[n]=t[n]);var o=arguments.length-2;if(o===1)s.children=r;else if(1<o){for(var c=Array(o),u=0;u<o;u++)c[u]=arguments[u+2];s.children=c}if(e&&e.defaultProps)for(n in o=e.defaultProps,o)s[n]===void 0&&(s[n]=o[n]);return{$$typeof:di,type:e,key:i,ref:l,props:s,_owner:bc.current}}function Gg(e,t){return{$$typeof:di,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function jc(e){return typeof e=="object"&&e!==null&&e.$$typeof===di}function qg(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(r){return t[r]})}var Vu=/\/+/g;function _l(e,t){return typeof e=="object"&&e!==null&&e.key!=null?qg(""+e.key):t.toString(36)}function Vi(e,t,r,n,s){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var l=!1;if(e===null)l=!0;else switch(i){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case di:case Dg:l=!0}}if(l)return l=e,s=s(l),e=n===""?"."+_l(l,0):n,Bu(s)?(r="",e!=null&&(r=e.replace(Vu,"$&/")+"/"),Vi(s,t,r,"",function(u){return u})):s!=null&&(jc(s)&&(s=Gg(s,r+(!s.key||l&&l.key===s.key?"":(""+s.key).replace(Vu,"$&/")+"/")+e)),t.push(s)),1;if(l=0,n=n===""?".":n+":",Bu(e))for(var o=0;o<e.length;o++){i=e[o];var c=n+_l(i,o);l+=Vi(i,t,r,c,s)}else if(c=Zg(e),typeof c=="function")for(e=c.call(e),o=0;!(i=e.next()).done;)i=i.value,c=n+_l(i,o++),l+=Vi(i,t,r,c,s);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function Si(e,t,r){if(e==null)return e;var n=[],s=0;return Vi(e,n,"","",function(i){return t.call(r,i,s++)}),n}function Qg(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(r){(e._status===0||e._status===-1)&&(e._status=1,e._result=r)},function(r){(e._status===0||e._status===-1)&&(e._status=2,e._result=r)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var tt={current:null},Wi={transition:null},Jg={ReactCurrentDispatcher:tt,ReactCurrentBatchConfig:Wi,ReactCurrentOwner:bc};function mm(){throw Error("act(...) is not supported in production builds of React.")}ie.Children={map:Si,forEach:function(e,t,r){Si(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return Si(e,function(){t++}),t},toArray:function(e){return Si(e,function(t){return t})||[]},only:function(e){if(!jc(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};ie.Component=ns;ie.Fragment=Ig;ie.Profiler=$g;ie.PureComponent=vc;ie.StrictMode=Mg;ie.Suspense=Vg;ie.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Jg;ie.act=mm;ie.cloneElement=function(e,t,r){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var n=lm({},e.props),s=e.key,i=e.ref,l=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,l=bc.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var o=e.type.defaultProps;for(c in t)um.call(t,c)&&!dm.hasOwnProperty(c)&&(n[c]=t[c]===void 0&&o!==void 0?o[c]:t[c])}var c=arguments.length-2;if(c===1)n.children=r;else if(1<c){o=Array(c);for(var u=0;u<c;u++)o[u]=arguments[u+2];n.children=o}return{$$typeof:di,type:e.type,key:s,ref:i,props:n,_owner:l}};ie.createContext=function(e){return e={$$typeof:Ug,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:zg,_context:e},e.Consumer=e};ie.createElement=fm;ie.createFactory=function(e){var t=fm.bind(null,e);return t.type=e,t};ie.createRef=function(){return{current:null}};ie.forwardRef=function(e){return{$$typeof:Bg,render:e}};ie.isValidElement=jc;ie.lazy=function(e){return{$$typeof:Hg,_payload:{_status:-1,_result:e},_init:Qg}};ie.memo=function(e,t){return{$$typeof:Wg,type:e,compare:t===void 0?null:t}};ie.startTransition=function(e){var t=Wi.transition;Wi.transition={};try{e()}finally{Wi.transition=t}};ie.unstable_act=mm;ie.useCallback=function(e,t){return tt.current.useCallback(e,t)};ie.useContext=function(e){return tt.current.useContext(e)};ie.useDebugValue=function(){};ie.useDeferredValue=function(e){return tt.current.useDeferredValue(e)};ie.useEffect=function(e,t){return tt.current.useEffect(e,t)};ie.useId=function(){return tt.current.useId()};ie.useImperativeHandle=function(e,t,r){return tt.current.useImperativeHandle(e,t,r)};ie.useInsertionEffect=function(e,t){return tt.current.useInsertionEffect(e,t)};ie.useLayoutEffect=function(e,t){return tt.current.useLayoutEffect(e,t)};ie.useMemo=function(e,t){return tt.current.useMemo(e,t)};ie.useReducer=function(e,t,r){return tt.current.useReducer(e,t,r)};ie.useRef=function(e){return tt.current.useRef(e)};ie.useState=function(e){return tt.current.useState(e)};ie.useSyncExternalStore=function(e,t,r){return tt.current.useSyncExternalStore(e,t,r)};ie.useTransition=function(){return tt.current.useTransition()};ie.version="18.3.1";im.exports=ie;var j=im.exports;const Ne=nm(j),Kg=Fg({__proto__:null,default:Ne},[j]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yg=j,Xg=Symbol.for("react.element"),e0=Symbol.for("react.fragment"),t0=Object.prototype.hasOwnProperty,r0=Yg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,n0={key:!0,ref:!0,__self:!0,__source:!0};function hm(e,t,r){var n,s={},i=null,l=null;r!==void 0&&(i=""+r),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(l=t.ref);for(n in t)t0.call(t,n)&&!n0.hasOwnProperty(n)&&(s[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps,t)s[n]===void 0&&(s[n]=t[n]);return{$$typeof:Xg,type:e,key:i,ref:l,props:s,_owner:r0.current}}Qa.Fragment=e0;Qa.jsx=hm;Qa.jsxs=hm;sm.exports=Qa;var a=sm.exports,ao={},pm={exports:{}},pt={},gm={exports:{}},xm={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(T,U){var D=T.length;T.push(U);e:for(;0<D;){var K=D-1>>>1,me=T[K];if(0<s(me,U))T[K]=U,T[D]=me,D=K;else break e}}function r(T){return T.length===0?null:T[0]}function n(T){if(T.length===0)return null;var U=T[0],D=T.pop();if(D!==U){T[0]=D;e:for(var K=0,me=T.length,yt=me>>>1;K<yt;){var Mt=2*(K+1)-1,Jt=T[Mt],Kt=Mt+1,pr=T[Kt];if(0>s(Jt,D))Kt<me&&0>s(pr,Jt)?(T[K]=pr,T[Kt]=D,K=Kt):(T[K]=Jt,T[Mt]=D,K=Mt);else if(Kt<me&&0>s(pr,D))T[K]=pr,T[Kt]=D,K=Kt;else break e}}return U}function s(T,U){var D=T.sortIndex-U.sortIndex;return D!==0?D:T.id-U.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var l=Date,o=l.now();e.unstable_now=function(){return l.now()-o}}var c=[],u=[],d=1,f=null,h=3,w=!1,p=!1,y=!1,b=typeof setTimeout=="function"?setTimeout:null,x=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(T){for(var U=r(u);U!==null;){if(U.callback===null)n(u);else if(U.startTime<=T)n(u),U.sortIndex=U.expirationTime,t(c,U);else break;U=r(u)}}function N(T){if(y=!1,g(T),!p)if(r(c)!==null)p=!0,xe(_);else{var U=r(u);U!==null&&V(N,U.startTime-T)}}function _(T,U){p=!1,y&&(y=!1,x(A),A=-1),w=!0;var D=h;try{for(g(U),f=r(c);f!==null&&(!(f.expirationTime>U)||T&&!ue());){var K=f.callback;if(typeof K=="function"){f.callback=null,h=f.priorityLevel;var me=K(f.expirationTime<=U);U=e.unstable_now(),typeof me=="function"?f.callback=me:f===r(c)&&n(c),g(U)}else n(c);f=r(c)}if(f!==null)var yt=!0;else{var Mt=r(u);Mt!==null&&V(N,Mt.startTime-U),yt=!1}return yt}finally{f=null,h=D,w=!1}}var R=!1,E=null,A=-1,q=5,Z=-1;function ue(){return!(e.unstable_now()-Z<q)}function ee(){if(E!==null){var T=e.unstable_now();Z=T;var U=!0;try{U=E(!0,T)}finally{U?re():(R=!1,E=null)}}else R=!1}var re;if(typeof m=="function")re=function(){m(ee)};else if(typeof MessageChannel<"u"){var ae=new MessageChannel,je=ae.port2;ae.port1.onmessage=ee,re=function(){je.postMessage(null)}}else re=function(){b(ee,0)};function xe(T){E=T,R||(R=!0,re())}function V(T,U){A=b(function(){T(e.unstable_now())},U)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(T){T.callback=null},e.unstable_continueExecution=function(){p||w||(p=!0,xe(_))},e.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):q=0<T?Math.floor(1e3/T):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return r(c)},e.unstable_next=function(T){switch(h){case 1:case 2:case 3:var U=3;break;default:U=h}var D=h;h=U;try{return T()}finally{h=D}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(T,U){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var D=h;h=T;try{return U()}finally{h=D}},e.unstable_scheduleCallback=function(T,U,D){var K=e.unstable_now();switch(typeof D=="object"&&D!==null?(D=D.delay,D=typeof D=="number"&&0<D?K+D:K):D=K,T){case 1:var me=-1;break;case 2:me=250;break;case 5:me=**********;break;case 4:me=1e4;break;default:me=5e3}return me=D+me,T={id:d++,callback:U,priorityLevel:T,startTime:D,expirationTime:me,sortIndex:-1},D>K?(T.sortIndex=D,t(u,T),r(c)===null&&T===r(u)&&(y?(x(A),A=-1):y=!0,V(N,D-K))):(T.sortIndex=me,t(c,T),p||w||(p=!0,xe(_))),T},e.unstable_shouldYield=ue,e.unstable_wrapCallback=function(T){var U=h;return function(){var D=h;h=U;try{return T.apply(this,arguments)}finally{h=D}}}})(xm);gm.exports=xm;var s0=gm.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var i0=j,mt=s0;function P(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var ym=new Set,zs={};function xn(e,t){Bn(e,t),Bn(e+"Capture",t)}function Bn(e,t){for(zs[e]=t,e=0;e<t.length;e++)ym.add(t[e])}var ar=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),lo=Object.prototype.hasOwnProperty,a0=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Wu={},Hu={};function l0(e){return lo.call(Hu,e)?!0:lo.call(Wu,e)?!1:a0.test(e)?Hu[e]=!0:(Wu[e]=!0,!1)}function o0(e,t,r,n){if(r!==null&&r.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return n?!1:r!==null?!r.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function c0(e,t,r,n){if(t===null||typeof t>"u"||o0(e,t,r,n))return!0;if(n)return!1;if(r!==null)switch(r.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function rt(e,t,r,n,s,i,l){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=n,this.attributeNamespace=s,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=l}var Ve={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ve[e]=new rt(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ve[t]=new rt(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ve[e]=new rt(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ve[e]=new rt(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ve[e]=new rt(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ve[e]=new rt(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ve[e]=new rt(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ve[e]=new rt(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ve[e]=new rt(e,5,!1,e.toLowerCase(),null,!1,!1)});var Nc=/[\-:]([a-z])/g;function kc(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Nc,kc);Ve[t]=new rt(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Nc,kc);Ve[t]=new rt(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Nc,kc);Ve[t]=new rt(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ve[e]=new rt(e,1,!1,e.toLowerCase(),null,!1,!1)});Ve.xlinkHref=new rt("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ve[e]=new rt(e,1,!1,e.toLowerCase(),null,!0,!0)});function Sc(e,t,r,n){var s=Ve.hasOwnProperty(t)?Ve[t]:null;(s!==null?s.type!==0:n||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(c0(t,r,s,n)&&(r=null),n||s===null?l0(t)&&(r===null?e.removeAttribute(t):e.setAttribute(t,""+r)):s.mustUseProperty?e[s.propertyName]=r===null?s.type===3?!1:"":r:(t=s.attributeName,n=s.attributeNamespace,r===null?e.removeAttribute(t):(s=s.type,r=s===3||s===4&&r===!0?"":""+r,n?e.setAttributeNS(n,t,r):e.setAttribute(t,r))))}var mr=i0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,_i=Symbol.for("react.element"),kn=Symbol.for("react.portal"),Sn=Symbol.for("react.fragment"),_c=Symbol.for("react.strict_mode"),oo=Symbol.for("react.profiler"),vm=Symbol.for("react.provider"),wm=Symbol.for("react.context"),Cc=Symbol.for("react.forward_ref"),co=Symbol.for("react.suspense"),uo=Symbol.for("react.suspense_list"),Ec=Symbol.for("react.memo"),vr=Symbol.for("react.lazy"),bm=Symbol.for("react.offscreen"),Zu=Symbol.iterator;function fs(e){return e===null||typeof e!="object"?null:(e=Zu&&e[Zu]||e["@@iterator"],typeof e=="function"?e:null)}var _e=Object.assign,Cl;function Ns(e){if(Cl===void 0)try{throw Error()}catch(r){var t=r.stack.trim().match(/\n( *(at )?)/);Cl=t&&t[1]||""}return`
`+Cl+e}var El=!1;function Pl(e,t){if(!e||El)return"";El=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var n=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){n=u}e.call(t.prototype)}else{try{throw Error()}catch(u){n=u}e()}}catch(u){if(u&&n&&typeof u.stack=="string"){for(var s=u.stack.split(`
`),i=n.stack.split(`
`),l=s.length-1,o=i.length-1;1<=l&&0<=o&&s[l]!==i[o];)o--;for(;1<=l&&0<=o;l--,o--)if(s[l]!==i[o]){if(l!==1||o!==1)do if(l--,o--,0>o||s[l]!==i[o]){var c=`
`+s[l].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=l&&0<=o);break}}}finally{El=!1,Error.prepareStackTrace=r}return(e=e?e.displayName||e.name:"")?Ns(e):""}function u0(e){switch(e.tag){case 5:return Ns(e.type);case 16:return Ns("Lazy");case 13:return Ns("Suspense");case 19:return Ns("SuspenseList");case 0:case 2:case 15:return e=Pl(e.type,!1),e;case 11:return e=Pl(e.type.render,!1),e;case 1:return e=Pl(e.type,!0),e;default:return""}}function fo(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Sn:return"Fragment";case kn:return"Portal";case oo:return"Profiler";case _c:return"StrictMode";case co:return"Suspense";case uo:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case wm:return(e.displayName||"Context")+".Consumer";case vm:return(e._context.displayName||"Context")+".Provider";case Cc:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Ec:return t=e.displayName||null,t!==null?t:fo(e.type)||"Memo";case vr:t=e._payload,e=e._init;try{return fo(e(t))}catch{}}return null}function d0(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return fo(t);case 8:return t===_c?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Br(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function jm(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function f0(e){var t=jm(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),n=""+e[t];if(!e.hasOwnProperty(t)&&typeof r<"u"&&typeof r.get=="function"&&typeof r.set=="function"){var s=r.get,i=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(l){n=""+l,i.call(this,l)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return n},setValue:function(l){n=""+l},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ci(e){e._valueTracker||(e._valueTracker=f0(e))}function Nm(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),n="";return e&&(n=jm(e)?e.checked?"true":"false":e.value),e=n,e!==r?(t.setValue(e),!0):!1}function la(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function mo(e,t){var r=t.checked;return _e({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:r??e._wrapperState.initialChecked})}function Gu(e,t){var r=t.defaultValue==null?"":t.defaultValue,n=t.checked!=null?t.checked:t.defaultChecked;r=Br(t.value!=null?t.value:r),e._wrapperState={initialChecked:n,initialValue:r,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function km(e,t){t=t.checked,t!=null&&Sc(e,"checked",t,!1)}function ho(e,t){km(e,t);var r=Br(t.value),n=t.type;if(r!=null)n==="number"?(r===0&&e.value===""||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if(n==="submit"||n==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?po(e,t.type,r):t.hasOwnProperty("defaultValue")&&po(e,t.type,Br(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function qu(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var n=t.type;if(!(n!=="submit"&&n!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}r=e.name,r!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,r!==""&&(e.name=r)}function po(e,t,r){(t!=="number"||la(e.ownerDocument)!==e)&&(r==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}var ks=Array.isArray;function Dn(e,t,r,n){if(e=e.options,t){t={};for(var s=0;s<r.length;s++)t["$"+r[s]]=!0;for(r=0;r<e.length;r++)s=t.hasOwnProperty("$"+e[r].value),e[r].selected!==s&&(e[r].selected=s),s&&n&&(e[r].defaultSelected=!0)}else{for(r=""+Br(r),t=null,s=0;s<e.length;s++){if(e[s].value===r){e[s].selected=!0,n&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function go(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(P(91));return _e({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Qu(e,t){var r=t.value;if(r==null){if(r=t.children,t=t.defaultValue,r!=null){if(t!=null)throw Error(P(92));if(ks(r)){if(1<r.length)throw Error(P(93));r=r[0]}t=r}t==null&&(t=""),r=t}e._wrapperState={initialValue:Br(r)}}function Sm(e,t){var r=Br(t.value),n=Br(t.defaultValue);r!=null&&(r=""+r,r!==e.value&&(e.value=r),t.defaultValue==null&&e.defaultValue!==r&&(e.defaultValue=r)),n!=null&&(e.defaultValue=""+n)}function Ju(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function _m(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function xo(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?_m(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ei,Cm=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,r,n,s){MSApp.execUnsafeLocalFunction(function(){return e(t,r,n,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ei=Ei||document.createElement("div"),Ei.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ei.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Us(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&r.nodeType===3){r.nodeValue=t;return}}e.textContent=t}var Es={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},m0=["Webkit","ms","Moz","O"];Object.keys(Es).forEach(function(e){m0.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Es[t]=Es[e]})});function Em(e,t,r){return t==null||typeof t=="boolean"||t===""?"":r||typeof t!="number"||t===0||Es.hasOwnProperty(e)&&Es[e]?(""+t).trim():t+"px"}function Pm(e,t){e=e.style;for(var r in t)if(t.hasOwnProperty(r)){var n=r.indexOf("--")===0,s=Em(r,t[r],n);r==="float"&&(r="cssFloat"),n?e.setProperty(r,s):e[r]=s}}var h0=_e({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function yo(e,t){if(t){if(h0[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(P(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(P(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(P(61))}if(t.style!=null&&typeof t.style!="object")throw Error(P(62))}}function vo(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var wo=null;function Pc(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var bo=null,In=null,Mn=null;function Ku(e){if(e=hi(e)){if(typeof bo!="function")throw Error(P(280));var t=e.stateNode;t&&(t=el(t),bo(e.stateNode,e.type,t))}}function Tm(e){In?Mn?Mn.push(e):Mn=[e]:In=e}function Rm(){if(In){var e=In,t=Mn;if(Mn=In=null,Ku(e),t)for(e=0;e<t.length;e++)Ku(t[e])}}function Om(e,t){return e(t)}function Am(){}var Tl=!1;function Lm(e,t,r){if(Tl)return e(t,r);Tl=!0;try{return Om(e,t,r)}finally{Tl=!1,(In!==null||Mn!==null)&&(Am(),Rm())}}function Bs(e,t){var r=e.stateNode;if(r===null)return null;var n=el(r);if(n===null)return null;r=n[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(e=e.type,n=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!n;break e;default:e=!1}if(e)return null;if(r&&typeof r!="function")throw Error(P(231,t,typeof r));return r}var jo=!1;if(ar)try{var ms={};Object.defineProperty(ms,"passive",{get:function(){jo=!0}}),window.addEventListener("test",ms,ms),window.removeEventListener("test",ms,ms)}catch{jo=!1}function p0(e,t,r,n,s,i,l,o,c){var u=Array.prototype.slice.call(arguments,3);try{t.apply(r,u)}catch(d){this.onError(d)}}var Ps=!1,oa=null,ca=!1,No=null,g0={onError:function(e){Ps=!0,oa=e}};function x0(e,t,r,n,s,i,l,o,c){Ps=!1,oa=null,p0.apply(g0,arguments)}function y0(e,t,r,n,s,i,l,o,c){if(x0.apply(this,arguments),Ps){if(Ps){var u=oa;Ps=!1,oa=null}else throw Error(P(198));ca||(ca=!0,No=u)}}function yn(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(r=t.return),e=t.return;while(e)}return t.tag===3?r:null}function Fm(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Yu(e){if(yn(e)!==e)throw Error(P(188))}function v0(e){var t=e.alternate;if(!t){if(t=yn(e),t===null)throw Error(P(188));return t!==e?null:e}for(var r=e,n=t;;){var s=r.return;if(s===null)break;var i=s.alternate;if(i===null){if(n=s.return,n!==null){r=n;continue}break}if(s.child===i.child){for(i=s.child;i;){if(i===r)return Yu(s),e;if(i===n)return Yu(s),t;i=i.sibling}throw Error(P(188))}if(r.return!==n.return)r=s,n=i;else{for(var l=!1,o=s.child;o;){if(o===r){l=!0,r=s,n=i;break}if(o===n){l=!0,n=s,r=i;break}o=o.sibling}if(!l){for(o=i.child;o;){if(o===r){l=!0,r=i,n=s;break}if(o===n){l=!0,n=i,r=s;break}o=o.sibling}if(!l)throw Error(P(189))}}if(r.alternate!==n)throw Error(P(190))}if(r.tag!==3)throw Error(P(188));return r.stateNode.current===r?e:t}function Dm(e){return e=v0(e),e!==null?Im(e):null}function Im(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Im(e);if(t!==null)return t;e=e.sibling}return null}var Mm=mt.unstable_scheduleCallback,Xu=mt.unstable_cancelCallback,w0=mt.unstable_shouldYield,b0=mt.unstable_requestPaint,Pe=mt.unstable_now,j0=mt.unstable_getCurrentPriorityLevel,Tc=mt.unstable_ImmediatePriority,$m=mt.unstable_UserBlockingPriority,ua=mt.unstable_NormalPriority,N0=mt.unstable_LowPriority,zm=mt.unstable_IdlePriority,Ja=null,Zt=null;function k0(e){if(Zt&&typeof Zt.onCommitFiberRoot=="function")try{Zt.onCommitFiberRoot(Ja,e,void 0,(e.current.flags&128)===128)}catch{}}var At=Math.clz32?Math.clz32:C0,S0=Math.log,_0=Math.LN2;function C0(e){return e>>>=0,e===0?32:31-(S0(e)/_0|0)|0}var Pi=64,Ti=4194304;function Ss(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function da(e,t){var r=e.pendingLanes;if(r===0)return 0;var n=0,s=e.suspendedLanes,i=e.pingedLanes,l=r&268435455;if(l!==0){var o=l&~s;o!==0?n=Ss(o):(i&=l,i!==0&&(n=Ss(i)))}else l=r&~s,l!==0?n=Ss(l):i!==0&&(n=Ss(i));if(n===0)return 0;if(t!==0&&t!==n&&!(t&s)&&(s=n&-n,i=t&-t,s>=i||s===16&&(i&4194240)!==0))return t;if(n&4&&(n|=r&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=n;0<t;)r=31-At(t),s=1<<r,n|=e[r],t&=~s;return n}function E0(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function P0(e,t){for(var r=e.suspendedLanes,n=e.pingedLanes,s=e.expirationTimes,i=e.pendingLanes;0<i;){var l=31-At(i),o=1<<l,c=s[l];c===-1?(!(o&r)||o&n)&&(s[l]=E0(o,t)):c<=t&&(e.expiredLanes|=o),i&=~o}}function ko(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Um(){var e=Pi;return Pi<<=1,!(Pi&4194240)&&(Pi=64),e}function Rl(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function fi(e,t,r){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-At(t),e[t]=r}function T0(e,t){var r=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var n=e.eventTimes;for(e=e.expirationTimes;0<r;){var s=31-At(r),i=1<<s;t[s]=0,n[s]=-1,e[s]=-1,r&=~i}}function Rc(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var n=31-At(r),s=1<<n;s&t|e[n]&t&&(e[n]|=t),r&=~s}}var fe=0;function Bm(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Vm,Oc,Wm,Hm,Zm,So=!1,Ri=[],Tr=null,Rr=null,Or=null,Vs=new Map,Ws=new Map,jr=[],R0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function ed(e,t){switch(e){case"focusin":case"focusout":Tr=null;break;case"dragenter":case"dragleave":Rr=null;break;case"mouseover":case"mouseout":Or=null;break;case"pointerover":case"pointerout":Vs.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ws.delete(t.pointerId)}}function hs(e,t,r,n,s,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:r,eventSystemFlags:n,nativeEvent:i,targetContainers:[s]},t!==null&&(t=hi(t),t!==null&&Oc(t)),e):(e.eventSystemFlags|=n,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function O0(e,t,r,n,s){switch(t){case"focusin":return Tr=hs(Tr,e,t,r,n,s),!0;case"dragenter":return Rr=hs(Rr,e,t,r,n,s),!0;case"mouseover":return Or=hs(Or,e,t,r,n,s),!0;case"pointerover":var i=s.pointerId;return Vs.set(i,hs(Vs.get(i)||null,e,t,r,n,s)),!0;case"gotpointercapture":return i=s.pointerId,Ws.set(i,hs(Ws.get(i)||null,e,t,r,n,s)),!0}return!1}function Gm(e){var t=Xr(e.target);if(t!==null){var r=yn(t);if(r!==null){if(t=r.tag,t===13){if(t=Fm(r),t!==null){e.blockedOn=t,Zm(e.priority,function(){Wm(r)});return}}else if(t===3&&r.stateNode.current.memoizedState.isDehydrated){e.blockedOn=r.tag===3?r.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Hi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var r=_o(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(r===null){r=e.nativeEvent;var n=new r.constructor(r.type,r);wo=n,r.target.dispatchEvent(n),wo=null}else return t=hi(r),t!==null&&Oc(t),e.blockedOn=r,!1;t.shift()}return!0}function td(e,t,r){Hi(e)&&r.delete(t)}function A0(){So=!1,Tr!==null&&Hi(Tr)&&(Tr=null),Rr!==null&&Hi(Rr)&&(Rr=null),Or!==null&&Hi(Or)&&(Or=null),Vs.forEach(td),Ws.forEach(td)}function ps(e,t){e.blockedOn===t&&(e.blockedOn=null,So||(So=!0,mt.unstable_scheduleCallback(mt.unstable_NormalPriority,A0)))}function Hs(e){function t(s){return ps(s,e)}if(0<Ri.length){ps(Ri[0],e);for(var r=1;r<Ri.length;r++){var n=Ri[r];n.blockedOn===e&&(n.blockedOn=null)}}for(Tr!==null&&ps(Tr,e),Rr!==null&&ps(Rr,e),Or!==null&&ps(Or,e),Vs.forEach(t),Ws.forEach(t),r=0;r<jr.length;r++)n=jr[r],n.blockedOn===e&&(n.blockedOn=null);for(;0<jr.length&&(r=jr[0],r.blockedOn===null);)Gm(r),r.blockedOn===null&&jr.shift()}var $n=mr.ReactCurrentBatchConfig,fa=!0;function L0(e,t,r,n){var s=fe,i=$n.transition;$n.transition=null;try{fe=1,Ac(e,t,r,n)}finally{fe=s,$n.transition=i}}function F0(e,t,r,n){var s=fe,i=$n.transition;$n.transition=null;try{fe=4,Ac(e,t,r,n)}finally{fe=s,$n.transition=i}}function Ac(e,t,r,n){if(fa){var s=_o(e,t,r,n);if(s===null)Ul(e,t,n,ma,r),ed(e,n);else if(O0(s,e,t,r,n))n.stopPropagation();else if(ed(e,n),t&4&&-1<R0.indexOf(e)){for(;s!==null;){var i=hi(s);if(i!==null&&Vm(i),i=_o(e,t,r,n),i===null&&Ul(e,t,n,ma,r),i===s)break;s=i}s!==null&&n.stopPropagation()}else Ul(e,t,n,null,r)}}var ma=null;function _o(e,t,r,n){if(ma=null,e=Pc(n),e=Xr(e),e!==null)if(t=yn(e),t===null)e=null;else if(r=t.tag,r===13){if(e=Fm(t),e!==null)return e;e=null}else if(r===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ma=e,null}function qm(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(j0()){case Tc:return 1;case $m:return 4;case ua:case N0:return 16;case zm:return 536870912;default:return 16}default:return 16}}var _r=null,Lc=null,Zi=null;function Qm(){if(Zi)return Zi;var e,t=Lc,r=t.length,n,s="value"in _r?_r.value:_r.textContent,i=s.length;for(e=0;e<r&&t[e]===s[e];e++);var l=r-e;for(n=1;n<=l&&t[r-n]===s[i-n];n++);return Zi=s.slice(e,1<n?1-n:void 0)}function Gi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Oi(){return!0}function rd(){return!1}function gt(e){function t(r,n,s,i,l){this._reactName=r,this._targetInst=s,this.type=n,this.nativeEvent=i,this.target=l,this.currentTarget=null;for(var o in e)e.hasOwnProperty(o)&&(r=e[o],this[o]=r?r(i):i[o]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Oi:rd,this.isPropagationStopped=rd,this}return _e(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var r=this.nativeEvent;r&&(r.preventDefault?r.preventDefault():typeof r.returnValue!="unknown"&&(r.returnValue=!1),this.isDefaultPrevented=Oi)},stopPropagation:function(){var r=this.nativeEvent;r&&(r.stopPropagation?r.stopPropagation():typeof r.cancelBubble!="unknown"&&(r.cancelBubble=!0),this.isPropagationStopped=Oi)},persist:function(){},isPersistent:Oi}),t}var ss={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Fc=gt(ss),mi=_e({},ss,{view:0,detail:0}),D0=gt(mi),Ol,Al,gs,Ka=_e({},mi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Dc,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==gs&&(gs&&e.type==="mousemove"?(Ol=e.screenX-gs.screenX,Al=e.screenY-gs.screenY):Al=Ol=0,gs=e),Ol)},movementY:function(e){return"movementY"in e?e.movementY:Al}}),nd=gt(Ka),I0=_e({},Ka,{dataTransfer:0}),M0=gt(I0),$0=_e({},mi,{relatedTarget:0}),Ll=gt($0),z0=_e({},ss,{animationName:0,elapsedTime:0,pseudoElement:0}),U0=gt(z0),B0=_e({},ss,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),V0=gt(B0),W0=_e({},ss,{data:0}),sd=gt(W0),H0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Z0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},G0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function q0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=G0[e])?!!t[e]:!1}function Dc(){return q0}var Q0=_e({},mi,{key:function(e){if(e.key){var t=H0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Gi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Z0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Dc,charCode:function(e){return e.type==="keypress"?Gi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Gi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),J0=gt(Q0),K0=_e({},Ka,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),id=gt(K0),Y0=_e({},mi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Dc}),X0=gt(Y0),ex=_e({},ss,{propertyName:0,elapsedTime:0,pseudoElement:0}),tx=gt(ex),rx=_e({},Ka,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),nx=gt(rx),sx=[9,13,27,32],Ic=ar&&"CompositionEvent"in window,Ts=null;ar&&"documentMode"in document&&(Ts=document.documentMode);var ix=ar&&"TextEvent"in window&&!Ts,Jm=ar&&(!Ic||Ts&&8<Ts&&11>=Ts),ad=String.fromCharCode(32),ld=!1;function Km(e,t){switch(e){case"keyup":return sx.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ym(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var _n=!1;function ax(e,t){switch(e){case"compositionend":return Ym(t);case"keypress":return t.which!==32?null:(ld=!0,ad);case"textInput":return e=t.data,e===ad&&ld?null:e;default:return null}}function lx(e,t){if(_n)return e==="compositionend"||!Ic&&Km(e,t)?(e=Qm(),Zi=Lc=_r=null,_n=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Jm&&t.locale!=="ko"?null:t.data;default:return null}}var ox={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function od(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!ox[e.type]:t==="textarea"}function Xm(e,t,r,n){Tm(n),t=ha(t,"onChange"),0<t.length&&(r=new Fc("onChange","change",null,r,n),e.push({event:r,listeners:t}))}var Rs=null,Zs=null;function cx(e){uh(e,0)}function Ya(e){var t=Pn(e);if(Nm(t))return e}function ux(e,t){if(e==="change")return t}var eh=!1;if(ar){var Fl;if(ar){var Dl="oninput"in document;if(!Dl){var cd=document.createElement("div");cd.setAttribute("oninput","return;"),Dl=typeof cd.oninput=="function"}Fl=Dl}else Fl=!1;eh=Fl&&(!document.documentMode||9<document.documentMode)}function ud(){Rs&&(Rs.detachEvent("onpropertychange",th),Zs=Rs=null)}function th(e){if(e.propertyName==="value"&&Ya(Zs)){var t=[];Xm(t,Zs,e,Pc(e)),Lm(cx,t)}}function dx(e,t,r){e==="focusin"?(ud(),Rs=t,Zs=r,Rs.attachEvent("onpropertychange",th)):e==="focusout"&&ud()}function fx(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ya(Zs)}function mx(e,t){if(e==="click")return Ya(t)}function hx(e,t){if(e==="input"||e==="change")return Ya(t)}function px(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Dt=typeof Object.is=="function"?Object.is:px;function Gs(e,t){if(Dt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(n=0;n<r.length;n++){var s=r[n];if(!lo.call(t,s)||!Dt(e[s],t[s]))return!1}return!0}function dd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function fd(e,t){var r=dd(e);e=0;for(var n;r;){if(r.nodeType===3){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=dd(r)}}function rh(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?rh(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function nh(){for(var e=window,t=la();t instanceof e.HTMLIFrameElement;){try{var r=typeof t.contentWindow.location.href=="string"}catch{r=!1}if(r)e=t.contentWindow;else break;t=la(e.document)}return t}function Mc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function gx(e){var t=nh(),r=e.focusedElem,n=e.selectionRange;if(t!==r&&r&&r.ownerDocument&&rh(r.ownerDocument.documentElement,r)){if(n!==null&&Mc(r)){if(t=n.start,e=n.end,e===void 0&&(e=t),"selectionStart"in r)r.selectionStart=t,r.selectionEnd=Math.min(e,r.value.length);else if(e=(t=r.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=r.textContent.length,i=Math.min(n.start,s);n=n.end===void 0?i:Math.min(n.end,s),!e.extend&&i>n&&(s=n,n=i,i=s),s=fd(r,i);var l=fd(r,n);s&&l&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),i>n?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}}for(t=[],e=r;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<t.length;r++)e=t[r],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var xx=ar&&"documentMode"in document&&11>=document.documentMode,Cn=null,Co=null,Os=null,Eo=!1;function md(e,t,r){var n=r.window===r?r.document:r.nodeType===9?r:r.ownerDocument;Eo||Cn==null||Cn!==la(n)||(n=Cn,"selectionStart"in n&&Mc(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),Os&&Gs(Os,n)||(Os=n,n=ha(Co,"onSelect"),0<n.length&&(t=new Fc("onSelect","select",null,t,r),e.push({event:t,listeners:n}),t.target=Cn)))}function Ai(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var En={animationend:Ai("Animation","AnimationEnd"),animationiteration:Ai("Animation","AnimationIteration"),animationstart:Ai("Animation","AnimationStart"),transitionend:Ai("Transition","TransitionEnd")},Il={},sh={};ar&&(sh=document.createElement("div").style,"AnimationEvent"in window||(delete En.animationend.animation,delete En.animationiteration.animation,delete En.animationstart.animation),"TransitionEvent"in window||delete En.transitionend.transition);function Xa(e){if(Il[e])return Il[e];if(!En[e])return e;var t=En[e],r;for(r in t)if(t.hasOwnProperty(r)&&r in sh)return Il[e]=t[r];return e}var ih=Xa("animationend"),ah=Xa("animationiteration"),lh=Xa("animationstart"),oh=Xa("transitionend"),ch=new Map,hd="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Zr(e,t){ch.set(e,t),xn(t,[e])}for(var Ml=0;Ml<hd.length;Ml++){var $l=hd[Ml],yx=$l.toLowerCase(),vx=$l[0].toUpperCase()+$l.slice(1);Zr(yx,"on"+vx)}Zr(ih,"onAnimationEnd");Zr(ah,"onAnimationIteration");Zr(lh,"onAnimationStart");Zr("dblclick","onDoubleClick");Zr("focusin","onFocus");Zr("focusout","onBlur");Zr(oh,"onTransitionEnd");Bn("onMouseEnter",["mouseout","mouseover"]);Bn("onMouseLeave",["mouseout","mouseover"]);Bn("onPointerEnter",["pointerout","pointerover"]);Bn("onPointerLeave",["pointerout","pointerover"]);xn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));xn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));xn("onBeforeInput",["compositionend","keypress","textInput","paste"]);xn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));xn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));xn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var _s="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),wx=new Set("cancel close invalid load scroll toggle".split(" ").concat(_s));function pd(e,t,r){var n=e.type||"unknown-event";e.currentTarget=r,y0(n,t,void 0,e),e.currentTarget=null}function uh(e,t){t=(t&4)!==0;for(var r=0;r<e.length;r++){var n=e[r],s=n.event;n=n.listeners;e:{var i=void 0;if(t)for(var l=n.length-1;0<=l;l--){var o=n[l],c=o.instance,u=o.currentTarget;if(o=o.listener,c!==i&&s.isPropagationStopped())break e;pd(s,o,u),i=c}else for(l=0;l<n.length;l++){if(o=n[l],c=o.instance,u=o.currentTarget,o=o.listener,c!==i&&s.isPropagationStopped())break e;pd(s,o,u),i=c}}}if(ca)throw e=No,ca=!1,No=null,e}function ye(e,t){var r=t[Ao];r===void 0&&(r=t[Ao]=new Set);var n=e+"__bubble";r.has(n)||(dh(t,e,2,!1),r.add(n))}function zl(e,t,r){var n=0;t&&(n|=4),dh(r,e,n,t)}var Li="_reactListening"+Math.random().toString(36).slice(2);function qs(e){if(!e[Li]){e[Li]=!0,ym.forEach(function(r){r!=="selectionchange"&&(wx.has(r)||zl(r,!1,e),zl(r,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Li]||(t[Li]=!0,zl("selectionchange",!1,t))}}function dh(e,t,r,n){switch(qm(t)){case 1:var s=L0;break;case 4:s=F0;break;default:s=Ac}r=s.bind(null,t,r,e),s=void 0,!jo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),n?s!==void 0?e.addEventListener(t,r,{capture:!0,passive:s}):e.addEventListener(t,r,!0):s!==void 0?e.addEventListener(t,r,{passive:s}):e.addEventListener(t,r,!1)}function Ul(e,t,r,n,s){var i=n;if(!(t&1)&&!(t&2)&&n!==null)e:for(;;){if(n===null)return;var l=n.tag;if(l===3||l===4){var o=n.stateNode.containerInfo;if(o===s||o.nodeType===8&&o.parentNode===s)break;if(l===4)for(l=n.return;l!==null;){var c=l.tag;if((c===3||c===4)&&(c=l.stateNode.containerInfo,c===s||c.nodeType===8&&c.parentNode===s))return;l=l.return}for(;o!==null;){if(l=Xr(o),l===null)return;if(c=l.tag,c===5||c===6){n=i=l;continue e}o=o.parentNode}}n=n.return}Lm(function(){var u=i,d=Pc(r),f=[];e:{var h=ch.get(e);if(h!==void 0){var w=Fc,p=e;switch(e){case"keypress":if(Gi(r)===0)break e;case"keydown":case"keyup":w=J0;break;case"focusin":p="focus",w=Ll;break;case"focusout":p="blur",w=Ll;break;case"beforeblur":case"afterblur":w=Ll;break;case"click":if(r.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=nd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=M0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=X0;break;case ih:case ah:case lh:w=U0;break;case oh:w=tx;break;case"scroll":w=D0;break;case"wheel":w=nx;break;case"copy":case"cut":case"paste":w=V0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=id}var y=(t&4)!==0,b=!y&&e==="scroll",x=y?h!==null?h+"Capture":null:h;y=[];for(var m=u,g;m!==null;){g=m;var N=g.stateNode;if(g.tag===5&&N!==null&&(g=N,x!==null&&(N=Bs(m,x),N!=null&&y.push(Qs(m,N,g)))),b)break;m=m.return}0<y.length&&(h=new w(h,p,null,r,d),f.push({event:h,listeners:y}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",w=e==="mouseout"||e==="pointerout",h&&r!==wo&&(p=r.relatedTarget||r.fromElement)&&(Xr(p)||p[lr]))break e;if((w||h)&&(h=d.window===d?d:(h=d.ownerDocument)?h.defaultView||h.parentWindow:window,w?(p=r.relatedTarget||r.toElement,w=u,p=p?Xr(p):null,p!==null&&(b=yn(p),p!==b||p.tag!==5&&p.tag!==6)&&(p=null)):(w=null,p=u),w!==p)){if(y=nd,N="onMouseLeave",x="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(y=id,N="onPointerLeave",x="onPointerEnter",m="pointer"),b=w==null?h:Pn(w),g=p==null?h:Pn(p),h=new y(N,m+"leave",w,r,d),h.target=b,h.relatedTarget=g,N=null,Xr(d)===u&&(y=new y(x,m+"enter",p,r,d),y.target=g,y.relatedTarget=b,N=y),b=N,w&&p)t:{for(y=w,x=p,m=0,g=y;g;g=bn(g))m++;for(g=0,N=x;N;N=bn(N))g++;for(;0<m-g;)y=bn(y),m--;for(;0<g-m;)x=bn(x),g--;for(;m--;){if(y===x||x!==null&&y===x.alternate)break t;y=bn(y),x=bn(x)}y=null}else y=null;w!==null&&gd(f,h,w,y,!1),p!==null&&b!==null&&gd(f,b,p,y,!0)}}e:{if(h=u?Pn(u):window,w=h.nodeName&&h.nodeName.toLowerCase(),w==="select"||w==="input"&&h.type==="file")var _=ux;else if(od(h))if(eh)_=hx;else{_=fx;var R=dx}else(w=h.nodeName)&&w.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(_=mx);if(_&&(_=_(e,u))){Xm(f,_,r,d);break e}R&&R(e,h,u),e==="focusout"&&(R=h._wrapperState)&&R.controlled&&h.type==="number"&&po(h,"number",h.value)}switch(R=u?Pn(u):window,e){case"focusin":(od(R)||R.contentEditable==="true")&&(Cn=R,Co=u,Os=null);break;case"focusout":Os=Co=Cn=null;break;case"mousedown":Eo=!0;break;case"contextmenu":case"mouseup":case"dragend":Eo=!1,md(f,r,d);break;case"selectionchange":if(xx)break;case"keydown":case"keyup":md(f,r,d)}var E;if(Ic)e:{switch(e){case"compositionstart":var A="onCompositionStart";break e;case"compositionend":A="onCompositionEnd";break e;case"compositionupdate":A="onCompositionUpdate";break e}A=void 0}else _n?Km(e,r)&&(A="onCompositionEnd"):e==="keydown"&&r.keyCode===229&&(A="onCompositionStart");A&&(Jm&&r.locale!=="ko"&&(_n||A!=="onCompositionStart"?A==="onCompositionEnd"&&_n&&(E=Qm()):(_r=d,Lc="value"in _r?_r.value:_r.textContent,_n=!0)),R=ha(u,A),0<R.length&&(A=new sd(A,e,null,r,d),f.push({event:A,listeners:R}),E?A.data=E:(E=Ym(r),E!==null&&(A.data=E)))),(E=ix?ax(e,r):lx(e,r))&&(u=ha(u,"onBeforeInput"),0<u.length&&(d=new sd("onBeforeInput","beforeinput",null,r,d),f.push({event:d,listeners:u}),d.data=E))}uh(f,t)})}function Qs(e,t,r){return{instance:e,listener:t,currentTarget:r}}function ha(e,t){for(var r=t+"Capture",n=[];e!==null;){var s=e,i=s.stateNode;s.tag===5&&i!==null&&(s=i,i=Bs(e,r),i!=null&&n.unshift(Qs(e,i,s)),i=Bs(e,t),i!=null&&n.push(Qs(e,i,s))),e=e.return}return n}function bn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function gd(e,t,r,n,s){for(var i=t._reactName,l=[];r!==null&&r!==n;){var o=r,c=o.alternate,u=o.stateNode;if(c!==null&&c===n)break;o.tag===5&&u!==null&&(o=u,s?(c=Bs(r,i),c!=null&&l.unshift(Qs(r,c,o))):s||(c=Bs(r,i),c!=null&&l.push(Qs(r,c,o)))),r=r.return}l.length!==0&&e.push({event:t,listeners:l})}var bx=/\r\n?/g,jx=/\u0000|\uFFFD/g;function xd(e){return(typeof e=="string"?e:""+e).replace(bx,`
`).replace(jx,"")}function Fi(e,t,r){if(t=xd(t),xd(e)!==t&&r)throw Error(P(425))}function pa(){}var Po=null,To=null;function Ro(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Oo=typeof setTimeout=="function"?setTimeout:void 0,Nx=typeof clearTimeout=="function"?clearTimeout:void 0,yd=typeof Promise=="function"?Promise:void 0,kx=typeof queueMicrotask=="function"?queueMicrotask:typeof yd<"u"?function(e){return yd.resolve(null).then(e).catch(Sx)}:Oo;function Sx(e){setTimeout(function(){throw e})}function Bl(e,t){var r=t,n=0;do{var s=r.nextSibling;if(e.removeChild(r),s&&s.nodeType===8)if(r=s.data,r==="/$"){if(n===0){e.removeChild(s),Hs(t);return}n--}else r!=="$"&&r!=="$?"&&r!=="$!"||n++;r=s}while(r);Hs(t)}function Ar(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function vd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="$"||r==="$!"||r==="$?"){if(t===0)return e;t--}else r==="/$"&&t++}e=e.previousSibling}return null}var is=Math.random().toString(36).slice(2),Wt="__reactFiber$"+is,Js="__reactProps$"+is,lr="__reactContainer$"+is,Ao="__reactEvents$"+is,_x="__reactListeners$"+is,Cx="__reactHandles$"+is;function Xr(e){var t=e[Wt];if(t)return t;for(var r=e.parentNode;r;){if(t=r[lr]||r[Wt]){if(r=t.alternate,t.child!==null||r!==null&&r.child!==null)for(e=vd(e);e!==null;){if(r=e[Wt])return r;e=vd(e)}return t}e=r,r=e.parentNode}return null}function hi(e){return e=e[Wt]||e[lr],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Pn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(P(33))}function el(e){return e[Js]||null}var Lo=[],Tn=-1;function Gr(e){return{current:e}}function ve(e){0>Tn||(e.current=Lo[Tn],Lo[Tn]=null,Tn--)}function ge(e,t){Tn++,Lo[Tn]=e.current,e.current=t}var Vr={},Je=Gr(Vr),at=Gr(!1),on=Vr;function Vn(e,t){var r=e.type.contextTypes;if(!r)return Vr;var n=e.stateNode;if(n&&n.__reactInternalMemoizedUnmaskedChildContext===t)return n.__reactInternalMemoizedMaskedChildContext;var s={},i;for(i in r)s[i]=t[i];return n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function lt(e){return e=e.childContextTypes,e!=null}function ga(){ve(at),ve(Je)}function wd(e,t,r){if(Je.current!==Vr)throw Error(P(168));ge(Je,t),ge(at,r)}function fh(e,t,r){var n=e.stateNode;if(t=t.childContextTypes,typeof n.getChildContext!="function")return r;n=n.getChildContext();for(var s in n)if(!(s in t))throw Error(P(108,d0(e)||"Unknown",s));return _e({},r,n)}function xa(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Vr,on=Je.current,ge(Je,e),ge(at,at.current),!0}function bd(e,t,r){var n=e.stateNode;if(!n)throw Error(P(169));r?(e=fh(e,t,on),n.__reactInternalMemoizedMergedChildContext=e,ve(at),ve(Je),ge(Je,e)):ve(at),ge(at,r)}var tr=null,tl=!1,Vl=!1;function mh(e){tr===null?tr=[e]:tr.push(e)}function Ex(e){tl=!0,mh(e)}function qr(){if(!Vl&&tr!==null){Vl=!0;var e=0,t=fe;try{var r=tr;for(fe=1;e<r.length;e++){var n=r[e];do n=n(!0);while(n!==null)}tr=null,tl=!1}catch(s){throw tr!==null&&(tr=tr.slice(e+1)),Mm(Tc,qr),s}finally{fe=t,Vl=!1}}return null}var Rn=[],On=0,ya=null,va=0,wt=[],bt=0,cn=null,rr=1,nr="";function Kr(e,t){Rn[On++]=va,Rn[On++]=ya,ya=e,va=t}function hh(e,t,r){wt[bt++]=rr,wt[bt++]=nr,wt[bt++]=cn,cn=e;var n=rr;e=nr;var s=32-At(n)-1;n&=~(1<<s),r+=1;var i=32-At(t)+s;if(30<i){var l=s-s%5;i=(n&(1<<l)-1).toString(32),n>>=l,s-=l,rr=1<<32-At(t)+s|r<<s|n,nr=i+e}else rr=1<<i|r<<s|n,nr=e}function $c(e){e.return!==null&&(Kr(e,1),hh(e,1,0))}function zc(e){for(;e===ya;)ya=Rn[--On],Rn[On]=null,va=Rn[--On],Rn[On]=null;for(;e===cn;)cn=wt[--bt],wt[bt]=null,nr=wt[--bt],wt[bt]=null,rr=wt[--bt],wt[bt]=null}var ft=null,dt=null,be=!1,Tt=null;function ph(e,t){var r=jt(5,null,null,0);r.elementType="DELETED",r.stateNode=t,r.return=e,t=e.deletions,t===null?(e.deletions=[r],e.flags|=16):t.push(r)}function jd(e,t){switch(e.tag){case 5:var r=e.type;return t=t.nodeType!==1||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,ft=e,dt=Ar(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,ft=e,dt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(r=cn!==null?{id:rr,overflow:nr}:null,e.memoizedState={dehydrated:t,treeContext:r,retryLane:1073741824},r=jt(18,null,null,0),r.stateNode=t,r.return=e,e.child=r,ft=e,dt=null,!0):!1;default:return!1}}function Fo(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Do(e){if(be){var t=dt;if(t){var r=t;if(!jd(e,t)){if(Fo(e))throw Error(P(418));t=Ar(r.nextSibling);var n=ft;t&&jd(e,t)?ph(n,r):(e.flags=e.flags&-4097|2,be=!1,ft=e)}}else{if(Fo(e))throw Error(P(418));e.flags=e.flags&-4097|2,be=!1,ft=e}}}function Nd(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;ft=e}function Di(e){if(e!==ft)return!1;if(!be)return Nd(e),be=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ro(e.type,e.memoizedProps)),t&&(t=dt)){if(Fo(e))throw gh(),Error(P(418));for(;t;)ph(e,t),t=Ar(t.nextSibling)}if(Nd(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(P(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="/$"){if(t===0){dt=Ar(e.nextSibling);break e}t--}else r!=="$"&&r!=="$!"&&r!=="$?"||t++}e=e.nextSibling}dt=null}}else dt=ft?Ar(e.stateNode.nextSibling):null;return!0}function gh(){for(var e=dt;e;)e=Ar(e.nextSibling)}function Wn(){dt=ft=null,be=!1}function Uc(e){Tt===null?Tt=[e]:Tt.push(e)}var Px=mr.ReactCurrentBatchConfig;function xs(e,t,r){if(e=r.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(r._owner){if(r=r._owner,r){if(r.tag!==1)throw Error(P(309));var n=r.stateNode}if(!n)throw Error(P(147,e));var s=n,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(l){var o=s.refs;l===null?delete o[i]:o[i]=l},t._stringRef=i,t)}if(typeof e!="string")throw Error(P(284));if(!r._owner)throw Error(P(290,e))}return e}function Ii(e,t){throw e=Object.prototype.toString.call(t),Error(P(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function kd(e){var t=e._init;return t(e._payload)}function xh(e){function t(x,m){if(e){var g=x.deletions;g===null?(x.deletions=[m],x.flags|=16):g.push(m)}}function r(x,m){if(!e)return null;for(;m!==null;)t(x,m),m=m.sibling;return null}function n(x,m){for(x=new Map;m!==null;)m.key!==null?x.set(m.key,m):x.set(m.index,m),m=m.sibling;return x}function s(x,m){return x=Ir(x,m),x.index=0,x.sibling=null,x}function i(x,m,g){return x.index=g,e?(g=x.alternate,g!==null?(g=g.index,g<m?(x.flags|=2,m):g):(x.flags|=2,m)):(x.flags|=1048576,m)}function l(x){return e&&x.alternate===null&&(x.flags|=2),x}function o(x,m,g,N){return m===null||m.tag!==6?(m=Jl(g,x.mode,N),m.return=x,m):(m=s(m,g),m.return=x,m)}function c(x,m,g,N){var _=g.type;return _===Sn?d(x,m,g.props.children,N,g.key):m!==null&&(m.elementType===_||typeof _=="object"&&_!==null&&_.$$typeof===vr&&kd(_)===m.type)?(N=s(m,g.props),N.ref=xs(x,m,g),N.return=x,N):(N=ea(g.type,g.key,g.props,null,x.mode,N),N.ref=xs(x,m,g),N.return=x,N)}function u(x,m,g,N){return m===null||m.tag!==4||m.stateNode.containerInfo!==g.containerInfo||m.stateNode.implementation!==g.implementation?(m=Kl(g,x.mode,N),m.return=x,m):(m=s(m,g.children||[]),m.return=x,m)}function d(x,m,g,N,_){return m===null||m.tag!==7?(m=ln(g,x.mode,N,_),m.return=x,m):(m=s(m,g),m.return=x,m)}function f(x,m,g){if(typeof m=="string"&&m!==""||typeof m=="number")return m=Jl(""+m,x.mode,g),m.return=x,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case _i:return g=ea(m.type,m.key,m.props,null,x.mode,g),g.ref=xs(x,null,m),g.return=x,g;case kn:return m=Kl(m,x.mode,g),m.return=x,m;case vr:var N=m._init;return f(x,N(m._payload),g)}if(ks(m)||fs(m))return m=ln(m,x.mode,g,null),m.return=x,m;Ii(x,m)}return null}function h(x,m,g,N){var _=m!==null?m.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return _!==null?null:o(x,m,""+g,N);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case _i:return g.key===_?c(x,m,g,N):null;case kn:return g.key===_?u(x,m,g,N):null;case vr:return _=g._init,h(x,m,_(g._payload),N)}if(ks(g)||fs(g))return _!==null?null:d(x,m,g,N,null);Ii(x,g)}return null}function w(x,m,g,N,_){if(typeof N=="string"&&N!==""||typeof N=="number")return x=x.get(g)||null,o(m,x,""+N,_);if(typeof N=="object"&&N!==null){switch(N.$$typeof){case _i:return x=x.get(N.key===null?g:N.key)||null,c(m,x,N,_);case kn:return x=x.get(N.key===null?g:N.key)||null,u(m,x,N,_);case vr:var R=N._init;return w(x,m,g,R(N._payload),_)}if(ks(N)||fs(N))return x=x.get(g)||null,d(m,x,N,_,null);Ii(m,N)}return null}function p(x,m,g,N){for(var _=null,R=null,E=m,A=m=0,q=null;E!==null&&A<g.length;A++){E.index>A?(q=E,E=null):q=E.sibling;var Z=h(x,E,g[A],N);if(Z===null){E===null&&(E=q);break}e&&E&&Z.alternate===null&&t(x,E),m=i(Z,m,A),R===null?_=Z:R.sibling=Z,R=Z,E=q}if(A===g.length)return r(x,E),be&&Kr(x,A),_;if(E===null){for(;A<g.length;A++)E=f(x,g[A],N),E!==null&&(m=i(E,m,A),R===null?_=E:R.sibling=E,R=E);return be&&Kr(x,A),_}for(E=n(x,E);A<g.length;A++)q=w(E,x,A,g[A],N),q!==null&&(e&&q.alternate!==null&&E.delete(q.key===null?A:q.key),m=i(q,m,A),R===null?_=q:R.sibling=q,R=q);return e&&E.forEach(function(ue){return t(x,ue)}),be&&Kr(x,A),_}function y(x,m,g,N){var _=fs(g);if(typeof _!="function")throw Error(P(150));if(g=_.call(g),g==null)throw Error(P(151));for(var R=_=null,E=m,A=m=0,q=null,Z=g.next();E!==null&&!Z.done;A++,Z=g.next()){E.index>A?(q=E,E=null):q=E.sibling;var ue=h(x,E,Z.value,N);if(ue===null){E===null&&(E=q);break}e&&E&&ue.alternate===null&&t(x,E),m=i(ue,m,A),R===null?_=ue:R.sibling=ue,R=ue,E=q}if(Z.done)return r(x,E),be&&Kr(x,A),_;if(E===null){for(;!Z.done;A++,Z=g.next())Z=f(x,Z.value,N),Z!==null&&(m=i(Z,m,A),R===null?_=Z:R.sibling=Z,R=Z);return be&&Kr(x,A),_}for(E=n(x,E);!Z.done;A++,Z=g.next())Z=w(E,x,A,Z.value,N),Z!==null&&(e&&Z.alternate!==null&&E.delete(Z.key===null?A:Z.key),m=i(Z,m,A),R===null?_=Z:R.sibling=Z,R=Z);return e&&E.forEach(function(ee){return t(x,ee)}),be&&Kr(x,A),_}function b(x,m,g,N){if(typeof g=="object"&&g!==null&&g.type===Sn&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case _i:e:{for(var _=g.key,R=m;R!==null;){if(R.key===_){if(_=g.type,_===Sn){if(R.tag===7){r(x,R.sibling),m=s(R,g.props.children),m.return=x,x=m;break e}}else if(R.elementType===_||typeof _=="object"&&_!==null&&_.$$typeof===vr&&kd(_)===R.type){r(x,R.sibling),m=s(R,g.props),m.ref=xs(x,R,g),m.return=x,x=m;break e}r(x,R);break}else t(x,R);R=R.sibling}g.type===Sn?(m=ln(g.props.children,x.mode,N,g.key),m.return=x,x=m):(N=ea(g.type,g.key,g.props,null,x.mode,N),N.ref=xs(x,m,g),N.return=x,x=N)}return l(x);case kn:e:{for(R=g.key;m!==null;){if(m.key===R)if(m.tag===4&&m.stateNode.containerInfo===g.containerInfo&&m.stateNode.implementation===g.implementation){r(x,m.sibling),m=s(m,g.children||[]),m.return=x,x=m;break e}else{r(x,m);break}else t(x,m);m=m.sibling}m=Kl(g,x.mode,N),m.return=x,x=m}return l(x);case vr:return R=g._init,b(x,m,R(g._payload),N)}if(ks(g))return p(x,m,g,N);if(fs(g))return y(x,m,g,N);Ii(x,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,m!==null&&m.tag===6?(r(x,m.sibling),m=s(m,g),m.return=x,x=m):(r(x,m),m=Jl(g,x.mode,N),m.return=x,x=m),l(x)):r(x,m)}return b}var Hn=xh(!0),yh=xh(!1),wa=Gr(null),ba=null,An=null,Bc=null;function Vc(){Bc=An=ba=null}function Wc(e){var t=wa.current;ve(wa),e._currentValue=t}function Io(e,t,r){for(;e!==null;){var n=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,n!==null&&(n.childLanes|=t)):n!==null&&(n.childLanes&t)!==t&&(n.childLanes|=t),e===r)break;e=e.return}}function zn(e,t){ba=e,Bc=An=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(it=!0),e.firstContext=null)}function kt(e){var t=e._currentValue;if(Bc!==e)if(e={context:e,memoizedValue:t,next:null},An===null){if(ba===null)throw Error(P(308));An=e,ba.dependencies={lanes:0,firstContext:e}}else An=An.next=e;return t}var en=null;function Hc(e){en===null?en=[e]:en.push(e)}function vh(e,t,r,n){var s=t.interleaved;return s===null?(r.next=r,Hc(t)):(r.next=s.next,s.next=r),t.interleaved=r,or(e,n)}function or(e,t){e.lanes|=t;var r=e.alternate;for(r!==null&&(r.lanes|=t),r=e,e=e.return;e!==null;)e.childLanes|=t,r=e.alternate,r!==null&&(r.childLanes|=t),r=e,e=e.return;return r.tag===3?r.stateNode:null}var wr=!1;function Zc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function wh(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ir(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Lr(e,t,r){var n=e.updateQueue;if(n===null)return null;if(n=n.shared,le&2){var s=n.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),n.pending=t,or(e,r)}return s=n.interleaved,s===null?(t.next=t,Hc(n)):(t.next=s.next,s.next=t),n.interleaved=t,or(e,r)}function qi(e,t,r){if(t=t.updateQueue,t!==null&&(t=t.shared,(r&4194240)!==0)){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,Rc(e,r)}}function Sd(e,t){var r=e.updateQueue,n=e.alternate;if(n!==null&&(n=n.updateQueue,r===n)){var s=null,i=null;if(r=r.firstBaseUpdate,r!==null){do{var l={eventTime:r.eventTime,lane:r.lane,tag:r.tag,payload:r.payload,callback:r.callback,next:null};i===null?s=i=l:i=i.next=l,r=r.next}while(r!==null);i===null?s=i=t:i=i.next=t}else s=i=t;r={baseState:n.baseState,firstBaseUpdate:s,lastBaseUpdate:i,shared:n.shared,effects:n.effects},e.updateQueue=r;return}e=r.lastBaseUpdate,e===null?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function ja(e,t,r,n){var s=e.updateQueue;wr=!1;var i=s.firstBaseUpdate,l=s.lastBaseUpdate,o=s.shared.pending;if(o!==null){s.shared.pending=null;var c=o,u=c.next;c.next=null,l===null?i=u:l.next=u,l=c;var d=e.alternate;d!==null&&(d=d.updateQueue,o=d.lastBaseUpdate,o!==l&&(o===null?d.firstBaseUpdate=u:o.next=u,d.lastBaseUpdate=c))}if(i!==null){var f=s.baseState;l=0,d=u=c=null,o=i;do{var h=o.lane,w=o.eventTime;if((n&h)===h){d!==null&&(d=d.next={eventTime:w,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var p=e,y=o;switch(h=t,w=r,y.tag){case 1:if(p=y.payload,typeof p=="function"){f=p.call(w,f,h);break e}f=p;break e;case 3:p.flags=p.flags&-65537|128;case 0:if(p=y.payload,h=typeof p=="function"?p.call(w,f,h):p,h==null)break e;f=_e({},f,h);break e;case 2:wr=!0}}o.callback!==null&&o.lane!==0&&(e.flags|=64,h=s.effects,h===null?s.effects=[o]:h.push(o))}else w={eventTime:w,lane:h,tag:o.tag,payload:o.payload,callback:o.callback,next:null},d===null?(u=d=w,c=f):d=d.next=w,l|=h;if(o=o.next,o===null){if(o=s.shared.pending,o===null)break;h=o,o=h.next,h.next=null,s.lastBaseUpdate=h,s.shared.pending=null}}while(1);if(d===null&&(c=f),s.baseState=c,s.firstBaseUpdate=u,s.lastBaseUpdate=d,t=s.shared.interleaved,t!==null){s=t;do l|=s.lane,s=s.next;while(s!==t)}else i===null&&(s.shared.lanes=0);dn|=l,e.lanes=l,e.memoizedState=f}}function _d(e,t,r){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var n=e[t],s=n.callback;if(s!==null){if(n.callback=null,n=r,typeof s!="function")throw Error(P(191,s));s.call(n)}}}var pi={},Gt=Gr(pi),Ks=Gr(pi),Ys=Gr(pi);function tn(e){if(e===pi)throw Error(P(174));return e}function Gc(e,t){switch(ge(Ys,t),ge(Ks,e),ge(Gt,pi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:xo(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=xo(t,e)}ve(Gt),ge(Gt,t)}function Zn(){ve(Gt),ve(Ks),ve(Ys)}function bh(e){tn(Ys.current);var t=tn(Gt.current),r=xo(t,e.type);t!==r&&(ge(Ks,e),ge(Gt,r))}function qc(e){Ks.current===e&&(ve(Gt),ve(Ks))}var ke=Gr(0);function Na(e){for(var t=e;t!==null;){if(t.tag===13){var r=t.memoizedState;if(r!==null&&(r=r.dehydrated,r===null||r.data==="$?"||r.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Wl=[];function Qc(){for(var e=0;e<Wl.length;e++)Wl[e]._workInProgressVersionPrimary=null;Wl.length=0}var Qi=mr.ReactCurrentDispatcher,Hl=mr.ReactCurrentBatchConfig,un=0,Se=null,De=null,$e=null,ka=!1,As=!1,Xs=0,Tx=0;function He(){throw Error(P(321))}function Jc(e,t){if(t===null)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!Dt(e[r],t[r]))return!1;return!0}function Kc(e,t,r,n,s,i){if(un=i,Se=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Qi.current=e===null||e.memoizedState===null?Lx:Fx,e=r(n,s),As){i=0;do{if(As=!1,Xs=0,25<=i)throw Error(P(301));i+=1,$e=De=null,t.updateQueue=null,Qi.current=Dx,e=r(n,s)}while(As)}if(Qi.current=Sa,t=De!==null&&De.next!==null,un=0,$e=De=Se=null,ka=!1,t)throw Error(P(300));return e}function Yc(){var e=Xs!==0;return Xs=0,e}function Ut(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return $e===null?Se.memoizedState=$e=e:$e=$e.next=e,$e}function St(){if(De===null){var e=Se.alternate;e=e!==null?e.memoizedState:null}else e=De.next;var t=$e===null?Se.memoizedState:$e.next;if(t!==null)$e=t,De=e;else{if(e===null)throw Error(P(310));De=e,e={memoizedState:De.memoizedState,baseState:De.baseState,baseQueue:De.baseQueue,queue:De.queue,next:null},$e===null?Se.memoizedState=$e=e:$e=$e.next=e}return $e}function ei(e,t){return typeof t=="function"?t(e):t}function Zl(e){var t=St(),r=t.queue;if(r===null)throw Error(P(311));r.lastRenderedReducer=e;var n=De,s=n.baseQueue,i=r.pending;if(i!==null){if(s!==null){var l=s.next;s.next=i.next,i.next=l}n.baseQueue=s=i,r.pending=null}if(s!==null){i=s.next,n=n.baseState;var o=l=null,c=null,u=i;do{var d=u.lane;if((un&d)===d)c!==null&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),n=u.hasEagerState?u.eagerState:e(n,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};c===null?(o=c=f,l=n):c=c.next=f,Se.lanes|=d,dn|=d}u=u.next}while(u!==null&&u!==i);c===null?l=n:c.next=o,Dt(n,t.memoizedState)||(it=!0),t.memoizedState=n,t.baseState=l,t.baseQueue=c,r.lastRenderedState=n}if(e=r.interleaved,e!==null){s=e;do i=s.lane,Se.lanes|=i,dn|=i,s=s.next;while(s!==e)}else s===null&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function Gl(e){var t=St(),r=t.queue;if(r===null)throw Error(P(311));r.lastRenderedReducer=e;var n=r.dispatch,s=r.pending,i=t.memoizedState;if(s!==null){r.pending=null;var l=s=s.next;do i=e(i,l.action),l=l.next;while(l!==s);Dt(i,t.memoizedState)||(it=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),r.lastRenderedState=i}return[i,n]}function jh(){}function Nh(e,t){var r=Se,n=St(),s=t(),i=!Dt(n.memoizedState,s);if(i&&(n.memoizedState=s,it=!0),n=n.queue,Xc(_h.bind(null,r,n,e),[e]),n.getSnapshot!==t||i||$e!==null&&$e.memoizedState.tag&1){if(r.flags|=2048,ti(9,Sh.bind(null,r,n,s,t),void 0,null),ze===null)throw Error(P(349));un&30||kh(r,t,s)}return s}function kh(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},t=Se.updateQueue,t===null?(t={lastEffect:null,stores:null},Se.updateQueue=t,t.stores=[e]):(r=t.stores,r===null?t.stores=[e]:r.push(e))}function Sh(e,t,r,n){t.value=r,t.getSnapshot=n,Ch(t)&&Eh(e)}function _h(e,t,r){return r(function(){Ch(t)&&Eh(e)})}function Ch(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!Dt(e,r)}catch{return!0}}function Eh(e){var t=or(e,1);t!==null&&Lt(t,e,1,-1)}function Cd(e){var t=Ut();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ei,lastRenderedState:e},t.queue=e,e=e.dispatch=Ax.bind(null,Se,e),[t.memoizedState,e]}function ti(e,t,r,n){return e={tag:e,create:t,destroy:r,deps:n,next:null},t=Se.updateQueue,t===null?(t={lastEffect:null,stores:null},Se.updateQueue=t,t.lastEffect=e.next=e):(r=t.lastEffect,r===null?t.lastEffect=e.next=e:(n=r.next,r.next=e,e.next=n,t.lastEffect=e)),e}function Ph(){return St().memoizedState}function Ji(e,t,r,n){var s=Ut();Se.flags|=e,s.memoizedState=ti(1|t,r,void 0,n===void 0?null:n)}function rl(e,t,r,n){var s=St();n=n===void 0?null:n;var i=void 0;if(De!==null){var l=De.memoizedState;if(i=l.destroy,n!==null&&Jc(n,l.deps)){s.memoizedState=ti(t,r,i,n);return}}Se.flags|=e,s.memoizedState=ti(1|t,r,i,n)}function Ed(e,t){return Ji(8390656,8,e,t)}function Xc(e,t){return rl(2048,8,e,t)}function Th(e,t){return rl(4,2,e,t)}function Rh(e,t){return rl(4,4,e,t)}function Oh(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Ah(e,t,r){return r=r!=null?r.concat([e]):null,rl(4,4,Oh.bind(null,t,e),r)}function eu(){}function Lh(e,t){var r=St();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&Jc(t,n[1])?n[0]:(r.memoizedState=[e,t],e)}function Fh(e,t){var r=St();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&Jc(t,n[1])?n[0]:(e=e(),r.memoizedState=[e,t],e)}function Dh(e,t,r){return un&21?(Dt(r,t)||(r=Um(),Se.lanes|=r,dn|=r,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,it=!0),e.memoizedState=r)}function Rx(e,t){var r=fe;fe=r!==0&&4>r?r:4,e(!0);var n=Hl.transition;Hl.transition={};try{e(!1),t()}finally{fe=r,Hl.transition=n}}function Ih(){return St().memoizedState}function Ox(e,t,r){var n=Dr(e);if(r={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null},Mh(e))$h(t,r);else if(r=vh(e,t,r,n),r!==null){var s=et();Lt(r,e,n,s),zh(r,t,n)}}function Ax(e,t,r){var n=Dr(e),s={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null};if(Mh(e))$h(t,s);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var l=t.lastRenderedState,o=i(l,r);if(s.hasEagerState=!0,s.eagerState=o,Dt(o,l)){var c=t.interleaved;c===null?(s.next=s,Hc(t)):(s.next=c.next,c.next=s),t.interleaved=s;return}}catch{}finally{}r=vh(e,t,s,n),r!==null&&(s=et(),Lt(r,e,n,s),zh(r,t,n))}}function Mh(e){var t=e.alternate;return e===Se||t!==null&&t===Se}function $h(e,t){As=ka=!0;var r=e.pending;r===null?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function zh(e,t,r){if(r&4194240){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,Rc(e,r)}}var Sa={readContext:kt,useCallback:He,useContext:He,useEffect:He,useImperativeHandle:He,useInsertionEffect:He,useLayoutEffect:He,useMemo:He,useReducer:He,useRef:He,useState:He,useDebugValue:He,useDeferredValue:He,useTransition:He,useMutableSource:He,useSyncExternalStore:He,useId:He,unstable_isNewReconciler:!1},Lx={readContext:kt,useCallback:function(e,t){return Ut().memoizedState=[e,t===void 0?null:t],e},useContext:kt,useEffect:Ed,useImperativeHandle:function(e,t,r){return r=r!=null?r.concat([e]):null,Ji(4194308,4,Oh.bind(null,t,e),r)},useLayoutEffect:function(e,t){return Ji(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ji(4,2,e,t)},useMemo:function(e,t){var r=Ut();return t=t===void 0?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var n=Ut();return t=r!==void 0?r(t):t,n.memoizedState=n.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},n.queue=e,e=e.dispatch=Ox.bind(null,Se,e),[n.memoizedState,e]},useRef:function(e){var t=Ut();return e={current:e},t.memoizedState=e},useState:Cd,useDebugValue:eu,useDeferredValue:function(e){return Ut().memoizedState=e},useTransition:function(){var e=Cd(!1),t=e[0];return e=Rx.bind(null,e[1]),Ut().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var n=Se,s=Ut();if(be){if(r===void 0)throw Error(P(407));r=r()}else{if(r=t(),ze===null)throw Error(P(349));un&30||kh(n,t,r)}s.memoizedState=r;var i={value:r,getSnapshot:t};return s.queue=i,Ed(_h.bind(null,n,i,e),[e]),n.flags|=2048,ti(9,Sh.bind(null,n,i,r,t),void 0,null),r},useId:function(){var e=Ut(),t=ze.identifierPrefix;if(be){var r=nr,n=rr;r=(n&~(1<<32-At(n)-1)).toString(32)+r,t=":"+t+"R"+r,r=Xs++,0<r&&(t+="H"+r.toString(32)),t+=":"}else r=Tx++,t=":"+t+"r"+r.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Fx={readContext:kt,useCallback:Lh,useContext:kt,useEffect:Xc,useImperativeHandle:Ah,useInsertionEffect:Th,useLayoutEffect:Rh,useMemo:Fh,useReducer:Zl,useRef:Ph,useState:function(){return Zl(ei)},useDebugValue:eu,useDeferredValue:function(e){var t=St();return Dh(t,De.memoizedState,e)},useTransition:function(){var e=Zl(ei)[0],t=St().memoizedState;return[e,t]},useMutableSource:jh,useSyncExternalStore:Nh,useId:Ih,unstable_isNewReconciler:!1},Dx={readContext:kt,useCallback:Lh,useContext:kt,useEffect:Xc,useImperativeHandle:Ah,useInsertionEffect:Th,useLayoutEffect:Rh,useMemo:Fh,useReducer:Gl,useRef:Ph,useState:function(){return Gl(ei)},useDebugValue:eu,useDeferredValue:function(e){var t=St();return De===null?t.memoizedState=e:Dh(t,De.memoizedState,e)},useTransition:function(){var e=Gl(ei)[0],t=St().memoizedState;return[e,t]},useMutableSource:jh,useSyncExternalStore:Nh,useId:Ih,unstable_isNewReconciler:!1};function Et(e,t){if(e&&e.defaultProps){t=_e({},t),e=e.defaultProps;for(var r in e)t[r]===void 0&&(t[r]=e[r]);return t}return t}function Mo(e,t,r,n){t=e.memoizedState,r=r(n,t),r=r==null?t:_e({},t,r),e.memoizedState=r,e.lanes===0&&(e.updateQueue.baseState=r)}var nl={isMounted:function(e){return(e=e._reactInternals)?yn(e)===e:!1},enqueueSetState:function(e,t,r){e=e._reactInternals;var n=et(),s=Dr(e),i=ir(n,s);i.payload=t,r!=null&&(i.callback=r),t=Lr(e,i,s),t!==null&&(Lt(t,e,s,n),qi(t,e,s))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var n=et(),s=Dr(e),i=ir(n,s);i.tag=1,i.payload=t,r!=null&&(i.callback=r),t=Lr(e,i,s),t!==null&&(Lt(t,e,s,n),qi(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=et(),n=Dr(e),s=ir(r,n);s.tag=2,t!=null&&(s.callback=t),t=Lr(e,s,n),t!==null&&(Lt(t,e,n,r),qi(t,e,n))}};function Pd(e,t,r,n,s,i,l){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(n,i,l):t.prototype&&t.prototype.isPureReactComponent?!Gs(r,n)||!Gs(s,i):!0}function Uh(e,t,r){var n=!1,s=Vr,i=t.contextType;return typeof i=="object"&&i!==null?i=kt(i):(s=lt(t)?on:Je.current,n=t.contextTypes,i=(n=n!=null)?Vn(e,s):Vr),t=new t(r,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=nl,e.stateNode=t,t._reactInternals=e,n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=i),t}function Td(e,t,r,n){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(r,n),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(r,n),t.state!==e&&nl.enqueueReplaceState(t,t.state,null)}function $o(e,t,r,n){var s=e.stateNode;s.props=r,s.state=e.memoizedState,s.refs={},Zc(e);var i=t.contextType;typeof i=="object"&&i!==null?s.context=kt(i):(i=lt(t)?on:Je.current,s.context=Vn(e,i)),s.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Mo(e,t,i,r),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&nl.enqueueReplaceState(s,s.state,null),ja(e,r,s,n),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function Gn(e,t){try{var r="",n=t;do r+=u0(n),n=n.return;while(n);var s=r}catch(i){s=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:s,digest:null}}function ql(e,t,r){return{value:e,source:null,stack:r??null,digest:t??null}}function zo(e,t){try{console.error(t.value)}catch(r){setTimeout(function(){throw r})}}var Ix=typeof WeakMap=="function"?WeakMap:Map;function Bh(e,t,r){r=ir(-1,r),r.tag=3,r.payload={element:null};var n=t.value;return r.callback=function(){Ca||(Ca=!0,Jo=n),zo(e,t)},r}function Vh(e,t,r){r=ir(-1,r),r.tag=3;var n=e.type.getDerivedStateFromError;if(typeof n=="function"){var s=t.value;r.payload=function(){return n(s)},r.callback=function(){zo(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(r.callback=function(){zo(e,t),typeof n!="function"&&(Fr===null?Fr=new Set([this]):Fr.add(this));var l=t.stack;this.componentDidCatch(t.value,{componentStack:l!==null?l:""})}),r}function Rd(e,t,r){var n=e.pingCache;if(n===null){n=e.pingCache=new Ix;var s=new Set;n.set(t,s)}else s=n.get(t),s===void 0&&(s=new Set,n.set(t,s));s.has(r)||(s.add(r),e=Kx.bind(null,e,t,r),t.then(e,e))}function Od(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Ad(e,t,r,n,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,r.flags|=131072,r.flags&=-52805,r.tag===1&&(r.alternate===null?r.tag=17:(t=ir(-1,1),t.tag=2,Lr(r,t,1))),r.lanes|=1),e)}var Mx=mr.ReactCurrentOwner,it=!1;function Ke(e,t,r,n){t.child=e===null?yh(t,null,r,n):Hn(t,e.child,r,n)}function Ld(e,t,r,n,s){r=r.render;var i=t.ref;return zn(t,s),n=Kc(e,t,r,n,i,s),r=Yc(),e!==null&&!it?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,cr(e,t,s)):(be&&r&&$c(t),t.flags|=1,Ke(e,t,n,s),t.child)}function Fd(e,t,r,n,s){if(e===null){var i=r.type;return typeof i=="function"&&!ou(i)&&i.defaultProps===void 0&&r.compare===null&&r.defaultProps===void 0?(t.tag=15,t.type=i,Wh(e,t,i,n,s)):(e=ea(r.type,null,n,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&s)){var l=i.memoizedProps;if(r=r.compare,r=r!==null?r:Gs,r(l,n)&&e.ref===t.ref)return cr(e,t,s)}return t.flags|=1,e=Ir(i,n),e.ref=t.ref,e.return=t,t.child=e}function Wh(e,t,r,n,s){if(e!==null){var i=e.memoizedProps;if(Gs(i,n)&&e.ref===t.ref)if(it=!1,t.pendingProps=n=i,(e.lanes&s)!==0)e.flags&131072&&(it=!0);else return t.lanes=e.lanes,cr(e,t,s)}return Uo(e,t,r,n,s)}function Hh(e,t,r){var n=t.pendingProps,s=n.children,i=e!==null?e.memoizedState:null;if(n.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ge(Fn,ut),ut|=r;else{if(!(r&1073741824))return e=i!==null?i.baseLanes|r:r,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ge(Fn,ut),ut|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},n=i!==null?i.baseLanes:r,ge(Fn,ut),ut|=n}else i!==null?(n=i.baseLanes|r,t.memoizedState=null):n=r,ge(Fn,ut),ut|=n;return Ke(e,t,s,r),t.child}function Zh(e,t){var r=t.ref;(e===null&&r!==null||e!==null&&e.ref!==r)&&(t.flags|=512,t.flags|=2097152)}function Uo(e,t,r,n,s){var i=lt(r)?on:Je.current;return i=Vn(t,i),zn(t,s),r=Kc(e,t,r,n,i,s),n=Yc(),e!==null&&!it?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,cr(e,t,s)):(be&&n&&$c(t),t.flags|=1,Ke(e,t,r,s),t.child)}function Dd(e,t,r,n,s){if(lt(r)){var i=!0;xa(t)}else i=!1;if(zn(t,s),t.stateNode===null)Ki(e,t),Uh(t,r,n),$o(t,r,n,s),n=!0;else if(e===null){var l=t.stateNode,o=t.memoizedProps;l.props=o;var c=l.context,u=r.contextType;typeof u=="object"&&u!==null?u=kt(u):(u=lt(r)?on:Je.current,u=Vn(t,u));var d=r.getDerivedStateFromProps,f=typeof d=="function"||typeof l.getSnapshotBeforeUpdate=="function";f||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(o!==n||c!==u)&&Td(t,l,n,u),wr=!1;var h=t.memoizedState;l.state=h,ja(t,n,l,s),c=t.memoizedState,o!==n||h!==c||at.current||wr?(typeof d=="function"&&(Mo(t,r,d,n),c=t.memoizedState),(o=wr||Pd(t,r,o,n,h,c,u))?(f||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount()),typeof l.componentDidMount=="function"&&(t.flags|=4194308)):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=n,t.memoizedState=c),l.props=n,l.state=c,l.context=u,n=o):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),n=!1)}else{l=t.stateNode,wh(e,t),o=t.memoizedProps,u=t.type===t.elementType?o:Et(t.type,o),l.props=u,f=t.pendingProps,h=l.context,c=r.contextType,typeof c=="object"&&c!==null?c=kt(c):(c=lt(r)?on:Je.current,c=Vn(t,c));var w=r.getDerivedStateFromProps;(d=typeof w=="function"||typeof l.getSnapshotBeforeUpdate=="function")||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(o!==f||h!==c)&&Td(t,l,n,c),wr=!1,h=t.memoizedState,l.state=h,ja(t,n,l,s);var p=t.memoizedState;o!==f||h!==p||at.current||wr?(typeof w=="function"&&(Mo(t,r,w,n),p=t.memoizedState),(u=wr||Pd(t,r,u,n,h,p,c)||!1)?(d||typeof l.UNSAFE_componentWillUpdate!="function"&&typeof l.componentWillUpdate!="function"||(typeof l.componentWillUpdate=="function"&&l.componentWillUpdate(n,p,c),typeof l.UNSAFE_componentWillUpdate=="function"&&l.UNSAFE_componentWillUpdate(n,p,c)),typeof l.componentDidUpdate=="function"&&(t.flags|=4),typeof l.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof l.componentDidUpdate!="function"||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=n,t.memoizedState=p),l.props=n,l.state=p,l.context=c,n=u):(typeof l.componentDidUpdate!="function"||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),n=!1)}return Bo(e,t,r,n,i,s)}function Bo(e,t,r,n,s,i){Zh(e,t);var l=(t.flags&128)!==0;if(!n&&!l)return s&&bd(t,r,!1),cr(e,t,i);n=t.stateNode,Mx.current=t;var o=l&&typeof r.getDerivedStateFromError!="function"?null:n.render();return t.flags|=1,e!==null&&l?(t.child=Hn(t,e.child,null,i),t.child=Hn(t,null,o,i)):Ke(e,t,o,i),t.memoizedState=n.state,s&&bd(t,r,!0),t.child}function Gh(e){var t=e.stateNode;t.pendingContext?wd(e,t.pendingContext,t.pendingContext!==t.context):t.context&&wd(e,t.context,!1),Gc(e,t.containerInfo)}function Id(e,t,r,n,s){return Wn(),Uc(s),t.flags|=256,Ke(e,t,r,n),t.child}var Vo={dehydrated:null,treeContext:null,retryLane:0};function Wo(e){return{baseLanes:e,cachePool:null,transitions:null}}function qh(e,t,r){var n=t.pendingProps,s=ke.current,i=!1,l=(t.flags&128)!==0,o;if((o=l)||(o=e!==null&&e.memoizedState===null?!1:(s&2)!==0),o?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),ge(ke,s&1),e===null)return Do(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(l=n.children,e=n.fallback,i?(n=t.mode,i=t.child,l={mode:"hidden",children:l},!(n&1)&&i!==null?(i.childLanes=0,i.pendingProps=l):i=al(l,n,0,null),e=ln(e,n,r,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Wo(r),t.memoizedState=Vo,e):tu(t,l));if(s=e.memoizedState,s!==null&&(o=s.dehydrated,o!==null))return $x(e,t,l,n,o,s,r);if(i){i=n.fallback,l=t.mode,s=e.child,o=s.sibling;var c={mode:"hidden",children:n.children};return!(l&1)&&t.child!==s?(n=t.child,n.childLanes=0,n.pendingProps=c,t.deletions=null):(n=Ir(s,c),n.subtreeFlags=s.subtreeFlags&14680064),o!==null?i=Ir(o,i):(i=ln(i,l,r,null),i.flags|=2),i.return=t,n.return=t,n.sibling=i,t.child=n,n=i,i=t.child,l=e.child.memoizedState,l=l===null?Wo(r):{baseLanes:l.baseLanes|r,cachePool:null,transitions:l.transitions},i.memoizedState=l,i.childLanes=e.childLanes&~r,t.memoizedState=Vo,n}return i=e.child,e=i.sibling,n=Ir(i,{mode:"visible",children:n.children}),!(t.mode&1)&&(n.lanes=r),n.return=t,n.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n}function tu(e,t){return t=al({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Mi(e,t,r,n){return n!==null&&Uc(n),Hn(t,e.child,null,r),e=tu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function $x(e,t,r,n,s,i,l){if(r)return t.flags&256?(t.flags&=-257,n=ql(Error(P(422))),Mi(e,t,l,n)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=n.fallback,s=t.mode,n=al({mode:"visible",children:n.children},s,0,null),i=ln(i,s,l,null),i.flags|=2,n.return=t,i.return=t,n.sibling=i,t.child=n,t.mode&1&&Hn(t,e.child,null,l),t.child.memoizedState=Wo(l),t.memoizedState=Vo,i);if(!(t.mode&1))return Mi(e,t,l,null);if(s.data==="$!"){if(n=s.nextSibling&&s.nextSibling.dataset,n)var o=n.dgst;return n=o,i=Error(P(419)),n=ql(i,n,void 0),Mi(e,t,l,n)}if(o=(l&e.childLanes)!==0,it||o){if(n=ze,n!==null){switch(l&-l){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(n.suspendedLanes|l)?0:s,s!==0&&s!==i.retryLane&&(i.retryLane=s,or(e,s),Lt(n,e,s,-1))}return lu(),n=ql(Error(P(421))),Mi(e,t,l,n)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=Yx.bind(null,e),s._reactRetry=t,null):(e=i.treeContext,dt=Ar(s.nextSibling),ft=t,be=!0,Tt=null,e!==null&&(wt[bt++]=rr,wt[bt++]=nr,wt[bt++]=cn,rr=e.id,nr=e.overflow,cn=t),t=tu(t,n.children),t.flags|=4096,t)}function Md(e,t,r){e.lanes|=t;var n=e.alternate;n!==null&&(n.lanes|=t),Io(e.return,t,r)}function Ql(e,t,r,n,s){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:n,tail:r,tailMode:s}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=n,i.tail=r,i.tailMode=s)}function Qh(e,t,r){var n=t.pendingProps,s=n.revealOrder,i=n.tail;if(Ke(e,t,n.children,r),n=ke.current,n&2)n=n&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Md(e,r,t);else if(e.tag===19)Md(e,r,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}n&=1}if(ge(ke,n),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(r=t.child,s=null;r!==null;)e=r.alternate,e!==null&&Na(e)===null&&(s=r),r=r.sibling;r=s,r===null?(s=t.child,t.child=null):(s=r.sibling,r.sibling=null),Ql(t,!1,s,r,i);break;case"backwards":for(r=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&Na(e)===null){t.child=s;break}e=s.sibling,s.sibling=r,r=s,s=e}Ql(t,!0,r,null,i);break;case"together":Ql(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ki(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function cr(e,t,r){if(e!==null&&(t.dependencies=e.dependencies),dn|=t.lanes,!(r&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(P(153));if(t.child!==null){for(e=t.child,r=Ir(e,e.pendingProps),t.child=r,r.return=t;e.sibling!==null;)e=e.sibling,r=r.sibling=Ir(e,e.pendingProps),r.return=t;r.sibling=null}return t.child}function zx(e,t,r){switch(t.tag){case 3:Gh(t),Wn();break;case 5:bh(t);break;case 1:lt(t.type)&&xa(t);break;case 4:Gc(t,t.stateNode.containerInfo);break;case 10:var n=t.type._context,s=t.memoizedProps.value;ge(wa,n._currentValue),n._currentValue=s;break;case 13:if(n=t.memoizedState,n!==null)return n.dehydrated!==null?(ge(ke,ke.current&1),t.flags|=128,null):r&t.child.childLanes?qh(e,t,r):(ge(ke,ke.current&1),e=cr(e,t,r),e!==null?e.sibling:null);ge(ke,ke.current&1);break;case 19:if(n=(r&t.childLanes)!==0,e.flags&128){if(n)return Qh(e,t,r);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),ge(ke,ke.current),n)break;return null;case 22:case 23:return t.lanes=0,Hh(e,t,r)}return cr(e,t,r)}var Jh,Ho,Kh,Yh;Jh=function(e,t){for(var r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}};Ho=function(){};Kh=function(e,t,r,n){var s=e.memoizedProps;if(s!==n){e=t.stateNode,tn(Gt.current);var i=null;switch(r){case"input":s=mo(e,s),n=mo(e,n),i=[];break;case"select":s=_e({},s,{value:void 0}),n=_e({},n,{value:void 0}),i=[];break;case"textarea":s=go(e,s),n=go(e,n),i=[];break;default:typeof s.onClick!="function"&&typeof n.onClick=="function"&&(e.onclick=pa)}yo(r,n);var l;r=null;for(u in s)if(!n.hasOwnProperty(u)&&s.hasOwnProperty(u)&&s[u]!=null)if(u==="style"){var o=s[u];for(l in o)o.hasOwnProperty(l)&&(r||(r={}),r[l]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(zs.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in n){var c=n[u];if(o=s!=null?s[u]:void 0,n.hasOwnProperty(u)&&c!==o&&(c!=null||o!=null))if(u==="style")if(o){for(l in o)!o.hasOwnProperty(l)||c&&c.hasOwnProperty(l)||(r||(r={}),r[l]="");for(l in c)c.hasOwnProperty(l)&&o[l]!==c[l]&&(r||(r={}),r[l]=c[l])}else r||(i||(i=[]),i.push(u,r)),r=c;else u==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,o=o?o.__html:void 0,c!=null&&o!==c&&(i=i||[]).push(u,c)):u==="children"?typeof c!="string"&&typeof c!="number"||(i=i||[]).push(u,""+c):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(zs.hasOwnProperty(u)?(c!=null&&u==="onScroll"&&ye("scroll",e),i||o===c||(i=[])):(i=i||[]).push(u,c))}r&&(i=i||[]).push("style",r);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};Yh=function(e,t,r,n){r!==n&&(t.flags|=4)};function ys(e,t){if(!be)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;t!==null;)t.alternate!==null&&(r=t),t=t.sibling;r===null?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var n=null;r!==null;)r.alternate!==null&&(n=r),r=r.sibling;n===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:n.sibling=null}}function Ze(e){var t=e.alternate!==null&&e.alternate.child===e.child,r=0,n=0;if(t)for(var s=e.child;s!==null;)r|=s.lanes|s.childLanes,n|=s.subtreeFlags&14680064,n|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)r|=s.lanes|s.childLanes,n|=s.subtreeFlags,n|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=n,e.childLanes=r,t}function Ux(e,t,r){var n=t.pendingProps;switch(zc(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ze(t),null;case 1:return lt(t.type)&&ga(),Ze(t),null;case 3:return n=t.stateNode,Zn(),ve(at),ve(Je),Qc(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Di(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Tt!==null&&(Xo(Tt),Tt=null))),Ho(e,t),Ze(t),null;case 5:qc(t);var s=tn(Ys.current);if(r=t.type,e!==null&&t.stateNode!=null)Kh(e,t,r,n,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!n){if(t.stateNode===null)throw Error(P(166));return Ze(t),null}if(e=tn(Gt.current),Di(t)){n=t.stateNode,r=t.type;var i=t.memoizedProps;switch(n[Wt]=t,n[Js]=i,e=(t.mode&1)!==0,r){case"dialog":ye("cancel",n),ye("close",n);break;case"iframe":case"object":case"embed":ye("load",n);break;case"video":case"audio":for(s=0;s<_s.length;s++)ye(_s[s],n);break;case"source":ye("error",n);break;case"img":case"image":case"link":ye("error",n),ye("load",n);break;case"details":ye("toggle",n);break;case"input":Gu(n,i),ye("invalid",n);break;case"select":n._wrapperState={wasMultiple:!!i.multiple},ye("invalid",n);break;case"textarea":Qu(n,i),ye("invalid",n)}yo(r,i),s=null;for(var l in i)if(i.hasOwnProperty(l)){var o=i[l];l==="children"?typeof o=="string"?n.textContent!==o&&(i.suppressHydrationWarning!==!0&&Fi(n.textContent,o,e),s=["children",o]):typeof o=="number"&&n.textContent!==""+o&&(i.suppressHydrationWarning!==!0&&Fi(n.textContent,o,e),s=["children",""+o]):zs.hasOwnProperty(l)&&o!=null&&l==="onScroll"&&ye("scroll",n)}switch(r){case"input":Ci(n),qu(n,i,!0);break;case"textarea":Ci(n),Ju(n);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(n.onclick=pa)}n=s,t.updateQueue=n,n!==null&&(t.flags|=4)}else{l=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=_m(r)),e==="http://www.w3.org/1999/xhtml"?r==="script"?(e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof n.is=="string"?e=l.createElement(r,{is:n.is}):(e=l.createElement(r),r==="select"&&(l=e,n.multiple?l.multiple=!0:n.size&&(l.size=n.size))):e=l.createElementNS(e,r),e[Wt]=t,e[Js]=n,Jh(e,t,!1,!1),t.stateNode=e;e:{switch(l=vo(r,n),r){case"dialog":ye("cancel",e),ye("close",e),s=n;break;case"iframe":case"object":case"embed":ye("load",e),s=n;break;case"video":case"audio":for(s=0;s<_s.length;s++)ye(_s[s],e);s=n;break;case"source":ye("error",e),s=n;break;case"img":case"image":case"link":ye("error",e),ye("load",e),s=n;break;case"details":ye("toggle",e),s=n;break;case"input":Gu(e,n),s=mo(e,n),ye("invalid",e);break;case"option":s=n;break;case"select":e._wrapperState={wasMultiple:!!n.multiple},s=_e({},n,{value:void 0}),ye("invalid",e);break;case"textarea":Qu(e,n),s=go(e,n),ye("invalid",e);break;default:s=n}yo(r,s),o=s;for(i in o)if(o.hasOwnProperty(i)){var c=o[i];i==="style"?Pm(e,c):i==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&Cm(e,c)):i==="children"?typeof c=="string"?(r!=="textarea"||c!=="")&&Us(e,c):typeof c=="number"&&Us(e,""+c):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(zs.hasOwnProperty(i)?c!=null&&i==="onScroll"&&ye("scroll",e):c!=null&&Sc(e,i,c,l))}switch(r){case"input":Ci(e),qu(e,n,!1);break;case"textarea":Ci(e),Ju(e);break;case"option":n.value!=null&&e.setAttribute("value",""+Br(n.value));break;case"select":e.multiple=!!n.multiple,i=n.value,i!=null?Dn(e,!!n.multiple,i,!1):n.defaultValue!=null&&Dn(e,!!n.multiple,n.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=pa)}switch(r){case"button":case"input":case"select":case"textarea":n=!!n.autoFocus;break e;case"img":n=!0;break e;default:n=!1}}n&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ze(t),null;case 6:if(e&&t.stateNode!=null)Yh(e,t,e.memoizedProps,n);else{if(typeof n!="string"&&t.stateNode===null)throw Error(P(166));if(r=tn(Ys.current),tn(Gt.current),Di(t)){if(n=t.stateNode,r=t.memoizedProps,n[Wt]=t,(i=n.nodeValue!==r)&&(e=ft,e!==null))switch(e.tag){case 3:Fi(n.nodeValue,r,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Fi(n.nodeValue,r,(e.mode&1)!==0)}i&&(t.flags|=4)}else n=(r.nodeType===9?r:r.ownerDocument).createTextNode(n),n[Wt]=t,t.stateNode=n}return Ze(t),null;case 13:if(ve(ke),n=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(be&&dt!==null&&t.mode&1&&!(t.flags&128))gh(),Wn(),t.flags|=98560,i=!1;else if(i=Di(t),n!==null&&n.dehydrated!==null){if(e===null){if(!i)throw Error(P(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(P(317));i[Wt]=t}else Wn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Ze(t),i=!1}else Tt!==null&&(Xo(Tt),Tt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=r,t):(n=n!==null,n!==(e!==null&&e.memoizedState!==null)&&n&&(t.child.flags|=8192,t.mode&1&&(e===null||ke.current&1?Ie===0&&(Ie=3):lu())),t.updateQueue!==null&&(t.flags|=4),Ze(t),null);case 4:return Zn(),Ho(e,t),e===null&&qs(t.stateNode.containerInfo),Ze(t),null;case 10:return Wc(t.type._context),Ze(t),null;case 17:return lt(t.type)&&ga(),Ze(t),null;case 19:if(ve(ke),i=t.memoizedState,i===null)return Ze(t),null;if(n=(t.flags&128)!==0,l=i.rendering,l===null)if(n)ys(i,!1);else{if(Ie!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(l=Na(e),l!==null){for(t.flags|=128,ys(i,!1),n=l.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),t.subtreeFlags=0,n=r,r=t.child;r!==null;)i=r,e=n,i.flags&=14680066,l=i.alternate,l===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=l.childLanes,i.lanes=l.lanes,i.child=l.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=l.memoizedProps,i.memoizedState=l.memoizedState,i.updateQueue=l.updateQueue,i.type=l.type,e=l.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return ge(ke,ke.current&1|2),t.child}e=e.sibling}i.tail!==null&&Pe()>qn&&(t.flags|=128,n=!0,ys(i,!1),t.lanes=4194304)}else{if(!n)if(e=Na(l),e!==null){if(t.flags|=128,n=!0,r=e.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),ys(i,!0),i.tail===null&&i.tailMode==="hidden"&&!l.alternate&&!be)return Ze(t),null}else 2*Pe()-i.renderingStartTime>qn&&r!==1073741824&&(t.flags|=128,n=!0,ys(i,!1),t.lanes=4194304);i.isBackwards?(l.sibling=t.child,t.child=l):(r=i.last,r!==null?r.sibling=l:t.child=l,i.last=l)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Pe(),t.sibling=null,r=ke.current,ge(ke,n?r&1|2:r&1),t):(Ze(t),null);case 22:case 23:return au(),n=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==n&&(t.flags|=8192),n&&t.mode&1?ut&1073741824&&(Ze(t),t.subtreeFlags&6&&(t.flags|=8192)):Ze(t),null;case 24:return null;case 25:return null}throw Error(P(156,t.tag))}function Bx(e,t){switch(zc(t),t.tag){case 1:return lt(t.type)&&ga(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Zn(),ve(at),ve(Je),Qc(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return qc(t),null;case 13:if(ve(ke),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(P(340));Wn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ve(ke),null;case 4:return Zn(),null;case 10:return Wc(t.type._context),null;case 22:case 23:return au(),null;case 24:return null;default:return null}}var $i=!1,qe=!1,Vx=typeof WeakSet=="function"?WeakSet:Set,z=null;function Ln(e,t){var r=e.ref;if(r!==null)if(typeof r=="function")try{r(null)}catch(n){Ee(e,t,n)}else r.current=null}function Zo(e,t,r){try{r()}catch(n){Ee(e,t,n)}}var $d=!1;function Wx(e,t){if(Po=fa,e=nh(),Mc(e)){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{r=(r=e.ownerDocument)&&r.defaultView||window;var n=r.getSelection&&r.getSelection();if(n&&n.rangeCount!==0){r=n.anchorNode;var s=n.anchorOffset,i=n.focusNode;n=n.focusOffset;try{r.nodeType,i.nodeType}catch{r=null;break e}var l=0,o=-1,c=-1,u=0,d=0,f=e,h=null;t:for(;;){for(var w;f!==r||s!==0&&f.nodeType!==3||(o=l+s),f!==i||n!==0&&f.nodeType!==3||(c=l+n),f.nodeType===3&&(l+=f.nodeValue.length),(w=f.firstChild)!==null;)h=f,f=w;for(;;){if(f===e)break t;if(h===r&&++u===s&&(o=l),h===i&&++d===n&&(c=l),(w=f.nextSibling)!==null)break;f=h,h=f.parentNode}f=w}r=o===-1||c===-1?null:{start:o,end:c}}else r=null}r=r||{start:0,end:0}}else r=null;for(To={focusedElem:e,selectionRange:r},fa=!1,z=t;z!==null;)if(t=z,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,z=e;else for(;z!==null;){t=z;try{var p=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(p!==null){var y=p.memoizedProps,b=p.memoizedState,x=t.stateNode,m=x.getSnapshotBeforeUpdate(t.elementType===t.type?y:Et(t.type,y),b);x.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(P(163))}}catch(N){Ee(t,t.return,N)}if(e=t.sibling,e!==null){e.return=t.return,z=e;break}z=t.return}return p=$d,$d=!1,p}function Ls(e,t,r){var n=t.updateQueue;if(n=n!==null?n.lastEffect:null,n!==null){var s=n=n.next;do{if((s.tag&e)===e){var i=s.destroy;s.destroy=void 0,i!==void 0&&Zo(t,r,i)}s=s.next}while(s!==n)}}function sl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var r=t=t.next;do{if((r.tag&e)===e){var n=r.create;r.destroy=n()}r=r.next}while(r!==t)}}function Go(e){var t=e.ref;if(t!==null){var r=e.stateNode;switch(e.tag){case 5:e=r;break;default:e=r}typeof t=="function"?t(e):t.current=e}}function Xh(e){var t=e.alternate;t!==null&&(e.alternate=null,Xh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Wt],delete t[Js],delete t[Ao],delete t[_x],delete t[Cx])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ep(e){return e.tag===5||e.tag===3||e.tag===4}function zd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ep(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function qo(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.nodeType===8?r.parentNode.insertBefore(e,t):r.insertBefore(e,t):(r.nodeType===8?(t=r.parentNode,t.insertBefore(e,r)):(t=r,t.appendChild(e)),r=r._reactRootContainer,r!=null||t.onclick!==null||(t.onclick=pa));else if(n!==4&&(e=e.child,e!==null))for(qo(e,t,r),e=e.sibling;e!==null;)qo(e,t,r),e=e.sibling}function Qo(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.insertBefore(e,t):r.appendChild(e);else if(n!==4&&(e=e.child,e!==null))for(Qo(e,t,r),e=e.sibling;e!==null;)Qo(e,t,r),e=e.sibling}var Ue=null,Pt=!1;function xr(e,t,r){for(r=r.child;r!==null;)tp(e,t,r),r=r.sibling}function tp(e,t,r){if(Zt&&typeof Zt.onCommitFiberUnmount=="function")try{Zt.onCommitFiberUnmount(Ja,r)}catch{}switch(r.tag){case 5:qe||Ln(r,t);case 6:var n=Ue,s=Pt;Ue=null,xr(e,t,r),Ue=n,Pt=s,Ue!==null&&(Pt?(e=Ue,r=r.stateNode,e.nodeType===8?e.parentNode.removeChild(r):e.removeChild(r)):Ue.removeChild(r.stateNode));break;case 18:Ue!==null&&(Pt?(e=Ue,r=r.stateNode,e.nodeType===8?Bl(e.parentNode,r):e.nodeType===1&&Bl(e,r),Hs(e)):Bl(Ue,r.stateNode));break;case 4:n=Ue,s=Pt,Ue=r.stateNode.containerInfo,Pt=!0,xr(e,t,r),Ue=n,Pt=s;break;case 0:case 11:case 14:case 15:if(!qe&&(n=r.updateQueue,n!==null&&(n=n.lastEffect,n!==null))){s=n=n.next;do{var i=s,l=i.destroy;i=i.tag,l!==void 0&&(i&2||i&4)&&Zo(r,t,l),s=s.next}while(s!==n)}xr(e,t,r);break;case 1:if(!qe&&(Ln(r,t),n=r.stateNode,typeof n.componentWillUnmount=="function"))try{n.props=r.memoizedProps,n.state=r.memoizedState,n.componentWillUnmount()}catch(o){Ee(r,t,o)}xr(e,t,r);break;case 21:xr(e,t,r);break;case 22:r.mode&1?(qe=(n=qe)||r.memoizedState!==null,xr(e,t,r),qe=n):xr(e,t,r);break;default:xr(e,t,r)}}function Ud(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var r=e.stateNode;r===null&&(r=e.stateNode=new Vx),t.forEach(function(n){var s=Xx.bind(null,e,n);r.has(n)||(r.add(n),n.then(s,s))})}}function Ct(e,t){var r=t.deletions;if(r!==null)for(var n=0;n<r.length;n++){var s=r[n];try{var i=e,l=t,o=l;e:for(;o!==null;){switch(o.tag){case 5:Ue=o.stateNode,Pt=!1;break e;case 3:Ue=o.stateNode.containerInfo,Pt=!0;break e;case 4:Ue=o.stateNode.containerInfo,Pt=!0;break e}o=o.return}if(Ue===null)throw Error(P(160));tp(i,l,s),Ue=null,Pt=!1;var c=s.alternate;c!==null&&(c.return=null),s.return=null}catch(u){Ee(s,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)rp(t,e),t=t.sibling}function rp(e,t){var r=e.alternate,n=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ct(t,e),$t(e),n&4){try{Ls(3,e,e.return),sl(3,e)}catch(y){Ee(e,e.return,y)}try{Ls(5,e,e.return)}catch(y){Ee(e,e.return,y)}}break;case 1:Ct(t,e),$t(e),n&512&&r!==null&&Ln(r,r.return);break;case 5:if(Ct(t,e),$t(e),n&512&&r!==null&&Ln(r,r.return),e.flags&32){var s=e.stateNode;try{Us(s,"")}catch(y){Ee(e,e.return,y)}}if(n&4&&(s=e.stateNode,s!=null)){var i=e.memoizedProps,l=r!==null?r.memoizedProps:i,o=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{o==="input"&&i.type==="radio"&&i.name!=null&&km(s,i),vo(o,l);var u=vo(o,i);for(l=0;l<c.length;l+=2){var d=c[l],f=c[l+1];d==="style"?Pm(s,f):d==="dangerouslySetInnerHTML"?Cm(s,f):d==="children"?Us(s,f):Sc(s,d,f,u)}switch(o){case"input":ho(s,i);break;case"textarea":Sm(s,i);break;case"select":var h=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!i.multiple;var w=i.value;w!=null?Dn(s,!!i.multiple,w,!1):h!==!!i.multiple&&(i.defaultValue!=null?Dn(s,!!i.multiple,i.defaultValue,!0):Dn(s,!!i.multiple,i.multiple?[]:"",!1))}s[Js]=i}catch(y){Ee(e,e.return,y)}}break;case 6:if(Ct(t,e),$t(e),n&4){if(e.stateNode===null)throw Error(P(162));s=e.stateNode,i=e.memoizedProps;try{s.nodeValue=i}catch(y){Ee(e,e.return,y)}}break;case 3:if(Ct(t,e),$t(e),n&4&&r!==null&&r.memoizedState.isDehydrated)try{Hs(t.containerInfo)}catch(y){Ee(e,e.return,y)}break;case 4:Ct(t,e),$t(e);break;case 13:Ct(t,e),$t(e),s=e.child,s.flags&8192&&(i=s.memoizedState!==null,s.stateNode.isHidden=i,!i||s.alternate!==null&&s.alternate.memoizedState!==null||(su=Pe())),n&4&&Ud(e);break;case 22:if(d=r!==null&&r.memoizedState!==null,e.mode&1?(qe=(u=qe)||d,Ct(t,e),qe=u):Ct(t,e),$t(e),n&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for(z=e,d=e.child;d!==null;){for(f=z=d;z!==null;){switch(h=z,w=h.child,h.tag){case 0:case 11:case 14:case 15:Ls(4,h,h.return);break;case 1:Ln(h,h.return);var p=h.stateNode;if(typeof p.componentWillUnmount=="function"){n=h,r=h.return;try{t=n,p.props=t.memoizedProps,p.state=t.memoizedState,p.componentWillUnmount()}catch(y){Ee(n,r,y)}}break;case 5:Ln(h,h.return);break;case 22:if(h.memoizedState!==null){Vd(f);continue}}w!==null?(w.return=h,z=w):Vd(f)}d=d.sibling}e:for(d=null,f=e;;){if(f.tag===5){if(d===null){d=f;try{s=f.stateNode,u?(i=s.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(o=f.stateNode,c=f.memoizedProps.style,l=c!=null&&c.hasOwnProperty("display")?c.display:null,o.style.display=Em("display",l))}catch(y){Ee(e,e.return,y)}}}else if(f.tag===6){if(d===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(y){Ee(e,e.return,y)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:Ct(t,e),$t(e),n&4&&Ud(e);break;case 21:break;default:Ct(t,e),$t(e)}}function $t(e){var t=e.flags;if(t&2){try{e:{for(var r=e.return;r!==null;){if(ep(r)){var n=r;break e}r=r.return}throw Error(P(160))}switch(n.tag){case 5:var s=n.stateNode;n.flags&32&&(Us(s,""),n.flags&=-33);var i=zd(e);Qo(e,i,s);break;case 3:case 4:var l=n.stateNode.containerInfo,o=zd(e);qo(e,o,l);break;default:throw Error(P(161))}}catch(c){Ee(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Hx(e,t,r){z=e,np(e)}function np(e,t,r){for(var n=(e.mode&1)!==0;z!==null;){var s=z,i=s.child;if(s.tag===22&&n){var l=s.memoizedState!==null||$i;if(!l){var o=s.alternate,c=o!==null&&o.memoizedState!==null||qe;o=$i;var u=qe;if($i=l,(qe=c)&&!u)for(z=s;z!==null;)l=z,c=l.child,l.tag===22&&l.memoizedState!==null?Wd(s):c!==null?(c.return=l,z=c):Wd(s);for(;i!==null;)z=i,np(i),i=i.sibling;z=s,$i=o,qe=u}Bd(e)}else s.subtreeFlags&8772&&i!==null?(i.return=s,z=i):Bd(e)}}function Bd(e){for(;z!==null;){var t=z;if(t.flags&8772){var r=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:qe||sl(5,t);break;case 1:var n=t.stateNode;if(t.flags&4&&!qe)if(r===null)n.componentDidMount();else{var s=t.elementType===t.type?r.memoizedProps:Et(t.type,r.memoizedProps);n.componentDidUpdate(s,r.memoizedState,n.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&_d(t,i,n);break;case 3:var l=t.updateQueue;if(l!==null){if(r=null,t.child!==null)switch(t.child.tag){case 5:r=t.child.stateNode;break;case 1:r=t.child.stateNode}_d(t,l,r)}break;case 5:var o=t.stateNode;if(r===null&&t.flags&4){r=o;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&r.focus();break;case"img":c.src&&(r.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var f=d.dehydrated;f!==null&&Hs(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(P(163))}qe||t.flags&512&&Go(t)}catch(h){Ee(t,t.return,h)}}if(t===e){z=null;break}if(r=t.sibling,r!==null){r.return=t.return,z=r;break}z=t.return}}function Vd(e){for(;z!==null;){var t=z;if(t===e){z=null;break}var r=t.sibling;if(r!==null){r.return=t.return,z=r;break}z=t.return}}function Wd(e){for(;z!==null;){var t=z;try{switch(t.tag){case 0:case 11:case 15:var r=t.return;try{sl(4,t)}catch(c){Ee(t,r,c)}break;case 1:var n=t.stateNode;if(typeof n.componentDidMount=="function"){var s=t.return;try{n.componentDidMount()}catch(c){Ee(t,s,c)}}var i=t.return;try{Go(t)}catch(c){Ee(t,i,c)}break;case 5:var l=t.return;try{Go(t)}catch(c){Ee(t,l,c)}}}catch(c){Ee(t,t.return,c)}if(t===e){z=null;break}var o=t.sibling;if(o!==null){o.return=t.return,z=o;break}z=t.return}}var Zx=Math.ceil,_a=mr.ReactCurrentDispatcher,ru=mr.ReactCurrentOwner,Nt=mr.ReactCurrentBatchConfig,le=0,ze=null,Le=null,Be=0,ut=0,Fn=Gr(0),Ie=0,ri=null,dn=0,il=0,nu=0,Fs=null,st=null,su=0,qn=1/0,er=null,Ca=!1,Jo=null,Fr=null,zi=!1,Cr=null,Ea=0,Ds=0,Ko=null,Yi=-1,Xi=0;function et(){return le&6?Pe():Yi!==-1?Yi:Yi=Pe()}function Dr(e){return e.mode&1?le&2&&Be!==0?Be&-Be:Px.transition!==null?(Xi===0&&(Xi=Um()),Xi):(e=fe,e!==0||(e=window.event,e=e===void 0?16:qm(e.type)),e):1}function Lt(e,t,r,n){if(50<Ds)throw Ds=0,Ko=null,Error(P(185));fi(e,r,n),(!(le&2)||e!==ze)&&(e===ze&&(!(le&2)&&(il|=r),Ie===4&&Nr(e,Be)),ot(e,n),r===1&&le===0&&!(t.mode&1)&&(qn=Pe()+500,tl&&qr()))}function ot(e,t){var r=e.callbackNode;P0(e,t);var n=da(e,e===ze?Be:0);if(n===0)r!==null&&Xu(r),e.callbackNode=null,e.callbackPriority=0;else if(t=n&-n,e.callbackPriority!==t){if(r!=null&&Xu(r),t===1)e.tag===0?Ex(Hd.bind(null,e)):mh(Hd.bind(null,e)),kx(function(){!(le&6)&&qr()}),r=null;else{switch(Bm(n)){case 1:r=Tc;break;case 4:r=$m;break;case 16:r=ua;break;case 536870912:r=zm;break;default:r=ua}r=dp(r,sp.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function sp(e,t){if(Yi=-1,Xi=0,le&6)throw Error(P(327));var r=e.callbackNode;if(Un()&&e.callbackNode!==r)return null;var n=da(e,e===ze?Be:0);if(n===0)return null;if(n&30||n&e.expiredLanes||t)t=Pa(e,n);else{t=n;var s=le;le|=2;var i=ap();(ze!==e||Be!==t)&&(er=null,qn=Pe()+500,an(e,t));do try{Qx();break}catch(o){ip(e,o)}while(1);Vc(),_a.current=i,le=s,Le!==null?t=0:(ze=null,Be=0,t=Ie)}if(t!==0){if(t===2&&(s=ko(e),s!==0&&(n=s,t=Yo(e,s))),t===1)throw r=ri,an(e,0),Nr(e,n),ot(e,Pe()),r;if(t===6)Nr(e,n);else{if(s=e.current.alternate,!(n&30)&&!Gx(s)&&(t=Pa(e,n),t===2&&(i=ko(e),i!==0&&(n=i,t=Yo(e,i))),t===1))throw r=ri,an(e,0),Nr(e,n),ot(e,Pe()),r;switch(e.finishedWork=s,e.finishedLanes=n,t){case 0:case 1:throw Error(P(345));case 2:Yr(e,st,er);break;case 3:if(Nr(e,n),(n&130023424)===n&&(t=su+500-Pe(),10<t)){if(da(e,0)!==0)break;if(s=e.suspendedLanes,(s&n)!==n){et(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=Oo(Yr.bind(null,e,st,er),t);break}Yr(e,st,er);break;case 4:if(Nr(e,n),(n&4194240)===n)break;for(t=e.eventTimes,s=-1;0<n;){var l=31-At(n);i=1<<l,l=t[l],l>s&&(s=l),n&=~i}if(n=s,n=Pe()-n,n=(120>n?120:480>n?480:1080>n?1080:1920>n?1920:3e3>n?3e3:4320>n?4320:1960*Zx(n/1960))-n,10<n){e.timeoutHandle=Oo(Yr.bind(null,e,st,er),n);break}Yr(e,st,er);break;case 5:Yr(e,st,er);break;default:throw Error(P(329))}}}return ot(e,Pe()),e.callbackNode===r?sp.bind(null,e):null}function Yo(e,t){var r=Fs;return e.current.memoizedState.isDehydrated&&(an(e,t).flags|=256),e=Pa(e,t),e!==2&&(t=st,st=r,t!==null&&Xo(t)),e}function Xo(e){st===null?st=e:st.push.apply(st,e)}function Gx(e){for(var t=e;;){if(t.flags&16384){var r=t.updateQueue;if(r!==null&&(r=r.stores,r!==null))for(var n=0;n<r.length;n++){var s=r[n],i=s.getSnapshot;s=s.value;try{if(!Dt(i(),s))return!1}catch{return!1}}}if(r=t.child,t.subtreeFlags&16384&&r!==null)r.return=t,t=r;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Nr(e,t){for(t&=~nu,t&=~il,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var r=31-At(t),n=1<<r;e[r]=-1,t&=~n}}function Hd(e){if(le&6)throw Error(P(327));Un();var t=da(e,0);if(!(t&1))return ot(e,Pe()),null;var r=Pa(e,t);if(e.tag!==0&&r===2){var n=ko(e);n!==0&&(t=n,r=Yo(e,n))}if(r===1)throw r=ri,an(e,0),Nr(e,t),ot(e,Pe()),r;if(r===6)throw Error(P(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Yr(e,st,er),ot(e,Pe()),null}function iu(e,t){var r=le;le|=1;try{return e(t)}finally{le=r,le===0&&(qn=Pe()+500,tl&&qr())}}function fn(e){Cr!==null&&Cr.tag===0&&!(le&6)&&Un();var t=le;le|=1;var r=Nt.transition,n=fe;try{if(Nt.transition=null,fe=1,e)return e()}finally{fe=n,Nt.transition=r,le=t,!(le&6)&&qr()}}function au(){ut=Fn.current,ve(Fn)}function an(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;if(r!==-1&&(e.timeoutHandle=-1,Nx(r)),Le!==null)for(r=Le.return;r!==null;){var n=r;switch(zc(n),n.tag){case 1:n=n.type.childContextTypes,n!=null&&ga();break;case 3:Zn(),ve(at),ve(Je),Qc();break;case 5:qc(n);break;case 4:Zn();break;case 13:ve(ke);break;case 19:ve(ke);break;case 10:Wc(n.type._context);break;case 22:case 23:au()}r=r.return}if(ze=e,Le=e=Ir(e.current,null),Be=ut=t,Ie=0,ri=null,nu=il=dn=0,st=Fs=null,en!==null){for(t=0;t<en.length;t++)if(r=en[t],n=r.interleaved,n!==null){r.interleaved=null;var s=n.next,i=r.pending;if(i!==null){var l=i.next;i.next=s,n.next=l}r.pending=n}en=null}return e}function ip(e,t){do{var r=Le;try{if(Vc(),Qi.current=Sa,ka){for(var n=Se.memoizedState;n!==null;){var s=n.queue;s!==null&&(s.pending=null),n=n.next}ka=!1}if(un=0,$e=De=Se=null,As=!1,Xs=0,ru.current=null,r===null||r.return===null){Ie=1,ri=t,Le=null;break}e:{var i=e,l=r.return,o=r,c=t;if(t=Be,o.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var u=c,d=o,f=d.tag;if(!(d.mode&1)&&(f===0||f===11||f===15)){var h=d.alternate;h?(d.updateQueue=h.updateQueue,d.memoizedState=h.memoizedState,d.lanes=h.lanes):(d.updateQueue=null,d.memoizedState=null)}var w=Od(l);if(w!==null){w.flags&=-257,Ad(w,l,o,i,t),w.mode&1&&Rd(i,u,t),t=w,c=u;var p=t.updateQueue;if(p===null){var y=new Set;y.add(c),t.updateQueue=y}else p.add(c);break e}else{if(!(t&1)){Rd(i,u,t),lu();break e}c=Error(P(426))}}else if(be&&o.mode&1){var b=Od(l);if(b!==null){!(b.flags&65536)&&(b.flags|=256),Ad(b,l,o,i,t),Uc(Gn(c,o));break e}}i=c=Gn(c,o),Ie!==4&&(Ie=2),Fs===null?Fs=[i]:Fs.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var x=Bh(i,c,t);Sd(i,x);break e;case 1:o=c;var m=i.type,g=i.stateNode;if(!(i.flags&128)&&(typeof m.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(Fr===null||!Fr.has(g)))){i.flags|=65536,t&=-t,i.lanes|=t;var N=Vh(i,o,t);Sd(i,N);break e}}i=i.return}while(i!==null)}op(r)}catch(_){t=_,Le===r&&r!==null&&(Le=r=r.return);continue}break}while(1)}function ap(){var e=_a.current;return _a.current=Sa,e===null?Sa:e}function lu(){(Ie===0||Ie===3||Ie===2)&&(Ie=4),ze===null||!(dn&268435455)&&!(il&268435455)||Nr(ze,Be)}function Pa(e,t){var r=le;le|=2;var n=ap();(ze!==e||Be!==t)&&(er=null,an(e,t));do try{qx();break}catch(s){ip(e,s)}while(1);if(Vc(),le=r,_a.current=n,Le!==null)throw Error(P(261));return ze=null,Be=0,Ie}function qx(){for(;Le!==null;)lp(Le)}function Qx(){for(;Le!==null&&!w0();)lp(Le)}function lp(e){var t=up(e.alternate,e,ut);e.memoizedProps=e.pendingProps,t===null?op(e):Le=t,ru.current=null}function op(e){var t=e;do{var r=t.alternate;if(e=t.return,t.flags&32768){if(r=Bx(r,t),r!==null){r.flags&=32767,Le=r;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Ie=6,Le=null;return}}else if(r=Ux(r,t,ut),r!==null){Le=r;return}if(t=t.sibling,t!==null){Le=t;return}Le=t=e}while(t!==null);Ie===0&&(Ie=5)}function Yr(e,t,r){var n=fe,s=Nt.transition;try{Nt.transition=null,fe=1,Jx(e,t,r,n)}finally{Nt.transition=s,fe=n}return null}function Jx(e,t,r,n){do Un();while(Cr!==null);if(le&6)throw Error(P(327));r=e.finishedWork;var s=e.finishedLanes;if(r===null)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(P(177));e.callbackNode=null,e.callbackPriority=0;var i=r.lanes|r.childLanes;if(T0(e,i),e===ze&&(Le=ze=null,Be=0),!(r.subtreeFlags&2064)&&!(r.flags&2064)||zi||(zi=!0,dp(ua,function(){return Un(),null})),i=(r.flags&15990)!==0,r.subtreeFlags&15990||i){i=Nt.transition,Nt.transition=null;var l=fe;fe=1;var o=le;le|=4,ru.current=null,Wx(e,r),rp(r,e),gx(To),fa=!!Po,To=Po=null,e.current=r,Hx(r),b0(),le=o,fe=l,Nt.transition=i}else e.current=r;if(zi&&(zi=!1,Cr=e,Ea=s),i=e.pendingLanes,i===0&&(Fr=null),k0(r.stateNode),ot(e,Pe()),t!==null)for(n=e.onRecoverableError,r=0;r<t.length;r++)s=t[r],n(s.value,{componentStack:s.stack,digest:s.digest});if(Ca)throw Ca=!1,e=Jo,Jo=null,e;return Ea&1&&e.tag!==0&&Un(),i=e.pendingLanes,i&1?e===Ko?Ds++:(Ds=0,Ko=e):Ds=0,qr(),null}function Un(){if(Cr!==null){var e=Bm(Ea),t=Nt.transition,r=fe;try{if(Nt.transition=null,fe=16>e?16:e,Cr===null)var n=!1;else{if(e=Cr,Cr=null,Ea=0,le&6)throw Error(P(331));var s=le;for(le|=4,z=e.current;z!==null;){var i=z,l=i.child;if(z.flags&16){var o=i.deletions;if(o!==null){for(var c=0;c<o.length;c++){var u=o[c];for(z=u;z!==null;){var d=z;switch(d.tag){case 0:case 11:case 15:Ls(8,d,i)}var f=d.child;if(f!==null)f.return=d,z=f;else for(;z!==null;){d=z;var h=d.sibling,w=d.return;if(Xh(d),d===u){z=null;break}if(h!==null){h.return=w,z=h;break}z=w}}}var p=i.alternate;if(p!==null){var y=p.child;if(y!==null){p.child=null;do{var b=y.sibling;y.sibling=null,y=b}while(y!==null)}}z=i}}if(i.subtreeFlags&2064&&l!==null)l.return=i,z=l;else e:for(;z!==null;){if(i=z,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Ls(9,i,i.return)}var x=i.sibling;if(x!==null){x.return=i.return,z=x;break e}z=i.return}}var m=e.current;for(z=m;z!==null;){l=z;var g=l.child;if(l.subtreeFlags&2064&&g!==null)g.return=l,z=g;else e:for(l=m;z!==null;){if(o=z,o.flags&2048)try{switch(o.tag){case 0:case 11:case 15:sl(9,o)}}catch(_){Ee(o,o.return,_)}if(o===l){z=null;break e}var N=o.sibling;if(N!==null){N.return=o.return,z=N;break e}z=o.return}}if(le=s,qr(),Zt&&typeof Zt.onPostCommitFiberRoot=="function")try{Zt.onPostCommitFiberRoot(Ja,e)}catch{}n=!0}return n}finally{fe=r,Nt.transition=t}}return!1}function Zd(e,t,r){t=Gn(r,t),t=Bh(e,t,1),e=Lr(e,t,1),t=et(),e!==null&&(fi(e,1,t),ot(e,t))}function Ee(e,t,r){if(e.tag===3)Zd(e,e,r);else for(;t!==null;){if(t.tag===3){Zd(t,e,r);break}else if(t.tag===1){var n=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(Fr===null||!Fr.has(n))){e=Gn(r,e),e=Vh(t,e,1),t=Lr(t,e,1),e=et(),t!==null&&(fi(t,1,e),ot(t,e));break}}t=t.return}}function Kx(e,t,r){var n=e.pingCache;n!==null&&n.delete(t),t=et(),e.pingedLanes|=e.suspendedLanes&r,ze===e&&(Be&r)===r&&(Ie===4||Ie===3&&(Be&130023424)===Be&&500>Pe()-su?an(e,0):nu|=r),ot(e,t)}function cp(e,t){t===0&&(e.mode&1?(t=Ti,Ti<<=1,!(Ti&130023424)&&(Ti=4194304)):t=1);var r=et();e=or(e,t),e!==null&&(fi(e,t,r),ot(e,r))}function Yx(e){var t=e.memoizedState,r=0;t!==null&&(r=t.retryLane),cp(e,r)}function Xx(e,t){var r=0;switch(e.tag){case 13:var n=e.stateNode,s=e.memoizedState;s!==null&&(r=s.retryLane);break;case 19:n=e.stateNode;break;default:throw Error(P(314))}n!==null&&n.delete(t),cp(e,r)}var up;up=function(e,t,r){if(e!==null)if(e.memoizedProps!==t.pendingProps||at.current)it=!0;else{if(!(e.lanes&r)&&!(t.flags&128))return it=!1,zx(e,t,r);it=!!(e.flags&131072)}else it=!1,be&&t.flags&1048576&&hh(t,va,t.index);switch(t.lanes=0,t.tag){case 2:var n=t.type;Ki(e,t),e=t.pendingProps;var s=Vn(t,Je.current);zn(t,r),s=Kc(null,t,n,e,s,r);var i=Yc();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,lt(n)?(i=!0,xa(t)):i=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,Zc(t),s.updater=nl,t.stateNode=s,s._reactInternals=t,$o(t,n,e,r),t=Bo(null,t,n,!0,i,r)):(t.tag=0,be&&i&&$c(t),Ke(null,t,s,r),t=t.child),t;case 16:n=t.elementType;e:{switch(Ki(e,t),e=t.pendingProps,s=n._init,n=s(n._payload),t.type=n,s=t.tag=ty(n),e=Et(n,e),s){case 0:t=Uo(null,t,n,e,r);break e;case 1:t=Dd(null,t,n,e,r);break e;case 11:t=Ld(null,t,n,e,r);break e;case 14:t=Fd(null,t,n,Et(n.type,e),r);break e}throw Error(P(306,n,""))}return t;case 0:return n=t.type,s=t.pendingProps,s=t.elementType===n?s:Et(n,s),Uo(e,t,n,s,r);case 1:return n=t.type,s=t.pendingProps,s=t.elementType===n?s:Et(n,s),Dd(e,t,n,s,r);case 3:e:{if(Gh(t),e===null)throw Error(P(387));n=t.pendingProps,i=t.memoizedState,s=i.element,wh(e,t),ja(t,n,null,r);var l=t.memoizedState;if(n=l.element,i.isDehydrated)if(i={element:n,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){s=Gn(Error(P(423)),t),t=Id(e,t,n,r,s);break e}else if(n!==s){s=Gn(Error(P(424)),t),t=Id(e,t,n,r,s);break e}else for(dt=Ar(t.stateNode.containerInfo.firstChild),ft=t,be=!0,Tt=null,r=yh(t,null,n,r),t.child=r;r;)r.flags=r.flags&-3|4096,r=r.sibling;else{if(Wn(),n===s){t=cr(e,t,r);break e}Ke(e,t,n,r)}t=t.child}return t;case 5:return bh(t),e===null&&Do(t),n=t.type,s=t.pendingProps,i=e!==null?e.memoizedProps:null,l=s.children,Ro(n,s)?l=null:i!==null&&Ro(n,i)&&(t.flags|=32),Zh(e,t),Ke(e,t,l,r),t.child;case 6:return e===null&&Do(t),null;case 13:return qh(e,t,r);case 4:return Gc(t,t.stateNode.containerInfo),n=t.pendingProps,e===null?t.child=Hn(t,null,n,r):Ke(e,t,n,r),t.child;case 11:return n=t.type,s=t.pendingProps,s=t.elementType===n?s:Et(n,s),Ld(e,t,n,s,r);case 7:return Ke(e,t,t.pendingProps,r),t.child;case 8:return Ke(e,t,t.pendingProps.children,r),t.child;case 12:return Ke(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(n=t.type._context,s=t.pendingProps,i=t.memoizedProps,l=s.value,ge(wa,n._currentValue),n._currentValue=l,i!==null)if(Dt(i.value,l)){if(i.children===s.children&&!at.current){t=cr(e,t,r);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var o=i.dependencies;if(o!==null){l=i.child;for(var c=o.firstContext;c!==null;){if(c.context===n){if(i.tag===1){c=ir(-1,r&-r),c.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?c.next=c:(c.next=d.next,d.next=c),u.pending=c}}i.lanes|=r,c=i.alternate,c!==null&&(c.lanes|=r),Io(i.return,r,t),o.lanes|=r;break}c=c.next}}else if(i.tag===10)l=i.type===t.type?null:i.child;else if(i.tag===18){if(l=i.return,l===null)throw Error(P(341));l.lanes|=r,o=l.alternate,o!==null&&(o.lanes|=r),Io(l,r,t),l=i.sibling}else l=i.child;if(l!==null)l.return=i;else for(l=i;l!==null;){if(l===t){l=null;break}if(i=l.sibling,i!==null){i.return=l.return,l=i;break}l=l.return}i=l}Ke(e,t,s.children,r),t=t.child}return t;case 9:return s=t.type,n=t.pendingProps.children,zn(t,r),s=kt(s),n=n(s),t.flags|=1,Ke(e,t,n,r),t.child;case 14:return n=t.type,s=Et(n,t.pendingProps),s=Et(n.type,s),Fd(e,t,n,s,r);case 15:return Wh(e,t,t.type,t.pendingProps,r);case 17:return n=t.type,s=t.pendingProps,s=t.elementType===n?s:Et(n,s),Ki(e,t),t.tag=1,lt(n)?(e=!0,xa(t)):e=!1,zn(t,r),Uh(t,n,s),$o(t,n,s,r),Bo(null,t,n,!0,e,r);case 19:return Qh(e,t,r);case 22:return Hh(e,t,r)}throw Error(P(156,t.tag))};function dp(e,t){return Mm(e,t)}function ey(e,t,r,n){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function jt(e,t,r,n){return new ey(e,t,r,n)}function ou(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ty(e){if(typeof e=="function")return ou(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Cc)return 11;if(e===Ec)return 14}return 2}function Ir(e,t){var r=e.alternate;return r===null?(r=jt(e.tag,t,e.key,e.mode),r.elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=e.flags&14680064,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function ea(e,t,r,n,s,i){var l=2;if(n=e,typeof e=="function")ou(e)&&(l=1);else if(typeof e=="string")l=5;else e:switch(e){case Sn:return ln(r.children,s,i,t);case _c:l=8,s|=8;break;case oo:return e=jt(12,r,t,s|2),e.elementType=oo,e.lanes=i,e;case co:return e=jt(13,r,t,s),e.elementType=co,e.lanes=i,e;case uo:return e=jt(19,r,t,s),e.elementType=uo,e.lanes=i,e;case bm:return al(r,s,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case vm:l=10;break e;case wm:l=9;break e;case Cc:l=11;break e;case Ec:l=14;break e;case vr:l=16,n=null;break e}throw Error(P(130,e==null?e:typeof e,""))}return t=jt(l,r,t,s),t.elementType=e,t.type=n,t.lanes=i,t}function ln(e,t,r,n){return e=jt(7,e,n,t),e.lanes=r,e}function al(e,t,r,n){return e=jt(22,e,n,t),e.elementType=bm,e.lanes=r,e.stateNode={isHidden:!1},e}function Jl(e,t,r){return e=jt(6,e,null,t),e.lanes=r,e}function Kl(e,t,r){return t=jt(4,e.children!==null?e.children:[],e.key,t),t.lanes=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function ry(e,t,r,n,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Rl(0),this.expirationTimes=Rl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Rl(0),this.identifierPrefix=n,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function cu(e,t,r,n,s,i,l,o,c){return e=new ry(e,t,r,o,c),t===1?(t=1,i===!0&&(t|=8)):t=0,i=jt(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:n,isDehydrated:r,cache:null,transitions:null,pendingSuspenseBoundaries:null},Zc(i),e}function ny(e,t,r){var n=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:kn,key:n==null?null:""+n,children:e,containerInfo:t,implementation:r}}function fp(e){if(!e)return Vr;e=e._reactInternals;e:{if(yn(e)!==e||e.tag!==1)throw Error(P(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(lt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(P(171))}if(e.tag===1){var r=e.type;if(lt(r))return fh(e,r,t)}return t}function mp(e,t,r,n,s,i,l,o,c){return e=cu(r,n,!0,e,s,i,l,o,c),e.context=fp(null),r=e.current,n=et(),s=Dr(r),i=ir(n,s),i.callback=t??null,Lr(r,i,s),e.current.lanes=s,fi(e,s,n),ot(e,n),e}function ll(e,t,r,n){var s=t.current,i=et(),l=Dr(s);return r=fp(r),t.context===null?t.context=r:t.pendingContext=r,t=ir(i,l),t.payload={element:e},n=n===void 0?null:n,n!==null&&(t.callback=n),e=Lr(s,t,l),e!==null&&(Lt(e,s,l,i),qi(e,s,l)),l}function Ta(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Gd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var r=e.retryLane;e.retryLane=r!==0&&r<t?r:t}}function uu(e,t){Gd(e,t),(e=e.alternate)&&Gd(e,t)}function sy(){return null}var hp=typeof reportError=="function"?reportError:function(e){console.error(e)};function du(e){this._internalRoot=e}ol.prototype.render=du.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(P(409));ll(e,t,null,null)};ol.prototype.unmount=du.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;fn(function(){ll(null,e,null,null)}),t[lr]=null}};function ol(e){this._internalRoot=e}ol.prototype.unstable_scheduleHydration=function(e){if(e){var t=Hm();e={blockedOn:null,target:e,priority:t};for(var r=0;r<jr.length&&t!==0&&t<jr[r].priority;r++);jr.splice(r,0,e),r===0&&Gm(e)}};function fu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function cl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function qd(){}function iy(e,t,r,n,s){if(s){if(typeof n=="function"){var i=n;n=function(){var u=Ta(l);i.call(u)}}var l=mp(t,n,e,0,null,!1,!1,"",qd);return e._reactRootContainer=l,e[lr]=l.current,qs(e.nodeType===8?e.parentNode:e),fn(),l}for(;s=e.lastChild;)e.removeChild(s);if(typeof n=="function"){var o=n;n=function(){var u=Ta(c);o.call(u)}}var c=cu(e,0,!1,null,null,!1,!1,"",qd);return e._reactRootContainer=c,e[lr]=c.current,qs(e.nodeType===8?e.parentNode:e),fn(function(){ll(t,c,r,n)}),c}function ul(e,t,r,n,s){var i=r._reactRootContainer;if(i){var l=i;if(typeof s=="function"){var o=s;s=function(){var c=Ta(l);o.call(c)}}ll(t,l,e,s)}else l=iy(r,t,e,s,n);return Ta(l)}Vm=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var r=Ss(t.pendingLanes);r!==0&&(Rc(t,r|1),ot(t,Pe()),!(le&6)&&(qn=Pe()+500,qr()))}break;case 13:fn(function(){var n=or(e,1);if(n!==null){var s=et();Lt(n,e,1,s)}}),uu(e,1)}};Oc=function(e){if(e.tag===13){var t=or(e,134217728);if(t!==null){var r=et();Lt(t,e,134217728,r)}uu(e,134217728)}};Wm=function(e){if(e.tag===13){var t=Dr(e),r=or(e,t);if(r!==null){var n=et();Lt(r,e,t,n)}uu(e,t)}};Hm=function(){return fe};Zm=function(e,t){var r=fe;try{return fe=e,t()}finally{fe=r}};bo=function(e,t,r){switch(t){case"input":if(ho(e,r),t=r.name,r.type==="radio"&&t!=null){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var n=r[t];if(n!==e&&n.form===e.form){var s=el(n);if(!s)throw Error(P(90));Nm(n),ho(n,s)}}}break;case"textarea":Sm(e,r);break;case"select":t=r.value,t!=null&&Dn(e,!!r.multiple,t,!1)}};Om=iu;Am=fn;var ay={usingClientEntryPoint:!1,Events:[hi,Pn,el,Tm,Rm,iu]},vs={findFiberByHostInstance:Xr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},ly={bundleType:vs.bundleType,version:vs.version,rendererPackageName:vs.rendererPackageName,rendererConfig:vs.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:mr.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Dm(e),e===null?null:e.stateNode},findFiberByHostInstance:vs.findFiberByHostInstance||sy,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ui=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ui.isDisabled&&Ui.supportsFiber)try{Ja=Ui.inject(ly),Zt=Ui}catch{}}pt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ay;pt.createPortal=function(e,t){var r=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!fu(t))throw Error(P(200));return ny(e,t,null,r)};pt.createRoot=function(e,t){if(!fu(e))throw Error(P(299));var r=!1,n="",s=hp;return t!=null&&(t.unstable_strictMode===!0&&(r=!0),t.identifierPrefix!==void 0&&(n=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=cu(e,1,!1,null,null,r,!1,n,s),e[lr]=t.current,qs(e.nodeType===8?e.parentNode:e),new du(t)};pt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(P(188)):(e=Object.keys(e).join(","),Error(P(268,e)));return e=Dm(t),e=e===null?null:e.stateNode,e};pt.flushSync=function(e){return fn(e)};pt.hydrate=function(e,t,r){if(!cl(t))throw Error(P(200));return ul(null,e,t,!0,r)};pt.hydrateRoot=function(e,t,r){if(!fu(e))throw Error(P(405));var n=r!=null&&r.hydratedSources||null,s=!1,i="",l=hp;if(r!=null&&(r.unstable_strictMode===!0&&(s=!0),r.identifierPrefix!==void 0&&(i=r.identifierPrefix),r.onRecoverableError!==void 0&&(l=r.onRecoverableError)),t=mp(t,null,e,1,r??null,s,!1,i,l),e[lr]=t.current,qs(e),n)for(e=0;e<n.length;e++)r=n[e],s=r._getVersion,s=s(r._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[r,s]:t.mutableSourceEagerHydrationData.push(r,s);return new ol(t)};pt.render=function(e,t,r){if(!cl(t))throw Error(P(200));return ul(null,e,t,!1,r)};pt.unmountComponentAtNode=function(e){if(!cl(e))throw Error(P(40));return e._reactRootContainer?(fn(function(){ul(null,null,e,!1,function(){e._reactRootContainer=null,e[lr]=null})}),!0):!1};pt.unstable_batchedUpdates=iu;pt.unstable_renderSubtreeIntoContainer=function(e,t,r,n){if(!cl(r))throw Error(P(200));if(e==null||e._reactInternals===void 0)throw Error(P(38));return ul(e,t,r,!1,n)};pt.version="18.3.1-next-f1338f8080-20240426";function pp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(pp)}catch(e){console.error(e)}}pp(),pm.exports=pt;var oy=pm.exports,Qd=oy;ao.createRoot=Qd.createRoot,ao.hydrateRoot=Qd.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ni(){return ni=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ni.apply(this,arguments)}var Er;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Er||(Er={}));const Jd="popstate";function cy(e){e===void 0&&(e={});function t(n,s){let{pathname:i,search:l,hash:o}=n.location;return ec("",{pathname:i,search:l,hash:o},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function r(n,s){return typeof s=="string"?s:Ra(s)}return dy(t,r,null,e)}function Te(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function gp(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function uy(){return Math.random().toString(36).substr(2,8)}function Kd(e,t){return{usr:e.state,key:e.key,idx:t}}function ec(e,t,r,n){return r===void 0&&(r=null),ni({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?as(t):t,{state:r,key:t&&t.key||n||uy()})}function Ra(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&r!=="?"&&(t+=r.charAt(0)==="?"?r:"?"+r),n&&n!=="#"&&(t+=n.charAt(0)==="#"?n:"#"+n),t}function as(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}function dy(e,t,r,n){n===void 0&&(n={});let{window:s=document.defaultView,v5Compat:i=!1}=n,l=s.history,o=Er.Pop,c=null,u=d();u==null&&(u=0,l.replaceState(ni({},l.state,{idx:u}),""));function d(){return(l.state||{idx:null}).idx}function f(){o=Er.Pop;let b=d(),x=b==null?null:b-u;u=b,c&&c({action:o,location:y.location,delta:x})}function h(b,x){o=Er.Push;let m=ec(y.location,b,x);r&&r(m,b),u=d()+1;let g=Kd(m,u),N=y.createHref(m);try{l.pushState(g,"",N)}catch(_){if(_ instanceof DOMException&&_.name==="DataCloneError")throw _;s.location.assign(N)}i&&c&&c({action:o,location:y.location,delta:1})}function w(b,x){o=Er.Replace;let m=ec(y.location,b,x);r&&r(m,b),u=d();let g=Kd(m,u),N=y.createHref(m);l.replaceState(g,"",N),i&&c&&c({action:o,location:y.location,delta:0})}function p(b){let x=s.location.origin!=="null"?s.location.origin:s.location.href,m=typeof b=="string"?b:Ra(b);return m=m.replace(/ $/,"%20"),Te(x,"No window.location.(origin|href) available to create URL for href: "+m),new URL(m,x)}let y={get action(){return o},get location(){return e(s,l)},listen(b){if(c)throw new Error("A history only accepts one active listener");return s.addEventListener(Jd,f),c=b,()=>{s.removeEventListener(Jd,f),c=null}},createHref(b){return t(s,b)},createURL:p,encodeLocation(b){let x=p(b);return{pathname:x.pathname,search:x.search,hash:x.hash}},push:h,replace:w,go(b){return l.go(b)}};return y}var Yd;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Yd||(Yd={}));function fy(e,t,r){return r===void 0&&(r="/"),my(e,t,r,!1)}function my(e,t,r,n){let s=typeof t=="string"?as(t):t,i=mu(s.pathname||"/",r);if(i==null)return null;let l=xp(e);hy(l);let o=null;for(let c=0;o==null&&c<l.length;++c){let u=Sy(i);o=Ny(l[c],u,n)}return o}function xp(e,t,r,n){t===void 0&&(t=[]),r===void 0&&(r=[]),n===void 0&&(n="");let s=(i,l,o)=>{let c={relativePath:o===void 0?i.path||"":o,caseSensitive:i.caseSensitive===!0,childrenIndex:l,route:i};c.relativePath.startsWith("/")&&(Te(c.relativePath.startsWith(n),'Absolute route path "'+c.relativePath+'" nested under path '+('"'+n+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),c.relativePath=c.relativePath.slice(n.length));let u=Mr([n,c.relativePath]),d=r.concat(c);i.children&&i.children.length>0&&(Te(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),xp(i.children,t,d,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:by(u,i.index),routesMeta:d})};return e.forEach((i,l)=>{var o;if(i.path===""||!((o=i.path)!=null&&o.includes("?")))s(i,l);else for(let c of yp(i.path))s(i,l,c)}),t}function yp(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,s=r.endsWith("?"),i=r.replace(/\?$/,"");if(n.length===0)return s?[i,""]:[i];let l=yp(n.join("/")),o=[];return o.push(...l.map(c=>c===""?i:[i,c].join("/"))),s&&o.push(...l),o.map(c=>e.startsWith("/")&&c===""?"/":c)}function hy(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:jy(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}const py=/^:[\w-]+$/,gy=3,xy=2,yy=1,vy=10,wy=-2,Xd=e=>e==="*";function by(e,t){let r=e.split("/"),n=r.length;return r.some(Xd)&&(n+=wy),t&&(n+=xy),r.filter(s=>!Xd(s)).reduce((s,i)=>s+(py.test(i)?gy:i===""?yy:vy),n)}function jy(e,t){return e.length===t.length&&e.slice(0,-1).every((n,s)=>n===t[s])?e[e.length-1]-t[t.length-1]:0}function Ny(e,t,r){r===void 0&&(r=!1);let{routesMeta:n}=e,s={},i="/",l=[];for(let o=0;o<n.length;++o){let c=n[o],u=o===n.length-1,d=i==="/"?t:t.slice(i.length)||"/",f=ef({path:c.relativePath,caseSensitive:c.caseSensitive,end:u},d),h=c.route;if(!f&&u&&r&&!n[n.length-1].route.index&&(f=ef({path:c.relativePath,caseSensitive:c.caseSensitive,end:!1},d)),!f)return null;Object.assign(s,f.params),l.push({params:s,pathname:Mr([i,f.pathname]),pathnameBase:Py(Mr([i,f.pathnameBase])),route:h}),f.pathnameBase!=="/"&&(i=Mr([i,f.pathnameBase]))}return l}function ef(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=ky(e.path,e.caseSensitive,e.end),s=t.match(r);if(!s)return null;let i=s[0],l=i.replace(/(.)\/+$/,"$1"),o=s.slice(1);return{params:n.reduce((u,d,f)=>{let{paramName:h,isOptional:w}=d;if(h==="*"){let y=o[f]||"";l=i.slice(0,i.length-y.length).replace(/(.)\/+$/,"$1")}const p=o[f];return w&&!p?u[h]=void 0:u[h]=(p||"").replace(/%2F/g,"/"),u},{}),pathname:i,pathnameBase:l,pattern:e}}function ky(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!0),gp(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let n=[],s="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(l,o,c)=>(n.push({paramName:o,isOptional:c!=null}),c?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),s+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?s+="\\/*$":e!==""&&e!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,t?void 0:"i"),n]}function Sy(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return gp(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function mu(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function _y(e,t){t===void 0&&(t="/");let{pathname:r,search:n="",hash:s=""}=typeof e=="string"?as(e):e;return{pathname:r?r.startsWith("/")?r:Cy(r,t):t,search:Ty(n),hash:Ry(s)}}function Cy(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(s=>{s===".."?r.length>1&&r.pop():s!=="."&&r.push(s)}),r.length>1?r.join("/"):"/"}function Yl(e,t,r,n){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(n)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Ey(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function hu(e,t){let r=Ey(e);return t?r.map((n,s)=>s===r.length-1?n.pathname:n.pathnameBase):r.map(n=>n.pathnameBase)}function pu(e,t,r,n){n===void 0&&(n=!1);let s;typeof e=="string"?s=as(e):(s=ni({},e),Te(!s.pathname||!s.pathname.includes("?"),Yl("?","pathname","search",s)),Te(!s.pathname||!s.pathname.includes("#"),Yl("#","pathname","hash",s)),Te(!s.search||!s.search.includes("#"),Yl("#","search","hash",s)));let i=e===""||s.pathname==="",l=i?"/":s.pathname,o;if(l==null)o=r;else{let f=t.length-1;if(!n&&l.startsWith("..")){let h=l.split("/");for(;h[0]==="..";)h.shift(),f-=1;s.pathname=h.join("/")}o=f>=0?t[f]:"/"}let c=_y(s,o),u=l&&l!=="/"&&l.endsWith("/"),d=(i||l===".")&&r.endsWith("/");return!c.pathname.endsWith("/")&&(u||d)&&(c.pathname+="/"),c}const Mr=e=>e.join("/").replace(/\/\/+/g,"/"),Py=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ty=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ry=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Oy(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const vp=["post","put","patch","delete"];new Set(vp);const Ay=["get",...vp];new Set(Ay);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function si(){return si=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},si.apply(this,arguments)}const gu=j.createContext(null),Ly=j.createContext(null),Qr=j.createContext(null),dl=j.createContext(null),Qt=j.createContext({outlet:null,matches:[],isDataRoute:!1}),wp=j.createContext(null);function Fy(e,t){let{relative:r}=t===void 0?{}:t;ls()||Te(!1);let{basename:n,navigator:s}=j.useContext(Qr),{hash:i,pathname:l,search:o}=jp(e,{relative:r}),c=l;return n!=="/"&&(c=l==="/"?n:Mr([n,l])),s.createHref({pathname:c,search:o,hash:i})}function ls(){return j.useContext(dl)!=null}function hr(){return ls()||Te(!1),j.useContext(dl).location}function bp(e){j.useContext(Qr).static||j.useLayoutEffect(e)}function xt(){let{isDataRoute:e}=j.useContext(Qt);return e?Jy():Dy()}function Dy(){ls()||Te(!1);let e=j.useContext(gu),{basename:t,future:r,navigator:n}=j.useContext(Qr),{matches:s}=j.useContext(Qt),{pathname:i}=hr(),l=JSON.stringify(hu(s,r.v7_relativeSplatPath)),o=j.useRef(!1);return bp(()=>{o.current=!0}),j.useCallback(function(u,d){if(d===void 0&&(d={}),!o.current)return;if(typeof u=="number"){n.go(u);return}let f=pu(u,JSON.parse(l),i,d.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:Mr([t,f.pathname])),(d.replace?n.replace:n.push)(f,d.state,d)},[t,n,l,i,e])}const Iy=j.createContext(null);function My(e){let t=j.useContext(Qt).outlet;return t&&j.createElement(Iy.Provider,{value:e},t)}function os(){let{matches:e}=j.useContext(Qt),t=e[e.length-1];return t?t.params:{}}function jp(e,t){let{relative:r}=t===void 0?{}:t,{future:n}=j.useContext(Qr),{matches:s}=j.useContext(Qt),{pathname:i}=hr(),l=JSON.stringify(hu(s,n.v7_relativeSplatPath));return j.useMemo(()=>pu(e,JSON.parse(l),i,r==="path"),[e,l,i,r])}function $y(e,t){return zy(e,t)}function zy(e,t,r,n){ls()||Te(!1);let{navigator:s}=j.useContext(Qr),{matches:i}=j.useContext(Qt),l=i[i.length-1],o=l?l.params:{};l&&l.pathname;let c=l?l.pathnameBase:"/";l&&l.route;let u=hr(),d;if(t){var f;let b=typeof t=="string"?as(t):t;c==="/"||(f=b.pathname)!=null&&f.startsWith(c)||Te(!1),d=b}else d=u;let h=d.pathname||"/",w=h;if(c!=="/"){let b=c.replace(/^\//,"").split("/");w="/"+h.replace(/^\//,"").split("/").slice(b.length).join("/")}let p=fy(e,{pathname:w}),y=Hy(p&&p.map(b=>Object.assign({},b,{params:Object.assign({},o,b.params),pathname:Mr([c,s.encodeLocation?s.encodeLocation(b.pathname).pathname:b.pathname]),pathnameBase:b.pathnameBase==="/"?c:Mr([c,s.encodeLocation?s.encodeLocation(b.pathnameBase).pathname:b.pathnameBase])})),i,r,n);return t&&y?j.createElement(dl.Provider,{value:{location:si({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:Er.Pop}},y):y}function Uy(){let e=Qy(),t=Oy(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,s={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},i=null;return j.createElement(j.Fragment,null,j.createElement("h2",null,"Unexpected Application Error!"),j.createElement("h3",{style:{fontStyle:"italic"}},t),r?j.createElement("pre",{style:s},r):null,i)}const By=j.createElement(Uy,null);class Vy extends j.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,r){return r.location!==t.location||r.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:r.error,location:r.location,revalidation:t.revalidation||r.revalidation}}componentDidCatch(t,r){console.error("React Router caught the following error during render",t,r)}render(){return this.state.error!==void 0?j.createElement(Qt.Provider,{value:this.props.routeContext},j.createElement(wp.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Wy(e){let{routeContext:t,match:r,children:n}=e,s=j.useContext(gu);return s&&s.static&&s.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=r.route.id),j.createElement(Qt.Provider,{value:t},n)}function Hy(e,t,r,n){var s;if(t===void 0&&(t=[]),r===void 0&&(r=null),n===void 0&&(n=null),e==null){var i;if(!r)return null;if(r.errors)e=r.matches;else if((i=n)!=null&&i.v7_partialHydration&&t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let l=e,o=(s=r)==null?void 0:s.errors;if(o!=null){let d=l.findIndex(f=>f.route.id&&(o==null?void 0:o[f.route.id])!==void 0);d>=0||Te(!1),l=l.slice(0,Math.min(l.length,d+1))}let c=!1,u=-1;if(r&&n&&n.v7_partialHydration)for(let d=0;d<l.length;d++){let f=l[d];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(u=d),f.route.id){let{loaderData:h,errors:w}=r,p=f.route.loader&&h[f.route.id]===void 0&&(!w||w[f.route.id]===void 0);if(f.route.lazy||p){c=!0,u>=0?l=l.slice(0,u+1):l=[l[0]];break}}}return l.reduceRight((d,f,h)=>{let w,p=!1,y=null,b=null;r&&(w=o&&f.route.id?o[f.route.id]:void 0,y=f.route.errorElement||By,c&&(u<0&&h===0?(Ky("route-fallback",!1),p=!0,b=null):u===h&&(p=!0,b=f.route.hydrateFallbackElement||null)));let x=t.concat(l.slice(0,h+1)),m=()=>{let g;return w?g=y:p?g=b:f.route.Component?g=j.createElement(f.route.Component,null):f.route.element?g=f.route.element:g=d,j.createElement(Wy,{match:f,routeContext:{outlet:d,matches:x,isDataRoute:r!=null},children:g})};return r&&(f.route.ErrorBoundary||f.route.errorElement||h===0)?j.createElement(Vy,{location:r.location,revalidation:r.revalidation,component:y,error:w,children:m(),routeContext:{outlet:null,matches:x,isDataRoute:!0}}):m()},null)}var Np=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Np||{}),Oa=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Oa||{});function Zy(e){let t=j.useContext(gu);return t||Te(!1),t}function Gy(e){let t=j.useContext(Ly);return t||Te(!1),t}function qy(e){let t=j.useContext(Qt);return t||Te(!1),t}function kp(e){let t=qy(),r=t.matches[t.matches.length-1];return r.route.id||Te(!1),r.route.id}function Qy(){var e;let t=j.useContext(wp),r=Gy(Oa.UseRouteError),n=kp(Oa.UseRouteError);return t!==void 0?t:(e=r.errors)==null?void 0:e[n]}function Jy(){let{router:e}=Zy(Np.UseNavigateStable),t=kp(Oa.UseNavigateStable),r=j.useRef(!1);return bp(()=>{r.current=!0}),j.useCallback(function(s,i){i===void 0&&(i={}),r.current&&(typeof s=="number"?e.navigate(s):e.navigate(s,si({fromRouteId:t},i)))},[e,t])}const tf={};function Ky(e,t,r){!t&&!tf[e]&&(tf[e]=!0)}function Yy(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function Bt(e){let{to:t,replace:r,state:n,relative:s}=e;ls()||Te(!1);let{future:i,static:l}=j.useContext(Qr),{matches:o}=j.useContext(Qt),{pathname:c}=hr(),u=xt(),d=pu(t,hu(o,i.v7_relativeSplatPath),c,s==="path"),f=JSON.stringify(d);return j.useEffect(()=>u(JSON.parse(f),{replace:r,state:n,relative:s}),[u,f,s,r,n]),null}function Sp(e){return My(e.context)}function we(e){Te(!1)}function Xy(e){let{basename:t="/",children:r=null,location:n,navigationType:s=Er.Pop,navigator:i,static:l=!1,future:o}=e;ls()&&Te(!1);let c=t.replace(/^\/*/,"/"),u=j.useMemo(()=>({basename:c,navigator:i,static:l,future:si({v7_relativeSplatPath:!1},o)}),[c,o,i,l]);typeof n=="string"&&(n=as(n));let{pathname:d="/",search:f="",hash:h="",state:w=null,key:p="default"}=n,y=j.useMemo(()=>{let b=mu(d,c);return b==null?null:{location:{pathname:b,search:f,hash:h,state:w,key:p},navigationType:s}},[c,d,f,h,w,p,s]);return y==null?null:j.createElement(Qr.Provider,{value:u},j.createElement(dl.Provider,{children:r,value:y}))}function ev(e){let{children:t,location:r}=e;return $y(tc(t),r)}new Promise(()=>{});function tc(e,t){t===void 0&&(t=[]);let r=[];return j.Children.forEach(e,(n,s)=>{if(!j.isValidElement(n))return;let i=[...t,s];if(n.type===j.Fragment){r.push.apply(r,tc(n.props.children,i));return}n.type!==we&&Te(!1),!n.props.index||!n.props.children||Te(!1);let l={id:n.props.id||i.join("-"),caseSensitive:n.props.caseSensitive,element:n.props.element,Component:n.props.Component,index:n.props.index,path:n.props.path,loader:n.props.loader,action:n.props.action,errorElement:n.props.errorElement,ErrorBoundary:n.props.ErrorBoundary,hasErrorBoundary:n.props.ErrorBoundary!=null||n.props.errorElement!=null,shouldRevalidate:n.props.shouldRevalidate,handle:n.props.handle,lazy:n.props.lazy};n.props.children&&(l.children=tc(n.props.children,i)),r.push(l)}),r}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function rc(){return rc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},rc.apply(this,arguments)}function tv(e,t){if(e==null)return{};var r={},n=Object.keys(e),s,i;for(i=0;i<n.length;i++)s=n[i],!(t.indexOf(s)>=0)&&(r[s]=e[s]);return r}function rv(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function nv(e,t){return e.button===0&&(!t||t==="_self")&&!rv(e)}function nc(e){return e===void 0&&(e=""),new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,r)=>{let n=e[r];return t.concat(Array.isArray(n)?n.map(s=>[r,s]):[[r,n]])},[]))}function sv(e,t){let r=nc(e);return t&&t.forEach((n,s)=>{r.has(s)||t.getAll(s).forEach(i=>{r.append(s,i)})}),r}const iv=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],av="6";try{window.__reactRouterVersion=av}catch{}const lv="startTransition",rf=Kg[lv];function ov(e){let{basename:t,children:r,future:n,window:s}=e,i=j.useRef();i.current==null&&(i.current=cy({window:s,v5Compat:!0}));let l=i.current,[o,c]=j.useState({action:l.action,location:l.location}),{v7_startTransition:u}=n||{},d=j.useCallback(f=>{u&&rf?rf(()=>c(f)):c(f)},[c,u]);return j.useLayoutEffect(()=>l.listen(d),[l,d]),j.useEffect(()=>Yy(n),[n]),j.createElement(Xy,{basename:t,children:r,location:o.location,navigationType:o.action,navigator:l,future:n})}const cv=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",uv=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,M=j.forwardRef(function(t,r){let{onClick:n,relative:s,reloadDocument:i,replace:l,state:o,target:c,to:u,preventScrollReset:d,viewTransition:f}=t,h=tv(t,iv),{basename:w}=j.useContext(Qr),p,y=!1;if(typeof u=="string"&&uv.test(u)&&(p=u,cv))try{let g=new URL(window.location.href),N=u.startsWith("//")?new URL(g.protocol+u):new URL(u),_=mu(N.pathname,w);N.origin===g.origin&&_!=null?u=_+N.search+N.hash:y=!0}catch{}let b=Fy(u,{relative:s}),x=dv(u,{replace:l,state:o,target:c,preventScrollReset:d,relative:s,viewTransition:f});function m(g){n&&n(g),g.defaultPrevented||x(g)}return j.createElement("a",rc({},h,{href:p||b,onClick:y||i?n:m,ref:r,target:c}))});var nf;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(nf||(nf={}));var sf;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(sf||(sf={}));function dv(e,t){let{target:r,replace:n,state:s,preventScrollReset:i,relative:l,viewTransition:o}=t===void 0?{}:t,c=xt(),u=hr(),d=jp(e,{relative:l});return j.useCallback(f=>{if(nv(f,r)){f.preventDefault();let h=n!==void 0?n:Ra(u)===Ra(d);c(e,{replace:h,state:s,preventScrollReset:i,relative:l,viewTransition:o})}},[u,c,d,n,s,r,e,i,l,o])}function _p(e){let t=j.useRef(nc(e)),r=j.useRef(!1),n=hr(),s=j.useMemo(()=>sv(n.search,r.current?null:t.current),[n.search]),i=xt(),l=j.useCallback((o,c)=>{const u=nc(typeof o=="function"?o(s):o);r.current=!0,i("?"+u,c)},[i,s]);return[s,l]}let fv={data:""},mv=e=>typeof window=="object"?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||fv,hv=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,pv=/\/\*[^]*?\*\/|  +/g,af=/\n+/g,kr=(e,t)=>{let r="",n="",s="";for(let i in e){let l=e[i];i[0]=="@"?i[1]=="i"?r=i+" "+l+";":n+=i[1]=="f"?kr(l,i):i+"{"+kr(l,i[1]=="k"?"":t)+"}":typeof l=="object"?n+=kr(l,t?t.replace(/([^,])+/g,o=>i.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,c=>/&/.test(c)?c.replace(/&/g,o):o?o+" "+c:c)):i):l!=null&&(i=/^--/.test(i)?i:i.replace(/[A-Z]/g,"-$&").toLowerCase(),s+=kr.p?kr.p(i,l):i+":"+l+";")}return r+(t&&s?t+"{"+s+"}":s)+n},Yt={},Cp=e=>{if(typeof e=="object"){let t="";for(let r in e)t+=r+Cp(e[r]);return t}return e},gv=(e,t,r,n,s)=>{let i=Cp(e),l=Yt[i]||(Yt[i]=(c=>{let u=0,d=11;for(;u<c.length;)d=101*d+c.charCodeAt(u++)>>>0;return"go"+d})(i));if(!Yt[l]){let c=i!==e?e:(u=>{let d,f,h=[{}];for(;d=hv.exec(u.replace(pv,""));)d[4]?h.shift():d[3]?(f=d[3].replace(af," ").trim(),h.unshift(h[0][f]=h[0][f]||{})):h[0][d[1]]=d[2].replace(af," ").trim();return h[0]})(e);Yt[l]=kr(s?{["@keyframes "+l]:c}:c,r?"":"."+l)}let o=r&&Yt.g?Yt.g:null;return r&&(Yt.g=Yt[l]),((c,u,d,f)=>{f?u.data=u.data.replace(f,c):u.data.indexOf(c)===-1&&(u.data=d?c+u.data:u.data+c)})(Yt[l],t,n,o),l},xv=(e,t,r)=>e.reduce((n,s,i)=>{let l=t[i];if(l&&l.call){let o=l(r),c=o&&o.props&&o.props.className||/^go/.test(o)&&o;l=c?"."+c:o&&typeof o=="object"?o.props?"":kr(o,""):o===!1?"":o}return n+s+(l??"")},"");function fl(e){let t=this||{},r=e.call?e(t.p):e;return gv(r.unshift?r.raw?xv(r,[].slice.call(arguments,1),t.p):r.reduce((n,s)=>Object.assign(n,s&&s.call?s(t.p):s),{}):r,mv(t.target),t.g,t.o,t.k)}let Ep,sc,ic;fl.bind({g:1});let ur=fl.bind({k:1});function yv(e,t,r,n){kr.p=t,Ep=e,sc=r,ic=n}function Jr(e,t){let r=this||{};return function(){let n=arguments;function s(i,l){let o=Object.assign({},i),c=o.className||s.className;r.p=Object.assign({theme:sc&&sc()},o),r.o=/ *go\d+/.test(c),o.className=fl.apply(r,n)+(c?" "+c:""),t&&(o.ref=l);let u=e;return e[0]&&(u=o.as||e,delete o.as),ic&&u[0]&&ic(o),Ep(u,o)}return t?t(s):s}}var vv=e=>typeof e=="function",Aa=(e,t)=>vv(e)?e(t):e,wv=(()=>{let e=0;return()=>(++e).toString()})(),Pp=(()=>{let e;return()=>{if(e===void 0&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),bv=20,Tp=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,bv)};case 1:return{...e,toasts:e.toasts.map(i=>i.id===t.toast.id?{...i,...t.toast}:i)};case 2:let{toast:r}=t;return Tp(e,{type:e.toasts.find(i=>i.id===r.id)?1:0,toast:r});case 3:let{toastId:n}=t;return{...e,toasts:e.toasts.map(i=>i.id===n||n===void 0?{...i,dismissed:!0,visible:!1}:i)};case 4:return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(i=>i.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let s=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(i=>({...i,pauseDuration:i.pauseDuration+s}))}}},ta=[],rn={toasts:[],pausedAt:void 0},vn=e=>{rn=Tp(rn,e),ta.forEach(t=>{t(rn)})},jv={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},Nv=(e={})=>{let[t,r]=j.useState(rn),n=j.useRef(rn);j.useEffect(()=>(n.current!==rn&&r(rn),ta.push(r),()=>{let i=ta.indexOf(r);i>-1&&ta.splice(i,1)}),[]);let s=t.toasts.map(i=>{var l,o,c;return{...e,...e[i.type],...i,removeDelay:i.removeDelay||((l=e[i.type])==null?void 0:l.removeDelay)||(e==null?void 0:e.removeDelay),duration:i.duration||((o=e[i.type])==null?void 0:o.duration)||(e==null?void 0:e.duration)||jv[i.type],style:{...e.style,...(c=e[i.type])==null?void 0:c.style,...i.style}}});return{...t,toasts:s}},kv=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(r==null?void 0:r.id)||wv()}),gi=e=>(t,r)=>{let n=kv(t,e,r);return vn({type:2,toast:n}),n.id},Xe=(e,t)=>gi("blank")(e,t);Xe.error=gi("error");Xe.success=gi("success");Xe.loading=gi("loading");Xe.custom=gi("custom");Xe.dismiss=e=>{vn({type:3,toastId:e})};Xe.remove=e=>vn({type:4,toastId:e});Xe.promise=(e,t,r)=>{let n=Xe.loading(t.loading,{...r,...r==null?void 0:r.loading});return typeof e=="function"&&(e=e()),e.then(s=>{let i=t.success?Aa(t.success,s):void 0;return i?Xe.success(i,{id:n,...r,...r==null?void 0:r.success}):Xe.dismiss(n),s}).catch(s=>{let i=t.error?Aa(t.error,s):void 0;i?Xe.error(i,{id:n,...r,...r==null?void 0:r.error}):Xe.dismiss(n)}),e};var Sv=(e,t)=>{vn({type:1,toast:{id:e,height:t}})},_v=()=>{vn({type:5,time:Date.now()})},Is=new Map,Cv=1e3,Ev=(e,t=Cv)=>{if(Is.has(e))return;let r=setTimeout(()=>{Is.delete(e),vn({type:4,toastId:e})},t);Is.set(e,r)},Pv=e=>{let{toasts:t,pausedAt:r}=Nv(e);j.useEffect(()=>{if(r)return;let i=Date.now(),l=t.map(o=>{if(o.duration===1/0)return;let c=(o.duration||0)+o.pauseDuration-(i-o.createdAt);if(c<0){o.visible&&Xe.dismiss(o.id);return}return setTimeout(()=>Xe.dismiss(o.id),c)});return()=>{l.forEach(o=>o&&clearTimeout(o))}},[t,r]);let n=j.useCallback(()=>{r&&vn({type:6,time:Date.now()})},[r]),s=j.useCallback((i,l)=>{let{reverseOrder:o=!1,gutter:c=8,defaultPosition:u}=l||{},d=t.filter(w=>(w.position||u)===(i.position||u)&&w.height),f=d.findIndex(w=>w.id===i.id),h=d.filter((w,p)=>p<f&&w.visible).length;return d.filter(w=>w.visible).slice(...o?[h+1]:[0,h]).reduce((w,p)=>w+(p.height||0)+c,0)},[t]);return j.useEffect(()=>{t.forEach(i=>{if(i.dismissed)Ev(i.id,i.removeDelay);else{let l=Is.get(i.id);l&&(clearTimeout(l),Is.delete(i.id))}})},[t]),{toasts:t,handlers:{updateHeight:Sv,startPause:_v,endPause:n,calculateOffset:s}}},Tv=ur`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Rv=ur`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Ov=ur`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Av=Jr("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Tv} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Rv} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Ov} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Lv=ur`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Fv=Jr("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${Lv} 1s linear infinite;
`,Dv=ur`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Iv=ur`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Mv=Jr("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Dv} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Iv} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,$v=Jr("div")`
  position: absolute;
`,zv=Jr("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,Uv=ur`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Bv=Jr("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Uv} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,Vv=({toast:e})=>{let{icon:t,type:r,iconTheme:n}=e;return t!==void 0?typeof t=="string"?j.createElement(Bv,null,t):t:r==="blank"?null:j.createElement(zv,null,j.createElement(Fv,{...n}),r!=="loading"&&j.createElement($v,null,r==="error"?j.createElement(Av,{...n}):j.createElement(Mv,{...n})))},Wv=e=>`
0% {transform: translate3d(0,${e*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,Hv=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${e*-150}%,-1px) scale(.6); opacity:0;}
`,Zv="0%{opacity:0;} 100%{opacity:1;}",Gv="0%{opacity:1;} 100%{opacity:0;}",qv=Jr("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,Qv=Jr("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,Jv=(e,t)=>{let r=e.includes("top")?1:-1,[n,s]=Pp()?[Zv,Gv]:[Wv(r),Hv(r)];return{animation:t?`${ur(n)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${ur(s)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},Kv=j.memo(({toast:e,position:t,style:r,children:n})=>{let s=e.height?Jv(e.position||t||"top-center",e.visible):{opacity:0},i=j.createElement(Vv,{toast:e}),l=j.createElement(Qv,{...e.ariaProps},Aa(e.message,e));return j.createElement(qv,{className:e.className,style:{...s,...r,...e.style}},typeof n=="function"?n({icon:i,message:l}):j.createElement(j.Fragment,null,i,l))});yv(j.createElement);var Yv=({id:e,className:t,style:r,onHeightUpdate:n,children:s})=>{let i=j.useCallback(l=>{if(l){let o=()=>{let c=l.getBoundingClientRect().height;n(e,c)};o(),new MutationObserver(o).observe(l,{subtree:!0,childList:!0,characterData:!0})}},[e,n]);return j.createElement("div",{ref:i,className:t,style:r},s)},Xv=(e,t)=>{let r=e.includes("top"),n=r?{top:0}:{bottom:0},s=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:Pp()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...n,...s}},e1=fl`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,Bi=16,t1=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:n,children:s,containerStyle:i,containerClassName:l})=>{let{toasts:o,handlers:c}=Pv(r);return j.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:Bi,left:Bi,right:Bi,bottom:Bi,pointerEvents:"none",...i},className:l,onMouseEnter:c.startPause,onMouseLeave:c.endPause},o.map(u=>{let d=u.position||t,f=c.calculateOffset(u,{reverseOrder:e,gutter:n,defaultPosition:t}),h=Xv(d,f);return j.createElement(Yv,{id:u.id,key:u.id,onHeightUpdate:c.updateHeight,className:u.visible?e1:"",style:h},u.type==="custom"?Aa(u.message,u):s?s(u):j.createElement(Kv,{toast:u,position:d}))}))},se=Xe;const lf=e=>{let t;const r=new Set,n=(d,f)=>{const h=typeof d=="function"?d(t):d;if(!Object.is(h,t)){const w=t;t=f??(typeof h!="object"||h===null)?h:Object.assign({},t,h),r.forEach(p=>p(t,w))}},s=()=>t,c={setState:n,getState:s,getInitialState:()=>u,subscribe:d=>(r.add(d),()=>r.delete(d)),destroy:()=>{r.clear()}},u=t=e(n,s,c);return c},r1=e=>e?lf(e):lf;var Rp={exports:{}},Op={},Ap={exports:{}},Lp={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qn=j;function n1(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var s1=typeof Object.is=="function"?Object.is:n1,i1=Qn.useState,a1=Qn.useEffect,l1=Qn.useLayoutEffect,o1=Qn.useDebugValue;function c1(e,t){var r=t(),n=i1({inst:{value:r,getSnapshot:t}}),s=n[0].inst,i=n[1];return l1(function(){s.value=r,s.getSnapshot=t,Xl(s)&&i({inst:s})},[e,r,t]),a1(function(){return Xl(s)&&i({inst:s}),e(function(){Xl(s)&&i({inst:s})})},[e]),o1(r),r}function Xl(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!s1(e,r)}catch{return!0}}function u1(e,t){return t()}var d1=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?u1:c1;Lp.useSyncExternalStore=Qn.useSyncExternalStore!==void 0?Qn.useSyncExternalStore:d1;Ap.exports=Lp;var f1=Ap.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ml=j,m1=f1;function h1(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var p1=typeof Object.is=="function"?Object.is:h1,g1=m1.useSyncExternalStore,x1=ml.useRef,y1=ml.useEffect,v1=ml.useMemo,w1=ml.useDebugValue;Op.useSyncExternalStoreWithSelector=function(e,t,r,n,s){var i=x1(null);if(i.current===null){var l={hasValue:!1,value:null};i.current=l}else l=i.current;i=v1(function(){function c(w){if(!u){if(u=!0,d=w,w=n(w),s!==void 0&&l.hasValue){var p=l.value;if(s(p,w))return f=p}return f=w}if(p=f,p1(d,w))return p;var y=n(w);return s!==void 0&&s(p,y)?(d=w,p):(d=w,f=y)}var u=!1,d,f,h=r===void 0?null:r;return[function(){return c(t())},h===null?void 0:function(){return c(h())}]},[t,r,n,s]);var o=g1(e,i[0],i[1]);return y1(function(){l.hasValue=!0,l.value=o},[o]),w1(o),o};Rp.exports=Op;var b1=Rp.exports;const j1=nm(b1),{useDebugValue:N1}=Ne,{useSyncExternalStoreWithSelector:k1}=j1;const S1=e=>e;function _1(e,t=S1,r){const n=k1(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,r);return N1(n),n}const of=e=>{const t=typeof e=="function"?r1(e):e,r=(n,s)=>_1(t,n,s);return Object.assign(r,t),r},C1=e=>e?of(e):of;function E1(e,t){let r;try{r=e()}catch{return}return{getItem:s=>{var i;const l=c=>c===null?null:JSON.parse(c,t==null?void 0:t.reviver),o=(i=r.getItem(s))!=null?i:null;return o instanceof Promise?o.then(l):l(o)},setItem:(s,i)=>r.setItem(s,JSON.stringify(i,t==null?void 0:t.replacer)),removeItem:s=>r.removeItem(s)}}const ii=e=>t=>{try{const r=e(t);return r instanceof Promise?r:{then(n){return ii(n)(r)},catch(n){return this}}}catch(r){return{then(n){return this},catch(n){return ii(n)(r)}}}},P1=(e,t)=>(r,n,s)=>{let i={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:b=>b,version:0,merge:(b,x)=>({...x,...b}),...t},l=!1;const o=new Set,c=new Set;let u;try{u=i.getStorage()}catch{}if(!u)return e((...b)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),r(...b)},n,s);const d=ii(i.serialize),f=()=>{const b=i.partialize({...n()});let x;const m=d({state:b,version:i.version}).then(g=>u.setItem(i.name,g)).catch(g=>{x=g});if(x)throw x;return m},h=s.setState;s.setState=(b,x)=>{h(b,x),f()};const w=e((...b)=>{r(...b),f()},n,s);let p;const y=()=>{var b;if(!u)return;l=!1,o.forEach(m=>m(n()));const x=((b=i.onRehydrateStorage)==null?void 0:b.call(i,n()))||void 0;return ii(u.getItem.bind(u))(i.name).then(m=>{if(m)return i.deserialize(m)}).then(m=>{if(m)if(typeof m.version=="number"&&m.version!==i.version){if(i.migrate)return i.migrate(m.state,m.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return m.state}).then(m=>{var g;return p=i.merge(m,(g=n())!=null?g:w),r(p,!0),f()}).then(()=>{x==null||x(p,void 0),l=!0,c.forEach(m=>m(p))}).catch(m=>{x==null||x(void 0,m)})};return s.persist={setOptions:b=>{i={...i,...b},b.getStorage&&(u=b.getStorage())},clearStorage:()=>{u==null||u.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>y(),hasHydrated:()=>l,onHydrate:b=>(o.add(b),()=>{o.delete(b)}),onFinishHydration:b=>(c.add(b),()=>{c.delete(b)})},y(),p||w},T1=(e,t)=>(r,n,s)=>{let i={storage:E1(()=>localStorage),partialize:y=>y,version:0,merge:(y,b)=>({...b,...y}),...t},l=!1;const o=new Set,c=new Set;let u=i.storage;if(!u)return e((...y)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),r(...y)},n,s);const d=()=>{const y=i.partialize({...n()});return u.setItem(i.name,{state:y,version:i.version})},f=s.setState;s.setState=(y,b)=>{f(y,b),d()};const h=e((...y)=>{r(...y),d()},n,s);s.getInitialState=()=>h;let w;const p=()=>{var y,b;if(!u)return;l=!1,o.forEach(m=>{var g;return m((g=n())!=null?g:h)});const x=((b=i.onRehydrateStorage)==null?void 0:b.call(i,(y=n())!=null?y:h))||void 0;return ii(u.getItem.bind(u))(i.name).then(m=>{if(m)if(typeof m.version=="number"&&m.version!==i.version){if(i.migrate)return[!0,i.migrate(m.state,m.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,m.state];return[!1,void 0]}).then(m=>{var g;const[N,_]=m;if(w=i.merge(_,(g=n())!=null?g:h),r(w,!0),N)return d()}).then(()=>{x==null||x(w,void 0),w=n(),l=!0,c.forEach(m=>m(w))}).catch(m=>{x==null||x(void 0,m)})};return s.persist={setOptions:y=>{i={...i,...y},y.storage&&(u=y.storage)},clearStorage:()=>{u==null||u.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>p(),hasHydrated:()=>l,onHydrate:y=>(o.add(y),()=>{o.delete(y)}),onFinishHydration:y=>(c.add(y),()=>{c.delete(y)})},i.skipHydration||p(),w||h},R1=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?P1(e,t):T1(e,t),O1=R1;function Fp(e,t){return function(){return e.apply(t,arguments)}}const{toString:A1}=Object.prototype,{getPrototypeOf:xu}=Object,{iterator:hl,toStringTag:Dp}=Symbol,pl=(e=>t=>{const r=A1.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),It=e=>(e=e.toLowerCase(),t=>pl(t)===e),gl=e=>t=>typeof t===e,{isArray:cs}=Array,ai=gl("undefined");function xi(e){return e!==null&&!ai(e)&&e.constructor!==null&&!ai(e.constructor)&&ct(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Ip=It("ArrayBuffer");function L1(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Ip(e.buffer),t}const F1=gl("string"),ct=gl("function"),Mp=gl("number"),yi=e=>e!==null&&typeof e=="object",D1=e=>e===!0||e===!1,ra=e=>{if(pl(e)!=="object")return!1;const t=xu(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Dp in e)&&!(hl in e)},I1=e=>{if(!yi(e)||xi(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},M1=It("Date"),$1=It("File"),z1=It("Blob"),U1=It("FileList"),B1=e=>yi(e)&&ct(e.pipe),V1=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||ct(e.append)&&((t=pl(e))==="formdata"||t==="object"&&ct(e.toString)&&e.toString()==="[object FormData]"))},W1=It("URLSearchParams"),[H1,Z1,G1,q1]=["ReadableStream","Request","Response","Headers"].map(It),Q1=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function vi(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let n,s;if(typeof e!="object"&&(e=[e]),cs(e))for(n=0,s=e.length;n<s;n++)t.call(null,e[n],n,e);else{if(xi(e))return;const i=r?Object.getOwnPropertyNames(e):Object.keys(e),l=i.length;let o;for(n=0;n<l;n++)o=i[n],t.call(null,e[o],o,e)}}function $p(e,t){if(xi(e))return null;t=t.toLowerCase();const r=Object.keys(e);let n=r.length,s;for(;n-- >0;)if(s=r[n],t===s.toLowerCase())return s;return null}const nn=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),zp=e=>!ai(e)&&e!==nn;function ac(){const{caseless:e}=zp(this)&&this||{},t={},r=(n,s)=>{const i=e&&$p(t,s)||s;ra(t[i])&&ra(n)?t[i]=ac(t[i],n):ra(n)?t[i]=ac({},n):cs(n)?t[i]=n.slice():t[i]=n};for(let n=0,s=arguments.length;n<s;n++)arguments[n]&&vi(arguments[n],r);return t}const J1=(e,t,r,{allOwnKeys:n}={})=>(vi(t,(s,i)=>{r&&ct(s)?e[i]=Fp(s,r):e[i]=s},{allOwnKeys:n}),e),K1=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Y1=(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},X1=(e,t,r,n)=>{let s,i,l;const o={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),i=s.length;i-- >0;)l=s[i],(!n||n(l,e,t))&&!o[l]&&(t[l]=e[l],o[l]=!0);e=r!==!1&&xu(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},ew=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const n=e.indexOf(t,r);return n!==-1&&n===r},tw=e=>{if(!e)return null;if(cs(e))return e;let t=e.length;if(!Mp(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},rw=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&xu(Uint8Array)),nw=(e,t)=>{const n=(e&&e[hl]).call(e);let s;for(;(s=n.next())&&!s.done;){const i=s.value;t.call(e,i[0],i[1])}},sw=(e,t)=>{let r;const n=[];for(;(r=e.exec(t))!==null;)n.push(r);return n},iw=It("HTMLFormElement"),aw=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,s){return n.toUpperCase()+s}),cf=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),lw=It("RegExp"),Up=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),n={};vi(r,(s,i)=>{let l;(l=t(s,i,e))!==!1&&(n[i]=l||s)}),Object.defineProperties(e,n)},ow=e=>{Up(e,(t,r)=>{if(ct(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=e[r];if(ct(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},cw=(e,t)=>{const r={},n=s=>{s.forEach(i=>{r[i]=!0})};return cs(e)?n(e):n(String(e).split(t)),r},uw=()=>{},dw=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function fw(e){return!!(e&&ct(e.append)&&e[Dp]==="FormData"&&e[hl])}const mw=e=>{const t=new Array(10),r=(n,s)=>{if(yi(n)){if(t.indexOf(n)>=0)return;if(xi(n))return n;if(!("toJSON"in n)){t[s]=n;const i=cs(n)?[]:{};return vi(n,(l,o)=>{const c=r(l,s+1);!ai(c)&&(i[o]=c)}),t[s]=void 0,i}}return n};return r(e,0)},hw=It("AsyncFunction"),pw=e=>e&&(yi(e)||ct(e))&&ct(e.then)&&ct(e.catch),Bp=((e,t)=>e?setImmediate:t?((r,n)=>(nn.addEventListener("message",({source:s,data:i})=>{s===nn&&i===r&&n.length&&n.shift()()},!1),s=>{n.push(s),nn.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",ct(nn.postMessage)),gw=typeof queueMicrotask<"u"?queueMicrotask.bind(nn):typeof process<"u"&&process.nextTick||Bp,xw=e=>e!=null&&ct(e[hl]),S={isArray:cs,isArrayBuffer:Ip,isBuffer:xi,isFormData:V1,isArrayBufferView:L1,isString:F1,isNumber:Mp,isBoolean:D1,isObject:yi,isPlainObject:ra,isEmptyObject:I1,isReadableStream:H1,isRequest:Z1,isResponse:G1,isHeaders:q1,isUndefined:ai,isDate:M1,isFile:$1,isBlob:z1,isRegExp:lw,isFunction:ct,isStream:B1,isURLSearchParams:W1,isTypedArray:rw,isFileList:U1,forEach:vi,merge:ac,extend:J1,trim:Q1,stripBOM:K1,inherits:Y1,toFlatObject:X1,kindOf:pl,kindOfTest:It,endsWith:ew,toArray:tw,forEachEntry:nw,matchAll:sw,isHTMLForm:iw,hasOwnProperty:cf,hasOwnProp:cf,reduceDescriptors:Up,freezeMethods:ow,toObjectSet:cw,toCamelCase:aw,noop:uw,toFiniteNumber:dw,findKey:$p,global:nn,isContextDefined:zp,isSpecCompliantForm:fw,toJSONObject:mw,isAsyncFn:hw,isThenable:pw,setImmediate:Bp,asap:gw,isIterable:xw};function te(e,t,r,n,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),s&&(this.response=s,this.status=s.status?s.status:null)}S.inherits(te,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:S.toJSONObject(this.config),code:this.code,status:this.status}}});const Vp=te.prototype,Wp={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Wp[e]={value:e}});Object.defineProperties(te,Wp);Object.defineProperty(Vp,"isAxiosError",{value:!0});te.from=(e,t,r,n,s,i)=>{const l=Object.create(Vp);return S.toFlatObject(e,l,function(c){return c!==Error.prototype},o=>o!=="isAxiosError"),te.call(l,e.message,t,r,n,s),l.cause=e,l.name=e.name,i&&Object.assign(l,i),l};const yw=null;function lc(e){return S.isPlainObject(e)||S.isArray(e)}function Hp(e){return S.endsWith(e,"[]")?e.slice(0,-2):e}function uf(e,t,r){return e?e.concat(t).map(function(s,i){return s=Hp(s),!r&&i?"["+s+"]":s}).join(r?".":""):t}function vw(e){return S.isArray(e)&&!e.some(lc)}const ww=S.toFlatObject(S,{},null,function(t){return/^is[A-Z]/.test(t)});function xl(e,t,r){if(!S.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=S.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,b){return!S.isUndefined(b[y])});const n=r.metaTokens,s=r.visitor||d,i=r.dots,l=r.indexes,c=(r.Blob||typeof Blob<"u"&&Blob)&&S.isSpecCompliantForm(t);if(!S.isFunction(s))throw new TypeError("visitor must be a function");function u(p){if(p===null)return"";if(S.isDate(p))return p.toISOString();if(S.isBoolean(p))return p.toString();if(!c&&S.isBlob(p))throw new te("Blob is not supported. Use a Buffer instead.");return S.isArrayBuffer(p)||S.isTypedArray(p)?c&&typeof Blob=="function"?new Blob([p]):Buffer.from(p):p}function d(p,y,b){let x=p;if(p&&!b&&typeof p=="object"){if(S.endsWith(y,"{}"))y=n?y:y.slice(0,-2),p=JSON.stringify(p);else if(S.isArray(p)&&vw(p)||(S.isFileList(p)||S.endsWith(y,"[]"))&&(x=S.toArray(p)))return y=Hp(y),x.forEach(function(g,N){!(S.isUndefined(g)||g===null)&&t.append(l===!0?uf([y],N,i):l===null?y:y+"[]",u(g))}),!1}return lc(p)?!0:(t.append(uf(b,y,i),u(p)),!1)}const f=[],h=Object.assign(ww,{defaultVisitor:d,convertValue:u,isVisitable:lc});function w(p,y){if(!S.isUndefined(p)){if(f.indexOf(p)!==-1)throw Error("Circular reference detected in "+y.join("."));f.push(p),S.forEach(p,function(x,m){(!(S.isUndefined(x)||x===null)&&s.call(t,x,S.isString(m)?m.trim():m,y,h))===!0&&w(x,y?y.concat(m):[m])}),f.pop()}}if(!S.isObject(e))throw new TypeError("data must be an object");return w(e),t}function df(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function yu(e,t){this._pairs=[],e&&xl(e,this,t)}const Zp=yu.prototype;Zp.append=function(t,r){this._pairs.push([t,r])};Zp.toString=function(t){const r=t?function(n){return t.call(this,n,df)}:df;return this._pairs.map(function(s){return r(s[0])+"="+r(s[1])},"").join("&")};function bw(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Gp(e,t,r){if(!t)return e;const n=r&&r.encode||bw;S.isFunction(r)&&(r={serialize:r});const s=r&&r.serialize;let i;if(s?i=s(t,r):i=S.isURLSearchParams(t)?t.toString():new yu(t,r).toString(n),i){const l=e.indexOf("#");l!==-1&&(e=e.slice(0,l)),e+=(e.indexOf("?")===-1?"?":"&")+i}return e}class jw{constructor(){this.handlers=[]}use(t,r,n){return this.handlers.push({fulfilled:t,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){S.forEach(this.handlers,function(n){n!==null&&t(n)})}}const ff=jw,qp={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Nw=typeof URLSearchParams<"u"?URLSearchParams:yu,kw=typeof FormData<"u"?FormData:null,Sw=typeof Blob<"u"?Blob:null,_w={isBrowser:!0,classes:{URLSearchParams:Nw,FormData:kw,Blob:Sw},protocols:["http","https","file","blob","url","data"]},vu=typeof window<"u"&&typeof document<"u",oc=typeof navigator=="object"&&navigator||void 0,Cw=vu&&(!oc||["ReactNative","NativeScript","NS"].indexOf(oc.product)<0),Ew=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),Pw=vu&&window.location.href||"http://localhost",Tw=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:vu,hasStandardBrowserEnv:Cw,hasStandardBrowserWebWorkerEnv:Ew,navigator:oc,origin:Pw},Symbol.toStringTag,{value:"Module"})),Qe={...Tw,..._w};function Rw(e,t){return xl(e,new Qe.classes.URLSearchParams,{visitor:function(r,n,s,i){return Qe.isNode&&S.isBuffer(r)?(this.append(n,r.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)},...t})}function Ow(e){return S.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Aw(e){const t={},r=Object.keys(e);let n;const s=r.length;let i;for(n=0;n<s;n++)i=r[n],t[i]=e[i];return t}function Qp(e){function t(r,n,s,i){let l=r[i++];if(l==="__proto__")return!0;const o=Number.isFinite(+l),c=i>=r.length;return l=!l&&S.isArray(s)?s.length:l,c?(S.hasOwnProp(s,l)?s[l]=[s[l],n]:s[l]=n,!o):((!s[l]||!S.isObject(s[l]))&&(s[l]=[]),t(r,n,s[l],i)&&S.isArray(s[l])&&(s[l]=Aw(s[l])),!o)}if(S.isFormData(e)&&S.isFunction(e.entries)){const r={};return S.forEachEntry(e,(n,s)=>{t(Ow(n),s,r,0)}),r}return null}function Lw(e,t,r){if(S.isString(e))try{return(t||JSON.parse)(e),S.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(e)}const wu={transitional:qp,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const n=r.getContentType()||"",s=n.indexOf("application/json")>-1,i=S.isObject(t);if(i&&S.isHTMLForm(t)&&(t=new FormData(t)),S.isFormData(t))return s?JSON.stringify(Qp(t)):t;if(S.isArrayBuffer(t)||S.isBuffer(t)||S.isStream(t)||S.isFile(t)||S.isBlob(t)||S.isReadableStream(t))return t;if(S.isArrayBufferView(t))return t.buffer;if(S.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let o;if(i){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Rw(t,this.formSerializer).toString();if((o=S.isFileList(t))||n.indexOf("multipart/form-data")>-1){const c=this.env&&this.env.FormData;return xl(o?{"files[]":t}:t,c&&new c,this.formSerializer)}}return i||s?(r.setContentType("application/json",!1),Lw(t)):t}],transformResponse:[function(t){const r=this.transitional||wu.transitional,n=r&&r.forcedJSONParsing,s=this.responseType==="json";if(S.isResponse(t)||S.isReadableStream(t))return t;if(t&&S.isString(t)&&(n&&!this.responseType||s)){const l=!(r&&r.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(o){if(l)throw o.name==="SyntaxError"?te.from(o,te.ERR_BAD_RESPONSE,this,null,this.response):o}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Qe.classes.FormData,Blob:Qe.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};S.forEach(["delete","get","head","post","put","patch"],e=>{wu.headers[e]={}});const bu=wu,Fw=S.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Dw=e=>{const t={};let r,n,s;return e&&e.split(`
`).forEach(function(l){s=l.indexOf(":"),r=l.substring(0,s).trim().toLowerCase(),n=l.substring(s+1).trim(),!(!r||t[r]&&Fw[r])&&(r==="set-cookie"?t[r]?t[r].push(n):t[r]=[n]:t[r]=t[r]?t[r]+", "+n:n)}),t},mf=Symbol("internals");function ws(e){return e&&String(e).trim().toLowerCase()}function na(e){return e===!1||e==null?e:S.isArray(e)?e.map(na):String(e)}function Iw(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(e);)t[n[1]]=n[2];return t}const Mw=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function eo(e,t,r,n,s){if(S.isFunction(n))return n.call(this,t,r);if(s&&(t=r),!!S.isString(t)){if(S.isString(n))return t.indexOf(n)!==-1;if(S.isRegExp(n))return n.test(t)}}function $w(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,n)=>r.toUpperCase()+n)}function zw(e,t){const r=S.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+r,{value:function(s,i,l){return this[n].call(this,t,s,i,l)},configurable:!0})})}class yl{constructor(t){t&&this.set(t)}set(t,r,n){const s=this;function i(o,c,u){const d=ws(c);if(!d)throw new Error("header name must be a non-empty string");const f=S.findKey(s,d);(!f||s[f]===void 0||u===!0||u===void 0&&s[f]!==!1)&&(s[f||c]=na(o))}const l=(o,c)=>S.forEach(o,(u,d)=>i(u,d,c));if(S.isPlainObject(t)||t instanceof this.constructor)l(t,r);else if(S.isString(t)&&(t=t.trim())&&!Mw(t))l(Dw(t),r);else if(S.isObject(t)&&S.isIterable(t)){let o={},c,u;for(const d of t){if(!S.isArray(d))throw TypeError("Object iterator must return a key-value pair");o[u=d[0]]=(c=o[u])?S.isArray(c)?[...c,d[1]]:[c,d[1]]:d[1]}l(o,r)}else t!=null&&i(r,t,n);return this}get(t,r){if(t=ws(t),t){const n=S.findKey(this,t);if(n){const s=this[n];if(!r)return s;if(r===!0)return Iw(s);if(S.isFunction(r))return r.call(this,s,n);if(S.isRegExp(r))return r.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=ws(t),t){const n=S.findKey(this,t);return!!(n&&this[n]!==void 0&&(!r||eo(this,this[n],n,r)))}return!1}delete(t,r){const n=this;let s=!1;function i(l){if(l=ws(l),l){const o=S.findKey(n,l);o&&(!r||eo(n,n[o],o,r))&&(delete n[o],s=!0)}}return S.isArray(t)?t.forEach(i):i(t),s}clear(t){const r=Object.keys(this);let n=r.length,s=!1;for(;n--;){const i=r[n];(!t||eo(this,this[i],i,t,!0))&&(delete this[i],s=!0)}return s}normalize(t){const r=this,n={};return S.forEach(this,(s,i)=>{const l=S.findKey(n,i);if(l){r[l]=na(s),delete r[i];return}const o=t?$w(i):String(i).trim();o!==i&&delete r[i],r[o]=na(s),n[o]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return S.forEach(this,(n,s)=>{n!=null&&n!==!1&&(r[s]=t&&S.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const n=new this(t);return r.forEach(s=>n.set(s)),n}static accessor(t){const n=(this[mf]=this[mf]={accessors:{}}).accessors,s=this.prototype;function i(l){const o=ws(l);n[o]||(zw(s,l),n[o]=!0)}return S.isArray(t)?t.forEach(i):i(t),this}}yl.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);S.reduceDescriptors(yl.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[r]=n}}});S.freezeMethods(yl);const Ft=yl;function to(e,t){const r=this||bu,n=t||r,s=Ft.from(n.headers);let i=n.data;return S.forEach(e,function(o){i=o.call(r,i,s.normalize(),t?t.status:void 0)}),s.normalize(),i}function Jp(e){return!!(e&&e.__CANCEL__)}function us(e,t,r){te.call(this,e??"canceled",te.ERR_CANCELED,t,r),this.name="CanceledError"}S.inherits(us,te,{__CANCEL__:!0});function Kp(e,t,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new te("Request failed with status code "+r.status,[te.ERR_BAD_REQUEST,te.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function Uw(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Bw(e,t){e=e||10;const r=new Array(e),n=new Array(e);let s=0,i=0,l;return t=t!==void 0?t:1e3,function(c){const u=Date.now(),d=n[i];l||(l=u),r[s]=c,n[s]=u;let f=i,h=0;for(;f!==s;)h+=r[f++],f=f%e;if(s=(s+1)%e,s===i&&(i=(i+1)%e),u-l<t)return;const w=d&&u-d;return w?Math.round(h*1e3/w):void 0}}function Vw(e,t){let r=0,n=1e3/t,s,i;const l=(u,d=Date.now())=>{r=d,s=null,i&&(clearTimeout(i),i=null),e(...u)};return[(...u)=>{const d=Date.now(),f=d-r;f>=n?l(u,d):(s=u,i||(i=setTimeout(()=>{i=null,l(s)},n-f)))},()=>s&&l(s)]}const La=(e,t,r=3)=>{let n=0;const s=Bw(50,250);return Vw(i=>{const l=i.loaded,o=i.lengthComputable?i.total:void 0,c=l-n,u=s(c),d=l<=o;n=l;const f={loaded:l,total:o,progress:o?l/o:void 0,bytes:c,rate:u||void 0,estimated:u&&o&&d?(o-l)/u:void 0,event:i,lengthComputable:o!=null,[t?"download":"upload"]:!0};e(f)},r)},hf=(e,t)=>{const r=e!=null;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},pf=e=>(...t)=>S.asap(()=>e(...t)),Ww=Qe.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,Qe.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(Qe.origin),Qe.navigator&&/(msie|trident)/i.test(Qe.navigator.userAgent)):()=>!0,Hw=Qe.hasStandardBrowserEnv?{write(e,t,r,n,s,i){const l=[e+"="+encodeURIComponent(t)];S.isNumber(r)&&l.push("expires="+new Date(r).toGMTString()),S.isString(n)&&l.push("path="+n),S.isString(s)&&l.push("domain="+s),i===!0&&l.push("secure"),document.cookie=l.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Zw(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Gw(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Yp(e,t,r){let n=!Zw(t);return e&&(n||r==!1)?Gw(e,t):t}const gf=e=>e instanceof Ft?{...e}:e;function mn(e,t){t=t||{};const r={};function n(u,d,f,h){return S.isPlainObject(u)&&S.isPlainObject(d)?S.merge.call({caseless:h},u,d):S.isPlainObject(d)?S.merge({},d):S.isArray(d)?d.slice():d}function s(u,d,f,h){if(S.isUndefined(d)){if(!S.isUndefined(u))return n(void 0,u,f,h)}else return n(u,d,f,h)}function i(u,d){if(!S.isUndefined(d))return n(void 0,d)}function l(u,d){if(S.isUndefined(d)){if(!S.isUndefined(u))return n(void 0,u)}else return n(void 0,d)}function o(u,d,f){if(f in t)return n(u,d);if(f in e)return n(void 0,u)}const c={url:i,method:i,data:i,baseURL:l,transformRequest:l,transformResponse:l,paramsSerializer:l,timeout:l,timeoutMessage:l,withCredentials:l,withXSRFToken:l,adapter:l,responseType:l,xsrfCookieName:l,xsrfHeaderName:l,onUploadProgress:l,onDownloadProgress:l,decompress:l,maxContentLength:l,maxBodyLength:l,beforeRedirect:l,transport:l,httpAgent:l,httpsAgent:l,cancelToken:l,socketPath:l,responseEncoding:l,validateStatus:o,headers:(u,d,f)=>s(gf(u),gf(d),f,!0)};return S.forEach(Object.keys({...e,...t}),function(d){const f=c[d]||s,h=f(e[d],t[d],d);S.isUndefined(h)&&f!==o||(r[d]=h)}),r}const Xp=e=>{const t=mn({},e);let{data:r,withXSRFToken:n,xsrfHeaderName:s,xsrfCookieName:i,headers:l,auth:o}=t;t.headers=l=Ft.from(l),t.url=Gp(Yp(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),o&&l.set("Authorization","Basic "+btoa((o.username||"")+":"+(o.password?unescape(encodeURIComponent(o.password)):"")));let c;if(S.isFormData(r)){if(Qe.hasStandardBrowserEnv||Qe.hasStandardBrowserWebWorkerEnv)l.setContentType(void 0);else if((c=l.getContentType())!==!1){const[u,...d]=c?c.split(";").map(f=>f.trim()).filter(Boolean):[];l.setContentType([u||"multipart/form-data",...d].join("; "))}}if(Qe.hasStandardBrowserEnv&&(n&&S.isFunction(n)&&(n=n(t)),n||n!==!1&&Ww(t.url))){const u=s&&i&&Hw.read(i);u&&l.set(s,u)}return t},qw=typeof XMLHttpRequest<"u",Qw=qw&&function(e){return new Promise(function(r,n){const s=Xp(e);let i=s.data;const l=Ft.from(s.headers).normalize();let{responseType:o,onUploadProgress:c,onDownloadProgress:u}=s,d,f,h,w,p;function y(){w&&w(),p&&p(),s.cancelToken&&s.cancelToken.unsubscribe(d),s.signal&&s.signal.removeEventListener("abort",d)}let b=new XMLHttpRequest;b.open(s.method.toUpperCase(),s.url,!0),b.timeout=s.timeout;function x(){if(!b)return;const g=Ft.from("getAllResponseHeaders"in b&&b.getAllResponseHeaders()),_={data:!o||o==="text"||o==="json"?b.responseText:b.response,status:b.status,statusText:b.statusText,headers:g,config:e,request:b};Kp(function(E){r(E),y()},function(E){n(E),y()},_),b=null}"onloadend"in b?b.onloadend=x:b.onreadystatechange=function(){!b||b.readyState!==4||b.status===0&&!(b.responseURL&&b.responseURL.indexOf("file:")===0)||setTimeout(x)},b.onabort=function(){b&&(n(new te("Request aborted",te.ECONNABORTED,e,b)),b=null)},b.onerror=function(){n(new te("Network Error",te.ERR_NETWORK,e,b)),b=null},b.ontimeout=function(){let N=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const _=s.transitional||qp;s.timeoutErrorMessage&&(N=s.timeoutErrorMessage),n(new te(N,_.clarifyTimeoutError?te.ETIMEDOUT:te.ECONNABORTED,e,b)),b=null},i===void 0&&l.setContentType(null),"setRequestHeader"in b&&S.forEach(l.toJSON(),function(N,_){b.setRequestHeader(_,N)}),S.isUndefined(s.withCredentials)||(b.withCredentials=!!s.withCredentials),o&&o!=="json"&&(b.responseType=s.responseType),u&&([h,p]=La(u,!0),b.addEventListener("progress",h)),c&&b.upload&&([f,w]=La(c),b.upload.addEventListener("progress",f),b.upload.addEventListener("loadend",w)),(s.cancelToken||s.signal)&&(d=g=>{b&&(n(!g||g.type?new us(null,e,b):g),b.abort(),b=null)},s.cancelToken&&s.cancelToken.subscribe(d),s.signal&&(s.signal.aborted?d():s.signal.addEventListener("abort",d)));const m=Uw(s.url);if(m&&Qe.protocols.indexOf(m)===-1){n(new te("Unsupported protocol "+m+":",te.ERR_BAD_REQUEST,e));return}b.send(i||null)})},Jw=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let n=new AbortController,s;const i=function(u){if(!s){s=!0,o();const d=u instanceof Error?u:this.reason;n.abort(d instanceof te?d:new us(d instanceof Error?d.message:d))}};let l=t&&setTimeout(()=>{l=null,i(new te(`timeout ${t} of ms exceeded`,te.ETIMEDOUT))},t);const o=()=>{e&&(l&&clearTimeout(l),l=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(i):u.removeEventListener("abort",i)}),e=null)};e.forEach(u=>u.addEventListener("abort",i));const{signal:c}=n;return c.unsubscribe=()=>S.asap(o),c}},Kw=Jw,Yw=function*(e,t){let r=e.byteLength;if(!t||r<t){yield e;return}let n=0,s;for(;n<r;)s=n+t,yield e.slice(n,s),n=s},Xw=async function*(e,t){for await(const r of eb(e))yield*Yw(r,t)},eb=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:r,value:n}=await t.read();if(r)break;yield n}}finally{await t.cancel()}},xf=(e,t,r,n)=>{const s=Xw(e,t);let i=0,l,o=c=>{l||(l=!0,n&&n(c))};return new ReadableStream({async pull(c){try{const{done:u,value:d}=await s.next();if(u){o(),c.close();return}let f=d.byteLength;if(r){let h=i+=f;r(h)}c.enqueue(new Uint8Array(d))}catch(u){throw o(u),u}},cancel(c){return o(c),s.return()}},{highWaterMark:2})},vl=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",eg=vl&&typeof ReadableStream=="function",tb=vl&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),tg=(e,...t)=>{try{return!!e(...t)}catch{return!1}},rb=eg&&tg(()=>{let e=!1;const t=new Request(Qe.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),yf=64*1024,cc=eg&&tg(()=>S.isReadableStream(new Response("").body)),Fa={stream:cc&&(e=>e.body)};vl&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Fa[t]&&(Fa[t]=S.isFunction(e[t])?r=>r[t]():(r,n)=>{throw new te(`Response type '${t}' is not supported`,te.ERR_NOT_SUPPORT,n)})})})(new Response);const nb=async e=>{if(e==null)return 0;if(S.isBlob(e))return e.size;if(S.isSpecCompliantForm(e))return(await new Request(Qe.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(S.isArrayBufferView(e)||S.isArrayBuffer(e))return e.byteLength;if(S.isURLSearchParams(e)&&(e=e+""),S.isString(e))return(await tb(e)).byteLength},sb=async(e,t)=>{const r=S.toFiniteNumber(e.getContentLength());return r??nb(t)},ib=vl&&(async e=>{let{url:t,method:r,data:n,signal:s,cancelToken:i,timeout:l,onDownloadProgress:o,onUploadProgress:c,responseType:u,headers:d,withCredentials:f="same-origin",fetchOptions:h}=Xp(e);u=u?(u+"").toLowerCase():"text";let w=Kw([s,i&&i.toAbortSignal()],l),p;const y=w&&w.unsubscribe&&(()=>{w.unsubscribe()});let b;try{if(c&&rb&&r!=="get"&&r!=="head"&&(b=await sb(d,n))!==0){let _=new Request(t,{method:"POST",body:n,duplex:"half"}),R;if(S.isFormData(n)&&(R=_.headers.get("content-type"))&&d.setContentType(R),_.body){const[E,A]=hf(b,La(pf(c)));n=xf(_.body,yf,E,A)}}S.isString(f)||(f=f?"include":"omit");const x="credentials"in Request.prototype;p=new Request(t,{...h,signal:w,method:r.toUpperCase(),headers:d.normalize().toJSON(),body:n,duplex:"half",credentials:x?f:void 0});let m=await fetch(p,h);const g=cc&&(u==="stream"||u==="response");if(cc&&(o||g&&y)){const _={};["status","statusText","headers"].forEach(q=>{_[q]=m[q]});const R=S.toFiniteNumber(m.headers.get("content-length")),[E,A]=o&&hf(R,La(pf(o),!0))||[];m=new Response(xf(m.body,yf,E,()=>{A&&A(),y&&y()}),_)}u=u||"text";let N=await Fa[S.findKey(Fa,u)||"text"](m,e);return!g&&y&&y(),await new Promise((_,R)=>{Kp(_,R,{data:N,headers:Ft.from(m.headers),status:m.status,statusText:m.statusText,config:e,request:p})})}catch(x){throw y&&y(),x&&x.name==="TypeError"&&/Load failed|fetch/i.test(x.message)?Object.assign(new te("Network Error",te.ERR_NETWORK,e,p),{cause:x.cause||x}):te.from(x,x&&x.code,e,p)}}),uc={http:yw,xhr:Qw,fetch:ib};S.forEach(uc,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const vf=e=>`- ${e}`,ab=e=>S.isFunction(e)||e===null||e===!1,rg={getAdapter:e=>{e=S.isArray(e)?e:[e];const{length:t}=e;let r,n;const s={};for(let i=0;i<t;i++){r=e[i];let l;if(n=r,!ab(r)&&(n=uc[(l=String(r)).toLowerCase()],n===void 0))throw new te(`Unknown adapter '${l}'`);if(n)break;s[l||"#"+i]=n}if(!n){const i=Object.entries(s).map(([o,c])=>`adapter ${o} `+(c===!1?"is not supported by the environment":"is not available in the build"));let l=t?i.length>1?`since :
`+i.map(vf).join(`
`):" "+vf(i[0]):"as no adapter specified";throw new te("There is no suitable adapter to dispatch the request "+l,"ERR_NOT_SUPPORT")}return n},adapters:uc};function ro(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new us(null,e)}function wf(e){return ro(e),e.headers=Ft.from(e.headers),e.data=to.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),rg.getAdapter(e.adapter||bu.adapter)(e).then(function(n){return ro(e),n.data=to.call(e,e.transformResponse,n),n.headers=Ft.from(n.headers),n},function(n){return Jp(n)||(ro(e),n&&n.response&&(n.response.data=to.call(e,e.transformResponse,n.response),n.response.headers=Ft.from(n.response.headers))),Promise.reject(n)})}const ng="1.11.0",wl={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{wl[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const bf={};wl.transitional=function(t,r,n){function s(i,l){return"[Axios v"+ng+"] Transitional option '"+i+"'"+l+(n?". "+n:"")}return(i,l,o)=>{if(t===!1)throw new te(s(l," has been removed"+(r?" in "+r:"")),te.ERR_DEPRECATED);return r&&!bf[l]&&(bf[l]=!0,console.warn(s(l," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(i,l,o):!0}};wl.spelling=function(t){return(r,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};function lb(e,t,r){if(typeof e!="object")throw new te("options must be an object",te.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let s=n.length;for(;s-- >0;){const i=n[s],l=t[i];if(l){const o=e[i],c=o===void 0||l(o,i,e);if(c!==!0)throw new te("option "+i+" must be "+c,te.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new te("Unknown option "+i,te.ERR_BAD_OPTION)}}const sa={assertOptions:lb,validators:wl},zt=sa.validators;class Da{constructor(t){this.defaults=t||{},this.interceptors={request:new ff,response:new ff}}async request(t,r){try{return await this._request(t,r)}catch(n){if(n instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const i=s.stack?s.stack.replace(/^.+\n/,""):"";try{n.stack?i&&!String(n.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+i):n.stack=i}catch{}}throw n}}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=mn(this.defaults,r);const{transitional:n,paramsSerializer:s,headers:i}=r;n!==void 0&&sa.assertOptions(n,{silentJSONParsing:zt.transitional(zt.boolean),forcedJSONParsing:zt.transitional(zt.boolean),clarifyTimeoutError:zt.transitional(zt.boolean)},!1),s!=null&&(S.isFunction(s)?r.paramsSerializer={serialize:s}:sa.assertOptions(s,{encode:zt.function,serialize:zt.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),sa.assertOptions(r,{baseUrl:zt.spelling("baseURL"),withXsrfToken:zt.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let l=i&&S.merge(i.common,i[r.method]);i&&S.forEach(["delete","get","head","post","put","patch","common"],p=>{delete i[p]}),r.headers=Ft.concat(l,i);const o=[];let c=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(r)===!1||(c=c&&y.synchronous,o.unshift(y.fulfilled,y.rejected))});const u=[];this.interceptors.response.forEach(function(y){u.push(y.fulfilled,y.rejected)});let d,f=0,h;if(!c){const p=[wf.bind(this),void 0];for(p.unshift(...o),p.push(...u),h=p.length,d=Promise.resolve(r);f<h;)d=d.then(p[f++],p[f++]);return d}h=o.length;let w=r;for(f=0;f<h;){const p=o[f++],y=o[f++];try{w=p(w)}catch(b){y.call(this,b);break}}try{d=wf.call(this,w)}catch(p){return Promise.reject(p)}for(f=0,h=u.length;f<h;)d=d.then(u[f++],u[f++]);return d}getUri(t){t=mn(this.defaults,t);const r=Yp(t.baseURL,t.url,t.allowAbsoluteUrls);return Gp(r,t.params,t.paramsSerializer)}}S.forEach(["delete","get","head","options"],function(t){Da.prototype[t]=function(r,n){return this.request(mn(n||{},{method:t,url:r,data:(n||{}).data}))}});S.forEach(["post","put","patch"],function(t){function r(n){return function(i,l,o){return this.request(mn(o||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:i,data:l}))}}Da.prototype[t]=r(),Da.prototype[t+"Form"]=r(!0)});const ia=Da;class ju{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(i){r=i});const n=this;this.promise.then(s=>{if(!n._listeners)return;let i=n._listeners.length;for(;i-- >0;)n._listeners[i](s);n._listeners=null}),this.promise.then=s=>{let i;const l=new Promise(o=>{n.subscribe(o),i=o}).then(s);return l.cancel=function(){n.unsubscribe(i)},l},t(function(i,l,o){n.reason||(n.reason=new us(i,l,o),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const t=new AbortController,r=n=>{t.abort(n)};return this.subscribe(r),t.signal.unsubscribe=()=>this.unsubscribe(r),t.signal}static source(){let t;return{token:new ju(function(s){t=s}),cancel:t}}}const ob=ju;function cb(e){return function(r){return e.apply(null,r)}}function ub(e){return S.isObject(e)&&e.isAxiosError===!0}const dc={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(dc).forEach(([e,t])=>{dc[t]=e});const db=dc;function sg(e){const t=new ia(e),r=Fp(ia.prototype.request,t);return S.extend(r,ia.prototype,t,{allOwnKeys:!0}),S.extend(r,t,null,{allOwnKeys:!0}),r.create=function(s){return sg(mn(e,s))},r}const Fe=sg(bu);Fe.Axios=ia;Fe.CanceledError=us;Fe.CancelToken=ob;Fe.isCancel=Jp;Fe.VERSION=ng;Fe.toFormData=xl;Fe.AxiosError=te;Fe.Cancel=Fe.CanceledError;Fe.all=function(t){return Promise.all(t)};Fe.spread=cb;Fe.isAxiosError=ub;Fe.mergeConfig=mn;Fe.AxiosHeaders=Ft;Fe.formToJSON=e=>Qp(S.isHTMLForm(e)?new FormData(e):e);Fe.getAdapter=rg.getAdapter;Fe.HttpStatusCode=db;Fe.default=Fe;const fb=Fe,J=fb.create({baseURL:{}.VITE_API_URL||"/api",timeout:1e4,headers:{"Content-Type":"application/json"}});J.interceptors.request.use(e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e));J.interceptors.response.use(e=>e,e=>{const{response:t}=e;if(t){const{status:r,data:n}=t;switch(r){case 401:localStorage.removeItem("token"),delete J.defaults.headers.common.Authorization,window.location.pathname.includes("/login")||(se.error("Session expired. Please login again."),window.location.href="/login");break;case 403:se.error("Access denied. You do not have permission to perform this action.");break;case 404:se.error("Resource not found.");break;case 422:n.errors&&Array.isArray(n.errors)?n.errors.forEach(s=>se.error(s)):n.message&&se.error(n.message);break;case 429:se.error("Too many requests. Please try again later.");break;case 500:se.error("Server error. Please try again later.");break;default:n.message?se.error(n.message):se.error("An unexpected error occurred.")}}else e.request?se.error("Network error. Please check your connection."):se.error("An unexpected error occurred.");return Promise.reject(e)});const Nu={getAll:e=>J.get("/gigs",{params:e}),getById:e=>J.get(`/gigs/${e}`),create:e=>J.post("/gigs",e),update:(e,t)=>J.put(`/gigs/${e}`,t),delete:e=>J.delete(`/gigs/${e}`),search:(e,t)=>J.get("/gigs/search",{params:{q:e,...t}}),getByCategory:(e,t)=>J.get(`/gigs/category/${e}`,{params:{subcategory:t}}),getMyGigs:()=>J.get("/gigs/my-gigs")},ku={getAll:e=>J.get("/orders",{params:e}),getById:e=>J.get(`/orders/${e}`),create:e=>J.post("/orders",e),updateStatus:(e,t)=>J.put(`/orders/${e}/status`,{status:t}),requestRevision:(e,t)=>J.post(`/orders/${e}/revision`,{reason:t}),deliverWork:(e,t)=>J.post(`/orders/${e}/deliver`,{work:t}),complete:e=>J.put(`/orders/${e}/complete`),cancel:(e,t)=>J.put(`/orders/${e}/cancel`,{reason:t}),getMyOrders:e=>J.get("/orders/my-orders",{params:{role:e}})},no={getConversations:e=>J.get("/messages/conversations",{params:e}),getConversation:e=>J.get(`/messages/conversations/${e}`),getMessages:(e,t)=>J.get(`/messages/conversations/${e}/messages`,{params:t}),sendMessage:(e,t)=>J.post(`/messages/conversations/${e}/messages`,t),sendFile:(e,t)=>J.post(`/messages/conversations/${e}/files`,t,{headers:{"Content-Type":"multipart/form-data"}}),sendVoiceMessage:(e,t)=>J.post(`/messages/conversations/${e}/voice`,t,{headers:{"Content-Type":"multipart/form-data"}}),sendPaymentRequest:(e,t)=>J.post(`/messages/conversations/${e}/payment-request`,t),markAsRead:e=>J.put(`/messages/conversations/${e}/read`),editMessage:(e,t)=>J.put(`/messages/${e}`,t),deleteMessage:e=>J.delete(`/messages/${e}`),flagMessage:(e,t)=>J.post(`/messages/${e}/flag`,{reason:t}),translateMessage:(e,t)=>J.post(`/messages/${e}/translate`,{targetLanguage:t}),summarizeConversation:e=>J.post(`/messages/conversations/${e}/summarize`),createConversation:e=>J.post("/messages/conversations",e)},We=C1(O1((e,t)=>({user:null,token:null,loading:!1,isAuthenticated:!1,login:async r=>{var n,s;try{e({loading:!0});const i=await J.post("/auth/login",r),{user:l,token:o}=i.data;return localStorage.setItem("token",o),J.defaults.headers.common.Authorization=`Bearer ${o}`,e({user:l,token:o,isAuthenticated:!0,loading:!1}),se.success("Login successful!"),{success:!0}}catch(i){e({loading:!1});const l=((s=(n=i.response)==null?void 0:n.data)==null?void 0:s.message)||"Login failed";return se.error(l),{success:!1,error:l}}},register:async r=>{var n,s;try{e({loading:!0});const i=await J.post("/auth/register",r),{user:l,token:o}=i.data;return localStorage.setItem("token",o),J.defaults.headers.common.Authorization=`Bearer ${o}`,e({user:l,token:o,isAuthenticated:!0,loading:!1}),se.success("Registration successful!"),{success:!0}}catch(i){e({loading:!1});const l=((s=(n=i.response)==null?void 0:n.data)==null?void 0:s.message)||"Registration failed";return se.error(l),{success:!1,error:l}}},logout:async()=>{try{await J.post("/auth/logout")}catch(r){console.error("Logout error:",r)}finally{localStorage.removeItem("token"),delete J.defaults.headers.common.Authorization,e({user:null,token:null,isAuthenticated:!1,loading:!1}),se.success("Logged out successfully")}},checkAuth:async()=>{try{e({loading:!0});const r=localStorage.getItem("token");if(!r){e({user:null,token:null,isAuthenticated:!1,loading:!1});return}console.log("Demo mode: Skipping backend auth verification"),J.defaults.headers.common.Authorization=`Bearer ${r}`,e({user:{_id:"demo-user",name:"Demo User",email:"<EMAIL>",role:"client",avatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"},token:r,isAuthenticated:!0,loading:!1})}catch(r){console.error("Auth check failed:",r),localStorage.removeItem("token"),delete J.defaults.headers.common.Authorization,e({user:null,token:null,isAuthenticated:!1,loading:!1})}},updateProfile:async r=>{var n,s;try{e({loading:!0});const i=await J.put("/auth/update-details",r),{user:l}=i.data;return e({user:l,loading:!1}),se.success("Profile updated successfully!"),{success:!0}}catch(i){e({loading:!1});const l=((s=(n=i.response)==null?void 0:n.data)==null?void 0:s.message)||"Profile update failed";return se.error(l),{success:!1,error:l}}},changePassword:async r=>{var n,s;try{return e({loading:!0}),await J.put("/auth/update-password",r),e({loading:!1}),se.success("Password updated successfully!"),{success:!0}}catch(i){e({loading:!1});const l=((s=(n=i.response)==null?void 0:n.data)==null?void 0:s.message)||"Password update failed";return se.error(l),{success:!1,error:l}}},forgotPassword:async r=>{var n,s;try{return e({loading:!0}),await J.post("/auth/forgot-password",{email:r}),e({loading:!1}),se.success("Password reset email sent!"),{success:!0}}catch(i){e({loading:!1});const l=((s=(n=i.response)==null?void 0:n.data)==null?void 0:s.message)||"Failed to send reset email";return se.error(l),{success:!1,error:l}}},resetPassword:async(r,n)=>{var s,i;try{return e({loading:!0}),await J.put(`/auth/reset-password/${r}`,{password:n}),e({loading:!1}),se.success("Password reset successful!"),{success:!0}}catch(l){e({loading:!1});const o=((i=(s=l.response)==null?void 0:s.data)==null?void 0:i.message)||"Password reset failed";return se.error(o),{success:!1,error:o}}},verifyEmail:async r=>{var n,s;try{e({loading:!0}),await J.get(`/auth/verify/${r}`);const i=t().user;return e(i?{user:{...i,isEmailVerified:!0},loading:!1}:{loading:!1}),se.success("Email verified successfully!"),{success:!0}}catch(i){e({loading:!1});const l=((s=(n=i.response)==null?void 0:n.data)==null?void 0:s.message)||"Email verification failed";return se.error(l),{success:!1,error:l}}},clearError:()=>{e({error:null})}}),{name:"auth-storage",partialize:e=>({user:e.user,token:e.token,isAuthenticated:e.isAuthenticated})}));var ig={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},jf=Ne.createContext&&Ne.createContext(ig),$r=globalThis&&globalThis.__assign||function(){return $r=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++){t=arguments[r];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s])}return e},$r.apply(this,arguments)},mb=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(e);s<n.length;s++)t.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(e,n[s])&&(r[n[s]]=e[n[s]]);return r};function ag(e){return e&&e.map(function(t,r){return Ne.createElement(t.tag,$r({key:r},t.attr),ag(t.child))})}function Q(e){return function(t){return Ne.createElement(hb,$r({attr:$r({},e.attr)},t),ag(e.child))}}function hb(e){var t=function(r){var n=e.attr,s=e.size,i=e.title,l=mb(e,["attr","size","title"]),o=s||r.size||"1em",c;return r.className&&(c=r.className),e.className&&(c=(c?c+" ":"")+e.className),Ne.createElement("svg",$r({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,n,l,{className:c,style:$r($r({color:e.color||r.color},r.style),e.style),height:o,width:o,xmlns:"http://www.w3.org/2000/svg"}),i&&Ne.createElement("title",null,i),e.children)};return jf!==void 0?Ne.createElement(jf.Consumer,null,function(r){return t(r)}):t(ig)}function lg(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"10"}},{tag:"line",attr:{x1:"12",y1:"8",x2:"12",y2:"12"}},{tag:"line",attr:{x1:"12",y1:"16",x2:"12.01",y2:"16"}}]})(e)}function pb(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"19",y1:"12",x2:"5",y2:"12"}},{tag:"polyline",attr:{points:"12 19 5 12 12 5"}}]})(e)}function Nf(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"5",y1:"12",x2:"19",y2:"12"}},{tag:"polyline",attr:{points:"12 5 19 12 12 19"}}]})(e)}function gb(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"8",r:"7"}},{tag:"polyline",attr:{points:"8.21 13.89 7 23 12 20 17 23 15.79 13.88"}}]})(e)}function og(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"rect",attr:{x:"3",y:"4",width:"18",height:"18",rx:"2",ry:"2"}},{tag:"line",attr:{x1:"16",y1:"2",x2:"16",y2:"6"}},{tag:"line",attr:{x1:"8",y1:"2",x2:"8",y2:"6"}},{tag:"line",attr:{x1:"3",y1:"10",x2:"21",y2:"10"}}]})(e)}function xb(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14"}},{tag:"polyline",attr:{points:"22 4 12 14.01 9 11.01"}}]})(e)}function hn(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"20 6 9 17 4 12"}}]})(e)}function pn(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"10"}},{tag:"polyline",attr:{points:"12 6 12 12 16 14"}}]})(e)}function yb(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"16 18 22 12 16 6"}},{tag:"polyline",attr:{points:"8 6 2 12 8 18"}}]})(e)}function Ia(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"12",y1:"1",x2:"12",y2:"23"}},{tag:"path",attr:{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"}}]})(e)}function cg(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"}},{tag:"path",attr:{d:"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"}}]})(e)}function li(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"}},{tag:"line",attr:{x1:"1",y1:"1",x2:"23",y2:"23"}}]})(e)}function Jn(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}},{tag:"circle",attr:{cx:"12",cy:"12",r:"3"}}]})(e)}function vb(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"}}]})(e)}function wb(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"}},{tag:"polyline",attr:{points:"14 2 14 8 20 8"}},{tag:"line",attr:{x1:"16",y1:"13",x2:"8",y2:"13"}},{tag:"line",attr:{x1:"16",y1:"17",x2:"8",y2:"17"}},{tag:"polyline",attr:{points:"10 9 9 9 8 9"}}]})(e)}function bb(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"}},{tag:"line",attr:{x1:"4",y1:"22",x2:"4",y2:"15"}}]})(e)}function ug(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"rect",attr:{x:"3",y:"3",width:"7",height:"7"}},{tag:"rect",attr:{x:"14",y:"3",width:"7",height:"7"}},{tag:"rect",attr:{x:"14",y:"14",width:"7",height:"7"}},{tag:"rect",attr:{x:"3",y:"14",width:"7",height:"7"}}]})(e)}function dg(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"}}]})(e)}function jb(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"rect",attr:{x:"2",y:"2",width:"20",height:"20",rx:"5",ry:"5"}},{tag:"path",attr:{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"}},{tag:"line",attr:{x1:"17.5",y1:"6.5",x2:"17.51",y2:"6.5"}}]})(e)}function Nb(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"}},{tag:"rect",attr:{x:"2",y:"9",width:"4",height:"12"}},{tag:"circle",attr:{cx:"4",cy:"4",r:"2"}}]})(e)}function fg(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"8",y1:"6",x2:"21",y2:"6"}},{tag:"line",attr:{x1:"8",y1:"12",x2:"21",y2:"12"}},{tag:"line",attr:{x1:"8",y1:"18",x2:"21",y2:"18"}},{tag:"line",attr:{x1:"3",y1:"6",x2:"3.01",y2:"6"}},{tag:"line",attr:{x1:"3",y1:"12",x2:"3.01",y2:"12"}},{tag:"line",attr:{x1:"3",y1:"18",x2:"3.01",y2:"18"}}]})(e)}function wn(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"12",y1:"2",x2:"12",y2:"6"}},{tag:"line",attr:{x1:"12",y1:"18",x2:"12",y2:"22"}},{tag:"line",attr:{x1:"4.93",y1:"4.93",x2:"7.76",y2:"7.76"}},{tag:"line",attr:{x1:"16.24",y1:"16.24",x2:"19.07",y2:"19.07"}},{tag:"line",attr:{x1:"2",y1:"12",x2:"6",y2:"12"}},{tag:"line",attr:{x1:"18",y1:"12",x2:"22",y2:"12"}},{tag:"line",attr:{x1:"4.93",y1:"19.07",x2:"7.76",y2:"16.24"}},{tag:"line",attr:{x1:"16.24",y1:"7.76",x2:"19.07",y2:"4.93"}}]})(e)}function oi(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"rect",attr:{x:"3",y:"11",width:"18",height:"11",rx:"2",ry:"2"}},{tag:"path",attr:{d:"M7 11V7a5 5 0 0 1 10 0v4"}}]})(e)}function kb(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"}},{tag:"polyline",attr:{points:"16 17 21 12 16 7"}},{tag:"line",attr:{x1:"21",y1:"12",x2:"9",y2:"12"}}]})(e)}function ds(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"}},{tag:"polyline",attr:{points:"22,6 12,13 2,6"}}]})(e)}function Su(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"}},{tag:"circle",attr:{cx:"12",cy:"10",r:"3"}}]})(e)}function Sb(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"3",y1:"12",x2:"21",y2:"12"}},{tag:"line",attr:{x1:"3",y1:"6",x2:"21",y2:"6"}},{tag:"line",attr:{x1:"3",y1:"18",x2:"21",y2:"18"}}]})(e)}function _u(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"}}]})(e)}function _b(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M9 18V5l12-2v13"}},{tag:"circle",attr:{cx:"6",cy:"18",r:"3"}},{tag:"circle",attr:{cx:"18",cy:"16",r:"3"}}]})(e)}function Cb(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"}}]})(e)}function Eb(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M12 19l7-7 3 3-7 7-3-3z"}},{tag:"path",attr:{d:"M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"}},{tag:"path",attr:{d:"M2 2l7.586 7.586"}},{tag:"circle",attr:{cx:"11",cy:"11",r:"2"}}]})(e)}function Pb(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"}}]})(e)}function Ma(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"12",y1:"5",x2:"12",y2:"19"}},{tag:"line",attr:{x1:"5",y1:"12",x2:"19",y2:"12"}}]})(e)}function mg(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"23 4 23 10 17 10"}},{tag:"polyline",attr:{points:"1 20 1 14 7 14"}},{tag:"path",attr:{d:"M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"}}]})(e)}function Tb(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"}},{tag:"polyline",attr:{points:"17 21 17 13 7 13 7 21"}},{tag:"polyline",attr:{points:"7 3 7 8 15 8"}}]})(e)}function sr(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"11",cy:"11",r:"8"}},{tag:"line",attr:{x1:"21",y1:"21",x2:"16.65",y2:"16.65"}}]})(e)}function Rb(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"22",y1:"2",x2:"11",y2:"13"}},{tag:"polygon",attr:{points:"22 2 15 22 11 13 2 9 22 2"}}]})(e)}function Ob(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"3"}},{tag:"path",attr:{d:"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"}}]})(e)}function Ab(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"18",cy:"5",r:"3"}},{tag:"circle",attr:{cx:"6",cy:"12",r:"3"}},{tag:"circle",attr:{cx:"18",cy:"19",r:"3"}},{tag:"line",attr:{x1:"8.59",y1:"13.51",x2:"15.42",y2:"17.49"}},{tag:"line",attr:{x1:"15.41",y1:"6.51",x2:"8.59",y2:"10.49"}}]})(e)}function $a(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"}},{tag:"line",attr:{x1:"3",y1:"6",x2:"21",y2:"6"}},{tag:"path",attr:{d:"M16 10a4 4 0 0 1-8 0"}}]})(e)}function zr(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polygon",attr:{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"}}]})(e)}function Lb(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"10"}},{tag:"circle",attr:{cx:"12",cy:"12",r:"6"}},{tag:"circle",attr:{cx:"12",cy:"12",r:"2"}}]})(e)}function Fb(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"23 6 13.5 15.5 8.5 10.5 1 18"}},{tag:"polyline",attr:{points:"17 6 23 6 23 12"}}]})(e)}function Db(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"}}]})(e)}function hg(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}},{tag:"polyline",attr:{points:"17 8 12 3 7 8"}},{tag:"line",attr:{x1:"12",y1:"3",x2:"12",y2:"15"}}]})(e)}function za(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"}},{tag:"circle",attr:{cx:"12",cy:"7",r:"4"}}]})(e)}function Ib(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}},{tag:"circle",attr:{cx:"9",cy:"7",r:"4"}},{tag:"path",attr:{d:"M23 21v-2a4 4 0 0 0-3-3.87"}},{tag:"path",attr:{d:"M16 3.13a4 4 0 0 1 0 7.75"}}]})(e)}function Mb(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polygon",attr:{points:"23 7 16 12 23 17 23 7"}},{tag:"rect",attr:{x:"1",y:"5",width:"15",height:"14",rx:"2",ry:"2"}}]})(e)}function Kn(e){return Q({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"18",y1:"6",x2:"6",y2:"18"}},{tag:"line",attr:{x1:"6",y1:"6",x2:"18",y2:"18"}}]})(e)}const $b=()=>{var p;const[e,t]=j.useState(!1),[r,n]=j.useState(!1),[s,i]=j.useState(""),{user:l,logout:o,isAuthenticated:c}=We(),u=xt(),d=hr(),f=y=>{y.preventDefault(),s.trim()&&(u(`/gigs?search=${encodeURIComponent(s.trim())}`),i(""))},h=async()=>{await o(),u("/"),n(!1)},w=y=>d.pathname===y;return a.jsxs("nav",{className:"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50",children:[a.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[a.jsxs("div",{className:"flex justify-between items-center h-16",children:[a.jsx("div",{className:"flex items-center",children:a.jsxs(M,{to:"/",className:"flex items-center space-x-2",children:[a.jsx("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:a.jsx("span",{className:"text-white font-bold text-lg",children:"F"})}),a.jsx("span",{className:"text-xl font-bold text-gray-900",children:"FreelanceHub"})]})}),a.jsx("div",{className:"hidden md:flex flex-1 max-w-lg mx-8",children:a.jsx("form",{onSubmit:f,className:"w-full",children:a.jsxs("div",{className:"relative",children:[a.jsx("input",{type:"text",value:s,onChange:y=>i(y.target.value),placeholder:"Search for services...",className:"w-full pl-4 pr-12 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),a.jsx("button",{type:"submit",className:"absolute right-2 top-1/2 transform -translate-y-1/2 p-2 text-gray-400 hover:text-blue-600",children:a.jsx(sr,{className:"w-5 h-5"})})]})})}),a.jsxs("div",{className:"hidden md:flex items-center space-x-6",children:[a.jsx(M,{to:"/gigs",className:`text-sm font-medium transition-colors ${w("/gigs")?"text-blue-600":"text-gray-700 hover:text-blue-600"}`,children:"Browse Gigs"}),c?a.jsxs(a.Fragment,{children:[a.jsx(M,{to:"/messages",className:"relative p-2 text-gray-700 hover:text-blue-600 transition-colors",children:a.jsx(_u,{className:"w-5 h-5"})}),a.jsx(M,{to:"/orders",className:"relative p-2 text-gray-700 hover:text-blue-600 transition-colors",children:a.jsx($a,{className:"w-5 h-5"})}),(l==null?void 0:l.role)==="freelancer"&&a.jsxs(M,{to:"/gigs/create",className:"flex items-center space-x-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[a.jsx(Ma,{className:"w-4 h-4"}),a.jsx("span",{children:"Create Gig"})]}),a.jsxs("div",{className:"relative",children:[a.jsxs("button",{onClick:()=>n(!r),className:"flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors",children:[a.jsx("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:l!=null&&l.avatar?a.jsx("img",{src:l.avatar,alt:l.name,className:"w-8 h-8 rounded-full object-cover"}):a.jsx(za,{className:"w-4 h-4 text-gray-600"})}),a.jsx("span",{className:"text-sm font-medium text-gray-700",children:(p=l==null?void 0:l.name)==null?void 0:p.split(" ")[0]})]}),r&&a.jsxs("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50",children:[a.jsxs(M,{to:"/dashboard",onClick:()=>n(!1),className:"flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[a.jsx(za,{className:"w-4 h-4"}),a.jsx("span",{children:"Dashboard"})]}),a.jsxs(M,{to:"/profile",onClick:()=>n(!1),className:"flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[a.jsx(Ob,{className:"w-4 h-4"}),a.jsx("span",{children:"Profile Settings"})]}),a.jsx("hr",{className:"my-1"}),a.jsxs("button",{onClick:h,className:"flex items-center space-x-2 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50",children:[a.jsx(kb,{className:"w-4 h-4"}),a.jsx("span",{children:"Sign Out"})]})]})]})]}):a.jsxs(a.Fragment,{children:[a.jsx(M,{to:"/login",className:"text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors",children:"Sign In"}),a.jsx(M,{to:"/register",className:"px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors",children:"Join"})]})]}),a.jsx("div",{className:"md:hidden",children:a.jsx("button",{onClick:()=>t(!e),className:"p-2 text-gray-700 hover:text-blue-600 transition-colors",children:e?a.jsx(Kn,{className:"w-6 h-6"}):a.jsx(Sb,{className:"w-6 h-6"})})})]}),e&&a.jsxs("div",{className:"md:hidden border-t border-gray-200 py-4",children:[a.jsx("form",{onSubmit:f,className:"mb-4",children:a.jsxs("div",{className:"relative",children:[a.jsx("input",{type:"text",value:s,onChange:y=>i(y.target.value),placeholder:"Search for services...",className:"w-full pl-4 pr-12 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),a.jsx("button",{type:"submit",className:"absolute right-2 top-1/2 transform -translate-y-1/2 p-2 text-gray-400 hover:text-blue-600",children:a.jsx(sr,{className:"w-5 h-5"})})]})}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(M,{to:"/gigs",onClick:()=>t(!1),className:"block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg",children:"Browse Gigs"}),c?a.jsxs(a.Fragment,{children:[a.jsx(M,{to:"/dashboard",onClick:()=>t(!1),className:"block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg",children:"Dashboard"}),a.jsx(M,{to:"/messages",onClick:()=>t(!1),className:"block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg",children:"Messages"}),a.jsx(M,{to:"/orders",onClick:()=>t(!1),className:"block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg",children:"Orders"}),a.jsx(M,{to:"/profile",onClick:()=>t(!1),className:"block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg",children:"Profile Settings"}),(l==null?void 0:l.role)==="freelancer"&&a.jsx(M,{to:"/gigs/create",onClick:()=>t(!1),className:"block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Create Gig"}),a.jsx("button",{onClick:()=>{h(),t(!1)},className:"block w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 rounded-lg",children:"Sign Out"})]}):a.jsxs(a.Fragment,{children:[a.jsx(M,{to:"/login",onClick:()=>t(!1),className:"block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg",children:"Sign In"}),a.jsx(M,{to:"/register",onClick:()=>t(!1),className:"block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Join"})]})]})]})]}),r&&a.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>n(!1)})]})},zb=()=>{const e=new Date().getFullYear(),t={categories:[{name:"Graphics & Design",href:"/gigs?category=graphics-design"},{name:"Digital Marketing",href:"/gigs?category=digital-marketing"},{name:"Writing & Translation",href:"/gigs?category=writing-translation"},{name:"Video & Animation",href:"/gigs?category=video-animation"},{name:"Music & Audio",href:"/gigs?category=music-audio"},{name:"Programming & Tech",href:"/gigs?category=programming-tech"}],about:[{name:"About Us",href:"/about"},{name:"Careers",href:"/careers"},{name:"Press & News",href:"/press"},{name:"Partnerships",href:"/partnerships"},{name:"Privacy Policy",href:"/privacy"},{name:"Terms of Service",href:"/terms"}],support:[{name:"Help & Support",href:"/help"},{name:"Trust & Safety",href:"/safety"},{name:"Selling on FreelanceHub",href:"/selling-guide"},{name:"Buying on FreelanceHub",href:"/buying-guide"},{name:"Community",href:"/community"},{name:"Contact Us",href:"/contact"}],community:[{name:"Customer Success Stories",href:"/success-stories"},{name:"Community Hub",href:"/community-hub"},{name:"Forum",href:"/forum"},{name:"Events",href:"/events"},{name:"Blog",href:"/blog"},{name:"Influencers",href:"/influencers"}]},r=[{name:"Twitter",icon:Db,href:"https://twitter.com/freelancehub"},{name:"Facebook",icon:vb,href:"https://facebook.com/freelancehub"},{name:"Instagram",icon:jb,href:"https://instagram.com/freelancehub"},{name:"LinkedIn",icon:Nb,href:"https://linkedin.com/company/freelancehub"}];return a.jsxs("footer",{className:"bg-gray-900 text-white",children:[a.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8",children:[a.jsxs("div",{className:"lg:col-span-1",children:[a.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[a.jsx("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:a.jsx("span",{className:"text-white font-bold text-lg",children:"F"})}),a.jsx("span",{className:"text-xl font-bold",children:"FreelanceHub"})]}),a.jsx("p",{className:"text-gray-400 text-sm mb-6",children:"The world's largest marketplace for creative and professional services. Connect with talented freelancers and grow your business."}),a.jsxs("div",{className:"space-y-2 text-sm text-gray-400",children:[a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(ds,{className:"w-4 h-4"}),a.jsx("span",{children:"<EMAIL>"})]}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(Pb,{className:"w-4 h-4"}),a.jsx("span",{children:"+1 (555) 123-4567"})]}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(Su,{className:"w-4 h-4"}),a.jsx("span",{children:"San Francisco, CA"})]})]})]}),a.jsxs("div",{children:[a.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Categories"}),a.jsx("ul",{className:"space-y-2",children:t.categories.map(n=>a.jsx("li",{children:a.jsx(M,{to:n.href,className:"text-gray-400 hover:text-white transition-colors text-sm",children:n.name})},n.name))})]}),a.jsxs("div",{children:[a.jsx("h3",{className:"text-lg font-semibold mb-4",children:"About"}),a.jsx("ul",{className:"space-y-2",children:t.about.map(n=>a.jsx("li",{children:a.jsx(M,{to:n.href,className:"text-gray-400 hover:text-white transition-colors text-sm",children:n.name})},n.name))})]}),a.jsxs("div",{children:[a.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Support"}),a.jsx("ul",{className:"space-y-2",children:t.support.map(n=>a.jsx("li",{children:a.jsx(M,{to:n.href,className:"text-gray-400 hover:text-white transition-colors text-sm",children:n.name})},n.name))})]}),a.jsxs("div",{children:[a.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Community"}),a.jsx("ul",{className:"space-y-2",children:t.community.map(n=>a.jsx("li",{children:a.jsx(M,{to:n.href,className:"text-gray-400 hover:text-white transition-colors text-sm",children:n.name})},n.name))})]})]}),a.jsx("div",{className:"mt-12 pt-8 border-t border-gray-800",children:a.jsxs("div",{className:"max-w-md",children:[a.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Stay Updated"}),a.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Subscribe to our newsletter for the latest updates and opportunities."}),a.jsxs("form",{className:"flex space-x-2",children:[a.jsx("input",{type:"email",placeholder:"Enter your email",className:"flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400"}),a.jsx("button",{type:"submit",className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Subscribe"})]})]})})]}),a.jsx("div",{className:"border-t border-gray-800",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:a.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[a.jsxs("div",{className:"text-gray-400 text-sm",children:["© ",e," FreelanceHub. All rights reserved."]}),a.jsxs("div",{className:"flex items-center space-x-4",children:[a.jsx("span",{className:"text-gray-400 text-sm",children:"Follow us:"}),a.jsx("div",{className:"flex space-x-3",children:r.map(n=>{const s=n.icon;return a.jsx("a",{href:n.href,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-white transition-colors","aria-label":n.name,children:a.jsx(s,{className:"w-5 h-5"})},n.name)})})]}),a.jsxs("div",{className:"flex items-center space-x-4 text-sm",children:[a.jsxs("select",{className:"bg-gray-800 border border-gray-700 rounded px-3 py-1 text-gray-400 focus:ring-2 focus:ring-blue-500",children:[a.jsx("option",{value:"en",children:"English"}),a.jsx("option",{value:"es",children:"Español"}),a.jsx("option",{value:"fr",children:"Français"}),a.jsx("option",{value:"de",children:"Deutsch"})]}),a.jsxs("select",{className:"bg-gray-800 border border-gray-700 rounded px-3 py-1 text-gray-400 focus:ring-2 focus:ring-blue-500",children:[a.jsx("option",{value:"usd",children:"USD"}),a.jsx("option",{value:"eur",children:"EUR"}),a.jsx("option",{value:"gbp",children:"GBP"}),a.jsx("option",{value:"cad",children:"CAD"})]})]})]})})})]})},Ub=[{_id:"1",slug:"professional-logo-design-modern-minimalist",title:"I will design a professional logo for your business",description:"Get a stunning, professional logo that represents your brand perfectly. I specialize in modern, minimalist designs that work across all platforms and media. With over 5 years of experience in graphic design, I've helped hundreds of businesses establish their visual identity.",fullDescription:`Welcome to my professional logo design service! I'm a passionate graphic designer with over 5 years of experience creating memorable brand identities for businesses worldwide.

What you get:
• Custom logo design tailored to your brand
• Multiple concept variations
• High-resolution files (PNG, JPG, SVG, AI)
• Commercial usage rights
• Unlimited revisions until satisfied
• Brand guidelines document
• Social media kit

My design process:
1. Brand discovery and research
2. Concept development
3. Initial design presentation
4. Revisions and refinements
5. Final delivery with all files

I work with businesses of all sizes, from startups to established companies. My designs are modern, timeless, and versatile - perfect for both digital and print applications.`,category:"graphics-design",subcategory:"logo-design",tags:["logo","branding","graphic design","business","modern","minimalist"],images:["https://images.unsplash.com/photo-1626785774573-4b799315345d?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1634942537034-2531766767d1?w=800&h=600&fit=crop"],pricing:{basic:{title:"Basic Logo",price:25,description:"1 logo concept, 2 revisions, 48h delivery",deliveryTime:2,revisions:2,features:["1 logo concept","2 revisions","High-res PNG & JPG","48h delivery"]},standard:{title:"Standard Package",price:50,description:"3 logo concepts, 5 revisions, 24h delivery",deliveryTime:1,revisions:5,features:["3 logo concepts","5 revisions","All file formats","Social media kit","24h delivery"]},premium:{title:"Premium Branding",price:100,description:"5 concepts, unlimited revisions, brand guidelines",deliveryTime:3,revisions:"unlimited",features:["5 logo concepts","Unlimited revisions","Brand guidelines","Business card design","Vector files","Commercial license"]}},seller:{_id:"seller1",name:"Sarah Johnson",username:"sarahdesigns",avatar:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",level:"Level 2 Seller",rating:4.9,reviewCount:847,responseTime:"1 hour",location:"United States",memberSince:"2019-03-15",languages:["English","Spanish"],skills:["Logo Design","Brand Identity","Graphic Design","Adobe Illustrator"]},rating:4.9,reviewCount:847,ordersInQueue:12,featured:!0,startingPrice:25,deliveryTime:2,createdAt:"2024-01-15T10:00:00Z",updatedAt:"2024-01-20T15:30:00Z"},{_id:"2",slug:"react-web-application-development",title:"I will develop a modern React web application",description:"Full-stack React developer with 7+ years experience. I build responsive, fast, and scalable web applications using the latest technologies including React, Node.js, and MongoDB.",fullDescription:`Transform your ideas into powerful web applications! I'm a senior full-stack developer specializing in React ecosystem with extensive experience in modern web development.

Technologies I use:
• Frontend: React, Next.js, TypeScript, Tailwind CSS
• Backend: Node.js, Express, MongoDB, PostgreSQL
• Tools: Git, Docker, AWS, Vercel
• Testing: Jest, Cypress, React Testing Library

What's included:
• Responsive design for all devices
• Clean, maintainable code
• SEO optimization
• Performance optimization
• Security best practices
• Documentation
• Post-delivery support

I follow agile development practices and maintain constant communication throughout the project.`,category:"programming-tech",subcategory:"web-development",tags:["react","javascript","web development","frontend","backend","full-stack"],images:["https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1517180102446-f3ece451e9d8?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800&h=600&fit=crop"],pricing:{basic:{title:"Landing Page",price:150,description:"Single page React application",deliveryTime:5,revisions:3,features:["Responsive design","Contact form","SEO optimization","3 revisions"]},standard:{title:"Multi-page Website",price:350,description:"Complete website with CMS",deliveryTime:10,revisions:5,features:["Up to 10 pages","Content management","Database integration","Admin panel","5 revisions"]},premium:{title:"Full Web App",price:750,description:"Complete web application with backend",deliveryTime:21,revisions:"unlimited",features:["Custom functionality","User authentication","Payment integration","API development","Deployment","Unlimited revisions"]}},seller:{_id:"seller2",name:"Alex Chen",username:"alexcodes",avatar:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",level:"Top Rated Seller",rating:4.95,reviewCount:1203,responseTime:"30 minutes",location:"Canada",memberSince:"2017-08-20",languages:["English","Mandarin"],skills:["React","Node.js","JavaScript","TypeScript","MongoDB"]},rating:4.95,reviewCount:1203,ordersInQueue:8,featured:!0,startingPrice:150,deliveryTime:5,createdAt:"2024-01-10T08:00:00Z",updatedAt:"2024-01-25T12:00:00Z"},{_id:"3",slug:"social-media-marketing-strategy",title:"I will create a comprehensive social media marketing strategy",description:"Boost your brand's online presence with a data-driven social media strategy. I'll help you grow your audience, increase engagement, and drive conversions across all major platforms.",fullDescription:`Ready to dominate social media? I'm a certified digital marketing specialist with 6+ years of experience helping brands achieve explosive growth on social platforms.

What you'll receive:
• Complete social media audit
• Platform-specific strategies
• Content calendar (30 days)
• Hashtag research
• Competitor analysis
• Growth tactics
• Analytics setup
• Performance metrics

Platforms I specialize in:
• Instagram & Instagram Stories
• Facebook & Facebook Ads
• Twitter/X
• LinkedIn
• TikTok
• YouTube
• Pinterest

I use proven strategies that have generated millions of impressions and thousands of conversions for my clients.`,category:"digital-marketing",subcategory:"social-media-marketing",tags:["social media","marketing","instagram","facebook","strategy","growth"],images:["https://images.unsplash.com/photo-1611926653458-09294b3142bf?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1432888622747-4eb9a8efeb07?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop"],pricing:{basic:{title:"Strategy Basics",price:75,description:"Social media audit + basic strategy",deliveryTime:3,revisions:2,features:["Platform audit","Basic strategy","Content ideas","2 revisions"]},standard:{title:"Complete Strategy",price:150,description:"Full strategy + content calendar",deliveryTime:5,revisions:3,features:["Complete audit","Detailed strategy","30-day content calendar","Hashtag research","3 revisions"]},premium:{title:"Growth Package",price:300,description:"Strategy + implementation + optimization",deliveryTime:7,revisions:5,features:["Everything in Standard","Competitor analysis","Ad strategy","60-day calendar","Monthly optimization","5 revisions"]}},seller:{_id:"seller3",name:"Maria Rodriguez",username:"mariamarketing",avatar:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",level:"Level 2 Seller",rating:4.8,reviewCount:592,responseTime:"2 hours",location:"Spain",memberSince:"2020-01-10",languages:["English","Spanish","French"],skills:["Social Media Marketing","Content Strategy","Facebook Ads","Instagram Growth"]},rating:4.8,reviewCount:592,ordersInQueue:15,featured:!1,startingPrice:75,deliveryTime:3,createdAt:"2024-01-12T14:00:00Z",updatedAt:"2024-01-22T16:45:00Z"}],Bb=[{_id:"4",slug:"professional-copywriting-sales-content",title:"I will write compelling copy that converts visitors into customers",description:"Award-winning copywriter with 8+ years experience. I craft persuasive copy for websites, emails, ads, and sales pages that drive results and boost conversions.",category:"writing-translation",subcategory:"copywriting",tags:["copywriting","sales copy","content writing","marketing","conversion"],images:["https://images.unsplash.com/photo-1455390582262-044cdead277a?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&h=600&fit=crop"],pricing:{basic:{title:"Website Copy",price:100,deliveryTime:3,revisions:2},standard:{title:"Sales Page",price:250,deliveryTime:5,revisions:3},premium:{title:"Complete Campaign",price:500,deliveryTime:7,revisions:5}},seller:{_id:"seller4",name:"David Thompson",username:"davidwrites",avatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",level:"Top Rated Seller",rating:4.92,reviewCount:1456},rating:4.92,reviewCount:1456,featured:!0,startingPrice:100},{_id:"5",slug:"animated-explainer-video-production",title:"I will create an engaging animated explainer video for your business",description:"Professional animator specializing in explainer videos. I create engaging, high-quality animations that explain your product or service in a clear and compelling way.",category:"video-animation",subcategory:"animated-videos",tags:["animation","explainer video","motion graphics","video production"],images:["https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=600&fit=crop"],pricing:{basic:{title:"30-second Video",price:200,deliveryTime:7,revisions:2},standard:{title:"60-second Video",price:400,deliveryTime:10,revisions:3},premium:{title:"90-second Video",price:650,deliveryTime:14,revisions:5}},seller:{_id:"seller5",name:"Emma Wilson",username:"emmaanimations",avatar:"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",level:"Level 2 Seller",rating:4.85,reviewCount:324},rating:4.85,reviewCount:324,featured:!1,startingPrice:200},{_id:"6",slug:"mobile-app-ui-ux-design",title:"I will design a stunning mobile app UI/UX that users love",description:"Expert mobile app designer with 6+ years experience. I create intuitive, beautiful interfaces that provide exceptional user experiences and drive engagement.",category:"graphics-design",subcategory:"mobile-design",tags:["mobile app","ui design","ux design","app interface","user experience"],images:["https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=600&fit=crop"],pricing:{basic:{title:"5 Screens",price:120,deliveryTime:5,revisions:3},standard:{title:"10 Screens",price:220,deliveryTime:7,revisions:4},premium:{title:"Complete App",price:450,deliveryTime:14,revisions:6}},seller:{_id:"seller6",name:"James Park",username:"jamesdesigns",avatar:"https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face",level:"Level 2 Seller",rating:4.87,reviewCount:678},rating:4.87,reviewCount:678,featured:!0,startingPrice:120},{_id:"7",slug:"seo-optimization-google-ranking",title:"I will boost your website ranking with advanced SEO strategies",description:"SEO specialist with proven track record of ranking websites on Google's first page. I use white-hat techniques to improve your search visibility and organic traffic.",category:"digital-marketing",subcategory:"seo",tags:["seo","google ranking","search optimization","organic traffic","keyword research"],images:["https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1553830591-fddf9c784d53?w=800&h=600&fit=crop"],pricing:{basic:{title:"SEO Audit",price:50,deliveryTime:2,revisions:1},standard:{title:"On-Page SEO",price:150,deliveryTime:7,revisions:2},premium:{title:"Complete SEO",price:350,deliveryTime:14,revisions:3}},seller:{_id:"seller7",name:"Lisa Kumar",username:"lisakumar",avatar:"https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face",level:"Top Rated Seller",rating:4.93,reviewCount:1124},rating:4.93,reviewCount:1124,featured:!1,startingPrice:50},{_id:"8",slug:"wordpress-website-development",title:"I will create a professional WordPress website for your business",description:"WordPress expert with 5+ years experience building custom websites. I create fast, secure, and SEO-optimized WordPress sites that convert visitors into customers.",category:"programming-tech",subcategory:"wordpress",tags:["wordpress","website development","cms","responsive design","ecommerce"],images:["https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800&h=600&fit=crop"],pricing:{basic:{title:"Basic Website",price:200,deliveryTime:7,revisions:3},standard:{title:"Business Website",price:400,deliveryTime:10,revisions:4},premium:{title:"E-commerce Site",price:800,deliveryTime:21,revisions:5}},seller:{_id:"seller8",name:"Michael Brown",username:"mikewp",avatar:"https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150&h=150&fit=crop&crop=face",level:"Level 2 Seller",rating:4.81,reviewCount:892},rating:4.81,reviewCount:892,featured:!0,startingPrice:200}],Vb=[{name:"Graphics & Design",slug:"graphics-design",icon:"🎨",description:"Logo design, branding, illustrations",gigCount:1247,subcategories:["Logo Design","Brand Style Guides","Business Cards","Illustration","Web Design"]},{name:"Programming & Tech",slug:"programming-tech",icon:"💻",description:"Web development, mobile apps, software",gigCount:892,subcategories:["Web Development","Mobile Apps","Desktop Applications","Databases","DevOps"]},{name:"Digital Marketing",slug:"digital-marketing",icon:"📈",description:"SEO, social media, advertising",gigCount:634,subcategories:["Social Media Marketing","SEO","Content Marketing","Email Marketing","PPC Advertising"]},{name:"Writing & Translation",slug:"writing-translation",icon:"✍️",description:"Content writing, copywriting, translation",gigCount:756,subcategories:["Content Writing","Copywriting","Technical Writing","Translation","Proofreading"]},{name:"Video & Animation",slug:"video-animation",icon:"🎬",description:"Video editing, animation, motion graphics",gigCount:423,subcategories:["Video Editing","Animation","Motion Graphics","Whiteboard Videos","Video Marketing"]},{name:"Music & Audio",slug:"music-audio",icon:"🎵",description:"Voice over, music production, audio editing",gigCount:298,subcategories:["Voice Over","Music Production","Audio Editing","Sound Design","Podcast Production"]}],Wb=[...Ub,...Bb],Hb=[{_id:"9",slug:"voice-over-professional-recording",title:"I will record a professional voice over for your project",description:"Professional voice actor with broadcast-quality home studio. I deliver clear, engaging voice overs for commercials, explainer videos, and audiobooks.",category:"music-audio",subcategory:"voice-over",tags:["voice over","narration","commercial","audio recording"],images:["https://images.unsplash.com/photo-1478737270239-2f02b77fc618?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1590602847861-f357a9332bbc?w=800&h=600&fit=crop"],pricing:{basic:{title:"100 Words",price:30,deliveryTime:2,revisions:2},standard:{title:"300 Words",price:75,deliveryTime:3,revisions:3},premium:{title:"500 Words",price:120,deliveryTime:5,revisions:4}},seller:{_id:"seller9",name:"Robert Voice",username:"robertvoice",avatar:"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",level:"Level 1 Seller",rating:4.76,reviewCount:234},rating:4.76,reviewCount:234,featured:!1,startingPrice:30},{_id:"10",slug:"business-plan-writing-service",title:"I will write a comprehensive business plan for your startup",description:"MBA-qualified business consultant with 10+ years experience. I create detailed, investor-ready business plans that help secure funding and guide growth.",category:"writing-translation",subcategory:"business-writing",tags:["business plan","startup","consulting","investor pitch","strategy"],images:["https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=800&h=600&fit=crop","https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop"],pricing:{basic:{title:"Basic Plan",price:300,deliveryTime:7,revisions:2},standard:{title:"Detailed Plan",price:600,deliveryTime:14,revisions:3},premium:{title:"Investor Ready",price:1200,deliveryTime:21,revisions:5}},seller:{_id:"seller10",name:"Jennifer Adams",username:"jenniferplans",avatar:"https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=150&h=150&fit=crop&crop=face",level:"Top Rated Seller",rating:4.96,reviewCount:567},rating:4.96,reviewCount:567,featured:!0,startingPrice:300}],yr=[...Wb,...Hb],Cu={getGigs:(e={})=>{let t=[...yr];if(e.search){const o=e.search.toLowerCase();t=t.filter(c=>c.title.toLowerCase().includes(o)||c.description.toLowerCase().includes(o)||c.tags.some(u=>u.toLowerCase().includes(o)))}if(e.category&&(t=t.filter(o=>o.category===e.category)),e.minPrice&&(t=t.filter(o=>o.startingPrice>=parseInt(e.minPrice))),e.maxPrice&&(t=t.filter(o=>o.startingPrice<=parseInt(e.maxPrice))),e.rating&&(t=t.filter(o=>o.rating>=parseFloat(e.rating))),e.delivery&&(t=t.filter(o=>o.deliveryTime<=parseInt(e.delivery))),e.sort)switch(e.sort){case"price-low":t.sort((o,c)=>o.startingPrice-c.startingPrice);break;case"price-high":t.sort((o,c)=>c.startingPrice-o.startingPrice);break;case"rating":t.sort((o,c)=>c.rating-o.rating);break;case"reviews":t.sort((o,c)=>c.reviewCount-o.reviewCount);break;case"newest":t.sort((o,c)=>new Date(c.createdAt)-new Date(o.createdAt));break;default:t.sort((o,c)=>o.featured&&!c.featured?-1:!o.featured&&c.featured?1:c.rating-o.rating)}const r=parseInt(e.page)||1,n=parseInt(e.limit)||12,s=(r-1)*n,i=s+n;return{gigs:t.slice(s,i),total:t.length,totalPages:Math.ceil(t.length/n),currentPage:r,hasNextPage:i<t.length,hasPrevPage:r>1}},getFeaturedGigs:(e=8)=>yr.filter(r=>r.featured).slice(0,e),getGigBySlug:e=>yr.find(t=>t.slug===e),getGigById:e=>yr.find(t=>t._id===e),getCategories:()=>Vb.map(e=>({...e,gigCount:yr.filter(t=>t.category===e.slug).length})),getRelatedGigs:(e,t=4)=>{const r=yr.find(s=>s._id===e);return r?yr.filter(s=>s._id!==e&&s.category===r.category).sort((s,i)=>i.rating-s.rating).slice(0,t):[]},getSearchSuggestions:e=>{if(!e||e.length<2)return[];const t=new Set,r=e.toLowerCase();return yr.forEach(n=>{n.title.toLowerCase().includes(r)&&t.add(n.title),n.tags.forEach(s=>{s.toLowerCase().includes(r)&&t.add(s)})}),Array.from(t).slice(0,5)}},Zb=[{_id:"review1",gigId:"1",buyer:{name:"John Smith",avatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face"},rating:5,comment:"Absolutely amazing work! Sarah delivered exactly what I needed and more. The logo perfectly captures our brand essence.",createdAt:"2024-01-20T10:00:00Z"},{_id:"review2",gigId:"1",buyer:{name:"Emily Davis",avatar:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face"},rating:5,comment:"Professional, fast, and creative. Will definitely work with Sarah again!",createdAt:"2024-01-18T15:30:00Z"}];function pg(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(r=pg(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function kf(){for(var e,t,r=0,n="",s=arguments.length;r<s;r++)(e=arguments[r])&&(t=pg(e))&&(n&&(n+=" "),n+=t);return n}const dr=({size:e="md",color:t="primary",className:r="",text:n=""})=>{const s={sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12",xl:"w-16 h-16"},i={primary:"border-blue-600",secondary:"border-gray-600",white:"border-white",gray:"border-gray-600"};return a.jsxs("div",{className:kf("flex flex-col items-center justify-center",r),children:[a.jsx("div",{className:kf("loading-spinner border-2 border-gray-300 border-t-2",s[e],i[t])}),n&&a.jsx("p",{className:"mt-2 text-sm text-gray-600",children:n})]})},Gb=()=>{const[e,t]=j.useState([]),[r,n]=j.useState(!0),[s,i]=j.useState(""),{isAuthenticated:l,user:o}=We(),c=xt();j.useEffect(()=>{u()},[]);const u=async()=>{try{const p=Cu.getFeaturedGigs(8);t(p)}catch(p){console.error("Error fetching featured gigs:",p),t([])}finally{n(!1)}},d=p=>{p.preventDefault(),s.trim()&&c(`/gigs?search=${encodeURIComponent(s.trim())}`)},f=[{name:"Graphics & Design",icon:a.jsx(Eb,{className:"w-8 h-8"}),emoji:"🎨",description:"Logo design, web design, print design",href:"/gigs?category=graphics-design",gigCount:"1,247+ services",color:"from-pink-500 to-rose-500"},{name:"Programming & Tech",icon:a.jsx(yb,{className:"w-8 h-8"}),emoji:"💻",description:"Web development, mobile apps, software",href:"/gigs?category=programming-tech",gigCount:"892+ services",color:"from-blue-500 to-cyan-500"},{name:"Digital Marketing",icon:a.jsx(Lb,{className:"w-8 h-8"}),emoji:"📈",description:"Social media, SEO, content marketing",href:"/gigs?category=digital-marketing",gigCount:"634+ services",color:"from-green-500 to-emerald-500"},{name:"Writing & Translation",icon:a.jsx(cg,{className:"w-8 h-8"}),emoji:"✍️",description:"Content writing, copywriting, translation",href:"/gigs?category=writing-translation",gigCount:"756+ services",color:"from-purple-500 to-violet-500"},{name:"Video & Animation",icon:a.jsx(Mb,{className:"w-8 h-8"}),emoji:"🎬",description:"Video editing, animation, motion graphics",href:"/gigs?category=video-animation",gigCount:"423+ services",color:"from-orange-500 to-red-500"},{name:"Music & Audio",icon:a.jsx(_b,{className:"w-8 h-8"}),emoji:"🎵",description:"Voice over, music production, audio editing",href:"/gigs?category=music-audio",gigCount:"298+ services",color:"from-indigo-500 to-purple-500"}],h=[{icon:Ib,value:"2M+",label:"Active Users"},{icon:Fb,value:"500K+",label:"Projects Completed"},{icon:gb,value:"99%",label:"Customer Satisfaction"}],w=[{title:"Find the perfect freelancer",description:"Browse through thousands of talented professionals ready to help with your project.",icon:"🔍"},{title:"Work with confidence",description:"Our secure payment system ensures you only pay when you're 100% satisfied.",icon:"🛡️"},{title:"Get work done fast",description:"Most projects are completed within days, not weeks or months.",icon:"⚡"}];return a.jsxs("div",{className:"min-h-screen",children:[a.jsxs("section",{className:"relative bg-gradient-to-br from-blue-600 via-blue-700 to-purple-800 text-white py-20 overflow-hidden",children:[a.jsxs("div",{className:"absolute inset-0 opacity-10",children:[a.jsx("div",{className:"absolute top-10 left-10 w-20 h-20 bg-white rounded-full animate-float"}),a.jsx("div",{className:"absolute top-32 right-20 w-16 h-16 bg-yellow-400 rounded-full animate-float",style:{animationDelay:"1s"}}),a.jsx("div",{className:"absolute bottom-20 left-1/4 w-12 h-12 bg-pink-400 rounded-full animate-float",style:{animationDelay:"2s"}}),a.jsx("div",{className:"absolute bottom-32 right-1/3 w-14 h-14 bg-green-400 rounded-full animate-float",style:{animationDelay:"0.5s"}})]}),a.jsx("div",{className:"absolute inset-0 opacity-5",children:a.jsx("div",{className:"absolute inset-0",style:{backgroundImage:`url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`}})}),a.jsx("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsxs("div",{className:"text-center",children:[a.jsxs("h1",{className:"text-4xl md:text-6xl font-bold mb-6 leading-tight animate-fadeInUp",children:["Find the perfect",a.jsx("span",{className:"block text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500 animate-slideInLeft",children:"freelance services"}),"for your business"]}),a.jsx("p",{className:"text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto animate-fadeInUp animate-stagger-2",children:"Connect with talented freelancers worldwide and get your projects done with quality and speed"}),a.jsx("form",{onSubmit:d,className:"max-w-2xl mx-auto mb-8 animate-fadeInUp animate-stagger-3",children:a.jsxs("div",{className:"relative group",children:[a.jsx("input",{type:"text",value:s,onChange:p=>i(p.target.value),placeholder:"Try 'logo design' or 'website development'",className:"w-full px-6 py-4 text-lg text-gray-900 rounded-xl focus:ring-4 focus:ring-blue-300 focus:outline-none shadow-lg transition-all duration-300 group-hover:shadow-xl"}),a.jsx("button",{type:"submit",className:"absolute right-2 top-1/2 transform -translate-y-1/2 px-6 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105",children:a.jsx(sr,{className:"w-5 h-5"})})]})}),a.jsxs("div",{className:"flex flex-wrap justify-center gap-2 text-sm",children:[a.jsx("span",{className:"text-blue-200",children:"Popular:"}),["Logo Design","WordPress","Voice Over","Video Editing","Translation"].map(p=>a.jsx("button",{onClick:()=>c(`/gigs?search=${encodeURIComponent(p)}`),className:"px-3 py-1 bg-blue-500 bg-opacity-30 rounded-full hover:bg-opacity-50 transition-colors",children:p},p))]})]})})]}),a.jsx("section",{className:"py-16 bg-gray-50",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 text-center",children:h.map((p,y)=>{const b=p.icon;return a.jsxs("div",{className:"flex flex-col items-center",children:[a.jsx("div",{className:"w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-4",children:a.jsx(b,{className:"w-8 h-8 text-white"})}),a.jsx("div",{className:"text-3xl font-bold text-gray-900 mb-2",children:p.value}),a.jsx("div",{className:"text-gray-600",children:p.label})]},y)})})})}),a.jsx("section",{className:"py-20 bg-gradient-to-b from-white to-gray-50",children:a.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[a.jsxs("div",{className:"text-center mb-16 animate-fadeInUp",children:[a.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Explore our categories"}),a.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Find the perfect service for your needs from our diverse range of categories"})]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:f.map((p,y)=>a.jsxs(M,{to:p.href,className:"group relative p-8 bg-white rounded-2xl shadow-lg border border-gray-100 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 animate-fadeInUp overflow-hidden",style:{animationDelay:`${(y+1)*.1}s`},children:[a.jsx("div",{className:`absolute inset-0 bg-gradient-to-br ${p.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500`}),a.jsxs("div",{className:"relative mb-6",children:[a.jsx("div",{className:`w-16 h-16 bg-gradient-to-br ${p.color} rounded-2xl flex items-center justify-center text-white mb-4 group-hover:scale-110 transition-transform duration-300`,children:p.icon}),a.jsx("div",{className:"text-3xl absolute -top-2 -right-2 opacity-20 group-hover:opacity-40 transition-opacity",children:p.emoji})]}),a.jsxs("div",{className:"relative",children:[a.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-3 group-hover:text-gray-800 transition-colors",children:p.name}),a.jsx("p",{className:"text-gray-600 mb-4 leading-relaxed",children:p.description}),a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm text-gray-500 font-medium",children:p.gigCount}),a.jsxs("div",{className:"flex items-center text-blue-600 group-hover:text-blue-700 font-medium",children:[a.jsx("span",{className:"text-sm",children:"Explore"}),a.jsx(Nf,{className:"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300"})]})]})]})]},y))})]})}),a.jsx("section",{className:"py-16 bg-gray-50",children:a.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[a.jsxs("div",{className:"text-center mb-12",children:[a.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Featured services"}),a.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Discover top-rated services from our most talented freelancers"})]}),r?a.jsx("div",{className:"flex justify-center",children:a.jsx(dr,{size:"lg"})}):a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:e.slice(0,8).map(p=>{var y,b,x,m;return a.jsxs(M,{to:`/gigs/${p.slug}`,className:"group bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow duration-300",children:[a.jsx("div",{className:"aspect-w-16 aspect-h-9 bg-gray-200",children:(y=p.images)!=null&&y[0]?a.jsx("img",{src:p.images[0],alt:p.title,className:"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"}):a.jsx("div",{className:"w-full h-48 bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center",children:a.jsx("span",{className:"text-white text-2xl font-bold",children:p.title.charAt(0)})})}),a.jsxs("div",{className:"p-4",children:[a.jsx("h3",{className:"font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors",children:p.title}),a.jsx("div",{className:"flex items-center mb-2",children:a.jsxs("div",{className:"flex items-center",children:[a.jsx(zr,{className:"w-4 h-4 text-yellow-400 fill-current"}),a.jsxs("span",{className:"text-sm text-gray-600 ml-1",children:[((b=p.rating)==null?void 0:b.toFixed(1))||"5.0"," (",p.reviewCount||0,")"]})]})}),a.jsx("div",{className:"flex items-center justify-between",children:a.jsxs("span",{className:"text-lg font-bold text-gray-900",children:["From $",((m=(x=p.pricing)==null?void 0:x.basic)==null?void 0:m.price)||p.startingPrice]})})]})]},p._id)})}),a.jsx("div",{className:"text-center mt-8",children:a.jsxs(M,{to:"/gigs",className:"inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors",children:["View All Services",a.jsx(Nf,{className:"w-5 h-5 ml-2"})]})})]})}),a.jsx("section",{className:"py-16",children:a.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[a.jsx("div",{className:"text-center mb-12",children:a.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Why choose FreelanceHub?"})}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:w.map((p,y)=>a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:"text-6xl mb-4",children:p.icon}),a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:p.title}),a.jsx("p",{className:"text-gray-600",children:p.description})]},y))})]})}),a.jsx("section",{className:"py-16 bg-blue-600",children:a.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[a.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-4",children:"Ready to get started?"}),a.jsx("p",{className:"text-xl text-blue-100 mb-8 max-w-2xl mx-auto",children:"Join millions of people who use FreelanceHub to turn their ideas into reality"}),a.jsx("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:l?a.jsx(M,{to:(o==null?void 0:o.role)==="freelancer"?"/dashboard/freelancer":"/gigs",className:"px-8 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors",children:(o==null?void 0:o.role)==="freelancer"?"Go to Dashboard":"Browse Services"}):a.jsxs(a.Fragment,{children:[a.jsx(M,{to:"/register",className:"px-8 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors",children:"Join as a Client"}),a.jsx(M,{to:"/register?role=freelancer",className:"px-8 py-3 bg-blue-700 text-white font-semibold rounded-lg hover:bg-blue-800 transition-colors border border-blue-500",children:"Become a Freelancer"})]})})]})})]})};var wi=e=>e.type==="checkbox",sn=e=>e instanceof Date,Ye=e=>e==null;const gg=e=>typeof e=="object";var Re=e=>!Ye(e)&&!Array.isArray(e)&&gg(e)&&!sn(e),qb=e=>Re(e)&&e.target?wi(e.target)?e.target.checked:e.target.value:e,Qb=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,Jb=(e,t)=>e.has(Qb(t)),Kb=e=>{const t=e.constructor&&e.constructor.prototype;return Re(t)&&t.hasOwnProperty("isPrototypeOf")},Eu=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function Me(e){let t;const r=Array.isArray(e),n=typeof FileList<"u"?e instanceof FileList:!1;if(e instanceof Date)t=new Date(e);else if(!(Eu&&(e instanceof Blob||n))&&(r||Re(e)))if(t=r?[]:Object.create(Object.getPrototypeOf(e)),!r&&!Kb(e))t=e;else for(const s in e)e.hasOwnProperty(s)&&(t[s]=Me(e[s]));else return e;return t}var bl=e=>/^\w*$/.test(e),Ae=e=>e===void 0,Pu=e=>Array.isArray(e)?e.filter(Boolean):[],Tu=e=>Pu(e.replace(/["|']|\]/g,"").split(/\.|\[/)),B=(e,t,r)=>{if(!t||!Re(e))return r;const n=(bl(t)?[t]:Tu(t)).reduce((s,i)=>Ye(s)?s:s[i],e);return Ae(n)||n===e?Ae(e[t])?r:e[t]:n},Vt=e=>typeof e=="boolean",he=(e,t,r)=>{let n=-1;const s=bl(t)?[t]:Tu(t),i=s.length,l=i-1;for(;++n<i;){const o=s[n];let c=r;if(n!==l){const u=e[o];c=Re(u)||Array.isArray(u)?u:isNaN(+s[n+1])?{}:[]}if(o==="__proto__"||o==="constructor"||o==="prototype")return;e[o]=c,e=e[o]}};const Sf={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},Rt={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},Xt={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},Yb=Ne.createContext(null);Yb.displayName="HookFormContext";var Xb=(e,t,r,n=!0)=>{const s={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(s,i,{get:()=>{const l=i;return t._proxyFormState[l]!==Rt.all&&(t._proxyFormState[l]=!n||Rt.all),r&&(r[l]=!0),e[l]}});return s};const e2=typeof window<"u"?Ne.useLayoutEffect:Ne.useEffect;var Ht=e=>typeof e=="string",t2=(e,t,r,n,s)=>Ht(e)?(n&&t.watch.add(e),B(r,e,s)):Array.isArray(e)?e.map(i=>(n&&t.watch.add(i),B(r,i))):(n&&(t.watchAll=!0),r),fc=e=>Ye(e)||!gg(e);function Sr(e,t,r=new WeakSet){if(fc(e)||fc(t))return e===t;if(sn(e)&&sn(t))return e.getTime()===t.getTime();const n=Object.keys(e),s=Object.keys(t);if(n.length!==s.length)return!1;if(r.has(e)||r.has(t))return!0;r.add(e),r.add(t);for(const i of n){const l=e[i];if(!s.includes(i))return!1;if(i!=="ref"){const o=t[i];if(sn(l)&&sn(o)||Re(l)&&Re(o)||Array.isArray(l)&&Array.isArray(o)?!Sr(l,o,r):l!==o)return!1}}return!0}var xg=(e,t,r,n,s)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[n]:s||!0}}:{},Ms=e=>Array.isArray(e)?e:[e],_f=()=>{let e=[];return{get observers(){return e},next:s=>{for(const i of e)i.next&&i.next(s)},subscribe:s=>(e.push(s),{unsubscribe:()=>{e=e.filter(i=>i!==s)}}),unsubscribe:()=>{e=[]}}},nt=e=>Re(e)&&!Object.keys(e).length,Ru=e=>e.type==="file",Ot=e=>typeof e=="function",Ua=e=>{if(!Eu)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},yg=e=>e.type==="select-multiple",Ou=e=>e.type==="radio",r2=e=>Ou(e)||wi(e),so=e=>Ua(e)&&e.isConnected;function n2(e,t){const r=t.slice(0,-1).length;let n=0;for(;n<r;)e=Ae(e)?n++:e[t[n++]];return e}function s2(e){for(const t in e)if(e.hasOwnProperty(t)&&!Ae(e[t]))return!1;return!0}function Oe(e,t){const r=Array.isArray(t)?t:bl(t)?[t]:Tu(t),n=r.length===1?e:n2(e,r),s=r.length-1,i=r[s];return n&&delete n[i],s!==0&&(Re(n)&&nt(n)||Array.isArray(n)&&s2(n))&&Oe(e,r.slice(0,-1)),e}var vg=e=>{for(const t in e)if(Ot(e[t]))return!0;return!1};function Ba(e,t={}){const r=Array.isArray(e);if(Re(e)||r)for(const n in e)Array.isArray(e[n])||Re(e[n])&&!vg(e[n])?(t[n]=Array.isArray(e[n])?[]:{},Ba(e[n],t[n])):Ye(e[n])||(t[n]=!0);return t}function wg(e,t,r){const n=Array.isArray(e);if(Re(e)||n)for(const s in e)Array.isArray(e[s])||Re(e[s])&&!vg(e[s])?Ae(t)||fc(r[s])?r[s]=Array.isArray(e[s])?Ba(e[s],[]):{...Ba(e[s])}:wg(e[s],Ye(t)?{}:t[s],r[s]):r[s]=!Sr(e[s],t[s]);return r}var bs=(e,t)=>wg(e,t,Ba(t));const Cf={value:!1,isValid:!1},Ef={value:!0,isValid:!0};var bg=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(r=>r&&r.checked&&!r.disabled).map(r=>r.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!Ae(e[0].attributes.value)?Ae(e[0].value)||e[0].value===""?Ef:{value:e[0].value,isValid:!0}:Ef:Cf}return Cf},jg=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:n})=>Ae(e)?e:t?e===""?NaN:e&&+e:r&&Ht(e)?new Date(e):n?n(e):e;const Pf={isValid:!1,value:null};var Ng=e=>Array.isArray(e)?e.reduce((t,r)=>r&&r.checked&&!r.disabled?{isValid:!0,value:r.value}:t,Pf):Pf;function Tf(e){const t=e.ref;return Ru(t)?t.files:Ou(t)?Ng(e.refs).value:yg(t)?[...t.selectedOptions].map(({value:r})=>r):wi(t)?bg(e.refs).value:jg(Ae(t.value)?e.ref.value:t.value,e)}var i2=(e,t,r,n)=>{const s={};for(const i of e){const l=B(t,i);l&&he(s,i,l._f)}return{criteriaMode:r,names:[...e],fields:s,shouldUseNativeValidation:n}},Va=e=>e instanceof RegExp,js=e=>Ae(e)?e:Va(e)?e.source:Re(e)?Va(e.value)?e.value.source:e.value:e,Rf=e=>({isOnSubmit:!e||e===Rt.onSubmit,isOnBlur:e===Rt.onBlur,isOnChange:e===Rt.onChange,isOnAll:e===Rt.all,isOnTouch:e===Rt.onTouched});const Of="AsyncFunction";var a2=e=>!!e&&!!e.validate&&!!(Ot(e.validate)&&e.validate.constructor.name===Of||Re(e.validate)&&Object.values(e.validate).find(t=>t.constructor.name===Of)),l2=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),Af=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(n=>e.startsWith(n)&&/^\.\w+/.test(e.slice(n.length))));const $s=(e,t,r,n)=>{for(const s of r||Object.keys(e)){const i=B(e,s);if(i){const{_f:l,...o}=i;if(l){if(l.refs&&l.refs[0]&&t(l.refs[0],s)&&!n)return!0;if(l.ref&&t(l.ref,l.name)&&!n)return!0;if($s(o,t))break}else if(Re(o)&&$s(o,t))break}}};function Lf(e,t,r){const n=B(e,r);if(n||bl(r))return{error:n,name:r};const s=r.split(".");for(;s.length;){const i=s.join("."),l=B(t,i),o=B(e,i);if(l&&!Array.isArray(l)&&r!==i)return{name:r};if(o&&o.type)return{name:i,error:o};if(o&&o.root&&o.root.type)return{name:`${i}.root`,error:o.root};s.pop()}return{name:r}}var o2=(e,t,r,n)=>{r(e);const{name:s,...i}=e;return nt(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(l=>t[l]===(!n||Rt.all))},c2=(e,t,r)=>!e||!t||e===t||Ms(e).some(n=>n&&(r?n===t:n.startsWith(t)||t.startsWith(n))),u2=(e,t,r,n,s)=>s.isOnAll?!1:!r&&s.isOnTouch?!(t||e):(r?n.isOnBlur:s.isOnBlur)?!e:(r?n.isOnChange:s.isOnChange)?e:!0,d2=(e,t)=>!Pu(B(e,t)).length&&Oe(e,t),f2=(e,t,r)=>{const n=Ms(B(e,r));return he(n,"root",t[r]),he(e,r,n),e},aa=e=>Ht(e);function Ff(e,t,r="validate"){if(aa(e)||Array.isArray(e)&&e.every(aa)||Vt(e)&&!e)return{type:r,message:aa(e)?e:"",ref:t}}var jn=e=>Re(e)&&!Va(e)?e:{value:e,message:""},Df=async(e,t,r,n,s,i)=>{const{ref:l,refs:o,required:c,maxLength:u,minLength:d,min:f,max:h,pattern:w,validate:p,name:y,valueAsNumber:b,mount:x}=e._f,m=B(r,y);if(!x||t.has(y))return{};const g=o?o[0]:l,N=ee=>{s&&g.reportValidity&&(g.setCustomValidity(Vt(ee)?"":ee||""),g.reportValidity())},_={},R=Ou(l),E=wi(l),A=R||E,q=(b||Ru(l))&&Ae(l.value)&&Ae(m)||Ua(l)&&l.value===""||m===""||Array.isArray(m)&&!m.length,Z=xg.bind(null,y,n,_),ue=(ee,re,ae,je=Xt.maxLength,xe=Xt.minLength)=>{const V=ee?re:ae;_[y]={type:ee?je:xe,message:V,ref:l,...Z(ee?je:xe,V)}};if(i?!Array.isArray(m)||!m.length:c&&(!A&&(q||Ye(m))||Vt(m)&&!m||E&&!bg(o).isValid||R&&!Ng(o).isValid)){const{value:ee,message:re}=aa(c)?{value:!!c,message:c}:jn(c);if(ee&&(_[y]={type:Xt.required,message:re,ref:g,...Z(Xt.required,re)},!n))return N(re),_}if(!q&&(!Ye(f)||!Ye(h))){let ee,re;const ae=jn(h),je=jn(f);if(!Ye(m)&&!isNaN(m)){const xe=l.valueAsNumber||m&&+m;Ye(ae.value)||(ee=xe>ae.value),Ye(je.value)||(re=xe<je.value)}else{const xe=l.valueAsDate||new Date(m),V=D=>new Date(new Date().toDateString()+" "+D),T=l.type=="time",U=l.type=="week";Ht(ae.value)&&m&&(ee=T?V(m)>V(ae.value):U?m>ae.value:xe>new Date(ae.value)),Ht(je.value)&&m&&(re=T?V(m)<V(je.value):U?m<je.value:xe<new Date(je.value))}if((ee||re)&&(ue(!!ee,ae.message,je.message,Xt.max,Xt.min),!n))return N(_[y].message),_}if((u||d)&&!q&&(Ht(m)||i&&Array.isArray(m))){const ee=jn(u),re=jn(d),ae=!Ye(ee.value)&&m.length>+ee.value,je=!Ye(re.value)&&m.length<+re.value;if((ae||je)&&(ue(ae,ee.message,re.message),!n))return N(_[y].message),_}if(w&&!q&&Ht(m)){const{value:ee,message:re}=jn(w);if(Va(ee)&&!m.match(ee)&&(_[y]={type:Xt.pattern,message:re,ref:l,...Z(Xt.pattern,re)},!n))return N(re),_}if(p){if(Ot(p)){const ee=await p(m,r),re=Ff(ee,g);if(re&&(_[y]={...re,...Z(Xt.validate,re.message)},!n))return N(re.message),_}else if(Re(p)){let ee={};for(const re in p){if(!nt(ee)&&!n)break;const ae=Ff(await p[re](m,r),g,re);ae&&(ee={...ae,...Z(re,ae.message)},N(ae.message),n&&(_[y]=ee))}if(!nt(ee)&&(_[y]={ref:g,...ee},!n))return _}}return N(!0),_};const m2={mode:Rt.onSubmit,reValidateMode:Rt.onChange,shouldFocusError:!0};function h2(e={}){let t={...m2,...e},r={submitCount:0,isDirty:!1,isReady:!1,isLoading:Ot(t.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1},n={},s=Re(t.defaultValues)||Re(t.values)?Me(t.defaultValues||t.values)||{}:{},i=t.shouldUnregister?{}:Me(s),l={action:!1,mount:!1,watch:!1},o={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},c,u=0;const d={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let f={...d};const h={array:_f(),state:_f()},w=t.criteriaMode===Rt.all,p=v=>k=>{clearTimeout(u),u=setTimeout(v,k)},y=async v=>{if(!t.disabled&&(d.isValid||f.isValid||v)){const k=t.resolver?nt((await E()).errors):await q(n,!0);k!==r.isValid&&h.state.next({isValid:k})}},b=(v,k)=>{!t.disabled&&(d.isValidating||d.validatingFields||f.isValidating||f.validatingFields)&&((v||Array.from(o.mount)).forEach(C=>{C&&(k?he(r.validatingFields,C,k):Oe(r.validatingFields,C))}),h.state.next({validatingFields:r.validatingFields,isValidating:!nt(r.validatingFields)}))},x=(v,k=[],C,$,F=!0,L=!0)=>{if($&&C&&!t.disabled){if(l.action=!0,L&&Array.isArray(B(n,v))){const G=C(B(n,v),$.argA,$.argB);F&&he(n,v,G)}if(L&&Array.isArray(B(r.errors,v))){const G=C(B(r.errors,v),$.argA,$.argB);F&&he(r.errors,v,G),d2(r.errors,v)}if((d.touchedFields||f.touchedFields)&&L&&Array.isArray(B(r.touchedFields,v))){const G=C(B(r.touchedFields,v),$.argA,$.argB);F&&he(r.touchedFields,v,G)}(d.dirtyFields||f.dirtyFields)&&(r.dirtyFields=bs(s,i)),h.state.next({name:v,isDirty:ue(v,k),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else he(i,v,k)},m=(v,k)=>{he(r.errors,v,k),h.state.next({errors:r.errors})},g=v=>{r.errors=v,h.state.next({errors:r.errors,isValid:!1})},N=(v,k,C,$)=>{const F=B(n,v);if(F){const L=B(i,v,Ae(C)?B(s,v):C);Ae(L)||$&&$.defaultChecked||k?he(i,v,k?L:Tf(F._f)):ae(v,L),l.mount&&y()}},_=(v,k,C,$,F)=>{let L=!1,G=!1;const de={name:v};if(!t.disabled){if(!C||$){(d.isDirty||f.isDirty)&&(G=r.isDirty,r.isDirty=de.isDirty=ue(),L=G!==de.isDirty);const pe=Sr(B(s,v),k);G=!!B(r.dirtyFields,v),pe?Oe(r.dirtyFields,v):he(r.dirtyFields,v,!0),de.dirtyFields=r.dirtyFields,L=L||(d.dirtyFields||f.dirtyFields)&&G!==!pe}if(C){const pe=B(r.touchedFields,v);pe||(he(r.touchedFields,v,C),de.touchedFields=r.touchedFields,L=L||(d.touchedFields||f.touchedFields)&&pe!==C)}L&&F&&h.state.next(de)}return L?de:{}},R=(v,k,C,$)=>{const F=B(r.errors,v),L=(d.isValid||f.isValid)&&Vt(k)&&r.isValid!==k;if(t.delayError&&C?(c=p(()=>m(v,C)),c(t.delayError)):(clearTimeout(u),c=null,C?he(r.errors,v,C):Oe(r.errors,v)),(C?!Sr(F,C):F)||!nt($)||L){const G={...$,...L&&Vt(k)?{isValid:k}:{},errors:r.errors,name:v};r={...r,...G},h.state.next(G)}},E=async v=>{b(v,!0);const k=await t.resolver(i,t.context,i2(v||o.mount,n,t.criteriaMode,t.shouldUseNativeValidation));return b(v),k},A=async v=>{const{errors:k}=await E(v);if(v)for(const C of v){const $=B(k,C);$?he(r.errors,C,$):Oe(r.errors,C)}else r.errors=k;return k},q=async(v,k,C={valid:!0})=>{for(const $ in v){const F=v[$];if(F){const{_f:L,...G}=F;if(L){const de=o.array.has(L.name),pe=F._f&&a2(F._f);pe&&d.validatingFields&&b([$],!0);const vt=await Df(F,o.disabled,i,w,t.shouldUseNativeValidation&&!k,de);if(pe&&d.validatingFields&&b([$]),vt[L.name]&&(C.valid=!1,k))break;!k&&(B(vt,L.name)?de?f2(r.errors,vt,L.name):he(r.errors,L.name,vt[L.name]):Oe(r.errors,L.name))}!nt(G)&&await q(G,k,C)}}return C.valid},Z=()=>{for(const v of o.unMount){const k=B(n,v);k&&(k._f.refs?k._f.refs.every(C=>!so(C)):!so(k._f.ref))&&pr(v)}o.unMount=new Set},ue=(v,k)=>!t.disabled&&(v&&k&&he(i,v,k),!Sr(D(),s)),ee=(v,k,C)=>t2(v,o,{...l.mount?i:Ae(k)?s:Ht(v)?{[v]:k}:k},C,k),re=v=>Pu(B(l.mount?i:s,v,t.shouldUnregister?B(s,v,[]):[])),ae=(v,k,C={})=>{const $=B(n,v);let F=k;if($){const L=$._f;L&&(!L.disabled&&he(i,v,jg(k,L)),F=Ua(L.ref)&&Ye(k)?"":k,yg(L.ref)?[...L.ref.options].forEach(G=>G.selected=F.includes(G.value)):L.refs?wi(L.ref)?L.refs.forEach(G=>{(!G.defaultChecked||!G.disabled)&&(Array.isArray(F)?G.checked=!!F.find(de=>de===G.value):G.checked=F===G.value||!!F)}):L.refs.forEach(G=>G.checked=G.value===F):Ru(L.ref)?L.ref.value="":(L.ref.value=F,L.ref.type||h.state.next({name:v,values:Me(i)})))}(C.shouldDirty||C.shouldTouch)&&_(v,F,C.shouldTouch,C.shouldDirty,!0),C.shouldValidate&&U(v)},je=(v,k,C)=>{for(const $ in k){if(!k.hasOwnProperty($))return;const F=k[$],L=v+"."+$,G=B(n,L);(o.array.has(v)||Re(F)||G&&!G._f)&&!sn(F)?je(L,F,C):ae(L,F,C)}},xe=(v,k,C={})=>{const $=B(n,v),F=o.array.has(v),L=Me(k);he(i,v,L),F?(h.array.next({name:v,values:Me(i)}),(d.isDirty||d.dirtyFields||f.isDirty||f.dirtyFields)&&C.shouldDirty&&h.state.next({name:v,dirtyFields:bs(s,i),isDirty:ue(v,L)})):$&&!$._f&&!Ye(L)?je(v,L,C):ae(v,L,C),Af(v,o)&&h.state.next({...r,name:v}),h.state.next({name:l.mount?v:void 0,values:Me(i)})},V=async v=>{l.mount=!0;const k=v.target;let C=k.name,$=!0;const F=B(n,C),L=pe=>{$=Number.isNaN(pe)||sn(pe)&&isNaN(pe.getTime())||Sr(pe,B(i,C,pe))},G=Rf(t.mode),de=Rf(t.reValidateMode);if(F){let pe,vt;const ki=k.type?Tf(F._f):qb(v),gr=v.type===Sf.BLUR||v.type===Sf.FOCUS_OUT,Og=!l2(F._f)&&!t.resolver&&!B(r.errors,C)&&!F._f.deps||u2(gr,B(r.touchedFields,C),r.isSubmitted,de,G),kl=Af(C,o,gr);he(i,C,ki),gr?(!k||!k.readOnly)&&(F._f.onBlur&&F._f.onBlur(v),c&&c(0)):F._f.onChange&&F._f.onChange(v);const Sl=_(C,ki,gr),Ag=!nt(Sl)||kl;if(!gr&&h.state.next({name:C,type:v.type,values:Me(i)}),Og)return(d.isValid||f.isValid)&&(t.mode==="onBlur"?gr&&y():gr||y()),Ag&&h.state.next({name:C,...kl?{}:Sl});if(!gr&&kl&&h.state.next({...r}),t.resolver){const{errors:$u}=await E([C]);if(L(ki),$){const Lg=Lf(r.errors,n,C),zu=Lf($u,n,Lg.name||C);pe=zu.error,C=zu.name,vt=nt($u)}}else b([C],!0),pe=(await Df(F,o.disabled,i,w,t.shouldUseNativeValidation))[C],b([C]),L(ki),$&&(pe?vt=!1:(d.isValid||f.isValid)&&(vt=await q(n,!0)));$&&(F._f.deps&&U(F._f.deps),R(C,vt,pe,Sl))}},T=(v,k)=>{if(B(r.errors,k)&&v.focus)return v.focus(),1},U=async(v,k={})=>{let C,$;const F=Ms(v);if(t.resolver){const L=await A(Ae(v)?v:F);C=nt(L),$=v?!F.some(G=>B(L,G)):C}else v?($=(await Promise.all(F.map(async L=>{const G=B(n,L);return await q(G&&G._f?{[L]:G}:G)}))).every(Boolean),!(!$&&!r.isValid)&&y()):$=C=await q(n);return h.state.next({...!Ht(v)||(d.isValid||f.isValid)&&C!==r.isValid?{}:{name:v},...t.resolver||!v?{isValid:C}:{},errors:r.errors}),k.shouldFocus&&!$&&$s(n,T,v?F:o.mount),$},D=v=>{const k={...l.mount?i:s};return Ae(v)?k:Ht(v)?B(k,v):v.map(C=>B(k,C))},K=(v,k)=>({invalid:!!B((k||r).errors,v),isDirty:!!B((k||r).dirtyFields,v),error:B((k||r).errors,v),isValidating:!!B(r.validatingFields,v),isTouched:!!B((k||r).touchedFields,v)}),me=v=>{v&&Ms(v).forEach(k=>Oe(r.errors,k)),h.state.next({errors:v?r.errors:{}})},yt=(v,k,C)=>{const $=(B(n,v,{_f:{}})._f||{}).ref,F=B(r.errors,v)||{},{ref:L,message:G,type:de,...pe}=F;he(r.errors,v,{...pe,...k,ref:$}),h.state.next({name:v,errors:r.errors,isValid:!1}),C&&C.shouldFocus&&$&&$.focus&&$.focus()},Mt=(v,k)=>Ot(v)?h.state.subscribe({next:C=>"values"in C&&v(ee(void 0,k),C)}):ee(v,k,!0),Jt=v=>h.state.subscribe({next:k=>{c2(v.name,k.name,v.exact)&&o2(k,v.formState||d,Rg,v.reRenderRoot)&&v.callback({values:{...i},...r,...k,defaultValues:s})}}).unsubscribe,Kt=v=>(l.mount=!0,f={...f,...v.formState},Jt({...v,formState:f})),pr=(v,k={})=>{for(const C of v?Ms(v):o.mount)o.mount.delete(C),o.array.delete(C),k.keepValue||(Oe(n,C),Oe(i,C)),!k.keepError&&Oe(r.errors,C),!k.keepDirty&&Oe(r.dirtyFields,C),!k.keepTouched&&Oe(r.touchedFields,C),!k.keepIsValidating&&Oe(r.validatingFields,C),!t.shouldUnregister&&!k.keepDefaultValue&&Oe(s,C);h.state.next({values:Me(i)}),h.state.next({...r,...k.keepDirty?{isDirty:ue()}:{}}),!k.keepIsValid&&y()},Lu=({disabled:v,name:k})=>{(Vt(v)&&l.mount||v||o.disabled.has(k))&&(v?o.disabled.add(k):o.disabled.delete(k))},jl=(v,k={})=>{let C=B(n,v);const $=Vt(k.disabled)||Vt(t.disabled);return he(n,v,{...C||{},_f:{...C&&C._f?C._f:{ref:{name:v}},name:v,mount:!0,...k}}),o.mount.add(v),C?Lu({disabled:Vt(k.disabled)?k.disabled:t.disabled,name:v}):N(v,!0,k.value),{...$?{disabled:k.disabled||t.disabled}:{},...t.progressive?{required:!!k.required,min:js(k.min),max:js(k.max),minLength:js(k.minLength),maxLength:js(k.maxLength),pattern:js(k.pattern)}:{},name:v,onChange:V,onBlur:V,ref:F=>{if(F){jl(v,k),C=B(n,v);const L=Ae(F.value)&&F.querySelectorAll&&F.querySelectorAll("input,select,textarea")[0]||F,G=r2(L),de=C._f.refs||[];if(G?de.find(pe=>pe===L):L===C._f.ref)return;he(n,v,{_f:{...C._f,...G?{refs:[...de.filter(so),L,...Array.isArray(B(s,v))?[{}]:[]],ref:{type:L.type,name:v}}:{ref:L}}}),N(v,!1,void 0,L)}else C=B(n,v,{}),C._f&&(C._f.mount=!1),(t.shouldUnregister||k.shouldUnregister)&&!(Jb(o.array,v)&&l.action)&&o.unMount.add(v)}}},Nl=()=>t.shouldFocusError&&$s(n,T,o.mount),Eg=v=>{Vt(v)&&(h.state.next({disabled:v}),$s(n,(k,C)=>{const $=B(n,C);$&&(k.disabled=$._f.disabled||v,Array.isArray($._f.refs)&&$._f.refs.forEach(F=>{F.disabled=$._f.disabled||v}))},0,!1))},Fu=(v,k)=>async C=>{let $;C&&(C.preventDefault&&C.preventDefault(),C.persist&&C.persist());let F=Me(i);if(h.state.next({isSubmitting:!0}),t.resolver){const{errors:L,values:G}=await E();r.errors=L,F=Me(G)}else await q(n);if(o.disabled.size)for(const L of o.disabled)Oe(F,L);if(Oe(r.errors,"root"),nt(r.errors)){h.state.next({errors:{}});try{await v(F,C)}catch(L){$=L}}else k&&await k({...r.errors},C),Nl(),setTimeout(Nl);if(h.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:nt(r.errors)&&!$,submitCount:r.submitCount+1,errors:r.errors}),$)throw $},Pg=(v,k={})=>{B(n,v)&&(Ae(k.defaultValue)?xe(v,Me(B(s,v))):(xe(v,k.defaultValue),he(s,v,Me(k.defaultValue))),k.keepTouched||Oe(r.touchedFields,v),k.keepDirty||(Oe(r.dirtyFields,v),r.isDirty=k.defaultValue?ue(v,Me(B(s,v))):ue()),k.keepError||(Oe(r.errors,v),d.isValid&&y()),h.state.next({...r}))},Du=(v,k={})=>{const C=v?Me(v):s,$=Me(C),F=nt(v),L=F?s:$;if(k.keepDefaultValues||(s=C),!k.keepValues){if(k.keepDirtyValues){const G=new Set([...o.mount,...Object.keys(bs(s,i))]);for(const de of Array.from(G))B(r.dirtyFields,de)?he(L,de,B(i,de)):xe(de,B(L,de))}else{if(Eu&&Ae(v))for(const G of o.mount){const de=B(n,G);if(de&&de._f){const pe=Array.isArray(de._f.refs)?de._f.refs[0]:de._f.ref;if(Ua(pe)){const vt=pe.closest("form");if(vt){vt.reset();break}}}}if(k.keepFieldsRef)for(const G of o.mount)xe(G,B(L,G));else n={}}i=t.shouldUnregister?k.keepDefaultValues?Me(s):{}:Me(L),h.array.next({values:{...L}}),h.state.next({values:{...L}})}o={mount:k.keepDirtyValues?o.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},l.mount=!d.isValid||!!k.keepIsValid||!!k.keepDirtyValues,l.watch=!!t.shouldUnregister,h.state.next({submitCount:k.keepSubmitCount?r.submitCount:0,isDirty:F?!1:k.keepDirty?r.isDirty:!!(k.keepDefaultValues&&!Sr(v,s)),isSubmitted:k.keepIsSubmitted?r.isSubmitted:!1,dirtyFields:F?{}:k.keepDirtyValues?k.keepDefaultValues&&i?bs(s,i):r.dirtyFields:k.keepDefaultValues&&v?bs(s,v):k.keepDirty?r.dirtyFields:{},touchedFields:k.keepTouched?r.touchedFields:{},errors:k.keepErrors?r.errors:{},isSubmitSuccessful:k.keepIsSubmitSuccessful?r.isSubmitSuccessful:!1,isSubmitting:!1,defaultValues:s})},Iu=(v,k)=>Du(Ot(v)?v(i):v,k),Tg=(v,k={})=>{const C=B(n,v),$=C&&C._f;if($){const F=$.refs?$.refs[0]:$.ref;F.focus&&(F.focus(),k.shouldSelect&&Ot(F.select)&&F.select())}},Rg=v=>{r={...r,...v}},Mu={control:{register:jl,unregister:pr,getFieldState:K,handleSubmit:Fu,setError:yt,_subscribe:Jt,_runSchema:E,_focusError:Nl,_getWatch:ee,_getDirty:ue,_setValid:y,_setFieldArray:x,_setDisabledField:Lu,_setErrors:g,_getFieldArray:re,_reset:Du,_resetDefaultValues:()=>Ot(t.defaultValues)&&t.defaultValues().then(v=>{Iu(v,t.resetOptions),h.state.next({isLoading:!1})}),_removeUnmounted:Z,_disableForm:Eg,_subjects:h,_proxyFormState:d,get _fields(){return n},get _formValues(){return i},get _state(){return l},set _state(v){l=v},get _defaultValues(){return s},get _names(){return o},set _names(v){o=v},get _formState(){return r},get _options(){return t},set _options(v){t={...t,...v}}},subscribe:Kt,trigger:U,register:jl,handleSubmit:Fu,watch:Mt,setValue:xe,getValues:D,reset:Iu,resetField:Pg,clearErrors:me,unregister:pr,setError:yt,setFocus:Tg,getFieldState:K};return{...Mu,formControl:Mu}}function bi(e={}){const t=Ne.useRef(void 0),r=Ne.useRef(void 0),[n,s]=Ne.useState({isDirty:!1,isValidating:!1,isLoading:Ot(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:Ot(e.defaultValues)?void 0:e.defaultValues});if(!t.current)if(e.formControl)t.current={...e.formControl,formState:n},e.defaultValues&&!Ot(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{const{formControl:l,...o}=h2(e);t.current={...o,formState:n}}const i=t.current.control;return i._options=e,e2(()=>{const l=i._subscribe({formState:i._proxyFormState,callback:()=>s({...i._formState}),reRenderRoot:!0});return s(o=>({...o,isReady:!0})),i._formState.isReady=!0,l},[i]),Ne.useEffect(()=>i._disableForm(e.disabled),[i,e.disabled]),Ne.useEffect(()=>{e.mode&&(i._options.mode=e.mode),e.reValidateMode&&(i._options.reValidateMode=e.reValidateMode)},[i,e.mode,e.reValidateMode]),Ne.useEffect(()=>{e.errors&&(i._setErrors(e.errors),i._focusError())},[i,e.errors]),Ne.useEffect(()=>{e.shouldUnregister&&i._subjects.state.next({values:i._getWatch()})},[i,e.shouldUnregister]),Ne.useEffect(()=>{if(i._proxyFormState.isDirty){const l=i._getDirty();l!==n.isDirty&&i._subjects.state.next({isDirty:l})}},[i,n.isDirty]),Ne.useEffect(()=>{e.values&&!Sr(e.values,r.current)?(i._reset(e.values,{keepFieldsRef:!0,...i._options.resetOptions}),r.current=e.values,s(l=>({...l}))):i._resetDefaultValues()},[i,e.values]),Ne.useEffect(()=>{i._state.mount||(i._setValid(),i._state.mount=!0),i._state.watch&&(i._state.watch=!1,i._subjects.state.next({...i._formState})),i._removeUnmounted()}),t.current.formState=Xb(n,i),t.current}const If=(e,t,r)=>{if(e&&"reportValidity"in e){const n=B(r,t);e.setCustomValidity(n&&n.message||""),e.reportValidity()}},kg=(e,t)=>{for(const r in t.fields){const n=t.fields[r];n&&n.ref&&"reportValidity"in n.ref?If(n.ref,r,e):n.refs&&n.refs.forEach(s=>If(s,r,e))}},p2=(e,t)=>{t.shouldUseNativeValidation&&kg(e,t);const r={};for(const n in e){const s=B(t.fields,n),i=Object.assign(e[n]||{},{ref:s&&s.ref});if(g2(t.names||Object.keys(e),n)){const l=Object.assign({},B(r,n));he(l,"root",i),he(r,n,l)}else he(r,n,i)}return r},g2=(e,t)=>e.some(r=>r.startsWith(t+"."));var x2=function(e,t){for(var r={};e.length;){var n=e[0],s=n.code,i=n.message,l=n.path.join(".");if(!r[l])if("unionErrors"in n){var o=n.unionErrors[0].errors[0];r[l]={message:o.message,type:o.code}}else r[l]={message:i,type:s};if("unionErrors"in n&&n.unionErrors.forEach(function(d){return d.errors.forEach(function(f){return e.push(f)})}),t){var c=r[l].types,u=c&&c[n.code];r[l]=xg(l,t,r,s,u?[].concat(u,n.message):n.message)}e.shift()}return r},ji=function(e,t,r){return r===void 0&&(r={}),function(n,s,i){try{return Promise.resolve(function(l,o){try{var c=Promise.resolve(e[r.mode==="sync"?"parse":"parseAsync"](n,t)).then(function(u){return i.shouldUseNativeValidation&&kg({},i),{errors:{},values:r.raw?n:u}})}catch(u){return o(u)}return c&&c.then?c.then(void 0,o):c}(0,function(l){if(function(o){return Array.isArray(o==null?void 0:o.errors)}(l))return{values:{},errors:p2(x2(l.errors,!i.shouldUseNativeValidation&&i.criteriaMode==="all"),i)};throw l}))}catch(l){return Promise.reject(l)}}},ce;(function(e){e.assertEqual=s=>{};function t(s){}e.assertIs=t;function r(s){throw new Error}e.assertNever=r,e.arrayToEnum=s=>{const i={};for(const l of s)i[l]=l;return i},e.getValidEnumValues=s=>{const i=e.objectKeys(s).filter(o=>typeof s[s[o]]!="number"),l={};for(const o of i)l[o]=s[o];return e.objectValues(l)},e.objectValues=s=>e.objectKeys(s).map(function(i){return s[i]}),e.objectKeys=typeof Object.keys=="function"?s=>Object.keys(s):s=>{const i=[];for(const l in s)Object.prototype.hasOwnProperty.call(s,l)&&i.push(l);return i},e.find=(s,i)=>{for(const l of s)if(i(l))return l},e.isInteger=typeof Number.isInteger=="function"?s=>Number.isInteger(s):s=>typeof s=="number"&&Number.isFinite(s)&&Math.floor(s)===s;function n(s,i=" | "){return s.map(l=>typeof l=="string"?`'${l}'`:l).join(i)}e.joinValues=n,e.jsonStringifyReplacer=(s,i)=>typeof i=="bigint"?i.toString():i})(ce||(ce={}));var Mf;(function(e){e.mergeShapes=(t,r)=>({...t,...r})})(Mf||(Mf={}));const W=ce.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),br=e=>{switch(typeof e){case"undefined":return W.undefined;case"string":return W.string;case"number":return Number.isNaN(e)?W.nan:W.number;case"boolean":return W.boolean;case"function":return W.function;case"bigint":return W.bigint;case"symbol":return W.symbol;case"object":return Array.isArray(e)?W.array:e===null?W.null:e.then&&typeof e.then=="function"&&e.catch&&typeof e.catch=="function"?W.promise:typeof Map<"u"&&e instanceof Map?W.map:typeof Set<"u"&&e instanceof Set?W.set:typeof Date<"u"&&e instanceof Date?W.date:W.object;default:return W.unknown}},O=ce.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class fr extends Error{get errors(){return this.issues}constructor(t){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const r=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,r):this.__proto__=r,this.name="ZodError",this.issues=t}format(t){const r=t||function(i){return i.message},n={_errors:[]},s=i=>{for(const l of i.issues)if(l.code==="invalid_union")l.unionErrors.map(s);else if(l.code==="invalid_return_type")s(l.returnTypeError);else if(l.code==="invalid_arguments")s(l.argumentsError);else if(l.path.length===0)n._errors.push(r(l));else{let o=n,c=0;for(;c<l.path.length;){const u=l.path[c];c===l.path.length-1?(o[u]=o[u]||{_errors:[]},o[u]._errors.push(r(l))):o[u]=o[u]||{_errors:[]},o=o[u],c++}}};return s(this),n}static assert(t){if(!(t instanceof fr))throw new Error(`Not a ZodError: ${t}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,ce.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(t=r=>r.message){const r={},n=[];for(const s of this.issues)if(s.path.length>0){const i=s.path[0];r[i]=r[i]||[],r[i].push(t(s))}else n.push(t(s));return{formErrors:n,fieldErrors:r}}get formErrors(){return this.flatten()}}fr.create=e=>new fr(e);const y2=(e,t)=>{let r;switch(e.code){case O.invalid_type:e.received===W.undefined?r="Required":r=`Expected ${e.expected}, received ${e.received}`;break;case O.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(e.expected,ce.jsonStringifyReplacer)}`;break;case O.unrecognized_keys:r=`Unrecognized key(s) in object: ${ce.joinValues(e.keys,", ")}`;break;case O.invalid_union:r="Invalid input";break;case O.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${ce.joinValues(e.options)}`;break;case O.invalid_enum_value:r=`Invalid enum value. Expected ${ce.joinValues(e.options)}, received '${e.received}'`;break;case O.invalid_arguments:r="Invalid function arguments";break;case O.invalid_return_type:r="Invalid function return type";break;case O.invalid_date:r="Invalid date";break;case O.invalid_string:typeof e.validation=="object"?"includes"in e.validation?(r=`Invalid input: must include "${e.validation.includes}"`,typeof e.validation.position=="number"&&(r=`${r} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?r=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?r=`Invalid input: must end with "${e.validation.endsWith}"`:ce.assertNever(e.validation):e.validation!=="regex"?r=`Invalid ${e.validation}`:r="Invalid";break;case O.too_small:e.type==="array"?r=`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:e.type==="string"?r=`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:e.type==="number"?r=`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:e.type==="bigint"?r=`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:e.type==="date"?r=`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:r="Invalid input";break;case O.too_big:e.type==="array"?r=`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:e.type==="string"?r=`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:e.type==="number"?r=`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:e.type==="bigint"?r=`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:e.type==="date"?r=`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:r="Invalid input";break;case O.custom:r="Invalid input";break;case O.invalid_intersection_types:r="Intersection results could not be merged";break;case O.not_multiple_of:r=`Number must be a multiple of ${e.multipleOf}`;break;case O.not_finite:r="Number must be finite";break;default:r=t.defaultError,ce.assertNever(e)}return{message:r}},mc=y2;let v2=mc;function w2(){return v2}const b2=e=>{const{data:t,path:r,errorMaps:n,issueData:s}=e,i=[...r,...s.path||[]],l={...s,path:i};if(s.message!==void 0)return{...s,path:i,message:s.message};let o="";const c=n.filter(u=>!!u).slice().reverse();for(const u of c)o=u(l,{data:t,defaultError:o}).message;return{...s,path:i,message:o}};function I(e,t){const r=w2(),n=b2({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,r,r===mc?void 0:mc].filter(s=>!!s)});e.common.issues.push(n)}class ht{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(t,r){const n=[];for(const s of r){if(s.status==="aborted")return Y;s.status==="dirty"&&t.dirty(),n.push(s.value)}return{status:t.value,value:n}}static async mergeObjectAsync(t,r){const n=[];for(const s of r){const i=await s.key,l=await s.value;n.push({key:i,value:l})}return ht.mergeObjectSync(t,n)}static mergeObjectSync(t,r){const n={};for(const s of r){const{key:i,value:l}=s;if(i.status==="aborted"||l.status==="aborted")return Y;i.status==="dirty"&&t.dirty(),l.status==="dirty"&&t.dirty(),i.value!=="__proto__"&&(typeof l.value<"u"||s.alwaysSet)&&(n[i.value]=l.value)}return{status:t.value,value:n}}}const Y=Object.freeze({status:"aborted"}),Cs=e=>({status:"dirty",value:e}),_t=e=>({status:"valid",value:e}),$f=e=>e.status==="aborted",zf=e=>e.status==="dirty",Yn=e=>e.status==="valid",Wa=e=>typeof Promise<"u"&&e instanceof Promise;var H;(function(e){e.errToObj=t=>typeof t=="string"?{message:t}:t||{},e.toString=t=>typeof t=="string"?t:t==null?void 0:t.message})(H||(H={}));class Wr{constructor(t,r,n,s){this._cachedPath=[],this.parent=t,this.data=r,this._path=n,this._key=s}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Uf=(e,t)=>{if(Yn(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const r=new fr(e.common.issues);return this._error=r,this._error}}};function ne(e){if(!e)return{};const{errorMap:t,invalid_type_error:r,required_error:n,description:s}=e;if(t&&(r||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return t?{errorMap:t,description:s}:{errorMap:(l,o)=>{const{message:c}=e;return l.code==="invalid_enum_value"?{message:c??o.defaultError}:typeof o.data>"u"?{message:c??n??o.defaultError}:l.code!=="invalid_type"?{message:o.defaultError}:{message:c??r??o.defaultError}},description:s}}class oe{get description(){return this._def.description}_getType(t){return br(t.data)}_getOrReturnCtx(t,r){return r||{common:t.parent.common,data:t.data,parsedType:br(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}_processInputParams(t){return{status:new ht,ctx:{common:t.parent.common,data:t.data,parsedType:br(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}}_parseSync(t){const r=this._parse(t);if(Wa(r))throw new Error("Synchronous parse encountered promise.");return r}_parseAsync(t){const r=this._parse(t);return Promise.resolve(r)}parse(t,r){const n=this.safeParse(t,r);if(n.success)return n.data;throw n.error}safeParse(t,r){const n={common:{issues:[],async:(r==null?void 0:r.async)??!1,contextualErrorMap:r==null?void 0:r.errorMap},path:(r==null?void 0:r.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:br(t)},s=this._parseSync({data:t,path:n.path,parent:n});return Uf(n,s)}"~validate"(t){var n,s;const r={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:br(t)};if(!this["~standard"].async)try{const i=this._parseSync({data:t,path:[],parent:r});return Yn(i)?{value:i.value}:{issues:r.common.issues}}catch(i){(s=(n=i==null?void 0:i.message)==null?void 0:n.toLowerCase())!=null&&s.includes("encountered")&&(this["~standard"].async=!0),r.common={issues:[],async:!0}}return this._parseAsync({data:t,path:[],parent:r}).then(i=>Yn(i)?{value:i.value}:{issues:r.common.issues})}async parseAsync(t,r){const n=await this.safeParseAsync(t,r);if(n.success)return n.data;throw n.error}async safeParseAsync(t,r){const n={common:{issues:[],contextualErrorMap:r==null?void 0:r.errorMap,async:!0},path:(r==null?void 0:r.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:br(t)},s=this._parse({data:t,path:n.path,parent:n}),i=await(Wa(s)?s:Promise.resolve(s));return Uf(n,i)}refine(t,r){const n=s=>typeof r=="string"||typeof r>"u"?{message:r}:typeof r=="function"?r(s):r;return this._refinement((s,i)=>{const l=t(s),o=()=>i.addIssue({code:O.custom,...n(s)});return typeof Promise<"u"&&l instanceof Promise?l.then(c=>c?!0:(o(),!1)):l?!0:(o(),!1)})}refinement(t,r){return this._refinement((n,s)=>t(n)?!0:(s.addIssue(typeof r=="function"?r(n,s):r),!1))}_refinement(t){return new ts({schema:this,typeName:X.ZodEffects,effect:{type:"refinement",refinement:t}})}superRefine(t){return this._refinement(t)}constructor(t){this.spa=this.safeParseAsync,this._def=t,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:r=>this["~validate"](r)}}optional(){return Ur.create(this,this._def)}nullable(){return rs.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return qt.create(this)}promise(){return qa.create(this,this._def)}or(t){return Za.create([this,t],this._def)}and(t){return Ga.create(this,t,this._def)}transform(t){return new ts({...ne(this._def),schema:this,typeName:X.ZodEffects,effect:{type:"transform",transform:t}})}default(t){const r=typeof t=="function"?t:()=>t;return new gc({...ne(this._def),innerType:this,defaultValue:r,typeName:X.ZodDefault})}brand(){return new W2({typeName:X.ZodBranded,type:this,...ne(this._def)})}catch(t){const r=typeof t=="function"?t:()=>t;return new xc({...ne(this._def),innerType:this,catchValue:r,typeName:X.ZodCatch})}describe(t){const r=this.constructor;return new r({...this._def,description:t})}pipe(t){return Au.create(this,t)}readonly(){return yc.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const j2=/^c[^\s-]{8,}$/i,N2=/^[0-9a-z]+$/,k2=/^[0-9A-HJKMNP-TV-Z]{26}$/i,S2=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,_2=/^[a-z0-9_-]{21}$/i,C2=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,E2=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,P2=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,T2="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let io;const R2=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,O2=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,A2=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,L2=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,F2=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,D2=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Sg="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",I2=new RegExp(`^${Sg}$`);function _g(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:e.precision==null&&(t=`${t}(\\.\\d+)?`);const r=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${r}`}function M2(e){return new RegExp(`^${_g(e)}$`)}function $2(e){let t=`${Sg}T${_g(e)}`;const r=[];return r.push(e.local?"Z?":"Z"),e.offset&&r.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${r.join("|")})`,new RegExp(`^${t}$`)}function z2(e,t){return!!((t==="v4"||!t)&&R2.test(e)||(t==="v6"||!t)&&A2.test(e))}function U2(e,t){if(!C2.test(e))return!1;try{const[r]=e.split(".");if(!r)return!1;const n=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),s=JSON.parse(atob(n));return!(typeof s!="object"||s===null||"typ"in s&&(s==null?void 0:s.typ)!=="JWT"||!s.alg||t&&s.alg!==t)}catch{return!1}}function B2(e,t){return!!((t==="v4"||!t)&&O2.test(e)||(t==="v6"||!t)&&L2.test(e))}class Pr extends oe{_parse(t){if(this._def.coerce&&(t.data=String(t.data)),this._getType(t)!==W.string){const i=this._getOrReturnCtx(t);return I(i,{code:O.invalid_type,expected:W.string,received:i.parsedType}),Y}const n=new ht;let s;for(const i of this._def.checks)if(i.kind==="min")t.data.length<i.value&&(s=this._getOrReturnCtx(t,s),I(s,{code:O.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),n.dirty());else if(i.kind==="max")t.data.length>i.value&&(s=this._getOrReturnCtx(t,s),I(s,{code:O.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),n.dirty());else if(i.kind==="length"){const l=t.data.length>i.value,o=t.data.length<i.value;(l||o)&&(s=this._getOrReturnCtx(t,s),l?I(s,{code:O.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):o&&I(s,{code:O.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),n.dirty())}else if(i.kind==="email")P2.test(t.data)||(s=this._getOrReturnCtx(t,s),I(s,{validation:"email",code:O.invalid_string,message:i.message}),n.dirty());else if(i.kind==="emoji")io||(io=new RegExp(T2,"u")),io.test(t.data)||(s=this._getOrReturnCtx(t,s),I(s,{validation:"emoji",code:O.invalid_string,message:i.message}),n.dirty());else if(i.kind==="uuid")S2.test(t.data)||(s=this._getOrReturnCtx(t,s),I(s,{validation:"uuid",code:O.invalid_string,message:i.message}),n.dirty());else if(i.kind==="nanoid")_2.test(t.data)||(s=this._getOrReturnCtx(t,s),I(s,{validation:"nanoid",code:O.invalid_string,message:i.message}),n.dirty());else if(i.kind==="cuid")j2.test(t.data)||(s=this._getOrReturnCtx(t,s),I(s,{validation:"cuid",code:O.invalid_string,message:i.message}),n.dirty());else if(i.kind==="cuid2")N2.test(t.data)||(s=this._getOrReturnCtx(t,s),I(s,{validation:"cuid2",code:O.invalid_string,message:i.message}),n.dirty());else if(i.kind==="ulid")k2.test(t.data)||(s=this._getOrReturnCtx(t,s),I(s,{validation:"ulid",code:O.invalid_string,message:i.message}),n.dirty());else if(i.kind==="url")try{new URL(t.data)}catch{s=this._getOrReturnCtx(t,s),I(s,{validation:"url",code:O.invalid_string,message:i.message}),n.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(t.data)||(s=this._getOrReturnCtx(t,s),I(s,{validation:"regex",code:O.invalid_string,message:i.message}),n.dirty())):i.kind==="trim"?t.data=t.data.trim():i.kind==="includes"?t.data.includes(i.value,i.position)||(s=this._getOrReturnCtx(t,s),I(s,{code:O.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),n.dirty()):i.kind==="toLowerCase"?t.data=t.data.toLowerCase():i.kind==="toUpperCase"?t.data=t.data.toUpperCase():i.kind==="startsWith"?t.data.startsWith(i.value)||(s=this._getOrReturnCtx(t,s),I(s,{code:O.invalid_string,validation:{startsWith:i.value},message:i.message}),n.dirty()):i.kind==="endsWith"?t.data.endsWith(i.value)||(s=this._getOrReturnCtx(t,s),I(s,{code:O.invalid_string,validation:{endsWith:i.value},message:i.message}),n.dirty()):i.kind==="datetime"?$2(i).test(t.data)||(s=this._getOrReturnCtx(t,s),I(s,{code:O.invalid_string,validation:"datetime",message:i.message}),n.dirty()):i.kind==="date"?I2.test(t.data)||(s=this._getOrReturnCtx(t,s),I(s,{code:O.invalid_string,validation:"date",message:i.message}),n.dirty()):i.kind==="time"?M2(i).test(t.data)||(s=this._getOrReturnCtx(t,s),I(s,{code:O.invalid_string,validation:"time",message:i.message}),n.dirty()):i.kind==="duration"?E2.test(t.data)||(s=this._getOrReturnCtx(t,s),I(s,{validation:"duration",code:O.invalid_string,message:i.message}),n.dirty()):i.kind==="ip"?z2(t.data,i.version)||(s=this._getOrReturnCtx(t,s),I(s,{validation:"ip",code:O.invalid_string,message:i.message}),n.dirty()):i.kind==="jwt"?U2(t.data,i.alg)||(s=this._getOrReturnCtx(t,s),I(s,{validation:"jwt",code:O.invalid_string,message:i.message}),n.dirty()):i.kind==="cidr"?B2(t.data,i.version)||(s=this._getOrReturnCtx(t,s),I(s,{validation:"cidr",code:O.invalid_string,message:i.message}),n.dirty()):i.kind==="base64"?F2.test(t.data)||(s=this._getOrReturnCtx(t,s),I(s,{validation:"base64",code:O.invalid_string,message:i.message}),n.dirty()):i.kind==="base64url"?D2.test(t.data)||(s=this._getOrReturnCtx(t,s),I(s,{validation:"base64url",code:O.invalid_string,message:i.message}),n.dirty()):ce.assertNever(i);return{status:n.value,value:t.data}}_regex(t,r,n){return this.refinement(s=>t.test(s),{validation:r,code:O.invalid_string,...H.errToObj(n)})}_addCheck(t){return new Pr({...this._def,checks:[...this._def.checks,t]})}email(t){return this._addCheck({kind:"email",...H.errToObj(t)})}url(t){return this._addCheck({kind:"url",...H.errToObj(t)})}emoji(t){return this._addCheck({kind:"emoji",...H.errToObj(t)})}uuid(t){return this._addCheck({kind:"uuid",...H.errToObj(t)})}nanoid(t){return this._addCheck({kind:"nanoid",...H.errToObj(t)})}cuid(t){return this._addCheck({kind:"cuid",...H.errToObj(t)})}cuid2(t){return this._addCheck({kind:"cuid2",...H.errToObj(t)})}ulid(t){return this._addCheck({kind:"ulid",...H.errToObj(t)})}base64(t){return this._addCheck({kind:"base64",...H.errToObj(t)})}base64url(t){return this._addCheck({kind:"base64url",...H.errToObj(t)})}jwt(t){return this._addCheck({kind:"jwt",...H.errToObj(t)})}ip(t){return this._addCheck({kind:"ip",...H.errToObj(t)})}cidr(t){return this._addCheck({kind:"cidr",...H.errToObj(t)})}datetime(t){return typeof t=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:t}):this._addCheck({kind:"datetime",precision:typeof(t==null?void 0:t.precision)>"u"?null:t==null?void 0:t.precision,offset:(t==null?void 0:t.offset)??!1,local:(t==null?void 0:t.local)??!1,...H.errToObj(t==null?void 0:t.message)})}date(t){return this._addCheck({kind:"date",message:t})}time(t){return typeof t=="string"?this._addCheck({kind:"time",precision:null,message:t}):this._addCheck({kind:"time",precision:typeof(t==null?void 0:t.precision)>"u"?null:t==null?void 0:t.precision,...H.errToObj(t==null?void 0:t.message)})}duration(t){return this._addCheck({kind:"duration",...H.errToObj(t)})}regex(t,r){return this._addCheck({kind:"regex",regex:t,...H.errToObj(r)})}includes(t,r){return this._addCheck({kind:"includes",value:t,position:r==null?void 0:r.position,...H.errToObj(r==null?void 0:r.message)})}startsWith(t,r){return this._addCheck({kind:"startsWith",value:t,...H.errToObj(r)})}endsWith(t,r){return this._addCheck({kind:"endsWith",value:t,...H.errToObj(r)})}min(t,r){return this._addCheck({kind:"min",value:t,...H.errToObj(r)})}max(t,r){return this._addCheck({kind:"max",value:t,...H.errToObj(r)})}length(t,r){return this._addCheck({kind:"length",value:t,...H.errToObj(r)})}nonempty(t){return this.min(1,H.errToObj(t))}trim(){return new Pr({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new Pr({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new Pr({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(t=>t.kind==="datetime")}get isDate(){return!!this._def.checks.find(t=>t.kind==="date")}get isTime(){return!!this._def.checks.find(t=>t.kind==="time")}get isDuration(){return!!this._def.checks.find(t=>t.kind==="duration")}get isEmail(){return!!this._def.checks.find(t=>t.kind==="email")}get isURL(){return!!this._def.checks.find(t=>t.kind==="url")}get isEmoji(){return!!this._def.checks.find(t=>t.kind==="emoji")}get isUUID(){return!!this._def.checks.find(t=>t.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(t=>t.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(t=>t.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(t=>t.kind==="cuid2")}get isULID(){return!!this._def.checks.find(t=>t.kind==="ulid")}get isIP(){return!!this._def.checks.find(t=>t.kind==="ip")}get isCIDR(){return!!this._def.checks.find(t=>t.kind==="cidr")}get isBase64(){return!!this._def.checks.find(t=>t.kind==="base64")}get isBase64url(){return!!this._def.checks.find(t=>t.kind==="base64url")}get minLength(){let t=null;for(const r of this._def.checks)r.kind==="min"&&(t===null||r.value>t)&&(t=r.value);return t}get maxLength(){let t=null;for(const r of this._def.checks)r.kind==="max"&&(t===null||r.value<t)&&(t=r.value);return t}}Pr.create=e=>new Pr({checks:[],typeName:X.ZodString,coerce:(e==null?void 0:e.coerce)??!1,...ne(e)});function V2(e,t){const r=(e.toString().split(".")[1]||"").length,n=(t.toString().split(".")[1]||"").length,s=r>n?r:n,i=Number.parseInt(e.toFixed(s).replace(".","")),l=Number.parseInt(t.toFixed(s).replace(".",""));return i%l/10**s}class Xn extends oe{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(t){if(this._def.coerce&&(t.data=Number(t.data)),this._getType(t)!==W.number){const i=this._getOrReturnCtx(t);return I(i,{code:O.invalid_type,expected:W.number,received:i.parsedType}),Y}let n;const s=new ht;for(const i of this._def.checks)i.kind==="int"?ce.isInteger(t.data)||(n=this._getOrReturnCtx(t,n),I(n,{code:O.invalid_type,expected:"integer",received:"float",message:i.message}),s.dirty()):i.kind==="min"?(i.inclusive?t.data<i.value:t.data<=i.value)&&(n=this._getOrReturnCtx(t,n),I(n,{code:O.too_small,minimum:i.value,type:"number",inclusive:i.inclusive,exact:!1,message:i.message}),s.dirty()):i.kind==="max"?(i.inclusive?t.data>i.value:t.data>=i.value)&&(n=this._getOrReturnCtx(t,n),I(n,{code:O.too_big,maximum:i.value,type:"number",inclusive:i.inclusive,exact:!1,message:i.message}),s.dirty()):i.kind==="multipleOf"?V2(t.data,i.value)!==0&&(n=this._getOrReturnCtx(t,n),I(n,{code:O.not_multiple_of,multipleOf:i.value,message:i.message}),s.dirty()):i.kind==="finite"?Number.isFinite(t.data)||(n=this._getOrReturnCtx(t,n),I(n,{code:O.not_finite,message:i.message}),s.dirty()):ce.assertNever(i);return{status:s.value,value:t.data}}gte(t,r){return this.setLimit("min",t,!0,H.toString(r))}gt(t,r){return this.setLimit("min",t,!1,H.toString(r))}lte(t,r){return this.setLimit("max",t,!0,H.toString(r))}lt(t,r){return this.setLimit("max",t,!1,H.toString(r))}setLimit(t,r,n,s){return new Xn({...this._def,checks:[...this._def.checks,{kind:t,value:r,inclusive:n,message:H.toString(s)}]})}_addCheck(t){return new Xn({...this._def,checks:[...this._def.checks,t]})}int(t){return this._addCheck({kind:"int",message:H.toString(t)})}positive(t){return this._addCheck({kind:"min",value:0,inclusive:!1,message:H.toString(t)})}negative(t){return this._addCheck({kind:"max",value:0,inclusive:!1,message:H.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:0,inclusive:!0,message:H.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:0,inclusive:!0,message:H.toString(t)})}multipleOf(t,r){return this._addCheck({kind:"multipleOf",value:t,message:H.toString(r)})}finite(t){return this._addCheck({kind:"finite",message:H.toString(t)})}safe(t){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:H.toString(t)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:H.toString(t)})}get minValue(){let t=null;for(const r of this._def.checks)r.kind==="min"&&(t===null||r.value>t)&&(t=r.value);return t}get maxValue(){let t=null;for(const r of this._def.checks)r.kind==="max"&&(t===null||r.value<t)&&(t=r.value);return t}get isInt(){return!!this._def.checks.find(t=>t.kind==="int"||t.kind==="multipleOf"&&ce.isInteger(t.value))}get isFinite(){let t=null,r=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(r===null||n.value>r)&&(r=n.value):n.kind==="max"&&(t===null||n.value<t)&&(t=n.value)}return Number.isFinite(r)&&Number.isFinite(t)}}Xn.create=e=>new Xn({checks:[],typeName:X.ZodNumber,coerce:(e==null?void 0:e.coerce)||!1,...ne(e)});class ci extends oe{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(t){if(this._def.coerce)try{t.data=BigInt(t.data)}catch{return this._getInvalidInput(t)}if(this._getType(t)!==W.bigint)return this._getInvalidInput(t);let n;const s=new ht;for(const i of this._def.checks)i.kind==="min"?(i.inclusive?t.data<i.value:t.data<=i.value)&&(n=this._getOrReturnCtx(t,n),I(n,{code:O.too_small,type:"bigint",minimum:i.value,inclusive:i.inclusive,message:i.message}),s.dirty()):i.kind==="max"?(i.inclusive?t.data>i.value:t.data>=i.value)&&(n=this._getOrReturnCtx(t,n),I(n,{code:O.too_big,type:"bigint",maximum:i.value,inclusive:i.inclusive,message:i.message}),s.dirty()):i.kind==="multipleOf"?t.data%i.value!==BigInt(0)&&(n=this._getOrReturnCtx(t,n),I(n,{code:O.not_multiple_of,multipleOf:i.value,message:i.message}),s.dirty()):ce.assertNever(i);return{status:s.value,value:t.data}}_getInvalidInput(t){const r=this._getOrReturnCtx(t);return I(r,{code:O.invalid_type,expected:W.bigint,received:r.parsedType}),Y}gte(t,r){return this.setLimit("min",t,!0,H.toString(r))}gt(t,r){return this.setLimit("min",t,!1,H.toString(r))}lte(t,r){return this.setLimit("max",t,!0,H.toString(r))}lt(t,r){return this.setLimit("max",t,!1,H.toString(r))}setLimit(t,r,n,s){return new ci({...this._def,checks:[...this._def.checks,{kind:t,value:r,inclusive:n,message:H.toString(s)}]})}_addCheck(t){return new ci({...this._def,checks:[...this._def.checks,t]})}positive(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:H.toString(t)})}negative(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:H.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:H.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:H.toString(t)})}multipleOf(t,r){return this._addCheck({kind:"multipleOf",value:t,message:H.toString(r)})}get minValue(){let t=null;for(const r of this._def.checks)r.kind==="min"&&(t===null||r.value>t)&&(t=r.value);return t}get maxValue(){let t=null;for(const r of this._def.checks)r.kind==="max"&&(t===null||r.value<t)&&(t=r.value);return t}}ci.create=e=>new ci({checks:[],typeName:X.ZodBigInt,coerce:(e==null?void 0:e.coerce)??!1,...ne(e)});class hc extends oe{_parse(t){if(this._def.coerce&&(t.data=!!t.data),this._getType(t)!==W.boolean){const n=this._getOrReturnCtx(t);return I(n,{code:O.invalid_type,expected:W.boolean,received:n.parsedType}),Y}return _t(t.data)}}hc.create=e=>new hc({typeName:X.ZodBoolean,coerce:(e==null?void 0:e.coerce)||!1,...ne(e)});class Ha extends oe{_parse(t){if(this._def.coerce&&(t.data=new Date(t.data)),this._getType(t)!==W.date){const i=this._getOrReturnCtx(t);return I(i,{code:O.invalid_type,expected:W.date,received:i.parsedType}),Y}if(Number.isNaN(t.data.getTime())){const i=this._getOrReturnCtx(t);return I(i,{code:O.invalid_date}),Y}const n=new ht;let s;for(const i of this._def.checks)i.kind==="min"?t.data.getTime()<i.value&&(s=this._getOrReturnCtx(t,s),I(s,{code:O.too_small,message:i.message,inclusive:!0,exact:!1,minimum:i.value,type:"date"}),n.dirty()):i.kind==="max"?t.data.getTime()>i.value&&(s=this._getOrReturnCtx(t,s),I(s,{code:O.too_big,message:i.message,inclusive:!0,exact:!1,maximum:i.value,type:"date"}),n.dirty()):ce.assertNever(i);return{status:n.value,value:new Date(t.data.getTime())}}_addCheck(t){return new Ha({...this._def,checks:[...this._def.checks,t]})}min(t,r){return this._addCheck({kind:"min",value:t.getTime(),message:H.toString(r)})}max(t,r){return this._addCheck({kind:"max",value:t.getTime(),message:H.toString(r)})}get minDate(){let t=null;for(const r of this._def.checks)r.kind==="min"&&(t===null||r.value>t)&&(t=r.value);return t!=null?new Date(t):null}get maxDate(){let t=null;for(const r of this._def.checks)r.kind==="max"&&(t===null||r.value<t)&&(t=r.value);return t!=null?new Date(t):null}}Ha.create=e=>new Ha({checks:[],coerce:(e==null?void 0:e.coerce)||!1,typeName:X.ZodDate,...ne(e)});class Bf extends oe{_parse(t){if(this._getType(t)!==W.symbol){const n=this._getOrReturnCtx(t);return I(n,{code:O.invalid_type,expected:W.symbol,received:n.parsedType}),Y}return _t(t.data)}}Bf.create=e=>new Bf({typeName:X.ZodSymbol,...ne(e)});class Vf extends oe{_parse(t){if(this._getType(t)!==W.undefined){const n=this._getOrReturnCtx(t);return I(n,{code:O.invalid_type,expected:W.undefined,received:n.parsedType}),Y}return _t(t.data)}}Vf.create=e=>new Vf({typeName:X.ZodUndefined,...ne(e)});class Wf extends oe{_parse(t){if(this._getType(t)!==W.null){const n=this._getOrReturnCtx(t);return I(n,{code:O.invalid_type,expected:W.null,received:n.parsedType}),Y}return _t(t.data)}}Wf.create=e=>new Wf({typeName:X.ZodNull,...ne(e)});class Hf extends oe{constructor(){super(...arguments),this._any=!0}_parse(t){return _t(t.data)}}Hf.create=e=>new Hf({typeName:X.ZodAny,...ne(e)});class Zf extends oe{constructor(){super(...arguments),this._unknown=!0}_parse(t){return _t(t.data)}}Zf.create=e=>new Zf({typeName:X.ZodUnknown,...ne(e)});class Hr extends oe{_parse(t){const r=this._getOrReturnCtx(t);return I(r,{code:O.invalid_type,expected:W.never,received:r.parsedType}),Y}}Hr.create=e=>new Hr({typeName:X.ZodNever,...ne(e)});class Gf extends oe{_parse(t){if(this._getType(t)!==W.undefined){const n=this._getOrReturnCtx(t);return I(n,{code:O.invalid_type,expected:W.void,received:n.parsedType}),Y}return _t(t.data)}}Gf.create=e=>new Gf({typeName:X.ZodVoid,...ne(e)});class qt extends oe{_parse(t){const{ctx:r,status:n}=this._processInputParams(t),s=this._def;if(r.parsedType!==W.array)return I(r,{code:O.invalid_type,expected:W.array,received:r.parsedType}),Y;if(s.exactLength!==null){const l=r.data.length>s.exactLength.value,o=r.data.length<s.exactLength.value;(l||o)&&(I(r,{code:l?O.too_big:O.too_small,minimum:o?s.exactLength.value:void 0,maximum:l?s.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:s.exactLength.message}),n.dirty())}if(s.minLength!==null&&r.data.length<s.minLength.value&&(I(r,{code:O.too_small,minimum:s.minLength.value,type:"array",inclusive:!0,exact:!1,message:s.minLength.message}),n.dirty()),s.maxLength!==null&&r.data.length>s.maxLength.value&&(I(r,{code:O.too_big,maximum:s.maxLength.value,type:"array",inclusive:!0,exact:!1,message:s.maxLength.message}),n.dirty()),r.common.async)return Promise.all([...r.data].map((l,o)=>s.type._parseAsync(new Wr(r,l,r.path,o)))).then(l=>ht.mergeArray(n,l));const i=[...r.data].map((l,o)=>s.type._parseSync(new Wr(r,l,r.path,o)));return ht.mergeArray(n,i)}get element(){return this._def.type}min(t,r){return new qt({...this._def,minLength:{value:t,message:H.toString(r)}})}max(t,r){return new qt({...this._def,maxLength:{value:t,message:H.toString(r)}})}length(t,r){return new qt({...this._def,exactLength:{value:t,message:H.toString(r)}})}nonempty(t){return this.min(1,t)}}qt.create=(e,t)=>new qt({type:e,minLength:null,maxLength:null,exactLength:null,typeName:X.ZodArray,...ne(t)});function Nn(e){if(e instanceof Ce){const t={};for(const r in e.shape){const n=e.shape[r];t[r]=Ur.create(Nn(n))}return new Ce({...e._def,shape:()=>t})}else return e instanceof qt?new qt({...e._def,type:Nn(e.element)}):e instanceof Ur?Ur.create(Nn(e.unwrap())):e instanceof rs?rs.create(Nn(e.unwrap())):e instanceof gn?gn.create(e.items.map(t=>Nn(t))):e}class Ce extends oe{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const t=this._def.shape(),r=ce.objectKeys(t);return this._cached={shape:t,keys:r},this._cached}_parse(t){if(this._getType(t)!==W.object){const u=this._getOrReturnCtx(t);return I(u,{code:O.invalid_type,expected:W.object,received:u.parsedType}),Y}const{status:n,ctx:s}=this._processInputParams(t),{shape:i,keys:l}=this._getCached(),o=[];if(!(this._def.catchall instanceof Hr&&this._def.unknownKeys==="strip"))for(const u in s.data)l.includes(u)||o.push(u);const c=[];for(const u of l){const d=i[u],f=s.data[u];c.push({key:{status:"valid",value:u},value:d._parse(new Wr(s,f,s.path,u)),alwaysSet:u in s.data})}if(this._def.catchall instanceof Hr){const u=this._def.unknownKeys;if(u==="passthrough")for(const d of o)c.push({key:{status:"valid",value:d},value:{status:"valid",value:s.data[d]}});else if(u==="strict")o.length>0&&(I(s,{code:O.unrecognized_keys,keys:o}),n.dirty());else if(u!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const u=this._def.catchall;for(const d of o){const f=s.data[d];c.push({key:{status:"valid",value:d},value:u._parse(new Wr(s,f,s.path,d)),alwaysSet:d in s.data})}}return s.common.async?Promise.resolve().then(async()=>{const u=[];for(const d of c){const f=await d.key,h=await d.value;u.push({key:f,value:h,alwaysSet:d.alwaysSet})}return u}).then(u=>ht.mergeObjectSync(n,u)):ht.mergeObjectSync(n,c)}get shape(){return this._def.shape()}strict(t){return H.errToObj,new Ce({...this._def,unknownKeys:"strict",...t!==void 0?{errorMap:(r,n)=>{var i,l;const s=((l=(i=this._def).errorMap)==null?void 0:l.call(i,r,n).message)??n.defaultError;return r.code==="unrecognized_keys"?{message:H.errToObj(t).message??s}:{message:s}}}:{}})}strip(){return new Ce({...this._def,unknownKeys:"strip"})}passthrough(){return new Ce({...this._def,unknownKeys:"passthrough"})}extend(t){return new Ce({...this._def,shape:()=>({...this._def.shape(),...t})})}merge(t){return new Ce({unknownKeys:t._def.unknownKeys,catchall:t._def.catchall,shape:()=>({...this._def.shape(),...t._def.shape()}),typeName:X.ZodObject})}setKey(t,r){return this.augment({[t]:r})}catchall(t){return new Ce({...this._def,catchall:t})}pick(t){const r={};for(const n of ce.objectKeys(t))t[n]&&this.shape[n]&&(r[n]=this.shape[n]);return new Ce({...this._def,shape:()=>r})}omit(t){const r={};for(const n of ce.objectKeys(this.shape))t[n]||(r[n]=this.shape[n]);return new Ce({...this._def,shape:()=>r})}deepPartial(){return Nn(this)}partial(t){const r={};for(const n of ce.objectKeys(this.shape)){const s=this.shape[n];t&&!t[n]?r[n]=s:r[n]=s.optional()}return new Ce({...this._def,shape:()=>r})}required(t){const r={};for(const n of ce.objectKeys(this.shape))if(t&&!t[n])r[n]=this.shape[n];else{let i=this.shape[n];for(;i instanceof Ur;)i=i._def.innerType;r[n]=i}return new Ce({...this._def,shape:()=>r})}keyof(){return Cg(ce.objectKeys(this.shape))}}Ce.create=(e,t)=>new Ce({shape:()=>e,unknownKeys:"strip",catchall:Hr.create(),typeName:X.ZodObject,...ne(t)});Ce.strictCreate=(e,t)=>new Ce({shape:()=>e,unknownKeys:"strict",catchall:Hr.create(),typeName:X.ZodObject,...ne(t)});Ce.lazycreate=(e,t)=>new Ce({shape:e,unknownKeys:"strip",catchall:Hr.create(),typeName:X.ZodObject,...ne(t)});class Za extends oe{_parse(t){const{ctx:r}=this._processInputParams(t),n=this._def.options;function s(i){for(const o of i)if(o.result.status==="valid")return o.result;for(const o of i)if(o.result.status==="dirty")return r.common.issues.push(...o.ctx.common.issues),o.result;const l=i.map(o=>new fr(o.ctx.common.issues));return I(r,{code:O.invalid_union,unionErrors:l}),Y}if(r.common.async)return Promise.all(n.map(async i=>{const l={...r,common:{...r.common,issues:[]},parent:null};return{result:await i._parseAsync({data:r.data,path:r.path,parent:l}),ctx:l}})).then(s);{let i;const l=[];for(const c of n){const u={...r,common:{...r.common,issues:[]},parent:null},d=c._parseSync({data:r.data,path:r.path,parent:u});if(d.status==="valid")return d;d.status==="dirty"&&!i&&(i={result:d,ctx:u}),u.common.issues.length&&l.push(u.common.issues)}if(i)return r.common.issues.push(...i.ctx.common.issues),i.result;const o=l.map(c=>new fr(c));return I(r,{code:O.invalid_union,unionErrors:o}),Y}}get options(){return this._def.options}}Za.create=(e,t)=>new Za({options:e,typeName:X.ZodUnion,...ne(t)});function pc(e,t){const r=br(e),n=br(t);if(e===t)return{valid:!0,data:e};if(r===W.object&&n===W.object){const s=ce.objectKeys(t),i=ce.objectKeys(e).filter(o=>s.indexOf(o)!==-1),l={...e,...t};for(const o of i){const c=pc(e[o],t[o]);if(!c.valid)return{valid:!1};l[o]=c.data}return{valid:!0,data:l}}else if(r===W.array&&n===W.array){if(e.length!==t.length)return{valid:!1};const s=[];for(let i=0;i<e.length;i++){const l=e[i],o=t[i],c=pc(l,o);if(!c.valid)return{valid:!1};s.push(c.data)}return{valid:!0,data:s}}else return r===W.date&&n===W.date&&+e==+t?{valid:!0,data:e}:{valid:!1}}class Ga extends oe{_parse(t){const{status:r,ctx:n}=this._processInputParams(t),s=(i,l)=>{if($f(i)||$f(l))return Y;const o=pc(i.value,l.value);return o.valid?((zf(i)||zf(l))&&r.dirty(),{status:r.value,value:o.data}):(I(n,{code:O.invalid_intersection_types}),Y)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([i,l])=>s(i,l)):s(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}Ga.create=(e,t,r)=>new Ga({left:e,right:t,typeName:X.ZodIntersection,...ne(r)});class gn extends oe{_parse(t){const{status:r,ctx:n}=this._processInputParams(t);if(n.parsedType!==W.array)return I(n,{code:O.invalid_type,expected:W.array,received:n.parsedType}),Y;if(n.data.length<this._def.items.length)return I(n,{code:O.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),Y;!this._def.rest&&n.data.length>this._def.items.length&&(I(n,{code:O.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),r.dirty());const i=[...n.data].map((l,o)=>{const c=this._def.items[o]||this._def.rest;return c?c._parse(new Wr(n,l,n.path,o)):null}).filter(l=>!!l);return n.common.async?Promise.all(i).then(l=>ht.mergeArray(r,l)):ht.mergeArray(r,i)}get items(){return this._def.items}rest(t){return new gn({...this._def,rest:t})}}gn.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new gn({items:e,typeName:X.ZodTuple,rest:null,...ne(t)})};class qf extends oe{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:r,ctx:n}=this._processInputParams(t);if(n.parsedType!==W.map)return I(n,{code:O.invalid_type,expected:W.map,received:n.parsedType}),Y;const s=this._def.keyType,i=this._def.valueType,l=[...n.data.entries()].map(([o,c],u)=>({key:s._parse(new Wr(n,o,n.path,[u,"key"])),value:i._parse(new Wr(n,c,n.path,[u,"value"]))}));if(n.common.async){const o=new Map;return Promise.resolve().then(async()=>{for(const c of l){const u=await c.key,d=await c.value;if(u.status==="aborted"||d.status==="aborted")return Y;(u.status==="dirty"||d.status==="dirty")&&r.dirty(),o.set(u.value,d.value)}return{status:r.value,value:o}})}else{const o=new Map;for(const c of l){const u=c.key,d=c.value;if(u.status==="aborted"||d.status==="aborted")return Y;(u.status==="dirty"||d.status==="dirty")&&r.dirty(),o.set(u.value,d.value)}return{status:r.value,value:o}}}}qf.create=(e,t,r)=>new qf({valueType:t,keyType:e,typeName:X.ZodMap,...ne(r)});class ui extends oe{_parse(t){const{status:r,ctx:n}=this._processInputParams(t);if(n.parsedType!==W.set)return I(n,{code:O.invalid_type,expected:W.set,received:n.parsedType}),Y;const s=this._def;s.minSize!==null&&n.data.size<s.minSize.value&&(I(n,{code:O.too_small,minimum:s.minSize.value,type:"set",inclusive:!0,exact:!1,message:s.minSize.message}),r.dirty()),s.maxSize!==null&&n.data.size>s.maxSize.value&&(I(n,{code:O.too_big,maximum:s.maxSize.value,type:"set",inclusive:!0,exact:!1,message:s.maxSize.message}),r.dirty());const i=this._def.valueType;function l(c){const u=new Set;for(const d of c){if(d.status==="aborted")return Y;d.status==="dirty"&&r.dirty(),u.add(d.value)}return{status:r.value,value:u}}const o=[...n.data.values()].map((c,u)=>i._parse(new Wr(n,c,n.path,u)));return n.common.async?Promise.all(o).then(c=>l(c)):l(o)}min(t,r){return new ui({...this._def,minSize:{value:t,message:H.toString(r)}})}max(t,r){return new ui({...this._def,maxSize:{value:t,message:H.toString(r)}})}size(t,r){return this.min(t,r).max(t,r)}nonempty(t){return this.min(1,t)}}ui.create=(e,t)=>new ui({valueType:e,minSize:null,maxSize:null,typeName:X.ZodSet,...ne(t)});class Qf extends oe{get schema(){return this._def.getter()}_parse(t){const{ctx:r}=this._processInputParams(t);return this._def.getter()._parse({data:r.data,path:r.path,parent:r})}}Qf.create=(e,t)=>new Qf({getter:e,typeName:X.ZodLazy,...ne(t)});class Jf extends oe{_parse(t){if(t.data!==this._def.value){const r=this._getOrReturnCtx(t);return I(r,{received:r.data,code:O.invalid_literal,expected:this._def.value}),Y}return{status:"valid",value:t.data}}get value(){return this._def.value}}Jf.create=(e,t)=>new Jf({value:e,typeName:X.ZodLiteral,...ne(t)});function Cg(e,t){return new es({values:e,typeName:X.ZodEnum,...ne(t)})}class es extends oe{_parse(t){if(typeof t.data!="string"){const r=this._getOrReturnCtx(t),n=this._def.values;return I(r,{expected:ce.joinValues(n),received:r.parsedType,code:O.invalid_type}),Y}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(t.data)){const r=this._getOrReturnCtx(t),n=this._def.values;return I(r,{received:r.data,code:O.invalid_enum_value,options:n}),Y}return _t(t.data)}get options(){return this._def.values}get enum(){const t={};for(const r of this._def.values)t[r]=r;return t}get Values(){const t={};for(const r of this._def.values)t[r]=r;return t}get Enum(){const t={};for(const r of this._def.values)t[r]=r;return t}extract(t,r=this._def){return es.create(t,{...this._def,...r})}exclude(t,r=this._def){return es.create(this.options.filter(n=>!t.includes(n)),{...this._def,...r})}}es.create=Cg;class Kf extends oe{_parse(t){const r=ce.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(t);if(n.parsedType!==W.string&&n.parsedType!==W.number){const s=ce.objectValues(r);return I(n,{expected:ce.joinValues(s),received:n.parsedType,code:O.invalid_type}),Y}if(this._cache||(this._cache=new Set(ce.getValidEnumValues(this._def.values))),!this._cache.has(t.data)){const s=ce.objectValues(r);return I(n,{received:n.data,code:O.invalid_enum_value,options:s}),Y}return _t(t.data)}get enum(){return this._def.values}}Kf.create=(e,t)=>new Kf({values:e,typeName:X.ZodNativeEnum,...ne(t)});class qa extends oe{unwrap(){return this._def.type}_parse(t){const{ctx:r}=this._processInputParams(t);if(r.parsedType!==W.promise&&r.common.async===!1)return I(r,{code:O.invalid_type,expected:W.promise,received:r.parsedType}),Y;const n=r.parsedType===W.promise?r.data:Promise.resolve(r.data);return _t(n.then(s=>this._def.type.parseAsync(s,{path:r.path,errorMap:r.common.contextualErrorMap})))}}qa.create=(e,t)=>new qa({type:e,typeName:X.ZodPromise,...ne(t)});class ts extends oe{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===X.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(t){const{status:r,ctx:n}=this._processInputParams(t),s=this._def.effect||null,i={addIssue:l=>{I(n,l),l.fatal?r.abort():r.dirty()},get path(){return n.path}};if(i.addIssue=i.addIssue.bind(i),s.type==="preprocess"){const l=s.transform(n.data,i);if(n.common.async)return Promise.resolve(l).then(async o=>{if(r.value==="aborted")return Y;const c=await this._def.schema._parseAsync({data:o,path:n.path,parent:n});return c.status==="aborted"?Y:c.status==="dirty"||r.value==="dirty"?Cs(c.value):c});{if(r.value==="aborted")return Y;const o=this._def.schema._parseSync({data:l,path:n.path,parent:n});return o.status==="aborted"?Y:o.status==="dirty"||r.value==="dirty"?Cs(o.value):o}}if(s.type==="refinement"){const l=o=>{const c=s.refinement(o,i);if(n.common.async)return Promise.resolve(c);if(c instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(n.common.async===!1){const o=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return o.status==="aborted"?Y:(o.status==="dirty"&&r.dirty(),l(o.value),{status:r.value,value:o.value})}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(o=>o.status==="aborted"?Y:(o.status==="dirty"&&r.dirty(),l(o.value).then(()=>({status:r.value,value:o.value}))))}if(s.type==="transform")if(n.common.async===!1){const l=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!Yn(l))return Y;const o=s.transform(l.value,i);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:r.value,value:o}}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(l=>Yn(l)?Promise.resolve(s.transform(l.value,i)).then(o=>({status:r.value,value:o})):Y);ce.assertNever(s)}}ts.create=(e,t,r)=>new ts({schema:e,typeName:X.ZodEffects,effect:t,...ne(r)});ts.createWithPreprocess=(e,t,r)=>new ts({schema:t,effect:{type:"preprocess",transform:e},typeName:X.ZodEffects,...ne(r)});class Ur extends oe{_parse(t){return this._getType(t)===W.undefined?_t(void 0):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}Ur.create=(e,t)=>new Ur({innerType:e,typeName:X.ZodOptional,...ne(t)});class rs extends oe{_parse(t){return this._getType(t)===W.null?_t(null):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}rs.create=(e,t)=>new rs({innerType:e,typeName:X.ZodNullable,...ne(t)});class gc extends oe{_parse(t){const{ctx:r}=this._processInputParams(t);let n=r.data;return r.parsedType===W.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:r.path,parent:r})}removeDefault(){return this._def.innerType}}gc.create=(e,t)=>new gc({innerType:e,typeName:X.ZodDefault,defaultValue:typeof t.default=="function"?t.default:()=>t.default,...ne(t)});class xc extends oe{_parse(t){const{ctx:r}=this._processInputParams(t),n={...r,common:{...r.common,issues:[]}},s=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return Wa(s)?s.then(i=>({status:"valid",value:i.status==="valid"?i.value:this._def.catchValue({get error(){return new fr(n.common.issues)},input:n.data})})):{status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new fr(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}xc.create=(e,t)=>new xc({innerType:e,typeName:X.ZodCatch,catchValue:typeof t.catch=="function"?t.catch:()=>t.catch,...ne(t)});class Yf extends oe{_parse(t){if(this._getType(t)!==W.nan){const n=this._getOrReturnCtx(t);return I(n,{code:O.invalid_type,expected:W.nan,received:n.parsedType}),Y}return{status:"valid",value:t.data}}}Yf.create=e=>new Yf({typeName:X.ZodNaN,...ne(e)});class W2 extends oe{_parse(t){const{ctx:r}=this._processInputParams(t),n=r.data;return this._def.type._parse({data:n,path:r.path,parent:r})}unwrap(){return this._def.type}}class Au extends oe{_parse(t){const{status:r,ctx:n}=this._processInputParams(t);if(n.common.async)return(async()=>{const i=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return i.status==="aborted"?Y:i.status==="dirty"?(r.dirty(),Cs(i.value)):this._def.out._parseAsync({data:i.value,path:n.path,parent:n})})();{const s=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return s.status==="aborted"?Y:s.status==="dirty"?(r.dirty(),{status:"dirty",value:s.value}):this._def.out._parseSync({data:s.value,path:n.path,parent:n})}}static create(t,r){return new Au({in:t,out:r,typeName:X.ZodPipeline})}}class yc extends oe{_parse(t){const r=this._def.innerType._parse(t),n=s=>(Yn(s)&&(s.value=Object.freeze(s.value)),s);return Wa(r)?r.then(s=>n(s)):n(r)}unwrap(){return this._def.innerType}}yc.create=(e,t)=>new yc({innerType:e,typeName:X.ZodReadonly,...ne(t)});Ce.lazycreate;var X;(function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"})(X||(X={}));const Ge=Pr.create,Xf=Xn.create,H2=hc.create;Hr.create;const em=qt.create,Ni=Ce.create;Ce.strictCreate;Za.create;Ga.create;gn.create;const Z2=es.create;qa.create;Ur.create;rs.create;const G2=Ni({email:Ge().min(1,"Email is required").email("Please enter a valid email address"),password:Ge().min(1,"Password is required").min(6,"Password must be at least 6 characters")}),q2=()=>{var h,w;const[e,t]=j.useState(!1),{login:r,loading:n}=We(),s=xt(),l=((w=(h=hr().state)==null?void 0:h.from)==null?void 0:w.pathname)||"/",{register:o,handleSubmit:c,formState:{errors:u},setError:d}=bi({resolver:ji(G2)}),f=async p=>{var b,x;const y=await r(p);y.success?s(l,{replace:!0}):(b=y.error)!=null&&b.includes("email")?d("email",{message:y.error}):(x=y.error)!=null&&x.includes("password")?d("password",{message:y.error}):d("root",{message:y.error})};return a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:a.jsxs("div",{className:"max-w-md w-full space-y-8",children:[a.jsxs("div",{className:"text-center",children:[a.jsxs(M,{to:"/",className:"flex items-center justify-center space-x-2 mb-6",children:[a.jsx("div",{className:"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center",children:a.jsx("span",{className:"text-white font-bold text-xl",children:"F"})}),a.jsx("span",{className:"text-2xl font-bold text-gray-900",children:"FreelanceHub"})]}),a.jsx("h2",{className:"text-3xl font-bold text-gray-900",children:"Welcome back"}),a.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Sign in to your account to continue"})]}),a.jsxs("form",{className:"mt-8 space-y-6",onSubmit:c(f),children:[u.root&&a.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:a.jsx("p",{className:"text-sm text-red-600",children:u.root.message})}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{children:[a.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email address"}),a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(ds,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{...o("email"),type:"email",autoComplete:"email",className:`block w-full pl-10 pr-3 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${u.email?"border-red-300 bg-red-50":"border-gray-300 bg-white"}`,placeholder:"Enter your email"})]}),u.email&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:u.email.message})]}),a.jsxs("div",{children:[a.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(oi,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{...o("password"),type:e?"text":"password",autoComplete:"current-password",className:`block w-full pl-10 pr-10 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${u.password?"border-red-300 bg-red-50":"border-gray-300 bg-white"}`,placeholder:"Enter your password"}),a.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>t(!e),children:e?a.jsx(li,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):a.jsx(Jn,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]}),u.password&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:u.password.message})]})]}),a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),a.jsx("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-700",children:"Remember me"})]}),a.jsx("div",{className:"text-sm",children:a.jsx(M,{to:"/forgot-password",className:"font-medium text-blue-600 hover:text-blue-500",children:"Forgot your password?"})})]}),a.jsx("div",{children:a.jsx("button",{type:"submit",disabled:n,className:"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:n?a.jsx(wn,{className:"w-5 h-5 animate-spin"}):"Sign in"})}),a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-0 flex items-center",children:a.jsx("div",{className:"w-full border-t border-gray-300"})}),a.jsx("div",{className:"relative flex justify-center text-sm",children:a.jsx("span",{className:"px-2 bg-gray-50 text-gray-500",children:"Or"})})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsxs("button",{type:"button",className:"w-full flex justify-center items-center px-4 py-3 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[a.jsxs("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",children:[a.jsx("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),a.jsx("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),a.jsx("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),a.jsx("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Continue with Google"]}),a.jsxs("button",{type:"button",className:"w-full flex justify-center items-center px-4 py-3 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[a.jsx("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})}),"Continue with Facebook"]})]}),a.jsx("div",{className:"text-center",children:a.jsxs("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",a.jsx(M,{to:"/register",className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign up for free"})]})})]})]})})},Q2=Ni({name:Ge().min(1,"Full name is required").min(2,"Name must be at least 2 characters").max(50,"Name must be less than 50 characters"),email:Ge().min(1,"Email is required").email("Please enter a valid email address"),password:Ge().min(1,"Password is required").min(8,"Password must be at least 8 characters").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,"Password must contain at least one uppercase letter, one lowercase letter, and one number"),confirmPassword:Ge().min(1,"Please confirm your password"),role:Z2(["client","freelancer"],{required_error:"Please select your role"}),agreeToTerms:H2().refine(e=>e===!0,"You must agree to the terms and conditions")}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]}),J2=()=>{const[e,t]=j.useState(!1),[r,n]=j.useState(!1),{register:s,loading:i}=We(),l=xt(),[o]=_p(),c=o.get("role")||"client",{register:u,handleSubmit:d,formState:{errors:f},setError:h,watch:w}=bi({resolver:ji(Q2),defaultValues:{role:c}}),p=w("role"),y=async b=>{var _;const{confirmPassword:x,agreeToTerms:m,...g}=b,N=await s(g);N.success?l("/"):(_=N.error)!=null&&_.includes("email")?h("email",{message:N.error}):h("root",{message:N.error})};return a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:a.jsxs("div",{className:"max-w-md w-full space-y-8",children:[a.jsxs("div",{className:"text-center",children:[a.jsxs(M,{to:"/",className:"flex items-center justify-center space-x-2 mb-6",children:[a.jsx("div",{className:"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center",children:a.jsx("span",{className:"text-white font-bold text-xl",children:"F"})}),a.jsx("span",{className:"text-2xl font-bold text-gray-900",children:"FreelanceHub"})]}),a.jsx("h2",{className:"text-3xl font-bold text-gray-900",children:"Create your account"}),a.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Join thousands of freelancers and clients"})]}),a.jsxs("form",{className:"mt-8 space-y-6",onSubmit:d(y),children:[f.root&&a.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:a.jsx("p",{className:"text-sm text-red-600",children:f.root.message})}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"I want to:"}),a.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[a.jsxs("label",{className:`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none ${p==="client"?"border-blue-600 bg-blue-50":"border-gray-300 bg-white hover:bg-gray-50"}`,children:[a.jsx("input",{...u("role"),type:"radio",value:"client",className:"sr-only"}),a.jsxs("div",{className:"flex flex-col",children:[a.jsx("span",{className:"block text-sm font-medium text-gray-900",children:"Hire freelancers"}),a.jsx("span",{className:"block text-sm text-gray-500",children:"I'm a client"})]}),p==="client"&&a.jsx("div",{className:"absolute top-2 right-2 w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center",children:a.jsx("div",{className:"w-2 h-2 bg-white rounded-full"})})]}),a.jsxs("label",{className:`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none ${p==="freelancer"?"border-blue-600 bg-blue-50":"border-gray-300 bg-white hover:bg-gray-50"}`,children:[a.jsx("input",{...u("role"),type:"radio",value:"freelancer",className:"sr-only"}),a.jsxs("div",{className:"flex flex-col",children:[a.jsx("span",{className:"block text-sm font-medium text-gray-900",children:"Work as freelancer"}),a.jsx("span",{className:"block text-sm text-gray-500",children:"I'm a freelancer"})]}),p==="freelancer"&&a.jsx("div",{className:"absolute top-2 right-2 w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center",children:a.jsx("div",{className:"w-2 h-2 bg-white rounded-full"})})]})]}),f.role&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:f.role.message})]}),a.jsxs("div",{children:[a.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:"Full Name"}),a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(za,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{...u("name"),type:"text",autoComplete:"name",className:`block w-full pl-10 pr-3 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${f.name?"border-red-300 bg-red-50":"border-gray-300 bg-white"}`,placeholder:"Enter your full name"})]}),f.name&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:f.name.message})]}),a.jsxs("div",{children:[a.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email address"}),a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(ds,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{...u("email"),type:"email",autoComplete:"email",className:`block w-full pl-10 pr-3 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${f.email?"border-red-300 bg-red-50":"border-gray-300 bg-white"}`,placeholder:"Enter your email"})]}),f.email&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:f.email.message})]}),a.jsxs("div",{children:[a.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(oi,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{...u("password"),type:e?"text":"password",autoComplete:"new-password",className:`block w-full pl-10 pr-10 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${f.password?"border-red-300 bg-red-50":"border-gray-300 bg-white"}`,placeholder:"Create a password"}),a.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>t(!e),children:e?a.jsx(li,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):a.jsx(Jn,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]}),f.password&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:f.password.message})]}),a.jsxs("div",{children:[a.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm Password"}),a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(oi,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{...u("confirmPassword"),type:r?"text":"password",autoComplete:"new-password",className:`block w-full pl-10 pr-10 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${f.confirmPassword?"border-red-300 bg-red-50":"border-gray-300 bg-white"}`,placeholder:"Confirm your password"}),a.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>n(!r),children:r?a.jsx(li,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):a.jsx(Jn,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]}),f.confirmPassword&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:f.confirmPassword.message})]})]}),a.jsxs("div",{className:"flex items-start",children:[a.jsx("div",{className:"flex items-center h-5",children:a.jsx("input",{...u("agreeToTerms"),type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})}),a.jsxs("div",{className:"ml-3 text-sm",children:[a.jsxs("label",{htmlFor:"agreeToTerms",className:"text-gray-700",children:["I agree to the"," ",a.jsx(M,{to:"/terms",className:"text-blue-600 hover:text-blue-500",children:"Terms of Service"})," ","and"," ",a.jsx(M,{to:"/privacy",className:"text-blue-600 hover:text-blue-500",children:"Privacy Policy"})]}),f.agreeToTerms&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:f.agreeToTerms.message})]})]}),a.jsx("div",{children:a.jsx("button",{type:"submit",disabled:i,className:"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:i?a.jsx(wn,{className:"w-5 h-5 animate-spin"}):"Create Account"})}),a.jsx("div",{className:"text-center",children:a.jsxs("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",a.jsx(M,{to:"/login",className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign in"})]})})]})]})})},K2=Ni({email:Ge().min(1,"Email is required").email("Please enter a valid email address")}),Y2=()=>{const[e,t]=j.useState(!1),{forgotPassword:r,loading:n}=We(),{register:s,handleSubmit:i,formState:{errors:l},setError:o,getValues:c}=bi({resolver:ji(K2)}),u=async d=>{const f=await r(d.email);f.success?t(!0):o("email",{message:f.error})};return e?a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:a.jsx("div",{className:"max-w-md w-full space-y-8",children:a.jsxs("div",{className:"text-center",children:[a.jsxs(M,{to:"/",className:"flex items-center justify-center space-x-2 mb-6",children:[a.jsx("div",{className:"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center",children:a.jsx("span",{className:"text-white font-bold text-xl",children:"F"})}),a.jsx("span",{className:"text-2xl font-bold text-gray-900",children:"FreelanceHub"})]}),a.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6",children:a.jsx(hn,{className:"w-8 h-8 text-green-600"})}),a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Check your email"}),a.jsxs("p",{className:"text-gray-600 mb-6",children:["We've sent a password reset link to"," ",a.jsx("span",{className:"font-medium text-gray-900",children:c("email")})]}),a.jsx("p",{className:"text-sm text-gray-500 mb-8",children:"Didn't receive the email? Check your spam folder or try again."}),a.jsxs("div",{className:"space-y-4",children:[a.jsx("button",{onClick:()=>t(!1),className:"w-full flex justify-center py-3 px-4 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",children:"Try another email"}),a.jsx(M,{to:"/login",className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",children:"Back to sign in"})]})]})})}):a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:a.jsxs("div",{className:"max-w-md w-full space-y-8",children:[a.jsxs("div",{className:"text-center",children:[a.jsxs(M,{to:"/",className:"flex items-center justify-center space-x-2 mb-6",children:[a.jsx("div",{className:"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center",children:a.jsx("span",{className:"text-white font-bold text-xl",children:"F"})}),a.jsx("span",{className:"text-2xl font-bold text-gray-900",children:"FreelanceHub"})]}),a.jsx("h2",{className:"text-3xl font-bold text-gray-900",children:"Forgot your password?"}),a.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"No worries! Enter your email and we'll send you a reset link."})]}),a.jsxs("form",{className:"mt-8 space-y-6",onSubmit:i(u),children:[a.jsx("div",{className:"space-y-4",children:a.jsxs("div",{children:[a.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email address"}),a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(ds,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{...s("email"),type:"email",autoComplete:"email",className:`block w-full pl-10 pr-3 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${l.email?"border-red-300 bg-red-50":"border-gray-300 bg-white"}`,placeholder:"Enter your email address"})]}),l.email&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:l.email.message})]})}),a.jsx("div",{children:a.jsx("button",{type:"submit",disabled:n,className:"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:n?a.jsx(wn,{className:"w-5 h-5 animate-spin"}):"Send reset link"})}),a.jsx("div",{className:"text-center",children:a.jsxs(M,{to:"/login",className:"inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-500",children:[a.jsx(pb,{className:"w-4 h-4 mr-1"}),"Back to sign in"]})})]}),a.jsxs("div",{className:"mt-8 p-4 bg-blue-50 rounded-lg",children:[a.jsx("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"Having trouble?"}),a.jsxs("p",{className:"text-sm text-blue-700",children:["If you don't receive an email within a few minutes, please check your spam folder or"," ",a.jsx(M,{to:"/contact",className:"underline hover:no-underline",children:"contact our support team"}),"."]})]})]})})},X2=Ni({password:Ge().min(1,"Password is required").min(8,"Password must be at least 8 characters").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,"Password must contain at least one uppercase letter, one lowercase letter, and one number"),confirmPassword:Ge().min(1,"Please confirm your password")}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]}),ej=()=>{const[e,t]=j.useState(!1),[r,n]=j.useState(!1),[s,i]=j.useState(!1),{token:l}=os(),o=xt(),{resetPassword:c,loading:u}=We(),{register:d,handleSubmit:f,formState:{errors:h},setError:w}=bi({resolver:ji(X2)}),p=async y=>{if(!l){w("root",{message:"Invalid reset token"});return}const b=await c(l,y.password);b.success?(i(!0),setTimeout(()=>{o("/login")},3e3)):w("root",{message:b.error})};return s?a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:a.jsx("div",{className:"max-w-md w-full space-y-8",children:a.jsxs("div",{className:"text-center",children:[a.jsxs(M,{to:"/",className:"flex items-center justify-center space-x-2 mb-6",children:[a.jsx("div",{className:"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center",children:a.jsx("span",{className:"text-white font-bold text-xl",children:"F"})}),a.jsx("span",{className:"text-2xl font-bold text-gray-900",children:"FreelanceHub"})]}),a.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6",children:a.jsx(hn,{className:"w-8 h-8 text-green-600"})}),a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Password reset successful!"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"Your password has been successfully reset. You can now sign in with your new password."}),a.jsx("p",{className:"text-sm text-gray-500 mb-8",children:"Redirecting to sign in page in 3 seconds..."}),a.jsx(M,{to:"/login",className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",children:"Sign in now"})]})})}):a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:a.jsxs("div",{className:"max-w-md w-full space-y-8",children:[a.jsxs("div",{className:"text-center",children:[a.jsxs(M,{to:"/",className:"flex items-center justify-center space-x-2 mb-6",children:[a.jsx("div",{className:"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center",children:a.jsx("span",{className:"text-white font-bold text-xl",children:"F"})}),a.jsx("span",{className:"text-2xl font-bold text-gray-900",children:"FreelanceHub"})]}),a.jsx("h2",{className:"text-3xl font-bold text-gray-900",children:"Reset your password"}),a.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Enter your new password below"})]}),a.jsxs("form",{className:"mt-8 space-y-6",onSubmit:f(p),children:[h.root&&a.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:a.jsx("p",{className:"text-sm text-red-600",children:h.root.message})}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{children:[a.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"New Password"}),a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(oi,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{...d("password"),type:e?"text":"password",autoComplete:"new-password",className:`block w-full pl-10 pr-10 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${h.password?"border-red-300 bg-red-50":"border-gray-300 bg-white"}`,placeholder:"Enter your new password"}),a.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>t(!e),children:e?a.jsx(li,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):a.jsx(Jn,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]}),h.password&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:h.password.message})]}),a.jsxs("div",{children:[a.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm New Password"}),a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(oi,{className:"h-5 w-5 text-gray-400"})}),a.jsx("input",{...d("confirmPassword"),type:r?"text":"password",autoComplete:"new-password",className:`block w-full pl-10 pr-10 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${h.confirmPassword?"border-red-300 bg-red-50":"border-gray-300 bg-white"}`,placeholder:"Confirm your new password"}),a.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>n(!r),children:r?a.jsx(li,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):a.jsx(Jn,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]}),h.confirmPassword&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:h.confirmPassword.message})]})]}),a.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[a.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Password requirements:"}),a.jsxs("ul",{className:"text-sm text-gray-600 space-y-1",children:[a.jsx("li",{children:"• At least 8 characters long"}),a.jsx("li",{children:"• Contains at least one uppercase letter"}),a.jsx("li",{children:"• Contains at least one lowercase letter"}),a.jsx("li",{children:"• Contains at least one number"})]})]}),a.jsx("div",{children:a.jsx("button",{type:"submit",disabled:u,className:"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:u?a.jsx(wn,{className:"w-5 h-5 animate-spin"}):"Reset password"})}),a.jsx("div",{className:"text-center",children:a.jsx(M,{to:"/login",className:"text-sm font-medium text-blue-600 hover:text-blue-500",children:"Back to sign in"})})]})]})})},tj=()=>{const[e,t]=j.useState("verifying"),[r,n]=j.useState(""),{token:s}=os(),i=xt(),{verifyEmail:l}=We();j.useEffect(()=>{s?o():(t("error"),n("Invalid verification token"))},[s]);const o=async()=>{try{const h=await l(s);h.success?(t("success"),n("Your email has been successfully verified!"),setTimeout(()=>{i("/dashboard")},3e3)):(t("error"),n(h.error||"Email verification failed"))}catch{t("error"),n("An unexpected error occurred during verification")}},c=()=>{switch(e){case"verifying":return a.jsx(wn,{className:"w-8 h-8 text-blue-600 animate-spin"});case"success":return a.jsx(hn,{className:"w-8 h-8 text-green-600"});case"error":return a.jsx(Kn,{className:"w-8 h-8 text-red-600"});default:return a.jsx(ds,{className:"w-8 h-8 text-gray-600"})}},u=()=>{switch(e){case"verifying":return"bg-blue-100";case"success":return"bg-green-100";case"error":return"bg-red-100";default:return"bg-gray-100"}},d=()=>{switch(e){case"verifying":return"Verifying your email...";case"success":return"Email verified successfully!";case"error":return"Verification failed";default:return"Email verification"}},f=()=>{switch(e){case"verifying":return"Please wait while we verify your email address.";case"success":return"Your email has been verified. You can now access all features of your account.";case"error":return r||"We were unable to verify your email address.";default:return"Verifying your email address..."}};return a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:a.jsx("div",{className:"max-w-md w-full space-y-8",children:a.jsxs("div",{className:"text-center",children:[a.jsxs(M,{to:"/",className:"flex items-center justify-center space-x-2 mb-8",children:[a.jsx("div",{className:"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center",children:a.jsx("span",{className:"text-white font-bold text-xl",children:"F"})}),a.jsx("span",{className:"text-2xl font-bold text-gray-900",children:"FreelanceHub"})]}),a.jsx("div",{className:`w-16 h-16 ${u()} rounded-full flex items-center justify-center mx-auto mb-6`,children:c()}),a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:d()}),a.jsx("p",{className:"text-gray-600 mb-8",children:f()}),a.jsxs("div",{className:"space-y-4",children:[e==="success"&&a.jsxs(a.Fragment,{children:[a.jsx("p",{className:"text-sm text-gray-500 mb-4",children:"Redirecting to your dashboard in 3 seconds..."}),a.jsx(M,{to:"/dashboard",className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",children:"Go to Dashboard"}),a.jsx(M,{to:"/",className:"w-full flex justify-center py-3 px-4 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",children:"Back to Home"})]}),e==="error"&&a.jsxs(a.Fragment,{children:[a.jsx("button",{onClick:o,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",children:"Try Again"}),a.jsx(M,{to:"/login",className:"w-full flex justify-center py-3 px-4 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",children:"Back to Sign In"})]}),e==="verifying"&&a.jsx("div",{className:"flex justify-center",children:a.jsx("div",{className:"text-sm text-gray-500",children:"This may take a few moments..."})})]}),e==="error"&&a.jsxs("div",{className:"mt-8 p-4 bg-yellow-50 rounded-lg",children:[a.jsx("h3",{className:"text-sm font-medium text-yellow-900 mb-2",children:"Need help?"}),a.jsxs("p",{className:"text-sm text-yellow-700",children:["If you continue to have issues verifying your email, please"," ",a.jsx(M,{to:"/contact",className:"underline hover:no-underline",children:"contact our support team"})," ","for assistance."]})]}),e==="success"&&a.jsxs("div",{className:"mt-8 p-4 bg-blue-50 rounded-lg",children:[a.jsx("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"What's next?"}),a.jsx("p",{className:"text-sm text-blue-700",children:"Now that your email is verified, you can:"}),a.jsxs("ul",{className:"text-sm text-blue-700 mt-2 space-y-1",children:[a.jsx("li",{children:"• Complete your profile setup"}),a.jsx("li",{children:"• Browse and purchase services"}),a.jsx("li",{children:"• Start selling your own services"}),a.jsx("li",{children:"• Connect with other freelancers"})]})]})]})})})},rj=({type:e="gig",count:t=6,viewMode:r="grid"})=>{const n=()=>a.jsxs("div",{className:`bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden animate-pulse ${r==="list"?"flex":""}`,children:[a.jsx("div",{className:`${r==="list"?"w-64 flex-shrink-0":""}`,children:a.jsx("div",{className:`bg-gray-200 animate-shimmer ${r==="list"?"h-full":"h-48"}`})}),a.jsxs("div",{className:"p-6 flex-1",children:[a.jsxs("div",{className:"mb-3",children:[a.jsx("div",{className:"h-5 bg-gray-200 rounded animate-shimmer mb-2"}),a.jsx("div",{className:"h-5 bg-gray-200 rounded animate-shimmer w-3/4"})]}),a.jsxs("div",{className:"flex items-center mb-3",children:[a.jsx("div",{className:"w-8 h-8 bg-gray-200 rounded-full animate-shimmer mr-3"}),a.jsxs("div",{children:[a.jsx("div",{className:"h-4 bg-gray-200 rounded animate-shimmer w-20 mb-1"}),a.jsx("div",{className:"h-3 bg-gray-200 rounded animate-shimmer w-16"})]})]}),a.jsxs("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("div",{className:"flex items-center",children:a.jsx("div",{className:"h-4 bg-gray-200 rounded animate-shimmer w-24"})}),a.jsx("div",{className:"h-4 bg-gray-200 rounded animate-shimmer w-16"})]}),a.jsx("div",{className:"flex items-center justify-between",children:a.jsxs("div",{children:[a.jsx("div",{className:"h-3 bg-gray-200 rounded animate-shimmer w-16 mb-1"}),a.jsx("div",{className:"h-6 bg-gray-200 rounded animate-shimmer w-12"})]})})]})]}),s=()=>a.jsxs("div",{className:"bg-white rounded-2xl shadow-lg border border-gray-100 p-8 animate-pulse",children:[a.jsx("div",{className:"mb-6",children:a.jsx("div",{className:"w-16 h-16 bg-gray-200 rounded-2xl animate-shimmer mb-4"})}),a.jsxs("div",{children:[a.jsx("div",{className:"h-6 bg-gray-200 rounded animate-shimmer mb-3"}),a.jsx("div",{className:"h-4 bg-gray-200 rounded animate-shimmer mb-4"}),a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("div",{className:"h-4 bg-gray-200 rounded animate-shimmer w-20"}),a.jsx("div",{className:"h-4 bg-gray-200 rounded animate-shimmer w-16"})]})]})]}),i=()=>a.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-pulse",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("div",{className:"h-4 bg-gray-200 rounded animate-shimmer w-20 mb-2"}),a.jsx("div",{className:"h-8 bg-gray-200 rounded animate-shimmer w-16"})]}),a.jsx("div",{className:"w-12 h-12 bg-gray-200 rounded-lg animate-shimmer"})]})}),l=()=>a.jsx("div",{className:"bg-gradient-to-br from-gray-300 to-gray-400 py-20 animate-pulse",children:a.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[a.jsx("div",{className:"h-12 bg-gray-200 rounded animate-shimmer mb-6 mx-auto w-3/4"}),a.jsx("div",{className:"h-6 bg-gray-200 rounded animate-shimmer mb-8 mx-auto w-1/2"}),a.jsx("div",{className:"max-w-2xl mx-auto mb-8",children:a.jsx("div",{className:"h-12 bg-gray-200 rounded-xl animate-shimmer"})}),a.jsx("div",{className:"flex justify-center gap-2",children:[...Array(5)].map((c,u)=>a.jsx("div",{className:"h-8 bg-gray-200 rounded-full animate-shimmer w-20"},u))})]})}),o=()=>{switch(e){case"gig":return[...Array(t)].map((c,u)=>a.jsx("div",{className:"animate-fadeInUp",style:{animationDelay:`${u*.1}s`},children:n()},u));case"category":return[...Array(t)].map((c,u)=>a.jsx("div",{className:"animate-fadeInUp",style:{animationDelay:`${u*.1}s`},children:s()},u));case"stat":return[...Array(t)].map((c,u)=>a.jsx("div",{className:"animate-fadeInUp",style:{animationDelay:`${u*.1}s`},children:i()},u));case"hero":return l();default:return n()}};return e==="hero"?o():a.jsx("div",{className:`${r==="grid"?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-6"}`,children:o()})},nj=()=>{const[e,t]=j.useState([]),[r,n]=j.useState(!0),[s,i]=_p(),[l,o]=j.useState("grid");j.useState(!1);const[c,u]=j.useState(s.get("search")||""),[d,f]=j.useState(s.get("category")||""),[h,w]=j.useState({min:s.get("minPrice")||"",max:s.get("maxPrice")||""}),[p,y]=j.useState(s.get("sort")||"relevance"),[b,x]=j.useState(s.get("delivery")||""),[m,g]=j.useState(s.get("rating")||""),[N,_]=j.useState(1),[R,E]=j.useState(1),[A,q]=j.useState(0),Z=[{value:"",label:"All Categories"},{value:"graphics-design",label:"Graphics & Design"},{value:"digital-marketing",label:"Digital Marketing"},{value:"writing-translation",label:"Writing & Translation"},{value:"video-animation",label:"Video & Animation"},{value:"music-audio",label:"Music & Audio"},{value:"programming-tech",label:"Programming & Tech"}],ue=[{value:"relevance",label:"Relevance"},{value:"rating",label:"Best Rating"},{value:"price-low",label:"Price: Low to High"},{value:"price-high",label:"Price: High to Low"},{value:"newest",label:"Newest First"},{value:"popular",label:"Most Popular"}];j.useEffect(()=>{ee()},[s,N]);const ee=async()=>{try{n(!0);const V={search:c,category:d,minPrice:h.min,maxPrice:h.max,sort:p,delivery:b,rating:m,page:N,limit:12};Object.keys(V).forEach(U=>{V[U]||delete V[U]});const T=Cu.getGigs(V);t(T.gigs||[]),E(T.totalPages||1),q(T.total||0)}catch(V){console.error("Error fetching gigs:",V),t([])}finally{n(!1)}},re=V=>{V.preventDefault(),ae({search:c,page:1})},ae=V=>{const T=new URLSearchParams(s);Object.entries(V).forEach(([U,D])=>{D?T.set(U,D):T.delete(U)}),i(T),_(1)},je=()=>{u(""),f(""),w({min:"",max:""}),y("relevance"),x(""),g(""),i({})},xe=V=>{_(V),window.scrollTo({top:0,behavior:"smooth"})};return a.jsxs("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("div",{className:"bg-white border-b border-gray-200",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:a.jsxs("form",{onSubmit:re,className:"flex gap-4",children:[a.jsxs("div",{className:"flex-1 relative",children:[a.jsx(sr,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),a.jsx("input",{type:"text",value:c,onChange:V=>u(V.target.value),placeholder:"Search for services...",className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),a.jsx("button",{type:"submit",className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Search"})]})})}),a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:a.jsxs("div",{className:"flex flex-col lg:flex-row gap-8",children:[a.jsx("div",{className:"lg:w-64 flex-shrink-0",children:a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[a.jsxs("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Filters"}),a.jsx("button",{onClick:je,className:"text-sm text-blue-600 hover:text-blue-700",children:"Clear all"})]}),a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Category"}),a.jsx("select",{value:d,onChange:V=>{f(V.target.value),ae({category:V.target.value})},className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:Z.map(V=>a.jsx("option",{value:V.value,children:V.label},V.value))})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Price Range"}),a.jsxs("div",{className:"flex gap-2",children:[a.jsx("input",{type:"number",placeholder:"Min",value:h.min,onChange:V=>w(T=>({...T,min:V.target.value})),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),a.jsx("input",{type:"number",placeholder:"Max",value:h.max,onChange:V=>w(T=>({...T,max:V.target.value})),className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),a.jsx("button",{onClick:()=>ae({minPrice:h.min,maxPrice:h.max}),className:"w-full mt-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:"Apply"})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Delivery Time"}),a.jsxs("select",{value:b,onChange:V=>{x(V.target.value),ae({delivery:V.target.value})},className:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[a.jsx("option",{value:"",children:"Any time"}),a.jsx("option",{value:"1",children:"Express 24H"}),a.jsx("option",{value:"3",children:"Up to 3 days"}),a.jsx("option",{value:"7",children:"Up to 1 week"}),a.jsx("option",{value:"30",children:"Up to 1 month"})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Seller Rating"}),a.jsx("div",{className:"space-y-2",children:[5,4,3].map(V=>a.jsxs("label",{className:"flex items-center",children:[a.jsx("input",{type:"radio",name:"rating",value:V,checked:m===V.toString(),onChange:T=>{g(T.target.value),ae({rating:T.target.value})},className:"mr-2"}),a.jsxs("div",{className:"flex items-center",children:[[...Array(5)].map((T,U)=>a.jsx(zr,{className:`w-4 h-4 ${U<V?"text-yellow-400 fill-current":"text-gray-300"}`},U)),a.jsx("span",{className:"ml-1 text-sm text-gray-600",children:"& up"})]})]},V))})]})]})]})}),a.jsxs("div",{className:"flex-1",children:[a.jsxs("div",{className:"flex items-center justify-between mb-6",children:[a.jsxs("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:c?`Results for "${c}"`:"All Services"}),a.jsxs("p",{className:"text-gray-600 mt-1",children:[A," services available"]})]}),a.jsxs("div",{className:"flex items-center gap-4",children:[a.jsx("select",{value:p,onChange:V=>{y(V.target.value),ae({sort:V.target.value})},className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:ue.map(V=>a.jsx("option",{value:V.value,children:V.label},V.value))}),a.jsxs("div",{className:"flex border border-gray-300 rounded-lg",children:[a.jsx("button",{onClick:()=>o("grid"),className:`p-2 ${l==="grid"?"bg-blue-600 text-white":"text-gray-600 hover:text-gray-900"}`,children:a.jsx(ug,{className:"w-5 h-5"})}),a.jsx("button",{onClick:()=>o("list"),className:`p-2 ${l==="list"?"bg-blue-600 text-white":"text-gray-600 hover:text-gray-900"}`,children:a.jsx(fg,{className:"w-5 h-5"})})]})]})]}),r?a.jsx(rj,{type:"gig",count:12,viewMode:l}):a.jsxs(a.Fragment,{children:[a.jsx("div",{className:`${l==="grid"?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-6"}`,children:e.map(V=>a.jsx(sj,{gig:V,viewMode:l},V._id))}),e.length===0&&a.jsxs("div",{className:"text-center py-12",children:[a.jsx("div",{className:"text-gray-400 text-6xl mb-4",children:"🔍"}),a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No services found"}),a.jsx("p",{className:"text-gray-600 mb-4",children:"Try adjusting your search criteria or browse different categories"}),a.jsx("button",{onClick:je,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Clear Filters"})]}),R>1&&a.jsx("div",{className:"flex justify-center mt-8",children:a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx("button",{onClick:()=>xe(N-1),disabled:N===1,className:"px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Previous"}),[...Array(Math.min(5,R))].map((V,T)=>{const U=T+1;return a.jsx("button",{onClick:()=>xe(U),className:`px-3 py-2 border rounded-lg ${N===U?"bg-blue-600 text-white border-blue-600":"border-gray-300 hover:bg-gray-50"}`,children:U},U)}),a.jsx("button",{onClick:()=>xe(N+1),disabled:N===R,className:"px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Next"})]})})]})]})]})})]})},sj=({gig:e,viewMode:t})=>{var r,n,s,i,l,o,c,u;return a.jsxs(M,{to:`/gigs/${e.slug}`,className:`group bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-xl hover:-translate-y-1 transition-all duration-300 animate-fadeInUp ${t==="list"?"flex":""}`,children:[a.jsxs("div",{className:`relative ${t==="list"?"w-64 flex-shrink-0":""}`,children:[(r=e.images)!=null&&r[0]?a.jsx("img",{src:e.images[0],alt:e.title,className:`w-full object-cover group-hover:scale-110 transition-transform duration-500 ${t==="list"?"h-full":"h-48"}`}):a.jsx("div",{className:`w-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center ${t==="list"?"h-full":"h-48"}`,children:a.jsx("span",{className:"text-white text-2xl font-bold",children:e.title.charAt(0)})}),e.featured&&a.jsx("div",{className:"absolute top-3 left-3",children:a.jsx("span",{className:"px-2 py-1 bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold rounded-full shadow-lg",children:"⭐ Featured"})}),a.jsx("button",{className:"absolute top-3 right-3 p-2 bg-white bg-opacity-80 rounded-full text-gray-400 hover:text-red-500 hover:bg-white transition-all duration-300 opacity-0 group-hover:opacity-100",children:a.jsx(dg,{className:"w-4 h-4"})})]}),a.jsxs("div",{className:"p-6 flex-1",children:[a.jsx("div",{className:"mb-3",children:a.jsx("h3",{className:"font-bold text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors text-lg leading-tight",children:e.title})}),a.jsxs("div",{className:"flex items-center mb-3",children:[a.jsx("img",{src:((n=e.seller)==null?void 0:n.avatar)||"/default-avatar.png",alt:(s=e.seller)==null?void 0:s.name,className:"w-8 h-8 rounded-full mr-3 border-2 border-gray-100"}),a.jsxs("div",{children:[a.jsx("span",{className:"text-sm font-medium text-gray-700",children:(i=e.seller)==null?void 0:i.name}),a.jsxs("div",{className:"flex items-center text-xs text-gray-500",children:[a.jsx(Su,{className:"w-3 h-3 mr-1"}),a.jsx("span",{children:((l=e.seller)==null?void 0:l.location)||"Global"})]})]})]}),a.jsxs("div",{className:"flex items-center justify-between mb-4",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"flex items-center text-yellow-500",children:[...Array(5)].map((d,f)=>a.jsx(zr,{className:`w-4 h-4 ${f<Math.floor(e.rating)?"fill-current":""}`},f))}),a.jsxs("span",{className:"text-sm text-gray-600 ml-2 font-medium",children:[((o=e.rating)==null?void 0:o.toFixed(1))||"5.0"," (",e.reviewCount||0,")"]})]}),a.jsxs("div",{className:"flex items-center text-gray-500 text-sm",children:[a.jsx(pn,{className:"w-4 h-4 mr-1"}),a.jsxs("span",{children:[e.deliveryTime||3," days"]})]})]}),a.jsx("div",{className:"flex items-center justify-between",children:a.jsxs("div",{children:[a.jsx("span",{className:"text-sm text-gray-500",children:"Starting at"}),a.jsxs("div",{className:"text-xl font-bold text-gray-900",children:["$",((u=(c=e.pricing)==null?void 0:c.basic)==null?void 0:u.price)||e.startingPrice]})]})})]})]})},ij=()=>{var E,A,q,Z,ue,ee,re,ae,je,xe,V,T,U;const{slug:e}=os(),t=xt(),{user:r,isAuthenticated:n}=We(),[s,i]=j.useState(null),[l,o]=j.useState([]),[c,u]=j.useState(!0),[d,f]=j.useState(!1),[h,w]=j.useState("basic"),[p,y]=j.useState(0),[b,x]=j.useState(!1);j.useEffect(()=>{e&&m()},[e]);const m=async()=>{try{u(!0);const D=Cu.getGigBySlug(e);D?(i(D),g(D._id)):(se.error("Gig not found"),t("/gigs"))}catch(D){console.error("Error fetching gig details:",D),se.error("Failed to load gig details"),t("/gigs")}finally{u(!1)}},g=async D=>{try{f(!0);const K=Zb.filter(me=>me.gigId===D);o(K)}catch(K){console.error("Error fetching reviews:",K)}finally{f(!1)}},N=async()=>{var D;if(!n){se.error("Please sign in to place an order"),t("/login",{state:{from:location}});return}if((r==null?void 0:r.role)==="freelancer"&&((D=s==null?void 0:s.seller)==null?void 0:D._id)===(r==null?void 0:r._id)){se.error("You cannot order your own gig");return}try{const K=s.pricing[h],me={gig:s._id,seller:s.seller._id,package:h,price:K.price,deliveryTime:K.deliveryTime,requirements:K.features},yt=await ku.create(me);se.success("Order placed successfully!"),t(`/orders/${yt.data._id}`)}catch(K){console.error("Error placing order:",K),se.error("Failed to place order")}},_=()=>{if(!n){se.error("Please sign in to contact the seller"),t("/login",{state:{from:location}});return}t(`/messages?seller=${s.seller._id}`)};if(c)return a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx(dr,{size:"lg"})});if(!s)return a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsxs("div",{className:"text-center",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Gig not found"}),a.jsx(M,{to:"/gigs",className:"text-blue-600 hover:text-blue-700",children:"Browse all gigs"})]})});const R=((E=s.pricing)==null?void 0:E[h])||((A=s.pricing)==null?void 0:A.basic);return a.jsx("div",{className:"min-h-screen bg-gray-50",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[a.jsxs("div",{className:"lg:col-span-2 space-y-8",children:[a.jsx("nav",{className:"flex","aria-label":"Breadcrumb",children:a.jsxs("ol",{className:"flex items-center space-x-2",children:[a.jsx("li",{children:a.jsx(M,{to:"/",className:"text-gray-500 hover:text-gray-700",children:"Home"})}),a.jsx("li",{children:a.jsx("span",{className:"text-gray-500",children:"/"})}),a.jsx("li",{children:a.jsx(M,{to:"/gigs",className:"text-gray-500 hover:text-gray-700",children:"Services"})}),a.jsx("li",{children:a.jsx("span",{className:"text-gray-500",children:"/"})}),a.jsx("li",{children:a.jsx("span",{className:"text-gray-900 font-medium",children:s.category})})]})}),a.jsxs("div",{className:"flex items-start justify-between",children:[a.jsxs("div",{className:"flex-1",children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:s.title}),a.jsxs("div",{className:"flex items-center space-x-4 mb-4",children:[a.jsx("img",{src:((q=s.seller)==null?void 0:q.avatar)||"/default-avatar.png",alt:(Z=s.seller)==null?void 0:Z.name,className:"w-12 h-12 rounded-full"}),a.jsxs("div",{children:[a.jsx(M,{to:`/profile/${(ue=s.seller)==null?void 0:ue._id}`,className:"font-semibold text-gray-900 hover:text-blue-600",children:(ee=s.seller)==null?void 0:ee.name}),a.jsxs("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx(zr,{className:"w-4 h-4 text-yellow-400 fill-current mr-1"}),a.jsx("span",{children:((re=s.rating)==null?void 0:re.toFixed(1))||"5.0"})]}),a.jsx("span",{children:"•"}),a.jsxs("span",{children:[s.reviewCount||0," reviews"]}),a.jsx("span",{children:"•"}),a.jsxs("span",{children:[s.ordersInQueue||0," in queue"]})]})]})]})]}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx("button",{className:"p-2 text-gray-600 hover:text-red-500 transition-colors",children:a.jsx(dg,{className:"w-5 h-5"})}),a.jsx("button",{className:"p-2 text-gray-600 hover:text-blue-500 transition-colors",children:a.jsx(Ab,{className:"w-5 h-5"})}),a.jsx("button",{className:"p-2 text-gray-600 hover:text-red-500 transition-colors",children:a.jsx(bb,{className:"w-5 h-5"})})]})]}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:s.images&&s.images.length>0?a.jsxs("div",{children:[a.jsx("img",{src:s.images[p],alt:s.title,className:"w-full h-96 object-cover"}),s.images.length>1&&a.jsx("div",{className:"p-4 flex space-x-2 overflow-x-auto",children:s.images.map((D,K)=>a.jsx("button",{onClick:()=>y(K),className:`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${p===K?"border-blue-600":"border-gray-200"}`,children:a.jsx("img",{src:D,alt:`${s.title} ${K+1}`,className:"w-full h-full object-cover"})},K))})]}):a.jsx("div",{className:"w-full h-96 bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center",children:a.jsx("span",{className:"text-white text-6xl font-bold",children:s.title.charAt(0)})})}),a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"About this gig"}),a.jsxs("div",{className:"prose max-w-none",children:[a.jsx("p",{className:"text-gray-700 leading-relaxed",children:b?s.description:`${(ae=s.description)==null?void 0:ae.substring(0,300)}${((je=s.description)==null?void 0:je.length)>300?"...":""}`}),((xe=s.description)==null?void 0:xe.length)>300&&a.jsx("button",{onClick:()=>x(!b),className:"text-blue-600 hover:text-blue-700 font-medium mt-2",children:b?"Show less":"Show more"})]})]}),a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[a.jsxs("div",{className:"flex items-center justify-between mb-6",children:[a.jsxs("h2",{className:"text-xl font-semibold text-gray-900",children:["Reviews (",s.reviewCount||0,")"]}),a.jsxs("div",{className:"flex items-center",children:[a.jsx(zr,{className:"w-5 h-5 text-yellow-400 fill-current mr-1"}),a.jsx("span",{className:"font-semibold",children:((V=s.rating)==null?void 0:V.toFixed(1))||"5.0"})]})]}),d?a.jsx("div",{className:"flex justify-center py-8",children:a.jsx(dr,{})}):l.length>0?a.jsx("div",{className:"space-y-6",children:l.map(D=>{var K,me,yt;return a.jsx("div",{className:"border-b border-gray-200 pb-6 last:border-b-0",children:a.jsxs("div",{className:"flex items-start space-x-4",children:[a.jsx("img",{src:((K=D.buyer)==null?void 0:K.avatar)||"/default-avatar.png",alt:(me=D.buyer)==null?void 0:me.name,className:"w-10 h-10 rounded-full"}),a.jsxs("div",{className:"flex-1",children:[a.jsxs("div",{className:"flex items-center justify-between mb-2",children:[a.jsxs("div",{children:[a.jsx("h4",{className:"font-semibold text-gray-900",children:(yt=D.buyer)==null?void 0:yt.name}),a.jsx("div",{className:"flex items-center",children:[...Array(5)].map((Mt,Jt)=>a.jsx(zr,{className:`w-4 h-4 ${Jt<D.rating?"text-yellow-400 fill-current":"text-gray-300"}`},Jt))})]}),a.jsx("span",{className:"text-sm text-gray-500",children:new Date(D.createdAt).toLocaleDateString()})]}),a.jsx("p",{className:"text-gray-700",children:D.comment})]})]})},D._id)})}):a.jsx("div",{className:"text-center py-8",children:a.jsx("p",{className:"text-gray-500",children:"No reviews yet"})})]})]}),a.jsx("div",{className:"lg:col-span-1",children:a.jsx("div",{className:"sticky top-8",children:a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[s.pricing&&a.jsx("div",{className:"mb-6",children:a.jsx("div",{className:"flex border-b border-gray-200",children:Object.keys(s.pricing).map(D=>a.jsx("button",{onClick:()=>w(D),className:`flex-1 py-3 px-4 text-sm font-medium capitalize ${h===D?"border-b-2 border-blue-600 text-blue-600":"text-gray-600 hover:text-gray-900"}`,children:D},D))})}),R&&a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("span",{className:"text-2xl font-bold text-gray-900",children:["$",R.price]}),a.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[a.jsx(pn,{className:"w-4 h-4 mr-1"}),a.jsxs("span",{children:[R.deliveryTime," days delivery"]})]})]}),a.jsx("p",{className:"text-gray-700",children:R.description}),a.jsx("div",{className:"space-y-2",children:(T=R.features)==null?void 0:T.map((D,K)=>a.jsxs("div",{className:"flex items-center",children:[a.jsx(hn,{className:"w-4 h-4 text-green-600 mr-2"}),a.jsx("span",{className:"text-sm text-gray-700",children:D})]},K))}),a.jsxs("div",{className:"flex items-center justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"Revisions:"}),a.jsx("span",{className:"font-medium",children:R.revisions===-1?"Unlimited":R.revisions})]}),a.jsxs("div",{className:"space-y-3 pt-4",children:[a.jsxs("button",{onClick:N,className:"w-full py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors",children:["Continue ($",R.price,")"]}),a.jsx("button",{onClick:_,className:"w-full py-3 border border-gray-300 text-gray-700 font-semibold rounded-lg hover:bg-gray-50 transition-colors",children:"Contact Seller"})]})]}),a.jsxs("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[a.jsx("h3",{className:"font-semibold text-gray-900 mb-4",children:"About the seller"}),a.jsxs("div",{className:"space-y-3",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"Response time:"}),a.jsx("span",{className:"text-sm font-medium",children:"1 hour"})]}),a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"Last delivery:"}),a.jsx("span",{className:"text-sm font-medium",children:"1 day"})]}),a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"Orders completed:"}),a.jsx("span",{className:"text-sm font-medium",children:((U=s.seller)==null?void 0:U.completedOrders)||0})]})]})]})]})})})]})})})},aj=Ni({title:Ge().min(1,"Title is required").max(80,"Title must be less than 80 characters"),category:Ge().min(1,"Category is required"),subcategory:Ge().optional(),description:Ge().min(1,"Description is required").min(120,"Description must be at least 120 characters"),tags:em(Ge()).min(1,"At least one tag is required").max(5,"Maximum 5 tags allowed"),basicPrice:Xf().min(5,"Minimum price is $5").max(1e4,"Maximum price is $10,000"),basicDeliveryTime:Xf().min(1,"Minimum delivery time is 1 day").max(30,"Maximum delivery time is 30 days"),basicDescription:Ge().min(1,"Package description is required"),basicFeatures:em(Ge()).min(1,"At least one feature is required")}),lj=()=>{const e=xt(),[t,r]=j.useState(!1),[n,s]=j.useState([]),[i,l]=j.useState([]),[o,c]=j.useState(""),[u,d]=j.useState([""]),{register:f,handleSubmit:h,formState:{errors:w},setValue:p,watch:y}=bi({resolver:ji(aj),defaultValues:{tags:[],basicFeatures:[""]}}),b=[{value:"graphics-design",label:"Graphics & Design"},{value:"digital-marketing",label:"Digital Marketing"},{value:"writing-translation",label:"Writing & Translation"},{value:"video-animation",label:"Video & Animation"},{value:"music-audio",label:"Music & Audio"},{value:"programming-tech",label:"Programming & Tech"}],x=()=>{if(o.trim()&&i.length<5&&!i.includes(o.trim())){const E=[...i,o.trim()];l(E),p("tags",E),c("")}},m=E=>{const A=i.filter(q=>q!==E);l(A),p("tags",A)},g=()=>{d([...u,""])},N=(E,A)=>{const q=u.map((Z,ue)=>ue===E?A:Z);d(q),p("basicFeatures",q.filter(Z=>Z.trim()))},_=E=>{if(u.length>1){const A=u.filter((q,Z)=>Z!==E);d(A),p("basicFeatures",A.filter(q=>q.trim()))}},R=async E=>{try{r(!0);const A={...E,images:n,pricing:{basic:{price:E.basicPrice,deliveryTime:E.basicDeliveryTime,description:E.basicDescription,features:E.basicFeatures.filter(Z=>Z.trim()),revisions:1}}},q=await Nu.create(A);se.success("Gig created successfully!"),e(`/gigs/${q.data.slug}`)}catch(A){console.error("Error creating gig:",A),se.error("Failed to create gig")}finally{r(!1)}};return a.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:a.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Create a New Gig"}),a.jsxs("form",{onSubmit:h(R),className:"space-y-8",children:[a.jsxs("div",{className:"space-y-6",children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Basic Information"}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Gig Title *"}),a.jsx("input",{...f("title"),type:"text",placeholder:"I will do something I'm really good at",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),w.title&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:w.title.message})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Category *"}),a.jsxs("select",{...f("category"),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[a.jsx("option",{value:"",children:"Select a category"}),b.map(E=>a.jsx("option",{value:E.value,children:E.label},E.value))]}),w.category&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:w.category.message})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Subcategory"}),a.jsx("input",{...f("subcategory"),type:"text",placeholder:"e.g., Logo Design",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Description *"}),a.jsx("textarea",{...f("description"),rows:6,placeholder:"Describe your service in detail...",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"}),w.description&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:w.description.message})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Tags *"}),a.jsx("div",{className:"flex flex-wrap gap-2 mb-3",children:i.map((E,A)=>a.jsxs("span",{className:"inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm",children:[E,a.jsx("button",{type:"button",onClick:()=>m(E),className:"ml-2 text-blue-600 hover:text-blue-800",children:a.jsx(Kn,{className:"w-4 h-4"})})]},A))}),a.jsxs("div",{className:"flex gap-2",children:[a.jsx("input",{type:"text",value:o,onChange:E=>c(E.target.value),onKeyPress:E=>E.key==="Enter"&&(E.preventDefault(),x()),placeholder:"Add a tag",className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),a.jsx("button",{type:"button",onClick:x,disabled:i.length>=5,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:"Add"})]}),w.tags&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:w.tags.message})]})]}),a.jsxs("div",{className:"space-y-6",children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Pricing & Packages"}),a.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Basic Package"}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Price ($) *"}),a.jsx("input",{...f("basicPrice",{valueAsNumber:!0}),type:"number",min:"5",max:"10000",placeholder:"25",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),w.basicPrice&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:w.basicPrice.message})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Delivery Time (days) *"}),a.jsx("input",{...f("basicDeliveryTime",{valueAsNumber:!0}),type:"number",min:"1",max:"30",placeholder:"3",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),w.basicDeliveryTime&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:w.basicDeliveryTime.message})]})]}),a.jsxs("div",{className:"mb-4",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Package Description *"}),a.jsx("textarea",{...f("basicDescription"),rows:3,placeholder:"Describe what's included in this package...",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"}),w.basicDescription&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:w.basicDescription.message})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Features Included *"}),a.jsxs("div",{className:"space-y-2",children:[u.map((E,A)=>a.jsxs("div",{className:"flex gap-2",children:[a.jsx("input",{type:"text",value:E,onChange:q=>N(A,q.target.value),placeholder:"e.g., High-resolution files",className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),u.length>1&&a.jsx("button",{type:"button",onClick:()=>_(A),className:"p-2 text-red-600 hover:text-red-800",children:a.jsx(Kn,{className:"w-5 h-5"})})]},A)),a.jsxs("button",{type:"button",onClick:g,className:"flex items-center text-blue-600 hover:text-blue-700",children:[a.jsx(Ma,{className:"w-4 h-4 mr-1"}),"Add Feature"]})]}),w.basicFeatures&&a.jsx("p",{className:"mt-1 text-sm text-red-600",children:w.basicFeatures.message})]})]})]}),a.jsxs("div",{className:"flex justify-end space-x-4",children:[a.jsx("button",{type:"button",onClick:()=>e("/dashboard/freelancer"),className:"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),a.jsx("button",{type:"submit",disabled:t,className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center",children:t?a.jsxs(a.Fragment,{children:[a.jsx(wn,{className:"w-5 h-5 mr-2 animate-spin"}),"Creating..."]}):"Create Gig"})]})]})]})})})},oj=()=>{const{id:e}=os(),t=xt(),[r,n]=j.useState(null),[s,i]=j.useState(!0);j.useEffect(()=>{l()},[e]);const l=async()=>{try{const o=await Nu.getById(e);n(o.data)}catch(o){console.error("Error fetching gig:",o),t("/dashboard/freelancer")}finally{i(!1)}};return s?a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx(dr,{size:"lg"})}):a.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:a.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Edit Gig"}),a.jsx("p",{className:"text-gray-600",children:"Edit gig functionality coming soon..."})]})})})},cj=()=>{var f,h,w,p,y,b,x,m,g,N;const{orderId:e}=os(),{user:t}=We(),[r,n]=j.useState(null),[s,i]=j.useState(!0);j.useState(!1),j.useState(!1),j.useState(!1),j.useState([]),j.useState(""),j.useState({rating:5,comment:""}),j.useEffect(()=>{l()},[e]);const l=async()=>{try{const _={_id:e,status:"in_progress",price:150,createdAt:new Date().toISOString(),gig:{title:"Professional Logo Design",images:[]},seller:{_id:"seller1",name:"John Designer",avatar:"/default-avatar.png"},buyer:{_id:"buyer1",name:"Jane Client",avatar:"/default-avatar.png"}};n(_)}catch(_){console.error("Error fetching order:",_),se.error("Failed to load order details")}finally{i(!1)}},o=_=>{switch(_){case"pending":return"bg-yellow-100 text-yellow-800";case"in_progress":return"bg-blue-100 text-blue-800";case"delivered":return"bg-purple-100 text-purple-800";case"completed":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},c=_=>{switch(_){case"pending":return a.jsx(pn,{className:"w-4 h-4"});case"in_progress":return a.jsx(mg,{className:"w-4 h-4"});case"delivered":return a.jsx(hg,{className:"w-4 h-4"});case"completed":return a.jsx(hn,{className:"w-4 h-4"});case"cancelled":return a.jsx(Kn,{className:"w-4 h-4"});default:return a.jsx(lg,{className:"w-4 h-4"})}},u=[{key:"pending",label:"Order Placed",description:"Waiting for seller confirmation"},{key:"in_progress",label:"In Progress",description:"Seller is working on your order"},{key:"delivered",label:"Delivered",description:"Work has been delivered"},{key:"completed",label:"Completed",description:"Order completed successfully"}];return(()=>r?u.findIndex(_=>_.key===r.status):0)(),(t==null?void 0:t.role)==="freelancer"&&((f=r==null?void 0:r.seller)==null||f._id,t==null||t._id),(t==null?void 0:t.role)==="client"&&((h=r==null?void 0:r.buyer)==null||h._id,t==null||t._id),s?a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-600",children:"Loading order details..."})]})}):r?a.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 py-8",children:a.jsxs("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:[a.jsx("div",{className:"mb-8",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsxs("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:["Order #",(w=r._id)==null?void 0:w.slice(-8)]}),a.jsxs("p",{className:"text-gray-600",children:["Order placed on ",new Date(r.createdAt).toLocaleDateString()]})]}),a.jsxs("div",{className:`px-4 py-2 rounded-full flex items-center space-x-2 ${o(r.status)}`,children:[c(r.status),a.jsx("span",{className:"font-medium capitalize",children:(p=r.status)==null?void 0:p.replace("_"," ")})]})]})}),a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[a.jsxs("div",{className:"lg:col-span-2 space-y-8",children:[a.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 transform hover:scale-105 transition-transform duration-300",children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Service Details"}),a.jsxs("div",{className:"flex items-start space-x-4",children:[a.jsx("div",{className:"w-20 h-20 bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg flex items-center justify-center",children:(b=(y=r.gig)==null?void 0:y.images)!=null&&b[0]?a.jsx("img",{src:r.gig.images[0],alt:r.gig.title,className:"w-full h-full object-cover rounded-lg"}):a.jsx("span",{className:"text-white text-2xl font-bold",children:(m=(x=r.gig)==null?void 0:x.title)==null?void 0:m.charAt(0)})}),a.jsxs("div",{className:"flex-1",children:[a.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:(g=r.gig)==null?void 0:g.title}),a.jsx("p",{className:"text-gray-600 text-sm mb-3",children:"Professional logo design service with unlimited revisions"}),a.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx(Ia,{className:"w-4 h-4 mr-1"}),a.jsxs("span",{children:["$",r.price]})]}),a.jsxs("div",{className:"flex items-center",children:[a.jsx(pn,{className:"w-4 h-4 mr-1"}),a.jsx("span",{children:"3 days delivery"})]})]})]})]})]}),a.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Actions"}),a.jsx("div",{className:"flex flex-wrap gap-3",children:a.jsxs(M,{to:"/messages",className:"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2 transform hover:scale-105",children:[a.jsx(_u,{className:"w-5 h-5"}),a.jsxs("span",{children:["Message ",(t==null?void 0:t.role)==="freelancer"?"Buyer":"Seller"]})]})})]})]}),a.jsx("div",{className:"lg:col-span-1 space-y-6",children:a.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 sticky top-8",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Order Summary"}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"Order ID:"}),a.jsxs("span",{className:"font-medium",children:["#",(N=r._id)==null?void 0:N.slice(-8)]})]}),a.jsxs("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"Price:"}),a.jsxs("span",{className:"font-medium text-green-600",children:["$",r.price]})]}),a.jsxs("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"Order Date:"}),a.jsx("span",{className:"font-medium",children:new Date(r.createdAt).toLocaleDateString()})]})]})]})})]})]})}):a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:"text-6xl mb-4",children:"📦"}),a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Order not found"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"The order you're looking for doesn't exist or you don't have access to it."}),a.jsx(M,{to:"/orders",className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Back to Orders"})]})})},uj=()=>{We();const[e,t]=j.useState([]),[r,n]=j.useState(!0),[s,i]=j.useState("grid"),[l,o]=j.useState("all"),[c,u]=j.useState(""),[d,f]=j.useState("date");j.useState([]),j.useEffect(()=>{h()},[]);const h=async()=>{try{const g=[{_id:"1",status:"completed",price:150,createdAt:new Date("2024-01-15").toISOString(),gig:{title:"Professional Logo Design",images:[]},seller:{_id:"seller1",name:"John Designer",avatar:"/default-avatar.png"},buyer:{_id:"buyer1",name:"Jane Client",avatar:"/default-avatar.png"}},{_id:"2",status:"in_progress",price:250,createdAt:new Date("2024-01-20").toISOString(),gig:{title:"Website Development",images:[]},seller:{_id:"seller2",name:"Mike Developer",avatar:"/default-avatar.png"},buyer:{_id:"buyer1",name:"Jane Client",avatar:"/default-avatar.png"}},{_id:"3",status:"delivered",price:75,createdAt:new Date("2024-01-25").toISOString(),gig:{title:"Content Writing",images:[]},seller:{_id:"seller3",name:"Sarah Writer",avatar:"/default-avatar.png"},buyer:{_id:"buyer1",name:"Jane Client",avatar:"/default-avatar.png"}}];t(g)}catch(g){console.error("Error fetching orders:",g),se.error("Failed to load orders")}finally{n(!1)}},w=()=>{const g=e.length,N=e.reduce((E,A)=>E+A.price,0),_=e.filter(E=>E.status==="completed").length,R=e.filter(E=>["pending","in_progress","delivered"].includes(E.status)).length;return{totalOrders:g,totalSpent:N,completedOrders:_,activeOrders:R}},p=g=>{switch(g){case"pending":return"bg-yellow-100 text-yellow-800";case"in_progress":return"bg-blue-100 text-blue-800";case"delivered":return"bg-purple-100 text-purple-800";case"completed":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},y=g=>{switch(g){case"pending":return a.jsx(pn,{className:"w-4 h-4"});case"in_progress":return a.jsx(mg,{className:"w-4 h-4"});case"delivered":return a.jsx(hg,{className:"w-4 h-4"});case"completed":return a.jsx(hn,{className:"w-4 h-4"});case"cancelled":return a.jsx(Kn,{className:"w-4 h-4"});default:return a.jsx(lg,{className:"w-4 h-4"})}},x=[...e.filter(g=>{const N=l==="all"||g.status===l,_=g.gig.title.toLowerCase().includes(c.toLowerCase())||g.seller.name.toLowerCase().includes(c.toLowerCase());return N&&_})].sort((g,N)=>{switch(d){case"date":return new Date(N.createdAt)-new Date(g.createdAt);case"price":return N.price-g.price;case"status":return g.status.localeCompare(N.status);default:return 0}}),m=w();return r?a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-600",children:"Loading your orders..."})]})}):a.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 py-8",children:a.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[a.jsxs("div",{className:"mb-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Order History"}),a.jsx("p",{className:"text-gray-600",children:"Track and manage all your orders"})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[a.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 transform hover:scale-105 transition-transform duration-300",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Orders"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:m.totalOrders})]}),a.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center",children:a.jsx(wb,{className:"w-6 h-6 text-blue-600"})})]})}),a.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 transform hover:scale-105 transition-transform duration-300",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Spent"}),a.jsxs("p",{className:"text-2xl font-bold text-gray-900",children:["$",m.totalSpent]})]}),a.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center",children:a.jsx(Ia,{className:"w-6 h-6 text-green-600"})})]})}),a.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 transform hover:scale-105 transition-transform duration-300",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Completed"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:m.completedOrders})]}),a.jsx("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center",children:a.jsx(hn,{className:"w-6 h-6 text-purple-600"})})]})}),a.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 transform hover:scale-105 transition-transform duration-300",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Active Orders"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:m.activeOrders})]}),a.jsx("div",{className:"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center",children:a.jsx(pn,{className:"w-6 h-6 text-orange-600"})})]})})]}),a.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8",children:a.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[a.jsxs("div",{className:"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4",children:[a.jsxs("div",{className:"relative",children:[a.jsx(sr,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),a.jsx("input",{type:"text",placeholder:"Search orders...",value:c,onChange:g=>u(g.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),a.jsxs("select",{value:l,onChange:g=>o(g.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[a.jsx("option",{value:"all",children:"All Status"}),a.jsx("option",{value:"pending",children:"Pending"}),a.jsx("option",{value:"in_progress",children:"In Progress"}),a.jsx("option",{value:"delivered",children:"Delivered"}),a.jsx("option",{value:"completed",children:"Completed"}),a.jsx("option",{value:"cancelled",children:"Cancelled"})]}),a.jsxs("select",{value:d,onChange:g=>f(g.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[a.jsx("option",{value:"date",children:"Sort by Date"}),a.jsx("option",{value:"price",children:"Sort by Price"}),a.jsx("option",{value:"status",children:"Sort by Status"})]})]}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx("button",{onClick:()=>i("grid"),className:`p-2 rounded-lg transition-colors ${s==="grid"?"bg-blue-100 text-blue-600":"text-gray-400 hover:text-gray-600"}`,children:a.jsx(ug,{className:"w-5 h-5"})}),a.jsx("button",{onClick:()=>i("list"),className:`p-2 rounded-lg transition-colors ${s==="list"?"bg-blue-100 text-blue-600":"text-gray-400 hover:text-gray-600"}`,children:a.jsx(fg,{className:"w-5 h-5"})})]})]})}),x.length===0?a.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center",children:[a.jsx("div",{className:"text-6xl mb-4",children:"📦"}),a.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No orders found"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"You haven't placed any orders yet or no orders match your filters."}),a.jsx(M,{to:"/gigs",className:"inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Browse Services"})]}):a.jsx("div",{className:s==="grid"?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:x.map(g=>a.jsx("div",{className:`bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow ${s==="list"?"flex items-center space-x-6":""}`,children:a.jsxs("div",{className:s==="list"?"flex-1":"",children:[a.jsxs("div",{className:"flex items-start justify-between mb-4",children:[a.jsxs("div",{children:[a.jsx("h3",{className:"font-semibold text-gray-900 mb-1",children:g.gig.title}),a.jsxs("p",{className:"text-sm text-gray-600",children:["Order #",g._id.slice(-8)]})]}),a.jsxs("div",{className:`px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${p(g.status)}`,children:[y(g.status),a.jsx("span",{className:"capitalize",children:g.status.replace("_"," ")})]})]}),a.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-500 mb-4",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx(za,{className:"w-4 h-4 mr-1"}),a.jsx("span",{children:g.seller.name})]}),a.jsxs("div",{className:"flex items-center",children:[a.jsx(Ia,{className:"w-4 h-4 mr-1"}),a.jsxs("span",{children:["$",g.price]})]}),a.jsxs("div",{className:"flex items-center",children:[a.jsx(og,{className:"w-4 h-4 mr-1"}),a.jsx("span",{children:new Date(g.createdAt).toLocaleDateString()})]})]}),a.jsxs("div",{className:"flex items-center space-x-3",children:[a.jsxs(M,{to:`/orders/${g._id}`,className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm",children:[a.jsx(Jn,{className:"w-4 h-4"}),a.jsx("span",{children:"View Details"})]}),a.jsxs(M,{to:"/messages",className:"flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm",children:[a.jsx(_u,{className:"w-4 h-4"}),a.jsx("span",{children:"Message"})]})]})]})},g._id))})]})})},dj=()=>{var d;const{user:e}=We(),[t,r]=j.useState({totalEarnings:0,activeOrders:0,totalGigs:0,averageRating:0}),[n,s]=j.useState([]),[i,l]=j.useState([]),[o,c]=j.useState(!0);j.useEffect(()=>{u()},[]);const u=async()=>{var f,h;try{const w=await Nu.getMyGigs();l(w.data.gigs||[]);const p=await ku.getMyOrders("freelancer");s(((f=p.data.orders)==null?void 0:f.slice(0,5))||[]),r({totalEarnings:2450,activeOrders:3,totalGigs:((h=w.data.gigs)==null?void 0:h.length)||0,averageRating:4.8})}catch(w){console.error("Error fetching dashboard data:",w)}finally{c(!1)}};return o?a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx(dr,{size:"lg"})}):a.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:a.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[a.jsxs("div",{className:"mb-8",children:[a.jsxs("h1",{className:"text-3xl font-bold text-gray-900",children:["Welcome back, ",(d=e==null?void 0:e.name)==null?void 0:d.split(" ")[0],"!"]}),a.jsx("p",{className:"text-gray-600 mt-2",children:"Here's what's happening with your freelance business"})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:a.jsx(Ia,{className:"w-6 h-6 text-green-600"})}),a.jsxs("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Earnings"}),a.jsxs("p",{className:"text-2xl font-bold text-gray-900",children:["$",t.totalEarnings]})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:a.jsx($a,{className:"w-6 h-6 text-blue-600"})}),a.jsxs("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Active Orders"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t.activeOrders})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:a.jsx(Ma,{className:"w-6 h-6 text-purple-600"})}),a.jsxs("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Gigs"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t.totalGigs})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-yellow-100 rounded-lg",children:a.jsx(zr,{className:"w-6 h-6 text-yellow-600"})}),a.jsxs("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Average Rating"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t.averageRating})]})]})})]}),a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[a.jsxs("div",{className:"flex items-center justify-between mb-6",children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"My Gigs"}),a.jsx(M,{to:"/gigs/create",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Create New Gig"})]}),i.length>0?a.jsx("div",{className:"space-y-4",children:i.slice(0,3).map(f=>{var h,w;return a.jsxs("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[a.jsxs("div",{className:"flex-1",children:[a.jsx("h3",{className:"font-medium text-gray-900",children:f.title}),a.jsxs("p",{className:"text-sm text-gray-600",children:["Starting at $",((w=(h=f.pricing)==null?void 0:h.basic)==null?void 0:w.price)||f.startingPrice]})]}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${f.status==="active"?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:f.status||"Active"}),a.jsx(M,{to:`/gigs/${f._id}/edit`,className:"text-blue-600 hover:text-blue-700 text-sm",children:"Edit"})]})]},f._id)})}):a.jsxs("div",{className:"text-center py-8",children:[a.jsx("p",{className:"text-gray-500 mb-4",children:"You haven't created any gigs yet"}),a.jsxs(M,{to:"/gigs/create",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[a.jsx(Ma,{className:"w-4 h-4 mr-2"}),"Create Your First Gig"]})]})]}),a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[a.jsxs("div",{className:"flex items-center justify-between mb-6",children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Recent Orders"}),a.jsx(M,{to:"/orders",className:"text-blue-600 hover:text-blue-700 text-sm",children:"View All"})]}),n.length>0?a.jsx("div",{className:"space-y-4",children:n.map(f=>{var h,w,p;return a.jsxs("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[a.jsxs("div",{className:"flex-1",children:[a.jsx("h3",{className:"font-medium text-gray-900",children:(h=f.gig)==null?void 0:h.title}),a.jsxs("p",{className:"text-sm text-gray-600",children:["From ",(w=f.buyer)==null?void 0:w.name]})]}),a.jsxs("div",{className:"text-right",children:[a.jsxs("p",{className:"font-medium text-gray-900",children:["$",f.price]}),a.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${f.status==="completed"?"bg-green-100 text-green-800":f.status==="in_progress"?"bg-blue-100 text-blue-800":"bg-yellow-100 text-yellow-800"}`,children:((p=f.status)==null?void 0:p.replace("_"," "))||"Pending"})]})]},f._id)})}):a.jsx("div",{className:"text-center py-8",children:a.jsx("p",{className:"text-gray-500",children:"No recent orders"})})]})]})]})})},fj=()=>{var c;const{user:e}=We(),[t,r]=j.useState({totalSpent:0,activeOrders:0,completedOrders:0,savedServices:0}),[n,s]=j.useState([]),[i,l]=j.useState(!0);j.useEffect(()=>{o()},[]);const o=async()=>{var u;try{const d=await ku.getMyOrders("client");s(((u=d.data.orders)==null?void 0:u.slice(0,5))||[]),r({totalSpent:1250,activeOrders:2,completedOrders:8,savedServices:5})}catch(d){console.error("Error fetching dashboard data:",d)}finally{l(!1)}};return i?a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx(dr,{size:"lg"})}):a.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:a.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[a.jsxs("div",{className:"mb-8",children:[a.jsxs("h1",{className:"text-3xl font-bold text-gray-900",children:["Welcome back, ",(c=e==null?void 0:e.name)==null?void 0:c.split(" ")[0],"!"]}),a.jsx("p",{className:"text-gray-600 mt-2",children:"Find the perfect freelance services for your projects"})]}),a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[a.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),a.jsxs("div",{className:"flex flex-wrap gap-4",children:[a.jsxs(M,{to:"/gigs",className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[a.jsx(sr,{className:"w-4 h-4 mr-2"}),"Browse Services"]}),a.jsxs(M,{to:"/orders",className:"flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:[a.jsx($a,{className:"w-4 h-4 mr-2"}),"My Orders"]}),a.jsx(M,{to:"/messages",className:"flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:"Messages"})]})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-green-100 rounded-lg",children:a.jsx($a,{className:"w-6 h-6 text-green-600"})}),a.jsxs("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Spent"}),a.jsxs("p",{className:"text-2xl font-bold text-gray-900",children:["$",t.totalSpent]})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-blue-100 rounded-lg",children:a.jsx(pn,{className:"w-6 h-6 text-blue-600"})}),a.jsxs("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Active Orders"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t.activeOrders})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-purple-100 rounded-lg",children:a.jsx(xb,{className:"w-6 h-6 text-purple-600"})}),a.jsxs("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Completed Orders"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t.completedOrders})]})]})}),a.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"p-2 bg-yellow-100 rounded-lg",children:a.jsx(sr,{className:"w-6 h-6 text-yellow-600"})}),a.jsxs("div",{className:"ml-4",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Saved Services"}),a.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t.savedServices})]})]})})]}),a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[a.jsxs("div",{className:"flex items-center justify-between mb-6",children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Recent Orders"}),a.jsx(M,{to:"/orders",className:"text-blue-600 hover:text-blue-700 text-sm",children:"View All"})]}),n.length>0?a.jsx("div",{className:"space-y-4",children:n.map(u=>{var d,f,h;return a.jsxs("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[a.jsxs("div",{className:"flex-1",children:[a.jsx("h3",{className:"font-medium text-gray-900",children:(d=u.gig)==null?void 0:d.title}),a.jsxs("p",{className:"text-sm text-gray-600",children:["From ",(f=u.seller)==null?void 0:f.name]})]}),a.jsxs("div",{className:"text-right",children:[a.jsxs("p",{className:"font-medium text-gray-900",children:["$",u.price]}),a.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${u.status==="completed"?"bg-green-100 text-green-800":u.status==="in_progress"?"bg-blue-100 text-blue-800":"bg-yellow-100 text-yellow-800"}`,children:((h=u.status)==null?void 0:h.replace("_"," "))||"Pending"})]})]},u._id)})}):a.jsxs("div",{className:"text-center py-8",children:[a.jsx("p",{className:"text-gray-500 mb-4",children:"No recent orders"}),a.jsxs(M,{to:"/gigs",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[a.jsx(sr,{className:"w-4 h-4 mr-2"}),"Browse Services"]})]})]}),a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[a.jsxs("div",{className:"flex items-center justify-between mb-6",children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Recommended for You"}),a.jsx(M,{to:"/gigs",className:"text-blue-600 hover:text-blue-700 text-sm",children:"View All"})]}),a.jsxs("div",{className:"text-center py-8",children:[a.jsx("p",{className:"text-gray-500 mb-4",children:"Personalized recommendations coming soon"}),a.jsxs(M,{to:"/gigs",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[a.jsx(sr,{className:"w-4 h-4 mr-2"}),"Explore Services"]})]})]})]})]})})},mj=()=>{var t,r;const{user:e}=We();return a.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:a.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:[a.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-8",children:a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsxs("div",{className:"flex items-center space-x-4",children:[a.jsx("img",{src:(e==null?void 0:e.avatar)||"/default-avatar.png",alt:e==null?void 0:e.name,className:"w-20 h-20 rounded-full border-4 border-white"}),a.jsxs("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-white",children:e==null?void 0:e.name}),a.jsx("p",{className:"text-blue-100 capitalize",children:e==null?void 0:e.role}),a.jsxs("div",{className:"flex items-center mt-2",children:[a.jsx(zr,{className:"w-4 h-4 text-yellow-400 fill-current mr-1"}),a.jsx("span",{className:"text-white text-sm",children:"4.9 (127 reviews)"})]})]})]}),a.jsxs(M,{to:"/profile/edit",className:"flex items-center px-4 py-2 bg-white text-blue-600 rounded-lg hover:bg-gray-100 transition-colors",children:[a.jsx(cg,{className:"w-4 h-4 mr-2"}),"Edit Profile"]})]})}),a.jsx("div",{className:"p-6",children:a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[a.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[a.jsxs("div",{children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"About"}),a.jsx("p",{className:"text-gray-700 leading-relaxed",children:(e==null?void 0:e.bio)||'No bio available. Click "Edit Profile" to add your bio.'})]}),a.jsxs("div",{children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Skills"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:((t=e==null?void 0:e.skills)==null?void 0:t.length)>0?e.skills.map((n,s)=>a.jsx("span",{className:"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm",children:n},s)):a.jsx("p",{className:"text-gray-500",children:"No skills added yet."})})]}),(e==null?void 0:e.role)==="freelancer"&&a.jsxs("div",{children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Languages"}),a.jsx("div",{className:"space-y-2",children:((r=e==null?void 0:e.languages)==null?void 0:r.length)>0?e.languages.map((n,s)=>a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-gray-700",children:n.name}),a.jsx("span",{className:"text-sm text-gray-500 capitalize",children:n.level})]},s)):a.jsx("p",{className:"text-gray-500",children:"No languages added yet."})})]})]}),a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Contact Info"}),a.jsxs("div",{className:"space-y-3",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx(ds,{className:"w-5 h-5 text-gray-400 mr-3"}),a.jsx("span",{className:"text-gray-700",children:e==null?void 0:e.email})]}),(e==null?void 0:e.location)&&a.jsxs("div",{className:"flex items-center",children:[a.jsx(Su,{className:"w-5 h-5 text-gray-400 mr-3"}),a.jsx("span",{className:"text-gray-700",children:e.location})]}),a.jsxs("div",{className:"flex items-center",children:[a.jsx(og,{className:"w-5 h-5 text-gray-400 mr-3"}),a.jsxs("span",{className:"text-gray-700",children:["Member since ",new Date(e==null?void 0:e.createdAt).toLocaleDateString()]})]})]})]}),(e==null?void 0:e.role)==="freelancer"&&a.jsxs("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Stats"}),a.jsxs("div",{className:"space-y-3",children:[a.jsxs("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"Response time:"}),a.jsx("span",{className:"font-medium",children:"1 hour"})]}),a.jsxs("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"Recent delivery:"}),a.jsx("span",{className:"font-medium",children:"1 day"})]}),a.jsxs("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"Orders completed:"}),a.jsx("span",{className:"font-medium",children:(e==null?void 0:e.completedOrders)||0})]})]})]}),a.jsxs("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Verification"}),a.jsxs("div",{className:"space-y-2",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"Email"}),a.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${e!=null&&e.isEmailVerified?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e!=null&&e.isEmailVerified?"Verified":"Not Verified"})]}),a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("span",{className:"text-gray-600",children:"Phone"}),a.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${e!=null&&e.isPhoneVerified?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e!=null&&e.isPhoneVerified?"Verified":"Not Verified"})]})]})]})]})]})})]})})})},hj=()=>{var c;const e=xt(),{user:t,updateProfile:r,loading:n}=We(),[s,i]=j.useState({name:(t==null?void 0:t.name)||"",bio:(t==null?void 0:t.bio)||"",location:(t==null?void 0:t.location)||"",skills:((c=t==null?void 0:t.skills)==null?void 0:c.join(", "))||"",languages:(t==null?void 0:t.languages)||[]}),l=async u=>{u.preventDefault();const d={...s,skills:s.skills.split(",").map(h=>h.trim()).filter(Boolean)};(await r(d)).success&&e("/profile")},o=u=>{i({...s,[u.target.name]:u.target.value})};return a.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:a.jsx("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Edit Profile"}),a.jsxs("form",{onSubmit:l,className:"space-y-6",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name"}),a.jsx("input",{type:"text",name:"name",value:s.name,onChange:o,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Bio"}),a.jsx("textarea",{name:"bio",value:s.bio,onChange:o,rows:4,placeholder:"Tell us about yourself...",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Location"}),a.jsx("input",{type:"text",name:"location",value:s.location,onChange:o,placeholder:"City, Country",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Skills"}),a.jsx("input",{type:"text",name:"skills",value:s.skills,onChange:o,placeholder:"JavaScript, React, Node.js (comma separated)",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),a.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Separate skills with commas"})]}),a.jsxs("div",{className:"flex justify-end space-x-4",children:[a.jsx("button",{type:"button",onClick:()=>e("/profile"),className:"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),a.jsx("button",{type:"submit",disabled:n,className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center",children:n?a.jsxs(a.Fragment,{children:[a.jsx(wn,{className:"w-5 h-5 mr-2 animate-spin"}),"Saving..."]}):a.jsxs(a.Fragment,{children:[a.jsx(Tb,{className:"w-5 h-5 mr-2"}),"Save Changes"]})})]})]})]})})})},tm=()=>{const{conversationId:e}=os(),[t,r]=j.useState([]),[n,s]=j.useState(null),[i,l]=j.useState([]),[o,c]=j.useState(""),[u,d]=j.useState(!0);j.useEffect(()=>{f()},[]),j.useEffect(()=>{e&&h(e)},[e]);const f=async()=>{try{const p=await no.getConversations();r(p.data.conversations||[])}catch(p){console.error("Error fetching conversations:",p)}finally{d(!1)}},h=async p=>{try{const y=await no.getMessages(p);l(y.data.messages||[]),s(p)}catch(y){console.error("Error fetching messages:",y)}},w=async p=>{if(p.preventDefault(),!(!o.trim()||!n))try{await no.sendMessage(n,{content:o.trim()}),c(""),h(n)}catch(y){console.error("Error sending message:",y)}};return u?a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx(dr,{size:"lg"})}):a.jsx("div",{className:"min-h-screen bg-gray-50",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:a.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden h-[600px] flex",children:[a.jsxs("div",{className:"w-1/3 border-r border-gray-200",children:[a.jsx("div",{className:"p-4 border-b border-gray-200",children:a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Messages"})}),a.jsx("div",{className:"overflow-y-auto h-full",children:t.length>0?t.map(p=>{var y,b,x,m;return a.jsx("button",{onClick:()=>h(p._id),className:`w-full p-4 text-left hover:bg-gray-50 border-b border-gray-100 ${n===p._id?"bg-blue-50":""}`,children:a.jsxs("div",{className:"flex items-center space-x-3",children:[a.jsx("img",{src:((y=p.otherUser)==null?void 0:y.avatar)||"/default-avatar.png",alt:(b=p.otherUser)==null?void 0:b.name,className:"w-10 h-10 rounded-full"}),a.jsxs("div",{className:"flex-1 min-w-0",children:[a.jsx("p",{className:"font-medium text-gray-900 truncate",children:(x=p.otherUser)==null?void 0:x.name}),a.jsx("p",{className:"text-sm text-gray-500 truncate",children:((m=p.lastMessage)==null?void 0:m.content)||"No messages yet"})]})]})},p._id)}):a.jsx("div",{className:"p-4 text-center text-gray-500",children:"No conversations yet"})})]}),a.jsx("div",{className:"flex-1 flex flex-col",children:n?a.jsxs(a.Fragment,{children:[a.jsx("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:i.map(p=>a.jsx("div",{className:`flex ${p.sender._id==="currentUserId"?"justify-end":"justify-start"}`,children:a.jsxs("div",{className:`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${p.sender._id==="currentUserId"?"bg-blue-600 text-white":"bg-gray-200 text-gray-900"}`,children:[a.jsx("p",{children:p.content}),a.jsx("p",{className:`text-xs mt-1 ${p.sender._id==="currentUserId"?"text-blue-100":"text-gray-500"}`,children:new Date(p.createdAt).toLocaleTimeString()})]})},p._id))}),a.jsx("form",{onSubmit:w,className:"p-4 border-t border-gray-200",children:a.jsxs("div",{className:"flex space-x-2",children:[a.jsx("button",{type:"button",className:"p-2 text-gray-400 hover:text-gray-600",children:a.jsx(Cb,{className:"w-5 h-5"})}),a.jsx("input",{type:"text",value:o,onChange:p=>c(p.target.value),placeholder:"Type a message...",className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),a.jsx("button",{type:"submit",disabled:!o.trim(),className:"p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:a.jsx(Rb,{className:"w-5 h-5"})})]})})]}):a.jsx("div",{className:"flex-1 flex items-center justify-center",children:a.jsx("div",{className:"text-center",children:a.jsx("p",{className:"text-gray-500 mb-4",children:"Select a conversation to start messaging"})})})})]})})})},pj=()=>{const{isAuthenticated:e,loading:t}=We(),r=hr();return t?a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx(dr,{size:"lg",text:"Loading..."})}):e?a.jsx(Sp,{}):a.jsx(Bt,{to:"/login",state:{from:r},replace:!0})},rm=({allowedRoles:e=[]})=>{const{user:t,isAuthenticated:r}=We();if(!r)return a.jsx(Bt,{to:"/login",replace:!0});if(!e.includes(t==null?void 0:t.role)){const n=(t==null?void 0:t.role)==="freelancer"?"/dashboard/freelancer":"/dashboard/client";return a.jsx(Bt,{to:n,replace:!0})}return a.jsx(Sp,{})};function gj(){const{user:e,loading:t,checkAuth:r}=We();return j.useEffect(()=>{r()},[r]),t?a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-blue-500 text-white",children:a.jsxs("div",{className:"text-center",children:[a.jsx(dr,{size:"lg"}),a.jsx("p",{className:"mt-4",children:"Loading FreelanceHub..."})]})}):a.jsxs("div",{className:"min-h-screen bg-gray-50 flex flex-col",children:[a.jsx($b,{}),a.jsx("main",{className:"flex-1",children:a.jsxs(ev,{children:[a.jsx(we,{path:"/",element:a.jsx(Gb,{})}),a.jsx(we,{path:"/gigs",element:a.jsx(nj,{})}),a.jsx(we,{path:"/gigs/:slug",element:a.jsx(ij,{})}),a.jsx(we,{path:"/login",element:e?a.jsx(Bt,{to:"/",replace:!0}):a.jsx(q2,{})}),a.jsx(we,{path:"/register",element:e?a.jsx(Bt,{to:"/",replace:!0}):a.jsx(J2,{})}),a.jsx(we,{path:"/forgot-password",element:e?a.jsx(Bt,{to:"/",replace:!0}):a.jsx(Y2,{})}),a.jsx(we,{path:"/reset-password/:token",element:e?a.jsx(Bt,{to:"/",replace:!0}):a.jsx(ej,{})}),a.jsx(we,{path:"/verify-email/:token",element:a.jsx(tj,{})}),a.jsxs(we,{element:a.jsx(pj,{}),children:[a.jsx(we,{path:"/profile",element:a.jsx(mj,{})}),a.jsx(we,{path:"/profile/edit",element:a.jsx(hj,{})}),a.jsx(we,{path:"/messages",element:a.jsx(tm,{})}),a.jsx(we,{path:"/messages/:conversationId",element:a.jsx(tm,{})}),a.jsx(we,{path:"/orders",element:a.jsx(uj,{})}),a.jsx(we,{path:"/orders/:orderId",element:a.jsx(cj,{})}),a.jsx(we,{path:"/dashboard",element:(e==null?void 0:e.role)==="freelancer"?a.jsx(Bt,{to:"/dashboard/freelancer",replace:!0}):a.jsx(Bt,{to:"/dashboard/client",replace:!0})}),a.jsxs(we,{element:a.jsx(rm,{allowedRoles:["freelancer"]}),children:[a.jsx(we,{path:"/dashboard/freelancer",element:a.jsx(dj,{})}),a.jsx(we,{path:"/gigs/create",element:a.jsx(lj,{})}),a.jsx(we,{path:"/gigs/:id/edit",element:a.jsx(oj,{})})]}),a.jsx(we,{element:a.jsx(rm,{allowedRoles:["client"]}),children:a.jsx(we,{path:"/dashboard/client",element:a.jsx(fj,{})})})]}),a.jsx(we,{path:"*",element:a.jsx(Bt,{to:"/",replace:!0})})]})}),a.jsx(zb,{})]})}ao.createRoot(document.getElementById("root")).render(a.jsx(Ne.StrictMode,{children:a.jsxs(ov,{children:[a.jsx(gj,{}),a.jsx(t1,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,theme:{primary:"#4aed88"}},error:{duration:5e3,theme:{primary:"#ff6b6b"}}}})]})}));
//# sourceMappingURL=index-7cc38d97.js.map
