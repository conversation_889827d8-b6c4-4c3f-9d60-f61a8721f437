{"version": 3, "file": "dash.reporting.min.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAgB,OAAID,IAEpBD,EAAa,OAAIC,GAClB,CATD,CASGK,MAAM,WACT,O,gDCgBA,SAASC,EAAWC,GAClB,GAAoB,iBAATA,EACT,MAAM,IAAIC,UAAU,mCAAqCC,KAAKC,UAAUH,GAE5E,CAGA,SAASI,EAAqBJ,EAAMK,GAMlC,IALA,IAIIC,EAJAC,EAAM,GACNC,EAAoB,EACpBC,GAAa,EACbC,EAAO,EAEFC,EAAI,EAAGA,GAAKX,EAAKY,SAAUD,EAAG,CACrC,GAAIA,EAAIX,EAAKY,OACXN,EAAON,EAAKa,WAAWF,OACpB,IAAa,KAATL,EACP,MAEAA,EAAO,EAAQ,CACjB,GAAa,KAATA,EAAmB,CACrB,GAAIG,IAAcE,EAAI,GAAc,IAATD,QAEpB,GAAID,IAAcE,EAAI,GAAc,IAATD,EAAY,CAC5C,GAAIH,EAAIK,OAAS,GAA2B,IAAtBJ,GAA8D,KAAnCD,EAAIM,WAAWN,EAAIK,OAAS,IAAsD,KAAnCL,EAAIM,WAAWN,EAAIK,OAAS,GAC1H,GAAIL,EAAIK,OAAS,EAAG,CAClB,IAAIE,EAAiBP,EAAIQ,YAAY,KACrC,GAAID,IAAmBP,EAAIK,OAAS,EAAG,EACb,IAApBE,GACFP,EAAM,GACNC,EAAoB,GAGpBA,GADAD,EAAMA,EAAIS,MAAM,EAAGF,IACKF,OAAS,EAAIL,EAAIQ,YAAY,KAEvDN,EAAYE,EACZD,EAAO,EACP,QACF,CACF,MAAO,GAAmB,IAAfH,EAAIK,QAA+B,IAAfL,EAAIK,OAAc,CAC/CL,EAAM,GACNC,EAAoB,EACpBC,EAAYE,EACZD,EAAO,EACP,QACF,CAEEL,IACEE,EAAIK,OAAS,EACfL,GAAO,MAEPA,EAAM,KACRC,EAAoB,EAExB,MACMD,EAAIK,OAAS,EACfL,GAAO,IAAMP,EAAKgB,MAAMP,EAAY,EAAGE,GAEvCJ,EAAMP,EAAKgB,MAAMP,EAAY,EAAGE,GAClCH,EAAoBG,EAAIF,EAAY,EAEtCA,EAAYE,EACZD,EAAO,CACT,MAAoB,KAATJ,IAA+B,IAAVI,IAC5BA,EAEFA,GAAQ,CAEZ,CACA,OAAOH,CACT,CAcA,IAAIU,EAAQ,CAEVC,QAAS,WAKP,IAJA,IAEIC,EAFAC,EAAe,GACfC,GAAmB,EAGdV,EAAIW,UAAUV,OAAS,EAAGD,IAAM,IAAMU,EAAkBV,IAAK,CACpE,IAAIX,EACAW,GAAK,EACPX,EAAOsB,UAAUX,SAELY,IAARJ,IACFA,EAAMK,QAAQL,OAChBnB,EAAOmB,GAGTpB,EAAWC,GAGS,IAAhBA,EAAKY,SAITQ,EAAepB,EAAO,IAAMoB,EAC5BC,EAA0C,KAAvBrB,EAAKa,WAAW,GACrC,CAQA,OAFAO,EAAehB,EAAqBgB,GAAeC,GAE/CA,EACED,EAAaR,OAAS,EACjB,IAAMQ,EAEN,IACAA,EAAaR,OAAS,EACxBQ,EAEA,GAEX,EAEAK,UAAW,SAAmBzB,GAG5B,GAFAD,EAAWC,GAES,IAAhBA,EAAKY,OAAc,MAAO,IAE9B,IAAIc,EAAoC,KAAvB1B,EAAKa,WAAW,GAC7Bc,EAAyD,KAArC3B,EAAKa,WAAWb,EAAKY,OAAS,GAQtD,OAHoB,KAFpBZ,EAAOI,EAAqBJ,GAAO0B,IAE1Bd,QAAiBc,IAAY1B,EAAO,KACzCA,EAAKY,OAAS,GAAKe,IAAmB3B,GAAQ,KAE9C0B,EAAmB,IAAM1B,EACtBA,CACT,EAEA0B,WAAY,SAAoB1B,GAE9B,OADAD,EAAWC,GACJA,EAAKY,OAAS,GAA4B,KAAvBZ,EAAKa,WAAW,EAC5C,EAEAe,KAAM,WACJ,GAAyB,IAArBN,UAAUV,OACZ,MAAO,IAET,IADA,IAAIiB,EACKlB,EAAI,EAAGA,EAAIW,UAAUV,SAAUD,EAAG,CACzC,IAAImB,EAAMR,UAAUX,GACpBZ,EAAW+B,GACPA,EAAIlB,OAAS,SACAW,IAAXM,EACFA,EAASC,EAETD,GAAU,IAAMC,EAEtB,CACA,YAAeP,IAAXM,EACK,IACFZ,EAAMQ,UAAUI,EACzB,EAEAE,SAAU,SAAkBC,EAAMC,GAIhC,GAHAlC,EAAWiC,GACXjC,EAAWkC,GAEPD,IAASC,EAAI,MAAO,GAKxB,IAHAD,EAAOf,EAAMC,QAAQc,OACrBC,EAAKhB,EAAMC,QAAQe,IAEF,MAAO,GAIxB,IADA,IAAIC,EAAY,EACTA,EAAYF,EAAKpB,QACa,KAA/BoB,EAAKnB,WAAWqB,KADYA,GASlC,IALA,IAAIC,EAAUH,EAAKpB,OACfwB,EAAUD,EAAUD,EAGpBG,EAAU,EACPA,EAAUJ,EAAGrB,QACa,KAA3BqB,EAAGpB,WAAWwB,KADUA,GAW9B,IAPA,IACIC,EADQL,EAAGrB,OACKyB,EAGhBzB,EAASwB,EAAUE,EAAQF,EAAUE,EACrCC,GAAiB,EACjB5B,EAAI,EACDA,GAAKC,IAAUD,EAAG,CACvB,GAAIA,IAAMC,EAAQ,CAChB,GAAI0B,EAAQ1B,EAAQ,CAClB,GAAmC,KAA/BqB,EAAGpB,WAAWwB,EAAU1B,GAG1B,OAAOsB,EAAGjB,MAAMqB,EAAU1B,EAAI,GACzB,GAAU,IAANA,EAGT,OAAOsB,EAAGjB,MAAMqB,EAAU1B,EAE9B,MAAWyB,EAAUxB,IACoB,KAAnCoB,EAAKnB,WAAWqB,EAAYvB,GAG9B4B,EAAgB5B,EACD,IAANA,IAGT4B,EAAgB,IAGpB,KACF,CACA,IAAIC,EAAWR,EAAKnB,WAAWqB,EAAYvB,GAE3C,GAAI6B,IADSP,EAAGpB,WAAWwB,EAAU1B,GAEnC,MACoB,KAAb6B,IACPD,EAAgB5B,EACpB,CAEA,IAAI8B,EAAM,GAGV,IAAK9B,EAAIuB,EAAYK,EAAgB,EAAG5B,GAAKwB,IAAWxB,EAClDA,IAAMwB,GAAkC,KAAvBH,EAAKnB,WAAWF,KAChB,IAAf8B,EAAI7B,OACN6B,GAAO,KAEPA,GAAO,OAMb,OAAIA,EAAI7B,OAAS,EACR6B,EAAMR,EAAGjB,MAAMqB,EAAUE,IAEhCF,GAAWE,EACoB,KAA3BN,EAAGpB,WAAWwB,MACdA,EACGJ,EAAGjB,MAAMqB,GAEpB,EAEAK,UAAW,SAAmB1C,GAC5B,OAAOA,CACT,EAEA2C,QAAS,SAAiB3C,GAExB,GADAD,EAAWC,GACS,IAAhBA,EAAKY,OAAc,MAAO,IAK9B,IAJA,IAAIN,EAAON,EAAKa,WAAW,GACvB+B,EAAmB,KAATtC,EACVuC,GAAO,EACPC,GAAe,EACVnC,EAAIX,EAAKY,OAAS,EAAGD,GAAK,IAAKA,EAEtC,GAAa,MADbL,EAAON,EAAKa,WAAWF,KAEnB,IAAKmC,EAAc,CACjBD,EAAMlC,EACN,KACF,OAGFmC,GAAe,EAInB,OAAa,IAATD,EAAmBD,EAAU,IAAM,IACnCA,GAAmB,IAARC,EAAkB,KAC1B7C,EAAKgB,MAAM,EAAG6B,EACvB,EAEAE,SAAU,SAAkB/C,EAAMgD,GAChC,QAAYzB,IAARyB,GAAoC,iBAARA,EAAkB,MAAM,IAAI/C,UAAU,mCACtEF,EAAWC,GAEX,IAGIW,EAHAsC,EAAQ,EACRJ,GAAO,EACPC,GAAe,EAGnB,QAAYvB,IAARyB,GAAqBA,EAAIpC,OAAS,GAAKoC,EAAIpC,QAAUZ,EAAKY,OAAQ,CACpE,GAAIoC,EAAIpC,SAAWZ,EAAKY,QAAUoC,IAAQhD,EAAM,MAAO,GACvD,IAAIkD,EAASF,EAAIpC,OAAS,EACtBuC,GAAoB,EACxB,IAAKxC,EAAIX,EAAKY,OAAS,EAAGD,GAAK,IAAKA,EAAG,CACrC,IAAIL,EAAON,EAAKa,WAAWF,GAC3B,GAAa,KAATL,GAGA,IAAKwC,EAAc,CACjBG,EAAQtC,EAAI,EACZ,KACF,OAEwB,IAAtBwC,IAGFL,GAAe,EACfK,EAAmBxC,EAAI,GAErBuC,GAAU,IAER5C,IAAS0C,EAAInC,WAAWqC,IACR,KAAZA,IAGJL,EAAMlC,IAKRuC,GAAU,EACVL,EAAMM,GAId,CAGA,OADIF,IAAUJ,EAAKA,EAAMM,GAAmC,IAATN,IAAYA,EAAM7C,EAAKY,QACnEZ,EAAKgB,MAAMiC,EAAOJ,EAC3B,CACE,IAAKlC,EAAIX,EAAKY,OAAS,EAAGD,GAAK,IAAKA,EAClC,GAA2B,KAAvBX,EAAKa,WAAWF,IAGhB,IAAKmC,EAAc,CACjBG,EAAQtC,EAAI,EACZ,KACF,OACkB,IAATkC,IAGXC,GAAe,EACfD,EAAMlC,EAAI,GAId,OAAa,IAATkC,EAAmB,GAChB7C,EAAKgB,MAAMiC,EAAOJ,EAE7B,EAEAO,QAAS,SAAiBpD,GACxBD,EAAWC,GAQX,IAPA,IAAIqD,GAAY,EACZC,EAAY,EACZT,GAAO,EACPC,GAAe,EAGfS,EAAc,EACT5C,EAAIX,EAAKY,OAAS,EAAGD,GAAK,IAAKA,EAAG,CACzC,IAAIL,EAAON,EAAKa,WAAWF,GAC3B,GAAa,KAATL,GASS,IAATuC,IAGFC,GAAe,EACfD,EAAMlC,EAAI,GAEC,KAATL,GAEkB,IAAd+C,EACFA,EAAW1C,EACY,IAAhB4C,IACPA,EAAc,IACK,IAAdF,IAGTE,GAAe,QArBb,IAAKT,EAAc,CACjBQ,EAAY3C,EAAI,EAChB,KACF,CAoBN,CAEA,OAAkB,IAAd0C,IAA4B,IAATR,GAEH,IAAhBU,GAEgB,IAAhBA,GAAqBF,IAAaR,EAAM,GAAKQ,IAAaC,EAAY,EACjE,GAEFtD,EAAKgB,MAAMqC,EAAUR,EAC9B,EAEAW,OAAQ,SAAgBC,GACtB,GAAmB,OAAfA,GAA6C,iBAAfA,EAChC,MAAM,IAAIxD,UAAU,0EAA4EwD,GAElG,OAvVJ,SAAiBC,EAAKD,GACpB,IAAIE,EAAMF,EAAWE,KAAOF,EAAWjE,KACnCoE,EAAOH,EAAWG,OAASH,EAAWI,MAAQ,KAAOJ,EAAWT,KAAO,IAC3E,OAAKW,EAGDA,IAAQF,EAAWjE,KACdmE,EAAMC,EAERD,EA8UU,IA9UEC,EALVA,CAMX,CA6UWE,CAAQ,EAAKL,EACtB,EAEAM,MAAO,SAAe/D,GACpBD,EAAWC,GAEX,IAAIgE,EAAM,CAAExE,KAAM,GAAImE,IAAK,GAAIC,KAAM,GAAIZ,IAAK,GAAIa,KAAM,IACxD,GAAoB,IAAhB7D,EAAKY,OAAc,OAAOoD,EAC9B,IAEIf,EAFA3C,EAAON,EAAKa,WAAW,GACvBa,EAAsB,KAATpB,EAEboB,GACFsC,EAAIxE,KAAO,IACXyD,EAAQ,GAERA,EAAQ,EAaV,IAXA,IAAII,GAAY,EACZC,EAAY,EACZT,GAAO,EACPC,GAAe,EACfnC,EAAIX,EAAKY,OAAS,EAIlB2C,EAAc,EAGX5C,GAAKsC,IAAStC,EAEnB,GAAa,MADbL,EAAON,EAAKa,WAAWF,KAUV,IAATkC,IAGFC,GAAe,EACfD,EAAMlC,EAAI,GAEC,KAATL,GAEkB,IAAd+C,EAAiBA,EAAW1C,EAA2B,IAAhB4C,IAAmBA,EAAc,IACrD,IAAdF,IAGXE,GAAe,QAlBb,IAAKT,EAAc,CACjBQ,EAAY3C,EAAI,EAChB,KACF,CAwCN,OArBkB,IAAd0C,IAA4B,IAATR,GAEP,IAAhBU,GAEgB,IAAhBA,GAAqBF,IAAaR,EAAM,GAAKQ,IAAaC,EAAY,GACvD,IAATT,IACiCmB,EAAIJ,KAAOI,EAAIH,KAAhC,IAAdP,GAAmB5B,EAAkC1B,EAAKgB,MAAM,EAAG6B,GAAgC7C,EAAKgB,MAAMsC,EAAWT,KAG7G,IAAdS,GAAmB5B,GACrBsC,EAAIH,KAAO7D,EAAKgB,MAAM,EAAGqC,GACzBW,EAAIJ,KAAO5D,EAAKgB,MAAM,EAAG6B,KAEzBmB,EAAIH,KAAO7D,EAAKgB,MAAMsC,EAAWD,GACjCW,EAAIJ,KAAO5D,EAAKgB,MAAMsC,EAAWT,IAEnCmB,EAAIhB,IAAMhD,EAAKgB,MAAMqC,EAAUR,IAG7BS,EAAY,EAAGU,EAAIL,IAAM3D,EAAKgB,MAAM,EAAGsC,EAAY,GAAY5B,IAAYsC,EAAIL,IAAM,KAElFK,CACT,EAEAN,IAAK,IACLO,UAAW,IACXC,MAAO,KACPjD,MAAO,MAGTA,EAAMA,MAAQA,EAEdtB,EAAOD,QAAUuB,C,uBChhBjB,OAUA,SAAWkD,EAAQ5C,GAEf,aAOA,IAGI6C,EAAc,WACdC,EAAc,YACdC,EAAc,SACdC,EAAc,SACdC,EAAc,QACdC,EAAc,QACdC,EAAc,OACdC,EAAc,OACdC,EAAc,SACdC,EAAc,UACdC,EAAc,eACdC,EAAc,UACdC,EAAc,SACdC,EAAc,SACdC,EAAc,UACdC,EAAc,WACdC,EAAc,WAGdC,EAAU,SACVC,EAAU,QACVC,EAAU,OACVC,EAAa,aACbC,EAAU,UACVC,EAAU,SAEVC,EAAU,UACVC,EAAU,SACVC,EAAU,SACVC,EAAU,KACVC,EAAY,YACZC,EAAY,WACZC,EAAU,QACVC,EAAU,UACVC,EAAU,QACVC,EAAU,OACVC,EAAU,SACVC,EAAU,QACVC,EAAc,WACdC,EAAc,cACdC,EAAU,SAiBVC,EAAY,SAAUC,GAElB,IADA,IAAIC,EAAQ,CAAC,EACJjG,EAAE,EAAGA,EAAEgG,EAAI/F,OAAQD,IACxBiG,EAAMD,EAAIhG,GAAGkG,eAAiBF,EAAIhG,GAEtC,OAAOiG,CACX,EACAE,EAAM,SAAUC,EAAMC,GAClB,cAAcD,IAASxC,IAAuD,IAA5C0C,EAASD,GAAME,QAAQD,EAASF,GACtE,EACAE,EAAW,SAAUE,GACjB,OAAOA,EAAIC,aACf,EAIAC,EAAO,SAAUF,EAAKG,GAClB,UAAWH,IAAS5C,EAEhB,OADA4C,EAAMA,EAAII,QAAQ,SA7EZ,WA8EQD,IAASjD,EAAa8C,EAAMA,EAAIK,UAAU,EA3DhD,IA6DpB,EAMIC,EAAY,SAAUC,EAAIC,GAKtB,IAHA,IAAWC,EAAGC,EAAGC,EAAGC,EAAGC,EAASC,EAA5BtH,EAAI,EAGDA,EAAIgH,EAAO/G,SAAWoH,GAAS,CAElC,IAAIE,EAAQP,EAAOhH,GACfwH,EAAQR,EAAOhH,EAAI,GAIvB,IAHAiH,EAAIC,EAAI,EAGDD,EAAIM,EAAMtH,SAAWoH,GAEnBE,EAAMN,IAGX,GAFAI,EAAUE,EAAMN,KAAKQ,KAAKV,GAGtB,IAAKI,EAAI,EAAGA,EAAIK,EAAMvH,OAAQkH,IAC1BG,EAAQD,IAAUH,UAClBE,EAAII,EAAML,MAEOxD,GAAYyD,EAAEnH,OAAS,EACnB,IAAbmH,EAAEnH,cACSmH,EAAE,IAAM3D,EAEfiE,KAAKN,EAAE,IAAMA,EAAE,GAAGO,KAAKD,KAAMJ,GAG7BI,KAAKN,EAAE,IAAMA,EAAE,GAEC,IAAbA,EAAEnH,cAEEmH,EAAE,KAAO3D,GAAe2D,EAAE,GAAGK,MAAQL,EAAE,GAAGQ,KAKjDF,KAAKN,EAAE,IAAME,EAAQA,EAAMV,QAAQQ,EAAE,GAAIA,EAAE,IAAMxG,EAHjD8G,KAAKN,EAAE,IAAME,EAAQF,EAAE,GAAGO,KAAKD,KAAMJ,EAAOF,EAAE,IAAMxG,EAKpC,IAAbwG,EAAEnH,SACLyH,KAAKN,EAAE,IAAME,EAAQF,EAAE,GAAGO,KAAKD,KAAMJ,EAAMV,QAAQQ,EAAE,GAAIA,EAAE,KAAOxG,GAG1E8G,KAAKN,GAAKE,GAAgB1G,EAK1CZ,GAAK,CACT,CACJ,EAEA6H,EAAY,SAAUrB,EAAKsB,GAEvB,IAAK,IAAI9H,KAAK8H,EAEV,UAAWA,EAAI9H,KAAO2D,GAAYmE,EAAI9H,GAAGC,OAAS,GAC9C,IAAK,IAAIgH,EAAI,EAAGA,EAAIa,EAAI9H,GAAGC,OAAQgH,IAC/B,GAAId,EAAI2B,EAAI9H,GAAGiH,GAAIT,GACf,MAjJN,MAiJcxG,EAAiBY,EAAYZ,OAG1C,GAAImG,EAAI2B,EAAI9H,GAAIwG,GACnB,MArJE,MAqJMxG,EAAiBY,EAAYZ,EAG7C,OAAOwG,CACf,EAiBIuB,EAAoB,CAChB,GAAc,OACd,UAAc,SACd,SAAc,QACd,IAAc,SACd,GAAc,CAAC,SAAU,UACzB,MAAc,SACd,EAAc,SACd,EAAc,SACd,IAAc,SACd,GAAc,CAAC,SAAU,WACzB,GAAc,OAOlBC,EAAU,CAEVC,QAAU,CAAC,CAEP,gCACG,CAAC/D,EAAS,CAACH,EAAM,WAAY,CAChC,+BACG,CAACG,EAAS,CAACH,EAAM,SAAU,CAG9B,4BACA,mDACA,2CACG,CAACA,EAAMG,GAAU,CACpB,yBACG,CAACA,EAAS,CAACH,EAAMuB,EAAM,UAAW,CACrC,4BACG,CAACpB,EAAS,CAACH,EAAMuB,EAAM,QAAS,CACnC,qBACG,CAACpB,EAAS,CAACH,EAAMuB,IAAS,CAG7B,0DACG,CAACpB,EAAS,CAACH,EAAM,UAAW,CAC/B,uBACA,8DAEA,uDACA,2BAGA,+LAEA,kCACA,uBACG,CAACA,EAAMG,GAAU,CACpB,qBACG,CAACA,EAAS,CAACH,EAAM,eAAgB,CACpC,qDACG,CAACG,EAAS,CAACH,EAAM,KAAKe,IAAW,CACpC,+BACA,+BACA,8BACG,CAACZ,EAAS,CAACH,EAAM,WAAY,CAChC,yBACG,CAACG,EAAS,CAACH,EAAM,cAAe,CACnC,+CACG,CAACG,EAAS,CAACH,EAAM,OAAQ,CAC5B,oCACG,CAACG,EAAS,CAACH,EAAM,WAAY,CAChC,yBACG,CAACG,EAAS,CAACH,EAAM,gBAAgBe,IAAW,CAC/C,2BACG,CAAC,CAACf,EAAM,OAAQ,aAAae,GAAUZ,GAAU,CACpD,uBACG,CAACA,EAAS,CAACH,EAAMiB,EAAQ,WAAY,CACxC,qBACG,CAACd,EAAS,CAACH,EAAMuB,EAAM,WAAY,CACtC,0BACG,CAACpB,EAAS,CAACH,EAAM,YAAa,CACjC,sBACG,CAACG,EAAS,CAACH,EAAM,YAAa,CACjC,qBACG,CAACG,EAAS,CAACH,EAAMuB,EAAM,WAAY,CACtC,2BACG,CAACpB,EAAS,CAACH,EAAM,QAAQe,IAAW,CACvC,sBACG,CAACZ,EAAS,CAACH,EAAMiB,IAAW,CAC/B,iCACG,CAAC,CAACjB,EAAM,OAASe,IAAW,CAC/B,oDACG,CAAC,CAACf,EAAM,OAAQ,MAAQe,GAAUZ,GAAU,CAC/C,8BACG,CAACA,EAAS,CAACH,EAAMwB,EAAU,cAAe,CAC7C,+BACG,CAAC,CAACxB,EAAM,KAAM,KAAMG,GAAU,CACjC,0BACG,CAACA,EAAS,CAACH,EAAM,mBAAoB,CACxC,4BACG,CAAC,CAACA,EAAM,gBAAiBG,GAAU,CACtC,gCACA,iDACA,8CACG,CAACH,EAAMG,GAAU,CACpB,eACA,sBACG,CAACH,GAAO,CAGX,+DACG,CAAC,CAACA,EAAM6B,GAAW1B,GAAU,CAChC,uBACA,uCACA,kCACA,4BACA,4BACA,6BACA,qCACA,iDACG,CAACH,EAAMG,GAAU,CACpB,gCACG,CAACA,EAAS,CAACH,EAAM,QAAS,CAC7B,8CACG,CAACG,EAAS,CAACH,EAAM,WAAY,CAEhC,oCACG,CAACG,EAAS,CAACH,EAAMgB,EAAO,cAAe,CAE1C,+BACG,CAAC,CAAChB,EAAMgB,EAAO,YAAab,GAAU,CAEzC,2DACG,CAACA,EAAS,CAACH,EAAM,WAAWe,IAAW,CAE1C,+DACG,CAACf,EAAMG,GAAU,CAEpB,gDACG,CAACA,EAAS,CAACH,EAAM,kBAAmB,CACvC,sDACG,CAACG,EAASH,GAAO,CACpB,gDACG,CAACA,EAAM,CAACG,EAAS2D,EAtJT,CACX,MAAU,KACV,IAAU,KACV,IAAU,KACV,MAAU,OACV,QAAU,OACV,QAAU,OACV,QAAU,OACV,IAAU,OA8IqC,CAE/C,8BACG,CAAC9D,EAAMG,GAAU,CAGpB,wCACG,CAAC,CAACH,EAAM,YAAaG,GAAU,CAClC,uCACG,CAACA,EAAS,CAACH,EAAMiB,EAAQ,aAAc,CAC1C,6BACA,cACA,mGAEA,+FAEA,wBACA,2CAGA,wHAEA,uBACA,sBACG,CAACjB,EAAMG,GAAU,CAEpB,wBACG,CAACH,EAAM,CAACG,EAAS,eAAgB,MAGxCgE,IAAM,CAAC,CAEH,iDACG,CAAC,CAAC/D,EAAc,UAAW,CAE9B,gBACG,CAAC,CAACA,EAAcmC,IAAY,CAE/B,0BACG,CAAC,CAACnC,EAAc,SAAU,CAE7B,oCACG,CAAC,CAACA,EAAc,UAAW,CAE9B,mCACG,CAAC,CAACA,EAAc,UAAW,CAG9B,8BACG,CAAC,CAACA,EAAc,QAAS,CAE5B,0CACG,CAAC,CAACA,EAAc,OA3WT,GA2WwBmC,IAAY,CAE9C,kBACG,CAAC,CAACnC,EAAc,UAAW,CAE9B,2HAEG,CAAC,CAACA,EAAcmC,KAGvB6B,OAAS,CAAC,CAON,mFACG,CAACrE,EAAO,CAACG,EAAQsB,GAAU,CAACvB,EAAMM,IAAU,CAC/C,yDACA,uBACA,iBACG,CAACR,EAAO,CAACG,EAAQsB,GAAU,CAACvB,EAAMK,IAAU,CAG/C,4CACG,CAACP,EAAO,CAACG,EAAQU,GAAQ,CAACX,EAAMK,IAAU,CAC7C,6BACA,oCACA,kCACG,CAACP,EAAO,CAACG,EAAQU,GAAQ,CAACX,EAAMM,IAAU,CAC7C,iBACG,CAACR,EAAO,CAACG,EAAQU,IAAS,CAG7B,iCACG,CAACb,EAAO,CAACG,EAAQuB,GAAQ,CAACxB,EAAMK,IAAU,CAG7C,+DACG,CAACP,EAAO,CAACG,EAAQiB,GAAS,CAAClB,EAAMM,IAAU,CAC9C,kCACA,sEACG,CAACR,EAAO,CAACG,EAAQiB,GAAS,CAAClB,EAAMK,IAAU,CAG9C,kDACA,yBACA,uCACA,iDACA,4DACA,yGACG,CAAC,CAACP,EAAO,KAAM,KAAM,CAACG,EAAQyB,GAAS,CAAC1B,EAAMK,IAAU,CAC3D,+CACA,8CACE,CAAC,CAACP,EAAO,KAAM,KAAM,CAACG,EAAQyB,GAAS,CAAC1B,EAAMM,IAAU,CAG1D,sBACA,mEACG,CAACR,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMK,IAAU,CAC9C,wBACG,CAACP,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMM,IAAU,CAG9C,yBACA,oCACG,CAACR,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMK,IAAU,CAG9C,mCACG,CAACP,EAAO,CAACG,EAAQ,UAAW,CAACD,EAAMK,IAAU,CAGhD,iFACA,4BACA,sDACG,CAACP,EAAO,CAACG,EAAQoB,GAAW,CAACrB,EAAMK,IAAU,CAChD,qCACG,CAACP,EAAO,CAACG,EAAQoB,GAAW,CAACrB,EAAMM,IAAU,CAGhD,iEACG,CAACR,EAAO,CAACG,EAAQkB,GAAK,CAACnB,EAAMM,IAAU,CAC1C,sDACA,oDACA,wBACG,CAACR,EAAO,CAACG,EAAQkB,GAAK,CAACnB,EAAMK,IAAU,CAG1C,oBACA,qEACG,CAACP,EAAO,CAACG,EAAQ,UAAW,CAACD,EAAMM,IAAU,CAGhD,qCACA,0BACG,CAAC,CAACR,EAAO,KAAM,KAAM,CAACG,EAAQ,SAAU,CAACD,EAAMK,IAAU,CAG5D,gBACG,CAACP,EAAO,CAACG,EAAQgB,GAAS,CAACjB,EAAMM,IAAU,CAC9C,6CACG,CAACR,EAAO,CAACG,EAAQgB,GAAS,CAACjB,EAAMK,IAAU,CAG9C,0GACG,CAACP,EAAO,CAACG,EAAQwB,GAAO,CAACzB,EAAMK,IAAU,CAC5C,oBACA,iCACG,CAAC,CAACP,EAAO,iBAAkB,CAACG,EAAQwB,GAAO,CAACzB,EAAMM,IAAU,CAG/D,sCACA,0CACG,CAACR,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMK,IAAU,CAGjD,eACA,uCACA,gCACG,CAACP,EAAO,CAACG,EAAQS,GAAS,CAACV,EAAMM,IAAU,CAC9C,iDACG,CAAC,CAACR,EAAO,QAAS,iBAAkB,CAACG,EAAQS,GAAS,CAACV,EAAMK,IAAU,CAG1E,gCACG,CAACP,EAAOG,EAAQ,CAACD,EAAMM,IAAU,CACpC,gCACA,kBACG,CAACR,EAAO,CAACG,EAAQY,GAAa,CAACb,EAAMK,IAAU,CAGlD,qFACG,CAACP,EAAO,CAACG,EAAQW,GAAO,CAACZ,EAAMM,IAAU,CAC5C,iDACG,CAACR,EAAO,CAACG,EAAQW,GAAO,CAACZ,EAAMK,IAAU,CAG5C,cACG,CAACP,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMM,IAAU,CAC7C,2CAGA,oCACA,iFACG,CAACL,EAAQ,CAACH,EAAO,KAAM,KAAM,CAACE,EAAMK,IAAU,CAGjD,uCACG,CAACP,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMM,IAAU,CAG9C,8BACA,qBACG,CAACR,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMK,IAAU,CAG/C,kDACG,CAACP,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMK,IAAU,CAGjD,gHAEA,mBACA,iBACA,8BACA,0BACA,WACA,yBACG,CAACJ,EAAQH,EAAO,CAACE,EAAMK,IAAU,CAEpC,2BACA,wBACA,uCACA,uBACA,4BACA,iCACA,kCACA,8BACA,gCACA,mCACG,CAACJ,EAAQH,EAAO,CAACE,EAAMM,IAAU,CAEpC,kBACG,CAACR,EAAO,CAACG,EAAQmB,GAAY,CAACpB,EAAMM,IAAU,CACjD,qCACG,CAACR,EAAO,CAACG,EAAQ,aAAc,CAACD,EAAMK,IAAU,CACnD,aACG,CAACP,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMK,IAAU,CAC9C,gBACG,CAACP,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMK,IAAU,CACjD,iBACG,CAACP,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMM,IAAU,CAC7C,0BACG,CAACR,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMM,IAAU,CAC9C,wBACG,CAACR,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMM,IAAU,CACjD,+CACG,CAACR,EAAO,CAACG,EAAQ,kBAAmB,CAACD,EAAMM,IAAU,CACxD,qBACG,CAACR,EAAO,CAACG,EAAQ,YAAa,CAACD,EAAMM,IAAU,CAClD,cACG,CAACR,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMM,IAAU,CAC7C,mBACG,CAACR,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMK,IAAU,CAC7C,wBACG,CAACP,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMK,IAAU,CAC/C,mBACG,CAACP,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMM,IAAU,CAC/C,wBACG,CAACR,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMM,IAAU,CAC9C,mBACA,sCACG,CAAC,CAACL,EAAQ,gBAAiBH,EAAO,CAACE,EAAMM,IAAU,CACtD,sBACG,CAACR,EAAO,CAACG,EAAQ,YAAa,CAACD,EAAMM,IAAU,CAClD,8BACG,CAACR,EAAO,CAACG,EAAQ,YAAa,CAACD,EAAMM,IAAU,CAClD,oDACG,CAAC,CAACL,EAAQ,SAAUH,EAAO,CAACE,EAAMK,IAAU,CAC/C,2BACG,CAAC,CAACJ,EAAQ,SAAUH,EAAO,CAACE,EAAMK,IAAU,CAC/C,cACG,CAACP,EAAO,CAACG,EAAQ,aAAc,CAACD,EAAMK,IAAU,CACnD,uCACG,CAACP,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMM,IAAU,CACjD,wBACG,CAACR,EAAO,CAACG,EAAQ,aAAc,CAACD,EAAMM,IAAU,CACnD,kBACG,CAACR,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMM,IAAU,CAC/C,qBACG,CAACR,EAAO,CAACG,EAAQ,UAAW,CAACD,EAAMM,IAAU,CAChD,mBACG,CAACL,EAAQH,EAAO,CAACE,EAAMK,IAAU,CACpC,sBACG,CAAC,CAACP,EAAO,MAAO,KAAM,CAACG,EAAQmB,GAAY,CAACpB,EAAMK,IAAU,CAC/D,yDACG,CAACP,EAAO,CAACG,EAAQ0B,GAAQ,CAAC3B,EAAMM,IAAU,CAC7C,yCACG,CAACR,EAAO,CAACG,EAAQ0B,GAAQ,CAAC3B,EAAMK,IAAU,CAM7C,wBACG,CAACJ,EAAQ,CAACD,EAAMO,IAAW,CAC9B,uBACG,CAAC,CAACT,EAAO,IAAK,WAAY,CAACG,EAAQsB,GAAU,CAACvB,EAAMO,IAAW,CAClE,8DACG,CAAC,CAACN,EAAQkB,GAAK,CAACnB,EAAMO,IAAW,CACpC,gBACG,CAACN,EAAQ,CAACH,EAAOa,EAAM,OAAQ,CAACX,EAAMO,IAAW,CACpD,UACG,CAAC,CAACT,EAAOiB,EAAO,QAAS,CAACd,EAAQgB,GAAS,CAACjB,EAAMO,IAAW,CAChE,6BACG,CAACT,EAAO,CAACG,EAAQS,GAAS,CAACV,EAAMO,IAAW,CAC/C,uBACA,uBACG,CAACT,EAAO,CAACG,EAAQuB,GAAQ,CAACxB,EAAMO,IAAU,CAC7C,4BACG,CAACT,EAAO,CAACG,EAAQwB,GAAO,CAACzB,EAAMO,IAAW,CAC7C,qBACG,CAACT,EAAO,CAACG,EAAQyB,GAAS,CAAC1B,EAAMO,IAAW,CAC/C,6BACG,CAACN,EAAQH,EAAO,CAACE,EAAMO,IAAW,CACrC,0CACA,6DACG,CAAC,CAACN,EAAQyC,GAAO,CAAC5C,EAAO4C,GAAO,CAAC1C,EAAMO,IAAW,CACrD,mDACG,CAAC,CAACP,EAAMO,IAAW,CAMtB,UACA,8BACG,CAACN,EAAQH,EAAO,CAACE,EAAMI,IAAW,CACrC,0BACG,CAACN,EAAO,CAACG,EAAQ,UAAW,CAACD,EAAMI,IAAW,CACjD,mCACG,CAACN,EAAO,CAACG,EAAQwB,GAAO,CAACzB,EAAMI,IAAW,CAC7C,sCACG,CAACN,EAAO,CAACG,EAAQmB,GAAY,CAACpB,EAAMI,IAAW,CAMlD,kBACG,CAACH,EAAQH,EAAO,CAACE,EAAMQ,IAAY,CACtC,wCACG,CAACV,EAAO,CAACG,EAAQU,GAAQ,CAACX,EAAMQ,IAAY,CAC/C,wBACG,CAACV,EAAO,CAACG,EAAQgB,GAAS,CAACjB,EAAMQ,IAAY,CAChD,6BACG,CAACV,EAAO,CAACG,EAAQ0B,GAAQ,CAAC3B,EAAMQ,IAAY,CAC/C,uBACG,CAACV,EAAO,CAACG,EAAQ2B,GAAW,CAAC5B,EAAMQ,IAAY,CAMlD,wCACG,CAACP,EAAQ,CAACD,EAAMS,IAAY,CAC/B,cACG,CAACX,EAAO,CAACG,EAAQS,GAAS,CAACV,EAAMS,IAAY,CAMhD,kEACG,CAACX,EAAO,CAACE,EAAMK,IAAU,CAC5B,+DACG,CAACP,EAAO,CAACE,EAAMM,IAAU,CAC5B,gDACG,CAAC,CAACN,EAAMM,IAAU,CACrB,kEACG,CAAC,CAACN,EAAMK,IAAU,CACrB,kCACG,CAACP,EAAO,CAACG,EAAQ,aAGxBmE,OAAS,CAAC,CAEN,8BACG,CAAClE,EAAS,CAACH,EAAMsE,aAAe,CAEnC,6CACG,CAACnE,EAAS,CAACH,EAAM,UAAW,CAE/B,uBACA,sEACA,0BACA,yCACA,8BACA,eACG,CAACA,EAAMG,GAAU,CAEpB,iCACG,CAACA,EAASH,IAGjBuE,GAAK,CAAC,CAGF,mCACG,CAACvE,EAAMG,GAAU,CACpB,yDACG,CAACH,EAAM,CAACG,EAAS2D,EAAWE,IAAqB,CACpD,0BACA,2CACA,wCACG,CAAC,CAAC7D,EAAS2D,EAAWE,GAAoB,CAAChE,EAAM,YAAa,CAGjE,sDACA,4CACA,wBACG,CAAC,CAACG,EAAS,KAAM,KAAM,CAACH,EAAM,QAAS,CAC1C,0BACA,yCACG,CAAC,CAACA,EAAM+B,GAAS,CAAC5B,EAAS,KAAM,MAAO,CAG3C,kDACG,CAACA,EAASH,GAAO,CACpB,+EACA,8BACA,+BACA,kBACG,CAACA,EAAMG,GAAU,CACpB,cACG,CAACA,EAAS,CAACH,EAAMc,IAAc,CAClC,6DACG,CAACX,EAAS,CAACH,EAAM,YAAa,CACjC,mFACG,CAACG,EAAS,CAACH,EAAMiB,EAAQ,QAAS,CACrC,kBACA,wCACG,CAACd,EAAS,CAACH,EAAM,UAAW,CAC/B,wCACG,CAACG,EAAS,CAACH,EAAM,YAAa,CAGjC,qBACG,CAACG,EAAS,CAACH,EAAMgB,EAAO,SAAU,CACrC,oCACG,CAAC,CAAChB,EAAM8B,GAAc3B,GAAS,CAGlC,qBACA,iBACA,2BAGA,mDACA,2BAGA,wCACA,yBACA,4BACA,8SAEA,2BACA,oBACA,6EACA,kBACG,CAACH,EAAMG,GAAU,CACpB,yBACG,CAAC,CAACH,EAAM,WAAYG,GAAU,CACjC,sCACA,kCACA,mEACA,sBACG,CAACH,EAAMG,KAQdqE,EAAW,SAAUxB,EAAIyB,GAOzB,UALWzB,IAAOpD,IACd6E,EAAazB,EACbA,EAAKnG,KAGH8G,gBAAgBa,GAClB,OAAO,IAAIA,EAASxB,EAAIyB,GAAYC,YAGxC,IAAIC,SAAqBlF,IAAWE,GAAcF,EAAOmF,UAAanF,EAAOmF,UAAY/H,EACrFgI,EAAM7B,IAAQ2B,GAAcA,EAAWG,UAAaH,EAAWG,UAnyBrD,IAoyBVC,EAASJ,GAAcA,EAAWK,cAAiBL,EAAWK,cAAgBnI,EAC9EoI,EAAUR,EArvBL,SAAUR,EAASQ,GACxB,IAAIS,EAAgB,CAAC,EACrB,IAAK,IAAIjJ,KAAKgI,EACNQ,EAAWxI,IAAMwI,EAAWxI,GAAGC,OAAS,GAAM,EAC9CgJ,EAAcjJ,GAAKwI,EAAWxI,GAAGkJ,OAAOlB,EAAQhI,IAEhDiJ,EAAcjJ,GAAKgI,EAAQhI,GAGnC,OAAOiJ,CACX,CA2uB2BE,CAAOnB,EAASQ,GAAcR,EACrDoB,EAAaV,GAAcA,EAAWG,WAAaD,EAyEvD,OAvEAlB,KAAK2B,WAAa,WACd,IAjuBiBC,EAiuBbC,EAAW,CAAC,EAShB,OARAA,EAASxF,GAAQnD,EACjB2I,EAASrF,GAAWtD,EACpBkG,EAAUa,KAAK4B,EAAUX,EAAKI,EAAQf,SACtCsB,EAAS1F,UAruBQyF,EAquBUC,EAASrF,MApuBTN,EAAW0F,EAAQ1C,QAAQ,WAzE5C,IAyE+D4C,MAAM,KAAK,GAAK5I,EAsuBrFwI,GAAcV,GAAcA,EAAWe,cAAgBf,EAAWe,MAAMC,SAAWjG,IACnF8F,EAASxF,GAAQ,SAEdwF,CACX,EACA7B,KAAKiC,OAAS,WACV,IAAIC,EAAO,CAAC,EAGZ,OAFAA,EAAKzF,GAAgBvD,EACrBkG,EAAUa,KAAKiC,EAAMhB,EAAKI,EAAQd,KAC3B0B,CACX,EACAlC,KAAKmC,UAAY,WACb,IAAIC,EAAU,CAAC,EAaf,OAZAA,EAAQ7F,GAAUrD,EAClBkJ,EAAQhG,GAASlD,EACjBkJ,EAAQ9F,GAAQpD,EAChBkG,EAAUa,KAAKmC,EAASlB,EAAKI,EAAQb,QACjCiB,IAAeU,EAAQ9F,IAAS8E,GAASA,EAAMiB,SAC/CD,EAAQ9F,GAAQK,GAGhB+E,GAAgC,aAAlBU,EAAQhG,IAAyB4E,UAAqBA,EAAWsB,aAAetG,GAAcgF,EAAWuB,gBAAkBvB,EAAWuB,eAAiB,IACrKH,EAAQhG,GAAS,OACjBgG,EAAQ9F,GAAQM,GAEbwF,CACX,EACApC,KAAKwC,UAAY,WACb,IAAIC,EAAU,CAAC,EAIf,OAHAA,EAAQpG,GAAQnD,EAChBuJ,EAAQjG,GAAWtD,EACnBkG,EAAUa,KAAKwC,EAASvB,EAAKI,EAAQZ,QAC9B+B,CACX,EACAzC,KAAK0C,MAAQ,WACT,IAAIC,EAAM,CAAC,EASX,OARAA,EAAItG,GAAQnD,EACZyJ,EAAInG,GAAWtD,EACfkG,EAAUa,KAAK0C,EAAKzB,EAAKI,EAAQV,IAC7Bc,IAAeiB,EAAItG,IAAS+E,GAASA,EAAMwB,UAA8B,WAAlBxB,EAAMwB,WAC7DD,EAAItG,GAAQ+E,EAAMwB,SACG1D,QAAQ,aAAcf,GACtBe,QAAQ,SAAUd,IAEpCuE,CACX,EACA3C,KAAKe,UAAY,WACb,MAAO,CACH1B,GAAUW,KAAK6C,QACftC,QAAUP,KAAK2B,aACfjB,OAAUV,KAAKwC,YACf5B,GAAUZ,KAAK0C,QACfjC,OAAUT,KAAKmC,YACf3B,IAAUR,KAAKiC,SAEvB,EACAjC,KAAK6C,MAAQ,WACT,OAAO3B,CACX,EACAlB,KAAK8C,MAAQ,SAAUzD,GAEnB,OADA6B,SAAc7B,IAAOnD,GAAYmD,EAAG9G,OAx1BxB,IAw1BkDyG,EAAKK,EAx1BvD,KAw1B4EA,EACjFW,IACX,EACAA,KAAK8C,MAAM5B,GACJlB,IACX,EAEAa,EAASrE,QAn3BS,SAo3BlBqE,EAASzD,QAAWiB,EAAU,CAAChC,EAAMG,EAASL,IAC9C0E,EAASkC,IAAM1E,EAAU,CAAC5B,IAC1BoE,EAASmC,OAAS3E,EAAU,CAACjC,EAAOG,EAAQD,EAAMI,EAASC,EAAQE,EAASD,EAAQE,EAAUC,IAC9F8D,EAASoC,OAASpC,EAASqC,GAAK7E,EAAU,CAAChC,EAAMG,WAOtCnF,IAAa2E,GAEgB1E,EAAOD,UACvCA,EAAUC,EAAOD,QAAUwJ,GAE/BxJ,EAAQwJ,SAAWA,GAGiBtJ,EAAAA,MAChCA,EAAAA,WACI,OAAOsJ,CACV,2CACa/E,IAAWE,IAEzBF,EAAO+E,SAAWA,GAS1B,IAAIsC,SAAWrH,IAAWE,IAAeF,EAAOsH,QAAUtH,EAAOuH,OACjE,GAAIF,IAAMA,EAAE9D,GAAI,CACZ,IAAIiE,GAAS,IAAIzC,EACjBsC,EAAE9D,GAAKiE,GAAOvC,YACdoC,EAAE9D,GAAGkE,IAAM,WACP,OAAOD,GAAOT,OAClB,EACAM,EAAE9D,GAAGmE,IAAM,SAAUnE,GACjBiE,GAAOR,MAAMzD,GACb,IAAIoE,EAASH,GAAOvC,YACpB,IAAK,IAAI2C,KAAQD,EACbN,EAAE9D,GAAGqE,GAAQD,EAAOC,EAE5B,CACJ,CAEH,CA96BD,CA86BqB,iBAAX5H,OAAsBA,OAASkE,K,oEC14BzC,SAAS2D,EAAMC,GAEXA,EAASA,GAAU,CAAC,EACpB,MAAMC,EAAU7D,KAAK6D,QACfC,GAAWC,EAAAA,EAAAA,GAASF,GAASG,cAC7BC,EAAWL,EAAOK,SAElBC,EAAQ,GAEd,IAAIC,EACAC,EACAC,EACAC,EAgBJ,SAASC,EAASC,GACd,OAAIA,GAAMA,EAAGC,KACFD,EAAGC,KAAK3I,OAAO4I,SAGnB5I,OAAO4I,QAAQC,IAAIF,KAAK3I,OAAO4I,QAC1C,CA0CA,SAASE,IAAiB,QAAAC,EAAA5L,UAAAV,OAARuM,EAAM,IAAAC,MAAAF,GAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAANF,EAAME,GAAA/L,UAAA+L,GACpBC,EAxFgB,EAwFOjF,QAAS8E,EACpC,CAEA,SAASI,IAAiB,QAAAC,EAAAlM,UAAAV,OAARuM,EAAM,IAAAC,MAAAI,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAANN,EAAMM,GAAAnM,UAAAmM,GACpBH,EA3FgB,EA2FOjF,QAAS8E,EACpC,CAEA,SAASO,IAAgB,QAAAC,EAAArM,UAAAV,OAARuM,EAAM,IAAAC,MAAAO,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAANT,EAAMS,GAAAtM,UAAAsM,GACnBN,EA9FkB,EA8FOjF,QAAS8E,EACtC,CAEA,SAASU,IAAgB,QAAAC,EAAAxM,UAAAV,OAARuM,EAAM,IAAAC,MAAAU,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAANZ,EAAMY,GAAAzM,UAAAyM,GACnBT,EAjGe,EAiGOjF,QAAS8E,EACnC,CAEA,SAASa,IAAiB,QAAAC,EAAA3M,UAAAV,OAARuM,EAAM,IAAAC,MAAAa,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAANf,EAAMe,GAAA5M,UAAA4M,GACpBZ,EApGgB,EAoGOjF,QAAS8E,EACpC,CAEA,SAASG,EAAMa,EAAOC,GAClB,IAAIC,EAAU,GACVC,EAAU,KAEV7B,IACA6B,GAAU,IAAIC,MAAOC,UACrBH,GAAW,KAAOC,EAAU3B,GAAa,KAGzCD,GAAkB0B,GAASA,EAAMK,eACjCJ,GAAW,IAAMD,EAAMK,eAAiB,IACpCL,EAAMM,UACNL,GAAW,IAAMD,EAAMM,UAAY,MAIvCL,EAAQzN,OAAS,IACjByN,GAAW,KACd,QAAAM,EAAArN,UAAAV,OAlB2BuM,EAAM,IAAAC,MAAAuB,EAAA,EAAAA,EAAA,KAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAANzB,EAAMyB,EAAA,GAAAtN,UAAAsN,GAoBlCxB,MAAMyB,MAAM,KAAM1B,GAAQ2B,SAAQ,SAAUC,GACxCV,GAAWU,EAAO,GACtB,IAGIxC,EAAM4B,IAAU7B,GAAYA,EAASV,MAAMoC,MAAMgB,UAAYb,GAC7D5B,EAAM4B,GAAOE,GAIb/B,GAAYA,EAASV,MAAMoC,MAAMiB,eACjC9C,EAAS+C,QAAQC,EAAAA,EAAOC,IAAK,CAAEf,QAASA,EAASF,MAAOA,GAEhE,CAUA,OARA3B,EAAW,CACP6C,UAxFJ,SAAmB7C,GACf,MAAO,CACHS,MAAOA,EAAMH,KAAKN,GAClBe,MAAOA,EAAMT,KAAKN,GAClBkB,KAAMA,EAAKZ,KAAKN,GAChBqB,KAAMA,EAAKf,KAAKN,GAChBwB,MAAOA,EAAMlB,KAAKN,GAE1B,EAiFI8C,uBAxEJ,SAAgCC,GAC5B9C,EAAmB8C,CACvB,EAuEIC,qBA9DJ,SAA8BD,GAC1B7C,EAAiB6C,CACrB,GA3DI9C,GAAmB,EACnBC,GAAiB,EACjBC,GAAY,IAAI4B,MAAOC,UAED,oBAAXrK,QAA0BA,OAAO4I,UACxCR,EA/BY,GA+BaK,EAASzI,OAAO4I,QAAQQ,OACjDhB,EA/BY,GA+BaK,EAASzI,OAAO4I,QAAQQ,OACjDhB,EA/Bc,GA+BaK,EAASzI,OAAO4I,QAAQW,MACnDnB,EA/BW,GA+BaK,EAASzI,OAAO4I,QAAQc,MAChDtB,EA/BY,GA+BaK,EAASzI,OAAO4I,QAAQiB,QAmHlDxB,CACX,CAEAR,EAAMyD,sBAAwB,QAE9B,MAAMhQ,EAAUiQ,EAAAA,EAAaC,oBAAoB3D,GACjDvM,EAAQmQ,eA7Je,EA8JvBnQ,EAAQoQ,gBA7JgB,EA8JxBpQ,EAAQqQ,gBA7JgB,EA8JxBrQ,EAAQsQ,kBA7JkB,EA8J1BtQ,EAAQuQ,eA7Je,EA8JvBvQ,EAAQwQ,gBA7JgB,EA8JxBP,EAAAA,EAAaQ,uBAAuBlE,EAAMyD,sBAAuBhQ,GACjE,K,2DClKA,SAAS2M,IAEL,IAAI+D,EAAW,CAAC,EAEhB,SAASC,EAAUC,EAAMC,EAAUC,GAA8C,IAAvCC,EAAOlP,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,EAAGmP,EAAenP,UAAAV,OAAA,QAAAW,IAAAD,UAAA,IAAAA,UAAA,GAEnE,IAAK+O,EACD,MAAM,IAAIK,MAAM,0CAEpB,IAAKJ,GAAkC,mBAAdA,EACrB,MAAM,IAAII,MAAM,gCAAkCJ,GAGtD,IAAIK,EAAWH,EAAQG,UAhBJ,EAkBnB,GAAIC,EAAcP,EAAMC,EAAUC,IAAU,EACxC,OAGJJ,EAASE,GAAQF,EAASE,IAAS,GAEnC,MAAMQ,EAAU,CACZC,SAAUR,EACVC,QACAI,WACAF,mBAGAF,GAASA,EAAMQ,cACfF,EAAQG,SAAWT,EAAMQ,eAEzBR,GAASA,EAAM7B,UACfmC,EAAQI,UAAYV,EAAM7B,WAE1B8B,GAAWA,EAAQU,OACnBL,EAAQK,KAAOV,EAAQU,MAGVf,EAASE,GAAMc,MAAK,CAACpC,EAAMqC,KACxC,GAAIrC,GAAQ4B,EAAW5B,EAAK4B,SAExB,OADAR,EAASE,GAAMgB,OAAOD,EAAK,EAAGP,IACvB,CACX,KAIAV,EAASE,GAAMiB,KAAKT,EAE5B,CAUA,SAASU,EAAIlB,EAAMC,EAAUC,GACzB,IAAKF,IAASC,IAAaH,EAASE,GAChC,OAEJ,MAAMe,EAAMR,EAAcP,EAAMC,EAAUC,GACtCa,EAAM,IAGVjB,EAASE,GAAMe,GAAO,KAC1B,CAoDA,SAASR,EAAcP,EAAMC,EAAUC,GAEnC,IAAIa,GAAO,EAEX,OAAKjB,EAASE,IAIdF,EAASE,GAAMc,MAAK,CAACpC,EAAMyC,KACvB,GAAIzC,GAAQA,EAAK+B,WAAaR,KAAcC,GAASA,IAAUxB,EAAKwB,OAEhE,OADAa,EAAMI,GACC,CACX,IAEGJ,GATIA,CAUf,CAMA,MAAM5E,EAAW,CACbiF,GA3FJ,SAAYpB,EAAMC,EAAUC,GACxBH,EAAUC,EAAMC,EAAUC,EADYjP,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,EAE9C,EA0FIoQ,KAxFJ,SAAcrB,EAAMC,EAAUC,GAC1BH,EAAUC,EAAMC,EAAUC,EADcjP,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,GACF,EAC9C,EAuFIiQ,MACArC,QA3EJ,SAAiBmB,GAAkC,IAA5BsB,EAAOrQ,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,EAAGsQ,EAAOtQ,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,EAC5C,IAAK+O,IAASF,EAASE,GACnB,OAKJ,GAFAsB,EAAUA,GAAW,CAAC,EAElBA,EAAQE,eAAe,QACvB,MAAM,IAAInB,MAAM,mDAGpBiB,EAAQtB,KAAOA,EAEXuB,EAAQZ,WACRW,EAAQX,SAAWY,EAAQZ,UAE3BY,EAAQX,YACRU,EAAQV,UAAYW,EAAQX,WAGhC,MAAMa,EAAmB,GACzB3B,EAASE,GACJ0B,QAAQlB,MACAA,GAGDe,EAAQZ,UAAYH,EAAQG,UAAYH,EAAQG,WAAaY,EAAQZ,UAGrEY,EAAQX,WAAaJ,EAAQI,WAAaJ,EAAQI,YAAcW,EAAQX,WAIvEW,EAAQV,MAAQL,EAAQK,MAAQL,EAAQK,OAASU,EAAQV,OAAWL,EAAQK,MAAQU,EAAQV,MAAQU,EAAQV,OAASc,EAAAA,EAAkBC,yBAK/InD,SAAS+B,IACNA,GAAWA,EAAQC,SAASxI,KAAKuI,EAAQN,MAAOoB,GAC5Cd,EAAQJ,iBACRqB,EAAiBR,KAAKT,EAC1B,IAGRiB,EAAiBhD,SAAS+B,IACtBU,EAAIlB,EAAMQ,EAAQC,SAAUD,EAAQN,MAAM,GAElD,EA4BI2B,MATJ,WACI/B,EAAW,CAAC,CAChB,GAUA,OAAO3D,CACX,CAEAJ,EAASqD,sBAAwB,WACjC,MAAMhQ,EAAUiQ,EAAAA,EAAaC,oBAAoBvD,GACjD3M,EAAQ0S,mBA5JmB,EA6J3B1S,EAAQ2S,oBA5JoB,IA6J5B1C,EAAAA,EAAaQ,uBAAuB9D,EAASqD,sBAAuBhQ,GACpE,K,iCC9JA,MAAMiQ,EAAgB,WAElB,IAAIlD,EACA6F,EAAoB,GACxB,MAAMC,EAAqB,CAAC,EACtBC,EAAiB,CAAC,EAuBxB,SAASC,EAAqBtG,EAASuG,GACnC,IAAK,MAAM9R,KAAK0R,EAAmB,CAC/B,MAAMK,EAAML,EAAkB1R,GAC9B,GAAI+R,EAAIxG,UAAYA,GAAWwG,EAAI7O,OAAS4O,EACxC,OAAOC,EAAIlG,QAEnB,CACA,OAAO,IACX,CA2CA,SAASmG,EAAiB9O,EAAM+O,GAC5B,OAAOA,EAAe/O,EAC1B,CAEA,SAASgP,EAAchP,EAAMpE,EAASmT,GAC9B/O,KAAQ+O,IACRA,EAAe/O,GAAQpE,EAE/B,CAmFA,SAASqT,EAAMC,EAAkB7G,EAAS8G,GAEtC,IAAIC,EACJ,MAAMR,EAAYM,EAAiBtD,sBAC7ByD,EAAkBhH,EAAQuG,GAEhC,GAAIS,EAAiB,CAEjB,IAAIC,EAAYD,EAAgB1G,SAEhC,IAAI0G,EAAgBE,SAiBhB,OAAOD,EAAUtE,MAAM,CACnB3C,UACAzM,QAAS+M,GACVwG,GAlBHC,EAAgBF,EAAiBlE,MAAM,CAAC3C,WAAU8G,GAClDG,EAAYA,EAAUtE,MAAM,CACxB3C,UACAzM,QAAS+M,EACT6G,OAAQJ,GACTD,GAEH,IAAK,MAAMjH,KAAQoH,EACXF,EAAcpB,eAAe9F,KAC7BkH,EAAclH,GAAQoH,EAAUpH,GAYhD,MAEIkH,EAAgBF,EAAiBlE,MAAM,CAAC3C,WAAU8G,GAMtD,OAFAC,EAAcxE,aAAe,WAAa,OAAOgE,CAAU,EAEpDQ,CACX,CAeA,OAbAzG,EAAW,CACP1C,OAhNJ,SAAgBjG,EAAMyP,EAAeF,EAAUlH,IACtCA,EAAQrI,IAASyP,IAClBpH,EAAQrI,GAAQ,CACZ2I,SAAU8G,EACVF,SAAUA,GAGtB,EA0MIZ,qBAAsBA,EACtBe,qBA1KJ,SAA8BrH,EAASuG,EAAWjG,GAC9C,IAAK,MAAM7L,KAAK0R,EAAmB,CAC/B,MAAMK,EAAML,EAAkB1R,GAC9B,GAAI+R,EAAIxG,UAAYA,GAAWwG,EAAI7O,OAAS4O,EAExC,YADAJ,EAAkB1R,GAAG6L,SAAWA,EAGxC,CACA6F,EAAkBf,KAAK,CACnBzN,KAAM4O,EACNvG,QAASA,EACTM,SAAUA,GAElB,EA8JIgH,yBArJJ,SAAkCtH,GAC9BmG,EAAoBA,EAAkBN,QAAO0B,GAAKA,EAAEvH,UAAYA,GACpE,EAoJIyD,oBAlFJ,SAA6BoD,GACzB,IAAItT,EAAUkT,EAAiBI,EAAiBtD,sBAAuB6C,GA6BvE,OA5BK7S,IACDA,EAAU,SAAUyM,GAChB,IAAIM,EAIJ,YAHgBjL,IAAZ2K,IACAA,EAAU,CAAC,GAER,CACHG,YAAa,WAcT,OAZKG,IACDA,EAAWgG,EAAqBtG,EAAS6G,EAAiBtD,wBAGzDjD,IACDA,EAAWsG,EAAMC,EAAkB7G,EAAS5K,WAC5C+Q,EAAkBf,KAAK,CACnBzN,KAAMkP,EAAiBtD,sBACvBvD,QAASA,EACTM,SAAUA,KAGXA,CACX,EAER,EACA8F,EAAmBS,EAAiBtD,uBAAyBhQ,GAG1DA,CACX,EAoDIiU,0BAvFJ,SAAmC7P,GAC/B,OAAO8O,EAAiB9O,EAAMyO,EAClC,EAsFIpC,uBA5FJ,SAAgCrM,EAAMpE,GAClCoT,EAAchP,EAAMpE,EAAS6S,EACjC,EA2FIqB,gBAvHJ,SAAyBZ,GACrB,IAAItT,EAAUkT,EAAiBI,EAAiBtD,sBAAuB8C,GAgBvE,OAdK9S,IACDA,EAAU,SAAUyM,GAIhB,YAHgB3K,IAAZ2K,IACAA,EAAU,CAAC,GAER,CACH0H,OAAQ,WACJ,OAAOd,EAAMC,EAAkB7G,EAAS5K,UAC5C,EAER,EAEAiR,EAAeQ,EAAiBtD,uBAAyBhQ,GAEtDA,CACX,EAsGIoU,sBA5HJ,SAA+BhQ,GAC3B,OAAO8O,EAAiB9O,EAAM0O,EAClC,EA2HIuB,mBAjIJ,SAA4BjQ,EAAMpE,GAC9BoT,EAAchP,EAAMpE,EAAS8S,EACjC,GAkIO/F,CAEX,CArOsB,GAuOtB,K,4GCiyBA,SAASuH,IACL,IAAIvH,EACJ,MAAMN,EAAU7D,KAAK6D,QACfC,GAAWC,EAAAA,EAAAA,GAASF,GAASG,cAC7B2H,EAAmB,CACrB,4BAA6B7E,EAAAA,EAAO8E,2BACpC,yCAA0C9E,EAAAA,EAAO+E,0CACjD,gCAAiC/E,EAAAA,EAAOgF,gCACxC,yCAA0ChF,EAAAA,EAAOiF,kCACjD,yCAA0CjF,EAAAA,EAAOkF,kCACjD,4CAA6ClF,EAAAA,EAAOmF,iCACpD,sCAAuCnF,EAAAA,EAAOmF,iCAC9C,oDAAqDnF,EAAAA,EAAOmF,iCAC5D,+CAAgDnF,EAAAA,EAAOmF,iCACvD,+CAAgDnF,EAAAA,EAAOmF,iCACvD,iDAAkDnF,EAAAA,EAAOmF,iCACzD,qCAAsCnF,EAAAA,EAAOmF,iCAC7C,sCAAuCnF,EAAAA,EAAOmF,iCAC9C,iCAAkCnF,EAAAA,EAAOoF,4BACzC,iCAAkCpF,EAAAA,EAAOoF,4BACzC,iCAAkCpF,EAAAA,EAAOqF,4BACzC,iCAAkCrF,EAAAA,EAAOqF,6BAOvCC,EAAkB,CACpBzG,MAAO,CACHgB,SAAUhD,EAAAA,EAAM+D,kBAChBd,eAAe,GAEnByF,UAAW,CACPC,mBAAoB,IACpBC,4BAA6B,IAC7BC,4BAA6B,IAC7BC,sCAAuC,EACvCC,mBAAmB,EACnBC,yBAAyB,EACzBC,4BAA4B,EAC5BC,sBAAsB,EACtBC,mCAAmC,EACnCC,iBAAiB,EACjBC,oCAAoC,EACpCC,aAAc,CACVC,sCAAsC,EACtCC,6BAA8B,CAC1B,CAAEC,YAAaC,EAAAA,EAAUC,0BACzB,CAAEF,YAAaC,EAAAA,EAAUE,+BAAgCrG,MAAO,WAChE,CAAEkG,YAAaC,EAAAA,EAAUG,uBACzB,CAAEJ,YAAaC,EAAAA,EAAUI,2BACzB,CAAEL,YAAaC,EAAAA,EAAUK,kCAAmCxG,MAAO,WACnE,CAAEkG,YAAaC,EAAAA,EAAUM,uCAAwCzG,MAAO,mBACrEmG,EAAAA,EAAUO,0BAA0BxN,KAAIyN,IAChC,CAAE,YAAeA,OAGhCC,yBAAyB,EACzBC,2CAA2C,EAC3CC,4CAA4C,GAEhDC,OAAQ,CACJC,4BAA6B,IAC7BC,8BAA+B,KAEnCC,gBAAiB,CACbC,yBAAyB,EACzBC,2BAA2B,GAE/BC,QAAS,CACLC,aAAc,KAElBC,MAAO,CACHC,uBAAwBC,IACxBC,UAAWD,IACXE,+BAA+B,GAEnCC,WAAY,CACRC,yBAAyB,EACzBC,yBAAyB,EACzBC,8BAA8B,EAC9BC,mBAAmB,GAEvBC,OAAQ,CACJC,4BAA4B,EAC5BC,kBAAmB,KACnBC,0BAA0B,EAC1BC,4BAA4B,EAC5BC,sBAAuB,GACvBC,aAAc,GACdC,uBAAwB,GACxBC,+BAAgC,GAChCC,mBAAoBjB,IACpBkB,kBAAmB,GACnBC,iCAAkC,IAClCC,eAAgB,GAChBC,yBAA0B,GAC1BC,iBAAiB,EACjBC,eAAe,EACfC,8BAA8B,EAC9BC,eAAe,EACfC,6BAA6B,EAC7BC,kCAAkC,EAClCC,qBAAsB,CAClBC,SAAS,EACTC,kBAAkB,IAG1BC,KAAM,CACFC,UAAU,EACVC,eAAe,EACfC,cAAe,IACfC,UAAW,GACXC,eAAe,EACfC,gBAAgB,EAChBC,UAAW,IAEfC,mBAAoB,CAChBV,SAAS,EACTW,iCAAiC,EACjCC,mBAAoB,EACpBC,wBAAyB,GACzBC,+BAAgC,IAChCC,+BAAgC,EAChCC,wCAAyC,EACzCC,oBAAqB,IACrBC,+CAA+C,EAC/CC,oBAAqB,CACjBC,OAAQ,qCACR1K,MAAO,oCAGf2K,WAAY,CACRC,eAAgB,IAChBC,kBAAmB,EACnBC,qBAAqB,GAEzBC,KAAM,CACFC,gBAAgB,EAChBC,4BAA4B,EAC5BC,qBAAqB,EACrBC,KAAM,CACFC,uBAAuB,EACvBC,cAAc,GAElBC,OAAQ,CACJC,wBAAwB,IAGhCC,YAAa,CACTC,SAAUhE,IACViE,aAAc,CACVC,IAAKlE,IACLmE,IAAKnE,KAEToE,kBAAmB,GACnBvC,QAAS,KACT3H,KAAMwE,EAAAA,EAAU2F,2BAEpBC,uBAAwB,CACpBzC,SAAS,EACT0C,IAAK,MAETC,6BAA8B,CAC1B3C,SAAS,EACT0C,IAAK,MAETE,iDAAiD,EACjDC,oBAAqB,CACjBC,MAAO,GACPC,MAAO,GAEXC,gBAAiB,CACbD,MAAOlG,EAAAA,EAAUoG,iCACjBH,MAAOjG,EAAAA,EAAUqG,iCAErBC,yBAAyB,EACzBC,oBAAoB,EACpBC,yBAAyB,EACzBC,6BAA8BzG,EAAAA,EAAU0G,wCACxCC,uBAAwB,IACxBC,gCAAiC,EACjCC,uBAAwB,IACxBC,eAAgB,CACZ,CAACC,EAAAA,EAAYC,UAAW,IACxB,CAACD,EAAAA,EAAYE,sBAAuB,IACpC,CAACF,EAAAA,EAAYG,oBAAqB,IAClC,CAACH,EAAAA,EAAYI,mBAAoB,IACjC,CAACJ,EAAAA,EAAYK,kCAAmC,IAChD,CAACL,EAAAA,EAAYM,oBAAqB,IAClC,CAACN,EAAAA,EAAYO,gCAAiC,IAC9C,CAACP,EAAAA,EAAYQ,SAAU,IACvB,CAACR,EAAAA,EAAYS,YAAa,IAC1BC,0BAA2B,IAE/BC,cAAe,CACX,CAACX,EAAAA,EAAYC,UAAW,EACxB,CAACD,EAAAA,EAAYE,sBAAuB,EACpC,CAACF,EAAAA,EAAYG,oBAAqB,EAClC,CAACH,EAAAA,EAAYI,mBAAoB,EACjC,CAACJ,EAAAA,EAAYK,kCAAmC,EAChD,CAACL,EAAAA,EAAYM,oBAAqB,EAClC,CAACN,EAAAA,EAAYO,gCAAiC,EAC9C,CAACP,EAAAA,EAAYQ,SAAU,EACvB,CAACR,EAAAA,EAAYS,YAAa,EAC1BG,yBAA0B,GAE9BC,IAAK,CACDC,sBAAsB,EACtBC,qCAAqC,EACrCC,kDAAkD,EAClDC,MAAO,CACHC,eAAgB,CACZC,QAAQ,GAEZC,SAAU,CACND,QAAQ,GAEZE,uBAAwB,CACpBF,QAAQ,EACRG,WAAY,CACRC,uBAAwB,GACxBC,mBAAoB,IAG5BC,kBAAmB,CACfN,QAAQ,EACRG,WAAY,CACRI,WAAY,EACZC,0BAA2B,OAGnCC,kBAAmB,CACfT,QAAQ,EACRG,WAAY,CACRO,kBAAmB,IACnBC,iCAAkC,MAG1CC,oBAAqB,CACjBZ,QAAQ,EACRG,WAAY,CACRU,0BAA2B,IAC3BC,oCAAqC,IACrCC,8BAA+B,IAGvCC,QAAS,CACLhB,QAAQ,GAEZiB,SAAU,CACNjB,QAAQ,IAGhBkB,WAAY,CACRC,uBAAwBrJ,EAAAA,EAAUsJ,6BAA6BC,KAC/DC,sCAAuCxJ,EAAAA,EAAUyJ,2CAA2CC,aAC5FC,sBAAsB,EACtBC,yBAA0B,CACtBC,KAAK,EACLC,OAAO,GAEXC,oBAAoB,EACpBC,sBAAuB,GACvBC,eAAgB,CACZC,KAAM,EACNC,IAAK,EACLC,4BAA4B,EAC5BC,cAAe,GACfC,cAAe,IACfC,sBAAuB,GACvBC,2BAA4B,GAEhCC,KAAM,CACFC,8BAA+B,EAC/BC,8BAA+B,EAC/BC,yBAA0B,EAC1BC,yBAA0B,EAC1BC,uCAAwC,QAGhDC,WAAY,CACR7E,OAAQ,EACRD,OAAQ,GAEZ+E,WAAY,CACR9E,OAAQ,EACRD,OAAQ,GAEZgF,eAAgB,CACZ/E,OAAQ,EACRD,OAAQ,GAEZiF,kBAAmB,CACfhF,OAAO,EACPD,OAAO,IAGfkF,KAAM,CACFC,wBAAwB,EACxBjI,SAAS,EACTkI,IAAK,KACLC,IAAK,KACLC,IAAK,KACLC,gBAAiB,EACjBhQ,KAAMwE,EAAAA,EAAUyL,gBAChBC,YAAa1L,EAAAA,EAAU2L,oBACvBC,kBAAmB,CAAC,UAAW,OAC/BrX,QAAS,GAEbsX,KAAM,CACF1I,SAAS,EACTyE,IAAK,CACDkE,SAAS,EACTC,eAAgB,IAGxBC,mBAAoB,CAChBC,UAAW,GACXC,0BAA2B,2CAC3BC,KAAM,0BACNC,cAAe,4BAGvBC,OAAQ,CACJC,gBAAiB,CACbC,iBAAkB,KAK9B,IAAI3V,EAAW4V,EAAAA,EAAMC,MAAM1N,GAI3B,SAAS2N,EAAcC,EAAQC,EAAMtiB,GACjC,IAAK,IAAIuiB,KAAKF,EACNA,EAAOxQ,eAAe0Q,KAClBD,EAAKzQ,eAAe0Q,GACK,iBAAdF,EAAOE,IAAqBF,EAAOE,aAAcC,QAAaH,EAAOE,aAAcnV,OAAwB,OAAdiV,EAAOE,IAG3GD,EAAKC,GAAKL,EAAAA,EAAMC,MAAME,EAAOE,IACzBvO,EAAiBhU,EAAOuiB,IACxBpW,EAAS+C,QAAQ8E,EAAiBhU,EAAOuiB,KAJ7CH,EAAcC,EAAOE,GAAID,EAAKC,GAAIviB,EAAKgB,QAAUuhB,EAAI,KAQzDxV,QAAQQ,MAAM,sBAAwBvN,EAAOuiB,EAAI,qBAIjE,CA4CA,OANA/V,EAAW,CACPZ,IAhCJ,WACI,OAAOU,CACX,EA+BImW,OAlBJ,SAAgBC,GACe,iBAAhBA,GACPN,EAAcM,EAAapW,EAAU,GAE7C,EAeI4F,MAPJ,WACI5F,EAAW4V,EAAAA,EAAMC,MAAM1N,EAC3B,GAQOjI,CACX,CAGAuH,EAAStE,sBAAwB,WACjC,IAAIhQ,EAAUiQ,EAAAA,EAAaC,oBAAoBoE,GAC/C,K,sECr5CA,MAAMmO,EACF,YAAOS,CAAML,EAAMD,EAAQO,GACvB,IAAIC,EACAC,EAAQ,CAAC,EACb,GAAIR,EACA,IAAK,IAAIze,KAAQwe,EACTA,EAAOxQ,eAAehO,KACtBgf,EAAIR,EAAOxe,GACLA,KAAQye,IAAUA,EAAKze,KAAUgf,GAAQhf,KAAQif,GAAUA,EAAMjf,KAAUgf,KACnD,iBAAfP,EAAKze,IAAqC,OAAfye,EAAKze,GACvCye,EAAKze,GAAQqe,EAAMS,MAAML,EAAKze,GAAOgf,EAAGD,GAExCN,EAAKze,GAAQ+e,EAAKC,KAMtC,OAAOP,CACX,CAEA,YAAOH,CAAMY,GACT,IAAKA,GAAsB,iBAARA,EACf,OAAOA,EAEX,GAAIA,aAAeP,OACf,OAAO,IAAIA,OAAOO,GAEtB,IAAIC,EACJ,GAAID,aAAe3V,MAAO,CAEtB4V,EAAI,GACJ,IAAK,IAAIriB,EAAI,EAAGsiB,EAAIF,EAAIniB,OAAQD,EAAIsiB,IAAKtiB,EACjCA,KAAKoiB,GACLC,EAAE1R,KAAK4Q,EAAMC,MAAMY,EAAIpiB,IAGnC,MACIqiB,EAAI,CAAC,EAET,OAAOd,EAAMS,MAAMK,EAAGD,EAAKb,EAAMC,MACrC,CAEA,uCAAOe,CAAiCC,EAAKhW,GACzC,IACI,IAAKA,GAA4B,IAAlBA,EAAOvM,OAClB,OAAOuiB,EAGX,IAAIC,EAAaD,EAKjB,OAJAhW,EAAO2B,SAAQuU,IAAoB,IAAnB,IAAEC,EAAG,MAAE/T,GAAO8T,EAC1B,MAAME,EAAYH,EAAWI,SAAS,KAAO,IAAM,IACnDJ,GAAc,GAAGG,IAAaE,mBAAmBH,MAAUG,mBAAmBlU,IAAS,IAEpF6T,CACX,CAAE,MAAOM,GACL,OAAOP,CACX,CACJ,CAEA,kCAAOQ,CAA4BR,EAAKS,GACpC,IAAKT,IAAQS,EACT,OAAOT,EAGX,MAAMU,EAAY,IAAIC,IAAIX,GAGpBhW,EAAS,IAAI4W,gBAAgBF,EAAUG,QAE7C,IAAK7W,GAA0B,IAAhBA,EAAO8W,OAAe9W,EAAOrG,IAAI8c,GAC5C,OAAOT,EAIXhW,EAAO+W,OAAON,GAGd,MAAMO,EAAc/W,MAAMpL,KAAKmL,EAAOiX,WACjC3b,KAAI4b,IAAA,IAAEf,EAAK/T,GAAM8U,EAAA,MAAK,GAAGf,KAAO/T,GAAO,IACvC3N,KAAK,KAGJ0iB,EAAU,GAAGT,EAAUU,SAASV,EAAUW,WAChD,OAAOL,EAAc,GAAGG,KAAWH,IAAgBG,CACvD,CAEA,uBAAOG,CAAiBC,GACpB,IAAIC,EAAU,CAAC,EACf,IAAKD,EACD,OAAOC,EAKX,IAAIC,EAAcF,EAAUrd,OAAO8C,MAAM,QACzC,IAAK,IAAIxJ,EAAI,EAAGkkB,EAAOD,EAAYhkB,OAAQD,EAAIkkB,EAAMlkB,IAAK,CACtD,IAAImkB,EAAaF,EAAYjkB,GACzB6Q,EAAQsT,EAAW5d,QAAQ,MAC3BsK,EAAQ,IACRmT,EAAQG,EAAWtd,UAAU,EAAGgK,IAAUsT,EAAWtd,UAAUgK,EAAQ,GAE/E,CACA,OAAOmT,CACX,CAOA,uBAAOI,CAAiBC,GACpB,MAAM7X,EAAS,GACT8X,EAAe,IAAIlB,gBAAgBiB,GACzC,IAAK,MAAO1B,EAAK/T,KAAU0V,EAAab,UACpCjX,EAAOmE,KAAK,CAAEgS,IAAK4B,mBAAmB5B,GAAM/T,MAAO2V,mBAAmB3V,KAE1E,OAAOpC,CACX,CAEA,mBAAOgY,GACH,IAAIC,GAAK,IAAI7W,MAAOC,UAMpB,MALa,uCAAuCjH,QAAQ,SAAS,SAAU8d,GAC3E,MAAMrC,GAAKoC,EAAqB,GAAhBE,KAAKC,UAAiB,GAAK,EAE3C,OADAH,EAAKE,KAAKE,MAAMJ,EAAK,KACR,KAALC,EAAWrC,EAAS,EAAJA,EAAU,GAAMyC,SAAS,GACrD,GAEJ,CAEA,uBAAOC,CAAiBC,GACpB,IAAIC,EAAO,EAEX,GAAsB,IAAlBD,EAAO/kB,OACP,OAAOglB,EAGX,IAAK,IAAIjlB,EAAI,EAAGA,EAAIglB,EAAO/kB,OAAQD,IAE/BilB,GAASA,GAAQ,GAAKA,EADVD,EAAO9kB,WAAWF,GAE9BilB,GAAQ,EAEZ,OAAOA,CACX,CAQA,qBAAOC,CAAeC,EAAaC,GAC/B,IACI,MAAMC,EAAW,IAAIlC,IAAIgC,GACnBG,EAAS,IAAInC,IAAIiC,GAIvB,GADAC,EAASE,SAAWD,EAAOC,SACvBF,EAASzB,SAAW0B,EAAO1B,OAC3B,OAAOwB,EAIX,IAAII,EAAenmB,EAAAA,SAAcgmB,EAASxB,SAAS4B,OAAO,EAAGJ,EAASxB,SAASzjB,YAAY,MAAOklB,EAAOzB,SAAS4B,OAAO,EAAGH,EAAOzB,SAASzjB,YAAY,OAGxJ,MAAMslB,EAA2C,IAAxBF,EAAavlB,OAAe,EAAI,EAIzD,OAHAulB,GAAgBF,EAAOzB,SAAS4B,OAAOH,EAAOzB,SAASzjB,YAAY,KAAOslB,EAAkBJ,EAAOzB,SAAS5jB,OAAS,GAGjHqlB,EAAOzB,SAAS5jB,OAASulB,EAAavlB,OAC/BqlB,EAAOzB,SAEX2B,CACX,CAAE,MAAOzC,GACL,OAAOqC,CACX,CACJ,CAEA,qBAAOO,CAAeC,GAClB,IAGI,OAFY,IAAIzC,IAAIyC,GAETC,IACf,CAAE,MAAO9C,GACL,OAAO,IACX,CACJ,CAEA,qBAAO+C,GAA0B,IAAX/e,EAAEpG,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG,KACvB,IACI,MAAMolB,EAAkB,OAAPhf,GAAmC,oBAAd4B,UAA4BA,UAAUE,UAAUpC,cAAqB,GAE3G,OAAO8B,EAAAA,EAAAA,UAASwd,EACpB,CAAE,MAAOhD,GACL,MAAO,CAAC,CACZ,CACJ,CAOA,wBAAOiD,CAAkBhB,GACrB,MAAQ,mBAAmBpd,KAAKod,EACpC,CAEA,6BAAOiB,CAAuBC,GAC1B,OAAO3E,EAAM4E,WAAWD,EAAcE,SAC1C,CAEA,yBAAOC,CAAmBH,GACtB,OAAO3E,EAAM4E,WAAWD,EAAcI,WAC1C,CAEA,yBAAOC,CAAmBC,GAEtB,OADgB,IAAIC,YAAY,SACjBC,OAAOF,EAC1B,CAEA,wBAAOG,CAAkBC,GACrB,MAAM5gB,EAAMub,EAAM8E,mBAAmBO,GACrC,IAAIC,EAAM,GACV,IAAK,IAAIjY,KAAS5I,EACd4I,EAAQA,EAAMkW,SAAS,IACF,IAAjBlW,EAAM3O,SACN2O,EAAQ,IAAMA,GAElBiY,GAAOjY,EAEX,OAAOiY,CACX,CAEA,iBAAOV,CAAWD,EAAcY,GAC5B,MAAMjQ,EAAS0K,EAAMwF,eAAeb,GACpC,IAAIc,EAAkB,EAClB,sBAAuBZ,WACvBY,EAAkBZ,SAASa,mBAG/B,MAAMC,IAAYhB,EAAaiB,YAAc,GAAKjB,EAAakB,YAC3DJ,EACEK,GAAanB,EAAaiB,YAAc,GAAMH,EAC9C1kB,EAAQqiB,KAAKE,MAAMF,KAAKnK,IAAI,EAAGmK,KAAKpK,IAAI8M,EAAUH,KAExD,OAAO,IAAIJ,EAAKjQ,EAAQvU,EADZqiB,KAAKE,MAAMF,KAAKpK,IAAIjY,EAAQqiB,KAAKnK,IAAI8M,IAAU,GAAIJ,IAC1B5kB,EACzC,CAEA,qBAAOykB,CAAeQ,GAClB,OAAIA,aAAgBC,YACTD,EAEAA,EAAK1Q,MAEpB,CAEA,qBAAO4Q,CAAeC,GAClB,MAAM,KAAEzkB,EAAI,QAAE0kB,GAAYpG,EAAMqG,eAAeF,GAE/C,OAAQzkB,GACJ,IAAK,OACD,OAAQ0kB,GACJ,IAAK,KACL,IAAK,KACL,IAAK,QACD,OAAO5S,EAAAA,EAAU8S,eAAeC,IACpC,IAAK,KACL,IAAK,KACL,IAAK,KACL,IAAK,OACL,IAAK,QACL,IAAK,OACL,IAAK,QACL,IAAK,QACL,IAAK,QACD,OAAO/S,EAAAA,EAAU8S,eAAeE,IACpC,IAAK,KACD,OAAOhT,EAAAA,EAAU8S,eAAeG,IACpC,IAAK,KACD,OAAOjT,EAAAA,EAAU8S,eAAeI,IACpC,IAAK,KACD,OAAOlT,EAAAA,EAAU8S,eAAeK,KACpC,IAAK,KACD,OAAOnT,EAAAA,EAAU8S,eAAeM,KAExC,MACJ,IAAK,OACL,IAAK,OACD,OAAOpT,EAAAA,EAAU8S,eAAeO,IACpC,IAAK,OACL,IAAK,OACD,OAAOrT,EAAAA,EAAU8S,eAAeQ,KACpC,QACI,OAAOplB,EAGf,OAAOA,CACX,CAEA,qBAAO2kB,CAAeF,GAClB,MAAOzkB,KAASqlB,GAAQZ,EAAYle,MAAM,KAE1C,MAAO,CAAEvG,OAAM0kB,QADCW,EAAKrnB,KAAK,KAE9B,EAIJ,K,kDCrTA,MAAMsnB,UAAmBC,EAAAA,EACrBC,WAAAA,GACIC,QACAhhB,KAAKihB,wBAA0B,wBAC/BjhB,KAAKkhB,oBAAsB,qBAC3BlhB,KAAKmhB,eAAiB,gBACtBnhB,KAAKohB,4BAA8B,2BACnCphB,KAAKqhB,2BAA6B,2BAClCrhB,KAAKshB,8BAAgC,6BACrCthB,KAAKuhB,mBAAqB,mBAC1BvhB,KAAKwhB,sBAAwB,sBAC7BxhB,KAAKyhB,sBAAwB,sBAC7BzhB,KAAK0hB,cAAgB,eACrB1hB,KAAK2hB,sBAAwB,sBAC7B3hB,KAAK4hB,qBAAuB,qBAC5B5hB,KAAK6hB,qBAAuB,qBAC5B7hB,KAAK8hB,yBAA2B,yBAChC9hB,KAAK+hB,yBAA2B,yBAChC/hB,KAAKgiB,kBAAoB,mBACzBhiB,KAAKiiB,iBAAmB,kBACxBjiB,KAAKkiB,sBAAwB,sBAC7BliB,KAAKmiB,kBAAoB,iBACzBniB,KAAKoiB,iBAAmB,kBACxBpiB,KAAKqiB,sBAAwB,sBAC7BriB,KAAKsiB,sBAAwB,sBAC7BtiB,KAAKuiB,kBAAoB,mBACzBviB,KAAKwiB,eAAiB,gBACtBxiB,KAAKyiB,+BAAiC,8BACtCziB,KAAK0iB,mCAAqC,kCAC1C1iB,KAAK2iB,wCAA0C,8BAC/C3iB,KAAK4iB,4CAA8C,kCACnD5iB,KAAK6iB,wCAA0C,sCAC/C7iB,KAAK8iB,4CAA8C,0CACnD9iB,KAAK+iB,mCAAqC,iCAC1C/iB,KAAKgjB,wBAA0B,uBAC/BhjB,KAAKijB,oBAAsB,oBAC3BjjB,KAAKkjB,iBAAmB,kBACxBljB,KAAKmjB,2BAA6B,2BAClCnjB,KAAKojB,4BAA8B,4BACnCpjB,KAAKqjB,8BAAgC,6BACrCrjB,KAAKsjB,+BAAiC,8BACtCtjB,KAAKujB,wBAA0B,uBAC/BvjB,KAAKwjB,sBAAwB,sBAC7BxjB,KAAKyjB,qBAAuB,qBAC5BzjB,KAAK0jB,uBAAyB,uBAC9B1jB,KAAK2jB,qBAAuB,qBAC5B3jB,KAAK4jB,YAAc,aACnB5jB,KAAK6jB,YAAc,aACnB7jB,KAAK4L,2BAA6B,0BAClC5L,KAAK6L,0CAA4C,uCACjD7L,KAAK8L,gCAAkC,+BACvC9L,KAAK+L,kCAAoC,gCACzC/L,KAAKgM,kCAAoC,gCACzChM,KAAKiM,iCAAmC,+BACxCjM,KAAKkM,4BAA8B,2BACnClM,KAAKmM,4BAA8B,0BACvC,EAGJ,K,kDC9DA,MAAMrF,UAAe+Z,EAAAA,GAGrB,IAAI5S,EAAS,IAAInH,EACjB,K,kCCiBA,IAvBA,MACIrF,MAAAA,CAAOwM,EAAQrK,GACX,IAAKqK,EACD,OAGJ,IAAIlD,IAAWnH,GAASA,EAAOmH,SAC3B+Y,IAAalgB,GAASA,EAAOkgB,WAGjC,IAAK,MAAMC,KAAO9V,GACTA,EAAOzE,eAAeua,IAAS/jB,KAAK+jB,KAAShZ,GAG9C+Y,IAAkD,IAApC7V,EAAO8V,GAAKllB,QAAQ,aAGtCmB,KAAK+jB,GAAO9V,EAAO8V,GAG3B,E,kCCZJ,IARA,MACIhD,WAAAA,GAEI/gB,KAAKoN,YAAc,GACnBpN,KAAKkH,MAAQ,EACjB,E,kDCHJ,MAAMyC,UAA0BmX,EAAAA,EAK5BC,WAAAA,GACIC,QAOAhhB,KAAKgkB,cAAgB,cAMrBhkB,KAAKikB,kBAAoB,kBAOzBjkB,KAAKkkB,aAAe,gBAOpBlkB,KAAKmkB,cAAgB,eAMrBnkB,KAAKokB,2BAA6B,qBAMlCpkB,KAAKqkB,qBAAuB,qBAM5BrkB,KAAKskB,wBAA0B,uBAM/BtkB,KAAKukB,2BAA6B,0BAMlCvkB,KAAKwkB,yBAA2B,wBAMhCxkB,KAAKykB,kBAAoB,kBAMzBzkB,KAAK0kB,MAAQ,QAKb1kB,KAAK2kB,2BAA6B,2BAMlC3kB,KAAK4kB,0BAA4B,0BAKjC5kB,KAAK6kB,yBAA2B,yBAMhC7kB,KAAK8kB,2BAA6B,2BAMlC9kB,KAAK+G,IAAM,MAMX/G,KAAK+kB,yBAA2B,yBAMhC/kB,KAAKglB,0BAA4B,0BAMjChlB,KAAKilB,gBAAkB,iBAMvBjlB,KAAKklB,gBAAkB,iBAMvBllB,KAAKmlB,eAAiB,gBAMtBnlB,KAAKolB,aAAe,cAMpBplB,KAAKqlB,eAAiB,gBAMtBrlB,KAAKslB,sBAAwB,sBAM7BtlB,KAAKulB,wBAA0B,wBAM/BvlB,KAAKwlB,yBAA2B,yBAMhCxlB,KAAKylB,wBAA0B,wBAM/BzlB,KAAK0lB,mBAAqB,mBAM1B1lB,KAAK2lB,sBAAwB,sBAM7B3lB,KAAK4lB,oBAAsB,qBAM3B5lB,KAAK6lB,eAAiB,gBAMtB7lB,KAAK8lB,iBAAmB,kBAMxB9lB,KAAK+lB,mBAAqB,oBAM1B/lB,KAAKgmB,mBAAqB,oBAM1BhmB,KAAKimB,yBAA2B,yBAMhCjmB,KAAKkmB,kBAAoB,qBAMzBlmB,KAAKmmB,iBAAmB,iBAMxBnmB,KAAKomB,UAAY,WAMjBpmB,KAAKqmB,SAAW,UAMhBrmB,KAAKsmB,8BAAgC,8BAMrCtmB,KAAKumB,YAAc,aAMnBvmB,KAAKwmB,cAAgB,cAMrBxmB,KAAKymB,iBAAmB,kBAMxBzmB,KAAK0mB,yBAA2B,yBAQhC1mB,KAAK2mB,SAAW,UAMhB3mB,KAAK4mB,iBAAmB,iBAMxB5mB,KAAK6mB,eAAiB,gBAOtB7mB,KAAK8mB,eAAiB,gBAOtB9mB,KAAK+mB,qBAAuB,sBAM5B/mB,KAAKgnB,qBAAuB,qBAO5BhnB,KAAKinB,yBAA2B,yBAOhCjnB,KAAKknB,qBAAuB,qBAM5BlnB,KAAKmnB,gBAAkB,iBAQvBnnB,KAAKonB,iBAAmB,kBAQxBpnB,KAAKqnB,kBAAoB,mBAMzBrnB,KAAKsnB,sBAAwB,sBAM7BtnB,KAAKunB,gBAAkB,iBAMvBvnB,KAAKwnB,iBAAmB,kBAMxBxnB,KAAKynB,iBAAmB,kBAQxBznB,KAAK0nB,iBAAmB,kBAMxB1nB,KAAK2nB,sBAAwB,sBAM7B3nB,KAAK4nB,wBAA0B,wBAO/B5nB,KAAK6nB,iBAAmB,kBAMxB7nB,KAAK8nB,0BAA4B,0BAMjC9nB,KAAK+nB,oBAAsB,mBAM3B/nB,KAAK4J,sBAAwB,qBAM7B5J,KAAKgoB,sBAAwB,uBAM7BhoB,KAAKioB,sBAAwB,uBAM7BjoB,KAAKkoB,uCAAyC,qCAM9CloB,KAAKmoB,mCAAqC,kCAM1CnoB,KAAKooB,YAAc,aAMnBpoB,KAAKqoB,qCAAuC,mCAM5CroB,KAAKsoB,mCAAqC,gCAC9C,EAGJ,IAAIC,EAAoB,IAAI5e,EAC5B,K,kCCjdA,KAMI6e,OAAQ,SAORC,MAAO,QAOPC,MAAO,QAOPC,KAAM,OAONC,MAAO,QAOPC,MAAO,QAOPC,KAAM,OAONC,KAAM,OAONC,IAAK,MAOLC,KAAM,OAONC,iBAAkB,kBAOlBlW,0BAA2B,yBAO3BmW,uBAAwB,sBAOxBC,8BAA+B,gBAO/BC,oBAAqB,OAOrBC,mBAAoB,oBAOpBC,qBAAsB,8BAOtB9V,iCAAkC,gBAOlCC,gCAAiC,eAOjC8V,iCAAkC,aAOlCC,qCAAsC,iBAOtC1V,wCAAyC,oBAOzC2V,kCAAmC,cAOnCC,eAAgB,OAOhB7Q,gBAAiB,QAOjB8Q,iBAAkB,SAOlB5Q,oBAAqB,CAAC,KAAM,IAAK,KAAM,KAAM,KAAM,KAAM,MAAO,MAAO,MAAO,KAAM,KAAM,MAAO,MAAO,KAAM,KAAM,MAAO,KAAM,KAMjI6Q,uBAAwB,CAAC,MAAO,OAOhCC,wBAAyB,CAAC,UAAW,MAAO,QAAS,WAAY,SAGjEC,WAAY,aACZC,aAAc,UACdC,YAAa,SACbC,cAAe,WACfC,4BAA6B,gCAC7BC,IAAK,MACLC,IAAK,MACLC,KAAM,QACNC,cAAe,cACfC,WAAY,YACZC,kCAAmC,qCACnCC,oCAAqC,wCACrCC,qBAAsB,8BACtB/c,0BAA2B,CAAC,mCAAoC,+CAChEN,yBAA0B,iCAC1BC,+BAAgC,sCAChCC,sBAAuB,8BACvBC,0BAA2B,8BAC3BC,kCAAmC,yCACnCC,uCAAwC,8CACxCid,kCAAmC,uBACnCC,2BAA4B,CACxBC,UAAW,YACXC,QAAS,UACTC,UAAW,aAEfC,uBAAwB,CACpBC,WAAY,CACRC,KAAM,OACNC,GAAI,KACJC,QAAS,WAEbC,iBAAkB,CACdH,KAAM,OACNI,GAAI,KACJC,IAAK,OAETC,iBAAkB,CACdC,iBAAkB,iBAClBC,OAAQ,SACRC,iBAAkB,mBAG1BC,IAAK,MACLC,aAAc,cACdC,kBAAmB,mBACnBC,gBAAiB,kBACjBC,aAAc,wBACdC,cAAe,wBACfC,2BAA4B,CACxBC,aAAc,EACdC,cAAe,EACfC,kBAAmB,EACnBC,iBAAkB,EAClBC,iBAAkB,GAEtBC,kBAAmB,CACfC,MAAO,eACPC,IAAK,cAETC,iBAAkB,CACdC,QAAS,0BACTC,UAAW,6BAEfnW,6BAA8B,CAC1BC,KAAM,gCACNmW,MAAO,iCACPC,gBAAiB,0CACjBC,mCAAoC,0DACpCC,8BAA+B,sDAC/BC,cAAe,wCACfC,iCAAkC,wDAClCC,4BAA6B,qDAEjCvW,2CAA4C,CACxCC,aAAc,mDACduW,gBAAiB,sDACjBC,KAAM,6CAEVC,YAAa,CACTC,qBAAsB,qBACtBC,uBAAwB,wBAE5BD,qBAAsB,CAClBE,UAAW,WACXC,gBAAiB,iBACjBC,yBAA0B,yBAC1BC,oBAAqB,oBACrBC,oBAAqB,oBACrBC,oBAAqB,UACrBC,cAAe,YAEnBP,uBAAwB,CACpBQ,qBAAsB,uBAQ1BC,kBAAmB,+BACnBC,2BAA4B,sBAC5BC,oBAAsB,0BACtBlO,eAAgB,CACZC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,KAAM,OACNC,KAAM,OACNC,IAAK,MACLC,KAAM,Q,iDC1Td,MAAM2N,UAA+BxN,EAAAA,EACjCC,WAAAA,GACIC,QAEAhhB,KAAKuuB,gCAAkC,uCACvCvuB,KAAKwuB,wBAA0B,iCAM/BxuB,KAAKyuB,oBAAsB,mBAC/B,EAGJ,IAAIC,EAAyB,IAAIJ,EACjC,K,wFCdA,SAASK,IAEL,IAAIxqB,EACAyqB,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAEJ,MAAMvrB,EAAU7D,KAAK6D,QACfI,GAAWyH,EAAAA,EAAAA,GAAS7H,GAASG,cASnC,SAASqrB,IACLP,EAAsB,GACtBC,EAAuB,GACvBC,EAAwB,GACxBC,EAAyB,GACzBC,EAA4B,GAC5BE,EAAiB,GACjBD,EAAsC,KACtCP,EAAmB,EACvB,CAsHA,SAASU,EAAkB/lB,EAASG,GAChC,IAAIP,GAAS,EACbI,EAAQT,MAAK,CAACpC,EAAMpO,KAChB,GAAIoO,IAASgD,EAET,OADAP,EAAQ7Q,GACD,CACX,IAEA6Q,EAAQ,GAGZI,EAAQP,OAAOG,EAAO,EAC1B,CAOA,SAASomB,EAAwBC,GAC7B,IAAIl3B,EACJ,IAAKA,EAAI,EAAGA,EAAI82B,EAAe72B,OAAQD,IACnC,GAAI82B,EAAe92B,GAAGk3B,WAAaA,EAC/B,OAAOl3B,EAGf,OAAQ,CACZ,CA0HA,SAASm3B,EAAmBriB,EAAalG,GACrCwoB,EAAsBtiB,EAAalG,GACnC,IAAIyoB,EAAK,IAAIC,EAAAA,EACbD,EAAGviB,YAAcA,EACjBuiB,EAAGzoB,MAAQA,EACX0nB,EAAiB3lB,KAAK0mB,EAC1B,CAeA,SAASD,EAAsBtiB,EAAalG,IACxC2oB,EAAAA,EAAAA,IAAmBziB,EAAa,WAChCyiB,EAAAA,EAAAA,IAAmB3oB,EAAO,UAC1B0nB,EAAiBnoB,SAAQ,SAAU4D,EAAKtB,GAChCsB,EAAI+C,cAAgBA,GAAe/C,EAAInD,QAAUA,GACjD0nB,EAAiB5lB,OAAOD,EAAK,EAErC,GACJ,CAqEA,OApCA5E,EAAW,CACP2rB,iBA9KJ,SAA0B9nB,EAAMwnB,EAAUO,GACtC,GAAoB,iBAAT/nB,GAAsBA,IAASqF,EAAAA,EAAUmgB,YAAYE,wBAA0B1lB,IAASqF,EAAAA,EAAUmgB,YAAYC,sBACjG,iBAAb+B,EACP,MAAMniB,EAAAA,EAAUic,mBAEpB,IAAIngB,EAAQomB,EAAwBC,IACrB,IAAXrmB,EAEAimB,EAAenmB,KAAK,CAChBjB,KAAMA,EACNwnB,SAAUA,EACVO,KAAMA,KAIVX,EAAejmB,GAAOnB,KAAOA,EAC7BonB,EAAejmB,GAAO4mB,KAAOA,EAErC,EA6JIC,sBAnHJ,SAA+BC,GAC3BnB,EAAoB7lB,KAAKgnB,EAC7B,EAkHIC,uBA1GJ,SAAgCD,GAC5BlB,EAAqB9lB,KAAKgnB,EAC9B,EAyGIR,qBACAU,6BAjCJ,WACIvB,EAAmB,EACvB,EAgCIwB,kBAjIJ,WACI,OAAOhB,CACX,EAgIIiB,6BAtPJ,WACI,OAAOnB,CACX,EAqPIoB,uCAnTJ,WACI,OAAOnB,CACX,EAkTIoB,yBA5SJ,WACI,OAAOvB,CACX,EA2SIwB,0BArSJ,WACI,OAAOvB,CACX,EAoSIwB,uBA1FJ,WACI,OAAO3B,CACX,EAyFI4B,wBAnFJ,WACI,OAAO3B,CACX,EAkFI4B,oBA/DJ,WACI,OAAO/B,CACX,EA8DIgC,6BApBJ,SAAsC5oB,GAClC,MAAM6oB,EAAWhC,EAAmB7mB,GAEpC,YAAoB9O,IAAb23B,EAAyBhC,EAAmBiC,QAAUD,CACjE,EAiBIE,iCApPJ,SAA0CrnB,GACtCwlB,EAA0BjmB,KAAKS,EACnC,EAmPIsnB,6BAjSJ,SAAsCtnB,GAClCslB,EAAsB/lB,KAAKS,EAC/B,EAgSIunB,8BAxRJ,SAAuCvnB,GACnCulB,EAAuBhmB,KAAKS,EAChC,EAuRIwnB,oBAtKJ,SAA6B1B,GACzB,GAAIA,EAAU,CACV,IAAIrmB,EAAQomB,EAAwBC,IAErB,IAAXrmB,GAEAimB,EAAepmB,OAAOG,EAAO,EAErC,MAEIimB,EAAiB,EAEzB,EA2JI+B,uBAtJJ,WACI/B,EAAiB,EACrB,EAqJIgC,yBAnHJ,SAAkCnB,GAC9BX,EAAkBR,EAAqBmB,EAC3C,EAkHIoB,0BA5GJ,SAAmCpB,GAC/BX,EAAkBP,EAAsBkB,EAC5C,EA2GIP,wBACA7lB,MA7VJ,WACIwlB,GACJ,EA4VIiC,yCA3UJ,WACInC,EAAsC,IAC1C,EA0UIoC,+BA9CJ,WACI,IAAIC,EAAyBvtB,EAASV,MAAM8I,UAAU6E,mBAAmBS,oBACzE8d,EAAmB+B,EAAuB5f,OAAQ4f,EAAuBtqB,MAC7E,EA4CIuqB,UA5VJ,WAEA,EA2VIC,uCArVJ,SAAgDC,GAC5CxC,EAAsCwC,CAC1C,EAoVIC,6BA5CJ,SAASA,EAA6B5pB,EAAMd,GACnCc,EAKD6mB,EAAmB7mB,KAAUd,EAJ7B2qB,OAAOC,KAAKjD,GAAoBpoB,SAAQwU,IACpC2W,EAA6B3W,EAAK/T,EAAM,GAKpD,EAqCI6qB,mCA1PJ,SAA4CroB,GACxC4lB,EAAkBJ,EAA2BxlB,EACjD,EAyPIsoB,+BA7RJ,SAAwCtoB,GACpC4lB,EAAkBN,EAAuBtlB,EAC7C,EA4RIuoB,gCAtRJ,SAAyCvoB,GACrC4lB,EAAkBL,EAAwBvlB,EAC9C,GAnGImlB,EAAqB,CACjBiC,SApByB,GAsB7BzB,IAyXGlrB,CACX,CAEAwqB,EAAsBvnB,sBAAwB,wBAC9C,IAAeC,EAAAA,EAAaC,oBAAoBqnB,E,0DCtZhD,SAASuD,IAoHL,MAAO,CACHC,qBApHuB,GAqHvB55B,OApHS,EAqHT65B,IAnHJ,SAAax3B,EAAOJ,GAChB,IAAIlC,EAGJ,IAAKA,EAAI,EAAIA,EAAI0H,KAAKmyB,qBAAqB55B,QAAYqC,EAAQoF,KAAKmyB,qBAAqB75B,GAAGsC,MAAQtC,KAIpG,IAFA0H,KAAKmyB,qBAAqBnpB,OAAO1Q,EAAG,EAAG,CAAEsC,MAAOA,EAAOJ,IAAKA,IAEvDlC,EAAI,EAAGA,EAAI0H,KAAKmyB,qBAAqB55B,OAAS,EAAGD,IAC9C0H,KAAKqyB,YAAY/5B,EAAGA,EAAI,IACxBA,IAGR0H,KAAKzH,OAASyH,KAAKmyB,qBAAqB55B,MAC5C,EAsGI+5B,MApGJ,WACItyB,KAAKmyB,qBAAuB,GAC5BnyB,KAAKzH,OAAS,CAClB,EAkGIg6B,OAhGJ,SAAgB33B,EAAOJ,GACnB,IAAK,IAAIlC,EAAI,EAAGA,EAAI0H,KAAKmyB,qBAAqB55B,OAAQD,IAClD,GAAIsC,GAASoF,KAAKmyB,qBAAqB75B,GAAGsC,OAASJ,GAAOwF,KAAKmyB,qBAAqB75B,GAAGkC,IASnFwF,KAAKmyB,qBAAqBnpB,OAAO1Q,EAAG,GACpCA,QAEG,IAAIsC,EAAQoF,KAAKmyB,qBAAqB75B,GAAGsC,OAASJ,EAAMwF,KAAKmyB,qBAAqB75B,GAAGkC,IAAK,CAG7FwF,KAAKmyB,qBAAqBnpB,OAAO1Q,EAAI,EAAG,EAAG,CAAEsC,MAAOJ,EAAKA,IAAKwF,KAAKmyB,qBAAqB75B,GAAGkC,MAC3FwF,KAAKmyB,qBAAqB75B,GAAGkC,IAAMI,EACnC,KACJ,CAAWA,EAAQoF,KAAKmyB,qBAAqB75B,GAAGsC,OAASA,EAAQoF,KAAKmyB,qBAAqB75B,GAAGkC,IAM1FwF,KAAKmyB,qBAAqB75B,GAAGkC,IAAMI,EAC5BJ,EAAMwF,KAAKmyB,qBAAqB75B,GAAGsC,OAASJ,EAAMwF,KAAKmyB,qBAAqB75B,GAAGkC,MAMtFwF,KAAKmyB,qBAAqB75B,GAAGsC,MAAQJ,EACzC,CAGJwF,KAAKzH,OAASyH,KAAKmyB,qBAAqB55B,MAC5C,EA2DI85B,YAzDJ,SAAqBG,EAAaC,GAC9B,IAAIC,EAAS1yB,KAAKmyB,qBAAqBK,GACnCG,EAAS3yB,KAAKmyB,qBAAqBM,GAEvC,OAAIC,EAAO93B,OAAS+3B,EAAO/3B,OAAS+3B,EAAO/3B,OAAS83B,EAAOl4B,KAAOk4B,EAAOl4B,KAAOm4B,EAAOn4B,KAGnFk4B,EAAOl4B,IAAMm4B,EAAOn4B,IACpBwF,KAAKmyB,qBAAqBnpB,OAAOypB,EAAa,IACvC,GAEAE,EAAO/3B,OAAS83B,EAAO93B,OAAS83B,EAAO93B,OAAS+3B,EAAOn4B,KAAOm4B,EAAOn4B,KAAOk4B,EAAOl4B,KAG1Fk4B,EAAO93B,MAAQ+3B,EAAO/3B,MACtBoF,KAAKmyB,qBAAqBnpB,OAAOypB,EAAa,IACvC,GACAE,EAAO/3B,OAAS83B,EAAO93B,OAAS83B,EAAO93B,OAAS+3B,EAAOn4B,KAAOk4B,EAAOl4B,KAAOm4B,EAAOn4B,KAG1FwF,KAAKmyB,qBAAqBnpB,OAAOwpB,EAAa,IACvC,GACAE,EAAO93B,OAAS+3B,EAAO/3B,OAAS+3B,EAAO/3B,OAAS83B,EAAOl4B,KAAOm4B,EAAOn4B,KAAOk4B,EAAOl4B,MAG1FwF,KAAKmyB,qBAAqBnpB,OAAOypB,EAAa,IACvC,EAGf,EA6BI73B,MA3BJ,SAAeuO,GAGX,OAFAypB,EAAAA,EAAAA,IAAazpB,GAETA,GAASnJ,KAAKmyB,qBAAqB55B,QAAU4Q,EAAQ,EAC9CwF,IAGJ3O,KAAKmyB,qBAAqBhpB,GAAOvO,KAC5C,EAoBIJ,IAlBJ,SAAa2O,GAGT,OAFAypB,EAAAA,EAAAA,IAAazpB,GAETA,GAASnJ,KAAKmyB,qBAAqB55B,QAAU4Q,EAAQ,EAC9CwF,IAGJ3O,KAAKmyB,qBAAqBhpB,GAAO3O,GAC5C,EAYJ,CAEA03B,EAAiB9qB,sBAAwB,mBACzC,IAAeC,EAAAA,EAAaiE,gBAAgB4mB,E,0GClIrC,SAASrC,EAAmBgD,EAAW7qB,GAC1C,UAAW6qB,IAAc7qB,EACrB,MAAMqF,EAAAA,EAAUic,kBAExB,CAEO,SAASsJ,EAAaC,GAGzB,GAF4B,OAAdA,GAAuBC,MAAMD,IAAeA,EAAY,GAAM,EAGxE,MAAMxlB,EAAAA,EAAUic,mBAAqB,+BAE7C,C,oECRA,MAAMlV,EAIF2M,WAAAA,GAKI/gB,KAAK+yB,MAAQ,KAab/yB,KAAKgI,KAAO,KAKZhI,KAAK8a,IAAM,KAKX9a,KAAKgzB,UAAY,KAKjBhzB,KAAKizB,MAAQ,KAKbjzB,KAAKkzB,SAAW,KAKhBlzB,KAAKmzB,UAAY,KAKjBnzB,KAAKozB,aAAe,KAKpBpzB,KAAKqzB,SAAW,KAKhBrzB,KAAKszB,MAAQ,GAKbtzB,KAAKkZ,KAAO,KAMZlZ,KAAKuzB,QAAU,KAKfvzB,KAAKwzB,SAAW,KAKhBxzB,KAAKyzB,eAAiB,KAKtBzzB,KAAK0zB,iBAAmB,KAKxB1zB,KAAK2zB,iBAAmB,KAIxB3zB,KAAK4zB,gBAAkB,KAIvB5zB,KAAK6zB,sBAAwB,IACjC,EA8BJzf,EAAY0f,IAAM,MAClB1f,EAAY2f,KAAO,OACnB3f,EAAYC,SAAW,MACvBD,EAAYE,qBAAuB,iBACnCF,EAAYI,kBAAoB,wBAChCJ,EAAYM,mBAAqB,eACjCN,EAAYG,mBAAqB,eACjCH,EAAYK,iCAAmC,4BAC/CL,EAAYO,+BAAiC,sBAC7CP,EAAY4f,mBAAqB,eACjC5f,EAAYQ,QAAU,UACtBR,EAAY6f,sBAAwB,kBACpC7f,EAAYS,WAAa,O,GCnLrBqf,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBl7B,IAAjBm7B,EACH,OAAOA,EAAah9B,QAGrB,IAAIC,EAAS48B,EAAyBE,GAAY,CAGjD/8B,QAAS,CAAC,GAOX,OAHAi9B,EAAoBF,GAAUn0B,KAAK3I,EAAOD,QAASC,EAAQA,EAAOD,QAAS88B,GAGpE78B,EAAOD,OACf,CCtBA88B,EAAoBI,KAAO,CAAC,ECC5BJ,EAAoBK,EAAI,SAASn9B,EAASo9B,GACzC,IAAI,IAAIxZ,KAAOwZ,EACXN,EAAoBO,EAAED,EAAYxZ,KAASkZ,EAAoBO,EAAEr9B,EAAS4jB,IAC5E4W,OAAO8C,eAAet9B,EAAS4jB,EAAK,CAAE2Z,YAAY,EAAMrxB,IAAKkxB,EAAWxZ,IAG3E,ECPAkZ,EAAoBO,EAAI,SAASrqB,EAAK3G,GAAQ,OAAOmuB,OAAOgD,UAAUrrB,eAAevJ,KAAKoK,EAAK3G,EAAO,E,8ECkCtG,MAAMoxB,EACF/T,WAAAA,GACI/gB,KAAK+0B,OAAS,KAId/0B,KAAKg1B,UAAY,KAoBjBh1B,KAAKi1B,OAAS,KAIdj1B,KAAK8a,IAAM,KAOX9a,KAAKk1B,UAAY,KAOjBl1B,KAAKm1B,gBAAkB,IAK3B,EAGJL,EAAUM,6BAA+B,MACzCN,EAAUO,sBAAwB,MAClCP,EAAUQ,iBAAmB,MAC7BR,EAAUS,mBAAqB,MAC/BT,EAAUU,iBAAmB,MAC7BV,EAAUW,sBAAwB,MAClCX,EAAUY,oBAAsB,MAChCZ,EAAUa,iBAAmB,MAC7Bb,EAAUc,gBAAkB,MAE5B,Q,kBC7DA,SAASC,EAAoBjyB,GAGzB,IAAIO,EACA2xB,EACJ,MAAMhyB,GAHNF,EAASA,GAAU,CAAC,GAGIE,SAClBiyB,EAAcnyB,EAAOmyB,YACrBC,EAAmBpyB,EAAOoyB,iBAE1BlvB,EAASlD,EAAOqK,OAEtB,SAASgoB,EAAOtG,GACZ,IAAI+E,EAAI,IAAII,EAEZ,GAAKgB,EAAL,CAIA,IAAK,MAAM7a,KAAO0U,EACVA,EAAGnmB,eAAeyR,KAClByZ,EAAEzZ,GAAO0U,EAAG1U,IAIfyZ,EAAEK,SACHL,EAAEK,OAASe,EAAIrY,aAAeqY,EAAIhb,KAGjC4Z,EAAEO,SACHP,EAAEO,OAAS,IAAI/uB,MAGnB6vB,EAAYG,aAAaxB,EAhBzB,CAiBJ,CAEA,SAASyB,EAAiB9a,GAClBA,EAAEnW,QAIN4wB,EAAMza,EAAE+a,SACZ,CAEA,SAASC,EAAyBhb,GAC9B4a,EAAO,CACHjB,UAAWF,EAAUa,iBACrBR,gBAAiB9Z,EAAEib,OAE3B,CAEA,SAASC,IACLN,EAAO,CACHjB,UAAWF,EAAUc,iBAE7B,CAiBA,SAASY,EAAcnb,GAfvB,IAA0BsU,EAgBdtU,EAAEob,SACDT,EAAiBU,eAhBD,KADH/G,EAkBGtU,EAAEnU,OAjBnBksB,cACgB,MAAnBzD,EAAGyD,cACHzD,EAAGyD,cAAgB,KACnBzD,EAAGyD,aAAe,KAClBzD,EAAGyD,cAAgB,MACpB6C,EAAO,CACHjB,UAAWrF,EAAGyD,cAAgB0B,EAAUU,iBACxC1a,IAAK6U,EAAG7U,IACRma,OAAQtF,EAAGwD,UACXgC,gBAAiBxF,EAAGgE,kBAahC,CAEA,SAASgD,EAAgBtb,GACrB,IACI2Z,EAEJ,OAHa3Z,EAAEnW,MAAQmW,EAAEnW,MAAMjN,KAAO,GAIlC,KAAK2+B,WAAWC,kBACZ7B,EAAYF,EAAUU,iBACtB,MACJ,KAAKoB,WAAWE,iBACZ9B,EAAYF,EAAUY,oBACtB,MACJ,QACI,OAGRO,EAAO,CACHjB,UAAWA,GAEnB,CAyCA,OALA7wB,EAAW,CACP4yB,WAnCJ,WACIjzB,EAASsF,GAAGtC,EAAOsb,iBAAkB+T,EAAkBhyB,GACvDL,EAASsF,GACLtC,EAAO8b,4CACPyT,EACAlyB,GAEJL,EAASsF,GAAGtC,EAAOse,aAAcoR,EAAeryB,GAChDL,EAASsF,GAAGtC,EAAOue,eAAgBmR,EAAeryB,GAClDL,EAASsF,GAAGtC,EAAOggB,eAAgB6P,EAAiBxyB,GACpDL,EAASsF,GACLklB,EAAAA,EAAuBE,wBACvB+H,EACApyB,EAER,EAqBI0F,MAnBJ,WACI/F,EAASoF,IAAIpC,EAAOsb,iBAAkB+T,EAAkBhyB,GACxDL,EAASoF,IACLpC,EAAO8b,4CACPyT,EACAlyB,GAEJL,EAASoF,IAAIpC,EAAOse,aAAcoR,EAAeryB,GACjDL,EAASoF,IAAIpC,EAAOue,eAAgBmR,EAAeryB,GACnDL,EAASoF,IAAIpC,EAAOggB,eAAgB6P,EAAiBxyB,GACrDL,EAASoF,IACLolB,EAAAA,EAAuBE,wBACvB+H,EACApyB,EAER,GAOOA,CACX,CAEA0xB,EAAoBzuB,sBAAwB,sBAC5C,MAAeC,EAAAA,EAAaC,oBAAoBuuB,G,UCjJhD,SAASmB,EAAgBpzB,GAErBA,EAASA,GAAU,CAAC,EACpB,IAEIO,EACA8yB,EAHAC,GAAmB,EACnBrzB,EAAU7D,KAAK6D,QAIfszB,EAAevzB,EAAOuzB,aAyD1B,OARAhzB,EAAW,CACP4yB,WAhDJ,SAAoBK,GACZA,GAAMA,EAAG7+B,SACT6+B,EAAG3wB,SAAQkU,IACP,IAAI/f,EAAQ+f,EAAE0c,UACV78B,EAAMI,EAAQ+f,EAAE2c,SAEpBL,EAAO7E,IAAIx3B,EAAOJ,EAAI,IAG1B08B,IAAqBE,EAAG,GAAGG,kBAEnC,EAsCI1tB,MApCJ,WACIotB,EAAO3E,OACX,EAmCIkF,UA7BJ,WACI,IACIC,EADAC,EAAYT,EAAO1+B,OAGvB,IAAKm/B,EACD,OAAO,EAKXD,EAAOP,GACF,IAAIhxB,MAAOC,UAAY,IACxBgxB,EAAaQ,YAEjB,IAAK,IAAIr/B,EAAI,EAAGA,EAAIo/B,EAAWp/B,GAAK,EAAG,CACnC,IAAIsC,EAAQq8B,EAAOr8B,MAAMtC,GACrBkC,EAAMy8B,EAAOz8B,IAAIlC,GAErB,GAAKsC,GAAS68B,GAAUA,EAAOj9B,EAC3B,OAAO,CAEf,CAEA,OAAO,CACX,GA3BIy8B,GAAS/E,EAAAA,EAAAA,GAAiBruB,GAAS0H,SAqChCpH,CACX,CAEA6yB,EAAgB5vB,sBAAwB,kBACxC,MAAeC,EAAAA,EAAaiE,gBAAgB0rB,GCnE5C,SAASY,IA2DL,MAAO,CACHC,UAvDJ,SAASA,EAAUpB,GACf,IAEIxb,EACA/T,EAHA4wB,EAAQ,GACRztB,EAAM,GASV,IAAK4Q,KAAOwb,EACR,GAAIA,EAAOjtB,eAAeyR,IAA8B,IAArBA,EAAIpc,QAAQ,KAAa,CAUxD,GATAqI,EAAQuvB,EAAOxb,GAIV/T,UACDA,EAAQ,IAIRnC,MAAMgzB,QAAQ7wB,GAAQ,CAEtB,IAAKA,EAAM3O,OACP,SAGJ8R,EAAM,GAENnD,EAAMT,SAAQ,SAAUuxB,GACpB,IAAIC,EAA+D,WAAnDpG,OAAOgD,UAAUzX,SAASnd,KAAK+3B,GAAGr/B,MAAM,GAAI,GAE5D0R,EAAIpB,KAAKgvB,EAAYD,EAAIH,EAAUG,GACvC,IAEA9wB,EAAQmD,EAAIjK,IAAIgb,oBAAoB7hB,KAAK,IAC7C,KAA4B,iBAAV2N,EACdA,EAAQkU,mBAAmBlU,GACpBA,aAAiBhB,KACxBgB,EAAQA,EAAMgxB,cACU,iBAAVhxB,IACdA,EAAQ+V,KAAKkb,MAAMjxB,IAGvB4wB,EAAM7uB,KAAKgS,EAAM,IAAM/T,EAC3B,CAKJ,OAAO4wB,EAAMv+B,KAAK,IACtB,EAKJ,CAEAq+B,EAAiBxwB,sBAAwB,mBACzC,MAAeC,EAAAA,EAAaC,oBAAoBswB,GCjEhD,SAASQ,IAIL,IAWIC,EACAlvB,EACAhF,EAbAm0B,EAASx8B,OAAOw8B,QAAUx8B,OAAOy8B,SAGjCC,EAAYC,YACZC,EAAYzb,KAAK0b,IAAI,EAAiC,EAA9BH,EAAUjZ,mBAAyB,EAW/D,SAASwX,IACDuB,IACKD,IACDA,EAAgB,IAAIG,EATP,KAWjBF,EAAOM,gBAAgBP,GACvBlvB,EAAQ,EAEhB,CAiCA,OANAhF,EAAW,CACP+Y,OA1BJ,SAAcrK,EAAKC,GACf,IAAI6H,EAqBJ,OAnBK9H,IACDA,EAAM,GAGLC,IACDA,EAAM,GAGNwlB,GACInvB,IAAUkvB,EAAc9/B,QACxBw+B,IAGJpc,EAAI0d,EAAclvB,GAASuvB,EAC3BvvB,GAAS,GAETwR,EAAIsC,KAAKC,SAGLvC,GAAK7H,EAAMD,GAAQA,CAC/B,GAMAkkB,IAEO5yB,CACX,CAEAi0B,EAAIhxB,sBAAwB,MAC5B,MAAeC,EAAAA,EAAaC,oBAAoB8wB,G,UChEhD,SAASS,EAAaj1B,GAElB,IAAIO,EADJP,EAASA,GAAU,CAAC,EAGpB,IACIk1B,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAPAv1B,EAAU7D,KAAK6D,QAWfw1B,EAAkB,GAEtB,MAAMrD,EAAmBpyB,EAAOoyB,iBAuHhC,SAASsD,IACLL,GAA+B,EAC/BC,GAAoB,EACpBC,EAAe,KACfC,EAAkB,IACtB,CAmBA,OARAj1B,EAAW,CACP8xB,OA3FJ,SAAgBjuB,EAAMuxB,GACbx0B,MAAMgzB,QAAQwB,KACfA,EAAM,CAACA,IAOPL,GAAqBE,EAAgB5B,aAIrC+B,EAAI9yB,SAAQ,SAAUkpB,GAClB,IAAI7U,EAAMge,EAAiBjB,UAAUlI,GAGV3nB,IAASguB,EAAiBwD,aACjD1e,EAAM,cAAc9S,KAAQ8S,KAMhCA,EAAM,GAAGqe,KAAgBre,IA3DrC,SAAsBA,EAAK2e,EAAWC,GAClC,IAAIC,EAAM,IAAIC,eACdD,EAAIE,gBAAkBd,EAAsBnI,6BAA6BoF,EAAiB8D,iCAC1F,MAAMC,EAAa,WACf,IAAIC,EAAWX,EAAgBx6B,QAAQ86B,IAErB,IAAdK,IAGAX,EAAgBrwB,OAAOgxB,EAAU,KAGhCL,EAAIM,QAAU,KAASN,EAAIM,OAAS,OAKjCP,GACAA,KAGZ,EAEAL,EAAgBpwB,KAAK0wB,GAErB,IACIA,EAAIO,KAAK,MAAOpf,GAChB6e,EAAIQ,UAAYJ,EAChBJ,EAAIS,QAAUL,EACdJ,EAAIU,MACR,CAAE,MAAOhf,GACLse,EAAIS,SACR,CACJ,CA8BYE,CAAaxf,EAAK,GAAM,WAOpBoe,GAAoB,CACxB,GACJ,GAER,EAqDInC,WAnDJ,SAAoBT,EAAOiE,GACvB,IAAIC,EAQJ,GANApB,EAAkBmB,EAElBpB,EAAe7C,EAAMmE,iBAIhBtB,EACD,MAAM,IAAI9wB,MACN,iDAOH4wB,IACDuB,EAAclE,EAAMoE,eAMhBF,IAAgC,MAAhBA,GAA0BA,EAAc,KAASxB,EAAsB9b,YACvFgc,GAAoB,GAGxBD,GAA+B,EAEvC,EAqBIpvB,MAZJ,WAMIyvB,GACJ,GAlIIR,EAAmBlB,EAAiB/zB,GAASG,cAC7Cg1B,EAAwBZ,EAAIv0B,GAASG,cACrC+0B,GAAwBpK,EAAAA,EAAAA,GAAsB9qB,GAASG,cAEvDs1B,IAwIGn1B,CACX,CAEA00B,EAAazxB,sBAAwB,eACrC,MAAeC,EAAAA,EAAaiE,gBAAgButB,GCtK5C,SAAS8B,EAAiB/2B,GACtBA,EAASA,GAAU,CAAC,EAEpB,MAAMg3B,EAA6B,CAC/B,8BAA+B/B,GAG7Bh1B,EAAU7D,KAAK6D,QACrB,IAAIM,EACJ,MAAM02B,EAASj3B,EAAO+B,MAAQ/B,EAAO+B,MAAMqB,UAAU7C,GAAY,CAAC,EAC5D6xB,EAAmBpyB,EAAOoyB,iBAC1B8E,EAAmBl3B,EAAOk3B,kBAAoB,CAAC,EAkCrD,OANA32B,EAAW,CACPoH,OA3BJ,SAAgB+qB,EAAO8C,GACnB,IAAI2B,EAEJ,IACIA,EAAYH,EAA2BtE,EAAMlpB,aAAavJ,GAAS0H,OAAO,CACtEyqB,iBAAkBA,EAClB8E,iBAAkBA,IAGtBC,EAAUhE,WAAWT,EAAO8C,EAChC,CAAE,MAAO/d,GACL0f,EAAY,KACZF,EAAO31B,MAAM,iEAAiEoxB,EAAMlpB,gBAAgBiO,EAAErV,WAC1G,CAEA,OAAO+0B,CACX,EAYIC,SAVJ,SAAkB5tB,EAAa6tB,GAC3BL,EAA2BxtB,GAAe6tB,CAC9C,EASIC,WAPJ,SAAoB9tB,UACTwtB,EAA2BxtB,EACtC,GAQOjJ,CACX,CAEAw2B,EAAiBvzB,sBAAwB,mBACzC,MAAeC,EAAAA,EAAaC,oBAAoBqzB,GCjDhD,SAASQ,EAAoBv3B,GAEzB,IACIO,EADAi3B,EAAY,GAGhB,MAAMC,EAAmBV,EAAiB36B,KAAK6D,SAASG,YAAYJ,GAgCpE,OANAO,EAAW,CACP4yB,WAzBJ,SAAoBgE,EAAW3B,GAK3B2B,EAAUjyB,MAAK6R,IACX,IAAI2gB,EAAWD,EAAiB9vB,OAAOoP,EAAGye,GAE1C,GAAIkC,EAEA,OADAF,EAAUnyB,KAAKqyB,IACR,CACX,GAER,EAaIzxB,MAXJ,WACIuxB,EAAU30B,SAAQkU,GAAKA,EAAE9Q,UACzBuxB,EAAY,EAChB,EASInF,OAPJ,SAAgBjuB,EAAMuxB,GAClB6B,EAAU30B,SAAQkU,GAAKA,EAAEsb,OAAOjuB,EAAMuxB,IAC1C,GAQOp1B,CACX,CAEAg3B,EAAoB/zB,sBAAwB,sBAC5C,MAAeC,EAAAA,EAAaiE,gBAAgB6vB,GCvC5C,SAASI,IACL,MAAO,CACHC,0BAA2B,SAAUvgB,EAAKf,EAAGlS,GACzC,IAAIyzB,EAAKxgB,EAYT,OAVIf,IACAuhB,GAAM,IAAMvhB,EAERlS,GAAQA,EAAKzP,SACbkjC,GAAM,IAAMzzB,GAGhByzB,GAAM,KAGHA,CACX,EAEAC,UAAW,SAAUC,GACjB,IAAKA,EACD,MAAM,IAAItzB,MAAM,aAGpB,GAAIyqB,MAAM6I,GACN,MAAM,IAAItzB,MAAM,YAKpB,GAAIszB,EAAO,EACP,MAAM,IAAItzB,MAAM,sBAGpB,OAAOszB,CACX,EAER,CAEAJ,EAAen0B,sBAAwB,iBACvC,MAAeC,EAAAA,EAAaC,oBAAoBi0B,GCzChD,SAASK,EAAmBh4B,GAGxB,IAAIO,EACA03B,EACA3hB,EACA1e,EACA63B,EACAyI,EANJl4B,EAASA,GAAU,CAAC,EAQpB,IAAIC,EAAU7D,KAAK6D,QACfk4B,EAAiBR,EAAe13B,GAASG,cAEzCg4B,EAAY,GAEhB,MAAMhG,EAAmBpyB,EAAOoyB,iBAgBhC,SAASiG,IACL,IAAItM,EAfR,WACI,IACI,OAAOkC,OAAOC,KAAKkK,GAAW57B,KAC1B6a,GAAO+gB,EAAU/gB,KACnBihB,QACE,CAACC,EAAGC,IACQD,EAAEr2B,MAAQs2B,EAAEt2B,MAASq2B,EAAIC,GAG7C,CAAE,MAAO/gB,GACL,MACJ,CACJ,CAGaghB,GAEL1M,GACImM,IAAqBnM,EAAG2M,IACxBR,EAAmBnM,EAAG2M,EACtBT,EAAoB5F,OAAOz6B,EAAMm0B,GAG7C,CAiCA,OANAxrB,EAAW,CACP4yB,WA1BJ,SAAoBr8B,EAAU6/B,EAAIoB,GAC1BpB,IAGArgB,EAAI6hB,EAAeL,UAAUC,GAC7BE,EAAsBtB,EACtB/+B,EAAOugC,EAAeP,0BAA0B9gC,EAAUihC,GAC1DtI,EAAWkJ,YAAYN,EAAkB/hB,GAEjD,EAkBIrQ,MAhBJ,WACI2yB,cAAcnJ,GACdA,EAAW,KACXnZ,EAAI,EACJ2hB,EAAsB,KACtBC,EAAmB,IACvB,EAWIW,gBATJ,SAAyBhG,EAAQ9G,EAAI3nB,GAC7ByuB,IAAWT,EAAiB0G,eAC5BV,EAAUh0B,GAAQ2nB,EAE1B,GAQOxrB,CACX,CAEAy3B,EAAmBx0B,sBAAwB,qBAC3C,MAAeC,EAAAA,EAAaiE,gBAAgBswB,GCrB5C,EAAev0B,EAAAA,EAAaiE,iBAxD5B,SAA0B1H,GAGtB,IAAIO,EACA03B,EAEA/3B,GAJJF,EAASA,GAAU,CAAC,GAIEE,SACtB,MAAMkyB,EAAmBpyB,EAAOoyB,iBAEhC,SAAS2G,IAEL74B,EAASoF,IACLolB,EAAAA,EAAuBC,gCACvBoO,EACA38B,MAKJ8D,EAAS+C,QAAQynB,EAAAA,EAAuBE,wBAC5C,CAiCA,OANArqB,EAAW,CACP4yB,WA1BJ,SAAoB6F,EAAQrC,GACpBA,IACAsB,EAAsBtB,EAEtBz2B,EAASsF,GACLklB,EAAAA,EAAuBC,gCACvBoO,EACA38B,MAGZ,EAiBI6J,MAfJ,WACIgyB,EAAsB,IAC1B,EAcIY,gBAZJ,SAAyBhG,EAAQ9G,GAEzB8G,IAAWT,EAAiBwD,YACxBqC,GACAA,EAAoB5F,OAAOQ,EAAQ9G,EAG/C,GAQOxrB,CACX,ICtDA,SAAS04B,EAAgBj5B,GAGrB,IAAIO,EACA03B,EACA3hB,EACAlS,EACAxM,EACA63B,EANJzvB,EAASA,GAAU,CAAC,EAQpB,IAAIk5B,EAAY,GAEZf,EAAiBR,EAAev7B,KAAK6D,SAASG,cAElD,MAAMgyB,EAAmBpyB,EAAOoyB,iBAEhC,SAASiG,IACL,IAAI1C,EAAMuD,EAENvD,EAAIhhC,QACAsjC,GACAA,EAAoB5F,OAAOz6B,EAAM+9B,GAIzCuD,EAAY,EAChB,CAgDA,OANA34B,EAAW,CACP4yB,WAzCJ,SAAoBr8B,EAAU6/B,EAAIoB,EAAMoB,GAChCxC,IAIArgB,EAAI6hB,EAAeL,UAAUC,GAE7BE,EAAsBtB,EAElBwC,GAAeA,EAAYxkC,SAC3ByP,EAAO+0B,GAGXvhC,EAAOugC,EAAeP,0BAClB9gC,EACAihC,EACAoB,GAGJ1J,EAAWkJ,YAAYN,EAAkB/hB,GAEjD,EAqBIrQ,MAnBJ,WACI2yB,cAAcnJ,GACdA,EAAW,KACXnZ,EAAI,KACJlS,EAAO,KACP80B,EAAY,GACZjB,EAAsB,IAC1B,EAaIY,gBAXJ,SAAyBhG,EAAQ9G,GACzB8G,IAAWT,EAAiBU,eACvB1uB,GAASA,IAAS2nB,EAAG3nB,MACtB80B,EAAU7zB,KAAK0mB,GAG3B,GAQOxrB,CACX,CAEA04B,EAAgBz1B,sBAAwB,kBACxC,MAAeC,EAAAA,EAAaiE,gBAAgBuxB,GC5E5C,SAASG,IAEL,IAAI74B,EACA84B,EACApB,EA2BJ,OANA13B,EAAW,CACP4yB,WApBJ,SAAoBv7B,EAAM++B,GACtB0C,EAAazhC,EACbqgC,EAAsBtB,CAC1B,EAkBI1wB,MAhBJ,WACIgyB,EAAsB,KACtBoB,OAAa/jC,CACjB,EAcIujC,gBAZJ,SAAyBhG,EAAQ9G,GAEzB8G,IAAWwG,GACPpB,GACAA,EAAoB5F,OAAOgH,EAAYtN,EAGnD,GAQOxrB,CACX,CAEA64B,EAAqB51B,sBAAwB,uBAC7C,MAAeC,EAAAA,EAAaiE,gBAAgB0xB,GClC5C,SAASE,EAAsBt5B,GAG3B,IAAIO,EACJ,MAAM02B,GAFNj3B,EAASA,GAAU,CAAC,GAEE+B,MAAQ/B,EAAO+B,MAAMqB,UAAU7C,GAAY,CAAC,EAGlE,IAAIg5B,EAAW,gDAEf,MAAMt5B,EAAU7D,KAAK6D,QACrB,IAAIu5B,EAAuB,CACvBC,YAAgBA,EAChBvI,UAAgBA,EAChBwI,SAAgBA,EAChBC,SAAgBP,EAChBQ,cAAgBR,EAChBS,QAAgBT,GA6CpB,OANA74B,EAAW,CACPoH,OArCJ,SAAgBmyB,EAAU7B,GACtB,IACIrzB,EADA7I,EAAU+9B,EAAS99B,MAAMu9B,GAG7B,GAAKx9B,EAAL,CAIA,KACI6I,EAAU40B,EAAqBz9B,EAAQ,IAAIkE,GAAS0H,OAAO,CACvDzH,SAAUF,EAAOE,SACjBkyB,iBAAkBpyB,EAAOoyB,oBAGrBe,WACJp3B,EAAQ,GACRk8B,EACAl8B,EAAQ,GACRA,EAAQ,GAEhB,CAAE,MAAO0b,GACL7S,EAAU,KACVqyB,EAAO31B,MAAM,4DAA4DvF,EAAQ,gBAAgBA,EAAQ,OAAOA,EAAQ,OAAO0b,EAAErV,WACrI,CAEA,OAAOwC,CAnBP,CAoBJ,EAYIwyB,SAVJ,SAAkB/f,EAAKzS,GACnB40B,EAAqBniB,GAAOzS,CAChC,EASI0yB,WAPJ,SAAoBjgB,UACTmiB,EAAqBniB,EAChC,GAQO9W,CACX,CAEA+4B,EAAsB91B,sBAAwB,wBAC9C,MAAeC,EAAAA,EAAaC,oBAAoB41B,GCpEhD,SAASS,EAA0B/5B,GAE/BA,EAASA,GAAU,CAAC,EACpB,IAEIO,EAFA2D,EAAW,GAGf,MAAMjE,EAAU7D,KAAK6D,QACfC,EAAWF,EAAOE,SAClBgD,EAASlD,EAAOqK,OAEtB,IAAI2vB,EAAwBV,EAAsBr5B,GAASG,YAAY,CACnE2B,MAAO/B,EAAO+B,MACd7B,SAAUF,EAAOE,SACjBkyB,iBAAkBpyB,EAAOoyB,mBAG7B,SAAS6H,EAAOxiB,GACZvT,EAASrB,SAAQ+B,IACbA,EAAQi0B,gBAAgBphB,EAAEob,OAAQpb,EAAEnU,MAAOmU,EAAEzS,UAAU,GAE/D,CAsEA,OALAzE,EAAW,CACP4yB,WAhEJ,SAAoBxoB,EAASstB,GACzBttB,EAAQzM,MAAM,KAAK2E,SACf,CAACq3B,EAAGC,EAAMC,KACN,IAAIx1B,EAKJ,IAAyB,IAApBs1B,EAAEj/B,QAAQ,OAAoC,IAApBi/B,EAAEj/B,QAAQ,KAAa,CAClD,IAAIo/B,EAAQD,EAAGD,EAAO,GAElBE,IAC6B,IAAxBA,EAAMp/B,QAAQ,OACU,IAAxBo/B,EAAMp/B,QAAQ,OACnBi/B,GAAK,IAAMG,SAGJD,EAAGD,EAAO,GAEzB,CAEAv1B,EAAUo1B,EAAsBryB,OAC5BuyB,EACAjC,GAGArzB,GACAV,EAASmB,KAAKT,EAClB,IAIR1E,EAASsF,GACLtC,EAAOse,aACPyY,EACA15B,GAGJL,EAASsF,GACLtC,EAAOue,eACPwY,EACA15B,EAER,EAsBI0F,MApBJ,WACI/F,EAASoF,IACLpC,EAAOse,aACPyY,EACA15B,GAGJL,EAASoF,IACLpC,EAAOue,eACPwY,EACA15B,GAGJ2D,EAASrB,SAAQ+B,GAAWA,EAAQqB,UAEpC/B,EAAW,EACf,GAOO3D,CACX,CAEAw5B,EAA0Bv2B,sBAAwB,4BAClD,MAAeC,EAAAA,EAAaiE,gBAAgBqyB,GC5F5C,SAASO,EAAkBt6B,GAGvB,IAAIu6B,EACAtC,EACAzC,EACAj1B,EAJJP,EAASA,GAAU,CAAC,EAMpB,IAAIC,EAAU7D,KAAK6D,QAgCnB,SAASgG,IACDs0B,GACAA,EAA0Bt0B,QAG1BgyB,GACAA,EAAoBhyB,QAGpBuvB,GACAA,EAAgBvvB,OAExB,CAOA,OALA1F,EAAW,CACP4yB,WA7CJ,SAAoBqH,GAChB,IACIhF,EAAkBpC,EAAgBnzB,GAAS0H,OAAO,CAC9C4rB,aAAcvzB,EAAOuzB,eAGzBiC,EAAgBrC,WAAWqH,EAAaC,OAExCxC,EAAsBV,EAAoBt3B,GAAS0H,OAAO,CACtD5F,MAAO/B,EAAO+B,MACdqwB,iBAAkBpyB,EAAOoyB,iBACzB8E,iBAAkBl3B,EAAOk3B,mBAG7Be,EAAoB9E,WAAWqH,EAAaE,UAAWlF,GAEvD+E,EAA4BR,EAA0B95B,GAAS0H,OAAO,CAClE5F,MAAO/B,EAAO+B,MACd7B,SAAUF,EAAOE,SACjBkyB,iBAAkBpyB,EAAOoyB,iBACzB/nB,OAAQrK,EAAOqK,SAGnBkwB,EAA0BpH,WAAWqH,EAAa7vB,QAASstB,EAC/D,CAAE,MAAOxgB,GAEL,MADAxR,IACMwR,CACV,CACJ,EAkBIxR,MAAYA,GAGT1F,CACX,CAEA+5B,EAAkB92B,sBAAwB,oBAC1C,MAAeC,EAAAA,EAAaiE,gBAAgB4yB,GCxD5C,EATA,MACInd,WAAAA,GAEI/gB,KAAKuO,QAAU,GACfvO,KAAKq+B,MAAQ,GACbr+B,KAAKs+B,UAAY,EACrB,GCMJ,EAZA,MACIvd,WAAAA,GAGI/gB,KAAKq3B,UAAY,EACjBr3B,KAAKs3B,SAAW1X,IAGhB5f,KAAKu3B,mBAAoB,CAC7B,GCOJ,EAZA,MACIxW,WAAAA,GAEI/gB,KAAKoN,YAAc,GACnBpN,KAAKkH,MAAQ,GAGblH,KAAKy6B,gBAAkB,GACvBz6B,KAAK06B,eAVmB,GAW5B,GC1CJ,SAAS6D,EAAiB36B,GAEtB,IAAIO,EACAq6B,GAFJ56B,EAASA,GAAU,CAAC,GAEC46B,QACrB,MAAMC,EAAY76B,EAAO66B,UAgHzB,OAJAt6B,EAAW,CACPu6B,WAzEJ,SAAoBtI,GAChB,IAAI7nB,EAAU,GAoEd,OAlEI6nB,GAAYA,EAASuI,SACrBvI,EAASuI,QAAQl4B,SAAQgwB,IACrB,IAAImI,EAAc,IAAID,EAClBE,EAAYL,EAAQM,aAAa1I,GAEjCK,EAAOjtB,eAAe,aACtBo1B,EAAYrwB,QAAUkoB,EAAOloB,QAK7BkoB,EAAO4H,OACP5H,EAAO4H,MAAM53B,SAAQwsB,IACjB,IAAI8L,EAAa,IAAIV,EAErBU,EAAW1H,UApD/B,SAAkCjB,EAAU4I,EAAS/L,GACjD,IAAIgM,EACAC,EACAC,EAAwB,EA4B5B,OA1BIH,EAKAG,EAAwBX,EAAQY,yBAAyBhJ,GAAY,KAKrE6I,EAAYT,EAAQa,kBAAkBjJ,GAElC6I,EAAU1mC,SACV4mC,EAAwBF,EAAU,GAAGrkC,QAO7CskC,EAAqBC,EAEjBlM,GAASA,EAAMzpB,eAAei1B,EAAUjU,cACxC0U,GAAsBjM,EAAMoE,WAGzB6H,CACX,CAqBwBI,CAAyBlJ,EAAUyI,EAAW5L,GAE9CA,EAAMzpB,eAAe,YACrBu1B,EAAWzH,SAAWrE,EAAMqE,SAI5ByH,EAAWzH,SAAWkH,EAAQe,YAAYnJ,GAG9C2I,EAAWxH,kBAAoBsH,EAE/BD,EAAYP,MAAMp1B,KAAK81B,EAAW,IAItCtI,EAAO6H,YACP7H,EAAO6H,UAAU73B,SAAQs0B,IACrB,IAAIyE,EAAiB,IAAIlB,EAErBvD,EAAUvxB,eAAei1B,EAAUlU,iBACnCiV,EAAepyB,YAAc2tB,EAAU3tB,YAMvC2tB,EAAUvxB,eAAe,WACzBg2B,EAAet4B,MAAQ6zB,EAAU7zB,OAGjC6zB,EAAUvxB,eAAei1B,EAAU1S,qBACnCyT,EAAe/E,gBAAkBM,EAAU0D,EAAU1S,oBAGrDgP,EAAUvxB,eAAei1B,EAAUzS,mBACnCwT,EAAe9E,eAAiBK,EAAU0D,EAAUzS,kBAGxD4S,EAAYN,UAAUr1B,KAAKu2B,GAAe,IAOlDjxB,EAAQtF,KAAK21B,IAAY,IAI1BrwB,CACX,GAMOpK,CACX,CAEAo6B,EAAgBn3B,sBAAwB,kBACxC,MAAeC,EAAAA,EAAaC,oBAAoBi3B,GCzFhD,SAASkB,EAA4B77B,GAGjC,IAAIO,EADJP,EAASA,GAAU,CAAC,EAEpB,IAAI87B,EAAqB,CAAC,EACtB77B,EAAU7D,KAAK6D,QACfC,EAAWF,EAAOE,SACtB,MAAMmK,EAASrK,EAAOqK,OAEtB,SAASmM,EAAOiB,GACZ,GAAIA,EAAEnW,MACF,OAIJ,IAAIy6B,EAAsB9N,OAAOC,KAAK4N,GAEtBnB,EAAgB16B,GAASG,YAAY,CACjDw6B,QAAS56B,EAAO46B,QAChBC,UAAW76B,EAAO66B,YACnBC,WAAWrjB,EAAE+a,UAER3vB,SAAQq3B,IACZ,MAAM7iB,EAAMpjB,KAAKC,UAAUgmC,GAE3B,GAAK4B,EAAmBl2B,eAAeyR,GAUnC0kB,EAAoB32B,OAAOiS,EAAK,QAThC,IACI,IAAI2kB,EAAa1B,EAAkBr6B,GAAS0H,OAAO3H,GACnDg8B,EAAW7I,WAAW+G,GACtB4B,EAAmBzkB,GAAO2kB,CAC9B,CAAE,MAAOvkB,GACL,CAKR,IAIJskB,EAAoBl5B,SAAQuW,IACxB0iB,EAAmB1iB,GAAGnT,eACf61B,EAAmB1iB,EAAE,IAGhClZ,EAAS+C,QAAQynB,EAAAA,EAAuBC,gCAC5C,CAEA,SAASsR,IACLhO,OAAOC,KAAK4N,GAAoBj5B,SAAQwU,IACpCykB,EAAmBzkB,GAAKpR,OAAO,IAGnC61B,EAAqB,CAAC,CAC1B,CAiBA,OALAv7B,EAAW,CACP0F,MANJ,WACI/F,EAASoF,IAAI+E,EAAOmU,iBAAkBhI,EAAQjW,GAC9CL,EAASoF,IAAI+E,EAAOgY,yBAA0B4Z,EAAyB17B,EAC3E,GAPIL,EAASsF,GAAG6E,EAAOmU,iBAAkBhI,EAAQjW,GAC7CL,EAASsF,GAAG6E,EAAOgY,yBAA0B4Z,EAAyB17B,GAanEA,CACX,CAEAs7B,EAA4Br4B,sBAAwB,8BACpD,MAAeC,EAAAA,EAAaiE,gBAAgBm0B,GC1E5C,SAASK,IAEL,IACI37B,EACA47B,EAFAl8B,EAAU7D,KAAK6D,QA0CnB,OANAM,EAAW,CACP67B,uBA5BJ,SAAgCp8B,GAQ5B,OAPAm8B,EAAsBlK,EAAoBhyB,GAASG,YAAY,CAC3DF,SAAUF,EAAOE,SACjBiyB,YAAanyB,EAAOmyB,YACpBC,iBAAkBpyB,EAAOoyB,iBACzB/nB,OAAQrK,EAAOqK,SAEnB8xB,EAAoBhJ,aACb0I,EAA4B57B,GAAS0H,OAAO3H,EACvD,EAoBIq8B,oBAdJ,WACI,OAAOtF,EAAiB92B,GAASG,aACrC,EAaIk8B,yBAPJ,WACI,OAAOhD,EAAsBr5B,GAASG,aAC1C,GAQOG,CACX,CAEA27B,EAAiB14B,sBAAwB,mBACzC,MAAMhQ,EAAU+oC,OAAO94B,aAAaiE,gBAAgBw0B,GACpD1oC,EAAQ6W,OAASqgB,EAAAA,EACjB6R,OAAO94B,aAAaoE,mBAAmBq0B,EAAiB14B,sBAAuBhQ,GAC/E,O", "sources": ["webpack://dashjs/webpack/universalModuleDefinition", "webpack://dashjs/./node_modules/path-browserify/index.js", "webpack://dashjs/./node_modules/ua-parser-js/src/ua-parser.js", "webpack://dashjs/./src/core/Debug.js", "webpack://dashjs/./src/core/EventBus.js", "webpack://dashjs/./src/core/FactoryMaker.js", "webpack://dashjs/./src/core/Settings.js", "webpack://dashjs/./src/core/Utils.js", "webpack://dashjs/./src/core/events/CoreEvents.js", "webpack://dashjs/./src/core/events/Events.js", "webpack://dashjs/./src/core/events/EventsBase.js", "webpack://dashjs/./src/dash/vo/UTCTiming.js", "webpack://dashjs/./src/streaming/MediaPlayerEvents.js", "webpack://dashjs/./src/streaming/constants/Constants.js", "webpack://dashjs/./src/streaming/metrics/MetricsReportingEvents.js", "webpack://dashjs/./src/streaming/models/CustomParametersModel.js", "webpack://dashjs/./src/streaming/utils/CustomTimeRanges.js", "webpack://dashjs/./src/streaming/utils/SupervisorTools.js", "webpack://dashjs/./src/streaming/vo/metrics/HTTPRequest.js", "webpack://dashjs/webpack/bootstrap", "webpack://dashjs/webpack/runtime/amd options", "webpack://dashjs/webpack/runtime/define property getters", "webpack://dashjs/webpack/runtime/hasOwnProperty shorthand", "webpack://dashjs/./src/streaming/metrics/vo/DVBErrors.js", "webpack://dashjs/./src/streaming/metrics/utils/DVBErrorsTranslator.js", "webpack://dashjs/./src/streaming/metrics/controllers/RangeController.js", "webpack://dashjs/./src/streaming/metrics/utils/MetricSerialiser.js", "webpack://dashjs/./src/streaming/metrics/utils/RNG.js", "webpack://dashjs/./src/streaming/metrics/reporting/reporters/DVBReporting.js", "webpack://dashjs/./src/streaming/metrics/reporting/ReportingFactory.js", "webpack://dashjs/./src/streaming/metrics/controllers/ReportingController.js", "webpack://dashjs/./src/streaming/metrics/utils/HandlerHelpers.js", "webpack://dashjs/./src/streaming/metrics/metrics/handlers/BufferLevelHandler.js", "webpack://dashjs/./src/streaming/metrics/metrics/handlers/DVBErrorsHandler.js", "webpack://dashjs/./src/streaming/metrics/metrics/handlers/HttpListHandler.js", "webpack://dashjs/./src/streaming/metrics/metrics/handlers/GenericMetricHandler.js", "webpack://dashjs/./src/streaming/metrics/metrics/MetricsHandlerFactory.js", "webpack://dashjs/./src/streaming/metrics/controllers/MetricsHandlersController.js", "webpack://dashjs/./src/streaming/metrics/controllers/MetricsController.js", "webpack://dashjs/./src/streaming/metrics/vo/Metrics.js", "webpack://dashjs/./src/streaming/metrics/vo/Range.js", "webpack://dashjs/./src/streaming/metrics/vo/Reporting.js", "webpack://dashjs/./src/streaming/metrics/utils/ManifestParsing.js", "webpack://dashjs/./src/streaming/metrics/controllers/MetricsCollectionController.js", "webpack://dashjs/./src/streaming/metrics/MetricsReporting.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"dashjs\"] = factory();\n\telse\n\t\troot[\"dashjs\"] = factory();\n})(self, function() {\nreturn ", "// 'path' module extracted from Node.js v8.11.1 (only the posix part)\n// transplited with Babel\n\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nfunction assertPath(path) {\n  if (typeof path !== 'string') {\n    throw new TypeError('Path must be a string. Received ' + JSON.stringify(path));\n  }\n}\n\n// Resolves . and .. elements in a path with directory names\nfunction normalizeStringPosix(path, allowAboveRoot) {\n  var res = '';\n  var lastSegmentLength = 0;\n  var lastSlash = -1;\n  var dots = 0;\n  var code;\n  for (var i = 0; i <= path.length; ++i) {\n    if (i < path.length)\n      code = path.charCodeAt(i);\n    else if (code === 47 /*/*/)\n      break;\n    else\n      code = 47 /*/*/;\n    if (code === 47 /*/*/) {\n      if (lastSlash === i - 1 || dots === 1) {\n        // NOOP\n      } else if (lastSlash !== i - 1 && dots === 2) {\n        if (res.length < 2 || lastSegmentLength !== 2 || res.charCodeAt(res.length - 1) !== 46 /*.*/ || res.charCodeAt(res.length - 2) !== 46 /*.*/) {\n          if (res.length > 2) {\n            var lastSlashIndex = res.lastIndexOf('/');\n            if (lastSlashIndex !== res.length - 1) {\n              if (lastSlashIndex === -1) {\n                res = '';\n                lastSegmentLength = 0;\n              } else {\n                res = res.slice(0, lastSlashIndex);\n                lastSegmentLength = res.length - 1 - res.lastIndexOf('/');\n              }\n              lastSlash = i;\n              dots = 0;\n              continue;\n            }\n          } else if (res.length === 2 || res.length === 1) {\n            res = '';\n            lastSegmentLength = 0;\n            lastSlash = i;\n            dots = 0;\n            continue;\n          }\n        }\n        if (allowAboveRoot) {\n          if (res.length > 0)\n            res += '/..';\n          else\n            res = '..';\n          lastSegmentLength = 2;\n        }\n      } else {\n        if (res.length > 0)\n          res += '/' + path.slice(lastSlash + 1, i);\n        else\n          res = path.slice(lastSlash + 1, i);\n        lastSegmentLength = i - lastSlash - 1;\n      }\n      lastSlash = i;\n      dots = 0;\n    } else if (code === 46 /*.*/ && dots !== -1) {\n      ++dots;\n    } else {\n      dots = -1;\n    }\n  }\n  return res;\n}\n\nfunction _format(sep, pathObject) {\n  var dir = pathObject.dir || pathObject.root;\n  var base = pathObject.base || (pathObject.name || '') + (pathObject.ext || '');\n  if (!dir) {\n    return base;\n  }\n  if (dir === pathObject.root) {\n    return dir + base;\n  }\n  return dir + sep + base;\n}\n\nvar posix = {\n  // path.resolve([from ...], to)\n  resolve: function resolve() {\n    var resolvedPath = '';\n    var resolvedAbsolute = false;\n    var cwd;\n\n    for (var i = arguments.length - 1; i >= -1 && !resolvedAbsolute; i--) {\n      var path;\n      if (i >= 0)\n        path = arguments[i];\n      else {\n        if (cwd === undefined)\n          cwd = process.cwd();\n        path = cwd;\n      }\n\n      assertPath(path);\n\n      // Skip empty entries\n      if (path.length === 0) {\n        continue;\n      }\n\n      resolvedPath = path + '/' + resolvedPath;\n      resolvedAbsolute = path.charCodeAt(0) === 47 /*/*/;\n    }\n\n    // At this point the path should be resolved to a full absolute path, but\n    // handle relative paths to be safe (might happen when process.cwd() fails)\n\n    // Normalize the path\n    resolvedPath = normalizeStringPosix(resolvedPath, !resolvedAbsolute);\n\n    if (resolvedAbsolute) {\n      if (resolvedPath.length > 0)\n        return '/' + resolvedPath;\n      else\n        return '/';\n    } else if (resolvedPath.length > 0) {\n      return resolvedPath;\n    } else {\n      return '.';\n    }\n  },\n\n  normalize: function normalize(path) {\n    assertPath(path);\n\n    if (path.length === 0) return '.';\n\n    var isAbsolute = path.charCodeAt(0) === 47 /*/*/;\n    var trailingSeparator = path.charCodeAt(path.length - 1) === 47 /*/*/;\n\n    // Normalize the path\n    path = normalizeStringPosix(path, !isAbsolute);\n\n    if (path.length === 0 && !isAbsolute) path = '.';\n    if (path.length > 0 && trailingSeparator) path += '/';\n\n    if (isAbsolute) return '/' + path;\n    return path;\n  },\n\n  isAbsolute: function isAbsolute(path) {\n    assertPath(path);\n    return path.length > 0 && path.charCodeAt(0) === 47 /*/*/;\n  },\n\n  join: function join() {\n    if (arguments.length === 0)\n      return '.';\n    var joined;\n    for (var i = 0; i < arguments.length; ++i) {\n      var arg = arguments[i];\n      assertPath(arg);\n      if (arg.length > 0) {\n        if (joined === undefined)\n          joined = arg;\n        else\n          joined += '/' + arg;\n      }\n    }\n    if (joined === undefined)\n      return '.';\n    return posix.normalize(joined);\n  },\n\n  relative: function relative(from, to) {\n    assertPath(from);\n    assertPath(to);\n\n    if (from === to) return '';\n\n    from = posix.resolve(from);\n    to = posix.resolve(to);\n\n    if (from === to) return '';\n\n    // Trim any leading backslashes\n    var fromStart = 1;\n    for (; fromStart < from.length; ++fromStart) {\n      if (from.charCodeAt(fromStart) !== 47 /*/*/)\n        break;\n    }\n    var fromEnd = from.length;\n    var fromLen = fromEnd - fromStart;\n\n    // Trim any leading backslashes\n    var toStart = 1;\n    for (; toStart < to.length; ++toStart) {\n      if (to.charCodeAt(toStart) !== 47 /*/*/)\n        break;\n    }\n    var toEnd = to.length;\n    var toLen = toEnd - toStart;\n\n    // Compare paths to find the longest common path from root\n    var length = fromLen < toLen ? fromLen : toLen;\n    var lastCommonSep = -1;\n    var i = 0;\n    for (; i <= length; ++i) {\n      if (i === length) {\n        if (toLen > length) {\n          if (to.charCodeAt(toStart + i) === 47 /*/*/) {\n            // We get here if `from` is the exact base path for `to`.\n            // For example: from='/foo/bar'; to='/foo/bar/baz'\n            return to.slice(toStart + i + 1);\n          } else if (i === 0) {\n            // We get here if `from` is the root\n            // For example: from='/'; to='/foo'\n            return to.slice(toStart + i);\n          }\n        } else if (fromLen > length) {\n          if (from.charCodeAt(fromStart + i) === 47 /*/*/) {\n            // We get here if `to` is the exact base path for `from`.\n            // For example: from='/foo/bar/baz'; to='/foo/bar'\n            lastCommonSep = i;\n          } else if (i === 0) {\n            // We get here if `to` is the root.\n            // For example: from='/foo'; to='/'\n            lastCommonSep = 0;\n          }\n        }\n        break;\n      }\n      var fromCode = from.charCodeAt(fromStart + i);\n      var toCode = to.charCodeAt(toStart + i);\n      if (fromCode !== toCode)\n        break;\n      else if (fromCode === 47 /*/*/)\n        lastCommonSep = i;\n    }\n\n    var out = '';\n    // Generate the relative path based on the path difference between `to`\n    // and `from`\n    for (i = fromStart + lastCommonSep + 1; i <= fromEnd; ++i) {\n      if (i === fromEnd || from.charCodeAt(i) === 47 /*/*/) {\n        if (out.length === 0)\n          out += '..';\n        else\n          out += '/..';\n      }\n    }\n\n    // Lastly, append the rest of the destination (`to`) path that comes after\n    // the common path parts\n    if (out.length > 0)\n      return out + to.slice(toStart + lastCommonSep);\n    else {\n      toStart += lastCommonSep;\n      if (to.charCodeAt(toStart) === 47 /*/*/)\n        ++toStart;\n      return to.slice(toStart);\n    }\n  },\n\n  _makeLong: function _makeLong(path) {\n    return path;\n  },\n\n  dirname: function dirname(path) {\n    assertPath(path);\n    if (path.length === 0) return '.';\n    var code = path.charCodeAt(0);\n    var hasRoot = code === 47 /*/*/;\n    var end = -1;\n    var matchedSlash = true;\n    for (var i = path.length - 1; i >= 1; --i) {\n      code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          if (!matchedSlash) {\n            end = i;\n            break;\n          }\n        } else {\n        // We saw the first non-path separator\n        matchedSlash = false;\n      }\n    }\n\n    if (end === -1) return hasRoot ? '/' : '.';\n    if (hasRoot && end === 1) return '//';\n    return path.slice(0, end);\n  },\n\n  basename: function basename(path, ext) {\n    if (ext !== undefined && typeof ext !== 'string') throw new TypeError('\"ext\" argument must be a string');\n    assertPath(path);\n\n    var start = 0;\n    var end = -1;\n    var matchedSlash = true;\n    var i;\n\n    if (ext !== undefined && ext.length > 0 && ext.length <= path.length) {\n      if (ext.length === path.length && ext === path) return '';\n      var extIdx = ext.length - 1;\n      var firstNonSlashEnd = -1;\n      for (i = path.length - 1; i >= 0; --i) {\n        var code = path.charCodeAt(i);\n        if (code === 47 /*/*/) {\n            // If we reached a path separator that was not part of a set of path\n            // separators at the end of the string, stop now\n            if (!matchedSlash) {\n              start = i + 1;\n              break;\n            }\n          } else {\n          if (firstNonSlashEnd === -1) {\n            // We saw the first non-path separator, remember this index in case\n            // we need it if the extension ends up not matching\n            matchedSlash = false;\n            firstNonSlashEnd = i + 1;\n          }\n          if (extIdx >= 0) {\n            // Try to match the explicit extension\n            if (code === ext.charCodeAt(extIdx)) {\n              if (--extIdx === -1) {\n                // We matched the extension, so mark this as the end of our path\n                // component\n                end = i;\n              }\n            } else {\n              // Extension does not match, so our result is the entire path\n              // component\n              extIdx = -1;\n              end = firstNonSlashEnd;\n            }\n          }\n        }\n      }\n\n      if (start === end) end = firstNonSlashEnd;else if (end === -1) end = path.length;\n      return path.slice(start, end);\n    } else {\n      for (i = path.length - 1; i >= 0; --i) {\n        if (path.charCodeAt(i) === 47 /*/*/) {\n            // If we reached a path separator that was not part of a set of path\n            // separators at the end of the string, stop now\n            if (!matchedSlash) {\n              start = i + 1;\n              break;\n            }\n          } else if (end === -1) {\n          // We saw the first non-path separator, mark this as the end of our\n          // path component\n          matchedSlash = false;\n          end = i + 1;\n        }\n      }\n\n      if (end === -1) return '';\n      return path.slice(start, end);\n    }\n  },\n\n  extname: function extname(path) {\n    assertPath(path);\n    var startDot = -1;\n    var startPart = 0;\n    var end = -1;\n    var matchedSlash = true;\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    var preDotState = 0;\n    for (var i = path.length - 1; i >= 0; --i) {\n      var code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          // If we reached a path separator that was not part of a set of path\n          // separators at the end of the string, stop now\n          if (!matchedSlash) {\n            startPart = i + 1;\n            break;\n          }\n          continue;\n        }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === 46 /*.*/) {\n          // If this is our first dot, mark it as the start of our extension\n          if (startDot === -1)\n            startDot = i;\n          else if (preDotState !== 1)\n            preDotState = 1;\n      } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n\n    if (startDot === -1 || end === -1 ||\n        // We saw a non-dot character immediately before the dot\n        preDotState === 0 ||\n        // The (right-most) trimmed path component is exactly '..'\n        preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n      return '';\n    }\n    return path.slice(startDot, end);\n  },\n\n  format: function format(pathObject) {\n    if (pathObject === null || typeof pathObject !== 'object') {\n      throw new TypeError('The \"pathObject\" argument must be of type Object. Received type ' + typeof pathObject);\n    }\n    return _format('/', pathObject);\n  },\n\n  parse: function parse(path) {\n    assertPath(path);\n\n    var ret = { root: '', dir: '', base: '', ext: '', name: '' };\n    if (path.length === 0) return ret;\n    var code = path.charCodeAt(0);\n    var isAbsolute = code === 47 /*/*/;\n    var start;\n    if (isAbsolute) {\n      ret.root = '/';\n      start = 1;\n    } else {\n      start = 0;\n    }\n    var startDot = -1;\n    var startPart = 0;\n    var end = -1;\n    var matchedSlash = true;\n    var i = path.length - 1;\n\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    var preDotState = 0;\n\n    // Get non-dir info\n    for (; i >= start; --i) {\n      code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          // If we reached a path separator that was not part of a set of path\n          // separators at the end of the string, stop now\n          if (!matchedSlash) {\n            startPart = i + 1;\n            break;\n          }\n          continue;\n        }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === 46 /*.*/) {\n          // If this is our first dot, mark it as the start of our extension\n          if (startDot === -1) startDot = i;else if (preDotState !== 1) preDotState = 1;\n        } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n\n    if (startDot === -1 || end === -1 ||\n    // We saw a non-dot character immediately before the dot\n    preDotState === 0 ||\n    // The (right-most) trimmed path component is exactly '..'\n    preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n      if (end !== -1) {\n        if (startPart === 0 && isAbsolute) ret.base = ret.name = path.slice(1, end);else ret.base = ret.name = path.slice(startPart, end);\n      }\n    } else {\n      if (startPart === 0 && isAbsolute) {\n        ret.name = path.slice(1, startDot);\n        ret.base = path.slice(1, end);\n      } else {\n        ret.name = path.slice(startPart, startDot);\n        ret.base = path.slice(startPart, end);\n      }\n      ret.ext = path.slice(startDot, end);\n    }\n\n    if (startPart > 0) ret.dir = path.slice(0, startPart - 1);else if (isAbsolute) ret.dir = '/';\n\n    return ret;\n  },\n\n  sep: '/',\n  delimiter: ':',\n  win32: null,\n  posix: null\n};\n\nposix.posix = posix;\n\nmodule.exports = posix;\n", "/////////////////////////////////////////////////////////////////////////////////\n/* UAParser.js v1.0.38\n   Copyright © 2012-2021 F<PERSON><PERSON> <<EMAIL>>\n   MIT License *//*\n   Detect Browser, Engine, OS, CPU, and Device type/model from User-Agent data.\n   Supports browser & node.js environment. \n   Demo   : https://faisalman.github.io/ua-parser-js\n   Source : https://github.com/faisalman/ua-parser-js */\n/////////////////////////////////////////////////////////////////////////////////\n\n(function (window, undefined) {\n\n    'use strict';\n\n    //////////////\n    // Constants\n    /////////////\n\n\n    var LIBVERSION  = '1.0.38',\n        EMPTY       = '',\n        UNKNOWN     = '?',\n        FUNC_TYPE   = 'function',\n        UNDEF_TYPE  = 'undefined',\n        OBJ_TYPE    = 'object',\n        STR_TYPE    = 'string',\n        MAJOR       = 'major',\n        MODEL       = 'model',\n        NAME        = 'name',\n        TYPE        = 'type',\n        VENDOR      = 'vendor',\n        VERSION     = 'version',\n        ARCHITECTURE= 'architecture',\n        CONSOLE     = 'console',\n        MOBILE      = 'mobile',\n        TABLET      = 'tablet',\n        SMARTTV     = 'smarttv',\n        WEARABLE    = 'wearable',\n        EMBEDDED    = 'embedded',\n        UA_MAX_LENGTH = 500;\n\n    var AMAZON  = 'Amazon',\n        APPLE   = 'Apple',\n        ASUS    = 'ASUS',\n        BLACKBERRY = 'BlackBerry',\n        BROWSER = 'Browser',\n        CHROME  = 'Chrome',\n        EDGE    = 'Edge',\n        FIREFOX = 'Firefox',\n        GOOGLE  = 'Google',\n        HUAWEI  = 'Huawei',\n        LG      = 'LG',\n        MICROSOFT = 'Microsoft',\n        MOTOROLA  = 'Motorola',\n        OPERA   = 'Opera',\n        SAMSUNG = 'Samsung',\n        SHARP   = 'Sharp',\n        SONY    = 'Sony',\n        XIAOMI  = 'Xiaomi',\n        ZEBRA   = 'Zebra',\n        FACEBOOK    = 'Facebook',\n        CHROMIUM_OS = 'Chromium OS',\n        MAC_OS  = 'Mac OS';\n\n    ///////////\n    // Helper\n    //////////\n\n    var extend = function (regexes, extensions) {\n            var mergedRegexes = {};\n            for (var i in regexes) {\n                if (extensions[i] && extensions[i].length % 2 === 0) {\n                    mergedRegexes[i] = extensions[i].concat(regexes[i]);\n                } else {\n                    mergedRegexes[i] = regexes[i];\n                }\n            }\n            return mergedRegexes;\n        },\n        enumerize = function (arr) {\n            var enums = {};\n            for (var i=0; i<arr.length; i++) {\n                enums[arr[i].toUpperCase()] = arr[i];\n            }\n            return enums;\n        },\n        has = function (str1, str2) {\n            return typeof str1 === STR_TYPE ? lowerize(str2).indexOf(lowerize(str1)) !== -1 : false;\n        },\n        lowerize = function (str) {\n            return str.toLowerCase();\n        },\n        majorize = function (version) {\n            return typeof(version) === STR_TYPE ? version.replace(/[^\\d\\.]/g, EMPTY).split('.')[0] : undefined;\n        },\n        trim = function (str, len) {\n            if (typeof(str) === STR_TYPE) {\n                str = str.replace(/^\\s\\s*/, EMPTY);\n                return typeof(len) === UNDEF_TYPE ? str : str.substring(0, UA_MAX_LENGTH);\n            }\n    };\n\n    ///////////////\n    // Map helper\n    //////////////\n\n    var rgxMapper = function (ua, arrays) {\n\n            var i = 0, j, k, p, q, matches, match;\n\n            // loop through all regexes maps\n            while (i < arrays.length && !matches) {\n\n                var regex = arrays[i],       // even sequence (0,2,4,..)\n                    props = arrays[i + 1];   // odd sequence (1,3,5,..)\n                j = k = 0;\n\n                // try matching uastring with regexes\n                while (j < regex.length && !matches) {\n\n                    if (!regex[j]) { break; }\n                    matches = regex[j++].exec(ua);\n\n                    if (!!matches) {\n                        for (p = 0; p < props.length; p++) {\n                            match = matches[++k];\n                            q = props[p];\n                            // check if given property is actually array\n                            if (typeof q === OBJ_TYPE && q.length > 0) {\n                                if (q.length === 2) {\n                                    if (typeof q[1] == FUNC_TYPE) {\n                                        // assign modified match\n                                        this[q[0]] = q[1].call(this, match);\n                                    } else {\n                                        // assign given value, ignore regex match\n                                        this[q[0]] = q[1];\n                                    }\n                                } else if (q.length === 3) {\n                                    // check whether function or regex\n                                    if (typeof q[1] === FUNC_TYPE && !(q[1].exec && q[1].test)) {\n                                        // call function (usually string mapper)\n                                        this[q[0]] = match ? q[1].call(this, match, q[2]) : undefined;\n                                    } else {\n                                        // sanitize match using given regex\n                                        this[q[0]] = match ? match.replace(q[1], q[2]) : undefined;\n                                    }\n                                } else if (q.length === 4) {\n                                        this[q[0]] = match ? q[3].call(this, match.replace(q[1], q[2])) : undefined;\n                                }\n                            } else {\n                                this[q] = match ? match : undefined;\n                            }\n                        }\n                    }\n                }\n                i += 2;\n            }\n        },\n\n        strMapper = function (str, map) {\n\n            for (var i in map) {\n                // check if current value is array\n                if (typeof map[i] === OBJ_TYPE && map[i].length > 0) {\n                    for (var j = 0; j < map[i].length; j++) {\n                        if (has(map[i][j], str)) {\n                            return (i === UNKNOWN) ? undefined : i;\n                        }\n                    }\n                } else if (has(map[i], str)) {\n                    return (i === UNKNOWN) ? undefined : i;\n                }\n            }\n            return str;\n    };\n\n    ///////////////\n    // String map\n    //////////////\n\n    // Safari < 3.0\n    var oldSafariMap = {\n            '1.0'   : '/8',\n            '1.2'   : '/1',\n            '1.3'   : '/3',\n            '2.0'   : '/412',\n            '2.0.2' : '/416',\n            '2.0.3' : '/417',\n            '2.0.4' : '/419',\n            '?'     : '/'\n        },\n        windowsVersionMap = {\n            'ME'        : '4.90',\n            'NT 3.11'   : 'NT3.51',\n            'NT 4.0'    : 'NT4.0',\n            '2000'      : 'NT 5.0',\n            'XP'        : ['NT 5.1', 'NT 5.2'],\n            'Vista'     : 'NT 6.0',\n            '7'         : 'NT 6.1',\n            '8'         : 'NT 6.2',\n            '8.1'       : 'NT 6.3',\n            '10'        : ['NT 6.4', 'NT 10.0'],\n            'RT'        : 'ARM'\n    };\n\n    //////////////\n    // Regex map\n    /////////////\n\n    var regexes = {\n\n        browser : [[\n\n            /\\b(?:crmo|crios)\\/([\\w\\.]+)/i                                      // Chrome for Android/iOS\n            ], [VERSION, [NAME, 'Chrome']], [\n            /edg(?:e|ios|a)?\\/([\\w\\.]+)/i                                       // Microsoft Edge\n            ], [VERSION, [NAME, 'Edge']], [\n\n            // Presto based\n            /(opera mini)\\/([-\\w\\.]+)/i,                                        // Opera Mini\n            /(opera [mobiletab]{3,6})\\b.+version\\/([-\\w\\.]+)/i,                 // Opera Mobi/Tablet\n            /(opera)(?:.+version\\/|[\\/ ]+)([\\w\\.]+)/i                           // Opera\n            ], [NAME, VERSION], [\n            /opios[\\/ ]+([\\w\\.]+)/i                                             // Opera mini on iphone >= 8.0\n            ], [VERSION, [NAME, OPERA+' Mini']], [\n            /\\bop(?:rg)?x\\/([\\w\\.]+)/i                                          // Opera GX\n            ], [VERSION, [NAME, OPERA+' GX']], [\n            /\\bopr\\/([\\w\\.]+)/i                                                 // Opera Webkit\n            ], [VERSION, [NAME, OPERA]], [\n\n            // Mixed\n            /\\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\\/ ]?([\\w\\.]+)/i            // Baidu\n            ], [VERSION, [NAME, 'Baidu']], [\n            /(kindle)\\/([\\w\\.]+)/i,                                             // Kindle\n            /(lunascape|maxthon|netfront|jasmine|blazer)[\\/ ]?([\\w\\.]*)/i,      // Lunascape/Maxthon/Netfront/Jasmine/Blazer\n            // Trident based\n            /(avant|iemobile|slim)\\s?(?:browser)?[\\/ ]?([\\w\\.]*)/i,             // Avant/IEMobile/SlimBrowser\n            /(?:ms|\\()(ie) ([\\w\\.]+)/i,                                         // Internet Explorer\n\n            // Webkit/KHTML based                                               // Flock/RockMelt/Midori/Epiphany/Silk/Skyfire/Bolt/Iron/Iridium/PhantomJS/Bowser/QupZilla/Falkon\n            /(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\\/([-\\w\\.]+)/i,\n                                                                                // Rekonq/Puffin/Brave/Whale/QQBrowserLite/QQ, aka ShouQ\n            /(heytap|ovi)browser\\/([\\d\\.]+)/i,                                  // Heytap/Ovi\n            /(weibo)__([\\d\\.]+)/i                                               // Weibo\n            ], [NAME, VERSION], [\n            /\\bddg\\/([\\w\\.]+)/i                                                 // DuckDuckGo\n            ], [VERSION, [NAME, 'DuckDuckGo']], [\n            /(?:\\buc? ?browser|(?:juc.+)ucweb)[\\/ ]?([\\w\\.]+)/i                 // UCBrowser\n            ], [VERSION, [NAME, 'UC'+BROWSER]], [\n            /microm.+\\bqbcore\\/([\\w\\.]+)/i,                                     // WeChat Desktop for Windows Built-in Browser\n            /\\bqbcore\\/([\\w\\.]+).+microm/i,\n            /micromessenger\\/([\\w\\.]+)/i                                        // WeChat\n            ], [VERSION, [NAME, 'WeChat']], [\n            /konqueror\\/([\\w\\.]+)/i                                             // Konqueror\n            ], [VERSION, [NAME, 'Konqueror']], [\n            /trident.+rv[: ]([\\w\\.]{1,9})\\b.+like gecko/i                       // IE11\n            ], [VERSION, [NAME, 'IE']], [\n            /ya(?:search)?browser\\/([\\w\\.]+)/i                                  // Yandex\n            ], [VERSION, [NAME, 'Yandex']], [\n            /slbrowser\\/([\\w\\.]+)/i                                             // Smart Lenovo Browser\n            ], [VERSION, [NAME, 'Smart Lenovo '+BROWSER]], [\n            /(avast|avg)\\/([\\w\\.]+)/i                                           // Avast/AVG Secure Browser\n            ], [[NAME, /(.+)/, '$1 Secure '+BROWSER], VERSION], [\n            /\\bfocus\\/([\\w\\.]+)/i                                               // Firefox Focus\n            ], [VERSION, [NAME, FIREFOX+' Focus']], [\n            /\\bopt\\/([\\w\\.]+)/i                                                 // Opera Touch\n            ], [VERSION, [NAME, OPERA+' Touch']], [\n            /coc_coc\\w+\\/([\\w\\.]+)/i                                            // Coc Coc Browser\n            ], [VERSION, [NAME, 'Coc Coc']], [\n            /dolfin\\/([\\w\\.]+)/i                                                // Dolphin\n            ], [VERSION, [NAME, 'Dolphin']], [\n            /coast\\/([\\w\\.]+)/i                                                 // Opera Coast\n            ], [VERSION, [NAME, OPERA+' Coast']], [\n            /miuibrowser\\/([\\w\\.]+)/i                                           // MIUI Browser\n            ], [VERSION, [NAME, 'MIUI '+BROWSER]], [\n            /fxios\\/([-\\w\\.]+)/i                                                // Firefox for iOS\n            ], [VERSION, [NAME, FIREFOX]], [\n            /\\bqihu|(qi?ho?o?|360)browser/i                                     // 360\n            ], [[NAME, '360 ' + BROWSER]], [\n            /(oculus|sailfish|huawei|vivo)browser\\/([\\w\\.]+)/i\n            ], [[NAME, /(.+)/, '$1 ' + BROWSER], VERSION], [                    // Oculus/Sailfish/HuaweiBrowser/VivoBrowser\n            /samsungbrowser\\/([\\w\\.]+)/i                                        // Samsung Internet\n            ], [VERSION, [NAME, SAMSUNG + ' Internet']], [\n            /(comodo_dragon)\\/([\\w\\.]+)/i                                       // Comodo Dragon\n            ], [[NAME, /_/g, ' '], VERSION], [\n            /metasr[\\/ ]?([\\d\\.]+)/i                                            // Sogou Explorer\n            ], [VERSION, [NAME, 'Sogou Explorer']], [\n            /(sogou)mo\\w+\\/([\\d\\.]+)/i                                          // Sogou Mobile\n            ], [[NAME, 'Sogou Mobile'], VERSION], [\n            /(electron)\\/([\\w\\.]+) safari/i,                                    // Electron-based App\n            /(tesla)(?: qtcarbrowser|\\/(20\\d\\d\\.[-\\w\\.]+))/i,                   // Tesla\n            /m?(qqbrowser|2345Explorer)[\\/ ]?([\\w\\.]+)/i                        // QQBrowser/2345 Browser\n            ], [NAME, VERSION], [\n            /(lbbrowser)/i,                                                     // LieBao Browser\n            /\\[(linkedin)app\\]/i                                                // LinkedIn App for iOS & Android\n            ], [NAME], [\n\n            // WebView\n            /((?:fban\\/fbios|fb_iab\\/fb4a)(?!.+fbav)|;fbav\\/([\\w\\.]+);)/i       // Facebook App for iOS & Android\n            ], [[NAME, FACEBOOK], VERSION], [\n            /(Klarna)\\/([\\w\\.]+)/i,                                             // Klarna Shopping Browser for iOS & Android\n            /(kakao(?:talk|story))[\\/ ]([\\w\\.]+)/i,                             // Kakao App\n            /(naver)\\(.*?(\\d+\\.[\\w\\.]+).*\\)/i,                                  // Naver InApp\n            /safari (line)\\/([\\w\\.]+)/i,                                        // Line App for iOS\n            /\\b(line)\\/([\\w\\.]+)\\/iab/i,                                        // Line App for Android\n            /(alipay)client\\/([\\w\\.]+)/i,                                       // Alipay\n            /(twitter)(?:and| f.+e\\/([\\w\\.]+))/i,                               // Twitter\n            /(chromium|instagram|snapchat)[\\/ ]([-\\w\\.]+)/i                     // Chromium/Instagram/Snapchat\n            ], [NAME, VERSION], [\n            /\\bgsa\\/([\\w\\.]+) .*safari\\//i                                      // Google Search Appliance on iOS\n            ], [VERSION, [NAME, 'GSA']], [\n            /musical_ly(?:.+app_?version\\/|_)([\\w\\.]+)/i                        // TikTok\n            ], [VERSION, [NAME, 'TikTok']], [\n\n            /headlesschrome(?:\\/([\\w\\.]+)| )/i                                  // Chrome Headless\n            ], [VERSION, [NAME, CHROME+' Headless']], [\n\n            / wv\\).+(chrome)\\/([\\w\\.]+)/i                                       // Chrome WebView\n            ], [[NAME, CHROME+' WebView'], VERSION], [\n\n            /droid.+ version\\/([\\w\\.]+)\\b.+(?:mobile safari|safari)/i           // Android Browser\n            ], [VERSION, [NAME, 'Android '+BROWSER]], [\n\n            /(chrome|omniweb|arora|[tizenoka]{5} ?browser)\\/v?([\\w\\.]+)/i       // Chrome/OmniWeb/Arora/Tizen/Nokia\n            ], [NAME, VERSION], [\n\n            /version\\/([\\w\\.\\,]+) .*mobile\\/\\w+ (safari)/i                      // Mobile Safari\n            ], [VERSION, [NAME, 'Mobile Safari']], [\n            /version\\/([\\w(\\.|\\,)]+) .*(mobile ?safari|safari)/i                // Safari & Safari Mobile\n            ], [VERSION, NAME], [\n            /webkit.+?(mobile ?safari|safari)(\\/[\\w\\.]+)/i                      // Safari < 3.0\n            ], [NAME, [VERSION, strMapper, oldSafariMap]], [\n\n            /(webkit|khtml)\\/([\\w\\.]+)/i\n            ], [NAME, VERSION], [\n\n            // Gecko based\n            /(navigator|netscape\\d?)\\/([-\\w\\.]+)/i                              // Netscape\n            ], [[NAME, 'Netscape'], VERSION], [\n            /mobile vr; rv:([\\w\\.]+)\\).+firefox/i                               // Firefox Reality\n            ], [VERSION, [NAME, FIREFOX+' Reality']], [\n            /ekiohf.+(flow)\\/([\\w\\.]+)/i,                                       // Flow\n            /(swiftfox)/i,                                                      // Swiftfox\n            /(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\\/ ]?([\\w\\.\\+]+)/i,\n                                                                                // IceDragon/Iceweasel/Camino/Chimera/Fennec/Maemo/Minimo/Conkeror/Klar\n            /(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\\/([-\\w\\.]+)$/i,\n                                                                                // Firefox/SeaMonkey/K-Meleon/IceCat/IceApe/Firebird/Phoenix\n            /(firefox)\\/([\\w\\.]+)/i,                                            // Other Firefox-based\n            /(mozilla)\\/([\\w\\.]+) .+rv\\:.+gecko\\/\\d+/i,                         // Mozilla\n\n            // Other\n            /(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\\. ]?browser)[-\\/ ]?v?([\\w\\.]+)/i,\n                                                                                // Polaris/Lynx/Dillo/iCab/Doris/Amaya/w3m/NetSurf/Sleipnir/Obigo/Mosaic/Go/ICE/UP.Browser\n            /(links) \\(([\\w\\.]+)/i,                                             // Links\n            /panasonic;(viera)/i                                                // Panasonic Viera\n            ], [NAME, VERSION], [\n            \n            /(cobalt)\\/([\\w\\.]+)/i                                              // Cobalt\n            ], [NAME, [VERSION, /master.|lts./, \"\"]]\n        ],\n\n        cpu : [[\n\n            /(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\\)]/i                     // AMD64 (x64)\n            ], [[ARCHITECTURE, 'amd64']], [\n\n            /(ia32(?=;))/i                                                      // IA32 (quicktime)\n            ], [[ARCHITECTURE, lowerize]], [\n\n            /((?:i[346]|x)86)[;\\)]/i                                            // IA32 (x86)\n            ], [[ARCHITECTURE, 'ia32']], [\n\n            /\\b(aarch64|arm(v?8e?l?|_?64))\\b/i                                 // ARM64\n            ], [[ARCHITECTURE, 'arm64']], [\n\n            /\\b(arm(?:v[67])?ht?n?[fl]p?)\\b/i                                   // ARMHF\n            ], [[ARCHITECTURE, 'armhf']], [\n\n            // PocketPC mistakenly identified as PowerPC\n            /windows (ce|mobile); ppc;/i\n            ], [[ARCHITECTURE, 'arm']], [\n\n            /((?:ppc|powerpc)(?:64)?)(?: mac|;|\\))/i                            // PowerPC\n            ], [[ARCHITECTURE, /ower/, EMPTY, lowerize]], [\n\n            /(sun4\\w)[;\\)]/i                                                    // SPARC\n            ], [[ARCHITECTURE, 'sparc']], [\n\n            /((?:avr32|ia64(?=;))|68k(?=\\))|\\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\\b|pa-risc)/i\n                                                                                // IA64, 68K, ARM/64, AVR/32, IRIX/64, MIPS/64, SPARC/64, PA-RISC\n            ], [[ARCHITECTURE, lowerize]]\n        ],\n\n        device : [[\n\n            //////////////////////////\n            // MOBILES & TABLETS\n            /////////////////////////\n\n            // Samsung\n            /\\b(sch-i[89]0\\d|shw-m380s|sm-[ptx]\\w{2,4}|gt-[pn]\\d{2,4}|sgh-t8[56]9|nexus 10)/i\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, TABLET]], [\n            /\\b((?:s[cgp]h|gt|sm)-\\w+|sc[g-]?[\\d]+a?|galaxy nexus)/i,\n            /samsung[- ]([-\\w]+)/i,\n            /sec-(sgh\\w+)/i\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, MOBILE]], [\n\n            // Apple\n            /(?:\\/|\\()(ip(?:hone|od)[\\w, ]*)(?:\\/|;)/i                          // iPod/iPhone\n            ], [MODEL, [VENDOR, APPLE], [TYPE, MOBILE]], [\n            /\\((ipad);[-\\w\\),; ]+apple/i,                                       // iPad\n            /applecoremedia\\/[\\w\\.]+ \\((ipad)/i,\n            /\\b(ipad)\\d\\d?,\\d\\d?[;\\]].+ios/i\n            ], [MODEL, [VENDOR, APPLE], [TYPE, TABLET]], [\n            /(macintosh);/i\n            ], [MODEL, [VENDOR, APPLE]], [\n\n            // Sharp\n            /\\b(sh-?[altvz]?\\d\\d[a-ekm]?)/i\n            ], [MODEL, [VENDOR, SHARP], [TYPE, MOBILE]], [\n\n            // Huawei\n            /\\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\\d{2})\\b(?!.+d\\/s)/i\n            ], [MODEL, [VENDOR, HUAWEI], [TYPE, TABLET]], [\n            /(?:huawei|honor)([-\\w ]+)[;\\)]/i,\n            /\\b(nexus 6p|\\w{2,4}e?-[atu]?[ln][\\dx][012359c][adn]?)\\b(?!.+d\\/s)/i\n            ], [MODEL, [VENDOR, HUAWEI], [TYPE, MOBILE]], [\n\n            // Xiaomi\n            /\\b(poco[\\w ]+|m2\\d{3}j\\d\\d[a-z]{2})(?: bui|\\))/i,                  // Xiaomi POCO\n            /\\b; (\\w+) build\\/hm\\1/i,                                           // Xiaomi Hongmi 'numeric' models\n            /\\b(hm[-_ ]?note?[_ ]?(?:\\d\\w)?) bui/i,                             // Xiaomi Hongmi\n            /\\b(redmi[\\-_ ]?(?:note|k)?[\\w_ ]+)(?: bui|\\))/i,                   // Xiaomi Redmi\n            /oid[^\\)]+; (m?[12][0-389][01]\\w{3,6}[c-y])( bui|; wv|\\))/i,        // Xiaomi Redmi 'numeric' models\n            /\\b(mi[-_ ]?(?:a\\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\\d?\\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\\))/i // Xiaomi Mi\n            ], [[MODEL, /_/g, ' '], [VENDOR, XIAOMI], [TYPE, MOBILE]], [\n            /oid[^\\)]+; (2\\d{4}(283|rpbf)[cgl])( bui|\\))/i,                     // Redmi Pad\n            /\\b(mi[-_ ]?(?:pad)(?:[\\w_ ]+))(?: bui|\\))/i                        // Mi Pad tablets\n            ],[[MODEL, /_/g, ' '], [VENDOR, XIAOMI], [TYPE, TABLET]], [\n\n            // OPPO\n            /; (\\w+) bui.+ oppo/i,\n            /\\b(cph[12]\\d{3}|p(?:af|c[al]|d\\w|e[ar])[mt]\\d0|x9007|a101op)\\b/i\n            ], [MODEL, [VENDOR, 'OPPO'], [TYPE, MOBILE]], [\n            /\\b(opd2\\d{3}a?) bui/i\n            ], [MODEL, [VENDOR, 'OPPO'], [TYPE, TABLET]], [\n\n            // Vivo\n            /vivo (\\w+)(?: bui|\\))/i,\n            /\\b(v[12]\\d{3}\\w?[at])(?: bui|;)/i\n            ], [MODEL, [VENDOR, 'Vivo'], [TYPE, MOBILE]], [\n\n            // Realme\n            /\\b(rmx[1-3]\\d{3})(?: bui|;|\\))/i\n            ], [MODEL, [VENDOR, 'Realme'], [TYPE, MOBILE]], [\n\n            // Motorola\n            /\\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\\b[\\w ]+build\\//i,\n            /\\bmot(?:orola)?[- ](\\w*)/i,\n            /((?:moto[\\w\\(\\) ]+|xt\\d{3,4}|nexus 6)(?= bui|\\)))/i\n            ], [MODEL, [VENDOR, MOTOROLA], [TYPE, MOBILE]], [\n            /\\b(mz60\\d|xoom[2 ]{0,2}) build\\//i\n            ], [MODEL, [VENDOR, MOTOROLA], [TYPE, TABLET]], [\n\n            // LG\n            /((?=lg)?[vl]k\\-?\\d{3}) bui| 3\\.[-\\w; ]{10}lg?-([06cv9]{3,4})/i\n            ], [MODEL, [VENDOR, LG], [TYPE, TABLET]], [\n            /(lm(?:-?f100[nv]?|-[\\w\\.]+)(?= bui|\\))|nexus [45])/i,\n            /\\blg[-e;\\/ ]+((?!browser|netcast|android tv)\\w+)/i,\n            /\\blg-?([\\d\\w]+) bui/i\n            ], [MODEL, [VENDOR, LG], [TYPE, MOBILE]], [\n\n            // Lenovo\n            /(ideatab[-\\w ]+)/i,\n            /lenovo ?(s[56]000[-\\w]+|tab(?:[\\w ]+)|yt[-\\d\\w]{6}|tb[-\\d\\w]{6})/i\n            ], [MODEL, [VENDOR, 'Lenovo'], [TYPE, TABLET]], [\n\n            // Nokia\n            /(?:maemo|nokia).*(n900|lumia \\d+)/i,\n            /nokia[-_ ]?([-\\w\\.]*)/i\n            ], [[MODEL, /_/g, ' '], [VENDOR, 'Nokia'], [TYPE, MOBILE]], [\n\n            // Google\n            /(pixel c)\\b/i                                                      // Google Pixel C\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, TABLET]], [\n            /droid.+; (pixel[\\daxl ]{0,6})(?: bui|\\))/i                         // Google Pixel\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, MOBILE]], [\n\n            // Sony\n            /droid.+ (a?\\d[0-2]{2}so|[c-g]\\d{4}|so[-gl]\\w+|xq-a\\w[4-7][12])(?= bui|\\).+chrome\\/(?![1-6]{0,1}\\d\\.))/i\n            ], [MODEL, [VENDOR, SONY], [TYPE, MOBILE]], [\n            /sony tablet [ps]/i,\n            /\\b(?:sony)?sgp\\w+(?: bui|\\))/i\n            ], [[MODEL, 'Xperia Tablet'], [VENDOR, SONY], [TYPE, TABLET]], [\n\n            // OnePlus\n            / (kb2005|in20[12]5|be20[12][59])\\b/i,\n            /(?:one)?(?:plus)? (a\\d0\\d\\d)(?: b|\\))/i\n            ], [MODEL, [VENDOR, 'OnePlus'], [TYPE, MOBILE]], [\n\n            // Amazon\n            /(alexa)webm/i,\n            /(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\\))/i,                             // Kindle Fire without Silk / Echo Show\n            /(kf[a-z]+)( bui|\\)).+silk\\//i                                      // Kindle Fire HD\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, TABLET]], [\n            /((?:sd|kf)[0349hijorstuw]+)( bui|\\)).+silk\\//i                     // Fire Phone\n            ], [[MODEL, /(.+)/g, 'Fire Phone $1'], [VENDOR, AMAZON], [TYPE, MOBILE]], [\n\n            // BlackBerry\n            /(playbook);[-\\w\\),; ]+(rim)/i                                      // BlackBerry PlayBook\n            ], [MODEL, VENDOR, [TYPE, TABLET]], [\n            /\\b((?:bb[a-f]|st[hv])100-\\d)/i,\n            /\\(bb10; (\\w+)/i                                                    // BlackBerry 10\n            ], [MODEL, [VENDOR, BLACKBERRY], [TYPE, MOBILE]], [\n\n            // Asus\n            /(?:\\b|asus_)(transfo[prime ]{4,10} \\w+|eeepc|slider \\w+|nexus 7|padfone|p00[cj])/i\n            ], [MODEL, [VENDOR, ASUS], [TYPE, TABLET]], [\n            / (z[bes]6[027][012][km][ls]|zenfone \\d\\w?)\\b/i\n            ], [MODEL, [VENDOR, ASUS], [TYPE, MOBILE]], [\n\n            // HTC\n            /(nexus 9)/i                                                        // HTC Nexus 9\n            ], [MODEL, [VENDOR, 'HTC'], [TYPE, TABLET]], [\n            /(htc)[-;_ ]{1,2}([\\w ]+(?=\\)| bui)|\\w+)/i,                         // HTC\n\n            // ZTE\n            /(zte)[- ]([\\w ]+?)(?: bui|\\/|\\))/i,\n            /(alcatel|geeksphone|nexian|panasonic(?!(?:;|\\.))|sony(?!-bra))[-_ ]?([-\\w]*)/i         // Alcatel/GeeksPhone/Nexian/Panasonic/Sony\n            ], [VENDOR, [MODEL, /_/g, ' '], [TYPE, MOBILE]], [\n\n            // Acer\n            /droid.+; ([ab][1-7]-?[0178a]\\d\\d?)/i\n            ], [MODEL, [VENDOR, 'Acer'], [TYPE, TABLET]], [\n\n            // Meizu\n            /droid.+; (m[1-5] note) bui/i,\n            /\\bmz-([-\\w]{2,})/i\n            ], [MODEL, [VENDOR, 'Meizu'], [TYPE, MOBILE]], [\n                \n            // Ulefone\n            /; ((?:power )?armor(?:[\\w ]{0,8}))(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Ulefone'], [TYPE, MOBILE]], [\n\n            // MIXED\n            /(blackberry|benq|palm(?=\\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno)[-_ ]?([-\\w]*)/i,\n                                                                                // BlackBerry/BenQ/Palm/Sony-Ericsson/Acer/Asus/Dell/Meizu/Motorola/Polytron\n            /(hp) ([\\w ]+\\w)/i,                                                 // HP iPAQ\n            /(asus)-?(\\w+)/i,                                                   // Asus\n            /(microsoft); (lumia[\\w ]+)/i,                                      // Microsoft Lumia\n            /(lenovo)[-_ ]?([-\\w]+)/i,                                          // Lenovo\n            /(jolla)/i,                                                         // Jolla\n            /(oppo) ?([\\w ]+) bui/i                                             // OPPO\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n\n            /(kobo)\\s(ereader|touch)/i,                                         // Kobo\n            /(archos) (gamepad2?)/i,                                            // Archos\n            /(hp).+(touchpad(?!.+tablet)|tablet)/i,                             // HP TouchPad\n            /(kindle)\\/([\\w\\.]+)/i,                                             // Kindle\n            /(nook)[\\w ]+build\\/(\\w+)/i,                                        // Nook\n            /(dell) (strea[kpr\\d ]*[\\dko])/i,                                   // Dell Streak\n            /(le[- ]+pan)[- ]+(\\w{1,9}) bui/i,                                  // Le Pan Tablets\n            /(trinity)[- ]*(t\\d{3}) bui/i,                                      // Trinity Tablets\n            /(gigaset)[- ]+(q\\w{1,9}) bui/i,                                    // Gigaset Tablets\n            /(vodafone) ([\\w ]+)(?:\\)| bui)/i                                   // Vodafone\n            ], [VENDOR, MODEL, [TYPE, TABLET]], [\n\n            /(surface duo)/i                                                    // Surface Duo\n            ], [MODEL, [VENDOR, MICROSOFT], [TYPE, TABLET]], [\n            /droid [\\d\\.]+; (fp\\du?)(?: b|\\))/i                                 // Fairphone\n            ], [MODEL, [VENDOR, 'Fairphone'], [TYPE, MOBILE]], [\n            /(u304aa)/i                                                         // AT&T\n            ], [MODEL, [VENDOR, 'AT&T'], [TYPE, MOBILE]], [\n            /\\bsie-(\\w*)/i                                                      // Siemens\n            ], [MODEL, [VENDOR, 'Siemens'], [TYPE, MOBILE]], [\n            /\\b(rct\\w+) b/i                                                     // RCA Tablets\n            ], [MODEL, [VENDOR, 'RCA'], [TYPE, TABLET]], [\n            /\\b(venue[\\d ]{2,7}) b/i                                            // Dell Venue Tablets\n            ], [MODEL, [VENDOR, 'Dell'], [TYPE, TABLET]], [\n            /\\b(q(?:mv|ta)\\w+) b/i                                              // Verizon Tablet\n            ], [MODEL, [VENDOR, 'Verizon'], [TYPE, TABLET]], [\n            /\\b(?:barnes[& ]+noble |bn[rt])([\\w\\+ ]*) b/i                       // Barnes & Noble Tablet\n            ], [MODEL, [VENDOR, 'Barnes & Noble'], [TYPE, TABLET]], [\n            /\\b(tm\\d{3}\\w+) b/i\n            ], [MODEL, [VENDOR, 'NuVision'], [TYPE, TABLET]], [\n            /\\b(k88) b/i                                                        // ZTE K Series Tablet\n            ], [MODEL, [VENDOR, 'ZTE'], [TYPE, TABLET]], [\n            /\\b(nx\\d{3}j) b/i                                                   // ZTE Nubia\n            ], [MODEL, [VENDOR, 'ZTE'], [TYPE, MOBILE]], [\n            /\\b(gen\\d{3}) b.+49h/i                                              // Swiss GEN Mobile\n            ], [MODEL, [VENDOR, 'Swiss'], [TYPE, MOBILE]], [\n            /\\b(zur\\d{3}) b/i                                                   // Swiss ZUR Tablet\n            ], [MODEL, [VENDOR, 'Swiss'], [TYPE, TABLET]], [\n            /\\b((zeki)?tb.*\\b) b/i                                              // Zeki Tablets\n            ], [MODEL, [VENDOR, 'Zeki'], [TYPE, TABLET]], [\n            /\\b([yr]\\d{2}) b/i,\n            /\\b(dragon[- ]+touch |dt)(\\w{5}) b/i                                // Dragon Touch Tablet\n            ], [[VENDOR, 'Dragon Touch'], MODEL, [TYPE, TABLET]], [\n            /\\b(ns-?\\w{0,9}) b/i                                                // Insignia Tablets\n            ], [MODEL, [VENDOR, 'Insignia'], [TYPE, TABLET]], [\n            /\\b((nxa|next)-?\\w{0,9}) b/i                                        // NextBook Tablets\n            ], [MODEL, [VENDOR, 'NextBook'], [TYPE, TABLET]], [\n            /\\b(xtreme\\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i                  // Voice Xtreme Phones\n            ], [[VENDOR, 'Voice'], MODEL, [TYPE, MOBILE]], [\n            /\\b(lvtel\\-)?(v1[12]) b/i                                           // LvTel Phones\n            ], [[VENDOR, 'LvTel'], MODEL, [TYPE, MOBILE]], [\n            /\\b(ph-1) /i                                                        // Essential PH-1\n            ], [MODEL, [VENDOR, 'Essential'], [TYPE, MOBILE]], [\n            /\\b(v(100md|700na|7011|917g).*\\b) b/i                               // Envizen Tablets\n            ], [MODEL, [VENDOR, 'Envizen'], [TYPE, TABLET]], [\n            /\\b(trio[-\\w\\. ]+) b/i                                              // MachSpeed Tablets\n            ], [MODEL, [VENDOR, 'MachSpeed'], [TYPE, TABLET]], [\n            /\\btu_(1491) b/i                                                    // Rotor Tablets\n            ], [MODEL, [VENDOR, 'Rotor'], [TYPE, TABLET]], [\n            /(shield[\\w ]+) b/i                                                 // Nvidia Shield Tablets\n            ], [MODEL, [VENDOR, 'Nvidia'], [TYPE, TABLET]], [\n            /(sprint) (\\w+)/i                                                   // Sprint Phones\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n            /(kin\\.[onetw]{3})/i                                                // Microsoft Kin\n            ], [[MODEL, /\\./g, ' '], [VENDOR, MICROSOFT], [TYPE, MOBILE]], [\n            /droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\\)/i             // Zebra\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, TABLET]], [\n            /droid.+; (ec30|ps20|tc[2-8]\\d[kx])\\)/i\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, MOBILE]], [\n\n            ///////////////////\n            // SMARTTVS\n            ///////////////////\n\n            /smart-tv.+(samsung)/i                                              // Samsung\n            ], [VENDOR, [TYPE, SMARTTV]], [\n            /hbbtv.+maple;(\\d+)/i\n            ], [[MODEL, /^/, 'SmartTV'], [VENDOR, SAMSUNG], [TYPE, SMARTTV]], [\n            /(nux; netcast.+smarttv|lg (netcast\\.tv-201\\d|android tv))/i        // LG SmartTV\n            ], [[VENDOR, LG], [TYPE, SMARTTV]], [\n            /(apple) ?tv/i                                                      // Apple TV\n            ], [VENDOR, [MODEL, APPLE+' TV'], [TYPE, SMARTTV]], [\n            /crkey/i                                                            // Google Chromecast\n            ], [[MODEL, CHROME+'cast'], [VENDOR, GOOGLE], [TYPE, SMARTTV]], [\n            /droid.+aft(\\w+)( bui|\\))/i                                         // Fire TV\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, SMARTTV]], [\n            /\\(dtv[\\);].+(aquos)/i,\n            /(aquos-tv[\\w ]+)\\)/i                                               // Sharp\n            ], [MODEL, [VENDOR, SHARP], [TYPE, SMARTTV]],[\n            /(bravia[\\w ]+)( bui|\\))/i                                              // Sony\n            ], [MODEL, [VENDOR, SONY], [TYPE, SMARTTV]], [\n            /(mitv-\\w{5}) bui/i                                                 // Xiaomi\n            ], [MODEL, [VENDOR, XIAOMI], [TYPE, SMARTTV]], [\n            /Hbbtv.*(technisat) (.*);/i                                         // TechniSAT\n            ], [VENDOR, MODEL, [TYPE, SMARTTV]], [\n            /\\b(roku)[\\dx]*[\\)\\/]((?:dvp-)?[\\d\\.]*)/i,                          // Roku\n            /hbbtv\\/\\d+\\.\\d+\\.\\d+ +\\([\\w\\+ ]*; *([\\w\\d][^;]*);([^;]*)/i         // HbbTV devices\n            ], [[VENDOR, trim], [MODEL, trim], [TYPE, SMARTTV]], [\n            /\\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\\b/i                   // SmartTV from Unidentified Vendors\n            ], [[TYPE, SMARTTV]], [\n\n            ///////////////////\n            // CONSOLES\n            ///////////////////\n\n            /(ouya)/i,                                                          // Ouya\n            /(nintendo) ([wids3utch]+)/i                                        // Nintendo\n            ], [VENDOR, MODEL, [TYPE, CONSOLE]], [\n            /droid.+; (shield) bui/i                                            // Nvidia\n            ], [MODEL, [VENDOR, 'Nvidia'], [TYPE, CONSOLE]], [\n            /(playstation [345portablevi]+)/i                                   // Playstation\n            ], [MODEL, [VENDOR, SONY], [TYPE, CONSOLE]], [\n            /\\b(xbox(?: one)?(?!; xbox))[\\); ]/i                                // Microsoft Xbox\n            ], [MODEL, [VENDOR, MICROSOFT], [TYPE, CONSOLE]], [\n\n            ///////////////////\n            // WEARABLES\n            ///////////////////\n\n            /((pebble))app/i                                                    // Pebble\n            ], [VENDOR, MODEL, [TYPE, WEARABLE]], [\n            /(watch)(?: ?os[,\\/]|\\d,\\d\\/)[\\d\\.]+/i                              // Apple Watch\n            ], [MODEL, [VENDOR, APPLE], [TYPE, WEARABLE]], [\n            /droid.+; (glass) \\d/i                                              // Google Glass\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, WEARABLE]], [\n            /droid.+; (wt63?0{2,3})\\)/i\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, WEARABLE]], [\n            /(quest( \\d| pro)?)/i                                               // Oculus Quest\n            ], [MODEL, [VENDOR, FACEBOOK], [TYPE, WEARABLE]], [\n\n            ///////////////////\n            // EMBEDDED\n            ///////////////////\n\n            /(tesla)(?: qtcarbrowser|\\/[-\\w\\.]+)/i                              // Tesla\n            ], [VENDOR, [TYPE, EMBEDDED]], [\n            /(aeobc)\\b/i                                                        // Echo Dot\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, EMBEDDED]], [\n\n            ////////////////////\n            // MIXED (GENERIC)\n            ///////////////////\n\n            /droid .+?; ([^;]+?)(?: bui|; wv\\)|\\) applew).+? mobile safari/i    // Android Phones from Unidentified Vendors\n            ], [MODEL, [TYPE, MOBILE]], [\n            /droid .+?; ([^;]+?)(?: bui|\\) applew).+?(?! mobile) safari/i       // Android Tablets from Unidentified Vendors\n            ], [MODEL, [TYPE, TABLET]], [\n            /\\b((tablet|tab)[;\\/]|focus\\/\\d(?!.+mobile))/i                      // Unidentifiable Tablet\n            ], [[TYPE, TABLET]], [\n            /(phone|mobile(?:[;\\/]| [ \\w\\/\\.]*safari)|pda(?=.+windows ce))/i    // Unidentifiable Mobile\n            ], [[TYPE, MOBILE]], [\n            /(android[-\\w\\. ]{0,9});.+buil/i                                    // Generic Android Device\n            ], [MODEL, [VENDOR, 'Generic']]\n        ],\n\n        engine : [[\n\n            /windows.+ edge\\/([\\w\\.]+)/i                                       // EdgeHTML\n            ], [VERSION, [NAME, EDGE+'HTML']], [\n\n            /webkit\\/537\\.36.+chrome\\/(?!27)([\\w\\.]+)/i                         // Blink\n            ], [VERSION, [NAME, 'Blink']], [\n\n            /(presto)\\/([\\w\\.]+)/i,                                             // Presto\n            /(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\\/([\\w\\.]+)/i, // WebKit/Trident/NetFront/NetSurf/Amaya/Lynx/w3m/Goanna\n            /ekioh(flow)\\/([\\w\\.]+)/i,                                          // Flow\n            /(khtml|tasman|links)[\\/ ]\\(?([\\w\\.]+)/i,                           // KHTML/Tasman/Links\n            /(icab)[\\/ ]([23]\\.[\\d\\.]+)/i,                                      // iCab\n            /\\b(libweb)/i\n            ], [NAME, VERSION], [\n\n            /rv\\:([\\w\\.]{1,9})\\b.+(gecko)/i                                     // Gecko\n            ], [VERSION, NAME]\n        ],\n\n        os : [[\n\n            // Windows\n            /microsoft (windows) (vista|xp)/i                                   // Windows (iTunes)\n            ], [NAME, VERSION], [\n            /(windows (?:phone(?: os)?|mobile))[\\/ ]?([\\d\\.\\w ]*)/i             // Windows Phone\n            ], [NAME, [VERSION, strMapper, windowsVersionMap]], [\n            /windows nt 6\\.2; (arm)/i,                                        // Windows RT\n            /windows[\\/ ]?([ntce\\d\\. ]+\\w)(?!.+xbox)/i,\n            /(?:win(?=3|9|n)|win 9x )([nt\\d\\.]+)/i\n            ], [[VERSION, strMapper, windowsVersionMap], [NAME, 'Windows']], [\n\n            // iOS/macOS\n            /ip[honead]{2,4}\\b(?:.*os ([\\w]+) like mac|; opera)/i,              // iOS\n            /(?:ios;fbsv\\/|iphone.+ios[\\/ ])([\\d\\.]+)/i,\n            /cfnetwork\\/.+darwin/i\n            ], [[VERSION, /_/g, '.'], [NAME, 'iOS']], [\n            /(mac os x) ?([\\w\\. ]*)/i,\n            /(macintosh|mac_powerpc\\b)(?!.+haiku)/i                             // Mac OS\n            ], [[NAME, MAC_OS], [VERSION, /_/g, '.']], [\n\n            // Mobile OSes\n            /droid ([\\w\\.]+)\\b.+(android[- ]x86|harmonyos)/i                    // Android-x86/HarmonyOS\n            ], [VERSION, NAME], [                                               // Android/WebOS/QNX/Bada/RIM/Maemo/MeeGo/Sailfish OS\n            /(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\\/ ]?([\\w\\.]*)/i,\n            /(blackberry)\\w*\\/([\\w\\.]*)/i,                                      // Blackberry\n            /(tizen|kaios)[\\/ ]([\\w\\.]+)/i,                                     // Tizen/KaiOS\n            /\\((series40);/i                                                    // Series 40\n            ], [NAME, VERSION], [\n            /\\(bb(10);/i                                                        // BlackBerry 10\n            ], [VERSION, [NAME, BLACKBERRY]], [\n            /(?:symbian ?os|symbos|s60(?=;)|series60)[-\\/ ]?([\\w\\.]*)/i         // Symbian\n            ], [VERSION, [NAME, 'Symbian']], [\n            /mozilla\\/[\\d\\.]+ \\((?:mobile|tablet|tv|mobile; [\\w ]+); rv:.+ gecko\\/([\\w\\.]+)/i // Firefox OS\n            ], [VERSION, [NAME, FIREFOX+' OS']], [\n            /web0s;.+rt(tv)/i,\n            /\\b(?:hp)?wos(?:browser)?\\/([\\w\\.]+)/i                              // WebOS\n            ], [VERSION, [NAME, 'webOS']], [\n            /watch(?: ?os[,\\/]|\\d,\\d\\/)([\\d\\.]+)/i                              // watchOS\n            ], [VERSION, [NAME, 'watchOS']], [\n\n            // Google Chromecast\n            /crkey\\/([\\d\\.]+)/i                                                 // Google Chromecast\n            ], [VERSION, [NAME, CHROME+'cast']], [\n            /(cros) [\\w]+(?:\\)| ([\\w\\.]+)\\b)/i                                  // Chromium OS\n            ], [[NAME, CHROMIUM_OS], VERSION],[\n\n            // Smart TVs\n            /panasonic;(viera)/i,                                               // Panasonic Viera\n            /(netrange)mmh/i,                                                   // Netrange\n            /(nettv)\\/(\\d+\\.[\\w\\.]+)/i,                                         // NetTV\n\n            // Console\n            /(nintendo|playstation) ([wids345portablevuch]+)/i,                 // Nintendo/Playstation\n            /(xbox); +xbox ([^\\);]+)/i,                                         // Microsoft Xbox (360, One, X, S, Series X, Series S)\n\n            // Other\n            /\\b(joli|palm)\\b ?(?:os)?\\/?([\\w\\.]*)/i,                            // Joli/Palm\n            /(mint)[\\/\\(\\) ]?(\\w*)/i,                                           // Mint\n            /(mageia|vectorlinux)[; ]/i,                                        // Mageia/VectorLinux\n            /([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\\/ ]?(?!chrom|package)([-\\w\\.]*)/i,\n                                                                                // Ubuntu/Debian/SUSE/Gentoo/Arch/Slackware/Fedora/Mandriva/CentOS/PCLinuxOS/RedHat/Zenwalk/Linpus/Raspbian/Plan9/Minix/RISCOS/Contiki/Deepin/Manjaro/elementary/Sabayon/Linspire\n            /(hurd|linux) ?([\\w\\.]*)/i,                                         // Hurd/Linux\n            /(gnu) ?([\\w\\.]*)/i,                                                // GNU\n            /\\b([-frentopcghs]{0,5}bsd|dragonfly)[\\/ ]?(?!amd|[ix346]{1,2}86)([\\w\\.]*)/i, // FreeBSD/NetBSD/OpenBSD/PC-BSD/GhostBSD/DragonFly\n            /(haiku) (\\w+)/i                                                    // Haiku\n            ], [NAME, VERSION], [\n            /(sunos) ?([\\w\\.\\d]*)/i                                             // Solaris\n            ], [[NAME, 'Solaris'], VERSION], [\n            /((?:open)?solaris)[-\\/ ]?([\\w\\.]*)/i,                              // Solaris\n            /(aix) ((\\d)(?=\\.|\\)| )[\\w\\.])*/i,                                  // AIX\n            /\\b(beos|os\\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i, // BeOS/OS2/AmigaOS/MorphOS/OpenVMS/Fuchsia/HP-UX/SerenityOS\n            /(unix) ?([\\w\\.]*)/i                                                // UNIX\n            ], [NAME, VERSION]\n        ]\n    };\n\n    /////////////////\n    // Constructor\n    ////////////////\n\n    var UAParser = function (ua, extensions) {\n\n        if (typeof ua === OBJ_TYPE) {\n            extensions = ua;\n            ua = undefined;\n        }\n\n        if (!(this instanceof UAParser)) {\n            return new UAParser(ua, extensions).getResult();\n        }\n\n        var _navigator = (typeof window !== UNDEF_TYPE && window.navigator) ? window.navigator : undefined;\n        var _ua = ua || ((_navigator && _navigator.userAgent) ? _navigator.userAgent : EMPTY);\n        var _uach = (_navigator && _navigator.userAgentData) ? _navigator.userAgentData : undefined;\n        var _rgxmap = extensions ? extend(regexes, extensions) : regexes;\n        var _isSelfNav = _navigator && _navigator.userAgent == _ua;\n\n        this.getBrowser = function () {\n            var _browser = {};\n            _browser[NAME] = undefined;\n            _browser[VERSION] = undefined;\n            rgxMapper.call(_browser, _ua, _rgxmap.browser);\n            _browser[MAJOR] = majorize(_browser[VERSION]);\n            // Brave-specific detection\n            if (_isSelfNav && _navigator && _navigator.brave && typeof _navigator.brave.isBrave == FUNC_TYPE) {\n                _browser[NAME] = 'Brave';\n            }\n            return _browser;\n        };\n        this.getCPU = function () {\n            var _cpu = {};\n            _cpu[ARCHITECTURE] = undefined;\n            rgxMapper.call(_cpu, _ua, _rgxmap.cpu);\n            return _cpu;\n        };\n        this.getDevice = function () {\n            var _device = {};\n            _device[VENDOR] = undefined;\n            _device[MODEL] = undefined;\n            _device[TYPE] = undefined;\n            rgxMapper.call(_device, _ua, _rgxmap.device);\n            if (_isSelfNav && !_device[TYPE] && _uach && _uach.mobile) {\n                _device[TYPE] = MOBILE;\n            }\n            // iPadOS-specific detection: identified as Mac, but has some iOS-only properties\n            if (_isSelfNav && _device[MODEL] == 'Macintosh' && _navigator && typeof _navigator.standalone !== UNDEF_TYPE && _navigator.maxTouchPoints && _navigator.maxTouchPoints > 2) {\n                _device[MODEL] = 'iPad';\n                _device[TYPE] = TABLET;\n            }\n            return _device;\n        };\n        this.getEngine = function () {\n            var _engine = {};\n            _engine[NAME] = undefined;\n            _engine[VERSION] = undefined;\n            rgxMapper.call(_engine, _ua, _rgxmap.engine);\n            return _engine;\n        };\n        this.getOS = function () {\n            var _os = {};\n            _os[NAME] = undefined;\n            _os[VERSION] = undefined;\n            rgxMapper.call(_os, _ua, _rgxmap.os);\n            if (_isSelfNav && !_os[NAME] && _uach && _uach.platform && _uach.platform != 'Unknown') {\n                _os[NAME] = _uach.platform  \n                                    .replace(/chrome os/i, CHROMIUM_OS)\n                                    .replace(/macos/i, MAC_OS);           // backward compatibility\n            }\n            return _os;\n        };\n        this.getResult = function () {\n            return {\n                ua      : this.getUA(),\n                browser : this.getBrowser(),\n                engine  : this.getEngine(),\n                os      : this.getOS(),\n                device  : this.getDevice(),\n                cpu     : this.getCPU()\n            };\n        };\n        this.getUA = function () {\n            return _ua;\n        };\n        this.setUA = function (ua) {\n            _ua = (typeof ua === STR_TYPE && ua.length > UA_MAX_LENGTH) ? trim(ua, UA_MAX_LENGTH) : ua;\n            return this;\n        };\n        this.setUA(_ua);\n        return this;\n    };\n\n    UAParser.VERSION = LIBVERSION;\n    UAParser.BROWSER =  enumerize([NAME, VERSION, MAJOR]);\n    UAParser.CPU = enumerize([ARCHITECTURE]);\n    UAParser.DEVICE = enumerize([MODEL, VENDOR, TYPE, CONSOLE, MOBILE, SMARTTV, TABLET, WEARABLE, EMBEDDED]);\n    UAParser.ENGINE = UAParser.OS = enumerize([NAME, VERSION]);\n\n    ///////////\n    // Export\n    //////////\n\n    // check js environment\n    if (typeof(exports) !== UNDEF_TYPE) {\n        // nodejs env\n        if (typeof module !== UNDEF_TYPE && module.exports) {\n            exports = module.exports = UAParser;\n        }\n        exports.UAParser = UAParser;\n    } else {\n        // requirejs env (optional)\n        if (typeof(define) === FUNC_TYPE && define.amd) {\n            define(function () {\n                return UAParser;\n            });\n        } else if (typeof window !== UNDEF_TYPE) {\n            // browser env\n            window.UAParser = UAParser;\n        }\n    }\n\n    // jQuery/Zepto specific (optional)\n    // Note:\n    //   In AMD env the global scope should be kept clean, but jQuery is an exception.\n    //   jQuery always exports to global scope, unless jQuery.noConflict(true) is used,\n    //   and we should catch that.\n    var $ = typeof window !== UNDEF_TYPE && (window.jQuery || window.Zepto);\n    if ($ && !$.ua) {\n        var parser = new UAParser();\n        $.ua = parser.getResult();\n        $.ua.get = function () {\n            return parser.getUA();\n        };\n        $.ua.set = function (ua) {\n            parser.setUA(ua);\n            var result = parser.getResult();\n            for (var prop in result) {\n                $.ua[prop] = result[prop];\n            }\n        };\n    }\n\n})(typeof window === 'object' ? window : this);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport EventBus from './EventBus.js';\nimport Events from './events/Events.js';\nimport FactoryMaker from './FactoryMaker.js';\n\nconst LOG_LEVEL_NONE = 0;\nconst LOG_LEVEL_FATAL = 1;\nconst LOG_LEVEL_ERROR = 2;\nconst LOG_LEVEL_WARNING = 3;\nconst LOG_LEVEL_INFO = 4;\nconst LOG_LEVEL_DEBUG = 5;\n\n/**\n * @module Debug\n * @param {object} config\n * @ignore\n */\nfunction Debug(config) {\n\n    config = config || {};\n    const context = this.context;\n    const eventBus = EventBus(context).getInstance();\n    const settings = config.settings;\n\n    const logFn = [];\n\n    let instance,\n        showLogTimestamp,\n        showCalleeName,\n        startTime;\n\n    function setup() {\n        showLogTimestamp = true;\n        showCalleeName = true;\n        startTime = new Date().getTime();\n\n        if (typeof window !== 'undefined' && window.console) {\n            logFn[LOG_LEVEL_FATAL] = getLogFn(window.console.error);\n            logFn[LOG_LEVEL_ERROR] = getLogFn(window.console.error);\n            logFn[LOG_LEVEL_WARNING] = getLogFn(window.console.warn);\n            logFn[LOG_LEVEL_INFO] = getLogFn(window.console.info);\n            logFn[LOG_LEVEL_DEBUG] = getLogFn(window.console.debug);\n        }\n    }\n\n    function getLogFn(fn) {\n        if (fn && fn.bind) {\n            return fn.bind(window.console);\n        }\n        // if not define, return the default function for reporting logs\n        return window.console.log.bind(window.console);\n    }\n\n    /**\n     * Retrieves a logger which can be used to write logging information in browser console.\n     * @param {object} instance Object for which the logger is created. It is used\n     * to include calle object information in log messages.\n     * @memberof module:Debug\n     * @returns {Logger}\n     * @instance\n     */\n    function getLogger(instance) {\n        return {\n            fatal: fatal.bind(instance),\n            error: error.bind(instance),\n            warn: warn.bind(instance),\n            info: info.bind(instance),\n            debug: debug.bind(instance)\n        };\n    }\n\n    /**\n     * Prepends a timestamp in milliseconds to each log message.\n     * @param {boolean} value Set to true if you want to see a timestamp in each log message.\n     * @default LOG_LEVEL_WARNING\n     * @memberof module:Debug\n     * @instance\n     */\n    function setLogTimestampVisible(value) {\n        showLogTimestamp = value;\n    }\n\n    /**\n     * Prepends the callee object name, and media type if available, to each log message.\n     * @param {boolean} value Set to true if you want to see the callee object name and media type in each log message.\n     * @default true\n     * @memberof module:Debug\n     * @instance\n     */\n    function setCalleeNameVisible(value) {\n        showCalleeName = value;\n    }\n\n    function fatal(...params) {\n        doLog(LOG_LEVEL_FATAL, this, ...params);\n    }\n\n    function error(...params) {\n        doLog(LOG_LEVEL_ERROR, this, ...params);\n    }\n\n    function warn(...params) {\n        doLog(LOG_LEVEL_WARNING, this, ...params);\n    }\n\n    function info(...params) {\n        doLog(LOG_LEVEL_INFO, this, ...params);\n    }\n\n    function debug(...params) {\n        doLog(LOG_LEVEL_DEBUG, this, ...params);\n    }\n\n    function doLog(level, _this, ...params) {\n        let message = '';\n        let logTime = null;\n\n        if (showLogTimestamp) {\n            logTime = new Date().getTime();\n            message += '[' + (logTime - startTime) + ']';\n        }\n\n        if (showCalleeName && _this && _this.getClassName) {\n            message += '[' + _this.getClassName() + ']';\n            if (_this.getType) {\n                message += '[' + _this.getType() + ']';\n            }\n        }\n\n        if (message.length > 0) {\n            message += ' ';\n        }\n\n        Array.apply(null, params).forEach(function (item) {\n            message += item + ' ';\n        });\n\n        // log to console if the log level is high enough\n        if (logFn[level] && settings && settings.get().debug.logLevel >= level) {\n            logFn[level](message);\n        }\n\n        // send log event regardless of log level\n        if (settings && settings.get().debug.dispatchEvent) {\n            eventBus.trigger(Events.LOG, { message: message, level: level });\n        }\n    }\n\n    instance = {\n        getLogger: getLogger,\n        setLogTimestampVisible: setLogTimestampVisible,\n        setCalleeNameVisible: setCalleeNameVisible\n    };\n\n    setup();\n\n    return instance;\n}\n\nDebug.__dashjs_factory_name = 'Debug';\n\nconst factory = FactoryMaker.getSingletonFactory(Debug);\nfactory.LOG_LEVEL_NONE = LOG_LEVEL_NONE;\nfactory.LOG_LEVEL_FATAL = LOG_LEVEL_FATAL;\nfactory.LOG_LEVEL_ERROR = LOG_LEVEL_ERROR;\nfactory.LOG_LEVEL_WARNING = LOG_LEVEL_WARNING;\nfactory.LOG_LEVEL_INFO = LOG_LEVEL_INFO;\nfactory.LOG_LEVEL_DEBUG = LOG_LEVEL_DEBUG;\nFactoryMaker.updateSingletonFactory(Debug.__dashjs_factory_name, factory);\nexport default factory;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport FactoryMaker from './FactoryMaker.js';\nimport MediaPlayerEvents from '../streaming/MediaPlayerEvents.js';\n\nconst EVENT_PRIORITY_LOW = 0;\nconst EVENT_PRIORITY_HIGH = 5000;\n\nfunction EventBus() {\n\n    let handlers = {};\n\n    function _commonOn(type, listener, scope, options = {}, executeOnlyOnce = false) {\n\n        if (!type) {\n            throw new Error('event type cannot be null or undefined');\n        }\n        if (!listener || typeof (listener) !== 'function') {\n            throw new Error('listener must be a function: ' + listener);\n        }\n\n        let priority = options.priority || EVENT_PRIORITY_LOW;\n\n        if (getHandlerIdx(type, listener, scope) >= 0) {\n            return;\n        }\n\n        handlers[type] = handlers[type] || [];\n\n        const handler = {\n            callback: listener,\n            scope,\n            priority,\n            executeOnlyOnce\n        };\n\n        if (scope && scope.getStreamId) {\n            handler.streamId = scope.getStreamId();\n        }\n        if (scope && scope.getType) {\n            handler.mediaType = scope.getType();\n        }\n        if (options && options.mode) {\n            handler.mode = options.mode;\n        }\n\n        const inserted = handlers[type].some((item, idx) => {\n            if (item && priority > item.priority) {\n                handlers[type].splice(idx, 0, handler);\n                return true;\n            }\n        });\n\n        if (!inserted) {\n            handlers[type].push(handler);\n        }\n    }\n\n    function on(type, listener, scope, options = {}) {\n        _commonOn(type, listener, scope, options);\n    }\n\n    function once(type, listener, scope, options = {}) {\n        _commonOn(type, listener, scope, options, true)\n    }\n\n    function off(type, listener, scope) {\n        if (!type || !listener || !handlers[type]) {\n            return;\n        }\n        const idx = getHandlerIdx(type, listener, scope);\n        if (idx < 0) {\n            return;\n        }\n        handlers[type][idx] = null;\n    }\n\n    function trigger(type, payload = {}, filters = {}) {\n        if (!type || !handlers[type]) {\n            return;\n        }\n\n        payload = payload || {};\n\n        if (payload.hasOwnProperty('type')) {\n            throw new Error('\\'type\\' is a reserved word for event dispatching');\n        }\n\n        payload.type = type;\n\n        if (filters.streamId) {\n            payload.streamId = filters.streamId;\n        }\n        if (filters.mediaType) {\n            payload.mediaType = filters.mediaType;\n        }\n\n        const handlersToRemove = [];\n        handlers[type]\n            .filter((handler) => {\n                if (!handler) {\n                    return false;\n                }\n                if (filters.streamId && handler.streamId && handler.streamId !== filters.streamId) {\n                    return false;\n                }\n                if (filters.mediaType && handler.mediaType && handler.mediaType !== filters.mediaType) {\n                    return false;\n                }\n                // This is used for dispatching DASH events. By default we use the onStart mode. Consequently we filter everything that has a non matching mode and the onReceive events for handlers that did not specify a mode.\n                if ((filters.mode && handler.mode && handler.mode !== filters.mode) || (!handler.mode && filters.mode && filters.mode === MediaPlayerEvents.EVENT_MODE_ON_RECEIVE)) {\n                    return false;\n                }\n                return true;\n            })\n            .forEach((handler) => {\n                handler && handler.callback.call(handler.scope, payload);\n                if (handler.executeOnlyOnce) {\n                    handlersToRemove.push(handler);\n                }\n            });\n\n        handlersToRemove.forEach((handler) => {\n            off(type, handler.callback, handler.scope);\n        })\n    }\n\n    function getHandlerIdx(type, listener, scope) {\n\n        let idx = -1;\n\n        if (!handlers[type]) {\n            return idx;\n        }\n\n        handlers[type].some((item, index) => {\n            if (item && item.callback === listener && (!scope || scope === item.scope)) {\n                idx = index;\n                return true;\n            }\n        });\n        return idx;\n    }\n\n    function reset() {\n        handlers = {};\n    }\n\n    const instance = {\n        on,\n        once,\n        off,\n        trigger,\n        reset\n    };\n\n    return instance;\n}\n\nEventBus.__dashjs_factory_name = 'EventBus';\nconst factory = FactoryMaker.getSingletonFactory(EventBus);\nfactory.EVENT_PRIORITY_LOW = EVENT_PRIORITY_LOW;\nfactory.EVENT_PRIORITY_HIGH = EVENT_PRIORITY_HIGH;\nFactoryMaker.updateSingletonFactory(EventBus.__dashjs_factory_name, factory);\nexport default factory;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @module FactoryMaker\n * @ignore\n */\nconst FactoryMaker = (function () {\n\n    let instance;\n    let singletonContexts = [];\n    const singletonFactories = {};\n    const classFactories = {};\n\n    function extend(name, childInstance, override, context) {\n        if (!context[name] && childInstance) {\n            context[name] = {\n                instance: childInstance,\n                override: override\n            };\n        }\n    }\n\n    /**\n     * Use this method from your extended object.  this.factory is injected into your object.\n     * this.factory.getSingletonInstance(this.context, 'VideoModel')\n     * will return the video model for use in the extended object.\n     *\n     * @param {Object} context - injected into extended object as this.context\n     * @param {string} className - string name found in all dash.js objects\n     * with name __dashjs_factory_name Will be at the bottom. Will be the same as the object's name.\n     * @returns {*} Context aware instance of specified singleton name.\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function getSingletonInstance(context, className) {\n        for (const i in singletonContexts) {\n            const obj = singletonContexts[i];\n            if (obj.context === context && obj.name === className) {\n                return obj.instance;\n            }\n        }\n        return null;\n    }\n\n    /**\n     * Use this method to add an singleton instance to the system.  Useful for unit testing to mock objects etc.\n     *\n     * @param {Object} context\n     * @param {string} className\n     * @param {Object} instance\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function setSingletonInstance(context, className, instance) {\n        for (const i in singletonContexts) {\n            const obj = singletonContexts[i];\n            if (obj.context === context && obj.name === className) {\n                singletonContexts[i].instance = instance;\n                return;\n            }\n        }\n        singletonContexts.push({\n            name: className,\n            context: context,\n            instance: instance\n        });\n    }\n\n    /**\n     * Use this method to remove all singleton instances associated with a particular context.\n     *\n     * @param {Object} context\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function deleteSingletonInstances(context) {\n        singletonContexts = singletonContexts.filter(x => x.context !== context);\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Factories storage Management\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function getFactoryByName(name, factoriesArray) {\n        return factoriesArray[name];\n    }\n\n    function updateFactory(name, factory, factoriesArray) {\n        if (name in factoriesArray) {\n            factoriesArray[name] = factory;\n        }\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Class Factories Management\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function updateClassFactory(name, factory) {\n        updateFactory(name, factory, classFactories);\n    }\n\n    function getClassFactoryByName(name) {\n        return getFactoryByName(name, classFactories);\n    }\n\n    function getClassFactory(classConstructor) {\n        let factory = getFactoryByName(classConstructor.__dashjs_factory_name, classFactories);\n\n        if (!factory) {\n            factory = function (context) {\n                if (context === undefined) {\n                    context = {};\n                }\n                return {\n                    create: function () {\n                        return merge(classConstructor, context, arguments);\n                    }\n                };\n            };\n\n            classFactories[classConstructor.__dashjs_factory_name] = factory; // store factory\n        }\n        return factory;\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Singleton Factory MAangement\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function updateSingletonFactory(name, factory) {\n        updateFactory(name, factory, singletonFactories);\n    }\n\n    function getSingletonFactoryByName(name) {\n        return getFactoryByName(name, singletonFactories);\n    }\n\n    function getSingletonFactory(classConstructor) {\n        let factory = getFactoryByName(classConstructor.__dashjs_factory_name, singletonFactories);\n        if (!factory) {\n            factory = function (context) {\n                let instance;\n                if (context === undefined) {\n                    context = {};\n                }\n                return {\n                    getInstance: function () {\n                        // If we don't have an instance yet check for one on the context\n                        if (!instance) {\n                            instance = getSingletonInstance(context, classConstructor.__dashjs_factory_name);\n                        }\n                        // If there's no instance on the context then create one\n                        if (!instance) {\n                            instance = merge(classConstructor, context, arguments);\n                            singletonContexts.push({\n                                name: classConstructor.__dashjs_factory_name,\n                                context: context,\n                                instance: instance\n                            });\n                        }\n                        return instance;\n                    }\n                };\n            };\n            singletonFactories[classConstructor.__dashjs_factory_name] = factory; // store factory\n        }\n\n        return factory;\n    }\n\n    function merge(classConstructor, context, args) {\n\n        let classInstance;\n        const className = classConstructor.__dashjs_factory_name;\n        const extensionObject = context[className];\n\n        if (extensionObject) {\n\n            let extension = extensionObject.instance;\n\n            if (extensionObject.override) { //Override public methods in parent but keep parent.\n\n                classInstance = classConstructor.apply({context}, args);\n                extension = extension.apply({\n                    context,\n                    factory: instance,\n                    parent: classInstance\n                }, args);\n\n                for (const prop in extension) {\n                    if (classInstance.hasOwnProperty(prop)) {\n                        classInstance[prop] = extension[prop];\n                    }\n                }\n\n            } else { //replace parent object completely with new object. Same as dijon.\n\n                return extension.apply({\n                    context,\n                    factory: instance\n                }, args);\n\n            }\n        } else {\n            // Create new instance of the class\n            classInstance = classConstructor.apply({context}, args);\n        }\n\n        // Add getClassName function to class instance prototype (used by Debug)\n        classInstance.getClassName = function () {return className;};\n\n        return classInstance;\n    }\n\n    instance = {\n        extend: extend,\n        getSingletonInstance: getSingletonInstance,\n        setSingletonInstance: setSingletonInstance,\n        deleteSingletonInstances: deleteSingletonInstances,\n        getSingletonFactory: getSingletonFactory,\n        getSingletonFactoryByName: getSingletonFactoryByName,\n        updateSingletonFactory: updateSingletonFactory,\n        getClassFactory: getClassFactory,\n        getClassFactoryByName: getClassFactoryByName,\n        updateClassFactory: updateClassFactory\n    };\n\n    return instance;\n\n}());\n\nexport default FactoryMaker;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport FactoryMaker from './FactoryMaker.js';\nimport Utils from './Utils.js';\nimport Debug from '../core/Debug.js';\nimport Constants from '../streaming/constants/Constants.js';\nimport {HTTPRequest} from '../streaming/vo/metrics/HTTPRequest.js';\nimport EventBus from './EventBus.js';\nimport Events from './events/Events.js';\n\n/** @module Settings\n * @description Define the configuration parameters of Dash.js MediaPlayer.\n * @see {@link module:Settings~PlayerSettings PlayerSettings} for further information about the supported configuration properties.\n */\n\n\n/**\n * @typedef {Object} PlayerSettings\n * @property {module:Settings~DebugSettings} [debug]\n * Debug related settings.\n * @property {module:Settings~ErrorSettings} [errors]\n * Error related settings\n * @property {module:Settings~StreamingSettings} [streaming]\n * Streaming related settings.\n * @example\n *\n * // Full settings object\n * settings = {\n *        debug: {\n *            logLevel: Debug.LOG_LEVEL_WARNING,\n *            dispatchEvent: false\n *        },\n *        streaming: {\n *            abandonLoadTimeout: 10000,\n *            wallclockTimeUpdateInterval: 100,\n *            manifestUpdateRetryInterval: 100,\n *            liveUpdateTimeThresholdInMilliseconds: 0,\n *            cacheInitSegments: false,\n *            applyServiceDescription: true,\n *            applyProducerReferenceTime: true,\n *            applyContentSteering: true,\n *            enableManifestDurationMismatchFix: true,\n *            parseInbandPrft: false,\n *            enableManifestTimescaleMismatchFix: false,\n *            capabilities: {\n *               filterUnsupportedEssentialProperties: true,\n *               supportedEssentialProperties: [\n *                   { schemeIdUri: Constants.FONT_DOWNLOAD_DVB_SCHEME },\n *                   { schemeIdUri: Constants.COLOUR_PRIMARIES_SCHEME_ID_URI, value: /1|5|6|7/ },\n *                   { schemeIdUri: Constants.URL_QUERY_INFO_SCHEME },\n *                   { schemeIdUri: Constants.EXT_URL_QUERY_INFO_SCHEME },\n *                   { schemeIdUri: Constants.MATRIX_COEFFICIENTS_SCHEME_ID_URI, value: /0|1|5|6/ },\n *                   { schemeIdUri: Constants.TRANSFER_CHARACTERISTICS_SCHEME_ID_URI, value: /1|6|13|14|15/ },\n *                   ...Constants.THUMBNAILS_SCHEME_ID_URIS.map(ep => { return { 'schemeIdUri': ep }; })\n *               ],\n *               useMediaCapabilitiesApi: true,\n *               filterVideoColorimetryEssentialProperties: false,\n *               filterHDRMetadataFormatEssentialProperties: false\n *            },\n *            events: {\n *              eventControllerRefreshDelay: 100,\n *              deleteEventMessageDataTimeout: 10000\n *            }\n *            timeShiftBuffer: {\n *                calcFromSegmentTimeline: false,\n *                fallbackToSegmentTimeline: true\n *            },\n *            metrics: {\n *              maxListDepth: 100\n *            },\n *            delay: {\n *                liveDelayFragmentCount: NaN,\n *                liveDelay: NaN,\n *                useSuggestedPresentationDelay: true\n *            },\n *            protection: {\n *                keepProtectionMediaKeys: false,\n *                ignoreEmeEncryptedEvent: false,\n *                detectPlayreadyMessageFormat: true,\n *                ignoreKeyStatuses: false\n *            },\n *            buffer: {\n *                enableSeekDecorrelationFix: false,\n *                fastSwitchEnabled: true,\n *                flushBufferAtTrackSwitch: false,\n *                reuseExistingSourceBuffers: true,\n *                bufferPruningInterval: 10,\n *                bufferToKeep: 20,\n *                bufferTimeAtTopQuality: 30,\n *                bufferTimeAtTopQualityLongForm: 60,\n *                initialBufferLevel: NaN,\n *                bufferTimeDefault: 18,\n *                longFormContentDurationThreshold: 600,\n *                stallThreshold: 0.3,\n *                lowLatencyStallThreshold: 0.3,\n *                useAppendWindow: true,\n *                setStallState: true,\n *                avoidCurrentTimeRangePruning: false,\n *                useChangeType: true,\n *                mediaSourceDurationInfinity: true,\n *                resetSourceBuffersForTrackSwitch: false,\n *                syntheticStallEvents: {\n *                    enabled: false,\n *                    ignoreReadyState: false\n *                }\n *            },\n *            gaps: {\n *                jumpGaps: true,\n *                jumpLargeGaps: true,\n *                smallGapLimit: 1.5,\n *                threshold: 0.3,\n *                enableSeekFix: true,\n *                enableStallFix: false,\n *                stallSeek: 0.1\n *            },\n *            utcSynchronization: {\n *                enabled: true,\n *                useManifestDateHeaderTimeSource: true,\n *                backgroundAttempts: 2,\n *                timeBetweenSyncAttempts: 30,\n *                maximumTimeBetweenSyncAttempts: 600,\n *                minimumTimeBetweenSyncAttempts: 2,\n *                timeBetweenSyncAttemptsAdjustmentFactor: 2,\n *                maximumAllowedDrift: 100,\n *                enableBackgroundSyncAfterSegmentDownloadError: true,\n *                defaultTimingSource: {\n *                    scheme: 'urn:mpeg:dash:utc:http-xsdate:2014',\n *                    value: 'http://time.akamai.com/?iso&ms'\n *                }\n *            },\n *            scheduling: {\n *                defaultTimeout: 500,\n *                lowLatencyTimeout: 0,\n *                scheduleWhilePaused: true\n *            },\n *            text: {\n *                defaultEnabled: true,\n *                dispatchForManualRendering: false,\n *                extendSegmentedCues: true,\n *                imsc: {\n *                    displayForcedOnlyMode: false,\n *                    enableRollUp: true\n *                },\n *                webvtt: {\n *                    customRenderingEnabled: false\n *                }\n *            },\n *            liveCatchup: {\n *                maxDrift: NaN,\n *                playbackRate: {min: NaN, max: NaN},\n *                playbackBufferMin: 0.5,\n *                enabled: null,\n *                mode: Constants.LIVE_CATCHUP_MODE_DEFAULT\n *            },\n *            lastBitrateCachingInfo: { enabled: true, ttl: 360000 },\n *            lastMediaSettingsCachingInfo: { enabled: true, ttl: 360000 },\n *            saveLastMediaSettingsForCurrentStreamingSession: true,\n *            cacheLoadThresholds: { video: 10, audio: 5 },\n *            trackSwitchMode: {\n *                audio: Constants.TRACK_SWITCH_MODE_ALWAYS_REPLACE,\n *                video: Constants.TRACK_SWITCH_MODE_NEVER_REPLACE\n *            },\n *            ignoreSelectionPriority: false,\n *            prioritizeRoleMain: true,\n *            assumeDefaultRoleAsMain: true,\n *            selectionModeForInitialTrack: Constants.TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY,\n *            fragmentRequestTimeout: 20000,\n *            fragmentRequestProgressTimeout: -1,\n *            manifestRequestTimeout: 10000,\n *            retryIntervals: {\n *                [HTTPRequest.MPD_TYPE]: 500,\n *                [HTTPRequest.XLINK_EXPANSION_TYPE]: 500,\n *                [HTTPRequest.MEDIA_SEGMENT_TYPE]: 1000,\n *                [HTTPRequest.INIT_SEGMENT_TYPE]: 1000,\n *                [HTTPRequest.BITSTREAM_SWITCHING_SEGMENT_TYPE]: 1000,\n *                [HTTPRequest.INDEX_SEGMENT_TYPE]: 1000,\n *                [HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE]: 1000,\n *                [HTTPRequest.LICENSE]: 1000,\n *                [HTTPRequest.OTHER_TYPE]: 1000,\n *                lowLatencyReductionFactor: 10\n *            },\n *            retryAttempts: {\n *                [HTTPRequest.MPD_TYPE]: 3,\n *                [HTTPRequest.XLINK_EXPANSION_TYPE]: 1,\n *                [HTTPRequest.MEDIA_SEGMENT_TYPE]: 3,\n *                [HTTPRequest.INIT_SEGMENT_TYPE]: 3,\n *                [HTTPRequest.BITSTREAM_SWITCHING_SEGMENT_TYPE]: 3,\n *                [HTTPRequest.INDEX_SEGMENT_TYPE]: 3,\n *                [HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE]: 3,\n *                [HTTPRequest.LICENSE]: 3,\n *                [HTTPRequest.OTHER_TYPE]: 3,\n *                lowLatencyMultiplyFactor: 5\n *            },\n *             abr: {\n *                 limitBitrateByPortal: false,\n *                 usePixelRatioInLimitBitrateByPortal: false,\n *                rules: {\n *                     throughputRule: {\n *                         active: true\n *                     },\n *                     bolaRule: {\n *                         active: true\n *                     },\n *                     insufficientBufferRule: {\n *                         active: true,\n *                         parameters: {\n *                             throughputSafetyFactor: 0.7,\n *                             segmentIgnoreCount: 2\n *                         }\n *                     },\n *                     switchHistoryRule: {\n *                         active: true,\n *                         parameters: {\n *                             sampleSize: 8,\n *                             switchPercentageThreshold: 0.075\n *                         }\n *                     },\n *                     droppedFramesRule: {\n *                         active: true,\n *                         parameters: {\n *                             minimumSampleSize: 375,\n *                             droppedFramesPercentageThreshold: 0.15\n *                         }\n *                     },\n *                     abandonRequestsRule: {\n *                         active: true,\n *                         parameters: {\n *                             abandonDurationMultiplier: 1.8,\n *                             minSegmentDownloadTimeThresholdInMs: 500,\n *                             minThroughputSamplesThreshold: 6\n *                         }\n *                     },\n *                     l2ARule: {\n *                         active: false\n *                     },\n *                     loLPRule: {\n *                         active: false\n *                     }\n *                 },\n *                 throughput: {\n *                     averageCalculationMode: Constants.THROUGHPUT_CALCULATION_MODES.EWMA,\n *                     lowLatencyDownloadTimeCalculationMode: Constants.LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE.MOOF_PARSING,\n *                     useResourceTimingApi: true,\n *                     useNetworkInformationApi: {\n *                         xhr: false,\n *                         fetch: false\n *                     },\n *                     useDeadTimeLatency: true,\n *                     bandwidthSafetyFactor: 0.9,\n *                     sampleSettings: {\n *                         live: 3,\n *                         vod: 4,\n *                         enableSampleSizeAdjustment: true,\n *                         decreaseScale: 0.7,\n *                         increaseScale: 1.3,\n *                         maxMeasurementsToKeep: 20,\n *                         averageLatencySampleAmount: 4,\n *                     },\n *                     ewma: {\n *                         throughputSlowHalfLifeSeconds: 8,\n *                         throughputFastHalfLifeSeconds: 3,\n *                         latencySlowHalfLifeCount: 2,\n *                         latencyFastHalfLifeCount: 1,\n *                         weightDownloadTimeMultiplicationFactor: 0.0015\n *                     }\n *                 },\n *                 maxBitrate: {\n *                     audio: -1,\n *                     video: -1\n *                 },\n *                 minBitrate: {\n *                     audio: -1,\n *                     video: -1\n *                 },\n *                 initialBitrate: {\n *                     audio: -1,\n *                     video: -1\n *                 },\n *                 autoSwitchBitrate: {\n *                     audio: true,\n *                     video: true\n *                 }\n *             },\n *            cmcd: {\n *                enabled: false,\n *                sid: null,\n *                cid: null,\n *                rtp: null,\n *                rtpSafetyFactor: 5,\n *                mode: Constants.CMCD_MODE_QUERY,\n *                enabledKeys: ['br', 'd', 'ot', 'tb' , 'bl', 'dl', 'mtp', 'nor', 'nrr', 'su' , 'bs', 'rtp' , 'cid', 'pr', 'sf', 'sid', 'st', 'v']\n *                includeInRequests: ['segment', 'mpd'],\n *                version: 1\n *            },\n *            cmsd: {\n *                enabled: false,\n *                abr: {\n *                    applyMb: false,\n *                    etpWeightRatio: 0\n *                }\n *            },\n *            defaultSchemeIdUri: {\n *                viewpoint: '',\n *                audioChannelConfiguration: 'urn:mpeg:mpegB:cicp:ChannelConfiguration',\n *                role: 'urn:mpeg:dash:role:2011',\n *                accessibility: 'urn:mpeg:dash:role:2011'\n *            }\n *          },\n *          errors: {\n *            recoverAttempts: {\n *                mediaErrorDecode: 5\n *             }\n *          }\n * }\n */\n\n/**\n * @typedef {Object} TimeShiftBuffer\n * @property {boolean} [calcFromSegmentTimeline=false]\n * Enable calculation of the DVR window for SegmentTimeline manifests based on the entries in \\<SegmentTimeline\\>.\n *  * @property {boolean} [fallbackToSegmentTimeline=true]\n * In case the MPD uses \\<SegmentTimeline\\ and no segment is found within the DVR window the DVR window is calculated based on the entries in \\<SegmentTimeline\\>.\n */\n\n/**\n * @typedef {Object} EventSettings\n * @property {number} [eventControllerRefreshDelay=100]\n * Interval timer used by the EventController to check if events need to be triggered or removed.\n * @property {number} [deleteEventMessageDataTimeout=10000]\n * If this value is larger than -1 the EventController will delete the message data attributes of events after they have been started and dispatched to the application.\n * This is to save memory in case events have a long duration and need to be persisted in the EventController.\n * This parameter defines the time in milliseconds between the start of an event and when the message data is deleted.\n * If an event is dispatched for the second time (e.g. when the user seeks back) the message data will not be included in the dispatched event if it has been deleted already.\n * Set this value to -1 to not delete any message data.\n */\n\n/**\n * @typedef {Object} LiveDelay\n * @property {number} [liveDelayFragmentCount=NaN]\n * Changing this value will lower or increase live stream latency.\n *\n * The detected segment duration will be multiplied by this value to define a time in seconds to delay a live stream from the live edge.\n *\n * Lowering this value will lower latency but may decrease the player's ability to build a stable buffer.\n * @property {number} [liveDelay=NaN]\n * Equivalent in seconds of setLiveDelayFragmentCount.\n *\n * Lowering this value will lower latency but may decrease the player's ability to build a stable buffer.\n *\n * This value should be less than the manifest duration by a couple of segment durations to avoid playback issues.\n *\n * If set, this parameter will take precedence over setLiveDelayFragmentCount and manifest info.\n * @property {boolean} [useSuggestedPresentationDelay=true]\n * Set to true if you would like to overwrite the default live delay and honor the SuggestedPresentationDelay attribute in by the manifest.\n */\n\n/**\n * @typedef {Object} Buffer\n * @property {boolean} [enableSeekDecorrelationFix=false]\n * Enables a workaround for playback start on some devices, e.g. WebOS 4.9.\n * It is necessary because some browsers do not support setting currentTime on video element to a value that is outside of current buffer.\n *\n * If you experience unexpected seeking triggered by BufferController, you can try setting this value to false.\n\n * @property {boolean} [fastSwitchEnabled=true]\n * When enabled, after an ABR up-switch in quality, instead of requesting and appending the next fragment at the end of the current buffer range it is requested and appended closer to the current time.\n *\n * When enabled, The maximum time to render a higher quality is current time + (1.5 * fragment duration).\n *\n * Note, When ABR down-switch is detected, we appended the lower quality at the end of the buffer range to preserve the\n * higher quality media for as long as possible.\n *\n * If enabled, it should be noted there are a few cases when the client will not replace inside buffer range but rather just append at the end.\n * 1. When the buffer level is less than one fragment duration.\n * 2. The client is in an Abandonment State due to recent fragment abandonment event.\n *\n * Known issues:\n * 1. In IE11 with auto switching off, if a user switches to a quality they can not download in time the fragment may be appended in the same range as the playhead or even in the past, in IE11 it may cause a stutter or stall in playback.\n * @property {boolean} [flushBufferAtTrackSwitch=false]\n * When enabled, after a track switch and in case buffer is being replaced, the video element is flushed (seek at current playback time) once a segment of the new track is appended in buffer in order to force video decoder to play new track.\n *\n * This can be required on some devices like GoogleCast devices to make track switching functional.\n *\n * Otherwise, track switching will be effective only once after previous buffered track is fully consumed.\n * @property {boolean} [reuseExistingSourceBuffers=true]\n * Enable reuse of existing MediaSource Sourcebuffers during period transition.\n * @property {number} [bufferPruningInterval=10]\n * The interval of pruning buffer in seconds.\n * @property {number} [bufferToKeep=20]\n * This value influences the buffer pruning logic.\n *\n * Allows you to modify the buffer that is kept in source buffer in seconds.\n * 0|-----------bufferToPrune-----------|-----bufferToKeep-----|currentTime|\n * @property {number} [bufferTimeDefault=18]\n * The time that the internal buffer target will be set to when not playing at the top quality.\n * @property {number} [bufferTimeAtTopQuality=30]\n * The time that the internal buffer target will be set to once playing the top quality.\n *\n * If there are multiple bitrates in your adaptation, and the media is playing at the highest bitrate, then we try to build a larger buffer at the top quality to increase stability and to maintain media quality.\n * @property {number} [bufferTimeAtTopQualityLongForm=60]\n * The time that the internal buffer target will be set to once playing the top quality for long form content.\n * @property {number} [longFormContentDurationThreshold=600]\n * The threshold which defines if the media is considered long form content.\n *\n * This will directly affect the buffer targets when playing back at the top quality.\n * @property {number} [initialBufferLevel=NaN]\n * Initial buffer level before playback starts\n * @property {number} [stallThreshold=0.3]\n * Stall threshold used in BufferController.js to determine whether a track should still be changed and which buffer range to prune.\n * @property {number} [lowLatencyStallThreshold=0.3]\n * Low Latency stall threshold used in BufferController.js to determine whether a track should still be changed and which buffer range to prune.\n * @property {boolean} [useAppendWindow=true]\n * Specifies if the appendWindow attributes of the MSE SourceBuffers should be set according to content duration from manifest.\n * @property {boolean} [setStallState=true]\n * Specifies if we fire manual waiting events once the stall threshold is reached.\n * @property {module:Settings~SyntheticStallSettings} [syntheticStallEvents]\n * Specifies if manual stall events are to be fired once the stall threshold is reached.\n * @property {boolean} [avoidCurrentTimeRangePruning=false]\n * Avoids pruning of the buffered range that contains the current playback time.\n *\n * That buffered range is likely to have been enqueued for playback. Pruning it causes a flush and reenqueue in WPE and WebKitGTK based browsers. This stresses the video decoder and can cause stuttering on embedded platforms.\n * @property {boolean} [useChangeType=true]\n * If this flag is set to true then dash.js will use the MSE v.2 API call \"changeType()\" before switching to a different codec family.\n * Note that some platforms might not implement the changeType function. dash.js is checking for the availability before trying to call it.\n * @property {boolean} [mediaSourceDurationInfinity=true]\n * If this flag is set to true then dash.js will allow `Infinity` to be set as the MediaSource duration otherwise the duration will be set to `Math.pow(2,32)` instead of `Infinity` to allow appending segments indefinitely.\n * Some platforms such as WebOS 4.x have issues with seeking when duration is set to `Infinity`, setting this flag to false resolve this.\n * @property {boolean} [resetSourceBuffersForTrackSwitch=false]\n * When switching to a track that is not compatible with the currently active MSE SourceBuffers, MSE will be reset. This happens when we switch codecs on a system\n * that does not properly implement \"changeType()\", such as webOS 4.0 and before.\n */\n\n/**\n * @typedef {Object} module:Settings~AudioVideoSettings\n * @property {number|boolean|string} [audio]\n * Configuration for audio media type of tracks.\n * @property {number|boolean|string} [video]\n * Configuration for video media type of tracks.\n */\n\n/**\n * @typedef {Object} module:Settings~SyntheticStallSettings\n * @property {boolean} [enabled]\n * Enables manual stall events and sets the playback rate to 0 once the stall threshold is reached.\n * @property {boolean} [ignoreReadyState]\n * Ignore the media element's ready state when entering or exiting a stall.\n * Enable this when either of these scenarios still occur with synthetic stalls enabled:\n * - If the buffer is empty, but playback is not stalled.\n * - If playback resumes, but a playing event isn't reported.\n */\n\n/**\n * @typedef {Object} DebugSettings\n * @property {number} [logLevel=dashjs.Debug.LOG_LEVEL_WARNING]\n * Sets up the log level. The levels are cumulative.\n *\n * For example, if you set the log level to dashjs.Debug.LOG_LEVEL_WARNING all warnings, errors and fatals will be logged.\n *\n * Possible values.\n *\n * - dashjs.Debug.LOG_LEVEL_NONE\n * No message is written in the browser console.\n *\n * - dashjs.Debug.LOG_LEVEL_FATAL\n * Log fatal errors.\n * An error is considered fatal when it causes playback to fail completely.\n *\n * - dashjs.Debug.LOG_LEVEL_ERROR\n * Log error messages.\n *\n * - dashjs.Debug.LOG_LEVEL_WARNING\n * Log warning messages.\n *\n * - dashjs.Debug.LOG_LEVEL_INFO\n * Log info messages.\n *\n * - dashjs.Debug.LOG_LEVEL_DEBUG\n * Log debug messages.\n * @property {boolean} [dispatchEvent=false]\n * Enable to trigger a Events.LOG event whenever log output is generated.\n *\n * Note this will be dispatched regardless of log level.\n */\n\n/**\n * @typedef {Object} module:Settings~ErrorSettings\n * @property {object} [recoverAttempts={mediaErrorDecode: 5}]\n * Defines the maximum number of recover attempts for specific media errors.\n *\n * For mediaErrorDecode the player will reset the MSE and skip the blacklisted segment that caused the decode error. The resulting gap will be handled by the GapController.\n */\n\n/**\n * @typedef {Object} CachingInfoSettings\n * @property {boolean} [enable]\n * Enable or disable the caching feature.\n * @property {number} [ttl]\n * Time to live.\n *\n * A value defined in milliseconds representing how log to cache the settings for.\n */\n\n/**\n * @typedef {Object} Gaps\n * @property {boolean} [jumpGaps=true]\n * Sets whether player should jump small gaps (discontinuities) in the buffer.\n * @property {boolean} [jumpLargeGaps=true]\n * Sets whether player should jump large gaps (discontinuities) in the buffer.\n * @property {number} [smallGapLimit=1.5]\n * Time in seconds for a gap to be considered small.\n * @property {number} [threshold=0.3]\n * Threshold at which the gap handling is executed. If currentRangeEnd - currentTime < threshold the gap jump will be triggered.\n * For live stream the jump might be delayed to keep a consistent live edge.\n * Note that the amount of buffer at which platforms automatically stall might differ.\n * @property {boolean} [enableSeekFix=true]\n * Enables the adjustment of the seek target once no valid segment request could be generated for a specific seek time. This can happen if the user seeks to a position for which there is a gap in the timeline.\n * @property {boolean} [enableStallFix=false]\n * If playback stalled in a buffered range this fix will perform a seek by the value defined in stallSeek to trigger playback again.\n * @property {number} [stallSeek=0.1]\n * Value to be used in case enableStallFix is set to true\n */\n\n/**\n * @typedef {Object} UtcSynchronizationSettings\n * @property {boolean} [enabled=true]\n * Enables or disables the UTC clock synchronization\n * @property {boolean} [useManifestDateHeaderTimeSource=true]\n * Allows you to enable the use of the Date Header, if exposed with CORS, as a timing source for live edge detection.\n *\n * The use of the date header will happen only after the other timing source that take precedence fail or are omitted as described.\n * @property {number} [backgroundAttempts=2]\n * Number of synchronization attempts to perform in the background after an initial synchronization request has been done. This is used to verify that the derived client-server offset is correct.\n *\n * The background requests are async and done in parallel to the start of the playback.\n *\n * This value is also used to perform a resync after 404 errors on segments.\n * @property {number} [timeBetweenSyncAttempts=30]\n * The time in seconds between two consecutive sync attempts.\n *\n * Note: This value is used as an initial starting value. The internal value of the TimeSyncController is adjusted during playback based on the drift between two consecutive synchronization attempts.\n *\n * Note: A sync is only performed after an MPD update. In case the @minimumUpdatePeriod is larger than this value the sync will be delayed until the next MPD update.\n * @property {number} [maximumTimeBetweenSyncAttempts=600]\n * The maximum time in seconds between two consecutive sync attempts.\n *\n * @property {number} [minimumTimeBetweenSyncAttempts=2]\n * The minimum time in seconds between two consecutive sync attempts.\n *\n * @property {number} [timeBetweenSyncAttemptsAdjustmentFactor=2]\n * The factor used to multiply or divide the timeBetweenSyncAttempts parameter after a sync. The maximumAllowedDrift defines whether this value is used as a factor or a dividend.\n *\n * @property {number} [maximumAllowedDrift=100]\n * The maximum allowed drift specified in milliseconds between two consecutive synchronization attempts.\n *\n * @property {boolean} [enableBackgroundSyncAfterSegmentDownloadError=true]\n * Enables or disables the background sync after the player ran into a segment download error.\n *\n * @property {object} [defaultTimingSource={scheme:'urn:mpeg:dash:utc:http-xsdate:2014',value: 'http://time.akamai.com/?iso&ms'}]\n * The default timing source to be used. The timing sources in the MPD take precedence over this one.\n */\n\n/**\n * @typedef {Object} Scheduling\n * @property {number} [defaultTimeout=500]\n * Default timeout between two consecutive segment scheduling attempts\n * @property {number} [lowLatencyTimeout=0]\n * Default timeout between two consecutive low-latency segment scheduling attempts\n * @property {boolean} [scheduleWhilePaused=true]\n * Set to true if you would like dash.js to keep downloading fragments in the background when the video element is paused.\n */\n\n/**\n * @typedef {Object} Text\n * @property {boolean} [defaultEnabled=true]\n * Enable/disable subtitle rendering by default.\n * @property {boolean} [dispatchForManualRendering=false]\n * Enable/disable firing of CueEnter/CueExt events. This will disable the display of subtitles and should be used when you want to have full control about rendering them.\n * @property {boolean} [extendSegmentedCues=true]\n * Enable/disable patching of segmented cues in order to merge as a single cue by extending cue end time.\n * @property {boolean} [imsc.displayForcedOnlyMode=false]\n * Enable/disable forced only mode in IMSC captions.\n * When true, only those captions where itts:forcedDisplay=\"true\" will be displayed.\n * @property {boolean} [imsc.enableRollUp=true]\n * Enable/disable rollUp style display of IMSC captions.\n * @property {object} [webvtt.customRenderingEnabled=false]\n * Enables the custom rendering for WebVTT captions. For details refer to the \"Subtitles and Captions\" sample section of dash.js.\n * Custom WebVTT rendering requires the external library vtt.js that can be found in the contrib folder.\n */\n\n/**\n * @typedef {Object} LiveCatchupSettings\n * @property {number} [maxDrift=NaN]\n * Use this method to set the maximum latency deviation allowed before dash.js to do a seeking to live position.\n *\n * In low latency mode, when the difference between the measured latency and the target one, as an absolute number, is higher than the one sets with this method, then dash.js does a seek to live edge position minus the target live delay.\n *\n * LowLatencyMaxDriftBeforeSeeking should be provided in seconds.\n *\n * If 0, then seeking operations won't be used for fixing latency deviations.\n *\n * Note: Catch-up mechanism is only applied when playing low latency live streams.\n * @property {number} [playbackRate={min: NaN, max: NaN}]\n * Use this parameter to set the minimum and maximum catch up rates, as percentages, for low latency live streams.\n *\n * In low latency mode, when measured latency is higher/lower than the target one, dash.js increases/decreases playback rate respectively up to (+/-) the percentage defined with this method until target is reached.\n *\n * Valid values for min catch up rate are in the range -0.5 to 0 (-50% to 0% playback rate decrease)\n *\n * Valid values for max catch up rate are in the range 0 to 1 (0% to 100% playback rate increase).\n *\n * Set min and max to NaN to turn off live catch up feature.\n *\n * These playback rate limits take precedence over any PlaybackRate values in ServiceDescription elements in an MPD. If only one of the min/max properties is given a value, the property without a value will not fall back to a ServiceDescription value. Its default value of NaN will be used.\n *\n * Note: Catch-up mechanism is only applied when playing low latency live streams.\n * @property {number} [playbackBufferMin=0.5]\n * Use this parameter to specify the minimum buffer which is used for LoL+ based playback rate reduction.\n *\n *\n * @property {boolean} [enabled=null]\n * Use this parameter to enable the catchup mode for non low-latency streams.\n *\n * @property {string} [mode=\"liveCatchupModeDefault\"]\n * Use this parameter to switch between different catchup modes.\n *\n * Options: \"liveCatchupModeDefault\" or \"liveCatchupModeLOLP\".\n *\n * Note: Catch-up mechanism is automatically applied when playing low latency live streams.\n */\n\n/**\n * @typedef {Object} RequestTypeSettings\n * @property {number} [MPD]\n * Manifest type of requests.\n * @property {number} [XLinkExpansion]\n * XLink expansion type of requests.\n * @property {number} [InitializationSegment]\n * Request to retrieve an initialization segment.\n * @property {number} [IndexSegment]\n * Request to retrieve an index segment (SegmentBase).\n * @property {number} [MediaSegment]\n * Request to retrieve a media segment (video/audio/image/text chunk).\n * @property {number} [BitstreamSwitchingSegment]\n * Bitrate stream switching type of request.\n * @property {number} [FragmentInfoSegment]\n * Request to retrieve a FragmentInfo segment (specific to Smooth Streaming live streams).\n * @property {number} [other]\n * Other type of request.\n * @property {number} [lowLatencyReductionFactor]\n * For low latency mode, values of type of request are divided by lowLatencyReductionFactor.\n *\n * Note: It's not type of request.\n * @property {number} [lowLatencyMultiplyFactor]\n * For low latency mode, values of type of request are multiplied by lowLatencyMultiplyFactor.\n *\n * Note: It's not type of request.\n */\n\n\n/**\n * @typedef {Object} Protection\n * @property {boolean} [keepProtectionMediaKeys=false]\n * Set the value for the ProtectionController and MediaKeys life cycle.\n *\n * If true, the ProtectionController and then created MediaKeys and MediaKeySessions will be preserved during the MediaPlayer lifetime.\n * @property {boolean} [ignoreEmeEncryptedEvent=false]\n * If set to true the player will ignore \"encrypted\" and \"needkey\" events thrown by the EME.\n *\n * @property {boolean} [detectPlayreadyMessageFormat=true]\n * If set to true the player will use the raw unwrapped message from the Playready CDM\n *\n * @property {boolean} [ignoreKeyStatuses=false]\n * If set to true the player will ignore the status of a key and try to play the corresponding track regardless whether the key is usable or not.\n */\n\n/**\n * @typedef {Object} Capabilities\n * @property {boolean} [filterUnsupportedEssentialProperties=true]\n * Enable to filter all the AdaptationSets and Representations which contain an unsupported \\<EssentialProperty\\> element.\n * @property {Array.<string>} [supportedEssentialProperties]\n * List of supported \\<EssentialProperty\\> elements\n * @property {boolean} [useMediaCapabilitiesApi=true]\n * Enable to use the MediaCapabilities API to check whether codecs are supported. If disabled MSE.isTypeSupported will be used instead.\n * @property {boolean} [filterVideoColorimetryEssentialProperties=false]\n * Enable dash.js to query MediaCapabilities API for signalled Colorimetry EssentialProperties (per schemeIdUris: 'urn:mpeg:mpegB:cicp:ColourPrimaries', 'urn:mpeg:mpegB:cicp:TransferCharacteristics').\n * If disabled, registered properties per supportedEssentialProperties will be allowed without any further checking (including 'urn:mpeg:mpegB:cicp:MatrixCoefficients').\n * @property {boolean} [filterHDRMetadataFormatEssentialProperties=false]\n * Enable dash.js to query MediaCapabilities API for signalled HDR-MetadataFormat EssentialProperty (per schemeIdUri:'urn:dvb:dash:hdr-dmi').\n */\n\n/**\n * @typedef {Object} AbrSettings\n * @property {boolean} [limitBitrateByPortal=false]\n * If true, the size of the video portal will limit the max chosen video resolution.\n * @property {boolean} [usePixelRatioInLimitBitrateByPortal=false]\n * Sets whether to take into account the device's pixel ratio when defining the portal dimensions.\n *\n * Useful on, for example, retina displays.\n * @property {module:Settings~AbrRules} [rules]\n * Enable/Disable individual ABR rules. Note that if the throughputRule and the bolaRule are activated at the same time we switch to a dynamic mode.\n * In the dynamic mode either ThroughputRule or BolaRule are active but not both at the same time.\n *\n * l2ARule and loLPRule are ABR rules that are designed for low latency streams. They are tested as standalone rules meaning the other rules should be deactivated when choosing these rules.\n * @property {module:Settings~ThroughputSettings} [throughput]\n * Settings related to throughput calculation\n * @property {module:Settings~AudioVideoSettings} [maxBitrate={audio: -1, video: -1}]\n * The maximum bitrate that the ABR algorithms will choose. This value is specified in kbps.\n *\n * Use -1 for no limit.\n * @property {module:Settings~AudioVideoSettings} [minBitrate={audio: -1, video: -1}]\n * The minimum bitrate that the ABR algorithms will choose. This value is specified in kbps.\n *\n * Use -1 for no limit.\n * @property {module:Settings~AudioVideoSettings} [initialBitrate={audio: -1, video: -1}]\n * Explicitly set the starting bitrate for audio or video. This value is specified in kbps.\n *\n * Use -1 to let the player decide.\n * @property {module:Settings~AudioVideoSettings} [autoSwitchBitrate={audio: true, video: true}]\n * Indicates whether the player should enable ABR algorithms to switch the bitrate.\n */\n\n/**\n * @typedef {Object} AbrRules\n * @property {module:Settings~ThroughputRule} [throughputRule]\n * Configuration of the Throughput rule\n * @property {module:Settings~BolaRule} [bolaRule]\n * Configuration of the BOLA rule\n * @property {module:Settings~InsufficientBufferRule} [insufficientBufferRule]\n * Configuration of the Insufficient Buffer rule\n * @property {module:Settings~SwitchHistoryRule} [switchHistoryRule]\n * Configuration of the Switch History rule\n * @property {module:Settings~DroppedFramesRule} [droppedFramesRule]\n * Configuration of the Dropped Frames rule\n * @property {module:Settings~AbandonRequestsRule} [abandonRequestsRule]\n * Configuration of the Abandon Requests rule\n * @property {module:Settings~L2ARule} [l2ARule]\n * Configuration of the L2A rule\n * @property {module:Settings~LoLPRule} [loLPRule]\n * Configuration of the LoLP rule\n */\n\n/**\n * @typedef {Object} ThroughputRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n */\n\n/**\n * @typedef {Object} BolaRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n */\n\n/**\n * @typedef {Object} InsufficientBufferRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n * @property {object} [parameters={throughputSafetyFactor=0.7, segmentIgnoreCount=2}]\n * Configures the rule specific parameters.\n *\n * - `throughputSafetyFactor`: The safety factor that is applied to the derived throughput, see example in the Description.\n * - `segmentIgnoreCount`: This rule is not taken into account until the first segmentIgnoreCount media segments have been appended to the buffer.\n */\n\n/**\n * @typedef {Object} SwitchHistoryRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n * @property {object} [parameters={sampleSize=8, switchPercentageThreshold=0.075}]\n * Configures the rule specific parameters.\n *\n * - `sampleSize`: Number of switch requests (\"no switch\", because of the selected Representation is already playing or \"actual switches\") required before the rule is applied\n * - `switchPercentageThreshold`: Ratio of actual quality drops compared to no drops before a quality down-switch is triggered\n */\n\n/**\n * @typedef {Object} DroppedFramesRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n * @property {object} [parameters={minimumSampleSize=375, droppedFramesPercentageThreshold=0.15}]\n * Configures the rule specific parameters.\n *\n * - `minimumSampleSize`: Sum of rendered and dropped frames required for each Representation before the rule kicks in.\n * - `droppedFramesPercentageThreshold`: Minimum percentage of dropped frames to trigger a quality down switch. Values are defined in the range of 0 - 1.\n */\n\n/**\n * @typedef {Object} AbandonRequestsRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n * @property {object} [parameters={abandonDurationMultiplier=1.8, minSegmentDownloadTimeThresholdInMs=500, minThroughputSamplesThreshold=6}]\n * Configures the rule specific parameters.\n *\n * - `abandonDurationMultiplier`: Factor to multiply with the segment duration to compare against the estimated remaining download time of the current segment. See code example above.\n * - `minSegmentDownloadTimeThresholdInMs`: The AbandonRequestRule only kicks if the download time of the current segment exceeds this value.\n * - `minThroughputSamplesThreshold`: Minimum throughput samples (equivalent to number of progress events) required before the AbandonRequestRule kicks in.\n */\n\n/**\n * @typedef {Object} L2ARule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n */\n\n/**\n * @typedef {Object} LoLPRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n */\n\n/**\n * @typedef {Object} ThroughputSettings\n * @property {string} [averageCalculationMode=Constants.THROUGHPUT_CALCULATION_MODES.EWMA]\n * Defines the default mode for calculating the throughput based on the samples collected during playback.\n *\n * For arithmetic and harmonic mean calculations we use a sliding window with the values defined in \"sampleSettings\"\n *\n * For exponential weighted moving average calculation the default values can be changed in \"ewma\"\n * @property {string} [lowLatencyDownloadTimeCalculationMode=Constants.LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE.MOOF_PARSING]\n * Defines the effective download time estimation method we use for low latency streams that utilize the Fetch API and chunked transfer coding\n * @property {boolean} [useResourceTimingApi=true]\n * If set to true the ResourceTimingApi is used to derive the download time and the number of downloaded bytes.\n * This option has no effect for low latency streaming as the download time equals the segment duration in most of the cases and therefor does not provide reliable values\n * @property {object} [useNetworkInformationApi = { xhr=false, fetch=false}]\n * If set to true the NetworkInformationApi is used to derive the current throughput. Browser support is limited, only available in Chrome and Edge.\n * Applies to standard (XHR requests) and/or low latency streaming (Fetch API requests).\n * @property {boolean} [useDeadTimeLatency=true]\n * If true, only the download portion will be considered part of the download bitrate and latency will be regarded as static.\n *\n * If false, the reciprocal of the whole transfer time will be used.\n * @property {number} [bandwidthSafetyFactor=0.9]\n * Standard ABR throughput rules multiply the throughput by this value.\n *\n * It should be between 0 and 1, with lower values giving less rebuffering (but also lower quality)\n * @property {object} [sampleSettings = {live=3,vod=4,enableSampleSizeAdjustment=true,decreaseScale=0.7,increaseScale=1.3,maxMeasurementsToKeep=20,averageLatencySampleAmount=4}]\n * When deriving the throughput based on the arithmetic or harmonic mean these settings define:\n * - `live`: Number of throughput samples to use (sample size) for live streams\n * - `vod`: Number of throughput samples to use (sample size) for VoD streams\n * - `enableSampleSizeAdjustment`: Adjust the sample sizes if throughput samples vary a lot\n * - `decreaseScale`: Increase sample size by one if the ratio of current and previous sample is below or equal this value\n * - `increaseScale`: Increase sample size by one if the ratio of current and previous sample is higher or equal this value\n * - `maxMeasurementsToKeep`: Number of samples to keep before sliding samples out of the window\n * - `averageLatencySampleAmount`: Number of latency samples to use (sample size)\n * @property {object} [ewma={throughputSlowHalfLifeSeconds=8,throughputFastHalfLifeSeconds=3,latencySlowHalfLifeCount=2,latencyFastHalfLifeCount=1, weightDownloadTimeMultiplicationFactor=0.0015}]\n * When deriving the throughput based on the exponential weighted moving average these settings define:\n * - `throughputSlowHalfLifeSeconds`: Number by which the weight of the current throughput measurement is divided, see ThroughputModel._updateEwmaValues\n * - `throughputFastHalfLifeSeconds`: Number by which the weight of the current throughput measurement is divided, see ThroughputModel._updateEwmaValues\n * - `latencySlowHalfLifeCount`: Number by which the weight of the current latency is divided, see ThroughputModel._updateEwmaValues\n * - `latencyFastHalfLifeCount`: Number by which the weight of the current latency is divided, see ThroughputModel._updateEwmaValues\n * - `weightDownloadTimeMultiplicationFactor`: This value is multiplied with the download time in milliseconds to derive the weight for the EWMA calculation.\n */\n\n/**\n * @typedef {Object} CmcdSettings\n * @property {boolean} [applyParametersFromMpd=true]\n * Set to true if dash.js should use the CMCD parameters defined in the MPD.\n * @property {boolean} [enable=false]\n * Enable or disable the CMCD reporting.\n * @property {string} [sid]\n * GUID identifying the current playback session.\n *\n * Should be in UUID format.\n *\n * If not specified a UUID will be automatically generated.\n * @property {string} [cid]\n * A unique string to identify the current content.\n *\n * If not specified it will be a hash of the MPD url.\n * @property {number} [rtp]\n * The requested maximum throughput that the client considers sufficient for delivery of the asset.\n *\n * If not specified this value will be dynamically calculated in the CMCDModel based on the current buffer level.\n * @property {number} [rtpSafetyFactor=5]\n * This value is used as a factor for the rtp value calculation: rtp = minBandwidth * rtpSafetyFactor\n *\n * If not specified this value defaults to 5. Note that this value is only used when no static rtp value is defined.\n * @property {number} [mode=\"query\"]\n * The method to use to attach cmcd metrics to the requests. 'query' to use query parameters, 'header' to use http headers.\n *\n * If not specified this value defaults to 'query'.\n * @property {Array.<string>} [enabledKeys]\n * This value is used to specify the desired CMCD parameters. Parameters not included in this list are not reported.\n * @property {Array.<string>} [includeInRequests]\n * Specifies which HTTP GET requests shall carry parameters.\n *\n * If not specified this value defaults to ['segment', 'mpd].\n * @property {number} [version=1]\n * The version of the CMCD to use.\n *\n * If not specified this value defaults to 1.\n */\n\n/**\n * @typedef {Object} module:Settings~CmsdSettings\n * @property {boolean} [enabled=false]\n * Enable or disable the CMSD response headers parsing.\n * @property {module:Settings~CmsdAbrSettings} [abr]\n * Sets additional ABR rules based on CMSD response headers.\n */\n\n/**\n * @typedef {Object} CmsdAbrSettings\n * @property {boolean} [applyMb=false]\n * Set to true if dash.js should apply CMSD maximum suggested bitrate in ABR logic.\n * @property {number} [etpWeightRatio=0]\n * Sets the weight ratio (between 0 and 1) that shall be applied on CMSD estimated throuhgput compared to measured throughput when calculating throughput.\n */\n\n/**\n * @typedef {Object} Metrics\n * @property {number} [metricsMaxListDepth=100]\n * Maximum number of metrics that are persisted per type.\n */\n\n/**\n * @typedef {Object} StreamingSettings\n * @property {number} [abandonLoadTimeout=10000]\n * A timeout value in seconds, which during the ABRController will block switch-up events.\n *\n * This will only take effect after an abandoned fragment event occurs.\n * @property {number} [wallclockTimeUpdateInterval=100]\n * How frequently the wallclockTimeUpdated internal event is triggered (in milliseconds).\n * @property {number} [manifestUpdateRetryInterval=100]\n * For live streams, set the interval-frequency in milliseconds at which dash.js will check if the current manifest is still processed before downloading the next manifest once the minimumUpdatePeriod time has.\n * @property {number} [liveUpdateTimeThresholdInMilliseconds=0]\n * For live streams, postpone syncing time updates until the threshold is passed. Increase if problems occurs during live streams on low end devices.\n * @property {boolean} [cacheInitSegments=false]\n * Enables the caching of init segments to avoid requesting the init segments before each representation switch.\n * @property {boolean} [applyServiceDescription=true]\n * Set to true if dash.js should use the parameters defined in ServiceDescription elements\n * @property {boolean} [applyProducerReferenceTime=true]\n * Set to true if dash.js should use the parameters defined in ProducerReferenceTime elements in combination with ServiceDescription elements.\n * @property {boolean} [applyContentSteering=true]\n * Set to true if dash.js should apply content steering during playback.\n * @property {boolean} [enableManifestDurationMismatchFix=true]\n * For multi-period streams, overwrite the manifest mediaPresentationDuration attribute with the sum of period durations if the manifest mediaPresentationDuration is greater than the sum of period durations\n * @property {boolean} [enableManifestTimescaleMismatchFix=false]\n * Overwrite the manifest segments base information timescale attributes with the timescale set in initialization segments\n * @property {boolean} [parseInbandPrft=false]\n * Set to true if dash.js should parse inband prft boxes (ProducerReferenceTime) and trigger events.\n * @property {module:Settings~Metrics} metrics Metric settings\n * @property {module:Settings~LiveDelay} delay Live Delay settings\n * @property {module:Settings~EventSettings} events Event settings\n * @property {module:Settings~TimeShiftBuffer} timeShiftBuffer TimeShiftBuffer settings\n * @property {module:Settings~Protection} protection DRM related settings\n * @property {module:Settings~Capabilities} capabilities Capability related settings\n * @property {module:Settings~Buffer}  buffer Buffer related settings\n * @property {module:Settings~Gaps}  gaps Gap related settings\n * @property {module:Settings~UtcSynchronizationSettings} utcSynchronization Settings related to UTC clock synchronization\n * @property {module:Settings~Scheduling} scheduling Settings related to segment scheduling\n * @property {module:Settings~Text} text Settings related to Subtitles and captions\n * @property {module:Settings~LiveCatchupSettings} liveCatchup  Settings related to live catchup.\n * @property {module:Settings~CachingInfoSettings} [lastBitrateCachingInfo={enabled: true, ttl: 360000}]\n * Set to false if you would like to disable the last known bit rate from being stored during playback and used to set the initial bit rate for subsequent playback within the expiration window.\n *\n * The default expiration is one hour, defined in milliseconds.\n *\n * If expired, the default initial bit rate (closest to 1000 kbps) will be used for that session and a new bit rate will be stored during that session.\n * @property {module:Settings~CachingInfoSettings} [lastMediaSettingsCachingInfo={enabled: true, ttl: 360000}]\n * Set to false if you would like to disable the last media settings from being stored to localStorage during playback and used to set the initial track for subsequent playback within the expiration window.\n *\n * The default expiration is one hour, defined in milliseconds.\n * @property {boolean} [saveLastMediaSettingsForCurrentStreamingSession=true]\n * Set to true if dash.js should save media settings from last selected track for incoming track selection during current streaming session.\n * @property {module:Settings~AudioVideoSettings} [cacheLoadThresholds={video: 10, audio: 5}]\n * For a given media type, the threshold which defines if the response to a fragment request is coming from browser cache or not.\n * @property {module:Settings~AudioVideoSettings} [trackSwitchMode={video: \"neverReplace\", audio: \"alwaysReplace\"}]\n * For a given media type defines if existing segments in the buffer should be overwritten once the track is switched. For instance if the user switches the audio language the existing segments in the audio buffer will be replaced when setting this value to \"alwaysReplace\".\n *\n * Possible values\n *\n * - Constants.TRACK_SWITCH_MODE_ALWAYS_REPLACE\n * Replace existing segments in the buffer\n *\n * - Constants.TRACK_SWITCH_MODE_NEVER_REPLACE\n * Do not replace existing segments in the buffer\n *\n * @property {} [ignoreSelectionPriority: false]\n * provides the option to disregard any signalled selectionPriority attribute. If disabled and if no initial media settings are set, track selection is accomplished as defined by selectionModeForInitialTrack.\n *\n * @property {} [prioritizeRoleMain: true]\n * provides the option to disable prioritization of AdaptationSets with their Role set to Main\n *\n * @property {} [assumeDefaultRoleAsMain: true]\n * when no Role descriptor is present, assume main per default\n * \n * @property {string} [selectionModeForInitialTrack=\"highestEfficiency\"]\n * Sets the selection mode for the initial track. This mode defines how the initial track will be selected if no initial media settings are set. If initial media settings are set this parameter will be ignored. Available options are:\n *\n * Possible values\n *\n * - Constants.TRACK_SELECTION_MODE_HIGHEST_BITRATE\n * This mode makes the player select the track with a highest bitrate.\n *\n * - Constants.TRACK_SELECTION_MODE_FIRST_TRACK\n * This mode makes the player select the first track found in the manifest.\n *\n * - Constants.TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY\n * This mode makes the player select the track with the lowest bitrate per pixel average.\n *\n * - Constants.TRACK_SELECTION_MODE_WIDEST_RANGE\n * This mode makes the player select the track with a widest range of bitrates.\n *\n *\n * @property {number} [fragmentRequestTimeout=20000]\n * Time in milliseconds before timing out on loading a media fragment.\n *\n * @property {number} [fragmentRequestProgressTimeout=-1]\n * Time in milliseconds before timing out on loading progress of a media fragment.\n *\n * @property {number} [manifestRequestTimeout=10000]\n * Time in milliseconds before timing out on loading a manifest.\n *\n * Fragments that timeout are retried as if they failed.\n * @property {module:Settings~RequestTypeSettings} [retryIntervals]\n * Time in milliseconds of which to reload a failed file load attempt.\n *\n * For low latency mode these values are divided by lowLatencyReductionFactor.\n * @property {module:Settings~RequestTypeSettings} [retryAttempts]\n * Total number of retry attempts that will occur on a file load before it fails.\n *\n * For low latency mode these values are multiplied by lowLatencyMultiplyFactor.\n * @property {module:Settings~AbrSettings} abr\n * Adaptive Bitrate algorithm related settings.\n * @property {module:Settings~CmcdSettings} cmcd\n * Settings related to Common Media Client Data reporting.\n * @property {module:Settings~CmsdSettings} cmsd\n * Settings related to Common Media Server Data parsing.\n * @property {module:Settings~defaultSchemeIdUri} defaultSchemeIdUri\n * Default schemeIdUri for descriptor type elements\n * These strings are used when not provided with setInitialMediaSettingsFor()\n */\n\n\n/**\n * @class\n * @ignore\n */\nfunction Settings() {\n    let instance;\n    const context = this.context;\n    const eventBus = EventBus(context).getInstance();\n    const DISPATCH_KEY_MAP = {\n        'streaming.delay.liveDelay': Events.SETTING_UPDATED_LIVE_DELAY,\n        'streaming.delay.liveDelayFragmentCount': Events.SETTING_UPDATED_LIVE_DELAY_FRAGMENT_COUNT,\n        'streaming.liveCatchup.enabled': Events.SETTING_UPDATED_CATCHUP_ENABLED,\n        'streaming.liveCatchup.playbackRate.min': Events.SETTING_UPDATED_PLAYBACK_RATE_MIN,\n        'streaming.liveCatchup.playbackRate.max': Events.SETTING_UPDATED_PLAYBACK_RATE_MAX,\n        'streaming.abr.rules.throughputRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.bolaRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.insufficientBufferRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.switchHistoryRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.droppedFramesRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.abandonRequestsRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.l2ARule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.loLPRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.maxBitrate.video': Events.SETTING_UPDATED_MAX_BITRATE,\n        'streaming.abr.maxBitrate.audio': Events.SETTING_UPDATED_MAX_BITRATE,\n        'streaming.abr.minBitrate.video': Events.SETTING_UPDATED_MIN_BITRATE,\n        'streaming.abr.minBitrate.audio': Events.SETTING_UPDATED_MIN_BITRATE,\n    };\n\n    /**\n     * @const {PlayerSettings} defaultSettings\n     * @ignore\n     */\n    const defaultSettings = {\n        debug: {\n            logLevel: Debug.LOG_LEVEL_WARNING,\n            dispatchEvent: false\n        },\n        streaming: {\n            abandonLoadTimeout: 10000,\n            wallclockTimeUpdateInterval: 100,\n            manifestUpdateRetryInterval: 100,\n            liveUpdateTimeThresholdInMilliseconds: 0,\n            cacheInitSegments: false,\n            applyServiceDescription: true,\n            applyProducerReferenceTime: true,\n            applyContentSteering: true,\n            enableManifestDurationMismatchFix: true,\n            parseInbandPrft: false,\n            enableManifestTimescaleMismatchFix: false,\n            capabilities: {\n                filterUnsupportedEssentialProperties: true,\n                supportedEssentialProperties: [\n                    { schemeIdUri: Constants.FONT_DOWNLOAD_DVB_SCHEME },\n                    { schemeIdUri: Constants.COLOUR_PRIMARIES_SCHEME_ID_URI, value: /1|5|6|7/ },\n                    { schemeIdUri: Constants.URL_QUERY_INFO_SCHEME },\n                    { schemeIdUri: Constants.EXT_URL_QUERY_INFO_SCHEME },\n                    { schemeIdUri: Constants.MATRIX_COEFFICIENTS_SCHEME_ID_URI, value: /0|1|5|6/ },\n                    { schemeIdUri: Constants.TRANSFER_CHARACTERISTICS_SCHEME_ID_URI, value: /1|6|13|14|15/ },\n                    ...Constants.THUMBNAILS_SCHEME_ID_URIS.map(ep => {\n                        return { 'schemeIdUri': ep };\n                    })\n                ],\n                useMediaCapabilitiesApi: true,\n                filterVideoColorimetryEssentialProperties: false,\n                filterHDRMetadataFormatEssentialProperties: false\n            },\n            events: {\n                eventControllerRefreshDelay: 100,\n                deleteEventMessageDataTimeout: 10000\n            },\n            timeShiftBuffer: {\n                calcFromSegmentTimeline: false,\n                fallbackToSegmentTimeline: true\n            },\n            metrics: {\n                maxListDepth: 100\n            },\n            delay: {\n                liveDelayFragmentCount: NaN,\n                liveDelay: NaN,\n                useSuggestedPresentationDelay: true\n            },\n            protection: {\n                keepProtectionMediaKeys: false,\n                ignoreEmeEncryptedEvent: false,\n                detectPlayreadyMessageFormat: true,\n                ignoreKeyStatuses: false\n            },\n            buffer: {\n                enableSeekDecorrelationFix: false,\n                fastSwitchEnabled: null,\n                flushBufferAtTrackSwitch: false,\n                reuseExistingSourceBuffers: true,\n                bufferPruningInterval: 10,\n                bufferToKeep: 20,\n                bufferTimeAtTopQuality: 30,\n                bufferTimeAtTopQualityLongForm: 60,\n                initialBufferLevel: NaN,\n                bufferTimeDefault: 18,\n                longFormContentDurationThreshold: 600,\n                stallThreshold: 0.3,\n                lowLatencyStallThreshold: 0.3,\n                useAppendWindow: true,\n                setStallState: true,\n                avoidCurrentTimeRangePruning: false,\n                useChangeType: true,\n                mediaSourceDurationInfinity: true,\n                resetSourceBuffersForTrackSwitch: false,\n                syntheticStallEvents: {\n                    enabled: false,\n                    ignoreReadyState: false\n                }\n            },\n            gaps: {\n                jumpGaps: true,\n                jumpLargeGaps: true,\n                smallGapLimit: 1.5,\n                threshold: 0.3,\n                enableSeekFix: true,\n                enableStallFix: false,\n                stallSeek: 0.1\n            },\n            utcSynchronization: {\n                enabled: true,\n                useManifestDateHeaderTimeSource: true,\n                backgroundAttempts: 2,\n                timeBetweenSyncAttempts: 30,\n                maximumTimeBetweenSyncAttempts: 600,\n                minimumTimeBetweenSyncAttempts: 2,\n                timeBetweenSyncAttemptsAdjustmentFactor: 2,\n                maximumAllowedDrift: 100,\n                enableBackgroundSyncAfterSegmentDownloadError: true,\n                defaultTimingSource: {\n                    scheme: 'urn:mpeg:dash:utc:http-xsdate:2014',\n                    value: 'https://time.akamai.com/?iso&ms'\n                }\n            },\n            scheduling: {\n                defaultTimeout: 500,\n                lowLatencyTimeout: 0,\n                scheduleWhilePaused: true\n            },\n            text: {\n                defaultEnabled: true,\n                dispatchForManualRendering: false,\n                extendSegmentedCues: true,\n                imsc: {\n                    displayForcedOnlyMode: false,\n                    enableRollUp: true\n                },\n                webvtt: {\n                    customRenderingEnabled: false\n                }\n            },\n            liveCatchup: {\n                maxDrift: NaN,\n                playbackRate: {\n                    min: NaN,\n                    max: NaN\n                },\n                playbackBufferMin: 0.5,\n                enabled: null,\n                mode: Constants.LIVE_CATCHUP_MODE_DEFAULT\n            },\n            lastBitrateCachingInfo: {\n                enabled: true,\n                ttl: 360000\n            },\n            lastMediaSettingsCachingInfo: {\n                enabled: true,\n                ttl: 360000\n            },\n            saveLastMediaSettingsForCurrentStreamingSession: true,\n            cacheLoadThresholds: {\n                video: 10,\n                audio: 5\n            },\n            trackSwitchMode: {\n                audio: Constants.TRACK_SWITCH_MODE_ALWAYS_REPLACE,\n                video: Constants.TRACK_SWITCH_MODE_NEVER_REPLACE\n            },\n            ignoreSelectionPriority: false,\n            prioritizeRoleMain: true,\n            assumeDefaultRoleAsMain: true,\n            selectionModeForInitialTrack: Constants.TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY,\n            fragmentRequestTimeout: 20000,\n            fragmentRequestProgressTimeout: -1,\n            manifestRequestTimeout: 10000,\n            retryIntervals: {\n                [HTTPRequest.MPD_TYPE]: 500,\n                [HTTPRequest.XLINK_EXPANSION_TYPE]: 500,\n                [HTTPRequest.MEDIA_SEGMENT_TYPE]: 1000,\n                [HTTPRequest.INIT_SEGMENT_TYPE]: 1000,\n                [HTTPRequest.BITSTREAM_SWITCHING_SEGMENT_TYPE]: 1000,\n                [HTTPRequest.INDEX_SEGMENT_TYPE]: 1000,\n                [HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE]: 1000,\n                [HTTPRequest.LICENSE]: 1000,\n                [HTTPRequest.OTHER_TYPE]: 1000,\n                lowLatencyReductionFactor: 10\n            },\n            retryAttempts: {\n                [HTTPRequest.MPD_TYPE]: 3,\n                [HTTPRequest.XLINK_EXPANSION_TYPE]: 1,\n                [HTTPRequest.MEDIA_SEGMENT_TYPE]: 3,\n                [HTTPRequest.INIT_SEGMENT_TYPE]: 3,\n                [HTTPRequest.BITSTREAM_SWITCHING_SEGMENT_TYPE]: 3,\n                [HTTPRequest.INDEX_SEGMENT_TYPE]: 3,\n                [HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE]: 3,\n                [HTTPRequest.LICENSE]: 3,\n                [HTTPRequest.OTHER_TYPE]: 3,\n                lowLatencyMultiplyFactor: 5\n            },\n            abr: {\n                limitBitrateByPortal: false,\n                usePixelRatioInLimitBitrateByPortal: false,\n                enableSupplementalPropertyAdaptationSetSwitching: true,\n                rules: {\n                    throughputRule: {\n                        active: true\n                    },\n                    bolaRule: {\n                        active: true\n                    },\n                    insufficientBufferRule: {\n                        active: true,\n                        parameters: {\n                            throughputSafetyFactor: 0.7,\n                            segmentIgnoreCount: 2\n                        }\n                    },\n                    switchHistoryRule: {\n                        active: true,\n                        parameters: {\n                            sampleSize: 8,\n                            switchPercentageThreshold: 0.075\n                        }\n                    },\n                    droppedFramesRule: {\n                        active: false,\n                        parameters: {\n                            minimumSampleSize: 375,\n                            droppedFramesPercentageThreshold: 0.15\n                        }\n                    },\n                    abandonRequestsRule: {\n                        active: true,\n                        parameters: {\n                            abandonDurationMultiplier: 1.8,\n                            minSegmentDownloadTimeThresholdInMs: 500,\n                            minThroughputSamplesThreshold: 6\n                        }\n                    },\n                    l2ARule: {\n                        active: false\n                    },\n                    loLPRule: {\n                        active: false\n                    }\n                },\n                throughput: {\n                    averageCalculationMode: Constants.THROUGHPUT_CALCULATION_MODES.EWMA,\n                    lowLatencyDownloadTimeCalculationMode: Constants.LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE.MOOF_PARSING,\n                    useResourceTimingApi: true,\n                    useNetworkInformationApi: {\n                        xhr: false,\n                        fetch: false\n                    },\n                    useDeadTimeLatency: true,\n                    bandwidthSafetyFactor: 0.9,\n                    sampleSettings: {\n                        live: 3,\n                        vod: 4,\n                        enableSampleSizeAdjustment: true,\n                        decreaseScale: 0.7,\n                        increaseScale: 1.3,\n                        maxMeasurementsToKeep: 20,\n                        averageLatencySampleAmount: 4,\n                    },\n                    ewma: {\n                        throughputSlowHalfLifeSeconds: 8,\n                        throughputFastHalfLifeSeconds: 3,\n                        latencySlowHalfLifeCount: 2,\n                        latencyFastHalfLifeCount: 1,\n                        weightDownloadTimeMultiplicationFactor: 0.0015\n                    }\n                },\n                maxBitrate: {\n                    audio: -1,\n                    video: -1\n                },\n                minBitrate: {\n                    audio: -1,\n                    video: -1\n                },\n                initialBitrate: {\n                    audio: -1,\n                    video: -1\n                },\n                autoSwitchBitrate: {\n                    audio: true,\n                    video: true\n                }\n            },\n            cmcd: {\n                applyParametersFromMpd: true,\n                enabled: false,\n                sid: null,\n                cid: null,\n                rtp: null,\n                rtpSafetyFactor: 5,\n                mode: Constants.CMCD_MODE_QUERY,\n                enabledKeys: Constants.CMCD_AVAILABLE_KEYS,\n                includeInRequests: ['segment', 'mpd'],\n                version: 1\n            },\n            cmsd: {\n                enabled: false,\n                abr: {\n                    applyMb: false,\n                    etpWeightRatio: 0\n                }\n            },\n            defaultSchemeIdUri: {\n                viewpoint: '',\n                audioChannelConfiguration: 'urn:mpeg:mpegB:cicp:ChannelConfiguration',\n                role: 'urn:mpeg:dash:role:2011',\n                accessibility: 'urn:mpeg:dash:role:2011'\n            }\n        },\n        errors: {\n            recoverAttempts: {\n                mediaErrorDecode: 5\n            }\n        }\n    };\n\n    let settings = Utils.clone(defaultSettings);\n\n    //Merge in the settings. If something exists in the new config that doesn't match the schema of the default config,\n    //regard it as an error and log it.\n    function mixinSettings(source, dest, path) {\n        for (let n in source) {\n            if (source.hasOwnProperty(n)) {\n                if (dest.hasOwnProperty(n)) {\n                    if (typeof source[n] === 'object' && !(source[n] instanceof RegExp) && !(source[n] instanceof Array) && source[n] !== null) {\n                        mixinSettings(source[n], dest[n], path.slice() + n + '.');\n                    } else {\n                        dest[n] = Utils.clone(source[n]);\n                        if (DISPATCH_KEY_MAP[path + n]) {\n                            eventBus.trigger(DISPATCH_KEY_MAP[path + n]);\n                        }\n                    }\n                } else {\n                    console.error('Settings parameter ' + path + n + ' is not supported');\n                }\n            }\n        }\n    }\n\n    /**\n     * Return the settings object. Don't copy/store this object, you won't get updates.\n     * @func\n     * @instance\n     */\n    function get() {\n        return settings;\n    }\n\n    /**\n     * @func\n     * @instance\n     * @param {object} settingsObj - This should be a partial object of the Settings.Schema type. That is, fields defined should match the path (e.g.\n     * settingsObj.streaming.abr.autoSwitchBitrate.audio -> defaultSettings.streaming.abr.autoSwitchBitrate.audio). Where an element's path does\n     * not match it is ignored, and a warning is logged.\n     *\n     * Use to change the settings object. Any new values defined will overwrite the settings and anything undefined will not change.\n     * Implementers of new settings should add it in an approriate namespace to the defaultSettings object and give it a default value (that is not undefined).\n     *\n     */\n    function update(settingsObj) {\n        if (typeof settingsObj === 'object') {\n            mixinSettings(settingsObj, settings, '');\n        }\n    }\n\n    /**\n     * Resets the settings object. Everything is set to its default value.\n     * @func\n     * @instance\n     *\n     */\n    function reset() {\n        settings = Utils.clone(defaultSettings);\n    }\n\n    instance = {\n        get,\n        update,\n        reset\n    };\n\n    return instance;\n}\n\n\nSettings.__dashjs_factory_name = 'Settings';\nlet factory = FactoryMaker.getSingletonFactory(Settings);\nexport default factory;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * @class\n * @ignore\n */\n\nimport path from 'path-browserify'\nimport {UAParser} from 'ua-parser-js'\nimport Constants from '../streaming/constants/Constants.js';\n\nclass Utils {\n    static mixin(dest, source, copy) {\n        let s;\n        let empty = {};\n        if (dest) {\n            for (let name in source) {\n                if (source.hasOwnProperty(name)) {\n                    s = source[name];\n                    if (!(name in dest) || (dest[name] !== s && (!(name in empty) || empty[name] !== s))) {\n                        if (typeof dest[name] === 'object' && dest[name] !== null) {\n                            dest[name] = Utils.mixin(dest[name], s, copy);\n                        } else {\n                            dest[name] = copy(s);\n                        }\n                    }\n                }\n            }\n        }\n        return dest;\n    }\n\n    static clone(src) {\n        if (!src || typeof src !== 'object') {\n            return src; // anything\n        }\n        if (src instanceof RegExp) {\n            return new RegExp(src);\n        }\n        let r;\n        if (src instanceof Array) {\n            // array\n            r = [];\n            for (let i = 0, l = src.length; i < l; ++i) {\n                if (i in src) {\n                    r.push(Utils.clone(src[i]));\n                }\n            }\n        } else {\n            r = {};\n        }\n        return Utils.mixin(r, src, Utils.clone);\n    }\n\n    static addAdditionalQueryParameterToUrl(url, params) {\n        try {\n            if (!params || params.length === 0) {\n                return url;\n            }\n\n            let updatedUrl = url;\n            params.forEach(({ key, value }) => {\n                const separator = updatedUrl.includes('?') ? '&' : '?';\n                updatedUrl += `${separator}${(encodeURIComponent(key))}=${(encodeURIComponent(value))}`;\n            });\n            return updatedUrl;\n        } catch (e) {\n            return url;\n        }\n    }\n\n    static removeQueryParameterFromUrl(url, queryParameter) {\n        if (!url || !queryParameter) {\n            return url;\n        }\n        // Parse the URL\n        const parsedUrl = new URL(url);\n\n        // Get the search parameters\n        const params = new URLSearchParams(parsedUrl.search);\n\n        if (!params || params.size === 0 || !params.has(queryParameter)) {\n            return url;\n        }\n\n        // Remove the queryParameter\n        params.delete(queryParameter);\n\n        // Manually reconstruct the query string without re-encoding\n        const queryString = Array.from(params.entries())\n            .map(([key, value]) => `${key}=${value}`)\n            .join('&');\n\n        // Reconstruct the URL\n        const baseUrl = `${parsedUrl.origin}${parsedUrl.pathname}`;\n        return queryString ? `${baseUrl}?${queryString}` : baseUrl;\n    }\n\n    static parseHttpHeaders(headerStr) {\n        let headers = {};\n        if (!headerStr) {\n            return headers;\n        }\n\n        // Trim headerStr to fix a MS Edge bug with xhr.getAllResponseHeaders method\n        // which send a string starting with a \"\\n\" character\n        let headerPairs = headerStr.trim().split('\\u000d\\u000a');\n        for (let i = 0, ilen = headerPairs.length; i < ilen; i++) {\n            let headerPair = headerPairs[i];\n            let index = headerPair.indexOf('\\u003a\\u0020');\n            if (index > 0) {\n                headers[headerPair.substring(0, index)] = headerPair.substring(index + 2);\n            }\n        }\n        return headers;\n    }\n\n    /**\n     * Parses query parameters from a string and returns them as an array of key-value pairs.\n     * @param {string} queryParamString - A string containing the query parameters.\n     * @return {Array<{key: string, value: string}>} An array of objects representing the query parameters.\n     */\n    static parseQueryParams(queryParamString) {\n        const params = [];\n        const searchParams = new URLSearchParams(queryParamString);\n        for (const [key, value] of searchParams.entries()) {\n            params.push({ key: decodeURIComponent(key), value: decodeURIComponent(value) });\n        }\n        return params;\n    }\n\n    static generateUuid() {\n        let dt = new Date().getTime();\n        const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n            const r = (dt + Math.random() * 16) % 16 | 0;\n            dt = Math.floor(dt / 16);\n            return (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16);\n        });\n        return uuid;\n    }\n\n    static generateHashCode(string) {\n        let hash = 0;\n\n        if (string.length === 0) {\n            return hash;\n        }\n\n        for (let i = 0; i < string.length; i++) {\n            const chr = string.charCodeAt(i);\n            hash = ((hash << 5) - hash) + chr;\n            hash |= 0;\n        }\n        return hash;\n    }\n\n    /**\n     * Compares both urls and returns a relative url (target relative to original)\n     * @param {string} originalUrl\n     * @param {string} targetUrl\n     * @return {string|*}\n     */\n    static getRelativeUrl(originalUrl, targetUrl) {\n        try {\n            const original = new URL(originalUrl);\n            const target = new URL(targetUrl);\n\n            // Unify the protocol to compare the origins\n            original.protocol = target.protocol;\n            if (original.origin !== target.origin) {\n                return targetUrl;\n            }\n\n            // Use the relative path implementation of the path library. We need to cut off the actual filename in the end to get the relative path\n            let relativePath = path.relative(original.pathname.substr(0, original.pathname.lastIndexOf('/')), target.pathname.substr(0, target.pathname.lastIndexOf('/')));\n\n            // In case the relative path is empty (both path are equal) return the filename only. Otherwise add a slash in front of the filename\n            const startIndexOffset = relativePath.length === 0 ? 1 : 0;\n            relativePath += target.pathname.substr(target.pathname.lastIndexOf('/') + startIndexOffset, target.pathname.length - 1);\n\n            // Build the other candidate, e.g. the 'host relative' path that starts with \"/\", and return the shortest of the two candidates.\n            if (target.pathname.length < relativePath.length) {\n                return target.pathname;\n            }\n            return relativePath;\n        } catch (e) {\n            return targetUrl\n        }\n    }\n\n    static getHostFromUrl(urlString) {\n        try {\n            const url = new URL(urlString);\n\n            return url.host\n        } catch (e) {\n            return null\n        }\n    }\n\n    static parseUserAgent(ua = null) {\n        try {\n            const uaString = ua === null ? typeof navigator !== 'undefined' ? navigator.userAgent.toLowerCase() : '' : '';\n\n            return UAParser(uaString);\n        } catch (e) {\n            return {};\n        }\n    }\n\n    /**\n     * Checks for existence of \"http\" or \"https\" in a string\n     * @param string\n     * @returns {boolean}\n     */\n    static stringHasProtocol(string) {\n        return (/(http(s?)):\\/\\//i.test(string))\n    }\n\n    static bufferSourceToDataView(bufferSource) {\n        return Utils.toDataView(bufferSource, DataView);\n    }\n\n    static bufferSourceToInt8(bufferSource) {\n        return Utils.toDataView(bufferSource, Uint8Array)\n    }\n\n    static uint8ArrayToString(uint8Array) {\n        const decoder = new TextDecoder('utf-8');\n        return decoder.decode(uint8Array);\n    }\n\n    static bufferSourceToHex(data) {\n        const arr = Utils.bufferSourceToInt8(data)\n        let hex = '';\n        for (let value of arr) {\n            value = value.toString(16);\n            if (value.length === 1) {\n                value = '0' + value;\n            }\n            hex += value;\n        }\n        return hex;\n    }\n\n    static toDataView(bufferSource, Type) {\n        const buffer = Utils.getArrayBuffer(bufferSource);\n        let bytesPerElement = 1;\n        if ('BYTES_PER_ELEMENT' in DataView) {\n            bytesPerElement = DataView.BYTES_PER_ELEMENT;\n        }\n\n        const dataEnd = ((bufferSource.byteOffset || 0) + bufferSource.byteLength) /\n            bytesPerElement;\n        const rawStart = ((bufferSource.byteOffset || 0)) / bytesPerElement;\n        const start = Math.floor(Math.max(0, Math.min(rawStart, dataEnd)));\n        const end = Math.floor(Math.min(start + Math.max(Infinity, 0), dataEnd));\n        return new Type(buffer, start, end - start);\n    }\n\n    static getArrayBuffer(view) {\n        if (view instanceof ArrayBuffer) {\n            return view;\n        } else {\n            return view.buffer;\n        }\n    }\n\n    static getCodecFamily(codecString) {\n        const { base, profile } = Utils._getCodecParts(codecString)\n\n        switch (base) {\n            case 'mp4a':\n                switch (profile) {\n                    case '69':\n                    case '6b':\n                    case '40.34':\n                        return Constants.CODEC_FAMILIES.MP3\n                    case '66':\n                    case '67':\n                    case '68':\n                    case '40.2':\n                    case '40.02':\n                    case '40.5':\n                    case '40.05':\n                    case '40.29':\n                    case '40.42':\n                        return Constants.CODEC_FAMILIES.AAC\n                    case 'a5':\n                        return Constants.CODEC_FAMILIES.AC3\n                    case 'e6':\n                        return Constants.CODEC_FAMILIES.EC3\n                    case 'b2':\n                        return Constants.CODEC_FAMILIES.DTSX\n                    case 'a9':\n                        return Constants.CODEC_FAMILIES.DTSC\n                }\n                break;\n            case 'avc1':\n            case 'avc3':\n                return Constants.CODEC_FAMILIES.AVC\n            case 'hvc1':\n            case 'hvc3':\n                return Constants.CODEC_FAMILIES.HEVC\n            default:\n                return base\n        }\n\n        return base;\n    }\n\n    static _getCodecParts(codecString) {\n        const [base, ...rest] = codecString.split('.');\n        const profile = rest.join('.');\n        return { base, profile };\n    }\n\n}\n\nexport default Utils;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport EventsBase from './EventsBase.js';\n\n/**\n * These are internal events that should not be needed at the player level.\n * If you find and event in here that you would like access to from MediaPlayer level\n * please add an issue at https://github.com/Dash-Industry-Forum/dash.js/issues/new\n * @class\n * @ignore\n */\nclass CoreEvents extends EventsBase {\n    constructor () {\n        super();\n        this.ATTEMPT_BACKGROUND_SYNC = 'attemptBackgroundSync';\n        this.BUFFERING_COMPLETED = 'bufferingCompleted';\n        this.BUFFER_CLEARED = 'bufferCleared';\n        this.BYTES_APPENDED_END_FRAGMENT = 'bytesAppendedEndFragment';\n        this.BUFFER_REPLACEMENT_STARTED = 'bufferReplacementStarted';\n        this.CHECK_FOR_EXISTENCE_COMPLETED = 'checkForExistenceCompleted';\n        this.CMSD_STATIC_HEADER = 'cmsdStaticHeader';\n        this.CURRENT_TRACK_CHANGED = 'currentTrackChanged';\n        this.DATA_UPDATE_COMPLETED = 'dataUpdateCompleted';\n        this.INBAND_EVENTS = 'inbandEvents';\n        this.INITIAL_STREAM_SWITCH = 'initialStreamSwitch';\n        this.INIT_FRAGMENT_LOADED = 'initFragmentLoaded';\n        this.INIT_FRAGMENT_NEEDED = 'initFragmentNeeded';\n        this.INTERNAL_MANIFEST_LOADED = 'internalManifestLoaded';\n        this.ORIGINAL_MANIFEST_LOADED = 'originalManifestLoaded';\n        this.LOADING_COMPLETED = 'loadingCompleted';\n        this.LOADING_PROGRESS = 'loadingProgress';\n        this.LOADING_DATA_PROGRESS = 'loadingDataProgress';\n        this.LOADING_ABANDONED = 'loadingAborted';\n        this.MANIFEST_UPDATED = 'manifestUpdated';\n        this.MEDIA_FRAGMENT_LOADED = 'mediaFragmentLoaded';\n        this.MEDIA_FRAGMENT_NEEDED = 'mediaFragmentNeeded';\n        this.MEDIAINFO_UPDATED = 'mediaInfoUpdated';\n        this.QUOTA_EXCEEDED = 'quotaExceeded';\n        this.SEGMENT_LOCATION_BLACKLIST_ADD = 'segmentLocationBlacklistAdd';\n        this.SEGMENT_LOCATION_BLACKLIST_CHANGED = 'segmentLocationBlacklistChanged';\n        this.SERVICE_LOCATION_BASE_URL_BLACKLIST_ADD = 'serviceLocationBlacklistAdd';\n        this.SERVICE_LOCATION_BASE_URL_BLACKLIST_CHANGED = 'serviceLocationBlacklistChanged';\n        this.SERVICE_LOCATION_LOCATION_BLACKLIST_ADD = 'serviceLocationLocationBlacklistAdd';\n        this.SERVICE_LOCATION_LOCATION_BLACKLIST_CHANGED = 'serviceLocationLocationBlacklistChanged';\n        this.SET_FRAGMENTED_TEXT_AFTER_DISABLED = 'setFragmentedTextAfterDisabled';\n        this.SET_NON_FRAGMENTED_TEXT = 'setNonFragmentedText';\n        this.SOURCE_BUFFER_ERROR = 'sourceBufferError';\n        this.STREAMS_COMPOSED = 'streamsComposed';\n        this.STREAM_BUFFERING_COMPLETED = 'streamBufferingCompleted';\n        this.STREAM_REQUESTING_COMPLETED = 'streamRequestingCompleted';\n        this.TEXT_TRACKS_QUEUE_INITIALIZED = 'textTracksQueueInitialized';\n        this.TIME_SYNCHRONIZATION_COMPLETED = 'timeSynchronizationComplete';\n        this.UPDATE_TIME_SYNC_OFFSET = 'updateTimeSyncOffset';\n        this.URL_RESOLUTION_FAILED = 'urlResolutionFailed';\n        this.VIDEO_CHUNK_RECEIVED = 'videoChunkReceived';\n        this.WALLCLOCK_TIME_UPDATED = 'wallclockTimeUpdated';\n        this.XLINK_ELEMENT_LOADED = 'xlinkElementLoaded';\n        this.XLINK_READY = 'xlinkReady';\n        this.SEEK_TARGET = 'seekTarget';\n        this.SETTING_UPDATED_LIVE_DELAY = 'settingUpdatedLiveDelay';\n        this.SETTING_UPDATED_LIVE_DELAY_FRAGMENT_COUNT = 'settingUpdatedLiveDelayFragmentCount';\n        this.SETTING_UPDATED_CATCHUP_ENABLED = 'settingUpdatedCatchupEnabled';\n        this.SETTING_UPDATED_PLAYBACK_RATE_MIN = 'settingUpdatedPlaybackRateMin';\n        this.SETTING_UPDATED_PLAYBACK_RATE_MAX = 'settingUpdatedPlaybackRateMax';\n        this.SETTING_UPDATED_ABR_ACTIVE_RULES = 'settingUpdatedAbrActiveRules';\n        this.SETTING_UPDATED_MAX_BITRATE = 'settingUpdatedMaxBitrate';\n        this.SETTING_UPDATED_MIN_BITRATE = 'settingUpdatedMinBitrate';\n    }\n}\n\nexport default CoreEvents;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nimport CoreEvents from './CoreEvents.js';\n\nclass Events extends CoreEvents {\n}\n\nlet events = new Events();\nexport default events;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass EventsBase {\n    extend(events, config) {\n        if (!events) {\n            return;\n        }\n\n        let override = config ? config.override : false;\n        let publicOnly = config ? config.publicOnly : false;\n\n\n        for (const evt in events) {\n            if (!events.hasOwnProperty(evt) || (this[evt] && !override)) {\n                continue;\n            }\n            if (publicOnly && events[evt].indexOf('public_') === -1) {\n                continue;\n            }\n            this[evt] = events[evt];\n\n        }\n    }\n}\n\nexport default EventsBase;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass UTCTiming {\n    constructor() {\n        // UTCTiming is a DescriptorType and doesn't have any additional fields\n        this.schemeIdUri = '';\n        this.value = '';\n    }\n}\n\nexport default UTCTiming;", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport EventsBase from '../core/events/EventsBase.js';\n\n/**\n * @class\n * @implements EventsBase\n */\nclass MediaPlayerEvents extends EventsBase {\n\n    /**\n     * @description Public facing external events to be used when developing a player that implements dash.js.\n     */\n    constructor() {\n        super();\n        /**\n         * Triggered when playback will not start yet\n         * as the MPD's availabilityStartTime is in the future.\n         * Check delay property in payload to determine time before playback will start.\n         * @event MediaPlayerEvents#AST_IN_FUTURE\n         */\n        this.AST_IN_FUTURE = 'astInFuture';\n\n        /**\n         * Triggered when the BaseURLs have been updated.\n         * @event MediaPlayerEvents#BASE_URLS_UPDATED\n         */\n        this.BASE_URLS_UPDATED = 'baseUrlsUpdated';\n\n        /**\n         * Triggered when the video element's buffer state changes to stalled.\n         * Check mediaType in payload to determine type (Video, Audio, FragmentedText).\n         * @event MediaPlayerEvents#BUFFER_EMPTY\n         */\n        this.BUFFER_EMPTY = 'bufferStalled';\n\n        /**\n         * Triggered when the video element's buffer state changes to loaded.\n         * Check mediaType in payload to determine type (Video, Audio, FragmentedText).\n         * @event MediaPlayerEvents#BUFFER_LOADED\n         */\n        this.BUFFER_LOADED = 'bufferLoaded';\n\n        /**\n         * Triggered when the video element's buffer state changes, either stalled or loaded. Check payload for state.\n         * @event MediaPlayerEvents#BUFFER_LEVEL_STATE_CHANGED\n         */\n        this.BUFFER_LEVEL_STATE_CHANGED = 'bufferStateChanged';\n\n        /**\n         * Triggered when the buffer level of a media type has been updated\n         * @event MediaPlayerEvents#BUFFER_LEVEL_UPDATED\n         */\n        this.BUFFER_LEVEL_UPDATED = 'bufferLevelUpdated';\n\n        /**\n         * Triggered when a font signalled by a DVB Font Download has been added to the document FontFaceSet interface.\n         * @event MediaPlayerEvents#DVB_FONT_DOWNLOAD_ADDED\n         */\n        this.DVB_FONT_DOWNLOAD_ADDED = 'dvbFontDownloadAdded';\n\n        /**\n         * Triggered when a font signalled by a DVB Font Download has successfully downloaded and the FontFace can be used.\n         * @event MediaPlayerEvents#DVB_FONT_DOWNLOAD_COMPLETE\n         */\n        this.DVB_FONT_DOWNLOAD_COMPLETE = 'dvbFontDownloadComplete';\n\n        /**\n         * Triggered when a font signalled by a DVB Font Download could not be successfully downloaded, so the FontFace will not be used.\n         * @event MediaPlayerEvents#DVB_FONT_DOWNLOAD_FAILED\n         */\n        this.DVB_FONT_DOWNLOAD_FAILED = 'dvbFontDownloadFailed';\n\n        /**\n         * Triggered when a dynamic stream changed to static (transition phase between Live and On-Demand).\n         * @event MediaPlayerEvents#DYNAMIC_TO_STATIC\n         */\n        this.DYNAMIC_TO_STATIC = 'dynamicToStatic';\n\n        /**\n         * Triggered when there is an error from the element or MSE source buffer.\n         * @event MediaPlayerEvents#ERROR\n         */\n        this.ERROR = 'error';\n        /**\n         * Triggered when a fragment download has completed.\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_COMPLETED\n         */\n        this.FRAGMENT_LOADING_COMPLETED = 'fragmentLoadingCompleted';\n\n        /**\n         * Triggered when a partial fragment download has completed.\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_PROGRESS\n         */\n        this.FRAGMENT_LOADING_PROGRESS = 'fragmentLoadingProgress';\n        /**\n         * Triggered when a fragment download has started.\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_STARTED\n         */\n        this.FRAGMENT_LOADING_STARTED = 'fragmentLoadingStarted';\n\n        /**\n         * Triggered when a fragment download is abandoned due to detection of slow download base on the ABR abandon rule..\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_ABANDONED\n         */\n        this.FRAGMENT_LOADING_ABANDONED = 'fragmentLoadingAbandoned';\n\n        /**\n         * Triggered when {@link module:Debug} logger methods are called.\n         * @event MediaPlayerEvents#LOG\n         */\n        this.LOG = 'log';\n\n        /**\n         * Triggered when the manifest load is started\n         * @event MediaPlayerEvents#MANIFEST_LOADING_STARTED\n         */\n        this.MANIFEST_LOADING_STARTED = 'manifestLoadingStarted';\n\n        /**\n         * Triggered when the manifest loading is finished, providing the request object information\n         * @event MediaPlayerEvents#MANIFEST_LOADING_FINISHED\n         */\n        this.MANIFEST_LOADING_FINISHED = 'manifestLoadingFinished';\n\n        /**\n         * Triggered when the manifest load is complete, providing the payload\n         * @event MediaPlayerEvents#MANIFEST_LOADED\n         */\n        this.MANIFEST_LOADED = 'manifestLoaded';\n\n        /**\n         * Triggered anytime there is a change to the overall metrics.\n         * @event MediaPlayerEvents#METRICS_CHANGED\n         */\n        this.METRICS_CHANGED = 'metricsChanged';\n\n        /**\n         * Triggered when an individual metric is added, updated or cleared.\n         * @event MediaPlayerEvents#METRIC_CHANGED\n         */\n        this.METRIC_CHANGED = 'metricChanged';\n\n        /**\n         * Triggered every time a new metric is added.\n         * @event MediaPlayerEvents#METRIC_ADDED\n         */\n        this.METRIC_ADDED = 'metricAdded';\n\n        /**\n         * Triggered every time a metric is updated.\n         * @event MediaPlayerEvents#METRIC_UPDATED\n         */\n        this.METRIC_UPDATED = 'metricUpdated';\n\n        /**\n         * Triggered when a new stream (period) starts.\n         * @event MediaPlayerEvents#PERIOD_SWITCH_STARTED\n         */\n        this.PERIOD_SWITCH_STARTED = 'periodSwitchStarted';\n\n        /**\n         * Triggered at the stream end of a period.\n         * @event MediaPlayerEvents#PERIOD_SWITCH_COMPLETED\n         */\n        this.PERIOD_SWITCH_COMPLETED = 'periodSwitchCompleted';\n\n        /**\n         * Triggered when an ABR up /down switch is initiated; either by user in manual mode or auto mode via ABR rules.\n         * @event MediaPlayerEvents#QUALITY_CHANGE_REQUESTED\n         */\n        this.QUALITY_CHANGE_REQUESTED = 'qualityChangeRequested';\n\n        /**\n         * Triggered when the new ABR quality is being rendered on-screen.\n         * @event MediaPlayerEvents#QUALITY_CHANGE_RENDERED\n         */\n        this.QUALITY_CHANGE_RENDERED = 'qualityChangeRendered';\n\n        /**\n         * Triggered when the new track is being selected\n         * @event MediaPlayerEvents#NEW_TRACK_SELECTED\n         */\n        this.NEW_TRACK_SELECTED = 'newTrackSelected';\n\n        /**\n         * Triggered when the new track is being rendered.\n         * @event MediaPlayerEvents#TRACK_CHANGE_RENDERED\n         */\n        this.TRACK_CHANGE_RENDERED = 'trackChangeRendered';\n\n        /**\n         * Triggered when a stream (period) is being loaded\n         * @event MediaPlayerEvents#STREAM_INITIALIZING\n         */\n        this.STREAM_INITIALIZING = 'streamInitializing';\n\n        /**\n         * Triggered when a stream (period) is loaded\n         * @event MediaPlayerEvents#STREAM_UPDATED\n         */\n        this.STREAM_UPDATED = 'streamUpdated';\n\n        /**\n         * Triggered when a stream (period) is activated\n         * @event MediaPlayerEvents#STREAM_ACTIVATED\n         */\n        this.STREAM_ACTIVATED = 'streamActivated';\n\n        /**\n         * Triggered when a stream (period) is deactivated\n         * @event MediaPlayerEvents#STREAM_DEACTIVATED\n         */\n        this.STREAM_DEACTIVATED = 'streamDeactivated';\n\n        /**\n         * Triggered when a stream (period) is activated\n         * @event MediaPlayerEvents#STREAM_INITIALIZED\n         */\n        this.STREAM_INITIALIZED = 'streamInitialized';\n\n        /**\n         * Triggered when the player has been reset.\n         * @event MediaPlayerEvents#STREAM_TEARDOWN_COMPLETE\n         */\n        this.STREAM_TEARDOWN_COMPLETE = 'streamTeardownComplete';\n\n        /**\n         * Triggered once all text tracks detected in the MPD are added to the video element.\n         * @event MediaPlayerEvents#TEXT_TRACKS_ADDED\n         */\n        this.TEXT_TRACKS_ADDED = 'allTextTracksAdded';\n\n        /**\n         * Triggered when a text track is added to the video element's TextTrackList\n         * @event MediaPlayerEvents#TEXT_TRACK_ADDED\n         */\n        this.TEXT_TRACK_ADDED = 'textTrackAdded';\n\n        /**\n         * Triggered when a text track should be shown\n         * @event MediaPlayerEvents#CUE_ENTER\n         */\n        this.CUE_ENTER = 'cueEnter'\n\n        /**\n         * Triggered when a text track should be hidden\n         * @event MediaPlayerEvents#CUE_ENTER\n         */\n        this.CUE_EXIT = 'cueExit'\n\n        /**\n         * Triggered when a throughput measurement based on the last segment request has been stored\n         * @event MediaPlayerEvents#THROUGHPUT_MEASUREMENT_STORED\n         */\n        this.THROUGHPUT_MEASUREMENT_STORED = 'throughputMeasurementStored';\n\n        /**\n         * Triggered when a ttml chunk is parsed.\n         * @event MediaPlayerEvents#TTML_PARSED\n         */\n        this.TTML_PARSED = 'ttmlParsed';\n\n        /**\n         * Triggered when a ttml chunk has to be parsed.\n         * @event MediaPlayerEvents#TTML_TO_PARSE\n         */\n        this.TTML_TO_PARSE = 'ttmlToParse';\n\n        /**\n         * Triggered when a caption is rendered.\n         * @event MediaPlayerEvents#CAPTION_RENDERED\n         */\n        this.CAPTION_RENDERED = 'captionRendered';\n\n        /**\n         * Triggered when the caption container is resized.\n         * @event MediaPlayerEvents#CAPTION_CONTAINER_RESIZE\n         */\n        this.CAPTION_CONTAINER_RESIZE = 'captionContainerResize';\n\n        /**\n         * Sent when enough data is available that the media can be played,\n         * at least for a couple of frames.  This corresponds to the\n         * HAVE_ENOUGH_DATA readyState.\n         * @event MediaPlayerEvents#CAN_PLAY\n         */\n        this.CAN_PLAY = 'canPlay';\n\n        /**\n         * This corresponds to the CAN_PLAY_THROUGH readyState.\n         * @event MediaPlayerEvents#CAN_PLAY_THROUGH\n         */\n        this.CAN_PLAY_THROUGH = 'canPlayThrough';\n\n        /**\n         * Sent when playback completes.\n         * @event MediaPlayerEvents#PLAYBACK_ENDED\n         */\n        this.PLAYBACK_ENDED = 'playbackEnded';\n\n        /**\n         * Sent when an error occurs.  The element's error\n         * attribute contains more information.\n         * @event MediaPlayerEvents#PLAYBACK_ERROR\n         */\n        this.PLAYBACK_ERROR = 'playbackError';\n\n        /**\n         * This event is fired once the playback has been initialized by MediaPlayer.js.\n         * After that event methods such as setTextTrack() can be used.\n         * @event MediaPlayerEvents#PLAYBACK_INITIALIZED\n         */\n        this.PLAYBACK_INITIALIZED = 'playbackInitialized';\n\n        /**\n         * Sent when playback is not allowed (for example if user gesture is needed).\n         * @event MediaPlayerEvents#PLAYBACK_NOT_ALLOWED\n         */\n        this.PLAYBACK_NOT_ALLOWED = 'playbackNotAllowed';\n\n        /**\n         * The media's metadata has finished loading; all attributes now\n         * contain as much useful information as they're going to.\n         * @event MediaPlayerEvents#PLAYBACK_METADATA_LOADED\n         */\n        this.PLAYBACK_METADATA_LOADED = 'playbackMetaDataLoaded';\n\n        /**\n         * The event is fired when the frame at the current playback position of the media has finished loading;\n         * often the first frame\n         * @event MediaPlayerEvents#PLAYBACK_LOADED_DATA\n         */\n        this.PLAYBACK_LOADED_DATA = 'playbackLoadedData';\n\n        /**\n         * Sent when playback is paused.\n         * @event MediaPlayerEvents#PLAYBACK_PAUSED\n         */\n        this.PLAYBACK_PAUSED = 'playbackPaused';\n\n        /**\n         * Sent when the media begins to play (either for the first time, after having been paused,\n         * or after ending and then restarting).\n         *\n         * @event MediaPlayerEvents#PLAYBACK_PLAYING\n         */\n        this.PLAYBACK_PLAYING = 'playbackPlaying';\n\n        /**\n         * Sent periodically to inform interested parties of progress downloading\n         * the media. Information about the current amount of the media that has\n         * been downloaded is available in the media element's buffered attribute.\n         * @event MediaPlayerEvents#PLAYBACK_PROGRESS\n         */\n        this.PLAYBACK_PROGRESS = 'playbackProgress';\n\n        /**\n         * Sent when the playback speed changes.\n         * @event MediaPlayerEvents#PLAYBACK_RATE_CHANGED\n         */\n        this.PLAYBACK_RATE_CHANGED = 'playbackRateChanged';\n\n        /**\n         * Sent when a seek operation completes.\n         * @event MediaPlayerEvents#PLAYBACK_SEEKED\n         */\n        this.PLAYBACK_SEEKED = 'playbackSeeked';\n\n        /**\n         * Sent when a seek operation begins.\n         * @event MediaPlayerEvents#PLAYBACK_SEEKING\n         */\n        this.PLAYBACK_SEEKING = 'playbackSeeking';\n\n        /**\n         * Sent when the video element reports stalled\n         * @event MediaPlayerEvents#PLAYBACK_STALLED\n         */\n        this.PLAYBACK_STALLED = 'playbackStalled';\n\n        /**\n         * Sent when playback of the media starts after having been paused;\n         * that is, when playback is resumed after a prior pause event.\n         *\n         * @event MediaPlayerEvents#PLAYBACK_STARTED\n         */\n        this.PLAYBACK_STARTED = 'playbackStarted';\n\n        /**\n         * The time indicated by the element's currentTime attribute has changed.\n         * @event MediaPlayerEvents#PLAYBACK_TIME_UPDATED\n         */\n        this.PLAYBACK_TIME_UPDATED = 'playbackTimeUpdated';\n\n        /**\n         * Sent when the video element reports that the volume has changed\n         * @event MediaPlayerEvents#PLAYBACK_VOLUME_CHANGED\n         */\n        this.PLAYBACK_VOLUME_CHANGED = 'playbackVolumeChanged';\n\n        /**\n         * Sent when the media playback has stopped because of a temporary lack of data.\n         *\n         * @event MediaPlayerEvents#PLAYBACK_WAITING\n         */\n        this.PLAYBACK_WAITING = 'playbackWaiting';\n\n        /**\n         * Manifest validity changed - As a result of an MPD validity expiration event.\n         * @event MediaPlayerEvents#MANIFEST_VALIDITY_CHANGED\n         */\n        this.MANIFEST_VALIDITY_CHANGED = 'manifestValidityChanged';\n\n        /**\n         * Dash events are triggered at their respective start points on the timeline.\n         * @event MediaPlayerEvents#EVENT_MODE_ON_START\n         */\n        this.EVENT_MODE_ON_START = 'eventModeOnStart';\n\n        /**\n         * Dash events are triggered as soon as they were parsed.\n         * @event MediaPlayerEvents#EVENT_MODE_ON_RECEIVE\n         */\n        this.EVENT_MODE_ON_RECEIVE = 'eventModeOnReceive';\n\n        /**\n         * Event that is dispatched whenever the player encounters a potential conformance validation that might lead to unexpected/not optimal behavior\n         * @event MediaPlayerEvents#CONFORMANCE_VIOLATION\n         */\n        this.CONFORMANCE_VIOLATION = 'conformanceViolation';\n\n        /**\n         * Event that is dispatched whenever the player switches to a different representation\n         * @event MediaPlayerEvents#REPRESENTATION_SWITCH\n         */\n        this.REPRESENTATION_SWITCH = 'representationSwitch';\n\n        /**\n         * Event that is dispatched whenever an adaptation set is removed due to all representations not being supported.\n         * @event MediaPlayerEvents#ADAPTATION_SET_REMOVED_NO_CAPABILITIES\n         */\n        this.ADAPTATION_SET_REMOVED_NO_CAPABILITIES = 'adaptationSetRemovedNoCapabilities';\n\n        /**\n         * Triggered when a content steering request has completed.\n         * @event MediaPlayerEvents#CONTENT_STEERING_REQUEST_COMPLETED\n         */\n        this.CONTENT_STEERING_REQUEST_COMPLETED = 'contentSteeringRequestCompleted';\n\n        /**\n         * Triggered when an inband prft (ProducerReferenceTime) boxes has been received.\n         * @event MediaPlayerEvents#INBAND_PRFT\n         */\n        this.INBAND_PRFT = 'inbandPrft';\n\n        /**\n         * The streaming attribute of the Managed Media Source is true\n         * @type {string}\n         */\n        this.MANAGED_MEDIA_SOURCE_START_STREAMING = 'managedMediaSourceStartStreaming';\n\n        /**\n         * The streaming attribute of the Managed Media Source is false\n         * @type {string}\n         */\n        this.MANAGED_MEDIA_SOURCE_END_STREAMING = 'managedMediaSourceEndStreaming';\n    }\n}\n\nlet mediaPlayerEvents = new MediaPlayerEvents();\nexport default mediaPlayerEvents;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * Constants declaration\n */\nexport default {\n    /**\n     *  @constant {string} STREAM Stream media type. Mainly used to report metrics relative to the full stream\n     *  @memberof Constants#\n     *  @static\n     */\n    STREAM: 'stream',\n\n    /**\n     *  @constant {string} VIDEO Video media type\n     *  @memberof Constants#\n     *  @static\n     */\n    VIDEO: 'video',\n\n    /**\n     *  @constant {string} AUDIO Audio media type\n     *  @memberof Constants#\n     *  @static\n     */\n    AUDIO: 'audio',\n\n    /**\n     *  @constant {string} TEXT Text media type\n     *  @memberof Constants#\n     *  @static\n     */\n    TEXT: 'text',\n\n    /**\n     *  @constant {string} MUXED Muxed (video/audio in the same chunk) media type\n     *  @memberof Constants#\n     *  @static\n     */\n    MUXED: 'muxed',\n\n    /**\n     *  @constant {string} IMAGE Image media type\n     *  @memberof Constants#\n     *  @static\n     */\n    IMAGE: 'image',\n\n    /**\n     *  @constant {string} STPP STTP Subtitles format\n     *  @memberof Constants#\n     *  @static\n     */\n    STPP: 'stpp',\n\n    /**\n     *  @constant {string} TTML STTP Subtitles format\n     *  @memberof Constants#\n     *  @static\n     */\n    TTML: 'ttml',\n\n    /**\n     *  @constant {string} VTT STTP Subtitles format\n     *  @memberof Constants#\n     *  @static\n     */\n    VTT: 'vtt',\n\n    /**\n     *  @constant {string} WVTT STTP Subtitles format\n     *  @memberof Constants#\n     *  @static\n     */\n    WVTT: 'wvtt',\n\n    /**\n     *  @constant {string} Content Steering\n     *  @memberof Constants#\n     *  @static\n     */\n    CONTENT_STEERING: 'contentSteering',\n\n    /**\n     *  @constant {string} LIVE_CATCHUP_MODE_DEFAULT Throughput calculation based on moof parsing\n     *  @memberof Constants#\n     *  @static\n     */\n    LIVE_CATCHUP_MODE_DEFAULT: 'liveCatchupModeDefault',\n\n    /**\n     *  @constant {string} LIVE_CATCHUP_MODE_LOLP Throughput calculation based on moof parsing\n     *  @memberof Constants#\n     *  @static\n     */\n    LIVE_CATCHUP_MODE_LOLP: 'liveCatchupModeLoLP',\n\n    /**\n     *  @constant {string} MOVING_AVERAGE_SLIDING_WINDOW Moving average sliding window\n     *  @memberof Constants#\n     *  @static\n     */\n    MOVING_AVERAGE_SLIDING_WINDOW: 'slidingWindow',\n\n    /**\n     *  @constant {string} EWMA Exponential moving average\n     *  @memberof Constants#\n     *  @static\n     */\n    MOVING_AVERAGE_EWMA: 'ewma',\n\n    /**\n     *  @constant {string} BAD_ARGUMENT_ERROR Invalid Arguments type of error\n     *  @memberof Constants#\n     *  @static\n     */\n    BAD_ARGUMENT_ERROR: 'Invalid Arguments',\n\n    /**\n     *  @constant {string} MISSING_CONFIG_ERROR Missing configuration parameters type of error\n     *  @memberof Constants#\n     *  @static\n     */\n    MISSING_CONFIG_ERROR: 'Missing config parameter(s)',\n\n    /**\n     *  @constant {string} TRACK_SWITCH_MODE_ALWAYS_REPLACE used to clear the buffered data (prior to current playback position) after track switch. Default for audio\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SWITCH_MODE_ALWAYS_REPLACE: 'alwaysReplace',\n\n    /**\n     *  @constant {string} TRACK_SWITCH_MODE_NEVER_REPLACE used to forbid clearing the buffered data (prior to current playback position) after track switch. Defers to fastSwitchEnabled for placement of new data. Default for video\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SWITCH_MODE_NEVER_REPLACE: 'neverReplace',\n\n    /**\n     *  @constant {string} TRACK_SELECTION_MODE_FIRST_TRACK makes the player select the first track found in the manifest.\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SELECTION_MODE_FIRST_TRACK: 'firstTrack',\n\n    /**\n     *  @constant {string} TRACK_SELECTION_MODE_HIGHEST_BITRATE makes the player select the track with a highest bitrate. This mode is a default mode.\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SELECTION_MODE_HIGHEST_BITRATE: 'highestBitrate',\n\n    /**\n     *  @constant {string} TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY makes the player select the track with the lowest bitrate per pixel average.\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY: 'highestEfficiency',\n\n    /**\n     *  @constant {string} TRACK_SELECTION_MODE_WIDEST_RANGE makes the player select the track with a widest range of bitrates.\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SELECTION_MODE_WIDEST_RANGE: 'widestRange',\n\n    /**\n     *  @constant {string} CMCD_QUERY_KEY specifies the key that is used for the CMCD query parameter.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_QUERY_KEY: 'CMCD',\n\n    /**\n     *  @constant {string} CMCD_MODE_QUERY specifies to attach CMCD metrics as query parameters.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_MODE_QUERY: 'query',\n\n    /**\n     *  @constant {string} CMCD_MODE_HEADER specifies to attach CMCD metrics as HTTP headers.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_MODE_HEADER: 'header',\n\n    /**\n     *  @constant {string} CMCD_AVAILABLE_KEYS specifies all the available keys for CMCD metrics.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_AVAILABLE_KEYS: ['br', 'd', 'ot', 'tb', 'bl', 'dl', 'mtp', 'nor', 'nrr', 'su', 'bs', 'rtp', 'cid', 'pr', 'sf', 'sid', 'st', 'v'],\n    /**\n     *  @constant {string} CMCD_AVAILABLE_KEYS_V2 specifies all the available keys for CMCD version 2 metrics.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_V2_AVAILABLE_KEYS: ['msd', 'ltc'],\n\n    /**\n     *  @constant {string} CMCD_AVAILABLE_REQUESTS specifies all the available requests type for CMCD metrics.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_AVAILABLE_REQUESTS: ['segment', 'mpd', 'xlink', 'steering', 'other'],\n\n\n    INITIALIZE: 'initialize',\n    TEXT_SHOWING: 'showing',\n    TEXT_HIDDEN: 'hidden',\n    TEXT_DISABLED: 'disabled',\n    ACCESSIBILITY_CEA608_SCHEME: 'urn:scte:dash:cc:cea-608:2015',\n    CC1: 'CC1',\n    CC3: 'CC3',\n    UTF8: 'utf-8',\n    SCHEME_ID_URI: 'schemeIdUri',\n    START_TIME: 'starttime',\n    SERVICE_DESCRIPTION_DVB_LL_SCHEME: 'urn:dvb:dash:lowlatency:scope:2019',\n    SUPPLEMENTAL_PROPERTY_DVB_LL_SCHEME: 'urn:dvb:dash:lowlatency:critical:2019',\n    CTA_5004_2023_SCHEME: 'urn:mpeg:dash:cta-5004:2023',\n    THUMBNAILS_SCHEME_ID_URIS: ['http://dashif.org/thumbnail_tile', 'http://dashif.org/guidelines/thumbnail_tile'],\n    FONT_DOWNLOAD_DVB_SCHEME: 'urn:dvb:dash:fontdownload:2014',\n    COLOUR_PRIMARIES_SCHEME_ID_URI: 'urn:mpeg:mpegB:cicp:ColourPrimaries',\n    URL_QUERY_INFO_SCHEME: 'urn:mpeg:dash:urlparam:2014',\n    EXT_URL_QUERY_INFO_SCHEME: 'urn:mpeg:dash:urlparam:2016',\n    MATRIX_COEFFICIENTS_SCHEME_ID_URI: 'urn:mpeg:mpegB:cicp:MatrixCoefficients',\n    TRANSFER_CHARACTERISTICS_SCHEME_ID_URI: 'urn:mpeg:mpegB:cicp:TransferCharacteristics',\n    HDR_METADATA_FORMAT_SCHEME_ID_URI: 'urn:dvb:dash:hdr-dmi',\n    HDR_METADATA_FORMAT_VALUES: {\n        ST2094_10: 'ST2094-10',\n        SL_HDR2: 'SL-HDR2',\n        ST2094_40: 'ST2094-40'\n    },\n    MEDIA_CAPABILITIES_API: {\n        COLORGAMUT: {\n            SRGB: 'srgb',\n            P3: 'p3',\n            REC2020: 'rec2020'\n        },\n        TRANSFERFUNCTION: {\n            SRGB: 'srgb',\n            PQ: 'pq',\n            HLG: 'hlg'\n        },\n        HDR_METADATATYPE: {\n            SMPTE_ST_2094_10: 'smpteSt2094-10',\n            SLHDR2: 'slhdr2',\n            SMPTE_ST_2094_40: 'smpteSt2094-40'\n        }\n    },\n    XML: 'XML',\n    ARRAY_BUFFER: 'ArrayBuffer',\n    DVB_REPORTING_URL: 'dvb:reportingUrl',\n    DVB_PROBABILITY: 'dvb:probability',\n    OFF_MIMETYPE: 'application/font-sfnt',\n    WOFF_MIMETYPE: 'application/font-woff',\n    VIDEO_ELEMENT_READY_STATES: {\n        HAVE_NOTHING: 0,\n        HAVE_METADATA: 1,\n        HAVE_CURRENT_DATA: 2,\n        HAVE_FUTURE_DATA: 3,\n        HAVE_ENOUGH_DATA: 4\n    },\n    FILE_LOADER_TYPES: {\n        FETCH: 'fetch_loader',\n        XHR: 'xhr_loader'\n    },\n    THROUGHPUT_TYPES: {\n        LATENCY: 'throughput_type_latency',\n        BANDWIDTH: 'throughput_type_bandwidth'\n    },\n    THROUGHPUT_CALCULATION_MODES: {\n        EWMA: 'throughputCalculationModeEwma',\n        ZLEMA: 'throughputCalculationModeZlema',\n        ARITHMETIC_MEAN: 'throughputCalculationModeArithmeticMean',\n        BYTE_SIZE_WEIGHTED_ARITHMETIC_MEAN: 'throughputCalculationModeByteSizeWeightedArithmeticMean',\n        DATE_WEIGHTED_ARITHMETIC_MEAN: 'throughputCalculationModeDateWeightedArithmeticMean',\n        HARMONIC_MEAN: 'throughputCalculationModeHarmonicMean',\n        BYTE_SIZE_WEIGHTED_HARMONIC_MEAN: 'throughputCalculationModeByteSizeWeightedHarmonicMean',\n        DATE_WEIGHTED_HARMONIC_MEAN: 'throughputCalculationModeDateWeightedHarmonicMean',\n    },\n    LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE: {\n        MOOF_PARSING: 'lowLatencyDownloadTimeCalculationModeMoofParsing',\n        DOWNLOADED_DATA: 'lowLatencyDownloadTimeCalculationModeDownloadedData',\n        AAST: 'lowLatencyDownloadTimeCalculationModeAast',\n    },\n    RULES_TYPES: {\n        QUALITY_SWITCH_RULES: 'qualitySwitchRules',\n        ABANDON_FRAGMENT_RULES: 'abandonFragmentRules'\n    },\n    QUALITY_SWITCH_RULES: {\n        BOLA_RULE: 'BolaRule',\n        THROUGHPUT_RULE: 'ThroughputRule',\n        INSUFFICIENT_BUFFER_RULE: 'InsufficientBufferRule',\n        SWITCH_HISTORY_RULE: 'SwitchHistoryRule',\n        DROPPED_FRAMES_RULE: 'DroppedFramesRule',\n        LEARN_TO_ADAPT_RULE: 'L2ARule',\n        LOL_PLUS_RULE: 'LoLPRule'\n    },\n    ABANDON_FRAGMENT_RULES: {\n        ABANDON_REQUEST_RULE: 'AbandonRequestsRule'\n    },\n\n    /**\n     *  @constant {string} ID3_SCHEME_ID_URI specifies scheme ID URI for ID3 timed metadata\n     *  @memberof Constants#\n     *  @static\n     */\n    ID3_SCHEME_ID_URI: 'https://aomedia.org/emsg/ID3',\n    COMMON_ACCESS_TOKEN_HEADER: 'common-access-token',\n    DASH_ROLE_SCHEME_ID : 'urn:mpeg:dash:role:2011',\n    CODEC_FAMILIES: {\n        MP3: 'mp3',\n        AAC: 'aac',\n        AC3: 'ac3',\n        EC3: 'ec3',\n        DTSX: 'dtsx',\n        DTSC: 'dtsc',\n        AVC: 'avc',\n        HEVC: 'hevc'\n    }\n}\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport EventsBase from '../../core/events/EventsBase.js';\n\n/**\n * @class\n * @implements EventsBase\n */\nclass MetricsReportingEvents extends EventsBase {\n    constructor () {\n        super();\n\n        this.METRICS_INITIALISATION_COMPLETE = 'internal_metricsReportingInitialized';\n        this.BECAME_REPORTING_PLAYER = 'internal_becameReportingPlayer';\n\n        /**\n         * Triggered when CMCD data was generated for a HTTP request\n         * @event MetricsReportingEvents#CMCD_DATA_GENERATED\n         */\n        this.CMCD_DATA_GENERATED = 'cmcdDataGenerated';\n    }\n}\n\nlet metricsReportingEvents = new MetricsReportingEvents();\nexport default metricsReportingEvents;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport UTCTiming from '../../dash/vo/UTCTiming.js';\nimport FactoryMaker from '../../core/FactoryMaker.js';\nimport Settings from '../../core/Settings.js';\nimport {checkParameterType} from '../utils/SupervisorTools.js';\nimport Constants from '../constants/Constants.js';\n\nconst DEFAULT_XHR_WITH_CREDENTIALS = false;\n\nfunction CustomParametersModel() {\n\n    let instance,\n        utcTimingSources,\n        xhrWithCredentials,\n        requestInterceptors,\n        responseInterceptors,\n        licenseRequestFilters,\n        licenseResponseFilters,\n        customCapabilitiesFilters,\n        customInitialTrackSelectionFunction,\n        customAbrRules;\n\n    const context = this.context;\n    const settings = Settings(context).getInstance();\n\n    function setup() {\n        xhrWithCredentials = {\n            default: DEFAULT_XHR_WITH_CREDENTIALS\n        };\n        _resetInitialSettings();\n    }\n\n    function _resetInitialSettings() {\n        requestInterceptors = [];\n        responseInterceptors = [];\n        licenseRequestFilters = [];\n        licenseResponseFilters = [];\n        customCapabilitiesFilters = [];\n        customAbrRules = [];\n        customInitialTrackSelectionFunction = null;\n        utcTimingSources = [];\n    }\n\n\n    function reset() {\n        _resetInitialSettings();\n    }\n\n    function setConfig() {\n\n    }\n\n    /**\n     * Registers a custom initial track selection function. Only one function is allowed. Calling this method will overwrite a potentially existing function.\n     * @param {function} customFunc - the custom function that returns the initial track\n     */\n    function setCustomInitialTrackSelectionFunction(customFunc) {\n        customInitialTrackSelectionFunction = customFunc;\n    }\n\n    /**\n     * Resets the custom initial track selection\n     */\n    function resetCustomInitialTrackSelectionFunction() {\n        customInitialTrackSelectionFunction = null;\n    }\n\n    /**\n     * Returns the initial track selection function\n     * @return {function}\n     */\n    function getCustomInitialTrackSelectionFunction() {\n        return customInitialTrackSelectionFunction;\n    }\n\n    /**\n     * Returns all license request filters\n     * @return {array}\n     */\n    function getLicenseRequestFilters() {\n        return licenseRequestFilters;\n    }\n\n    /**\n     * Returns all license response filters\n     * @return {array}\n     */\n    function getLicenseResponseFilters() {\n        return licenseResponseFilters;\n    }\n\n    /**\n     * Registers a license request filter. This enables application to manipulate/overwrite any request parameter and/or request data.\n     * The provided callback function shall return a promise that shall be resolved once the filter process is completed.\n     * The filters are applied in the order they are registered.\n     * @param {function} filter - the license request filter callback\n     */\n    function registerLicenseRequestFilter(filter) {\n        licenseRequestFilters.push(filter);\n    }\n\n    /**\n     * Registers a license response filter. This enables application to manipulate/overwrite the response data\n     * The provided callback function shall return a promise that shall be resolved once the filter process is completed.\n     * The filters are applied in the order they are registered.\n     * @param {function} filter - the license response filter callback\n     */\n    function registerLicenseResponseFilter(filter) {\n        licenseResponseFilters.push(filter);\n    }\n\n    /**\n     * Unregisters a license request filter.\n     * @param {function} filter - the license request filter callback\n     */\n    function unregisterLicenseRequestFilter(filter) {\n        _unregisterFilter(licenseRequestFilters, filter);\n    }\n\n    /**\n     * Unregisters a license response filter.\n     * @param {function} filter - the license response filter callback\n     */\n    function unregisterLicenseResponseFilter(filter) {\n        _unregisterFilter(licenseResponseFilters, filter);\n    }\n\n    /**\n     * Returns all custom capabilities filter\n     * @return {array}\n     */\n    function getCustomCapabilitiesFilters() {\n        return customCapabilitiesFilters;\n    }\n\n    /**\n     * Registers a custom capabilities filter. This enables application to filter representations to use.\n     * The provided callback function shall return a boolean or promise resolving to a boolean based on whether or not to use the representation.\n     * The filters are applied in the order they are registered.\n     * @param {function} filter - the custom capabilities filter callback\n     */\n    function registerCustomCapabilitiesFilter(filter) {\n        customCapabilitiesFilters.push(filter);\n    }\n\n    /**\n     * Unregisters a custom capabilities filter.\n     * @param {function} filter - the custom capabilities filter callback\n     */\n    function unregisterCustomCapabilitiesFilter(filter) {\n        _unregisterFilter(customCapabilitiesFilters, filter);\n    }\n\n    /**\n     * Unregister a filter from the list of existing filers.\n     * @param {array} filters\n     * @param {function} filter\n     * @private\n     */\n    function _unregisterFilter(filters, filter) {\n        let index = -1;\n        filters.some((item, i) => {\n            if (item === filter) {\n                index = i;\n                return true;\n            }\n        });\n        if (index < 0) {\n            return;\n        }\n        filters.splice(index, 1);\n    }\n\n    /**\n     * Iterate through the list of custom ABR rules and find the right rule by name\n     * @param {string} rulename\n     * @return {number} rule number\n     */\n    function _findAbrCustomRuleIndex(rulename) {\n        let i;\n        for (i = 0; i < customAbrRules.length; i++) {\n            if (customAbrRules[i].rulename === rulename) {\n                return i;\n            }\n        }\n        return -1;\n    }\n\n    /**\n     * Add a custom ABR Rule\n     * Rule will be apply on next stream if a stream is being played\n     *\n     * @param {string} type - rule type (one of ['qualitySwitchRules','abandonFragmentRules'])\n     * @param {string} rulename - name of rule (used to identify custom rule). If one rule of same name has been added, then existing rule will be updated\n     * @param {object} rule - the rule object instance\n     * @throws {@link Constants#BAD_ARGUMENT_ERROR BAD_ARGUMENT_ERROR} if called with invalid arguments.\n     */\n    function addAbrCustomRule(type, rulename, rule) {\n        if (typeof type !== 'string' || (type !== Constants.RULES_TYPES.ABANDON_FRAGMENT_RULES && type !== Constants.RULES_TYPES.QUALITY_SWITCH_RULES) ||\n            typeof rulename !== 'string') {\n            throw Constants.BAD_ARGUMENT_ERROR;\n        }\n        let index = _findAbrCustomRuleIndex(rulename);\n        if (index === -1) {\n            // add rule\n            customAbrRules.push({\n                type: type,\n                rulename: rulename,\n                rule: rule\n            });\n        } else {\n            // update rule\n            customAbrRules[index].type = type;\n            customAbrRules[index].rule = rule;\n        }\n    }\n\n    /**\n     * Remove a custom ABR Rule\n     *\n     * @param {string} rulename - name of the rule to be removed\n     */\n    function removeAbrCustomRule(rulename) {\n        if (rulename) {\n            let index = _findAbrCustomRuleIndex(rulename);\n            //if no rulename custom rule has been found, do nothing\n            if (index !== -1) {\n                // remove rule\n                customAbrRules.splice(index, 1);\n            }\n        } else {\n            //if no rulename is defined, remove all ABR custome rules\n            customAbrRules = [];\n        }\n    }\n\n    /**\n     * Remove all custom rules\n     */\n    function removeAllAbrCustomRule() {\n        customAbrRules = [];\n    }\n\n    /**\n     * Return all ABR custom rules\n     * @return {array}\n     */\n    function getAbrCustomRules() {\n        return customAbrRules;\n    }\n\n    /**\n     * Adds a request interceptor. This enables application to monitor, manipulate, overwrite any request parameter and/or request data.\n     * The provided callback function shall return a promise with updated request that shall be resolved once the process of the request is completed.\n     * The interceptors are applied in the order they are added.\n     * @param {function} interceptor - the request interceptor callback\n     */\n    function addRequestInterceptor(interceptor) {\n        requestInterceptors.push(interceptor);\n    }\n\n    /**\n     * Adds a response interceptor. This enables application to monitor, manipulate, overwrite the response data\n     * The provided callback function shall return a promise with updated response that shall be resolved once the process of the response is completed.\n     * The interceptors are applied in the order they are added.\n     * @param {function} interceptor - the response interceptor callback\n     */\n    function addResponseInterceptor(interceptor) {\n        responseInterceptors.push(interceptor);\n    }\n\n    /**\n     * Unregisters a request interceptor.\n     * @param {function} interceptor - the request interceptor callback\n     */\n    function removeRequestInterceptor(interceptor) {\n        _unregisterFilter(requestInterceptors, interceptor);\n    }\n\n    /**\n     * Unregisters a response interceptor.\n     * @param {function} interceptor - the request interceptor callback\n     */\n    function removeResponseInterceptor(interceptor) {\n        _unregisterFilter(responseInterceptors, interceptor);\n    }\n\n    /**\n     * Returns all request interceptors\n     * @return {array}\n     */\n    function getRequestInterceptors() {\n        return requestInterceptors;\n    }\n\n    /**\n     * Returns all response interceptors\n     * @return {array}\n     */\n    function getResponseInterceptors() {\n        return responseInterceptors;\n    }\n\n    /**\n     * Add a UTC timing source at the top of the list\n     * @param {string} schemeIdUri\n     * @param {string} value\n     */\n    function addUTCTimingSource(schemeIdUri, value) {\n        removeUTCTimingSource(schemeIdUri, value); //check if it already exists and remove if so.\n        let vo = new UTCTiming();\n        vo.schemeIdUri = schemeIdUri;\n        vo.value = value;\n        utcTimingSources.push(vo);\n    }\n\n    /**\n     * Return all UTC timing sources\n     * @return {array}\n     */\n    function getUTCTimingSources() {\n        return utcTimingSources;\n    }\n\n    /**\n     * Remove a specific timing source from the array\n     * @param {string} schemeIdUri\n     * @param {string} value\n     */\n    function removeUTCTimingSource(schemeIdUri, value) {\n        checkParameterType(schemeIdUri, 'string');\n        checkParameterType(value, 'string');\n        utcTimingSources.forEach(function (obj, idx) {\n            if (obj.schemeIdUri === schemeIdUri && obj.value === value) {\n                utcTimingSources.splice(idx, 1);\n            }\n        });\n    }\n\n    /**\n     * Remove all timing sources\n     */\n    function clearDefaultUTCTimingSources() {\n        utcTimingSources = [];\n    }\n\n    /**\n     * Add the default timing source to the list\n     */\n    function restoreDefaultUTCTimingSources() {\n        let defaultUtcTimingSource = settings.get().streaming.utcSynchronization.defaultTimingSource;\n        addUTCTimingSource(defaultUtcTimingSource.scheme, defaultUtcTimingSource.value);\n    }\n\n    function setXHRWithCredentialsForType(type, value) {\n        if (!type) {\n            Object.keys(xhrWithCredentials).forEach(key => {\n                setXHRWithCredentialsForType(key, value);\n            });\n        } else {\n            xhrWithCredentials[type] = !!value;\n        }\n    }\n\n    function getXHRWithCredentialsForType(type) {\n        const useCreds = xhrWithCredentials[type];\n\n        return useCreds === undefined ? xhrWithCredentials.default : useCreds;\n    }\n\n    instance = {\n        addAbrCustomRule,\n        addRequestInterceptor,\n        addResponseInterceptor,\n        addUTCTimingSource,\n        clearDefaultUTCTimingSources,\n        getAbrCustomRules,\n        getCustomCapabilitiesFilters,\n        getCustomInitialTrackSelectionFunction,\n        getLicenseRequestFilters,\n        getLicenseResponseFilters,\n        getRequestInterceptors,\n        getResponseInterceptors,\n        getUTCTimingSources,\n        getXHRWithCredentialsForType,\n        registerCustomCapabilitiesFilter,\n        registerLicenseRequestFilter,\n        registerLicenseResponseFilter,\n        removeAbrCustomRule,\n        removeAllAbrCustomRule,\n        removeRequestInterceptor,\n        removeResponseInterceptor,\n        removeUTCTimingSource,\n        reset,\n        resetCustomInitialTrackSelectionFunction,\n        restoreDefaultUTCTimingSources,\n        setConfig,\n        setCustomInitialTrackSelectionFunction,\n        setXHRWithCredentialsForType,\n        unregisterCustomCapabilitiesFilter,\n        unregisterLicenseRequestFilter,\n        unregisterLicenseResponseFilter,\n    };\n\n    setup();\n\n    return instance;\n}\n\nCustomParametersModel.__dashjs_factory_name = 'CustomParametersModel';\nexport default FactoryMaker.getSingletonFactory(CustomParametersModel);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport FactoryMaker from '../../core/FactoryMaker.js';\nimport {checkInteger} from './SupervisorTools.js';\n\nfunction CustomTimeRanges(/*config*/) {\n    let customTimeRangeArray = [];\n    let length = 0;\n\n    function add(start, end) {\n        let i;\n\n        // eslint-disable-next-line curly\n        for (i = 0; (i < this.customTimeRangeArray.length) && (start > this.customTimeRangeArray[i].start); i++) ;\n\n        this.customTimeRangeArray.splice(i, 0, { start: start, end: end });\n\n        for (i = 0; i < this.customTimeRangeArray.length - 1; i++) {\n            if (this.mergeRanges(i, i + 1)) {\n                i--;\n            }\n        }\n        this.length = this.customTimeRangeArray.length;\n    }\n\n    function clear() {\n        this.customTimeRangeArray = [];\n        this.length = 0;\n    }\n\n    function remove(start, end) {\n        for (let i = 0; i < this.customTimeRangeArray.length; i++) {\n            if (start <= this.customTimeRangeArray[i].start && end >= this.customTimeRangeArray[i].end) {\n                //      |--------------Range i-------|\n                //|---------------Range to remove ---------------|\n                //    or\n                //|--------------Range i-------|\n                //|--------------Range to remove ---------------|\n                //    or\n                //                 |--------------Range i-------|\n                //|--------------Range to remove ---------------|\n                this.customTimeRangeArray.splice(i, 1);\n                i--;\n\n            } else if (start > this.customTimeRangeArray[i].start && end < this.customTimeRangeArray[i].end) {\n                //|-----------------Range i----------------|\n                //        |-------Range to remove -----|\n                this.customTimeRangeArray.splice(i + 1, 0, { start: end, end: this.customTimeRangeArray[i].end });\n                this.customTimeRangeArray[i].end = start;\n                break;\n            } else if (start > this.customTimeRangeArray[i].start && start < this.customTimeRangeArray[i].end) {\n                //|-----------Range i----------|\n                //                    |---------Range to remove --------|\n                //    or\n                //|-----------------Range i----------------|\n                //            |-------Range to remove -----|\n                this.customTimeRangeArray[i].end = start;\n            } else if (end > this.customTimeRangeArray[i].start && end < this.customTimeRangeArray[i].end) {\n                //                     |-----------Range i----------|\n                //|---------Range to remove --------|\n                //            or\n                //|-----------------Range i----------------|\n                //|-------Range to remove -----|\n                this.customTimeRangeArray[i].start = end;\n            }\n        }\n\n        this.length = this.customTimeRangeArray.length;\n    }\n\n    function mergeRanges(rangeIndex1, rangeIndex2) {\n        let range1 = this.customTimeRangeArray[rangeIndex1];\n        let range2 = this.customTimeRangeArray[rangeIndex2];\n\n        if (range1.start <= range2.start && range2.start <= range1.end && range1.end <= range2.end) {\n            //|-----------Range1----------|\n            //                    |-----------Range2----------|\n            range1.end = range2.end;\n            this.customTimeRangeArray.splice(rangeIndex2, 1);\n            return true;\n\n        } else if (range2.start <= range1.start && range1.start <= range2.end && range2.end <= range1.end) {\n            //                |-----------Range1----------|\n            //|-----------Range2----------|\n            range1.start = range2.start;\n            this.customTimeRangeArray.splice(rangeIndex2, 1);\n            return true;\n        } else if (range2.start <= range1.start && range1.start <= range2.end && range1.end <= range2.end) {\n            //      |--------Range1-------|\n            //|---------------Range2--------------|\n            this.customTimeRangeArray.splice(rangeIndex1, 1);\n            return true;\n        } else if (range1.start <= range2.start && range2.start <= range1.end && range2.end <= range1.end) {\n            //|-----------------Range1--------------|\n            //        |-----------Range2----------|\n            this.customTimeRangeArray.splice(rangeIndex2, 1);\n            return true;\n        }\n        return false;\n    }\n\n    function start(index) {\n        checkInteger(index);\n\n        if (index >= this.customTimeRangeArray.length || index < 0) {\n            return NaN;\n        }\n\n        return this.customTimeRangeArray[index].start;\n    }\n\n    function end(index) {\n        checkInteger(index);\n\n        if (index >= this.customTimeRangeArray.length || index < 0) {\n            return NaN;\n        }\n\n        return this.customTimeRangeArray[index].end;\n    }\n\n    return {\n        customTimeRangeArray: customTimeRangeArray,\n        length: length,\n        add: add,\n        clear: clear,\n        remove: remove,\n        mergeRanges: mergeRanges,\n        start: start,\n        end: end\n    };\n}\n\nCustomTimeRanges.__dashjs_factory_name = 'CustomTimeRanges';\nexport default FactoryMaker.getClassFactory(CustomTimeRanges);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport Constants from '../constants/Constants.js';\n\nexport function checkParameterType(parameter, type) {\n    if (typeof parameter !== type) {\n        throw Constants.BAD_ARGUMENT_ERROR;\n    }\n}\n\nexport function checkInteger(parameter) {\n    const isInt = parameter !== null && !isNaN(parameter) && (parameter % 1 === 0);\n\n    if (!isInt) {\n        throw Constants.BAD_ARGUMENT_ERROR + ' : argument is not an integer';\n    }\n}\n\nexport function checkRange(parameter, min, max) {\n    if (parameter < min || parameter > max) {\n        throw Constants.BAD_ARGUMENT_ERROR + ' : argument out of range';\n    }\n}\n\nexport function checkIsVideoOrAudioType(type) {\n    if (typeof type !== 'string' || (type !== Constants.AUDIO && type !== Constants.VIDEO)) {\n        throw Constants.BAD_ARGUMENT_ERROR;\n    }\n}\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @classdesc This Object holds reference to the HTTPRequest for manifest, fragment and xlink loading.\n * Members which are not defined in ISO23009-1 Annex D should be prefixed by a _ so that they are ignored\n * by Metrics Reporting code.\n * @ignore\n */\nclass HTTPRequest {\n    /**\n     * @class\n     */\n    constructor() {\n        /**\n         * Identifier of the TCP connection on which the HTTP request was sent.\n         * @public\n         */\n        this.tcpid = null;\n        /**\n         * This is an optional parameter and should not be included in HTTP request/response transactions for progressive download.\n         * The type of the request:\n         * - MPD\n         * - XLink expansion\n         * - Initialization Fragment\n         * - Index Fragment\n         * - Media Fragment\n         * - Bitstream Switching Fragment\n         * - other\n         * @public\n         */\n        this.type = null;\n        /**\n         * The original URL (before any redirects or failures)\n         * @public\n         */\n        this.url = null;\n        /**\n         * The actual URL requested, if different from above\n         * @public\n         */\n        this.actualurl = null;\n        /**\n         * The contents of the byte-range-spec part of the HTTP Range header.\n         * @public\n         */\n        this.range = null;\n        /**\n         * Real-Time | The real time at which the request was sent.\n         * @public\n         */\n        this.trequest = null;\n        /**\n         * Real-Time | The real time at which the first byte of the response was received.\n         * @public\n         */\n        this.tresponse = null;\n        /**\n         * The HTTP response code.\n         * @public\n         */\n        this.responsecode = null;\n        /**\n         * The duration of the throughput trace intervals (ms), for successful requests only.\n         * @public\n         */\n        this.interval = null;\n        /**\n         * Throughput traces, for successful requests only.\n         * @public\n         */\n        this.trace = [];\n        /**\n         * The CMSD static and dynamic values retrieved from CMSD response headers.\n         * @public\n         */\n        this.cmsd = null;\n\n        /**\n         * Type of stream (\"audio\" | \"video\" etc..)\n         * @public\n         */\n        this._stream = null;\n        /**\n         * Real-Time | The real time at which the request finished.\n         * @public\n         */\n        this._tfinish = null;\n        /**\n         * The duration of the media requests, if available, in seconds.\n         * @public\n         */\n        this._mediaduration = null;\n        /**\n         * all the response headers from request.\n         * @public\n         */\n        this._responseHeaders = null;\n        /**\n         * The selected service location for the request. string.\n         * @public\n         */\n        this._serviceLocation = null;\n        /**\n         * The type of the loader that was used. Distinguish between fetch loader and xhr loader\n         */\n        this._fileLoaderType = null;\n        /**\n         * The values derived from the ResourceTimingAPI.\n         */\n        this._resourceTimingValues = null;\n    }\n}\n\n/**\n * @classdesc This Object holds reference to the progress of the HTTPRequest.\n * @ignore\n */\nclass HTTPRequestTrace {\n    /**\n     * @class\n     */\n    constructor() {\n        /**\n         * Real-Time | Measurement stream start.\n         * @public\n         */\n        this.s = null;\n        /**\n         * Measurement stream duration (ms).\n         * @public\n         */\n        this.d = null;\n        /**\n         * List of integers counting the bytes received in each trace interval within the measurement stream.\n         * @public\n         */\n        this.b = [];\n    }\n}\n\nHTTPRequest.GET = 'GET';\nHTTPRequest.HEAD = 'HEAD';\nHTTPRequest.MPD_TYPE = 'MPD';\nHTTPRequest.XLINK_EXPANSION_TYPE = 'XLinkExpansion';\nHTTPRequest.INIT_SEGMENT_TYPE = 'InitializationSegment';\nHTTPRequest.INDEX_SEGMENT_TYPE = 'IndexSegment';\nHTTPRequest.MEDIA_SEGMENT_TYPE = 'MediaSegment';\nHTTPRequest.BITSTREAM_SWITCHING_SEGMENT_TYPE = 'BitstreamSwitchingSegment';\nHTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE = 'FragmentInfoSegment';\nHTTPRequest.DVB_REPORTING_TYPE = 'DVBReporting';\nHTTPRequest.LICENSE = 'license';\nHTTPRequest.CONTENT_STEERING_TYPE = 'ContentSteering';\nHTTPRequest.OTHER_TYPE = 'other';\n\nexport {HTTPRequest, HTTPRequestTrace};\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "__webpack_require__.amdO = {};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass DVBErrors {\n    constructor() {\n        this.mpdurl = null;\n        // String - Absolute URL from which the MPD was originally\n        // retrieved (MPD updates will not change this value).\n\n        this.errorcode = null;\n        // String - The value of errorcode depends upon the type\n        // of error being reported. For an error listed in the\n        // ErrorType column below the value is as described in the\n        // Value column.\n        //\n        // ErrorType                                            Value\n        // ---------                                            -----\n        // HTTP error status code                               HTTP status code\n        // Unknown HTTP status code                             HTTP status code\n        // SSL connection failed                                \"SSL\" followed by SSL alert value\n        // DNS resolution failed                                \"C00\"\n        // Host unreachable                                     \"C01\"\n        // Connection refused                                   \"C02\"\n        // Connection error – Not otherwise specified           \"C03\"\n        // Corrupt media – ISO BMFF container cannot be parsed  \"M00\"\n        // Corrupt media – Not otherwise specified              \"M01\"\n        // Changing Base URL in use due to errors               \"F00\"\n        // Becoming an error reporting Player                   \"S00\"\n\n        this.terror = null;\n        // Real-Time - Date and time at which error occurred in UTC,\n        // formatted as a combined date and time according to ISO 8601.\n\n        this.url = null;\n        // String - Absolute URL from which data was being requested\n        // when this error occurred. If the error report is in relation\n        // to corrupt media or changing BaseURL, this may be a null\n        // string if the URL from which the media was obtained or\n        // which led to the change of BaseURL is no longer known.\n\n        this.ipaddress = null;\n        // String - IP Address which the host name in \"url\" resolved to.\n        // If the error report is in relation to corrupt media or\n        // changing BaseURL, this may be a null string if the URL\n        // from which the media was obtained or which led to the\n        // change of BaseURL is no longer known.\n\n        this.servicelocation = null;\n        // String - The value of the serviceLocation field in the\n        // BaseURL being used. In the event of this report indicating\n        // a change of BaseURL this is the value from the BaseURL\n        // being moved from.\n    }\n}\n\nDVBErrors.SSL_CONNECTION_FAILED_PREFIX = 'SSL';\nDVBErrors.DNS_RESOLUTION_FAILED = 'C00';\nDVBErrors.HOST_UNREACHABLE = 'C01';\nDVBErrors.CONNECTION_REFUSED = 'C02';\nDVBErrors.CONNECTION_ERROR = 'C03';\nDVBErrors.CORRUPT_MEDIA_ISOBMFF = 'M00';\nDVBErrors.CORRUPT_MEDIA_OTHER = 'M01';\nDVBErrors.BASE_URL_CHANGED = 'F00';\nDVBErrors.BECAME_REPORTER = 'S00';\n\nexport default DVBErrors;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport DVBErrors from '../vo/DVBErrors.js';\nimport MetricsReportingEvents from '../MetricsReportingEvents.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction DVBErrorsTranslator(config) {\n\n    config = config || {};\n    let instance,\n        mpd;\n    const eventBus = config.eventBus;\n    const dashMetrics = config.dashMetrics;\n    const metricsConstants = config.metricsConstants;\n    //MediaPlayerEvents have been added to Events in MediaPlayer class\n    const Events = config.events;\n\n    function report(vo) {\n        let o = new DVBErrors();\n\n        if (!mpd) {\n            return;\n        }\n\n        for (const key in vo) {\n            if (vo.hasOwnProperty(key)) {\n                o[key] = vo[key];\n            }\n        }\n\n        if (!o.mpdurl) {\n            o.mpdurl = mpd.originalUrl || mpd.url;\n        }\n\n        if (!o.terror) {\n            o.terror = new Date();\n        }\n\n        dashMetrics.addDVBErrors(o);\n    }\n\n    function onManifestUpdate(e) {\n        if (e.error) {\n            return;\n        }\n\n        mpd = e.manifest;\n    }\n\n    function onServiceLocationChanged(e) {\n        report({\n            errorcode: DVBErrors.BASE_URL_CHANGED,\n            servicelocation: e.entry\n        });\n    }\n\n    function onBecameReporter() {\n        report({\n            errorcode: DVBErrors.BECAME_REPORTER\n        });\n    }\n\n    function handleHttpMetric(vo) {\n        if ((vo.responsecode === 0) || // connection failure - unknown\n            (vo.responsecode == null) || // Generated on .catch() and when uninitialized\n            (vo.responsecode >= 400) || // HTTP error status code\n            (vo.responsecode < 100) || // unknown status codes\n            (vo.responsecode >= 600)) { // unknown status codes\n            report({\n                errorcode: vo.responsecode || DVBErrors.CONNECTION_ERROR,\n                url: vo.url,\n                terror: vo.tresponse,\n                servicelocation: vo._serviceLocation\n            });\n        }\n    }\n\n    function onMetricEvent(e) {\n        switch (e.metric) {\n            case metricsConstants.HTTP_REQUEST:\n                handleHttpMetric(e.value);\n                break;\n            default:\n                break;\n        }\n    }\n\n    function onPlaybackError(e) {\n        let reason = e.error ? e.error.code : 0;\n        let errorcode;\n\n        switch (reason) {\n            case MediaError.MEDIA_ERR_NETWORK:\n                errorcode = DVBErrors.CONNECTION_ERROR;\n                break;\n            case MediaError.MEDIA_ERR_DECODE:\n                errorcode = DVBErrors.CORRUPT_MEDIA_OTHER;\n                break;\n            default:\n                return;\n        }\n\n        report({\n            errorcode: errorcode\n        });\n    }\n\n    function initialize() {\n        eventBus.on(Events.MANIFEST_UPDATED, onManifestUpdate, instance);\n        eventBus.on(\n            Events.SERVICE_LOCATION_BASE_URL_BLACKLIST_CHANGED,\n            onServiceLocationChanged,\n            instance\n        );\n        eventBus.on(Events.METRIC_ADDED, onMetricEvent, instance);\n        eventBus.on(Events.METRIC_UPDATED, onMetricEvent, instance);\n        eventBus.on(Events.PLAYBACK_ERROR, onPlaybackError, instance);\n        eventBus.on(\n            MetricsReportingEvents.BECAME_REPORTING_PLAYER,\n            onBecameReporter,\n            instance\n        );\n    }\n\n    function reset() {\n        eventBus.off(Events.MANIFEST_UPDATED, onManifestUpdate, instance);\n        eventBus.off(\n            Events.SERVICE_LOCATION_BASE_URL_BLACKLIST_CHANGED,\n            onServiceLocationChanged,\n            instance\n        );\n        eventBus.off(Events.METRIC_ADDED, onMetricEvent, instance);\n        eventBus.off(Events.METRIC_UPDATED, onMetricEvent, instance);\n        eventBus.off(Events.PLAYBACK_ERROR, onPlaybackError, instance);\n        eventBus.off(\n            MetricsReportingEvents.BECAME_REPORTING_PLAYER,\n            onBecameReporter,\n            instance\n        );\n    }\n\n    instance = {\n        initialize,\n        reset\n    };\n\n    return instance;\n}\n\nDVBErrorsTranslator.__dashjs_factory_name = 'DVBErrorsTranslator';\nexport default FactoryMaker.getSingletonFactory(DVBErrorsTranslator); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport CustomTimeRanges from '../../utils/CustomTimeRanges.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction RangeController(config) {\n\n    config = config || {};\n    let useWallClockTime = false;\n    let context = this.context;\n    let instance,\n        ranges;\n\n    let mediaElement = config.mediaElement;\n\n    function initialize(rs) {\n        if (rs && rs.length) {\n            rs.forEach(r => {\n                let start = r.starttime;\n                let end = start + r.duration;\n\n                ranges.add(start, end);\n            });\n\n            useWallClockTime = !!rs[0]._useWallClockTime;\n        }\n    }\n\n    function reset() {\n        ranges.clear();\n    }\n\n    function setup() {\n        ranges = CustomTimeRanges(context).create();\n    }\n\n    function isEnabled() {\n        let numRanges = ranges.length;\n        let time;\n\n        if (!numRanges) {\n            return true;\n        }\n\n        // When not present, DASH Metrics reporting is requested\n        // for the whole duration of the content.\n        time = useWallClockTime ?\n            (new Date().getTime() / 1000) :\n            mediaElement.currentTime;\n\n        for (let i = 0; i < numRanges; i += 1) {\n            let start = ranges.start(i);\n            let end = ranges.end(i);\n\n            if ((start <= time) && (time < end)) {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    instance = {\n        initialize: initialize,\n        reset: reset,\n        isEnabled: isEnabled\n    };\n\n    setup();\n\n    return instance;\n}\n\nRangeController.__dashjs_factory_name = 'RangeController';\nexport default FactoryMaker.getClassFactory(RangeController); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\n/**\n * @ignore\n */\nfunction MetricSerialiser() {\n\n    // For each entry in the top level list within the metric (in the case\n    // of the DVBErrors metric each entry corresponds to an \"error event\"\n    // described in clause 10.8.4) the Player shall:\n    function serialise(metric) {\n        let pairs = [];\n        let obj = [];\n        let key,\n            value;\n\n        // Take each (key, value) pair from the metric entry and create a\n        // string consisting of the name of the key, followed by an equals\n        // ('=') character, followed by the string representation of the\n        // value. The string representation of the value is created based\n        // on the type of the value following the instructions in Table 22.\n        for (key in metric) {\n            if (metric.hasOwnProperty(key) && (key.indexOf('_') !== 0)) {\n                value = metric[key];\n\n                // we want to ensure that keys still end up in the report\n                // even if there is no value\n                if ((value === undefined) || (value === null)) {\n                    value = '';\n                }\n\n                // DVB A168 10.12.4 Table 22\n                if (Array.isArray(value)) {\n                    // if trace or similar is null, do not include in output\n                    if (!value.length) {\n                        continue;\n                    }\n\n                    obj = [];\n\n                    value.forEach(function (v) {\n                        let isBuiltIn = Object.prototype.toString.call(v).slice(8, -1) !== 'Object';\n\n                        obj.push(isBuiltIn ? v : serialise(v));\n                    });\n\n                    value = obj.map(encodeURIComponent).join(',');\n                } else if (typeof value === 'string') {\n                    value = encodeURIComponent(value);\n                } else if (value instanceof Date) {\n                    value = value.toISOString();\n                } else if (typeof value === 'number') {\n                    value = Math.round(value);\n                }\n\n                pairs.push(key + '=' + value);\n            }\n        }\n\n        // Concatenate the strings created in the previous step with an\n        // ampersand ('&') character between each one.\n        return pairs.join('&');\n    }\n\n    return {\n        serialise: serialise\n    };\n}\n\nMetricSerialiser.__dashjs_factory_name = 'MetricSerialiser';\nexport default FactoryMaker.getSingletonFactory(MetricSerialiser); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\n/**\n * @ignore\n */\nfunction RNG() {\n\n    // check whether secure random numbers are available. if not, revert to\n    // using Math.random\n    let crypto = window.crypto || window.msCrypto;\n\n    // could just as easily use any other array type by changing line below\n    let ArrayType = Uint32Array;\n    let MAX_VALUE = Math.pow(2, ArrayType.BYTES_PER_ELEMENT * 8) - 1;\n\n    // currently there is only one client for this code, and that only uses\n    // a single random number per initialisation. may want to increase this\n    // number if more consumers in the future\n    let NUM_RANDOM_NUMBERS = 10;\n\n    let randomNumbers,\n        index,\n        instance;\n\n    function initialize() {\n        if (crypto) {\n            if (!randomNumbers) {\n                randomNumbers = new ArrayType(NUM_RANDOM_NUMBERS);\n            }\n            crypto.getRandomValues(randomNumbers);\n            index = 0;\n        }\n    }\n\n    function rand(min, max) {\n        let r;\n\n        if (!min) {\n            min = 0;\n        }\n\n        if (!max) {\n            max = 1;\n        }\n\n        if (crypto) {\n            if (index === randomNumbers.length) {\n                initialize();\n            }\n\n            r = randomNumbers[index] / MAX_VALUE;\n            index += 1;\n        } else {\n            r = Math.random();\n        }\n\n        return (r * (max - min)) + min;\n    }\n\n    instance = {\n        random: rand\n    };\n\n    initialize();\n\n    return instance;\n}\n\nRNG.__dashjs_factory_name = 'RNG';\nexport default FactoryMaker.getSingletonFactory(RNG); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport MetricSerialiser from '../../utils/MetricSerialiser.js';\nimport RNG from '../../utils/RNG.js';\nimport CustomParametersModel from '../../../models/CustomParametersModel.js';\nimport FactoryMaker from '../../../../core/FactoryMaker.js';\n\nfunction DVBReporting(config) {\n    config = config || {};\n    let instance;\n\n    let context = this.context;\n    let metricSerialiser,\n        customParametersModel,\n        randomNumberGenerator,\n        reportingPlayerStatusDecided,\n        isReportingPlayer,\n        reportingUrl,\n        rangeController;\n\n    let USE_DRAFT_DVB_SPEC = true;\n    let allowPendingRequestsToCompleteOnReset = true;\n    let pendingRequests = [];\n\n    const metricsConstants = config.metricsConstants;\n\n    function setup() {\n        metricSerialiser = MetricSerialiser(context).getInstance();\n        randomNumberGenerator = RNG(context).getInstance();\n        customParametersModel = CustomParametersModel(context).getInstance();\n\n        resetInitialSettings();\n    }\n\n    function doGetRequest(url, successCB, failureCB) {\n        let req = new XMLHttpRequest();\n        req.withCredentials = customParametersModel.getXHRWithCredentialsForType(metricsConstants.HTTP_REQUEST_DVB_REPORTING_TYPE);\n        const oncomplete = function () {\n            let reqIndex = pendingRequests.indexOf(req);\n\n            if (reqIndex === -1) {\n                return;\n            } else {\n                pendingRequests.splice(reqIndex, 1);\n            }\n\n            if ((req.status >= 200) && (req.status < 300)) {\n                if (successCB) {\n                    successCB();\n                }\n            } else {\n                if (failureCB) {\n                    failureCB();\n                }\n            }\n        };\n\n        pendingRequests.push(req);\n\n        try {\n            req.open('GET', url);\n            req.onloadend = oncomplete;\n            req.onerror = oncomplete;\n            req.send();\n        } catch (e) {\n            req.onerror();\n        }\n    }\n\n    function report(type, vos) {\n        if (!Array.isArray(vos)) {\n            vos = [vos];\n        }\n\n        // If the Player is not a reporting Player, then the Player shall\n        // not report any errors.\n        // ... In addition to any time restrictions specified by a Range\n        // element within the Metrics element.\n        if (isReportingPlayer && rangeController.isEnabled()) {\n\n            // This reporting mechanism operates by creating one HTTP GET\n            // request for every entry in the top level list of the metric.\n            vos.forEach(function (vo) {\n                let url = metricSerialiser.serialise(vo);\n\n                // this has been proposed for errata\n                if (USE_DRAFT_DVB_SPEC && (type !== metricsConstants.DVB_ERRORS)) {\n                    url = `metricname=${type}&${url}`;\n                }\n\n                // Take the value of the @reportingUrl attribute, append a\n                // question mark ('?') character and then append the string\n                // created in the previous step.\n                url = `${reportingUrl}?${url}`;\n\n                // Make an HTTP GET request to the URL contained within the\n                // string created in the previous step.\n                doGetRequest(url, null, function () {\n                    // If the Player is unable to make the report, for\n                    // example because the @reportingUrl is invalid, the\n                    // host cannot be reached, or an HTTP status code other\n                    // than one in the 200 series is received, the Player\n                    // shall cease being a reporting Player for the\n                    // duration of the MPD.\n                    isReportingPlayer = false;\n                });\n            });\n        }\n    }\n\n    function initialize(entry, rc) {\n        let probability;\n\n        rangeController = rc;\n\n        reportingUrl = entry.dvbReportingUrl;\n\n        // If a required attribute is missing, the Reporting descriptor may\n        // be ignored by the Player\n        if (!reportingUrl) {\n            throw new Error(\n                'required parameter missing (dvb:reportingUrl)'\n            );\n        }\n\n        // A Player's status, as a reporting Player or not, shall remain\n        // static for the duration of the MPD, regardless of MPD updates.\n        // (i.e. only calling reset (or failure) changes this state)\n        if (!reportingPlayerStatusDecided) {\n            probability = entry.dvbProbability;\n            // TS 103 285 Clause *********\n            // If the @probability attribute is set to 1000, it shall be a reporting Player.\n            // If the @probability attribute is absent it will take the default value of 1000.\n            // For any other value of the @probability attribute, it shall decide at random whether to be a\n            // reporting Player, such that the probability of being one is @probability/1000.\n            if (probability && (probability === 1000 || ((probability / 1000) >= randomNumberGenerator.random()))) {\n                isReportingPlayer = true;\n            }\n\n            reportingPlayerStatusDecided = true;\n        }\n    }\n\n    function resetInitialSettings() {\n        reportingPlayerStatusDecided = false;\n        isReportingPlayer = false;\n        reportingUrl = null;\n        rangeController = null;\n    }\n\n    function reset() {\n        if (!allowPendingRequestsToCompleteOnReset) {\n            pendingRequests.forEach(req => req.abort());\n            pendingRequests = [];\n        }\n\n        resetInitialSettings();\n    }\n\n    instance = {\n        report:     report,\n        initialize: initialize,\n        reset:      reset\n    };\n\n    setup();\n\n    return instance;\n}\n\nDVBReporting.__dashjs_factory_name = 'DVBReporting';\nexport default FactoryMaker.getClassFactory(DVBReporting); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport DVBReporting from './reporters/DVBReporting.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction ReportingFactory(config) {\n    config = config || {};\n\n    const knownReportingSchemeIdUris = {\n        'urn:dvb:dash:reporting:2014': DVBReporting\n    };\n\n    const context = this.context;\n    let instance;\n    const logger = config.debug ? config.debug.getLogger(instance) : {};\n    const metricsConstants = config.metricsConstants;\n    const mediaPlayerModel = config.mediaPlayerModel || {};\n\n    function create(entry, rangeController) {\n        let reporting;\n\n        try {\n            reporting = knownReportingSchemeIdUris[entry.schemeIdUri](context).create({\n                metricsConstants: metricsConstants,\n                mediaPlayerModel: mediaPlayerModel\n            });\n\n            reporting.initialize(entry, rangeController);\n        } catch (e) {\n            reporting = null;\n            logger.error(`ReportingFactory: could not create Reporting with schemeIdUri ${entry.schemeIdUri} (${e.message})`);\n        }\n\n        return reporting;\n    }\n\n    function register(schemeIdUri, moduleName) {\n        knownReportingSchemeIdUris[schemeIdUri] = moduleName;\n    }\n\n    function unregister(schemeIdUri) {\n        delete knownReportingSchemeIdUris[schemeIdUri];\n    }\n\n    instance = {\n        create:     create,\n        register:   register,\n        unregister: unregister\n    };\n\n    return instance;\n}\n\nReportingFactory.__dashjs_factory_name = 'ReportingFactory';\nexport default FactoryMaker.getSingletonFactory(ReportingFactory); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport ReportingFactory from '../reporting/ReportingFactory.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction ReportingController(config) {\n\n    let reporters = [];\n    let instance;\n\n    const reportingFactory = ReportingFactory(this.context).getInstance(config);\n\n    function initialize(reporting, rangeController) {\n        // \"if multiple Reporting elements are present, it is expected that\n        // the client processes one of the recognized reporting schemes.\"\n        // to ignore this, and support multiple Reporting per Metric,\n        // simply change the 'some' below to 'forEach'\n        reporting.some(r => {\n            let reporter = reportingFactory.create(r, rangeController);\n\n            if (reporter) {\n                reporters.push(reporter);\n                return true;\n            }\n        });\n    }\n\n    function reset() {\n        reporters.forEach(r => r.reset());\n        reporters = [];\n    }\n\n    function report(type, vos) {\n        reporters.forEach(r => r.report(type, vos));\n    }\n\n    instance = {\n        initialize: initialize,\n        reset:      reset,\n        report:     report\n    };\n\n    return instance;\n}\n\nReportingController.__dashjs_factory_name = 'ReportingController';\nexport default FactoryMaker.getClassFactory(ReportingController); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\n/**\n * @ignore\n */\nfunction HandlerHelpers() {\n    return {\n        reconstructFullMetricName: function (key, n, type) {\n            let mn = key;\n\n            if (n) {\n                mn += '(' + n;\n\n                if (type && type.length) {\n                    mn += ',' + type;\n                }\n\n                mn += ')';\n            }\n\n            return mn;\n        },\n\n        validateN: function (n_ms) {\n            if (!n_ms) {\n                throw new Error('missing n');\n            }\n\n            if (isNaN(n_ms)) {\n                throw new Error('n is NaN');\n            }\n\n            // n is a positive integer is defined to refer to the metric\n            // in which the buffer level is recorded every n ms.\n            if (n_ms < 0) {\n                throw new Error('n must be positive');\n            }\n\n            return n_ms;\n        }\n    };\n}\n\nHandlerHelpers.__dashjs_factory_name = 'HandlerHelpers';\nexport default FactoryMaker.getSingletonFactory(HandlerHelpers); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport HandlerHelpers from '../../utils/HandlerHelpers.js';\nimport FactoryMaker from '../../../../core/FactoryMaker.js';\n\nfunction BufferLevelHandler(config) {\n\n    config = config || {};\n    let instance,\n        reportingController,\n        n,\n        name,\n        interval,\n        lastReportedTime;\n\n    let context = this.context;\n    let handlerHelpers = HandlerHelpers(context).getInstance();\n\n    let storedVOs = [];\n\n    const metricsConstants = config.metricsConstants;\n\n    function getLowestBufferLevelVO() {\n        try {\n            return Object.keys(storedVOs).map(\n                key => storedVOs[key]\n            ).reduce(\n                (a, b) => {\n                    return (a.level < b.level) ? a : b;\n                }\n            );\n        } catch (e) {\n            return;\n        }\n    }\n\n    function intervalCallback() {\n        let vo = getLowestBufferLevelVO();\n\n        if (vo) {\n            if (lastReportedTime !== vo.t) {\n                lastReportedTime = vo.t;\n                reportingController.report(name, vo);\n            }\n        }\n    }\n\n    function initialize(basename, rc, n_ms) {\n        if (rc) {\n            // this will throw if n is invalid, to be\n            // caught by the initialize caller.\n            n = handlerHelpers.validateN(n_ms);\n            reportingController = rc;\n            name = handlerHelpers.reconstructFullMetricName(basename, n_ms);\n            interval = setInterval(intervalCallback, n);\n        }\n    }\n\n    function reset() {\n        clearInterval(interval);\n        interval = null;\n        n = 0;\n        reportingController = null;\n        lastReportedTime = null;\n    }\n\n    function handleNewMetric(metric, vo, type) {\n        if (metric === metricsConstants.BUFFER_LEVEL) {\n            storedVOs[type] = vo;\n        }\n    }\n\n    instance = {\n        initialize:         initialize,\n        reset:              reset,\n        handleNewMetric:    handleNewMetric\n    };\n\n    return instance;\n}\n\nBufferLevelHandler.__dashjs_factory_name = 'BufferLevelHandler';\nexport default FactoryMaker.getClassFactory(BufferLevelHandler); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport MetricsReportingEvents from '../../MetricsReportingEvents.js';\nimport FactoryMaker from '../../../../core/FactoryMaker.js';\n\nfunction DVBErrorsHandler(config) {\n\n    config = config || {};\n    let instance,\n        reportingController;\n\n    let eventBus = config.eventBus;\n    const metricsConstants = config.metricsConstants;\n\n    function onInitialisationComplete() {\n        // we only want to report this once per call to initialize\n        eventBus.off(\n            MetricsReportingEvents.METRICS_INITIALISATION_COMPLETE,\n            onInitialisationComplete,\n            this\n        );\n\n        // Note: A Player becoming a reporting Player is itself\n        // something which is recorded by the DVBErrors metric.\n        eventBus.trigger(MetricsReportingEvents.BECAME_REPORTING_PLAYER);\n    }\n\n    function initialize(unused, rc) {\n        if (rc) {\n            reportingController = rc;\n\n            eventBus.on(\n                MetricsReportingEvents.METRICS_INITIALISATION_COMPLETE,\n                onInitialisationComplete,\n                this\n            );\n        }\n    }\n\n    function reset() {\n        reportingController = null;\n    }\n\n    function handleNewMetric(metric, vo) {\n        // simply pass metric straight through\n        if (metric === metricsConstants.DVB_ERRORS) {\n            if (reportingController) {\n                reportingController.report(metric, vo);\n            }\n        }\n    }\n\n    instance = {\n        initialize:         initialize,\n        reset:              reset,\n        handleNewMetric:    handleNewMetric\n    };\n\n    return instance;\n}\n\nexport default FactoryMaker.getClassFactory(DVBErrorsHandler); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport HandlerHelpers from '../../utils/HandlerHelpers.js';\nimport FactoryMaker from '../../../../core/FactoryMaker.js';\n\nfunction HttpListHandler(config) {\n\n    config = config || {};\n    let instance,\n        reportingController,\n        n,\n        type,\n        name,\n        interval;\n\n    let storedVos = [];\n\n    let handlerHelpers = HandlerHelpers(this.context).getInstance();\n\n    const metricsConstants = config.metricsConstants;\n\n    function intervalCallback() {\n        var vos = storedVos;\n\n        if (vos.length) {\n            if (reportingController) {\n                reportingController.report(name, vos);\n            }\n        }\n\n        storedVos = [];\n    }\n\n    function initialize(basename, rc, n_ms, requestType) {\n        if (rc) {\n\n            // this will throw if n is invalid, to be\n            // caught by the initialize caller.\n            n = handlerHelpers.validateN(n_ms);\n\n            reportingController = rc;\n\n            if (requestType && requestType.length) {\n                type = requestType;\n            }\n\n            name = handlerHelpers.reconstructFullMetricName(\n                basename,\n                n_ms,\n                requestType\n            );\n\n            interval = setInterval(intervalCallback, n);\n        }\n    }\n\n    function reset() {\n        clearInterval(interval);\n        interval = null;\n        n = null;\n        type = null;\n        storedVos = [];\n        reportingController = null;\n    }\n\n    function handleNewMetric(metric, vo) {\n        if (metric === metricsConstants.HTTP_REQUEST) {\n            if (!type || (type === vo.type)) {\n                storedVos.push(vo);\n            }\n        }\n    }\n\n    instance = {\n        initialize:         initialize,\n        reset:              reset,\n        handleNewMetric:    handleNewMetric\n    };\n\n    return instance;\n}\n\nHttpListHandler.__dashjs_factory_name = 'HttpListHandler';\nexport default FactoryMaker.getClassFactory(HttpListHandler); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport FactoryMaker from '../../../../core/FactoryMaker.js';\n\n/**\n * @ignore\n */\nfunction GenericMetricHandler() {\n\n    let instance,\n        metricName,\n        reportingController;\n\n    function initialize(name, rc) {\n        metricName = name;\n        reportingController = rc;\n    }\n\n    function reset() {\n        reportingController = null;\n        metricName = undefined;\n    }\n\n    function handleNewMetric(metric, vo) {\n        // simply pass metric straight through\n        if (metric === metricName) {\n            if (reportingController) {\n                reportingController.report(metricName, vo);\n            }\n        }\n    }\n\n    instance = {\n        initialize: initialize,\n        reset: reset,\n        handleNewMetric: handleNewMetric\n    };\n\n    return instance;\n}\n\nGenericMetricHandler.__dashjs_factory_name = 'GenericMetricHandler';\nexport default FactoryMaker.getClassFactory(GenericMetricHandler); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport BufferLevel from './handlers/BufferLevelHandler.js';\nimport DVBErrors from './handlers/DVBErrorsHandler.js';\nimport HttpList from './handlers/HttpListHandler.js';\nimport GenericMetricHandler from './handlers/GenericMetricHandler.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction MetricsHandlerFactory(config) {\n\n    config = config || {};\n    let instance;\n    const logger = config.debug ? config.debug.getLogger(instance) : {};\n\n    // group 1: key, [group 3: n [, group 5: type]]\n    let keyRegex = /([a-zA-Z]*)(\\(([0-9]*)(\\,\\s*([a-zA-Z]*))?\\))?/;\n\n    const context = this.context;\n    let knownFactoryProducts = {\n        BufferLevel:    BufferLevel,\n        DVBErrors:      DVBErrors,\n        HttpList:       HttpList,\n        PlayList:       GenericMetricHandler,\n        RepSwitchList:  GenericMetricHandler,\n        TcpList:        GenericMetricHandler\n    };\n\n    function create(listType, reportingController) {\n        var matches = listType.match(keyRegex);\n        var handler;\n\n        if (!matches) {\n            return;\n        }\n\n        try {\n            handler = knownFactoryProducts[matches[1]](context).create({\n                eventBus: config.eventBus,\n                metricsConstants: config.metricsConstants\n            });\n\n            handler.initialize(\n                matches[1],\n                reportingController,\n                matches[3],\n                matches[5]\n            );\n        } catch (e) {\n            handler = null;\n            logger.error(`MetricsHandlerFactory: Could not create handler for type ${matches[1]} with args ${matches[3]}, ${matches[5]} (${e.message})`);\n        }\n\n        return handler;\n    }\n\n    function register(key, handler) {\n        knownFactoryProducts[key] = handler;\n    }\n\n    function unregister(key) {\n        delete knownFactoryProducts[key];\n    }\n\n    instance = {\n        create:     create,\n        register:   register,\n        unregister: unregister\n    };\n\n    return instance;\n}\n\nMetricsHandlerFactory.__dashjs_factory_name = 'MetricsHandlerFactory';\nexport default FactoryMaker.getSingletonFactory(MetricsHandlerFactory); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport MetricsHandlerFactory from '../metrics/MetricsHandlerFactory.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction MetricsHandlersController(config) {\n\n    config = config || {};\n    let handlers = [];\n\n    let instance;\n    const context = this.context;\n    const eventBus = config.eventBus;\n    const Events = config.events;\n\n    let metricsHandlerFactory = MetricsHandlerFactory(context).getInstance({\n        debug: config.debug,\n        eventBus: config.eventBus,\n        metricsConstants: config.metricsConstants\n    });\n\n    function handle(e) {\n        handlers.forEach(handler => {\n            handler.handleNewMetric(e.metric, e.value, e.mediaType);\n        });\n    }\n\n    function initialize(metrics, reportingController) {\n        metrics.split(',').forEach(\n            (m, midx, ms) => {\n                let handler;\n\n                // there is a bug in ISO23009-1 where the metrics attribute\n                // is a comma-separated list but HttpList key can contain a\n                // comma enclosed by ().\n                if ((m.indexOf('(') !== -1) && m.indexOf(')') === -1) {\n                    let nextm = ms[midx + 1];\n\n                    if (nextm &&\n                            (nextm.indexOf('(') === -1) &&\n                            (nextm.indexOf(')') !== -1)) {\n                        m += ',' + nextm;\n\n                        // delete the next metric so forEach does not visit.\n                        delete ms[midx + 1];\n                    }\n                }\n\n                handler = metricsHandlerFactory.create(\n                    m,\n                    reportingController\n                );\n\n                if (handler) {\n                    handlers.push(handler);\n                }\n            }\n        );\n\n        eventBus.on(\n            Events.METRIC_ADDED,\n            handle,\n            instance\n        );\n\n        eventBus.on(\n            Events.METRIC_UPDATED,\n            handle,\n            instance\n        );\n    }\n\n    function reset() {\n        eventBus.off(\n            Events.METRIC_ADDED,\n            handle,\n            instance\n        );\n\n        eventBus.off(\n            Events.METRIC_UPDATED,\n            handle,\n            instance\n        );\n\n        handlers.forEach(handler => handler.reset());\n\n        handlers = [];\n    }\n\n    instance = {\n        initialize: initialize,\n        reset:      reset\n    };\n\n    return instance;\n}\n\nMetricsHandlersController.__dashjs_factory_name = 'MetricsHandlersController';\nexport default FactoryMaker.getClassFactory(MetricsHandlersController); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport RangeController from './RangeController.js';\nimport ReportingController from './ReportingController.js';\nimport MetricsHandlersController from './MetricsHandlersController.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction MetricsController(config) {\n\n    config = config || {};\n    let metricsHandlersController,\n        reportingController,\n        rangeController,\n        instance;\n\n    let context = this.context;\n\n    function initialize(metricsEntry) {\n        try {\n            rangeController = RangeController(context).create({\n                mediaElement: config.mediaElement\n            });\n\n            rangeController.initialize(metricsEntry.Range);\n\n            reportingController = ReportingController(context).create({\n                debug: config.debug,\n                metricsConstants: config.metricsConstants,\n                mediaPlayerModel: config.mediaPlayerModel\n            });\n\n            reportingController.initialize(metricsEntry.Reporting, rangeController);\n\n            metricsHandlersController = MetricsHandlersController(context).create({\n                debug: config.debug,\n                eventBus: config.eventBus,\n                metricsConstants: config.metricsConstants,\n                events: config.events\n            });\n\n            metricsHandlersController.initialize(metricsEntry.metrics, reportingController);\n        } catch (e) {\n            reset();\n            throw e;\n        }\n    }\n\n    function reset() {\n        if (metricsHandlersController) {\n            metricsHandlersController.reset();\n        }\n\n        if (reportingController) {\n            reportingController.reset();\n        }\n\n        if (rangeController) {\n            rangeController.reset();\n        }\n    }\n\n    instance = {\n        initialize: initialize,\n        reset:      reset\n    };\n\n    return instance;\n}\n\nMetricsController.__dashjs_factory_name = 'MetricsController';\nexport default FactoryMaker.getClassFactory(MetricsController); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass Metrics {\n    constructor() {\n\n        this.metrics = '';\n        this.Range = [];\n        this.Reporting = [];\n    }\n}\n\nexport default Metrics;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass Range {\n    constructor() {\n\n        // as defined in ISO23009-1\n        this.starttime = 0;\n        this.duration = Infinity;\n\n        // for internal use\n        this._useWallClockTime = false;\n    }\n}\n\nexport default Range;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\n\n// TS 103 285 Clause 10.12.3.3\nconst DEFAULT_DVB_PROBABILITY = 1000;\n\nclass Reporting {\n    constructor() {\n\n        this.schemeIdUri = '';\n        this.value = '';\n\n        // DVB Extensions\n        this.dvbReportingUrl = '';\n        this.dvbProbability = DEFAULT_DVB_PROBABILITY;\n    }\n}\n\nexport default Reporting;\n", "import Metrics from '../vo/Metrics.js';\nimport Range from '../vo/Range.js';\nimport Reporting from '../vo/Reporting.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction ManifestParsing (config) {\n    config = config || {};\n    let instance;\n    let adapter = config.adapter;\n    const constants = config.constants;\n\n    function getMetricsRangeStartTime(manifest, dynamic, range) {\n        let voPeriods,\n            reportingStartTime;\n        let presentationStartTime = 0;\n\n        if (dynamic) {\n            // For services with MPD@type='dynamic', the start time is\n            // indicated in wall clock time by adding the value of this\n            // attribute to the value of the MPD@availabilityStartTime\n            // attribute.\n            presentationStartTime = adapter.getAvailabilityStartTime(manifest) / 1000;\n        } else {\n            // For services with MPD@type='static', the start time is indicated\n            // in Media Presentation time and is relative to the PeriodStart\n            // time of the first Period in this MPD.\n            voPeriods = adapter.getRegularPeriods(manifest);\n\n            if (voPeriods.length) {\n                presentationStartTime = voPeriods[0].start;\n            }\n        }\n\n        // When not present, DASH Metrics collection is\n        // requested from the beginning of content\n        // consumption.\n        reportingStartTime = presentationStartTime;\n\n        if (range && range.hasOwnProperty(constants.START_TIME)) {\n            reportingStartTime += range.starttime;\n        }\n\n        return reportingStartTime;\n    }\n\n    function getMetrics(manifest) {\n        let metrics = [];\n\n        if (manifest && manifest.Metrics) {\n            manifest.Metrics.forEach(metric => {\n                var metricEntry = new Metrics();\n                var isDynamic = adapter.getIsDynamic(manifest);\n\n                if (metric.hasOwnProperty('metrics')) {\n                    metricEntry.metrics = metric.metrics;\n                } else {\n                    return;\n                }\n\n                if (metric.Range) {\n                    metric.Range.forEach(range => {\n                        var rangeEntry = new Range();\n\n                        rangeEntry.starttime =\n                            getMetricsRangeStartTime(manifest, isDynamic, range);\n\n                        if (range.hasOwnProperty('duration')) {\n                            rangeEntry.duration = range.duration;\n                        } else {\n                            // if not present, the value is identical to the\n                            // Media Presentation duration.\n                            rangeEntry.duration = adapter.getDuration(manifest);\n                        }\n\n                        rangeEntry._useWallClockTime = isDynamic;\n\n                        metricEntry.Range.push(rangeEntry);\n                    });\n                }\n\n                if (metric.Reporting) {\n                    metric.Reporting.forEach(reporting => {\n                        var reportingEntry = new Reporting();\n\n                        if (reporting.hasOwnProperty(constants.SCHEME_ID_URI)) {\n                            reportingEntry.schemeIdUri = reporting.schemeIdUri;\n                        } else {\n                            // Invalid Reporting. schemeIdUri must be set. Ignore.\n                            return;\n                        }\n\n                        if (reporting.hasOwnProperty('value')) {\n                            reportingEntry.value = reporting.value;\n                        }\n\n                        if (reporting.hasOwnProperty(constants.DVB_REPORTING_URL)) {\n                            reportingEntry.dvbReportingUrl = reporting[constants.DVB_REPORTING_URL];\n                        }\n\n                        if (reporting.hasOwnProperty(constants.DVB_PROBABILITY)) {\n                            reportingEntry.dvbProbability = reporting[constants.DVB_PROBABILITY];\n                        }\n\n                        metricEntry.Reporting.push(reportingEntry);\n                    });\n                } else {\n                    // Invalid Metrics. At least one reporting must be present. Ignore\n                    return;\n                }\n\n                metrics.push(metricEntry);\n            });\n        }\n\n        return metrics;\n    }\n\n    instance = {\n        getMetrics: getMetrics\n    };\n\n    return instance;\n}\n\nManifestParsing.__dashjs_factory_name = 'ManifestParsing';\nexport default FactoryMaker.getSingletonFactory(ManifestParsing); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport MetricsController from './MetricsController.js';\nimport ManifestParsing from '../utils/ManifestParsing.js';\nimport MetricsReportingEvents from '../MetricsReportingEvents.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction MetricsCollectionController(config) {\n\n    config = config || {};\n    let instance;\n    let metricsControllers = {};\n    let context = this.context;\n    let eventBus = config.eventBus;\n    const events = config.events;\n\n    function update(e) {\n        if (e.error) {\n            return;\n        }\n\n        // start by assuming all existing controllers need removing\n        let controllersToRemove = Object.keys(metricsControllers);\n\n        const metrics = ManifestParsing(context).getInstance({\n            adapter: config.adapter,\n            constants: config.constants\n        }).getMetrics(e.manifest);\n\n        metrics.forEach(m => {\n            const key = JSON.stringify(m);\n\n            if (!metricsControllers.hasOwnProperty(key)) {\n                try {\n                    let controller = MetricsController(context).create(config);\n                    controller.initialize(m);\n                    metricsControllers[key] = controller;\n                } catch (e) {\n                    // fail quietly\n                }\n            } else {\n                // we still need this controller - delete from removal list\n                controllersToRemove.splice(key, 1);\n            }\n        });\n\n        // now remove the unwanted controllers\n        controllersToRemove.forEach(c => {\n            metricsControllers[c].reset();\n            delete metricsControllers[c];\n        });\n\n        eventBus.trigger(MetricsReportingEvents.METRICS_INITIALISATION_COMPLETE);\n    }\n\n    function resetMetricsControllers() {\n        Object.keys(metricsControllers).forEach(key => {\n            metricsControllers[key].reset();\n        });\n\n        metricsControllers = {};\n    }\n\n    function setup() {\n        eventBus.on(events.MANIFEST_UPDATED, update, instance);\n        eventBus.on(events.STREAM_TEARDOWN_COMPLETE, resetMetricsControllers, instance);\n    }\n\n    function reset() {\n        eventBus.off(events.MANIFEST_UPDATED, update, instance);\n        eventBus.off(events.STREAM_TEARDOWN_COMPLETE, resetMetricsControllers, instance);\n    }\n\n    instance = {\n        reset: reset\n    };\n\n    setup();\n    return instance;\n}\n\nMetricsCollectionController.__dashjs_factory_name = 'MetricsCollectionController';\nexport default FactoryMaker.getClassFactory(MetricsCollectionController); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport DVBErrorsTranslator from './utils/DVBErrorsTranslator.js';\nimport MetricsReportingEvents from './MetricsReportingEvents.js';\nimport MetricsCollectionController from './controllers/MetricsCollectionController.js';\nimport MetricsHandlerFactory from './metrics/MetricsHandlerFactory.js';\nimport ReportingFactory from './reporting/ReportingFactory.js';\n\nfunction MetricsReporting() {\n\n    let context = this.context;\n    let instance,\n        dvbErrorsTranslator;\n\n    /**\n     * Create a MetricsCollectionController, and a DVBErrorsTranslator\n     * @param {Object} config - dependancies from owner\n     * @return {MetricsCollectionController} Metrics Collection Controller\n     */\n    function createMetricsReporting(config) {\n        dvbErrorsTranslator = DVBErrorsTranslator(context).getInstance({\n            eventBus: config.eventBus,\n            dashMetrics: config.dashMetrics,\n            metricsConstants: config.metricsConstants,\n            events: config.events\n        });\n        dvbErrorsTranslator.initialize();\n        return MetricsCollectionController(context).create(config);\n    }\n\n    /**\n     * Get the ReportingFactory to allow new reporters to be registered\n     * @return {ReportingFactory} Reporting Factory\n     */\n    function getReportingFactory() {\n        return ReportingFactory(context).getInstance();\n    }\n\n    /**\n     * Get the MetricsHandlerFactory to allow new handlers to be registered\n     * @return {MetricsHandlerFactory} Metrics Handler Factory\n     */\n    function getMetricsHandlerFactory() {\n        return MetricsHandlerFactory(context).getInstance();\n    }\n\n    instance = {\n        createMetricsReporting:     createMetricsReporting,\n        getReportingFactory:        getReportingFactory,\n        getMetricsHandlerFactory:   getMetricsHandlerFactory\n    };\n\n    return instance;\n}\n\nMetricsReporting.__dashjs_factory_name = 'MetricsReporting';\nconst factory = dashjs.FactoryMaker.getClassFactory(MetricsReporting); \nfactory.events = MetricsReportingEvents;\ndashjs.FactoryMaker.updateClassFactory(MetricsReporting.__dashjs_factory_name, factory); \nexport default factory;\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "assertPath", "path", "TypeError", "JSON", "stringify", "normalizeStringPosix", "allowAboveRoot", "code", "res", "lastSegmentLength", "lastSlash", "dots", "i", "length", "charCodeAt", "lastSlashIndex", "lastIndexOf", "slice", "posix", "resolve", "cwd", "<PERSON><PERSON><PERSON>", "resolvedAbsolute", "arguments", "undefined", "process", "normalize", "isAbsolute", "trailingSeparator", "join", "joined", "arg", "relative", "from", "to", "fromStart", "fromEnd", "fromLen", "toStart", "toLen", "lastCommonSep", "fromCode", "out", "_makeLong", "dirname", "hasRoot", "end", "matchedSlash", "basename", "ext", "start", "extIdx", "firstNonSlashEnd", "extname", "startDot", "startPart", "preDotState", "format", "pathObject", "sep", "dir", "base", "name", "_format", "parse", "ret", "delimiter", "win32", "window", "FUNC_TYPE", "UNDEF_TYPE", "OBJ_TYPE", "STR_TYPE", "MAJOR", "MODEL", "NAME", "TYPE", "VENDOR", "VERSION", "ARCHITECTURE", "CONSOLE", "MOBILE", "TABLET", "SMARTTV", "WEARABLE", "EMBEDDED", "AMAZON", "APPLE", "ASUS", "BLACKBERRY", "BROWSER", "CHROME", "FIREFOX", "GOOGLE", "HUAWEI", "LG", "MICROSOFT", "MOTOROLA", "OPERA", "SAMSUNG", "SHARP", "SONY", "XIAOMI", "ZEBRA", "FACEBOOK", "CHROMIUM_OS", "MAC_OS", "enumerize", "arr", "enums", "toUpperCase", "has", "str1", "str2", "lowerize", "indexOf", "str", "toLowerCase", "trim", "len", "replace", "substring", "rgxMapper", "ua", "arrays", "j", "k", "p", "q", "matches", "match", "regex", "props", "exec", "this", "call", "test", "strMapper", "map", "windowsVersionMap", "regexes", "browser", "cpu", "device", "engine", "EDGE", "os", "<PERSON><PERSON><PERSON><PERSON>", "extensions", "getResult", "_navigator", "navigator", "_ua", "userAgent", "_uach", "userAgentData", "_rgxmap", "mergedRegexes", "concat", "extend", "_isSelfNav", "<PERSON><PERSON><PERSON><PERSON>", "version", "_browser", "split", "brave", "isBrave", "getCPU", "_cpu", "getDevice", "_device", "mobile", "standalone", "maxTouchPoints", "getEngine", "_engine", "getOS", "_os", "platform", "getUA", "setUA", "CPU", "DEVICE", "ENGINE", "OS", "$", "j<PERSON><PERSON><PERSON>", "Zepto", "parser", "get", "set", "result", "prop", "Debug", "config", "context", "eventBus", "EventBus", "getInstance", "settings", "logFn", "instance", "showLogTimestamp", "showCalleeName", "startTime", "getLogFn", "fn", "bind", "console", "log", "fatal", "_len", "params", "Array", "_key", "doLog", "error", "_len2", "_key2", "warn", "_len3", "_key3", "info", "_len4", "_key4", "debug", "_len5", "_key5", "level", "_this", "message", "logTime", "Date", "getTime", "getClassName", "getType", "_len6", "_key6", "apply", "for<PERSON>ach", "item", "logLevel", "dispatchEvent", "trigger", "Events", "LOG", "<PERSON><PERSON><PERSON><PERSON>", "setLogTimestampVisible", "value", "setCalleeNameVisible", "__dashjs_factory_name", "FactoryMaker", "getSingletonFactory", "LOG_LEVEL_NONE", "LOG_LEVEL_FATAL", "LOG_LEVEL_ERROR", "LOG_LEVEL_WARNING", "LOG_LEVEL_INFO", "LOG_LEVEL_DEBUG", "updateSingletonFactory", "handlers", "_commonOn", "type", "listener", "scope", "options", "executeOnlyOnce", "Error", "priority", "getHandlerIdx", "handler", "callback", "getStreamId", "streamId", "mediaType", "mode", "some", "idx", "splice", "push", "off", "index", "on", "once", "payload", "filters", "hasOwnProperty", "handlersToRemove", "filter", "MediaPlayerEvents", "EVENT_MODE_ON_RECEIVE", "reset", "EVENT_PRIORITY_LOW", "EVENT_PRIORITY_HIGH", "singletonContexts", "singletonFactories", "classFactories", "getSingletonInstance", "className", "obj", "getFactoryByName", "factoriesArray", "updateFactory", "merge", "classConstructor", "args", "classInstance", "extensionObject", "extension", "override", "parent", "childInstance", "setSingletonInstance", "deleteSingletonInstances", "x", "getSingletonFactoryByName", "getClassFactory", "create", "getClassFactoryByName", "updateClassFactory", "Settings", "DISPATCH_KEY_MAP", "SETTING_UPDATED_LIVE_DELAY", "SETTING_UPDATED_LIVE_DELAY_FRAGMENT_COUNT", "SETTING_UPDATED_CATCHUP_ENABLED", "SETTING_UPDATED_PLAYBACK_RATE_MIN", "SETTING_UPDATED_PLAYBACK_RATE_MAX", "SETTING_UPDATED_ABR_ACTIVE_RULES", "SETTING_UPDATED_MAX_BITRATE", "SETTING_UPDATED_MIN_BITRATE", "defaultSettings", "streaming", "abandonLoadTimeout", "wallclockTimeUpdateInterval", "manifestUpdateRetryInterval", "liveUpdateTimeThresholdInMilliseconds", "cacheInitSegments", "applyServiceDescription", "applyProducerReferenceTime", "applyContentSteering", "enableManifestDurationMismatchFix", "parseInbandPrft", "enableManifestTimescaleMismatchFix", "capabilities", "filterUnsupportedEssentialProperties", "supportedEssentialProperties", "schemeIdUri", "Constants", "FONT_DOWNLOAD_DVB_SCHEME", "COLOUR_PRIMARIES_SCHEME_ID_URI", "URL_QUERY_INFO_SCHEME", "EXT_URL_QUERY_INFO_SCHEME", "MATRIX_COEFFICIENTS_SCHEME_ID_URI", "TRANSFER_CHARACTERISTICS_SCHEME_ID_URI", "THUMBNAILS_SCHEME_ID_URIS", "ep", "useMediaCapabilitiesApi", "filterVideoColorimetryEssentialProperties", "filterHDRMetadataFormatEssentialProperties", "events", "eventControllerRefreshDelay", "deleteEventMessageDataTimeout", "timeShiftBuffer", "calcFromSegmentTimeline", "fallbackToSegmentTimeline", "metrics", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "delay", "liveDelayFragmentCount", "NaN", "liveDelay", "useSuggestedPresentationDelay", "protection", "keepProtectionMediaKeys", "ignoreEmeEncryptedEvent", "detectPlayreadyMessageFormat", "ignoreKeyStatuses", "buffer", "enableSeekDecorrelationFix", "fastSwitchEnabled", "flushBufferAtTrackSwitch", "reuseExistingSourceBuffers", "bufferPruningInterval", "bufferToKeep", "bufferTimeAtTopQuality", "bufferTimeAtTopQualityLongForm", "initialBufferLevel", "bufferTimeDefault", "longFormContentDurationThreshold", "stallThreshold", "lowLatencyStallThreshold", "useAppendWindow", "setStallState", "avoidCurrentTimeRangePruning", "useChangeType", "mediaSourceDurationInfinity", "resetSourceBuffersForTrackSwitch", "syntheticStallEvents", "enabled", "ignoreReadyState", "gaps", "jumpGaps", "jumpLargeGaps", "smallGapLimit", "threshold", "enableSeekFix", "enableStallFix", "stallSeek", "utcSynchronization", "useManifestDateHeaderTimeSource", "backgroundAttempts", "timeBetweenSyncAttempts", "maximumTimeBetweenSyncAttempts", "minimumTimeBetweenSyncAttempts", "timeBetweenSyncAttemptsAdjustmentFactor", "maximumAllowedDrift", "enableBackgroundSyncAfterSegmentDownloadError", "defaultTimingSource", "scheme", "scheduling", "defaultTimeout", "lowLatencyTimeout", "scheduleWhilePaused", "text", "defaultEnabled", "dispatchForManualRendering", "extendSegmentedCues", "imsc", "displayForcedOnlyMode", "enableRollUp", "webvtt", "customRenderingEnabled", "liveCatchup", "maxDrift", "playbackRate", "min", "max", "playbackBufferMin", "LIVE_CATCHUP_MODE_DEFAULT", "lastBitrateCachingInfo", "ttl", "lastMediaSettingsCachingInfo", "saveLastMediaSettingsForCurrentStreamingSession", "cacheLoadThresholds", "video", "audio", "trackSwitchMode", "TRACK_SWITCH_MODE_ALWAYS_REPLACE", "TRACK_SWITCH_MODE_NEVER_REPLACE", "ignoreSelectionPriority", "prioritizeRoleMain", "assumeDefaultRoleAsMain", "selectionModeForInitialTrack", "TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY", "fragmentRequestTimeout", "fragmentRequestProgressTimeout", "manifestRequestTimeout", "retryIntervals", "HTTPRequest", "MPD_TYPE", "XLINK_EXPANSION_TYPE", "MEDIA_SEGMENT_TYPE", "INIT_SEGMENT_TYPE", "BITSTREAM_SWITCHING_SEGMENT_TYPE", "INDEX_SEGMENT_TYPE", "MSS_FRAGMENT_INFO_SEGMENT_TYPE", "LICENSE", "OTHER_TYPE", "lowLatencyReductionFactor", "retryAttempts", "lowLatencyMultiplyFactor", "abr", "limitBitrateByPortal", "usePixelRatioInLimitBitrateByPortal", "enableSupplementalPropertyAdaptationSetSwitching", "rules", "throughputRule", "active", "bolaRule", "insufficientBufferRule", "parameters", "throughputSafetyFactor", "segmentIgnoreCount", "switchHistoryRule", "sampleSize", "switchPercentageThreshold", "droppedFramesRule", "minimumSampleSize", "droppedFramesPercentageThreshold", "abandonRequestsRule", "abandonDurationMultiplier", "minSegmentDownloadTimeThresholdInMs", "minThroughputSamplesThreshold", "l2ARule", "loLPRule", "throughput", "averageCalculationMode", "THROUGHPUT_CALCULATION_MODES", "EWMA", "lowLatencyDownloadTimeCalculationMode", "LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE", "MOOF_PARSING", "useResourceTimingApi", "useNetworkInformationApi", "xhr", "fetch", "useDeadTimeLatency", "bandwidthSafetyFactor", "sampleSettings", "live", "vod", "enableSampleSizeAdjustment", "decreaseScale", "increaseScale", "maxMeasurementsToKeep", "averageLatencySampleAmount", "ewma", "throughputSlowHalfLifeSeconds", "throughputFastHalfLifeSeconds", "latencySlowHalfLifeCount", "latencyFastHalfLifeCount", "weightDownloadTimeMultiplicationFactor", "maxBitrate", "minBitrate", "initialBitrate", "autoSwitchBitrate", "cmcd", "applyParametersFromMpd", "sid", "cid", "rtp", "rtpSafetyFactor", "CMCD_MODE_QUERY", "<PERSON><PERSON><PERSON><PERSON>", "CMCD_AVAILABLE_KEYS", "includeInRequests", "cmsd", "applyMb", "etpWeightRatio", "defaultSchemeIdUri", "viewpoint", "audioChannelConfiguration", "role", "accessibility", "errors", "recoverAttempts", "mediaErrorDecode", "Utils", "clone", "mixinSettings", "source", "dest", "n", "RegExp", "update", "settingsObj", "mixin", "copy", "s", "empty", "src", "r", "l", "addAdditionalQueryParameterToUrl", "url", "updatedUrl", "_ref", "key", "separator", "includes", "encodeURIComponent", "e", "removeQueryParameterFromUrl", "queryParameter", "parsedUrl", "URL", "URLSearchParams", "search", "size", "delete", "queryString", "entries", "_ref2", "baseUrl", "origin", "pathname", "parseHttpHeaders", "headerStr", "headers", "headerPairs", "ilen", "headerPair", "parseQueryParams", "queryParamString", "searchParams", "decodeURIComponent", "generateUuid", "dt", "c", "Math", "random", "floor", "toString", "generateHashCode", "string", "hash", "getRelativeUrl", "originalUrl", "targetUrl", "original", "target", "protocol", "relativePath", "substr", "startIndexOffset", "getHostFromUrl", "urlString", "host", "parseUserAgent", "uaString", "stringHasProtocol", "bufferSourceToDataView", "bufferSource", "toDataView", "DataView", "bufferSourceToInt8", "Uint8Array", "uint8ArrayToString", "uint8Array", "TextDecoder", "decode", "bufferSourceToHex", "data", "hex", "Type", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bytesPerElement", "BYTES_PER_ELEMENT", "dataEnd", "byteOffset", "byteLength", "rawStart", "Infinity", "view", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getCodecFamily", "codecString", "profile", "_getCodecParts", "CODEC_FAMILIES", "MP3", "AAC", "AC3", "EC3", "DTSX", "DTSC", "AVC", "HEVC", "rest", "CoreEvents", "EventsBase", "constructor", "super", "ATTEMPT_BACKGROUND_SYNC", "BUFFERING_COMPLETED", "BUFFER_CLEARED", "BYTES_APPENDED_END_FRAGMENT", "BUFFER_REPLACEMENT_STARTED", "CHECK_FOR_EXISTENCE_COMPLETED", "CMSD_STATIC_HEADER", "CURRENT_TRACK_CHANGED", "DATA_UPDATE_COMPLETED", "INBAND_EVENTS", "INITIAL_STREAM_SWITCH", "INIT_FRAGMENT_LOADED", "INIT_FRAGMENT_NEEDED", "INTERNAL_MANIFEST_LOADED", "ORIGINAL_MANIFEST_LOADED", "LOADING_COMPLETED", "LOADING_PROGRESS", "LOADING_DATA_PROGRESS", "LOADING_ABANDONED", "MANIFEST_UPDATED", "MEDIA_FRAGMENT_LOADED", "MEDIA_FRAGMENT_NEEDED", "MEDIAINFO_UPDATED", "QUOTA_EXCEEDED", "SEGMENT_LOCATION_BLACKLIST_ADD", "SEGMENT_LOCATION_BLACKLIST_CHANGED", "SERVICE_LOCATION_BASE_URL_BLACKLIST_ADD", "SERVICE_LOCATION_BASE_URL_BLACKLIST_CHANGED", "SERVICE_LOCATION_LOCATION_BLACKLIST_ADD", "SERVICE_LOCATION_LOCATION_BLACKLIST_CHANGED", "SET_FRAGMENTED_TEXT_AFTER_DISABLED", "SET_NON_FRAGMENTED_TEXT", "SOURCE_BUFFER_ERROR", "STREAMS_COMPOSED", "STREAM_BUFFERING_COMPLETED", "STREAM_REQUESTING_COMPLETED", "TEXT_TRACKS_QUEUE_INITIALIZED", "TIME_SYNCHRONIZATION_COMPLETED", "UPDATE_TIME_SYNC_OFFSET", "URL_RESOLUTION_FAILED", "VIDEO_CHUNK_RECEIVED", "WALLCLOCK_TIME_UPDATED", "XLINK_ELEMENT_LOADED", "XLINK_READY", "SEEK_TARGET", "publicOnly", "evt", "AST_IN_FUTURE", "BASE_URLS_UPDATED", "BUFFER_EMPTY", "BUFFER_LOADED", "BUFFER_LEVEL_STATE_CHANGED", "BUFFER_LEVEL_UPDATED", "DVB_FONT_DOWNLOAD_ADDED", "DVB_FONT_DOWNLOAD_COMPLETE", "DVB_FONT_DOWNLOAD_FAILED", "DYNAMIC_TO_STATIC", "ERROR", "FRAGMENT_LOADING_COMPLETED", "FRAGMENT_LOADING_PROGRESS", "FRAGMENT_LOADING_STARTED", "FRAGMENT_LOADING_ABANDONED", "MANIFEST_LOADING_STARTED", "MANIFEST_LOADING_FINISHED", "MANIFEST_LOADED", "METRICS_CHANGED", "METRIC_CHANGED", "METRIC_ADDED", "METRIC_UPDATED", "PERIOD_SWITCH_STARTED", "PERIOD_SWITCH_COMPLETED", "QUALITY_CHANGE_REQUESTED", "QUALITY_CHANGE_RENDERED", "NEW_TRACK_SELECTED", "TRACK_CHANGE_RENDERED", "STREAM_INITIALIZING", "STREAM_UPDATED", "STREAM_ACTIVATED", "STREAM_DEACTIVATED", "STREAM_INITIALIZED", "STREAM_TEARDOWN_COMPLETE", "TEXT_TRACKS_ADDED", "TEXT_TRACK_ADDED", "CUE_ENTER", "CUE_EXIT", "THROUGHPUT_MEASUREMENT_STORED", "TTML_PARSED", "TTML_TO_PARSE", "CAPTION_RENDERED", "CAPTION_CONTAINER_RESIZE", "CAN_PLAY", "CAN_PLAY_THROUGH", "PLAYBACK_ENDED", "PLAYBACK_ERROR", "PLAYBACK_INITIALIZED", "PLAYBACK_NOT_ALLOWED", "PLAYBACK_METADATA_LOADED", "PLAYBACK_LOADED_DATA", "PLAYBACK_PAUSED", "PLAYBACK_PLAYING", "PLAYBACK_PROGRESS", "PLAYBACK_RATE_CHANGED", "PLAYBACK_SEEKED", "PLAYBACK_SEEKING", "PLAYBACK_STALLED", "PLAYBACK_STARTED", "PLAYBACK_TIME_UPDATED", "PLAYBACK_VOLUME_CHANGED", "PLAYBACK_WAITING", "MANIFEST_VALIDITY_CHANGED", "EVENT_MODE_ON_START", "CONFORMANCE_VIOLATION", "REPRESENTATION_SWITCH", "ADAPTATION_SET_REMOVED_NO_CAPABILITIES", "CONTENT_STEERING_REQUEST_COMPLETED", "INBAND_PRFT", "MANAGED_MEDIA_SOURCE_START_STREAMING", "MANAGED_MEDIA_SOURCE_END_STREAMING", "mediaPlayerEvents", "STREAM", "VIDEO", "AUDIO", "TEXT", "MUXED", "IMAGE", "STPP", "TTML", "VTT", "WVTT", "CONTENT_STEERING", "LIVE_CATCHUP_MODE_LOLP", "MOVING_AVERAGE_SLIDING_WINDOW", "MOVING_AVERAGE_EWMA", "BAD_ARGUMENT_ERROR", "MISSING_CONFIG_ERROR", "TRACK_SELECTION_MODE_FIRST_TRACK", "TRACK_SELECTION_MODE_HIGHEST_BITRATE", "TRACK_SELECTION_MODE_WIDEST_RANGE", "CMCD_QUERY_KEY", "CMCD_MODE_HEADER", "CMCD_V2_AVAILABLE_KEYS", "CMCD_AVAILABLE_REQUESTS", "INITIALIZE", "TEXT_SHOWING", "TEXT_HIDDEN", "TEXT_DISABLED", "ACCESSIBILITY_CEA608_SCHEME", "CC1", "CC3", "UTF8", "SCHEME_ID_URI", "START_TIME", "SERVICE_DESCRIPTION_DVB_LL_SCHEME", "SUPPLEMENTAL_PROPERTY_DVB_LL_SCHEME", "CTA_5004_2023_SCHEME", "HDR_METADATA_FORMAT_SCHEME_ID_URI", "HDR_METADATA_FORMAT_VALUES", "ST2094_10", "SL_HDR2", "ST2094_40", "MEDIA_CAPABILITIES_API", "COLORGAMUT", "SRGB", "P3", "REC2020", "TRANSFERFUNCTION", "PQ", "HLG", "HDR_METADATATYPE", "SMPTE_ST_2094_10", "SLHDR2", "SMPTE_ST_2094_40", "XML", "ARRAY_BUFFER", "DVB_REPORTING_URL", "DVB_PROBABILITY", "OFF_MIMETYPE", "WOFF_MIMETYPE", "VIDEO_ELEMENT_READY_STATES", "HAVE_NOTHING", "HAVE_METADATA", "HAVE_CURRENT_DATA", "HAVE_FUTURE_DATA", "HAVE_ENOUGH_DATA", "FILE_LOADER_TYPES", "FETCH", "XHR", "THROUGHPUT_TYPES", "LATENCY", "BANDWIDTH", "ZLEMA", "ARITHMETIC_MEAN", "BYTE_SIZE_WEIGHTED_ARITHMETIC_MEAN", "DATE_WEIGHTED_ARITHMETIC_MEAN", "HARMONIC_MEAN", "BYTE_SIZE_WEIGHTED_HARMONIC_MEAN", "DATE_WEIGHTED_HARMONIC_MEAN", "DOWNLOADED_DATA", "AAST", "RULES_TYPES", "QUALITY_SWITCH_RULES", "ABANDON_FRAGMENT_RULES", "BOLA_RULE", "THROUGHPUT_RULE", "INSUFFICIENT_BUFFER_RULE", "SWITCH_HISTORY_RULE", "DROPPED_FRAMES_RULE", "LEARN_TO_ADAPT_RULE", "LOL_PLUS_RULE", "ABANDON_REQUEST_RULE", "ID3_SCHEME_ID_URI", "COMMON_ACCESS_TOKEN_HEADER", "DASH_ROLE_SCHEME_ID", "MetricsReportingEvents", "METRICS_INITIALISATION_COMPLETE", "BECAME_REPORTING_PLAYER", "CMCD_DATA_GENERATED", "metricsReportingEvents", "CustomParametersModel", "utcTimingSources", "xhrWithCredentials", "requestInterceptors", "responseInterceptors", "licenseRequestFilters", "licenseResponseFilters", "customCapabilitiesFilters", "customInitialTrackSelectionFunction", "customAbrRules", "_resetInitialSettings", "_unregisterFilter", "_findAbrCustomRuleIndex", "rulename", "addUTCTimingSource", "removeUTCTimingSource", "vo", "UTCTiming", "checkParameterType", "addAbrCustomRule", "rule", "addRequestInterceptor", "interceptor", "addResponseInterceptor", "clearDefaultUTCTimingSources", "getAbrCustomRules", "getCustomCapabilitiesFilters", "getCustomInitialTrackSelectionFunction", "getLicenseRequestFilters", "getLicenseResponseFilters", "getRequestInterceptors", "getResponseInterceptors", "getUTCTimingSources", "getXHRWithCredentialsForType", "useCreds", "default", "registerCustomCapabilitiesFilter", "registerLicenseRequestFilter", "registerLicenseResponseFilter", "removeAbrCustomRule", "removeAllAbrCustomRule", "removeRequestInterceptor", "removeResponseInterceptor", "resetCustomInitialTrackSelectionFunction", "restoreDefaultUTCTimingSources", "defaultUtcTimingSource", "setConfig", "setCustomInitialTrackSelectionFunction", "customFunc", "setXHRWithCredentialsForType", "Object", "keys", "unregisterCustomCapabilitiesFilter", "unregisterLicenseRequestFilter", "unregisterLicenseResponseFilter", "CustomTimeRanges", "customTimeRangeArray", "add", "mergeRanges", "clear", "remove", "rangeIndex1", "rangeIndex2", "range1", "range2", "checkInteger", "parameter", "isNaN", "tcpid", "<PERSON><PERSON><PERSON>", "range", "trequest", "tresponse", "responsecode", "interval", "trace", "_stream", "_tfinish", "_mediaduration", "_responseHeaders", "_serviceLocation", "_fileLoaderType", "_resourceTimingValues", "GET", "HEAD", "DVB_REPORTING_TYPE", "CONTENT_STEERING_TYPE", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "amdO", "d", "definition", "o", "defineProperty", "enumerable", "prototype", "DVBErrors", "mpdurl", "errorcode", "terror", "ipaddress", "servicelocation", "SSL_CONNECTION_FAILED_PREFIX", "DNS_RESOLUTION_FAILED", "HOST_UNREACHABLE", "CONNECTION_REFUSED", "CONNECTION_ERROR", "CORRUPT_MEDIA_ISOBMFF", "CORRUPT_MEDIA_OTHER", "BASE_URL_CHANGED", "BECAME_REPORTER", "DVBErrorsTranslator", "mpd", "dashMetrics", "metricsConstants", "report", "addDVBErrors", "onManifestUpdate", "manifest", "onServiceLocationChanged", "entry", "onBecameReporter", "onMetricEvent", "metric", "HTTP_REQUEST", "onPlaybackError", "MediaError", "MEDIA_ERR_NETWORK", "MEDIA_ERR_DECODE", "initialize", "RangeController", "ranges", "useWallClockTime", "mediaElement", "rs", "starttime", "duration", "_useWallClockTime", "isEnabled", "time", "numRanges", "currentTime", "MetricSerialiser", "serialise", "pairs", "isArray", "v", "isBuiltIn", "toISOString", "round", "RNG", "randomNumbers", "crypto", "msCrypto", "ArrayType", "Uint32Array", "MAX_VALUE", "pow", "getRandomValues", "DVBReporting", "metricSerialiser", "customParametersModel", "randomNumberGenerator", "reportingPlayerStatusDecided", "isReportingPlayer", "reportingUrl", "rangeController", "pendingRequests", "resetInitialSettings", "vos", "DVB_ERRORS", "successCB", "failureCB", "req", "XMLHttpRequest", "withCredentials", "HTTP_REQUEST_DVB_REPORTING_TYPE", "oncomplete", "reqIndex", "status", "open", "onloadend", "onerror", "send", "doGetRequest", "rc", "probability", "dvbReportingUrl", "dvbProbability", "ReportingFactory", "knownReportingSchemeIdUris", "logger", "mediaPlayerModel", "reporting", "register", "moduleName", "unregister", "ReportingController", "reporters", "reportingFactory", "reporter", "HandlerHelpers", "reconstructFullMetricName", "mn", "validateN", "n_ms", "BufferLevelHandler", "reportingController", "lastReportedTime", "handlerHelpers", "storedVOs", "intervalCallback", "reduce", "a", "b", "getLowestBufferLevelVO", "t", "setInterval", "clearInterval", "handleNewMetric", "BUFFER_LEVEL", "onInitialisationComplete", "unused", "HttpListHandler", "storedVos", "requestType", "GenericMetricHandler", "metricName", "MetricsHandlerFactory", "keyRegex", "knownFactoryProducts", "BufferLevel", "HttpList", "PlayList", "RepSwitchList", "TcpList", "listType", "MetricsHandlersController", "metricsHandlerFactory", "handle", "m", "midx", "ms", "nextm", "MetricsController", "metricsHandlersController", "metricsEntry", "Range", "Reporting", "ManifestParsing", "adapter", "constants", "getMetrics", "Metrics", "metricEntry", "isDynamic", "getIsDynamic", "rangeEntry", "dynamic", "voPeriods", "reportingStartTime", "presentationStartTime", "getAvailabilityStartTime", "getRegularPeriods", "getMetricsRangeStartTime", "getDuration", "reportingEntry", "MetricsCollectionController", "metricsControllers", "controllersToRemove", "controller", "resetMetricsControllers", "MetricsReporting", "dvbErrorsTranslator", "createMetricsReporting", "getReportingFactory", "getMetricsHandlerFactory", "dashjs"], "sourceRoot": ""}