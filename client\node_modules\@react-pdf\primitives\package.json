{"name": "@react-pdf/primitives", "version": "4.1.1", "license": "MIT", "description": "Define uninitialized elements", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/diegomura/react-pdf#readme", "type": "module", "main": "./lib/index.js", "types": "./lib/index.d.ts", "repository": {"type": "git", "url": "https://github.com/diegomura/react-pdf.git", "directory": "packages/primitives"}, "scripts": {"build": "rimraf ./lib && rollup -c", "watch": "rimraf ./lib && rollup -c -w", "test": "vitest"}, "files": ["lib"]}