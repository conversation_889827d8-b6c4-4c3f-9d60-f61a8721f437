{"version": 3, "file": "dash.protection.min.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAgB,OAAID,IAEpBD,EAAa,OAAIC,GAClB,CATD,CASGK,MAAM,WACT,O,gDCgBA,SAASC,EAAWC,GAClB,GAAoB,iBAATA,EACT,MAAM,IAAIC,UAAU,mCAAqCC,KAAKC,UAAUH,GAE5E,CAGA,SAASI,EAAqBJ,EAAMK,GAMlC,IALA,IAIIC,EAJAC,EAAM,GACNC,EAAoB,EACpBC,GAAa,EACbC,EAAO,EAEFC,EAAI,EAAGA,GAAKX,EAAKY,SAAUD,EAAG,CACrC,GAAIA,EAAIX,EAAKY,OACXN,EAAON,EAAKa,WAAWF,OACpB,IAAa,KAATL,EACP,MAEAA,EAAO,EAAQ,CACjB,GAAa,KAATA,EAAmB,CACrB,GAAIG,IAAcE,EAAI,GAAc,IAATD,QAEpB,GAAID,IAAcE,EAAI,GAAc,IAATD,EAAY,CAC5C,GAAIH,EAAIK,OAAS,GAA2B,IAAtBJ,GAA8D,KAAnCD,EAAIM,WAAWN,EAAIK,OAAS,IAAsD,KAAnCL,EAAIM,WAAWN,EAAIK,OAAS,GAC1H,GAAIL,EAAIK,OAAS,EAAG,CAClB,IAAIE,EAAiBP,EAAIQ,YAAY,KACrC,GAAID,IAAmBP,EAAIK,OAAS,EAAG,EACb,IAApBE,GACFP,EAAM,GACNC,EAAoB,GAGpBA,GADAD,EAAMA,EAAIS,MAAM,EAAGF,IACKF,OAAS,EAAIL,EAAIQ,YAAY,KAEvDN,EAAYE,EACZD,EAAO,EACP,QACF,CACF,MAAO,GAAmB,IAAfH,EAAIK,QAA+B,IAAfL,EAAIK,OAAc,CAC/CL,EAAM,GACNC,EAAoB,EACpBC,EAAYE,EACZD,EAAO,EACP,QACF,CAEEL,IACEE,EAAIK,OAAS,EACfL,GAAO,MAEPA,EAAM,KACRC,EAAoB,EAExB,MACMD,EAAIK,OAAS,EACfL,GAAO,IAAMP,EAAKgB,MAAMP,EAAY,EAAGE,GAEvCJ,EAAMP,EAAKgB,MAAMP,EAAY,EAAGE,GAClCH,EAAoBG,EAAIF,EAAY,EAEtCA,EAAYE,EACZD,EAAO,CACT,MAAoB,KAATJ,IAA+B,IAAVI,IAC5BA,EAEFA,GAAQ,CAEZ,CACA,OAAOH,CACT,CAcA,IAAIU,EAAQ,CAEVC,QAAS,WAKP,IAJA,IAEIC,EAFAC,EAAe,GACfC,GAAmB,EAGdV,EAAIW,UAAUV,OAAS,EAAGD,IAAM,IAAMU,EAAkBV,IAAK,CACpE,IAAIX,EACAW,GAAK,EACPX,EAAOsB,UAAUX,SAELY,IAARJ,IACFA,EAAMK,QAAQL,OAChBnB,EAAOmB,GAGTpB,EAAWC,GAGS,IAAhBA,EAAKY,SAITQ,EAAepB,EAAO,IAAMoB,EAC5BC,EAA0C,KAAvBrB,EAAKa,WAAW,GACrC,CAQA,OAFAO,EAAehB,EAAqBgB,GAAeC,GAE/CA,EACED,EAAaR,OAAS,EACjB,IAAMQ,EAEN,IACAA,EAAaR,OAAS,EACxBQ,EAEA,GAEX,EAEAK,UAAW,SAAmBzB,GAG5B,GAFAD,EAAWC,GAES,IAAhBA,EAAKY,OAAc,MAAO,IAE9B,IAAIc,EAAoC,KAAvB1B,EAAKa,WAAW,GAC7Bc,EAAyD,KAArC3B,EAAKa,WAAWb,EAAKY,OAAS,GAQtD,OAHoB,KAFpBZ,EAAOI,EAAqBJ,GAAO0B,IAE1Bd,QAAiBc,IAAY1B,EAAO,KACzCA,EAAKY,OAAS,GAAKe,IAAmB3B,GAAQ,KAE9C0B,EAAmB,IAAM1B,EACtBA,CACT,EAEA0B,WAAY,SAAoB1B,GAE9B,OADAD,EAAWC,GACJA,EAAKY,OAAS,GAA4B,KAAvBZ,EAAKa,WAAW,EAC5C,EAEAe,KAAM,WACJ,GAAyB,IAArBN,UAAUV,OACZ,MAAO,IAET,IADA,IAAIiB,EACKlB,EAAI,EAAGA,EAAIW,UAAUV,SAAUD,EAAG,CACzC,IAAImB,EAAMR,UAAUX,GACpBZ,EAAW+B,GACPA,EAAIlB,OAAS,SACAW,IAAXM,EACFA,EAASC,EAETD,GAAU,IAAMC,EAEtB,CACA,YAAeP,IAAXM,EACK,IACFZ,EAAMQ,UAAUI,EACzB,EAEAE,SAAU,SAAkBC,EAAMC,GAIhC,GAHAlC,EAAWiC,GACXjC,EAAWkC,GAEPD,IAASC,EAAI,MAAO,GAKxB,IAHAD,EAAOf,EAAMC,QAAQc,OACrBC,EAAKhB,EAAMC,QAAQe,IAEF,MAAO,GAIxB,IADA,IAAIC,EAAY,EACTA,EAAYF,EAAKpB,QACa,KAA/BoB,EAAKnB,WAAWqB,KADYA,GASlC,IALA,IAAIC,EAAUH,EAAKpB,OACfwB,EAAUD,EAAUD,EAGpBG,EAAU,EACPA,EAAUJ,EAAGrB,QACa,KAA3BqB,EAAGpB,WAAWwB,KADUA,GAW9B,IAPA,IACIC,EADQL,EAAGrB,OACKyB,EAGhBzB,EAASwB,EAAUE,EAAQF,EAAUE,EACrCC,GAAiB,EACjB5B,EAAI,EACDA,GAAKC,IAAUD,EAAG,CACvB,GAAIA,IAAMC,EAAQ,CAChB,GAAI0B,EAAQ1B,EAAQ,CAClB,GAAmC,KAA/BqB,EAAGpB,WAAWwB,EAAU1B,GAG1B,OAAOsB,EAAGjB,MAAMqB,EAAU1B,EAAI,GACzB,GAAU,IAANA,EAGT,OAAOsB,EAAGjB,MAAMqB,EAAU1B,EAE9B,MAAWyB,EAAUxB,IACoB,KAAnCoB,EAAKnB,WAAWqB,EAAYvB,GAG9B4B,EAAgB5B,EACD,IAANA,IAGT4B,EAAgB,IAGpB,KACF,CACA,IAAIC,EAAWR,EAAKnB,WAAWqB,EAAYvB,GAE3C,GAAI6B,IADSP,EAAGpB,WAAWwB,EAAU1B,GAEnC,MACoB,KAAb6B,IACPD,EAAgB5B,EACpB,CAEA,IAAI8B,EAAM,GAGV,IAAK9B,EAAIuB,EAAYK,EAAgB,EAAG5B,GAAKwB,IAAWxB,EAClDA,IAAMwB,GAAkC,KAAvBH,EAAKnB,WAAWF,KAChB,IAAf8B,EAAI7B,OACN6B,GAAO,KAEPA,GAAO,OAMb,OAAIA,EAAI7B,OAAS,EACR6B,EAAMR,EAAGjB,MAAMqB,EAAUE,IAEhCF,GAAWE,EACoB,KAA3BN,EAAGpB,WAAWwB,MACdA,EACGJ,EAAGjB,MAAMqB,GAEpB,EAEAK,UAAW,SAAmB1C,GAC5B,OAAOA,CACT,EAEA2C,QAAS,SAAiB3C,GAExB,GADAD,EAAWC,GACS,IAAhBA,EAAKY,OAAc,MAAO,IAK9B,IAJA,IAAIN,EAAON,EAAKa,WAAW,GACvB+B,EAAmB,KAATtC,EACVuC,GAAO,EACPC,GAAe,EACVnC,EAAIX,EAAKY,OAAS,EAAGD,GAAK,IAAKA,EAEtC,GAAa,MADbL,EAAON,EAAKa,WAAWF,KAEnB,IAAKmC,EAAc,CACjBD,EAAMlC,EACN,KACF,OAGFmC,GAAe,EAInB,OAAa,IAATD,EAAmBD,EAAU,IAAM,IACnCA,GAAmB,IAARC,EAAkB,KAC1B7C,EAAKgB,MAAM,EAAG6B,EACvB,EAEAE,SAAU,SAAkB/C,EAAMgD,GAChC,QAAYzB,IAARyB,GAAoC,iBAARA,EAAkB,MAAM,IAAI/C,UAAU,mCACtEF,EAAWC,GAEX,IAGIW,EAHAsC,EAAQ,EACRJ,GAAO,EACPC,GAAe,EAGnB,QAAYvB,IAARyB,GAAqBA,EAAIpC,OAAS,GAAKoC,EAAIpC,QAAUZ,EAAKY,OAAQ,CACpE,GAAIoC,EAAIpC,SAAWZ,EAAKY,QAAUoC,IAAQhD,EAAM,MAAO,GACvD,IAAIkD,EAASF,EAAIpC,OAAS,EACtBuC,GAAoB,EACxB,IAAKxC,EAAIX,EAAKY,OAAS,EAAGD,GAAK,IAAKA,EAAG,CACrC,IAAIL,EAAON,EAAKa,WAAWF,GAC3B,GAAa,KAATL,GAGA,IAAKwC,EAAc,CACjBG,EAAQtC,EAAI,EACZ,KACF,OAEwB,IAAtBwC,IAGFL,GAAe,EACfK,EAAmBxC,EAAI,GAErBuC,GAAU,IAER5C,IAAS0C,EAAInC,WAAWqC,IACR,KAAZA,IAGJL,EAAMlC,IAKRuC,GAAU,EACVL,EAAMM,GAId,CAGA,OADIF,IAAUJ,EAAKA,EAAMM,GAAmC,IAATN,IAAYA,EAAM7C,EAAKY,QACnEZ,EAAKgB,MAAMiC,EAAOJ,EAC3B,CACE,IAAKlC,EAAIX,EAAKY,OAAS,EAAGD,GAAK,IAAKA,EAClC,GAA2B,KAAvBX,EAAKa,WAAWF,IAGhB,IAAKmC,EAAc,CACjBG,EAAQtC,EAAI,EACZ,KACF,OACkB,IAATkC,IAGXC,GAAe,EACfD,EAAMlC,EAAI,GAId,OAAa,IAATkC,EAAmB,GAChB7C,EAAKgB,MAAMiC,EAAOJ,EAE7B,EAEAO,QAAS,SAAiBpD,GACxBD,EAAWC,GAQX,IAPA,IAAIqD,GAAY,EACZC,EAAY,EACZT,GAAO,EACPC,GAAe,EAGfS,EAAc,EACT5C,EAAIX,EAAKY,OAAS,EAAGD,GAAK,IAAKA,EAAG,CACzC,IAAIL,EAAON,EAAKa,WAAWF,GAC3B,GAAa,KAATL,GASS,IAATuC,IAGFC,GAAe,EACfD,EAAMlC,EAAI,GAEC,KAATL,GAEkB,IAAd+C,EACFA,EAAW1C,EACY,IAAhB4C,IACPA,EAAc,IACK,IAAdF,IAGTE,GAAe,QArBb,IAAKT,EAAc,CACjBQ,EAAY3C,EAAI,EAChB,KACF,CAoBN,CAEA,OAAkB,IAAd0C,IAA4B,IAATR,GAEH,IAAhBU,GAEgB,IAAhBA,GAAqBF,IAAaR,EAAM,GAAKQ,IAAaC,EAAY,EACjE,GAEFtD,EAAKgB,MAAMqC,EAAUR,EAC9B,EAEAW,OAAQ,SAAgBC,GACtB,GAAmB,OAAfA,GAA6C,iBAAfA,EAChC,MAAM,IAAIxD,UAAU,0EAA4EwD,GAElG,OAvVJ,SAAiBC,EAAKD,GACpB,IAAIE,EAAMF,EAAWE,KAAOF,EAAWjE,KACnCoE,EAAOH,EAAWG,OAASH,EAAWI,MAAQ,KAAOJ,EAAWT,KAAO,IAC3E,OAAKW,EAGDA,IAAQF,EAAWjE,KACdmE,EAAMC,EAERD,EA8UU,IA9UEC,EALVA,CAMX,CA6UWE,CAAQ,EAAKL,EACtB,EAEAM,MAAO,SAAe/D,GACpBD,EAAWC,GAEX,IAAIgE,EAAM,CAAExE,KAAM,GAAImE,IAAK,GAAIC,KAAM,GAAIZ,IAAK,GAAIa,KAAM,IACxD,GAAoB,IAAhB7D,EAAKY,OAAc,OAAOoD,EAC9B,IAEIf,EAFA3C,EAAON,EAAKa,WAAW,GACvBa,EAAsB,KAATpB,EAEboB,GACFsC,EAAIxE,KAAO,IACXyD,EAAQ,GAERA,EAAQ,EAaV,IAXA,IAAII,GAAY,EACZC,EAAY,EACZT,GAAO,EACPC,GAAe,EACfnC,EAAIX,EAAKY,OAAS,EAIlB2C,EAAc,EAGX5C,GAAKsC,IAAStC,EAEnB,GAAa,MADbL,EAAON,EAAKa,WAAWF,KAUV,IAATkC,IAGFC,GAAe,EACfD,EAAMlC,EAAI,GAEC,KAATL,GAEkB,IAAd+C,EAAiBA,EAAW1C,EAA2B,IAAhB4C,IAAmBA,EAAc,IACrD,IAAdF,IAGXE,GAAe,QAlBb,IAAKT,EAAc,CACjBQ,EAAY3C,EAAI,EAChB,KACF,CAwCN,OArBkB,IAAd0C,IAA4B,IAATR,GAEP,IAAhBU,GAEgB,IAAhBA,GAAqBF,IAAaR,EAAM,GAAKQ,IAAaC,EAAY,GACvD,IAATT,IACiCmB,EAAIJ,KAAOI,EAAIH,KAAhC,IAAdP,GAAmB5B,EAAkC1B,EAAKgB,MAAM,EAAG6B,GAAgC7C,EAAKgB,MAAMsC,EAAWT,KAG7G,IAAdS,GAAmB5B,GACrBsC,EAAIH,KAAO7D,EAAKgB,MAAM,EAAGqC,GACzBW,EAAIJ,KAAO5D,EAAKgB,MAAM,EAAG6B,KAEzBmB,EAAIH,KAAO7D,EAAKgB,MAAMsC,EAAWD,GACjCW,EAAIJ,KAAO5D,EAAKgB,MAAMsC,EAAWT,IAEnCmB,EAAIhB,IAAMhD,EAAKgB,MAAMqC,EAAUR,IAG7BS,EAAY,EAAGU,EAAIL,IAAM3D,EAAKgB,MAAM,EAAGsC,EAAY,GAAY5B,IAAYsC,EAAIL,IAAM,KAElFK,CACT,EAEAN,IAAK,IACLO,UAAW,IACXC,MAAO,KACPjD,MAAO,MAGTA,EAAMA,MAAQA,EAEdtB,EAAOD,QAAUuB,C,uBChhBjB,OAUA,SAAWkD,EAAQ5C,GAEf,aAOA,IAGI6C,EAAc,WACdC,EAAc,YACdC,EAAc,SACdC,EAAc,SACdC,EAAc,QACdC,EAAc,QACdC,EAAc,OACdC,EAAc,OACdC,EAAc,SACdC,EAAc,UACdC,EAAc,eACdC,EAAc,UACdC,EAAc,SACdC,EAAc,SACdC,EAAc,UACdC,EAAc,WACdC,EAAc,WAGdC,EAAU,SACVC,EAAU,QACVC,EAAU,OACVC,EAAa,aACbC,EAAU,UACVC,EAAU,SAEVC,EAAU,UACVC,EAAU,SACVC,EAAU,SACVC,EAAU,KACVC,EAAY,YACZC,EAAY,WACZC,EAAU,QACVC,EAAU,UACVC,EAAU,QACVC,EAAU,OACVC,EAAU,SACVC,EAAU,QACVC,EAAc,WACdC,EAAc,cACdC,EAAU,SAiBVC,EAAY,SAAUC,GAElB,IADA,IAAIC,EAAQ,CAAC,EACJjG,EAAE,EAAGA,EAAEgG,EAAI/F,OAAQD,IACxBiG,EAAMD,EAAIhG,GAAGkG,eAAiBF,EAAIhG,GAEtC,OAAOiG,CACX,EACAE,EAAM,SAAUC,EAAMC,GAClB,cAAcD,IAASxC,IAAuD,IAA5C0C,EAASD,GAAME,QAAQD,EAASF,GACtE,EACAE,EAAW,SAAUE,GACjB,OAAOA,EAAIC,aACf,EAIAC,EAAO,SAAUF,EAAKG,GAClB,UAAWH,IAAS5C,EAEhB,OADA4C,EAAMA,EAAII,QAAQ,SA7EZ,WA8EQD,IAASjD,EAAa8C,EAAMA,EAAIK,UAAU,EA3DhD,IA6DpB,EAMIC,EAAY,SAAUC,EAAIC,GAKtB,IAHA,IAAWC,EAAGC,EAAGC,EAAGC,EAAGC,EAASC,EAA5BtH,EAAI,EAGDA,EAAIgH,EAAO/G,SAAWoH,GAAS,CAElC,IAAIE,EAAQP,EAAOhH,GACfwH,EAAQR,EAAOhH,EAAI,GAIvB,IAHAiH,EAAIC,EAAI,EAGDD,EAAIM,EAAMtH,SAAWoH,GAEnBE,EAAMN,IAGX,GAFAI,EAAUE,EAAMN,KAAKQ,KAAKV,GAGtB,IAAKI,EAAI,EAAGA,EAAIK,EAAMvH,OAAQkH,IAC1BG,EAAQD,IAAUH,UAClBE,EAAII,EAAML,MAEOxD,GAAYyD,EAAEnH,OAAS,EACnB,IAAbmH,EAAEnH,cACSmH,EAAE,IAAM3D,EAEfiE,KAAKN,EAAE,IAAMA,EAAE,GAAGO,KAAKD,KAAMJ,GAG7BI,KAAKN,EAAE,IAAMA,EAAE,GAEC,IAAbA,EAAEnH,cAEEmH,EAAE,KAAO3D,GAAe2D,EAAE,GAAGK,MAAQL,EAAE,GAAGQ,KAKjDF,KAAKN,EAAE,IAAME,EAAQA,EAAMV,QAAQQ,EAAE,GAAIA,EAAE,IAAMxG,EAHjD8G,KAAKN,EAAE,IAAME,EAAQF,EAAE,GAAGO,KAAKD,KAAMJ,EAAOF,EAAE,IAAMxG,EAKpC,IAAbwG,EAAEnH,SACLyH,KAAKN,EAAE,IAAME,EAAQF,EAAE,GAAGO,KAAKD,KAAMJ,EAAMV,QAAQQ,EAAE,GAAIA,EAAE,KAAOxG,GAG1E8G,KAAKN,GAAKE,GAAgB1G,EAK1CZ,GAAK,CACT,CACJ,EAEA6H,EAAY,SAAUrB,EAAKsB,GAEvB,IAAK,IAAI9H,KAAK8H,EAEV,UAAWA,EAAI9H,KAAO2D,GAAYmE,EAAI9H,GAAGC,OAAS,GAC9C,IAAK,IAAIgH,EAAI,EAAGA,EAAIa,EAAI9H,GAAGC,OAAQgH,IAC/B,GAAId,EAAI2B,EAAI9H,GAAGiH,GAAIT,GACf,MAjJN,MAiJcxG,EAAiBY,EAAYZ,OAG1C,GAAImG,EAAI2B,EAAI9H,GAAIwG,GACnB,MArJE,MAqJMxG,EAAiBY,EAAYZ,EAG7C,OAAOwG,CACf,EAiBIuB,EAAoB,CAChB,GAAc,OACd,UAAc,SACd,SAAc,QACd,IAAc,SACd,GAAc,CAAC,SAAU,UACzB,MAAc,SACd,EAAc,SACd,EAAc,SACd,IAAc,SACd,GAAc,CAAC,SAAU,WACzB,GAAc,OAOlBC,EAAU,CAEVC,QAAU,CAAC,CAEP,gCACG,CAAC/D,EAAS,CAACH,EAAM,WAAY,CAChC,+BACG,CAACG,EAAS,CAACH,EAAM,SAAU,CAG9B,4BACA,mDACA,2CACG,CAACA,EAAMG,GAAU,CACpB,yBACG,CAACA,EAAS,CAACH,EAAMuB,EAAM,UAAW,CACrC,4BACG,CAACpB,EAAS,CAACH,EAAMuB,EAAM,QAAS,CACnC,qBACG,CAACpB,EAAS,CAACH,EAAMuB,IAAS,CAG7B,0DACG,CAACpB,EAAS,CAACH,EAAM,UAAW,CAC/B,uBACA,8DAEA,uDACA,2BAGA,+LAEA,kCACA,uBACG,CAACA,EAAMG,GAAU,CACpB,qBACG,CAACA,EAAS,CAACH,EAAM,eAAgB,CACpC,qDACG,CAACG,EAAS,CAACH,EAAM,KAAKe,IAAW,CACpC,+BACA,+BACA,8BACG,CAACZ,EAAS,CAACH,EAAM,WAAY,CAChC,yBACG,CAACG,EAAS,CAACH,EAAM,cAAe,CACnC,+CACG,CAACG,EAAS,CAACH,EAAM,OAAQ,CAC5B,oCACG,CAACG,EAAS,CAACH,EAAM,WAAY,CAChC,yBACG,CAACG,EAAS,CAACH,EAAM,gBAAgBe,IAAW,CAC/C,2BACG,CAAC,CAACf,EAAM,OAAQ,aAAae,GAAUZ,GAAU,CACpD,uBACG,CAACA,EAAS,CAACH,EAAMiB,EAAQ,WAAY,CACxC,qBACG,CAACd,EAAS,CAACH,EAAMuB,EAAM,WAAY,CACtC,0BACG,CAACpB,EAAS,CAACH,EAAM,YAAa,CACjC,sBACG,CAACG,EAAS,CAACH,EAAM,YAAa,CACjC,qBACG,CAACG,EAAS,CAACH,EAAMuB,EAAM,WAAY,CACtC,2BACG,CAACpB,EAAS,CAACH,EAAM,QAAQe,IAAW,CACvC,sBACG,CAACZ,EAAS,CAACH,EAAMiB,IAAW,CAC/B,iCACG,CAAC,CAACjB,EAAM,OAASe,IAAW,CAC/B,oDACG,CAAC,CAACf,EAAM,OAAQ,MAAQe,GAAUZ,GAAU,CAC/C,8BACG,CAACA,EAAS,CAACH,EAAMwB,EAAU,cAAe,CAC7C,+BACG,CAAC,CAACxB,EAAM,KAAM,KAAMG,GAAU,CACjC,0BACG,CAACA,EAAS,CAACH,EAAM,mBAAoB,CACxC,4BACG,CAAC,CAACA,EAAM,gBAAiBG,GAAU,CACtC,gCACA,iDACA,8CACG,CAACH,EAAMG,GAAU,CACpB,eACA,sBACG,CAACH,GAAO,CAGX,+DACG,CAAC,CAACA,EAAM6B,GAAW1B,GAAU,CAChC,uBACA,uCACA,kCACA,4BACA,4BACA,6BACA,qCACA,iDACG,CAACH,EAAMG,GAAU,CACpB,gCACG,CAACA,EAAS,CAACH,EAAM,QAAS,CAC7B,8CACG,CAACG,EAAS,CAACH,EAAM,WAAY,CAEhC,oCACG,CAACG,EAAS,CAACH,EAAMgB,EAAO,cAAe,CAE1C,+BACG,CAAC,CAAChB,EAAMgB,EAAO,YAAab,GAAU,CAEzC,2DACG,CAACA,EAAS,CAACH,EAAM,WAAWe,IAAW,CAE1C,+DACG,CAACf,EAAMG,GAAU,CAEpB,gDACG,CAACA,EAAS,CAACH,EAAM,kBAAmB,CACvC,sDACG,CAACG,EAASH,GAAO,CACpB,gDACG,CAACA,EAAM,CAACG,EAAS2D,EAtJT,CACX,MAAU,KACV,IAAU,KACV,IAAU,KACV,MAAU,OACV,QAAU,OACV,QAAU,OACV,QAAU,OACV,IAAU,OA8IqC,CAE/C,8BACG,CAAC9D,EAAMG,GAAU,CAGpB,wCACG,CAAC,CAACH,EAAM,YAAaG,GAAU,CAClC,uCACG,CAACA,EAAS,CAACH,EAAMiB,EAAQ,aAAc,CAC1C,6BACA,cACA,mGAEA,+FAEA,wBACA,2CAGA,wHAEA,uBACA,sBACG,CAACjB,EAAMG,GAAU,CAEpB,wBACG,CAACH,EAAM,CAACG,EAAS,eAAgB,MAGxCgE,IAAM,CAAC,CAEH,iDACG,CAAC,CAAC/D,EAAc,UAAW,CAE9B,gBACG,CAAC,CAACA,EAAcmC,IAAY,CAE/B,0BACG,CAAC,CAACnC,EAAc,SAAU,CAE7B,oCACG,CAAC,CAACA,EAAc,UAAW,CAE9B,mCACG,CAAC,CAACA,EAAc,UAAW,CAG9B,8BACG,CAAC,CAACA,EAAc,QAAS,CAE5B,0CACG,CAAC,CAACA,EAAc,OA3WT,GA2WwBmC,IAAY,CAE9C,kBACG,CAAC,CAACnC,EAAc,UAAW,CAE9B,2HAEG,CAAC,CAACA,EAAcmC,KAGvB6B,OAAS,CAAC,CAON,mFACG,CAACrE,EAAO,CAACG,EAAQsB,GAAU,CAACvB,EAAMM,IAAU,CAC/C,yDACA,uBACA,iBACG,CAACR,EAAO,CAACG,EAAQsB,GAAU,CAACvB,EAAMK,IAAU,CAG/C,4CACG,CAACP,EAAO,CAACG,EAAQU,GAAQ,CAACX,EAAMK,IAAU,CAC7C,6BACA,oCACA,kCACG,CAACP,EAAO,CAACG,EAAQU,GAAQ,CAACX,EAAMM,IAAU,CAC7C,iBACG,CAACR,EAAO,CAACG,EAAQU,IAAS,CAG7B,iCACG,CAACb,EAAO,CAACG,EAAQuB,GAAQ,CAACxB,EAAMK,IAAU,CAG7C,+DACG,CAACP,EAAO,CAACG,EAAQiB,GAAS,CAAClB,EAAMM,IAAU,CAC9C,kCACA,sEACG,CAACR,EAAO,CAACG,EAAQiB,GAAS,CAAClB,EAAMK,IAAU,CAG9C,kDACA,yBACA,uCACA,iDACA,4DACA,yGACG,CAAC,CAACP,EAAO,KAAM,KAAM,CAACG,EAAQyB,GAAS,CAAC1B,EAAMK,IAAU,CAC3D,+CACA,8CACE,CAAC,CAACP,EAAO,KAAM,KAAM,CAACG,EAAQyB,GAAS,CAAC1B,EAAMM,IAAU,CAG1D,sBACA,mEACG,CAACR,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMK,IAAU,CAC9C,wBACG,CAACP,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMM,IAAU,CAG9C,yBACA,oCACG,CAACR,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMK,IAAU,CAG9C,mCACG,CAACP,EAAO,CAACG,EAAQ,UAAW,CAACD,EAAMK,IAAU,CAGhD,iFACA,4BACA,sDACG,CAACP,EAAO,CAACG,EAAQoB,GAAW,CAACrB,EAAMK,IAAU,CAChD,qCACG,CAACP,EAAO,CAACG,EAAQoB,GAAW,CAACrB,EAAMM,IAAU,CAGhD,iEACG,CAACR,EAAO,CAACG,EAAQkB,GAAK,CAACnB,EAAMM,IAAU,CAC1C,sDACA,oDACA,wBACG,CAACR,EAAO,CAACG,EAAQkB,GAAK,CAACnB,EAAMK,IAAU,CAG1C,oBACA,qEACG,CAACP,EAAO,CAACG,EAAQ,UAAW,CAACD,EAAMM,IAAU,CAGhD,qCACA,0BACG,CAAC,CAACR,EAAO,KAAM,KAAM,CAACG,EAAQ,SAAU,CAACD,EAAMK,IAAU,CAG5D,gBACG,CAACP,EAAO,CAACG,EAAQgB,GAAS,CAACjB,EAAMM,IAAU,CAC9C,6CACG,CAACR,EAAO,CAACG,EAAQgB,GAAS,CAACjB,EAAMK,IAAU,CAG9C,0GACG,CAACP,EAAO,CAACG,EAAQwB,GAAO,CAACzB,EAAMK,IAAU,CAC5C,oBACA,iCACG,CAAC,CAACP,EAAO,iBAAkB,CAACG,EAAQwB,GAAO,CAACzB,EAAMM,IAAU,CAG/D,sCACA,0CACG,CAACR,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMK,IAAU,CAGjD,eACA,uCACA,gCACG,CAACP,EAAO,CAACG,EAAQS,GAAS,CAACV,EAAMM,IAAU,CAC9C,iDACG,CAAC,CAACR,EAAO,QAAS,iBAAkB,CAACG,EAAQS,GAAS,CAACV,EAAMK,IAAU,CAG1E,gCACG,CAACP,EAAOG,EAAQ,CAACD,EAAMM,IAAU,CACpC,gCACA,kBACG,CAACR,EAAO,CAACG,EAAQY,GAAa,CAACb,EAAMK,IAAU,CAGlD,qFACG,CAACP,EAAO,CAACG,EAAQW,GAAO,CAACZ,EAAMM,IAAU,CAC5C,iDACG,CAACR,EAAO,CAACG,EAAQW,GAAO,CAACZ,EAAMK,IAAU,CAG5C,cACG,CAACP,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMM,IAAU,CAC7C,2CAGA,oCACA,iFACG,CAACL,EAAQ,CAACH,EAAO,KAAM,KAAM,CAACE,EAAMK,IAAU,CAGjD,uCACG,CAACP,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMM,IAAU,CAG9C,8BACA,qBACG,CAACR,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMK,IAAU,CAG/C,kDACG,CAACP,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMK,IAAU,CAGjD,gHAEA,mBACA,iBACA,8BACA,0BACA,WACA,yBACG,CAACJ,EAAQH,EAAO,CAACE,EAAMK,IAAU,CAEpC,2BACA,wBACA,uCACA,uBACA,4BACA,iCACA,kCACA,8BACA,gCACA,mCACG,CAACJ,EAAQH,EAAO,CAACE,EAAMM,IAAU,CAEpC,kBACG,CAACR,EAAO,CAACG,EAAQmB,GAAY,CAACpB,EAAMM,IAAU,CACjD,qCACG,CAACR,EAAO,CAACG,EAAQ,aAAc,CAACD,EAAMK,IAAU,CACnD,aACG,CAACP,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMK,IAAU,CAC9C,gBACG,CAACP,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMK,IAAU,CACjD,iBACG,CAACP,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMM,IAAU,CAC7C,0BACG,CAACR,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMM,IAAU,CAC9C,wBACG,CAACR,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMM,IAAU,CACjD,+CACG,CAACR,EAAO,CAACG,EAAQ,kBAAmB,CAACD,EAAMM,IAAU,CACxD,qBACG,CAACR,EAAO,CAACG,EAAQ,YAAa,CAACD,EAAMM,IAAU,CAClD,cACG,CAACR,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMM,IAAU,CAC7C,mBACG,CAACR,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMK,IAAU,CAC7C,wBACG,CAACP,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMK,IAAU,CAC/C,mBACG,CAACP,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMM,IAAU,CAC/C,wBACG,CAACR,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMM,IAAU,CAC9C,mBACA,sCACG,CAAC,CAACL,EAAQ,gBAAiBH,EAAO,CAACE,EAAMM,IAAU,CACtD,sBACG,CAACR,EAAO,CAACG,EAAQ,YAAa,CAACD,EAAMM,IAAU,CAClD,8BACG,CAACR,EAAO,CAACG,EAAQ,YAAa,CAACD,EAAMM,IAAU,CAClD,oDACG,CAAC,CAACL,EAAQ,SAAUH,EAAO,CAACE,EAAMK,IAAU,CAC/C,2BACG,CAAC,CAACJ,EAAQ,SAAUH,EAAO,CAACE,EAAMK,IAAU,CAC/C,cACG,CAACP,EAAO,CAACG,EAAQ,aAAc,CAACD,EAAMK,IAAU,CACnD,uCACG,CAACP,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMM,IAAU,CACjD,wBACG,CAACR,EAAO,CAACG,EAAQ,aAAc,CAACD,EAAMM,IAAU,CACnD,kBACG,CAACR,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMM,IAAU,CAC/C,qBACG,CAACR,EAAO,CAACG,EAAQ,UAAW,CAACD,EAAMM,IAAU,CAChD,mBACG,CAACL,EAAQH,EAAO,CAACE,EAAMK,IAAU,CACpC,sBACG,CAAC,CAACP,EAAO,MAAO,KAAM,CAACG,EAAQmB,GAAY,CAACpB,EAAMK,IAAU,CAC/D,yDACG,CAACP,EAAO,CAACG,EAAQ0B,GAAQ,CAAC3B,EAAMM,IAAU,CAC7C,yCACG,CAACR,EAAO,CAACG,EAAQ0B,GAAQ,CAAC3B,EAAMK,IAAU,CAM7C,wBACG,CAACJ,EAAQ,CAACD,EAAMO,IAAW,CAC9B,uBACG,CAAC,CAACT,EAAO,IAAK,WAAY,CAACG,EAAQsB,GAAU,CAACvB,EAAMO,IAAW,CAClE,8DACG,CAAC,CAACN,EAAQkB,GAAK,CAACnB,EAAMO,IAAW,CACpC,gBACG,CAACN,EAAQ,CAACH,EAAOa,EAAM,OAAQ,CAACX,EAAMO,IAAW,CACpD,UACG,CAAC,CAACT,EAAOiB,EAAO,QAAS,CAACd,EAAQgB,GAAS,CAACjB,EAAMO,IAAW,CAChE,6BACG,CAACT,EAAO,CAACG,EAAQS,GAAS,CAACV,EAAMO,IAAW,CAC/C,uBACA,uBACG,CAACT,EAAO,CAACG,EAAQuB,GAAQ,CAACxB,EAAMO,IAAU,CAC7C,4BACG,CAACT,EAAO,CAACG,EAAQwB,GAAO,CAACzB,EAAMO,IAAW,CAC7C,qBACG,CAACT,EAAO,CAACG,EAAQyB,GAAS,CAAC1B,EAAMO,IAAW,CAC/C,6BACG,CAACN,EAAQH,EAAO,CAACE,EAAMO,IAAW,CACrC,0CACA,6DACG,CAAC,CAACN,EAAQyC,GAAO,CAAC5C,EAAO4C,GAAO,CAAC1C,EAAMO,IAAW,CACrD,mDACG,CAAC,CAACP,EAAMO,IAAW,CAMtB,UACA,8BACG,CAACN,EAAQH,EAAO,CAACE,EAAMI,IAAW,CACrC,0BACG,CAACN,EAAO,CAACG,EAAQ,UAAW,CAACD,EAAMI,IAAW,CACjD,mCACG,CAACN,EAAO,CAACG,EAAQwB,GAAO,CAACzB,EAAMI,IAAW,CAC7C,sCACG,CAACN,EAAO,CAACG,EAAQmB,GAAY,CAACpB,EAAMI,IAAW,CAMlD,kBACG,CAACH,EAAQH,EAAO,CAACE,EAAMQ,IAAY,CACtC,wCACG,CAACV,EAAO,CAACG,EAAQU,GAAQ,CAACX,EAAMQ,IAAY,CAC/C,wBACG,CAACV,EAAO,CAACG,EAAQgB,GAAS,CAACjB,EAAMQ,IAAY,CAChD,6BACG,CAACV,EAAO,CAACG,EAAQ0B,GAAQ,CAAC3B,EAAMQ,IAAY,CAC/C,uBACG,CAACV,EAAO,CAACG,EAAQ2B,GAAW,CAAC5B,EAAMQ,IAAY,CAMlD,wCACG,CAACP,EAAQ,CAACD,EAAMS,IAAY,CAC/B,cACG,CAACX,EAAO,CAACG,EAAQS,GAAS,CAACV,EAAMS,IAAY,CAMhD,kEACG,CAACX,EAAO,CAACE,EAAMK,IAAU,CAC5B,+DACG,CAACP,EAAO,CAACE,EAAMM,IAAU,CAC5B,gDACG,CAAC,CAACN,EAAMM,IAAU,CACrB,kEACG,CAAC,CAACN,EAAMK,IAAU,CACrB,kCACG,CAACP,EAAO,CAACG,EAAQ,aAGxBmE,OAAS,CAAC,CAEN,8BACG,CAAClE,EAAS,CAACH,EAAMsE,aAAe,CAEnC,6CACG,CAACnE,EAAS,CAACH,EAAM,UAAW,CAE/B,uBACA,sEACA,0BACA,yCACA,8BACA,eACG,CAACA,EAAMG,GAAU,CAEpB,iCACG,CAACA,EAASH,IAGjBuE,GAAK,CAAC,CAGF,mCACG,CAACvE,EAAMG,GAAU,CACpB,yDACG,CAACH,EAAM,CAACG,EAAS2D,EAAWE,IAAqB,CACpD,0BACA,2CACA,wCACG,CAAC,CAAC7D,EAAS2D,EAAWE,GAAoB,CAAChE,EAAM,YAAa,CAGjE,sDACA,4CACA,wBACG,CAAC,CAACG,EAAS,KAAM,KAAM,CAACH,EAAM,QAAS,CAC1C,0BACA,yCACG,CAAC,CAACA,EAAM+B,GAAS,CAAC5B,EAAS,KAAM,MAAO,CAG3C,kDACG,CAACA,EAASH,GAAO,CACpB,+EACA,8BACA,+BACA,kBACG,CAACA,EAAMG,GAAU,CACpB,cACG,CAACA,EAAS,CAACH,EAAMc,IAAc,CAClC,6DACG,CAACX,EAAS,CAACH,EAAM,YAAa,CACjC,mFACG,CAACG,EAAS,CAACH,EAAMiB,EAAQ,QAAS,CACrC,kBACA,wCACG,CAACd,EAAS,CAACH,EAAM,UAAW,CAC/B,wCACG,CAACG,EAAS,CAACH,EAAM,YAAa,CAGjC,qBACG,CAACG,EAAS,CAACH,EAAMgB,EAAO,SAAU,CACrC,oCACG,CAAC,CAAChB,EAAM8B,GAAc3B,GAAS,CAGlC,qBACA,iBACA,2BAGA,mDACA,2BAGA,wCACA,yBACA,4BACA,8SAEA,2BACA,oBACA,6EACA,kBACG,CAACH,EAAMG,GAAU,CACpB,yBACG,CAAC,CAACH,EAAM,WAAYG,GAAU,CACjC,sCACA,kCACA,mEACA,sBACG,CAACH,EAAMG,KAQdqE,EAAW,SAAUxB,EAAIyB,GAOzB,UALWzB,IAAOpD,IACd6E,EAAazB,EACbA,EAAKnG,KAGH8G,gBAAgBa,GAClB,OAAO,IAAIA,EAASxB,EAAIyB,GAAYC,YAGxC,IAAIC,SAAqBlF,IAAWE,GAAcF,EAAOmF,UAAanF,EAAOmF,UAAY/H,EACrFgI,EAAM7B,IAAQ2B,GAAcA,EAAWG,UAAaH,EAAWG,UAnyBrD,IAoyBVC,EAASJ,GAAcA,EAAWK,cAAiBL,EAAWK,cAAgBnI,EAC9EoI,EAAUR,EArvBL,SAAUR,EAASQ,GACxB,IAAIS,EAAgB,CAAC,EACrB,IAAK,IAAIjJ,KAAKgI,EACNQ,EAAWxI,IAAMwI,EAAWxI,GAAGC,OAAS,GAAM,EAC9CgJ,EAAcjJ,GAAKwI,EAAWxI,GAAGkJ,OAAOlB,EAAQhI,IAEhDiJ,EAAcjJ,GAAKgI,EAAQhI,GAGnC,OAAOiJ,CACX,CA2uB2BE,CAAOnB,EAASQ,GAAcR,EACrDoB,EAAaV,GAAcA,EAAWG,WAAaD,EAyEvD,OAvEAlB,KAAK2B,WAAa,WACd,IAjuBiBC,EAiuBbC,EAAW,CAAC,EAShB,OARAA,EAASxF,GAAQnD,EACjB2I,EAASrF,GAAWtD,EACpBkG,EAAUa,KAAK4B,EAAUX,EAAKI,EAAQf,SACtCsB,EAAS1F,UAruBQyF,EAquBUC,EAASrF,MApuBTN,EAAW0F,EAAQ1C,QAAQ,WAzE5C,IAyE+D4C,MAAM,KAAK,GAAK5I,EAsuBrFwI,GAAcV,GAAcA,EAAWe,cAAgBf,EAAWe,MAAMC,SAAWjG,IACnF8F,EAASxF,GAAQ,SAEdwF,CACX,EACA7B,KAAKiC,OAAS,WACV,IAAIC,EAAO,CAAC,EAGZ,OAFAA,EAAKzF,GAAgBvD,EACrBkG,EAAUa,KAAKiC,EAAMhB,EAAKI,EAAQd,KAC3B0B,CACX,EACAlC,KAAKmC,UAAY,WACb,IAAIC,EAAU,CAAC,EAaf,OAZAA,EAAQ7F,GAAUrD,EAClBkJ,EAAQhG,GAASlD,EACjBkJ,EAAQ9F,GAAQpD,EAChBkG,EAAUa,KAAKmC,EAASlB,EAAKI,EAAQb,QACjCiB,IAAeU,EAAQ9F,IAAS8E,GAASA,EAAMiB,SAC/CD,EAAQ9F,GAAQK,GAGhB+E,GAAgC,aAAlBU,EAAQhG,IAAyB4E,UAAqBA,EAAWsB,aAAetG,GAAcgF,EAAWuB,gBAAkBvB,EAAWuB,eAAiB,IACrKH,EAAQhG,GAAS,OACjBgG,EAAQ9F,GAAQM,GAEbwF,CACX,EACApC,KAAKwC,UAAY,WACb,IAAIC,EAAU,CAAC,EAIf,OAHAA,EAAQpG,GAAQnD,EAChBuJ,EAAQjG,GAAWtD,EACnBkG,EAAUa,KAAKwC,EAASvB,EAAKI,EAAQZ,QAC9B+B,CACX,EACAzC,KAAK0C,MAAQ,WACT,IAAIC,EAAM,CAAC,EASX,OARAA,EAAItG,GAAQnD,EACZyJ,EAAInG,GAAWtD,EACfkG,EAAUa,KAAK0C,EAAKzB,EAAKI,EAAQV,IAC7Bc,IAAeiB,EAAItG,IAAS+E,GAASA,EAAMwB,UAA8B,WAAlBxB,EAAMwB,WAC7DD,EAAItG,GAAQ+E,EAAMwB,SACG1D,QAAQ,aAAcf,GACtBe,QAAQ,SAAUd,IAEpCuE,CACX,EACA3C,KAAKe,UAAY,WACb,MAAO,CACH1B,GAAUW,KAAK6C,QACftC,QAAUP,KAAK2B,aACfjB,OAAUV,KAAKwC,YACf5B,GAAUZ,KAAK0C,QACfjC,OAAUT,KAAKmC,YACf3B,IAAUR,KAAKiC,SAEvB,EACAjC,KAAK6C,MAAQ,WACT,OAAO3B,CACX,EACAlB,KAAK8C,MAAQ,SAAUzD,GAEnB,OADA6B,SAAc7B,IAAOnD,GAAYmD,EAAG9G,OAx1BxB,IAw1BkDyG,EAAKK,EAx1BvD,KAw1B4EA,EACjFW,IACX,EACAA,KAAK8C,MAAM5B,GACJlB,IACX,EAEAa,EAASrE,QAn3BS,SAo3BlBqE,EAASzD,QAAWiB,EAAU,CAAChC,EAAMG,EAASL,IAC9C0E,EAASkC,IAAM1E,EAAU,CAAC5B,IAC1BoE,EAASmC,OAAS3E,EAAU,CAACjC,EAAOG,EAAQD,EAAMI,EAASC,EAAQE,EAASD,EAAQE,EAAUC,IAC9F8D,EAASoC,OAASpC,EAASqC,GAAK7E,EAAU,CAAChC,EAAMG,WAOtCnF,IAAa2E,GAEgB1E,EAAOD,UACvCA,EAAUC,EAAOD,QAAUwJ,GAE/BxJ,EAAQwJ,SAAWA,GAGiBtJ,EAAAA,MAChCA,EAAAA,WACI,OAAOsJ,CACV,2CACa/E,IAAWE,IAEzBF,EAAO+E,SAAWA,GAS1B,IAAIsC,SAAWrH,IAAWE,IAAeF,EAAOsH,QAAUtH,EAAOuH,OACjE,GAAIF,IAAMA,EAAE9D,GAAI,CACZ,IAAIiE,GAAS,IAAIzC,EACjBsC,EAAE9D,GAAKiE,GAAOvC,YACdoC,EAAE9D,GAAGkE,IAAM,WACP,OAAOD,GAAOT,OAClB,EACAM,EAAE9D,GAAGmE,IAAM,SAAUnE,GACjBiE,GAAOR,MAAMzD,GACb,IAAIoE,EAASH,GAAOvC,YACpB,IAAK,IAAI2C,KAAQD,EACbN,EAAE9D,GAAGqE,GAAQD,EAAOC,EAE5B,CACJ,CAEH,CA96BD,CA86BqB,iBAAX5H,OAAsBA,OAASkE,K,iCCt5BzC,MAAM2D,EAAgB,WAElB,IAAIC,EACAC,EAAoB,GACxB,MAAMC,EAAqB,CAAC,EACtBC,EAAiB,CAAC,EAuBxB,SAASC,EAAqBC,EAASC,GACnC,IAAK,MAAM5L,KAAKuL,EAAmB,CAC/B,MAAMM,EAAMN,EAAkBvL,GAC9B,GAAI6L,EAAIF,UAAYA,GAAWE,EAAI3I,OAAS0I,EACxC,OAAOC,EAAIP,QAEnB,CACA,OAAO,IACX,CA2CA,SAASQ,EAAiB5I,EAAM6I,GAC5B,OAAOA,EAAe7I,EAC1B,CAEA,SAAS8I,EAAc9I,EAAMpE,EAASiN,GAC9B7I,KAAQ6I,IACRA,EAAe7I,GAAQpE,EAE/B,CAmFA,SAASmN,EAAMC,EAAkBP,EAASQ,GAEtC,IAAIC,EACJ,MAAMR,EAAYM,EAAiBG,sBAC7BC,EAAkBX,EAAQC,GAEhC,GAAIU,EAAiB,CAEjB,IAAIC,EAAYD,EAAgBhB,SAEhC,IAAIgB,EAAgBE,SAiBhB,OAAOD,EAAUE,MAAM,CACnBd,UACA7M,QAASwM,GACVa,GAlBHC,EAAgBF,EAAiBO,MAAM,CAACd,WAAUQ,GAClDI,EAAYA,EAAUE,MAAM,CACxBd,UACA7M,QAASwM,EACToB,OAAQN,GACTD,GAEH,IAAK,MAAMf,KAAQmB,EACXH,EAAcO,eAAevB,KAC7BgB,EAAchB,GAAQmB,EAAUnB,GAYhD,MAEIgB,EAAgBF,EAAiBO,MAAM,CAACd,WAAUQ,GAMtD,OAFAC,EAAcQ,aAAe,WAAa,OAAOhB,CAAU,EAEpDQ,CACX,CAeA,OAbAd,EAAW,CACPnC,OAhNJ,SAAgBjG,EAAM2J,EAAeL,EAAUb,IACtCA,EAAQzI,IAAS2J,IAClBlB,EAAQzI,GAAQ,CACZoI,SAAUuB,EACVL,SAAUA,GAGtB,EA0MId,qBAAsBA,EACtBoB,qBA1KJ,SAA8BnB,EAASC,EAAWN,GAC9C,IAAK,MAAMtL,KAAKuL,EAAmB,CAC/B,MAAMM,EAAMN,EAAkBvL,GAC9B,GAAI6L,EAAIF,UAAYA,GAAWE,EAAI3I,OAAS0I,EAExC,YADAL,EAAkBvL,GAAGsL,SAAWA,EAGxC,CACAC,EAAkBwB,KAAK,CACnB7J,KAAM0I,EACND,QAASA,EACTL,SAAUA,GAElB,EA8JI0B,yBArJJ,SAAkCrB,GAC9BJ,EAAoBA,EAAkB0B,QAAOC,GAAKA,EAAEvB,UAAYA,GACpE,EAoJIwB,oBAlFJ,SAA6BjB,GACzB,IAAIpN,EAAUgN,EAAiBI,EAAiBG,sBAAuBb,GA6BvE,OA5BK1M,IACDA,EAAU,SAAU6M,GAChB,IAAIL,EAIJ,YAHgB1K,IAAZ+K,IACAA,EAAU,CAAC,GAER,CACHyB,YAAa,WAcT,OAZK9B,IACDA,EAAWI,EAAqBC,EAASO,EAAiBG,wBAGzDf,IACDA,EAAWW,EAAMC,EAAkBP,EAAShL,WAC5C4K,EAAkBwB,KAAK,CACnB7J,KAAMgJ,EAAiBG,sBACvBV,QAASA,EACTL,SAAUA,KAGXA,CACX,EAER,EACAE,EAAmBU,EAAiBG,uBAAyBvN,GAG1DA,CACX,EAoDIuO,0BAvFJ,SAAmCnK,GAC/B,OAAO4I,EAAiB5I,EAAMsI,EAClC,EAsFI8B,uBA5FJ,SAAgCpK,EAAMpE,GAClCkN,EAAc9I,EAAMpE,EAAS0M,EACjC,EA2FI+B,gBAvHJ,SAAyBrB,GACrB,IAAIpN,EAAUgN,EAAiBI,EAAiBG,sBAAuBZ,GAgBvE,OAdK3M,IACDA,EAAU,SAAU6M,GAIhB,YAHgB/K,IAAZ+K,IACAA,EAAU,CAAC,GAER,CACH6B,OAAQ,WACJ,OAAOvB,EAAMC,EAAkBP,EAAShL,UAC5C,EAER,EAEA8K,EAAeS,EAAiBG,uBAAyBvN,GAEtDA,CACX,EAsGI2O,sBA5HJ,SAA+BvK,GAC3B,OAAO4I,EAAiB5I,EAAMuI,EAClC,EA2HIiC,mBAjIJ,SAA4BxK,EAAMpE,GAC9BkN,EAAc9I,EAAMpE,EAAS2M,EACjC,GAkIOH,CAEX,CArOsB,GAuOtB,K,sECjOA,MAAMqC,EACF,YAAOC,CAAMC,EAAMC,EAAQC,GACvB,IAAIC,EACAC,EAAQ,CAAC,EACb,GAAIJ,EACA,IAAK,IAAI3K,KAAQ4K,EACTA,EAAOnB,eAAezJ,KACtB8K,EAAIF,EAAO5K,GACLA,KAAQ2K,IAAUA,EAAK3K,KAAU8K,GAAQ9K,KAAQ+K,GAAUA,EAAM/K,KAAU8K,KACnD,iBAAfH,EAAK3K,IAAqC,OAAf2K,EAAK3K,GACvC2K,EAAK3K,GAAQyK,EAAMC,MAAMC,EAAK3K,GAAO8K,EAAGD,GAExCF,EAAK3K,GAAQ6K,EAAKC,KAMtC,OAAOH,CACX,CAEA,YAAOK,CAAMC,GACT,IAAKA,GAAsB,iBAARA,EACf,OAAOA,EAEX,GAAIA,aAAeC,OACf,OAAO,IAAIA,OAAOD,GAEtB,IAAIE,EACJ,GAAIF,aAAeG,MAAO,CAEtBD,EAAI,GACJ,IAAK,IAAIrO,EAAI,EAAGuO,EAAIJ,EAAIlO,OAAQD,EAAIuO,IAAKvO,EACjCA,KAAKmO,GACLE,EAAEtB,KAAKY,EAAMO,MAAMC,EAAInO,IAGnC,MACIqO,EAAI,CAAC,EAET,OAAOV,EAAMC,MAAMS,EAAGF,EAAKR,EAAMO,MACrC,CAEA,uCAAOM,CAAiCC,EAAKC,GACzC,IACI,IAAKA,GAA4B,IAAlBA,EAAOzO,OAClB,OAAOwO,EAGX,IAAIE,EAAaF,EAKjB,OAJAC,EAAOE,SAAQC,IAAoB,IAAnB,IAAEC,EAAG,MAAEC,GAAOF,EAC1B,MAAMG,EAAYL,EAAWM,SAAS,KAAO,IAAM,IACnDN,GAAc,GAAGK,IAAaE,mBAAmBJ,MAAUI,mBAAmBH,IAAS,IAEpFJ,CACX,CAAE,MAAOQ,GACL,OAAOV,CACX,CACJ,CAEA,kCAAOW,CAA4BX,EAAKY,GACpC,IAAKZ,IAAQY,EACT,OAAOZ,EAGX,MAAMa,EAAY,IAAIC,IAAId,GAGpBC,EAAS,IAAIc,gBAAgBF,EAAUG,QAE7C,IAAKf,GAA0B,IAAhBA,EAAOgB,OAAehB,EAAOvI,IAAIkJ,GAC5C,OAAOZ,EAIXC,EAAOiB,OAAON,GAGd,MAAMO,EAActB,MAAMjN,KAAKqN,EAAOmB,WACjC/H,KAAIgI,IAAA,IAAEhB,EAAKC,GAAMe,EAAA,MAAK,GAAGhB,KAAOC,GAAO,IACvC9N,KAAK,KAGJ8O,EAAU,GAAGT,EAAUU,SAASV,EAAUW,WAChD,OAAOL,EAAc,GAAGG,KAAWH,IAAgBG,CACvD,CAEA,uBAAOG,CAAiBC,GACpB,IAAIC,EAAU,CAAC,EACf,IAAKD,EACD,OAAOC,EAKX,IAAIC,EAAcF,EAAUzJ,OAAO8C,MAAM,QACzC,IAAK,IAAIxJ,EAAI,EAAGsQ,EAAOD,EAAYpQ,OAAQD,EAAIsQ,EAAMtQ,IAAK,CACtD,IAAIuQ,EAAaF,EAAYrQ,GACzBwQ,EAAQD,EAAWhK,QAAQ,MAC3BiK,EAAQ,IACRJ,EAAQG,EAAW1J,UAAU,EAAG2J,IAAUD,EAAW1J,UAAU2J,EAAQ,GAE/E,CACA,OAAOJ,CACX,CAOA,uBAAOK,CAAiBC,GACpB,MAAMhC,EAAS,GACTiC,EAAe,IAAInB,gBAAgBkB,GACzC,IAAK,MAAO5B,EAAKC,KAAU4B,EAAad,UACpCnB,EAAO3B,KAAK,CAAE+B,IAAK8B,mBAAmB9B,GAAMC,MAAO6B,mBAAmB7B,KAE1E,OAAOL,CACX,CAEA,mBAAOmC,GACH,IAAIC,GAAK,IAAIC,MAAOC,UAMpB,MALa,uCAAuCpK,QAAQ,SAAS,SAAUqK,GAC3E,MAAM5C,GAAKyC,EAAqB,GAAhBI,KAAKC,UAAiB,GAAK,EAE3C,OADAL,EAAKI,KAAKE,MAAMN,EAAK,KACR,KAALG,EAAW5C,EAAS,EAAJA,EAAU,GAAMgD,SAAS,GACrD,GAEJ,CAEA,uBAAOC,CAAiBC,GACpB,IAAIC,EAAO,EAEX,GAAsB,IAAlBD,EAAOtR,OACP,OAAOuR,EAGX,IAAK,IAAIxR,EAAI,EAAGA,EAAIuR,EAAOtR,OAAQD,IAE/BwR,GAASA,GAAQ,GAAKA,EADVD,EAAOrR,WAAWF,GAE9BwR,GAAQ,EAEZ,OAAOA,CACX,CAQA,qBAAOC,CAAeC,EAAaC,GAC/B,IACI,MAAMC,EAAW,IAAIrC,IAAImC,GACnBG,EAAS,IAAItC,IAAIoC,GAIvB,GADAC,EAASE,SAAWD,EAAOC,SACvBF,EAAS5B,SAAW6B,EAAO7B,OAC3B,OAAO2B,EAIX,IAAII,EAAe1S,EAAAA,SAAcuS,EAAS3B,SAAS+B,OAAO,EAAGJ,EAAS3B,SAAS7P,YAAY,MAAOyR,EAAO5B,SAAS+B,OAAO,EAAGH,EAAO5B,SAAS7P,YAAY,OAGxJ,MAAM6R,EAA2C,IAAxBF,EAAa9R,OAAe,EAAI,EAIzD,OAHA8R,GAAgBF,EAAO5B,SAAS+B,OAAOH,EAAO5B,SAAS7P,YAAY,KAAO6R,EAAkBJ,EAAO5B,SAAShQ,OAAS,GAGjH4R,EAAO5B,SAAShQ,OAAS8R,EAAa9R,OAC/B4R,EAAO5B,SAEX8B,CACX,CAAE,MAAO5C,GACL,OAAOwC,CACX,CACJ,CAEA,qBAAOO,CAAeC,GAClB,IAGI,OAFY,IAAI5C,IAAI4C,GAETC,IACf,CAAE,MAAOjD,GACL,OAAO,IACX,CACJ,CAEA,qBAAOkD,GAA0B,IAAXtL,EAAEpG,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG,KACvB,IACI,MAAM2R,EAAkB,OAAPvL,GAAmC,oBAAd4B,UAA4BA,UAAUE,UAAUpC,cAAqB,GAE3G,OAAO8B,EAAAA,EAAAA,UAAS+J,EACpB,CAAE,MAAOnD,GACL,MAAO,CAAC,CACZ,CACJ,CAOA,wBAAOoD,CAAkBhB,GACrB,MAAQ,mBAAmB3J,KAAK2J,EACpC,CAEA,6BAAOiB,CAAuBC,GAC1B,OAAO9E,EAAM+E,WAAWD,EAAcE,SAC1C,CAEA,yBAAOC,CAAmBH,GACtB,OAAO9E,EAAM+E,WAAWD,EAAcI,WAC1C,CAEA,yBAAOC,CAAmBC,GAEtB,OADgB,IAAIC,YAAY,SACjBC,OAAOF,EAC1B,CAEA,wBAAOG,CAAkBC,GACrB,MAAMnN,EAAM2H,EAAMiF,mBAAmBO,GACrC,IAAIC,EAAM,GACV,IAAK,IAAIrE,KAAS/I,EACd+I,EAAQA,EAAMsC,SAAS,IACF,IAAjBtC,EAAM9O,SACN8O,EAAQ,IAAMA,GAElBqE,GAAOrE,EAEX,OAAOqE,CACX,CAEA,iBAAOV,CAAWD,EAAcY,GAC5B,MAAMC,EAAS3F,EAAM4F,eAAed,GACpC,IAAIe,EAAkB,EAClB,sBAAuBb,WACvBa,EAAkBb,SAASc,mBAG/B,MAAMC,IAAYjB,EAAakB,YAAc,GAAKlB,EAAamB,YAC3DJ,EACEK,GAAapB,EAAakB,YAAc,GAAMH,EAC9ClR,EAAQ4O,KAAKE,MAAMF,KAAK4C,IAAI,EAAG5C,KAAK6C,IAAIF,EAAUH,KAExD,OAAO,IAAIL,EAAKC,EAAQhR,EADZ4O,KAAKE,MAAMF,KAAK6C,IAAIzR,EAAQ4O,KAAK4C,IAAIE,IAAU,GAAIN,IAC1BpR,EACzC,CAEA,qBAAOiR,CAAeU,GAClB,OAAIA,aAAgBC,YACTD,EAEAA,EAAKX,MAEpB,CAEA,qBAAOa,CAAeC,GAClB,MAAM,KAAEnR,EAAI,QAAEoR,GAAY1G,EAAM2G,eAAeF,GAE/C,OAAQnR,GACJ,IAAK,OACD,OAAQoR,GACJ,IAAK,KACL,IAAK,KACL,IAAK,QACD,OAAOE,EAAAA,EAAUC,eAAeC,IACpC,IAAK,KACL,IAAK,KACL,IAAK,KACL,IAAK,OACL,IAAK,QACL,IAAK,OACL,IAAK,QACL,IAAK,QACL,IAAK,QACD,OAAOF,EAAAA,EAAUC,eAAeE,IACpC,IAAK,KACD,OAAOH,EAAAA,EAAUC,eAAeG,IACpC,IAAK,KACD,OAAOJ,EAAAA,EAAUC,eAAeI,IACpC,IAAK,KACD,OAAOL,EAAAA,EAAUC,eAAeK,KACpC,IAAK,KACD,OAAON,EAAAA,EAAUC,eAAeM,KAExC,MACJ,IAAK,OACL,IAAK,OACD,OAAOP,EAAAA,EAAUC,eAAeO,IACpC,IAAK,OACL,IAAK,OACD,OAAOR,EAAAA,EAAUC,eAAeQ,KACpC,QACI,OAAO/R,EAGf,OAAOA,CACX,CAEA,qBAAOqR,CAAeF,GAClB,MAAOnR,KAASgS,GAAQb,EAAY5K,MAAM,KAE1C,MAAO,CAAEvG,OAAMoR,QADCY,EAAKhU,KAAK,KAE9B,EAIJ,K,kCCnSA,IAvBA,MACIkI,MAAAA,CAAO+L,EAAQC,GACX,IAAKD,EACD,OAGJ,IAAI1I,IAAW2I,GAASA,EAAO3I,SAC3B4I,IAAaD,GAASA,EAAOC,WAGjC,IAAK,MAAMC,KAAOH,GACTA,EAAOvI,eAAe0I,IAAS3N,KAAK2N,KAAS7I,GAG9C4I,IAAkD,IAApCF,EAAOG,GAAK9O,QAAQ,aAGtCmB,KAAK2N,GAAOH,EAAOG,GAG3B,E,kCCGJ,IAvBA,MACIlM,MAAAA,CAAOmM,EAAQH,GACX,IAAKG,EACD,OAGJ,IAAI9I,IAAW2I,GAASA,EAAO3I,SAC3B4I,IAAaD,GAASA,EAAOC,WAGjC,IAAK,MAAMG,KAAOD,GACTA,EAAO3I,eAAe4I,IAAS7N,KAAK6N,KAAS/I,GAG9C4I,IAAkD,IAApCE,EAAOC,GAAKhP,QAAQ,aAGtCmB,KAAK6N,GAAOD,EAAOC,GAG3B,E,kCCnBJ,KACIC,cAAe,gBACfC,eAAgB,gBAChBC,gBAAiB,iBACjBC,uCAAwC,8CACxCC,IAAK,MACLC,iBAAkB,kBAClBC,4BAA6B,4BAC7BC,oBAAqB,oBACrBC,sBAAuB,sBACvBC,wBAAyB,wBACzBC,2BAA4B,2BAC5BC,yBAA0B,yBAC1BC,SAAU,YACVC,SAAU,UACVC,oBAAqB,qBACrBC,0BAA2B,qBAC3BC,WAAY,YACZC,QAAS,UACTC,iBAAkB,mBAClBC,sBAAuB,sBACvBC,mBAAoB,oBACpBC,gBAAiB,iBACjBC,OAAQ,SACRC,mBAAoB,mBACpBC,kBAAmB,mBACnBC,kBAAmB,mBACnBC,mBAAoB,oBACpBC,iBAAkB,kBAClBC,0BAA2B,CACvBlT,QAAS,UACTmT,IAAK,MACLC,WAAY,aACZC,iBAAkB,mBAClBC,eAAgB,iBAChBC,QAAS,UACTC,GAAI,KACJC,gBAAiB,kBACjBC,KAAM,OACNC,OAAQ,UAEZC,aAAc,cACdC,yBAA0B,yBAC1BC,cAAe,eACfC,SAAU,WACVC,aAAe,eACfC,WAAa,aACbC,QAAU,UACVC,aAAe,eACfC,eAAiB,iBACjBC,QAAS,UACTC,WAAY,YACZC,mBAAoB,oBACpBC,MAAO,QACPC,aAAc,cACdC,gBAAiB,kBACjBC,UAAW,YACXC,cAAe,eACfC,YAAa,aACbC,OAAQ,SACRtB,GAAI,KACJuB,OAAQ,SACRC,oBAAqB,oBACrBC,MAAO,QACPC,YAAa,aACbC,eAAgB,iBAChBC,qBAAsB,iBACtBC,OAAQ,QACRC,kBAAmB,QACnBC,MAAO,QACPC,KAAM,OACNC,SAAU,WACVC,KAAM,OACNC,mBAAoB,mBACpBC,iBAAkB,iBAClBC,qBAAsB,qBACtBC,wBAAyB,wBACzBC,MAAO,QACPC,4BAA6B,4BAC7BC,YAAa,aACbC,0BAA2B,yBAC3BC,QAAS,UACTC,cAAe,UACfC,UAAW,WACXC,sBAAuB,sBACvBC,gBAAiB,gBACjBC,sBAAuB,mCACvBC,IAAK,MACLC,SAAU,MACVC,eAAgB,WAChBC,gBAAiB,QACjBC,sBAAuB,sBACvBC,eAAgB,gBAChBC,OAAQ,SACRC,kBAAmB,mBACnBC,yBAA0B,yBAC1BC,IAAK,MACLC,wBAAyB,wBACzBC,6BAA8B,CAC1BC,QAAS,UACTC,SAAU,WACVC,YAAa,eAEjBC,SAAU,WACVC,KAAM,OACNC,aAAc,cACdC,gBAAkB,iBAClBC,mBAAoB,mBACpBC,WAAY,cACZC,MAAO,QACPC,OAAQ,SACRC,IAAK,MACLC,OAAQ,QACRC,OAAQ,SACRC,QAAS,UACTC,UAAW,YACXC,eAAgB,iBAChBC,qBAAsB,sBACtBC,WAAY,aACZC,KAAM,OACNC,EAAG,IACHC,IAAK,MACLC,UAAW,WACXC,kBAAmB,mBACnBC,aAAc,cACdC,aAAc,cACdC,iBAAkB,kBAClBC,iBAAkB,kBAClBC,iBAAkB,kBAClBC,aAAc,UACdC,YAAa,aACbC,oBAAqB,qBACrBC,4BAA6B,UAC7BC,wCAAyC,qBACzCC,sCAAuC,mBACvCC,kCAAmC,eACnCC,0BAA2B,QAC3BC,iBAAkB,kBAClBC,kBAAmB,mBACnBC,WAAY,YACZC,MAAO,QACPC,aAAc,cACdC,eAAgB,eAChBC,OAAQ,SACRC,cAAe,WACfC,OAAQ,SACRC,SAAU,WACVC,mBAAoB,oBACpBC,sBAAuB,sBACvBC,6BAA8B,6BAC9BC,sBAAuB,uBACvBC,oBAAqB,6BACrBC,UAAW,YACXC,uBAAwB,uBACxBvH,IAAK,MACLrT,KAAM,OACN6a,WAAY,YACZC,MAAO,QACPC,UAAW,YACXC,gBAAiB,gBACjBC,MAAO,Q,kCCjKX,KAMIC,OAAQ,SAORC,MAAO,QAOPC,MAAO,QAOPC,KAAM,OAONC,MAAO,QAOPC,MAAO,QAOPC,KAAM,OAONC,KAAM,OAONC,IAAK,MAOLC,KAAM,OAONxI,iBAAkB,kBAOlByI,0BAA2B,yBAO3BC,uBAAwB,sBAOxBC,8BAA+B,gBAO/BC,oBAAqB,OAOrBC,mBAAoB,oBAOpBC,qBAAsB,8BAOtBC,iCAAkC,gBAOlCC,gCAAiC,eAOjCC,iCAAkC,aAOlCC,qCAAsC,iBAOtCC,wCAAyC,oBAOzCC,kCAAmC,cAOnCC,eAAgB,OAOhBC,gBAAiB,QAOjBC,iBAAkB,SAOlBC,oBAAqB,CAAC,KAAM,IAAK,KAAM,KAAM,KAAM,KAAM,MAAO,MAAO,MAAO,KAAM,KAAM,MAAO,MAAO,KAAM,KAAM,MAAO,KAAM,KAMjIC,uBAAwB,CAAC,MAAO,OAOhCC,wBAAyB,CAAC,UAAW,MAAO,QAAS,WAAY,SAGjEC,WAAY,aACZC,aAAc,UACdC,YAAa,SACbC,cAAe,WACfC,4BAA6B,gCAC7BC,IAAK,MACLC,IAAK,MACLC,KAAM,QACNC,cAAe,cACfC,WAAY,YACZC,kCAAmC,qCACnCC,oCAAqC,wCACrCC,qBAAsB,8BACtBC,0BAA2B,CAAC,mCAAoC,+CAChEC,yBAA0B,iCAC1BC,+BAAgC,sCAChCC,sBAAuB,8BACvBC,0BAA2B,8BAC3BC,kCAAmC,yCACnCC,uCAAwC,8CACxCC,kCAAmC,uBACnCC,2BAA4B,CACxBC,UAAW,YACXC,QAAS,UACTC,UAAW,aAEfC,uBAAwB,CACpBC,WAAY,CACRC,KAAM,OACNC,GAAI,KACJC,QAAS,WAEbC,iBAAkB,CACdH,KAAM,OACNI,GAAI,KACJC,IAAK,OAETC,iBAAkB,CACdC,iBAAkB,iBAClBC,OAAQ,SACRC,iBAAkB,mBAG1BC,IAAK,MACLC,aAAc,cACdC,kBAAmB,mBACnBC,gBAAiB,kBACjBC,aAAc,wBACdC,cAAe,wBACfC,2BAA4B,CACxBC,aAAc,EACdC,cAAe,EACfC,kBAAmB,EACnBC,iBAAkB,EAClBC,iBAAkB,GAEtBC,kBAAmB,CACfC,MAAO,eACPC,IAAK,cAETC,iBAAkB,CACdC,QAAS,0BACTC,UAAW,6BAEfC,6BAA8B,CAC1BC,KAAM,gCACNC,MAAO,iCACPC,gBAAiB,0CACjBC,mCAAoC,0DACpCC,8BAA+B,sDAC/BC,cAAe,wCACfC,iCAAkC,wDAClCC,4BAA6B,qDAEjCC,2CAA4C,CACxCC,aAAc,mDACdC,gBAAiB,sDACjBC,KAAM,6CAEVC,YAAa,CACTC,qBAAsB,qBACtBC,uBAAwB,wBAE5BD,qBAAsB,CAClBE,UAAW,WACXC,gBAAiB,iBACjBC,yBAA0B,yBAC1BC,oBAAqB,oBACrBC,oBAAqB,oBACrBC,oBAAqB,UACrBC,cAAe,YAEnBP,uBAAwB,CACpBQ,qBAAsB,uBAQ1BC,kBAAmB,+BACnBC,2BAA4B,sBAC5BC,oBAAsB,0BACtBvR,eAAgB,CACZC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,KAAM,OACNC,KAAM,OACNC,IAAK,MACLC,KAAM,Q,kCC3Td,KACIgR,wBAAyB,kBACzBC,wBAAyB,qBACzBC,yBAA0B,0BAC1BC,wCAAyC,yCACzCC,cAAe,uCACfC,eAAgB,uCAChBC,cAAe,uCACfC,kBAAmB,uCACnBC,8BAA+B,OAC/BC,gCAAiC,SACjCC,8BAA+B,OAC/BC,uBAAwB,OACxBC,uBAAwB,OACxBC,wBAAyB,CACrBC,gBAAiB,kBACjBC,gBAAiB,kBACjBC,gBAAiB,kBACjBC,0BAA2B,6BAE/BC,mBAAoB,CAChBC,SAAU,CACNC,iBAAkB,mBAClBC,iBAAkB,mBAClBC,iBAAkB,mBAClBC,iBAAkB,mBAClBC,cAAe,kBAGvBC,mBAAoB,CAChBC,OAAQ,SACRC,QAAS,UACTC,SAAU,WACVC,kBAAmB,oBACnBC,kBAAmB,oBACnBC,eAAgB,iBAChBC,eAAgB,kB,iDCpCxB,MAAMC,UAAyBC,EAAAA,EAM3BC,WAAAA,GACIC,QAQA1gB,KAAK2gB,qBAAuB,qBAM5B3gB,KAAK4gB,8BAAgC,6BAWrC5gB,KAAK6gB,UAAY,kBAMjB7gB,KAAK8gB,UAAY,kBAOjB9gB,KAAK+gB,YAAc,oBAOnB/gB,KAAKghB,mBAAqB,0BAO1BhhB,KAAKihB,oBAAsB,2BAO3BjhB,KAAKkhB,oBAAsB,2BAO3BlhB,KAAKmhB,qBAAuB,4BAO5BnhB,KAAKohB,yBAA2B,wBAOhCphB,KAAKqhB,2BAA6B,iCAOlCrhB,KAAKshB,oBAAsB,2BAO3BthB,KAAKuhB,yBAA2B,gCAMhCvhB,KAAKwhB,wBAA0B,+BAM/BxhB,KAAKyhB,SAAW,UAMhBzhB,KAAK0hB,mBAAqB,2BAM1B1hB,KAAK2hB,qBAAuB,6BAO5B3hB,KAAK4hB,2BAA6B,2BAOlC5hB,KAAK6hB,kBAAoB,6BAOzB7hB,KAAK8hB,uBAAyB,uBAM9B9hB,KAAK+hB,oBAAsB,0BAC/B,EAGJ,IAAIC,EAAmB,IAAIzB,EAC3B,K,kDC7JA,MAAM0B,UAAyBC,EAAAA,EAC3BzB,WAAAA,GACIC,QAKA1gB,KAAKmiB,kBAAoB,IAIzBniB,KAAKoiB,0BAA4B,IAIjCpiB,KAAKqiB,yBAA2B,IAIhCriB,KAAKsiB,0BAA4B,IAIjCtiB,KAAKuiB,yBAA2B,IAIhCviB,KAAKwiB,iCAAmC,IAIxCxiB,KAAKyiB,yBAA2B,IAKhCziB,KAAK0iB,6BAA+B,IAIpC1iB,KAAK2iB,0CAA4C,IAIjD3iB,KAAK4iB,sCAAwC,IAI7C5iB,KAAK6iB,sCAAwC,IAI7C7iB,KAAK8iB,mDAAqD,IAI1D9iB,KAAK+iB,oCAAsC,IAI3C/iB,KAAKgjB,+BAAiC,IAItChjB,KAAKijB,sCAAwC,IAE7CjjB,KAAKkjB,6BAA+B,wGACpCljB,KAAKmjB,4BAA8B,oDACnCnjB,KAAKojB,6BAA+B,8EACpCpjB,KAAKqjB,4BAA8B,2GACnCrjB,KAAKsjB,oCAAsC,qEAC3CtjB,KAAKujB,4BAA8B,6HACnCvjB,KAAKwjB,gCAAkC,wHACvCxjB,KAAKyjB,6CAA+C,kCACpDzjB,KAAK0jB,yCAA2C,wCAChD1jB,KAAK2jB,yCAA2C,qDAChD3jB,KAAK4jB,sDAAwD,wCAC7D5jB,KAAK6jB,uCAAyC,oCAC9C7jB,KAAK8jB,kCAAoC,oCACzC9jB,KAAK+jB,yCAA2C,yBACpD,EAGJ,IAAIC,EAAmB,IAAI/B,EAC3B,K,kCC7EA,IARA,MACIxB,WAAAA,CAAYxoB,EAAMgsB,EAASxY,GACvBzL,KAAK/H,KAAOA,GAAQ,KACpB+H,KAAKikB,QAAUA,GAAW,KAC1BjkB,KAAKyL,KAAOA,GAAQ,IACxB,E,oECHJ,MAAMyY,EAIFzD,WAAAA,GAKIzgB,KAAKmkB,MAAQ,KAabnkB,KAAKokB,KAAO,KAKZpkB,KAAK+G,IAAM,KAKX/G,KAAKqkB,UAAY,KAKjBrkB,KAAKskB,MAAQ,KAKbtkB,KAAKukB,SAAW,KAKhBvkB,KAAKwkB,UAAY,KAKjBxkB,KAAKykB,aAAe,KAKpBzkB,KAAK0kB,SAAW,KAKhB1kB,KAAK2kB,MAAQ,GAKb3kB,KAAK4kB,KAAO,KAMZ5kB,KAAK6kB,QAAU,KAKf7kB,KAAK8kB,SAAW,KAKhB9kB,KAAK+kB,eAAiB,KAKtB/kB,KAAKglB,iBAAmB,KAKxBhlB,KAAKilB,iBAAmB,KAIxBjlB,KAAKklB,gBAAkB,KAIvBllB,KAAKmlB,sBAAwB,IACjC,EA8BJjB,EAAYkB,IAAM,MAClBlB,EAAYmB,KAAO,OACnBnB,EAAYhR,SAAW,MACvBgR,EAAYoB,qBAAuB,iBACnCpB,EAAYqB,kBAAoB,wBAChCrB,EAAYsB,mBAAqB,eACjCtB,EAAYuB,mBAAqB,eACjCvB,EAAYwB,iCAAmC,4BAC/CxB,EAAYyB,+BAAiC,sBAC7CzB,EAAY0B,mBAAqB,eACjC1B,EAAY2B,QAAU,UACtB3B,EAAY4B,sBAAwB,kBACpC5B,EAAY6B,WAAa,O,GCnLrBC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBhtB,IAAjBitB,EACH,OAAOA,EAAa9uB,QAGrB,IAAIC,EAAS0uB,EAAyBE,GAAY,CAGjD7uB,QAAS,CAAC,GAOX,OAHA+uB,EAAoBF,GAAUjmB,KAAK3I,EAAOD,QAASC,EAAQA,EAAOD,QAAS4uB,GAGpE3uB,EAAOD,OACf,CCtBA4uB,EAAoBI,KAAO,CAAC,ECC5BJ,EAAoBK,EAAI,SAASjvB,EAASkvB,GACzC,IAAI,IAAInf,KAAOmf,EACXN,EAAoBO,EAAED,EAAYnf,KAAS6e,EAAoBO,EAAEnvB,EAAS+P,IAC5Eqf,OAAOC,eAAervB,EAAS+P,EAAK,CAAEuf,YAAY,EAAMpjB,IAAKgjB,EAAWnf,IAG3E,ECPA6e,EAAoBO,EAAI,SAASriB,EAAKT,GAAQ,OAAO+iB,OAAOG,UAAU3hB,eAAehF,KAAKkE,EAAKT,EAAO,E,uGCiCtG,MAAMmjB,EAAyC,CAC3CC,SAAU,CAAC,WAAY,SAAU,OAOrC,MAAMC,EASF,+BAAOC,CAAyBC,GAC5B,IAAIC,EAAS,KACb,IAAK,IAAI5uB,EAAI,EAAGA,EAAI2uB,EAAQ1uB,SAAUD,EAAG,CACrC,IAAI6uB,EAAKF,EAAQ3uB,GACb6uB,EAAGC,aAAeD,EAAGC,YAAYroB,gBAAkBsoB,EAAAA,EAAcrU,uBAAyBmU,EAAG9f,QAC5F8f,EAAG9f,MAAMtI,gBAAkBuoB,EAAAA,EAAoBrI,wBAA0BkI,EAAG9f,MAAMtI,gBAAkBuoB,EAAAA,EAAoBpI,0BACzHgI,EAASC,EAEjB,CACA,OAAOD,CACX,CAQA,kBAAOK,CAAYC,GACf,IAAIC,EAAS,EACTlb,EAAO,IAAItB,SAASuc,GAGpB5lB,EAAU2K,EAAKmb,SAASD,GAS5B,OAPAA,GAAU,GAEN7lB,EAAU,IACV6lB,GAAU,EAAK,GAAKlb,EAAKob,UAAUF,IAGvCA,GAAU,EACHD,EAAK7uB,MAAM8uB,EACtB,CAYA,0BAAOG,CAAoBC,EAAWC,GAClC,IAAIC,EAAWhB,EAAiBiB,cAAcF,GAC9C,OAAID,GAAaE,EAAS9iB,eAAe4iB,EAAUI,KAAKlpB,eAC7CgpB,EAASF,EAAUI,KAAKlpB,eAE5B,IACX,CAUA,yCAAOmpB,CAAmCC,EAAQC,GAC9C,MAAI,SAAUD,GAAUA,EAAOX,MAG3BW,EAAOX,KAAKa,OAASF,EAAOX,KAAKa,OAAOnpB,QAAQ,YAAa,IAAIA,QAAQ,OAAQ,IAE1EkpB,EAAOE,YAAYH,EAAOX,KAAKa,QAAQzc,QAE3C,IACX,CAWA,oBAAOoc,CAAcvc,GAEjB,GAAIA,QACA,MAAO,GAGX,IAAI8c,EAAK,IAAItd,SAASQ,EAAKG,QAAUH,GAEjC+b,EAAO,CAAC,EAGRgB,EAAa,EACjB,OAAc,CAEV,IAAIxgB,EACAygB,EACA7mB,EACA8mB,EA+BApwB,EAAGqwB,EA9BHC,EAAWJ,EAEf,GAAIA,GAAcD,EAAG3c,OAAOM,WACxB,MASJ,GALAlE,EAAOugB,EAAGZ,UAAUa,GACpBC,EAAUD,EAAaxgB,EACvBwgB,GAAc,EAGmB,aAA7BD,EAAGZ,UAAUa,GAQjB,GAJAA,GAAc,EAGd5mB,EAAU2mB,EAAGb,SAASc,GACN,IAAZ5mB,GAA6B,IAAZA,EAArB,CAWA,IAPA4mB,IAEAA,GAAc,EAGdE,EAAW,GAENpwB,EAAI,EAAGA,EAAI,EAAGA,IACfqwB,EAAMJ,EAAGb,SAASc,EAAalwB,GAAGqR,SAAS,IAC3C+e,GAA4B,IAAfC,EAAIpwB,OAAgB,IAAMowB,EAAMA,EAIjD,IAFAH,GAAc,EACdE,GAAY,IACPpwB,EAAI,EAAGA,EAAI,EAAGA,IACfqwB,EAAMJ,EAAGb,SAASc,EAAalwB,GAAGqR,SAAS,IAC3C+e,GAA4B,IAAfC,EAAIpwB,OAAgB,IAAMowB,EAAMA,EAIjD,IAFAH,GAAc,EACdE,GAAY,IACPpwB,EAAI,EAAGA,EAAI,EAAGA,IACfqwB,EAAMJ,EAAGb,SAASc,EAAalwB,GAAGqR,SAAS,IAC3C+e,GAA4B,IAAfC,EAAIpwB,OAAgB,IAAMowB,EAAMA,EAIjD,IAFAH,GAAc,EACdE,GAAY,IACPpwB,EAAI,EAAGA,EAAI,EAAGA,IACfqwB,EAAMJ,EAAGb,SAASc,EAAalwB,GAAGqR,SAAS,IAC3C+e,GAA4B,IAAfC,EAAIpwB,OAAgB,IAAMowB,EAAMA,EAIjD,IAFAH,GAAc,EACdE,GAAY,IACPpwB,EAAI,EAAGA,EAAI,EAAGA,IACfqwB,EAAMJ,EAAGb,SAASc,EAAalwB,GAAGqR,SAAS,IAC3C+e,GAA4B,IAAfC,EAAIpwB,OAAgB,IAAMowB,EAAMA,EAEjDH,GAAc,EAEdE,EAAWA,EAAS3pB,cAGpBypB,GAAc,EAGdhB,EAAKkB,GAAYH,EAAG3c,OAAOjT,MAAMiwB,EAAUH,GAC3CD,EAAaC,CA7Cb,MAFID,EAAaC,OARbD,EAAaC,CAwDrB,CAEA,OAAOjB,CACX,CAEA,uCAAOqB,CAAiCC,EAAc1B,GAClD,IAEI,IAAK0B,GAAwC,IAAxBA,EAAavwB,OAC9B,OAAO,KAGX,IAAID,EAAI,EACJywB,EAAgB,KAEpB,KAAOzwB,EAAIwwB,EAAavwB,SAAWwwB,GAAe,CAC9C,MAAMC,EAAYF,EAAaxwB,GAE/B,GAAI0wB,GAAaA,EAAUC,mBAAqBD,EAAUC,kBAAkB1wB,OAAS,EAAG,CACpF,MAAM2wB,EAAuBF,EAAUC,kBAAkB1jB,QAAQ4hB,GACtDA,EAAGC,aAAeD,EAAGC,cAAgBA,IAGhD,GAAI8B,GAAwBA,EAAqB3wB,OAAS,EAAG,CACzD,IAAIgH,EAAI,EACR,KAAOA,EAAI2pB,EAAqB3wB,SAAWwwB,GAAe,CACtD,MAAME,EAAoBC,EAAqB3pB,GAC3C0pB,EAAkBE,OACfF,EAAkBE,MAAMC,UACxBvC,EAAuCC,SAASvf,SAAS0hB,EAAkBE,MAAMC,WACjFH,EAAkBE,MAAMd,SAC3BU,EAAgBE,EAAkBE,MAAMd,QAE5C9oB,GAAK,CACT,CACJ,CACJ,CACAjH,GAAK,CACT,CACA,OAAOywB,CACX,CAAE,MAAOthB,GACL,OAAO,IACX,CACJ,CAEA,2BAAO4hB,CAAqBC,GACxB,MAAMC,EAAgBD,EAAOpqB,QAAQ,KAAM,IAM3C,OAJmB,IAAIiM,WAAWoe,EAAc3pB,MAAM,gBAAgBQ,KAAI,SAAUopB,GAChF,OAAOC,SAASD,EAAG,GACvB,KAEkB5d,MACtB,EAGJ,QCnOA,EAbA,MAOI6U,WAAAA,CAAYiJ,EAAaC,GACrB3pB,KAAK0pB,YAAcA,EACnB1pB,KAAK2pB,WAAaA,CACtB,GCwBJ,EA9BA,MAgBIlJ,WAAAA,CAAYmJ,EAAmBC,EAAmBC,EAAuBC,EAAiBC,EAAcC,GACpGjqB,KAAKiqB,cAAgBA,GAAiBA,EAAc1xB,OAAS,EAAI0xB,EAAgB,CAAC3C,EAAAA,EAAoBxI,+BAClG8K,GAAqBA,EAAkBrxB,SACvCyH,KAAK4pB,kBAAoBA,GAEzBC,GAAqBA,EAAkBtxB,SACvCyH,KAAK6pB,kBAAoBA,GAE7B7pB,KAAK8pB,sBAAwBA,EAC7B9pB,KAAK+pB,gBAAkBA,EACvB/pB,KAAKgqB,aAAeA,CACxB,G,oBCmBJ,EAlDA,MAMIvJ,WAAAA,CAAY1Z,EAAKmjB,EAAQC,EAAczhB,EAAS0hB,EAAiBC,EAAaC,EAAW7e,GAKrFzL,KAAK+G,IAAMA,EAKX/G,KAAKkqB,OAASA,EAKdlqB,KAAKmqB,aAAeA,EAKpBnqB,KAAK0I,QAAUA,EAKf1I,KAAKoqB,gBAAkBA,EAKvBpqB,KAAKqqB,YAAcA,EAKnBrqB,KAAKsqB,UAAYA,EAKjBtqB,KAAKyL,KAAOA,CAChB,GCtBJ,EA1BA,MAOIgV,WAAAA,CAAY1Z,EAAK2B,EAAS+C,GAKtBzL,KAAK+G,IAAMA,EAKX/G,KAAK0I,QAAUA,EAKf1I,KAAKyL,KAAOA,CAChB,G,uCCUJ,SAAS8e,EAAqB9c,GAG1B,MAAM2a,GADN3a,EAASA,GAAU,CAAC,GACE2a,OAChBoC,EAAY/c,EAAO+c,UACnBC,EAAYhd,EAAOgd,UACnBC,EAAwBjd,EAAOid,sBAC/BC,EAAQld,EAAOkd,MACfC,EAAWnd,EAAOmd,SAClBhd,EAASH,EAAOG,OAChBid,EAA0Bpd,EAAOod,wBACjCC,EAAWrd,EAAOqd,SACxB,IAGIC,EACAnnB,EACAonB,EACAC,EACAC,EACAC,EACAC,EACAtC,EACAuC,EACAC,EACAC,EACAC,EAdAC,EAAkBhe,EAAOge,gBACzBC,EAAiB,GA2BrB,SAASC,IACL,KAAKf,GAAaA,EAAS3lB,eAAe,OAAU4lB,GAA4BA,EAAwB5lB,eAAe,uDACnH,MAAM,IAAI2mB,MAAM,8BAExB,CAkEA,SAASC,EAAoCC,EAA6BC,GAEjER,GAAsBN,EAKlBM,GACLS,IAUR,SAAiCF,EAA6BC,GAC1D,GAAId,EACA,OA6ER,IAAmCgB,EA1E/BhB,GAA+B,EAM/B,MAAMiB,EA4EV,SAAqCJ,GACjC,MAAMI,EAAmC,GACzC,IAAK,IAAI5zB,EAAI,EAAGA,EAAIwzB,EAA4BvzB,OAAQD,IAAK,CACzD,MAAM6zB,EAAyBC,EAA2BN,EAA4BxzB,IACtF4zB,EAAiC7mB,KAAK,CAClCgnB,GAAIP,EAA4BxzB,GAAG+zB,GACnCC,QAAS,CAACH,GACVI,SAAUT,EAA4BxzB,GAAGi0B,UAEjD,CAEA,OAAOL,CACX,CAxF6CM,CAHzCV,GAuE+BG,EAvEyBH,GAwE7BW,MAAK,CAACC,EAAKC,KACpB5B,GAAqCA,EAAkC2B,EAAIL,GAAGO,eAAiB7B,EAAkC2B,EAAIL,GAAGO,cAAcC,UAAY,EAAK9B,EAAkC2B,EAAIL,GAAGO,cAAcC,SAAWZ,EAAoB1zB,SAC7PwyB,GAAqCA,EAAkC4B,EAAIN,GAAGO,eAAiB7B,EAAkC4B,EAAIN,GAAGO,cAAcC,UAAY,EAAK9B,EAAkC4B,EAAIN,GAAGO,cAAcC,SAAWZ,EAAoB1zB,WArE/Q,IAAIu0B,EACJrB,EAAgBsB,uBAAuBb,GAClCc,MAAMC,IACHH,EAAkBG,EAAMxhB,KAYpC,SAA8BqhB,GAC1B,IAAII,EAAuBJ,GAAmBA,EAAgBI,qBAAuBJ,EAAgBI,qBAAuBJ,EAAgBjF,UAAU+E,aAEtJ,OADAxB,EAAO+B,KAAK,oDAAsDD,EAAuB,+BAClFzB,EAAgB2B,gBAAgBN,EAC3C,CAfmBO,CAAqBP,MAE/BE,MAAMnF,KAef,SAA6BA,EAAWiF,GACpCvB,EAAoB1D,EACpBoD,GAA+B,EAE/BL,EAAS0C,QAAQ1f,EAAO0T,oBAAqB,CAAE7V,KAAMqhB,IAGrD,MAAMP,EAAWgB,EAAyBhC,GACtCgB,GAAYA,EAASiB,mBAAqBjB,EAASiB,kBAAkBj1B,OAAS,GAC9EkzB,EAAgBgC,qBAAqBrF,EAAOE,YAAYiE,EAASiB,mBAAmB5hB,QAGxFogB,GACJ,CA3BY0B,CAAoB7F,EAAWiF,EAAgB,IAElDa,OAAOV,KA8ChB,SAAwCA,EAAOlB,GAC3CR,EAAoB,KACpBN,GAA+B,EAC1Bc,GACDnB,EAAS0C,QAAQ1f,EAAO0T,oBAAqB,CACzC7V,KAAM,KACNmiB,MAAO,IAAIC,EAAAA,EAAY5L,EAAAA,EAAiBc,oCAAqCd,EAAAA,EAAiB4B,uCAAyC,kCAAoCoJ,EAAMW,QAG7L,CAtDYE,CAA+Bb,EAAOlB,EAAa,GAG/D,CAzCQgC,CAAwBjC,EAA6BC,EAO7D,CA6DA,SAASC,IAEL,IAAIgC,EACJ,IAAK,IAAI11B,EAAI,EAAGA,EAAI+yB,EAA0B9yB,OAAQD,IAClD,IAAK01B,EAAQ,EAAGA,EAAQ3C,EAA0B/yB,GAAGC,OAAQy1B,IACzD,GAAIzC,IAAsBF,EAA0B/yB,GAAG01B,GAAO3B,GAAI,CA4E7C4B,EA3ES5C,EAA0B/yB,GAAG01B,GA4E/DnD,EAAwBqD,WAAW3C,IAiB3C,SAAgC0C,GAG5B,GAAIA,EAAkB1B,UAAY0B,EAAkB1B,SAAStnB,eAAe,cAA6E,IAA7DwhB,OAAO0H,KAAKF,EAAkB1B,SAAS6B,WAAW71B,OAAc,CACxJ,MAAMuvB,EAAW,CAAEuG,KAAM5H,OAAO0H,KAAKF,EAAkB1B,SAAS6B,YAChEH,EAAkBnG,UAAW,IAAIwG,aAAcC,OAAO12B,KAAKC,UAAUgwB,GACzE,CACJ,CAvBQ0G,CAAuBP,GAIvBA,EAAkB3D,UAElBmE,EAAeR,GAIqB,OAA/BA,EAAkBnG,UAEvB4G,EAAiBT,GAvFT,KACJ,CAwEZ,IAAiCA,EArE7B5C,EAA4B,EAChC,CAyCA,SAASe,EAA2BuC,GAChC,MAAMpC,EAAWoC,EAAcpC,SACzB3C,EAAoB,GACpBC,EAAoB,GACpBI,EAAiBsC,GAAYA,EAAStC,eAAiBsC,EAAStC,cAAc1xB,OAAS,EAAKg0B,EAAStC,cAAgB,CAAC3C,EAAAA,EAAoBxI,+BAC1I8P,EAAmBrC,GAAYA,EAASqC,iBAAmBrC,EAASqC,gBAAgBr2B,OAAS,EAAKg0B,EAASqC,gBAAkBtD,EAC7HuD,EAAmBtC,GAAYA,EAASsC,iBAAmBtC,EAASsC,gBAAgBt2B,OAAS,EAAKg0B,EAASsC,gBAAkBvD,EAC7HwD,EAAgBH,EAAcnD,YAC9B1B,EAAyByC,GAAYA,EAASzC,sBAAyByC,EAASzC,sBAAwB,WACxGC,EAAmBwC,GAAYA,EAASxC,gBAAmBwC,EAASxC,gBAAqC,cAAlB+E,EAAiC,WAAa,WAU3I,OARAhG,EAAa5hB,SAAS6nB,IACdA,EAAM3K,OAASqG,EAAU/S,MACzBkS,EAAkBvkB,KAAK,IAAI2pB,EAAgBD,EAAME,MAAOL,IACjDG,EAAM3K,OAASqG,EAAUhT,OAChCoS,EAAkBxkB,KAAK,IAAI2pB,EAAgBD,EAAME,MAAOJ,GAC5D,IAGG,IAAIK,EAAuBtF,EAAmBC,EAAmBC,EAAuBC,EAAiB,CAAC+E,GAAgB7E,EACrI,CA2CA,SAASwE,EAAeR,GACpBtC,IACAF,EAAgBgD,eAAeR,EACnC,CAYA,SAASS,EAAiBT,GAGtB,GAAIA,GAiFR,SAAoCkB,GAChC,IAAKA,EACD,OAAO,EAGX,IACI,MAAMC,EAAW3D,EAAgB4D,mBACjC,IAAK,IAAI/2B,EAAI,EAAGA,EAAI82B,EAAS72B,OAAQD,IACjC,GAAI82B,EAAS92B,GAAGg3B,aAAeH,EAC3B,OAAO,EAGf,OAAO,CACX,CAAE,MAAO1nB,GACL,OAAO,CACX,CACJ,CAjG6B8nB,CAA2BtB,EAAkBkB,OAClE,OAGJ,MAAMK,EAAgBzI,EAAiBa,oBAAoB2D,EAAmB0C,EAAoBA,EAAkBnG,SAAW,MAC/H,GAAI0H,EAAe,CAGf,GAAIC,EAAqBD,GACrB,OAGJ,IACIvB,EAAkBnG,SAAW0H,EAC7B/D,EAAgBiD,iBAAiBT,EACrC,CAAE,MAAOL,GACLhD,EAAS0C,QAAQ1f,EAAOqT,oBAAqB,CACzCxV,KAAM,KACNmiB,MAAO,IAAIC,EAAAA,EAAY5L,EAAAA,EAAiBe,+BAAgCf,EAAAA,EAAiB6B,kCAAoC8J,EAAM3J,UAE3I,CACJ,MAAWgK,GAAqBA,EAAkBnG,SAC9C2D,EAAgBiD,iBAAiBT,GAEjCrD,EAAS0C,QAAQ1f,EAAOqT,oBAAqB,CACzCxV,KAAM,KACNmiB,MAAO,IAAIC,EAAAA,EAAY5L,EAAAA,EAAiBe,+BAAgCf,EAAAA,EAAiB6B,kCAAoC,2BAA6ByH,EAAoBA,EAAkBqB,aAAe,MAAQ,sFAGnO,CAQA,SAASW,EAAyB1F,GAC9B,GAAIA,EAAW,CACX,MAAM6H,EAAkB7H,EAAU+E,aAElC,GAAI7B,EACA,OAAQ2E,KAAmB3E,EAAqCA,EAAkC2E,GAAmB,IAE7H,CACA,OAAO,IACX,CA2DA,SAASD,EAAqBD,GAE1B,IAAKA,EACD,OAAO,EAGX,IACI,MAAMG,EAAkBlE,EAAgBmE,iBACxC,IAAK,IAAIt3B,EAAI,EAAGA,EAAIq3B,EAAgBp3B,OAAQD,IACxC,GAAIuyB,EAAwBgF,eAAeL,EAAeG,EAAgBr3B,IAEtE,OADA8yB,EAAOT,MAAM,4DACN,EAIf,OAAO,CACX,CAAE,MAAOljB,GACL,OAAO,CACX,CACJ,CA8DA,SAASqoB,EAAgBC,GACrBpE,IACIoE,GACAtE,EAAgBqE,gBAAgBC,GAChCnF,EAASoF,GAAGpiB,EAAO6T,SAAUwO,EAAYrsB,IACtB,OAAZmsB,IACPtE,EAAgBqE,gBAAgBC,GAChCnF,EAASsF,IAAItiB,EAAO6T,SAAUwO,EAAYrsB,GAElD,CA+FA,SAASusB,EAAc1oB,GACnB2jB,EAAOT,MAAM,qBAGb,MAAMyF,EAAa3oB,EAAEgE,KACrBmf,EAAS0C,QAAQ1f,EAAOmT,YAAa,CAAEtV,KAAM2kB,IAC7C,MAAM/F,EAAe+F,EAAW/F,YAAe+F,EAAW/F,YAAc/C,EAAAA,EAAoBnI,wBAAwBC,gBAC9G6E,EAAUmM,EAAWnM,QACrBoM,EAAeD,EAAWC,aAC1B9D,EAAWgB,EAAyBhC,GACpC+E,EAA6BzF,EAAwB0F,8BAA8BhF,EAAmBgB,EAAUlC,GAChHmG,EAAY,CAAEH,aAAcA,EAAchG,YAAaA,GAG7D,GAAKpG,GAAkC,IAAvBA,EAAQ/X,WAAxB,CAMA,IAAKokB,EAGD,OAFAlF,EAAOT,MAAM,qEAAuEljB,EAAEgE,KAAK4e,YAAc,oBAAsBgG,EAAaI,qBAC5IC,EAAiCF,GAKrC,GAAI3F,EAAwBqD,WAAW3C,GAAoB,CACvD,MAAM6C,EAAYvD,EAAwB8F,8BAA8BpF,EAAmBgB,EAAUtI,GACrG,GAAImK,GAAaA,EAAUwC,UAAYxC,EAAUwC,SAASr4B,OAAS,EAI/D,OAHA6yB,EAAOT,MAAM,yDACb+F,EAAiCF,QACjC/E,EAAgBoF,iBAAiBR,EAAcjC,EAGvD,EAuBJ,SAA8BgC,EAAYU,EAAmBvE,GACzD,MAAM8D,EAAeD,EAAWC,aAC1BhG,EAAe+F,EAAW/F,YAAe+F,EAAW/F,YAAc/C,EAAAA,EAAoBnI,wBAAwBC,gBAC9GoR,EAAY,CAAEH,aAAcA,EAAchG,YAAaA,GACvDqF,EAAkBnE,EAAoBA,EAAkBqB,aAAe,KAG7E,IAAI7lB,EAiNR,SAA8BwlB,EAAUlC,EAAagG,EAAcD,EAAYU,GAC3E,IAAI/pB,EAAM,KACV,MAAMkd,EAAUmM,EAAWnM,QAG3B,GAAIsI,GAAYA,EAASwE,UAAW,CAChC,MAAMA,EAAYxE,EAASwE,UACF,iBAAdA,GAAwC,KAAdA,EACjChqB,EAAMgqB,EACsB,iBAAdA,GAA0BA,EAAU9rB,eAAeolB,KACjEtjB,EAAMgqB,EAAU1G,GAExB,MAGK,GAAIkC,GAAYA,EAASyE,OAA4B,KAAnBzE,EAASyE,MAC5CjqB,EAAMwlB,EAASyE,WASf,GAHAjqB,EAAMggB,EAAiB8B,iCAAiCC,EAAcyC,EAAkB0F,cAGnFlqB,IAAQ8jB,EAAwBqD,WAAW3C,GAAoB,CAChE,MAAM2F,EAAWnK,EAAiBQ,YAAY8I,EAAavI,UAC3D/gB,EAAMwkB,EAAkB4F,gCAAgCD,GAGnDnqB,IACDA,EAAMqpB,EAAWY,MAEzB,CAKJ,OAFAjqB,EAAM+pB,EAAkBM,wBAAwBrqB,EAAKkd,EAASoG,GAEvDtjB,CACX,CAxPcsqB,CAAqB9E,EAAUlC,EAAagG,EAAcD,EAAYU,GAGhF,IAAK/pB,EAED,YADA2pB,EAAiCF,EAAW,IAAI3C,EAAAA,EAAY5L,EAAAA,EAAiBa,mDAAoDb,EAAAA,EAAiB2B,wDAKtJ,MAAM0N,EAAa,CAAC,EACpB,IAAIlH,GAAkB,EAClBmC,GACAgF,EAAeD,EAAY/E,EAASiF,oBAExC,MAAMvN,EAAUmM,EAAWnM,QAE3BsN,EAAeD,EADY/F,EAAkBkG,6BAA6BxN,IAG1EwC,OAAO0H,KAAKmD,GAAYpqB,SAASE,IACzB,kBAAoBA,EAAIrI,gBACxBqrB,GAAkB,EACtB,IAIAmC,GAA+C,kBAA5BA,EAASnC,kBAC5BA,EAAkBmC,EAASnC,iBAG/B,MAAMsH,EAAS,SAAUC,GACrB,GAAKlG,EAIL,GAAIkG,EAAIC,QAAU,KAAOD,EAAIC,QAAU,IAAK,CACxC,MAAMC,EAAkB5rB,EAAAA,EAAMuC,iBAAiBmpB,EAAIG,sBAAwBH,EAAIG,wBAA0B,MACzG,IAAIC,EAAkB,IAAIC,EAAgBL,EAAIM,YAAaJ,EAAiBF,EAAIO,UAEhFC,EAD+BzH,EAAsB0H,4BACfL,GACjC/E,MAAK,KACF,MAAMqF,EAAiBvB,EAAkBwB,kBAAkBP,EAAgBtmB,KAAMikB,EAAiBrF,GAC3E,OAAnBgI,GACA3B,EAAiCF,GACjC/E,EAAgBoF,iBAAiBR,EAAcgC,IAE/CE,EAAaZ,EAAKnB,EAAWd,EAAiBrF,EAAayG,EAC/D,GAEZ,MACIyB,EAAaZ,EAAKnB,EAAWd,EAAiBrF,EAAayG,EAEnE,EAEM0B,EAAU,SAAUb,GACtBjB,EAAiCF,EAAW,IAAI3C,EAAAA,EAAY5L,EAAAA,EAAiBgB,sCACzEhB,EAAAA,EAAiB8B,yCAA2C2L,EAAkB,oCAC9EiC,EAAIc,WAAa,MAAQd,EAAIC,OAAS,oBAAsBD,EAAIe,YACxE,EAEMC,EAAU,SAAUhB,GACtBjB,EAAiCF,EAAW,IAAI3C,EAAAA,EAAY5L,EAAAA,EAAiBgB,sCACzEhB,EAAAA,EAAiB8B,yCAA2C2L,EAAkB,kCAC9EiC,EAAIc,WAAa,MAAQd,EAAIC,OAAS,oBAAsBD,EAAIe,YACxE,EAEME,EAAarH,EAAkBsH,6BAA6B5O,GAC5D6O,EAAYhC,EAAkBiC,cAAc1I,GAC5CF,EAAe2G,EAAkBkC,gBAAgBtD,EAAiBrF,GAClE4I,EAAU1G,IAAa2G,MAAM3G,EAAS4G,aAAe5G,EAAS4G,YApvB7B,IAqvBjC7I,EAAY+F,EAAaI,gBAAkB,KAEjD,IAAI2C,EAAiB,IAAIC,EAAetsB,EAAK+rB,EAAW3I,EAAcmH,EAAYlH,EAAiBC,EAAaC,EAAWsI,GAC3H,MAAMU,EAAiBJ,MAAMpI,EAASvnB,MAAMgwB,UAAUD,cAAcpP,EAAAA,EAAY2B,UA1vBjD,EA0vB6DiF,EAASvnB,MAAMgwB,UAAUD,cAAcpP,EAAAA,EAAY2B,SAE/IsM,EAD8BzH,EAAsB8I,2BACfJ,GAChCpG,MAAK,KACFyG,EAAkBL,EAAgBE,EAAeL,EAASvB,EAAQc,EAASG,EAAQ,GAE/F,CAzGIe,CAAqBtD,EAAYE,EAA4B/D,EArB7D,MAFImE,EAAiCF,EAAW,IAAI3C,EAAAA,EAAY5L,EAAAA,EAAiBU,0CAA2CV,EAAAA,EAAiBwB,8CAwBjJ,CAQA,SAASiN,EAAiCjlB,GAAoB,IAAdmiB,EAAK30B,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG,KACpD2xB,EAAS0C,QAAQ1f,EAAO2T,yBAA0B,CAAE9V,KAAMA,EAAMmiB,MAAOA,GAC3E,CA0GA,SAAS6F,EAAkBE,EAASC,EAAcX,EAASvB,EAAQc,EAASG,GACxE,MAAMhB,EAAM,IAAIkC,eACVC,EAAiBtJ,EAAUuJ,gCAEjC,GAAIvJ,EAAUwJ,kBACOF,EAAeG,KAAOH,EAAeG,KAAOnJ,EAASvnB,MAAMgwB,UAAUW,KAAKD,QAC1EpnB,EAAAA,EAAUkM,gBAAiB,CACxC,MAAMob,EAAa3J,EAAU4J,kBAAkB,CAC3CrtB,IAAK4sB,EAAQ5sB,IACbqd,KAAMF,EAAAA,EAAY2B,UAGlBsO,IACAR,EAAQ5sB,IAAMd,EAAAA,EAAMa,iCAAiC6sB,EAAQ5sB,IAAK,CAACotB,IAE3E,CAGJxC,EAAI0C,KAAKV,EAAQzJ,OAAQyJ,EAAQ5sB,KAAK,GACtC4qB,EAAIxH,aAAewJ,EAAQxJ,aAC3BwH,EAAIvH,gBAAkBuJ,EAAQvJ,gBAC1B6I,EAAU,IACVtB,EAAIsB,QAAUA,GAElB,IAAK,MAAM7rB,KAAOusB,EAAQjrB,QACtBipB,EAAI2C,iBAAiBltB,EAAKusB,EAAQjrB,QAAQtB,IAG9C,GAAIojB,EAAUwJ,kBACOF,EAAeG,KAAOH,EAAeG,KAAOnJ,EAASvnB,MAAMgwB,UAAUW,KAAKD,QAC1EpnB,EAAAA,EAAUmM,iBAAkB,CACzC,MAAMub,EAAc/J,EAAUgK,oBAAoB,CAC9CztB,IAAK4sB,EAAQ5sB,IACbqd,KAAMF,EAAAA,EAAY2B,UAGtB,GAAI0O,EACA,IAAK,MAAME,KAAUF,EAAa,CAC9B,IAAIltB,EAAQktB,EAAYE,GACpBptB,GACAsqB,EAAI2C,iBAAiBG,EAAQptB,EAErC,CAER,CAGJ,MAAMqtB,EAAgB,WAElBd,IACA,MAAMe,EAAiBzB,MAAMpI,EAASvnB,MAAMgwB,UAAUqB,eAAe1Q,EAAAA,EAAY2B,UA7zB/C,IA6zB2DiF,EAASvnB,MAAMgwB,UAAUqB,eAAe1Q,EAAAA,EAAY2B,SACjJqF,EAA6B2J,YAAW,WACpCpB,EAAkBE,EAASC,EAAcX,EAASvB,EAAQc,EAASG,EACvE,GAAGgC,EACP,EAEAhD,EAAImD,OAAS,WACT3J,EAAoB,KAChBnrB,KAAK4xB,QAAU,KAAO5xB,KAAK4xB,QAAU,KAAOgC,GAAgB,EAC5DlC,EAAO1xB,OAEPorB,EAAO2J,KAAK,2BAA6B/0B,KAAK4xB,OAAS,sCAAwCgC,GAC/Fc,IAER,EAEA/C,EAAIqD,UAAYrD,EAAIsD,QAAU,WAC1B9J,EAAoB,KAChByI,GAAgB,EAChBjB,EAAQ3yB,OAERorB,EAAO2J,KAAK,4EAA8EnB,GAC1Fc,IAER,EAEA/C,EAAIuD,QAAU,WACV1C,EAAQxyB,KACZ,EAGA4qB,EAAS0C,QAAQ1f,EAAO4T,wBAAyB,CAC7Cza,IAAK4sB,EAAQ5sB,IACb2B,QAASirB,EAAQjrB,QACjBysB,QAASxB,EAAQloB,KACjB6e,UAAWqJ,EAAQrJ,YAGvBa,EAAoBwG,EACpBA,EAAIyD,KAAKzB,EAAQloB,KACrB,CAMA,SAAS4pB,IACDlK,IACAA,EAAkBmK,UAAYnK,EAAkB8J,QAAU9J,EAAkBoK,gBAAar8B,EACzFiyB,EAAkBqK,QAClBrK,EAAoB,MAGpBD,IACAuK,aAAavK,GACbA,EAA6B,KAErC,CA2DA,SAASqG,EAAeD,EAAY5oB,GAChC,GAAIA,EACA,IAAK,MAAMtB,KAAOsB,EACd4oB,EAAWlqB,GAAOsB,EAAQtB,EAGtC,CAWA,SAASmrB,EAAaZ,EAAKnB,EAAWd,EAAiBrF,EAAayG,GAChE,IAAI4E,EAAW,OACXjqB,EAAO,KAEPkmB,EAAIO,WACJwD,EAAW5E,EAAkB6E,iBAAiBhE,EAAIO,SAAUxC,EAAiBrF,GAC7E5e,EAAO,CACHmqB,eAAgBjE,EAAIO,UAAY,KAChC2D,aAAclE,EAAIC,QAAU,KAC5BkE,aAAcnE,EAAIc,YAAc,OAIxC/B,EAAiCF,EAAW,IAAI3C,EAAAA,EAAY5L,EAAAA,EAAiBgB,sCACzEhB,EAAAA,EAAiB8B,yCAA2C2L,EAAkB,qCAC9EiC,EAAIc,WAAa,MAAQd,EAAIC,OAAS,oBAAsBD,EAAIe,WAAa,kBAAoBgD,EACjGjqB,GAER,CASA,SAAS0mB,EAAc4D,EAASC,GAC5B,OAAKD,EAGEA,EAAQE,QAAO,CAACC,EAAMC,IAClBD,EAAKlJ,MAAK,IACNmJ,EAAKH,MAEjBI,QAAQv9B,WANAu9B,QAAQv9B,SAOvB,CAQA,SAASo3B,EAAWhD,EAAOoJ,GACvB,GAAIvL,EAASvnB,MAAMgwB,UAAU+C,WAAWC,wBACpC,OAMJ,GAHAnL,EAAOT,MAAM,kBAGTsC,EAAM7lB,IAAIovB,eAAiBlP,EAAAA,EAAoBxI,8BAE/C,YADAsM,EAAO2J,KAAK,wEAA4E9H,EAAM7lB,IAAIovB,cAItG,GAA4B,IAAxB1N,EAAavwB,SACb6yB,EAAO2J,KAAK,4EACZsB,OAAyB,IAAVA,EAAwB,EAAIA,EAAQ,GAjgCrB,GAsgC1B,YAHA3K,EAAermB,KAAKwvB,YAAW,KAC3B5E,EAAWhD,EAAOoJ,EAAM,GAngCF,MA2gClC,IAAII,EAAaxJ,EAAM7lB,IAAI0gB,SAM3B,GALItb,YAAYkqB,OAAOD,KACnBA,EAAaA,EAAW7qB,QAIxB2f,EAAmB,CACnB,MAAMiE,EAAgBzI,EAAiBa,oBAAoB2D,EAAmBkL,GAC9E,GAAIjH,GAEIC,EAAqBD,GACrB,MAGZ,CAEApE,EAAOT,MAAM,iBAAkBgM,OAAOC,aAAa7xB,MAAM,KAAM,IAAIoG,WAAWsrB,KAE9E,MAAM3K,EAA8BjB,EAAwBgM,6CAA6CJ,EAAY1L,EAAmCS,GAC7G,IAAvCM,EAA4BvzB,OAt6BpC,SAAkCuzB,GAC9BT,EAA0BhmB,KAAKymB,GAC/BD,EAAoCC,GAA6B,EACrE,CAw6BIgL,CAAyBhL,GAJrBV,EAAOT,MAAM,0FAKrB,CA2FA,SAASoM,EAAyBC,EAAkBhM,GAChD,GAAIgM,EAAiBhvB,MAAQ,EACzB,OAAO,EAQX,GALsBgjB,EAAahjB,KAAO,GAAK,IAAIgvB,GAAkBC,OAAOC,IACxE,MAAMC,EAAYnM,EAAaznB,IAAI2zB,GACnC,YAA4B,IAAdC,GAA2C,KAAdA,CAAgB,IAI3D,OAAO,EAGX,MAAMC,EAAgB3L,EAAgB4D,mBAEtC,GAAI+H,GAAiBA,EAAc7+B,OAAS,EAAG,CAC3C,MAAM8+B,EAAsBD,EAAc7xB,QAAQ8qB,GACvC,IAAI2G,GAAkBzvB,SAAS8oB,EAAa6G,mBAKvD,GAH0CG,EAAoBC,MAAMjH,IACxDA,EAAakH,kCAE+C,IAA/BF,EAAoB9+B,OACzD,OAAO,CAEf,CACA,OAAQuyB,EAASvnB,MAAMgwB,UAAU+C,WAAWkB,mBAAqBR,GAAoBA,EAAiBhvB,KAAO,GAAKgjB,GAAgBA,EAAahjB,KAAO,CAC1J,CA0BA,OAxBApE,EAAW,CACP6zB,iBA/CJ,SAA0BT,GACtB,IACI,QAAKD,EAAyBC,EAAkBhM,IAIzC,IAAIgM,GAAkBC,OAAOC,GACdlM,EAAaznB,IAAI2zB,KACd5P,EAAAA,EAAoBvH,mBAAmBE,SAEpE,CAAE,MAAO2N,GAEL,OADAxC,EAAOwC,MAAMA,IACN,CACX,CACJ,EAkCI8J,gBAhEJ,SAAyBV,GACrB,IACI,OAAKD,EAAyBC,EAAkBhM,IAIzC,IAAIgM,GAAkBM,MAAMJ,IAC/B,MAAMC,EAAYnM,EAAaznB,IAAI2zB,GACnC,OAAOC,GAAaA,IAAc7P,EAAAA,EAAoBvH,mBAAmBO,gBAAkB6W,IAAc7P,EAAAA,EAAoBvH,mBAAmBI,iBAAiB,GAEzK,CAAE,MAAOyN,GAEL,OADAxC,EAAOwC,MAAMA,IACN,CACX,CACJ,EAmDI+J,oBA5xBJ,WACI7O,EAAe,EACnB,EA2xBI8O,gBArrBJ,SAAyBvH,GACrB1E,IACAF,EAAgBmM,gBAAgBvH,EACpC,EAmrBI3B,mBACAmJ,cAzHJ,WACI,OAAOhN,EAA0BA,EAAwBgN,gBAAkB,EAC/E,EAwHIC,mDA7wBJ,SAA4DC,GAExD,OADApM,IACOd,EAAwBiN,mDAAmDC,EAAKhN,EAAmCS,EAC9H,EA2wBIwM,4BAzkCJ,WACI,IAAKlP,GAAwC,IAAxBA,EAAavwB,OAC9B,OAGJ,IAAIuzB,EAA8B,GAClChD,EAAa5hB,SAAS8hB,IAClB,MAAMiP,EAAqBpN,EAAwBiN,mDAAmD9O,EAAUC,kBAAmB8B,EAAmCS,GAElKyM,EAAmB1/B,OAAS,IACe,IAAvCuzB,EAA4BvzB,SAC5BuzB,EAA8BmM,GAGlC5M,EAA0BhmB,KAAK4yB,GACnC,IAGAnM,GAA+BA,EAA4BvzB,OAAS,GACpEszB,EAAoCC,GAA6B,EAEzE,EAqjCIoM,mBA5lCJ,SAA4BlP,GAKxB,IAAKA,EACD,MAAM,IAAI4C,MAAM,0CAGpBD,IACA7C,EAAazjB,KAAK2jB,EACtB,EAklCIyF,iBACA0J,iBA5sBJ,SAA0B9H,GACtB1E,IACAF,EAAgB0M,iBAAiB9H,EACrC,EA0sBI+H,MArlBJ,WACIxN,EAASsF,IAAItiB,EAAO+S,qBAAsBwP,EAAevsB,GAEzD+nB,IAEA0J,IAEAvF,EAAgB,MAEhBvE,EAAoB,KACpBN,GAA+B,EAE/BD,EAAe,IAAIqN,IAEf5M,IACAA,EAAgB2M,QAChB3M,EAAkB,MAGtBC,EAAexkB,SAAQoxB,GAAgB7C,aAAa6C,KACpD5M,EAAiB,GAEjB5C,EAAe,GACfuC,EAA4B,EAChC,EA8jBIkN,cAxHJ,SAAuBC,GACf3N,GACAA,EAAwB0N,cAAcC,EAE9C,EAqHI1I,kBACA2I,kBApnBJ,SAA2BhtB,GACvBsf,EAAoCtf,EACpCof,EAAwB4N,kBAAkBhtB,EAC9C,EAknBIitB,mBAnoBJ,SAA4BC,GACxBrN,EAAkBqN,CACtB,EAkoBIlL,qBAlrBJ,SAA8BD,GAC1B7B,IACAF,EAAgBgC,qBAAqBD,EACzC,EAgrBIoL,eAjpBJ,SAAwBvxB,GACpBmkB,EAAcnkB,CAClB,EAgpBIwxB,KA7mBJ,WACIxD,IACI5J,GACAA,EAAgBoN,MAExB,EAymBIC,qBAzHJ,SAA8BrxB,GAC1B,IACI,IAAKA,IAAMA,EAAE4oB,eAAiB5oB,EAAEsxB,kBAC5B,OAGJtxB,EAAE4oB,aAAakH,gCAAiC,EAChD,MAAMwB,EAAoBtxB,EAAEsxB,kBACtB15B,EAAK4G,EAAAA,EAAM0E,iBACXquB,EAAgB35B,GAAMA,EAAGkB,SAAWlB,EAAGkB,QAAQ/E,MAA0C,SAAlC6D,EAAGkB,QAAQ/E,KAAKuD,cAC7Eg6B,EAAkB7xB,SAASiwB,IACnB6B,GACGzN,EAAkBtD,OAASX,EAAAA,EAAoB3I,gBAC/CwY,EAAUhI,OAAwC,KAA/BgI,EAAUhI,MAAMjjB,YAetD,SAA+BijB,GAC3B,MAAM8J,EAAWhzB,EAAAA,EAAM6E,uBAAuBqkB,GACxC+J,EAAQD,EAAStR,UAAU,GAAa,GACxCwR,EAAQF,EAASG,UAAU,GAAa,GACxCC,EAAQJ,EAASG,UAAU,GAAa,GAE9CH,EAASK,UAAU,EAAGJ,GAAiB,GACvCD,EAASM,UAAU,EAAGJ,GAAiB,GACvCF,EAASM,UAAU,EAAGF,GAAiB,EAC3C,CAvBgBG,CAAsBrC,EAAUhI,OAGpC,MAAMsK,EAAaxzB,EAAAA,EAAMuF,kBAAkB2rB,EAAUhI,OAAOx2B,MAAM,EAAG,IACjE8gC,GAA6B,KAAfA,GACdzO,EAAaxnB,IAAIi2B,EAAYtC,EAAUvF,OAC3C,IAEJhH,EAAS0C,QAAQ1f,EAAOwT,yBAA0B,CAAE4J,gBACxD,CAAE,MAAOvjB,GACL2jB,EAAOwC,MAAMnmB,EACjB,CACJ,GAhiCI2jB,EAAST,EAAM+O,UAAU91B,GACzBynB,EAA4B,GAC5BvC,EAAe,GACf0C,EAAc,YACdF,EAAkB,GAClBH,EAAoB,KACpBD,EAA6B,KAC7BF,EAAe,IAAIqN,IACnBzN,EAASoF,GAAGpiB,EAAO+S,qBAAsBwP,EAAevsB,GA2nCrDA,CACX,CAEA2mB,EAAqB5lB,sBAAwB,uBAC7C,MAAehB,EAAAA,EAAakC,gBAAgB0kB,GCvrC5C,EAbA,MAOI9J,WAAAA,CAAYkZ,EAAOvyB,GACfpH,KAAK25B,MAAQA,EACb35B,KAAKoH,IAAMA,CACf,GC2CJ,EAnDA,MAQIqZ,WAAAA,CAAYmQ,EAAUxM,GAClB,GAAIA,GAAiB,eAATA,GAAkC,cAATA,EACjC,MAAM,IAAIwH,MAAM,8EAEpB5rB,KAAK4wB,SAAWA,EAChB5wB,KAAKokB,KAAOA,CAChB,CAOAwV,KAAAA,GACI,IAAIthC,EACAuhC,EAAU75B,KAAK4wB,SAASr4B,OACxBuhC,EAAM,CAAE3L,KAAM,IAElB,IAAK71B,EAAI,EAAGA,EAAIuhC,EAASvhC,IAAK,CAC1B,IAAI8O,EAAM,CACN2yB,IAAK,MACLC,IAAK,SACLC,IAAKj6B,KAAK4wB,SAASt4B,GAAGqhC,MACtBn6B,EAAGQ,KAAK4wB,SAASt4B,GAAG8O,KAExB0yB,EAAI3L,KAAK9oB,KAAK+B,EAClB,CACIpH,KAAKokB,OACL0V,EAAI1V,KAAOpkB,KAAKokB,MAEpB,IAAI8V,EAAYriC,KAAKC,UAAUgiC,GAC/B,MAAM76B,EAAMi7B,EAAU3hC,OAGtB,IAAI4hC,EAAM,IAAI3tB,YAAYvN,GACtBm7B,EAAQ,IAAIjvB,WAAWgvB,GAC3B,IAAK7hC,EAAI,EAAGA,EAAI2G,EAAK3G,IACjB8hC,EAAM9hC,GAAK4hC,EAAU1hC,WAAWF,GAEpC,OAAO6hC,CACX,GC/CJ,MAAMlS,EAAOX,EAAAA,EAAoB1I,cAC3BgO,EAAetF,EAAAA,EAAoBhJ,wBACnC2S,EAAc,YAAchJ,EAElC,SAASoS,EAAkB5sB,GAGvB,IAAI7J,EACJ,MAAMwkB,GAFN3a,EAASA,GAAU,CAAC,GAEE2a,OA8FtB,OAZAxkB,EAAW,CACPqkB,OACAgJ,cACArE,eACA0N,YArDJ,SAAqBnT,EAAIoT,GACrB,IACI,IAAIzS,EAAWf,EAAiBmB,mCAAmCf,EAAIiB,GAEvE,IAAKN,GAAYyS,EAAuB,CACpC,MACM9uB,EAAO,CAAE4iB,KAAM,CAUjC,SAA8CmM,GAC1C,IACI,IAAIP,EAAMO,EAAet7B,QAAQ,KAAM,IAIvC,OAHA+6B,EAAMQ,KAAKR,EAAIr6B,MAAM,UAAUQ,KAAKs6B,GACzB/D,OAAOC,aAAanN,SAASiR,EAAG,OACxCnhC,KAAK,KACD0gC,EAAI/6B,QAAQ,KAAM,IACpBA,QAAQ,MAAO,KACfA,QAAQ,MAAO,IACxB,CAAE,MAAOuI,GACL,OAAO,IACX,CACJ,CAvBmCkzB,CAAqCJ,EAAsBC,kBAElF1S,GAAW,IAAIwG,aAAcC,OAAO12B,KAAKC,UAAU2T,GACvD,CAEA,OAAOqc,CACX,CAAE,MAAOrgB,GACL,OAAO,IACX,CACJ,EAwCIgqB,6BAxBJ,WAEI,MAAO,CACH,eAAgB,mBAExB,EAoBIoB,6BAlBJ,SAAsC5O,GAClC,OAAOpsB,KAAKC,UAAUD,KAAK6D,MAAMi7B,OAAOC,aAAa7xB,MAAM,KAAM,IAAIoG,WAAW8Y,KACpF,EAiBIkN,gCAfJ,WACI,OAAO,IACX,EAcIyJ,WAZJ,WACI,OAAO,IACX,EAWIC,+BA/EJ,SAAwCC,EAAgB7W,GACpD,IAAI8W,EAAc,KAClB,GAAID,EAAgB,CAGhB,MAAME,EAAUnjC,KAAK6D,MAAMi7B,OAAOC,aAAa7xB,MAAM,KAAM,IAAIoG,WAAW8Y,KACpE2M,EAAW,GACjB,IAAK,IAAIt4B,EAAI,EAAGA,EAAI0iC,EAAQ3M,KAAK91B,OAAQD,IAAK,CAC1C,MAAM2iC,EAAaD,EAAQ3M,KAAK/1B,GAC1B4iC,EAAYJ,EAAe1M,WAAa0M,EAAe1M,UAAUnpB,eAAeg2B,GAAeH,EAAe1M,UAAU6M,GAAc,KAC5I,IAAKC,EACD,MAAM,IAAItP,MAAM,wBAA0BqP,EAAa,mBAG3DrK,EAASvrB,KAAK,IAAI81B,EAAQF,EAAYC,GAC1C,CACAH,EAAc,IAAIK,EAAexK,EACrC,CACA,OAAOmK,CACX,GA+DOn3B,CACX,CAEAy2B,EAAkB11B,sBAAwB,oBAC1C,MAAehB,EAAAA,EAAa8B,oBAAoB40B,GC1GhD,MAAMpS,EAAOX,EAAAA,EAAoBzI,kBAC3B+N,EAAetF,EAAAA,EAAoBhJ,wBACnC2S,EAAc,YAAchJ,EAElC,SAASoT,EAAqB5tB,GAC1B,IAAI7J,EACJ,MAAMwkB,EAAS3a,EAAO2a,OAChBgD,EAAS3d,EAAOkd,MAAM+O,UAAU91B,GAkEtC,OAZAA,EAAW,CACPqkB,KAAMA,EACNgJ,YAAaA,EACbrE,aAAcA,EACd0N,YAxBJ,SAAqBnT,GACjB,OAAOJ,EAAiBmB,mCAAmCf,EAAIiB,EACnE,EAuBIqJ,6BArBJ,WACI,OAAO,IACX,EAoBIoB,6BAlBJ,SAAsC5O,GAClC,OAAO,IAAI9Y,WAAW8Y,EAC1B,EAiBIkN,gCAfJ,WACI,OAAO,IACX,EAcIyJ,WAZJ,WACI,OAAO,IACX,EAWIC,+BApDJ,SAAwCC,EAAgB7W,GACpD,IAAI8W,EAAc,KAClB,GAAID,EAAgB,CAGhB,MAAME,EAAUnjC,KAAK6D,MAAMi7B,OAAOC,aAAa7xB,MAAM,KAAM,IAAIoG,WAAW8Y,KACpE2M,EAAW,GACjB,IAAK,IAAIt4B,EAAI,EAAGA,EAAI0iC,EAAQ3M,KAAK91B,OAAQD,IAAK,CAC1C,MAAM2iC,EAAaD,EAAQ3M,KAAK/1B,GAC1B4iC,EAAYJ,EAAe1M,WAAa0M,EAAe1M,UAAUnpB,eAAeg2B,GAAeH,EAAe1M,UAAU6M,GAAc,KAC5I,IAAKC,EACD,MAAM,IAAItP,MAAM,wBAA0BqP,EAAa,mBAG3DrK,EAASvrB,KAAK,IAAI81B,EAAQF,EAAYC,GAC1C,CACAH,EAAc,IAAIK,EAAexK,GAEjCxF,EAAO2J,KAAK,4JAChB,CACA,OAAOgG,CACX,GAkCOn3B,CACX,CAEAy3B,EAAqB12B,sBAAwB,uBAC7C,MAAehB,EAAAA,EAAa8B,oBAAoB41B,GCxEhD,MAAMpT,EAAOX,EAAAA,EAAoB5I,cAC3BkO,EAAetF,EAAAA,EAAoB/I,wBACnC0S,EAAc,YAAchJ,EAElC,SAASqT,EAAkB7tB,GAGvB,IAAI7J,EACJ,MAAMwkB,GAFN3a,EAASA,GAAU,CAAC,GAEE2a,OAiCtB,OAXAxkB,EAAW,CACPqkB,KAAI,EACJgJ,YAAW,EACXrE,aAAY,EACZ0N,YAxBJ,SAAqBnT,GACjB,OAAOJ,EAAiBmB,mCAAmCf,EAAIiB,EACnE,EAuBIqJ,6BArBJ,WACI,OAAO,IACX,EAoBIoB,6BAlBJ,SAAsC5O,GAClC,OAAO,IAAI9Y,WAAW8Y,EAC1B,EAiBIkN,gCAfJ,WACI,OAAO,IACX,EAcIyJ,WAZJ,WACI,OAAO,IACX,GAaOh3B,CACX,CAEA03B,EAAkB32B,sBAAwB,oBAC1C,MAAehB,EAAAA,EAAa8B,oBAAoB61B,GC9ChD,MAAMrT,EAAOX,EAAAA,EAAoB3I,eAC3BiO,EAAetF,EAAAA,EAAoB9I,yBACnCyS,EAAc,YAAchJ,EAGlC,SAASsT,EAAmB9tB,GAGxB,IAAI7J,EACA43B,EAAgB,SACpB,MAAMpT,GAHN3a,EAASA,GAAU,CAAC,GAGE2a,OAChB0C,EAAWrd,EAAOqd,SAExB,SAAS2Q,IACL,IAAKrT,IAAWA,EAAOnjB,eAAe,iBAAmBmjB,EAAOnjB,eAAe,eAC3E,MAAM,IAAI2mB,MAAM,8BAExB,CAsPA,OAZAhoB,EAAW,CACPqkB,KAAI,EACJgJ,YAAW,EACXrE,aAAY,EACZ0N,YA1HJ,SAAqBnS,GASjB,MAAMuT,EAAc,IAAIvwB,WAAW,CAAC,IAAM,IAAM,IAAM,IAAM,EAAM,EAAM,EAAM,IACxEwwB,EAAoB,IAAIxwB,WAAW,CAAC,IAAM,EAAM,IAAM,IAAM,IAAM,GAAM,GAAM,IAAM,IAAM,IAAM,IAAM,GAAM,IAAM,IAAM,GAAM,MAEpI,IAGIywB,EACAC,EACAC,EACAC,EACAC,EAPAxT,EAAa,EACbyT,EAA6B,KASjC,GADAR,KACKtT,EACD,OAAO,KAGX,GAAI,SAAUA,GAAUA,EAAOX,KAC3B,OAAOT,EAAiBmB,mCAAmCC,EAAQC,GAGvE,GAAI,QAASD,GAAUA,EAAO+T,IAC1BD,EAA6B7T,EAAOE,YAAYH,EAAO+T,IAAI7T,YACxD,MAAI,aAAcF,KAAUA,EAAOgU,SAGtC,OAAO,KAFPF,EAA6B7T,EAAOE,YAAYH,EAAOgU,SAAS9T,OAGpE,CAyBA,OAvBAuT,EAAUK,EAA2B1jC,OACrCsjC,EAAW,EAAMH,EAAYnjC,OAASojC,EAAkBpjC,OAAS,EAAMqjC,EAEvEE,EAAgB,IAAItvB,YAAYqvB,GAEhCE,EAAU,IAAI5wB,WAAW2wB,GACzBE,EAAW,IAAI/wB,SAAS6wB,GAExBE,EAAS1C,UAAU9Q,EAAYqT,GAC/BrT,GAAc,EAEduT,EAAQv4B,IAAIk4B,EAAalT,GACzBA,GAAckT,EAAYnjC,OAE1BwjC,EAAQv4B,IAAIm4B,EAAmBnT,GAC/BA,GAAcmT,EAAkBpjC,OAEhCyjC,EAAS1C,UAAU9Q,EAAYoT,GAC/BpT,GAAc,EAEduT,EAAQv4B,IAAIy4B,EAA4BzT,GACxCA,GAAcoT,EAEPG,EAAQnwB,MACnB,EA6DI6lB,6BA7OJ,SAAsCxN,GAClC,IAAImY,EACAC,EACJ,MAAM3zB,EAAU,CAAC,EACXpF,EAAS,IAAIg5B,UAEnB,GAAIxR,GAAYA,EAASvnB,MAAMgwB,UAAU+C,WAAWiG,8BAE1B,WAAlBf,GAA8BvX,GAAWA,EAAQ/X,WAAa,GAAM,EAEpE,OADAxD,EAAQ,gBAAkB,0BACnBA,EAIf,MAAM8zB,EAA8B,WAAlBhB,EAA8B,IAAIiB,YAAYxY,GAAW,IAAI9Y,WAAW8Y,GAE1FmY,EAAMzF,OAAOC,aAAa7xB,MAAM,KAAMy3B,GACtCH,EAAS/4B,EAAOo5B,gBAAgBN,EAAK,mBAErC,MAAMO,EAAiBN,EAAOO,qBAAqB,QAC7CC,EAAkBR,EAAOO,qBAAqB,SACpD,IAAK,IAAItkC,EAAI,EAAGA,EAAIqkC,EAAepkC,OAAQD,IACvCoQ,EAAQi0B,EAAerkC,GAAGwkC,WAAW,GAAGC,WAAaF,EAAgBvkC,GAAGwkC,WAAW,GAAGC,UAc1F,OATIr0B,EAAQzD,eAAe,aACvByD,EAAQ,gBAAkBA,EAAQs0B,eAC3Bt0B,EAAQs0B,SAIdt0B,EAAQzD,eAAe,kBACxByD,EAAQ,gBAAkB,2BAEvBA,CACX,EAyMImqB,6BAvMJ,SAAsC5O,GAClC,IAAImP,EAAiB,KACrB,MAAM9vB,EAAS,IAAIg5B,UAEnB,GAAIxR,GAAYA,EAASvnB,MAAMgwB,UAAU+C,WAAWiG,8BAE1B,WAAlBf,GAA8BvX,GAAWA,EAAQ/X,WAAa,GAAM,EACpE,OAAO+X,EAIf,MAAMuY,EAA8B,WAAlBhB,EAA8B,IAAIiB,YAAYxY,GAAW,IAAI9Y,WAAW8Y,GAE1FwX,IACA,MAAMW,EAAMzF,OAAOC,aAAa7xB,MAAM,KAAMy3B,GACtCH,EAAS/4B,EAAOo5B,gBAAgBN,EAAK,mBAE3C,IAAIC,EAAOO,qBAAqB,uBAAuB,GASnD,OAAO3Y,EATgD,CACvD,MAAMgZ,EAAYZ,EAAOO,qBAAqB,aAAa,GAAGE,WAAW,GAAGC,UACxEE,IACA7J,EAAiBhL,EAAO7c,OAAO0xB,GAEvC,CAOA,OAAO7J,CACX,EA0KIjC,gCAxKJ,SAAyCrJ,GACrC,GAAIA,EAAU,CACV,MAAMrc,EAAO,IAAIR,SAAS6c,GACpBoV,EAAazxB,EAAK2tB,UAAU,GAAG,GACrC,IAAI3R,EAAS,EACb,MAAMnkB,EAAS,IAAIg5B,UAEnB,IAAK,IAAIhkC,EAAI,EAAGA,EAAI4kC,EAAY5kC,IAAK,CAEjC,MAAM6kC,EAAa1xB,EAAK2tB,UAAU3R,GAAQ,GAC1CA,GAAU,EACV,MAAM2V,EAAe3xB,EAAK2tB,UAAU3R,GAAQ,GAE5C,GADAA,GAAU,EACS,IAAf0V,EAAuB,CACvB1V,GAAU2V,EACV,QACJ,CAEA,MAAMC,EAAavV,EAASnvB,MAAM8uB,EAAQA,EAAS2V,GAC7CE,EAAS3G,OAAOC,aAAa7xB,MAAM,KAAM,IAAI03B,YAAYY,IACzDhB,EAAS/4B,EAAOo5B,gBAAgBY,EAAQ,mBAG9C,GAAIjB,EAAOO,qBAAqB,UAAU,GAAI,CAC1C,MAAMW,EAAQlB,EAAOO,qBAAqB,UAAU,GAAGE,WAAW,GAAGC,UACrE,GAAIQ,EACA,OAAOA,CAEf,CAGA,GAAIlB,EAAOO,qBAAqB,WAAW,GAAI,CAC3C,MAAMY,EAASnB,EAAOO,qBAAqB,WAAW,GAAGE,WAAW,GAAGC,UACvE,GAAIS,EACA,OAAOA,CAEf,CACJ,CACJ,CAEA,OAAO,IACX,EAgII5C,WA5CJ,SAAoB6C,GAChB,IAAIC,EACAC,EACAC,EACAtlC,EAGJ,GADAmjC,KACKgC,EACD,OAAO,KAKX,IADAC,EAAa,GACRplC,EAAI,EAAGA,EAAImlC,EAASllC,SAAUD,EAC/BolC,EAAWr4B,KAAKo4B,EAASjlC,WAAWF,IACpColC,EAAWr4B,KAAK,GAapB,IAXAq4B,EAAa/G,OAAOC,aAAa7xB,MAAM,KAAM24B,GAG7CA,EAAatV,EAAOmG,OAAOmP,GAI3BC,EA5OU,yMA4OUz+B,QAAQ,eAAgBw+B,GAG5CE,EAAe,GACVtlC,EAAI,EAAGA,EAAIqlC,EAAQplC,SAAUD,EAC9BslC,EAAav4B,KAAKs4B,EAAQnlC,WAAWF,IACrCslC,EAAav4B,KAAK,GAGtB,OAAO,IAAI8F,WAAWyyB,GAAchyB,MACxC,EAWIiyB,0BAvDJ,SAAmC1iC,GAC/B,GAAe,UAAXA,GAAiC,WAAXA,EACtB,MAAM,IAAIywB,MAAM,8DAEpB4P,EAAgBrgC,CACpB,GAqDOyI,CACX,CAEA23B,EAAmB52B,sBAAwB,qBAC3C,MAAehB,EAAAA,EAAa8B,oBAAoB81B,GC3QhD,SAASuC,EAASrwB,GAGd,MAAM2a,GADN3a,EAASA,GAAU,CAAC,GACE2a,OAEhBoQ,EAAa,CAAC,EAoBpB,IAAI50B,EAqCJ,OAxDA40B,EAAWlR,EAAAA,EAAoB/I,yBAA2B,CACtD4L,aAAc,OACdmI,kBAAmB,SAAUJ,GACzB,OAAO9J,EAAOE,YAAY4J,EAAS6L,QACvC,EACApI,iBAAkB,SAAUzD,GACxB,OAAOA,CACX,GAEJsG,EAAWlR,EAAAA,EAAoB9I,0BAA4B,CACvD2L,aAAc,cACdmI,kBAAmB,SAAUJ,GACzB,OAAOA,CACX,EACAyD,iBAAkB,SAAUzD,GACxB,OAAOyE,OAAOC,aAAa7xB,MAAM,KAAM,IAAIoG,WAAW+mB,GAC1D,GAgCJtuB,EAAW,CACPwtB,wBAtBJ,SAAiCrqB,GAC7B,OAAOA,CACX,EAqBIgsB,cAnBJ,WACI,MAAO,MACX,EAkBIC,gBAhBJ,SAAyBgL,GACrB,OAAOxF,EAAWwF,GAAc7T,YACpC,EAeImI,kBAbJ,SAA2BsD,EAAgBoI,GAEvC,OApBJ,WACI,IAAK5V,IAAWA,EAAOnjB,eAAe,eAClC,MAAM,IAAI2mB,MAAM,8BAExB,CAeI6P,GACOjD,EAAWwF,GAAc1L,kBAAkBsD,EACtD,EAWID,iBATJ,SAA0BC,EAAgBoI,GACtC,OAAOxF,EAAWwF,GAAcrI,iBAAiBC,EACrD,GAUOhyB,CACX,CAEAk6B,EAASn5B,sBAAwB,WACjC,MAAehB,EAAAA,EAAa8B,oBAAoBq4B,GC9DhD,SAASG,IAEL,IAAIr6B,EAEJ,MAAMs6B,EAAO,4CAEb,SAASC,EAAaC,GAClB,MAAMC,EAAgB1H,OAAOC,aAAa7xB,MAAM,KAAM,IAAIoG,WAAWizB,IAErE,OADsBl1B,mBAAmBo1B,OAAOD,GAEpD,CAEA,SAASE,EAAoB3I,GACzB,GAAI95B,OAAOwgC,UAAW,CAClB,MAAMkC,EAAiBL,EAAavI,GAE9ByG,GADS,IAAIvgC,OAAOwgC,WACJI,gBAAgB8B,EAAgB,YAChDC,EAAWpC,EAASA,EAAOqC,uBAAuBR,EAAM,YAAY,GAAK,KACzES,EAAOF,EAAWA,EAASC,uBAAuBR,EAAM,QAAQ,GAAK,KAG3E,GAFcS,GAAOA,EAAKD,uBAAuBR,EAAM,SAAS,GAG5D,OAAO,IAEf,CACA,OAAOtI,CACX,CAEA,SAASgJ,EAAmBhJ,GACxB,IAAIiJ,EAAc,GACdC,EAAa,GACb7a,EAAU,GACV8a,GAAW,EACXC,GAAS,EAEb,GAAIljC,OAAOwgC,UAAW,CAClB,MAAMkC,EAAiBL,EAAavI,GAE9ByG,GADS,IAAIvgC,OAAOwgC,WACJI,gBAAgB8B,EAAgB,YAChDC,EAAWpC,EAASA,EAAOqC,uBAAuBR,EAAM,YAAY,GAAK,KACzES,EAAOF,EAAWA,EAASC,uBAAuBR,EAAM,QAAQ,GAAK,KACrEe,EAAQN,EAAOA,EAAKD,uBAAuBR,EAAM,SAAS,GAAK,KAC/DgB,EAASD,EAAQA,EAAMrC,qBAAqB,UAAU,GAAK,KAC3DuC,EAAYD,EAASA,EAAOtC,qBAAqB,aAAa,GAAK,KACzE,IAAIwC,EAAO,KAEX,GAAc,OAAVH,EACA,OAAOT,EAGXY,EAAOH,EAAMrC,qBAAqB,eAAe,GAAGyC,WACpDR,EAAcO,EAAOA,EAAKrC,UAAY,KAEpB,OAAdoC,IACAC,EAAOD,EAAUvC,qBAAqB,cAAc,GACpDkC,EAAaM,EAAOA,EAAKC,WAAWtC,UAAY,KAChDqC,EAAOD,EAAUvC,qBAAqB,WAAW,GACjD3Y,EAAUmb,EAAOA,EAAKC,WAAWtC,UAAY,KAC7CgC,EAAU9a,EAAUA,EAAQvrB,YAAY,KAAO,GAAK,EACpDsmC,EAAQ/a,EAAUA,EAAQplB,QAAQ,MAAQ,EAC1ColB,EAAUA,EAAUA,EAAQ9kB,UAAU4/B,EAASC,GAAS,GAEhE,CAEA,IAAIM,EAAc,SAASR,YAAqBD,IAKhD,OAJI5a,IACAqb,GAAe,cAAcrb,KAG1Bqb,CACX,CA8BA,OARA17B,EAAW,CACPwtB,wBArBJ,SAAiCrqB,GAC7B,OAAOA,CACX,EAoBIgsB,cAlBJ,WACI,MAAO,MACX,EAiBIC,gBAfJ,WACI,MAAO,aACX,EAcIV,kBAZJ,SAA2BsD,GACvB,OAAO2I,EAAoBt+B,KAAKD,KAAM41B,EAC1C,EAWID,iBATJ,SAA0BC,GACtB,OAAOgJ,EAAmB3+B,KAAKD,KAAM41B,EACzC,GAUOhyB,CACX,CAEAq6B,EAAUt5B,sBAAwB,YAClC,MAAehB,EAAAA,EAAa8B,oBAAoBw4B,GCjHhD,SAASsB,IAEL,IAAI37B,EA8BJ,OARAA,EAAW,CACPwtB,wBArBJ,SAAiCrqB,GAC7B,OAAOA,CACX,EAoBIgsB,cAlBJ,WACI,MAAO,MACX,EAiBIC,gBAfJ,WACI,MAAO,aACX,EAcIV,kBAZJ,SAA2BsD,GACvB,OAAOA,CACX,EAWID,iBATJ,SAA0BC,GACtB,OAAOe,OAAOC,aAAa7xB,MAAM,KAAM,IAAIoG,WAAWyqB,GAC1D,GAUOhyB,CACX,CAEA27B,EAAS56B,sBAAwB,WACjC,MAAehB,EAAAA,EAAa8B,oBAAoB85B,GC5BhD,SAASC,IAEL,IAAI57B,EAyCJ,OARAA,EAAW,CACPwtB,wBAhCJ,SAAiCrqB,GAC7B,OAAOA,CACX,EA+BIgsB,cA7BJ,WACI,MAAO,MACX,EA4BIC,gBA1BJ,WACI,MAAO,MACX,EAyBIV,kBAvBJ,SAA2BsD,GACvB,IAAKA,EAAe3wB,eAAe,QAC/B,OAAO,KAEX,IAAI2rB,EAAW,GACf,IAAK,IAAIt4B,EAAI,EAAGA,EAAIs9B,EAAezH,KAAK51B,OAAQD,IAAK,CACjD,IAAImnC,EAAU7J,EAAezH,KAAK71B,GAC9BonC,EAAQD,EAAQxF,IAAI/6B,QAAQ,KAAM,IAClCkI,EAAMq4B,EAAQjgC,EAAEN,QAAQ,KAAM,IAElC0xB,EAASvrB,KAAK,IAAI81B,EAAQuE,EAAOt4B,GACrC,CACA,OAAO,IAAIg0B,EAAexK,EAC9B,EAWI+E,iBATJ,SAA0BC,GACtB,OAAOe,OAAOC,aAAa7xB,MAAM,KAAM,IAAIoG,WAAWyqB,GAC1D,GAUOhyB,CACX,CAEA47B,EAAS76B,sBAAwB,WACjC,MAAehB,EAAAA,EAAa8B,oBAAoB+5B,GC5ChD,EAZA,MACI/e,WAAAA,CAAYhT,GACRzN,KAAKqsB,GAAK5e,EAAO4e,GACjBrsB,KAAKmvB,MAAQ1hB,EAAO0hB,MACpBnvB,KAAK8nB,SAAWra,EAAOqa,SACvB9nB,KAAKusB,SAAW9e,EAAO8e,SACvBvsB,KAAK29B,QAAUlwB,EAAOkwB,QACtB39B,KAAKsqB,UAAY7c,EAAO6c,UACxBtqB,KAAKwrB,YAAc/d,EAAO+d,WAC9B,GCIJ,SAASmU,IAEL,IAEI/7B,EACA+mB,EACAS,EACAoN,EACApQ,EACA0C,EACA8U,EACAC,EATA57B,EAAUjE,KAAKiE,QAiUnB,SAASspB,EAAyBX,EAAckT,GAC5C,OAAKA,GAGGlT,KAAgBkT,EAAeA,EAAYlT,GAFxC,IAGf,CAEA,SAASmT,EAAcxT,EAAUpF,GAE7B,OAAIoF,GAAYA,EAASjC,UACdiC,EAASjC,UACTnD,GAAMA,EAAGmD,UACTnD,EAAGmD,UAEP,IACX,CAEA,SAAS0V,EAAgBzT,EAAUf,GAC/B,OAAQe,GAAYA,EAASf,YAAee,EAASf,YAAcA,CACvE,CAiBA,OAfA5nB,EAAW,CACPq8B,2BA3PJ,SAAoCrT,GAChC,IAAK,IAAIt0B,EAAI,EAAGA,EAAIkgC,EAAWjgC,OAAQD,IACnC,GAAIkgC,EAAWlgC,GAAGs0B,eAAiBA,EAC/B,OAAO4L,EAAWlgC,GAG1B,OAAO,IACX,EAqPIu/B,cAxRJ,WACI,OAAOW,CACX,EAuRIjI,8BApFJ,SAAuC1I,EAAW0E,EAAUlC,GAIxD,GAAIA,IAAgB/C,EAAAA,EAAoBnI,wBAAwBG,iBAAmB+K,IAAgB/C,EAAAA,EAAoBnI,wBAAwBI,0BAC3I,OAAO,KAGX,IAAIuR,EAAoB,KAWxB,OAVIvE,GAAYA,EAAStnB,eAAe,YACpC6rB,EAAoBgN,EAAS75B,GAASyB,YAAY,CAAC0iB,OAAQA,IACpDP,EAAU+E,eAAiBtF,EAAAA,EAAoB/I,wBACtDuS,EAAoByO,EAASt7B,GAASyB,cAC/BmiB,EAAU+E,eAAiBtF,EAAAA,EAAoB9I,yBACtDsS,EAAoBmN,EAAUh6B,GAASyB,cAChCmiB,EAAU+E,eAAiBtF,EAAAA,EAAoBhJ,0BACtDwS,EAAoB0O,EAASv7B,GAASyB,eAGnCorB,CACX,EAiEIgH,mDA1LJ,SAA4DoI,EAA2BC,EAAoC3U,GACvH,IAAI4U,EAA0BvY,EAAWmG,EAAOqS,EAC5CC,EAAc,GAElB,IAAKJ,IAA8BA,EAA0B3nC,OACzD,OAAO+nC,EAGX,MAAMC,EAAuBxZ,EAAiBC,yBAAyBkZ,GACvE,IAAKlS,EAAQ,EAAGA,EAAQwK,EAAWjgC,OAAQy1B,IAAS,CAChDnG,EAAY2Q,EAAWxK,GAGvB,MAAMzB,EAAWgB,EAAyB1F,EAAU+E,aAAcuT,GAElE,IAAKE,EAAQ,EAAGA,EAAQH,EAA0B3nC,OAAQ8nC,IAEtD,GADAD,EAA2BF,EAA0BG,GACjDD,EAAyBhZ,YAAYroB,gBAAkB8oB,EAAUoJ,YAAa,CAE9E,IAAInJ,EAAWD,EAAUyS,YAAY8F,EAA0BG,GAC/D,MAAMtS,EAAoB,IAAIuS,EAAkB,CAC5CnU,GAAImM,EAAWxK,GACfmB,MAAOiR,EAAyBjR,MAChCrH,SAAUA,EACVyE,SAAUA,EACVoR,QAAS9V,EAAU+S,WAAWrO,EAAWA,EAASoR,QAAU,MAC5DrT,UAAWyV,EAAcxT,EAAU6T,GACnC5U,YAAawU,EAAgBzT,EAAUf,KAGvCe,EACA+T,EAAYG,QAAQxS,GAEpBqS,EAAYj7B,KAAK4oB,EAEzB,CAER,CAEA,OAAOqS,CACX,EAmJIzJ,6CAjIJ,SAAsD/O,EAAUgY,EAAatU,GACzE,IAEIa,EAAIqD,EAFJ4Q,EAAc,GACd9Y,EAAOT,EAAiBiB,cAAcF,GAG1C,IAAK,IAAIkG,EAAQ,EAAGA,EAAQwK,EAAWjgC,SAAUy1B,EAAO,CACpD3B,EAAKmM,EAAWxK,GAChB0B,EAAkBrD,EAAGO,aAGrB,MAAML,EAAWgB,EAAyBmC,EAAiBoQ,GAEvDzT,EAAGpE,QAAQT,GACX8Y,EAAYj7B,KAAK,CACbgnB,GAAIA,EACJvE,SAAUN,EAAK6E,EAAGpE,MAClBsE,SAAUA,EACVoR,QAAStR,EAAGuO,WAAWrO,EAAWA,EAASoR,QAAU,MACrDrT,UAAWyV,EAAcxT,GACzBf,YAAawU,EAAgBzT,EAAUf,IAGnD,CACA,OAAO8U,CACX,EA0GIzQ,eA3NJ,SAAwB6Q,EAAWC,GAC/B,GAAID,EAAUx0B,aAAey0B,EAAUz0B,WAAY,CAC/C,IAAI00B,EAAQ,IAAIz1B,WAAWu1B,GACvBG,EAAQ,IAAI11B,WAAWw1B,GAE3B,IAAK,IAAIphC,EAAI,EAAGA,EAAIqhC,EAAMroC,OAAQgH,IAC9B,GAAIqhC,EAAMrhC,KAAOshC,EAAMthC,GACnB,OAAO,EAGf,OAAO,CACX,CACA,OAAO,CACX,EA+MIuhC,WA/TJ,WAGI,IAAIjZ,EAFJ2Q,EAAa,GAKb3Q,EAAY0T,EAAmBt3B,GAASyB,YAAY,CAAC0iB,OAAQA,EAAQ0C,SAAUA,IAC/E0N,EAAWnzB,KAAKwiB,GAGhBA,EAAYyT,EAAkBr3B,GAASyB,YAAY,CAAC0iB,OAAQA,IAC5DoQ,EAAWnzB,KAAKwiB,GAGhBA,EAAYwS,EAAkBp2B,GAASyB,YAAY,CAAC0iB,OAAQA,IAC5DoQ,EAAWnzB,KAAKwiB,GAChB+X,EAAoB/X,EAGpBA,EAAYwT,EAAqBp3B,GAASyB,YAAY,CAAC0iB,OAAQA,EAAQuC,MAAOA,IAC9E6N,EAAWnzB,KAAKwiB,GAChBgY,EAAuBhY,CAC3B,EA0SIqG,WA3OJ,SAAoBrG,GAChB,OAAQA,IAAc+X,GAAqB/X,IAAcgY,CAC7D,EA0OIlP,8BAxDJ,SAAuCiP,EAAmBrT,EAAUtI,GAChE,IACI,OAAO2b,EAAkB/E,+BAA+BtO,EAAUtI,EACtE,CAAE,MAAO2J,GAEL,OADAxC,EAAOwC,MAAM,oDACN,IACX,CACJ,EAkDImT,UArVJ,SAAmBtzB,GACVA,IAIDA,EAAOkd,QACPA,EAAQld,EAAOkd,MACfS,EAAST,EAAM+O,UAAU91B,IAGzB6J,EAAO2a,SACPA,EAAS3a,EAAO2a,QAGhB3a,EAAOqd,WACPA,EAAWrd,EAAOqd,UAE1B,EAqUIyN,cApRJ,SAAuByI,GACnBxI,EAAawI,CACjB,EAmRIvI,kBAlDJ,SAA2BwI,GASvB,IARA,IAAkCvR,EAC1BnD,EAOCj0B,EAAI,EAAGA,EAAIkgC,EAAWjgC,OAAQD,IAAK,CACxC,IAAIuvB,EAAY2Q,EAAWlgC,GACvBuvB,EAAU5iB,eAAe,SACzB4iB,EAAUqZ,MAXgBxR,EAWO7H,EAAU+E,aAV3CL,WAAW,KACX0U,IACA1U,EAAYmD,KAAmBuR,EAAqBA,EAAkBvR,GAAmB,MAEtFnD,GAQX,CACJ,GAsCO3oB,CACX,CAEA+7B,EAAwBh7B,sBAAwB,0BAChD,MAAehB,EAAAA,EAAa8B,oBAAoBk6B,G,SC7WhD,EAZA,MAMIlf,WAAAA,CAAYqH,EAAU0O,GAClBx2B,KAAK8nB,SAAWA,EAChB9nB,KAAKw2B,aAAeA,CACxB,GCWJ,GAlBA,MAUI/V,WAAAA,CAAY4P,EAAcpM,EAASkd,EAAY9W,GAC3CrqB,KAAKqwB,aAAeA,EACpBrwB,KAAKikB,QAAUA,EACfjkB,KAAKmhC,WAAaA,EAClBnhC,KAAKqqB,YAAcA,GAA4B/C,EAAAA,EAAoBnI,wBAAwBC,eAC/F,GCEJ,GAjBA,MASIqB,WAAAA,CAAYoH,EAAWuZ,GACnBphC,KAAK6nB,UAAYA,EACjB7nB,KAAKohC,gBAAkBA,EACvBphC,KAAKqhC,iCAAmC,KACxCrhC,KAAKktB,qBAAuB,IAChC,GCFJ,MAAMoU,GAAyB,CAAC,EAKhC,SAASC,GAAuB9zB,GAE5BA,EAASA,GAAU,CAAC,EACpB,MAAMxJ,EAAUjE,KAAKiE,QACf2mB,EAAWnd,EAAOmd,SAClBhd,EAASH,EAAOG,OAChB+c,EAAQld,EAAOkd,MAErB,IAAI/mB,EACAwnB,EACAvD,EACA2Z,EACAC,EACArK,EACAsK,EACA7W,EAmFJ,SAAS8W,EAAgCzV,EAAkC0V,EAAK/oC,EAASgpC,GAGrF,QAA8C3oC,IAA1C+H,UAAU6gC,6BACuC,mBAA1C7gC,UAAU6gC,4BAA4C,CAC7D,MAAM1F,EAAM,mCAGZ,OAFAxR,EAAS0C,QAAQ1f,EAAOyT,2BAA4B,CAAEuM,MAAOwO,SAC7DyF,EAAO,CAAEjU,MAAOwO,GAEpB,CAIA,MAAM2F,EAA+B7V,EAAiC0V,GAAKrV,UAAYL,EAAiC0V,GAAKrV,SAASyV,qBAAuB9V,EAAiC0V,GAAKrV,SAASyV,qBAAuB,KAC7N1V,EAAUJ,EAAiC0V,GAAKtV,QAChD2V,EAAmB/V,EAAiC0V,GAAKvV,GAC/D,IAAIO,EAAeqV,EAAiBrV,cAkCxC,SAAkCsV,EAAsB5V,GACpD,OAAO,IAAI8J,SAAQ,CAACv9B,EAASgpC,KACzBM,EAA6BD,EAAsB5V,EAAS,EAAGzzB,EAASgpC,EAAO,GAEvF,EAhCIO,CAH6BL,IAA8DT,GAAuB1U,GAAgB0U,GAAuB1U,GAAgB,CAACA,IAG3HN,GAC1CU,MAAMvhB,IACH,MAAM42B,EAAgB52B,GAAQA,EAAK41B,kCAAsG,mBAA3D51B,EAAK41B,iCAAiCiB,iBAChH72B,EAAK41B,iCAAiCiB,mBAAqB,KACzDxV,EAAkB,IAAIyV,GAAgBN,EAAkBI,GAC9DvV,EAAgBI,qBAAuBzhB,EAAKyhB,qBAC5CJ,EAAgBuU,iCAAmC51B,EAAK41B,iCACxDzW,EAAS0C,QAAQ1f,EAAOyT,2BAA4B,CAAE5V,KAAMqhB,IAC5Dj0B,EAAQ,CAAE4S,KAAMqhB,GAAkB,IAErCa,OAAOlmB,IACJ,GAAIm6B,EAAM,EAAI1V,EAAiC3zB,OAC3CopC,EAAgCzV,EAAkC0V,EAAM,EAAG/oC,EAASgpC,OACjF,CACH,MAAMW,EAAe,6BACrB5X,EAAS0C,QAAQ1f,EAAOyT,2BAA4B,CAAEuM,MAAO4U,EAAe/6B,EAAEwc,UAC9E4d,EAAO,CAAEjU,MAAO4U,EAAe/6B,EAAEwc,SACrC,IAEZ,CAwBA,SAASke,EAA6BD,EAAsB5V,EAASsV,EAAK/oC,EAASgpC,GAC/E,MAAMjV,EAAesV,EAAqBN,GAE1CxW,EAAOT,MAAM,kDAAkDiC,KAE/D3rB,UAAU6gC,4BAA4BlV,EAAcN,GAC/CU,MAAMyV,IACH5pC,EAAQ,CAAEwoC,iCAAkCoB,EAAsBvV,qBAAsBN,GAAe,IAE1Ge,OAAOlmB,IACAm6B,EAAM,EAAIM,EAAqB3pC,OAC/B4pC,EAA6BD,EAAsB5V,EAASsV,EAAM,EAAG/oC,EAASgpC,GAE9EA,EAAOp6B,EACX,GAEZ,CAkLA,SAASi7B,EAAyBrS,GAC9B,IAAKA,IAAiBA,EAAasS,QAC/B,OAAOvM,QAAQv9B,QAEnB,MAAM8pC,EAAUtS,EAAasS,QAO7B,OAJAA,EAAQC,oBAAoB,oBAAqBvS,GACjDsS,EAAQC,oBAAoB,UAAWvS,GAGhCsS,EAAQE,OACnB,CAoBA,SAASC,EAAcC,GAEnB,IAAK,IAAIzqC,EAAI,EAAGA,EAAI8+B,EAAc7+B,OAAQD,IACtC,GAAI8+B,EAAc9+B,KAAOyqC,EAAO,CAC5B3L,EAAc4L,OAAO1qC,EAAG,GACxB,KACJ,CAER,CAIA,SAAS2qC,EAAoBN,EAAS1U,GAClC,MAAM8U,EAAQ,CACVJ,QAASA,EACTxT,MAAOlB,EAAkBkB,MACzB+H,gBAAiBjJ,GAAqBA,EAAkBkB,OAA4C,iBAA5BlB,EAAkBkB,MAAqBlB,EAAkBkB,MAAMjwB,QAAQ,KAAM,IAAIH,cAAgB,GACzK+oB,SAAUmG,EAAkBnG,SAC5BwC,UAAW2D,EAAkB3D,UAC7BkB,YAAayC,EAAkBzC,YAC/B+L,gCAAgC,EAKhC2L,YAAa,SAAUjW,GACnB,OAAQA,EAAM7I,MACV,IAAK,oBACDpkB,KAAKmjC,qBAAqBlW,GAC1B,MAEJ,IAAK,UACDjtB,KAAKmwB,cAAclD,GAG/B,EAEAkW,qBAAsB,SAAUlW,GAC5BrC,EAAS0C,QAAQ1f,EAAOuT,qBAAsB,CAAE1V,KAAMzL,OAEtD,MAAMojC,EAAc,GACpBnW,EAAM9iB,OAAOi5B,YAAYl8B,SAAQ,WAC7Bk8B,EAAY/9B,KAAKg+B,EAAgBpqC,WACrC,IACA2xB,EAAS0C,QAAQ1f,EAAOgT,8BAA+B,CACnDmY,kBAAmBqK,EACnB/S,aAAc0S,GAEtB,EAEA5S,cAAe,SAAUlD,GACrB,IAAIhJ,EAAUzX,YAAYkqB,OAAOzJ,EAAMhJ,SAAWgJ,EAAMhJ,QAAQrY,OAASqhB,EAAMhJ,QAC/E2G,EAAS0C,QAAQ1f,EAAO+S,qBAAsB,CAAElV,KAAM,IAAI63B,GAAWtjC,KAAMikB,OAAS/qB,EAAW+zB,EAAM5C,cAEzG,EAEAiF,SAAU,WACN,OAAOtvB,KAAKmvB,KAChB,EAEAsB,aAAc,WACV,OAAOkS,EAAQrY,SACnB,EAEAiZ,eAAgB,WACZ,OAAOvjC,KAAKwrB,WAChB,EAEAgY,kBAAmB,WACf,OAAOb,EAAQc,UACnB,EAEAC,eAAgB,WACZ,OAAOf,EAAQS,WACnB,EAEAO,UAAW,WACP,IAAIC,GAAS,EAOb,OANAjB,EAAQS,YAAYl8B,SAAQ,WACRm8B,EAAgBpqC,WAClB24B,SAAWtK,EAAAA,EAAoBvH,mBAAmBC,SAC5D4jB,GAAS,EAEjB,IACOA,CACX,GAkBJ,OAbAjB,EAAQkB,iBAAiB,oBAAqBd,GAC9CJ,EAAQkB,iBAAiB,UAAWd,GAGpCJ,EAAQmB,OAAO9W,MAAK,KAChB8V,EAAcC,GACd3X,EAAOT,MAAM,qCAAuCoY,EAAMtS,gBAC1D7F,EAAS0C,QAAQ1f,EAAOoT,mBAAoB,CAAEvV,KAAMs3B,EAAMtS,gBAAiB,IAI/E2G,EAAc/xB,KAAK09B,GAEZA,CACX,CAEA,SAASM,EAAgB5+B,GAErB,IAAImtB,EAAQzC,EAkBZ,OAjBI1qB,GAAQA,EAAKlM,OAAS,IAClBkM,EAAK,KACkB,iBAAZA,EAAK,GACZmtB,EAASntB,EAAK,GAEd0qB,EAAQ1qB,EAAK,IAIjBA,EAAK,KACkB,iBAAZA,EAAK,GACZmtB,EAASntB,EAAK,GAEd0qB,EAAQ1qB,EAAK,KAIlB,CACHmtB,OAAQA,EACRzC,MAAOA,EAEf,CAoBA,OAlBAvrB,EAAW,CACPg0B,gBAhLJ,SAAyBvH,GAErBqS,EAAyBrS,GAAc1C,OAAM,SAAUC,GACnDkV,EAAczS,GACdzF,EAAS0C,QAAQ1f,EAAOoT,mBAAoB,CACxCvV,KAAM,KACNmiB,MAAO,0BAA4ByC,EAAaI,eAAiB,KAAO7C,EAAMpyB,MAEtF,GACJ,EAwKIkzB,iBAjRJ,SAA0BT,GACtB,IAAKpG,IAAc4Z,EACf,MAAM,IAAI7V,MAAM,gEAGpB,MAAMmY,EAAkBtC,EAAUuC,cAAc/V,EAAkBzC,aAC5D6E,EAAe4S,EAAoBc,EAAiB9V,GAGpDgW,EAAWpc,EAAU+E,eAAiBtF,EAAAA,EAAoBhJ,0BAA4B2P,EAAkBnG,UAAamG,EAAkB1B,UAAY0B,EAAkB1B,SAAS6B,WAAc9G,EAAAA,EAAoBvI,gCAAkCuI,EAAAA,EAAoBxI,8BAE5QilB,EAAgBG,gBAAgBD,EAAUhW,EAAkBnG,UACvDkF,MAAK,WACF5B,EAAOT,MAAM,sCAAwC0F,EAAaI,gBAClE7F,EAAS0C,QAAQ1f,EAAOqT,oBAAqB,CAAExV,KAAM4kB,GACzD,IACC1C,OAAM,SAAUC,GACbkV,EAAczS,GACdzF,EAAS0C,QAAQ1f,EAAOqT,oBAAqB,CACzCxV,KAAM,KACNmiB,MAAO,IAAIC,EAAAA,EAAY5L,EAAAA,EAAiBe,+BAAgCf,EAAAA,EAAiB6B,kCAAoC,mCAAqC8J,EAAMpyB,OAEhL,GACR,EA2PIo0B,eAtcJ,WACI,MAAM1I,EAAS,GACf,IAAK,IAAI5uB,EAAI,EAAGA,EAAI8+B,EAAc7+B,OAAQD,IAClC8+B,EAAc9+B,GAAGwvB,UACjBZ,EAAO7hB,KAAK+xB,EAAc9+B,GAAGwvB,UAGrC,OAAOZ,CACX,EA+bImI,iBA7bJ,WACI,OAAO+H,CACX,EA4bI3I,eA3OJ,SAAwBR,GACpB,IAAKpG,IAAc4Z,EACf,MAAM,IAAI7V,MAAM,8DAGpB,MAAMtB,EAAY2D,EAAkB3D,UAGpC,IAAK,IAAIhyB,EAAI,EAAGA,EAAI8+B,EAAc7+B,OAAQD,IACtC,GAAIgyB,IAAc8M,EAAc9+B,GAAGgyB,UAE/B,YADAc,EAAO2J,KAAK,6DAKpB,MAAM4N,EAAUlB,EAAUuC,cAAc/V,EAAkBzC,aACpD6E,EAAe4S,EAAoBN,EAAS1U,GAClDoC,EAAakH,gCAAiC,EAG9CoL,EAAQwB,KAAK7Z,GAAW0C,MAAK,SAAUoX,GAC/BA,GACAhZ,EAAOT,MAAM,qCAAuC0F,EAAaI,gBACjE7F,EAAS0C,QAAQ1f,EAAOqT,oBAAqB,CAAExV,KAAM4kB,MAErDyS,EAAczS,GACdzF,EAAS0C,QAAQ1f,EAAOqT,oBAAqB,CACzCxV,KAAM,KACNmiB,MAAO,IAAIC,EAAAA,EAAY5L,EAAAA,EAAiBe,+BAAgCf,EAAAA,EAAiB6B,kCAAoC,+CAAiDwG,EAAY,OAGtM,IAAGqD,OAAM,SAAUC,GACfkV,EAAczS,GACdzF,EAAS0C,QAAQ1f,EAAOqT,oBAAqB,CACzCxV,KAAM,KACNmiB,MAAO,IAAIC,EAAAA,EAAY5L,EAAAA,EAAiBe,+BAAgCf,EAAAA,EAAiB6B,kCAAoC,2BAA6BwG,EAAY,MAAQsD,EAAMpyB,OAE5L,GACJ,EAsMI28B,iBApMJ,SAA0B9H,GACNA,EAAasS,QAErB0B,SAASrX,MAAK,WAClB5B,EAAOT,MAAM,sCAAwC0F,EAAaI,gBAClE7F,EAAS0C,QAAQ1f,EAAOsT,oBAAqB,CAAEzV,KAAM4kB,EAAaI,gBACtE,IAAG,SAAU7C,GACThD,EAAS0C,QAAQ1f,EAAOsT,oBAAqB,CACzCzV,KAAM,KACNmiB,MAAO,2BAA6ByC,EAAaI,eAAiB,MAAQ7C,EAAMpyB,MAGxF,GACJ,EAwLIuxB,uBA5bJ,SAAgCb,GAC5B,OAAO,IAAIkK,SAAQ,CAACv9B,EAASgpC,KACzBF,EAAgCzV,EAAkC,EAAGrzB,EAASgpC,EAAO,GAE7F,EAybIzJ,MAtfJ,WACI,MAAMkM,EAAclN,EAAc7+B,OAClC,IAAIoqC,EAEJ,GAAoB,IAAhB2B,EAAmB,CAEnB,MAAMC,EAAO,SAAU5B,GACnBG,EAAcH,GACe,IAAzBvL,EAAc7+B,SACVipC,GACAA,EAAaoB,oBAAoB,YAAalB,GAC9CF,EAAagD,aAAa,MAAMxX,MAAK,WACjCpC,EAAS0C,QAAQ1f,EAAOiU,kBAC5B,KAEA+I,EAAS0C,QAAQ1f,EAAOiU,mBAGpC,EACA,IAAK,IAAIvpB,EAAI,EAAGA,EAAIgsC,EAAahsC,IAC7BqqC,EAAUvL,EAAc9+B,GACbgO,EAGRq8B,EAFCD,EAAyBC,GACzB4B,EAAKj+B,EAGjB,MACIskB,EAAS0C,QAAQ1f,EAAOiU,mBANpB,IAAWvb,CAQvB,EA0dI8mB,gBAxVJ,SAAyBN,GACrB,OAAO,IAAIsJ,SAAQ,CAACv9B,EAASgpC,KACzB/U,EAAgBuU,iCAAiCoD,kBAC5CzX,MAAM0X,IACH7c,EAAYiF,EAAgBjF,UAC5B4Z,EAAYiD,EACRlD,EACOA,EAAagD,aAAa/C,GAE1BrL,QAAQv9B,aAGtBm0B,MAAK,KACFn0B,EAAQgvB,EAAU,IAErB8F,OAAM,WACHkU,EAAO,CAAEjU,MAAO,gCAAkCd,EAAgBjF,UAAU+E,aAAe,yCAC/F,GAAE,GAEd,EAsUIkD,gBApUJ,SAAyB6U,GACjBnD,IAAiBmD,IAKjBnD,IACAA,EAAaoB,oBAAoB,YAAalB,GAC1CF,EAAagD,cACbhD,EAAagD,aAAa,OAIlChD,EAAemD,EAGXnD,IACAA,EAAaqC,iBAAiB,YAAanC,GACvCF,EAAagD,cAAgB/C,GAC7BD,EAAagD,aAAa/C,IAGtC,EA+SIhU,qBA7SJ,SAA8BD,GAC1B,OAAO,IAAI4I,SAAQ,CAACv9B,EAASgpC,KACzBJ,EAAUhU,qBAAqBD,GAC1BR,MAAK,WACF5B,EAAO+B,KAAK,yDACZvC,EAAS0C,QAAQ1f,EAAOgU,4BACxB/oB,GACJ,IACC80B,OAAOC,IACJiU,EAAOjU,GACPhD,EAAS0C,QAAQ1f,EAAOgU,2BAA4B,CAAEgM,MAAO,IAAIC,EAAAA,EAAY5L,EAAAA,EAAiBW,sCAAuCX,EAAAA,EAAiByB,yCAA2CkK,EAAMpyB,OAAQ,GACjN,GAEd,EAiSIq9B,KA3dJ,WAEI,IAAI8J,EACJ,IAAK,IAAIrqC,EAAI,EAAGA,EAAI8+B,EAAc7+B,OAAQD,IACtCqqC,EAAUvL,EAAc9+B,GACnBqqC,EAAQgB,cACTjB,EAAyBC,GACzBG,EAAcH,GAG1B,EAkdI9R,iBAnQJ,SAA0BR,EAAcpM,GACpC,MAAM0e,EAAUtS,EAAasS,QAGzB9X,EAAwBqD,WAAWrG,KACnC5D,EAAUA,EAAQ2V,SAEtB+I,EAAQiC,OAAO3gB,GACV+I,MAAK,KACFpC,EAAS0C,QAAQ1f,EAAOmU,oBAAoB,IAE/C4L,OAAM,SAAUC,GACbhD,EAAS0C,QAAQ1f,EAAOkT,UAAW,CAAE8M,MAAO,IAAIC,EAAAA,EAAY5L,EAAAA,EAAiBE,kBAAmB,mCAAqCyL,EAAMpyB,KAAM60B,IACrJ,GACR,GA/QIjF,EAAST,EAAM+O,UAAU91B,GACzBikB,EAAY,KACZ2Z,EAAe,KACfC,EAAY,KACZrK,EAAgB,GAChBvM,EAA0B8U,EAAwB17B,GAASyB,cAC3Dg8B,EA+VO,CACHwB,YAAa,SAAUjW,GACnB,GACS,cADDA,EAAM7I,MAEF6I,EAAMnF,SAAU,CAChB,IAAIA,EAAWtb,YAAYkqB,OAAOzJ,EAAMnF,UAAYmF,EAAMnF,SAASlc,OAASqhB,EAAMnF,SAClF8C,EAAS0C,QAAQ1f,EAAO6T,SAAU,CAAEra,IAAK,IAAIy9B,EAAQ/c,EAAUmF,EAAMuJ,eACzE,CAGZ,GA0JD5yB,CACX,CAhiBA09B,GAAuBha,EAAAA,EAAoB9I,0BAA4B,CAAC8I,EAAAA,EAAoB9I,yBAA0B8I,EAAAA,EAAoB7I,yCAC1I6iB,GAAuBha,EAAAA,EAAoB/I,yBAA2B,CAAC+I,EAAAA,EAAoB/I,yBAC3F+iB,GAAuBha,EAAAA,EAAoBhJ,yBAA2B,CAACgJ,EAAAA,EAAoBhJ,yBAgiB3FijB,GAAuB58B,sBAAwB,yBAC/C,OAAehB,EAAAA,EAAakC,gBAAgB07B,ICliB5C,SAASuD,GAAyBr3B,GAE9BA,EAASA,GAAU,CAAC,EACpB,MAAMxJ,EAAUjE,KAAKiE,QACf2mB,EAAWnd,EAAOmd,SAClBhd,EAASH,EAAOG,OAChB+c,EAAQld,EAAOkd,MACfoa,EAAMt3B,EAAOs3B,IAEnB,IAAInhC,EACAwnB,EACAoW,EACA3Z,EACA4Z,EACA3U,EACAsK,EACAsK,EACA7W,EAaJ,SAASuN,IACL,IACI,IAAK,IAAI9/B,EAAI,EAAGA,EAAI8+B,EAAc7+B,OAAQD,IACtCs/B,EAAgBR,EAAc9+B,IAE9BkpC,GACAA,EAAaoB,oBAAoBmC,EAAIC,QAAStD,GAElD9W,EAAS0C,QAAQ1f,EAAOiU,kBAC5B,CAAE,MAAO+L,GACLhD,EAAS0C,QAAQ1f,EAAOiU,kBAAmB,CAAE+L,MAAO,qDAAuDA,EAAM3J,SACrH,CACJ,CA4KA,SAAS2T,EAAgBvH,GACrB,MAAMsS,EAAUtS,EAAasS,QAG7BA,EAAQC,oBAAoBmC,EAAInX,MAAOyC,GACvCsS,EAAQC,oBAAoBmC,EAAI9gB,QAASoM,GACzCsS,EAAQC,oBAAoBmC,EAAIE,MAAO5U,GACvCsS,EAAQC,oBAAoBmC,EAAIlC,MAAOxS,GAGvC,IAAK,IAAI/3B,EAAI,EAAGA,EAAI8+B,EAAc7+B,OAAQD,IACtC,GAAI8+B,EAAc9+B,KAAO+3B,EAAc,CACnC+G,EAAc4L,OAAO1qC,EAAG,GACxB,KACJ,CAIJqqC,EAAQoC,EAAIG,UAChB,CAgCA,SAASV,IACL,IAAIW,EAAiB,KACrB,MAAMC,EAAY,WACd5D,EAAaoB,oBAAoB,iBAAkBuC,GACnD3D,EAAauD,EAAIP,cAAc/C,GAC/B7W,EAAS0C,QAAQ1f,EAAOkU,uBAC5B,EACI0f,EAAa9O,YAAc,EAC3B0S,KAEAD,EAAiBC,EAAUC,KAAKrlC,MAChCwhC,EAAaqC,iBAAiB,iBAAkBsB,GAGxD,CAsFA,OAlBAvhC,EAAW,CACPgsB,eAhTJ,WACI,MAAM1I,EAAS,GACf,IAAK,IAAI5uB,EAAI,EAAGA,EAAI8+B,EAAc7+B,OAAQD,IACtC4uB,EAAO7hB,KAAK+xB,EAAc9+B,GAAGwvB,UAEjC,OAAOZ,CACX,EA2SImI,iBAzSJ,WACI,OAAO+H,CACX,EAwSIrK,uBAtSJ,SAAgCuY,GAC5B,OAAO,IAAIlP,SAAQ,CAACv9B,EAASgpC,KAGzB,IAAI0D,GAAQ,EACZ,IAAK,IAAIvX,EAAQ,EAAGA,EAAQsX,EAAiB/sC,OAAQy1B,IAAS,CAC1D,MAAMpB,EAAe0Y,EAAiBtX,GAAO3B,GAAGO,aAC1CN,EAAUgZ,EAAiBtX,GAAO1B,QACxC,IAAIkZ,EAAiB,KACjBC,EAAiB,KAIrB,IAAK,IAAIC,EAAY,EAAGA,EAAYpZ,EAAQ/zB,OAAQmtC,IAAa,CAC7D,MAAMC,EAASrZ,EAAQoZ,GAAW9b,kBAC5Bgc,EAAStZ,EAAQoZ,GAAW7b,kBAGlC,GAAI8b,GAA4B,IAAlBA,EAAOptC,OAAc,CAC/BitC,EAAiB,GACjB,IAAK,IAAIK,EAAW,EAAGA,EAAWF,EAAOptC,OAAQstC,IACzC/pC,OAAOipC,EAAIe,WAAWC,gBAAgBnZ,EAAc+Y,EAAOE,GAAUnc,cACrE8b,EAAengC,KAAKsgC,EAAOE,GAGvC,CAGA,GAAID,GAA4B,IAAlBA,EAAOrtC,OAAc,CAC/BktC,EAAiB,GACjB,IAAK,IAAIO,EAAW,EAAGA,EAAWJ,EAAOrtC,OAAQytC,IACzClqC,OAAOipC,EAAIe,WAAWC,gBAAgBnZ,EAAcgZ,EAAOI,GAAUtc,cACrE+b,EAAepgC,KAAKugC,EAAOI,GAGvC,CAIA,IAAMR,IAAmBC,GACpBD,GAA4C,IAA1BA,EAAejtC,QACjCktC,GAA4C,IAA1BA,EAAeltC,OAClC,SAIJgtC,GAAQ,EACR,MAAMU,EAAW,IAAI/W,EAAuBsW,EAAgBC,GACtDpZ,EAAKxB,EAAwBoV,2BAA2BrT,GACxDE,EAAkB,IAAIyV,GAAgBlW,EAAI4Z,GAChDrb,EAAS0C,QAAQ1f,EAAOyT,2BAA4B,CAAE5V,KAAMqhB,IAC5Dj0B,EAAQ,CAAE4S,KAAMqhB,IAChB,KACJ,CACJ,CACA,IAAKyY,EAAO,CACR,MAAM/C,EAAe,qFACrB5X,EAAS0C,QAAQ1f,EAAOyT,2BAA4B,CAAEuM,MAAO4U,IAC7DX,EAAO,CAAEjU,MAAO4U,GACpB,IAER,EA0OIpV,gBAxOJ,SAAyB8Y,GACrB,OAAO,IAAI9P,SAAQ,CAACv9B,EAASgpC,KACzB,IACIJ,EAAYyE,EAASzE,UAAY,IAAI3lC,OAAOipC,EAAIe,WAAWI,EAASre,UAAU+E,cAC9E/E,EAAYqe,EAASre,UACrBiF,EAAkBoZ,EACd1E,GACAgD,IAEJ3rC,EAAQgvB,EACZ,CAAE,MAAO+F,GACLiU,EAAO,CAAEjU,MAAO,gCAAkC/F,EAAU+E,aAAe,yCAC/E,IAER,EA2NIkD,gBAzNJ,SAAyB6U,GACjBnD,IAAiBmD,IAKjBnD,GACAA,EAAaoB,oBAAoBmC,EAAIC,QAAStD,GAGlDF,EAAemD,EAGXnD,IACAA,EAAaqC,iBAAiBkB,EAAIC,QAAStD,GACvCD,GACA+C,KAGZ,EAuMI9V,iBArMJ,SAA0ByX,GACtB,IAAKte,IAAc4Z,IAAc3U,EAC7B,MAAM,IAAIlB,MAAM,gEAOpB,IAAIwa,EAAe,KAUnB,GARItZ,EAAgBsU,gBAAgBvX,mBAAqBiD,EAAgBsU,gBAAgBvX,kBAAkBtxB,OAAS,IAChH6tC,EAAetZ,EAAgBsU,gBAAgBvX,kBAAkB,IAGhD,OAAjBuc,GAAyBtZ,EAAgBsU,gBAAgBxX,mBAAqBkD,EAAgBsU,gBAAgBxX,kBAAkBrxB,OAAS,IACzI6tC,EAAetZ,EAAgBsU,gBAAgBxX,kBAAkB,IAGhD,OAAjBwc,EACA,MAAM,IAAIxa,MAAM,sDAGpB,MAAMlC,EAAc0c,EAAa1c,YAC3BiZ,EAAUlB,EAAUuC,cAActa,EAAa,IAAIve,WAAWg7B,EAAOre,UAAWqe,EAAOxI,QAAU,IAAIxyB,WAAWg7B,EAAOxI,SAAW,MAClItN,EAsGV,SAA4BgW,EAAYF,GACpC,MAAO,CAEHxD,QAAS0D,EACTlX,MAAOgX,EAAOhX,MACd+H,gBAAiBiP,GAAUA,EAAOhX,OAAiC,iBAAjBgX,EAAOhX,MAAqBgX,EAAOhX,MAAMjwB,QAAQ,KAAM,IAAIH,cAAgB,GAC7H+oB,SAAUqe,EAAOre,SACjByP,gCAAgC,EAEhCjI,SAAU,WACN,OAAOtvB,KAAKmvB,KAChB,EAEAsB,aAAc,WACV,OAAOzwB,KAAK2iC,QAAQrY,SACxB,EAEAkZ,kBAAmB,WACf,OAAO8C,GACX,EAEA/C,eAAgB,WACZ,MAAO,WACX,EAEAG,eAAgB,WACZ,MAAO,CACH17B,KAAM,EACNvJ,IAAKA,KACM,EAEX8E,IAAKA,KACe,EAG5B,EAKA2/B,YAAa,SAAUjW,GACnB,OAAQA,EAAM7I,MACV,KAAK2gB,EAAInX,MACL,IAAI2Y,EAAW,WACf3b,EAAS0C,QAAQ1f,EAAOkT,UAAW,CAAE8M,MAAO,IAAIC,EAAAA,EAAY5L,EAAAA,EAAiBE,kBAAmBokB,EAAUvmC,QAC1G,MACJ,KAAK+kC,EAAI9gB,QACL,IAAIA,EAAUzX,YAAYkqB,OAAOzJ,EAAMhJ,SAAWgJ,EAAMhJ,QAAQrY,OAASqhB,EAAMhJ,QAC/E2G,EAAS0C,QAAQ1f,EAAO+S,qBAAsB,CAAElV,KAAM,IAAI63B,GAAWtjC,KAAMikB,EAASgJ,EAAMuZ,kBAC1F,MACJ,KAAKzB,EAAIE,MACL7Z,EAAOT,MAAM,mBACbC,EAAS0C,QAAQ1f,EAAOiT,WACxB,MAEJ,KAAKkkB,EAAIlC,MACLzX,EAAOT,MAAM,qCAAuC3qB,KAAKywB,gBACzD7F,EAAS0C,QAAQ1f,EAAOoT,mBAAoB,CAAEvV,KAAMzL,KAAKywB,iBAGrE,EAER,CApKyBgW,CAAmB9D,EAASwD,GAGjDxD,EAAQkB,iBAAiBkB,EAAInX,MAAOyC,GACpCsS,EAAQkB,iBAAiBkB,EAAI9gB,QAASoM,GACtCsS,EAAQkB,iBAAiBkB,EAAIE,MAAO5U,GACpCsS,EAAQkB,iBAAiBkB,EAAIlC,MAAOxS,GAGpC+G,EAAc/xB,KAAKgrB,GACnBjF,EAAOT,MAAM,sCAAwC0F,EAAaI,gBAClE7F,EAAS0C,QAAQ1f,EAAOqT,oBAAqB,CAAExV,KAAM4kB,GACzD,EAiKIQ,iBA/JJ,SAA0BR,EAAcpM,GACpC,MAAM0e,EAAUtS,EAAasS,QAExB9X,EAAwBqD,WAAWrG,GAKpC8a,EAAQiC,OAAO,IAAIz5B,WAAW8Y,EAAQ2V,UAHtC+I,EAAQiC,OAAO,IAAIz5B,WAAW8Y,IAKlC2G,EAAS0C,QAAQ1f,EAAOmU,oBAC5B,EAqJI6V,kBACAnK,qBAzHJ,WAAuD,EA0HnDgB,eAvHJ,WAAsC,EAwHlC0J,iBArHJ,WAA8C,EAsH1CU,KAAMT,EACNA,SApVAhN,EAAST,EAAM+O,UAAU91B,GACzB49B,EAAe,KACf3Z,EAAY,KACZ4Z,EAAY,KACZ3U,EAAkB,KAClBsK,EAAgB,GAChBvM,EAA0B8U,EAAwB17B,GAASyB,cAC3Dg8B,EA2NO,CACHwB,YAAa,SAAUjW,GACnB,GAAQA,EAAM7I,OAEL2gB,EAAIC,SACD/X,EAAMnF,SAAU,CAChB,MAAMA,EAAWtb,YAAYkqB,OAAOzJ,EAAMnF,UAAYmF,EAAMnF,SAASlc,OAASqhB,EAAMnF,SACpF8C,EAAS0C,QAAQ1f,EAAO6T,SAAU,CAAEra,IAAK,IAAIy9B,EAAQ/c,EAAUR,EAAAA,EAAoBxI,gCACvF,CAGZ,GA4GDlb,CACX,CAEAkhC,GAAyBngC,sBAAwB,2BACjD,OAAehB,EAAAA,EAAakC,gBAAgBi/B,IClX5C,SAAS4B,GAAoBj5B,GAEzBA,EAASA,GAAU,CAAC,EACpB,MAAMxJ,EAAUjE,KAAKiE,QACf2mB,EAAWnd,EAAOmd,SAClBhd,EAASH,EAAOG,OAChB+c,EAAQld,EAAOkd,MACfoa,EAAMt3B,EAAOs3B,IACb4B,EAAal5B,EAAOk5B,WAE1B,IAAI/iC,EACAwnB,EACAoW,EACA3Z,EACAgD,EAQA+b,EAIAxP,EAMAyP,EAKAnF,EAYJ,SAAStJ,IACDoJ,GACAsF,IAEJ,IAAK,IAAIxuC,EAAI,EAAGA,EAAI8+B,EAAc7+B,OAAQD,IACtCs/B,EAAgBR,EAAc9+B,IAElCsyB,EAAS0C,QAAQ1f,EAAOiU,kBAC5B,CAmLA,SAAS+V,EAAgBvH,GAErB,IACImR,EAAauD,EAAIgC,kBAAkBlf,EAAU+E,aAAcyD,EAAa/F,UAC5E,CAAE,MAAOsD,GACLhD,EAAS0C,QAAQ1f,EAAOoT,mBAAoB,CACxCvV,KAAM,KACNmiB,MAAO,0BAA4ByC,EAAa/F,UAAY,KAAOsD,EAAM3J,SAEjF,CACJ,CAqIA,SAAS+iB,EAAgBC,EAAc3c,GACnC,GAAKA,GAAc2c,EAEZ,CACH,MAAMhoC,EAAMgoC,EAAa1uC,OACzB,IAAK,IAAID,EAAI,EAAGA,EAAI2G,EAAK3G,IACrB,GAAI2uC,EAAa3uC,GAAGgyB,WAAaA,EAC7B,OAAO2c,EAAa3uC,GAG5B,OAAO,IACX,CATI,OAAO,IAUf,CAEA,SAASwuC,IACLtF,EAAaoB,oBAAoBmC,EAAImC,SAAUxF,GAC/CF,EAAaoB,oBAAoBmC,EAAIC,QAAStD,GAC9CF,EAAaoB,oBAAoBmC,EAAIoC,WAAYzF,GACjDF,EAAaoB,oBAAoBmC,EAAIqC,SAAU1F,EACnD,CAoBA,OAlBA99B,EAAW,CACPgsB,eAtVJ,WACI,MAAM1I,EAAS,GACf,IAAK,IAAI5uB,EAAI,EAAGA,EAAIsuC,EAAgBruC,OAAQD,IACxC4uB,EAAO7hB,KAAKuhC,EAAgBtuC,GAAGwvB,UAEnC,IAAK,IAAIxvB,EAAI,EAAGA,EAAI8+B,EAAc7+B,OAAQD,IACtC4uB,EAAO7hB,KAAK+xB,EAAc9+B,GAAGwvB,UAEjC,OAAOZ,CACX,EA8UImI,iBA5UJ,WACI,OAAO+H,EAAc51B,OAAOolC,EAChC,EA2UI7Z,uBAzUJ,SAAgCuY,GAC5B,OAAO,IAAIlP,SAAQ,CAACv9B,EAASgpC,KACzB,IAAIwF,EAAK7F,EACJ6F,IACDA,EAAKC,SAASC,cAAc,UAKhC,IAAIhC,GAAQ,EACZ,IAAK,IAAIvX,EAAQ,EAAGA,EAAQsX,EAAiB/sC,OAAQy1B,IAAS,CAC1D,MAAMpB,EAAe0Y,EAAiBtX,GAAO3B,GAAGO,aAC1CN,EAAUgZ,EAAiBtX,GAAO1B,QACxC,IAAIkZ,EAAiB,KACjBC,EAAiB,KAIrB,IAAK,IAAIC,EAAY,EAAGA,EAAYpZ,EAAQ/zB,OAAQmtC,IAAa,CAE7D,MAAME,EAAStZ,EAAQoZ,GAAW7b,kBAElC,GAAI+b,GAA4B,IAAlBA,EAAOrtC,OAAc,CAC/BktC,EAAiB,GACjB,IAAK,IAAIO,EAAW,EAAGA,EAAWJ,EAAOrtC,OAAQytC,IACsB,KAA/DqB,EAAGG,YAAY5B,EAAOI,GAAUtc,YAAakD,IAC7C6Y,EAAepgC,KAAKugC,EAAOI,GAGvC,CAIA,IAAMR,IAAmBC,GACpBD,GAA4C,IAA1BA,EAAejtC,QACjCktC,GAA4C,IAA1BA,EAAeltC,OAClC,SAIJgtC,GAAQ,EACR,MAAMU,EAAW,IAAI/W,EAAuBsW,EAAgBC,GACtDpZ,EAAKxB,EAAwBoV,2BAA2BrT,GACxDE,EAAkB,IAAIyV,GAAgBlW,EAAI4Z,GAChDrb,EAAS0C,QAAQ1f,EAAOyT,2BAA4B,CAAE5V,KAAMqhB,IAC5Dj0B,EAAQ,CAAE4S,KAAMqhB,IAChB,KACJ,CACJ,CACA,IAAKyY,EAAO,CACR,MAAM/C,EAAe,qFACrB5X,EAAS0C,QAAQ1f,EAAOyT,2BAA4B,CAAEuM,MAAO4U,IAC7DX,EAAO,CAAEjU,MAAO4U,GACpB,IAGR,EAkRIpV,gBAhRJ,SAAyBN,GAErB,OADAjF,EAAYiF,EAAgBjF,UACrBuO,QAAQv9B,QAAQgvB,EAC3B,EA8QIiI,gBA5QJ,SAAyB6U,GACrB,GAAInD,IAAiBmD,EAArB,CAKA,GAAInD,EAAc,CACdsF,IAGA,IAAK,IAAIxuC,EAAI,EAAGA,EAAI8+B,EAAc7+B,OAAQD,IACtCs/B,EAAgBR,EAAc9+B,IAElC8+B,EAAgB,EACpB,CAEAoK,EAAemD,EAGXnD,IACAA,EAAaqC,iBAAiBkB,EAAImC,SAAUxF,GAC5CF,EAAaqC,iBAAiBkB,EAAIC,QAAStD,GAC3CF,EAAaqC,iBAAiBkB,EAAIoC,WAAYzF,GAC9CF,EAAaqC,iBAAiBkB,EAAIqC,SAAU1F,GAC5C9W,EAAS0C,QAAQ1f,EAAOkU,wBArB5B,CAuBJ,EAmPI4M,iBAjPJ,SAA0ByX,GACtB,IAAKte,EACD,MAAM,IAAI+D,MAAM,gEAIpB,GAAIib,GAAgD,IAAzBzP,EAAc7+B,OAAc,CACnD,MAAMkvC,EAAa,CACfnd,UAAW,KACX6E,MAAOgX,EAAOhX,MACd+H,gBAAiBiP,GAAUA,EAAOhX,OAAiC,iBAAjBgX,EAAOhX,MAAqBgX,EAAOhX,MAAMjwB,QAAQ,KAAM,IAAIH,cAAgB,GAC7H+oB,SAAUqe,EAAOre,SACjByP,gCAAgC,EAEhCjI,SAAU,WACN,OAAOtvB,KAAKmvB,KAChB,EAEAsB,aAAc,WACV,OAAOzwB,KAAKsqB,SAChB,EAEAkZ,kBAAmB,WACf,OAAO8C,GACX,EAEA/C,eAAgB,WACZ,MAAO,WACX,EAEAG,eAAgB,WACZ,MAAO,CACH17B,KAAM,EACNvJ,IAAKA,KACM,EAEX8E,IAAKA,KACe,EAG5B,GAOJ,OALAqjC,EAAgBvhC,KAAKoiC,GAGrBjG,EAAauD,EAAI2C,oBAAoB7f,EAAU+E,aAAc,IAAIzhB,WAAWg7B,EAAOre,WAE5E2f,CAEX,CACI,MAAM,IAAI7b,MAAM,iCAGxB,EA6LIiF,iBA3LJ,SAA0BR,EAAcpM,GACpC,MAAMqG,EAAY+F,EAAa/F,UAC/B,GAAKO,EAAwBqD,WAAWrG,GAMpC,IAAK,IAAIvvB,EAAI,EAAGA,EAAI2rB,EAAQ2M,SAASr4B,OAAQD,IACzCkpC,EAAauD,EAAI4C,QAAQ9f,EAAU+E,aAC/B3I,EAAQ2M,SAASt4B,GAAG8O,IAAK6c,EAAQ2M,SAASt4B,GAAGqhC,MAAOrP,QAN5DkX,EAAauD,EAAI4C,QAAQ9f,EAAU+E,aAC/B,IAAIzhB,WAAW8Y,GAAU,IAAI9Y,WAAWklB,EAAavI,UAAWwC,GAQxEM,EAAS0C,QAAQ1f,EAAOmU,oBAC5B,EA8KI6V,kBACAnK,qBAjKJ,WAAuD,EAkKnDgB,eA/JJ,WAAsC,EAgKlC0J,iBA7JJ,WAA8C,EA8J1CU,KAAMT,EACNA,SArXAhN,EAAST,EAAM+O,UAAU91B,GACzB49B,EAAe,KACf3Z,EAAY,KACZ+e,EAAkB,GAClBxP,EAAgB,GAChBvM,EAA0B8U,EAAwB17B,GAASyB,cAC3Dg8B,EAoNO,CACHwB,YAAa,SAAUjW,GACnB,IAAIoD,EAAe,KACnB,OAAQpD,EAAM7I,MACV,KAAK2gB,EAAIC,QACL,IAAIld,EAAWtb,YAAYkqB,OAAOzJ,EAAMnF,UAAYmF,EAAMnF,SAASlc,OAASqhB,EAAMnF,SAClF8C,EAAS0C,QAAQ1f,EAAO6T,SAAU,CAAEra,IAAK,IAAIy9B,EAAQ/c,EAAUR,EAAAA,EAAoBxI,iCACnF,MAEJ,KAAKimB,EAAImC,SAML,GALA7W,EAAe2W,EAAgB5P,EAAenK,EAAM3C,WAC/C+F,IACDA,EAAe2W,EAAgBJ,EAAiB3Z,EAAM3C,YAGtD+F,EAAc,CACd,IAAIp4B,EAAOgqB,EAAAA,EAAiBE,kBACxBia,EAAM,GACV,OAAQnP,EAAM2a,UAAU3vC,MACpB,KAAK,EACDA,EAAOgqB,EAAAA,EAAiBG,0BACxBga,GAAO,0BAA4Bna,EAAAA,EAAiBiB,6BACpD,MACJ,KAAK,EACDjrB,EAAOgqB,EAAAA,EAAiBI,yBACxB+Z,GAAO,yBAA2Bna,EAAAA,EAAiBkB,4BACnD,MACJ,KAAK,EACDlrB,EAAOgqB,EAAAA,EAAiBK,0BACxB8Z,GAAO,0BAA4Bna,EAAAA,EAAiBmB,6BACpD,MACJ,KAAK,EACDnrB,EAAOgqB,EAAAA,EAAiBM,yBACxB6Z,GAAO,yBAA2Bna,EAAAA,EAAiBoB,4BACnD,MACJ,KAAK,EACDprB,EAAOgqB,EAAAA,EAAiBO,iCACxB4Z,GAAO,iCAAmCna,EAAAA,EAAiBqB,oCAC3D,MACJ,KAAK,EACDrrB,EAAOgqB,EAAAA,EAAiBQ,yBACxB2Z,GAAO,yBAA2Bna,EAAAA,EAAiBsB,4BAG3D6Y,GAAO,mBAAqBnP,EAAM4a,WAElCjd,EAAS0C,QAAQ1f,EAAOkT,UAAW,CAAE8M,MAAO,IAAIC,EAAAA,EAAY51B,EAAMmkC,EAAK/L,IAC3E,MACIjF,EAAOwC,MAAM,wCAEjB,MAEJ,KAAKmX,EAAIqC,SACL/W,EAAe2W,EAAgB5P,EAAenK,EAAM3C,WAC/C+F,IACDA,EAAe2W,EAAgBJ,EAAiB3Z,EAAM3C,YAGtD+F,GACAjF,EAAOT,MAAM,mBACbC,EAAS0C,QAAQ1f,EAAOiT,UAAW,CAAEpV,KAAM4kB,KAE3CjF,EAAOT,MAAM,wCAEjB,MAEJ,KAAKoa,EAAIoC,WA4BL,GAzBAN,EAA2C,OAApB5Z,EAAM3C,gBAA4CpxB,IAApB+zB,EAAM3C,UAGvDuc,GAEAxW,EAAe2W,EAAgB5P,EAAenK,EAAM3C,YAC/C+F,GAAgBuW,EAAgBruC,OAAS,IAI1C83B,EAAeuW,EAAgBkB,QAC/B1Q,EAAc/xB,KAAKgrB,GACnBA,EAAa/F,UAAY2C,EAAM3C,UAE/BM,EAAS0C,QAAQ1f,EAAOqT,oBAAqB,CAAExV,KAAM4kB,MAElDuW,EAAgBruC,OAAS,IAChC83B,EAAeuW,EAAgBkB,QAC/B1Q,EAAc/xB,KAAKgrB,GAEY,IAA3BuW,EAAgBruC,QAChBouC,EAAW/Y,MAAM,IAAIC,EAAAA,EAAY5L,EAAAA,EAAiBS,6BAA8BT,EAAAA,EAAiBuB,mCAIrG6M,EAAc,CACd,IAAIpM,EAAUzX,YAAYkqB,OAAOzJ,EAAMhJ,SAAWgJ,EAAMhJ,QAAQrY,OAASqhB,EAAMhJ,QAK/EoM,EAAaD,WAAanM,EAC1B2G,EAAS0C,QAAQ1f,EAAO+S,qBAAsB,CAAElV,KAAM,IAAI63B,GAAWjT,EAAcpM,EAASgJ,EAAMkU,aAEtG,MACI/V,EAAO2J,KAAK,0CAI5B,GAoDDnxB,CACX,CAEA8iC,GAAoB/hC,sBAAwB,sBAC5C,OAAehB,EAAAA,EAAakC,gBAAgB6gC,ICjb5C,MAAMqB,GAA2B,CAE7B,CAEIL,mBAAoB,qBACpBC,OAAQ,SACRZ,iBAAkB,mBAGlB/B,QAAS,UACTkC,SAAU,WACVE,SAAU,WACVD,WAAY,cAGhB,CAEIO,mBAAoB,2BACpBC,OAAQ,eACRZ,iBAAkB,yBAGlB/B,QAAS,gBACTkC,SAAU,iBACVE,SAAU,iBACVD,WAAY,qBAIda,GAAgC,CAGlC,CAEIxD,aAAc,eAEdsB,UAAW,YAEXZ,QAAS,QAGTF,QAAS,UACTpX,MAAO,WACP3J,QAAS,aACTghB,MAAO,WACPpC,MAAO,YAGX,CAEI2B,aAAc,iBAEdsB,UAAW,cAEXZ,QAAS,QAETF,QAAS,YACTpX,MAAO,aACP3J,QAAS,eACTghB,MAAO,aACPpC,MAAO,eAIf,SAASoF,KACL,IAAIrkC,EACJ,MAAMK,EAAUjE,KAAKiE,QA2ErB,SAASikC,EAAQ1G,EAAc2G,GAC3B,IAAK,IAAI7vC,EAAI,EAAGA,EAAI6vC,EAAK5vC,OAAQD,IAAK,CAClC,MAAMysC,EAAMoD,EAAK7vC,GAGjB,GAAsD,mBAA3CkpC,EAAauD,EAAIte,OAAO0H,KAAK4W,GAAK,KAI7C,OAAOA,CACX,CAEA,OAAO,IACX,CAMA,OAJAnhC,EAAW,CACPwkC,uBAjFJ,SAAgC36B,GAC5B,IAAI46B,EAAa,KAEjB,MAAMxd,EAA0B8U,EAAwB17B,GAASyB,cACjEmlB,EAAwBkW,UAAU,CAAEpW,MAAOld,EAAOkd,MAAOvC,OAAQ3a,EAAO2a,OAAQ0C,SAAUrd,EAAOqd,WACjGD,EAAwBiW,aAExB,IAAIrV,EAoBR,SAA6Bhe,GACzB,MAAMkd,EAAQld,EAAOkd,MACfS,EAAST,EAAM+O,UAAU91B,GACzBgnB,EAAWnd,EAAOmd,SAClB+b,EAAal5B,EAAOk5B,WACpBnF,EAAe/zB,EAAO66B,WAAa76B,EAAO66B,WAAWC,aAAe,KAE1E,OAAM/G,QAA6CtoC,IAA7BsoC,EAAagH,aAC7BhH,QAA2CtoC,IAA3BsoC,EAAaC,UAOxByG,EAAQ1G,EAAcwG,KAC7B5c,EAAO+B,KAAK,+DACL2X,GAAyB7gC,GAAS6B,OAAO,CAC5C6kB,MAAOA,EACPC,SAAUA,EACVhd,OAAQH,EAAOG,OACfm3B,IAAKmD,EAAQ1G,EAAcwG,OAExBE,EAAQ1G,EAAcuG,KAC7B3c,EAAO+B,KAAK,0DACLuZ,GAAoBziC,GAAS6B,OAAO,CACvC6kB,MAAOA,EACPC,SAAUA,EACV+b,WAAYA,EACZ/4B,OAAQH,EAAOG,OACfm3B,IAAKmD,EAAQ1G,EAAcuG,QAG/B3c,EAAO2J,KAAK,4GACL,OAzBP3J,EAAO+B,KAAK,4DACLoU,GAAuBt9B,GAAS6B,OAAO,CAC1C6kB,MAAOA,EACPC,SAAUA,EACVhd,OAAQH,EAAOG,SAuB3B,CAxD0B66B,CAAoBh7B,GAiB1C,OAfIge,IACA4c,EAAa9d,EAAqBtmB,GAAS6B,OAAO,CAC9CsiB,OAAQ3a,EAAO2a,OACfoC,UAAW/c,EAAO+c,UAClBC,UAAWhd,EAAOgd,UAClBC,sBAAuBjd,EAAOid,sBAC9BC,MAAOld,EAAOkd,MACdC,SAAUnd,EAAOmd,SACjBhd,OAAQH,EAAOG,OACfid,wBAAyBA,EACzBY,gBAAiBA,EACjBX,SAAUrd,EAAOqd,WAErBrd,EAAO24B,aAAasC,4BAA2B,IAE5CL,CACX,GA2DOzkC,CACX,CAEAqkC,GAAWtjC,sBAAwB,aACnC,MAAMvN,GAAUuxC,OAAOhlC,aAAakC,gBAAgBoiC,IACpD7wC,GAAQwW,OAAS2S,EAAAA,EACjBnpB,GAAQoW,OAASyU,EAAAA,EACjB0mB,OAAOhlC,aAAaqC,mBAAmBiiC,GAAWtjC,sBAAuBvN,IACzE,S", "sources": ["webpack://dashjs/webpack/universalModuleDefinition", "webpack://dashjs/./node_modules/path-browserify/index.js", "webpack://dashjs/./node_modules/ua-parser-js/src/ua-parser.js", "webpack://dashjs/./src/core/FactoryMaker.js", "webpack://dashjs/./src/core/Utils.js", "webpack://dashjs/./src/core/errors/ErrorsBase.js", "webpack://dashjs/./src/core/events/EventsBase.js", "webpack://dashjs/./src/dash/constants/DashConstants.js", "webpack://dashjs/./src/streaming/constants/Constants.js", "webpack://dashjs/./src/streaming/constants/ProtectionConstants.js", "webpack://dashjs/./src/streaming/protection/ProtectionEvents.js", "webpack://dashjs/./src/streaming/protection/errors/ProtectionErrors.js", "webpack://dashjs/./src/streaming/vo/DashJSError.js", "webpack://dashjs/./src/streaming/vo/metrics/HTTPRequest.js", "webpack://dashjs/webpack/bootstrap", "webpack://dashjs/webpack/runtime/amd options", "webpack://dashjs/webpack/runtime/define property getters", "webpack://dashjs/webpack/runtime/hasOwnProperty shorthand", "webpack://dashjs/./src/streaming/protection/CommonEncryption.js", "webpack://dashjs/./src/streaming/protection/vo/MediaCapability.js", "webpack://dashjs/./src/streaming/protection/vo/KeySystemConfiguration.js", "webpack://dashjs/./src/streaming/protection/vo/LicenseRequest.js", "webpack://dashjs/./src/streaming/protection/vo/LicenseResponse.js", "webpack://dashjs/./src/streaming/protection/controllers/ProtectionController.js", "webpack://dashjs/./src/streaming/protection/vo/KeyPair.js", "webpack://dashjs/./src/streaming/protection/vo/ClearKeyKeySet.js", "webpack://dashjs/./src/streaming/protection/drm/KeySystemClearKey.js", "webpack://dashjs/./src/streaming/protection/drm/KeySystemW3CClearKey.js", "webpack://dashjs/./src/streaming/protection/drm/KeySystemWidevine.js", "webpack://dashjs/./src/streaming/protection/drm/KeySystemPlayReady.js", "webpack://dashjs/./src/streaming/protection/servers/DRMToday.js", "webpack://dashjs/./src/streaming/protection/servers/PlayReady.js", "webpack://dashjs/./src/streaming/protection/servers/Widevine.js", "webpack://dashjs/./src/streaming/protection/servers/ClearKey.js", "webpack://dashjs/./src/streaming/protection/vo/KeySystemMetadata.js", "webpack://dashjs/./src/streaming/protection/controllers/ProtectionKeyController.js", "webpack://dashjs/./src/streaming/protection/vo/NeedKey.js", "webpack://dashjs/./src/streaming/protection/vo/KeyMessage.js", "webpack://dashjs/./src/streaming/protection/vo/KeySystemAccess.js", "webpack://dashjs/./src/streaming/protection/models/DefaultProtectionModel.js", "webpack://dashjs/./src/streaming/protection/models/ProtectionModel_3Feb2014.js", "webpack://dashjs/./src/streaming/protection/models/ProtectionModel_01b.js", "webpack://dashjs/./src/streaming/protection/Protection.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"dashjs\"] = factory();\n\telse\n\t\troot[\"dashjs\"] = factory();\n})(self, function() {\nreturn ", "// 'path' module extracted from Node.js v8.11.1 (only the posix part)\n// transplited with Babel\n\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nfunction assertPath(path) {\n  if (typeof path !== 'string') {\n    throw new TypeError('Path must be a string. Received ' + JSON.stringify(path));\n  }\n}\n\n// Resolves . and .. elements in a path with directory names\nfunction normalizeStringPosix(path, allowAboveRoot) {\n  var res = '';\n  var lastSegmentLength = 0;\n  var lastSlash = -1;\n  var dots = 0;\n  var code;\n  for (var i = 0; i <= path.length; ++i) {\n    if (i < path.length)\n      code = path.charCodeAt(i);\n    else if (code === 47 /*/*/)\n      break;\n    else\n      code = 47 /*/*/;\n    if (code === 47 /*/*/) {\n      if (lastSlash === i - 1 || dots === 1) {\n        // NOOP\n      } else if (lastSlash !== i - 1 && dots === 2) {\n        if (res.length < 2 || lastSegmentLength !== 2 || res.charCodeAt(res.length - 1) !== 46 /*.*/ || res.charCodeAt(res.length - 2) !== 46 /*.*/) {\n          if (res.length > 2) {\n            var lastSlashIndex = res.lastIndexOf('/');\n            if (lastSlashIndex !== res.length - 1) {\n              if (lastSlashIndex === -1) {\n                res = '';\n                lastSegmentLength = 0;\n              } else {\n                res = res.slice(0, lastSlashIndex);\n                lastSegmentLength = res.length - 1 - res.lastIndexOf('/');\n              }\n              lastSlash = i;\n              dots = 0;\n              continue;\n            }\n          } else if (res.length === 2 || res.length === 1) {\n            res = '';\n            lastSegmentLength = 0;\n            lastSlash = i;\n            dots = 0;\n            continue;\n          }\n        }\n        if (allowAboveRoot) {\n          if (res.length > 0)\n            res += '/..';\n          else\n            res = '..';\n          lastSegmentLength = 2;\n        }\n      } else {\n        if (res.length > 0)\n          res += '/' + path.slice(lastSlash + 1, i);\n        else\n          res = path.slice(lastSlash + 1, i);\n        lastSegmentLength = i - lastSlash - 1;\n      }\n      lastSlash = i;\n      dots = 0;\n    } else if (code === 46 /*.*/ && dots !== -1) {\n      ++dots;\n    } else {\n      dots = -1;\n    }\n  }\n  return res;\n}\n\nfunction _format(sep, pathObject) {\n  var dir = pathObject.dir || pathObject.root;\n  var base = pathObject.base || (pathObject.name || '') + (pathObject.ext || '');\n  if (!dir) {\n    return base;\n  }\n  if (dir === pathObject.root) {\n    return dir + base;\n  }\n  return dir + sep + base;\n}\n\nvar posix = {\n  // path.resolve([from ...], to)\n  resolve: function resolve() {\n    var resolvedPath = '';\n    var resolvedAbsolute = false;\n    var cwd;\n\n    for (var i = arguments.length - 1; i >= -1 && !resolvedAbsolute; i--) {\n      var path;\n      if (i >= 0)\n        path = arguments[i];\n      else {\n        if (cwd === undefined)\n          cwd = process.cwd();\n        path = cwd;\n      }\n\n      assertPath(path);\n\n      // Skip empty entries\n      if (path.length === 0) {\n        continue;\n      }\n\n      resolvedPath = path + '/' + resolvedPath;\n      resolvedAbsolute = path.charCodeAt(0) === 47 /*/*/;\n    }\n\n    // At this point the path should be resolved to a full absolute path, but\n    // handle relative paths to be safe (might happen when process.cwd() fails)\n\n    // Normalize the path\n    resolvedPath = normalizeStringPosix(resolvedPath, !resolvedAbsolute);\n\n    if (resolvedAbsolute) {\n      if (resolvedPath.length > 0)\n        return '/' + resolvedPath;\n      else\n        return '/';\n    } else if (resolvedPath.length > 0) {\n      return resolvedPath;\n    } else {\n      return '.';\n    }\n  },\n\n  normalize: function normalize(path) {\n    assertPath(path);\n\n    if (path.length === 0) return '.';\n\n    var isAbsolute = path.charCodeAt(0) === 47 /*/*/;\n    var trailingSeparator = path.charCodeAt(path.length - 1) === 47 /*/*/;\n\n    // Normalize the path\n    path = normalizeStringPosix(path, !isAbsolute);\n\n    if (path.length === 0 && !isAbsolute) path = '.';\n    if (path.length > 0 && trailingSeparator) path += '/';\n\n    if (isAbsolute) return '/' + path;\n    return path;\n  },\n\n  isAbsolute: function isAbsolute(path) {\n    assertPath(path);\n    return path.length > 0 && path.charCodeAt(0) === 47 /*/*/;\n  },\n\n  join: function join() {\n    if (arguments.length === 0)\n      return '.';\n    var joined;\n    for (var i = 0; i < arguments.length; ++i) {\n      var arg = arguments[i];\n      assertPath(arg);\n      if (arg.length > 0) {\n        if (joined === undefined)\n          joined = arg;\n        else\n          joined += '/' + arg;\n      }\n    }\n    if (joined === undefined)\n      return '.';\n    return posix.normalize(joined);\n  },\n\n  relative: function relative(from, to) {\n    assertPath(from);\n    assertPath(to);\n\n    if (from === to) return '';\n\n    from = posix.resolve(from);\n    to = posix.resolve(to);\n\n    if (from === to) return '';\n\n    // Trim any leading backslashes\n    var fromStart = 1;\n    for (; fromStart < from.length; ++fromStart) {\n      if (from.charCodeAt(fromStart) !== 47 /*/*/)\n        break;\n    }\n    var fromEnd = from.length;\n    var fromLen = fromEnd - fromStart;\n\n    // Trim any leading backslashes\n    var toStart = 1;\n    for (; toStart < to.length; ++toStart) {\n      if (to.charCodeAt(toStart) !== 47 /*/*/)\n        break;\n    }\n    var toEnd = to.length;\n    var toLen = toEnd - toStart;\n\n    // Compare paths to find the longest common path from root\n    var length = fromLen < toLen ? fromLen : toLen;\n    var lastCommonSep = -1;\n    var i = 0;\n    for (; i <= length; ++i) {\n      if (i === length) {\n        if (toLen > length) {\n          if (to.charCodeAt(toStart + i) === 47 /*/*/) {\n            // We get here if `from` is the exact base path for `to`.\n            // For example: from='/foo/bar'; to='/foo/bar/baz'\n            return to.slice(toStart + i + 1);\n          } else if (i === 0) {\n            // We get here if `from` is the root\n            // For example: from='/'; to='/foo'\n            return to.slice(toStart + i);\n          }\n        } else if (fromLen > length) {\n          if (from.charCodeAt(fromStart + i) === 47 /*/*/) {\n            // We get here if `to` is the exact base path for `from`.\n            // For example: from='/foo/bar/baz'; to='/foo/bar'\n            lastCommonSep = i;\n          } else if (i === 0) {\n            // We get here if `to` is the root.\n            // For example: from='/foo'; to='/'\n            lastCommonSep = 0;\n          }\n        }\n        break;\n      }\n      var fromCode = from.charCodeAt(fromStart + i);\n      var toCode = to.charCodeAt(toStart + i);\n      if (fromCode !== toCode)\n        break;\n      else if (fromCode === 47 /*/*/)\n        lastCommonSep = i;\n    }\n\n    var out = '';\n    // Generate the relative path based on the path difference between `to`\n    // and `from`\n    for (i = fromStart + lastCommonSep + 1; i <= fromEnd; ++i) {\n      if (i === fromEnd || from.charCodeAt(i) === 47 /*/*/) {\n        if (out.length === 0)\n          out += '..';\n        else\n          out += '/..';\n      }\n    }\n\n    // Lastly, append the rest of the destination (`to`) path that comes after\n    // the common path parts\n    if (out.length > 0)\n      return out + to.slice(toStart + lastCommonSep);\n    else {\n      toStart += lastCommonSep;\n      if (to.charCodeAt(toStart) === 47 /*/*/)\n        ++toStart;\n      return to.slice(toStart);\n    }\n  },\n\n  _makeLong: function _makeLong(path) {\n    return path;\n  },\n\n  dirname: function dirname(path) {\n    assertPath(path);\n    if (path.length === 0) return '.';\n    var code = path.charCodeAt(0);\n    var hasRoot = code === 47 /*/*/;\n    var end = -1;\n    var matchedSlash = true;\n    for (var i = path.length - 1; i >= 1; --i) {\n      code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          if (!matchedSlash) {\n            end = i;\n            break;\n          }\n        } else {\n        // We saw the first non-path separator\n        matchedSlash = false;\n      }\n    }\n\n    if (end === -1) return hasRoot ? '/' : '.';\n    if (hasRoot && end === 1) return '//';\n    return path.slice(0, end);\n  },\n\n  basename: function basename(path, ext) {\n    if (ext !== undefined && typeof ext !== 'string') throw new TypeError('\"ext\" argument must be a string');\n    assertPath(path);\n\n    var start = 0;\n    var end = -1;\n    var matchedSlash = true;\n    var i;\n\n    if (ext !== undefined && ext.length > 0 && ext.length <= path.length) {\n      if (ext.length === path.length && ext === path) return '';\n      var extIdx = ext.length - 1;\n      var firstNonSlashEnd = -1;\n      for (i = path.length - 1; i >= 0; --i) {\n        var code = path.charCodeAt(i);\n        if (code === 47 /*/*/) {\n            // If we reached a path separator that was not part of a set of path\n            // separators at the end of the string, stop now\n            if (!matchedSlash) {\n              start = i + 1;\n              break;\n            }\n          } else {\n          if (firstNonSlashEnd === -1) {\n            // We saw the first non-path separator, remember this index in case\n            // we need it if the extension ends up not matching\n            matchedSlash = false;\n            firstNonSlashEnd = i + 1;\n          }\n          if (extIdx >= 0) {\n            // Try to match the explicit extension\n            if (code === ext.charCodeAt(extIdx)) {\n              if (--extIdx === -1) {\n                // We matched the extension, so mark this as the end of our path\n                // component\n                end = i;\n              }\n            } else {\n              // Extension does not match, so our result is the entire path\n              // component\n              extIdx = -1;\n              end = firstNonSlashEnd;\n            }\n          }\n        }\n      }\n\n      if (start === end) end = firstNonSlashEnd;else if (end === -1) end = path.length;\n      return path.slice(start, end);\n    } else {\n      for (i = path.length - 1; i >= 0; --i) {\n        if (path.charCodeAt(i) === 47 /*/*/) {\n            // If we reached a path separator that was not part of a set of path\n            // separators at the end of the string, stop now\n            if (!matchedSlash) {\n              start = i + 1;\n              break;\n            }\n          } else if (end === -1) {\n          // We saw the first non-path separator, mark this as the end of our\n          // path component\n          matchedSlash = false;\n          end = i + 1;\n        }\n      }\n\n      if (end === -1) return '';\n      return path.slice(start, end);\n    }\n  },\n\n  extname: function extname(path) {\n    assertPath(path);\n    var startDot = -1;\n    var startPart = 0;\n    var end = -1;\n    var matchedSlash = true;\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    var preDotState = 0;\n    for (var i = path.length - 1; i >= 0; --i) {\n      var code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          // If we reached a path separator that was not part of a set of path\n          // separators at the end of the string, stop now\n          if (!matchedSlash) {\n            startPart = i + 1;\n            break;\n          }\n          continue;\n        }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === 46 /*.*/) {\n          // If this is our first dot, mark it as the start of our extension\n          if (startDot === -1)\n            startDot = i;\n          else if (preDotState !== 1)\n            preDotState = 1;\n      } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n\n    if (startDot === -1 || end === -1 ||\n        // We saw a non-dot character immediately before the dot\n        preDotState === 0 ||\n        // The (right-most) trimmed path component is exactly '..'\n        preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n      return '';\n    }\n    return path.slice(startDot, end);\n  },\n\n  format: function format(pathObject) {\n    if (pathObject === null || typeof pathObject !== 'object') {\n      throw new TypeError('The \"pathObject\" argument must be of type Object. Received type ' + typeof pathObject);\n    }\n    return _format('/', pathObject);\n  },\n\n  parse: function parse(path) {\n    assertPath(path);\n\n    var ret = { root: '', dir: '', base: '', ext: '', name: '' };\n    if (path.length === 0) return ret;\n    var code = path.charCodeAt(0);\n    var isAbsolute = code === 47 /*/*/;\n    var start;\n    if (isAbsolute) {\n      ret.root = '/';\n      start = 1;\n    } else {\n      start = 0;\n    }\n    var startDot = -1;\n    var startPart = 0;\n    var end = -1;\n    var matchedSlash = true;\n    var i = path.length - 1;\n\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    var preDotState = 0;\n\n    // Get non-dir info\n    for (; i >= start; --i) {\n      code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          // If we reached a path separator that was not part of a set of path\n          // separators at the end of the string, stop now\n          if (!matchedSlash) {\n            startPart = i + 1;\n            break;\n          }\n          continue;\n        }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === 46 /*.*/) {\n          // If this is our first dot, mark it as the start of our extension\n          if (startDot === -1) startDot = i;else if (preDotState !== 1) preDotState = 1;\n        } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n\n    if (startDot === -1 || end === -1 ||\n    // We saw a non-dot character immediately before the dot\n    preDotState === 0 ||\n    // The (right-most) trimmed path component is exactly '..'\n    preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n      if (end !== -1) {\n        if (startPart === 0 && isAbsolute) ret.base = ret.name = path.slice(1, end);else ret.base = ret.name = path.slice(startPart, end);\n      }\n    } else {\n      if (startPart === 0 && isAbsolute) {\n        ret.name = path.slice(1, startDot);\n        ret.base = path.slice(1, end);\n      } else {\n        ret.name = path.slice(startPart, startDot);\n        ret.base = path.slice(startPart, end);\n      }\n      ret.ext = path.slice(startDot, end);\n    }\n\n    if (startPart > 0) ret.dir = path.slice(0, startPart - 1);else if (isAbsolute) ret.dir = '/';\n\n    return ret;\n  },\n\n  sep: '/',\n  delimiter: ':',\n  win32: null,\n  posix: null\n};\n\nposix.posix = posix;\n\nmodule.exports = posix;\n", "/////////////////////////////////////////////////////////////////////////////////\n/* UAParser.js v1.0.38\n   Copyright © 2012-2021 F<PERSON><PERSON> <<EMAIL>>\n   MIT License *//*\n   Detect Browser, Engine, OS, CPU, and Device type/model from User-Agent data.\n   Supports browser & node.js environment. \n   Demo   : https://faisalman.github.io/ua-parser-js\n   Source : https://github.com/faisalman/ua-parser-js */\n/////////////////////////////////////////////////////////////////////////////////\n\n(function (window, undefined) {\n\n    'use strict';\n\n    //////////////\n    // Constants\n    /////////////\n\n\n    var LIBVERSION  = '1.0.38',\n        EMPTY       = '',\n        UNKNOWN     = '?',\n        FUNC_TYPE   = 'function',\n        UNDEF_TYPE  = 'undefined',\n        OBJ_TYPE    = 'object',\n        STR_TYPE    = 'string',\n        MAJOR       = 'major',\n        MODEL       = 'model',\n        NAME        = 'name',\n        TYPE        = 'type',\n        VENDOR      = 'vendor',\n        VERSION     = 'version',\n        ARCHITECTURE= 'architecture',\n        CONSOLE     = 'console',\n        MOBILE      = 'mobile',\n        TABLET      = 'tablet',\n        SMARTTV     = 'smarttv',\n        WEARABLE    = 'wearable',\n        EMBEDDED    = 'embedded',\n        UA_MAX_LENGTH = 500;\n\n    var AMAZON  = 'Amazon',\n        APPLE   = 'Apple',\n        ASUS    = 'ASUS',\n        BLACKBERRY = 'BlackBerry',\n        BROWSER = 'Browser',\n        CHROME  = 'Chrome',\n        EDGE    = 'Edge',\n        FIREFOX = 'Firefox',\n        GOOGLE  = 'Google',\n        HUAWEI  = 'Huawei',\n        LG      = 'LG',\n        MICROSOFT = 'Microsoft',\n        MOTOROLA  = 'Motorola',\n        OPERA   = 'Opera',\n        SAMSUNG = 'Samsung',\n        SHARP   = 'Sharp',\n        SONY    = 'Sony',\n        XIAOMI  = 'Xiaomi',\n        ZEBRA   = 'Zebra',\n        FACEBOOK    = 'Facebook',\n        CHROMIUM_OS = 'Chromium OS',\n        MAC_OS  = 'Mac OS';\n\n    ///////////\n    // Helper\n    //////////\n\n    var extend = function (regexes, extensions) {\n            var mergedRegexes = {};\n            for (var i in regexes) {\n                if (extensions[i] && extensions[i].length % 2 === 0) {\n                    mergedRegexes[i] = extensions[i].concat(regexes[i]);\n                } else {\n                    mergedRegexes[i] = regexes[i];\n                }\n            }\n            return mergedRegexes;\n        },\n        enumerize = function (arr) {\n            var enums = {};\n            for (var i=0; i<arr.length; i++) {\n                enums[arr[i].toUpperCase()] = arr[i];\n            }\n            return enums;\n        },\n        has = function (str1, str2) {\n            return typeof str1 === STR_TYPE ? lowerize(str2).indexOf(lowerize(str1)) !== -1 : false;\n        },\n        lowerize = function (str) {\n            return str.toLowerCase();\n        },\n        majorize = function (version) {\n            return typeof(version) === STR_TYPE ? version.replace(/[^\\d\\.]/g, EMPTY).split('.')[0] : undefined;\n        },\n        trim = function (str, len) {\n            if (typeof(str) === STR_TYPE) {\n                str = str.replace(/^\\s\\s*/, EMPTY);\n                return typeof(len) === UNDEF_TYPE ? str : str.substring(0, UA_MAX_LENGTH);\n            }\n    };\n\n    ///////////////\n    // Map helper\n    //////////////\n\n    var rgxMapper = function (ua, arrays) {\n\n            var i = 0, j, k, p, q, matches, match;\n\n            // loop through all regexes maps\n            while (i < arrays.length && !matches) {\n\n                var regex = arrays[i],       // even sequence (0,2,4,..)\n                    props = arrays[i + 1];   // odd sequence (1,3,5,..)\n                j = k = 0;\n\n                // try matching uastring with regexes\n                while (j < regex.length && !matches) {\n\n                    if (!regex[j]) { break; }\n                    matches = regex[j++].exec(ua);\n\n                    if (!!matches) {\n                        for (p = 0; p < props.length; p++) {\n                            match = matches[++k];\n                            q = props[p];\n                            // check if given property is actually array\n                            if (typeof q === OBJ_TYPE && q.length > 0) {\n                                if (q.length === 2) {\n                                    if (typeof q[1] == FUNC_TYPE) {\n                                        // assign modified match\n                                        this[q[0]] = q[1].call(this, match);\n                                    } else {\n                                        // assign given value, ignore regex match\n                                        this[q[0]] = q[1];\n                                    }\n                                } else if (q.length === 3) {\n                                    // check whether function or regex\n                                    if (typeof q[1] === FUNC_TYPE && !(q[1].exec && q[1].test)) {\n                                        // call function (usually string mapper)\n                                        this[q[0]] = match ? q[1].call(this, match, q[2]) : undefined;\n                                    } else {\n                                        // sanitize match using given regex\n                                        this[q[0]] = match ? match.replace(q[1], q[2]) : undefined;\n                                    }\n                                } else if (q.length === 4) {\n                                        this[q[0]] = match ? q[3].call(this, match.replace(q[1], q[2])) : undefined;\n                                }\n                            } else {\n                                this[q] = match ? match : undefined;\n                            }\n                        }\n                    }\n                }\n                i += 2;\n            }\n        },\n\n        strMapper = function (str, map) {\n\n            for (var i in map) {\n                // check if current value is array\n                if (typeof map[i] === OBJ_TYPE && map[i].length > 0) {\n                    for (var j = 0; j < map[i].length; j++) {\n                        if (has(map[i][j], str)) {\n                            return (i === UNKNOWN) ? undefined : i;\n                        }\n                    }\n                } else if (has(map[i], str)) {\n                    return (i === UNKNOWN) ? undefined : i;\n                }\n            }\n            return str;\n    };\n\n    ///////////////\n    // String map\n    //////////////\n\n    // Safari < 3.0\n    var oldSafariMap = {\n            '1.0'   : '/8',\n            '1.2'   : '/1',\n            '1.3'   : '/3',\n            '2.0'   : '/412',\n            '2.0.2' : '/416',\n            '2.0.3' : '/417',\n            '2.0.4' : '/419',\n            '?'     : '/'\n        },\n        windowsVersionMap = {\n            'ME'        : '4.90',\n            'NT 3.11'   : 'NT3.51',\n            'NT 4.0'    : 'NT4.0',\n            '2000'      : 'NT 5.0',\n            'XP'        : ['NT 5.1', 'NT 5.2'],\n            'Vista'     : 'NT 6.0',\n            '7'         : 'NT 6.1',\n            '8'         : 'NT 6.2',\n            '8.1'       : 'NT 6.3',\n            '10'        : ['NT 6.4', 'NT 10.0'],\n            'RT'        : 'ARM'\n    };\n\n    //////////////\n    // Regex map\n    /////////////\n\n    var regexes = {\n\n        browser : [[\n\n            /\\b(?:crmo|crios)\\/([\\w\\.]+)/i                                      // Chrome for Android/iOS\n            ], [VERSION, [NAME, 'Chrome']], [\n            /edg(?:e|ios|a)?\\/([\\w\\.]+)/i                                       // Microsoft Edge\n            ], [VERSION, [NAME, 'Edge']], [\n\n            // Presto based\n            /(opera mini)\\/([-\\w\\.]+)/i,                                        // Opera Mini\n            /(opera [mobiletab]{3,6})\\b.+version\\/([-\\w\\.]+)/i,                 // Opera Mobi/Tablet\n            /(opera)(?:.+version\\/|[\\/ ]+)([\\w\\.]+)/i                           // Opera\n            ], [NAME, VERSION], [\n            /opios[\\/ ]+([\\w\\.]+)/i                                             // Opera mini on iphone >= 8.0\n            ], [VERSION, [NAME, OPERA+' Mini']], [\n            /\\bop(?:rg)?x\\/([\\w\\.]+)/i                                          // Opera GX\n            ], [VERSION, [NAME, OPERA+' GX']], [\n            /\\bopr\\/([\\w\\.]+)/i                                                 // Opera Webkit\n            ], [VERSION, [NAME, OPERA]], [\n\n            // Mixed\n            /\\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\\/ ]?([\\w\\.]+)/i            // Baidu\n            ], [VERSION, [NAME, 'Baidu']], [\n            /(kindle)\\/([\\w\\.]+)/i,                                             // Kindle\n            /(lunascape|maxthon|netfront|jasmine|blazer)[\\/ ]?([\\w\\.]*)/i,      // Lunascape/Maxthon/Netfront/Jasmine/Blazer\n            // Trident based\n            /(avant|iemobile|slim)\\s?(?:browser)?[\\/ ]?([\\w\\.]*)/i,             // Avant/IEMobile/SlimBrowser\n            /(?:ms|\\()(ie) ([\\w\\.]+)/i,                                         // Internet Explorer\n\n            // Webkit/KHTML based                                               // Flock/RockMelt/Midori/Epiphany/Silk/Skyfire/Bolt/Iron/Iridium/PhantomJS/Bowser/QupZilla/Falkon\n            /(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\\/([-\\w\\.]+)/i,\n                                                                                // Rekonq/Puffin/Brave/Whale/QQBrowserLite/QQ, aka ShouQ\n            /(heytap|ovi)browser\\/([\\d\\.]+)/i,                                  // Heytap/Ovi\n            /(weibo)__([\\d\\.]+)/i                                               // Weibo\n            ], [NAME, VERSION], [\n            /\\bddg\\/([\\w\\.]+)/i                                                 // DuckDuckGo\n            ], [VERSION, [NAME, 'DuckDuckGo']], [\n            /(?:\\buc? ?browser|(?:juc.+)ucweb)[\\/ ]?([\\w\\.]+)/i                 // UCBrowser\n            ], [VERSION, [NAME, 'UC'+BROWSER]], [\n            /microm.+\\bqbcore\\/([\\w\\.]+)/i,                                     // WeChat Desktop for Windows Built-in Browser\n            /\\bqbcore\\/([\\w\\.]+).+microm/i,\n            /micromessenger\\/([\\w\\.]+)/i                                        // WeChat\n            ], [VERSION, [NAME, 'WeChat']], [\n            /konqueror\\/([\\w\\.]+)/i                                             // Konqueror\n            ], [VERSION, [NAME, 'Konqueror']], [\n            /trident.+rv[: ]([\\w\\.]{1,9})\\b.+like gecko/i                       // IE11\n            ], [VERSION, [NAME, 'IE']], [\n            /ya(?:search)?browser\\/([\\w\\.]+)/i                                  // Yandex\n            ], [VERSION, [NAME, 'Yandex']], [\n            /slbrowser\\/([\\w\\.]+)/i                                             // Smart Lenovo Browser\n            ], [VERSION, [NAME, 'Smart Lenovo '+BROWSER]], [\n            /(avast|avg)\\/([\\w\\.]+)/i                                           // Avast/AVG Secure Browser\n            ], [[NAME, /(.+)/, '$1 Secure '+BROWSER], VERSION], [\n            /\\bfocus\\/([\\w\\.]+)/i                                               // Firefox Focus\n            ], [VERSION, [NAME, FIREFOX+' Focus']], [\n            /\\bopt\\/([\\w\\.]+)/i                                                 // Opera Touch\n            ], [VERSION, [NAME, OPERA+' Touch']], [\n            /coc_coc\\w+\\/([\\w\\.]+)/i                                            // Coc Coc Browser\n            ], [VERSION, [NAME, 'Coc Coc']], [\n            /dolfin\\/([\\w\\.]+)/i                                                // Dolphin\n            ], [VERSION, [NAME, 'Dolphin']], [\n            /coast\\/([\\w\\.]+)/i                                                 // Opera Coast\n            ], [VERSION, [NAME, OPERA+' Coast']], [\n            /miuibrowser\\/([\\w\\.]+)/i                                           // MIUI Browser\n            ], [VERSION, [NAME, 'MIUI '+BROWSER]], [\n            /fxios\\/([-\\w\\.]+)/i                                                // Firefox for iOS\n            ], [VERSION, [NAME, FIREFOX]], [\n            /\\bqihu|(qi?ho?o?|360)browser/i                                     // 360\n            ], [[NAME, '360 ' + BROWSER]], [\n            /(oculus|sailfish|huawei|vivo)browser\\/([\\w\\.]+)/i\n            ], [[NAME, /(.+)/, '$1 ' + BROWSER], VERSION], [                    // Oculus/Sailfish/HuaweiBrowser/VivoBrowser\n            /samsungbrowser\\/([\\w\\.]+)/i                                        // Samsung Internet\n            ], [VERSION, [NAME, SAMSUNG + ' Internet']], [\n            /(comodo_dragon)\\/([\\w\\.]+)/i                                       // Comodo Dragon\n            ], [[NAME, /_/g, ' '], VERSION], [\n            /metasr[\\/ ]?([\\d\\.]+)/i                                            // Sogou Explorer\n            ], [VERSION, [NAME, 'Sogou Explorer']], [\n            /(sogou)mo\\w+\\/([\\d\\.]+)/i                                          // Sogou Mobile\n            ], [[NAME, 'Sogou Mobile'], VERSION], [\n            /(electron)\\/([\\w\\.]+) safari/i,                                    // Electron-based App\n            /(tesla)(?: qtcarbrowser|\\/(20\\d\\d\\.[-\\w\\.]+))/i,                   // Tesla\n            /m?(qqbrowser|2345Explorer)[\\/ ]?([\\w\\.]+)/i                        // QQBrowser/2345 Browser\n            ], [NAME, VERSION], [\n            /(lbbrowser)/i,                                                     // LieBao Browser\n            /\\[(linkedin)app\\]/i                                                // LinkedIn App for iOS & Android\n            ], [NAME], [\n\n            // WebView\n            /((?:fban\\/fbios|fb_iab\\/fb4a)(?!.+fbav)|;fbav\\/([\\w\\.]+);)/i       // Facebook App for iOS & Android\n            ], [[NAME, FACEBOOK], VERSION], [\n            /(Klarna)\\/([\\w\\.]+)/i,                                             // Klarna Shopping Browser for iOS & Android\n            /(kakao(?:talk|story))[\\/ ]([\\w\\.]+)/i,                             // Kakao App\n            /(naver)\\(.*?(\\d+\\.[\\w\\.]+).*\\)/i,                                  // Naver InApp\n            /safari (line)\\/([\\w\\.]+)/i,                                        // Line App for iOS\n            /\\b(line)\\/([\\w\\.]+)\\/iab/i,                                        // Line App for Android\n            /(alipay)client\\/([\\w\\.]+)/i,                                       // Alipay\n            /(twitter)(?:and| f.+e\\/([\\w\\.]+))/i,                               // Twitter\n            /(chromium|instagram|snapchat)[\\/ ]([-\\w\\.]+)/i                     // Chromium/Instagram/Snapchat\n            ], [NAME, VERSION], [\n            /\\bgsa\\/([\\w\\.]+) .*safari\\//i                                      // Google Search Appliance on iOS\n            ], [VERSION, [NAME, 'GSA']], [\n            /musical_ly(?:.+app_?version\\/|_)([\\w\\.]+)/i                        // TikTok\n            ], [VERSION, [NAME, 'TikTok']], [\n\n            /headlesschrome(?:\\/([\\w\\.]+)| )/i                                  // Chrome Headless\n            ], [VERSION, [NAME, CHROME+' Headless']], [\n\n            / wv\\).+(chrome)\\/([\\w\\.]+)/i                                       // Chrome WebView\n            ], [[NAME, CHROME+' WebView'], VERSION], [\n\n            /droid.+ version\\/([\\w\\.]+)\\b.+(?:mobile safari|safari)/i           // Android Browser\n            ], [VERSION, [NAME, 'Android '+BROWSER]], [\n\n            /(chrome|omniweb|arora|[tizenoka]{5} ?browser)\\/v?([\\w\\.]+)/i       // Chrome/OmniWeb/Arora/Tizen/Nokia\n            ], [NAME, VERSION], [\n\n            /version\\/([\\w\\.\\,]+) .*mobile\\/\\w+ (safari)/i                      // Mobile Safari\n            ], [VERSION, [NAME, 'Mobile Safari']], [\n            /version\\/([\\w(\\.|\\,)]+) .*(mobile ?safari|safari)/i                // Safari & Safari Mobile\n            ], [VERSION, NAME], [\n            /webkit.+?(mobile ?safari|safari)(\\/[\\w\\.]+)/i                      // Safari < 3.0\n            ], [NAME, [VERSION, strMapper, oldSafariMap]], [\n\n            /(webkit|khtml)\\/([\\w\\.]+)/i\n            ], [NAME, VERSION], [\n\n            // Gecko based\n            /(navigator|netscape\\d?)\\/([-\\w\\.]+)/i                              // Netscape\n            ], [[NAME, 'Netscape'], VERSION], [\n            /mobile vr; rv:([\\w\\.]+)\\).+firefox/i                               // Firefox Reality\n            ], [VERSION, [NAME, FIREFOX+' Reality']], [\n            /ekiohf.+(flow)\\/([\\w\\.]+)/i,                                       // Flow\n            /(swiftfox)/i,                                                      // Swiftfox\n            /(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\\/ ]?([\\w\\.\\+]+)/i,\n                                                                                // IceDragon/Iceweasel/Camino/Chimera/Fennec/Maemo/Minimo/Conkeror/Klar\n            /(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\\/([-\\w\\.]+)$/i,\n                                                                                // Firefox/SeaMonkey/K-Meleon/IceCat/IceApe/Firebird/Phoenix\n            /(firefox)\\/([\\w\\.]+)/i,                                            // Other Firefox-based\n            /(mozilla)\\/([\\w\\.]+) .+rv\\:.+gecko\\/\\d+/i,                         // Mozilla\n\n            // Other\n            /(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\\. ]?browser)[-\\/ ]?v?([\\w\\.]+)/i,\n                                                                                // Polaris/Lynx/Dillo/iCab/Doris/Amaya/w3m/NetSurf/Sleipnir/Obigo/Mosaic/Go/ICE/UP.Browser\n            /(links) \\(([\\w\\.]+)/i,                                             // Links\n            /panasonic;(viera)/i                                                // Panasonic Viera\n            ], [NAME, VERSION], [\n            \n            /(cobalt)\\/([\\w\\.]+)/i                                              // Cobalt\n            ], [NAME, [VERSION, /master.|lts./, \"\"]]\n        ],\n\n        cpu : [[\n\n            /(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\\)]/i                     // AMD64 (x64)\n            ], [[ARCHITECTURE, 'amd64']], [\n\n            /(ia32(?=;))/i                                                      // IA32 (quicktime)\n            ], [[ARCHITECTURE, lowerize]], [\n\n            /((?:i[346]|x)86)[;\\)]/i                                            // IA32 (x86)\n            ], [[ARCHITECTURE, 'ia32']], [\n\n            /\\b(aarch64|arm(v?8e?l?|_?64))\\b/i                                 // ARM64\n            ], [[ARCHITECTURE, 'arm64']], [\n\n            /\\b(arm(?:v[67])?ht?n?[fl]p?)\\b/i                                   // ARMHF\n            ], [[ARCHITECTURE, 'armhf']], [\n\n            // PocketPC mistakenly identified as PowerPC\n            /windows (ce|mobile); ppc;/i\n            ], [[ARCHITECTURE, 'arm']], [\n\n            /((?:ppc|powerpc)(?:64)?)(?: mac|;|\\))/i                            // PowerPC\n            ], [[ARCHITECTURE, /ower/, EMPTY, lowerize]], [\n\n            /(sun4\\w)[;\\)]/i                                                    // SPARC\n            ], [[ARCHITECTURE, 'sparc']], [\n\n            /((?:avr32|ia64(?=;))|68k(?=\\))|\\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\\b|pa-risc)/i\n                                                                                // IA64, 68K, ARM/64, AVR/32, IRIX/64, MIPS/64, SPARC/64, PA-RISC\n            ], [[ARCHITECTURE, lowerize]]\n        ],\n\n        device : [[\n\n            //////////////////////////\n            // MOBILES & TABLETS\n            /////////////////////////\n\n            // Samsung\n            /\\b(sch-i[89]0\\d|shw-m380s|sm-[ptx]\\w{2,4}|gt-[pn]\\d{2,4}|sgh-t8[56]9|nexus 10)/i\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, TABLET]], [\n            /\\b((?:s[cgp]h|gt|sm)-\\w+|sc[g-]?[\\d]+a?|galaxy nexus)/i,\n            /samsung[- ]([-\\w]+)/i,\n            /sec-(sgh\\w+)/i\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, MOBILE]], [\n\n            // Apple\n            /(?:\\/|\\()(ip(?:hone|od)[\\w, ]*)(?:\\/|;)/i                          // iPod/iPhone\n            ], [MODEL, [VENDOR, APPLE], [TYPE, MOBILE]], [\n            /\\((ipad);[-\\w\\),; ]+apple/i,                                       // iPad\n            /applecoremedia\\/[\\w\\.]+ \\((ipad)/i,\n            /\\b(ipad)\\d\\d?,\\d\\d?[;\\]].+ios/i\n            ], [MODEL, [VENDOR, APPLE], [TYPE, TABLET]], [\n            /(macintosh);/i\n            ], [MODEL, [VENDOR, APPLE]], [\n\n            // Sharp\n            /\\b(sh-?[altvz]?\\d\\d[a-ekm]?)/i\n            ], [MODEL, [VENDOR, SHARP], [TYPE, MOBILE]], [\n\n            // Huawei\n            /\\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\\d{2})\\b(?!.+d\\/s)/i\n            ], [MODEL, [VENDOR, HUAWEI], [TYPE, TABLET]], [\n            /(?:huawei|honor)([-\\w ]+)[;\\)]/i,\n            /\\b(nexus 6p|\\w{2,4}e?-[atu]?[ln][\\dx][012359c][adn]?)\\b(?!.+d\\/s)/i\n            ], [MODEL, [VENDOR, HUAWEI], [TYPE, MOBILE]], [\n\n            // Xiaomi\n            /\\b(poco[\\w ]+|m2\\d{3}j\\d\\d[a-z]{2})(?: bui|\\))/i,                  // Xiaomi POCO\n            /\\b; (\\w+) build\\/hm\\1/i,                                           // Xiaomi Hongmi 'numeric' models\n            /\\b(hm[-_ ]?note?[_ ]?(?:\\d\\w)?) bui/i,                             // Xiaomi Hongmi\n            /\\b(redmi[\\-_ ]?(?:note|k)?[\\w_ ]+)(?: bui|\\))/i,                   // Xiaomi Redmi\n            /oid[^\\)]+; (m?[12][0-389][01]\\w{3,6}[c-y])( bui|; wv|\\))/i,        // Xiaomi Redmi 'numeric' models\n            /\\b(mi[-_ ]?(?:a\\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\\d?\\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\\))/i // Xiaomi Mi\n            ], [[MODEL, /_/g, ' '], [VENDOR, XIAOMI], [TYPE, MOBILE]], [\n            /oid[^\\)]+; (2\\d{4}(283|rpbf)[cgl])( bui|\\))/i,                     // Redmi Pad\n            /\\b(mi[-_ ]?(?:pad)(?:[\\w_ ]+))(?: bui|\\))/i                        // Mi Pad tablets\n            ],[[MODEL, /_/g, ' '], [VENDOR, XIAOMI], [TYPE, TABLET]], [\n\n            // OPPO\n            /; (\\w+) bui.+ oppo/i,\n            /\\b(cph[12]\\d{3}|p(?:af|c[al]|d\\w|e[ar])[mt]\\d0|x9007|a101op)\\b/i\n            ], [MODEL, [VENDOR, 'OPPO'], [TYPE, MOBILE]], [\n            /\\b(opd2\\d{3}a?) bui/i\n            ], [MODEL, [VENDOR, 'OPPO'], [TYPE, TABLET]], [\n\n            // Vivo\n            /vivo (\\w+)(?: bui|\\))/i,\n            /\\b(v[12]\\d{3}\\w?[at])(?: bui|;)/i\n            ], [MODEL, [VENDOR, 'Vivo'], [TYPE, MOBILE]], [\n\n            // Realme\n            /\\b(rmx[1-3]\\d{3})(?: bui|;|\\))/i\n            ], [MODEL, [VENDOR, 'Realme'], [TYPE, MOBILE]], [\n\n            // Motorola\n            /\\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\\b[\\w ]+build\\//i,\n            /\\bmot(?:orola)?[- ](\\w*)/i,\n            /((?:moto[\\w\\(\\) ]+|xt\\d{3,4}|nexus 6)(?= bui|\\)))/i\n            ], [MODEL, [VENDOR, MOTOROLA], [TYPE, MOBILE]], [\n            /\\b(mz60\\d|xoom[2 ]{0,2}) build\\//i\n            ], [MODEL, [VENDOR, MOTOROLA], [TYPE, TABLET]], [\n\n            // LG\n            /((?=lg)?[vl]k\\-?\\d{3}) bui| 3\\.[-\\w; ]{10}lg?-([06cv9]{3,4})/i\n            ], [MODEL, [VENDOR, LG], [TYPE, TABLET]], [\n            /(lm(?:-?f100[nv]?|-[\\w\\.]+)(?= bui|\\))|nexus [45])/i,\n            /\\blg[-e;\\/ ]+((?!browser|netcast|android tv)\\w+)/i,\n            /\\blg-?([\\d\\w]+) bui/i\n            ], [MODEL, [VENDOR, LG], [TYPE, MOBILE]], [\n\n            // Lenovo\n            /(ideatab[-\\w ]+)/i,\n            /lenovo ?(s[56]000[-\\w]+|tab(?:[\\w ]+)|yt[-\\d\\w]{6}|tb[-\\d\\w]{6})/i\n            ], [MODEL, [VENDOR, 'Lenovo'], [TYPE, TABLET]], [\n\n            // Nokia\n            /(?:maemo|nokia).*(n900|lumia \\d+)/i,\n            /nokia[-_ ]?([-\\w\\.]*)/i\n            ], [[MODEL, /_/g, ' '], [VENDOR, 'Nokia'], [TYPE, MOBILE]], [\n\n            // Google\n            /(pixel c)\\b/i                                                      // Google Pixel C\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, TABLET]], [\n            /droid.+; (pixel[\\daxl ]{0,6})(?: bui|\\))/i                         // Google Pixel\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, MOBILE]], [\n\n            // Sony\n            /droid.+ (a?\\d[0-2]{2}so|[c-g]\\d{4}|so[-gl]\\w+|xq-a\\w[4-7][12])(?= bui|\\).+chrome\\/(?![1-6]{0,1}\\d\\.))/i\n            ], [MODEL, [VENDOR, SONY], [TYPE, MOBILE]], [\n            /sony tablet [ps]/i,\n            /\\b(?:sony)?sgp\\w+(?: bui|\\))/i\n            ], [[MODEL, 'Xperia Tablet'], [VENDOR, SONY], [TYPE, TABLET]], [\n\n            // OnePlus\n            / (kb2005|in20[12]5|be20[12][59])\\b/i,\n            /(?:one)?(?:plus)? (a\\d0\\d\\d)(?: b|\\))/i\n            ], [MODEL, [VENDOR, 'OnePlus'], [TYPE, MOBILE]], [\n\n            // Amazon\n            /(alexa)webm/i,\n            /(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\\))/i,                             // Kindle Fire without Silk / Echo Show\n            /(kf[a-z]+)( bui|\\)).+silk\\//i                                      // Kindle Fire HD\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, TABLET]], [\n            /((?:sd|kf)[0349hijorstuw]+)( bui|\\)).+silk\\//i                     // Fire Phone\n            ], [[MODEL, /(.+)/g, 'Fire Phone $1'], [VENDOR, AMAZON], [TYPE, MOBILE]], [\n\n            // BlackBerry\n            /(playbook);[-\\w\\),; ]+(rim)/i                                      // BlackBerry PlayBook\n            ], [MODEL, VENDOR, [TYPE, TABLET]], [\n            /\\b((?:bb[a-f]|st[hv])100-\\d)/i,\n            /\\(bb10; (\\w+)/i                                                    // BlackBerry 10\n            ], [MODEL, [VENDOR, BLACKBERRY], [TYPE, MOBILE]], [\n\n            // Asus\n            /(?:\\b|asus_)(transfo[prime ]{4,10} \\w+|eeepc|slider \\w+|nexus 7|padfone|p00[cj])/i\n            ], [MODEL, [VENDOR, ASUS], [TYPE, TABLET]], [\n            / (z[bes]6[027][012][km][ls]|zenfone \\d\\w?)\\b/i\n            ], [MODEL, [VENDOR, ASUS], [TYPE, MOBILE]], [\n\n            // HTC\n            /(nexus 9)/i                                                        // HTC Nexus 9\n            ], [MODEL, [VENDOR, 'HTC'], [TYPE, TABLET]], [\n            /(htc)[-;_ ]{1,2}([\\w ]+(?=\\)| bui)|\\w+)/i,                         // HTC\n\n            // ZTE\n            /(zte)[- ]([\\w ]+?)(?: bui|\\/|\\))/i,\n            /(alcatel|geeksphone|nexian|panasonic(?!(?:;|\\.))|sony(?!-bra))[-_ ]?([-\\w]*)/i         // Alcatel/GeeksPhone/Nexian/Panasonic/Sony\n            ], [VENDOR, [MODEL, /_/g, ' '], [TYPE, MOBILE]], [\n\n            // Acer\n            /droid.+; ([ab][1-7]-?[0178a]\\d\\d?)/i\n            ], [MODEL, [VENDOR, 'Acer'], [TYPE, TABLET]], [\n\n            // Meizu\n            /droid.+; (m[1-5] note) bui/i,\n            /\\bmz-([-\\w]{2,})/i\n            ], [MODEL, [VENDOR, 'Meizu'], [TYPE, MOBILE]], [\n                \n            // Ulefone\n            /; ((?:power )?armor(?:[\\w ]{0,8}))(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Ulefone'], [TYPE, MOBILE]], [\n\n            // MIXED\n            /(blackberry|benq|palm(?=\\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno)[-_ ]?([-\\w]*)/i,\n                                                                                // BlackBerry/BenQ/Palm/Sony-Ericsson/Acer/Asus/Dell/Meizu/Motorola/Polytron\n            /(hp) ([\\w ]+\\w)/i,                                                 // HP iPAQ\n            /(asus)-?(\\w+)/i,                                                   // Asus\n            /(microsoft); (lumia[\\w ]+)/i,                                      // Microsoft Lumia\n            /(lenovo)[-_ ]?([-\\w]+)/i,                                          // Lenovo\n            /(jolla)/i,                                                         // Jolla\n            /(oppo) ?([\\w ]+) bui/i                                             // OPPO\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n\n            /(kobo)\\s(ereader|touch)/i,                                         // Kobo\n            /(archos) (gamepad2?)/i,                                            // Archos\n            /(hp).+(touchpad(?!.+tablet)|tablet)/i,                             // HP TouchPad\n            /(kindle)\\/([\\w\\.]+)/i,                                             // Kindle\n            /(nook)[\\w ]+build\\/(\\w+)/i,                                        // Nook\n            /(dell) (strea[kpr\\d ]*[\\dko])/i,                                   // Dell Streak\n            /(le[- ]+pan)[- ]+(\\w{1,9}) bui/i,                                  // Le Pan Tablets\n            /(trinity)[- ]*(t\\d{3}) bui/i,                                      // Trinity Tablets\n            /(gigaset)[- ]+(q\\w{1,9}) bui/i,                                    // Gigaset Tablets\n            /(vodafone) ([\\w ]+)(?:\\)| bui)/i                                   // Vodafone\n            ], [VENDOR, MODEL, [TYPE, TABLET]], [\n\n            /(surface duo)/i                                                    // Surface Duo\n            ], [MODEL, [VENDOR, MICROSOFT], [TYPE, TABLET]], [\n            /droid [\\d\\.]+; (fp\\du?)(?: b|\\))/i                                 // Fairphone\n            ], [MODEL, [VENDOR, 'Fairphone'], [TYPE, MOBILE]], [\n            /(u304aa)/i                                                         // AT&T\n            ], [MODEL, [VENDOR, 'AT&T'], [TYPE, MOBILE]], [\n            /\\bsie-(\\w*)/i                                                      // Siemens\n            ], [MODEL, [VENDOR, 'Siemens'], [TYPE, MOBILE]], [\n            /\\b(rct\\w+) b/i                                                     // RCA Tablets\n            ], [MODEL, [VENDOR, 'RCA'], [TYPE, TABLET]], [\n            /\\b(venue[\\d ]{2,7}) b/i                                            // Dell Venue Tablets\n            ], [MODEL, [VENDOR, 'Dell'], [TYPE, TABLET]], [\n            /\\b(q(?:mv|ta)\\w+) b/i                                              // Verizon Tablet\n            ], [MODEL, [VENDOR, 'Verizon'], [TYPE, TABLET]], [\n            /\\b(?:barnes[& ]+noble |bn[rt])([\\w\\+ ]*) b/i                       // Barnes & Noble Tablet\n            ], [MODEL, [VENDOR, 'Barnes & Noble'], [TYPE, TABLET]], [\n            /\\b(tm\\d{3}\\w+) b/i\n            ], [MODEL, [VENDOR, 'NuVision'], [TYPE, TABLET]], [\n            /\\b(k88) b/i                                                        // ZTE K Series Tablet\n            ], [MODEL, [VENDOR, 'ZTE'], [TYPE, TABLET]], [\n            /\\b(nx\\d{3}j) b/i                                                   // ZTE Nubia\n            ], [MODEL, [VENDOR, 'ZTE'], [TYPE, MOBILE]], [\n            /\\b(gen\\d{3}) b.+49h/i                                              // Swiss GEN Mobile\n            ], [MODEL, [VENDOR, 'Swiss'], [TYPE, MOBILE]], [\n            /\\b(zur\\d{3}) b/i                                                   // Swiss ZUR Tablet\n            ], [MODEL, [VENDOR, 'Swiss'], [TYPE, TABLET]], [\n            /\\b((zeki)?tb.*\\b) b/i                                              // Zeki Tablets\n            ], [MODEL, [VENDOR, 'Zeki'], [TYPE, TABLET]], [\n            /\\b([yr]\\d{2}) b/i,\n            /\\b(dragon[- ]+touch |dt)(\\w{5}) b/i                                // Dragon Touch Tablet\n            ], [[VENDOR, 'Dragon Touch'], MODEL, [TYPE, TABLET]], [\n            /\\b(ns-?\\w{0,9}) b/i                                                // Insignia Tablets\n            ], [MODEL, [VENDOR, 'Insignia'], [TYPE, TABLET]], [\n            /\\b((nxa|next)-?\\w{0,9}) b/i                                        // NextBook Tablets\n            ], [MODEL, [VENDOR, 'NextBook'], [TYPE, TABLET]], [\n            /\\b(xtreme\\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i                  // Voice Xtreme Phones\n            ], [[VENDOR, 'Voice'], MODEL, [TYPE, MOBILE]], [\n            /\\b(lvtel\\-)?(v1[12]) b/i                                           // LvTel Phones\n            ], [[VENDOR, 'LvTel'], MODEL, [TYPE, MOBILE]], [\n            /\\b(ph-1) /i                                                        // Essential PH-1\n            ], [MODEL, [VENDOR, 'Essential'], [TYPE, MOBILE]], [\n            /\\b(v(100md|700na|7011|917g).*\\b) b/i                               // Envizen Tablets\n            ], [MODEL, [VENDOR, 'Envizen'], [TYPE, TABLET]], [\n            /\\b(trio[-\\w\\. ]+) b/i                                              // MachSpeed Tablets\n            ], [MODEL, [VENDOR, 'MachSpeed'], [TYPE, TABLET]], [\n            /\\btu_(1491) b/i                                                    // Rotor Tablets\n            ], [MODEL, [VENDOR, 'Rotor'], [TYPE, TABLET]], [\n            /(shield[\\w ]+) b/i                                                 // Nvidia Shield Tablets\n            ], [MODEL, [VENDOR, 'Nvidia'], [TYPE, TABLET]], [\n            /(sprint) (\\w+)/i                                                   // Sprint Phones\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n            /(kin\\.[onetw]{3})/i                                                // Microsoft Kin\n            ], [[MODEL, /\\./g, ' '], [VENDOR, MICROSOFT], [TYPE, MOBILE]], [\n            /droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\\)/i             // Zebra\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, TABLET]], [\n            /droid.+; (ec30|ps20|tc[2-8]\\d[kx])\\)/i\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, MOBILE]], [\n\n            ///////////////////\n            // SMARTTVS\n            ///////////////////\n\n            /smart-tv.+(samsung)/i                                              // Samsung\n            ], [VENDOR, [TYPE, SMARTTV]], [\n            /hbbtv.+maple;(\\d+)/i\n            ], [[MODEL, /^/, 'SmartTV'], [VENDOR, SAMSUNG], [TYPE, SMARTTV]], [\n            /(nux; netcast.+smarttv|lg (netcast\\.tv-201\\d|android tv))/i        // LG SmartTV\n            ], [[VENDOR, LG], [TYPE, SMARTTV]], [\n            /(apple) ?tv/i                                                      // Apple TV\n            ], [VENDOR, [MODEL, APPLE+' TV'], [TYPE, SMARTTV]], [\n            /crkey/i                                                            // Google Chromecast\n            ], [[MODEL, CHROME+'cast'], [VENDOR, GOOGLE], [TYPE, SMARTTV]], [\n            /droid.+aft(\\w+)( bui|\\))/i                                         // Fire TV\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, SMARTTV]], [\n            /\\(dtv[\\);].+(aquos)/i,\n            /(aquos-tv[\\w ]+)\\)/i                                               // Sharp\n            ], [MODEL, [VENDOR, SHARP], [TYPE, SMARTTV]],[\n            /(bravia[\\w ]+)( bui|\\))/i                                              // Sony\n            ], [MODEL, [VENDOR, SONY], [TYPE, SMARTTV]], [\n            /(mitv-\\w{5}) bui/i                                                 // Xiaomi\n            ], [MODEL, [VENDOR, XIAOMI], [TYPE, SMARTTV]], [\n            /Hbbtv.*(technisat) (.*);/i                                         // TechniSAT\n            ], [VENDOR, MODEL, [TYPE, SMARTTV]], [\n            /\\b(roku)[\\dx]*[\\)\\/]((?:dvp-)?[\\d\\.]*)/i,                          // Roku\n            /hbbtv\\/\\d+\\.\\d+\\.\\d+ +\\([\\w\\+ ]*; *([\\w\\d][^;]*);([^;]*)/i         // HbbTV devices\n            ], [[VENDOR, trim], [MODEL, trim], [TYPE, SMARTTV]], [\n            /\\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\\b/i                   // SmartTV from Unidentified Vendors\n            ], [[TYPE, SMARTTV]], [\n\n            ///////////////////\n            // CONSOLES\n            ///////////////////\n\n            /(ouya)/i,                                                          // Ouya\n            /(nintendo) ([wids3utch]+)/i                                        // Nintendo\n            ], [VENDOR, MODEL, [TYPE, CONSOLE]], [\n            /droid.+; (shield) bui/i                                            // Nvidia\n            ], [MODEL, [VENDOR, 'Nvidia'], [TYPE, CONSOLE]], [\n            /(playstation [345portablevi]+)/i                                   // Playstation\n            ], [MODEL, [VENDOR, SONY], [TYPE, CONSOLE]], [\n            /\\b(xbox(?: one)?(?!; xbox))[\\); ]/i                                // Microsoft Xbox\n            ], [MODEL, [VENDOR, MICROSOFT], [TYPE, CONSOLE]], [\n\n            ///////////////////\n            // WEARABLES\n            ///////////////////\n\n            /((pebble))app/i                                                    // Pebble\n            ], [VENDOR, MODEL, [TYPE, WEARABLE]], [\n            /(watch)(?: ?os[,\\/]|\\d,\\d\\/)[\\d\\.]+/i                              // Apple Watch\n            ], [MODEL, [VENDOR, APPLE], [TYPE, WEARABLE]], [\n            /droid.+; (glass) \\d/i                                              // Google Glass\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, WEARABLE]], [\n            /droid.+; (wt63?0{2,3})\\)/i\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, WEARABLE]], [\n            /(quest( \\d| pro)?)/i                                               // Oculus Quest\n            ], [MODEL, [VENDOR, FACEBOOK], [TYPE, WEARABLE]], [\n\n            ///////////////////\n            // EMBEDDED\n            ///////////////////\n\n            /(tesla)(?: qtcarbrowser|\\/[-\\w\\.]+)/i                              // Tesla\n            ], [VENDOR, [TYPE, EMBEDDED]], [\n            /(aeobc)\\b/i                                                        // Echo Dot\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, EMBEDDED]], [\n\n            ////////////////////\n            // MIXED (GENERIC)\n            ///////////////////\n\n            /droid .+?; ([^;]+?)(?: bui|; wv\\)|\\) applew).+? mobile safari/i    // Android Phones from Unidentified Vendors\n            ], [MODEL, [TYPE, MOBILE]], [\n            /droid .+?; ([^;]+?)(?: bui|\\) applew).+?(?! mobile) safari/i       // Android Tablets from Unidentified Vendors\n            ], [MODEL, [TYPE, TABLET]], [\n            /\\b((tablet|tab)[;\\/]|focus\\/\\d(?!.+mobile))/i                      // Unidentifiable Tablet\n            ], [[TYPE, TABLET]], [\n            /(phone|mobile(?:[;\\/]| [ \\w\\/\\.]*safari)|pda(?=.+windows ce))/i    // Unidentifiable Mobile\n            ], [[TYPE, MOBILE]], [\n            /(android[-\\w\\. ]{0,9});.+buil/i                                    // Generic Android Device\n            ], [MODEL, [VENDOR, 'Generic']]\n        ],\n\n        engine : [[\n\n            /windows.+ edge\\/([\\w\\.]+)/i                                       // EdgeHTML\n            ], [VERSION, [NAME, EDGE+'HTML']], [\n\n            /webkit\\/537\\.36.+chrome\\/(?!27)([\\w\\.]+)/i                         // Blink\n            ], [VERSION, [NAME, 'Blink']], [\n\n            /(presto)\\/([\\w\\.]+)/i,                                             // Presto\n            /(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\\/([\\w\\.]+)/i, // WebKit/Trident/NetFront/NetSurf/Amaya/Lynx/w3m/Goanna\n            /ekioh(flow)\\/([\\w\\.]+)/i,                                          // Flow\n            /(khtml|tasman|links)[\\/ ]\\(?([\\w\\.]+)/i,                           // KHTML/Tasman/Links\n            /(icab)[\\/ ]([23]\\.[\\d\\.]+)/i,                                      // iCab\n            /\\b(libweb)/i\n            ], [NAME, VERSION], [\n\n            /rv\\:([\\w\\.]{1,9})\\b.+(gecko)/i                                     // Gecko\n            ], [VERSION, NAME]\n        ],\n\n        os : [[\n\n            // Windows\n            /microsoft (windows) (vista|xp)/i                                   // Windows (iTunes)\n            ], [NAME, VERSION], [\n            /(windows (?:phone(?: os)?|mobile))[\\/ ]?([\\d\\.\\w ]*)/i             // Windows Phone\n            ], [NAME, [VERSION, strMapper, windowsVersionMap]], [\n            /windows nt 6\\.2; (arm)/i,                                        // Windows RT\n            /windows[\\/ ]?([ntce\\d\\. ]+\\w)(?!.+xbox)/i,\n            /(?:win(?=3|9|n)|win 9x )([nt\\d\\.]+)/i\n            ], [[VERSION, strMapper, windowsVersionMap], [NAME, 'Windows']], [\n\n            // iOS/macOS\n            /ip[honead]{2,4}\\b(?:.*os ([\\w]+) like mac|; opera)/i,              // iOS\n            /(?:ios;fbsv\\/|iphone.+ios[\\/ ])([\\d\\.]+)/i,\n            /cfnetwork\\/.+darwin/i\n            ], [[VERSION, /_/g, '.'], [NAME, 'iOS']], [\n            /(mac os x) ?([\\w\\. ]*)/i,\n            /(macintosh|mac_powerpc\\b)(?!.+haiku)/i                             // Mac OS\n            ], [[NAME, MAC_OS], [VERSION, /_/g, '.']], [\n\n            // Mobile OSes\n            /droid ([\\w\\.]+)\\b.+(android[- ]x86|harmonyos)/i                    // Android-x86/HarmonyOS\n            ], [VERSION, NAME], [                                               // Android/WebOS/QNX/Bada/RIM/Maemo/MeeGo/Sailfish OS\n            /(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\\/ ]?([\\w\\.]*)/i,\n            /(blackberry)\\w*\\/([\\w\\.]*)/i,                                      // Blackberry\n            /(tizen|kaios)[\\/ ]([\\w\\.]+)/i,                                     // Tizen/KaiOS\n            /\\((series40);/i                                                    // Series 40\n            ], [NAME, VERSION], [\n            /\\(bb(10);/i                                                        // BlackBerry 10\n            ], [VERSION, [NAME, BLACKBERRY]], [\n            /(?:symbian ?os|symbos|s60(?=;)|series60)[-\\/ ]?([\\w\\.]*)/i         // Symbian\n            ], [VERSION, [NAME, 'Symbian']], [\n            /mozilla\\/[\\d\\.]+ \\((?:mobile|tablet|tv|mobile; [\\w ]+); rv:.+ gecko\\/([\\w\\.]+)/i // Firefox OS\n            ], [VERSION, [NAME, FIREFOX+' OS']], [\n            /web0s;.+rt(tv)/i,\n            /\\b(?:hp)?wos(?:browser)?\\/([\\w\\.]+)/i                              // WebOS\n            ], [VERSION, [NAME, 'webOS']], [\n            /watch(?: ?os[,\\/]|\\d,\\d\\/)([\\d\\.]+)/i                              // watchOS\n            ], [VERSION, [NAME, 'watchOS']], [\n\n            // Google Chromecast\n            /crkey\\/([\\d\\.]+)/i                                                 // Google Chromecast\n            ], [VERSION, [NAME, CHROME+'cast']], [\n            /(cros) [\\w]+(?:\\)| ([\\w\\.]+)\\b)/i                                  // Chromium OS\n            ], [[NAME, CHROMIUM_OS], VERSION],[\n\n            // Smart TVs\n            /panasonic;(viera)/i,                                               // Panasonic Viera\n            /(netrange)mmh/i,                                                   // Netrange\n            /(nettv)\\/(\\d+\\.[\\w\\.]+)/i,                                         // NetTV\n\n            // Console\n            /(nintendo|playstation) ([wids345portablevuch]+)/i,                 // Nintendo/Playstation\n            /(xbox); +xbox ([^\\);]+)/i,                                         // Microsoft Xbox (360, One, X, S, Series X, Series S)\n\n            // Other\n            /\\b(joli|palm)\\b ?(?:os)?\\/?([\\w\\.]*)/i,                            // Joli/Palm\n            /(mint)[\\/\\(\\) ]?(\\w*)/i,                                           // Mint\n            /(mageia|vectorlinux)[; ]/i,                                        // Mageia/VectorLinux\n            /([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\\/ ]?(?!chrom|package)([-\\w\\.]*)/i,\n                                                                                // Ubuntu/Debian/SUSE/Gentoo/Arch/Slackware/Fedora/Mandriva/CentOS/PCLinuxOS/RedHat/Zenwalk/Linpus/Raspbian/Plan9/Minix/RISCOS/Contiki/Deepin/Manjaro/elementary/Sabayon/Linspire\n            /(hurd|linux) ?([\\w\\.]*)/i,                                         // Hurd/Linux\n            /(gnu) ?([\\w\\.]*)/i,                                                // GNU\n            /\\b([-frentopcghs]{0,5}bsd|dragonfly)[\\/ ]?(?!amd|[ix346]{1,2}86)([\\w\\.]*)/i, // FreeBSD/NetBSD/OpenBSD/PC-BSD/GhostBSD/DragonFly\n            /(haiku) (\\w+)/i                                                    // Haiku\n            ], [NAME, VERSION], [\n            /(sunos) ?([\\w\\.\\d]*)/i                                             // Solaris\n            ], [[NAME, 'Solaris'], VERSION], [\n            /((?:open)?solaris)[-\\/ ]?([\\w\\.]*)/i,                              // Solaris\n            /(aix) ((\\d)(?=\\.|\\)| )[\\w\\.])*/i,                                  // AIX\n            /\\b(beos|os\\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i, // BeOS/OS2/AmigaOS/MorphOS/OpenVMS/Fuchsia/HP-UX/SerenityOS\n            /(unix) ?([\\w\\.]*)/i                                                // UNIX\n            ], [NAME, VERSION]\n        ]\n    };\n\n    /////////////////\n    // Constructor\n    ////////////////\n\n    var UAParser = function (ua, extensions) {\n\n        if (typeof ua === OBJ_TYPE) {\n            extensions = ua;\n            ua = undefined;\n        }\n\n        if (!(this instanceof UAParser)) {\n            return new UAParser(ua, extensions).getResult();\n        }\n\n        var _navigator = (typeof window !== UNDEF_TYPE && window.navigator) ? window.navigator : undefined;\n        var _ua = ua || ((_navigator && _navigator.userAgent) ? _navigator.userAgent : EMPTY);\n        var _uach = (_navigator && _navigator.userAgentData) ? _navigator.userAgentData : undefined;\n        var _rgxmap = extensions ? extend(regexes, extensions) : regexes;\n        var _isSelfNav = _navigator && _navigator.userAgent == _ua;\n\n        this.getBrowser = function () {\n            var _browser = {};\n            _browser[NAME] = undefined;\n            _browser[VERSION] = undefined;\n            rgxMapper.call(_browser, _ua, _rgxmap.browser);\n            _browser[MAJOR] = majorize(_browser[VERSION]);\n            // Brave-specific detection\n            if (_isSelfNav && _navigator && _navigator.brave && typeof _navigator.brave.isBrave == FUNC_TYPE) {\n                _browser[NAME] = 'Brave';\n            }\n            return _browser;\n        };\n        this.getCPU = function () {\n            var _cpu = {};\n            _cpu[ARCHITECTURE] = undefined;\n            rgxMapper.call(_cpu, _ua, _rgxmap.cpu);\n            return _cpu;\n        };\n        this.getDevice = function () {\n            var _device = {};\n            _device[VENDOR] = undefined;\n            _device[MODEL] = undefined;\n            _device[TYPE] = undefined;\n            rgxMapper.call(_device, _ua, _rgxmap.device);\n            if (_isSelfNav && !_device[TYPE] && _uach && _uach.mobile) {\n                _device[TYPE] = MOBILE;\n            }\n            // iPadOS-specific detection: identified as Mac, but has some iOS-only properties\n            if (_isSelfNav && _device[MODEL] == 'Macintosh' && _navigator && typeof _navigator.standalone !== UNDEF_TYPE && _navigator.maxTouchPoints && _navigator.maxTouchPoints > 2) {\n                _device[MODEL] = 'iPad';\n                _device[TYPE] = TABLET;\n            }\n            return _device;\n        };\n        this.getEngine = function () {\n            var _engine = {};\n            _engine[NAME] = undefined;\n            _engine[VERSION] = undefined;\n            rgxMapper.call(_engine, _ua, _rgxmap.engine);\n            return _engine;\n        };\n        this.getOS = function () {\n            var _os = {};\n            _os[NAME] = undefined;\n            _os[VERSION] = undefined;\n            rgxMapper.call(_os, _ua, _rgxmap.os);\n            if (_isSelfNav && !_os[NAME] && _uach && _uach.platform && _uach.platform != 'Unknown') {\n                _os[NAME] = _uach.platform  \n                                    .replace(/chrome os/i, CHROMIUM_OS)\n                                    .replace(/macos/i, MAC_OS);           // backward compatibility\n            }\n            return _os;\n        };\n        this.getResult = function () {\n            return {\n                ua      : this.getUA(),\n                browser : this.getBrowser(),\n                engine  : this.getEngine(),\n                os      : this.getOS(),\n                device  : this.getDevice(),\n                cpu     : this.getCPU()\n            };\n        };\n        this.getUA = function () {\n            return _ua;\n        };\n        this.setUA = function (ua) {\n            _ua = (typeof ua === STR_TYPE && ua.length > UA_MAX_LENGTH) ? trim(ua, UA_MAX_LENGTH) : ua;\n            return this;\n        };\n        this.setUA(_ua);\n        return this;\n    };\n\n    UAParser.VERSION = LIBVERSION;\n    UAParser.BROWSER =  enumerize([NAME, VERSION, MAJOR]);\n    UAParser.CPU = enumerize([ARCHITECTURE]);\n    UAParser.DEVICE = enumerize([MODEL, VENDOR, TYPE, CONSOLE, MOBILE, SMARTTV, TABLET, WEARABLE, EMBEDDED]);\n    UAParser.ENGINE = UAParser.OS = enumerize([NAME, VERSION]);\n\n    ///////////\n    // Export\n    //////////\n\n    // check js environment\n    if (typeof(exports) !== UNDEF_TYPE) {\n        // nodejs env\n        if (typeof module !== UNDEF_TYPE && module.exports) {\n            exports = module.exports = UAParser;\n        }\n        exports.UAParser = UAParser;\n    } else {\n        // requirejs env (optional)\n        if (typeof(define) === FUNC_TYPE && define.amd) {\n            define(function () {\n                return UAParser;\n            });\n        } else if (typeof window !== UNDEF_TYPE) {\n            // browser env\n            window.UAParser = UAParser;\n        }\n    }\n\n    // jQuery/Zepto specific (optional)\n    // Note:\n    //   In AMD env the global scope should be kept clean, but jQuery is an exception.\n    //   jQuery always exports to global scope, unless jQuery.noConflict(true) is used,\n    //   and we should catch that.\n    var $ = typeof window !== UNDEF_TYPE && (window.jQuery || window.Zepto);\n    if ($ && !$.ua) {\n        var parser = new UAParser();\n        $.ua = parser.getResult();\n        $.ua.get = function () {\n            return parser.getUA();\n        };\n        $.ua.set = function (ua) {\n            parser.setUA(ua);\n            var result = parser.getResult();\n            for (var prop in result) {\n                $.ua[prop] = result[prop];\n            }\n        };\n    }\n\n})(typeof window === 'object' ? window : this);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @module FactoryMaker\n * @ignore\n */\nconst FactoryMaker = (function () {\n\n    let instance;\n    let singletonContexts = [];\n    const singletonFactories = {};\n    const classFactories = {};\n\n    function extend(name, childInstance, override, context) {\n        if (!context[name] && childInstance) {\n            context[name] = {\n                instance: childInstance,\n                override: override\n            };\n        }\n    }\n\n    /**\n     * Use this method from your extended object.  this.factory is injected into your object.\n     * this.factory.getSingletonInstance(this.context, 'VideoModel')\n     * will return the video model for use in the extended object.\n     *\n     * @param {Object} context - injected into extended object as this.context\n     * @param {string} className - string name found in all dash.js objects\n     * with name __dashjs_factory_name Will be at the bottom. Will be the same as the object's name.\n     * @returns {*} Context aware instance of specified singleton name.\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function getSingletonInstance(context, className) {\n        for (const i in singletonContexts) {\n            const obj = singletonContexts[i];\n            if (obj.context === context && obj.name === className) {\n                return obj.instance;\n            }\n        }\n        return null;\n    }\n\n    /**\n     * Use this method to add an singleton instance to the system.  Useful for unit testing to mock objects etc.\n     *\n     * @param {Object} context\n     * @param {string} className\n     * @param {Object} instance\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function setSingletonInstance(context, className, instance) {\n        for (const i in singletonContexts) {\n            const obj = singletonContexts[i];\n            if (obj.context === context && obj.name === className) {\n                singletonContexts[i].instance = instance;\n                return;\n            }\n        }\n        singletonContexts.push({\n            name: className,\n            context: context,\n            instance: instance\n        });\n    }\n\n    /**\n     * Use this method to remove all singleton instances associated with a particular context.\n     *\n     * @param {Object} context\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function deleteSingletonInstances(context) {\n        singletonContexts = singletonContexts.filter(x => x.context !== context);\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Factories storage Management\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function getFactoryByName(name, factoriesArray) {\n        return factoriesArray[name];\n    }\n\n    function updateFactory(name, factory, factoriesArray) {\n        if (name in factoriesArray) {\n            factoriesArray[name] = factory;\n        }\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Class Factories Management\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function updateClassFactory(name, factory) {\n        updateFactory(name, factory, classFactories);\n    }\n\n    function getClassFactoryByName(name) {\n        return getFactoryByName(name, classFactories);\n    }\n\n    function getClassFactory(classConstructor) {\n        let factory = getFactoryByName(classConstructor.__dashjs_factory_name, classFactories);\n\n        if (!factory) {\n            factory = function (context) {\n                if (context === undefined) {\n                    context = {};\n                }\n                return {\n                    create: function () {\n                        return merge(classConstructor, context, arguments);\n                    }\n                };\n            };\n\n            classFactories[classConstructor.__dashjs_factory_name] = factory; // store factory\n        }\n        return factory;\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Singleton Factory MAangement\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function updateSingletonFactory(name, factory) {\n        updateFactory(name, factory, singletonFactories);\n    }\n\n    function getSingletonFactoryByName(name) {\n        return getFactoryByName(name, singletonFactories);\n    }\n\n    function getSingletonFactory(classConstructor) {\n        let factory = getFactoryByName(classConstructor.__dashjs_factory_name, singletonFactories);\n        if (!factory) {\n            factory = function (context) {\n                let instance;\n                if (context === undefined) {\n                    context = {};\n                }\n                return {\n                    getInstance: function () {\n                        // If we don't have an instance yet check for one on the context\n                        if (!instance) {\n                            instance = getSingletonInstance(context, classConstructor.__dashjs_factory_name);\n                        }\n                        // If there's no instance on the context then create one\n                        if (!instance) {\n                            instance = merge(classConstructor, context, arguments);\n                            singletonContexts.push({\n                                name: classConstructor.__dashjs_factory_name,\n                                context: context,\n                                instance: instance\n                            });\n                        }\n                        return instance;\n                    }\n                };\n            };\n            singletonFactories[classConstructor.__dashjs_factory_name] = factory; // store factory\n        }\n\n        return factory;\n    }\n\n    function merge(classConstructor, context, args) {\n\n        let classInstance;\n        const className = classConstructor.__dashjs_factory_name;\n        const extensionObject = context[className];\n\n        if (extensionObject) {\n\n            let extension = extensionObject.instance;\n\n            if (extensionObject.override) { //Override public methods in parent but keep parent.\n\n                classInstance = classConstructor.apply({context}, args);\n                extension = extension.apply({\n                    context,\n                    factory: instance,\n                    parent: classInstance\n                }, args);\n\n                for (const prop in extension) {\n                    if (classInstance.hasOwnProperty(prop)) {\n                        classInstance[prop] = extension[prop];\n                    }\n                }\n\n            } else { //replace parent object completely with new object. Same as dijon.\n\n                return extension.apply({\n                    context,\n                    factory: instance\n                }, args);\n\n            }\n        } else {\n            // Create new instance of the class\n            classInstance = classConstructor.apply({context}, args);\n        }\n\n        // Add getClassName function to class instance prototype (used by Debug)\n        classInstance.getClassName = function () {return className;};\n\n        return classInstance;\n    }\n\n    instance = {\n        extend: extend,\n        getSingletonInstance: getSingletonInstance,\n        setSingletonInstance: setSingletonInstance,\n        deleteSingletonInstances: deleteSingletonInstances,\n        getSingletonFactory: getSingletonFactory,\n        getSingletonFactoryByName: getSingletonFactoryByName,\n        updateSingletonFactory: updateSingletonFactory,\n        getClassFactory: getClassFactory,\n        getClassFactoryByName: getClassFactoryByName,\n        updateClassFactory: updateClassFactory\n    };\n\n    return instance;\n\n}());\n\nexport default FactoryMaker;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * @class\n * @ignore\n */\n\nimport path from 'path-browserify'\nimport {UAParser} from 'ua-parser-js'\nimport Constants from '../streaming/constants/Constants.js';\n\nclass Utils {\n    static mixin(dest, source, copy) {\n        let s;\n        let empty = {};\n        if (dest) {\n            for (let name in source) {\n                if (source.hasOwnProperty(name)) {\n                    s = source[name];\n                    if (!(name in dest) || (dest[name] !== s && (!(name in empty) || empty[name] !== s))) {\n                        if (typeof dest[name] === 'object' && dest[name] !== null) {\n                            dest[name] = Utils.mixin(dest[name], s, copy);\n                        } else {\n                            dest[name] = copy(s);\n                        }\n                    }\n                }\n            }\n        }\n        return dest;\n    }\n\n    static clone(src) {\n        if (!src || typeof src !== 'object') {\n            return src; // anything\n        }\n        if (src instanceof RegExp) {\n            return new RegExp(src);\n        }\n        let r;\n        if (src instanceof Array) {\n            // array\n            r = [];\n            for (let i = 0, l = src.length; i < l; ++i) {\n                if (i in src) {\n                    r.push(Utils.clone(src[i]));\n                }\n            }\n        } else {\n            r = {};\n        }\n        return Utils.mixin(r, src, Utils.clone);\n    }\n\n    static addAdditionalQueryParameterToUrl(url, params) {\n        try {\n            if (!params || params.length === 0) {\n                return url;\n            }\n\n            let updatedUrl = url;\n            params.forEach(({ key, value }) => {\n                const separator = updatedUrl.includes('?') ? '&' : '?';\n                updatedUrl += `${separator}${(encodeURIComponent(key))}=${(encodeURIComponent(value))}`;\n            });\n            return updatedUrl;\n        } catch (e) {\n            return url;\n        }\n    }\n\n    static removeQueryParameterFromUrl(url, queryParameter) {\n        if (!url || !queryParameter) {\n            return url;\n        }\n        // Parse the URL\n        const parsedUrl = new URL(url);\n\n        // Get the search parameters\n        const params = new URLSearchParams(parsedUrl.search);\n\n        if (!params || params.size === 0 || !params.has(queryParameter)) {\n            return url;\n        }\n\n        // Remove the queryParameter\n        params.delete(queryParameter);\n\n        // Manually reconstruct the query string without re-encoding\n        const queryString = Array.from(params.entries())\n            .map(([key, value]) => `${key}=${value}`)\n            .join('&');\n\n        // Reconstruct the URL\n        const baseUrl = `${parsedUrl.origin}${parsedUrl.pathname}`;\n        return queryString ? `${baseUrl}?${queryString}` : baseUrl;\n    }\n\n    static parseHttpHeaders(headerStr) {\n        let headers = {};\n        if (!headerStr) {\n            return headers;\n        }\n\n        // Trim headerStr to fix a MS Edge bug with xhr.getAllResponseHeaders method\n        // which send a string starting with a \"\\n\" character\n        let headerPairs = headerStr.trim().split('\\u000d\\u000a');\n        for (let i = 0, ilen = headerPairs.length; i < ilen; i++) {\n            let headerPair = headerPairs[i];\n            let index = headerPair.indexOf('\\u003a\\u0020');\n            if (index > 0) {\n                headers[headerPair.substring(0, index)] = headerPair.substring(index + 2);\n            }\n        }\n        return headers;\n    }\n\n    /**\n     * Parses query parameters from a string and returns them as an array of key-value pairs.\n     * @param {string} queryParamString - A string containing the query parameters.\n     * @return {Array<{key: string, value: string}>} An array of objects representing the query parameters.\n     */\n    static parseQueryParams(queryParamString) {\n        const params = [];\n        const searchParams = new URLSearchParams(queryParamString);\n        for (const [key, value] of searchParams.entries()) {\n            params.push({ key: decodeURIComponent(key), value: decodeURIComponent(value) });\n        }\n        return params;\n    }\n\n    static generateUuid() {\n        let dt = new Date().getTime();\n        const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n            const r = (dt + Math.random() * 16) % 16 | 0;\n            dt = Math.floor(dt / 16);\n            return (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16);\n        });\n        return uuid;\n    }\n\n    static generateHashCode(string) {\n        let hash = 0;\n\n        if (string.length === 0) {\n            return hash;\n        }\n\n        for (let i = 0; i < string.length; i++) {\n            const chr = string.charCodeAt(i);\n            hash = ((hash << 5) - hash) + chr;\n            hash |= 0;\n        }\n        return hash;\n    }\n\n    /**\n     * Compares both urls and returns a relative url (target relative to original)\n     * @param {string} originalUrl\n     * @param {string} targetUrl\n     * @return {string|*}\n     */\n    static getRelativeUrl(originalUrl, targetUrl) {\n        try {\n            const original = new URL(originalUrl);\n            const target = new URL(targetUrl);\n\n            // Unify the protocol to compare the origins\n            original.protocol = target.protocol;\n            if (original.origin !== target.origin) {\n                return targetUrl;\n            }\n\n            // Use the relative path implementation of the path library. We need to cut off the actual filename in the end to get the relative path\n            let relativePath = path.relative(original.pathname.substr(0, original.pathname.lastIndexOf('/')), target.pathname.substr(0, target.pathname.lastIndexOf('/')));\n\n            // In case the relative path is empty (both path are equal) return the filename only. Otherwise add a slash in front of the filename\n            const startIndexOffset = relativePath.length === 0 ? 1 : 0;\n            relativePath += target.pathname.substr(target.pathname.lastIndexOf('/') + startIndexOffset, target.pathname.length - 1);\n\n            // Build the other candidate, e.g. the 'host relative' path that starts with \"/\", and return the shortest of the two candidates.\n            if (target.pathname.length < relativePath.length) {\n                return target.pathname;\n            }\n            return relativePath;\n        } catch (e) {\n            return targetUrl\n        }\n    }\n\n    static getHostFromUrl(urlString) {\n        try {\n            const url = new URL(urlString);\n\n            return url.host\n        } catch (e) {\n            return null\n        }\n    }\n\n    static parseUserAgent(ua = null) {\n        try {\n            const uaString = ua === null ? typeof navigator !== 'undefined' ? navigator.userAgent.toLowerCase() : '' : '';\n\n            return UAParser(uaString);\n        } catch (e) {\n            return {};\n        }\n    }\n\n    /**\n     * Checks for existence of \"http\" or \"https\" in a string\n     * @param string\n     * @returns {boolean}\n     */\n    static stringHasProtocol(string) {\n        return (/(http(s?)):\\/\\//i.test(string))\n    }\n\n    static bufferSourceToDataView(bufferSource) {\n        return Utils.toDataView(bufferSource, DataView);\n    }\n\n    static bufferSourceToInt8(bufferSource) {\n        return Utils.toDataView(bufferSource, Uint8Array)\n    }\n\n    static uint8ArrayToString(uint8Array) {\n        const decoder = new TextDecoder('utf-8');\n        return decoder.decode(uint8Array);\n    }\n\n    static bufferSourceToHex(data) {\n        const arr = Utils.bufferSourceToInt8(data)\n        let hex = '';\n        for (let value of arr) {\n            value = value.toString(16);\n            if (value.length === 1) {\n                value = '0' + value;\n            }\n            hex += value;\n        }\n        return hex;\n    }\n\n    static toDataView(bufferSource, Type) {\n        const buffer = Utils.getArrayBuffer(bufferSource);\n        let bytesPerElement = 1;\n        if ('BYTES_PER_ELEMENT' in DataView) {\n            bytesPerElement = DataView.BYTES_PER_ELEMENT;\n        }\n\n        const dataEnd = ((bufferSource.byteOffset || 0) + bufferSource.byteLength) /\n            bytesPerElement;\n        const rawStart = ((bufferSource.byteOffset || 0)) / bytesPerElement;\n        const start = Math.floor(Math.max(0, Math.min(rawStart, dataEnd)));\n        const end = Math.floor(Math.min(start + Math.max(Infinity, 0), dataEnd));\n        return new Type(buffer, start, end - start);\n    }\n\n    static getArrayBuffer(view) {\n        if (view instanceof ArrayBuffer) {\n            return view;\n        } else {\n            return view.buffer;\n        }\n    }\n\n    static getCodecFamily(codecString) {\n        const { base, profile } = Utils._getCodecParts(codecString)\n\n        switch (base) {\n            case 'mp4a':\n                switch (profile) {\n                    case '69':\n                    case '6b':\n                    case '40.34':\n                        return Constants.CODEC_FAMILIES.MP3\n                    case '66':\n                    case '67':\n                    case '68':\n                    case '40.2':\n                    case '40.02':\n                    case '40.5':\n                    case '40.05':\n                    case '40.29':\n                    case '40.42':\n                        return Constants.CODEC_FAMILIES.AAC\n                    case 'a5':\n                        return Constants.CODEC_FAMILIES.AC3\n                    case 'e6':\n                        return Constants.CODEC_FAMILIES.EC3\n                    case 'b2':\n                        return Constants.CODEC_FAMILIES.DTSX\n                    case 'a9':\n                        return Constants.CODEC_FAMILIES.DTSC\n                }\n                break;\n            case 'avc1':\n            case 'avc3':\n                return Constants.CODEC_FAMILIES.AVC\n            case 'hvc1':\n            case 'hvc3':\n                return Constants.CODEC_FAMILIES.HEVC\n            default:\n                return base\n        }\n\n        return base;\n    }\n\n    static _getCodecParts(codecString) {\n        const [base, ...rest] = codecString.split('.');\n        const profile = rest.join('.');\n        return { base, profile };\n    }\n\n}\n\nexport default Utils;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass ErrorsBase {\n    extend(errors, config) {\n        if (!errors) {\n            return;\n        }\n\n        let override = config ? config.override : false;\n        let publicOnly = config ? config.publicOnly : false;\n\n\n        for (const err in errors) {\n            if (!errors.hasOwnProperty(err) || (this[err] && !override)) {\n                continue;\n            }\n            if (publicOnly && errors[err].indexOf('public_') === -1) {\n                continue;\n            }\n            this[err] = errors[err];\n\n        }\n    }\n}\n\nexport default ErrorsBase;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass EventsBase {\n    extend(events, config) {\n        if (!events) {\n            return;\n        }\n\n        let override = config ? config.override : false;\n        let publicOnly = config ? config.publicOnly : false;\n\n\n        for (const evt in events) {\n            if (!events.hasOwnProperty(evt) || (this[evt] && !override)) {\n                continue;\n            }\n            if (publicOnly && events[evt].indexOf('public_') === -1) {\n                continue;\n            }\n            this[evt] = events[evt];\n\n        }\n    }\n}\n\nexport default EventsBase;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES, LOSS OF USE, DATA, OR\n *  PROFITS, OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * Dash constants declaration\n * @ignore\n */\nexport default {\n    ACCESSIBILITY: 'Accessibility',\n    ADAPTATION_SET: 'AdaptationSet',\n    ADAPTATION_SETS: 'adaptationSets',\n    ADAPTATION_SET_SWITCHING_SCHEME_ID_URI: 'urn:mpeg:dash:adaptation-set-switching:2016',\n    ADD: 'add',\n    ASSET_IDENTIFIER: 'AssetIdentifier',\n    AUDIO_CHANNEL_CONFIGURATION: 'AudioChannelConfiguration',\n    AUDIO_SAMPLING_RATE: 'audioSamplingRate',\n    AVAILABILITY_END_TIME: 'availabilityEndTime',\n    AVAILABILITY_START_TIME: 'availabilityStartTime',\n    AVAILABILITY_TIME_COMPLETE: 'availabilityTimeComplete',\n    AVAILABILITY_TIME_OFFSET: 'availabilityTimeOffset',\n    BANDWITH: 'bandwidth',\n    BASE_URL: 'BaseURL',\n    BITSTREAM_SWITCHING: 'BitstreamSwitching',\n    BITSTREAM_SWITCHING_MINUS: 'bitstreamSwitching',\n    BYTE_RANGE: 'byteRange',\n    CAPTION: 'caption',\n    CENC_DEFAULT_KID: 'cenc:default_KID',\n    CLIENT_DATA_REPORTING: 'ClientDataReporting',\n    CLIENT_REQUIREMENT: 'clientRequirement',\n    CMCD_PARAMETERS: 'CMCDParameters',\n    CODECS: 'codecs',\n    CODEC_PRIVATE_DATA: 'codecPrivateData',\n    CODING_DEPENDENCY: 'codingDependency',\n    CONTENT_COMPONENT: 'ContentComponent',\n    CONTENT_PROTECTION: 'ContentProtection',\n    CONTENT_STEERING: 'ContentSteering',\n    CONTENT_STEERING_RESPONSE: {\n        VERSION: 'VERSION',\n        TTL: 'TTL',\n        RELOAD_URI: 'RELOAD-URI',\n        PATHWAY_PRIORITY: 'PATHWAY-PRIORITY',\n        PATHWAY_CLONES: 'PATHWAY-CLONES',\n        BASE_ID: 'BASE-ID',\n        ID: 'ID',\n        URI_REPLACEMENT: 'URI-REPLACEMENT',\n        HOST: 'HOST',\n        PARAMS: 'PARAMS'\n    },\n    CONTENT_TYPE: 'contentType',\n    DEFAULT_SERVICE_LOCATION: 'defaultServiceLocation',\n    DEPENDENCY_ID: 'dependencyId',\n    DURATION: 'duration',\n    DVB_PRIORITY : 'dvb:priority',\n    DVB_WEIGHT : 'dvb:weight',\n    DVB_URL : 'dvb:url',\n    DVB_MIMETYPE : 'dvb:mimeType',\n    DVB_FONTFAMILY : 'dvb:fontFamily',\n    DYNAMIC: 'dynamic',\n    END_NUMBER: 'endNumber',\n    ESSENTIAL_PROPERTY: 'EssentialProperty',\n    EVENT: 'Event',\n    EVENT_STREAM: 'EventStream',\n    FORCED_SUBTITLE: 'forced-subtitle',\n    FRAMERATE: 'frameRate',\n    FRAME_PACKING: 'FramePacking',\n    GROUP_LABEL: 'GroupLabel',\n    HEIGHT: 'height',\n    ID: 'id',\n    INBAND: 'inband',\n    INBAND_EVENT_STREAM: 'InbandEventStream',\n    INDEX: 'index',\n    INDEX_RANGE: 'indexRange',\n    INITIALIZATION: 'Initialization',\n    INITIALIZATION_MINUS: 'initialization',\n    LA_URL: 'Laurl',\n    LA_URL_LOWER_CASE: 'laurl',\n    LABEL: 'Label',\n    LANG: 'lang',\n    LOCATION: 'Location',\n    MAIN: 'main',\n    MAXIMUM_SAP_PERIOD: 'maximumSAPPeriod',\n    MAX_PLAYOUT_RATE: 'maxPlayoutRate',\n    MAX_SEGMENT_DURATION: 'maxSegmentDuration',\n    MAX_SUBSEGMENT_DURATION: 'maxSubsegmentDuration',\n    MEDIA: 'media',\n    MEDIA_PRESENTATION_DURATION: 'mediaPresentationDuration',\n    MEDIA_RANGE: 'mediaRange',\n    MEDIA_STREAM_STRUCTURE_ID: 'mediaStreamStructureId',\n    METRICS: 'Metrics',\n    METRICS_MINUS: 'metrics',\n    MIME_TYPE: 'mimeType',\n    MINIMUM_UPDATE_PERIOD: 'minimumUpdatePeriod',\n    MIN_BUFFER_TIME: 'minBufferTime',\n    MP4_PROTECTION_SCHEME: 'urn:mpeg:dash:mp4protection:2011',\n    MPD: 'MPD',\n    MPD_TYPE: 'mpd',\n    MPD_PATCH_TYPE: 'mpdpatch',\n    ORIGINAL_MPD_ID: 'mpdId',\n    ORIGINAL_PUBLISH_TIME: 'originalPublishTime',\n    PATCH_LOCATION: 'PatchLocation',\n    PERIOD: 'Period',\n    PRESENTATION_TIME: 'presentationTime',\n    PRESENTATION_TIME_OFFSET: 'presentationTimeOffset',\n    PRO: 'pro',\n    PRODUCER_REFERENCE_TIME: 'ProducerReferenceTime',\n    PRODUCER_REFERENCE_TIME_TYPE: {\n        ENCODER: 'encoder',\n        CAPTURED: 'captured',\n        APPLICATION: 'application'\n    },\n    PROFILES: 'profiles',\n    PSSH: 'pssh',\n    PUBLISH_TIME: 'publishTime',\n    QUALITY_RANKING : 'qualityRanking',\n    QUERY_BEFORE_START: 'queryBeforeStart',\n    QUERY_PART: '$querypart$',\n    RANGE: 'range',\n    RATING: 'Rating',\n    REF: 'ref',\n    REF_ID: 'refId',\n    REMOVE: 'remove',\n    REPLACE: 'replace',\n    REPORTING: 'Reporting',\n    REPRESENTATION: 'Representation',\n    REPRESENTATION_INDEX: 'RepresentationIndex',\n    ROBUSTNESS: 'robustness',\n    ROLE: 'Role',\n    S: 'S',\n    SAR: 'sar',\n    SCAN_TYPE: 'scanType',\n    SEGMENT_ALIGNMENT: 'segmentAlignment',\n    SEGMENT_BASE: 'SegmentBase',\n    SEGMENT_LIST: 'SegmentList',\n    SEGMENT_PROFILES: 'segmentProfiles',\n    SEGMENT_TEMPLATE: 'SegmentTemplate',\n    SEGMENT_TIMELINE: 'SegmentTimeline',\n    SEGMENT_TYPE: 'segment',\n    SEGMENT_URL: 'SegmentURL',\n    SERVICE_DESCRIPTION: 'ServiceDescription',\n    SERVICE_DESCRIPTION_LATENCY: 'Latency',\n    SERVICE_DESCRIPTION_OPERATING_BANDWIDTH: 'OperatingBandwidth',\n    SERVICE_DESCRIPTION_OPERATING_QUALITY: 'OperatingQuality',\n    SERVICE_DESCRIPTION_PLAYBACK_RATE: 'PlaybackRate',\n    SERVICE_DESCRIPTION_SCOPE: 'Scope',\n    SERVICE_LOCATION: 'serviceLocation',\n    SERVICE_LOCATIONS: 'serviceLocations',\n    SOURCE_URL: 'sourceURL',\n    START: 'start',\n    START_NUMBER: 'startNumber',\n    START_WITH_SAP: 'startWithSAP',\n    STATIC: 'static',\n    STEERING_TYPE: 'steering',\n    SUBSET: 'Subset',\n    SUBTITLE: 'subtitle',\n    SUB_REPRESENTATION: 'SubRepresentation',\n    SUB_SEGMENT_ALIGNMENT: 'subsegmentAlignment',\n    SUGGESTED_PRESENTATION_DELAY: 'suggestedPresentationDelay',\n    SUPPLEMENTAL_PROPERTY: 'SupplementalProperty',\n    SUPPLEMENTAL_CODECS: 'scte214:supplementalCodecs',\n    TIMESCALE: 'timescale',\n    TIMESHIFT_BUFFER_DEPTH: 'timeShiftBufferDepth',\n    TTL: 'ttl',\n    TYPE: 'type',\n    UTC_TIMING: 'UTCTiming',\n    VALUE: 'value',\n    VIEWPOINT: 'Viewpoint',\n    WALL_CLOCK_TIME: 'wallClockTime',\n    WIDTH: 'width',\n}\n\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * Constants declaration\n */\nexport default {\n    /**\n     *  @constant {string} STREAM Stream media type. Mainly used to report metrics relative to the full stream\n     *  @memberof Constants#\n     *  @static\n     */\n    STREAM: 'stream',\n\n    /**\n     *  @constant {string} VIDEO Video media type\n     *  @memberof Constants#\n     *  @static\n     */\n    VIDEO: 'video',\n\n    /**\n     *  @constant {string} AUDIO Audio media type\n     *  @memberof Constants#\n     *  @static\n     */\n    AUDIO: 'audio',\n\n    /**\n     *  @constant {string} TEXT Text media type\n     *  @memberof Constants#\n     *  @static\n     */\n    TEXT: 'text',\n\n    /**\n     *  @constant {string} MUXED Muxed (video/audio in the same chunk) media type\n     *  @memberof Constants#\n     *  @static\n     */\n    MUXED: 'muxed',\n\n    /**\n     *  @constant {string} IMAGE Image media type\n     *  @memberof Constants#\n     *  @static\n     */\n    IMAGE: 'image',\n\n    /**\n     *  @constant {string} STPP STTP Subtitles format\n     *  @memberof Constants#\n     *  @static\n     */\n    STPP: 'stpp',\n\n    /**\n     *  @constant {string} TTML STTP Subtitles format\n     *  @memberof Constants#\n     *  @static\n     */\n    TTML: 'ttml',\n\n    /**\n     *  @constant {string} VTT STTP Subtitles format\n     *  @memberof Constants#\n     *  @static\n     */\n    VTT: 'vtt',\n\n    /**\n     *  @constant {string} WVTT STTP Subtitles format\n     *  @memberof Constants#\n     *  @static\n     */\n    WVTT: 'wvtt',\n\n    /**\n     *  @constant {string} Content Steering\n     *  @memberof Constants#\n     *  @static\n     */\n    CONTENT_STEERING: 'contentSteering',\n\n    /**\n     *  @constant {string} LIVE_CATCHUP_MODE_DEFAULT Throughput calculation based on moof parsing\n     *  @memberof Constants#\n     *  @static\n     */\n    LIVE_CATCHUP_MODE_DEFAULT: 'liveCatchupModeDefault',\n\n    /**\n     *  @constant {string} LIVE_CATCHUP_MODE_LOLP Throughput calculation based on moof parsing\n     *  @memberof Constants#\n     *  @static\n     */\n    LIVE_CATCHUP_MODE_LOLP: 'liveCatchupModeLoLP',\n\n    /**\n     *  @constant {string} MOVING_AVERAGE_SLIDING_WINDOW Moving average sliding window\n     *  @memberof Constants#\n     *  @static\n     */\n    MOVING_AVERAGE_SLIDING_WINDOW: 'slidingWindow',\n\n    /**\n     *  @constant {string} EWMA Exponential moving average\n     *  @memberof Constants#\n     *  @static\n     */\n    MOVING_AVERAGE_EWMA: 'ewma',\n\n    /**\n     *  @constant {string} BAD_ARGUMENT_ERROR Invalid Arguments type of error\n     *  @memberof Constants#\n     *  @static\n     */\n    BAD_ARGUMENT_ERROR: 'Invalid Arguments',\n\n    /**\n     *  @constant {string} MISSING_CONFIG_ERROR Missing configuration parameters type of error\n     *  @memberof Constants#\n     *  @static\n     */\n    MISSING_CONFIG_ERROR: 'Missing config parameter(s)',\n\n    /**\n     *  @constant {string} TRACK_SWITCH_MODE_ALWAYS_REPLACE used to clear the buffered data (prior to current playback position) after track switch. Default for audio\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SWITCH_MODE_ALWAYS_REPLACE: 'alwaysReplace',\n\n    /**\n     *  @constant {string} TRACK_SWITCH_MODE_NEVER_REPLACE used to forbid clearing the buffered data (prior to current playback position) after track switch. Defers to fastSwitchEnabled for placement of new data. Default for video\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SWITCH_MODE_NEVER_REPLACE: 'neverReplace',\n\n    /**\n     *  @constant {string} TRACK_SELECTION_MODE_FIRST_TRACK makes the player select the first track found in the manifest.\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SELECTION_MODE_FIRST_TRACK: 'firstTrack',\n\n    /**\n     *  @constant {string} TRACK_SELECTION_MODE_HIGHEST_BITRATE makes the player select the track with a highest bitrate. This mode is a default mode.\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SELECTION_MODE_HIGHEST_BITRATE: 'highestBitrate',\n\n    /**\n     *  @constant {string} TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY makes the player select the track with the lowest bitrate per pixel average.\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY: 'highestEfficiency',\n\n    /**\n     *  @constant {string} TRACK_SELECTION_MODE_WIDEST_RANGE makes the player select the track with a widest range of bitrates.\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SELECTION_MODE_WIDEST_RANGE: 'widestRange',\n\n    /**\n     *  @constant {string} CMCD_QUERY_KEY specifies the key that is used for the CMCD query parameter.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_QUERY_KEY: 'CMCD',\n\n    /**\n     *  @constant {string} CMCD_MODE_QUERY specifies to attach CMCD metrics as query parameters.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_MODE_QUERY: 'query',\n\n    /**\n     *  @constant {string} CMCD_MODE_HEADER specifies to attach CMCD metrics as HTTP headers.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_MODE_HEADER: 'header',\n\n    /**\n     *  @constant {string} CMCD_AVAILABLE_KEYS specifies all the available keys for CMCD metrics.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_AVAILABLE_KEYS: ['br', 'd', 'ot', 'tb', 'bl', 'dl', 'mtp', 'nor', 'nrr', 'su', 'bs', 'rtp', 'cid', 'pr', 'sf', 'sid', 'st', 'v'],\n    /**\n     *  @constant {string} CMCD_AVAILABLE_KEYS_V2 specifies all the available keys for CMCD version 2 metrics.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_V2_AVAILABLE_KEYS: ['msd', 'ltc'],\n\n    /**\n     *  @constant {string} CMCD_AVAILABLE_REQUESTS specifies all the available requests type for CMCD metrics.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_AVAILABLE_REQUESTS: ['segment', 'mpd', 'xlink', 'steering', 'other'],\n\n\n    INITIALIZE: 'initialize',\n    TEXT_SHOWING: 'showing',\n    TEXT_HIDDEN: 'hidden',\n    TEXT_DISABLED: 'disabled',\n    ACCESSIBILITY_CEA608_SCHEME: 'urn:scte:dash:cc:cea-608:2015',\n    CC1: 'CC1',\n    CC3: 'CC3',\n    UTF8: 'utf-8',\n    SCHEME_ID_URI: 'schemeIdUri',\n    START_TIME: 'starttime',\n    SERVICE_DESCRIPTION_DVB_LL_SCHEME: 'urn:dvb:dash:lowlatency:scope:2019',\n    SUPPLEMENTAL_PROPERTY_DVB_LL_SCHEME: 'urn:dvb:dash:lowlatency:critical:2019',\n    CTA_5004_2023_SCHEME: 'urn:mpeg:dash:cta-5004:2023',\n    THUMBNAILS_SCHEME_ID_URIS: ['http://dashif.org/thumbnail_tile', 'http://dashif.org/guidelines/thumbnail_tile'],\n    FONT_DOWNLOAD_DVB_SCHEME: 'urn:dvb:dash:fontdownload:2014',\n    COLOUR_PRIMARIES_SCHEME_ID_URI: 'urn:mpeg:mpegB:cicp:ColourPrimaries',\n    URL_QUERY_INFO_SCHEME: 'urn:mpeg:dash:urlparam:2014',\n    EXT_URL_QUERY_INFO_SCHEME: 'urn:mpeg:dash:urlparam:2016',\n    MATRIX_COEFFICIENTS_SCHEME_ID_URI: 'urn:mpeg:mpegB:cicp:MatrixCoefficients',\n    TRANSFER_CHARACTERISTICS_SCHEME_ID_URI: 'urn:mpeg:mpegB:cicp:TransferCharacteristics',\n    HDR_METADATA_FORMAT_SCHEME_ID_URI: 'urn:dvb:dash:hdr-dmi',\n    HDR_METADATA_FORMAT_VALUES: {\n        ST2094_10: 'ST2094-10',\n        SL_HDR2: 'SL-HDR2',\n        ST2094_40: 'ST2094-40'\n    },\n    MEDIA_CAPABILITIES_API: {\n        COLORGAMUT: {\n            SRGB: 'srgb',\n            P3: 'p3',\n            REC2020: 'rec2020'\n        },\n        TRANSFERFUNCTION: {\n            SRGB: 'srgb',\n            PQ: 'pq',\n            HLG: 'hlg'\n        },\n        HDR_METADATATYPE: {\n            SMPTE_ST_2094_10: 'smpteSt2094-10',\n            SLHDR2: 'slhdr2',\n            SMPTE_ST_2094_40: 'smpteSt2094-40'\n        }\n    },\n    XML: 'XML',\n    ARRAY_BUFFER: 'ArrayBuffer',\n    DVB_REPORTING_URL: 'dvb:reportingUrl',\n    DVB_PROBABILITY: 'dvb:probability',\n    OFF_MIMETYPE: 'application/font-sfnt',\n    WOFF_MIMETYPE: 'application/font-woff',\n    VIDEO_ELEMENT_READY_STATES: {\n        HAVE_NOTHING: 0,\n        HAVE_METADATA: 1,\n        HAVE_CURRENT_DATA: 2,\n        HAVE_FUTURE_DATA: 3,\n        HAVE_ENOUGH_DATA: 4\n    },\n    FILE_LOADER_TYPES: {\n        FETCH: 'fetch_loader',\n        XHR: 'xhr_loader'\n    },\n    THROUGHPUT_TYPES: {\n        LATENCY: 'throughput_type_latency',\n        BANDWIDTH: 'throughput_type_bandwidth'\n    },\n    THROUGHPUT_CALCULATION_MODES: {\n        EWMA: 'throughputCalculationModeEwma',\n        ZLEMA: 'throughputCalculationModeZlema',\n        ARITHMETIC_MEAN: 'throughputCalculationModeArithmeticMean',\n        BYTE_SIZE_WEIGHTED_ARITHMETIC_MEAN: 'throughputCalculationModeByteSizeWeightedArithmeticMean',\n        DATE_WEIGHTED_ARITHMETIC_MEAN: 'throughputCalculationModeDateWeightedArithmeticMean',\n        HARMONIC_MEAN: 'throughputCalculationModeHarmonicMean',\n        BYTE_SIZE_WEIGHTED_HARMONIC_MEAN: 'throughputCalculationModeByteSizeWeightedHarmonicMean',\n        DATE_WEIGHTED_HARMONIC_MEAN: 'throughputCalculationModeDateWeightedHarmonicMean',\n    },\n    LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE: {\n        MOOF_PARSING: 'lowLatencyDownloadTimeCalculationModeMoofParsing',\n        DOWNLOADED_DATA: 'lowLatencyDownloadTimeCalculationModeDownloadedData',\n        AAST: 'lowLatencyDownloadTimeCalculationModeAast',\n    },\n    RULES_TYPES: {\n        QUALITY_SWITCH_RULES: 'qualitySwitchRules',\n        ABANDON_FRAGMENT_RULES: 'abandonFragmentRules'\n    },\n    QUALITY_SWITCH_RULES: {\n        BOLA_RULE: 'BolaRule',\n        THROUGHPUT_RULE: 'ThroughputRule',\n        INSUFFICIENT_BUFFER_RULE: 'InsufficientBufferRule',\n        SWITCH_HISTORY_RULE: 'SwitchHistoryRule',\n        DROPPED_FRAMES_RULE: 'DroppedFramesRule',\n        LEARN_TO_ADAPT_RULE: 'L2ARule',\n        LOL_PLUS_RULE: 'LoLPRule'\n    },\n    ABANDON_FRAGMENT_RULES: {\n        ABANDON_REQUEST_RULE: 'AbandonRequestsRule'\n    },\n\n    /**\n     *  @constant {string} ID3_SCHEME_ID_URI specifies scheme ID URI for ID3 timed metadata\n     *  @memberof Constants#\n     *  @static\n     */\n    ID3_SCHEME_ID_URI: 'https://aomedia.org/emsg/ID3',\n    COMMON_ACCESS_TOKEN_HEADER: 'common-access-token',\n    DASH_ROLE_SCHEME_ID : 'urn:mpeg:dash:role:2011',\n    CODEC_FAMILIES: {\n        MP3: 'mp3',\n        AAC: 'aac',\n        AC3: 'ac3',\n        EC3: 'ec3',\n        DTSX: 'dtsx',\n        DTSC: 'dtsc',\n        AVC: 'avc',\n        HEVC: 'hevc'\n    }\n}\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES, LOSS OF USE, DATA, OR\n *  PROFITS, OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * Protection Constants declaration\n * @ignore\n */\nexport default {\n    CLEARKEY_KEYSTEM_STRING: 'org.w3.clearkey',\n    WIDEVINE_KEYSTEM_STRING: 'com.widevine.alpha',\n    PLAYREADY_KEYSTEM_STRING: 'com.microsoft.playready',\n    PLAYREADY_RECOMMENDATION_KEYSTEM_STRING: 'com.microsoft.playready.recommendation',\n    WIDEVINE_UUID: 'edef8ba9-79d6-4ace-a3c8-27dcd51d21ed',\n    PLAYREADY_UUID: '9a04f079-9840-4286-ab92-e65be0885f95',\n    CLEARKEY_UUID: 'e2719d58-a985-b3c9-781a-b030af78d30e',\n    W3C_CLEARKEY_UUID: '1077efec-c0b2-4d02-ace3-3c1e52e2fb4b',\n    INITIALIZATION_DATA_TYPE_CENC: 'cenc',\n    INITIALIZATION_DATA_TYPE_KEYIDS: 'keyids',\n    INITIALIZATION_DATA_TYPE_WEBM: 'webm',\n    ENCRYPTION_SCHEME_CENC: 'cenc',\n    ENCRYPTION_SCHEME_CBCS: 'cbcs',\n    MEDIA_KEY_MESSAGE_TYPES: {\n        LICENSE_REQUEST: 'license-request',\n        LICENSE_RENEWAL: 'license-renewal',\n        LICENSE_RELEASE: 'license-release',\n        INDIVIDUALIZATION_REQUEST: 'individualization-request',\n    },\n    ROBUSTNESS_STRINGS: {\n        WIDEVINE: {\n            SW_SECURE_CRYPTO: 'SW_SECURE_CRYPTO',\n            SW_SECURE_DECODE: 'SW_SECURE_DECODE',\n            HW_SECURE_CRYPTO: 'HW_SECURE_CRYPTO',\n            HW_SECURE_DECODE: 'HW_SECURE_DECODE',\n            HW_SECURE_ALL: 'HW_SECURE_ALL'\n        }\n    },\n    MEDIA_KEY_STATUSES: {\n        USABLE: 'usable',\n        EXPIRED: 'expired',\n        RELEASED: 'released',\n        OUTPUT_RESTRICTED: 'output-restricted',\n        OUTPUT_DOWNSCALED: 'output-downscaled',\n        STATUS_PENDING: 'status-pending',\n        INTERNAL_ERROR: 'internal-error',\n    }\n}\n\n\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport EventsBase from '../../core/events/EventsBase.js';\n\n/**\n * @class\n */\nclass ProtectionEvents extends EventsBase {\n    /**\n     * @description Public facing external events to be used when including protection package.\n     * All public events will be aggregated into the MediaPlayerEvents Class and can be accessed\n     * via MediaPlayer.events.  public_ is the prefix that we use to move event names to MediaPlayerEvents.\n     */\n    constructor() {\n        super();\n\n        /**\n         * Event ID for events delivered when the protection set receives\n         * a key message from the CDM\n         *\n         * @ignore\n         */\n        this.INTERNAL_KEY_MESSAGE = 'internalKeyMessage';\n\n        /**\n         * Event ID for events delivered when the status of one decryption keys has changed\n         * @ignore\n         */\n        this.INTERNAL_KEY_STATUSES_CHANGED = 'internalkeyStatusesChanged';\n\n        /**\n         * Event ID for events delivered when a new key has been added\n         *\n         * @constant\n         * @deprecated The latest versions of the EME specification no longer\n         * use this event.  {@MediaPlayer.models.protectionModel.eventList.KEY_STATUSES_CHANGED}\n         * is preferred.\n         * @event ProtectionEvents#KEY_ADDED\n         */\n        this.KEY_ADDED = 'public_keyAdded';\n        /**\n         * Event ID for events delivered when an error is encountered by the CDM\n         * while processing a license server response message\n         * @event ProtectionEvents#KEY_ERROR\n         */\n        this.KEY_ERROR = 'public_keyError';\n\n        /**\n         * Event ID for events delivered when the protection set receives\n         * a key message from the CDM\n         * @event ProtectionEvents#KEY_MESSAGE\n         */\n        this.KEY_MESSAGE = 'public_keyMessage';\n\n        /**\n         * Event ID for events delivered when a key session close\n         * process has completed\n         * @event ProtectionEvents#KEY_SESSION_CLOSED\n         */\n        this.KEY_SESSION_CLOSED = 'public_keySessionClosed';\n\n        /**\n         * Event ID for events delivered when a new key sessions creation\n         * process has completed\n         * @event ProtectionEvents#KEY_SESSION_CREATED\n         */\n        this.KEY_SESSION_CREATED = 'public_keySessionCreated';\n\n        /**\n         * Event ID for events delivered when a key session removal\n         * process has completed\n         * @event ProtectionEvents#KEY_SESSION_REMOVED\n         */\n        this.KEY_SESSION_REMOVED = 'public_keySessionRemoved';\n\n        /**\n         * Event ID for events delivered when the status of one or more\n         * decryption keys has changed\n         * @event ProtectionEvents#KEY_STATUSES_CHANGED\n         */\n        this.KEY_STATUSES_CHANGED = 'public_keyStatusesChanged';\n\n        /**\n         * Triggered when the key statuses Map() of the ProtectionController was updated. This happens after there is a keystatuseschange.\n         * The event can be used as an indicator when to refresh the list of possible Representations\n         * @event ProtectionEvents#KEY_STATUSES_MAP_UPDATED\n         */\n        this.KEY_STATUSES_MAP_UPDATED = 'keyStatusesMapUpdated';\n\n        /**\n         * Event ID for events delivered when a key system access procedure\n         * has completed\n         * @event ProtectionEvents#KEY_SYSTEM_ACCESS_COMPLETE\n         */\n        this.KEY_SYSTEM_ACCESS_COMPLETE = 'public_keySystemAccessComplete';\n\n        /**\n         * Event ID for events delivered when a key system selection procedure\n         * completes\n         * @event ProtectionEvents#KEY_SYSTEM_SELECTED\n         */\n        this.KEY_SYSTEM_SELECTED = 'public_keySystemSelected';\n\n        /**\n         * Event ID for events delivered when a license request procedure\n         * has completed\n         * @event ProtectionEvents#LICENSE_REQUEST_COMPLETE\n         */\n        this.LICENSE_REQUEST_COMPLETE = 'public_licenseRequestComplete';\n\n        /**\n         * Sending a license rquest\n         * @event ProtectionEvents#LICENSE_REQUEST_SENDING\n         */\n        this.LICENSE_REQUEST_SENDING = 'public_licenseRequestSending';\n\n        /**\n         * Event ID for needkey/encrypted events\n         * @ignore\n         */\n        this.NEED_KEY = 'needkey';\n\n        /**\n         * Event ID for events delivered when the Protection system is detected and created.\n         * @event ProtectionEvents#PROTECTION_CREATED\n         */\n        this.PROTECTION_CREATED = 'public_protectioncreated';\n\n        /**\n         * Event ID for events delivered when the Protection system is destroyed.\n         * @event ProtectionEvents#PROTECTION_DESTROYED\n         */\n        this.PROTECTION_DESTROYED = 'public_protectiondestroyed';\n\n        /**\n         * Event ID for events delivered when a new server certificate has\n         * been delivered to the CDM\n         * @ignore\n         */\n        this.SERVER_CERTIFICATE_UPDATED = 'serverCertificateUpdated';\n\n        /**\n         * Event ID for events delivered when the process of shutting down\n         * a protection set has completed\n         * @ignore\n         */\n        this.TEARDOWN_COMPLETE = 'protectionTeardownComplete';\n\n        /**\n         * Event ID for events delivered when a HTMLMediaElement has been\n         * associated with the protection set\n         * @ignore\n         */\n        this.VIDEO_ELEMENT_SELECTED = 'videoElementSelected';\n\n        /**\n         * Triggered when the key session has been updated successfully\n         * @ignore\n         */\n        this.KEY_SESSION_UPDATED = 'public_keySessionUpdated';\n    }\n}\n\nlet protectionEvents = new ProtectionEvents();\nexport default protectionEvents;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport ErrorsBase from '../../../core/errors/ErrorsBase.js';\n\n/**\n * @class\n */\nclass ProtectionErrors extends ErrorsBase {\n    constructor() {\n        super();\n\n        /**\n         *  Generid key Error code\n         */\n        this.MEDIA_KEYERR_CODE = 100;\n        /**\n         *  Error code returned by keyerror api for ProtectionModel_01b\n         */\n        this.MEDIA_KEYERR_UNKNOWN_CODE = 101;\n        /**\n         *  Error code returned by keyerror api for ProtectionModel_01b\n         */\n        this.MEDIA_KEYERR_CLIENT_CODE = 102;\n        /**\n         *  Error code returned by keyerror api for ProtectionModel_01b\n         */\n        this.MEDIA_KEYERR_SERVICE_CODE = 103;\n        /**\n         *  Error code returned by keyerror api for ProtectionModel_01b\n         */\n        this.MEDIA_KEYERR_OUTPUT_CODE = 104;\n        /**\n         *  Error code returned by keyerror api for ProtectionModel_01b\n         */\n        this.MEDIA_KEYERR_HARDWARECHANGE_CODE = 105;\n        /**\n         *  Error code returned by keyerror api for ProtectionModel_01b\n         */\n        this.MEDIA_KEYERR_DOMAIN_CODE = 106;\n\n        /**\n         *  Error code returned when an error occured in keymessage event for ProtectionModel_01b\n         */\n        this.MEDIA_KEY_MESSAGE_ERROR_CODE = 107;\n        /**\n         *  Error code returned when challenge is invalid in keymessage event (event triggered by CDM)\n         */\n        this.MEDIA_KEY_MESSAGE_NO_CHALLENGE_ERROR_CODE = 108;\n        /**\n         *  Error code returned when License server certificate has not been successfully updated\n         */\n        this.SERVER_CERTIFICATE_UPDATED_ERROR_CODE = 109;\n        /**\n         *  Error code returned when license validity has expired\n         */\n        this.KEY_STATUS_CHANGED_EXPIRED_ERROR_CODE = 110;\n        /**\n         *  Error code returned when no licenser url is defined\n         */\n        this.MEDIA_KEY_MESSAGE_NO_LICENSE_SERVER_URL_ERROR_CODE = 111;\n        /**\n         *  Error code returned when key system access is denied\n         */\n        this.KEY_SYSTEM_ACCESS_DENIED_ERROR_CODE = 112;\n        /**\n         *  Error code returned when key session has not been successfully created\n         */\n        this.KEY_SESSION_CREATED_ERROR_CODE = 113;\n        /**\n         *  Error code returned when license request failed after a keymessage event has been triggered\n         */\n        this.MEDIA_KEY_MESSAGE_LICENSER_ERROR_CODE = 114;\n\n        this.MEDIA_KEYERR_UNKNOWN_MESSAGE = 'An unspecified error occurred. This value is used for errors that don\\'t match any of the other codes.';\n        this.MEDIA_KEYERR_CLIENT_MESSAGE = 'The Key System could not be installed or updated.';\n        this.MEDIA_KEYERR_SERVICE_MESSAGE = 'The message passed into update indicated an error from the license service.';\n        this.MEDIA_KEYERR_OUTPUT_MESSAGE = 'There is no available output device with the required characteristics for the content protection system.';\n        this.MEDIA_KEYERR_HARDWARECHANGE_MESSAGE = 'A hardware configuration change caused a content protection error.';\n        this.MEDIA_KEYERR_DOMAIN_MESSAGE = 'An error occurred in a multi-device domain licensing configuration. The most common error is a failure to join the domain.';\n        this.MEDIA_KEY_MESSAGE_ERROR_MESSAGE = 'Multiple key sessions were creates with a user-agent that does not support sessionIDs!! Unpredictable behavior ahead!';\n        this.MEDIA_KEY_MESSAGE_NO_CHALLENGE_ERROR_MESSAGE = 'DRM: Empty key message from CDM';\n        this.SERVER_CERTIFICATE_UPDATED_ERROR_MESSAGE = 'Error updating server certificate -- ';\n        this.KEY_STATUS_CHANGED_EXPIRED_ERROR_MESSAGE = 'DRM: KeyStatusChange error! -- License has expired';\n        this.MEDIA_KEY_MESSAGE_NO_LICENSE_SERVER_URL_ERROR_MESSAGE = 'DRM: No license server URL specified!';\n        this.KEY_SYSTEM_ACCESS_DENIED_ERROR_MESSAGE = 'DRM: KeySystem Access Denied! -- ';\n        this.KEY_SESSION_CREATED_ERROR_MESSAGE = 'DRM: unable to create session! --';\n        this.MEDIA_KEY_MESSAGE_LICENSER_ERROR_MESSAGE = 'DRM: licenser error! --';\n    }\n}\n\nlet protectionErrors = new ProtectionErrors();\nexport default protectionErrors;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass DashJSError {\n    constructor(code, message, data) {\n        this.code = code || null;\n        this.message = message || null;\n        this.data = data || null;\n    }\n}\n\nexport default DashJSError;", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @classdesc This Object holds reference to the HTTPRequest for manifest, fragment and xlink loading.\n * Members which are not defined in ISO23009-1 Annex D should be prefixed by a _ so that they are ignored\n * by Metrics Reporting code.\n * @ignore\n */\nclass HTTPRequest {\n    /**\n     * @class\n     */\n    constructor() {\n        /**\n         * Identifier of the TCP connection on which the HTTP request was sent.\n         * @public\n         */\n        this.tcpid = null;\n        /**\n         * This is an optional parameter and should not be included in HTTP request/response transactions for progressive download.\n         * The type of the request:\n         * - MPD\n         * - XLink expansion\n         * - Initialization Fragment\n         * - Index Fragment\n         * - Media Fragment\n         * - Bitstream Switching Fragment\n         * - other\n         * @public\n         */\n        this.type = null;\n        /**\n         * The original URL (before any redirects or failures)\n         * @public\n         */\n        this.url = null;\n        /**\n         * The actual URL requested, if different from above\n         * @public\n         */\n        this.actualurl = null;\n        /**\n         * The contents of the byte-range-spec part of the HTTP Range header.\n         * @public\n         */\n        this.range = null;\n        /**\n         * Real-Time | The real time at which the request was sent.\n         * @public\n         */\n        this.trequest = null;\n        /**\n         * Real-Time | The real time at which the first byte of the response was received.\n         * @public\n         */\n        this.tresponse = null;\n        /**\n         * The HTTP response code.\n         * @public\n         */\n        this.responsecode = null;\n        /**\n         * The duration of the throughput trace intervals (ms), for successful requests only.\n         * @public\n         */\n        this.interval = null;\n        /**\n         * Throughput traces, for successful requests only.\n         * @public\n         */\n        this.trace = [];\n        /**\n         * The CMSD static and dynamic values retrieved from CMSD response headers.\n         * @public\n         */\n        this.cmsd = null;\n\n        /**\n         * Type of stream (\"audio\" | \"video\" etc..)\n         * @public\n         */\n        this._stream = null;\n        /**\n         * Real-Time | The real time at which the request finished.\n         * @public\n         */\n        this._tfinish = null;\n        /**\n         * The duration of the media requests, if available, in seconds.\n         * @public\n         */\n        this._mediaduration = null;\n        /**\n         * all the response headers from request.\n         * @public\n         */\n        this._responseHeaders = null;\n        /**\n         * The selected service location for the request. string.\n         * @public\n         */\n        this._serviceLocation = null;\n        /**\n         * The type of the loader that was used. Distinguish between fetch loader and xhr loader\n         */\n        this._fileLoaderType = null;\n        /**\n         * The values derived from the ResourceTimingAPI.\n         */\n        this._resourceTimingValues = null;\n    }\n}\n\n/**\n * @classdesc This Object holds reference to the progress of the HTTPRequest.\n * @ignore\n */\nclass HTTPRequestTrace {\n    /**\n     * @class\n     */\n    constructor() {\n        /**\n         * Real-Time | Measurement stream start.\n         * @public\n         */\n        this.s = null;\n        /**\n         * Measurement stream duration (ms).\n         * @public\n         */\n        this.d = null;\n        /**\n         * List of integers counting the bytes received in each trace interval within the measurement stream.\n         * @public\n         */\n        this.b = [];\n    }\n}\n\nHTTPRequest.GET = 'GET';\nHTTPRequest.HEAD = 'HEAD';\nHTTPRequest.MPD_TYPE = 'MPD';\nHTTPRequest.XLINK_EXPANSION_TYPE = 'XLinkExpansion';\nHTTPRequest.INIT_SEGMENT_TYPE = 'InitializationSegment';\nHTTPRequest.INDEX_SEGMENT_TYPE = 'IndexSegment';\nHTTPRequest.MEDIA_SEGMENT_TYPE = 'MediaSegment';\nHTTPRequest.BITSTREAM_SWITCHING_SEGMENT_TYPE = 'BitstreamSwitchingSegment';\nHTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE = 'FragmentInfoSegment';\nHTTPRequest.DVB_REPORTING_TYPE = 'DVBReporting';\nHTTPRequest.LICENSE = 'license';\nHTTPRequest.CONTENT_STEERING_TYPE = 'ContentSteering';\nHTTPRequest.OTHER_TYPE = 'other';\n\nexport {HTTPRequest, HTTPRequestTrace};\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "__webpack_require__.amdO = {};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport DashConstants from '../../dash/constants/DashConstants.js';\nimport ProtectionConstants from '../constants/ProtectionConstants.js';\n\nconst LICENSE_SERVER_MANIFEST_CONFIGURATIONS = {\n    prefixes: ['clearkey', 'dashif', 'ck']\n};\n\n/**\n * @class\n * @ignore\n */\nclass CommonEncryption {\n    /**\n     * Find and return the ContentProtection element in the given array\n     * that indicates support for MP4 Common Encryption\n     *\n     * @param {Array} cpArray array of content protection elements\n     * @returns {Object|null} the Common Encryption content protection element or\n     * null if one was not found\n     */\n    static findMp4ProtectionElement(cpArray) {\n        let retVal = null;\n        for (let i = 0; i < cpArray.length; ++i) {\n            let cp = cpArray[i];\n            if (cp.schemeIdUri && cp.schemeIdUri.toLowerCase() === DashConstants.MP4_PROTECTION_SCHEME && cp.value &&\n                (cp.value.toLowerCase() === ProtectionConstants.ENCRYPTION_SCHEME_CENC || cp.value.toLowerCase() === ProtectionConstants.ENCRYPTION_SCHEME_CBCS)) {\n                retVal = cp;\n            }\n        }\n        return retVal;\n    }\n\n    /**\n     * Returns just the data portion of a single PSSH\n     *\n     * @param {ArrayBuffer} pssh - the PSSH\n     * @return {ArrayBuffer} data portion of the PSSH\n     */\n    static getPSSHData(pssh) {\n        let offset = 8; // Box size and type fields\n        let view = new DataView(pssh);\n\n        // Read version\n        let version = view.getUint8(offset);\n\n        offset += 20; // Version (1), flags (3), system ID (16)\n\n        if (version > 0) {\n            offset += 4 + (16 * view.getUint32(offset)); // Key ID count (4) and All key IDs (16*count)\n        }\n\n        offset += 4; // Data size\n        return pssh.slice(offset);\n    }\n\n    /**\n     * Returns the PSSH associated with the given key system from the concatenated\n     * list of PSSH boxes in the given initData\n     *\n     * @param {KeySystem} keySystem the desired\n     * key system\n     * @param {ArrayBuffer} initData 'cenc' initialization data.  Concatenated list of PSSH.\n     * @returns {ArrayBuffer|null} The PSSH box data corresponding to the given key system, null if not found\n     * or null if a valid association could not be found.\n     */\n    static getPSSHForKeySystem(keySystem, initData) {\n        let psshList = CommonEncryption.parsePSSHList(initData);\n        if (keySystem && psshList.hasOwnProperty(keySystem.uuid.toLowerCase())) {\n            return psshList[keySystem.uuid.toLowerCase()];\n        }\n        return null;\n    }\n\n    /**\n     * Parse a standard common encryption PSSH which contains a simple\n     * base64-encoding of the init data\n     *\n     * @param {Object} cpData the ContentProtection element\n     * @param {BASE64} BASE64 reference\n     * @returns {ArrayBuffer|null} the init data or null if not found\n     */\n    static parseInitDataFromContentProtection(cpData, BASE64) {\n        if ('pssh' in cpData && cpData.pssh) {\n\n            // Remove whitespaces and newlines from pssh text\n            cpData.pssh.__text = cpData.pssh.__text.replace(/\\r?\\n|\\r/g, '').replace(/\\s+/g, '');\n\n            return BASE64.decodeArray(cpData.pssh.__text).buffer;\n        }\n        return null;\n    }\n\n    /**\n     * Parses list of PSSH boxes into keysystem-specific PSSH data\n     *\n     * @param {ArrayBuffer} data - the concatenated list of PSSH boxes as provided by\n     * CDM as initialization data when CommonEncryption content is detected\n     * @returns {Object|Array} an object that has a property named according to each of\n     * the detected key system UUIDs (e.g. 00000000-0000-0000-0000-0000000000)\n     * and a ArrayBuffer (the entire PSSH box) as the property value\n     */\n    static parsePSSHList(data) {\n\n        if (data === null || data === undefined) {\n            return [];\n        }\n\n        let dv = new DataView(data.buffer || data); // data.buffer first for Uint8Array support\n        let done = false;\n        let pssh = {};\n\n        // TODO: Need to check every data read for end of buffer\n        let byteCursor = 0;\n        while (!done) {\n\n            let size,\n                nextBox,\n                version,\n                systemID;\n            let boxStart = byteCursor;\n\n            if (byteCursor >= dv.buffer.byteLength) {\n                break;\n            }\n\n            /* Box size */\n            size = dv.getUint32(byteCursor);\n            nextBox = byteCursor + size;\n            byteCursor += 4;\n\n            /* Verify PSSH */\n            if (dv.getUint32(byteCursor) !== 0x70737368) {\n                byteCursor = nextBox;\n                continue;\n            }\n            byteCursor += 4;\n\n            /* Version must be 0 or 1 */\n            version = dv.getUint8(byteCursor);\n            if (version !== 0 && version !== 1) {\n                byteCursor = nextBox;\n                continue;\n            }\n            byteCursor++;\n\n            byteCursor += 3; /* skip flags */\n\n            // 16-byte UUID/SystemID\n            systemID = '';\n            let i, val;\n            for (i = 0; i < 4; i++) {\n                val = dv.getUint8(byteCursor + i).toString(16);\n                systemID += (val.length === 1) ? '0' + val : val;\n            }\n            byteCursor += 4;\n            systemID += '-';\n            for (i = 0; i < 2; i++) {\n                val = dv.getUint8(byteCursor + i).toString(16);\n                systemID += (val.length === 1) ? '0' + val : val;\n            }\n            byteCursor += 2;\n            systemID += '-';\n            for (i = 0; i < 2; i++) {\n                val = dv.getUint8(byteCursor + i).toString(16);\n                systemID += (val.length === 1) ? '0' + val : val;\n            }\n            byteCursor += 2;\n            systemID += '-';\n            for (i = 0; i < 2; i++) {\n                val = dv.getUint8(byteCursor + i).toString(16);\n                systemID += (val.length === 1) ? '0' + val : val;\n            }\n            byteCursor += 2;\n            systemID += '-';\n            for (i = 0; i < 6; i++) {\n                val = dv.getUint8(byteCursor + i).toString(16);\n                systemID += (val.length === 1) ? '0' + val : val;\n            }\n            byteCursor += 6;\n\n            systemID = systemID.toLowerCase();\n\n            /* PSSH Data Size */\n            byteCursor += 4;\n\n            /* PSSH Data */\n            pssh[systemID] = dv.buffer.slice(boxStart, nextBox);\n            byteCursor = nextBox;\n        }\n\n        return pssh;\n    }\n\n    static getLicenseServerUrlFromMediaInfo(mediaInfoArr, schemeIdUri) {\n        try {\n\n            if (!mediaInfoArr || mediaInfoArr.length === 0) {\n                return null;\n            }\n\n            let i = 0;\n            let licenseServer = null;\n\n            while (i < mediaInfoArr.length && !licenseServer) {\n                const mediaInfo = mediaInfoArr[i];\n\n                if (mediaInfo && mediaInfo.contentProtection && mediaInfo.contentProtection.length > 0) {\n                    const targetProtectionData = mediaInfo.contentProtection.filter((cp) => {\n                        return cp.schemeIdUri && cp.schemeIdUri === schemeIdUri;\n                    });\n\n                    if (targetProtectionData && targetProtectionData.length > 0) {\n                        let j = 0;\n                        while (j < targetProtectionData.length && !licenseServer) {\n                            const contentProtection = targetProtectionData[j];\n                            if (contentProtection.laUrl\n                                && contentProtection.laUrl.__prefix\n                                && LICENSE_SERVER_MANIFEST_CONFIGURATIONS.prefixes.includes(contentProtection.laUrl.__prefix)\n                                && contentProtection.laUrl.__text) {\n                                licenseServer = contentProtection.laUrl.__text;\n                            }\n                            j += 1;\n                        }\n                    }\n                }\n                i += 1;\n            }\n            return licenseServer;\n        } catch (e) {\n            return null;\n        }\n    }\n\n    static hexKidToBufferSource(hexKid) {\n        const cleanedHexKid = hexKid.replace(/-/g, '');\n\n        const typedArray = new Uint8Array(cleanedHexKid.match(/[\\da-f]{2}/gi).map(function (h) {\n            return parseInt(h, 16)\n        }))\n\n        return typedArray.buffer\n    }\n}\n\nexport default CommonEncryption;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @classdesc A media capability\n * @ignore\n */\nclass MediaCapability {\n    /**\n     * @param {string} contentType MIME type and codecs (RFC6386)\n     * @param {string} robustness\n     * @class\n     * @ignore\n     */\n    constructor(contentType, robustness) {\n        this.contentType = contentType;\n        this.robustness = robustness;\n    }\n}\n\nexport default MediaCapability;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport ProtectionConstants from '../../constants/ProtectionConstants.js';\n\n/**\n * @classdesc Represents a set of configurations that describe the capabilities desired for\n *  support by a given CDM\n * @ignore\n */\nclass KeySystemConfiguration {\n    /**\n     * @param {Array.<MediaCapability>} audioCapabilities array of\n     * desired audio capabilities.  Higher preference capabilities should be placed earlier\n     * in the array.\n     * @param {Array.<MediaCapability>} videoCapabilities array of\n     * desired video capabilities.  Higher preference capabilities should be placed earlier\n     * in the array.\n     * @param {string} distinctiveIdentifier desired use of distinctive identifiers.\n     * One of \"required\", \"optional\", or \"not-allowed\"\n     * @param {string} persistentState desired support for persistent storage of\n     * key systems.  One of \"required\", \"optional\", or \"not-allowed\"\n     * @param {Array.<string>} sessionTypes List of session types that must\n     * be supported by the key system\n     * @class\n     */\n    constructor(audioCapabilities, videoCapabilities, distinctiveIdentifier, persistentState, sessionTypes, initDataTypes) {\n        this.initDataTypes = initDataTypes && initDataTypes.length > 0 ? initDataTypes : [ProtectionConstants.INITIALIZATION_DATA_TYPE_CENC];\n        if (audioCapabilities && audioCapabilities.length) {\n            this.audioCapabilities = audioCapabilities;\n        }\n        if (videoCapabilities && videoCapabilities.length) {\n            this.videoCapabilities = videoCapabilities;\n        }\n        this.distinctiveIdentifier = distinctiveIdentifier;\n        this.persistentState = persistentState;\n        this.sessionTypes = sessionTypes;\n    }\n}\n\nexport default KeySystemConfiguration;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @classdesc Defines a license request\n * @ignore\n */\nclass LicenseRequest {\n    /**\n     * Defines a license request\n     *\n     * @class\n     */\n    constructor(url, method, responseType, headers, withCredentials, messageType, sessionId, data) {\n\n        /**\n         * The license request url\n         */\n        this.url = url;\n\n        /**\n         * The HTTP method\n         */\n        this.method = method;\n\n        /**\n         * The HTTP response type\n         */\n        this.responseType = responseType;\n\n        /**\n         * The HTP request headers\n         */\n        this.headers = headers;\n\n        /**\n         * Wether request is done using credentials (cross-site cookies)\n         */\n        this.withCredentials = withCredentials;\n\n        /**\n         * The license request message type (see https://www.w3.org/TR/encrypted-media/#dom-mediakeymessagetype)\n         */\n        this.messageType = messageType;\n\n        /**\n         * The corresponding EME session ID\n         */\n        this.sessionId = sessionId;\n\n        /**\n         * The license request data\n         */\n        this.data = data;\n    }\n}\n\nexport default LicenseRequest;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @classdesc Defines a license response\n */\nclass LicenseResponse {\n    /**\n     * Defines a license response\n     *\n     * @class\n     * @ignore\n     */\n    constructor(url, headers, data) {\n\n        /**\n         * The url that was loaded, that can be redirected from original request url\n         */\n        this.url = url;\n\n        /**\n         * The HTP response headers\n         */\n        this.headers = headers;\n\n        /**\n         * The license response data\n         */\n        this.data = data;\n    }\n}\n\nexport default LicenseResponse;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport CommonEncryption from '../CommonEncryption.js';\nimport MediaCapability from '../vo/MediaCapability.js';\nimport KeySystemConfiguration from '../vo/KeySystemConfiguration.js';\nimport ProtectionErrors from '../errors/ProtectionErrors.js';\nimport DashJSError from '../../vo/DashJSError.js';\nimport LicenseRequest from '../vo/LicenseRequest.js';\nimport LicenseResponse from '../vo/LicenseResponse.js';\nimport {HTTPRequest} from '../../vo/metrics/HTTPRequest.js';\nimport Utils from '../../../core/Utils.js';\nimport Constants from '../../constants/Constants.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\nimport ProtectionConstants from '../../constants/ProtectionConstants.js';\n\nconst NEEDKEY_BEFORE_INITIALIZE_RETRIES = 5;\nconst NEEDKEY_BEFORE_INITIALIZE_TIMEOUT = 500;\n\nconst LICENSE_SERVER_REQUEST_RETRIES = 3;\nconst LICENSE_SERVER_REQUEST_RETRY_INTERVAL = 1000;\nconst LICENSE_SERVER_REQUEST_DEFAULT_TIMEOUT = 8000;\n\n/**\n * @module ProtectionController\n * @description Provides access to media protection information and functionality.  Each\n * ProtectionController manages a single {@link MediaPlayer.models.ProtectionModel}\n * which encapsulates a set of protection information (EME APIs, selected key system,\n * key sessions).  The APIs of ProtectionController mostly align with the latest EME\n * APIs.  Key system selection is mostly automated when combined with app-overrideable\n * functionality provided in {@link ProtectionKeyController}.\n * @todo ProtectionController does almost all of its tasks automatically after init() is\n * called.  Applications might want more control over this process and want to go through\n * each step manually (key system selection, session creation, session maintenance).\n * This module can be accessed using the MediaPlayer API getProtectionController()\n * @param {Object} config\n */\n\nfunction ProtectionController(config) {\n\n    config = config || {};\n    const BASE64 = config.BASE64;\n    const cmcdModel = config.cmcdModel;\n    const constants = config.constants;\n    const customParametersModel = config.customParametersModel;\n    const debug = config.debug;\n    const eventBus = config.eventBus;\n    const events = config.events;\n    const protectionKeyController = config.protectionKeyController;\n    const settings = config.settings;\n    let protectionModel = config.protectionModel;\n    let needkeyRetries = [];\n\n    let applicationProvidedProtectionData,\n        instance,\n        keyStatusMap,\n        keySystemSelectionInProgress,\n        licenseRequestRetryTimeout,\n        licenseXhrRequest,\n        logger,\n        mediaInfoArr,\n        pendingMediaTypesToHandle,\n        robustnessLevel,\n        selectedKeySystem,\n        sessionType;\n\n    function setup() {\n        logger = debug.getLogger(instance);\n        pendingMediaTypesToHandle = [];\n        mediaInfoArr = [];\n        sessionType = 'temporary';\n        robustnessLevel = '';\n        licenseXhrRequest = null;\n        licenseRequestRetryTimeout = null;\n        keyStatusMap = new Map();\n        eventBus.on(events.INTERNAL_KEY_MESSAGE, _onKeyMessage, instance);\n    }\n\n    function _checkConfig() {\n        if (!eventBus || !eventBus.hasOwnProperty('on') || !protectionKeyController || !protectionKeyController.hasOwnProperty('getSupportedKeySystemMetadataFromContentProtection')) {\n            throw new Error('Missing config parameter(s)');\n        }\n    }\n\n    /**\n     * Initialize this protection system for a given media type.\n     *\n     * @param {StreamInfo} [mediaInfo] Media information\n     * @memberof module:ProtectionController\n     * @instance\n     */\n    function initializeForMedia(mediaInfo) {\n        // Not checking here if a session for similar KS/KID combination is already created\n        // because still don't know which keysystem will be selected.\n        // Once Keysystem is selected and before creating the session, we will do that check\n        // so we create the strictly necessary DRM sessions\n        if (!mediaInfo) {\n            throw new Error('mediaInfo can not be null or undefined');\n        }\n\n        _checkConfig();\n        mediaInfoArr.push(mediaInfo);\n    }\n\n    /**\n     * Once all mediaInfo objects have been added to our mediaInfoArray we can select a key system or check if the kid has changed and we need to trigger a new license request\n     * @memberof module:ProtectionController\n     * @instance\n     */\n    function handleKeySystemFromManifest() {\n        if (!mediaInfoArr || mediaInfoArr.length === 0) {\n            return;\n        }\n\n        let supportedKeySystemsMetadata = [];\n        mediaInfoArr.forEach((mediaInfo) => {\n            const keySystemsMetadata = protectionKeyController.getSupportedKeySystemMetadataFromContentProtection(mediaInfo.contentProtection, applicationProvidedProtectionData, sessionType);\n            // We assume that the same key systems are signaled for each AS. We can use the first entry we find\n            if (keySystemsMetadata.length > 0) {\n                if (supportedKeySystemsMetadata.length === 0) {\n                    supportedKeySystemsMetadata = keySystemsMetadata;\n                }\n                // Save config for creating key session once we selected a key system\n                pendingMediaTypesToHandle.push(keySystemsMetadata);\n            }\n        })\n\n        if (supportedKeySystemsMetadata && supportedKeySystemsMetadata.length > 0) {\n            _selectKeySystemOrUpdateKeySessions(supportedKeySystemsMetadata, true);\n        }\n    }\n\n    /**\n     * Selects a key system if we dont have any one yet. Otherwise we use the existing key system and trigger a new license request if the initdata has changed\n     * @param {array} supportedKeySystemsMetadata\n     * @private\n     */\n    function _handleKeySystemFromPssh(supportedKeySystemsMetadata) {\n        pendingMediaTypesToHandle.push(supportedKeySystemsMetadata);\n        _selectKeySystemOrUpdateKeySessions(supportedKeySystemsMetadata, false);\n    }\n\n    /**\n     * Select the key system or update one of our existing key sessions\n     * @param {array} supportedKeySystemsMetadata\n     * @param {boolean} fromManifest\n     * @private\n     */\n    function _selectKeySystemOrUpdateKeySessions(supportedKeySystemsMetadata, fromManifest) {\n        // First time, so we need to select a key system\n        if (!selectedKeySystem && !keySystemSelectionInProgress) {\n            _selectInitialKeySystem(supportedKeySystemsMetadata, fromManifest);\n        }\n\n        // We already selected a key system. We only need to trigger a new license exchange if the init data has changed\n        else if (selectedKeySystem) {\n            _handlePendingMediaTypes();\n        }\n    }\n\n    /**\n     * We do not have a key system yet. Select one\n     * @param {array} supportedKeySystemsMetadata\n     * @param {boolean} fromManifest\n     * @private\n     */\n    function _selectInitialKeySystem(supportedKeySystemsMetadata, fromManifest) {\n        if (keySystemSelectionInProgress) {\n            return\n        }\n\n        keySystemSelectionInProgress = true;\n\n        // Reorder key systems according to priority order provided in protectionData\n        supportedKeySystemsMetadata = _sortKeySystemsByPriority(supportedKeySystemsMetadata)\n\n        // Add all key systems to our request list since we have yet to select a key system\n        const keySystemConfigurationsToRequest = _getKeySystemConfigurations(supportedKeySystemsMetadata);\n\n        let keySystemAccess;\n        protectionModel.requestKeySystemAccess(keySystemConfigurationsToRequest)\n            .then((event) => {\n                keySystemAccess = event.data;\n                return _onKeySystemAccessed(keySystemAccess);\n            })\n            .then((keySystem) => {\n                _onMediaKeysCreated(keySystem, keySystemAccess);\n            })\n            .catch((event) => {\n                _handleKeySystemSelectionError(event, fromManifest);\n            })\n\n    }\n\n    function _onKeySystemAccessed(keySystemAccess) {\n        let selectedSystemString = keySystemAccess && keySystemAccess.selectedSystemString ? keySystemAccess.selectedSystemString : keySystemAccess.keySystem.systemString;\n        logger.info('DRM: KeySystem Access Granted for system string (' + selectedSystemString + ')!  Selecting key system...');\n        return protectionModel.selectKeySystem(keySystemAccess);\n    }\n\n    function _onMediaKeysCreated(keySystem, keySystemAccess) {\n        selectedKeySystem = keySystem;\n        keySystemSelectionInProgress = false;\n\n        eventBus.trigger(events.KEY_SYSTEM_SELECTED, { data: keySystemAccess });\n\n        // Set server certificate from protData\n        const protData = _getProtDataForKeySystem(selectedKeySystem);\n        if (protData && protData.serverCertificate && protData.serverCertificate.length > 0) {\n            protectionModel.setServerCertificate(BASE64.decodeArray(protData.serverCertificate).buffer);\n        }\n\n        _handlePendingMediaTypes();\n    }\n\n    /**\n     * If we have already selected a key system we only need to create a new key session and issue a new license request if the init data has changed.\n     * @private\n     */\n    function _handlePendingMediaTypes() {\n        // Create key sessions for the different AdaptationSets\n        let ksIdx;\n        for (let i = 0; i < pendingMediaTypesToHandle.length; i++) {\n            for (ksIdx = 0; ksIdx < pendingMediaTypesToHandle[i].length; ksIdx++) {\n                if (selectedKeySystem === pendingMediaTypesToHandle[i][ksIdx].ks) {\n                    const keySystemMetadata = pendingMediaTypesToHandle[i][ksIdx]\n                    _loadOrCreateKeySession(keySystemMetadata)\n                    break;\n                }\n            }\n        }\n        pendingMediaTypesToHandle = [];\n    }\n\n    function _handleKeySystemSelectionError(event, fromManifest) {\n        selectedKeySystem = null;\n        keySystemSelectionInProgress = false;\n        if (!fromManifest) {\n            eventBus.trigger(events.KEY_SYSTEM_SELECTED, {\n                data: null,\n                error: new DashJSError(ProtectionErrors.KEY_SYSTEM_ACCESS_DENIED_ERROR_CODE, ProtectionErrors.KEY_SYSTEM_ACCESS_DENIED_ERROR_MESSAGE + 'Error selecting key system! -- ' + event.error)\n            });\n        }\n    }\n\n    function _sortKeySystemsByPriority(supportedKeySystems) {\n        return supportedKeySystems.sort((ksA, ksB) => {\n            let indexA = (applicationProvidedProtectionData && applicationProvidedProtectionData[ksA.ks.systemString] && applicationProvidedProtectionData[ksA.ks.systemString].priority >= 0) ? applicationProvidedProtectionData[ksA.ks.systemString].priority : supportedKeySystems.length;\n            let indexB = (applicationProvidedProtectionData && applicationProvidedProtectionData[ksB.ks.systemString] && applicationProvidedProtectionData[ksB.ks.systemString].priority >= 0) ? applicationProvidedProtectionData[ksB.ks.systemString].priority : supportedKeySystems.length;\n            return indexA - indexB;\n        });\n    }\n\n    function _getKeySystemConfigurations(supportedKeySystemsMetadata) {\n        const keySystemConfigurationsToRequest = [];\n        for (let i = 0; i < supportedKeySystemsMetadata.length; i++) {\n            const keySystemConfiguration = _getKeySystemConfiguration(supportedKeySystemsMetadata[i]);\n            keySystemConfigurationsToRequest.push({\n                ks: supportedKeySystemsMetadata[i].ks,\n                configs: [keySystemConfiguration],\n                protData: supportedKeySystemsMetadata[i].protData\n            });\n        }\n\n        return keySystemConfigurationsToRequest;\n    }\n\n    /**\n     * Returns an object corresponding to the EME MediaKeySystemConfiguration dictionary\n     * @param {object} keySystem\n     * @return {KeySystemConfiguration}\n     * @private\n     */\n    function _getKeySystemConfiguration(keySystemData) {\n        const protData = keySystemData.protData;\n        const audioCapabilities = [];\n        const videoCapabilities = [];\n        const initDataTypes = (protData && protData.initDataTypes && protData.initDataTypes.length > 0) ? protData.initDataTypes : [ProtectionConstants.INITIALIZATION_DATA_TYPE_CENC];\n        const audioRobustness = (protData && protData.audioRobustness && protData.audioRobustness.length > 0) ? protData.audioRobustness : robustnessLevel;\n        const videoRobustness = (protData && protData.videoRobustness && protData.videoRobustness.length > 0) ? protData.videoRobustness : robustnessLevel;\n        const ksSessionType = keySystemData.sessionType;\n        const distinctiveIdentifier = (protData && protData.distinctiveIdentifier) ? protData.distinctiveIdentifier : 'optional';\n        const persistentState = (protData && protData.persistentState) ? protData.persistentState : (ksSessionType === 'temporary') ? 'optional' : 'required';\n\n        mediaInfoArr.forEach((media) => {\n            if (media.type === constants.AUDIO) {\n                audioCapabilities.push(new MediaCapability(media.codec, audioRobustness));\n            } else if (media.type === constants.VIDEO) {\n                videoCapabilities.push(new MediaCapability(media.codec, videoRobustness));\n            }\n        });\n\n        return new KeySystemConfiguration(audioCapabilities, videoCapabilities, distinctiveIdentifier, persistentState, [ksSessionType], initDataTypes);\n    }\n\n    /**\n     * Loads an existing key session if we already have a session id. Otherwise we create a new key session\n     * @param {object} keySystemMetadata\n     * @private\n     */\n    function _loadOrCreateKeySession(keySystemMetadata) {\n        if (protectionKeyController.isClearKey(selectedKeySystem)) {\n            _handleClearkeySession(keySystemMetadata)\n        }\n\n        // Reuse existing KeySession\n        if (keySystemMetadata.sessionId) {\n            // Load MediaKeySession with sessionId\n            loadKeySession(keySystemMetadata);\n        }\n\n        // Create a new KeySession\n        else if (keySystemMetadata.initData !== null) {\n            // Create new MediaKeySession with initData\n            createKeySession(keySystemMetadata);\n        }\n    }\n\n    function _handleClearkeySession(keySystemMetadata) {\n        // For Clearkey: if parameters for generating init data was provided by the user, use them for generating\n        // initData and overwrite possible initData indicated in encrypted event (EME)\n        if (keySystemMetadata.protData && keySystemMetadata.protData.hasOwnProperty('clearkeys') && Object.keys(keySystemMetadata.protData.clearkeys).length !== 0) {\n            const initData = { kids: Object.keys(keySystemMetadata.protData.clearkeys) };\n            keySystemMetadata.initData = new TextEncoder().encode(JSON.stringify(initData));\n        }\n    }\n\n    /**\n     * Loads a key session with the given session ID from persistent storage.  This essentially creates a new key session\n     *\n     * @param {object} ksInfo\n     * @memberof module:ProtectionController\n     * @instance\n     * @fires ProtectionController#KeySessionCreated\n     * @ignore\n     */\n    function loadKeySession(keySystemMetadata) {\n        _checkConfig();\n        protectionModel.loadKeySession(keySystemMetadata);\n    }\n\n    /**\n     * Create a new key session associated with the given initialization data from the MPD or from the PSSH box in the media\n     * For the latest version of the EME a request is generated. Once this request is ready we get notified via the INTERNAL_KEY_MESSAGE event\n     * @param {ArrayBuffer} initData the initialization data\n     * @param {Uint8Array} cdmData the custom data to provide to licenser\n     * @memberof module:ProtectionController\n     * @instance\n     * @fires ProtectionController#KeySessionCreated\n     * @ignore\n     */\n    function createKeySession(keySystemMetadata) {\n\n        // Check for duplicate key id\n        if (keySystemMetadata && _doesSessionForKeyIdExists(keySystemMetadata.keyId)) {\n            return;\n        }\n\n        const initDataForKS = CommonEncryption.getPSSHForKeySystem(selectedKeySystem, keySystemMetadata ? keySystemMetadata.initData : null);\n        if (initDataForKS) {\n\n            // Check for duplicate initData\n            if (_isInitDataDuplicate(initDataForKS)) {\n                return;\n            }\n\n            try {\n                keySystemMetadata.initData = initDataForKS;\n                protectionModel.createKeySession(keySystemMetadata);\n            } catch (error) {\n                eventBus.trigger(events.KEY_SESSION_CREATED, {\n                    data: null,\n                    error: new DashJSError(ProtectionErrors.KEY_SESSION_CREATED_ERROR_CODE, ProtectionErrors.KEY_SESSION_CREATED_ERROR_MESSAGE + error.message)\n                });\n            }\n        } else if (keySystemMetadata && keySystemMetadata.initData) {\n            protectionModel.createKeySession(keySystemMetadata);\n        } else {\n            eventBus.trigger(events.KEY_SESSION_CREATED, {\n                data: null,\n                error: new DashJSError(ProtectionErrors.KEY_SESSION_CREATED_ERROR_CODE, ProtectionErrors.KEY_SESSION_CREATED_ERROR_MESSAGE + 'Selected key system is ' + (selectedKeySystem ? selectedKeySystem.systemString : null) + '.  needkey/encrypted event contains no initData corresponding to that key system!')\n            });\n        }\n    }\n\n    /**\n     * Returns the protectionData for a specific keysystem as specified by the application.\n     * @param {object} keySystem\n     * @return {object | null}\n     * @private\n     */\n    function _getProtDataForKeySystem(keySystem) {\n        if (keySystem) {\n            const keySystemString = keySystem.systemString;\n\n            if (applicationProvidedProtectionData) {\n                return (keySystemString in applicationProvidedProtectionData) ? applicationProvidedProtectionData[keySystemString] : null;\n            }\n        }\n        return null;\n    }\n\n    /**\n     * Removes all entries from the mediaInfoArr\n     */\n    function clearMediaInfoArray() {\n        mediaInfoArr = [];\n    }\n\n    /**\n     * Returns a set of supported key systems and CENC initialization data\n     * from the given array of ContentProtection elements.  Only\n     * key systems that are supported by this player will be returned.\n     * Key systems are returned in priority order (highest first).\n     *\n     * @param {Array.<Object>} cps - array of content protection elements parsed\n     * from the manifest\n     * @returns {Array.<Object>} array of objects indicating which supported key\n     * systems were found.  Empty array is returned if no\n     * supported key systems were found\n     * @memberof module:ProtectionKeyController\n     * @instance\n     * @ignore\n     */\n    function getSupportedKeySystemMetadataFromContentProtection(cps) {\n        _checkConfig();\n        return protectionKeyController.getSupportedKeySystemMetadataFromContentProtection(cps, applicationProvidedProtectionData, sessionType);\n    }\n\n    /**\n     * Checks if a session has already created for the provided key id\n     * @param {string} keyId\n     * @return {boolean}\n     * @private\n     */\n    function _doesSessionForKeyIdExists(keyId) {\n        if (!keyId) {\n            return false;\n        }\n\n        try {\n            const sessions = protectionModel.getSessionTokens();\n            for (let i = 0; i < sessions.length; i++) {\n                if (sessions[i].getKeyId() === keyId) {\n                    return true;\n                }\n            }\n            return false;\n        } catch (e) {\n            return false;\n        }\n    }\n\n    /**\n     * Checks if the provided init data is equal to one of the existing init data values\n     * @param {any} initDataForKS\n     * @return {boolean}\n     * @private\n     */\n    function _isInitDataDuplicate(initDataForKS) {\n\n        if (!initDataForKS) {\n            return false;\n        }\n\n        try {\n            const currentInitData = protectionModel.getAllInitData();\n            for (let i = 0; i < currentInitData.length; i++) {\n                if (protectionKeyController.initDataEquals(initDataForKS, currentInitData[i])) {\n                    logger.debug('DRM: Ignoring initData because we have already seen it!');\n                    return true;\n                }\n            }\n\n            return false;\n        } catch (e) {\n            return false;\n        }\n    }\n\n    /**\n     * Removes the given key session from persistent storage and closes the session\n     * as if {@link ProtectionController#closeKeySession}\n     * was called\n     *\n     * @param {SessionToken} sessionToken the session\n     * token\n     * @memberof module:ProtectionController\n     * @instance\n     * @fires ProtectionController#KeySessionRemoved\n     * @fires ProtectionController#KeySessionClosed\n     * @ignore\n     */\n    function removeKeySession(sessionToken) {\n        _checkConfig();\n        protectionModel.removeKeySession(sessionToken);\n    }\n\n    /**\n     * Closes the key session and releases all associated decryption keys.  These\n     * keys will no longer be available for decrypting media\n     *\n     * @param {SessionToken} sessionToken the session\n     * token\n     * @memberof module:ProtectionController\n     * @instance\n     * @fires ProtectionController#KeySessionClosed\n     * @ignore\n     */\n    function closeKeySession(sessionToken) {\n        _checkConfig();\n        protectionModel.closeKeySession(sessionToken);\n    }\n\n    /**\n     * Sets a server certificate for use by the CDM when signing key messages\n     * intended for a particular license server.  This will fire\n     * an error event if a key system has not yet been selected.\n     *\n     * @param {ArrayBuffer} serverCertificate a CDM-specific license server\n     * certificate\n     * @memberof module:ProtectionController\n     * @instance\n     * @fires ProtectionController#ServerCertificateUpdated\n     */\n    function setServerCertificate(serverCertificate) {\n        _checkConfig();\n        protectionModel.setServerCertificate(serverCertificate);\n    }\n\n    /**\n     * Associate this protection system with the given HTMLMediaElement.  This\n     * causes the system to register for needkey/encrypted events from the given\n     * element and provides a destination for setting of MediaKeys\n     *\n     * @param {HTMLMediaElement} element the media element to which the protection\n     * system should be associated\n     * @memberof module:ProtectionController\n     * @instance\n     */\n    function setMediaElement(element) {\n        _checkConfig();\n        if (element) {\n            protectionModel.setMediaElement(element);\n            eventBus.on(events.NEED_KEY, _onNeedKey, instance);\n        } else if (element === null) {\n            protectionModel.setMediaElement(element);\n            eventBus.off(events.NEED_KEY, _onNeedKey, instance);\n        }\n    }\n\n    /**\n     * Sets the session type to use when creating key sessions.  Either \"temporary\" or\n     * \"persistent-license\".  Default is \"temporary\".\n     *\n     * @param {string} value the session type\n     * @memberof module:ProtectionController\n     * @instance\n     */\n    function setSessionType(value) {\n        sessionType = value;\n    }\n\n    /**\n     * Sets the robustness level for video and audio capabilities. Optional to remove Chrome warnings.\n     * Possible values are SW_SECURE_CRYPTO, SW_SECURE_DECODE, HW_SECURE_CRYPTO, HW_SECURE_CRYPTO, HW_SECURE_DECODE, HW_SECURE_ALL.\n     *\n     * @param {string} level the robustness level\n     * @memberof module:ProtectionController\n     * @instance\n     */\n    function setRobustnessLevel(level) {\n        robustnessLevel = level;\n    }\n\n    /**\n     * Attach KeySystem-specific data to use for license acquisition with EME\n     *\n     * @param {Object} data an object containing property names corresponding to\n     * key system name strings (e.g. \"org.w3.clearkey\") and associated values\n     * being instances of {@link ProtectionData}\n     * @memberof module:ProtectionController\n     * @instance\n     * @ignore\n     */\n    function setProtectionData(data) {\n        applicationProvidedProtectionData = data;\n        protectionKeyController.setProtectionData(data);\n    }\n\n    /**\n     * Stop method is called when current playback is stopped/resetted.\n     *\n     * @memberof module:ProtectionController\n     * @instance\n     */\n    function stop() {\n        _abortLicenseRequest();\n        if (protectionModel) {\n            protectionModel.stop();\n        }\n    }\n\n    /**\n     * Destroys all protection data associated with this protection set.  This includes\n     * deleting all key sessions. In the case of persistent key sessions, the sessions\n     * will simply be unloaded and not deleted.  Additionally, if this protection set is\n     * associated with a HTMLMediaElement, it will be detached from that element.\n     *\n     * @memberof module:ProtectionController\n     * @instance\n     * @ignore\n     */\n    function reset() {\n        eventBus.off(events.INTERNAL_KEY_MESSAGE, _onKeyMessage, instance);\n\n        _checkConfig();\n\n        _abortLicenseRequest();\n\n        setMediaElement(null);\n\n        selectedKeySystem = null;\n        keySystemSelectionInProgress = false;\n\n        keyStatusMap = new Map();\n\n        if (protectionModel) {\n            protectionModel.reset();\n            protectionModel = null;\n        }\n\n        needkeyRetries.forEach(retryTimeout => clearTimeout(retryTimeout));\n        needkeyRetries = [];\n\n        mediaInfoArr = [];\n        pendingMediaTypesToHandle = [];\n    }\n\n    /**\n     * Event handler for the key message event. Once we have a key message we can issue a license request\n     * @param {object} e\n     * @private\n     */\n    function _onKeyMessage(e) {\n        logger.debug('DRM: onKeyMessage');\n\n        // Dispatch event to applications indicating we received a key message\n        const keyMessage = e.data;\n        eventBus.trigger(events.KEY_MESSAGE, { data: keyMessage });\n        const messageType = (keyMessage.messageType) ? keyMessage.messageType : ProtectionConstants.MEDIA_KEY_MESSAGE_TYPES.LICENSE_REQUEST;\n        const message = keyMessage.message;\n        const sessionToken = keyMessage.sessionToken;\n        const protData = _getProtDataForKeySystem(selectedKeySystem);\n        const licenseServerModelInstance = protectionKeyController.getLicenseServerModelInstance(selectedKeySystem, protData, messageType);\n        const eventData = { sessionToken: sessionToken, messageType: messageType };\n\n        // Ensure message from CDM is not empty\n        if (!message || message.byteLength === 0) {\n            _sendLicenseRequestCompleteEvent(eventData, new DashJSError(ProtectionErrors.MEDIA_KEY_MESSAGE_NO_CHALLENGE_ERROR_CODE, ProtectionErrors.MEDIA_KEY_MESSAGE_NO_CHALLENGE_ERROR_MESSAGE));\n            return;\n        }\n\n        // Message not destined for license server\n        if (!licenseServerModelInstance) {\n            logger.debug('DRM: License server request not required for this message (type = ' + e.data.messageType + ').  Session ID = ' + sessionToken.getSessionId());\n            _sendLicenseRequestCompleteEvent(eventData);\n            return;\n        }\n\n        // Perform any special handling for ClearKey\n        if (protectionKeyController.isClearKey(selectedKeySystem)) {\n            const clearkeys = protectionKeyController.processClearKeyLicenseRequest(selectedKeySystem, protData, message);\n            if (clearkeys && clearkeys.keyPairs && clearkeys.keyPairs.length > 0) {\n                logger.debug('DRM: ClearKey license request handled by application!');\n                _sendLicenseRequestCompleteEvent(eventData);\n                protectionModel.updateKeySession(sessionToken, clearkeys);\n                return;\n            }\n        }\n\n        // In all other cases we have to make a license request\n        _issueLicenseRequest(keyMessage, licenseServerModelInstance, protData);\n    }\n\n    /**\n     * Notify other classes that the license request was completed\n     * @param {object} data\n     * @param {object} error\n     * @private\n     */\n    function _sendLicenseRequestCompleteEvent(data, error = null) {\n        eventBus.trigger(events.LICENSE_REQUEST_COMPLETE, { data: data, error: error });\n    }\n\n    /**\n     * Start issuing a license request\n     * @param {object} keyMessage\n     * @param {object} licenseServerData\n     * @param {object} protData\n     * @private\n     */\n    function _issueLicenseRequest(keyMessage, licenseServerData, protData) {\n        const sessionToken = keyMessage.sessionToken;\n        const messageType = (keyMessage.messageType) ? keyMessage.messageType : ProtectionConstants.MEDIA_KEY_MESSAGE_TYPES.LICENSE_REQUEST;\n        const eventData = { sessionToken: sessionToken, messageType: messageType };\n        const keySystemString = selectedKeySystem ? selectedKeySystem.systemString : null;\n\n        // Determine license server URL\n        let url = _getLicenseServerUrl(protData, messageType, sessionToken, keyMessage, licenseServerData);\n\n        // Ensure valid license server URL\n        if (!url) {\n            _sendLicenseRequestCompleteEvent(eventData, new DashJSError(ProtectionErrors.MEDIA_KEY_MESSAGE_NO_LICENSE_SERVER_URL_ERROR_CODE, ProtectionErrors.MEDIA_KEY_MESSAGE_NO_LICENSE_SERVER_URL_ERROR_MESSAGE));\n            return;\n        }\n\n        // Set optional XMLHttpRequest headers from protection data and message\n        const reqHeaders = {};\n        let withCredentials = false;\n        if (protData) {\n            _updateHeaders(reqHeaders, protData.httpRequestHeaders);\n        }\n        const message = keyMessage.message;\n        const headersFromMessage = selectedKeySystem.getRequestHeadersFromMessage(message);\n        _updateHeaders(reqHeaders, headersFromMessage);\n\n        Object.keys(reqHeaders).forEach((key) => {\n            if ('authorization' === key.toLowerCase()) {\n                withCredentials = true;\n            }\n        });\n\n        // Overwrite withCredentials property from protData if present\n        if (protData && typeof protData.withCredentials == 'boolean') {\n            withCredentials = protData.withCredentials;\n        }\n\n        const onLoad = function (xhr) {\n            if (!protectionModel) {\n                return;\n            }\n\n            if (xhr.status >= 200 && xhr.status <= 299) {\n                const responseHeaders = Utils.parseHttpHeaders(xhr.getAllResponseHeaders ? xhr.getAllResponseHeaders() : null);\n                let licenseResponse = new LicenseResponse(xhr.responseURL, responseHeaders, xhr.response);\n                const licenseResponseFilters = customParametersModel.getLicenseResponseFilters();\n                _applyFilters(licenseResponseFilters, licenseResponse)\n                    .then(() => {\n                        const licenseMessage = licenseServerData.getLicenseMessage(licenseResponse.data, keySystemString, messageType);\n                        if (licenseMessage !== null) {\n                            _sendLicenseRequestCompleteEvent(eventData);\n                            protectionModel.updateKeySession(sessionToken, licenseMessage);\n                        } else {\n                            _reportError(xhr, eventData, keySystemString, messageType, licenseServerData);\n                        }\n                    });\n            } else {\n                _reportError(xhr, eventData, keySystemString, messageType, licenseServerData);\n            }\n        };\n\n        const onAbort = function (xhr) {\n            _sendLicenseRequestCompleteEvent(eventData, new DashJSError(ProtectionErrors.MEDIA_KEY_MESSAGE_LICENSER_ERROR_CODE,\n                ProtectionErrors.MEDIA_KEY_MESSAGE_LICENSER_ERROR_MESSAGE + keySystemString + ' update, XHR aborted. status is \"' +\n                xhr.statusText + '\" (' + xhr.status + '), readyState is ' + xhr.readyState));\n        };\n\n        const onError = function (xhr) {\n            _sendLicenseRequestCompleteEvent(eventData, new DashJSError(ProtectionErrors.MEDIA_KEY_MESSAGE_LICENSER_ERROR_CODE,\n                ProtectionErrors.MEDIA_KEY_MESSAGE_LICENSER_ERROR_MESSAGE + keySystemString + ' update, XHR error. status is \"' +\n                xhr.statusText + '\" (' + xhr.status + '), readyState is ' + xhr.readyState));\n        };\n\n        const reqPayload = selectedKeySystem.getLicenseRequestFromMessage(message);\n        const reqMethod = licenseServerData.getHTTPMethod(messageType);\n        const responseType = licenseServerData.getResponseType(keySystemString, messageType);\n        const timeout = protData && !isNaN(protData.httpTimeout) ? protData.httpTimeout : LICENSE_SERVER_REQUEST_DEFAULT_TIMEOUT;\n        const sessionId = sessionToken.getSessionId() || null;\n\n        let licenseRequest = new LicenseRequest(url, reqMethod, responseType, reqHeaders, withCredentials, messageType, sessionId, reqPayload);\n        const retryAttempts = !isNaN(settings.get().streaming.retryAttempts[HTTPRequest.LICENSE]) ? settings.get().streaming.retryAttempts[HTTPRequest.LICENSE] : LICENSE_SERVER_REQUEST_RETRIES;\n        const licenseRequestFilters = customParametersModel.getLicenseRequestFilters();\n        _applyFilters(licenseRequestFilters, licenseRequest)\n            .then(() => {\n                _doLicenseRequest(licenseRequest, retryAttempts, timeout, onLoad, onAbort, onError);\n            });\n    }\n\n    /**\n     * Implement license requests with a retry mechanism to avoid temporary network issues to affect playback experience\n     * @param {object} request\n     * @param {number} retriesCount\n     * @param {number} timeout\n     * @param {function} onLoad\n     * @param {function} onAbort\n     * @param {function} onError\n     * @private\n     */\n    function _doLicenseRequest(request, retriesCount, timeout, onLoad, onAbort, onError) {\n        const xhr = new XMLHttpRequest();\n        const cmcdParameters = cmcdModel.getCmcdParametersFromManifest();\n\n        if (cmcdModel.isCmcdEnabled()) {\n            const cmcdMode = cmcdParameters.mode ? cmcdParameters.mode : settings.get().streaming.cmcd.mode;\n            if (cmcdMode === Constants.CMCD_MODE_QUERY) {\n                const cmcdParams = cmcdModel.getQueryParameter({\n                    url: request.url,\n                    type: HTTPRequest.LICENSE\n                });\n\n                if (cmcdParams) {\n                    request.url = Utils.addAdditionalQueryParameterToUrl(request.url, [cmcdParams]);\n                }\n            }\n        }\n\n        xhr.open(request.method, request.url, true);\n        xhr.responseType = request.responseType;\n        xhr.withCredentials = request.withCredentials;\n        if (timeout > 0) {\n            xhr.timeout = timeout;\n        }\n        for (const key in request.headers) {\n            xhr.setRequestHeader(key, request.headers[key]);\n        }\n\n        if (cmcdModel.isCmcdEnabled()) {\n            const cmcdMode = cmcdParameters.mode ? cmcdParameters.mode : settings.get().streaming.cmcd.mode;\n            if (cmcdMode === Constants.CMCD_MODE_HEADER) {\n                const cmcdHeaders = cmcdModel.getHeaderParameters({\n                    url: request.url,\n                    type: HTTPRequest.LICENSE\n                });\n\n                if (cmcdHeaders) {\n                    for (const header in cmcdHeaders) {\n                        let value = cmcdHeaders[header];\n                        if (value) {\n                            xhr.setRequestHeader(header, value);\n                        }\n                    }\n                }\n            }\n        }\n\n        const _retryRequest = function () {\n            // fail silently and retry\n            retriesCount--;\n            const retryInterval = !isNaN(settings.get().streaming.retryIntervals[HTTPRequest.LICENSE]) ? settings.get().streaming.retryIntervals[HTTPRequest.LICENSE] : LICENSE_SERVER_REQUEST_RETRY_INTERVAL;\n            licenseRequestRetryTimeout = setTimeout(function () {\n                _doLicenseRequest(request, retriesCount, timeout, onLoad, onAbort, onError);\n            }, retryInterval);\n        };\n\n        xhr.onload = function () {\n            licenseXhrRequest = null;\n            if (this.status >= 200 && this.status <= 299 || retriesCount <= 0) {\n                onLoad(this);\n            } else {\n                logger.warn('License request failed (' + this.status + '). Retrying it... Pending retries: ' + retriesCount);\n                _retryRequest();\n            }\n        };\n\n        xhr.ontimeout = xhr.onerror = function () {\n            licenseXhrRequest = null;\n            if (retriesCount <= 0) {\n                onError(this);\n            } else {\n                logger.warn('License request network request failed . Retrying it... Pending retries: ' + retriesCount);\n                _retryRequest();\n            }\n        };\n\n        xhr.onabort = function () {\n            onAbort(this);\n        };\n\n        // deprecated, to be removed\n        eventBus.trigger(events.LICENSE_REQUEST_SENDING, {\n            url: request.url,\n            headers: request.headers,\n            payload: request.data,\n            sessionId: request.sessionId\n        });\n\n        licenseXhrRequest = xhr;\n        xhr.send(request.data);\n    }\n\n    /**\n     * Aborts license request\n     * @private\n     */\n    function _abortLicenseRequest() {\n        if (licenseXhrRequest) {\n            licenseXhrRequest.onloadend = licenseXhrRequest.onerror = licenseXhrRequest.onprogress = undefined; //Ignore events from aborted requests.\n            licenseXhrRequest.abort();\n            licenseXhrRequest = null;\n        }\n\n        if (licenseRequestRetryTimeout) {\n            clearTimeout(licenseRequestRetryTimeout);\n            licenseRequestRetryTimeout = null;\n        }\n    }\n\n    /**\n     * Returns the url of the license server\n     * @param {object} protData\n     * @param {string} messageType\n     * @param {object} sessionToken\n     * @param {object} keyMessage\n     * @param {object} licenseServerData\n     * @return {*}\n     * @private\n     */\n    function _getLicenseServerUrl(protData, messageType, sessionToken, keyMessage, licenseServerData) {\n        let url = null;\n        const message = keyMessage.message;\n\n        // Check if the url is defined by the application\n        if (protData && protData.serverURL) {\n            const serverURL = protData.serverURL;\n            if (typeof serverURL === 'string' && serverURL !== '') {\n                url = serverURL;\n            } else if (typeof serverURL === 'object' && serverURL.hasOwnProperty(messageType)) {\n                url = serverURL[messageType];\n            }\n        }\n\n        // This is the old way of providing the url\n        else if (protData && protData.laURL && protData.laURL !== '') {\n            url = protData.laURL;\n        }\n\n        // No url provided by the app. Check the manifest and the pssh\n        else {\n            // Check for url defined in the manifest\n            url = CommonEncryption.getLicenseServerUrlFromMediaInfo(mediaInfoArr, selectedKeySystem.schemeIdURI);\n\n            // In case we are not using Clearky we can still get a url from the pssh.\n            if (!url && !protectionKeyController.isClearKey(selectedKeySystem)) {\n                const psshData = CommonEncryption.getPSSHData(sessionToken.initData);\n                url = selectedKeySystem.getLicenseServerURLFromInitData(psshData);\n\n                // Still no url, check the keymessage\n                if (!url) {\n                    url = keyMessage.laURL;\n                }\n            }\n        }\n        // Possibly update or override the URL based on the message\n        url = licenseServerData.getServerURLFromMessage(url, message, messageType);\n\n        return url;\n    }\n\n    /**\n     * Add new headers to the existing ones\n     * @param {array} reqHeaders\n     * @param {object} headers\n     * @private\n     */\n    function _updateHeaders(reqHeaders, headers) {\n        if (headers) {\n            for (const key in headers) {\n                reqHeaders[key] = headers[key];\n            }\n        }\n    }\n\n    /**\n     * Reports an error that might have occured during the license request\n     * @param {object} xhr\n     * @param {object} eventData\n     * @param {string} keySystemString\n     * @param {string} messageType\n     * @param {object} licenseServerData\n     * @private\n     */\n    function _reportError(xhr, eventData, keySystemString, messageType, licenseServerData) {\n        let errorMsg = 'NONE';\n        let data = null;\n\n        if (xhr.response) {\n            errorMsg = licenseServerData.getErrorResponse(xhr.response, keySystemString, messageType);\n            data = {\n                serverResponse: xhr.response || null,\n                responseCode: xhr.status || null,\n                responseText: xhr.statusText || null\n            }\n        }\n\n        _sendLicenseRequestCompleteEvent(eventData, new DashJSError(ProtectionErrors.MEDIA_KEY_MESSAGE_LICENSER_ERROR_CODE,\n            ProtectionErrors.MEDIA_KEY_MESSAGE_LICENSER_ERROR_MESSAGE + keySystemString + ' update, XHR complete. status is \"' +\n            xhr.statusText + '\" (' + xhr.status + '), readyState is ' + xhr.readyState + '.  Response is ' + errorMsg,\n            data\n        ));\n    }\n\n    /**\n     * Applies custom filters defined by the application\n     * @param {array} filters\n     * @param {object} param\n     * @return {Promise<void>|*}\n     * @private\n     */\n    function _applyFilters(filters, param) {\n        if (!filters) {\n            return Promise.resolve();\n        }\n        return filters.reduce((prev, next) => {\n            return prev.then(() => {\n                return next(param);\n            });\n        }, Promise.resolve());\n    }\n\n    /**\n     * Event handler for \"needkey\" and \"encrypted\" events\n     * @param {object} event\n     * @param {number} retry\n     * @private\n     */\n    function _onNeedKey(event, retry) {\n        if (settings.get().streaming.protection.ignoreEmeEncryptedEvent) {\n            return\n        }\n\n        logger.debug('DRM: onNeedKey');\n\n        // Ignore non-cenc initData\n        if (event.key.initDataType !== ProtectionConstants.INITIALIZATION_DATA_TYPE_CENC) {\n            logger.warn('DRM:  Only \\'cenc\\' initData is supported!  Ignoring initData of type: ' + event.key.initDataType);\n            return;\n        }\n\n        if (mediaInfoArr.length === 0) {\n            logger.warn('DRM: onNeedKey called before initializeForMedia, wait until initialized');\n            retry = typeof retry === 'undefined' ? 1 : retry + 1;\n            if (retry < NEEDKEY_BEFORE_INITIALIZE_RETRIES) {\n                needkeyRetries.push(setTimeout(() => {\n                    _onNeedKey(event, retry);\n                }, NEEDKEY_BEFORE_INITIALIZE_TIMEOUT));\n                return;\n            }\n        }\n\n        // Some browsers return initData as Uint8Array (IE), some as ArrayBuffer (Chrome).\n        // Convert to ArrayBuffer\n        let abInitData = event.key.initData;\n        if (ArrayBuffer.isView(abInitData)) {\n            abInitData = abInitData.buffer;\n        }\n\n        // If key system has already been selected and initData already seen, then do nothing\n        if (selectedKeySystem) {\n            const initDataForKS = CommonEncryption.getPSSHForKeySystem(selectedKeySystem, abInitData);\n            if (initDataForKS) {\n                // Check for duplicate initData\n                if (_isInitDataDuplicate(initDataForKS)) {\n                    return;\n                }\n            }\n        }\n\n        logger.debug('DRM: initData:', String.fromCharCode.apply(null, new Uint8Array(abInitData)));\n\n        const supportedKeySystemsMetadata = protectionKeyController.getSupportedKeySystemMetadataFromSegmentPssh(abInitData, applicationProvidedProtectionData, sessionType);\n        if (supportedKeySystemsMetadata.length === 0) {\n            logger.debug('DRM: Received needkey event with initData, but we don\\'t support any of the key systems!');\n            return;\n        }\n\n        _handleKeySystemFromPssh(supportedKeySystemsMetadata);\n    }\n\n    /**\n     * Returns all available key systems\n     * @return {array}\n     */\n    function getKeySystems() {\n        return protectionKeyController ? protectionKeyController.getKeySystems() : [];\n    }\n\n    /**\n     * Sets all available key systems\n     * @param {array} keySystems\n     */\n    function setKeySystems(keySystems) {\n        if (protectionKeyController) {\n            protectionKeyController.setKeySystems(keySystems);\n        }\n    }\n\n    function updateKeyStatusesMap(e) {\n        try {\n            if (!e || !e.sessionToken || !e.parsedKeyStatuses) {\n                return\n            }\n\n            e.sessionToken.hasTriggeredKeyStatusMapUpdate = true;\n            const parsedKeyStatuses = e.parsedKeyStatuses;\n            const ua = Utils.parseUserAgent();\n            const isEdgeBrowser = ua && ua.browser && ua.browser.name && ua.browser.name.toLowerCase() === 'edge';\n            parsedKeyStatuses.forEach((keyStatus) => {\n                if (isEdgeBrowser\n                    && selectedKeySystem.uuid === ProtectionConstants.PLAYREADY_UUID\n                    && keyStatus.keyId && keyStatus.keyId.byteLength === 16) {\n                    _handlePlayreadyKeyId(keyStatus.keyId);\n                }\n\n                const keyIdInHex = Utils.bufferSourceToHex(keyStatus.keyId).slice(0, 32);\n                if (keyIdInHex && keyIdInHex !== '') {\n                    keyStatusMap.set(keyIdInHex, keyStatus.status);\n                }\n            })\n            eventBus.trigger(events.KEY_STATUSES_MAP_UPDATED, { keyStatusMap });\n        } catch (e) {\n            logger.error(e);\n        }\n    }\n\n    function _handlePlayreadyKeyId(keyId) {\n        const dataView = Utils.bufferSourceToDataView(keyId);\n        const part0 = dataView.getUint32(0, /* LE= */ true);\n        const part1 = dataView.getUint16(4, /* LE= */ true);\n        const part2 = dataView.getUint16(6, /* LE= */ true);\n        // Write it back in big-endian:\n        dataView.setUint32(0, part0, /* BE= */ false);\n        dataView.setUint16(4, part1, /* BE= */ false);\n        dataView.setUint16(6, part2, /* BE= */ false);\n    }\n\n    function areKeyIdsUsable(normalizedKeyIds) {\n        try {\n            if (!_shouldCheckKeyStatusMap(normalizedKeyIds, keyStatusMap)) {\n                return true;\n            }\n\n            return [...normalizedKeyIds].some((normalizedKeyId) => {\n                const keyStatus = keyStatusMap.get(normalizedKeyId);\n                return keyStatus && keyStatus !== ProtectionConstants.MEDIA_KEY_STATUSES.INTERNAL_ERROR && keyStatus !== ProtectionConstants.MEDIA_KEY_STATUSES.OUTPUT_RESTRICTED;\n            });\n        } catch (error) {\n            logger.error(error);\n            return true\n        }\n    }\n\n    function areKeyIdsExpired(normalizedKeyIds) {\n        try {\n            if (!_shouldCheckKeyStatusMap(normalizedKeyIds, keyStatusMap)) {\n                return false;\n            }\n\n            return [...normalizedKeyIds].every((normalizedKeyId) => {\n                const keyStatus = keyStatusMap.get(normalizedKeyId);\n                return keyStatus === ProtectionConstants.MEDIA_KEY_STATUSES.EXPIRED;\n            })\n        } catch (error) {\n            logger.error(error);\n            return false\n        }\n    }\n\n    function _shouldCheckKeyStatusMap(normalizedKeyIds, keyStatusMap) {\n        if (normalizedKeyIds.size <= 0) {\n            return false;\n        }\n\n        const allHaveStatus = keyStatusMap.size > 0 && [...normalizedKeyIds].every((normalizedKeyId) => {\n            const keyStatus = keyStatusMap.get(normalizedKeyId);\n            return typeof keyStatus !== 'undefined' && keyStatus !== '';\n        });\n\n        if (allHaveStatus) {\n            return true\n        }\n\n        const sessionTokens = protectionModel.getSessionTokens();\n\n        if (sessionTokens && sessionTokens.length > 0) {\n            const targetSessionTokens = sessionTokens.filter((sessionToken) => {\n                return [...normalizedKeyIds].includes(sessionToken.normalizedKeyId);\n            })\n            const hasNotTriggeredKeyStatusMapUpdate = targetSessionTokens.some((sessionToken) => {\n                return !sessionToken.hasTriggeredKeyStatusMapUpdate;\n            })\n            if (hasNotTriggeredKeyStatusMapUpdate || targetSessionTokens.length === 0) {\n                return false;\n            }\n        }\n        return !settings.get().streaming.protection.ignoreKeyStatuses && normalizedKeyIds && normalizedKeyIds.size > 0 && keyStatusMap && keyStatusMap.size > 0\n    }\n\n    instance = {\n        areKeyIdsExpired,\n        areKeyIdsUsable,\n        clearMediaInfoArray,\n        closeKeySession,\n        createKeySession,\n        getKeySystems,\n        getSupportedKeySystemMetadataFromContentProtection,\n        handleKeySystemFromManifest,\n        initializeForMedia,\n        loadKeySession,\n        removeKeySession,\n        reset,\n        setKeySystems,\n        setMediaElement,\n        setProtectionData,\n        setRobustnessLevel,\n        setServerCertificate,\n        setSessionType,\n        stop,\n        updateKeyStatusesMap\n    };\n\n    setup();\n    return instance;\n}\n\nProtectionController.__dashjs_factory_name = 'ProtectionController';\nexport default FactoryMaker.getClassFactory(ProtectionController);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @classdesc Represents a 128-bit keyID and 128-bit encryption key\n * @ignore\n */\nclass KeyPair {\n    /**\n     * @param {string} keyID 128-bit key ID, base64 encoded, with no padding\n     * @param {string} key 128-bit encryption key, base64 encoded, with no padding\n     * @class\n     * @ignore\n     */\n    constructor(keyID, key) {\n        this.keyID = keyID;\n        this.key = key;\n    }\n}\n\nexport default KeyPair;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * @classdesc A collection of ClearKey encryption keys with an (optional) associated\n *  type\n * @ignore\n */\nclass ClearKeyKeySet {\n    /**\n     * @param {Array.<KeyPair>} keyPairs\n     * @param {string} type the type of keys in this set.  One of either 'persistent'\n     * or 'temporary'.  Can also be null or undefined.\n     * @class\n     * @ignore\n     */\n    constructor(keyPairs, type) {\n        if (type && type !== 'persistent' && type !== 'temporary') {\n            throw new Error('Invalid ClearKey key set type!  Must be one of \\'persistent\\' or \\'temporary\\'');\n        }\n        this.keyPairs = keyPairs;\n        this.type = type;\n    }\n\n    /**\n     * Convert this key set to its JSON Web Key (JWK) representation\n     *\n     * @return {ArrayBuffer} JWK object UTF-8 encoded as ArrayBuffer\n     */\n    toJWK() {\n        let i;\n        let numKeys = this.keyPairs.length;\n        let jwk = { keys: [] };\n\n        for (i = 0; i < numKeys; i++) {\n            let key = {\n                kty: 'oct',\n                alg: 'A128KW',\n                kid: this.keyPairs[i].keyID,\n                k: this.keyPairs[i].key\n            };\n            jwk.keys.push(key);\n        }\n        if (this.type) {\n            jwk.type = this.type;\n        }\n        let jwkString = JSON.stringify(jwk);\n        const len = jwkString.length;\n\n        // Convert JSON string to ArrayBuffer\n        let buf = new ArrayBuffer(len);\n        let bView = new Uint8Array(buf);\n        for (i = 0; i < len; i++) {\n            bView[i] = jwkString.charCodeAt(i);\n        }\n        return buf;\n    }\n}\n\nexport default ClearKeyKeySet;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport KeyPair from '../vo/KeyPair.js';\nimport ClearKeyKeySet from '../vo/ClearKeyKeySet.js';\nimport CommonEncryption from '../CommonEncryption.js';\nimport ProtectionConstants from '../../constants/ProtectionConstants.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nconst uuid = ProtectionConstants.CLEARKEY_UUID;\nconst systemString = ProtectionConstants.CLEARKEY_KEYSTEM_STRING;\nconst schemeIdURI = 'urn:uuid:' + uuid;\n\nfunction KeySystemClearKey(config) {\n\n    config = config || {};\n    let instance;\n    const BASE64 = config.BASE64;\n\n    /**\n     * Returns desired clearkeys (as specified in the CDM message) from protection data\n     *\n     * @param {ProtectionData} protectionData the protection data\n     * @param {ArrayBuffer} message the ClearKey CDM message\n     * @returns {ClearKeyKeySet} the key set or null if none found\n     * @throws {Error} if a keyID specified in the CDM message was not found in the\n     * protection data\n     * @memberof KeySystemClearKey\n     */\n    function getClearKeysFromProtectionData(protectionData, message) {\n        let clearkeySet = null;\n        if (protectionData) {\n            // ClearKey is the only system that does not require a license server URL, so we\n            // handle it here when keys are specified in protection data\n            const jsonMsg = JSON.parse(String.fromCharCode.apply(null, new Uint8Array(message)));\n            const keyPairs = [];\n            for (let i = 0; i < jsonMsg.kids.length; i++) {\n                const clearkeyID = jsonMsg.kids[i];\n                const clearkey = (protectionData.clearkeys && protectionData.clearkeys.hasOwnProperty(clearkeyID)) ? protectionData.clearkeys[clearkeyID] : null;\n                if (!clearkey) {\n                    throw new Error('DRM: ClearKey keyID (' + clearkeyID + ') is not known!');\n                }\n                // KeyIDs from CDM are not base64 padded.  Keys may or may not be padded\n                keyPairs.push(new KeyPair(clearkeyID, clearkey));\n            }\n            clearkeySet = new ClearKeyKeySet(keyPairs);\n        }\n        return clearkeySet;\n    }\n\n    function getInitData(cp, cencContentProtection) {\n        try {\n            let initData = CommonEncryption.parseInitDataFromContentProtection(cp, BASE64);\n\n            if (!initData && cencContentProtection) {\n                const cencDefaultKid = cencDefaultKidToBase64Representation(cencContentProtection.cencDefaultKid);\n                const data = { kids: [cencDefaultKid] };\n                initData = new TextEncoder().encode(JSON.stringify(data));\n            }\n\n            return initData;\n        } catch (e) {\n            return null;\n        }\n    }\n\n    function cencDefaultKidToBase64Representation(cencDefaultKid) {\n        try {\n            let kid = cencDefaultKid.replace(/-/g, '');\n            kid = btoa(kid.match(/\\w{2}/g).map((a) => {\n                return String.fromCharCode(parseInt(a, 16));\n            }).join(''));\n            return kid.replace(/=/g, '')\n                .replace(/\\//g, '_')\n                .replace(/\\+/g, '-');\n        } catch (e) {\n            return null;\n        }\n    }\n\n    function getRequestHeadersFromMessage(/*message*/) {\n        // Set content type to application/json by default\n        return {\n            'Content-Type': 'application/json'\n        };\n    }\n\n    function getLicenseRequestFromMessage(message) {\n        return JSON.stringify(JSON.parse(String.fromCharCode.apply(null, new Uint8Array(message))));\n    }\n\n    function getLicenseServerURLFromInitData(/*initData*/) {\n        return null;\n    }\n\n    function getCDMData(/*cdmData*/) {\n        return null;\n    }\n\n    instance = {\n        uuid,\n        schemeIdURI,\n        systemString,\n        getInitData,\n        getRequestHeadersFromMessage,\n        getLicenseRequestFromMessage,\n        getLicenseServerURLFromInitData,\n        getCDMData,\n        getClearKeysFromProtectionData\n    };\n\n    return instance;\n}\n\nKeySystemClearKey.__dashjs_factory_name = 'KeySystemClearKey';\nexport default FactoryMaker.getSingletonFactory(KeySystemClearKey);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport KeyPair from '../vo/KeyPair.js';\nimport ClearKeyKeySet from '../vo/ClearKeyKeySet.js';\nimport CommonEncryption from '../CommonEncryption.js';\nimport ProtectionConstants from '../../constants/ProtectionConstants.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nconst uuid = ProtectionConstants.W3C_CLEARKEY_UUID;\nconst systemString = ProtectionConstants.CLEARKEY_KEYSTEM_STRING;\nconst schemeIdURI = 'urn:uuid:' + uuid;\n\nfunction KeySystemW3CClearKey(config) {\n    let instance;\n    const BASE64 = config.BASE64;\n    const logger = config.debug.getLogger(instance);\n    /**\n     * Returns desired clearkeys (as specified in the CDM message) from protection data\n     *\n     * @param {ProtectionDataSet} protectionData the protection data\n     * @param {ArrayBuffer} message the ClearKey CDM message\n     * @returns {ClearKeyKeySet} the key set or null if none found\n     * @throws {Error} if a keyID specified in the CDM message was not found in the\n     * protection data\n     * @memberof KeySystemClearKey\n     */\n    function getClearKeysFromProtectionData(protectionData, message) {\n        let clearkeySet = null;\n        if (protectionData) {\n            // ClearKey is the only system that does not require a license server URL, so we\n            // handle it here when keys are specified in protection data\n            const jsonMsg = JSON.parse(String.fromCharCode.apply(null, new Uint8Array(message)));\n            const keyPairs = [];\n            for (let i = 0; i < jsonMsg.kids.length; i++) {\n                const clearkeyID = jsonMsg.kids[i];\n                const clearkey = (protectionData.clearkeys && protectionData.clearkeys.hasOwnProperty(clearkeyID)) ? protectionData.clearkeys[clearkeyID] : null;\n                if (!clearkey) {\n                    throw new Error('DRM: ClearKey keyID (' + clearkeyID + ') is not known!');\n                }\n                // KeyIDs from CDM are not base64 padded.  Keys may or may not be padded\n                keyPairs.push(new KeyPair(clearkeyID, clearkey));\n            }\n            clearkeySet = new ClearKeyKeySet(keyPairs);\n\n            logger.warn('ClearKey schemeIdURI is using W3C Common PSSH systemID (1077efec-c0b2-4d02-ace3-3c1e52e2fb4b) in Content Protection. See DASH-IF IOP v4.1 section 7.6.2.4');\n        }\n        return clearkeySet;\n    }\n\n    function getInitData(cp) {\n        return CommonEncryption.parseInitDataFromContentProtection(cp, BASE64);\n    }\n\n    function getRequestHeadersFromMessage(/*message*/) {\n        return null;\n    }\n\n    function getLicenseRequestFromMessage(message) {\n        return new Uint8Array(message);\n    }\n\n    function getLicenseServerURLFromInitData(/*initData*/) {\n        return null;\n    }\n\n    function getCDMData(/*cdmData*/) {\n        return null;\n    }\n\n    instance = {\n        uuid: uuid,\n        schemeIdURI: schemeIdURI,\n        systemString: systemString,\n        getInitData: getInitData,\n        getRequestHeadersFromMessage: getRequestHeadersFromMessage,\n        getLicenseRequestFromMessage: getLicenseRequestFromMessage,\n        getLicenseServerURLFromInitData: getLicenseServerURLFromInitData,\n        getCDMData: getCDMData,\n        getClearKeysFromProtectionData: getClearKeysFromProtectionData\n    };\n\n    return instance;\n}\n\nKeySystemW3CClearKey.__dashjs_factory_name = 'KeySystemW3CClearKey';\nexport default FactoryMaker.getSingletonFactory(KeySystemW3CClearKey);\n\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * Google Widevine DRM\n *\n * @class\n * @implements MediaPlayer.dependencies.protection.KeySystem\n */\n\nimport CommonEncryption from '../CommonEncryption.js';\nimport ProtectionConstants from '../../constants/ProtectionConstants.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nconst uuid = ProtectionConstants.WIDEVINE_UUID;\nconst systemString = ProtectionConstants.WIDEVINE_KEYSTEM_STRING;\nconst schemeIdURI = 'urn:uuid:' + uuid;\n\nfunction KeySystemWidevine(config) {\n\n    config = config || {};\n    let instance;\n    const BASE64 = config.BASE64;\n\n    function getInitData(cp) {\n        return CommonEncryption.parseInitDataFromContentProtection(cp, BASE64);\n    }\n\n    function getRequestHeadersFromMessage( /*message*/ ) {\n        return null;\n    }\n\n    function getLicenseRequestFromMessage(message) {\n        return new Uint8Array(message);\n    }\n\n    function getLicenseServerURLFromInitData( /*initData*/ ) {\n        return null;\n    }\n\n    function getCDMData(/*cdmData*/) {\n        return null;\n    }\n\n    instance = {\n        uuid,\n        schemeIdURI,\n        systemString,\n        getInitData,\n        getRequestHeadersFromMessage,\n        getLicenseRequestFromMessage,\n        getLicenseServerURLFromInitData,\n        getCDMData\n    };\n\n    return instance;\n}\n\nKeySystemWidevine.__dashjs_factory_name = 'KeySystemWidevine';\nexport default FactoryMaker.getSingletonFactory(KeySystemWidevine);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * Microsoft PlayReady DRM\n *\n * @class\n * @implements KeySystem\n */\nimport CommonEncryption from '../CommonEncryption.js';\nimport ProtectionConstants from '../../constants/ProtectionConstants.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nconst uuid = ProtectionConstants.PLAYREADY_UUID;\nconst systemString = ProtectionConstants.PLAYREADY_KEYSTEM_STRING;\nconst schemeIdURI = 'urn:uuid:' + uuid;\nconst PRCDMData = '<PlayReadyCDMData type=\"LicenseAcquisition\"><LicenseAcquisition version=\"1.0\" Proactive=\"false\"><CustomData encoding=\"base64encoded\">%CUSTOMDATA%</CustomData></LicenseAcquisition></PlayReadyCDMData>';\n\nfunction KeySystemPlayReady(config) {\n\n    config = config || {};\n    let instance;\n    let messageFormat = 'utf-16';\n    const BASE64 = config.BASE64;\n    const settings = config.settings;\n\n    function checkConfig() {\n        if (!BASE64 || !BASE64.hasOwnProperty('decodeArray') || !BASE64.hasOwnProperty('decodeArray')) {\n            throw new Error('Missing config parameter(s)');\n        }\n    }\n\n    function getRequestHeadersFromMessage(message) {\n        let msg,\n            xmlDoc;\n        const headers = {};\n        const parser = new DOMParser();\n\n        if (settings && settings.get().streaming.protection.detectPlayreadyMessageFormat) {\n            // If message format configured/defaulted to utf-16 AND number of bytes is odd, assume 'unwrapped' raw CDM message.\n            if (messageFormat === 'utf-16' && message && message.byteLength % 2 === 1) {\n                headers['Content-Type'] = 'text/xml; charset=utf-8';\n                return headers;\n            }\n        }\n\n        const dataview = (messageFormat === 'utf-16') ? new Uint16Array(message) : new Uint8Array(message);\n\n        msg = String.fromCharCode.apply(null, dataview);\n        xmlDoc = parser.parseFromString(msg, 'application/xml');\n\n        const headerNameList = xmlDoc.getElementsByTagName('name');\n        const headerValueList = xmlDoc.getElementsByTagName('value');\n        for (let i = 0; i < headerNameList.length; i++) {\n            headers[headerNameList[i].childNodes[0].nodeValue] = headerValueList[i].childNodes[0].nodeValue;\n        }\n        // Some versions of the PlayReady CDM return 'Content' instead of 'Content-Type'.\n        // this is NOT w3c conform and license servers may reject the request!\n        // -> rename it to proper w3c definition!\n        if (headers.hasOwnProperty('Content')) {\n            headers['Content-Type'] = headers.Content;\n            delete headers.Content;\n        }\n        // Set Content-Type header by default if not provided in the the CDM message (<PlayReadyKeyMessage/>)\n        // or if the message contains directly the challenge itself (Ex: LG SmartTVs)\n        if (!headers.hasOwnProperty('Content-Type')) {\n            headers['Content-Type'] = 'text/xml; charset=utf-8';\n        }\n        return headers;\n    }\n\n    function getLicenseRequestFromMessage(message) {\n        let licenseRequest = null;\n        const parser = new DOMParser();\n\n        if (settings && settings.get().streaming.protection.detectPlayreadyMessageFormat) {\n            // If message format configured/defaulted to utf-16 AND number of bytes is odd, assume 'unwrapped' raw CDM message.\n            if (messageFormat === 'utf-16' && message && message.byteLength % 2 === 1) {\n                return message;\n            }\n        }\n\n        const dataview = (messageFormat === 'utf-16') ? new Uint16Array(message) : new Uint8Array(message);\n\n        checkConfig();\n        const msg = String.fromCharCode.apply(null, dataview);\n        const xmlDoc = parser.parseFromString(msg, 'application/xml');\n\n        if (xmlDoc.getElementsByTagName('PlayReadyKeyMessage')[0]) {\n            const Challenge = xmlDoc.getElementsByTagName('Challenge')[0].childNodes[0].nodeValue;\n            if (Challenge) {\n                licenseRequest = BASE64.decode(Challenge);\n            }\n        } else {\n            // The message from CDM is not a wrapped message as on IE11 and Edge,\n            // thus it contains direclty the challenge itself\n            // (note that the xmlDoc at this point may be unreadable since it may have been interpreted as UTF-16)\n            return message;\n        }\n\n        return licenseRequest;\n    }\n\n    function getLicenseServerURLFromInitData(initData) {\n        if (initData) {\n            const data = new DataView(initData);\n            const numRecords = data.getUint16(4, true);\n            let offset = 6;\n            const parser = new DOMParser();\n\n            for (let i = 0; i < numRecords; i++) {\n                // Parse the PlayReady Record header\n                const recordType = data.getUint16(offset, true);\n                offset += 2;\n                const recordLength = data.getUint16(offset, true);\n                offset += 2;\n                if (recordType !== 0x0001) {\n                    offset += recordLength;\n                    continue;\n                }\n\n                const recordData = initData.slice(offset, offset + recordLength);\n                const record = String.fromCharCode.apply(null, new Uint16Array(recordData));\n                const xmlDoc = parser.parseFromString(record, 'application/xml');\n\n                // First try <LA_URL>\n                if (xmlDoc.getElementsByTagName('LA_URL')[0]) {\n                    const laurl = xmlDoc.getElementsByTagName('LA_URL')[0].childNodes[0].nodeValue;\n                    if (laurl) {\n                        return laurl;\n                    }\n                }\n\n                // Optionally, try <LUI_URL>\n                if (xmlDoc.getElementsByTagName('LUI_URL')[0]) {\n                    const luiurl = xmlDoc.getElementsByTagName('LUI_URL')[0].childNodes[0].nodeValue;\n                    if (luiurl) {\n                        return luiurl;\n                    }\n                }\n            }\n        }\n\n        return null;\n    }\n\n    function getInitData(cpData) {\n        // * desc@ getInitData\n        // *   generate PSSH data from PROHeader defined in MPD file\n        // *   PSSH format:\n        // *   size (4)\n        // *   box type(PSSH) (8)\n        // *   Protection SystemID (16)\n        // *   protection system data size (4) - length of decoded PROHeader\n        // *   decoded PROHeader data from MPD file\n        const PSSHBoxType = new Uint8Array([0x70, 0x73, 0x73, 0x68, 0x00, 0x00, 0x00, 0x00]); //'PSSH' 8 bytes\n        const playreadySystemID = new Uint8Array([0x9a, 0x04, 0xf0, 0x79, 0x98, 0x40, 0x42, 0x86, 0xab, 0x92, 0xe6, 0x5b, 0xe0, 0x88, 0x5f, 0x95]);\n\n        let byteCursor = 0;\n        let uint8arraydecodedPROHeader = null;\n\n        let PROSize,\n            PSSHSize,\n            PSSHBoxBuffer,\n            PSSHBox,\n            PSSHData;\n\n        checkConfig();\n        if (!cpData) {\n            return null;\n        }\n        // Handle common encryption PSSH\n        if ('pssh' in cpData && cpData.pssh) {\n            return CommonEncryption.parseInitDataFromContentProtection(cpData, BASE64);\n        }\n        // Handle native MS PlayReady ContentProtection elements\n        if ('pro' in cpData && cpData.pro) {\n            uint8arraydecodedPROHeader = BASE64.decodeArray(cpData.pro.__text);\n        } else if ('prheader' in cpData && cpData.prheader) {\n            uint8arraydecodedPROHeader = BASE64.decodeArray(cpData.prheader.__text);\n        } else {\n            return null;\n        }\n\n        PROSize = uint8arraydecodedPROHeader.length;\n        PSSHSize = 0x4 + PSSHBoxType.length + playreadySystemID.length + 0x4 + PROSize;\n\n        PSSHBoxBuffer = new ArrayBuffer(PSSHSize);\n\n        PSSHBox = new Uint8Array(PSSHBoxBuffer);\n        PSSHData = new DataView(PSSHBoxBuffer);\n\n        PSSHData.setUint32(byteCursor, PSSHSize);\n        byteCursor += 0x4;\n\n        PSSHBox.set(PSSHBoxType, byteCursor);\n        byteCursor += PSSHBoxType.length;\n\n        PSSHBox.set(playreadySystemID, byteCursor);\n        byteCursor += playreadySystemID.length;\n\n        PSSHData.setUint32(byteCursor, PROSize);\n        byteCursor += 0x4;\n\n        PSSHBox.set(uint8arraydecodedPROHeader, byteCursor);\n        byteCursor += PROSize;\n\n        return PSSHBox.buffer;\n    }\n\n    /**\n     * It seems that some PlayReady implementations return their XML-based CDM\n     * messages using UTF16, while others return them as UTF8.  Use this function\n     * to modify the message format to expect when parsing CDM messages.\n     *\n     * @param {string} format the expected message format.  Either \"utf-8\" or \"utf-16\".\n     * @throws {Error} Specified message format is not one of \"utf8\" or \"utf16\"\n     */\n    function setPlayReadyMessageFormat(format) {\n        if (format !== 'utf-8' && format !== 'utf-16') {\n            throw new Error('Specified message format is not one of \"utf-8\" or \"utf-16\"');\n        }\n        messageFormat = format;\n    }\n\n    /**\n     * Get Playready Custom data\n     */\n    function getCDMData(_cdmData) {\n        let customData,\n            cdmData,\n            cdmDataBytes,\n            i;\n\n        checkConfig();\n        if (!_cdmData) {\n            return null;\n        }\n\n        // Convert custom data into multibyte string\n        customData = [];\n        for (i = 0; i < _cdmData.length; ++i) {\n            customData.push(_cdmData.charCodeAt(i));\n            customData.push(0);\n        }\n        customData = String.fromCharCode.apply(null, customData);\n\n        // Encode in Base 64 the custom data string\n        customData = BASE64.encode(customData);\n\n        // Initialize CDM data with Base 64 encoded custom data\n        // (see https://msdn.microsoft.com/en-us/library/dn457361.aspx)\n        cdmData = PRCDMData.replace('%CUSTOMDATA%', customData);\n\n        // Convert CDM data into multibyte characters\n        cdmDataBytes = [];\n        for (i = 0; i < cdmData.length; ++i) {\n            cdmDataBytes.push(cdmData.charCodeAt(i));\n            cdmDataBytes.push(0);\n        }\n\n        return new Uint8Array(cdmDataBytes).buffer;\n    }\n\n    instance = {\n        uuid,\n        schemeIdURI,\n        systemString,\n        getInitData,\n        getRequestHeadersFromMessage,\n        getLicenseRequestFromMessage,\n        getLicenseServerURLFromInitData,\n        getCDMData,\n        setPlayReadyMessageFormat\n    };\n\n    return instance;\n}\n\nKeySystemPlayReady.__dashjs_factory_name = 'KeySystemPlayReady';\nexport default FactoryMaker.getSingletonFactory(KeySystemPlayReady);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * CastLabs DRMToday License Server implementation\n *\n * @implements LicenseServer\n * @class\n */\n\nimport ProtectionConstants from '../../constants/ProtectionConstants.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction DRMToday(config) {\n\n    config = config || {};\n    const BASE64 = config.BASE64;\n\n    const keySystems = {};\n    keySystems[ProtectionConstants.WIDEVINE_KEYSTEM_STRING] = {\n        responseType: 'json',\n        getLicenseMessage: function (response) {\n            return BASE64.decodeArray(response.license);\n        },\n        getErrorResponse: function (response) {\n            return response;\n        }\n    };\n    keySystems[ProtectionConstants.PLAYREADY_KEYSTEM_STRING] = {\n        responseType: 'arraybuffer',\n        getLicenseMessage: function (response) {\n            return response;\n        },\n        getErrorResponse: function (response) {\n            return String.fromCharCode.apply(null, new Uint8Array(response));\n        }\n    };\n\n    let instance;\n\n    function checkConfig() {\n        if (!BASE64 || !BASE64.hasOwnProperty('decodeArray')) {\n            throw new Error('Missing config parameter(s)');\n        }\n    }\n\n    function getServerURLFromMessage(url /*, message, messageType*/) {\n        return url;\n    }\n\n    function getHTTPMethod(/*messageType*/) {\n        return 'POST';\n    }\n\n    function getResponseType(keySystemStr/*, messageType*/) {\n        return keySystems[keySystemStr].responseType;\n    }\n\n    function getLicenseMessage(serverResponse, keySystemStr/*, messageType*/) {\n        checkConfig();\n        return keySystems[keySystemStr].getLicenseMessage(serverResponse);\n    }\n\n    function getErrorResponse(serverResponse, keySystemStr/*, messageType*/) {\n        return keySystems[keySystemStr].getErrorResponse(serverResponse);\n    }\n\n    instance = {\n        getServerURLFromMessage,\n        getHTTPMethod,\n        getResponseType,\n        getLicenseMessage,\n        getErrorResponse\n    };\n\n    return instance;\n}\n\nDRMToday.__dashjs_factory_name = 'DRMToday';\nexport default FactoryMaker.getSingletonFactory(DRMToday); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/* global escape: true */\n\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\n/**\n * Microsoft PlayReady Test License Server\n *\n * For testing content that uses the PlayReady test server at\n *\n * @implements LicenseServer\n * @class\n * @ignore\n */\n\nfunction PlayReady() {\n\n    let instance;\n\n    const soap = 'http://schemas.xmlsoap.org/soap/envelope/';\n\n    function uintToString(arrayBuffer) {\n        const encodedString = String.fromCharCode.apply(null, new Uint8Array(arrayBuffer));\n        const decodedString = decodeURIComponent(escape(encodedString));\n        return decodedString;\n    }\n\n    function parseServerResponse(serverResponse) {\n        if (window.DOMParser) {\n            const stringResponse = uintToString(serverResponse);\n            const parser = new window.DOMParser();\n            const xmlDoc = parser.parseFromString(stringResponse, 'text/xml');\n            const envelope = xmlDoc ? xmlDoc.getElementsByTagNameNS(soap, 'Envelope')[0] : null;\n            const body = envelope ? envelope.getElementsByTagNameNS(soap, 'Body')[0] : null;\n            const fault = body ? body.getElementsByTagNameNS(soap, 'Fault')[0] : null;\n\n            if (fault) {\n                return null;\n            }\n        }\n        return serverResponse;\n    }\n\n    function parseErrorResponse(serverResponse) {\n        let faultstring = '';\n        let statusCode = '';\n        let message = '';\n        let idStart = -1;\n        let idEnd = -1;\n\n        if (window.DOMParser) {\n            const stringResponse = uintToString(serverResponse);\n            const parser = new window.DOMParser();\n            const xmlDoc = parser.parseFromString(stringResponse, 'text/xml');\n            const envelope = xmlDoc ? xmlDoc.getElementsByTagNameNS(soap, 'Envelope')[0] : null;\n            const body = envelope ? envelope.getElementsByTagNameNS(soap, 'Body')[0] : null;\n            const fault = body ? body.getElementsByTagNameNS(soap, 'Fault')[0] : null;\n            const detail = fault ? fault.getElementsByTagName('detail')[0] : null;\n            const exception = detail ? detail.getElementsByTagName('Exception')[0] : null;\n            let node = null;\n\n            if (fault === null) {\n                return stringResponse;\n            }\n\n            node = fault.getElementsByTagName('faultstring')[0].firstChild;\n            faultstring = node ? node.nodeValue : null;\n\n            if (exception !== null) {\n                node = exception.getElementsByTagName('StatusCode')[0];\n                statusCode = node ? node.firstChild.nodeValue : null;\n                node = exception.getElementsByTagName('Message')[0];\n                message = node ? node.firstChild.nodeValue : null;\n                idStart = message ? message.lastIndexOf('[') + 1 : -1;\n                idEnd = message ? message.indexOf(']') : -1;\n                message = message ? message.substring(idStart, idEnd) : '';\n            }\n        }\n\n        let errorString = `code: ${statusCode}, name: ${faultstring}`;\n        if (message) {\n            errorString += `, message: ${message}`;\n        }\n\n        return errorString;\n    }\n\n    function getServerURLFromMessage(url /*, message, messageType*/) {\n        return url;\n    }\n\n    function getHTTPMethod(/*messageType*/) {\n        return 'POST';\n    }\n\n    function getResponseType(/*keySystemStr, messageType*/) {\n        return 'arraybuffer';\n    }\n\n    function getLicenseMessage(serverResponse/*, keySystemStr, messageType*/) {\n        return parseServerResponse.call(this, serverResponse);\n    }\n\n    function getErrorResponse(serverResponse/*, keySystemStr, messageType*/) {\n        return parseErrorResponse.call(this, serverResponse);\n    }\n\n    instance = {\n        getServerURLFromMessage,\n        getHTTPMethod,\n        getResponseType,\n        getLicenseMessage,\n        getErrorResponse\n    };\n\n    return instance;\n}\n\nPlayReady.__dashjs_factory_name = 'PlayReady';\nexport default FactoryMaker.getSingletonFactory(PlayReady); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\n/**\n * @ignore\n */\nfunction Widevine() {\n\n    let instance;\n\n    function getServerURLFromMessage(url /*, message, messageType*/) {\n        return url;\n    }\n\n    function getHTTPMethod(/*messageType*/) {\n        return 'POST';\n    }\n\n    function getResponseType(/*keySystemStr, messageType*/) {\n        return 'arraybuffer';\n    }\n\n    function getLicenseMessage(serverResponse/*, keySystemStr, messageType*/) {\n        return serverResponse;\n    }\n\n    function getErrorResponse(serverResponse/*, keySystemStr, messageType*/) {\n        return String.fromCharCode.apply(null, new Uint8Array(serverResponse));\n    }\n\n    instance = {\n        getServerURLFromMessage,\n        getHTTPMethod,\n        getResponseType,\n        getLicenseMessage,\n        getErrorResponse\n    };\n\n    return instance;\n}\n\nWidevine.__dashjs_factory_name = 'Widevine';\nexport default FactoryMaker.getSingletonFactory(Widevine); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * CableLabs ClearKey license server implementation\n *\n * For testing purposes and evaluating potential uses for ClearKey, we have developed\n * a dirt-simple API for requesting ClearKey licenses from a remote server.\n *\n * @implements LicenseServer\n * @class\n */\nimport KeyPair from '../vo/KeyPair.js';\nimport ClearKeyKeySet from '../vo/ClearKeyKeySet.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction ClearKey() {\n\n    let instance;\n\n    function getServerURLFromMessage(url /* message, messageType*/) {\n        return url;\n    }\n\n    function getHTTPMethod(/*messageType*/) {\n        return 'POST';\n    }\n\n    function getResponseType(/*keySystemStr*/) {\n        return 'json';\n    }\n\n    function getLicenseMessage(serverResponse/*, keySystemStr, messageType*/) {\n        if (!serverResponse.hasOwnProperty('keys')) {\n            return null;\n        }\n        let keyPairs = [];\n        for (let i = 0; i < serverResponse.keys.length; i++) {\n            let keypair = serverResponse.keys[i];\n            let keyid = keypair.kid.replace(/=/g, '');\n            let key = keypair.k.replace(/=/g, '');\n\n            keyPairs.push(new KeyPair(keyid, key));\n        }\n        return new ClearKeyKeySet(keyPairs);\n    }\n\n    function getErrorResponse(serverResponse/*, keySystemStr, messageType*/) {\n        return String.fromCharCode.apply(null, new Uint8Array(serverResponse));\n    }\n\n    instance = {\n        getServerURLFromMessage,\n        getHTTPMethod,\n        getResponseType,\n        getLicenseMessage,\n        getErrorResponse\n    };\n\n    return instance;\n}\n\nClearKey.__dashjs_factory_name = 'ClearKey';\nexport default FactoryMaker.getSingletonFactory(ClearKey); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @classdesc A model class to save metadata about a key system\n * @ignore\n */\n\nclass KeySystemMetadata {\n    constructor(config) {\n        this.ks = config.ks;\n        this.keyId = config.keyId;\n        this.initData = config.initData;\n        this.protData = config.protData;\n        this.cdmData = config.cdmData;\n        this.sessionId = config.sessionId;\n        this.sessionType = config.sessionType;\n    }\n}\n\nexport default KeySystemMetadata;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport CommonEncryption from './../CommonEncryption.js';\nimport KeySystemClearKey from './../drm/KeySystemClearKey.js';\nimport KeySystemW3CClearKey from './../drm/KeySystemW3CClearKey.js';\nimport KeySystemWidevine from './../drm/KeySystemWidevine.js';\nimport KeySystemPlayReady from './../drm/KeySystemPlayReady.js';\nimport DRMToday from './../servers/DRMToday.js';\nimport PlayReady from './../servers/PlayReady.js';\nimport Widevine from './../servers/Widevine.js';\nimport ClearKey from './../servers/ClearKey.js';\nimport ProtectionConstants from '../../constants/ProtectionConstants.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\nimport KeySystemMetadata from '../vo/KeySystemMetadata.js';\n\n/**\n * @module ProtectionKeyController\n * @ignore\n * @description Media protection key system functionality that can be modified/overridden by applications\n */\nfunction ProtectionKeyController() {\n\n    let context = this.context;\n\n    let instance,\n        debug,\n        logger,\n        keySystems,\n        BASE64,\n        settings,\n        clearkeyKeySystem,\n        clearkeyW3CKeySystem;\n\n    function setConfig(config) {\n        if (!config) {\n            return;\n        }\n\n        if (config.debug) {\n            debug = config.debug;\n            logger = debug.getLogger(instance);\n        }\n\n        if (config.BASE64) {\n            BASE64 = config.BASE64;\n        }\n\n        if (config.settings) {\n            settings = config.settings\n        }\n    }\n\n    function initialize() {\n        keySystems = [];\n\n        let keySystem;\n\n        // PlayReady\n        keySystem = KeySystemPlayReady(context).getInstance({BASE64: BASE64, settings: settings});\n        keySystems.push(keySystem);\n\n        // Widevine\n        keySystem = KeySystemWidevine(context).getInstance({BASE64: BASE64});\n        keySystems.push(keySystem);\n\n        // ClearKey\n        keySystem = KeySystemClearKey(context).getInstance({BASE64: BASE64});\n        keySystems.push(keySystem);\n        clearkeyKeySystem = keySystem;\n\n        // W3C ClearKey\n        keySystem = KeySystemW3CClearKey(context).getInstance({BASE64: BASE64, debug: debug});\n        keySystems.push(keySystem);\n        clearkeyW3CKeySystem = keySystem;\n    }\n\n    /**\n     * Returns a prioritized list of key systems supported\n     * by this player (not necessarily those supported by the\n     * user agent)\n     *\n     * @returns {Array.<KeySystem>} a prioritized\n     * list of key systems\n     * @memberof module:ProtectionKeyController\n     * @instance\n     */\n    function getKeySystems() {\n        return keySystems;\n    }\n\n    /**\n     * Sets the prioritized list of key systems to be supported\n     * by this player.\n     *\n     * @param {Array.<KeySystem>} newKeySystems the new prioritized\n     * list of key systems\n     * @memberof module:ProtectionKeyController\n     * @instance\n     */\n    function setKeySystems(newKeySystems) {\n        keySystems = newKeySystems;\n    }\n\n    /**\n     * Returns the key system associated with the given key system string\n     * name (i.e. 'org.w3.clearkey')\n     *\n     * @param {string} systemString the system string\n     * @returns {KeySystem|null} the key system\n     * or null if no supported key system is associated with the given key\n     * system string\n     * @memberof module:ProtectionKeyController\n     * @instance\n     */\n    function getKeySystemBySystemString(systemString) {\n        for (let i = 0; i < keySystems.length; i++) {\n            if (keySystems[i].systemString === systemString) {\n                return keySystems[i];\n            }\n        }\n        return null;\n    }\n\n    /**\n     * Determines whether the given key system is ClearKey.  This is\n     * necessary because the EME spec defines ClearKey and its method\n     * for providing keys to the key session; and this method has changed\n     * between the various API versions.  Our EME-specific ProtectionModels\n     * must know if the system is ClearKey so that it can format the keys\n     * according to the particular spec version.\n     *\n     * @param {Object} keySystem the key\n     * @returns {boolean} true if this is the ClearKey key system, false\n     * otherwise\n     * @memberof module:ProtectionKeyController\n     * @instance\n     */\n    function isClearKey(keySystem) {\n        return (keySystem === clearkeyKeySystem || keySystem === clearkeyW3CKeySystem);\n    }\n\n    /**\n     * Check equality of initData array buffers.\n     *\n     * @param {ArrayBuffer} initData1 - first initData\n     * @param {ArrayBuffer} initData2 - second initData\n     * @returns {boolean} true if the initData arrays are equal in size and\n     * contents, false otherwise\n     * @memberof module:ProtectionKeyController\n     * @instance\n     */\n    function initDataEquals(initData1, initData2) {\n        if (initData1.byteLength === initData2.byteLength) {\n            let data1 = new Uint8Array(initData1);\n            let data2 = new Uint8Array(initData2);\n\n            for (let j = 0; j < data1.length; j++) {\n                if (data1[j] !== data2[j]) {\n                    return false;\n                }\n            }\n            return true;\n        }\n        return false;\n    }\n\n    /**\n     * Returns a set of supported key systems and CENC initialization data\n     * from the given array of ContentProtection elements.  Only\n     * key systems that are supported by this player will be returned.\n     * Key systems are returned in priority order (highest first).\n     *\n     * @param {Array.<Object>} contentProtectionElements - array of content protection elements parsed\n     * from the manifest\n     * @param {ProtectionData} applicationSpecifiedProtectionData user specified protection data - license server url etc\n     * supported by the content\n     * @param {string} sessionType session type\n     * @returns {Array.<Object>} array of objects indicating which supported key\n     * systems were found.  Empty array is returned if no supported key systems were found\n     * @memberof module:ProtectionKeyController\n     * @instance\n     */\n    function getSupportedKeySystemMetadataFromContentProtection(contentProtectionElements, applicationSpecifiedProtectionData, sessionType) {\n        let contentProtectionElement, keySystem, ksIdx, cpIdx;\n        let supportedKS = [];\n\n        if (!contentProtectionElements || !contentProtectionElements.length) {\n            return supportedKS\n        }\n\n        const mp4ProtectionElement = CommonEncryption.findMp4ProtectionElement(contentProtectionElements);\n        for (ksIdx = 0; ksIdx < keySystems.length; ksIdx++) {\n            keySystem = keySystems[ksIdx];\n\n            // Get protection data that applies for current key system\n            const protData = _getProtDataForKeySystem(keySystem.systemString, applicationSpecifiedProtectionData);\n\n            for (cpIdx = 0; cpIdx < contentProtectionElements.length; cpIdx++) {\n                contentProtectionElement = contentProtectionElements[cpIdx];\n                if (contentProtectionElement.schemeIdUri.toLowerCase() === keySystem.schemeIdURI) {\n                    // Look for DRM-specific ContentProtection\n                    let initData = keySystem.getInitData(contentProtectionElement, mp4ProtectionElement);\n                    const keySystemMetadata = new KeySystemMetadata({\n                        ks: keySystems[ksIdx],\n                        keyId: contentProtectionElement.keyId,\n                        initData: initData,\n                        protData: protData,\n                        cdmData: keySystem.getCDMData(protData ? protData.cdmData : null),\n                        sessionId: _getSessionId(protData, contentProtectionElement),\n                        sessionType: _getSessionType(protData, sessionType)\n                    })\n\n                    if (protData) {\n                        supportedKS.unshift(keySystemMetadata);\n                    } else {\n                        supportedKS.push(keySystemMetadata);\n                    }\n                }\n            }\n        }\n\n        return supportedKS;\n    }\n\n    /**\n     * Returns key systems supported by this player for the given PSSH\n     * initializationData. Key systems are returned in priority order\n     * (highest priority first)\n     *\n     * @param {ArrayBuffer} initData Concatenated PSSH data for all DRMs\n     * supported by the content\n     * @param {ProtectionData} protDataSet user specified protection data - license server url etc\n     * supported by the content\n     * @param {string} default session type\n     * @returns {Array.<Object>} array of objects indicating which supported key\n     * systems were found.  Empty array is returned if no\n     * supported key systems were found\n     * @memberof module:ProtectionKeyController\n     * @instance\n     */\n    function getSupportedKeySystemMetadataFromSegmentPssh(initData, protDataSet, sessionType) {\n        let supportedKS = [];\n        let pssh = CommonEncryption.parsePSSHList(initData);\n        let ks, keySystemString;\n\n        for (let ksIdx = 0; ksIdx < keySystems.length; ++ksIdx) {\n            ks = keySystems[ksIdx];\n            keySystemString = ks.systemString;\n\n            // Get protection data that applies for current key system\n            const protData = _getProtDataForKeySystem(keySystemString, protDataSet);\n\n            if (ks.uuid in pssh) {\n                supportedKS.push({\n                    ks: ks,\n                    initData: pssh[ks.uuid],\n                    protData: protData,\n                    cdmData: ks.getCDMData(protData ? protData.cdmData : null),\n                    sessionId: _getSessionId(protData),\n                    sessionType: _getSessionType(protData, sessionType)\n                });\n            }\n        }\n        return supportedKS;\n    }\n\n    /**\n     * Returns the license server implementation data that should be used for this request.\n     *\n     * @param {KeySystem} keySystem the key system\n     * associated with this license request\n     * @param {ProtectionData} protData protection data to use for the\n     * request\n     * @param {string} [messageType=\"license-request\"] the message type associated with this\n     * request.  Supported message types can be found\n     * {@link https://w3c.github.io/encrypted-media/#idl-def-MediaKeyMessageType|here}.\n     * @returns {LicenseServer|null} the license server\n     * implementation that should be used for this request or null if the player should not\n     * pass messages of the given type to a license server\n     * @memberof module:ProtectionKeyController\n     * @instance\n     *\n     */\n    function getLicenseServerModelInstance(keySystem, protData, messageType) {\n\n        // Our default server implementations do not do anything with \"license-release\" or\n        // \"individualization-request\" messages, so we just send a success event\n        if (messageType === ProtectionConstants.MEDIA_KEY_MESSAGE_TYPES.LICENSE_RELEASE || messageType === ProtectionConstants.MEDIA_KEY_MESSAGE_TYPES.INDIVIDUALIZATION_REQUEST) {\n            return null;\n        }\n\n        let licenseServerData = null;\n        if (protData && protData.hasOwnProperty('drmtoday')) {\n            licenseServerData = DRMToday(context).getInstance({BASE64: BASE64});\n        } else if (keySystem.systemString === ProtectionConstants.WIDEVINE_KEYSTEM_STRING) {\n            licenseServerData = Widevine(context).getInstance();\n        } else if (keySystem.systemString === ProtectionConstants.PLAYREADY_KEYSTEM_STRING) {\n            licenseServerData = PlayReady(context).getInstance();\n        } else if (keySystem.systemString === ProtectionConstants.CLEARKEY_KEYSTEM_STRING) {\n            licenseServerData = ClearKey(context).getInstance();\n        }\n\n        return licenseServerData;\n    }\n\n    /**\n     * Allows application-specific retrieval of ClearKey keys.\n     *\n     * @param {KeySystem} clearkeyKeySystem They exact ClearKey System to be used\n     * @param {ProtectionData} protData protection data to use for the\n     * request\n     * @param {ArrayBuffer} message the key message from the CDM\n     * @return {ClearKeyKeySet|null} the clear keys associated with\n     * the request or null if no keys can be returned by this function\n     * @memberof module:ProtectionKeyController\n     * @instance\n     */\n    function processClearKeyLicenseRequest(clearkeyKeySystem, protData, message) {\n        try {\n            return clearkeyKeySystem.getClearKeysFromProtectionData(protData, message);\n        } catch (error) {\n            logger.error('Failed to retrieve clearkeys from ProtectionData');\n            return null;\n        }\n    }\n\n    function setProtectionData(protectionDataSet) {\n        var getProtectionData = function (keySystemString) {\n            var protData = null;\n            if (protectionDataSet) {\n                protData = (keySystemString in protectionDataSet) ? protectionDataSet[keySystemString] : null;\n            }\n            return protData;\n        };\n\n        for (var i = 0; i < keySystems.length; i++) {\n            var keySystem = keySystems[i];\n            if (keySystem.hasOwnProperty('init')) {\n                keySystem.init(getProtectionData(keySystem.systemString));\n            }\n        }\n    }\n\n    function _getProtDataForKeySystem(systemString, protDataSet) {\n        if (!protDataSet) {\n            return null;\n        }\n        return (systemString in protDataSet) ? protDataSet[systemString] : null;\n    }\n\n    function _getSessionId(protData, cp) {\n        // Get sessionId from protectionData or from manifest (ContentProtection)\n        if (protData && protData.sessionId) {\n            return protData.sessionId;\n        } else if (cp && cp.sessionId) {\n            return cp.sessionId;\n        }\n        return null;\n    }\n\n    function _getSessionType(protData, sessionType) {\n        return (protData && protData.sessionType) ? protData.sessionType : sessionType;\n    }\n\n    instance = {\n        getKeySystemBySystemString,\n        getKeySystems,\n        getLicenseServerModelInstance,\n        getSupportedKeySystemMetadataFromContentProtection,\n        getSupportedKeySystemMetadataFromSegmentPssh,\n        initDataEquals,\n        initialize,\n        isClearKey,\n        processClearKeyLicenseRequest,\n        setConfig,\n        setKeySystems,\n        setProtectionData,\n    };\n\n    return instance;\n}\n\nProtectionKeyController.__dashjs_factory_name = 'ProtectionKeyController';\nexport default FactoryMaker.getSingletonFactory(ProtectionKeyController);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @classdesc NeedKey\n * @ignore\n */\nclass NeedKey {\n    /**\n     * @param {ArrayBuffer} initData the initialization data\n     * @param {string} initDataType initialization data type\n     * @class\n     */\n    constructor(initData, initDataType) {\n        this.initData = initData;\n        this.initDataType = initDataType;\n    }\n}\n\nexport default NeedKey;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport ProtectionConstants from '../../constants/ProtectionConstants.js';\n\n/**\n * @classdesc EME-independent KeyMessage\n * @ignore\n */\nclass KeyMessage {\n    /**\n     * @param {SessionToken} sessionToken the session\n     * to which the key message is associated\n     * @param {ArrayBuffer} message the key message\n     * @param {string} defaultURL license acquisition URL provided by the CDM\n     * @param {string} messageType Supported message types can be found\n     * {@link https://w3c.github.io/encrypted-media/#idl-def-MediaKeyMessageType|here}.\n     * @class\n     */\n    constructor(sessionToken, message, defaultURL, messageType) {\n        this.sessionToken = sessionToken;\n        this.message = message;\n        this.defaultURL = defaultURL;\n        this.messageType = messageType ? messageType : ProtectionConstants.MEDIA_KEY_MESSAGE_TYPES.LICENSE_REQUEST;\n    }\n}\n\nexport default KeyMessage;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @classdesc Creates a new key system access token.  Represents a valid key system for\n * given piece of content and key system requirements.  Used to initialize license\n * acquisition operations.\n * @ignore\n */\nclass KeySystemAccess {\n    /**\n     * @param {MediaPlayer.dependencies.protection.KeySystem} keySystem the key system\n     * @param {KeySystemConfiguration} ksConfiguration the\n     * subset of configurations passed to the key system access request that are supported\n     * by this user agent\n     * @class\n     * @ignore\n     */\n    constructor(keySystem, ksConfiguration) {\n        this.keySystem = keySystem;\n        this.ksConfiguration = ksConfiguration;\n        this.nativeMediaKeySystemAccessObject = null;\n        this.selectedSystemString = null;\n    }\n}\n\nexport default KeySystemAccess;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * Most recent EME implementation\n *\n * Implemented by Google Chrome v36+ (Windows, OSX, Linux)\n *\n * @implements ProtectionModel\n * @class\n */\nimport ProtectionKeyController from '../controllers/ProtectionKeyController.js';\nimport NeedKey from '../vo/NeedKey.js';\nimport ProtectionErrors from '../errors/ProtectionErrors.js';\nimport DashJSError from '../../vo/DashJSError.js';\nimport KeyMessage from '../vo/KeyMessage.js';\nimport KeySystemAccess from '../vo/KeySystemAccess.js';\nimport ProtectionConstants from '../../constants/ProtectionConstants.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nconst SYSTEM_STRING_PRIORITY = {};\nSYSTEM_STRING_PRIORITY[ProtectionConstants.PLAYREADY_KEYSTEM_STRING] = [ProtectionConstants.PLAYREADY_KEYSTEM_STRING, ProtectionConstants.PLAYREADY_RECOMMENDATION_KEYSTEM_STRING];\nSYSTEM_STRING_PRIORITY[ProtectionConstants.WIDEVINE_KEYSTEM_STRING] = [ProtectionConstants.WIDEVINE_KEYSTEM_STRING];\nSYSTEM_STRING_PRIORITY[ProtectionConstants.CLEARKEY_KEYSTEM_STRING] = [ProtectionConstants.CLEARKEY_KEYSTEM_STRING];\n\nfunction DefaultProtectionModel(config) {\n\n    config = config || {};\n    const context = this.context;\n    const eventBus = config.eventBus;//Need to pass in here so we can use same instance since this is optional module\n    const events = config.events;\n    const debug = config.debug;\n\n    let instance,\n        logger,\n        keySystem,\n        videoElement,\n        mediaKeys,\n        sessionTokens,\n        eventHandler,\n        protectionKeyController;\n\n    function setup() {\n        logger = debug.getLogger(instance);\n        keySystem = null;\n        videoElement = null;\n        mediaKeys = null;\n        sessionTokens = [];\n        protectionKeyController = ProtectionKeyController(context).getInstance();\n        eventHandler = createEventHandler();\n    }\n\n    function reset() {\n        const numSessions = sessionTokens.length;\n        let session;\n\n        if (numSessions !== 0) {\n            // Called when we are done closing a session.  Success or fail\n            const done = function (session) {\n                removeSession(session);\n                if (sessionTokens.length === 0) {\n                    if (videoElement) {\n                        videoElement.removeEventListener('encrypted', eventHandler);\n                        videoElement.setMediaKeys(null).then(function () {\n                            eventBus.trigger(events.TEARDOWN_COMPLETE);\n                        });\n                    } else {\n                        eventBus.trigger(events.TEARDOWN_COMPLETE);\n                    }\n                }\n            };\n            for (let i = 0; i < numSessions; i++) {\n                session = sessionTokens[i];\n                (function (s) {\n                    _closeKeySessionInternal(session)\n                    done(s);\n                })(session);\n            }\n        } else {\n            eventBus.trigger(events.TEARDOWN_COMPLETE);\n        }\n    }\n\n    function stop() {\n        // Close and remove not usable sessions\n        let session;\n        for (let i = 0; i < sessionTokens.length; i++) {\n            session = sessionTokens[i];\n            if (!session.getUsable()) {\n                _closeKeySessionInternal(session)\n                removeSession(session);\n            }\n        }\n    }\n\n    function getAllInitData() {\n        const retVal = [];\n        for (let i = 0; i < sessionTokens.length; i++) {\n            if (sessionTokens[i].initData) {\n                retVal.push(sessionTokens[i].initData);\n            }\n        }\n        return retVal;\n    }\n\n    function getSessionTokens() {\n        return sessionTokens;\n    }\n\n    function requestKeySystemAccess(keySystemConfigurationsToRequest) {\n        return new Promise((resolve, reject) => {\n            _requestKeySystemAccessInternal(keySystemConfigurationsToRequest, 0, resolve, reject);\n        })\n    }\n\n    /**\n     * Initializes access to a key system. Once we found a valid configuration we get a mediaKeySystemAccess object\n     * @param keySystemConfigurationsToRequest\n     * @param idx\n     * @param resolve\n     * @param reject\n     * @private\n     */\n    function _requestKeySystemAccessInternal(keySystemConfigurationsToRequest, idx, resolve, reject) {\n\n        // In case requestMediaKeySystemAccess is not available we can not proceed and dispatch an error\n        if (navigator.requestMediaKeySystemAccess === undefined ||\n            typeof navigator.requestMediaKeySystemAccess !== 'function') {\n            const msg = 'Insecure origins are not allowed';\n            eventBus.trigger(events.KEY_SYSTEM_ACCESS_COMPLETE, { error: msg });\n            reject({ error: msg });\n            return;\n        }\n\n        // If a systemStringPriority is defined by the application we use these values. Otherwise, we use the default system string\n        // This is useful for DRM systems such as Playready for which multiple system strings are possible for instance com.microsoft.playready and com.microsoft.playready.recommendation\n        const protDataSystemStringPriority = keySystemConfigurationsToRequest[idx].protData && keySystemConfigurationsToRequest[idx].protData.systemStringPriority ? keySystemConfigurationsToRequest[idx].protData.systemStringPriority : null;\n        const configs = keySystemConfigurationsToRequest[idx].configs;\n        const currentKeySystem = keySystemConfigurationsToRequest[idx].ks;\n        let systemString = currentKeySystem.systemString;\n\n        // Use the default values in case no values are provided by the application\n        const systemStringsToApply = protDataSystemStringPriority ? protDataSystemStringPriority : SYSTEM_STRING_PRIORITY[systemString] ? SYSTEM_STRING_PRIORITY[systemString] : [systemString];\n\n        // Check all the available system strings and the available configurations for support\n        _checkAccessForKeySystem(systemStringsToApply, configs)\n            .then((data) => {\n                const configuration = data && data.nativeMediaKeySystemAccessObject && typeof data.nativeMediaKeySystemAccessObject.getConfiguration === 'function' ?\n                    data.nativeMediaKeySystemAccessObject.getConfiguration() : null;\n                const keySystemAccess = new KeySystemAccess(currentKeySystem, configuration);\n                keySystemAccess.selectedSystemString = data.selectedSystemString;\n                keySystemAccess.nativeMediaKeySystemAccessObject = data.nativeMediaKeySystemAccessObject;\n                eventBus.trigger(events.KEY_SYSTEM_ACCESS_COMPLETE, { data: keySystemAccess });\n                resolve({ data: keySystemAccess });\n            })\n            .catch((e) => {\n                if (idx + 1 < keySystemConfigurationsToRequest.length) {\n                    _requestKeySystemAccessInternal(keySystemConfigurationsToRequest, idx + 1, resolve, reject);\n                } else {\n                    const errorMessage = 'Key system access denied! ';\n                    eventBus.trigger(events.KEY_SYSTEM_ACCESS_COMPLETE, { error: errorMessage + e.message });\n                    reject({ error: errorMessage + e.message });\n                }\n            })\n    }\n\n    /**\n     * For a specific key system: Iterate over the possible system strings and resolve once a valid configuration was found\n     * @param {array} systemStringsToApply\n     * @param {object} configs\n     * @return {Promise}\n     * @private\n     */\n    function _checkAccessForKeySystem(systemStringsToApply, configs) {\n        return new Promise((resolve, reject) => {\n            _checkAccessForSystemStrings(systemStringsToApply, configs, 0, resolve, reject);\n        })\n    }\n\n    /**\n     * Recursively iterate over the possible system strings until a supported configuration is found or we ran out of options\n     * @param {array} systemStringsToApply\n     * @param {object} configs\n     * @param {number} idx\n     * @param {function} resolve\n     * @param {function} reject\n     * @private\n     */\n    function _checkAccessForSystemStrings(systemStringsToApply, configs, idx, resolve, reject) {\n        const systemString = systemStringsToApply[idx];\n\n        logger.debug(`Requesting key system access for system string ${systemString}`);\n\n        navigator.requestMediaKeySystemAccess(systemString, configs)\n            .then((mediaKeySystemAccess) => {\n                resolve({ nativeMediaKeySystemAccessObject: mediaKeySystemAccess, selectedSystemString: systemString });\n            })\n            .catch((e) => {\n                if (idx + 1 < systemStringsToApply.length) {\n                    _checkAccessForSystemStrings(systemStringsToApply, configs, idx + 1, resolve, reject);\n                } else {\n                    reject(e);\n                }\n            });\n    }\n\n    /**\n     * Selects a key system by creating the mediaKeys and adding them to the video element\n     * @param keySystemAccess\n     * @return {Promise<unknown>}\n     */\n    function selectKeySystem(keySystemAccess) {\n        return new Promise((resolve, reject) => {\n            keySystemAccess.nativeMediaKeySystemAccessObject.createMediaKeys()\n                .then((mkeys) => {\n                    keySystem = keySystemAccess.keySystem;\n                    mediaKeys = mkeys;\n                    if (videoElement) {\n                        return videoElement.setMediaKeys(mediaKeys)\n                    } else {\n                        return Promise.resolve();\n                    }\n                })\n                .then(() => {\n                    resolve(keySystem);\n                })\n                .catch(function () {\n                    reject({ error: 'Error selecting keys system (' + keySystemAccess.keySystem.systemString + ')! Could not create MediaKeys -- TODO' });\n                });\n        })\n    }\n\n    function setMediaElement(mediaElement) {\n        if (videoElement === mediaElement) {\n            return;\n        }\n\n        // Replacing the previous element\n        if (videoElement) {\n            videoElement.removeEventListener('encrypted', eventHandler);\n            if (videoElement.setMediaKeys) {\n                videoElement.setMediaKeys(null);\n            }\n        }\n\n        videoElement = mediaElement;\n\n        // Only if we are not detaching from the existing element\n        if (videoElement) {\n            videoElement.addEventListener('encrypted', eventHandler);\n            if (videoElement.setMediaKeys && mediaKeys) {\n                videoElement.setMediaKeys(mediaKeys);\n            }\n        }\n    }\n\n    function setServerCertificate(serverCertificate) {\n        return new Promise((resolve, reject) => {\n            mediaKeys.setServerCertificate(serverCertificate)\n                .then(function () {\n                    logger.info('DRM: License server certificate successfully updated.');\n                    eventBus.trigger(events.SERVER_CERTIFICATE_UPDATED);\n                    resolve();\n                })\n                .catch((error) => {\n                    reject(error);\n                    eventBus.trigger(events.SERVER_CERTIFICATE_UPDATED, { error: new DashJSError(ProtectionErrors.SERVER_CERTIFICATE_UPDATED_ERROR_CODE, ProtectionErrors.SERVER_CERTIFICATE_UPDATED_ERROR_MESSAGE + error.name) });\n                });\n        })\n    }\n\n    /**\n     * Create a key session, a session token and initialize a request by calling generateRequest\n     * @param keySystemMetadata\n     */\n    function createKeySession(keySystemMetadata) {\n        if (!keySystem || !mediaKeys) {\n            throw new Error('Can not create sessions until you have selected a key system');\n        }\n\n        const mediaKeySession = mediaKeys.createSession(keySystemMetadata.sessionType);\n        const sessionToken = _createSessionToken(mediaKeySession, keySystemMetadata);\n\n        // The \"keyids\" type is used for Clearkey when keys are provided directly in the protection data and a request to a license server is not needed\n        const dataType = keySystem.systemString === ProtectionConstants.CLEARKEY_KEYSTEM_STRING && (keySystemMetadata.initData || (keySystemMetadata.protData && keySystemMetadata.protData.clearkeys)) ? ProtectionConstants.INITIALIZATION_DATA_TYPE_KEYIDS : ProtectionConstants.INITIALIZATION_DATA_TYPE_CENC;\n\n        mediaKeySession.generateRequest(dataType, keySystemMetadata.initData)\n            .then(function () {\n                logger.debug('DRM: Session created.  SessionID = ' + sessionToken.getSessionId());\n                eventBus.trigger(events.KEY_SESSION_CREATED, { data: sessionToken });\n            })\n            .catch(function (error) {\n                removeSession(sessionToken);\n                eventBus.trigger(events.KEY_SESSION_CREATED, {\n                    data: null,\n                    error: new DashJSError(ProtectionErrors.KEY_SESSION_CREATED_ERROR_CODE, ProtectionErrors.KEY_SESSION_CREATED_ERROR_MESSAGE + 'Error generating key request -- ' + error.name)\n                });\n            });\n    }\n\n    function updateKeySession(sessionToken, message) {\n        const session = sessionToken.session;\n\n        // Send our request to the key session\n        if (protectionKeyController.isClearKey(keySystem)) {\n            message = message.toJWK();\n        }\n        session.update(message)\n            .then(() => {\n                eventBus.trigger(events.KEY_SESSION_UPDATED);\n            })\n            .catch(function (error) {\n                eventBus.trigger(events.KEY_ERROR, { error: new DashJSError(ProtectionErrors.MEDIA_KEYERR_CODE, 'Error sending update() message! ' + error.name, sessionToken) });\n            });\n    }\n\n    function loadKeySession(keySystemMetadata) {\n        if (!keySystem || !mediaKeys) {\n            throw new Error('Can not load sessions until you have selected a key system');\n        }\n\n        const sessionId = keySystemMetadata.sessionId;\n\n        // Check if session Id is not already loaded or loading\n        for (let i = 0; i < sessionTokens.length; i++) {\n            if (sessionId === sessionTokens[i].sessionId) {\n                logger.warn('DRM: Ignoring session ID because we have already seen it!');\n                return;\n            }\n        }\n\n        const session = mediaKeys.createSession(keySystemMetadata.sessionType);\n        const sessionToken = _createSessionToken(session, keySystemMetadata);\n        sessionToken.hasTriggeredKeyStatusMapUpdate = true;\n\n        // Load persisted session data into our newly created session object\n        session.load(sessionId).then(function (success) {\n            if (success) {\n                logger.debug('DRM: Session loaded.  SessionID = ' + sessionToken.getSessionId());\n                eventBus.trigger(events.KEY_SESSION_CREATED, { data: sessionToken });\n            } else {\n                removeSession(sessionToken);\n                eventBus.trigger(events.KEY_SESSION_CREATED, {\n                    data: null,\n                    error: new DashJSError(ProtectionErrors.KEY_SESSION_CREATED_ERROR_CODE, ProtectionErrors.KEY_SESSION_CREATED_ERROR_MESSAGE + 'Could not load session! Invalid Session ID (' + sessionId + ')')\n                });\n            }\n        }).catch(function (error) {\n            removeSession(sessionToken);\n            eventBus.trigger(events.KEY_SESSION_CREATED, {\n                data: null,\n                error: new DashJSError(ProtectionErrors.KEY_SESSION_CREATED_ERROR_CODE, ProtectionErrors.KEY_SESSION_CREATED_ERROR_MESSAGE + 'Could not load session (' + sessionId + ')! ' + error.name)\n            });\n        });\n    }\n\n    function removeKeySession(sessionToken) {\n        const session = sessionToken.session;\n\n        session.remove().then(function () {\n            logger.debug('DRM: Session removed.  SessionID = ' + sessionToken.getSessionId());\n            eventBus.trigger(events.KEY_SESSION_REMOVED, { data: sessionToken.getSessionId() });\n        }, function (error) {\n            eventBus.trigger(events.KEY_SESSION_REMOVED, {\n                data: null,\n                error: 'Error removing session (' + sessionToken.getSessionId() + '). ' + error.name\n            });\n\n        });\n    }\n\n    function closeKeySession(sessionToken) {\n        // Send our request to the key session\n        _closeKeySessionInternal(sessionToken).catch(function (error) {\n            removeSession(sessionToken);\n            eventBus.trigger(events.KEY_SESSION_CLOSED, {\n                data: null,\n                error: 'Error closing session (' + sessionToken.getSessionId() + ') ' + error.name\n            });\n        });\n    }\n\n    function _closeKeySessionInternal(sessionToken) {\n        if (!sessionToken || !sessionToken.session) {\n            return Promise.resolve;\n        }\n        const session = sessionToken.session;\n\n        // Remove event listeners\n        session.removeEventListener('keystatuseschange', sessionToken);\n        session.removeEventListener('message', sessionToken);\n\n        // Send our request to the key session\n        return session.close();\n    }\n\n    // This is our main event handler for all desired HTMLMediaElement events\n    // related to EME.  These events are translated into our API-independent\n    // versions of the same events\n    function createEventHandler() {\n        return {\n            handleEvent: function (event) {\n                switch (event.type) {\n                    case 'encrypted':\n                        if (event.initData) {\n                            let initData = ArrayBuffer.isView(event.initData) ? event.initData.buffer : event.initData;\n                            eventBus.trigger(events.NEED_KEY, { key: new NeedKey(initData, event.initDataType) });\n                        }\n                        break;\n                }\n            }\n        };\n    }\n\n    function removeSession(token) {\n        // Remove from our session list\n        for (let i = 0; i < sessionTokens.length; i++) {\n            if (sessionTokens[i] === token) {\n                sessionTokens.splice(i, 1);\n                break;\n            }\n        }\n    }\n\n    // Function to create our session token objects which manage the EME\n    // MediaKeySession and session-specific event handler\n    function _createSessionToken(session, keySystemMetadata) {\n        const token = { // Implements SessionToken\n            session: session,\n            keyId: keySystemMetadata.keyId,\n            normalizedKeyId: keySystemMetadata && keySystemMetadata.keyId && typeof keySystemMetadata.keyId === 'string' ? keySystemMetadata.keyId.replace(/-/g, '').toLowerCase() : '',\n            initData: keySystemMetadata.initData,\n            sessionId: keySystemMetadata.sessionId,\n            sessionType: keySystemMetadata.sessionType,\n            hasTriggeredKeyStatusMapUpdate: false,\n\n            // This is our main event handler for all desired MediaKeySession events\n            // These events are translated into our API-independent versions of the\n            // same events\n            handleEvent: function (event) {\n                switch (event.type) {\n                    case 'keystatuseschange':\n                        this._onKeyStatusesChange(event);\n                        break;\n\n                    case 'message':\n                        this._onKeyMessage(event);\n                        break;\n                }\n            },\n\n            _onKeyStatusesChange: function (event) {\n                eventBus.trigger(events.KEY_STATUSES_CHANGED, { data: this });\n\n                const keyStatuses = [];\n                event.target.keyStatuses.forEach(function () {\n                    keyStatuses.push(_parseKeyStatus(arguments));\n                });\n                eventBus.trigger(events.INTERNAL_KEY_STATUSES_CHANGED, {\n                    parsedKeyStatuses: keyStatuses,\n                    sessionToken: token\n                });\n            },\n\n            _onKeyMessage: function (event) {\n                let message = ArrayBuffer.isView(event.message) ? event.message.buffer : event.message;\n                eventBus.trigger(events.INTERNAL_KEY_MESSAGE, { data: new KeyMessage(this, message, undefined, event.messageType) });\n\n            },\n\n            getKeyId: function () {\n                return this.keyId;\n            },\n\n            getSessionId: function () {\n                return session.sessionId;\n            },\n\n            getSessionType: function () {\n                return this.sessionType;\n            },\n\n            getExpirationTime: function () {\n                return session.expiration;\n            },\n\n            getKeyStatuses: function () {\n                return session.keyStatuses;\n            },\n\n            getUsable: function () {\n                let usable = false;\n                session.keyStatuses.forEach(function () {\n                    let keyStatus = _parseKeyStatus(arguments);\n                    if (keyStatus.status === ProtectionConstants.MEDIA_KEY_STATUSES.USABLE) {\n                        usable = true;\n                    }\n                });\n                return usable;\n            }\n\n        };\n\n        // Add all event listeners\n        session.addEventListener('keystatuseschange', token);\n        session.addEventListener('message', token);\n\n        // Register callback for session closed Promise\n        session.closed.then(() => {\n            removeSession(token);\n            logger.debug('DRM: Session closed.  SessionID = ' + token.getSessionId());\n            eventBus.trigger(events.KEY_SESSION_CLOSED, { data: token.getSessionId() });\n        });\n\n        // Add to our session list\n        sessionTokens.push(token);\n\n        return token;\n    }\n\n    function _parseKeyStatus(args) {\n        // Edge and Chrome implement different version of keystatuses, param are not on same order\n        let status, keyId;\n        if (args && args.length > 0) {\n            if (args[0]) {\n                if (typeof args[0] === 'string') {\n                    status = args[0];\n                } else {\n                    keyId = args[0];\n                }\n            }\n\n            if (args[1]) {\n                if (typeof args[1] === 'string') {\n                    status = args[1];\n                } else {\n                    keyId = args[1];\n                }\n            }\n        }\n        return {\n            status: status,\n            keyId: keyId\n        };\n    }\n\n    instance = {\n        closeKeySession,\n        createKeySession,\n        getAllInitData,\n        getSessionTokens,\n        loadKeySession,\n        removeKeySession,\n        requestKeySystemAccess,\n        reset,\n        selectKeySystem,\n        setMediaElement,\n        setServerCertificate,\n        stop,\n        updateKeySession,\n    };\n\n    setup();\n\n    return instance;\n}\n\nDefaultProtectionModel.__dashjs_factory_name = 'DefaultProtectionModel';\nexport default FactoryMaker.getClassFactory(DefaultProtectionModel);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * Implementation of the EME APIs as of the 3 Feb 2014 state of the specification.\n *\n * Implemented by Internet Explorer 11 (Windows 8.1)\n *\n * @implements ProtectionModel\n * @class\n */\n\nimport ProtectionKeyController from '../controllers/ProtectionKeyController.js';\nimport NeedKey from '../vo/NeedKey.js';\nimport DashJSError from '../../vo/DashJSError.js';\nimport ProtectionErrors from '../errors/ProtectionErrors.js';\nimport KeyMessage from '../vo/KeyMessage.js';\nimport KeySystemConfiguration from '../vo/KeySystemConfiguration.js';\nimport KeySystemAccess from '../vo/KeySystemAccess.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\nimport ProtectionConstants from '../../constants/ProtectionConstants.js';\n\nfunction ProtectionModel_3Feb2014(config) {\n\n    config = config || {};\n    const context = this.context;\n    const eventBus = config.eventBus;//Need to pass in here so we can use same instance since this is optional module\n    const events = config.events;\n    const debug = config.debug;\n    const api = config.api;\n\n    let instance,\n        logger,\n        videoElement,\n        keySystem,\n        mediaKeys,\n        keySystemAccess,\n        sessionTokens,\n        eventHandler,\n        protectionKeyController;\n\n    function setup() {\n        logger = debug.getLogger(instance);\n        videoElement = null;\n        keySystem = null;\n        mediaKeys = null;\n        keySystemAccess = null;\n        sessionTokens = [];\n        protectionKeyController = ProtectionKeyController(context).getInstance();\n        eventHandler = createEventHandler();\n    }\n\n    function reset() {\n        try {\n            for (let i = 0; i < sessionTokens.length; i++) {\n                closeKeySession(sessionTokens[i]);\n            }\n            if (videoElement) {\n                videoElement.removeEventListener(api.needkey, eventHandler);\n            }\n            eventBus.trigger(events.TEARDOWN_COMPLETE);\n        } catch (error) {\n            eventBus.trigger(events.TEARDOWN_COMPLETE, { error: 'Error tearing down key sessions and MediaKeys! -- ' + error.message });\n        }\n    }\n\n    function getAllInitData() {\n        const retVal = [];\n        for (let i = 0; i < sessionTokens.length; i++) {\n            retVal.push(sessionTokens[i].initData);\n        }\n        return retVal;\n    }\n\n    function getSessionTokens() {\n        return sessionTokens;\n    }\n\n    function requestKeySystemAccess(ksConfigurations) {\n        return new Promise((resolve, reject) => {\n            // Try key systems in order, first one with supported key system configuration\n            // is used\n            let found = false;\n            for (let ksIdx = 0; ksIdx < ksConfigurations.length; ksIdx++) {\n                const systemString = ksConfigurations[ksIdx].ks.systemString;\n                const configs = ksConfigurations[ksIdx].configs;\n                let supportedAudio = null;\n                let supportedVideo = null;\n\n                // Try key system configs in order, first one with supported audio/video\n                // is used\n                for (let configIdx = 0; configIdx < configs.length; configIdx++) {\n                    const audios = configs[configIdx].audioCapabilities;\n                    const videos = configs[configIdx].videoCapabilities;\n\n                    // Look for supported audio container/codecs\n                    if (audios && audios.length !== 0) {\n                        supportedAudio = []; // Indicates that we have a requested audio config\n                        for (let audioIdx = 0; audioIdx < audios.length; audioIdx++) {\n                            if (window[api.MediaKeys].isTypeSupported(systemString, audios[audioIdx].contentType)) {\n                                supportedAudio.push(audios[audioIdx]);\n                            }\n                        }\n                    }\n\n                    // Look for supported video container/codecs\n                    if (videos && videos.length !== 0) {\n                        supportedVideo = []; // Indicates that we have a requested video config\n                        for (let videoIdx = 0; videoIdx < videos.length; videoIdx++) {\n                            if (window[api.MediaKeys].isTypeSupported(systemString, videos[videoIdx].contentType)) {\n                                supportedVideo.push(videos[videoIdx]);\n                            }\n                        }\n                    }\n\n                    // No supported audio or video in this configuration OR we have\n                    // requested audio or video configuration that is not supported\n                    if ((!supportedAudio && !supportedVideo) ||\n                        (supportedAudio && supportedAudio.length === 0) ||\n                        (supportedVideo && supportedVideo.length === 0)) {\n                        continue;\n                    }\n\n                    // This configuration is supported\n                    found = true;\n                    const ksConfig = new KeySystemConfiguration(supportedAudio, supportedVideo);\n                    const ks = protectionKeyController.getKeySystemBySystemString(systemString);\n                    const keySystemAccess = new KeySystemAccess(ks, ksConfig);\n                    eventBus.trigger(events.KEY_SYSTEM_ACCESS_COMPLETE, { data: keySystemAccess });\n                    resolve({ data: keySystemAccess });\n                    break;\n                }\n            }\n            if (!found) {\n                const errorMessage = 'Key system access denied! -- No valid audio/video content configurations detected!';\n                eventBus.trigger(events.KEY_SYSTEM_ACCESS_COMPLETE, { error: errorMessage });\n                reject({ error: errorMessage });\n            }\n        })\n    }\n\n    function selectKeySystem(ksAccess) {\n        return new Promise((resolve, reject) => {\n            try {\n                mediaKeys = ksAccess.mediaKeys = new window[api.MediaKeys](ksAccess.keySystem.systemString);\n                keySystem = ksAccess.keySystem;\n                keySystemAccess = ksAccess;\n                if (videoElement) {\n                    setMediaKeys();\n                }\n                resolve(keySystem);\n            } catch (error) {\n                reject({ error: 'Error selecting keys system (' + keySystem.systemString + ')! Could not create MediaKeys -- TODO' });\n            }\n        })\n    }\n\n    function setMediaElement(mediaElement) {\n        if (videoElement === mediaElement) {\n            return;\n        }\n\n        // Replacing the previous element\n        if (videoElement) {\n            videoElement.removeEventListener(api.needkey, eventHandler);\n        }\n\n        videoElement = mediaElement;\n\n        // Only if we are not detaching from the existing element\n        if (videoElement) {\n            videoElement.addEventListener(api.needkey, eventHandler);\n            if (mediaKeys) {\n                setMediaKeys();\n            }\n        }\n    }\n\n    function createKeySession(ksInfo) {\n        if (!keySystem || !mediaKeys || !keySystemAccess) {\n            throw new Error('Can not create sessions until you have selected a key system');\n        }\n\n        // Use the first video capability for the contentType.\n        // TODO:  Not sure if there is a way to concatenate all capability data into a RFC6386-compatible format\n\n        // If player is trying to playback Audio only stream - don't error out.\n        let capabilities = null;\n\n        if (keySystemAccess.ksConfiguration.videoCapabilities && keySystemAccess.ksConfiguration.videoCapabilities.length > 0) {\n            capabilities = keySystemAccess.ksConfiguration.videoCapabilities[0];\n        }\n\n        if (capabilities === null && keySystemAccess.ksConfiguration.audioCapabilities && keySystemAccess.ksConfiguration.audioCapabilities.length > 0) {\n            capabilities = keySystemAccess.ksConfiguration.audioCapabilities[0];\n        }\n\n        if (capabilities === null) {\n            throw new Error('Can not create sessions for unknown content types.');\n        }\n\n        const contentType = capabilities.contentType;\n        const session = mediaKeys.createSession(contentType, new Uint8Array(ksInfo.initData), ksInfo.cdmData ? new Uint8Array(ksInfo.cdmData) : null);\n        const sessionToken = createSessionToken(session, ksInfo);\n\n        // Add all event listeners\n        session.addEventListener(api.error, sessionToken);\n        session.addEventListener(api.message, sessionToken);\n        session.addEventListener(api.ready, sessionToken);\n        session.addEventListener(api.close, sessionToken);\n\n        // Add to our session list\n        sessionTokens.push(sessionToken);\n        logger.debug('DRM: Session created.  SessionID = ' + sessionToken.getSessionId());\n        eventBus.trigger(events.KEY_SESSION_CREATED, { data: sessionToken });\n    }\n\n    function updateKeySession(sessionToken, message) {\n        const session = sessionToken.session;\n\n        if (!protectionKeyController.isClearKey(keySystem)) {\n            // Send our request to the key session\n            session.update(new Uint8Array(message));\n        } else {\n            // For clearkey, message is a ClearKeyKeySet\n            session.update(new Uint8Array(message.toJWK()));\n        }\n        eventBus.trigger(events.KEY_SESSION_UPDATED);\n    }\n\n    /**\n     * Close the given session and release all associated keys.  Following\n     * this call, the sessionToken becomes invalid\n     *\n     * @param {Object} sessionToken - the session token\n     */\n    function closeKeySession(sessionToken) {\n        const session = sessionToken.session;\n\n        // Remove event listeners\n        session.removeEventListener(api.error, sessionToken);\n        session.removeEventListener(api.message, sessionToken);\n        session.removeEventListener(api.ready, sessionToken);\n        session.removeEventListener(api.close, sessionToken);\n\n        // Remove from our session list\n        for (let i = 0; i < sessionTokens.length; i++) {\n            if (sessionTokens[i] === sessionToken) {\n                sessionTokens.splice(i, 1);\n                break;\n            }\n        }\n\n        // Send our request to the key session\n        session[api.release]();\n    }\n\n    function setServerCertificate(/*serverCertificate*/) { /* Not supported */\n    }\n\n    function loadKeySession(/*ksInfo*/) { /* Not supported */\n    }\n\n    function removeKeySession(/*sessionToken*/) { /* Not supported */\n    }\n\n\n    function createEventHandler() {\n        return {\n            handleEvent: function (event) {\n                switch (event.type) {\n\n                    case api.needkey:\n                        if (event.initData) {\n                            const initData = ArrayBuffer.isView(event.initData) ? event.initData.buffer : event.initData;\n                            eventBus.trigger(events.NEED_KEY, { key: new NeedKey(initData, ProtectionConstants.INITIALIZATION_DATA_TYPE_CENC) });\n                        }\n                        break;\n                }\n            }\n        };\n    }\n\n\n    // IE11 does not let you set MediaKeys until it has entered a certain\n    // readyState, so we need this logic to ensure we don't set the keys\n    // too early\n    function setMediaKeys() {\n        let boundDoSetKeys = null;\n        const doSetKeys = function () {\n            videoElement.removeEventListener('loadedmetadata', boundDoSetKeys);\n            videoElement[api.setMediaKeys](mediaKeys);\n            eventBus.trigger(events.VIDEO_ELEMENT_SELECTED);\n        };\n        if (videoElement.readyState >= 1) {\n            doSetKeys();\n        } else {\n            boundDoSetKeys = doSetKeys.bind(this);\n            videoElement.addEventListener('loadedmetadata', boundDoSetKeys);\n        }\n\n    }\n\n    // Function to create our session token objects which manage the EME\n    // MediaKeySession and session-specific event handler\n    function createSessionToken(keySession, ksInfo) {\n        return {\n            // Implements SessionToken\n            session: keySession,\n            keyId: ksInfo.keyId,\n            normalizedKeyId: ksInfo && ksInfo.keyId && typeof ksInfo.keyId === 'string' ? ksInfo.keyId.replace(/-/g, '').toLowerCase() : '',\n            initData: ksInfo.initData,\n            hasTriggeredKeyStatusMapUpdate: false,\n\n            getKeyId: function () {\n                return this.keyId;\n            },\n\n            getSessionId: function () {\n                return this.session.sessionId;\n            },\n\n            getExpirationTime: function () {\n                return NaN;\n            },\n\n            getSessionType: function () {\n                return 'temporary';\n            },\n\n            getKeyStatuses: function () {\n                return {\n                    size: 0,\n                    has: () => {\n                        return false\n                    },\n                    get: () => {\n                        return undefined\n                    }\n                }\n            },\n\n            // This is our main event handler for all desired MediaKeySession events\n            // These events are translated into our API-independent versions of the\n            // same events\n            handleEvent: function (event) {\n                switch (event.type) {\n                    case api.error:\n                        let errorStr = 'KeyError'; // TODO: Make better string from event\n                        eventBus.trigger(events.KEY_ERROR, { error: new DashJSError(ProtectionErrors.MEDIA_KEYERR_CODE, errorStr, this) });\n                        break;\n                    case api.message:\n                        let message = ArrayBuffer.isView(event.message) ? event.message.buffer : event.message;\n                        eventBus.trigger(events.INTERNAL_KEY_MESSAGE, { data: new KeyMessage(this, message, event.destinationURL) });\n                        break;\n                    case api.ready:\n                        logger.debug('DRM: Key added.');\n                        eventBus.trigger(events.KEY_ADDED);\n                        break;\n\n                    case api.close:\n                        logger.debug('DRM: Session closed.  SessionID = ' + this.getSessionId());\n                        eventBus.trigger(events.KEY_SESSION_CLOSED, { data: this.getSessionId() });\n                        break;\n                }\n            }\n        };\n    }\n\n    instance = {\n        getAllInitData,\n        getSessionTokens,\n        requestKeySystemAccess,\n        selectKeySystem,\n        setMediaElement,\n        createKeySession,\n        updateKeySession,\n        closeKeySession,\n        setServerCertificate,\n        loadKeySession,\n        removeKeySession,\n        stop: reset,\n        reset\n    };\n\n    setup();\n\n    return instance;\n}\n\nProtectionModel_3Feb2014.__dashjs_factory_name = 'ProtectionModel_3Feb2014';\nexport default FactoryMaker.getClassFactory(ProtectionModel_3Feb2014);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * Initial implementation of EME\n *\n * Implemented by Google Chrome prior to v36\n *\n * @implements ProtectionModel\n * @class\n */\nimport ProtectionKeyController from '../controllers/ProtectionKeyController.js';\nimport NeedKey from '../vo/NeedKey.js';\nimport DashJSError from '../../vo/DashJSError.js';\nimport KeyMessage from '../vo/KeyMessage.js';\nimport KeySystemConfiguration from '../vo/KeySystemConfiguration.js';\nimport KeySystemAccess from '../vo/KeySystemAccess.js';\nimport ProtectionErrors from '../errors/ProtectionErrors.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\nimport ProtectionConstants from '../../constants/ProtectionConstants.js';\n\nfunction ProtectionModel_01b(config) {\n\n    config = config || {};\n    const context = this.context;\n    const eventBus = config.eventBus;//Need to pass in here so we can use same instance since this is optional module\n    const events = config.events;\n    const debug = config.debug;\n    const api = config.api;\n    const errHandler = config.errHandler;\n\n    let instance,\n        logger,\n        videoElement,\n        keySystem,\n        protectionKeyController,\n\n        // With this version of the EME APIs, sessionIds are not assigned to\n        // sessions until the first key message is received.  We are assuming\n        // that in the case of multiple sessions, key messages will be received\n        // in the order that generateKeyRequest() is called.\n        // Holding spot for newly-created sessions until we determine whether or\n        // not the CDM supports sessionIds\n        pendingSessions,\n\n        // List of sessions that have been initialized.  Only the first position will\n        // be used in the case that the CDM does not support sessionIds\n        sessionTokens,\n\n        // Not all CDMs support the notion of sessionIds.  Without sessionIds\n        // there is no way for us to differentiate between sessions, therefore\n        // we must only allow a single session.  Once we receive the first key\n        // message we can set this flag to determine if more sessions are allowed\n        moreSessionsAllowed,\n\n        // This is our main event handler for all desired HTMLMediaElement events\n        // related to EME.  These events are translated into our API-independent\n        // versions of the same events\n        eventHandler;\n\n    function setup() {\n        logger = debug.getLogger(instance);\n        videoElement = null;\n        keySystem = null;\n        pendingSessions = [];\n        sessionTokens = [];\n        protectionKeyController = ProtectionKeyController(context).getInstance();\n        eventHandler = createEventHandler();\n    }\n\n    function reset() {\n        if (videoElement) {\n            removeEventListeners();\n        }\n        for (let i = 0; i < sessionTokens.length; i++) {\n            closeKeySession(sessionTokens[i]);\n        }\n        eventBus.trigger(events.TEARDOWN_COMPLETE);\n    }\n\n    function getAllInitData() {\n        const retVal = [];\n        for (let i = 0; i < pendingSessions.length; i++) {\n            retVal.push(pendingSessions[i].initData);\n        }\n        for (let i = 0; i < sessionTokens.length; i++) {\n            retVal.push(sessionTokens[i].initData);\n        }\n        return retVal;\n    }\n\n    function getSessionTokens() {\n        return sessionTokens.concat(pendingSessions);\n    }\n\n    function requestKeySystemAccess(ksConfigurations) {\n        return new Promise((resolve, reject) => {\n            let ve = videoElement;\n            if (!ve) { // Must have a video element to do this capability tests\n                ve = document.createElement('video');\n            }\n\n            // Try key systems in order, first one with supported key system configuration\n            // is used\n            let found = false;\n            for (let ksIdx = 0; ksIdx < ksConfigurations.length; ksIdx++) {\n                const systemString = ksConfigurations[ksIdx].ks.systemString;\n                const configs = ksConfigurations[ksIdx].configs;\n                let supportedAudio = null;\n                let supportedVideo = null;\n\n                // Try key system configs in order, first one with supported audio/video\n                // is used\n                for (let configIdx = 0; configIdx < configs.length; configIdx++) {\n                    //let audios = configs[configIdx].audioCapabilities;\n                    const videos = configs[configIdx].videoCapabilities;\n                    // Look for supported video container/codecs\n                    if (videos && videos.length !== 0) {\n                        supportedVideo = []; // Indicates that we have a requested video config\n                        for (let videoIdx = 0; videoIdx < videos.length; videoIdx++) {\n                            if (ve.canPlayType(videos[videoIdx].contentType, systemString) !== '') {\n                                supportedVideo.push(videos[videoIdx]);\n                            }\n                        }\n                    }\n\n                    // No supported audio or video in this configuration OR we have\n                    // requested audio or video configuration that is not supported\n                    if ((!supportedAudio && !supportedVideo) ||\n                        (supportedAudio && supportedAudio.length === 0) ||\n                        (supportedVideo && supportedVideo.length === 0)) {\n                        continue;\n                    }\n\n                    // This configuration is supported\n                    found = true;\n                    const ksConfig = new KeySystemConfiguration(supportedAudio, supportedVideo);\n                    const ks = protectionKeyController.getKeySystemBySystemString(systemString);\n                    const keySystemAccess = new KeySystemAccess(ks, ksConfig)\n                    eventBus.trigger(events.KEY_SYSTEM_ACCESS_COMPLETE, { data: keySystemAccess });\n                    resolve({ data: keySystemAccess });\n                    break;\n                }\n            }\n            if (!found) {\n                const errorMessage = 'Key system access denied! -- No valid audio/video content configurations detected!';\n                eventBus.trigger(events.KEY_SYSTEM_ACCESS_COMPLETE, { error: errorMessage });\n                reject({ error: errorMessage });\n            }\n        })\n\n    }\n\n    function selectKeySystem(keySystemAccess) {\n        keySystem = keySystemAccess.keySystem;\n        return Promise.resolve(keySystem);\n    }\n\n    function setMediaElement(mediaElement) {\n        if (videoElement === mediaElement) {\n            return;\n        }\n\n        // Replacing the previous element\n        if (videoElement) {\n            removeEventListeners();\n\n            // Close any open sessions - avoids memory leak on LG webOS 2016/2017 TVs\n            for (var i = 0; i < sessionTokens.length; i++) {\n                closeKeySession(sessionTokens[i]);\n            }\n            sessionTokens = [];\n        }\n\n        videoElement = mediaElement;\n\n        // Only if we are not detaching from the existing element\n        if (videoElement) {\n            videoElement.addEventListener(api.keyerror, eventHandler);\n            videoElement.addEventListener(api.needkey, eventHandler);\n            videoElement.addEventListener(api.keymessage, eventHandler);\n            videoElement.addEventListener(api.keyadded, eventHandler);\n            eventBus.trigger(events.VIDEO_ELEMENT_SELECTED);\n        }\n    }\n\n    function createKeySession(ksInfo) {\n        if (!keySystem) {\n            throw new Error('Can not create sessions until you have selected a key system');\n        }\n\n        // Determine if creating a new session is allowed\n        if (moreSessionsAllowed || sessionTokens.length === 0) {\n            const newSession = { // Implements SessionToken\n                sessionId: null,\n                keyId: ksInfo.keyId,\n                normalizedKeyId: ksInfo && ksInfo.keyId && typeof ksInfo.keyId === 'string' ? ksInfo.keyId.replace(/-/g, '').toLowerCase() : '',\n                initData: ksInfo.initData,\n                hasTriggeredKeyStatusMapUpdate: false,\n\n                getKeyId: function () {\n                    return this.keyId;\n                },\n\n                getSessionId: function () {\n                    return this.sessionId;\n                },\n\n                getExpirationTime: function () {\n                    return NaN;\n                },\n\n                getSessionType: function () {\n                    return 'temporary';\n                },\n\n                getKeyStatuses: function () {\n                    return {\n                        size: 0,\n                        has: () => {\n                            return false\n                        },\n                        get: () => {\n                            return undefined\n                        }\n                    }\n                }\n            };\n            pendingSessions.push(newSession);\n\n            // Send our request to the CDM\n            videoElement[api.generateKeyRequest](keySystem.systemString, new Uint8Array(ksInfo.initData));\n\n            return newSession;\n\n        } else {\n            throw new Error('Multiple sessions not allowed!');\n        }\n\n    }\n\n    function updateKeySession(sessionToken, message) {\n        const sessionId = sessionToken.sessionId;\n        if (!protectionKeyController.isClearKey(keySystem)) {\n            // Send our request to the CDM\n            videoElement[api.addKey](keySystem.systemString,\n                new Uint8Array(message), new Uint8Array(sessionToken.initData), sessionId);\n        } else {\n            // For clearkey, message is a ClearKeyKeySet\n            for (let i = 0; i < message.keyPairs.length; i++) {\n                videoElement[api.addKey](keySystem.systemString,\n                    message.keyPairs[i].key, message.keyPairs[i].keyID, sessionId);\n            }\n        }\n        eventBus.trigger(events.KEY_SESSION_UPDATED);\n    }\n\n    function closeKeySession(sessionToken) {\n        // Send our request to the CDM\n        try {\n            videoElement[api.cancelKeyRequest](keySystem.systemString, sessionToken.sessionId);\n        } catch (error) {\n            eventBus.trigger(events.KEY_SESSION_CLOSED, {\n                data: null,\n                error: 'Error closing session (' + sessionToken.sessionId + ') ' + error.message\n            });\n        }\n    }\n\n    function setServerCertificate(/*serverCertificate*/) { /* Not supported */\n    }\n\n    function loadKeySession(/*ksInfo*/) { /* Not supported */\n    }\n\n    function removeKeySession(/*sessionToken*/) { /* Not supported */\n    }\n\n    function createEventHandler() {\n        return {\n            handleEvent: function (event) {\n                let sessionToken = null;\n                switch (event.type) {\n                    case api.needkey:\n                        let initData = ArrayBuffer.isView(event.initData) ? event.initData.buffer : event.initData;\n                        eventBus.trigger(events.NEED_KEY, { key: new NeedKey(initData, ProtectionConstants.INITIALIZATION_DATA_TYPE_CENC) });\n                        break;\n\n                    case api.keyerror:\n                        sessionToken = findSessionByID(sessionTokens, event.sessionId);\n                        if (!sessionToken) {\n                            sessionToken = findSessionByID(pendingSessions, event.sessionId);\n                        }\n\n                        if (sessionToken) {\n                            let code = ProtectionErrors.MEDIA_KEYERR_CODE;\n                            let msg = '';\n                            switch (event.errorCode.code) {\n                                case 1:\n                                    code = ProtectionErrors.MEDIA_KEYERR_UNKNOWN_CODE;\n                                    msg += 'MEDIA_KEYERR_UNKNOWN - ' + ProtectionErrors.MEDIA_KEYERR_UNKNOWN_MESSAGE;\n                                    break;\n                                case 2:\n                                    code = ProtectionErrors.MEDIA_KEYERR_CLIENT_CODE;\n                                    msg += 'MEDIA_KEYERR_CLIENT - ' + ProtectionErrors.MEDIA_KEYERR_CLIENT_MESSAGE;\n                                    break;\n                                case 3:\n                                    code = ProtectionErrors.MEDIA_KEYERR_SERVICE_CODE;\n                                    msg += 'MEDIA_KEYERR_SERVICE - ' + ProtectionErrors.MEDIA_KEYERR_SERVICE_MESSAGE;\n                                    break;\n                                case 4:\n                                    code = ProtectionErrors.MEDIA_KEYERR_OUTPUT_CODE;\n                                    msg += 'MEDIA_KEYERR_OUTPUT - ' + ProtectionErrors.MEDIA_KEYERR_OUTPUT_MESSAGE;\n                                    break;\n                                case 5:\n                                    code = ProtectionErrors.MEDIA_KEYERR_HARDWARECHANGE_CODE;\n                                    msg += 'MEDIA_KEYERR_HARDWARECHANGE - ' + ProtectionErrors.MEDIA_KEYERR_HARDWARECHANGE_MESSAGE;\n                                    break;\n                                case 6:\n                                    code = ProtectionErrors.MEDIA_KEYERR_DOMAIN_CODE;\n                                    msg += 'MEDIA_KEYERR_DOMAIN - ' + ProtectionErrors.MEDIA_KEYERR_DOMAIN_MESSAGE;\n                                    break;\n                            }\n                            msg += '  System Code = ' + event.systemCode;\n                            // TODO: Build error string based on key error\n                            eventBus.trigger(events.KEY_ERROR, { error: new DashJSError(code, msg, sessionToken) });\n                        } else {\n                            logger.error('No session token found for key error');\n                        }\n                        break;\n\n                    case api.keyadded:\n                        sessionToken = findSessionByID(sessionTokens, event.sessionId);\n                        if (!sessionToken) {\n                            sessionToken = findSessionByID(pendingSessions, event.sessionId);\n                        }\n\n                        if (sessionToken) {\n                            logger.debug('DRM: Key added.');\n                            eventBus.trigger(events.KEY_ADDED, { data: sessionToken });//TODO not sure anything is using sessionToken? why there?\n                        } else {\n                            logger.debug('No session token found for key added');\n                        }\n                        break;\n\n                    case api.keymessage:\n                        // If this CDM does not support session IDs, we will be limited\n                        // to a single session\n                        moreSessionsAllowed = (event.sessionId !== null) && (event.sessionId !== undefined);\n\n                        // SessionIDs supported\n                        if (moreSessionsAllowed) {\n                            // Attempt to find an uninitialized token with this sessionId\n                            sessionToken = findSessionByID(sessionTokens, event.sessionId);\n                            if (!sessionToken && pendingSessions.length > 0) {\n\n                                // This is the first message for our latest session, so set the\n                                // sessionId and add it to our list\n                                sessionToken = pendingSessions.shift();\n                                sessionTokens.push(sessionToken);\n                                sessionToken.sessionId = event.sessionId;\n\n                                eventBus.trigger(events.KEY_SESSION_CREATED, { data: sessionToken });\n                            }\n                        } else if (pendingSessions.length > 0) { // SessionIDs not supported\n                            sessionToken = pendingSessions.shift();\n                            sessionTokens.push(sessionToken);\n\n                            if (pendingSessions.length !== 0) {\n                                errHandler.error(new DashJSError(ProtectionErrors.MEDIA_KEY_MESSAGE_ERROR_CODE, ProtectionErrors.MEDIA_KEY_MESSAGE_ERROR_MESSAGE));\n                            }\n                        }\n\n                        if (sessionToken) {\n                            let message = ArrayBuffer.isView(event.message) ? event.message.buffer : event.message;\n\n                            // For ClearKey, the spec mandates that you pass this message to the\n                            // addKey method, so we always save it to the token since there is no\n                            // way to tell which key system is in use\n                            sessionToken.keyMessage = message;\n                            eventBus.trigger(events.INTERNAL_KEY_MESSAGE, { data: new KeyMessage(sessionToken, message, event.defaultURL) });\n\n                        } else {\n                            logger.warn('No session token found for key message');\n                        }\n                        break;\n                }\n            }\n        };\n    }\n\n\n    /**\n     * Helper function to retrieve the stored session token based on a given\n     * sessionId value\n     *\n     * @param {Array} sessionArray - the array of sessions to search\n     * @param {*} sessionId - the sessionId to search for\n     * @returns {*} the session token with the given sessionId\n     */\n    function findSessionByID(sessionArray, sessionId) {\n        if (!sessionId || !sessionArray) {\n            return null;\n        } else {\n            const len = sessionArray.length;\n            for (let i = 0; i < len; i++) {\n                if (sessionArray[i].sessionId == sessionId) {\n                    return sessionArray[i];\n                }\n            }\n            return null;\n        }\n    }\n\n    function removeEventListeners() {\n        videoElement.removeEventListener(api.keyerror, eventHandler);\n        videoElement.removeEventListener(api.needkey, eventHandler);\n        videoElement.removeEventListener(api.keymessage, eventHandler);\n        videoElement.removeEventListener(api.keyadded, eventHandler);\n    }\n\n    instance = {\n        getAllInitData,\n        getSessionTokens,\n        requestKeySystemAccess,\n        selectKeySystem,\n        setMediaElement,\n        createKeySession,\n        updateKeySession,\n        closeKeySession,\n        setServerCertificate,\n        loadKeySession,\n        removeKeySession,\n        stop: reset,\n        reset\n    };\n\n    setup();\n\n    return instance;\n}\n\nProtectionModel_01b.__dashjs_factory_name = 'ProtectionModel_01b';\nexport default FactoryMaker.getClassFactory(ProtectionModel_01b);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport ProtectionController from './controllers/ProtectionController.js';\nimport ProtectionKeyController from './controllers/ProtectionKeyController.js';\nimport ProtectionEvents from './ProtectionEvents.js';\nimport ProtectionErrors from './errors/ProtectionErrors.js';\nimport DefaultProtectionModel from './models/DefaultProtectionModel.js';\nimport ProtectionModel_3Feb2014 from './models/ProtectionModel_3Feb2014.js';\nimport ProtectionModel_01b from './models/ProtectionModel_01b.js';\n\nconst APIS_ProtectionModel_01b = [\n    // Un-prefixed as per spec\n    {\n        // Video Element\n        generateKeyRequest: 'generateKeyRequest',\n        addKey: 'addKey',\n        cancelKeyRequest: 'cancelKeyRequest',\n\n        // Events\n        needkey: 'needkey',\n        keyerror: 'keyerror',\n        keyadded: 'keyadded',\n        keymessage: 'keymessage'\n    },\n    // Webkit-prefixed (early Chrome versions and Chrome with EME disabled in chrome://flags)\n    {\n        // Video Element\n        generateKeyRequest: 'webkitGenerateKeyRequest',\n        addKey: 'webkitAddKey',\n        cancelKeyRequest: 'webkitCancelKeyRequest',\n\n        // Events\n        needkey: 'webkitneedkey',\n        keyerror: 'webkitkeyerror',\n        keyadded: 'webkitkeyadded',\n        keymessage: 'webkitkeymessage'\n    }\n];\n\nconst APIS_ProtectionModel_3Feb2014 = [\n    // Un-prefixed as per spec\n    // Chrome 38-39 (and some earlier versions) with chrome://flags -- Enable Encrypted Media Extensions\n    {\n        // Video Element\n        setMediaKeys: 'setMediaKeys',\n        // MediaKeys\n        MediaKeys: 'MediaKeys',\n        // MediaKeySession\n        release: 'close',\n\n        // Events\n        needkey: 'needkey',\n        error: 'keyerror',\n        message: 'keymessage',\n        ready: 'keyadded',\n        close: 'keyclose'\n    },\n    // MS-prefixed (IE11, Windows 8.1)\n    {\n        // Video Element\n        setMediaKeys: 'msSetMediaKeys',\n        // MediaKeys\n        MediaKeys: 'MSMediaKeys',\n        // MediaKeySession\n        release: 'close',\n        // Events\n        needkey: 'msneedkey',\n        error: 'mskeyerror',\n        message: 'mskeymessage',\n        ready: 'mskeyadded',\n        close: 'mskeyclose'\n    }\n];\n\nfunction Protection() {\n    let instance;\n    const context = this.context;\n\n    /**\n     * Create a ProtectionController and associated ProtectionModel for use with\n     * a single piece of content.\n     *\n     * @param {Object} config\n     * @return {ProtectionController} protection controller\n     *\n     */\n    function createProtectionSystem(config) {\n        let controller = null;\n\n        const protectionKeyController = ProtectionKeyController(context).getInstance();\n        protectionKeyController.setConfig({ debug: config.debug, BASE64: config.BASE64, settings: config.settings });\n        protectionKeyController.initialize();\n\n        let protectionModel = _getProtectionModel(config);\n\n        if (protectionModel) {\n            controller = ProtectionController(context).create({\n                BASE64: config.BASE64,\n                cmcdModel: config.cmcdModel,\n                constants: config.constants,\n                customParametersModel: config.customParametersModel,\n                debug: config.debug,\n                eventBus: config.eventBus,\n                events: config.events,\n                protectionKeyController: protectionKeyController,\n                protectionModel: protectionModel,\n                settings: config.settings\n            });\n            config.capabilities.setEncryptedMediaSupported(true);\n        }\n        return controller;\n    }\n\n    function _getProtectionModel(config) {\n        const debug = config.debug;\n        const logger = debug.getLogger(instance);\n        const eventBus = config.eventBus;\n        const errHandler = config.errHandler;\n        const videoElement = config.videoModel ? config.videoModel.getElement() : null;\n\n        if ((!videoElement || videoElement.onencrypted !== undefined) &&\n            (!videoElement || videoElement.mediaKeys !== undefined)) {\n            logger.info('EME detected on this user agent! (DefaultProtectionModel');\n            return DefaultProtectionModel(context).create({\n                debug: debug,\n                eventBus: eventBus,\n                events: config.events\n            });\n        } else if (_getAPI(videoElement, APIS_ProtectionModel_3Feb2014)) {\n            logger.info('EME detected on this user agent! (ProtectionModel_3Feb2014)');\n            return ProtectionModel_3Feb2014(context).create({\n                debug: debug,\n                eventBus: eventBus,\n                events: config.events,\n                api: _getAPI(videoElement, APIS_ProtectionModel_3Feb2014)\n            });\n        } else if (_getAPI(videoElement, APIS_ProtectionModel_01b)) {\n            logger.info('EME detected on this user agent! (ProtectionModel_01b)');\n            return ProtectionModel_01b(context).create({\n                debug: debug,\n                eventBus: eventBus,\n                errHandler: errHandler,\n                events: config.events,\n                api: _getAPI(videoElement, APIS_ProtectionModel_01b)\n            });\n        } else {\n            logger.warn('No supported version of EME detected on this user agent! - Attempts to play encrypted content will fail!');\n            return null;\n        }\n    }\n\n    function _getAPI(videoElement, apis) {\n        for (let i = 0; i < apis.length; i++) {\n            const api = apis[i];\n            // detect if api is supported by browser\n            // check only first function in api -> should be fine\n            if (typeof videoElement[api[Object.keys(api)[0]]] !== 'function') {\n                continue;\n            }\n\n            return api;\n        }\n\n        return null;\n    }\n\n    instance = {\n        createProtectionSystem\n    };\n\n    return instance;\n}\n\nProtection.__dashjs_factory_name = 'Protection';\nconst factory = dashjs.FactoryMaker.getClassFactory(Protection);\nfactory.events = ProtectionEvents;\nfactory.errors = ProtectionErrors;\ndashjs.FactoryMaker.updateClassFactory(Protection.__dashjs_factory_name, factory);\nexport default factory;\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "assertPath", "path", "TypeError", "JSON", "stringify", "normalizeStringPosix", "allowAboveRoot", "code", "res", "lastSegmentLength", "lastSlash", "dots", "i", "length", "charCodeAt", "lastSlashIndex", "lastIndexOf", "slice", "posix", "resolve", "cwd", "<PERSON><PERSON><PERSON>", "resolvedAbsolute", "arguments", "undefined", "process", "normalize", "isAbsolute", "trailingSeparator", "join", "joined", "arg", "relative", "from", "to", "fromStart", "fromEnd", "fromLen", "toStart", "toLen", "lastCommonSep", "fromCode", "out", "_makeLong", "dirname", "hasRoot", "end", "matchedSlash", "basename", "ext", "start", "extIdx", "firstNonSlashEnd", "extname", "startDot", "startPart", "preDotState", "format", "pathObject", "sep", "dir", "base", "name", "_format", "parse", "ret", "delimiter", "win32", "window", "FUNC_TYPE", "UNDEF_TYPE", "OBJ_TYPE", "STR_TYPE", "MAJOR", "MODEL", "NAME", "TYPE", "VENDOR", "VERSION", "ARCHITECTURE", "CONSOLE", "MOBILE", "TABLET", "SMARTTV", "WEARABLE", "EMBEDDED", "AMAZON", "APPLE", "ASUS", "BLACKBERRY", "BROWSER", "CHROME", "FIREFOX", "GOOGLE", "HUAWEI", "LG", "MICROSOFT", "MOTOROLA", "OPERA", "SAMSUNG", "SHARP", "SONY", "XIAOMI", "ZEBRA", "FACEBOOK", "CHROMIUM_OS", "MAC_OS", "enumerize", "arr", "enums", "toUpperCase", "has", "str1", "str2", "lowerize", "indexOf", "str", "toLowerCase", "trim", "len", "replace", "substring", "rgxMapper", "ua", "arrays", "j", "k", "p", "q", "matches", "match", "regex", "props", "exec", "this", "call", "test", "strMapper", "map", "windowsVersionMap", "regexes", "browser", "cpu", "device", "engine", "EDGE", "os", "<PERSON><PERSON><PERSON><PERSON>", "extensions", "getResult", "_navigator", "navigator", "_ua", "userAgent", "_uach", "userAgentData", "_rgxmap", "mergedRegexes", "concat", "extend", "_isSelfNav", "<PERSON><PERSON><PERSON><PERSON>", "version", "_browser", "split", "brave", "isBrave", "getCPU", "_cpu", "getDevice", "_device", "mobile", "standalone", "maxTouchPoints", "getEngine", "_engine", "getOS", "_os", "platform", "getUA", "setUA", "CPU", "DEVICE", "ENGINE", "OS", "$", "j<PERSON><PERSON><PERSON>", "Zepto", "parser", "get", "set", "result", "prop", "FactoryMaker", "instance", "singletonContexts", "singletonFactories", "classFactories", "getSingletonInstance", "context", "className", "obj", "getFactoryByName", "factoriesArray", "updateFactory", "merge", "classConstructor", "args", "classInstance", "__dashjs_factory_name", "extensionObject", "extension", "override", "apply", "parent", "hasOwnProperty", "getClassName", "childInstance", "setSingletonInstance", "push", "deleteSingletonInstances", "filter", "x", "getSingletonFactory", "getInstance", "getSingletonFactoryByName", "updateSingletonFactory", "getClassFactory", "create", "getClassFactoryByName", "updateClassFactory", "Utils", "mixin", "dest", "source", "copy", "s", "empty", "clone", "src", "RegExp", "r", "Array", "l", "addAdditionalQueryParameterToUrl", "url", "params", "updatedUrl", "for<PERSON>ach", "_ref", "key", "value", "separator", "includes", "encodeURIComponent", "e", "removeQueryParameterFromUrl", "queryParameter", "parsedUrl", "URL", "URLSearchParams", "search", "size", "delete", "queryString", "entries", "_ref2", "baseUrl", "origin", "pathname", "parseHttpHeaders", "headerStr", "headers", "headerPairs", "ilen", "headerPair", "index", "parseQueryParams", "queryParamString", "searchParams", "decodeURIComponent", "generateUuid", "dt", "Date", "getTime", "c", "Math", "random", "floor", "toString", "generateHashCode", "string", "hash", "getRelativeUrl", "originalUrl", "targetUrl", "original", "target", "protocol", "relativePath", "substr", "startIndexOffset", "getHostFromUrl", "urlString", "host", "parseUserAgent", "uaString", "stringHasProtocol", "bufferSourceToDataView", "bufferSource", "toDataView", "DataView", "bufferSourceToInt8", "Uint8Array", "uint8ArrayToString", "uint8Array", "TextDecoder", "decode", "bufferSourceToHex", "data", "hex", "Type", "buffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bytesPerElement", "BYTES_PER_ELEMENT", "dataEnd", "byteOffset", "byteLength", "rawStart", "max", "min", "Infinity", "view", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getCodecFamily", "codecString", "profile", "_getCodecParts", "Constants", "CODEC_FAMILIES", "MP3", "AAC", "AC3", "EC3", "DTSX", "DTSC", "AVC", "HEVC", "rest", "errors", "config", "publicOnly", "err", "events", "evt", "ACCESSIBILITY", "ADAPTATION_SET", "ADAPTATION_SETS", "ADAPTATION_SET_SWITCHING_SCHEME_ID_URI", "ADD", "ASSET_IDENTIFIER", "AUDIO_CHANNEL_CONFIGURATION", "AUDIO_SAMPLING_RATE", "AVAILABILITY_END_TIME", "AVAILABILITY_START_TIME", "AVAILABILITY_TIME_COMPLETE", "AVAILABILITY_TIME_OFFSET", "BANDWITH", "BASE_URL", "BITSTREAM_SWITCHING", "BITSTREAM_SWITCHING_MINUS", "BYTE_RANGE", "CAPTION", "CENC_DEFAULT_KID", "CLIENT_DATA_REPORTING", "CLIENT_REQUIREMENT", "CMCD_PARAMETERS", "CODECS", "CODEC_PRIVATE_DATA", "CODING_DEPENDENCY", "CONTENT_COMPONENT", "CONTENT_PROTECTION", "CONTENT_STEERING", "CONTENT_STEERING_RESPONSE", "TTL", "RELOAD_URI", "PATHWAY_PRIORITY", "PATHWAY_CLONES", "BASE_ID", "ID", "URI_REPLACEMENT", "HOST", "PARAMS", "CONTENT_TYPE", "DEFAULT_SERVICE_LOCATION", "DEPENDENCY_ID", "DURATION", "DVB_PRIORITY", "DVB_WEIGHT", "DVB_URL", "DVB_MIMETYPE", "DVB_FONTFAMILY", "DYNAMIC", "END_NUMBER", "ESSENTIAL_PROPERTY", "EVENT", "EVENT_STREAM", "FORCED_SUBTITLE", "FRAMERATE", "FRAME_PACKING", "GROUP_LABEL", "HEIGHT", "INBAND", "INBAND_EVENT_STREAM", "INDEX", "INDEX_RANGE", "INITIALIZATION", "INITIALIZATION_MINUS", "LA_URL", "LA_URL_LOWER_CASE", "LABEL", "LANG", "LOCATION", "MAIN", "MAXIMUM_SAP_PERIOD", "MAX_PLAYOUT_RATE", "MAX_SEGMENT_DURATION", "MAX_SUBSEGMENT_DURATION", "MEDIA", "MEDIA_PRESENTATION_DURATION", "MEDIA_RANGE", "MEDIA_STREAM_STRUCTURE_ID", "METRICS", "METRICS_MINUS", "MIME_TYPE", "MINIMUM_UPDATE_PERIOD", "MIN_BUFFER_TIME", "MP4_PROTECTION_SCHEME", "MPD", "MPD_TYPE", "MPD_PATCH_TYPE", "ORIGINAL_MPD_ID", "ORIGINAL_PUBLISH_TIME", "PATCH_LOCATION", "PERIOD", "PRESENTATION_TIME", "PRESENTATION_TIME_OFFSET", "PRO", "PRODUCER_REFERENCE_TIME", "PRODUCER_REFERENCE_TIME_TYPE", "ENCODER", "CAPTURED", "APPLICATION", "PROFILES", "PSSH", "PUBLISH_TIME", "QUALITY_RANKING", "QUERY_BEFORE_START", "QUERY_PART", "RANGE", "RATING", "REF", "REF_ID", "REMOVE", "REPLACE", "REPORTING", "REPRESENTATION", "REPRESENTATION_INDEX", "ROBUSTNESS", "ROLE", "S", "SAR", "SCAN_TYPE", "SEGMENT_ALIGNMENT", "SEGMENT_BASE", "SEGMENT_LIST", "SEGMENT_PROFILES", "SEGMENT_TEMPLATE", "SEGMENT_TIMELINE", "SEGMENT_TYPE", "SEGMENT_URL", "SERVICE_DESCRIPTION", "SERVICE_DESCRIPTION_LATENCY", "SERVICE_DESCRIPTION_OPERATING_BANDWIDTH", "SERVICE_DESCRIPTION_OPERATING_QUALITY", "SERVICE_DESCRIPTION_PLAYBACK_RATE", "SERVICE_DESCRIPTION_SCOPE", "SERVICE_LOCATION", "SERVICE_LOCATIONS", "SOURCE_URL", "START", "START_NUMBER", "START_WITH_SAP", "STATIC", "STEERING_TYPE", "SUBSET", "SUBTITLE", "SUB_REPRESENTATION", "SUB_SEGMENT_ALIGNMENT", "SUGGESTED_PRESENTATION_DELAY", "SUPPLEMENTAL_PROPERTY", "SUPPLEMENTAL_CODECS", "TIMESCALE", "TIMESHIFT_BUFFER_DEPTH", "UTC_TIMING", "VALUE", "VIEWPOINT", "WALL_CLOCK_TIME", "WIDTH", "STREAM", "VIDEO", "AUDIO", "TEXT", "MUXED", "IMAGE", "STPP", "TTML", "VTT", "WVTT", "LIVE_CATCHUP_MODE_DEFAULT", "LIVE_CATCHUP_MODE_LOLP", "MOVING_AVERAGE_SLIDING_WINDOW", "MOVING_AVERAGE_EWMA", "BAD_ARGUMENT_ERROR", "MISSING_CONFIG_ERROR", "TRACK_SWITCH_MODE_ALWAYS_REPLACE", "TRACK_SWITCH_MODE_NEVER_REPLACE", "TRACK_SELECTION_MODE_FIRST_TRACK", "TRACK_SELECTION_MODE_HIGHEST_BITRATE", "TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY", "TRACK_SELECTION_MODE_WIDEST_RANGE", "CMCD_QUERY_KEY", "CMCD_MODE_QUERY", "CMCD_MODE_HEADER", "CMCD_AVAILABLE_KEYS", "CMCD_V2_AVAILABLE_KEYS", "CMCD_AVAILABLE_REQUESTS", "INITIALIZE", "TEXT_SHOWING", "TEXT_HIDDEN", "TEXT_DISABLED", "ACCESSIBILITY_CEA608_SCHEME", "CC1", "CC3", "UTF8", "SCHEME_ID_URI", "START_TIME", "SERVICE_DESCRIPTION_DVB_LL_SCHEME", "SUPPLEMENTAL_PROPERTY_DVB_LL_SCHEME", "CTA_5004_2023_SCHEME", "THUMBNAILS_SCHEME_ID_URIS", "FONT_DOWNLOAD_DVB_SCHEME", "COLOUR_PRIMARIES_SCHEME_ID_URI", "URL_QUERY_INFO_SCHEME", "EXT_URL_QUERY_INFO_SCHEME", "MATRIX_COEFFICIENTS_SCHEME_ID_URI", "TRANSFER_CHARACTERISTICS_SCHEME_ID_URI", "HDR_METADATA_FORMAT_SCHEME_ID_URI", "HDR_METADATA_FORMAT_VALUES", "ST2094_10", "SL_HDR2", "ST2094_40", "MEDIA_CAPABILITIES_API", "COLORGAMUT", "SRGB", "P3", "REC2020", "TRANSFERFUNCTION", "PQ", "HLG", "HDR_METADATATYPE", "SMPTE_ST_2094_10", "SLHDR2", "SMPTE_ST_2094_40", "XML", "ARRAY_BUFFER", "DVB_REPORTING_URL", "DVB_PROBABILITY", "OFF_MIMETYPE", "WOFF_MIMETYPE", "VIDEO_ELEMENT_READY_STATES", "HAVE_NOTHING", "HAVE_METADATA", "HAVE_CURRENT_DATA", "HAVE_FUTURE_DATA", "HAVE_ENOUGH_DATA", "FILE_LOADER_TYPES", "FETCH", "XHR", "THROUGHPUT_TYPES", "LATENCY", "BANDWIDTH", "THROUGHPUT_CALCULATION_MODES", "EWMA", "ZLEMA", "ARITHMETIC_MEAN", "BYTE_SIZE_WEIGHTED_ARITHMETIC_MEAN", "DATE_WEIGHTED_ARITHMETIC_MEAN", "HARMONIC_MEAN", "BYTE_SIZE_WEIGHTED_HARMONIC_MEAN", "DATE_WEIGHTED_HARMONIC_MEAN", "LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE", "MOOF_PARSING", "DOWNLOADED_DATA", "AAST", "RULES_TYPES", "QUALITY_SWITCH_RULES", "ABANDON_FRAGMENT_RULES", "BOLA_RULE", "THROUGHPUT_RULE", "INSUFFICIENT_BUFFER_RULE", "SWITCH_HISTORY_RULE", "DROPPED_FRAMES_RULE", "LEARN_TO_ADAPT_RULE", "LOL_PLUS_RULE", "ABANDON_REQUEST_RULE", "ID3_SCHEME_ID_URI", "COMMON_ACCESS_TOKEN_HEADER", "DASH_ROLE_SCHEME_ID", "CLEARKEY_KEYSTEM_STRING", "WIDEVINE_KEYSTEM_STRING", "PLAYREADY_KEYSTEM_STRING", "PLAYREADY_RECOMMENDATION_KEYSTEM_STRING", "WIDEVINE_UUID", "PLAYREADY_UUID", "CLEARKEY_UUID", "W3C_CLEARKEY_UUID", "INITIALIZATION_DATA_TYPE_CENC", "INITIALIZATION_DATA_TYPE_KEYIDS", "INITIALIZATION_DATA_TYPE_WEBM", "ENCRYPTION_SCHEME_CENC", "ENCRYPTION_SCHEME_CBCS", "MEDIA_KEY_MESSAGE_TYPES", "LICENSE_REQUEST", "LICENSE_RENEWAL", "LICENSE_RELEASE", "INDIVIDUALIZATION_REQUEST", "ROBUSTNESS_STRINGS", "WIDEVINE", "SW_SECURE_CRYPTO", "SW_SECURE_DECODE", "HW_SECURE_CRYPTO", "HW_SECURE_DECODE", "HW_SECURE_ALL", "MEDIA_KEY_STATUSES", "USABLE", "EXPIRED", "RELEASED", "OUTPUT_RESTRICTED", "OUTPUT_DOWNSCALED", "STATUS_PENDING", "INTERNAL_ERROR", "ProtectionEvents", "EventsBase", "constructor", "super", "INTERNAL_KEY_MESSAGE", "INTERNAL_KEY_STATUSES_CHANGED", "KEY_ADDED", "KEY_ERROR", "KEY_MESSAGE", "KEY_SESSION_CLOSED", "KEY_SESSION_CREATED", "KEY_SESSION_REMOVED", "KEY_STATUSES_CHANGED", "KEY_STATUSES_MAP_UPDATED", "KEY_SYSTEM_ACCESS_COMPLETE", "KEY_SYSTEM_SELECTED", "LICENSE_REQUEST_COMPLETE", "LICENSE_REQUEST_SENDING", "NEED_KEY", "PROTECTION_CREATED", "PROTECTION_DESTROYED", "SERVER_CERTIFICATE_UPDATED", "TEARDOWN_COMPLETE", "VIDEO_ELEMENT_SELECTED", "KEY_SESSION_UPDATED", "protectionEvents", "ProtectionErrors", "ErrorsBase", "MEDIA_KEYERR_CODE", "MEDIA_KEYERR_UNKNOWN_CODE", "MEDIA_KEYERR_CLIENT_CODE", "MEDIA_KEYERR_SERVICE_CODE", "MEDIA_KEYERR_OUTPUT_CODE", "MEDIA_KEYERR_HARDWARECHANGE_CODE", "MEDIA_KEYERR_DOMAIN_CODE", "MEDIA_KEY_MESSAGE_ERROR_CODE", "MEDIA_KEY_MESSAGE_NO_CHALLENGE_ERROR_CODE", "SERVER_CERTIFICATE_UPDATED_ERROR_CODE", "KEY_STATUS_CHANGED_EXPIRED_ERROR_CODE", "MEDIA_KEY_MESSAGE_NO_LICENSE_SERVER_URL_ERROR_CODE", "KEY_SYSTEM_ACCESS_DENIED_ERROR_CODE", "KEY_SESSION_CREATED_ERROR_CODE", "MEDIA_KEY_MESSAGE_LICENSER_ERROR_CODE", "MEDIA_KEYERR_UNKNOWN_MESSAGE", "MEDIA_KEYERR_CLIENT_MESSAGE", "MEDIA_KEYERR_SERVICE_MESSAGE", "MEDIA_KEYERR_OUTPUT_MESSAGE", "MEDIA_KEYERR_HARDWARECHANGE_MESSAGE", "MEDIA_KEYERR_DOMAIN_MESSAGE", "MEDIA_KEY_MESSAGE_ERROR_MESSAGE", "MEDIA_KEY_MESSAGE_NO_CHALLENGE_ERROR_MESSAGE", "SERVER_CERTIFICATE_UPDATED_ERROR_MESSAGE", "KEY_STATUS_CHANGED_EXPIRED_ERROR_MESSAGE", "MEDIA_KEY_MESSAGE_NO_LICENSE_SERVER_URL_ERROR_MESSAGE", "KEY_SYSTEM_ACCESS_DENIED_ERROR_MESSAGE", "KEY_SESSION_CREATED_ERROR_MESSAGE", "MEDIA_KEY_MESSAGE_LICENSER_ERROR_MESSAGE", "protectionErrors", "message", "HTTPRequest", "tcpid", "type", "<PERSON><PERSON><PERSON>", "range", "trequest", "tresponse", "responsecode", "interval", "trace", "cmsd", "_stream", "_tfinish", "_mediaduration", "_responseHeaders", "_serviceLocation", "_fileLoaderType", "_resourceTimingValues", "GET", "HEAD", "XLINK_EXPANSION_TYPE", "INIT_SEGMENT_TYPE", "INDEX_SEGMENT_TYPE", "MEDIA_SEGMENT_TYPE", "BITSTREAM_SWITCHING_SEGMENT_TYPE", "MSS_FRAGMENT_INFO_SEGMENT_TYPE", "DVB_REPORTING_TYPE", "LICENSE", "CONTENT_STEERING_TYPE", "OTHER_TYPE", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "amdO", "d", "definition", "o", "Object", "defineProperty", "enumerable", "prototype", "LICENSE_SERVER_MANIFEST_CONFIGURATIONS", "prefixes", "CommonEncryption", "findMp4ProtectionElement", "cpA<PERSON>y", "retVal", "cp", "schemeIdUri", "DashConstants", "ProtectionConstants", "getPSSHData", "pssh", "offset", "getUint8", "getUint32", "getPSSHForKeySystem", "keySystem", "initData", "psshList", "parsePSSHList", "uuid", "parseInitDataFromContentProtection", "cpData", "BASE64", "__text", "decodeArray", "dv", "byteCursor", "nextBox", "systemID", "val", "boxStart", "getLicenseServerUrlFromMediaInfo", "mediaInfoArr", "licenseServer", "mediaInfo", "contentProtection", "targetProtectionData", "laUrl", "__prefix", "hexKidToBufferSource", "hexKid", "cleanedHexKid", "h", "parseInt", "contentType", "robustness", "audioCapabilities", "videoCapabilities", "distinctiveIdentifier", "persistentState", "sessionTypes", "initDataTypes", "method", "responseType", "withCredentials", "messageType", "sessionId", "ProtectionController", "cmcdModel", "constants", "customParametersModel", "debug", "eventBus", "protectionKeyController", "settings", "applicationProvidedProtectionData", "keyStatusMap", "keySystemSelectionInProgress", "licenseRequestRetryTimeout", "licenseXhrRequest", "logger", "pendingMediaTypesToHandle", "robustnessLevel", "selectedKeySystem", "sessionType", "protectionModel", "needkeyRetries", "_checkConfig", "Error", "_selectKeySystemOrUpdateKeySessions", "supportedKeySystemsMetadata", "fromManifest", "_handlePendingMediaTypes", "supportedKeySystems", "keySystemConfigurationsToRequest", "keySystemConfiguration", "_getKeySystemConfiguration", "ks", "configs", "protData", "_getKeySystemConfigurations", "sort", "ksA", "ksB", "systemString", "priority", "keySystemAccess", "requestKeySystemAccess", "then", "event", "selectedSystemString", "info", "selectKeySystem", "_onKeySystemAccessed", "trigger", "_getProtDataForKeySystem", "serverCertificate", "setServerCertificate", "_onMediaKeysCreated", "catch", "error", "DashJSError", "_handleKeySystemSelectionError", "_selectInitialKeySystem", "ksIdx", "keySystemMetadata", "isClear<PERSON>ey", "keys", "clearkeys", "kids", "TextEncoder", "encode", "_handleClearkeySession", "loadKeySession", "createKeySession", "keySystemData", "audioRobustness", "videoRobustness", "ksSessionType", "media", "MediaCapability", "codec", "KeySystemConfiguration", "keyId", "sessions", "getSessionTokens", "getKeyId", "_doesSessionForKeyIdExists", "initDataForKS", "_isInitDataDuplicate", "keySystemString", "currentInitData", "getAllInitData", "initDataEquals", "setMediaElement", "element", "on", "_on<PERSON><PERSON><PERSON><PERSON>", "off", "_onKeyMessage", "keyMessage", "sessionToken", "licenseServerModelInstance", "getLicenseServerModelInstance", "eventData", "getSessionId", "_sendLicenseRequestCompleteEvent", "processClearKeyLicenseRequest", "keyPairs", "updateKeySession", "licenseServerData", "serverURL", "laURL", "schemeIdURI", "psshData", "getLicenseServerURLFromInitData", "getServerURLFromMessage", "_getLicenseServerUrl", "reqHeaders", "_updateHeaders", "httpRequestHeaders", "getRequestHeadersFromMessage", "onLoad", "xhr", "status", "responseHeaders", "getAllResponseHeaders", "licenseResponse", "LicenseResponse", "responseURL", "response", "_applyFilters", "getLicenseResponseFilters", "licenseMessage", "getLicenseMessage", "_reportError", "onAbort", "statusText", "readyState", "onError", "reqPayload", "getLicenseRequestFromMessage", "req<PERSON><PERSON><PERSON>", "getHTTPMethod", "getResponseType", "timeout", "isNaN", "httpTimeout", "licenseRequest", "LicenseRequest", "retryAttempts", "streaming", "getLicenseRequestFilters", "_doLicenseRequest", "_issueLicenseRequest", "request", "retriesCount", "XMLHttpRequest", "cmcdParameters", "getCmcdParametersFromManifest", "isCmcdEnabled", "mode", "cmcd", "cmcdParams", "getQueryParameter", "open", "setRequestHeader", "cmcdHeaders", "getHeaderParameters", "header", "_retryRequest", "retryInterval", "retryIntervals", "setTimeout", "onload", "warn", "ontimeout", "onerror", "<PERSON>ab<PERSON>", "payload", "send", "_abortLicenseRequest", "onloadend", "onprogress", "abort", "clearTimeout", "errorMsg", "getErrorResponse", "serverResponse", "responseCode", "responseText", "filters", "param", "reduce", "prev", "next", "Promise", "retry", "protection", "ignoreEmeEncryptedEvent", "initDataType", "abInitData", "<PERSON><PERSON><PERSON><PERSON>", "String", "fromCharCode", "getSupportedKeySystemMetadataFromSegmentPssh", "_handleKeySystemFromPssh", "_shouldCheckKeyStatusMap", "normalizedKeyIds", "every", "normalizedKeyId", "keyStatus", "sessionTokens", "targetSessionTokens", "some", "hasTriggeredKeyStatusMapUpdate", "ignoreKeyStatuses", "areKeyIdsExpired", "areKeyIdsUsable", "clearMediaInfoArray", "closeKeySession", "getKeySystems", "getSupportedKeySystemMetadataFromContentProtection", "cps", "handleKeySystemFromManifest", "keySystemsMetadata", "initializeForMedia", "removeKeySession", "reset", "Map", "retryTimeout", "setKeySystems", "keySystems", "setProtectionData", "setRobustnessLevel", "level", "setSessionType", "stop", "updateKeyStatusesMap", "parsedKeyStatuses", "isEdge<PERSON><PERSON>er", "dataView", "part0", "part1", "getUint16", "part2", "setUint32", "setUint16", "_handlePlayreadyKeyId", "keyIdInHex", "<PERSON><PERSON><PERSON><PERSON>", "keyID", "toJWK", "num<PERSON>eys", "jwk", "kty", "alg", "kid", "jwkString", "buf", "b<PERSON><PERSON><PERSON>", "KeySystemClearKey", "getInitData", "cencContentProtection", "cencDefaultKid", "btoa", "a", "cencDefaultKidToBase64Representation", "getCDMData", "getClearKeysFromProtectionData", "protectionData", "clearkeySet", "jsonMsg", "clearkeyID", "clearkey", "KeyPair", "ClearKeyKeySet", "KeySystemW3CClearKey", "KeySystemWidevine", "KeySystemPlayReady", "messageFormat", "checkConfig", "PSSHBoxType", "playreadySystemID", "PROSize", "PSSHSize", "PSSHBoxBuffer", "PSSHBox", "PSSHData", "uint8arraydecodedPROHeader", "pro", "prheader", "msg", "xmlDoc", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detectPlayreadyMessageFormat", "dataview", "Uint16Array", "parseFromString", "headerNameList", "getElementsByTagName", "headerValueList", "childNodes", "nodeValue", "Content", "Challenge", "numRecords", "recordType", "recordLength", "recordData", "record", "laurl", "luiurl", "_cdmData", "customData", "cdmData", "cdmDataBytes", "setPlayReadyMessageFormat", "DRMToday", "license", "keySystemStr", "PlayReady", "soap", "uintToString", "arrayBuffer", "encodedString", "escape", "parseServerResponse", "stringResponse", "envelope", "getElementsByTagNameNS", "body", "parseErrorResponse", "faultstring", "statusCode", "idStart", "idEnd", "fault", "detail", "exception", "node", "<PERSON><PERSON><PERSON><PERSON>", "errorString", "<PERSON><PERSON><PERSON>", "ClearKey", "keypair", "keyid", "ProtectionKeyController", "clearkeyKeySystem", "clearkeyW3CKeySystem", "protDataSet", "_getSessionId", "_getSessionType", "getKeySystemBySystemString", "contentProtectionElements", "applicationSpecifiedProtectionData", "contentProtectionElement", "cpIdx", "supportedKS", "mp4ProtectionElement", "KeySystemMetadata", "unshift", "initData1", "initData2", "data1", "data2", "initialize", "setConfig", "newKeySystems", "protectionDataSet", "init", "defaultURL", "ksConfiguration", "nativeMediaKeySystemAccessObject", "SYSTEM_STRING_PRIORITY", "DefaultProtectionModel", "videoElement", "mediaKeys", "<PERSON><PERSON><PERSON><PERSON>", "_requestKeySystemAccessInternal", "idx", "reject", "requestMediaKeySystemAccess", "protDataSystemStringPriority", "systemStringPriority", "currentKeySystem", "systemStringsToApply", "_checkAccessForSystemStrings", "_checkAccessForKeySystem", "configuration", "getConfiguration", "KeySystemAccess", "errorMessage", "mediaKeySystemAccess", "_closeKeySessionInternal", "session", "removeEventListener", "close", "removeSession", "token", "splice", "_createSessionToken", "handleEvent", "_onKeyStatusesChange", "keyStatuses", "_parseKeyStatus", "KeyMessage", "getSessionType", "getExpirationTime", "expiration", "getKeyStatuses", "getUsable", "usable", "addEventListener", "closed", "mediaKeySession", "createSession", "dataType", "generateRequest", "load", "success", "remove", "numSessions", "done", "setMediaKeys", "createMediaKeys", "mkeys", "mediaElement", "update", "<PERSON><PERSON><PERSON>", "ProtectionModel_3Feb2014", "api", "needkey", "ready", "release", "boundDoSetKeys", "doSetKeys", "bind", "ksConfigurations", "found", "supportedAudio", "supportedVideo", "configIdx", "audios", "videos", "audioIdx", "MediaKeys", "isTypeSupported", "videoIdx", "ksConfig", "ksAccess", "ksInfo", "capabilities", "keySession", "NaN", "errorStr", "destinationURL", "createSessionToken", "ProtectionModel_01b", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pendingSessions", "moreSessionsAllowed", "removeEventListeners", "cancelKeyRequest", "findSessionByID", "sessionA<PERSON>y", "keyerror", "keymessage", "keyadded", "ve", "document", "createElement", "canPlayType", "newSession", "generateKeyRequest", "<PERSON><PERSON><PERSON>", "errorCode", "systemCode", "shift", "APIS_ProtectionModel_01b", "APIS_ProtectionModel_3Feb2014", "Protection", "_getAPI", "apis", "createProtectionSystem", "controller", "videoModel", "getElement", "onencrypted", "_getProtectionModel", "setEncryptedMediaSupported", "dashjs"], "sourceRoot": ""}