/* videojs-vtt.js - v0.15.2 (https://github.com/videojs/vtt.js) built on 26-09-2022 */
!function(a){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=a();else if("function"==typeof define&&define.amd)define([],a);else{var b;b="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,b.vttjs=a()}}(function(){return function(){function a(b,c,d){function e(g,h){if(!c[g]){if(!b[g]){var i="function"==typeof require&&require;if(!h&&i)return i(g,!0);if(f)return f(g,!0);var j=new Error("Cannot find module '"+g+"'");throw j.code="MODULE_NOT_FOUND",j}var k=c[g]={exports:{}};b[g][0].call(k.exports,function(a){return e(b[g][1][a]||a)},k,k.exports,a,b,c,d)}return c[g].exports}for(var f="function"==typeof require&&require,g=0;g<d.length;g++)e(d[g]);return e}return a}()({1:[function(a,b,c){var d=a("./process/parse-content.js"),e=function(a,b){return a&&b?d(a,b):null};b.exports=e},{"./process/parse-content.js":14}],2:[function(a,b,c){function d(a,b,c){function d(){var b=h(a);if(null===b)throw new e(e.Errors.BadTimeStamp,"Malformed timestamp: "+k);return a=a.replace(/^[^\sa-zA-Z-]+/,""),b}function i(a,b){var d=new f;g(a,function(a,b){switch(a){case"region":for(var e=c.length-1;e>=0;e--)if(c[e].id===b){d.set(a,c[e].region);break}break;case"vertical":d.alt(a,b,["rl","lr"]);break;case"line":var f=b.split(","),g=f[0];d.integer(a,g),d.percent(a,g)&&d.set("snapToLines",!1),d.alt(a,g,["auto"]),2===f.length&&d.alt("lineAlign",f[1],["start","center","end"]);break;case"position":f=b.split(","),d.percent(a,f[0]),2===f.length&&d.alt("positionAlign",f[1],["start","center","end"]);break;case"size":d.percent(a,b);break;case"align":d.alt(a,b,["start","center","end","left","right"])}},/:/,/\s/),b.region=d.get("region",null),b.vertical=d.get("vertical","");try{b.line=d.get("line","auto")}catch(a){}b.lineAlign=d.get("lineAlign","start"),b.snapToLines=d.get("snapToLines",!0),b.size=d.get("size",100);try{b.align=d.get("align","center")}catch(a){b.align=d.get("align","middle")}try{b.position=d.get("position","auto")}catch(a){b.position=d.get("position",{start:0,left:0,center:50,middle:50,end:100,right:100},b.align)}b.positionAlign=d.get("positionAlign",{start:"start",left:"start",center:"center",middle:"center",end:"end",right:"end"},b.align)}function j(){a=a.replace(/^\s+/,"")}var k=a;if(j(),b.startTime=d(),j(),"--\x3e"!==a.substr(0,3))throw new e(e.Errors.BadTimeStamp,"Malformed time stamp (time stamps must be separated by '--\x3e'): "+k);a=a.substr(3),j(),b.endTime=d(),j(),i(a,b)}var e=a("./parsing-error.js"),f=a("./settings.js"),g=a("./parse-options.js"),h=a("./parse-timestamp.js");b.exports=d},{"./parse-options.js":3,"./parse-timestamp.js":4,"./parsing-error.js":6,"./settings.js":7}],3:[function(a,b,c){function d(a,b,c,d){var e=d?a.split(d):[a];for(var f in e)if("string"==typeof e[f]){var g=e[f].split(c);if(2===g.length){var h=g[0].trim(),i=g[1].trim();b(h,i)}}}b.exports=d},{}],4:[function(a,b,c){function d(a){function b(a,b,c,d){return 3600*(0|a)+60*(0|b)+(0|c)+(0|d)/1e3}var c=a.match(/^(\d+):(\d{1,2})(:\d{1,2})?\.(\d{3})/);return c?c[3]?b(c[1],c[2],c[3].replace(":",""),c[4]):c[1]>59?b(c[1],c[2],0,c[4]):b(0,c[1],c[2],c[4]):null}b.exports=d},{}],5:[function(a,b,c){var d=a("./parsing-error.js"),e=a("./settings.js"),f=a("./parse-options.js"),g=a("./parse-cue.js"),h=a("./parse-timestamp.js"),i=function(a,b,c){c||(c=b,b={}),b||(b={}),this.window=a,this.vttjs=b,this.state="INITIAL",this.buffer="",this.decoder=c||new a.TextDecoder("utf8"),this.regionList=[]};i.prototype.reportOrThrowError=function(a){if(!(a instanceof d))throw a;this.onparsingerror&&this.onparsingerror(a)},i.prototype.parse=function(a){function b(){for(var a=k.buffer,b=0;b<a.length&&"\r"!==a[b]&&"\n"!==a[b];)++b;var c=a.substr(0,b);return"\r"===a[b]&&++b,"\n"===a[b]&&++b,k.buffer=a.substr(b),c}function c(a){var b=new e;if(f(a,function(a,c){switch(a){case"id":b.set(a,c);break;case"width":b.percent(a,c);break;case"lines":b.integer(a,c);break;case"regionanchor":case"viewportanchor":var d=c.split(",");if(2!==d.length)break;var f=new e;if(f.percent("x",d[0]),f.percent("y",d[1]),!f.has("x")||!f.has("y"))break;b.set(a+"X",f.get("x")),b.set(a+"Y",f.get("y"));break;case"scroll":b.alt(a,c,["up"])}},/=/,/\s/),b.has("id")){var c=new(k.vttjs.VTTRegion||k.window.VTTRegion);c.width=b.get("width",100),c.lines=b.get("lines",3),c.regionAnchorX=b.get("regionanchorX",0),c.regionAnchorY=b.get("regionanchorY",100),c.viewportAnchorX=b.get("viewportanchorX",0),c.viewportAnchorY=b.get("viewportanchorY",100),c.scroll=b.get("scroll",""),k.onregion&&k.onregion(c),k.regionList.push({id:b.get("id"),region:c})}}function i(a){var b=new e;f(a,function(a,c){switch(a){case"MPEGT":b.integer(a+"S",c);break;case"LOCA":b.set(a+"L",h(c))}},/[^\d]:/,/,/),k.ontimestampmap&&k.ontimestampmap({MPEGTS:b.get("MPEGTS"),LOCAL:b.get("LOCAL")})}function j(a){a.match(/X-TIMESTAMP-MAP/)?f(a,function(a,b){switch(a){case"X-TIMESTAMP-MAP":i(b)}},/=/):f(a,function(a,b){switch(a){case"Region":c(b)}},/:/)}var k=this;a&&(k.buffer+=k.decoder.decode(a,{stream:!0}));try{var l;if("INITIAL"===k.state){if(!/\r\n|\n/.test(k.buffer))return this;l=b();var m=l.match(/^WEBVTT([ \t].*)?$/);if(!m||!m[0])throw new d(d.Errors.BadSignature);k.state="HEADER"}for(var n=!1;k.buffer;){if(!/\r\n|\n/.test(k.buffer))return this;switch(n?n=!1:l=b(),k.state){case"HEADER":/:/.test(l)?j(l):l||(k.state="ID");continue;case"NOTE":l||(k.state="ID");continue;case"ID":if(/^NOTE($|[ \t])/.test(l)){k.state="NOTE";break}if(!l)continue;k.cue=new(k.vttjs.VTTCue||k.window.VTTCue)(0,0,"");try{k.cue.align="center"}catch(a){k.cue.align="middle"}if(k.state="CUE",-1===l.indexOf("--\x3e")){k.cue.id=l;continue}case"CUE":try{g(l,k.cue,k.regionList)}catch(a){k.reportOrThrowError(a),k.cue=null,k.state="BADCUE";continue}k.state="CUETEXT";continue;case"CUETEXT":var o=-1!==l.indexOf("--\x3e");if(!l||o&&(n=!0)){k.oncue&&k.oncue(k.cue),k.cue=null,k.state="ID";continue}k.cue.text&&(k.cue.text+="\n"),k.cue.text+=l.replace(/\u2028/g,"\n").replace(/u2029/g,"\n");continue;case"BADCUE":l||(k.state="ID");continue}}}catch(a){k.reportOrThrowError(a),"CUETEXT"===k.state&&k.cue&&k.oncue&&k.oncue(k.cue),k.cue=null,k.state="INITIAL"===k.state?"BADWEBVTT":"BADCUE"}return this},i.prototype.flush=function(){var a=this;try{if(a.buffer+=a.decoder.decode(),(a.cue||"HEADER"===a.state)&&(a.buffer+="\n\n",a.parse()),"INITIAL"===a.state)throw new d(d.Errors.BadSignature)}catch(b){a.reportOrThrowError(b)}return a.onflush&&a.onflush(),this},b.exports=i},{"./parse-cue.js":2,"./parse-options.js":3,"./parse-timestamp.js":4,"./parsing-error.js":6,"./settings.js":7}],6:[function(a,b,c){function d(a,b){this.name="ParsingError",this.code=a.code,this.message=b||a.message}d.prototype=Object.create(Error.prototype),d.prototype.constructor=d,d.Errors={BadSignature:{code:0,message:"Malformed WebVTT signature."},BadTimeStamp:{code:1,message:"Malformed time stamp."}},b.exports=d},{}],7:[function(a,b,c){function d(){this.values=Object.create(null)}d.prototype={set:function(a,b){this.get(a)||""===b||(this.values[a]=b)},get:function(a,b,c){return c?this.has(a)?this.values[a]:b[c]:this.has(a)?this.values[a]:b},has:function(a){return a in this.values},alt:function(a,b,c){for(var d=0;d<c.length;++d)if(b===c[d]){this.set(a,b);break}},integer:function(a,b){/^-?\d+$/.test(b)&&this.set(a,parseInt(b,10))},percent:function(a,b){return!!(b.match(/^([\d]{1,3})(\.[\d]*)?%$/)&&(b=parseFloat(b))>=0&&b<=100)&&(this.set(a,b),!0)}},b.exports=d},{}],8:[function(a,b,c){function d(a){var b,c,d,e;if(a.div){c=a.div.offsetHeight,d=a.div.offsetWidth,e=a.div.offsetTop;var f=(f=a.div.childNodes)&&(f=f[0])&&f.getClientRects&&f.getClientRects();a=a.div.getBoundingClientRect(),b=f?Math.max(f[0]&&f[0].height||0,a.height/f.length):0}this.left=a.left,this.right=a.right,this.top=a.top||e,this.height=a.height||c,this.bottom=a.bottom||e+(a.height||c),this.width=a.width||d,this.lineHeight=void 0!==b?b:a.lineHeight}d.prototype.move=function(a,b){switch(b=void 0!==b?b:this.lineHeight,a){case"+x":this.left+=b,this.right+=b;break;case"-x":this.left-=b,this.right-=b;break;case"+y":this.top+=b,this.bottom+=b;break;case"-y":this.top-=b,this.bottom-=b}},d.prototype.overlaps=function(a){return this.left<a.right&&this.right>a.left&&this.top<a.bottom&&this.bottom>a.top},d.prototype.overlapsAny=function(a){for(var b=0;b<a.length;b++)if(this.overlaps(a[b]))return!0;return!1},d.prototype.within=function(a){return this.top>=a.top&&this.bottom<=a.bottom&&this.left>=a.left&&this.right<=a.right},d.prototype.overlapsOppositeAxis=function(a,b){switch(b){case"+x":return this.left<a.left;case"-x":return this.right>a.right;case"+y":return this.top<a.top;case"-y":return this.bottom>a.bottom}},d.prototype.intersectPercentage=function(a){return Math.max(0,Math.min(this.right,a.right)-Math.max(this.left,a.left))*Math.max(0,Math.min(this.bottom,a.bottom)-Math.max(this.top,a.top))/(this.height*this.width)},d.prototype.toCSSCompatValues=function(a){return{top:this.top-a.top,bottom:a.bottom-this.bottom,left:this.left-a.left,right:a.right-this.right,height:this.height,width:this.width}},d.getSimpleBoxPosition=function(a){var b=a.div?a.div.offsetHeight:a.tagName?a.offsetHeight:0,c=a.div?a.div.offsetWidth:a.tagName?a.offsetWidth:0,d=a.div?a.div.offsetTop:a.tagName?a.offsetTop:0;return a=a.div?a.div.getBoundingClientRect():a.tagName?a.getBoundingClientRect():a,{left:a.left,right:a.right,top:a.top||d,height:a.height||b,bottom:a.bottom||d+(a.height||b),width:a.width||c}},b.exports=d},{}],9:[function(a,b,c){function d(a){if("number"==typeof a.line&&(a.snapToLines||a.line>=0&&a.line<=100))return a.line;if(!a.track||!a.track.textTrackList||!a.track.textTrackList.mediaElement)return-1;for(var b=a.track,c=b.textTrackList,d=0,e=0;e<c.length&&c[e]!==b;e++)"showing"===c[e].mode&&d++;return-1*++d}b.exports=d},{}],10:[function(a,b,c){function d(a,b,c){e.call(this),this.cue=b,this.cueDiv=g(a,b.text);var d={color:"rgba(255, 255, 255, 1)",backgroundColor:"rgba(0, 0, 0, 0.8)",position:"relative",left:0,right:0,top:0,bottom:0,display:"inline",writingMode:""===b.vertical?"horizontal-tb":"lr"===b.vertical?"vertical-lr":"vertical-rl",unicodeBidi:"plaintext"};this.applyStyles(d,this.cueDiv),this.div=a.document.createElement("div"),d={direction:f(this.cueDiv),writingMode:""===b.vertical?"horizontal-tb":"lr"===b.vertical?"vertical-lr":"vertical-rl",unicodeBidi:"plaintext",textAlign:"middle"===b.align?"center":b.align,font:c.font,whiteSpace:"pre-line",position:"absolute"},this.applyStyles(d),this.div.appendChild(this.cueDiv);var h=0;switch(b.positionAlign){case"start":h=b.position;break;case"center":h=b.position-b.size/2;break;case"end":h=b.position-b.size}""===b.vertical?this.applyStyles({left:this.formatStyle(h,"%"),width:this.formatStyle(b.size,"%")}):this.applyStyles({top:this.formatStyle(h,"%"),height:this.formatStyle(b.size,"%")}),this.move=function(a){this.applyStyles({top:this.formatStyle(a.top,"px"),bottom:this.formatStyle(a.bottom,"px"),left:this.formatStyle(a.left,"px"),right:this.formatStyle(a.right,"px"),height:this.formatStyle(a.height,"px"),width:this.formatStyle(a.width,"px")})}}var e=a("./style-box.js"),f=a("./determine-bidi.js"),g=a("./parse-content.js");d.prototype=Object.create(e.prototype),d.prototype.constructor=d,b.exports=d},{"./determine-bidi.js":11,"./parse-content.js":14,"./style-box.js":16}],11:[function(a,b,c){function d(a){function b(a,b){for(var c=b.childNodes.length-1;c>=0;c--)a.push(b.childNodes[c])}function c(a){if(!a||!a.length)return null;var d=a.pop(),e=d.textContent||d.innerText;if(e){var f=e.match(/^.*(\n|\r)/);return f?(a.length=0,f[0]):e}return"ruby"===d.tagName?c(a):d.childNodes?(b(a,d),c(a)):void 0}var d,f=[],g="";if(!a||!a.childNodes)return"ltr";for(b(f,a);g=c(f);)for(var h=0;h<g.length;h++)if(d=g.charCodeAt(h),e(d))return"rtl";return"ltr"}var e=a("./is-strong-rtl-char.js");b.exports=d},{"./is-strong-rtl-char.js":12}],12:[function(a,b,c){function d(a){for(var b=0;b<e.length;b++){var c=e[b];if(a>=c[0]&&a<=c[1])return!0}return!1}var e=[[1470,1470],[1472,1472],[1475,1475],[1478,1478],[1488,1514],[1520,1524],[1544,1544],[1547,1547],[1549,1549],[1563,1563],[1566,1610],[1645,1647],[1649,1749],[1765,1766],[1774,1775],[1786,1805],[1807,1808],[1810,1839],[1869,1957],[1969,1969],[1984,2026],[2036,2037],[2042,2042],[2048,2069],[2074,2074],[2084,2084],[2088,2088],[2096,2110],[2112,2136],[2142,2142],[2208,2208],[2210,2220],[8207,8207],[64285,64285],[64287,64296],[64298,64310],[64312,64316],[64318,64318],[64320,64321],[64323,64324],[64326,64449],[64467,64829],[64848,64911],[64914,64967],[65008,65020],[65136,65140],[65142,65276],[67584,67589],[67592,67592],[67594,67637],[67639,67640],[67644,67644],[67647,67669],[67671,67679],[67840,67867],[67872,67897],[67903,67903],[67968,68023],[68030,68031],[68096,68096],[68112,68115],[68117,68119],[68121,68147],[68160,68167],[68176,68184],[68192,68223],[68352,68405],[68416,68437],[68440,68466],[68472,68479],[68608,68680],[126464,126467],[126469,126495],[126497,126498],[126500,126500],[126503,126503],[126505,126514],[126516,126519],[126521,126521],[126523,126523],[126530,126530],[126535,126535],[126537,126537],[126539,126539],[126541,126543],[126545,126546],[126548,126548],[126551,126551],[126553,126553],[126555,126555],[126557,126557],[126559,126559],[126561,126562],[126564,126564],[126567,126570],[126572,126578],[126580,126583],[126585,126588],[126590,126590],[126592,126601],[126603,126619],[126625,126627],[126629,126633],[126635,126651],[1114109,1114109]];b.exports=d},{}],13:[function(a,b,c){function d(a,b,c,d){function g(a,b){for(var f,g=new e(a),h=1,i=0;i<b.length;i++){for(;a.overlapsOppositeAxis(c,b[i])||a.within(c)&&a.overlapsAny(d);)a.move(b[i]);if(a.within(c))return a;var j=a.intersectPercentage(c);h>j&&(f=new e(a),h=j),a=new e(g)}return f||g}var h=new e(b),i=b.cue,j=f(i),k=[];if(i.snapToLines){var l;switch(i.vertical){case"":k=["+y","-y"],l="height";break;case"rl":k=["+x","-x"],l="width";break;case"lr":k=["-x","+x"],l="width"}var m=h.lineHeight,n=m*Math.round(j),o=c[l]+m,p=k[0];Math.abs(n)>o&&(n=n<0?-1:1,n*=Math.ceil(o/m)*m),j<0&&(n+=""===i.vertical?c.height:c.width,k=k.reverse()),h.move(p,n)}else{var q=h.lineHeight/c.height*100;switch(i.lineAlign){case"center":j-=q/2;break;case"end":j-=q}switch(i.vertical){case"":b.applyStyles({top:b.formatStyle(j,"%")});break;case"rl":b.applyStyles({left:b.formatStyle(j,"%")});break;case"lr":b.applyStyles({right:b.formatStyle(j,"%")})}k=["+y","-x","+x","-y"],h=new e(b)}var r=g(h,k);b.move(r.toCSSCompatValues(c))}var e=a("./box-position.js"),f=a("./compute-line-pos.js");b.exports=d},{"./box-position.js":8,"./compute-line-pos.js":9}],14:[function(a,b,c){function d(a,b){function c(){function a(a){return b=b.substr(a.length),a}if(!b)return null;var c=b.match(/^([^<]*)(<[^>]*>?)?/);return a(c[1]?c[1]:c[2])}function d(a){return g.innerHTML=a,a=g.textContent,g.textContent="",a}function e(a,b){return!h[b.localName]||h[b.localName]===a.localName}function l(b,c){var d=i[b];if(!d)return null;var e=a.document.createElement(d),f=j[b];return f&&c&&(e[f]=c.trim()),e}for(var m,n=a.document.createElement("div"),o=n,p=[];null!==(m=c());)if("<"!==m[0])o.appendChild(a.document.createTextNode(d(m)));else{if("/"===m[1]){p.length&&p[p.length-1]===m.substr(2).replace(">","")&&(p.pop(),o=o.parentNode);continue}var q,r=f(m.substr(1,m.length-2));if(r){q=a.document.createProcessingInstruction("timestamp",r),o.appendChild(q);continue}var s=m.match(/^<([^.\s/0-9>]+)(\.[^\s\\>]+)?([^>\\]+)?(\\?)>?$/);if(!s)continue;if(!(q=l(s[1],s[3])))continue;if(!e(o,q))continue;if(s[2]){var t=s[2].split(".");t.forEach(function(a){var b=/^bg_/.test(a),c=b?a.slice(3):a;if(Object.prototype.hasOwnProperty.call(k,c)){var d=b?"background-color":"color",e=k[c];q.style[d]=e}}),q.className=t.join(" ")}p.push(s[1]),o.appendChild(q),o=q}return n}var e=a("global/document"),f=a("../parser/parse-timestamp.js"),g=e.createElement&&e.createElement("textarea"),h={rt:"ruby"},i={c:"span",i:"i",b:"b",u:"u",ruby:"ruby",rt:"rt",v:"span",lang:"span"},j={v:"title",lang:"lang"},k={white:"rgba(255,255,255,1)",lime:"rgba(0,255,0,1)",cyan:"rgba(0,255,255,1)",red:"rgba(255,0,0,1)",yellow:"rgba(255,255,0,1)",magenta:"rgba(255,0,255,1)",blue:"rgba(0,0,255,1)",black:"rgba(0,0,0,1)"};b.exports=d},{"../parser/parse-timestamp.js":4,"global/document":21}],15:[function(a,b,c){var d=a("./box-position.js"),e=a("./cue-style-box.js"),f=a("./move-box-to-line-position.js"),g=.25,h="sans-serif",i="1.5%",j=function(a,b,c,j){function k(a){for(var b=0;b<a.length;b++)if(a[b].hasBeenReset||!a[b].displayState)return!0;return!1}if(!a||!b||!c)return null;for(;c.firstChild;)c.removeChild(c.firstChild);var l=a.document.createElement("div");if(l.style.position="absolute",l.style.left="0",l.style.right="0",l.style.top="0",l.style.bottom="0",l.style.margin=i,j&&(l.id=j),c.appendChild(l),k(b)){var m=[],n=d.getSimpleBoxPosition(l),o=Math.round(n.height*g*100)/100,p={font:o+"px "+h};!function(){for(var c,g,h=0;h<b.length;h++)g=b[h],c=new e(a,g,p),l.appendChild(c.div),f(a,c,n,m),g.displayState=c.div,m.push(d.getSimpleBoxPosition(c))}()}else for(var q=0;q<b.length;q++)l.appendChild(b[q].displayState)};b.exports=j},{"./box-position.js":8,"./cue-style-box.js":10,"./move-box-to-line-position.js":13}],16:[function(a,b,c){function d(){}d.prototype.applyStyles=function(a,b){b=b||this.div;for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&(b.style[c]=a[c])},d.prototype.formatStyle=function(a,b){return 0===a?0:a+b},b.exports=d},{}],17:[function(a,b,c){function d(a){return a.default||a}function e(){}e.StringDecoder=function(){return{decode:function(a){if(!a)return"";if("string"!=typeof a)throw new Error("Error - expected string data.");return decodeURIComponent(encodeURIComponent(a))}}},e.convertCueToDOMTree=d(a("./convert-cue-to-dom-tree.js")),e.processCues=d(a("./process/process-cues.js")),e.Parser=d(a("./parser/parser.js")),b.exports=e},{"./convert-cue-to-dom-tree.js":1,"./parser/parser.js":5,"./process/process-cues.js":15}],18:[function(a,b,c){function d(a){return a.default||a}function e(a){return"string"==typeof a&&(!!j[a.toLowerCase()]&&a.toLowerCase())}function f(a){return"string"==typeof a&&(!!k[a.toLowerCase()]&&a.toLowerCase())}function g(a,b,c){this.hasBeenReset=!1;var d="",g=!1,h=a,j=b,k=c,l=null,m="",n=!0,o="auto",p="start",q="auto",r="auto",s=100,t="center";Object.defineProperties(this,{id:{enumerable:!0,get:function(){return d},set:function(a){d=""+a}},pauseOnExit:{enumerable:!0,get:function(){return g},set:function(a){g=!!a}},startTime:{enumerable:!0,get:function(){return h},set:function(a){if("number"!=typeof a)throw new TypeError("Start time must be set to a number.");h=a,this.hasBeenReset=!0}},endTime:{enumerable:!0,get:function(){return j},set:function(a){if("number"!=typeof a)throw new TypeError("End time must be set to a number.");j=a,this.hasBeenReset=!0}},text:{enumerable:!0,get:function(){return k},set:function(a){k=""+a,this.hasBeenReset=!0}},region:{enumerable:!0,get:function(){return l},set:function(a){l=a,this.hasBeenReset=!0}},vertical:{enumerable:!0,get:function(){return m},set:function(a){var b=e(a);if(!1===b)throw new SyntaxError("Vertical: an invalid or illegal direction string was specified.");m=b,this.hasBeenReset=!0}},snapToLines:{enumerable:!0,get:function(){return n},set:function(a){n=!!a,this.hasBeenReset=!0}},line:{enumerable:!0,get:function(){return o},set:function(a){if("number"!=typeof a&&a!==i)throw new SyntaxError("Line: an invalid number or illegal string was specified.");o=a,this.hasBeenReset=!0}},lineAlign:{enumerable:!0,get:function(){return p},set:function(a){var b=f(a);b?(p=b,this.hasBeenReset=!0):console.warn("lineAlign: an invalid or illegal string was specified.")}},position:{enumerable:!0,get:function(){return q},set:function(a){if(a<0||a>100)throw new Error("Position must be between 0 and 100.");q=a,this.hasBeenReset=!0}},positionAlign:{enumerable:!0,get:function(){return r},set:function(a){var b=f(a);b?(r=b,this.hasBeenReset=!0):console.warn("positionAlign: an invalid or illegal string was specified.")}},size:{enumerable:!0,get:function(){return s},set:function(a){if(a<0||a>100)throw new Error("Size must be between 0 and 100.");s=a,this.hasBeenReset=!0}},align:{enumerable:!0,get:function(){return t},set:function(a){var b=f(a);if(!b)throw new SyntaxError("align: an invalid or illegal alignment string was specified.");t=b,this.hasBeenReset=!0}}}),this.displayState=void 0}var h=d(a("./convert-cue-to-dom-tree.js")),i="auto",j={"":1,lr:1,rl:1},k={start:1,center:1,end:1,left:1,right:1,auto:1,"line-left":1,"line-right":1};g.prototype.getCueAsHTML=function(){return h(window,this.text)},b.exports=g},{"./convert-cue-to-dom-tree.js":1}],19:[function(a,b,c){function d(a){return"string"==typeof a&&(!!g[a.toLowerCase()]&&a.toLowerCase())}function e(a){return"number"==typeof a&&a>=0&&a<=100}function f(){var a=100,b=3,c=0,f=100,g=0,h=100,i="";Object.defineProperties(this,{width:{enumerable:!0,get:function(){return a},set:function(b){if(!e(b))throw new Error("Width must be between 0 and 100.");a=b}},lines:{enumerable:!0,get:function(){return b},set:function(a){if("number"!=typeof a)throw new TypeError("Lines must be set to a number.");b=a}},regionAnchorY:{enumerable:!0,get:function(){return f},set:function(a){if(!e(a))throw new Error("RegionAnchorX must be between 0 and 100.");f=a}},regionAnchorX:{enumerable:!0,get:function(){return c},set:function(a){if(!e(a))throw new Error("RegionAnchorY must be between 0 and 100.");c=a}},viewportAnchorY:{enumerable:!0,get:function(){return h},set:function(a){if(!e(a))throw new Error("ViewportAnchorY must be between 0 and 100.");h=a}},viewportAnchorX:{enumerable:!0,get:function(){return g},set:function(a){if(!e(a))throw new Error("ViewportAnchorX must be between 0 and 100.");g=a}},scroll:{enumerable:!0,get:function(){return i},set:function(a){var b=d(a);!1===b?console.warn("Scroll: an invalid or illegal string was specified."):i=b}}})}var g={"":!0,up:!0};b.exports=f},{}],20:[function(a,b,c){},{}],21:[function(a,b,c){(function(c){var d,e=void 0!==c?c:"undefined"!=typeof window?window:{},f=a("min-document");"undefined"!=typeof document?d=document:(d=e["__GLOBAL_DOCUMENT_CACHE@4"])||(d=e["__GLOBAL_DOCUMENT_CACHE@4"]=f),b.exports=d}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"min-document":20}],22:[function(a,b,c){(function(a){var c;c="undefined"!=typeof window?window:void 0!==a?a:"undefined"!=typeof self?self:{},b.exports=c}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],23:[function(a,b,c){var d=a("global/window"),e=b.exports={WebVTT:a("./vtt.js"),VTTCue:a("./vttcue.js"),VTTRegion:a("./vttregion.js")};d.vttjs=e,d.WebVTT=e.WebVTT;var f=e.VTTCue,g=e.VTTRegion,h=d.VTTCue,i=d.VTTRegion;e.shim=function(){d.VTTCue=f,d.VTTRegion=g},e.restore=function(){d.VTTCue=h,d.VTTRegion=i},d.VTTCue||e.shim()},{"./vtt.js":17,"./vttcue.js":18,"./vttregion.js":19,"global/window":22}]},{},[23])(23)});