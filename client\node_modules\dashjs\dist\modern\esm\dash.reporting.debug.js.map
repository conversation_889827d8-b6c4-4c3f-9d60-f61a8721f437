{"version": 3, "file": "dash.reporting.debug.js", "mappings": ";;;;;;;;AAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEa;;AAEb,SAASA,UAAUA,CAACC,IAAI,EAAE;EACxB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5B,MAAM,IAAIC,SAAS,CAAC,kCAAkC,GAAGC,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC,CAAC;EAChF;AACF;;AAEA;AACA,SAASI,oBAAoBA,CAACJ,IAAI,EAAEK,cAAc,EAAE;EAClD,IAAIC,GAAG,GAAG,EAAE;EACZ,IAAIC,iBAAiB,GAAG,CAAC;EACzB,IAAIC,SAAS,GAAG,CAAC,CAAC;EAClB,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAIC,IAAI;EACR,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIX,IAAI,CAACY,MAAM,EAAE,EAAED,CAAC,EAAE;IACrC,IAAIA,CAAC,GAAGX,IAAI,CAACY,MAAM,EACjBF,IAAI,GAAGV,IAAI,CAACa,UAAU,CAACF,CAAC,CAAC,CAAC,KACvB,IAAID,IAAI,KAAK,EAAE,CAAC,OACnB,MAAM,KAENA,IAAI,GAAG,EAAE,CAAC;IACZ,IAAIA,IAAI,KAAK,EAAE,CAAC,OAAO;MACrB,IAAIF,SAAS,KAAKG,CAAC,GAAG,CAAC,IAAIF,IAAI,KAAK,CAAC,EAAE;QACrC;MAAA,CACD,MAAM,IAAID,SAAS,KAAKG,CAAC,GAAG,CAAC,IAAIF,IAAI,KAAK,CAAC,EAAE;QAC5C,IAAIH,GAAG,CAACM,MAAM,GAAG,CAAC,IAAIL,iBAAiB,KAAK,CAAC,IAAID,GAAG,CAACO,UAAU,CAACP,GAAG,CAACM,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,SAASN,GAAG,CAACO,UAAU,CAACP,GAAG,CAACM,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,OAAO;UAC3I,IAAIN,GAAG,CAACM,MAAM,GAAG,CAAC,EAAE;YAClB,IAAIE,cAAc,GAAGR,GAAG,CAACS,WAAW,CAAC,GAAG,CAAC;YACzC,IAAID,cAAc,KAAKR,GAAG,CAACM,MAAM,GAAG,CAAC,EAAE;cACrC,IAAIE,cAAc,KAAK,CAAC,CAAC,EAAE;gBACzBR,GAAG,GAAG,EAAE;gBACRC,iBAAiB,GAAG,CAAC;cACvB,CAAC,MAAM;gBACLD,GAAG,GAAGA,GAAG,CAACU,KAAK,CAAC,CAAC,EAAEF,cAAc,CAAC;gBAClCP,iBAAiB,GAAGD,GAAG,CAACM,MAAM,GAAG,CAAC,GAAGN,GAAG,CAACS,WAAW,CAAC,GAAG,CAAC;cAC3D;cACAP,SAAS,GAAGG,CAAC;cACbF,IAAI,GAAG,CAAC;cACR;YACF;UACF,CAAC,MAAM,IAAIH,GAAG,CAACM,MAAM,KAAK,CAAC,IAAIN,GAAG,CAACM,MAAM,KAAK,CAAC,EAAE;YAC/CN,GAAG,GAAG,EAAE;YACRC,iBAAiB,GAAG,CAAC;YACrBC,SAAS,GAAGG,CAAC;YACbF,IAAI,GAAG,CAAC;YACR;UACF;QACF;QACA,IAAIJ,cAAc,EAAE;UAClB,IAAIC,GAAG,CAACM,MAAM,GAAG,CAAC,EAChBN,GAAG,IAAI,KAAK,CAAC,KAEbA,GAAG,GAAG,IAAI;UACZC,iBAAiB,GAAG,CAAC;QACvB;MACF,CAAC,MAAM;QACL,IAAID,GAAG,CAACM,MAAM,GAAG,CAAC,EAChBN,GAAG,IAAI,GAAG,GAAGN,IAAI,CAACgB,KAAK,CAACR,SAAS,GAAG,CAAC,EAAEG,CAAC,CAAC,CAAC,KAE1CL,GAAG,GAAGN,IAAI,CAACgB,KAAK,CAACR,SAAS,GAAG,CAAC,EAAEG,CAAC,CAAC;QACpCJ,iBAAiB,GAAGI,CAAC,GAAGH,SAAS,GAAG,CAAC;MACvC;MACAA,SAAS,GAAGG,CAAC;MACbF,IAAI,GAAG,CAAC;IACV,CAAC,MAAM,IAAIC,IAAI,KAAK,EAAE,CAAC,SAASD,IAAI,KAAK,CAAC,CAAC,EAAE;MAC3C,EAAEA,IAAI;IACR,CAAC,MAAM;MACLA,IAAI,GAAG,CAAC,CAAC;IACX;EACF;EACA,OAAOH,GAAG;AACZ;AAEA,SAASW,OAAOA,CAACC,GAAG,EAAEC,UAAU,EAAE;EAChC,IAAIC,GAAG,GAAGD,UAAU,CAACC,GAAG,IAAID,UAAU,CAACE,IAAI;EAC3C,IAAIC,IAAI,GAAGH,UAAU,CAACG,IAAI,IAAI,CAACH,UAAU,CAACI,IAAI,IAAI,EAAE,KAAKJ,UAAU,CAACK,GAAG,IAAI,EAAE,CAAC;EAC9E,IAAI,CAACJ,GAAG,EAAE;IACR,OAAOE,IAAI;EACb;EACA,IAAIF,GAAG,KAAKD,UAAU,CAACE,IAAI,EAAE;IAC3B,OAAOD,GAAG,GAAGE,IAAI;EACnB;EACA,OAAOF,GAAG,GAAGF,GAAG,GAAGI,IAAI;AACzB;AAEA,IAAIG,KAAK,GAAG;EACV;EACAC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;IAC1B,IAAIC,YAAY,GAAG,EAAE;IACrB,IAAIC,gBAAgB,GAAG,KAAK;IAC5B,IAAIC,GAAG;IAEP,KAAK,IAAIlB,CAAC,GAAGmB,SAAS,CAAClB,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,CAAC,IAAI,CAACiB,gBAAgB,EAAEjB,CAAC,EAAE,EAAE;MACpE,IAAIX,IAAI;MACR,IAAIW,CAAC,IAAI,CAAC,EACRX,IAAI,GAAG8B,SAAS,CAACnB,CAAC,CAAC,CAAC,KACjB;QACH,IAAIkB,GAAG,KAAKE,SAAS,EACnBF,GAAG,GAAGG,OAAO,CAACH,GAAG,CAAC,CAAC;QACrB7B,IAAI,GAAG6B,GAAG;MACZ;MAEA9B,UAAU,CAACC,IAAI,CAAC;;MAEhB;MACA,IAAIA,IAAI,CAACY,MAAM,KAAK,CAAC,EAAE;QACrB;MACF;MAEAe,YAAY,GAAG3B,IAAI,GAAG,GAAG,GAAG2B,YAAY;MACxCC,gBAAgB,GAAG5B,IAAI,CAACa,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;IAC/C;;IAEA;IACA;;IAEA;IACAc,YAAY,GAAGvB,oBAAoB,CAACuB,YAAY,EAAE,CAACC,gBAAgB,CAAC;IAEpE,IAAIA,gBAAgB,EAAE;MACpB,IAAID,YAAY,CAACf,MAAM,GAAG,CAAC,EACzB,OAAO,GAAG,GAAGe,YAAY,CAAC,KAE1B,OAAO,GAAG;IACd,CAAC,MAAM,IAAIA,YAAY,CAACf,MAAM,GAAG,CAAC,EAAE;MAClC,OAAOe,YAAY;IACrB,CAAC,MAAM;MACL,OAAO,GAAG;IACZ;EACF,CAAC;EAEDM,SAAS,EAAE,SAASA,SAASA,CAACjC,IAAI,EAAE;IAClCD,UAAU,CAACC,IAAI,CAAC;IAEhB,IAAIA,IAAI,CAACY,MAAM,KAAK,CAAC,EAAE,OAAO,GAAG;IAEjC,IAAIsB,UAAU,GAAGlC,IAAI,CAACa,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;IAC3C,IAAIsB,iBAAiB,GAAGnC,IAAI,CAACa,UAAU,CAACb,IAAI,CAACY,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;;IAEhE;IACAZ,IAAI,GAAGI,oBAAoB,CAACJ,IAAI,EAAE,CAACkC,UAAU,CAAC;IAE9C,IAAIlC,IAAI,CAACY,MAAM,KAAK,CAAC,IAAI,CAACsB,UAAU,EAAElC,IAAI,GAAG,GAAG;IAChD,IAAIA,IAAI,CAACY,MAAM,GAAG,CAAC,IAAIuB,iBAAiB,EAAEnC,IAAI,IAAI,GAAG;IAErD,IAAIkC,UAAU,EAAE,OAAO,GAAG,GAAGlC,IAAI;IACjC,OAAOA,IAAI;EACb,CAAC;EAEDkC,UAAU,EAAE,SAASA,UAAUA,CAAClC,IAAI,EAAE;IACpCD,UAAU,CAACC,IAAI,CAAC;IAChB,OAAOA,IAAI,CAACY,MAAM,GAAG,CAAC,IAAIZ,IAAI,CAACa,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;EACtD,CAAC;EAEDuB,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;IACpB,IAAIN,SAAS,CAAClB,MAAM,KAAK,CAAC,EACxB,OAAO,GAAG;IACZ,IAAIyB,MAAM;IACV,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,SAAS,CAAClB,MAAM,EAAE,EAAED,CAAC,EAAE;MACzC,IAAI2B,GAAG,GAAGR,SAAS,CAACnB,CAAC,CAAC;MACtBZ,UAAU,CAACuC,GAAG,CAAC;MACf,IAAIA,GAAG,CAAC1B,MAAM,GAAG,CAAC,EAAE;QAClB,IAAIyB,MAAM,KAAKN,SAAS,EACtBM,MAAM,GAAGC,GAAG,CAAC,KAEbD,MAAM,IAAI,GAAG,GAAGC,GAAG;MACvB;IACF;IACA,IAAID,MAAM,KAAKN,SAAS,EACtB,OAAO,GAAG;IACZ,OAAON,KAAK,CAACQ,SAAS,CAACI,MAAM,CAAC;EAChC,CAAC;EAEDE,QAAQ,EAAE,SAASA,QAAQA,CAACC,IAAI,EAAEC,EAAE,EAAE;IACpC1C,UAAU,CAACyC,IAAI,CAAC;IAChBzC,UAAU,CAAC0C,EAAE,CAAC;IAEd,IAAID,IAAI,KAAKC,EAAE,EAAE,OAAO,EAAE;IAE1BD,IAAI,GAAGf,KAAK,CAACC,OAAO,CAACc,IAAI,CAAC;IAC1BC,EAAE,GAAGhB,KAAK,CAACC,OAAO,CAACe,EAAE,CAAC;IAEtB,IAAID,IAAI,KAAKC,EAAE,EAAE,OAAO,EAAE;;IAE1B;IACA,IAAIC,SAAS,GAAG,CAAC;IACjB,OAAOA,SAAS,GAAGF,IAAI,CAAC5B,MAAM,EAAE,EAAE8B,SAAS,EAAE;MAC3C,IAAIF,IAAI,CAAC3B,UAAU,CAAC6B,SAAS,CAAC,KAAK,EAAE,CAAC,OACpC;IACJ;IACA,IAAIC,OAAO,GAAGH,IAAI,CAAC5B,MAAM;IACzB,IAAIgC,OAAO,GAAGD,OAAO,GAAGD,SAAS;;IAEjC;IACA,IAAIG,OAAO,GAAG,CAAC;IACf,OAAOA,OAAO,GAAGJ,EAAE,CAAC7B,MAAM,EAAE,EAAEiC,OAAO,EAAE;MACrC,IAAIJ,EAAE,CAAC5B,UAAU,CAACgC,OAAO,CAAC,KAAK,EAAE,CAAC,OAChC;IACJ;IACA,IAAIC,KAAK,GAAGL,EAAE,CAAC7B,MAAM;IACrB,IAAImC,KAAK,GAAGD,KAAK,GAAGD,OAAO;;IAE3B;IACA,IAAIjC,MAAM,GAAGgC,OAAO,GAAGG,KAAK,GAAGH,OAAO,GAAGG,KAAK;IAC9C,IAAIC,aAAa,GAAG,CAAC,CAAC;IACtB,IAAIrC,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,IAAIC,MAAM,EAAE,EAAED,CAAC,EAAE;MACvB,IAAIA,CAAC,KAAKC,MAAM,EAAE;QAChB,IAAImC,KAAK,GAAGnC,MAAM,EAAE;UAClB,IAAI6B,EAAE,CAAC5B,UAAU,CAACgC,OAAO,GAAGlC,CAAC,CAAC,KAAK,EAAE,CAAC,OAAO;YAC3C;YACA;YACA,OAAO8B,EAAE,CAACzB,KAAK,CAAC6B,OAAO,GAAGlC,CAAC,GAAG,CAAC,CAAC;UAClC,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;YAClB;YACA;YACA,OAAO8B,EAAE,CAACzB,KAAK,CAAC6B,OAAO,GAAGlC,CAAC,CAAC;UAC9B;QACF,CAAC,MAAM,IAAIiC,OAAO,GAAGhC,MAAM,EAAE;UAC3B,IAAI4B,IAAI,CAAC3B,UAAU,CAAC6B,SAAS,GAAG/B,CAAC,CAAC,KAAK,EAAE,CAAC,OAAO;YAC/C;YACA;YACAqC,aAAa,GAAGrC,CAAC;UACnB,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;YAClB;YACA;YACAqC,aAAa,GAAG,CAAC;UACnB;QACF;QACA;MACF;MACA,IAAIC,QAAQ,GAAGT,IAAI,CAAC3B,UAAU,CAAC6B,SAAS,GAAG/B,CAAC,CAAC;MAC7C,IAAIuC,MAAM,GAAGT,EAAE,CAAC5B,UAAU,CAACgC,OAAO,GAAGlC,CAAC,CAAC;MACvC,IAAIsC,QAAQ,KAAKC,MAAM,EACrB,MAAM,KACH,IAAID,QAAQ,KAAK,EAAE,CAAC,OACvBD,aAAa,GAAGrC,CAAC;IACrB;IAEA,IAAIwC,GAAG,GAAG,EAAE;IACZ;IACA;IACA,KAAKxC,CAAC,GAAG+B,SAAS,GAAGM,aAAa,GAAG,CAAC,EAAErC,CAAC,IAAIgC,OAAO,EAAE,EAAEhC,CAAC,EAAE;MACzD,IAAIA,CAAC,KAAKgC,OAAO,IAAIH,IAAI,CAAC3B,UAAU,CAACF,CAAC,CAAC,KAAK,EAAE,CAAC,OAAO;QACpD,IAAIwC,GAAG,CAACvC,MAAM,KAAK,CAAC,EAClBuC,GAAG,IAAI,IAAI,CAAC,KAEZA,GAAG,IAAI,KAAK;MAChB;IACF;;IAEA;IACA;IACA,IAAIA,GAAG,CAACvC,MAAM,GAAG,CAAC,EAChB,OAAOuC,GAAG,GAAGV,EAAE,CAACzB,KAAK,CAAC6B,OAAO,GAAGG,aAAa,CAAC,CAAC,KAC5C;MACHH,OAAO,IAAIG,aAAa;MACxB,IAAIP,EAAE,CAAC5B,UAAU,CAACgC,OAAO,CAAC,KAAK,EAAE,CAAC,OAChC,EAAEA,OAAO;MACX,OAAOJ,EAAE,CAACzB,KAAK,CAAC6B,OAAO,CAAC;IAC1B;EACF,CAAC;EAEDO,SAAS,EAAE,SAASA,SAASA,CAACpD,IAAI,EAAE;IAClC,OAAOA,IAAI;EACb,CAAC;EAEDqD,OAAO,EAAE,SAASA,OAAOA,CAACrD,IAAI,EAAE;IAC9BD,UAAU,CAACC,IAAI,CAAC;IAChB,IAAIA,IAAI,CAACY,MAAM,KAAK,CAAC,EAAE,OAAO,GAAG;IACjC,IAAIF,IAAI,GAAGV,IAAI,CAACa,UAAU,CAAC,CAAC,CAAC;IAC7B,IAAIyC,OAAO,GAAG5C,IAAI,KAAK,EAAE,CAAC;IAC1B,IAAI6C,GAAG,GAAG,CAAC,CAAC;IACZ,IAAIC,YAAY,GAAG,IAAI;IACvB,KAAK,IAAI7C,CAAC,GAAGX,IAAI,CAACY,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MACzCD,IAAI,GAAGV,IAAI,CAACa,UAAU,CAACF,CAAC,CAAC;MACzB,IAAID,IAAI,KAAK,EAAE,CAAC,OAAO;QACnB,IAAI,CAAC8C,YAAY,EAAE;UACjBD,GAAG,GAAG5C,CAAC;UACP;QACF;MACF,CAAC,MAAM;QACP;QACA6C,YAAY,GAAG,KAAK;MACtB;IACF;IAEA,IAAID,GAAG,KAAK,CAAC,CAAC,EAAE,OAAOD,OAAO,GAAG,GAAG,GAAG,GAAG;IAC1C,IAAIA,OAAO,IAAIC,GAAG,KAAK,CAAC,EAAE,OAAO,IAAI;IACrC,OAAOvD,IAAI,CAACgB,KAAK,CAAC,CAAC,EAAEuC,GAAG,CAAC;EAC3B,CAAC;EAEDE,QAAQ,EAAE,SAASA,QAAQA,CAACzD,IAAI,EAAEwB,GAAG,EAAE;IACrC,IAAIA,GAAG,KAAKO,SAAS,IAAI,OAAOP,GAAG,KAAK,QAAQ,EAAE,MAAM,IAAIvB,SAAS,CAAC,iCAAiC,CAAC;IACxGF,UAAU,CAACC,IAAI,CAAC;IAEhB,IAAI0D,KAAK,GAAG,CAAC;IACb,IAAIH,GAAG,GAAG,CAAC,CAAC;IACZ,IAAIC,YAAY,GAAG,IAAI;IACvB,IAAI7C,CAAC;IAEL,IAAIa,GAAG,KAAKO,SAAS,IAAIP,GAAG,CAACZ,MAAM,GAAG,CAAC,IAAIY,GAAG,CAACZ,MAAM,IAAIZ,IAAI,CAACY,MAAM,EAAE;MACpE,IAAIY,GAAG,CAACZ,MAAM,KAAKZ,IAAI,CAACY,MAAM,IAAIY,GAAG,KAAKxB,IAAI,EAAE,OAAO,EAAE;MACzD,IAAI2D,MAAM,GAAGnC,GAAG,CAACZ,MAAM,GAAG,CAAC;MAC3B,IAAIgD,gBAAgB,GAAG,CAAC,CAAC;MACzB,KAAKjD,CAAC,GAAGX,IAAI,CAACY,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;QACrC,IAAID,IAAI,GAAGV,IAAI,CAACa,UAAU,CAACF,CAAC,CAAC;QAC7B,IAAID,IAAI,KAAK,EAAE,CAAC,OAAO;UACnB;UACA;UACA,IAAI,CAAC8C,YAAY,EAAE;YACjBE,KAAK,GAAG/C,CAAC,GAAG,CAAC;YACb;UACF;QACF,CAAC,MAAM;UACP,IAAIiD,gBAAgB,KAAK,CAAC,CAAC,EAAE;YAC3B;YACA;YACAJ,YAAY,GAAG,KAAK;YACpBI,gBAAgB,GAAGjD,CAAC,GAAG,CAAC;UAC1B;UACA,IAAIgD,MAAM,IAAI,CAAC,EAAE;YACf;YACA,IAAIjD,IAAI,KAAKc,GAAG,CAACX,UAAU,CAAC8C,MAAM,CAAC,EAAE;cACnC,IAAI,EAAEA,MAAM,KAAK,CAAC,CAAC,EAAE;gBACnB;gBACA;gBACAJ,GAAG,GAAG5C,CAAC;cACT;YACF,CAAC,MAAM;cACL;cACA;cACAgD,MAAM,GAAG,CAAC,CAAC;cACXJ,GAAG,GAAGK,gBAAgB;YACxB;UACF;QACF;MACF;MAEA,IAAIF,KAAK,KAAKH,GAAG,EAAEA,GAAG,GAAGK,gBAAgB,CAAC,KAAK,IAAIL,GAAG,KAAK,CAAC,CAAC,EAAEA,GAAG,GAAGvD,IAAI,CAACY,MAAM;MAChF,OAAOZ,IAAI,CAACgB,KAAK,CAAC0C,KAAK,EAAEH,GAAG,CAAC;IAC/B,CAAC,MAAM;MACL,KAAK5C,CAAC,GAAGX,IAAI,CAACY,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;QACrC,IAAIX,IAAI,CAACa,UAAU,CAACF,CAAC,CAAC,KAAK,EAAE,CAAC,OAAO;UACjC;UACA;UACA,IAAI,CAAC6C,YAAY,EAAE;YACjBE,KAAK,GAAG/C,CAAC,GAAG,CAAC;YACb;UACF;QACF,CAAC,MAAM,IAAI4C,GAAG,KAAK,CAAC,CAAC,EAAE;UACvB;UACA;UACAC,YAAY,GAAG,KAAK;UACpBD,GAAG,GAAG5C,CAAC,GAAG,CAAC;QACb;MACF;MAEA,IAAI4C,GAAG,KAAK,CAAC,CAAC,EAAE,OAAO,EAAE;MACzB,OAAOvD,IAAI,CAACgB,KAAK,CAAC0C,KAAK,EAAEH,GAAG,CAAC;IAC/B;EACF,CAAC;EAEDM,OAAO,EAAE,SAASA,OAAOA,CAAC7D,IAAI,EAAE;IAC9BD,UAAU,CAACC,IAAI,CAAC;IAChB,IAAI8D,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIR,GAAG,GAAG,CAAC,CAAC;IACZ,IAAIC,YAAY,GAAG,IAAI;IACvB;IACA;IACA,IAAIQ,WAAW,GAAG,CAAC;IACnB,KAAK,IAAIrD,CAAC,GAAGX,IAAI,CAACY,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MACzC,IAAID,IAAI,GAAGV,IAAI,CAACa,UAAU,CAACF,CAAC,CAAC;MAC7B,IAAID,IAAI,KAAK,EAAE,CAAC,OAAO;QACnB;QACA;QACA,IAAI,CAAC8C,YAAY,EAAE;UACjBO,SAAS,GAAGpD,CAAC,GAAG,CAAC;UACjB;QACF;QACA;MACF;MACF,IAAI4C,GAAG,KAAK,CAAC,CAAC,EAAE;QACd;QACA;QACAC,YAAY,GAAG,KAAK;QACpBD,GAAG,GAAG5C,CAAC,GAAG,CAAC;MACb;MACA,IAAID,IAAI,KAAK,EAAE,CAAC,OAAO;QACnB;QACA,IAAIoD,QAAQ,KAAK,CAAC,CAAC,EACjBA,QAAQ,GAAGnD,CAAC,CAAC,KACV,IAAIqD,WAAW,KAAK,CAAC,EACxBA,WAAW,GAAG,CAAC;MACrB,CAAC,MAAM,IAAIF,QAAQ,KAAK,CAAC,CAAC,EAAE;QAC1B;QACA;QACAE,WAAW,GAAG,CAAC,CAAC;MAClB;IACF;IAEA,IAAIF,QAAQ,KAAK,CAAC,CAAC,IAAIP,GAAG,KAAK,CAAC,CAAC;IAC7B;IACAS,WAAW,KAAK,CAAC;IACjB;IACAA,WAAW,KAAK,CAAC,IAAIF,QAAQ,KAAKP,GAAG,GAAG,CAAC,IAAIO,QAAQ,KAAKC,SAAS,GAAG,CAAC,EAAE;MAC3E,OAAO,EAAE;IACX;IACA,OAAO/D,IAAI,CAACgB,KAAK,CAAC8C,QAAQ,EAAEP,GAAG,CAAC;EAClC,CAAC;EAEDU,MAAM,EAAE,SAASA,MAAMA,CAAC9C,UAAU,EAAE;IAClC,IAAIA,UAAU,KAAK,IAAI,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;MACzD,MAAM,IAAIlB,SAAS,CAAC,kEAAkE,GAAG,OAAOkB,UAAU,CAAC;IAC7G;IACA,OAAOF,OAAO,CAAC,GAAG,EAAEE,UAAU,CAAC;EACjC,CAAC;EAED+C,KAAK,EAAE,SAASA,KAAKA,CAAClE,IAAI,EAAE;IAC1BD,UAAU,CAACC,IAAI,CAAC;IAEhB,IAAImE,GAAG,GAAG;MAAE9C,IAAI,EAAE,EAAE;MAAED,GAAG,EAAE,EAAE;MAAEE,IAAI,EAAE,EAAE;MAAEE,GAAG,EAAE,EAAE;MAAED,IAAI,EAAE;IAAG,CAAC;IAC5D,IAAIvB,IAAI,CAACY,MAAM,KAAK,CAAC,EAAE,OAAOuD,GAAG;IACjC,IAAIzD,IAAI,GAAGV,IAAI,CAACa,UAAU,CAAC,CAAC,CAAC;IAC7B,IAAIqB,UAAU,GAAGxB,IAAI,KAAK,EAAE,CAAC;IAC7B,IAAIgD,KAAK;IACT,IAAIxB,UAAU,EAAE;MACdiC,GAAG,CAAC9C,IAAI,GAAG,GAAG;MACdqC,KAAK,GAAG,CAAC;IACX,CAAC,MAAM;MACLA,KAAK,GAAG,CAAC;IACX;IACA,IAAII,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIR,GAAG,GAAG,CAAC,CAAC;IACZ,IAAIC,YAAY,GAAG,IAAI;IACvB,IAAI7C,CAAC,GAAGX,IAAI,CAACY,MAAM,GAAG,CAAC;;IAEvB;IACA;IACA,IAAIoD,WAAW,GAAG,CAAC;;IAEnB;IACA,OAAOrD,CAAC,IAAI+C,KAAK,EAAE,EAAE/C,CAAC,EAAE;MACtBD,IAAI,GAAGV,IAAI,CAACa,UAAU,CAACF,CAAC,CAAC;MACzB,IAAID,IAAI,KAAK,EAAE,CAAC,OAAO;QACnB;QACA;QACA,IAAI,CAAC8C,YAAY,EAAE;UACjBO,SAAS,GAAGpD,CAAC,GAAG,CAAC;UACjB;QACF;QACA;MACF;MACF,IAAI4C,GAAG,KAAK,CAAC,CAAC,EAAE;QACd;QACA;QACAC,YAAY,GAAG,KAAK;QACpBD,GAAG,GAAG5C,CAAC,GAAG,CAAC;MACb;MACA,IAAID,IAAI,KAAK,EAAE,CAAC,OAAO;QACnB;QACA,IAAIoD,QAAQ,KAAK,CAAC,CAAC,EAAEA,QAAQ,GAAGnD,CAAC,CAAC,KAAK,IAAIqD,WAAW,KAAK,CAAC,EAAEA,WAAW,GAAG,CAAC;MAC/E,CAAC,MAAM,IAAIF,QAAQ,KAAK,CAAC,CAAC,EAAE;QAC5B;QACA;QACAE,WAAW,GAAG,CAAC,CAAC;MAClB;IACF;IAEA,IAAIF,QAAQ,KAAK,CAAC,CAAC,IAAIP,GAAG,KAAK,CAAC,CAAC;IACjC;IACAS,WAAW,KAAK,CAAC;IACjB;IACAA,WAAW,KAAK,CAAC,IAAIF,QAAQ,KAAKP,GAAG,GAAG,CAAC,IAAIO,QAAQ,KAAKC,SAAS,GAAG,CAAC,EAAE;MACvE,IAAIR,GAAG,KAAK,CAAC,CAAC,EAAE;QACd,IAAIQ,SAAS,KAAK,CAAC,IAAI7B,UAAU,EAAEiC,GAAG,CAAC7C,IAAI,GAAG6C,GAAG,CAAC5C,IAAI,GAAGvB,IAAI,CAACgB,KAAK,CAAC,CAAC,EAAEuC,GAAG,CAAC,CAAC,KAAKY,GAAG,CAAC7C,IAAI,GAAG6C,GAAG,CAAC5C,IAAI,GAAGvB,IAAI,CAACgB,KAAK,CAAC+C,SAAS,EAAER,GAAG,CAAC;MACnI;IACF,CAAC,MAAM;MACL,IAAIQ,SAAS,KAAK,CAAC,IAAI7B,UAAU,EAAE;QACjCiC,GAAG,CAAC5C,IAAI,GAAGvB,IAAI,CAACgB,KAAK,CAAC,CAAC,EAAE8C,QAAQ,CAAC;QAClCK,GAAG,CAAC7C,IAAI,GAAGtB,IAAI,CAACgB,KAAK,CAAC,CAAC,EAAEuC,GAAG,CAAC;MAC/B,CAAC,MAAM;QACLY,GAAG,CAAC5C,IAAI,GAAGvB,IAAI,CAACgB,KAAK,CAAC+C,SAAS,EAAED,QAAQ,CAAC;QAC1CK,GAAG,CAAC7C,IAAI,GAAGtB,IAAI,CAACgB,KAAK,CAAC+C,SAAS,EAAER,GAAG,CAAC;MACvC;MACAY,GAAG,CAAC3C,GAAG,GAAGxB,IAAI,CAACgB,KAAK,CAAC8C,QAAQ,EAAEP,GAAG,CAAC;IACrC;IAEA,IAAIQ,SAAS,GAAG,CAAC,EAAEI,GAAG,CAAC/C,GAAG,GAAGpB,IAAI,CAACgB,KAAK,CAAC,CAAC,EAAE+C,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI7B,UAAU,EAAEiC,GAAG,CAAC/C,GAAG,GAAG,GAAG;IAE5F,OAAO+C,GAAG;EACZ,CAAC;EAEDjD,GAAG,EAAE,GAAG;EACRkD,SAAS,EAAE,GAAG;EACdC,KAAK,EAAE,IAAI;EACX5C,KAAK,EAAE;AACT,CAAC;AAEDA,KAAK,CAACA,KAAK,GAAGA,KAAK;AAEnB6C,MAAM,CAACC,OAAO,GAAG9C,KAAK;;;;;;;;;;AChhBtB;AACA;AACA;AACA,iBAFA,CAEiB;AACjB;AACA;AACA;AACA;AACA;;AAEA,CAAC,UAAU+C,MAAM,EAAEzC,SAAS,EAAE;EAE1B,YAAY;;EAEZ;EACA;EACA;EAGA,IAAI0C,UAAU,GAAI,QAAQ;IACtBC,KAAK,GAAS,EAAE;IAChBC,OAAO,GAAO,GAAG;IACjBC,SAAS,GAAK,UAAU;IACxBC,UAAU,GAAI,WAAW;IACzBC,QAAQ,GAAM,QAAQ;IACtBC,QAAQ,GAAM,QAAQ;IACtBC,KAAK,GAAS,OAAO;IACrBC,KAAK,GAAS,OAAO;IACrBC,IAAI,GAAU,MAAM;IACpBC,IAAI,GAAU,MAAM;IACpBC,MAAM,GAAQ,QAAQ;IACtBC,OAAO,GAAO,SAAS;IACvBC,YAAY,GAAE,cAAc;IAC5BC,OAAO,GAAO,SAAS;IACvBC,MAAM,GAAQ,QAAQ;IACtBC,MAAM,GAAQ,QAAQ;IACtBC,OAAO,GAAO,SAAS;IACvBC,QAAQ,GAAM,UAAU;IACxBC,QAAQ,GAAM,UAAU;IACxBC,aAAa,GAAG,GAAG;EAEvB,IAAIC,MAAM,GAAI,QAAQ;IAClBC,KAAK,GAAK,OAAO;IACjBC,IAAI,GAAM,MAAM;IAChBC,UAAU,GAAG,YAAY;IACzBC,OAAO,GAAG,SAAS;IACnBC,MAAM,GAAI,QAAQ;IAClBC,IAAI,GAAM,MAAM;IAChBC,OAAO,GAAG,SAAS;IACnBC,MAAM,GAAI,QAAQ;IAClBC,MAAM,GAAI,QAAQ;IAClBC,EAAE,GAAQ,IAAI;IACdC,SAAS,GAAG,WAAW;IACvBC,QAAQ,GAAI,UAAU;IACtBC,KAAK,GAAK,OAAO;IACjBC,OAAO,GAAG,SAAS;IACnBC,KAAK,GAAK,OAAO;IACjBC,IAAI,GAAM,MAAM;IAChBC,MAAM,GAAI,QAAQ;IAClBC,KAAK,GAAK,OAAO;IACjBC,QAAQ,GAAM,UAAU;IACxBC,WAAW,GAAG,aAAa;IAC3BC,MAAM,GAAI,QAAQ;;EAEtB;EACA;EACA;;EAEA,IAAIC,MAAM,GAAG,SAAAA,CAAUC,OAAO,EAAEC,UAAU,EAAE;MACpC,IAAIC,aAAa,GAAG,CAAC,CAAC;MACtB,KAAK,IAAI5G,CAAC,IAAI0G,OAAO,EAAE;QACnB,IAAIC,UAAU,CAAC3G,CAAC,CAAC,IAAI2G,UAAU,CAAC3G,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;UACjD2G,aAAa,CAAC5G,CAAC,CAAC,GAAG2G,UAAU,CAAC3G,CAAC,CAAC,CAAC6G,MAAM,CAACH,OAAO,CAAC1G,CAAC,CAAC,CAAC;QACvD,CAAC,MAAM;UACH4G,aAAa,CAAC5G,CAAC,CAAC,GAAG0G,OAAO,CAAC1G,CAAC,CAAC;QACjC;MACJ;MACA,OAAO4G,aAAa;IACxB,CAAC;IACDE,SAAS,GAAG,SAAAA,CAAUC,GAAG,EAAE;MACvB,IAAIC,KAAK,GAAG,CAAC,CAAC;MACd,KAAK,IAAIhH,CAAC,GAAC,CAAC,EAAEA,CAAC,GAAC+G,GAAG,CAAC9G,MAAM,EAAED,CAAC,EAAE,EAAE;QAC7BgH,KAAK,CAACD,GAAG,CAAC/G,CAAC,CAAC,CAACiH,WAAW,CAAC,CAAC,CAAC,GAAGF,GAAG,CAAC/G,CAAC,CAAC;MACxC;MACA,OAAOgH,KAAK;IAChB,CAAC;IACDE,GAAG,GAAG,SAAAA,CAAUC,IAAI,EAAEC,IAAI,EAAE;MACxB,OAAO,OAAOD,IAAI,KAAK/C,QAAQ,GAAGiD,QAAQ,CAACD,IAAI,CAAC,CAACE,OAAO,CAACD,QAAQ,CAACF,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK;IAC3F,CAAC;IACDE,QAAQ,GAAG,SAAAA,CAAUE,GAAG,EAAE;MACtB,OAAOA,GAAG,CAACC,WAAW,CAAC,CAAC;IAC5B,CAAC;IACDC,QAAQ,GAAG,SAAAA,CAAUC,OAAO,EAAE;MAC1B,OAAO,OAAOA,OAAQ,KAAKtD,QAAQ,GAAGsD,OAAO,CAACC,OAAO,CAAC,UAAU,EAAE5D,KAAK,CAAC,CAAC6D,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGxG,SAAS;IACtG,CAAC;IACDyG,IAAI,GAAG,SAAAA,CAAUN,GAAG,EAAEO,GAAG,EAAE;MACvB,IAAI,OAAOP,GAAI,KAAKnD,QAAQ,EAAE;QAC1BmD,GAAG,GAAGA,GAAG,CAACI,OAAO,CAAC,QAAQ,EAAE5D,KAAK,CAAC;QAClC,OAAO,OAAO+D,GAAI,KAAK5D,UAAU,GAAGqD,GAAG,GAAGA,GAAG,CAACQ,SAAS,CAAC,CAAC,EAAE7C,aAAa,CAAC;MAC7E;IACR,CAAC;;EAED;EACA;EACA;;EAEA,IAAI8C,SAAS,GAAG,SAAAA,CAAUC,EAAE,EAAEC,MAAM,EAAE;MAE9B,IAAIlI,CAAC,GAAG,CAAC;QAAEmI,CAAC;QAAEC,CAAC;QAAEC,CAAC;QAAEC,CAAC;QAAEC,OAAO;QAAEC,KAAK;;MAErC;MACA,OAAOxI,CAAC,GAAGkI,MAAM,CAACjI,MAAM,IAAI,CAACsI,OAAO,EAAE;QAElC,IAAIE,KAAK,GAAGP,MAAM,CAAClI,CAAC,CAAC;UAAQ;UACzB0I,KAAK,GAAGR,MAAM,CAAClI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAG;QAC7BmI,CAAC,GAAGC,CAAC,GAAG,CAAC;;QAET;QACA,OAAOD,CAAC,GAAGM,KAAK,CAACxI,MAAM,IAAI,CAACsI,OAAO,EAAE;UAEjC,IAAI,CAACE,KAAK,CAACN,CAAC,CAAC,EAAE;YAAE;UAAO;UACxBI,OAAO,GAAGE,KAAK,CAACN,CAAC,EAAE,CAAC,CAACQ,IAAI,CAACV,EAAE,CAAC;UAE7B,IAAI,CAAC,CAACM,OAAO,EAAE;YACX,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,KAAK,CAACzI,MAAM,EAAEoI,CAAC,EAAE,EAAE;cAC/BG,KAAK,GAAGD,OAAO,CAAC,EAAEH,CAAC,CAAC;cACpBE,CAAC,GAAGI,KAAK,CAACL,CAAC,CAAC;cACZ;cACA,IAAI,OAAOC,CAAC,KAAKnE,QAAQ,IAAImE,CAAC,CAACrI,MAAM,GAAG,CAAC,EAAE;gBACvC,IAAIqI,CAAC,CAACrI,MAAM,KAAK,CAAC,EAAE;kBAChB,IAAI,OAAOqI,CAAC,CAAC,CAAC,CAAC,IAAIrE,SAAS,EAAE;oBAC1B;oBACA,IAAI,CAACqE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI,CAAC,IAAI,EAAEJ,KAAK,CAAC;kBACvC,CAAC,MAAM;oBACH;oBACA,IAAI,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;kBACrB;gBACJ,CAAC,MAAM,IAAIA,CAAC,CAACrI,MAAM,KAAK,CAAC,EAAE;kBACvB;kBACA,IAAI,OAAOqI,CAAC,CAAC,CAAC,CAAC,KAAKrE,SAAS,IAAI,EAAEqE,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,IAAIL,CAAC,CAAC,CAAC,CAAC,CAACO,IAAI,CAAC,EAAE;oBACxD;oBACA,IAAI,CAACP,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,KAAK,GAAGF,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI,CAAC,IAAI,EAAEJ,KAAK,EAAEF,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGlH,SAAS;kBACjE,CAAC,MAAM;oBACH;oBACA,IAAI,CAACkH,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,KAAK,GAAGA,KAAK,CAACb,OAAO,CAACW,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGlH,SAAS;kBAC9D;gBACJ,CAAC,MAAM,IAAIkH,CAAC,CAACrI,MAAM,KAAK,CAAC,EAAE;kBACnB,IAAI,CAACqI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,KAAK,GAAGF,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI,CAAC,IAAI,EAAEJ,KAAK,CAACb,OAAO,CAACW,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGlH,SAAS;gBACnF;cACJ,CAAC,MAAM;gBACH,IAAI,CAACkH,CAAC,CAAC,GAAGE,KAAK,GAAGA,KAAK,GAAGpH,SAAS;cACvC;YACJ;UACJ;QACJ;QACApB,CAAC,IAAI,CAAC;MACV;IACJ,CAAC;IAED8I,SAAS,GAAG,SAAAA,CAAUvB,GAAG,EAAEwB,GAAG,EAAE;MAE5B,KAAK,IAAI/I,CAAC,IAAI+I,GAAG,EAAE;QACf;QACA,IAAI,OAAOA,GAAG,CAAC/I,CAAC,CAAC,KAAKmE,QAAQ,IAAI4E,GAAG,CAAC/I,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;UACjD,KAAK,IAAIkI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,GAAG,CAAC/I,CAAC,CAAC,CAACC,MAAM,EAAEkI,CAAC,EAAE,EAAE;YACpC,IAAIjB,GAAG,CAAC6B,GAAG,CAAC/I,CAAC,CAAC,CAACmI,CAAC,CAAC,EAAEZ,GAAG,CAAC,EAAE;cACrB,OAAQvH,CAAC,KAAKgE,OAAO,GAAI5C,SAAS,GAAGpB,CAAC;YAC1C;UACJ;QACJ,CAAC,MAAM,IAAIkH,GAAG,CAAC6B,GAAG,CAAC/I,CAAC,CAAC,EAAEuH,GAAG,CAAC,EAAE;UACzB,OAAQvH,CAAC,KAAKgE,OAAO,GAAI5C,SAAS,GAAGpB,CAAC;QAC1C;MACJ;MACA,OAAOuH,GAAG;IAClB,CAAC;;EAED;EACA;EACA;;EAEA;EACA,IAAIyB,YAAY,GAAG;MACX,KAAK,EAAK,IAAI;MACd,KAAK,EAAK,IAAI;MACd,KAAK,EAAK,IAAI;MACd,KAAK,EAAK,MAAM;MAChB,OAAO,EAAG,MAAM;MAChB,OAAO,EAAG,MAAM;MAChB,OAAO,EAAG,MAAM;MAChB,GAAG,EAAO;IACd,CAAC;IACDC,iBAAiB,GAAG;MAChB,IAAI,EAAU,MAAM;MACpB,SAAS,EAAK,QAAQ;MACtB,QAAQ,EAAM,OAAO;MACrB,MAAM,EAAQ,QAAQ;MACtB,IAAI,EAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC;MAClC,OAAO,EAAO,QAAQ;MACtB,GAAG,EAAW,QAAQ;MACtB,GAAG,EAAW,QAAQ;MACtB,KAAK,EAAS,QAAQ;MACtB,IAAI,EAAU,CAAC,QAAQ,EAAE,SAAS,CAAC;MACnC,IAAI,EAAU;IACtB,CAAC;;EAED;EACA;EACA;;EAEA,IAAIvC,OAAO,GAAG;IAEVwC,OAAO,EAAG,CAAC,CAEP,8BAA8B,CAAsC;IAAA,CACnE,EAAE,CAACxE,OAAO,EAAE,CAACH,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAChC,6BAA6B,CAAuC;IAAA,CACnE,EAAE,CAACG,OAAO,EAAE,CAACH,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE;IAE9B;IACA,2BAA2B;IAAyC;IACpE,kDAAkD;IAAkB;IACpE,yCAAyC,CAA2B;IAAA,CACnE,EAAE,CAACA,IAAI,EAAEG,OAAO,CAAC,EAAE,CACpB,uBAAuB,CAA6C;IAAA,CACnE,EAAE,CAACA,OAAO,EAAE,CAACH,IAAI,EAAEyB,KAAK,GAAC,OAAO,CAAC,CAAC,EAAE,CACrC,0BAA0B,CAA0C;IAAA,CACnE,EAAE,CAACtB,OAAO,EAAE,CAACH,IAAI,EAAEyB,KAAK,GAAC,KAAK,CAAC,CAAC,EAAE,CACnC,mBAAmB,CAAiD;IAAA,CACnE,EAAE,CAACtB,OAAO,EAAE,CAACH,IAAI,EAAEyB,KAAK,CAAC,CAAC,EAAE;IAE7B;IACA,wDAAwD,CAAY;IAAA,CACnE,EAAE,CAACtB,OAAO,EAAE,CAACH,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE,CAC/B,sBAAsB;IAA8C;IACpE,6DAA6D;IAAO;IACpE;IACA,sDAAsD;IAAc;IACpE,0BAA0B;IAA0C;;IAEpE;IACA,8LAA8L;IAC1H;IACpE,iCAAiC;IAAmC;IACpE,qBAAqB,CAA+C;IAAA,CACnE,EAAE,CAACA,IAAI,EAAEG,OAAO,CAAC,EAAE,CACpB,mBAAmB,CAAiD;IAAA,CACnE,EAAE,CAACA,OAAO,EAAE,CAACH,IAAI,EAAE,YAAY,CAAC,CAAC,EAAE,CACpC,mDAAmD,CAAiB;IAAA,CACnE,EAAE,CAACG,OAAO,EAAE,CAACH,IAAI,EAAE,IAAI,GAACgB,OAAO,CAAC,CAAC,EAAE,CACpC,8BAA8B;IAAsC;IACpE,8BAA8B,EAC9B,4BAA4B,CAAwC;IAAA,CACnE,EAAE,CAACb,OAAO,EAAE,CAACH,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAChC,uBAAuB,CAA6C;IAAA,CACnE,EAAE,CAACG,OAAO,EAAE,CAACH,IAAI,EAAE,WAAW,CAAC,CAAC,EAAE,CACnC,6CAA6C,CAAuB;IAAA,CACnE,EAAE,CAACG,OAAO,EAAE,CAACH,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,CAC5B,kCAAkC,CAAkC;IAAA,CACnE,EAAE,CAACG,OAAO,EAAE,CAACH,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAChC,uBAAuB,CAA6C;IAAA,CACnE,EAAE,CAACG,OAAO,EAAE,CAACH,IAAI,EAAE,eAAe,GAACgB,OAAO,CAAC,CAAC,EAAE,CAC/C,yBAAyB,CAA2C;IAAA,CACnE,EAAE,CAAC,CAAChB,IAAI,EAAE,MAAM,EAAE,YAAY,GAACgB,OAAO,CAAC,EAAEb,OAAO,CAAC,EAAE,CACpD,qBAAqB,CAA+C;IAAA,CACnE,EAAE,CAACA,OAAO,EAAE,CAACH,IAAI,EAAEmB,OAAO,GAAC,QAAQ,CAAC,CAAC,EAAE,CACxC,mBAAmB,CAAiD;IAAA,CACnE,EAAE,CAAChB,OAAO,EAAE,CAACH,IAAI,EAAEyB,KAAK,GAAC,QAAQ,CAAC,CAAC,EAAE,CACtC,wBAAwB,CAA4C;IAAA,CACnE,EAAE,CAACtB,OAAO,EAAE,CAACH,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CACjC,oBAAoB,CAAgD;IAAA,CACnE,EAAE,CAACG,OAAO,EAAE,CAACH,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CACjC,mBAAmB,CAAiD;IAAA,CACnE,EAAE,CAACG,OAAO,EAAE,CAACH,IAAI,EAAEyB,KAAK,GAAC,QAAQ,CAAC,CAAC,EAAE,CACtC,yBAAyB,CAA2C;IAAA,CACnE,EAAE,CAACtB,OAAO,EAAE,CAACH,IAAI,EAAE,OAAO,GAACgB,OAAO,CAAC,CAAC,EAAE,CACvC,oBAAoB,CAAgD;IAAA,CACnE,EAAE,CAACb,OAAO,EAAE,CAACH,IAAI,EAAEmB,OAAO,CAAC,CAAC,EAAE,CAC/B,+BAA+B,CAAqC;IAAA,CACnE,EAAE,CAAC,CAACnB,IAAI,EAAE,MAAM,GAAGgB,OAAO,CAAC,CAAC,EAAE,CAC/B,kDAAkD,CACjD,EAAE,CAAC,CAAChB,IAAI,EAAE,MAAM,EAAE,KAAK,GAAGgB,OAAO,CAAC,EAAEb,OAAO,CAAC,EAAE;IAAqB;IACpE,4BAA4B,CAAwC;IAAA,CACnE,EAAE,CAACA,OAAO,EAAE,CAACH,IAAI,EAAE0B,OAAO,GAAG,WAAW,CAAC,CAAC,EAAE,CAC7C,6BAA6B,CAAuC;IAAA,CACnE,EAAE,CAAC,CAAC1B,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,EAAEG,OAAO,CAAC,EAAE,CACjC,wBAAwB,CAA4C;IAAA,CACnE,EAAE,CAACA,OAAO,EAAE,CAACH,IAAI,EAAE,gBAAgB,CAAC,CAAC,EAAE,CACxC,0BAA0B,CAA0C;IAAA,CACnE,EAAE,CAAC,CAACA,IAAI,EAAE,cAAc,CAAC,EAAEG,OAAO,CAAC,EAAE,CACtC,+BAA+B;IAAqC;IACpE,gDAAgD;IAAoB;IACpE,4CAA4C,CAAwB;IAAA,CACnE,EAAE,CAACH,IAAI,EAAEG,OAAO,CAAC,EAAE,CACpB,cAAc;IAAsD;IACpE,oBAAoB,CAAgD;IAAA,CACnE,EAAE,CAACH,IAAI,CAAC,EAAE;IAEX;IACA,6DAA6D,CAAO;IAAA,CACnE,EAAE,CAAC,CAACA,IAAI,EAAE+B,QAAQ,CAAC,EAAE5B,OAAO,CAAC,EAAE,CAChC,sBAAsB;IAA8C;IACpE,sCAAsC;IAA8B;IACpE,iCAAiC;IAAmC;IACpE,2BAA2B;IAAyC;IACpE,2BAA2B;IAAyC;IACpE,4BAA4B;IAAwC;IACpE,oCAAoC;IAAgC;IACpE,+CAA+C,CAAqB;IAAA,CACnE,EAAE,CAACH,IAAI,EAAEG,OAAO,CAAC,EAAE,CACpB,8BAA8B,CAAsC;IAAA,CACnE,EAAE,CAACA,OAAO,EAAE,CAACH,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE,CAC7B,4CAA4C,CAAwB;IAAA,CACnE,EAAE,CAACG,OAAO,EAAE,CAACH,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,CAEhC,kCAAkC,CAAkC;IAAA,CACnE,EAAE,CAACG,OAAO,EAAE,CAACH,IAAI,EAAEiB,MAAM,GAAC,WAAW,CAAC,CAAC,EAAE,CAE1C,6BAA6B,CAAuC;IAAA,CACnE,EAAE,CAAC,CAACjB,IAAI,EAAEiB,MAAM,GAAC,UAAU,CAAC,EAAEd,OAAO,CAAC,EAAE,CAEzC,yDAAyD,CAAW;IAAA,CACnE,EAAE,CAACA,OAAO,EAAE,CAACH,IAAI,EAAE,UAAU,GAACgB,OAAO,CAAC,CAAC,EAAE,CAE1C,6DAA6D,CAAO;IAAA,CACnE,EAAE,CAAChB,IAAI,EAAEG,OAAO,CAAC,EAAE,CAEpB,8CAA8C,CAAsB;IAAA,CACnE,EAAE,CAACA,OAAO,EAAE,CAACH,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,CACvC,oDAAoD,CAAgB;IAAA,CACnE,EAAE,CAACG,OAAO,EAAEH,IAAI,CAAC,EAAE,CACpB,8CAA8C,CAAsB;IAAA,CACnE,EAAE,CAACA,IAAI,EAAE,CAACG,OAAO,EAAEoE,SAAS,EAAEE,YAAY,CAAC,CAAC,EAAE,CAE/C,4BAA4B,CAC3B,EAAE,CAACzE,IAAI,EAAEG,OAAO,CAAC,EAAE;IAEpB;IACA,sCAAsC,CAA8B;IAAA,CACnE,EAAE,CAAC,CAACH,IAAI,EAAE,UAAU,CAAC,EAAEG,OAAO,CAAC,EAAE,CAClC,qCAAqC,CAA+B;IAAA,CACnE,EAAE,CAACA,OAAO,EAAE,CAACH,IAAI,EAAEmB,OAAO,GAAC,UAAU,CAAC,CAAC,EAAE,CAC1C,4BAA4B;IAAwC;IACpE,aAAa;IAAuD;IACpE,kGAAkG;IAC9B;IACpE,8FAA8F;IAC1B;IACpE,uBAAuB;IAA6C;IACpE,0CAA0C;IAA0B;;IAEpE;IACA,uHAAuH;IACnD;IACpE,sBAAsB;IAA8C;IACpE,oBAAoB,CAAgD;IAAA,CACnE,EAAE,CAACnB,IAAI,EAAEG,OAAO,CAAC,EAAE,CAEpB,sBAAsB,CAA8C;IAAA,CACnE,EAAE,CAACH,IAAI,EAAE,CAACG,OAAO,EAAE,cAAc,EAAE,EAAE,CAAC,CAAC,CAC3C;IAEDyE,GAAG,EAAG,CAAC,CAEH,+CAA+C,CAAqB;IAAA,CACnE,EAAE,CAAC,CAACxE,YAAY,EAAE,OAAO,CAAC,CAAC,EAAE,CAE9B,cAAc,CAAsD;IAAA,CACnE,EAAE,CAAC,CAACA,YAAY,EAAE0C,QAAQ,CAAC,CAAC,EAAE,CAE/B,wBAAwB,CAA4C;IAAA,CACnE,EAAE,CAAC,CAAC1C,YAAY,EAAE,MAAM,CAAC,CAAC,EAAE,CAE7B,kCAAkC,CAAiC;IAAA,CAClE,EAAE,CAAC,CAACA,YAAY,EAAE,OAAO,CAAC,CAAC,EAAE,CAE9B,iCAAiC,CAAmC;IAAA,CACnE,EAAE,CAAC,CAACA,YAAY,EAAE,OAAO,CAAC,CAAC,EAAE;IAE9B;IACA,4BAA4B,CAC3B,EAAE,CAAC,CAACA,YAAY,EAAE,KAAK,CAAC,CAAC,EAAE,CAE5B,wCAAwC,CAA4B;IAAA,CACnE,EAAE,CAAC,CAACA,YAAY,EAAE,MAAM,EAAEZ,KAAK,EAAEsD,QAAQ,CAAC,CAAC,EAAE,CAE9C,gBAAgB,CAAoD;IAAA,CACnE,EAAE,CAAC,CAAC1C,YAAY,EAAE,OAAO,CAAC,CAAC,EAAE,CAE9B;IACoE;IAAA,CACnE,EAAE,CAAC,CAACA,YAAY,EAAE0C,QAAQ,CAAC,CAAC,CAChC;IAED+B,MAAM,EAAG,CAAC;IAEN;IACA;IACA;;IAEA;IACA,iFAAiF,CAChF,EAAE,CAAC9E,KAAK,EAAE,CAACG,MAAM,EAAEwB,OAAO,CAAC,EAAE,CAACzB,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAC/C,wDAAwD,EACxD,sBAAsB,EACtB,eAAe,CACd,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAEwB,OAAO,CAAC,EAAE,CAACzB,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE;IAE/C;IACA,0CAA0C,CAA0B;IAAA,CACnE,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAEW,KAAK,CAAC,EAAE,CAACZ,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE,CAC7C,4BAA4B;IAAwC;IACpE,mCAAmC,EACnC,gCAAgC,CAC/B,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAEW,KAAK,CAAC,EAAE,CAACZ,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAC7C,eAAe,CACd,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAEW,KAAK,CAAC,CAAC,EAAE;IAE7B;IACA,+BAA+B,CAC9B,EAAE,CAACd,KAAK,EAAE,CAACG,MAAM,EAAEyB,KAAK,CAAC,EAAE,CAAC1B,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE;IAE7C;IACA,6DAA6D,CAC5D,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAEmB,MAAM,CAAC,EAAE,CAACpB,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAC9C,iCAAiC,EACjC,oEAAoE,CACnE,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAEmB,MAAM,CAAC,EAAE,CAACpB,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE;IAE9C;IACA,iDAAiD;IAAmB;IACpE,wBAAwB;IAA4C;IACpE,sCAAsC;IAA8B;IACpE,gDAAgD;IAAoB;IACpE,2DAA2D;IAAS;IACpE,uGAAuG,CAAC;IAAA,CACvG,EAAE,CAAC,CAACP,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAACG,MAAM,EAAE2B,MAAM,CAAC,EAAE,CAAC5B,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE,CAC3D,8CAA8C;IAAsB;IACpE,4CAA4C,CAAwB;IAAA,CACnE,EAAC,CAAC,CAACP,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAACG,MAAM,EAAE2B,MAAM,CAAC,EAAE,CAAC5B,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE;IAE1D;IACA,qBAAqB,EACrB,iEAAiE,CAChE,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAE,MAAM,CAAC,EAAE,CAACD,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE,CAC9C,sBAAsB,CACrB,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAE,MAAM,CAAC,EAAE,CAACD,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE;IAE9C;IACA,wBAAwB,EACxB,kCAAkC,CACjC,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAE,MAAM,CAAC,EAAE,CAACD,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE;IAE9C;IACA,iCAAiC,CAChC,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAE,QAAQ,CAAC,EAAE,CAACD,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE;IAEhD;IACA,gFAAgF,EAChF,2BAA2B,EAC3B,oDAAoD,CACnD,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAEsB,QAAQ,CAAC,EAAE,CAACvB,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE,CAChD,mCAAmC,CAClC,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAEsB,QAAQ,CAAC,EAAE,CAACvB,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE;IAEhD;IACA,+DAA+D,CAC9D,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAEoB,EAAE,CAAC,EAAE,CAACrB,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAC1C,qDAAqD,EACrD,mDAAmD,EACnD,sBAAsB,CACrB,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAEoB,EAAE,CAAC,EAAE,CAACrB,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE;IAE1C;IACA,mBAAmB,EACnB,mEAAmE,CAClE,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAE,QAAQ,CAAC,EAAE,CAACD,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE;IAEhD;IACA,oCAAoC,EACpC,wBAAwB,CACvB,EAAE,CAAC,CAACR,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAACG,MAAM,EAAE,OAAO,CAAC,EAAE,CAACD,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE;IAE5D;IACA,cAAc,CAAsD;IAAA,CACnE,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAEkB,MAAM,CAAC,EAAE,CAACnB,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAC9C,2CAA2C,CAAyB;IAAA,CACnE,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAEkB,MAAM,CAAC,EAAE,CAACnB,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE;IAE9C;IACA,wGAAwG,CACvG,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAE0B,IAAI,CAAC,EAAE,CAAC3B,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE,CAC5C,mBAAmB,EACnB,+BAA+B,CAC9B,EAAE,CAAC,CAACP,KAAK,EAAE,eAAe,CAAC,EAAE,CAACG,MAAM,EAAE0B,IAAI,CAAC,EAAE,CAAC3B,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE;IAE/D;IACA,qCAAqC,EACrC,wCAAwC,CACvC,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAE,SAAS,CAAC,EAAE,CAACD,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE;IAEjD;IACA,cAAc,EACd,sCAAsC;IAA8B;IACpE,8BAA8B,CAAsC;IAAA,CACnE,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAEU,MAAM,CAAC,EAAE,CAACX,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAC9C,+CAA+C,CAAqB;IAAA,CACnE,EAAE,CAAC,CAACR,KAAK,EAAE,OAAO,EAAE,eAAe,CAAC,EAAE,CAACG,MAAM,EAAEU,MAAM,CAAC,EAAE,CAACX,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE;IAE1E;IACA,8BAA8B,CAAsC;IAAA,CACnE,EAAE,CAACP,KAAK,EAAEG,MAAM,EAAE,CAACD,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CACpC,+BAA+B,EAC/B,gBAAgB,CAAoD;IAAA,CACnE,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAEa,UAAU,CAAC,EAAE,CAACd,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE;IAElD;IACA,mFAAmF,CAClF,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAEY,IAAI,CAAC,EAAE,CAACb,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAC5C,+CAA+C,CAC9C,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAEY,IAAI,CAAC,EAAE,CAACb,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE;IAE5C;IACA,YAAY,CAAwD;IAAA,CACnE,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAE,KAAK,CAAC,EAAE,CAACD,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAC7C,0CAA0C;IAA0B;;IAEpE;IACA,mCAAmC,EACnC,+EAA+E,CAAS;IAAA,CACvF,EAAE,CAACL,MAAM,EAAE,CAACH,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAACE,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE;IAEjD;IACA,qCAAqC,CACpC,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAE,MAAM,CAAC,EAAE,CAACD,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE;IAE9C;IACA,6BAA6B,EAC7B,mBAAmB,CAClB,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAE,OAAO,CAAC,EAAE,CAACD,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE;IAE/C;IACA,gDAAgD,CAC/C,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAE,SAAS,CAAC,EAAE,CAACD,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE;IAEjD;IACA,+GAA+G;IAC3C;IACpE,kBAAkB;IAAkD;IACpE,gBAAgB;IAAoD;IACpE,6BAA6B;IAAuC;IACpE,yBAAyB;IAA2C;IACpE,UAAU;IAA0D;IACpE,uBAAuB,CAA6C;IAAA,CACnE,EAAE,CAACJ,MAAM,EAAEH,KAAK,EAAE,CAACE,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE,CAEpC,0BAA0B;IAA0C;IACpE,uBAAuB;IAA6C;IACpE,sCAAsC;IAA8B;IACpE,sBAAsB;IAA8C;IACpE,2BAA2B;IAAyC;IACpE,gCAAgC;IAAoC;IACpE,iCAAiC;IAAmC;IACpE,6BAA6B;IAAuC;IACpE,+BAA+B;IAAqC;IACpE,iCAAiC,CAAmC;IAAA,CACnE,EAAE,CAACJ,MAAM,EAAEH,KAAK,EAAE,CAACE,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAEpC,gBAAgB,CAAoD;IAAA,CACnE,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAEqB,SAAS,CAAC,EAAE,CAACtB,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CACjD,mCAAmC,CAAiC;IAAA,CACnE,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAE,WAAW,CAAC,EAAE,CAACD,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE,CACnD,WAAW,CAAyD;IAAA,CACnE,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAE,MAAM,CAAC,EAAE,CAACD,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE,CAC9C,cAAc,CAAsD;IAAA,CACnE,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAE,SAAS,CAAC,EAAE,CAACD,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE,CACjD,eAAe,CAAqD;IAAA,CACnE,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAE,KAAK,CAAC,EAAE,CAACD,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAC7C,wBAAwB,CAA4C;IAAA,CACnE,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAE,MAAM,CAAC,EAAE,CAACD,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAC9C,sBAAsB,CAA8C;IAAA,CACnE,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAE,SAAS,CAAC,EAAE,CAACD,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CACjD,6CAA6C,CAAuB;IAAA,CACnE,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAE,gBAAgB,CAAC,EAAE,CAACD,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CACxD,mBAAmB,CAClB,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAE,UAAU,CAAC,EAAE,CAACD,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAClD,YAAY,CAAwD;IAAA,CACnE,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAE,KAAK,CAAC,EAAE,CAACD,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAC7C,iBAAiB,CAAmD;IAAA,CACnE,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAE,KAAK,CAAC,EAAE,CAACD,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE,CAC7C,sBAAsB,CAA8C;IAAA,CACnE,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAE,OAAO,CAAC,EAAE,CAACD,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE,CAC/C,iBAAiB,CAAmD;IAAA,CACnE,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAE,OAAO,CAAC,EAAE,CAACD,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAC/C,sBAAsB,CAA8C;IAAA,CACnE,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAE,MAAM,CAAC,EAAE,CAACD,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAC9C,kBAAkB,EAClB,oCAAoC,CAAgC;IAAA,CACnE,EAAE,CAAC,CAACL,MAAM,EAAE,cAAc,CAAC,EAAEH,KAAK,EAAE,CAACE,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CACtD,oBAAoB,CAAgD;IAAA,CACnE,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAE,UAAU,CAAC,EAAE,CAACD,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAClD,4BAA4B,CAAwC;IAAA,CACnE,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAE,UAAU,CAAC,EAAE,CAACD,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAClD,kDAAkD,CAAkB;IAAA,CACnE,EAAE,CAAC,CAACL,MAAM,EAAE,OAAO,CAAC,EAAEH,KAAK,EAAE,CAACE,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE,CAC/C,yBAAyB,CAA2C;IAAA,CACnE,EAAE,CAAC,CAACJ,MAAM,EAAE,OAAO,CAAC,EAAEH,KAAK,EAAE,CAACE,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE,CAC/C,YAAY,CAAwD;IAAA,CACnE,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAE,WAAW,CAAC,EAAE,CAACD,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE,CACnD,qCAAqC,CAA+B;IAAA,CACnE,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAE,SAAS,CAAC,EAAE,CAACD,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CACjD,sBAAsB,CAA8C;IAAA,CACnE,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAE,WAAW,CAAC,EAAE,CAACD,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CACnD,gBAAgB,CAAoD;IAAA,CACnE,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAE,OAAO,CAAC,EAAE,CAACD,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAC/C,mBAAmB,CAAiD;IAAA,CACnE,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAE,QAAQ,CAAC,EAAE,CAACD,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAChD,iBAAiB,CAAmD;IAAA,CACnE,EAAE,CAACL,MAAM,EAAEH,KAAK,EAAE,CAACE,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE,CACpC,oBAAoB,CAAgD;IAAA,CACnE,EAAE,CAAC,CAACP,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAACG,MAAM,EAAEqB,SAAS,CAAC,EAAE,CAACtB,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE,CAC/D,uDAAuD,CAAa;IAAA,CACnE,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAE4B,KAAK,CAAC,EAAE,CAAC7B,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAC7C,uCAAuC,CACtC,EAAE,CAACR,KAAK,EAAE,CAACG,MAAM,EAAE4B,KAAK,CAAC,EAAE,CAAC7B,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE;IAE7C;IACA;IACA;;IAEA,sBAAsB,CAA8C;IAAA,CACnE,EAAE,CAACJ,MAAM,EAAE,CAACD,IAAI,EAAEO,OAAO,CAAC,CAAC,EAAE,CAC9B,qBAAqB,CACpB,EAAE,CAAC,CAACT,KAAK,EAAE,GAAG,EAAE,SAAS,CAAC,EAAE,CAACG,MAAM,EAAEwB,OAAO,CAAC,EAAE,CAACzB,IAAI,EAAEO,OAAO,CAAC,CAAC,EAAE,CAClE,4DAA4D,CAAQ;IAAA,CACnE,EAAE,CAAC,CAACN,MAAM,EAAEoB,EAAE,CAAC,EAAE,CAACrB,IAAI,EAAEO,OAAO,CAAC,CAAC,EAAE,CACpC,cAAc,CAAsD;IAAA,CACnE,EAAE,CAACN,MAAM,EAAE,CAACH,KAAK,EAAEc,KAAK,GAAC,KAAK,CAAC,EAAE,CAACZ,IAAI,EAAEO,OAAO,CAAC,CAAC,EAAE,CACpD,QAAQ,CAA4D;IAAA,CACnE,EAAE,CAAC,CAACT,KAAK,EAAEkB,MAAM,GAAC,MAAM,CAAC,EAAE,CAACf,MAAM,EAAEkB,MAAM,CAAC,EAAE,CAACnB,IAAI,EAAEO,OAAO,CAAC,CAAC,EAAE,CAChE,2BAA2B,CAAyC;IAAA,CACnE,EAAE,CAACT,KAAK,EAAE,CAACG,MAAM,EAAEU,MAAM,CAAC,EAAE,CAACX,IAAI,EAAEO,OAAO,CAAC,CAAC,EAAE,CAC/C,sBAAsB,EACtB,qBAAqB,CAA+C;IAAA,CACnE,EAAE,CAACT,KAAK,EAAE,CAACG,MAAM,EAAEyB,KAAK,CAAC,EAAE,CAAC1B,IAAI,EAAEO,OAAO,CAAC,CAAC,EAAC,CAC7C,0BAA0B,CAA8C;IAAA,CACvE,EAAE,CAACT,KAAK,EAAE,CAACG,MAAM,EAAE0B,IAAI,CAAC,EAAE,CAAC3B,IAAI,EAAEO,OAAO,CAAC,CAAC,EAAE,CAC7C,mBAAmB,CAAiD;IAAA,CACnE,EAAE,CAACT,KAAK,EAAE,CAACG,MAAM,EAAE2B,MAAM,CAAC,EAAE,CAAC5B,IAAI,EAAEO,OAAO,CAAC,CAAC,EAAE,CAC/C,2BAA2B,CAAyC;IAAA,CACnE,EAAE,CAACN,MAAM,EAAEH,KAAK,EAAE,CAACE,IAAI,EAAEO,OAAO,CAAC,CAAC,EAAE,CACrC,yCAAyC;IAA2B;IACpE,2DAA2D,CAAS;IAAA,CACnE,EAAE,CAAC,CAACN,MAAM,EAAEoD,IAAI,CAAC,EAAE,CAACvD,KAAK,EAAEuD,IAAI,CAAC,EAAE,CAACrD,IAAI,EAAEO,OAAO,CAAC,CAAC,EAAE,CACrD,iDAAiD,CAAmB;IAAA,CACnE,EAAE,CAAC,CAACP,IAAI,EAAEO,OAAO,CAAC,CAAC,EAAE;IAEtB;IACA;IACA;;IAEA,SAAS;IAA2D;IACpE,4BAA4B,CAAwC;IAAA,CACnE,EAAE,CAACN,MAAM,EAAEH,KAAK,EAAE,CAACE,IAAI,EAAEI,OAAO,CAAC,CAAC,EAAE,CACrC,wBAAwB,CAA4C;IAAA,CACnE,EAAE,CAACN,KAAK,EAAE,CAACG,MAAM,EAAE,QAAQ,CAAC,EAAE,CAACD,IAAI,EAAEI,OAAO,CAAC,CAAC,EAAE,CACjD,iCAAiC,CAAmC;IAAA,CACnE,EAAE,CAACN,KAAK,EAAE,CAACG,MAAM,EAAE0B,IAAI,CAAC,EAAE,CAAC3B,IAAI,EAAEI,OAAO,CAAC,CAAC,EAAE,CAC7C,oCAAoC,CAAgC;IAAA,CACnE,EAAE,CAACN,KAAK,EAAE,CAACG,MAAM,EAAEqB,SAAS,CAAC,EAAE,CAACtB,IAAI,EAAEI,OAAO,CAAC,CAAC,EAAE;IAElD;IACA;IACA;;IAEA,gBAAgB,CAAoD;IAAA,CACnE,EAAE,CAACH,MAAM,EAAEH,KAAK,EAAE,CAACE,IAAI,EAAEQ,QAAQ,CAAC,CAAC,EAAE,CACtC,sCAAsC,CAA8B;IAAA,CACnE,EAAE,CAACV,KAAK,EAAE,CAACG,MAAM,EAAEW,KAAK,CAAC,EAAE,CAACZ,IAAI,EAAEQ,QAAQ,CAAC,CAAC,EAAE,CAC/C,sBAAsB,CAA8C;IAAA,CACnE,EAAE,CAACV,KAAK,EAAE,CAACG,MAAM,EAAEkB,MAAM,CAAC,EAAE,CAACnB,IAAI,EAAEQ,QAAQ,CAAC,CAAC,EAAE,CAChD,2BAA2B,CAC1B,EAAE,CAACV,KAAK,EAAE,CAACG,MAAM,EAAE4B,KAAK,CAAC,EAAE,CAAC7B,IAAI,EAAEQ,QAAQ,CAAC,CAAC,EAAE,CAC/C,qBAAqB,CAA+C;IAAA,CACnE,EAAE,CAACV,KAAK,EAAE,CAACG,MAAM,EAAE6B,QAAQ,CAAC,EAAE,CAAC9B,IAAI,EAAEQ,QAAQ,CAAC,CAAC,EAAE;IAElD;IACA;IACA;;IAEA,sCAAsC,CAA8B;IAAA,CACnE,EAAE,CAACP,MAAM,EAAE,CAACD,IAAI,EAAES,QAAQ,CAAC,CAAC,EAAE,CAC/B,YAAY,CAAwD;IAAA,CACnE,EAAE,CAACX,KAAK,EAAE,CAACG,MAAM,EAAEU,MAAM,CAAC,EAAE,CAACX,IAAI,EAAES,QAAQ,CAAC,CAAC,EAAE;IAEhD;IACA;IACA;;IAEA,gEAAgE,CAAI;IAAA,CACnE,EAAE,CAACX,KAAK,EAAE,CAACE,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE,CAC5B,6DAA6D,CAAO;IAAA,CACnE,EAAE,CAACP,KAAK,EAAE,CAACE,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CAC5B,8CAA8C,CAAsB;IAAA,CACnE,EAAE,CAAC,CAACN,IAAI,EAAEM,MAAM,CAAC,CAAC,EAAE,CACrB,gEAAgE,CAAI;IAAA,CACnE,EAAE,CAAC,CAACN,IAAI,EAAEK,MAAM,CAAC,CAAC,EAAE,CACrB,gCAAgC,CAAoC;IAAA,CACnE,EAAE,CAACP,KAAK,EAAE,CAACG,MAAM,EAAE,SAAS,CAAC,CAAC,CAClC;IAED4E,MAAM,EAAG,CAAC,CAEN,4BAA4B,CAAuC;IAAA,CAClE,EAAE,CAAC3E,OAAO,EAAE,CAACH,IAAI,EAAEkB,IAAI,GAAC,MAAM,CAAC,CAAC,EAAE,CAEnC,2CAA2C,CAAyB;IAAA,CACnE,EAAE,CAACf,OAAO,EAAE,CAACH,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE,CAE/B,sBAAsB;IAA8C;IACpE,qEAAqE;IAAE;IACvE,yBAAyB;IAA2C;IACpE,wCAAwC;IAA4B;IACpE,6BAA6B;IAAuC;IACpE,aAAa,CACZ,EAAE,CAACA,IAAI,EAAEG,OAAO,CAAC,EAAE,CAEpB,+BAA+B,CAAqC;IAAA,CACnE,EAAE,CAACA,OAAO,EAAEH,IAAI,CAAC,CACrB;IAED+E,EAAE,EAAG,CAAC;IAEF;IACA,iCAAiC,CAAmC;IAAA,CACnE,EAAE,CAAC/E,IAAI,EAAEG,OAAO,CAAC,EAAE,CACpB,uDAAuD,CAAa;IAAA,CACnE,EAAE,CAACH,IAAI,EAAE,CAACG,OAAO,EAAEoE,SAAS,EAAEG,iBAAiB,CAAC,CAAC,EAAE,CACpD,yBAAyB;IAAyC;IAClE,0CAA0C,EAC1C,sCAAsC,CACrC,EAAE,CAAC,CAACvE,OAAO,EAAEoE,SAAS,EAAEG,iBAAiB,CAAC,EAAE,CAAC1E,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE;IAEjE;IACA,qDAAqD;IAAe;IACpE,2CAA2C,EAC3C,sBAAsB,CACrB,EAAE,CAAC,CAACG,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAACH,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE,CAC1C,yBAAyB,EACzB,uCAAuC,CAA6B;IAAA,CACnE,EAAE,CAAC,CAACA,IAAI,EAAEiC,MAAM,CAAC,EAAE,CAAC9B,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE;IAE3C;IACA,gDAAgD,CAAoB;IAAA,CACnE,EAAE,CAACA,OAAO,EAAEH,IAAI,CAAC,EAAE;IAAgD;IACpE,8EAA8E,EAC9E,6BAA6B;IAAuC;IACpE,8BAA8B;IAAsC;IACpE,gBAAgB,CAAoD;IAAA,CACnE,EAAE,CAACA,IAAI,EAAEG,OAAO,CAAC,EAAE,CACpB,YAAY,CAAwD;IAAA,CACnE,EAAE,CAACA,OAAO,EAAE,CAACH,IAAI,EAAEe,UAAU,CAAC,CAAC,EAAE,CAClC,2DAA2D,CAAS;IAAA,CACnE,EAAE,CAACZ,OAAO,EAAE,CAACH,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CACjC,iFAAiF,CAAC;IAAA,CACjF,EAAE,CAACG,OAAO,EAAE,CAACH,IAAI,EAAEmB,OAAO,GAAC,KAAK,CAAC,CAAC,EAAE,CACrC,iBAAiB,EACjB,sCAAsC,CAA8B;IAAA,CACnE,EAAE,CAAChB,OAAO,EAAE,CAACH,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE,CAC/B,sCAAsC,CAA8B;IAAA,CACnE,EAAE,CAACG,OAAO,EAAE,CAACH,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE;IAEjC;IACA,mBAAmB,CAAiD;IAAA,CACnE,EAAE,CAACG,OAAO,EAAE,CAACH,IAAI,EAAEiB,MAAM,GAAC,MAAM,CAAC,CAAC,EAAE,CACrC,kCAAkC,CAAkC;IAAA,CACnE,EAAE,CAAC,CAACjB,IAAI,EAAEgC,WAAW,CAAC,EAAE7B,OAAO,CAAC,EAAC;IAElC;IACA,oBAAoB;IAAgD;IACpE,gBAAgB;IAAoD;IACpE,0BAA0B;IAA0C;;IAEpE;IACA,kDAAkD;IAAkB;IACpE,0BAA0B;IAA0C;;IAEpE;IACA,uCAAuC;IAA6B;IACpE,wBAAwB;IAA4C;IACpE,2BAA2B;IAAyC;IACpE,6SAA6S;IACzO;IACpE,0BAA0B;IAA0C;IACpE,mBAAmB;IAAiD;IACpE,4EAA4E;IAAE;IAC9E,gBAAgB,CAAoD;IAAA,CACnE,EAAE,CAACH,IAAI,EAAEG,OAAO,CAAC,EAAE,CACpB,uBAAuB,CAA6C;IAAA,CACnE,EAAE,CAAC,CAACH,IAAI,EAAE,SAAS,CAAC,EAAEG,OAAO,CAAC,EAAE,CACjC,qCAAqC;IAA+B;IACpE,iCAAiC;IAAmC;IACpE,kEAAkE;IAAE;IACpE,oBAAoB,CAAgD;IAAA,CACnE,EAAE,CAACH,IAAI,EAAEG,OAAO,CAAC;EAE1B,CAAC;;EAED;EACA;EACA;;EAEA,IAAI6E,QAAQ,GAAG,SAAAA,CAAUtB,EAAE,EAAEtB,UAAU,EAAE;IAErC,IAAI,OAAOsB,EAAE,KAAK9D,QAAQ,EAAE;MACxBwC,UAAU,GAAGsB,EAAE;MACfA,EAAE,GAAG7G,SAAS;IAClB;IAEA,IAAI,EAAE,IAAI,YAAYmI,QAAQ,CAAC,EAAE;MAC7B,OAAO,IAAIA,QAAQ,CAACtB,EAAE,EAAEtB,UAAU,CAAC,CAAC6C,SAAS,CAAC,CAAC;IACnD;IAEA,IAAIC,UAAU,GAAI,OAAO5F,MAAM,KAAKK,UAAU,IAAIL,MAAM,CAAC6F,SAAS,GAAI7F,MAAM,CAAC6F,SAAS,GAAGtI,SAAS;IAClG,IAAIuI,GAAG,GAAG1B,EAAE,KAAMwB,UAAU,IAAIA,UAAU,CAACG,SAAS,GAAIH,UAAU,CAACG,SAAS,GAAG7F,KAAK,CAAC;IACrF,IAAI8F,KAAK,GAAIJ,UAAU,IAAIA,UAAU,CAACK,aAAa,GAAIL,UAAU,CAACK,aAAa,GAAG1I,SAAS;IAC3F,IAAI2I,OAAO,GAAGpD,UAAU,GAAGF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGD,OAAO;IAChE,IAAIsD,UAAU,GAAGP,UAAU,IAAIA,UAAU,CAACG,SAAS,IAAID,GAAG;IAE1D,IAAI,CAACM,UAAU,GAAG,YAAY;MAC1B,IAAIC,QAAQ,GAAG,CAAC,CAAC;MACjBA,QAAQ,CAAC3F,IAAI,CAAC,GAAGnD,SAAS;MAC1B8I,QAAQ,CAACxF,OAAO,CAAC,GAAGtD,SAAS;MAC7B4G,SAAS,CAACY,IAAI,CAACsB,QAAQ,EAAEP,GAAG,EAAEI,OAAO,CAACb,OAAO,CAAC;MAC9CgB,QAAQ,CAAC7F,KAAK,CAAC,GAAGoD,QAAQ,CAACyC,QAAQ,CAACxF,OAAO,CAAC,CAAC;MAC7C;MACA,IAAIsF,UAAU,IAAIP,UAAU,IAAIA,UAAU,CAACU,KAAK,IAAI,OAAOV,UAAU,CAACU,KAAK,CAACC,OAAO,IAAInG,SAAS,EAAE;QAC9FiG,QAAQ,CAAC3F,IAAI,CAAC,GAAG,OAAO;MAC5B;MACA,OAAO2F,QAAQ;IACnB,CAAC;IACD,IAAI,CAACG,MAAM,GAAG,YAAY;MACtB,IAAIC,IAAI,GAAG,CAAC,CAAC;MACbA,IAAI,CAAC3F,YAAY,CAAC,GAAGvD,SAAS;MAC9B4G,SAAS,CAACY,IAAI,CAAC0B,IAAI,EAAEX,GAAG,EAAEI,OAAO,CAACZ,GAAG,CAAC;MACtC,OAAOmB,IAAI;IACf,CAAC;IACD,IAAI,CAACC,SAAS,GAAG,YAAY;MACzB,IAAIC,OAAO,GAAG,CAAC,CAAC;MAChBA,OAAO,CAAC/F,MAAM,CAAC,GAAGrD,SAAS;MAC3BoJ,OAAO,CAAClG,KAAK,CAAC,GAAGlD,SAAS;MAC1BoJ,OAAO,CAAChG,IAAI,CAAC,GAAGpD,SAAS;MACzB4G,SAAS,CAACY,IAAI,CAAC4B,OAAO,EAAEb,GAAG,EAAEI,OAAO,CAACX,MAAM,CAAC;MAC5C,IAAIY,UAAU,IAAI,CAACQ,OAAO,CAAChG,IAAI,CAAC,IAAIqF,KAAK,IAAIA,KAAK,CAACY,MAAM,EAAE;QACvDD,OAAO,CAAChG,IAAI,CAAC,GAAGK,MAAM;MAC1B;MACA;MACA,IAAImF,UAAU,IAAIQ,OAAO,CAAClG,KAAK,CAAC,IAAI,WAAW,IAAImF,UAAU,IAAI,OAAOA,UAAU,CAACiB,UAAU,KAAKxG,UAAU,IAAIuF,UAAU,CAACkB,cAAc,IAAIlB,UAAU,CAACkB,cAAc,GAAG,CAAC,EAAE;QACxKH,OAAO,CAAClG,KAAK,CAAC,GAAG,MAAM;QACvBkG,OAAO,CAAChG,IAAI,CAAC,GAAGM,MAAM;MAC1B;MACA,OAAO0F,OAAO;IAClB,CAAC;IACD,IAAI,CAACI,SAAS,GAAG,YAAY;MACzB,IAAIC,OAAO,GAAG,CAAC,CAAC;MAChBA,OAAO,CAACtG,IAAI,CAAC,GAAGnD,SAAS;MACzByJ,OAAO,CAACnG,OAAO,CAAC,GAAGtD,SAAS;MAC5B4G,SAAS,CAACY,IAAI,CAACiC,OAAO,EAAElB,GAAG,EAAEI,OAAO,CAACV,MAAM,CAAC;MAC5C,OAAOwB,OAAO;IAClB,CAAC;IACD,IAAI,CAACC,KAAK,GAAG,YAAY;MACrB,IAAIC,GAAG,GAAG,CAAC,CAAC;MACZA,GAAG,CAACxG,IAAI,CAAC,GAAGnD,SAAS;MACrB2J,GAAG,CAACrG,OAAO,CAAC,GAAGtD,SAAS;MACxB4G,SAAS,CAACY,IAAI,CAACmC,GAAG,EAAEpB,GAAG,EAAEI,OAAO,CAACT,EAAE,CAAC;MACpC,IAAIU,UAAU,IAAI,CAACe,GAAG,CAACxG,IAAI,CAAC,IAAIsF,KAAK,IAAIA,KAAK,CAACmB,QAAQ,IAAInB,KAAK,CAACmB,QAAQ,IAAI,SAAS,EAAE;QACpFD,GAAG,CAACxG,IAAI,CAAC,GAAGsF,KAAK,CAACmB,QAAQ,CACLrD,OAAO,CAAC,YAAY,EAAEpB,WAAW,CAAC,CAClCoB,OAAO,CAAC,QAAQ,EAAEnB,MAAM,CAAC,CAAC,CAAW;MAC9D;MACA,OAAOuE,GAAG;IACd,CAAC;IACD,IAAI,CAACvB,SAAS,GAAG,YAAY;MACzB,OAAO;QACHvB,EAAE,EAAQ,IAAI,CAACgD,KAAK,CAAC,CAAC;QACtB/B,OAAO,EAAG,IAAI,CAACe,UAAU,CAAC,CAAC;QAC3BZ,MAAM,EAAI,IAAI,CAACuB,SAAS,CAAC,CAAC;QAC1BtB,EAAE,EAAQ,IAAI,CAACwB,KAAK,CAAC,CAAC;QACtB1B,MAAM,EAAI,IAAI,CAACmB,SAAS,CAAC,CAAC;QAC1BpB,GAAG,EAAO,IAAI,CAACkB,MAAM,CAAC;MAC1B,CAAC;IACL,CAAC;IACD,IAAI,CAACY,KAAK,GAAG,YAAY;MACrB,OAAOtB,GAAG;IACd,CAAC;IACD,IAAI,CAACuB,KAAK,GAAG,UAAUjD,EAAE,EAAE;MACvB0B,GAAG,GAAI,OAAO1B,EAAE,KAAK7D,QAAQ,IAAI6D,EAAE,CAAChI,MAAM,GAAGiF,aAAa,GAAI2C,IAAI,CAACI,EAAE,EAAE/C,aAAa,CAAC,GAAG+C,EAAE;MAC1F,OAAO,IAAI;IACf,CAAC;IACD,IAAI,CAACiD,KAAK,CAACvB,GAAG,CAAC;IACf,OAAO,IAAI;EACf,CAAC;EAEDJ,QAAQ,CAAC7E,OAAO,GAAGZ,UAAU;EAC7ByF,QAAQ,CAAChE,OAAO,GAAIuB,SAAS,CAAC,CAACvC,IAAI,EAAEG,OAAO,EAAEL,KAAK,CAAC,CAAC;EACrDkF,QAAQ,CAAC4B,GAAG,GAAGrE,SAAS,CAAC,CAACnC,YAAY,CAAC,CAAC;EACxC4E,QAAQ,CAAC6B,MAAM,GAAGtE,SAAS,CAAC,CAACxC,KAAK,EAAEG,MAAM,EAAED,IAAI,EAAEI,OAAO,EAAEC,MAAM,EAAEE,OAAO,EAAED,MAAM,EAAEE,QAAQ,EAAEC,QAAQ,CAAC,CAAC;EACxGsE,QAAQ,CAAC8B,MAAM,GAAG9B,QAAQ,CAAC+B,EAAE,GAAGxE,SAAS,CAAC,CAACvC,IAAI,EAAEG,OAAO,CAAC,CAAC;;EAE1D;EACA;EACA;;EAEA;EACA,IAAI,OAAOd,OAAQ,KAAKM,UAAU,EAAE;IAChC;IACA,IAAI,QAAa,KAAKA,UAAU,IAAIP,MAAM,CAACC,OAAO,EAAE;MAChDA,OAAO,GAAGD,MAAM,CAACC,OAAO,GAAG2F,QAAQ;IACvC;IACA3F,gBAAgB,GAAG2F,QAAQ;EAC/B,CAAC,MAAM;IACH;IACA,IAAI,UAAc,KAAKtF,SAAS,IAAIsH,wBAAU,EAAE;MAC5CA,mCAAO,YAAY;QACf,OAAOhC,QAAQ;MACnB,CAAC;AAAA,kGAAC;IACN,CAAC,MAAM,IAAI,OAAO1F,MAAM,KAAKK,UAAU,EAAE;MACrC;MACAL,MAAM,CAAC0F,QAAQ,GAAGA,QAAQ;IAC9B;EACJ;;EAEA;EACA;EACA;EACA;EACA;EACA,IAAIkC,CAAC,GAAG,OAAO5H,MAAM,KAAKK,UAAU,KAAKL,MAAM,CAAC6H,MAAM,IAAI7H,MAAM,CAAC8H,KAAK,CAAC;EACvE,IAAIF,CAAC,IAAI,CAACA,CAAC,CAACxD,EAAE,EAAE;IACZ,IAAI2D,MAAM,GAAG,IAAIrC,QAAQ,CAAC,CAAC;IAC3BkC,CAAC,CAACxD,EAAE,GAAG2D,MAAM,CAACpC,SAAS,CAAC,CAAC;IACzBiC,CAAC,CAACxD,EAAE,CAAC4D,GAAG,GAAG,YAAY;MACnB,OAAOD,MAAM,CAACX,KAAK,CAAC,CAAC;IACzB,CAAC;IACDQ,CAAC,CAACxD,EAAE,CAAC6D,GAAG,GAAG,UAAU7D,EAAE,EAAE;MACrB2D,MAAM,CAACV,KAAK,CAACjD,EAAE,CAAC;MAChB,IAAI8D,MAAM,GAAGH,MAAM,CAACpC,SAAS,CAAC,CAAC;MAC/B,KAAK,IAAIwC,IAAI,IAAID,MAAM,EAAE;QACrBN,CAAC,CAACxD,EAAE,CAAC+D,IAAI,CAAC,GAAGD,MAAM,CAACC,IAAI,CAAC;MAC7B;IACJ,CAAC;EACL;AAEJ,CAAC,EAAE,OAAOnI,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAG,IAAI,CAAC;;;;;;;;;;;;;;ACx7B9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACqC;AACG;AACK;AAE7C,MAAMuI,cAAc,GAAG,CAAC;AACxB,MAAMC,eAAe,GAAG,CAAC;AACzB,MAAMC,eAAe,GAAG,CAAC;AACzB,MAAMC,iBAAiB,GAAG,CAAC;AAC3B,MAAMC,cAAc,GAAG,CAAC;AACxB,MAAMC,eAAe,GAAG,CAAC;;AAEzB;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,MAAM,EAAE;EAEnBA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACrB,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;EAC5B,MAAMC,QAAQ,GAAGZ,wDAAQ,CAACW,OAAO,CAAC,CAACE,WAAW,CAAC,CAAC;EAChD,MAAMC,QAAQ,GAAGJ,MAAM,CAACI,QAAQ;EAEhC,MAAMC,KAAK,GAAG,EAAE;EAEhB,IAAIC,QAAQ,EACRC,gBAAgB,EAChBC,cAAc,EACdC,SAAS;EAEb,SAASC,KAAKA,CAAA,EAAG;IACbH,gBAAgB,GAAG,IAAI;IACvBC,cAAc,GAAG,IAAI;IACrBC,SAAS,GAAG,IAAIE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IAEhC,IAAI,OAAO1J,MAAM,KAAK,WAAW,IAAIA,MAAM,CAAC2J,OAAO,EAAE;MACjDR,KAAK,CAACX,eAAe,CAAC,GAAGoB,QAAQ,CAAC5J,MAAM,CAAC2J,OAAO,CAACE,KAAK,CAAC;MACvDV,KAAK,CAACV,eAAe,CAAC,GAAGmB,QAAQ,CAAC5J,MAAM,CAAC2J,OAAO,CAACE,KAAK,CAAC;MACvDV,KAAK,CAACT,iBAAiB,CAAC,GAAGkB,QAAQ,CAAC5J,MAAM,CAAC2J,OAAO,CAACG,IAAI,CAAC;MACxDX,KAAK,CAACR,cAAc,CAAC,GAAGiB,QAAQ,CAAC5J,MAAM,CAAC2J,OAAO,CAACI,IAAI,CAAC;MACrDZ,KAAK,CAACP,eAAe,CAAC,GAAGgB,QAAQ,CAAC5J,MAAM,CAAC2J,OAAO,CAACK,KAAK,CAAC;IAC3D;EACJ;EAEA,SAASJ,QAAQA,CAACK,EAAE,EAAE;IAClB,IAAIA,EAAE,IAAIA,EAAE,CAACC,IAAI,EAAE;MACf,OAAOD,EAAE,CAACC,IAAI,CAAClK,MAAM,CAAC2J,OAAO,CAAC;IAClC;IACA;IACA,OAAO3J,MAAM,CAAC2J,OAAO,CAACQ,GAAG,CAACD,IAAI,CAAClK,MAAM,CAAC2J,OAAO,CAAC;EAClD;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASS,SAASA,CAAChB,QAAQ,EAAE;IACzB,OAAO;MACHiB,KAAK,EAAEA,KAAK,CAACH,IAAI,CAACd,QAAQ,CAAC;MAC3BS,KAAK,EAAEA,KAAK,CAACK,IAAI,CAACd,QAAQ,CAAC;MAC3BU,IAAI,EAAEA,IAAI,CAACI,IAAI,CAACd,QAAQ,CAAC;MACzBW,IAAI,EAAEA,IAAI,CAACG,IAAI,CAACd,QAAQ,CAAC;MACzBY,KAAK,EAAEA,KAAK,CAACE,IAAI,CAACd,QAAQ;IAC9B,CAAC;EACL;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASkB,sBAAsBA,CAACC,KAAK,EAAE;IACnClB,gBAAgB,GAAGkB,KAAK;EAC5B;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASC,oBAAoBA,CAACD,KAAK,EAAE;IACjCjB,cAAc,GAAGiB,KAAK;EAC1B;EAEA,SAASF,KAAKA,CAAA,EAAY;IAAA,SAAAI,IAAA,GAAAnN,SAAA,CAAAlB,MAAA,EAARsO,MAAM,OAAAC,KAAA,CAAAF,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;MAANF,MAAM,CAAAE,IAAA,IAAAtN,SAAA,CAAAsN,IAAA;IAAA;IACpBC,KAAK,CAACrC,eAAe,EAAE,IAAI,EAAE,GAAGkC,MAAM,CAAC;EAC3C;EAEA,SAASb,KAAKA,CAAA,EAAY;IAAA,SAAAiB,KAAA,GAAAxN,SAAA,CAAAlB,MAAA,EAARsO,MAAM,OAAAC,KAAA,CAAAG,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAANL,MAAM,CAAAK,KAAA,IAAAzN,SAAA,CAAAyN,KAAA;IAAA;IACpBF,KAAK,CAACpC,eAAe,EAAE,IAAI,EAAE,GAAGiC,MAAM,CAAC;EAC3C;EAEA,SAASZ,IAAIA,CAAA,EAAY;IAAA,SAAAkB,KAAA,GAAA1N,SAAA,CAAAlB,MAAA,EAARsO,MAAM,OAAAC,KAAA,CAAAK,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAANP,MAAM,CAAAO,KAAA,IAAA3N,SAAA,CAAA2N,KAAA;IAAA;IACnBJ,KAAK,CAACnC,iBAAiB,EAAE,IAAI,EAAE,GAAGgC,MAAM,CAAC;EAC7C;EAEA,SAASX,IAAIA,CAAA,EAAY;IAAA,SAAAmB,KAAA,GAAA5N,SAAA,CAAAlB,MAAA,EAARsO,MAAM,OAAAC,KAAA,CAAAO,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAANT,MAAM,CAAAS,KAAA,IAAA7N,SAAA,CAAA6N,KAAA;IAAA;IACnBN,KAAK,CAAClC,cAAc,EAAE,IAAI,EAAE,GAAG+B,MAAM,CAAC;EAC1C;EAEA,SAASV,KAAKA,CAAA,EAAY;IAAA,SAAAoB,KAAA,GAAA9N,SAAA,CAAAlB,MAAA,EAARsO,MAAM,OAAAC,KAAA,CAAAS,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAANX,MAAM,CAAAW,KAAA,IAAA/N,SAAA,CAAA+N,KAAA;IAAA;IACpBR,KAAK,CAACjC,eAAe,EAAE,IAAI,EAAE,GAAG8B,MAAM,CAAC;EAC3C;EAEA,SAASG,KAAKA,CAACS,KAAK,EAAEC,KAAK,EAAa;IACpC,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIC,OAAO,GAAG,IAAI;IAElB,IAAIpC,gBAAgB,EAAE;MAClBoC,OAAO,GAAG,IAAIhC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MAC9B8B,OAAO,IAAI,GAAG,IAAIC,OAAO,GAAGlC,SAAS,CAAC,GAAG,GAAG;IAChD;IAEA,IAAID,cAAc,IAAIiC,KAAK,IAAIA,KAAK,CAACG,YAAY,EAAE;MAC/CF,OAAO,IAAI,GAAG,GAAGD,KAAK,CAACG,YAAY,CAAC,CAAC,GAAG,GAAG;MAC3C,IAAIH,KAAK,CAACI,OAAO,EAAE;QACfH,OAAO,IAAI,GAAG,GAAGD,KAAK,CAACI,OAAO,CAAC,CAAC,GAAG,GAAG;MAC1C;IACJ;IAEA,IAAIH,OAAO,CAACpP,MAAM,GAAG,CAAC,EAAE;MACpBoP,OAAO,IAAI,GAAG;IAClB;IAAC,SAAAI,KAAA,GAAAtO,SAAA,CAAAlB,MAAA,EAlB2BsO,MAAM,OAAAC,KAAA,CAAAiB,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAANnB,MAAM,CAAAmB,KAAA,QAAAvO,SAAA,CAAAuO,KAAA;IAAA;IAoBlClB,KAAK,CAACmB,KAAK,CAAC,IAAI,EAAEpB,MAAM,CAAC,CAACqB,OAAO,CAAC,UAAUC,IAAI,EAAE;MAC9CR,OAAO,IAAIQ,IAAI,GAAG,GAAG;IACzB,CAAC,CAAC;;IAEF;IACA,IAAI7C,KAAK,CAACmC,KAAK,CAAC,IAAIpC,QAAQ,IAAIA,QAAQ,CAAClB,GAAG,CAAC,CAAC,CAACgC,KAAK,CAACiC,QAAQ,IAAIX,KAAK,EAAE;MACpEnC,KAAK,CAACmC,KAAK,CAAC,CAACE,OAAO,CAAC;IACzB;;IAEA;IACA,IAAItC,QAAQ,IAAIA,QAAQ,CAAClB,GAAG,CAAC,CAAC,CAACgC,KAAK,CAACkC,aAAa,EAAE;MAChDlD,QAAQ,CAACmD,OAAO,CAAC9D,yDAAM,CAAC+D,GAAG,EAAE;QAAEZ,OAAO,EAAEA,OAAO;QAAEF,KAAK,EAAEA;MAAM,CAAC,CAAC;IACpE;EACJ;EAEAlC,QAAQ,GAAG;IACPgB,SAAS,EAAEA,SAAS;IACpBE,sBAAsB,EAAEA,sBAAsB;IAC9CE,oBAAoB,EAAEA;EAC1B,CAAC;EAEDhB,KAAK,CAAC,CAAC;EAEP,OAAOJ,QAAQ;AACnB;AAEAP,KAAK,CAACwD,qBAAqB,GAAG,OAAO;AAErC,MAAMC,OAAO,GAAGhE,wDAAY,CAACiE,mBAAmB,CAAC1D,KAAK,CAAC;AACvDyD,OAAO,CAAC/D,cAAc,GAAGA,cAAc;AACvC+D,OAAO,CAAC9D,eAAe,GAAGA,eAAe;AACzC8D,OAAO,CAAC7D,eAAe,GAAGA,eAAe;AACzC6D,OAAO,CAAC5D,iBAAiB,GAAGA,iBAAiB;AAC7C4D,OAAO,CAAC3D,cAAc,GAAGA,cAAc;AACvC2D,OAAO,CAAC1D,eAAe,GAAGA,eAAe;AACzCN,wDAAY,CAACkE,sBAAsB,CAAC3D,KAAK,CAACwD,qBAAqB,EAAEC,OAAO,CAAC;AACzE,+DAAeA,OAAO;;;;;;;;;;;;;ACtMtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAC6C;AACqB;AAElE,MAAMI,kBAAkB,GAAG,CAAC;AAC5B,MAAMC,mBAAmB,GAAG,IAAI;AAEhC,SAASvE,QAAQA,CAAA,EAAG;EAEhB,IAAIwE,QAAQ,GAAG,CAAC,CAAC;EAEjB,SAASC,SAASA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAyC;IAAA,IAAvCC,OAAO,GAAA3P,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;IAAA,IAAE4P,eAAe,GAAA5P,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;IAE3E,IAAI,CAACwP,IAAI,EAAE;MACP,MAAM,IAAIK,KAAK,CAAC,wCAAwC,CAAC;IAC7D;IACA,IAAI,CAACJ,QAAQ,IAAI,OAAQA,QAAS,KAAK,UAAU,EAAE;MAC/C,MAAM,IAAII,KAAK,CAAC,+BAA+B,GAAGJ,QAAQ,CAAC;IAC/D;IAEA,IAAIK,QAAQ,GAAGH,OAAO,CAACG,QAAQ,IAAIV,kBAAkB;IAErD,IAAIW,aAAa,CAACP,IAAI,EAAEC,QAAQ,EAAEC,KAAK,CAAC,IAAI,CAAC,EAAE;MAC3C;IACJ;IAEAJ,QAAQ,CAACE,IAAI,CAAC,GAAGF,QAAQ,CAACE,IAAI,CAAC,IAAI,EAAE;IAErC,MAAMQ,OAAO,GAAG;MACZC,QAAQ,EAAER,QAAQ;MAClBC,KAAK;MACLI,QAAQ;MACRF;IACJ,CAAC;IAED,IAAIF,KAAK,IAAIA,KAAK,CAACQ,WAAW,EAAE;MAC5BF,OAAO,CAACG,QAAQ,GAAGT,KAAK,CAACQ,WAAW,CAAC,CAAC;IAC1C;IACA,IAAIR,KAAK,IAAIA,KAAK,CAACrB,OAAO,EAAE;MACxB2B,OAAO,CAACI,SAAS,GAAGV,KAAK,CAACrB,OAAO,CAAC,CAAC;IACvC;IACA,IAAIsB,OAAO,IAAIA,OAAO,CAACU,IAAI,EAAE;MACzBL,OAAO,CAACK,IAAI,GAAGV,OAAO,CAACU,IAAI;IAC/B;IAEA,MAAMC,QAAQ,GAAGhB,QAAQ,CAACE,IAAI,CAAC,CAACe,IAAI,CAAC,CAAC7B,IAAI,EAAE8B,GAAG,KAAK;MAChD,IAAI9B,IAAI,IAAIoB,QAAQ,GAAGpB,IAAI,CAACoB,QAAQ,EAAE;QAClCR,QAAQ,CAACE,IAAI,CAAC,CAACiB,MAAM,CAACD,GAAG,EAAE,CAAC,EAAER,OAAO,CAAC;QACtC,OAAO,IAAI;MACf;IACJ,CAAC,CAAC;IAEF,IAAI,CAACM,QAAQ,EAAE;MACXhB,QAAQ,CAACE,IAAI,CAAC,CAACkB,IAAI,CAACV,OAAO,CAAC;IAChC;EACJ;EAEA,SAASW,EAAEA,CAACnB,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAgB;IAAA,IAAdC,OAAO,GAAA3P,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;IAC3CuP,SAAS,CAACC,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,CAAC;EAC7C;EAEA,SAASiB,IAAIA,CAACpB,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAgB;IAAA,IAAdC,OAAO,GAAA3P,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;IAC7CuP,SAAS,CAACC,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAE,IAAI,CAAC;EACnD;EAEA,SAASkB,GAAGA,CAACrB,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAE;IAChC,IAAI,CAACF,IAAI,IAAI,CAACC,QAAQ,IAAI,CAACH,QAAQ,CAACE,IAAI,CAAC,EAAE;MACvC;IACJ;IACA,MAAMgB,GAAG,GAAGT,aAAa,CAACP,IAAI,EAAEC,QAAQ,EAAEC,KAAK,CAAC;IAChD,IAAIc,GAAG,GAAG,CAAC,EAAE;MACT;IACJ;IACAlB,QAAQ,CAACE,IAAI,CAAC,CAACgB,GAAG,CAAC,GAAG,IAAI;EAC9B;EAEA,SAAS3B,OAAOA,CAACW,IAAI,EAA8B;IAAA,IAA5BsB,OAAO,GAAA9Q,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;IAAA,IAAE+Q,OAAO,GAAA/Q,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;IAC7C,IAAI,CAACwP,IAAI,IAAI,CAACF,QAAQ,CAACE,IAAI,CAAC,EAAE;MAC1B;IACJ;IAEAsB,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IAEvB,IAAIA,OAAO,CAACE,cAAc,CAAC,MAAM,CAAC,EAAE;MAChC,MAAM,IAAInB,KAAK,CAAC,mDAAmD,CAAC;IACxE;IAEAiB,OAAO,CAACtB,IAAI,GAAGA,IAAI;IAEnB,IAAIuB,OAAO,CAACZ,QAAQ,EAAE;MAClBW,OAAO,CAACX,QAAQ,GAAGY,OAAO,CAACZ,QAAQ;IACvC;IACA,IAAIY,OAAO,CAACX,SAAS,EAAE;MACnBU,OAAO,CAACV,SAAS,GAAGW,OAAO,CAACX,SAAS;IACzC;IAEA,MAAMa,gBAAgB,GAAG,EAAE;IAC3B3B,QAAQ,CAACE,IAAI,CAAC,CACT0B,MAAM,CAAElB,OAAO,IAAK;MACjB,IAAI,CAACA,OAAO,EAAE;QACV,OAAO,KAAK;MAChB;MACA,IAAIe,OAAO,CAACZ,QAAQ,IAAIH,OAAO,CAACG,QAAQ,IAAIH,OAAO,CAACG,QAAQ,KAAKY,OAAO,CAACZ,QAAQ,EAAE;QAC/E,OAAO,KAAK;MAChB;MACA,IAAIY,OAAO,CAACX,SAAS,IAAIJ,OAAO,CAACI,SAAS,IAAIJ,OAAO,CAACI,SAAS,KAAKW,OAAO,CAACX,SAAS,EAAE;QACnF,OAAO,KAAK;MAChB;MACA;MACA,IAAKW,OAAO,CAACV,IAAI,IAAIL,OAAO,CAACK,IAAI,IAAIL,OAAO,CAACK,IAAI,KAAKU,OAAO,CAACV,IAAI,IAAM,CAACL,OAAO,CAACK,IAAI,IAAIU,OAAO,CAACV,IAAI,IAAIU,OAAO,CAACV,IAAI,KAAKlB,uEAAiB,CAACgC,qBAAsB,EAAE;QAChK,OAAO,KAAK;MAChB;MACA,OAAO,IAAI;IACf,CAAC,CAAC,CACD1C,OAAO,CAAEuB,OAAO,IAAK;MAClBA,OAAO,IAAIA,OAAO,CAACC,QAAQ,CAACxI,IAAI,CAACuI,OAAO,CAACN,KAAK,EAAEoB,OAAO,CAAC;MACxD,IAAId,OAAO,CAACJ,eAAe,EAAE;QACzBqB,gBAAgB,CAACP,IAAI,CAACV,OAAO,CAAC;MAClC;IACJ,CAAC,CAAC;IAENiB,gBAAgB,CAACxC,OAAO,CAAEuB,OAAO,IAAK;MAClCa,GAAG,CAACrB,IAAI,EAAEQ,OAAO,CAACC,QAAQ,EAAED,OAAO,CAACN,KAAK,CAAC;IAC9C,CAAC,CAAC;EACN;EAEA,SAASK,aAAaA,CAACP,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAE;IAE1C,IAAIc,GAAG,GAAG,CAAC,CAAC;IAEZ,IAAI,CAAClB,QAAQ,CAACE,IAAI,CAAC,EAAE;MACjB,OAAOgB,GAAG;IACd;IAEAlB,QAAQ,CAACE,IAAI,CAAC,CAACe,IAAI,CAAC,CAAC7B,IAAI,EAAE0C,KAAK,KAAK;MACjC,IAAI1C,IAAI,IAAIA,IAAI,CAACuB,QAAQ,KAAKR,QAAQ,KAAK,CAACC,KAAK,IAAIA,KAAK,KAAKhB,IAAI,CAACgB,KAAK,CAAC,EAAE;QACxEc,GAAG,GAAGY,KAAK;QACX,OAAO,IAAI;MACf;IACJ,CAAC,CAAC;IACF,OAAOZ,GAAG;EACd;EAEA,SAASa,KAAKA,CAAA,EAAG;IACb/B,QAAQ,GAAG,CAAC,CAAC;EACjB;EAEA,MAAMxD,QAAQ,GAAG;IACb6E,EAAE;IACFC,IAAI;IACJC,GAAG;IACHhC,OAAO;IACPwC;EACJ,CAAC;EAED,OAAOvF,QAAQ;AACnB;AAEAhB,QAAQ,CAACiE,qBAAqB,GAAG,UAAU;AAC3C,MAAMC,OAAO,GAAGhE,wDAAY,CAACiE,mBAAmB,CAACnE,QAAQ,CAAC;AAC1DkE,OAAO,CAACI,kBAAkB,GAAGA,kBAAkB;AAC/CJ,OAAO,CAACK,mBAAmB,GAAGA,mBAAmB;AACjDrE,wDAAY,CAACkE,sBAAsB,CAACpE,QAAQ,CAACiE,qBAAqB,EAAEC,OAAO,CAAC;AAC5E,+DAAeA,OAAO;;;;;;;;;;;AChMtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMhE,YAAY,GAAI,YAAY;EAE9B,IAAIc,QAAQ;EACZ,IAAIwF,iBAAiB,GAAG,EAAE;EAC1B,MAAMC,kBAAkB,GAAG,CAAC,CAAC;EAC7B,MAAMC,cAAc,GAAG,CAAC,CAAC;EAEzB,SAASlM,MAAMA,CAAC7F,IAAI,EAAEgS,aAAa,EAAEC,QAAQ,EAAEjG,OAAO,EAAE;IACpD,IAAI,CAACA,OAAO,CAAChM,IAAI,CAAC,IAAIgS,aAAa,EAAE;MACjChG,OAAO,CAAChM,IAAI,CAAC,GAAG;QACZqM,QAAQ,EAAE2F,aAAa;QACvBC,QAAQ,EAAEA;MACd,CAAC;IACL;EACJ;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASC,oBAAoBA,CAAClG,OAAO,EAAEmG,SAAS,EAAE;IAC9C,KAAK,MAAM/S,CAAC,IAAIyS,iBAAiB,EAAE;MAC/B,MAAMO,GAAG,GAAGP,iBAAiB,CAACzS,CAAC,CAAC;MAChC,IAAIgT,GAAG,CAACpG,OAAO,KAAKA,OAAO,IAAIoG,GAAG,CAACpS,IAAI,KAAKmS,SAAS,EAAE;QACnD,OAAOC,GAAG,CAAC/F,QAAQ;MACvB;IACJ;IACA,OAAO,IAAI;EACf;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASgG,oBAAoBA,CAACrG,OAAO,EAAEmG,SAAS,EAAE9F,QAAQ,EAAE;IACxD,KAAK,MAAMjN,CAAC,IAAIyS,iBAAiB,EAAE;MAC/B,MAAMO,GAAG,GAAGP,iBAAiB,CAACzS,CAAC,CAAC;MAChC,IAAIgT,GAAG,CAACpG,OAAO,KAAKA,OAAO,IAAIoG,GAAG,CAACpS,IAAI,KAAKmS,SAAS,EAAE;QACnDN,iBAAiB,CAACzS,CAAC,CAAC,CAACiN,QAAQ,GAAGA,QAAQ;QACxC;MACJ;IACJ;IACAwF,iBAAiB,CAACZ,IAAI,CAAC;MACnBjR,IAAI,EAAEmS,SAAS;MACfnG,OAAO,EAAEA,OAAO;MAChBK,QAAQ,EAAEA;IACd,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASiG,wBAAwBA,CAACtG,OAAO,EAAE;IACvC6F,iBAAiB,GAAGA,iBAAiB,CAACJ,MAAM,CAACc,CAAC,IAAIA,CAAC,CAACvG,OAAO,KAAKA,OAAO,CAAC;EAC5E;;EAEA;;EAEA;;EAEA;;EAEA,SAASwG,gBAAgBA,CAACxS,IAAI,EAAEyS,cAAc,EAAE;IAC5C,OAAOA,cAAc,CAACzS,IAAI,CAAC;EAC/B;EAEA,SAAS0S,aAAaA,CAAC1S,IAAI,EAAEuP,OAAO,EAAEkD,cAAc,EAAE;IAClD,IAAIzS,IAAI,IAAIyS,cAAc,EAAE;MACxBA,cAAc,CAACzS,IAAI,CAAC,GAAGuP,OAAO;IAClC;EACJ;;EAEA;;EAEA;;EAEA;;EAEA,SAASoD,kBAAkBA,CAAC3S,IAAI,EAAEuP,OAAO,EAAE;IACvCmD,aAAa,CAAC1S,IAAI,EAAEuP,OAAO,EAAEwC,cAAc,CAAC;EAChD;EAEA,SAASa,qBAAqBA,CAAC5S,IAAI,EAAE;IACjC,OAAOwS,gBAAgB,CAACxS,IAAI,EAAE+R,cAAc,CAAC;EACjD;EAEA,SAASc,eAAeA,CAACC,gBAAgB,EAAE;IACvC,IAAIvD,OAAO,GAAGiD,gBAAgB,CAACM,gBAAgB,CAACxD,qBAAqB,EAAEyC,cAAc,CAAC;IAEtF,IAAI,CAACxC,OAAO,EAAE;MACVA,OAAO,GAAG,SAAAA,CAAUvD,OAAO,EAAE;QACzB,IAAIA,OAAO,KAAKxL,SAAS,EAAE;UACvBwL,OAAO,GAAG,CAAC,CAAC;QAChB;QACA,OAAO;UACH+G,MAAM,EAAE,SAAAA,CAAA,EAAY;YAChB,OAAOC,KAAK,CAACF,gBAAgB,EAAE9G,OAAO,EAAEzL,SAAS,CAAC;UACtD;QACJ,CAAC;MACL,CAAC;MAEDwR,cAAc,CAACe,gBAAgB,CAACxD,qBAAqB,CAAC,GAAGC,OAAO,CAAC,CAAC;IACtE;IACA,OAAOA,OAAO;EAClB;;EAEA;;EAEA;;EAEA;;EAEA,SAASE,sBAAsBA,CAACzP,IAAI,EAAEuP,OAAO,EAAE;IAC3CmD,aAAa,CAAC1S,IAAI,EAAEuP,OAAO,EAAEuC,kBAAkB,CAAC;EACpD;EAEA,SAASmB,yBAAyBA,CAACjT,IAAI,EAAE;IACrC,OAAOwS,gBAAgB,CAACxS,IAAI,EAAE8R,kBAAkB,CAAC;EACrD;EAEA,SAAStC,mBAAmBA,CAACsD,gBAAgB,EAAE;IAC3C,IAAIvD,OAAO,GAAGiD,gBAAgB,CAACM,gBAAgB,CAACxD,qBAAqB,EAAEwC,kBAAkB,CAAC;IAC1F,IAAI,CAACvC,OAAO,EAAE;MACVA,OAAO,GAAG,SAAAA,CAAUvD,OAAO,EAAE;QACzB,IAAIK,QAAQ;QACZ,IAAIL,OAAO,KAAKxL,SAAS,EAAE;UACvBwL,OAAO,GAAG,CAAC,CAAC;QAChB;QACA,OAAO;UACHE,WAAW,EAAE,SAAAA,CAAA,EAAY;YACrB;YACA,IAAI,CAACG,QAAQ,EAAE;cACXA,QAAQ,GAAG6F,oBAAoB,CAAClG,OAAO,EAAE8G,gBAAgB,CAACxD,qBAAqB,CAAC;YACpF;YACA;YACA,IAAI,CAACjD,QAAQ,EAAE;cACXA,QAAQ,GAAG2G,KAAK,CAACF,gBAAgB,EAAE9G,OAAO,EAAEzL,SAAS,CAAC;cACtDsR,iBAAiB,CAACZ,IAAI,CAAC;gBACnBjR,IAAI,EAAE8S,gBAAgB,CAACxD,qBAAqB;gBAC5CtD,OAAO,EAAEA,OAAO;gBAChBK,QAAQ,EAAEA;cACd,CAAC,CAAC;YACN;YACA,OAAOA,QAAQ;UACnB;QACJ,CAAC;MACL,CAAC;MACDyF,kBAAkB,CAACgB,gBAAgB,CAACxD,qBAAqB,CAAC,GAAGC,OAAO,CAAC,CAAC;IAC1E;IAEA,OAAOA,OAAO;EAClB;EAEA,SAASyD,KAAKA,CAACF,gBAAgB,EAAE9G,OAAO,EAAEkH,IAAI,EAAE;IAE5C,IAAIC,aAAa;IACjB,MAAMhB,SAAS,GAAGW,gBAAgB,CAACxD,qBAAqB;IACxD,MAAM8D,eAAe,GAAGpH,OAAO,CAACmG,SAAS,CAAC;IAE1C,IAAIiB,eAAe,EAAE;MAEjB,IAAIC,SAAS,GAAGD,eAAe,CAAC/G,QAAQ;MAExC,IAAI+G,eAAe,CAACnB,QAAQ,EAAE;QAAE;;QAE5BkB,aAAa,GAAGL,gBAAgB,CAAC/D,KAAK,CAAC;UAAC/C;QAAO,CAAC,EAAEkH,IAAI,CAAC;QACvDG,SAAS,GAAGA,SAAS,CAACtE,KAAK,CAAC;UACxB/C,OAAO;UACPuD,OAAO,EAAElD,QAAQ;UACjBiH,MAAM,EAAEH;QACZ,CAAC,EAAED,IAAI,CAAC;QAER,KAAK,MAAM9H,IAAI,IAAIiI,SAAS,EAAE;UAC1B,IAAIF,aAAa,CAAC5B,cAAc,CAACnG,IAAI,CAAC,EAAE;YACpC+H,aAAa,CAAC/H,IAAI,CAAC,GAAGiI,SAAS,CAACjI,IAAI,CAAC;UACzC;QACJ;MAEJ,CAAC,MAAM;QAAE;;QAEL,OAAOiI,SAAS,CAACtE,KAAK,CAAC;UACnB/C,OAAO;UACPuD,OAAO,EAAElD;QACb,CAAC,EAAE6G,IAAI,CAAC;MAEZ;IACJ,CAAC,MAAM;MACH;MACAC,aAAa,GAAGL,gBAAgB,CAAC/D,KAAK,CAAC;QAAC/C;MAAO,CAAC,EAAEkH,IAAI,CAAC;IAC3D;;IAEA;IACAC,aAAa,CAACxE,YAAY,GAAG,YAAY;MAAC,OAAOwD,SAAS;IAAC,CAAC;IAE5D,OAAOgB,aAAa;EACxB;EAEA9G,QAAQ,GAAG;IACPxG,MAAM,EAAEA,MAAM;IACdqM,oBAAoB,EAAEA,oBAAoB;IAC1CG,oBAAoB,EAAEA,oBAAoB;IAC1CC,wBAAwB,EAAEA,wBAAwB;IAClD9C,mBAAmB,EAAEA,mBAAmB;IACxCyD,yBAAyB,EAAEA,yBAAyB;IACpDxD,sBAAsB,EAAEA,sBAAsB;IAC9CoD,eAAe,EAAEA,eAAe;IAChCD,qBAAqB,EAAEA,qBAAqB;IAC5CD,kBAAkB,EAAEA;EACxB,CAAC;EAED,OAAOtG,QAAQ;AAEnB,CAAC,CAAC,CAAE;AAEJ,+DAAed,YAAY;;;;;;;;;;;;;;;;;;ACzQ3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAC6C;AACd;AACM;AACuB;AACO;AAC9B;AACG;;AAEx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mI,QAAQA,CAAA,EAAG;EAChB,IAAIrH,QAAQ;EACZ,MAAML,OAAO,GAAG,IAAI,CAACA,OAAO;EAC5B,MAAMC,QAAQ,GAAGZ,wDAAQ,CAACW,OAAO,CAAC,CAACE,WAAW,CAAC,CAAC;EAChD,MAAMyH,gBAAgB,GAAG;IACrB,2BAA2B,EAAErI,yDAAM,CAACsI,0BAA0B;IAC9D,wCAAwC,EAAEtI,yDAAM,CAACuI,yCAAyC;IAC1F,+BAA+B,EAAEvI,yDAAM,CAACwI,+BAA+B;IACvE,wCAAwC,EAAExI,yDAAM,CAACyI,iCAAiC;IAClF,wCAAwC,EAAEzI,yDAAM,CAAC0I,iCAAiC;IAClF,2CAA2C,EAAE1I,yDAAM,CAAC2I,gCAAgC;IACpF,qCAAqC,EAAE3I,yDAAM,CAAC2I,gCAAgC;IAC9E,mDAAmD,EAAE3I,yDAAM,CAAC2I,gCAAgC;IAC5F,8CAA8C,EAAE3I,yDAAM,CAAC2I,gCAAgC;IACvF,8CAA8C,EAAE3I,yDAAM,CAAC2I,gCAAgC;IACvF,gDAAgD,EAAE3I,yDAAM,CAAC2I,gCAAgC;IACzF,oCAAoC,EAAE3I,yDAAM,CAAC2I,gCAAgC;IAC7E,qCAAqC,EAAE3I,yDAAM,CAAC2I,gCAAgC;IAC9E,gCAAgC,EAAE3I,yDAAM,CAAC4I,2BAA2B;IACpE,gCAAgC,EAAE5I,yDAAM,CAAC4I,2BAA2B;IACpE,gCAAgC,EAAE5I,yDAAM,CAAC6I,2BAA2B;IACpE,gCAAgC,EAAE7I,yDAAM,CAAC6I;EAC7C,CAAC;;EAED;AACJ;AACA;AACA;EACI,MAAMC,eAAe,GAAG;IACpBnH,KAAK,EAAE;MACHiC,QAAQ,EAAEpD,sDAAK,CAACH,iBAAiB;MACjCwD,aAAa,EAAE;IACnB,CAAC;IACDkF,SAAS,EAAE;MACPC,kBAAkB,EAAE,KAAK;MACzBC,2BAA2B,EAAE,GAAG;MAChCC,2BAA2B,EAAE,GAAG;MAChCC,qCAAqC,EAAE,CAAC;MACxCC,iBAAiB,EAAE,KAAK;MACxBC,uBAAuB,EAAE,IAAI;MAC7BC,0BAA0B,EAAE,IAAI;MAChCC,oBAAoB,EAAE,IAAI;MAC1BC,iCAAiC,EAAE,IAAI;MACvCC,eAAe,EAAE,KAAK;MACtBC,kCAAkC,EAAE,KAAK;MACzCC,YAAY,EAAE;QACVC,oCAAoC,EAAE,IAAI;QAC1CC,4BAA4B,EAAE,CAC1B;UAAEC,WAAW,EAAE5B,yEAAS,CAAC6B;QAAyB,CAAC,EACnD;UAAED,WAAW,EAAE5B,yEAAS,CAAC8B,8BAA8B;UAAE9H,KAAK,EAAE;QAAU,CAAC,EAC3E;UAAE4H,WAAW,EAAE5B,yEAAS,CAAC+B;QAAsB,CAAC,EAChD;UAAEH,WAAW,EAAE5B,yEAAS,CAACgC;QAA0B,CAAC,EACpD;UAAEJ,WAAW,EAAE5B,yEAAS,CAACiC,iCAAiC;UAAEjI,KAAK,EAAE;QAAU,CAAC,EAC9E;UAAE4H,WAAW,EAAE5B,yEAAS,CAACkC,sCAAsC;UAAElI,KAAK,EAAE;QAAe,CAAC,EACxF,GAAGgG,yEAAS,CAACmC,yBAAyB,CAACxN,GAAG,CAACyN,EAAE,IAAI;UAC7C,OAAO;YAAE,aAAa,EAAEA;UAAG,CAAC;QAChC,CAAC,CAAC,CACL;QACDC,uBAAuB,EAAE,IAAI;QAC7BC,yCAAyC,EAAE,KAAK;QAChDC,0CAA0C,EAAE;MAChD,CAAC;MACDC,MAAM,EAAE;QACJC,2BAA2B,EAAE,GAAG;QAChCC,6BAA6B,EAAE;MACnC,CAAC;MACDC,eAAe,EAAE;QACbC,uBAAuB,EAAE,KAAK;QAC9BC,yBAAyB,EAAE;MAC/B,CAAC;MACDC,OAAO,EAAE;QACLC,YAAY,EAAE;MAClB,CAAC;MACDC,KAAK,EAAE;QACHC,sBAAsB,EAAEC,GAAG;QAC3BC,SAAS,EAAED,GAAG;QACdE,6BAA6B,EAAE;MACnC,CAAC;MACDC,UAAU,EAAE;QACRC,uBAAuB,EAAE,KAAK;QAC9BC,uBAAuB,EAAE,KAAK;QAC9BC,4BAA4B,EAAE,IAAI;QAClCC,iBAAiB,EAAE;MACvB,CAAC;MACDC,MAAM,EAAE;QACJC,0BAA0B,EAAE,KAAK;QACjCC,iBAAiB,EAAE,IAAI;QACvBC,wBAAwB,EAAE,KAAK;QAC/BC,0BAA0B,EAAE,IAAI;QAChCC,qBAAqB,EAAE,EAAE;QACzBC,YAAY,EAAE,EAAE;QAChBC,sBAAsB,EAAE,EAAE;QAC1BC,8BAA8B,EAAE,EAAE;QAClCC,kBAAkB,EAAEjB,GAAG;QACvBkB,iBAAiB,EAAE,EAAE;QACrBC,gCAAgC,EAAE,GAAG;QACrCC,cAAc,EAAE,GAAG;QACnBC,wBAAwB,EAAE,GAAG;QAC7BC,eAAe,EAAE,IAAI;QACrBC,aAAa,EAAE,IAAI;QACnBC,4BAA4B,EAAE,KAAK;QACnCC,aAAa,EAAE,IAAI;QACnBC,2BAA2B,EAAE,IAAI;QACjCC,gCAAgC,EAAE,KAAK;QACvCC,oBAAoB,EAAE;UAClBC,OAAO,EAAE,KAAK;UACdC,gBAAgB,EAAE;QACtB;MACJ,CAAC;MACDC,IAAI,EAAE;QACFC,QAAQ,EAAE,IAAI;QACdC,aAAa,EAAE,IAAI;QACnBC,aAAa,EAAE,GAAG;QAClBC,SAAS,EAAE,GAAG;QACdC,aAAa,EAAE,IAAI;QACnBC,cAAc,EAAE,KAAK;QACrBC,SAAS,EAAE;MACf,CAAC;MACDC,kBAAkB,EAAE;QAChBV,OAAO,EAAE,IAAI;QACbW,+BAA+B,EAAE,IAAI;QACrCC,kBAAkB,EAAE,CAAC;QACrBC,uBAAuB,EAAE,EAAE;QAC3BC,8BAA8B,EAAE,GAAG;QACnCC,8BAA8B,EAAE,CAAC;QACjCC,uCAAuC,EAAE,CAAC;QAC1CC,mBAAmB,EAAE,GAAG;QACxBC,6CAA6C,EAAE,IAAI;QACnDC,mBAAmB,EAAE;UACjBC,MAAM,EAAE,oCAAoC;UAC5CnM,KAAK,EAAE;QACX;MACJ,CAAC;MACDoM,UAAU,EAAE;QACRC,cAAc,EAAE,GAAG;QACnBC,iBAAiB,EAAE,CAAC;QACpBC,mBAAmB,EAAE;MACzB,CAAC;MACDC,IAAI,EAAE;QACFC,cAAc,EAAE,IAAI;QACpBC,0BAA0B,EAAE,KAAK;QACjCC,mBAAmB,EAAE,IAAI;QACzBC,IAAI,EAAE;UACFC,qBAAqB,EAAE,KAAK;UAC5BC,YAAY,EAAE;QAClB,CAAC;QACDC,MAAM,EAAE;UACJC,sBAAsB,EAAE;QAC5B;MACJ,CAAC;MACDC,WAAW,EAAE;QACTC,QAAQ,EAAEhE,GAAG;QACbiE,YAAY,EAAE;UACVC,GAAG,EAAElE,GAAG;UACRmE,GAAG,EAAEnE;QACT,CAAC;QACDoE,iBAAiB,EAAE,GAAG;QACtBvC,OAAO,EAAE,IAAI;QACb3H,IAAI,EAAE4C,yEAAS,CAACuH;MACpB,CAAC;MACDC,sBAAsB,EAAE;QACpBzC,OAAO,EAAE,IAAI;QACb0C,GAAG,EAAE;MACT,CAAC;MACDC,4BAA4B,EAAE;QAC1B3C,OAAO,EAAE,IAAI;QACb0C,GAAG,EAAE;MACT,CAAC;MACDE,+CAA+C,EAAE,IAAI;MACrDC,mBAAmB,EAAE;QACjBC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE;MACX,CAAC;MACDC,eAAe,EAAE;QACbD,KAAK,EAAE9H,yEAAS,CAACgI,gCAAgC;QACjDH,KAAK,EAAE7H,yEAAS,CAACiI;MACrB,CAAC;MACDC,uBAAuB,EAAE,KAAK;MAC9BC,kBAAkB,EAAE,IAAI;MACxBC,uBAAuB,EAAE,IAAI;MAC7BC,4BAA4B,EAAErI,yEAAS,CAACsI,uCAAuC;MAC/EC,sBAAsB,EAAE,KAAK;MAC7BC,8BAA8B,EAAE,CAAC,CAAC;MAClCC,sBAAsB,EAAE,KAAK;MAC7BC,cAAc,EAAE;QACZ,CAACzI,6EAAW,CAAC0I,QAAQ,GAAG,GAAG;QAC3B,CAAC1I,6EAAW,CAAC2I,oBAAoB,GAAG,GAAG;QACvC,CAAC3I,6EAAW,CAAC4I,kBAAkB,GAAG,IAAI;QACtC,CAAC5I,6EAAW,CAAC6I,iBAAiB,GAAG,IAAI;QACrC,CAAC7I,6EAAW,CAAC8I,gCAAgC,GAAG,IAAI;QACpD,CAAC9I,6EAAW,CAAC+I,kBAAkB,GAAG,IAAI;QACtC,CAAC/I,6EAAW,CAACgJ,8BAA8B,GAAG,IAAI;QAClD,CAAChJ,6EAAW,CAACiJ,OAAO,GAAG,IAAI;QAC3B,CAACjJ,6EAAW,CAACkJ,UAAU,GAAG,IAAI;QAC9BC,yBAAyB,EAAE;MAC/B,CAAC;MACDC,aAAa,EAAE;QACX,CAACpJ,6EAAW,CAAC0I,QAAQ,GAAG,CAAC;QACzB,CAAC1I,6EAAW,CAAC2I,oBAAoB,GAAG,CAAC;QACrC,CAAC3I,6EAAW,CAAC4I,kBAAkB,GAAG,CAAC;QACnC,CAAC5I,6EAAW,CAAC6I,iBAAiB,GAAG,CAAC;QAClC,CAAC7I,6EAAW,CAAC8I,gCAAgC,GAAG,CAAC;QACjD,CAAC9I,6EAAW,CAAC+I,kBAAkB,GAAG,CAAC;QACnC,CAAC/I,6EAAW,CAACgJ,8BAA8B,GAAG,CAAC;QAC/C,CAAChJ,6EAAW,CAACiJ,OAAO,GAAG,CAAC;QACxB,CAACjJ,6EAAW,CAACkJ,UAAU,GAAG,CAAC;QAC3BG,wBAAwB,EAAE;MAC9B,CAAC;MACDC,GAAG,EAAE;QACDC,oBAAoB,EAAE,KAAK;QAC3BC,mCAAmC,EAAE,KAAK;QAC1CC,gDAAgD,EAAE,IAAI;QACtDC,KAAK,EAAE;UACHC,cAAc,EAAE;YACZC,MAAM,EAAE;UACZ,CAAC;UACDC,QAAQ,EAAE;YACND,MAAM,EAAE;UACZ,CAAC;UACDE,sBAAsB,EAAE;YACpBF,MAAM,EAAE,IAAI;YACZG,UAAU,EAAE;cACRC,sBAAsB,EAAE,GAAG;cAC3BC,kBAAkB,EAAE;YACxB;UACJ,CAAC;UACDC,iBAAiB,EAAE;YACfN,MAAM,EAAE,IAAI;YACZG,UAAU,EAAE;cACRI,UAAU,EAAE,CAAC;cACbC,yBAAyB,EAAE;YAC/B;UACJ,CAAC;UACDC,iBAAiB,EAAE;YACfT,MAAM,EAAE,KAAK;YACbG,UAAU,EAAE;cACRO,iBAAiB,EAAE,GAAG;cACtBC,gCAAgC,EAAE;YACtC;UACJ,CAAC;UACDC,mBAAmB,EAAE;YACjBZ,MAAM,EAAE,IAAI;YACZG,UAAU,EAAE;cACRU,yBAAyB,EAAE,GAAG;cAC9BC,mCAAmC,EAAE,GAAG;cACxCC,6BAA6B,EAAE;YACnC;UACJ,CAAC;UACDC,OAAO,EAAE;YACLhB,MAAM,EAAE;UACZ,CAAC;UACDiB,QAAQ,EAAE;YACNjB,MAAM,EAAE;UACZ;QACJ,CAAC;QACDkB,UAAU,EAAE;UACRC,sBAAsB,EAAEhL,yEAAS,CAACiL,4BAA4B,CAACC,IAAI;UACnEC,qCAAqC,EAAEnL,yEAAS,CAACoL,0CAA0C,CAACC,YAAY;UACxGC,oBAAoB,EAAE,IAAI;UAC1BC,wBAAwB,EAAE;YACtBC,GAAG,EAAE,KAAK;YACVC,KAAK,EAAE;UACX,CAAC;UACDC,kBAAkB,EAAE,IAAI;UACxBC,qBAAqB,EAAE,GAAG;UAC1BC,cAAc,EAAE;YACZC,IAAI,EAAE,CAAC;YACPC,GAAG,EAAE,CAAC;YACNC,0BAA0B,EAAE,IAAI;YAChCC,aAAa,EAAE,GAAG;YAClBC,aAAa,EAAE,GAAG;YAClBC,qBAAqB,EAAE,EAAE;YACzBC,0BAA0B,EAAE;UAChC,CAAC;UACDC,IAAI,EAAE;YACFC,6BAA6B,EAAE,CAAC;YAChCC,6BAA6B,EAAE,CAAC;YAChCC,wBAAwB,EAAE,CAAC;YAC3BC,wBAAwB,EAAE,CAAC;YAC3BC,sCAAsC,EAAE;UAC5C;QACJ,CAAC;QACDC,UAAU,EAAE;UACR5E,KAAK,EAAE,CAAC,CAAC;UACTD,KAAK,EAAE,CAAC;QACZ,CAAC;QACD8E,UAAU,EAAE;UACR7E,KAAK,EAAE,CAAC,CAAC;UACTD,KAAK,EAAE,CAAC;QACZ,CAAC;QACD+E,cAAc,EAAE;UACZ9E,KAAK,EAAE,CAAC,CAAC;UACTD,KAAK,EAAE,CAAC;QACZ,CAAC;QACDgF,iBAAiB,EAAE;UACf/E,KAAK,EAAE,IAAI;UACXD,KAAK,EAAE;QACX;MACJ,CAAC;MACDiF,IAAI,EAAE;QACFC,sBAAsB,EAAE,IAAI;QAC5BhI,OAAO,EAAE,KAAK;QACdiI,GAAG,EAAE,IAAI;QACTC,GAAG,EAAE,IAAI;QACTC,GAAG,EAAE,IAAI;QACTC,eAAe,EAAE,CAAC;QAClB/P,IAAI,EAAE4C,yEAAS,CAACoN,eAAe;QAC/BC,WAAW,EAAErN,yEAAS,CAACsN,mBAAmB;QAC1CC,iBAAiB,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC;QACrCja,OAAO,EAAE;MACb,CAAC;MACDka,IAAI,EAAE;QACFzI,OAAO,EAAE,KAAK;QACdwE,GAAG,EAAE;UACDkE,OAAO,EAAE,KAAK;UACdC,cAAc,EAAE;QACpB;MACJ,CAAC;MACDC,kBAAkB,EAAE;QAChBC,SAAS,EAAE,EAAE;QACbC,yBAAyB,EAAE,0CAA0C;QACrEC,IAAI,EAAE,yBAAyB;QAC/BC,aAAa,EAAE;MACnB;IACJ,CAAC;IACDC,MAAM,EAAE;MACJC,eAAe,EAAE;QACbC,gBAAgB,EAAE;MACtB;IACJ;EACJ,CAAC;EAED,IAAIvV,QAAQ,GAAGoH,iDAAK,CAACoO,KAAK,CAACvN,eAAe,CAAC;;EAE3C;EACA;EACA,SAASwN,aAAaA,CAACC,MAAM,EAAEC,IAAI,EAAErjB,IAAI,EAAE;IACvC,KAAK,IAAIsjB,CAAC,IAAIF,MAAM,EAAE;MAClB,IAAIA,MAAM,CAACtQ,cAAc,CAACwQ,CAAC,CAAC,EAAE;QAC1B,IAAID,IAAI,CAACvQ,cAAc,CAACwQ,CAAC,CAAC,EAAE;UACxB,IAAI,OAAOF,MAAM,CAACE,CAAC,CAAC,KAAK,QAAQ,IAAI,EAAEF,MAAM,CAACE,CAAC,CAAC,YAAYC,MAAM,CAAC,IAAI,EAAEH,MAAM,CAACE,CAAC,CAAC,YAAYnU,KAAK,CAAC,IAAIiU,MAAM,CAACE,CAAC,CAAC,KAAK,IAAI,EAAE;YACxHH,aAAa,CAACC,MAAM,CAACE,CAAC,CAAC,EAAED,IAAI,CAACC,CAAC,CAAC,EAAEtjB,IAAI,CAACgB,KAAK,CAAC,CAAC,GAAGsiB,CAAC,GAAG,GAAG,CAAC;UAC7D,CAAC,MAAM;YACHD,IAAI,CAACC,CAAC,CAAC,GAAGxO,iDAAK,CAACoO,KAAK,CAACE,MAAM,CAACE,CAAC,CAAC,CAAC;YAChC,IAAIpO,gBAAgB,CAAClV,IAAI,GAAGsjB,CAAC,CAAC,EAAE;cAC5B9V,QAAQ,CAACmD,OAAO,CAACuE,gBAAgB,CAAClV,IAAI,GAAGsjB,CAAC,CAAC,CAAC;YAChD;UACJ;QACJ,CAAC,MAAM;UACHnV,OAAO,CAACE,KAAK,CAAC,qBAAqB,GAAGrO,IAAI,GAAGsjB,CAAC,GAAG,mBAAmB,CAAC;QACzE;MACJ;IACJ;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EACI,SAAS9W,GAAGA,CAAA,EAAG;IACX,OAAOkB,QAAQ;EACnB;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAS8V,MAAMA,CAACC,WAAW,EAAE;IACzB,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;MACjCN,aAAa,CAACM,WAAW,EAAE/V,QAAQ,EAAE,EAAE,CAAC;IAC5C;EACJ;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,SAASyF,KAAKA,CAAA,EAAG;IACbzF,QAAQ,GAAGoH,iDAAK,CAACoO,KAAK,CAACvN,eAAe,CAAC;EAC3C;EAEA/H,QAAQ,GAAG;IACPpB,GAAG;IACHgX,MAAM;IACNrQ;EACJ,CAAC;EAED,OAAOvF,QAAQ;AACnB;AAGAqH,QAAQ,CAACpE,qBAAqB,GAAG,UAAU;AAC3C,IAAIC,OAAO,GAAGhE,wDAAY,CAACiE,mBAAmB,CAACkE,QAAQ,CAAC;AACxD,+DAAenE,OAAO;;;;;;;;;;;;;;AC77CtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEkC;AACG;AACuB;AAE5D,MAAMgE,KAAK,CAAC;EACR,OAAO4O,KAAKA,CAACL,IAAI,EAAED,MAAM,EAAEO,IAAI,EAAE;IAC7B,IAAIC,CAAC;IACL,IAAIC,KAAK,GAAG,CAAC,CAAC;IACd,IAAIR,IAAI,EAAE;MACN,KAAK,IAAI9hB,IAAI,IAAI6hB,MAAM,EAAE;QACrB,IAAIA,MAAM,CAACtQ,cAAc,CAACvR,IAAI,CAAC,EAAE;UAC7BqiB,CAAC,GAAGR,MAAM,CAAC7hB,IAAI,CAAC;UAChB,IAAI,EAAEA,IAAI,IAAI8hB,IAAI,CAAC,IAAKA,IAAI,CAAC9hB,IAAI,CAAC,KAAKqiB,CAAC,KAAK,EAAEriB,IAAI,IAAIsiB,KAAK,CAAC,IAAIA,KAAK,CAACtiB,IAAI,CAAC,KAAKqiB,CAAC,CAAE,EAAE;YAClF,IAAI,OAAOP,IAAI,CAAC9hB,IAAI,CAAC,KAAK,QAAQ,IAAI8hB,IAAI,CAAC9hB,IAAI,CAAC,KAAK,IAAI,EAAE;cACvD8hB,IAAI,CAAC9hB,IAAI,CAAC,GAAGuT,KAAK,CAAC4O,KAAK,CAACL,IAAI,CAAC9hB,IAAI,CAAC,EAAEqiB,CAAC,EAAED,IAAI,CAAC;YACjD,CAAC,MAAM;cACHN,IAAI,CAAC9hB,IAAI,CAAC,GAAGoiB,IAAI,CAACC,CAAC,CAAC;YACxB;UACJ;QACJ;MACJ;IACJ;IACA,OAAOP,IAAI;EACf;EAEA,OAAOH,KAAKA,CAACY,GAAG,EAAE;IACd,IAAI,CAACA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MACjC,OAAOA,GAAG,CAAC,CAAC;IAChB;IACA,IAAIA,GAAG,YAAYP,MAAM,EAAE;MACvB,OAAO,IAAIA,MAAM,CAACO,GAAG,CAAC;IAC1B;IACA,IAAIC,CAAC;IACL,IAAID,GAAG,YAAY3U,KAAK,EAAE;MACtB;MACA4U,CAAC,GAAG,EAAE;MACN,KAAK,IAAIpjB,CAAC,GAAG,CAAC,EAAEqjB,CAAC,GAAGF,GAAG,CAACljB,MAAM,EAAED,CAAC,GAAGqjB,CAAC,EAAE,EAAErjB,CAAC,EAAE;QACxC,IAAIA,CAAC,IAAImjB,GAAG,EAAE;UACVC,CAAC,CAACvR,IAAI,CAACsC,KAAK,CAACoO,KAAK,CAACY,GAAG,CAACnjB,CAAC,CAAC,CAAC,CAAC;QAC/B;MACJ;IACJ,CAAC,MAAM;MACHojB,CAAC,GAAG,CAAC,CAAC;IACV;IACA,OAAOjP,KAAK,CAAC4O,KAAK,CAACK,CAAC,EAAED,GAAG,EAAEhP,KAAK,CAACoO,KAAK,CAAC;EAC3C;EAEA,OAAOe,gCAAgCA,CAACC,GAAG,EAAEhV,MAAM,EAAE;IACjD,IAAI;MACA,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACtO,MAAM,KAAK,CAAC,EAAE;QAChC,OAAOsjB,GAAG;MACd;MAEA,IAAIC,UAAU,GAAGD,GAAG;MACpBhV,MAAM,CAACqB,OAAO,CAAC6T,IAAA,IAAoB;QAAA,IAAnB;UAAEC,GAAG;UAAEtV;QAAM,CAAC,GAAAqV,IAAA;QAC1B,MAAME,SAAS,GAAGH,UAAU,CAACI,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;QACtDJ,UAAU,IAAI,GAAGG,SAAS,GAAIE,kBAAkB,CAACH,GAAG,CAAC,IAAMG,kBAAkB,CAACzV,KAAK,CAAC,EAAG;MAC3F,CAAC,CAAC;MACF,OAAOoV,UAAU;IACrB,CAAC,CAAC,OAAOM,CAAC,EAAE;MACR,OAAOP,GAAG;IACd;EACJ;EAEA,OAAOQ,2BAA2BA,CAACR,GAAG,EAAES,cAAc,EAAE;IACpD,IAAI,CAACT,GAAG,IAAI,CAACS,cAAc,EAAE;MACzB,OAAOT,GAAG;IACd;IACA;IACA,MAAMU,SAAS,GAAG,IAAIC,GAAG,CAACX,GAAG,CAAC;;IAE9B;IACA,MAAMhV,MAAM,GAAG,IAAI4V,eAAe,CAACF,SAAS,CAACG,MAAM,CAAC;IAEpD,IAAI,CAAC7V,MAAM,IAAIA,MAAM,CAAC8V,IAAI,KAAK,CAAC,IAAI,CAAC9V,MAAM,CAACrH,GAAG,CAAC8c,cAAc,CAAC,EAAE;MAC7D,OAAOT,GAAG;IACd;;IAEA;IACAhV,MAAM,CAAC+V,MAAM,CAACN,cAAc,CAAC;;IAE7B;IACA,MAAMO,WAAW,GAAG/V,KAAK,CAAC3M,IAAI,CAAC0M,MAAM,CAACiW,OAAO,CAAC,CAAC,CAAC,CAC3Czb,GAAG,CAAC0b,KAAA;MAAA,IAAC,CAACf,GAAG,EAAEtV,KAAK,CAAC,GAAAqW,KAAA;MAAA,OAAK,GAAGf,GAAG,IAAItV,KAAK,EAAE;IAAA,EAAC,CACxC3M,IAAI,CAAC,GAAG,CAAC;;IAEd;IACA,MAAMijB,OAAO,GAAG,GAAGT,SAAS,CAACU,MAAM,GAAGV,SAAS,CAACW,QAAQ,EAAE;IAC1D,OAAOL,WAAW,GAAG,GAAGG,OAAO,IAAIH,WAAW,EAAE,GAAGG,OAAO;EAC9D;EAEA,OAAOG,gBAAgBA,CAACC,SAAS,EAAE;IAC/B,IAAIC,OAAO,GAAG,CAAC,CAAC;IAChB,IAAI,CAACD,SAAS,EAAE;MACZ,OAAOC,OAAO;IAClB;;IAEA;IACA;IACA,IAAIC,WAAW,GAAGF,SAAS,CAACjd,IAAI,CAAC,CAAC,CAACD,KAAK,CAAC,cAAc,CAAC;IACxD,KAAK,IAAI5H,CAAC,GAAG,CAAC,EAAEilB,IAAI,GAAGD,WAAW,CAAC/kB,MAAM,EAAED,CAAC,GAAGilB,IAAI,EAAEjlB,CAAC,EAAE,EAAE;MACtD,IAAIklB,UAAU,GAAGF,WAAW,CAAChlB,CAAC,CAAC;MAC/B,IAAIuS,KAAK,GAAG2S,UAAU,CAAC5d,OAAO,CAAC,cAAc,CAAC;MAC9C,IAAIiL,KAAK,GAAG,CAAC,EAAE;QACXwS,OAAO,CAACG,UAAU,CAACnd,SAAS,CAAC,CAAC,EAAEwK,KAAK,CAAC,CAAC,GAAG2S,UAAU,CAACnd,SAAS,CAACwK,KAAK,GAAG,CAAC,CAAC;MAC7E;IACJ;IACA,OAAOwS,OAAO;EAClB;;EAEA;AACJ;AACA;AACA;AACA;EACI,OAAOI,gBAAgBA,CAACC,gBAAgB,EAAE;IACtC,MAAM7W,MAAM,GAAG,EAAE;IACjB,MAAM8W,YAAY,GAAG,IAAIlB,eAAe,CAACiB,gBAAgB,CAAC;IAC1D,KAAK,MAAM,CAAC1B,GAAG,EAAEtV,KAAK,CAAC,IAAIiX,YAAY,CAACb,OAAO,CAAC,CAAC,EAAE;MAC/CjW,MAAM,CAACsD,IAAI,CAAC;QAAE6R,GAAG,EAAE4B,kBAAkB,CAAC5B,GAAG,CAAC;QAAEtV,KAAK,EAAEkX,kBAAkB,CAAClX,KAAK;MAAE,CAAC,CAAC;IACnF;IACA,OAAOG,MAAM;EACjB;EAEA,OAAOgX,YAAYA,CAAA,EAAG;IAClB,IAAIC,EAAE,GAAG,IAAIlY,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IAC7B,MAAMkY,IAAI,GAAG,sCAAsC,CAAC9d,OAAO,CAAC,OAAO,EAAE,UAAU+d,CAAC,EAAE;MAC9E,MAAMtC,CAAC,GAAG,CAACoC,EAAE,GAAGG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC;MAC5CJ,EAAE,GAAGG,IAAI,CAACE,KAAK,CAACL,EAAE,GAAG,EAAE,CAAC;MACxB,OAAO,CAACE,CAAC,IAAI,GAAG,GAAGtC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI,EAAE0C,QAAQ,CAAC,EAAE,CAAC;IACxD,CAAC,CAAC;IACF,OAAOL,IAAI;EACf;EAEA,OAAOM,gBAAgBA,CAACC,MAAM,EAAE;IAC5B,IAAIC,IAAI,GAAG,CAAC;IAEZ,IAAID,MAAM,CAAC/lB,MAAM,KAAK,CAAC,EAAE;MACrB,OAAOgmB,IAAI;IACf;IAEA,KAAK,IAAIjmB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgmB,MAAM,CAAC/lB,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,MAAMkmB,GAAG,GAAGF,MAAM,CAAC9lB,UAAU,CAACF,CAAC,CAAC;MAChCimB,IAAI,GAAI,CAACA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAIC,GAAG;MACjCD,IAAI,IAAI,CAAC;IACb;IACA,OAAOA,IAAI;EACf;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,OAAOE,cAAcA,CAACC,WAAW,EAAEC,SAAS,EAAE;IAC1C,IAAI;MACA,MAAMC,QAAQ,GAAG,IAAIpC,GAAG,CAACkC,WAAW,CAAC;MACrC,MAAMG,MAAM,GAAG,IAAIrC,GAAG,CAACmC,SAAS,CAAC;;MAEjC;MACAC,QAAQ,CAACE,QAAQ,GAAGD,MAAM,CAACC,QAAQ;MACnC,IAAIF,QAAQ,CAAC3B,MAAM,KAAK4B,MAAM,CAAC5B,MAAM,EAAE;QACnC,OAAO0B,SAAS;MACpB;;MAEA;MACA,IAAII,YAAY,GAAGpnB,qDAAa,CAACinB,QAAQ,CAAC1B,QAAQ,CAAC8B,MAAM,CAAC,CAAC,EAAEJ,QAAQ,CAAC1B,QAAQ,CAACxkB,WAAW,CAAC,GAAG,CAAC,CAAC,EAAEmmB,MAAM,CAAC3B,QAAQ,CAAC8B,MAAM,CAAC,CAAC,EAAEH,MAAM,CAAC3B,QAAQ,CAACxkB,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;;MAE9J;MACA,MAAMumB,gBAAgB,GAAGF,YAAY,CAACxmB,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;MAC1DwmB,YAAY,IAAIF,MAAM,CAAC3B,QAAQ,CAAC8B,MAAM,CAACH,MAAM,CAAC3B,QAAQ,CAACxkB,WAAW,CAAC,GAAG,CAAC,GAAGumB,gBAAgB,EAAEJ,MAAM,CAAC3B,QAAQ,CAAC3kB,MAAM,GAAG,CAAC,CAAC;;MAEvH;MACA,IAAIsmB,MAAM,CAAC3B,QAAQ,CAAC3kB,MAAM,GAAGwmB,YAAY,CAACxmB,MAAM,EAAE;QAC9C,OAAOsmB,MAAM,CAAC3B,QAAQ;MAC1B;MACA,OAAO6B,YAAY;IACvB,CAAC,CAAC,OAAO3C,CAAC,EAAE;MACR,OAAOuC,SAAS;IACpB;EACJ;EAEA,OAAOO,cAAcA,CAACC,SAAS,EAAE;IAC7B,IAAI;MACA,MAAMtD,GAAG,GAAG,IAAIW,GAAG,CAAC2C,SAAS,CAAC;MAE9B,OAAOtD,GAAG,CAACuD,IAAI;IACnB,CAAC,CAAC,OAAOhD,CAAC,EAAE;MACR,OAAO,IAAI;IACf;EACJ;EAEA,OAAOiD,cAAcA,CAAA,EAAY;IAAA,IAAX9e,EAAE,GAAA9G,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;IAC3B,IAAI;MACA,MAAM6lB,QAAQ,GAAG/e,EAAE,KAAK,IAAI,GAAG,OAAOyB,SAAS,KAAK,WAAW,GAAGA,SAAS,CAACE,SAAS,CAACpC,WAAW,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;MAE7G,OAAO+B,sDAAQ,CAACyd,QAAQ,CAAC;IAC7B,CAAC,CAAC,OAAOlD,CAAC,EAAE;MACR,OAAO,CAAC,CAAC;IACb;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EACI,OAAOmD,iBAAiBA,CAACjB,MAAM,EAAE;IAC7B,OAAQ,kBAAkB,CAACnd,IAAI,CAACmd,MAAM,CAAC;EAC3C;EAEA,OAAOkB,sBAAsBA,CAACC,YAAY,EAAE;IACxC,OAAOhT,KAAK,CAACiT,UAAU,CAACD,YAAY,EAAEE,QAAQ,CAAC;EACnD;EAEA,OAAOC,kBAAkBA,CAACH,YAAY,EAAE;IACpC,OAAOhT,KAAK,CAACiT,UAAU,CAACD,YAAY,EAAEI,UAAU,CAAC;EACrD;EAEA,OAAOC,kBAAkBA,CAACC,UAAU,EAAE;IAClC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,OAAO,CAAC;IACxC,OAAOD,OAAO,CAACE,MAAM,CAACH,UAAU,CAAC;EACrC;EAEA,OAAOI,iBAAiBA,CAACC,IAAI,EAAE;IAC3B,MAAM/gB,GAAG,GAAGoN,KAAK,CAACmT,kBAAkB,CAACQ,IAAI,CAAC;IAC1C,IAAIC,GAAG,GAAG,EAAE;IACZ,KAAK,IAAI3Z,KAAK,IAAIrH,GAAG,EAAE;MACnBqH,KAAK,GAAGA,KAAK,CAAC0X,QAAQ,CAAC,EAAE,CAAC;MAC1B,IAAI1X,KAAK,CAACnO,MAAM,KAAK,CAAC,EAAE;QACpBmO,KAAK,GAAG,GAAG,GAAGA,KAAK;MACvB;MACA2Z,GAAG,IAAI3Z,KAAK;IAChB;IACA,OAAO2Z,GAAG;EACd;EAEA,OAAOX,UAAUA,CAACD,YAAY,EAAEa,IAAI,EAAE;IAClC,MAAMlQ,MAAM,GAAG3D,KAAK,CAAC8T,cAAc,CAACd,YAAY,CAAC;IACjD,IAAIe,eAAe,GAAG,CAAC;IACvB,IAAI,mBAAmB,IAAIb,QAAQ,EAAE;MACjCa,eAAe,GAAGb,QAAQ,CAACc,iBAAiB;IAChD;IAEA,MAAMC,OAAO,GAAG,CAAC,CAACjB,YAAY,CAACkB,UAAU,IAAI,CAAC,IAAIlB,YAAY,CAACmB,UAAU,IACrEJ,eAAe;IACnB,MAAMK,QAAQ,GAAG,CAAEpB,YAAY,CAACkB,UAAU,IAAI,CAAC,IAAKH,eAAe;IACnE,MAAMnlB,KAAK,GAAG4iB,IAAI,CAACE,KAAK,CAACF,IAAI,CAAClK,GAAG,CAAC,CAAC,EAAEkK,IAAI,CAACnK,GAAG,CAAC+M,QAAQ,EAAEH,OAAO,CAAC,CAAC,CAAC;IAClE,MAAMxlB,GAAG,GAAG+iB,IAAI,CAACE,KAAK,CAACF,IAAI,CAACnK,GAAG,CAACzY,KAAK,GAAG4iB,IAAI,CAAClK,GAAG,CAAC+M,QAAQ,EAAE,CAAC,CAAC,EAAEJ,OAAO,CAAC,CAAC;IACxE,OAAO,IAAIJ,IAAI,CAAClQ,MAAM,EAAE/U,KAAK,EAAEH,GAAG,GAAGG,KAAK,CAAC;EAC/C;EAEA,OAAOklB,cAAcA,CAACQ,IAAI,EAAE;IACxB,IAAIA,IAAI,YAAYC,WAAW,EAAE;MAC7B,OAAOD,IAAI;IACf,CAAC,MAAM;MACH,OAAOA,IAAI,CAAC3Q,MAAM;IACtB;EACJ;EAEA,OAAO6Q,cAAcA,CAACC,WAAW,EAAE;IAC/B,MAAM;MAAEjoB,IAAI;MAAEkoB;IAAQ,CAAC,GAAG1U,KAAK,CAAC2U,cAAc,CAACF,WAAW,CAAC;IAE3D,QAAQjoB,IAAI;MACR,KAAK,MAAM;QACP,QAAQkoB,OAAO;UACX,KAAK,IAAI;UACT,KAAK,IAAI;UACT,KAAK,OAAO;YACR,OAAOzU,yEAAS,CAAC2U,cAAc,CAACC,GAAG;UACvC,KAAK,IAAI;UACT,KAAK,IAAI;UACT,KAAK,IAAI;UACT,KAAK,MAAM;UACX,KAAK,OAAO;UACZ,KAAK,MAAM;UACX,KAAK,OAAO;UACZ,KAAK,OAAO;UACZ,KAAK,OAAO;YACR,OAAO5U,yEAAS,CAAC2U,cAAc,CAACE,GAAG;UACvC,KAAK,IAAI;YACL,OAAO7U,yEAAS,CAAC2U,cAAc,CAACG,GAAG;UACvC,KAAK,IAAI;YACL,OAAO9U,yEAAS,CAAC2U,cAAc,CAACI,GAAG;UACvC,KAAK,IAAI;YACL,OAAO/U,yEAAS,CAAC2U,cAAc,CAACK,IAAI;UACxC,KAAK,IAAI;YACL,OAAOhV,yEAAS,CAAC2U,cAAc,CAACM,IAAI;QAC5C;QACA;MACJ,KAAK,MAAM;MACX,KAAK,MAAM;QACP,OAAOjV,yEAAS,CAAC2U,cAAc,CAACO,GAAG;MACvC,KAAK,MAAM;MACX,KAAK,MAAM;QACP,OAAOlV,yEAAS,CAAC2U,cAAc,CAACQ,IAAI;MACxC;QACI,OAAO5oB,IAAI;IACnB;IAEA,OAAOA,IAAI;EACf;EAEA,OAAOmoB,cAAcA,CAACF,WAAW,EAAE;IAC/B,MAAM,CAACjoB,IAAI,EAAE,GAAG6oB,IAAI,CAAC,GAAGZ,WAAW,CAAChhB,KAAK,CAAC,GAAG,CAAC;IAC9C,MAAMihB,OAAO,GAAGW,IAAI,CAAC/nB,IAAI,CAAC,GAAG,CAAC;IAC9B,OAAO;MAAEd,IAAI;MAAEkoB;IAAQ,CAAC;EAC5B;AAEJ;AAEA,+DAAe1U,KAAK;;;;;;;;;;;;AC5VpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACyC;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuV,UAAU,SAASD,sDAAU,CAAC;EAChCE,WAAWA,CAAA,EAAI;IACX,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,uBAAuB,GAAG,uBAAuB;IACtD,IAAI,CAACC,mBAAmB,GAAG,oBAAoB;IAC/C,IAAI,CAACC,cAAc,GAAG,eAAe;IACrC,IAAI,CAACC,2BAA2B,GAAG,0BAA0B;IAC7D,IAAI,CAACC,0BAA0B,GAAG,0BAA0B;IAC5D,IAAI,CAACC,6BAA6B,GAAG,4BAA4B;IACjE,IAAI,CAACC,kBAAkB,GAAG,kBAAkB;IAC5C,IAAI,CAACC,qBAAqB,GAAG,qBAAqB;IAClD,IAAI,CAACC,qBAAqB,GAAG,qBAAqB;IAClD,IAAI,CAACC,aAAa,GAAG,cAAc;IACnC,IAAI,CAACC,qBAAqB,GAAG,qBAAqB;IAClD,IAAI,CAACC,oBAAoB,GAAG,oBAAoB;IAChD,IAAI,CAACC,oBAAoB,GAAG,oBAAoB;IAChD,IAAI,CAACC,wBAAwB,GAAG,wBAAwB;IACxD,IAAI,CAACC,wBAAwB,GAAG,wBAAwB;IACxD,IAAI,CAACC,iBAAiB,GAAG,kBAAkB;IAC3C,IAAI,CAACC,gBAAgB,GAAG,iBAAiB;IACzC,IAAI,CAACC,qBAAqB,GAAG,qBAAqB;IAClD,IAAI,CAACC,iBAAiB,GAAG,gBAAgB;IACzC,IAAI,CAACC,gBAAgB,GAAG,iBAAiB;IACzC,IAAI,CAACC,qBAAqB,GAAG,qBAAqB;IAClD,IAAI,CAACC,qBAAqB,GAAG,qBAAqB;IAClD,IAAI,CAACC,iBAAiB,GAAG,kBAAkB;IAC3C,IAAI,CAACC,cAAc,GAAG,eAAe;IACrC,IAAI,CAACC,8BAA8B,GAAG,6BAA6B;IACnE,IAAI,CAACC,kCAAkC,GAAG,iCAAiC;IAC3E,IAAI,CAACC,uCAAuC,GAAG,6BAA6B;IAC5E,IAAI,CAACC,2CAA2C,GAAG,iCAAiC;IACpF,IAAI,CAACC,uCAAuC,GAAG,qCAAqC;IACpF,IAAI,CAACC,2CAA2C,GAAG,yCAAyC;IAC5F,IAAI,CAACC,kCAAkC,GAAG,gCAAgC;IAC1E,IAAI,CAACC,uBAAuB,GAAG,sBAAsB;IACrD,IAAI,CAACC,mBAAmB,GAAG,mBAAmB;IAC9C,IAAI,CAACC,gBAAgB,GAAG,iBAAiB;IACzC,IAAI,CAACC,0BAA0B,GAAG,0BAA0B;IAC5D,IAAI,CAACC,2BAA2B,GAAG,2BAA2B;IAC9D,IAAI,CAACC,6BAA6B,GAAG,4BAA4B;IACjE,IAAI,CAACC,8BAA8B,GAAG,6BAA6B;IACnE,IAAI,CAACC,uBAAuB,GAAG,sBAAsB;IACrD,IAAI,CAACC,qBAAqB,GAAG,qBAAqB;IAClD,IAAI,CAACC,oBAAoB,GAAG,oBAAoB;IAChD,IAAI,CAACC,sBAAsB,GAAG,sBAAsB;IACpD,IAAI,CAACC,oBAAoB,GAAG,oBAAoB;IAChD,IAAI,CAACC,WAAW,GAAG,YAAY;IAC/B,IAAI,CAACC,WAAW,GAAG,YAAY;IAC/B,IAAI,CAAChY,0BAA0B,GAAG,yBAAyB;IAC3D,IAAI,CAACC,yCAAyC,GAAG,sCAAsC;IACvF,IAAI,CAACC,+BAA+B,GAAG,8BAA8B;IACrE,IAAI,CAACC,iCAAiC,GAAG,+BAA+B;IACxE,IAAI,CAACC,iCAAiC,GAAG,+BAA+B;IACxE,IAAI,CAACC,gCAAgC,GAAG,8BAA8B;IACtE,IAAI,CAACC,2BAA2B,GAAG,0BAA0B;IAC7D,IAAI,CAACC,2BAA2B,GAAG,0BAA0B;EACjE;AACJ;AAEA,+DAAe2U,UAAU;;;;;;;;;;;;AClGzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACyC;AAEzC,MAAMxd,MAAM,SAASwd,sDAAU,CAAC;AAGhC,IAAI9S,MAAM,GAAG,IAAI1K,MAAM,CAAC,CAAC;AACzB,+DAAe0K,MAAM;;;;;;;;;;;ACxCrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6S,UAAU,CAAC;EACbhjB,MAAMA,CAACmQ,MAAM,EAAEjK,MAAM,EAAE;IACnB,IAAI,CAACiK,MAAM,EAAE;MACT;IACJ;IAEA,IAAI/D,QAAQ,GAAGlG,MAAM,GAAGA,MAAM,CAACkG,QAAQ,GAAG,KAAK;IAC/C,IAAI4Z,UAAU,GAAG9f,MAAM,GAAGA,MAAM,CAAC8f,UAAU,GAAG,KAAK;IAGnD,KAAK,MAAMC,GAAG,IAAI9V,MAAM,EAAE;MACtB,IAAI,CAACA,MAAM,CAACzE,cAAc,CAACua,GAAG,CAAC,IAAK,IAAI,CAACA,GAAG,CAAC,IAAI,CAAC7Z,QAAS,EAAE;QACzD;MACJ;MACA,IAAI4Z,UAAU,IAAI7V,MAAM,CAAC8V,GAAG,CAAC,CAACplB,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;QACrD;MACJ;MACA,IAAI,CAAColB,GAAG,CAAC,GAAG9V,MAAM,CAAC8V,GAAG,CAAC;IAE3B;EACJ;AACJ;AAEA,+DAAejD,UAAU;;;;;;;;;;;ACzDzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkD,SAAS,CAAC;EACZhD,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAAC3T,WAAW,GAAG,EAAE;IACrB,IAAI,CAAC5H,KAAK,GAAG,EAAE;EACnB;AACJ;AAEA,+DAAeue,SAAS;;;;;;;;;;;;AC1CxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACsD;;AAEtD;AACA;AACA;AACA;AACA,MAAMrc,iBAAiB,SAASmZ,kEAAU,CAAC;EAEvC;AACJ;AACA;EACIE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACiD,aAAa,GAAG,aAAa;;IAElC;AACR;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,iBAAiB;;IAE1C;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,eAAe;;IAEnC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,cAAc;;IAEnC;AACR;AACA;AACA;IACQ,IAAI,CAACC,0BAA0B,GAAG,oBAAoB;;IAEtD;AACR;AACA;AACA;IACQ,IAAI,CAACC,oBAAoB,GAAG,oBAAoB;;IAEhD;AACR;AACA;AACA;IACQ,IAAI,CAACC,uBAAuB,GAAG,sBAAsB;;IAErD;AACR;AACA;AACA;IACQ,IAAI,CAACC,0BAA0B,GAAG,yBAAyB;;IAE3D;AACR;AACA;AACA;IACQ,IAAI,CAACC,wBAAwB,GAAG,uBAAuB;;IAEvD;AACR;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,iBAAiB;;IAE1C;AACR;AACA;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,OAAO;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACC,0BAA0B,GAAG,0BAA0B;;IAE5D;AACR;AACA;AACA;IACQ,IAAI,CAACC,yBAAyB,GAAG,yBAAyB;IAC1D;AACR;AACA;AACA;IACQ,IAAI,CAACC,wBAAwB,GAAG,wBAAwB;;IAExD;AACR;AACA;AACA;IACQ,IAAI,CAACC,0BAA0B,GAAG,0BAA0B;;IAE5D;AACR;AACA;AACA;IACQ,IAAI,CAACzd,GAAG,GAAG,KAAK;;IAEhB;AACR;AACA;AACA;IACQ,IAAI,CAAC0d,wBAAwB,GAAG,wBAAwB;;IAExD;AACR;AACA;AACA;IACQ,IAAI,CAACC,yBAAyB,GAAG,yBAAyB;;IAE1D;AACR;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,gBAAgB;;IAEvC;AACR;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,gBAAgB;;IAEvC;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,eAAe;;IAErC;AACR;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,aAAa;;IAEjC;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,eAAe;;IAErC;AACR;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,qBAAqB;;IAElD;AACR;AACA;AACA;IACQ,IAAI,CAACC,uBAAuB,GAAG,uBAAuB;;IAEtD;AACR;AACA;AACA;IACQ,IAAI,CAACC,wBAAwB,GAAG,wBAAwB;;IAExD;AACR;AACA;AACA;IACQ,IAAI,CAACC,uBAAuB,GAAG,uBAAuB;;IAEtD;AACR;AACA;AACA;IACQ,IAAI,CAACC,kBAAkB,GAAG,kBAAkB;;IAE5C;AACR;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,qBAAqB;;IAElD;AACR;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,oBAAoB;;IAE/C;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,eAAe;;IAErC;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,iBAAiB;;IAEzC;AACR;AACA;AACA;IACQ,IAAI,CAACC,kBAAkB,GAAG,mBAAmB;;IAE7C;AACR;AACA;AACA;IACQ,IAAI,CAACC,kBAAkB,GAAG,mBAAmB;;IAE7C;AACR;AACA;AACA;IACQ,IAAI,CAACC,wBAAwB,GAAG,wBAAwB;;IAExD;AACR;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,oBAAoB;;IAE7C;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,gBAAgB;;IAExC;AACR;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,UAAU;;IAE3B;AACR;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,SAAS;;IAEzB;AACR;AACA;AACA;IACQ,IAAI,CAACC,6BAA6B,GAAG,6BAA6B;;IAElE;AACR;AACA;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,YAAY;;IAE/B;AACR;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,aAAa;;IAElC;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,iBAAiB;;IAEzC;AACR;AACA;AACA;IACQ,IAAI,CAACC,wBAAwB,GAAG,wBAAwB;;IAExD;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,SAAS;;IAEzB;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,gBAAgB;;IAExC;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,eAAe;;IAErC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,eAAe;;IAErC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,oBAAoB,GAAG,qBAAqB;;IAEjD;AACR;AACA;AACA;IACQ,IAAI,CAACC,oBAAoB,GAAG,oBAAoB;;IAEhD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,wBAAwB,GAAG,wBAAwB;;IAExD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,oBAAoB,GAAG,oBAAoB;;IAEhD;AACR;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,gBAAgB;;IAEvC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,iBAAiB;;IAEzC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,kBAAkB;;IAE3C;AACR;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,qBAAqB;;IAElD;AACR;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,gBAAgB;;IAEvC;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,iBAAiB;;IAEzC;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,iBAAiB;;IAEzC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,iBAAiB;;IAEzC;AACR;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,qBAAqB;;IAElD;AACR;AACA;AACA;IACQ,IAAI,CAACC,uBAAuB,GAAG,uBAAuB;;IAEtD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,iBAAiB;;IAEzC;AACR;AACA;AACA;IACQ,IAAI,CAACC,yBAAyB,GAAG,yBAAyB;;IAE1D;AACR;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,kBAAkB;;IAE7C;AACR;AACA;AACA;IACQ,IAAI,CAACre,qBAAqB,GAAG,oBAAoB;;IAEjD;AACR;AACA;AACA;IACQ,IAAI,CAACse,qBAAqB,GAAG,sBAAsB;;IAEnD;AACR;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,sBAAsB;;IAEnD;AACR;AACA;AACA;IACQ,IAAI,CAACC,sCAAsC,GAAG,oCAAoC;;IAElF;AACR;AACA;AACA;IACQ,IAAI,CAACC,kCAAkC,GAAG,iCAAiC;;IAE3E;AACR;AACA;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,YAAY;;IAE/B;AACR;AACA;AACA;IACQ,IAAI,CAACC,oCAAoC,GAAG,kCAAkC;;IAE9E;AACR;AACA;AACA;IACQ,IAAI,CAACC,kCAAkC,GAAG,gCAAgC;EAC9E;AACJ;AAEA,IAAIC,iBAAiB,GAAG,IAAI7gB,iBAAiB,CAAC,CAAC;AAC/C,+DAAe6gB,iBAAiB;;;;;;;;;;;ACnfhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,+DAAe;EACX;AACJ;AACA;AACA;AACA;EACIC,MAAM,EAAE,QAAQ;EAEhB;AACJ;AACA;AACA;AACA;EACIC,KAAK,EAAE,OAAO;EAEd;AACJ;AACA;AACA;AACA;EACIC,KAAK,EAAE,OAAO;EAEd;AACJ;AACA;AACA;AACA;EACIC,IAAI,EAAE,MAAM;EAEZ;AACJ;AACA;AACA;AACA;EACIC,KAAK,EAAE,OAAO;EAEd;AACJ;AACA;AACA;AACA;EACIC,KAAK,EAAE,OAAO;EAEd;AACJ;AACA;AACA;AACA;EACIC,IAAI,EAAE,MAAM;EAEZ;AACJ;AACA;AACA;AACA;EACIC,IAAI,EAAE,MAAM;EAEZ;AACJ;AACA;AACA;AACA;EACIC,GAAG,EAAE,KAAK;EAEV;AACJ;AACA;AACA;AACA;EACIC,IAAI,EAAE,MAAM;EAEZ;AACJ;AACA;AACA;AACA;EACIC,gBAAgB,EAAE,iBAAiB;EAEnC;AACJ;AACA;AACA;AACA;EACInW,yBAAyB,EAAE,wBAAwB;EAEnD;AACJ;AACA;AACA;AACA;EACIoW,sBAAsB,EAAE,qBAAqB;EAE7C;AACJ;AACA;AACA;AACA;EACIC,6BAA6B,EAAE,eAAe;EAE9C;AACJ;AACA;AACA;AACA;EACIC,mBAAmB,EAAE,MAAM;EAE3B;AACJ;AACA;AACA;AACA;EACIC,kBAAkB,EAAE,mBAAmB;EAEvC;AACJ;AACA;AACA;AACA;EACIC,oBAAoB,EAAE,6BAA6B;EAEnD;AACJ;AACA;AACA;AACA;EACI/V,gCAAgC,EAAE,eAAe;EAEjD;AACJ;AACA;AACA;AACA;EACIC,+BAA+B,EAAE,cAAc;EAE/C;AACJ;AACA;AACA;AACA;EACI+V,gCAAgC,EAAE,YAAY;EAE9C;AACJ;AACA;AACA;AACA;EACIC,oCAAoC,EAAE,gBAAgB;EAEtD;AACJ;AACA;AACA;AACA;EACI3V,uCAAuC,EAAE,mBAAmB;EAE5D;AACJ;AACA;AACA;AACA;EACI4V,iCAAiC,EAAE,aAAa;EAEhD;AACJ;AACA;AACA;AACA;EACIC,cAAc,EAAE,MAAM;EAEtB;AACJ;AACA;AACA;AACA;EACI/Q,eAAe,EAAE,OAAO;EAExB;AACJ;AACA;AACA;AACA;EACIgR,gBAAgB,EAAE,QAAQ;EAE1B;AACJ;AACA;AACA;AACA;EACI9Q,mBAAmB,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC;EACrI;AACJ;AACA;AACA;AACA;EACI+Q,sBAAsB,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAEtC;AACJ;AACA;AACA;AACA;EACIC,uBAAuB,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC;EAGzEC,UAAU,EAAE,YAAY;EACxBC,YAAY,EAAE,SAAS;EACvBC,WAAW,EAAE,QAAQ;EACrBC,aAAa,EAAE,UAAU;EACzBC,2BAA2B,EAAE,+BAA+B;EAC5DC,GAAG,EAAE,KAAK;EACVC,GAAG,EAAE,KAAK;EACVC,IAAI,EAAE,OAAO;EACbC,aAAa,EAAE,aAAa;EAC5BC,UAAU,EAAE,WAAW;EACvBC,iCAAiC,EAAE,oCAAoC;EACvEC,mCAAmC,EAAE,uCAAuC;EAC5EC,oBAAoB,EAAE,6BAA6B;EACnDhd,yBAAyB,EAAE,CAAC,kCAAkC,EAAE,6CAA6C,CAAC;EAC9GN,wBAAwB,EAAE,gCAAgC;EAC1DC,8BAA8B,EAAE,qCAAqC;EACrEC,qBAAqB,EAAE,6BAA6B;EACpDC,yBAAyB,EAAE,6BAA6B;EACxDC,iCAAiC,EAAE,wCAAwC;EAC3EC,sCAAsC,EAAE,6CAA6C;EACrFkd,iCAAiC,EAAE,sBAAsB;EACzDC,0BAA0B,EAAE;IACxBC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE;EACf,CAAC;EACDC,sBAAsB,EAAE;IACpBC,UAAU,EAAE;MACRC,IAAI,EAAE,MAAM;MACZC,EAAE,EAAE,IAAI;MACRC,OAAO,EAAE;IACb,CAAC;IACDC,gBAAgB,EAAE;MACdH,IAAI,EAAE,MAAM;MACZI,EAAE,EAAE,IAAI;MACRC,GAAG,EAAE;IACT,CAAC;IACDC,gBAAgB,EAAE;MACdC,gBAAgB,EAAE,gBAAgB;MAClCC,MAAM,EAAE,QAAQ;MAChBC,gBAAgB,EAAE;IACtB;EACJ,CAAC;EACDC,GAAG,EAAE,KAAK;EACVC,YAAY,EAAE,aAAa;EAC3BC,iBAAiB,EAAE,kBAAkB;EACrCC,eAAe,EAAE,iBAAiB;EAClCC,YAAY,EAAE,uBAAuB;EACrCC,aAAa,EAAE,uBAAuB;EACtCC,0BAA0B,EAAE;IACxBC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,iBAAiB,EAAE,CAAC;IACpBC,gBAAgB,EAAE,CAAC;IACnBC,gBAAgB,EAAE;EACtB,CAAC;EACDC,iBAAiB,EAAE;IACfC,KAAK,EAAE,cAAc;IACrBC,GAAG,EAAE;EACT,CAAC;EACDC,gBAAgB,EAAE;IACdC,OAAO,EAAE,yBAAyB;IAClCC,SAAS,EAAE;EACf,CAAC;EACDrW,4BAA4B,EAAE;IAC1BC,IAAI,EAAE,+BAA+B;IACrCqW,KAAK,EAAE,gCAAgC;IACvCC,eAAe,EAAE,yCAAyC;IAC1DC,kCAAkC,EAAE,yDAAyD;IAC7FC,6BAA6B,EAAE,qDAAqD;IACpFC,aAAa,EAAE,uCAAuC;IACtDC,gCAAgC,EAAE,uDAAuD;IACzFC,2BAA2B,EAAE;EACjC,CAAC;EACDzW,0CAA0C,EAAE;IACxCC,YAAY,EAAE,kDAAkD;IAChEyW,eAAe,EAAE,qDAAqD;IACtEC,IAAI,EAAE;EACV,CAAC;EACDC,WAAW,EAAE;IACTC,oBAAoB,EAAE,oBAAoB;IAC1CC,sBAAsB,EAAE;EAC5B,CAAC;EACDD,oBAAoB,EAAE;IAClBE,SAAS,EAAE,UAAU;IACrBC,eAAe,EAAE,gBAAgB;IACjCC,wBAAwB,EAAE,wBAAwB;IAClDC,mBAAmB,EAAE,mBAAmB;IACxCC,mBAAmB,EAAE,mBAAmB;IACxCC,mBAAmB,EAAE,SAAS;IAC9BC,aAAa,EAAE;EACnB,CAAC;EACDP,sBAAsB,EAAE;IACpBQ,oBAAoB,EAAE;EAC1B,CAAC;EAED;AACJ;AACA;AACA;AACA;EACIC,iBAAiB,EAAE,8BAA8B;EACjDC,0BAA0B,EAAE,qBAAqB;EACjDC,mBAAmB,EAAG,yBAAyB;EAC/ClO,cAAc,EAAE;IACZC,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,KAAK;IACVC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,KAAK;IACVC,IAAI,EAAE;EACV;AACJ,CAAC;;;;;;;;;;;;AChWD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACyD;;AAEzD;AACA;AACA;AACA;AACA,MAAM2N,sBAAsB,SAASzN,kEAAU,CAAC;EAC5CE,WAAWA,CAAA,EAAI;IACX,KAAK,CAAC,CAAC;IAEP,IAAI,CAACwN,+BAA+B,GAAG,sCAAsC;IAC7E,IAAI,CAACC,uBAAuB,GAAG,gCAAgC;;IAE/D;AACR;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,mBAAmB;EAClD;AACJ;AAEA,IAAIC,sBAAsB,GAAG,IAAIJ,sBAAsB,CAAC,CAAC;AACzD,+DAAeI,sBAAsB;;;;;;;;;;;;;;;ACpDrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEuD;AACG;AACQ;AACT;AAEzD,SAASG,2BAA2BA,CAAC9qB,MAAM,EAAE;EAEzCA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACrB,IAAIM,QAAQ;EACZ,IAAIyqB,kBAAkB,GAAG,CAAC,CAAC;EAC3B,IAAI9qB,OAAO,GAAG,IAAI,CAACA,OAAO;EAC1B,IAAIC,QAAQ,GAAGF,MAAM,CAACE,QAAQ;EAC9B,MAAM+J,MAAM,GAAGjK,MAAM,CAACiK,MAAM;EAE5B,SAASiM,MAAMA,CAACiB,CAAC,EAAE;IACf,IAAIA,CAAC,CAACpW,KAAK,EAAE;MACT;IACJ;;IAEA;IACA,IAAIiqB,mBAAmB,GAAGC,MAAM,CAACC,IAAI,CAACH,kBAAkB,CAAC;IAEzD,MAAMxgB,OAAO,GAAGsgB,qEAAe,CAAC5qB,OAAO,CAAC,CAACE,WAAW,CAAC;MACjDgrB,OAAO,EAAEnrB,MAAM,CAACmrB,OAAO;MACvBC,SAAS,EAAEprB,MAAM,CAACorB;IACtB,CAAC,CAAC,CAACC,UAAU,CAAClU,CAAC,CAACmU,QAAQ,CAAC;IAEzB/gB,OAAO,CAACtH,OAAO,CAACsoB,CAAC,IAAI;MACjB,MAAMxU,GAAG,GAAGnkB,IAAI,CAACC,SAAS,CAAC04B,CAAC,CAAC;MAE7B,IAAI,CAACR,kBAAkB,CAACvlB,cAAc,CAACuR,GAAG,CAAC,EAAE;QACzC,IAAI;UACA,IAAIyU,UAAU,GAAGZ,iEAAiB,CAAC3qB,OAAO,CAAC,CAAC+G,MAAM,CAAChH,MAAM,CAAC;UAC1DwrB,UAAU,CAACC,UAAU,CAACF,CAAC,CAAC;UACxBR,kBAAkB,CAAChU,GAAG,CAAC,GAAGyU,UAAU;QACxC,CAAC,CAAC,OAAOrU,CAAC,EAAE;UACR;QAAA;MAER,CAAC,MAAM;QACH;QACA6T,mBAAmB,CAAC/lB,MAAM,CAAC8R,GAAG,EAAE,CAAC,CAAC;MACtC;IACJ,CAAC,CAAC;;IAEF;IACAiU,mBAAmB,CAAC/nB,OAAO,CAAC8V,CAAC,IAAI;MAC7BgS,kBAAkB,CAAChS,CAAC,CAAC,CAAClT,KAAK,CAAC,CAAC;MAC7B,OAAOklB,kBAAkB,CAAChS,CAAC,CAAC;IAChC,CAAC,CAAC;IAEF7Y,QAAQ,CAACmD,OAAO,CAACknB,kEAAsB,CAACC,+BAA+B,CAAC;EAC5E;EAEA,SAASkB,uBAAuBA,CAAA,EAAG;IAC/BT,MAAM,CAACC,IAAI,CAACH,kBAAkB,CAAC,CAAC9nB,OAAO,CAAC8T,GAAG,IAAI;MAC3CgU,kBAAkB,CAAChU,GAAG,CAAC,CAAClR,KAAK,CAAC,CAAC;IACnC,CAAC,CAAC;IAEFklB,kBAAkB,GAAG,CAAC,CAAC;EAC3B;EAEA,SAASrqB,KAAKA,CAAA,EAAG;IACbR,QAAQ,CAACiF,EAAE,CAAC8E,MAAM,CAACmU,gBAAgB,EAAElI,MAAM,EAAE5V,QAAQ,CAAC;IACtDJ,QAAQ,CAACiF,EAAE,CAAC8E,MAAM,CAACiY,wBAAwB,EAAEwJ,uBAAuB,EAAEprB,QAAQ,CAAC;EACnF;EAEA,SAASuF,KAAKA,CAAA,EAAG;IACb3F,QAAQ,CAACmF,GAAG,CAAC4E,MAAM,CAACmU,gBAAgB,EAAElI,MAAM,EAAE5V,QAAQ,CAAC;IACvDJ,QAAQ,CAACmF,GAAG,CAAC4E,MAAM,CAACiY,wBAAwB,EAAEwJ,uBAAuB,EAAEprB,QAAQ,CAAC;EACpF;EAEAA,QAAQ,GAAG;IACPuF,KAAK,EAAEA;EACX,CAAC;EAEDnF,KAAK,CAAC,CAAC;EACP,OAAOJ,QAAQ;AACnB;AAEAwqB,2BAA2B,CAACvnB,qBAAqB,GAAG,6BAA6B;AACjF,+DAAe/D,6DAAY,CAACsH,eAAe,CAACgkB,2BAA2B,CAAC;;;;;;;;;;;;;;;AC/GxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEmD;AACQ;AACY;AACd;AAEzD,SAASF,iBAAiBA,CAAC5qB,MAAM,EAAE;EAE/BA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACrB,IAAI8rB,yBAAyB,EACzBC,mBAAmB,EACnBC,eAAe,EACf1rB,QAAQ;EAEZ,IAAIL,OAAO,GAAG,IAAI,CAACA,OAAO;EAE1B,SAASwrB,UAAUA,CAACQ,YAAY,EAAE;IAC9B,IAAI;MACAD,eAAe,GAAGL,+DAAe,CAAC1rB,OAAO,CAAC,CAAC+G,MAAM,CAAC;QAC9CklB,YAAY,EAAElsB,MAAM,CAACksB;MACzB,CAAC,CAAC;MAEFF,eAAe,CAACP,UAAU,CAACQ,YAAY,CAACE,KAAK,CAAC;MAE9CJ,mBAAmB,GAAGH,mEAAmB,CAAC3rB,OAAO,CAAC,CAAC+G,MAAM,CAAC;QACtD9F,KAAK,EAAElB,MAAM,CAACkB,KAAK;QACnBkrB,gBAAgB,EAAEpsB,MAAM,CAACosB,gBAAgB;QACzCC,gBAAgB,EAAErsB,MAAM,CAACqsB;MAC7B,CAAC,CAAC;MAEFN,mBAAmB,CAACN,UAAU,CAACQ,YAAY,CAACK,SAAS,EAAEN,eAAe,CAAC;MAEvEF,yBAAyB,GAAGD,yEAAyB,CAAC5rB,OAAO,CAAC,CAAC+G,MAAM,CAAC;QAClE9F,KAAK,EAAElB,MAAM,CAACkB,KAAK;QACnBhB,QAAQ,EAAEF,MAAM,CAACE,QAAQ;QACzBksB,gBAAgB,EAAEpsB,MAAM,CAACosB,gBAAgB;QACzCniB,MAAM,EAAEjK,MAAM,CAACiK;MACnB,CAAC,CAAC;MAEF6hB,yBAAyB,CAACL,UAAU,CAACQ,YAAY,CAAC1hB,OAAO,EAAEwhB,mBAAmB,CAAC;IACnF,CAAC,CAAC,OAAO5U,CAAC,EAAE;MACRtR,KAAK,CAAC,CAAC;MACP,MAAMsR,CAAC;IACX;EACJ;EAEA,SAAStR,KAAKA,CAAA,EAAG;IACb,IAAIimB,yBAAyB,EAAE;MAC3BA,yBAAyB,CAACjmB,KAAK,CAAC,CAAC;IACrC;IAEA,IAAIkmB,mBAAmB,EAAE;MACrBA,mBAAmB,CAAClmB,KAAK,CAAC,CAAC;IAC/B;IAEA,IAAImmB,eAAe,EAAE;MACjBA,eAAe,CAACnmB,KAAK,CAAC,CAAC;IAC3B;EACJ;EAEAvF,QAAQ,GAAG;IACPmrB,UAAU,EAAEA,UAAU;IACtB5lB,KAAK,EAAOA;EAChB,CAAC;EAED,OAAOvF,QAAQ;AACnB;AAEAsqB,iBAAiB,CAACrnB,qBAAqB,GAAG,mBAAmB;AAC7D,+DAAe/D,6DAAY,CAACsH,eAAe,CAAC8jB,iBAAiB,CAAC;;;;;;;;;;;;;ACnG9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEwE;AACf;AAEzD,SAASiB,yBAAyBA,CAAC7rB,MAAM,EAAE;EAEvCA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACrB,IAAI8D,QAAQ,GAAG,EAAE;EAEjB,IAAIxD,QAAQ;EACZ,MAAML,OAAO,GAAG,IAAI,CAACA,OAAO;EAC5B,MAAMC,QAAQ,GAAGF,MAAM,CAACE,QAAQ;EAChC,MAAMX,MAAM,GAAGS,MAAM,CAACiK,MAAM;EAE5B,IAAIuiB,qBAAqB,GAAGD,6EAAqB,CAACtsB,OAAO,CAAC,CAACE,WAAW,CAAC;IACnEe,KAAK,EAAElB,MAAM,CAACkB,KAAK;IACnBhB,QAAQ,EAAEF,MAAM,CAACE,QAAQ;IACzBksB,gBAAgB,EAAEpsB,MAAM,CAACosB;EAC7B,CAAC,CAAC;EAEF,SAASK,MAAMA,CAACtV,CAAC,EAAE;IACfrT,QAAQ,CAACb,OAAO,CAACuB,OAAO,IAAI;MACxBA,OAAO,CAACkoB,eAAe,CAACvV,CAAC,CAACwV,MAAM,EAAExV,CAAC,CAAC1V,KAAK,EAAE0V,CAAC,CAACvS,SAAS,CAAC;IAC3D,CAAC,CAAC;EACN;EAEA,SAAS6mB,UAAUA,CAAClhB,OAAO,EAAEwhB,mBAAmB,EAAE;IAC9CxhB,OAAO,CAACtP,KAAK,CAAC,GAAG,CAAC,CAACgI,OAAO,CACtB,CAACsoB,CAAC,EAAEqB,IAAI,EAAEC,EAAE,KAAK;MACb,IAAIroB,OAAO;;MAEX;MACA;MACA;MACA,IAAK+mB,CAAC,CAAC5wB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAK4wB,CAAC,CAAC5wB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QAClD,IAAImyB,KAAK,GAAGD,EAAE,CAACD,IAAI,GAAG,CAAC,CAAC;QAExB,IAAIE,KAAK,IACAA,KAAK,CAACnyB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAE,IAC1BmyB,KAAK,CAACnyB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAE,EAAE;UACjC4wB,CAAC,IAAI,GAAG,GAAGuB,KAAK;;UAEhB;UACA,OAAOD,EAAE,CAACD,IAAI,GAAG,CAAC,CAAC;QACvB;MACJ;MAEApoB,OAAO,GAAGgoB,qBAAqB,CAACxlB,MAAM,CAClCukB,CAAC,EACDQ,mBACJ,CAAC;MAED,IAAIvnB,OAAO,EAAE;QACTV,QAAQ,CAACoB,IAAI,CAACV,OAAO,CAAC;MAC1B;IACJ,CACJ,CAAC;IAEDtE,QAAQ,CAACiF,EAAE,CACP5F,MAAM,CAAC8hB,YAAY,EACnBoL,MAAM,EACNnsB,QACJ,CAAC;IAEDJ,QAAQ,CAACiF,EAAE,CACP5F,MAAM,CAAC+hB,cAAc,EACrBmL,MAAM,EACNnsB,QACJ,CAAC;EACL;EAEA,SAASuF,KAAKA,CAAA,EAAG;IACb3F,QAAQ,CAACmF,GAAG,CACR9F,MAAM,CAAC8hB,YAAY,EACnBoL,MAAM,EACNnsB,QACJ,CAAC;IAEDJ,QAAQ,CAACmF,GAAG,CACR9F,MAAM,CAAC+hB,cAAc,EACrBmL,MAAM,EACNnsB,QACJ,CAAC;IAEDwD,QAAQ,CAACb,OAAO,CAACuB,OAAO,IAAIA,OAAO,CAACqB,KAAK,CAAC,CAAC,CAAC;IAE5C/B,QAAQ,GAAG,EAAE;EACjB;EAEAxD,QAAQ,GAAG;IACPmrB,UAAU,EAAEA,UAAU;IACtB5lB,KAAK,EAAOA;EAChB,CAAC;EAED,OAAOvF,QAAQ;AACnB;AAEAurB,yBAAyB,CAACtoB,qBAAqB,GAAG,2BAA2B;AAC7E,+DAAe/D,6DAAY,CAACsH,eAAe,CAAC+kB,yBAAyB,CAAC;;;;;;;;;;;;;AChItE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAE+D;AACN;AAEzD,SAASF,eAAeA,CAAC3rB,MAAM,EAAE;EAE7BA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACrB,IAAIgtB,gBAAgB,GAAG,KAAK;EAC5B,IAAI/sB,OAAO,GAAG,IAAI,CAACA,OAAO;EAC1B,IAAIK,QAAQ,EACR2sB,MAAM;EAEV,IAAIf,YAAY,GAAGlsB,MAAM,CAACksB,YAAY;EAEtC,SAAST,UAAUA,CAACyB,EAAE,EAAE;IACpB,IAAIA,EAAE,IAAIA,EAAE,CAAC55B,MAAM,EAAE;MACjB45B,EAAE,CAACjqB,OAAO,CAACwT,CAAC,IAAI;QACZ,IAAIrgB,KAAK,GAAGqgB,CAAC,CAAC0W,SAAS;QACvB,IAAIl3B,GAAG,GAAGG,KAAK,GAAGqgB,CAAC,CAAC2W,QAAQ;QAE5BH,MAAM,CAACI,GAAG,CAACj3B,KAAK,EAAEH,GAAG,CAAC;MAC1B,CAAC,CAAC;MAEF+2B,gBAAgB,GAAG,CAAC,CAACE,EAAE,CAAC,CAAC,CAAC,CAACI,iBAAiB;IAChD;EACJ;EAEA,SAASznB,KAAKA,CAAA,EAAG;IACbonB,MAAM,CAACM,KAAK,CAAC,CAAC;EAClB;EAEA,SAAS7sB,KAAKA,CAAA,EAAG;IACbusB,MAAM,GAAGF,sEAAgB,CAAC9sB,OAAO,CAAC,CAAC+G,MAAM,CAAC,CAAC;EAC/C;EAEA,SAASwmB,SAASA,CAAA,EAAG;IACjB,IAAIC,SAAS,GAAGR,MAAM,CAAC35B,MAAM;IAC7B,IAAIo6B,IAAI;IAER,IAAI,CAACD,SAAS,EAAE;MACZ,OAAO,IAAI;IACf;;IAEA;IACA;IACAC,IAAI,GAAGV,gBAAgB,GAClB,IAAIrsB,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAI,GAC5BsrB,YAAY,CAACyB,WAAW;IAE5B,KAAK,IAAIt6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGo6B,SAAS,EAAEp6B,CAAC,IAAI,CAAC,EAAE;MACnC,IAAI+C,KAAK,GAAG62B,MAAM,CAAC72B,KAAK,CAAC/C,CAAC,CAAC;MAC3B,IAAI4C,GAAG,GAAGg3B,MAAM,CAACh3B,GAAG,CAAC5C,CAAC,CAAC;MAEvB,IAAK+C,KAAK,IAAIs3B,IAAI,IAAMA,IAAI,GAAGz3B,GAAI,EAAE;QACjC,OAAO,IAAI;MACf;IACJ;IAEA,OAAO,KAAK;EAChB;EAEAqK,QAAQ,GAAG;IACPmrB,UAAU,EAAEA,UAAU;IACtB5lB,KAAK,EAAEA,KAAK;IACZ2nB,SAAS,EAAEA;EACf,CAAC;EAED9sB,KAAK,CAAC,CAAC;EAEP,OAAOJ,QAAQ;AACnB;AAEAqrB,eAAe,CAACpoB,qBAAqB,GAAG,iBAAiB;AACzD,+DAAe/D,6DAAY,CAACsH,eAAe,CAAC6kB,eAAe,CAAC;;;;;;;;;;;;;ACvG5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEgE;AACP;AAEzD,SAASC,mBAAmBA,CAAC5rB,MAAM,EAAE;EAEjC,IAAI6tB,SAAS,GAAG,EAAE;EAClB,IAAIvtB,QAAQ;EAEZ,MAAMwtB,gBAAgB,GAAGF,0EAAgB,CAAC,IAAI,CAAC3tB,OAAO,CAAC,CAACE,WAAW,CAACH,MAAM,CAAC;EAE3E,SAASyrB,UAAUA,CAACsC,SAAS,EAAE/B,eAAe,EAAE;IAC5C;IACA;IACA;IACA;IACA+B,SAAS,CAAChpB,IAAI,CAAC0R,CAAC,IAAI;MAChB,IAAIuX,QAAQ,GAAGF,gBAAgB,CAAC9mB,MAAM,CAACyP,CAAC,EAAEuV,eAAe,CAAC;MAE1D,IAAIgC,QAAQ,EAAE;QACVH,SAAS,CAAC3oB,IAAI,CAAC8oB,QAAQ,CAAC;QACxB,OAAO,IAAI;MACf;IACJ,CAAC,CAAC;EACN;EAEA,SAASnoB,KAAKA,CAAA,EAAG;IACbgoB,SAAS,CAAC5qB,OAAO,CAACwT,CAAC,IAAIA,CAAC,CAAC5Q,KAAK,CAAC,CAAC,CAAC;IACjCgoB,SAAS,GAAG,EAAE;EAClB;EAEA,SAASI,MAAMA,CAACjqB,IAAI,EAAEkqB,GAAG,EAAE;IACvBL,SAAS,CAAC5qB,OAAO,CAACwT,CAAC,IAAIA,CAAC,CAACwX,MAAM,CAACjqB,IAAI,EAAEkqB,GAAG,CAAC,CAAC;EAC/C;EAEA5tB,QAAQ,GAAG;IACPmrB,UAAU,EAAEA,UAAU;IACtB5lB,KAAK,EAAOA,KAAK;IACjBooB,MAAM,EAAMA;EAChB,CAAC;EAED,OAAO3tB,QAAQ;AACnB;AAEAsrB,mBAAmB,CAACroB,qBAAqB,GAAG,qBAAqB;AACjE,+DAAe/D,6DAAY,CAACsH,eAAe,CAAC8kB,mBAAmB,CAAC;;;;;;;;;;;;;;;;AC3EhE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAE2D;AACJ;AACF;AACiB;AACb;AAEzD,SAASW,qBAAqBA,CAACvsB,MAAM,EAAE;EAEnCA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACrB,IAAIM,QAAQ;EACZ,MAAMiuB,MAAM,GAAGvuB,MAAM,CAACkB,KAAK,GAAGlB,MAAM,CAACkB,KAAK,CAACI,SAAS,CAAChB,QAAQ,CAAC,GAAG,CAAC,CAAC;;EAEnE;EACA,IAAIkuB,QAAQ,GAAG,+CAA+C;EAE9D,MAAMvuB,OAAO,GAAG,IAAI,CAACA,OAAO;EAC5B,IAAIwuB,oBAAoB,GAAG;IACvBN,WAAW,EAAKA,uEAAW;IAC3BC,SAAS,EAAOA,qEAAS;IACzBC,QAAQ,EAAQA,oEAAQ;IACxBK,QAAQ,EAAQJ,yEAAoB;IACpCK,aAAa,EAAGL,yEAAoB;IACpCM,OAAO,EAASN,yEAAoBA;EACxC,CAAC;EAED,SAAStnB,MAAMA,CAAC6nB,QAAQ,EAAE9C,mBAAmB,EAAE;IAC3C,IAAInwB,OAAO,GAAGizB,QAAQ,CAAChzB,KAAK,CAAC2yB,QAAQ,CAAC;IACtC,IAAIhqB,OAAO;IAEX,IAAI,CAAC5I,OAAO,EAAE;MACV;IACJ;IAEA,IAAI;MACA4I,OAAO,GAAGiqB,oBAAoB,CAAC7yB,OAAO,CAAC,CAAC,CAAC,CAAC,CAACqE,OAAO,CAAC,CAAC+G,MAAM,CAAC;QACvD9G,QAAQ,EAAEF,MAAM,CAACE,QAAQ;QACzBksB,gBAAgB,EAAEpsB,MAAM,CAACosB;MAC7B,CAAC,CAAC;MAEF5nB,OAAO,CAACinB,UAAU,CACd7vB,OAAO,CAAC,CAAC,CAAC,EACVmwB,mBAAmB,EACnBnwB,OAAO,CAAC,CAAC,CAAC,EACVA,OAAO,CAAC,CAAC,CACb,CAAC;IACL,CAAC,CAAC,OAAOub,CAAC,EAAE;MACR3S,OAAO,GAAG,IAAI;MACd+pB,MAAM,CAACxtB,KAAK,CAAC,4DAA4DnF,OAAO,CAAC,CAAC,CAAC,cAAcA,OAAO,CAAC,CAAC,CAAC,KAAKA,OAAO,CAAC,CAAC,CAAC,KAAKub,CAAC,CAACzU,OAAO,GAAG,CAAC;IAChJ;IAEA,OAAO8B,OAAO;EAClB;EAEA,SAASsqB,QAAQA,CAAC/X,GAAG,EAAEvS,OAAO,EAAE;IAC5BiqB,oBAAoB,CAAC1X,GAAG,CAAC,GAAGvS,OAAO;EACvC;EAEA,SAASuqB,UAAUA,CAAChY,GAAG,EAAE;IACrB,OAAO0X,oBAAoB,CAAC1X,GAAG,CAAC;EACpC;EAEAzW,QAAQ,GAAG;IACP0G,MAAM,EAAMA,MAAM;IAClB8nB,QAAQ,EAAIA,QAAQ;IACpBC,UAAU,EAAEA;EAChB,CAAC;EAED,OAAOzuB,QAAQ;AACnB;AAEAisB,qBAAqB,CAAChpB,qBAAqB,GAAG,uBAAuB;AACrE,+DAAe/D,6DAAY,CAACiE,mBAAmB,CAAC8oB,qBAAqB,CAAC;;;;;;;;;;;;;ACtGtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAE2D;AACC;AAE5D,SAAS0C,kBAAkBA,CAACjvB,MAAM,EAAE;EAEhCA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACrB,IAAIM,QAAQ,EACRyrB,mBAAmB,EACnB/V,CAAC,EACD/hB,IAAI,EACJi7B,QAAQ,EACRC,gBAAgB;EAEpB,IAAIlvB,OAAO,GAAG,IAAI,CAACA,OAAO;EAC1B,IAAImvB,cAAc,GAAGJ,oEAAc,CAAC/uB,OAAO,CAAC,CAACE,WAAW,CAAC,CAAC;EAE1D,IAAIkvB,SAAS,GAAG,EAAE;EAElB,MAAMjD,gBAAgB,GAAGpsB,MAAM,CAACosB,gBAAgB;EAEhD,SAASkD,sBAAsBA,CAAA,EAAG;IAC9B,IAAI;MACA,OAAOrE,MAAM,CAACC,IAAI,CAACmE,SAAS,CAAC,CAACjzB,GAAG,CAC7B2a,GAAG,IAAIsY,SAAS,CAACtY,GAAG,CACxB,CAAC,CAACwY,MAAM,CACJ,CAACC,CAAC,EAAEC,CAAC,KAAK;QACN,OAAQD,CAAC,CAAChtB,KAAK,GAAGitB,CAAC,CAACjtB,KAAK,GAAIgtB,CAAC,GAAGC,CAAC;MACtC,CACJ,CAAC;IACL,CAAC,CAAC,OAAOtY,CAAC,EAAE;MACR;IACJ;EACJ;EAEA,SAASuY,gBAAgBA,CAAA,EAAG;IACxB,IAAIC,EAAE,GAAGL,sBAAsB,CAAC,CAAC;IAEjC,IAAIK,EAAE,EAAE;MACJ,IAAIR,gBAAgB,KAAKQ,EAAE,CAACC,CAAC,EAAE;QAC3BT,gBAAgB,GAAGQ,EAAE,CAACC,CAAC;QACvB7D,mBAAmB,CAACkC,MAAM,CAACh6B,IAAI,EAAE07B,EAAE,CAAC;MACxC;IACJ;EACJ;EAEA,SAASlE,UAAUA,CAACt1B,QAAQ,EAAE05B,EAAE,EAAEC,IAAI,EAAE;IACpC,IAAID,EAAE,EAAE;MACJ;MACA;MACA7Z,CAAC,GAAGoZ,cAAc,CAACW,SAAS,CAACD,IAAI,CAAC;MAClC/D,mBAAmB,GAAG8D,EAAE;MACxB57B,IAAI,GAAGm7B,cAAc,CAACY,yBAAyB,CAAC75B,QAAQ,EAAE25B,IAAI,CAAC;MAC/DZ,QAAQ,GAAGe,WAAW,CAACP,gBAAgB,EAAE1Z,CAAC,CAAC;IAC/C;EACJ;EAEA,SAASnQ,KAAKA,CAAA,EAAG;IACbqqB,aAAa,CAAChB,QAAQ,CAAC;IACvBA,QAAQ,GAAG,IAAI;IACflZ,CAAC,GAAG,CAAC;IACL+V,mBAAmB,GAAG,IAAI;IAC1BoD,gBAAgB,GAAG,IAAI;EAC3B;EAEA,SAASzC,eAAeA,CAACC,MAAM,EAAEgD,EAAE,EAAE3rB,IAAI,EAAE;IACvC,IAAI2oB,MAAM,KAAKP,gBAAgB,CAAC+D,YAAY,EAAE;MAC1Cd,SAAS,CAACrrB,IAAI,CAAC,GAAG2rB,EAAE;IACxB;EACJ;EAEArvB,QAAQ,GAAG;IACPmrB,UAAU,EAAUA,UAAU;IAC9B5lB,KAAK,EAAeA,KAAK;IACzB6mB,eAAe,EAAKA;EACxB,CAAC;EAED,OAAOpsB,QAAQ;AACnB;AAEA2uB,kBAAkB,CAAC1rB,qBAAqB,GAAG,oBAAoB;AAC/D,+DAAe/D,6DAAY,CAACsH,eAAe,CAACmoB,kBAAkB,CAAC;;;;;;;;;;;;;AC/G/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEqE;AACT;AAE5D,SAASmB,gBAAgBA,CAACpwB,MAAM,EAAE;EAE9BA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACrB,IAAIM,QAAQ,EACRyrB,mBAAmB;EAEvB,IAAI7rB,QAAQ,GAAGF,MAAM,CAACE,QAAQ;EAC9B,MAAMksB,gBAAgB,GAAGpsB,MAAM,CAACosB,gBAAgB;EAEhD,SAASiE,wBAAwBA,CAAA,EAAG;IAChC;IACAnwB,QAAQ,CAACmF,GAAG,CACRklB,kEAAsB,CAACC,+BAA+B,EACtD6F,wBAAwB,EACxB,IACJ,CAAC;;IAED;IACA;IACAnwB,QAAQ,CAACmD,OAAO,CAACknB,kEAAsB,CAACE,uBAAuB,CAAC;EACpE;EAEA,SAASgB,UAAUA,CAAC6E,MAAM,EAAET,EAAE,EAAE;IAC5B,IAAIA,EAAE,EAAE;MACJ9D,mBAAmB,GAAG8D,EAAE;MAExB3vB,QAAQ,CAACiF,EAAE,CACPolB,kEAAsB,CAACC,+BAA+B,EACtD6F,wBAAwB,EACxB,IACJ,CAAC;IACL;EACJ;EAEA,SAASxqB,KAAKA,CAAA,EAAG;IACbkmB,mBAAmB,GAAG,IAAI;EAC9B;EAEA,SAASW,eAAeA,CAACC,MAAM,EAAEgD,EAAE,EAAE;IACjC;IACA,IAAIhD,MAAM,KAAKP,gBAAgB,CAACmE,UAAU,EAAE;MACxC,IAAIxE,mBAAmB,EAAE;QACrBA,mBAAmB,CAACkC,MAAM,CAACtB,MAAM,EAAEgD,EAAE,CAAC;MAC1C;IACJ;EACJ;EAEArvB,QAAQ,GAAG;IACPmrB,UAAU,EAAUA,UAAU;IAC9B5lB,KAAK,EAAeA,KAAK;IACzB6mB,eAAe,EAAKA;EACxB,CAAC;EAED,OAAOpsB,QAAQ;AACnB;AAEA,+DAAed,6DAAY,CAACsH,eAAe,CAACspB,gBAAgB,CAAC;;;;;;;;;;;;AC1F7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAE4D;;AAE5D;AACA;AACA;AACA,SAAS9B,oBAAoBA,CAAA,EAAG;EAE5B,IAAIhuB,QAAQ,EACRkwB,UAAU,EACVzE,mBAAmB;EAEvB,SAASN,UAAUA,CAACx3B,IAAI,EAAE47B,EAAE,EAAE;IAC1BW,UAAU,GAAGv8B,IAAI;IACjB83B,mBAAmB,GAAG8D,EAAE;EAC5B;EAEA,SAAShqB,KAAKA,CAAA,EAAG;IACbkmB,mBAAmB,GAAG,IAAI;IAC1ByE,UAAU,GAAG/7B,SAAS;EAC1B;EAEA,SAASi4B,eAAeA,CAACC,MAAM,EAAEgD,EAAE,EAAE;IACjC;IACA,IAAIhD,MAAM,KAAK6D,UAAU,EAAE;MACvB,IAAIzE,mBAAmB,EAAE;QACrBA,mBAAmB,CAACkC,MAAM,CAACuC,UAAU,EAAEb,EAAE,CAAC;MAC9C;IACJ;EACJ;EAEArvB,QAAQ,GAAG;IACPmrB,UAAU,EAAEA,UAAU;IACtB5lB,KAAK,EAAEA,KAAK;IACZ6mB,eAAe,EAAEA;EACrB,CAAC;EAED,OAAOpsB,QAAQ;AACnB;AAEAguB,oBAAoB,CAAC/qB,qBAAqB,GAAG,sBAAsB;AACnE,+DAAe/D,6DAAY,CAACsH,eAAe,CAACwnB,oBAAoB,CAAC;;;;;;;;;;;;;ACvEjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAE2D;AACC;AAE5D,SAASmC,eAAeA,CAACzwB,MAAM,EAAE;EAE7BA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACrB,IAAIM,QAAQ,EACRyrB,mBAAmB,EACnB/V,CAAC,EACDhS,IAAI,EACJ/P,IAAI,EACJi7B,QAAQ;EAEZ,IAAIwB,SAAS,GAAG,EAAE;EAElB,IAAItB,cAAc,GAAGJ,oEAAc,CAAC,IAAI,CAAC/uB,OAAO,CAAC,CAACE,WAAW,CAAC,CAAC;EAE/D,MAAMisB,gBAAgB,GAAGpsB,MAAM,CAACosB,gBAAgB;EAEhD,SAASsD,gBAAgBA,CAAA,EAAG;IACxB,IAAIxB,GAAG,GAAGwC,SAAS;IAEnB,IAAIxC,GAAG,CAAC56B,MAAM,EAAE;MACZ,IAAIy4B,mBAAmB,EAAE;QACrBA,mBAAmB,CAACkC,MAAM,CAACh6B,IAAI,EAAEi6B,GAAG,CAAC;MACzC;IACJ;IAEAwC,SAAS,GAAG,EAAE;EAClB;EAEA,SAASjF,UAAUA,CAACt1B,QAAQ,EAAE05B,EAAE,EAAEC,IAAI,EAAEa,WAAW,EAAE;IACjD,IAAId,EAAE,EAAE;MAEJ;MACA;MACA7Z,CAAC,GAAGoZ,cAAc,CAACW,SAAS,CAACD,IAAI,CAAC;MAElC/D,mBAAmB,GAAG8D,EAAE;MAExB,IAAIc,WAAW,IAAIA,WAAW,CAACr9B,MAAM,EAAE;QACnC0Q,IAAI,GAAG2sB,WAAW;MACtB;MAEA18B,IAAI,GAAGm7B,cAAc,CAACY,yBAAyB,CAC3C75B,QAAQ,EACR25B,IAAI,EACJa,WACJ,CAAC;MAEDzB,QAAQ,GAAGe,WAAW,CAACP,gBAAgB,EAAE1Z,CAAC,CAAC;IAC/C;EACJ;EAEA,SAASnQ,KAAKA,CAAA,EAAG;IACbqqB,aAAa,CAAChB,QAAQ,CAAC;IACvBA,QAAQ,GAAG,IAAI;IACflZ,CAAC,GAAG,IAAI;IACRhS,IAAI,GAAG,IAAI;IACX0sB,SAAS,GAAG,EAAE;IACd3E,mBAAmB,GAAG,IAAI;EAC9B;EAEA,SAASW,eAAeA,CAACC,MAAM,EAAEgD,EAAE,EAAE;IACjC,IAAIhD,MAAM,KAAKP,gBAAgB,CAACwE,YAAY,EAAE;MAC1C,IAAI,CAAC5sB,IAAI,IAAKA,IAAI,KAAK2rB,EAAE,CAAC3rB,IAAK,EAAE;QAC7B0sB,SAAS,CAACxrB,IAAI,CAACyqB,EAAE,CAAC;MACtB;IACJ;EACJ;EAEArvB,QAAQ,GAAG;IACPmrB,UAAU,EAAUA,UAAU;IAC9B5lB,KAAK,EAAeA,KAAK;IACzB6mB,eAAe,EAAKA;EACxB,CAAC;EAED,OAAOpsB,QAAQ;AACnB;AAEAmwB,eAAe,CAACltB,qBAAqB,GAAG,iBAAiB;AACzD,+DAAe/D,6DAAY,CAACsH,eAAe,CAAC2pB,eAAe,CAAC;;;;;;;;;;;;;AChH5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEuD;AACE;AAEzD,SAAS7C,gBAAgBA,CAAC5tB,MAAM,EAAE;EAC9BA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EAErB,MAAM8wB,0BAA0B,GAAG;IAC/B,6BAA6B,EAAED,kEAAYA;EAC/C,CAAC;EAED,MAAM5wB,OAAO,GAAG,IAAI,CAACA,OAAO;EAC5B,IAAIK,QAAQ;EACZ,MAAMiuB,MAAM,GAAGvuB,MAAM,CAACkB,KAAK,GAAGlB,MAAM,CAACkB,KAAK,CAACI,SAAS,CAAChB,QAAQ,CAAC,GAAG,CAAC,CAAC;EACnE,MAAM8rB,gBAAgB,GAAGpsB,MAAM,CAACosB,gBAAgB;EAChD,MAAMC,gBAAgB,GAAGrsB,MAAM,CAACqsB,gBAAgB,IAAI,CAAC,CAAC;EAEtD,SAASrlB,MAAMA,CAAC+pB,KAAK,EAAE/E,eAAe,EAAE;IACpC,IAAI+B,SAAS;IAEb,IAAI;MACAA,SAAS,GAAG+C,0BAA0B,CAACC,KAAK,CAAC1nB,WAAW,CAAC,CAACpJ,OAAO,CAAC,CAAC+G,MAAM,CAAC;QACtEolB,gBAAgB,EAAEA,gBAAgB;QAClCC,gBAAgB,EAAEA;MACtB,CAAC,CAAC;MAEF0B,SAAS,CAACtC,UAAU,CAACsF,KAAK,EAAE/E,eAAe,CAAC;IAChD,CAAC,CAAC,OAAO7U,CAAC,EAAE;MACR4W,SAAS,GAAG,IAAI;MAChBQ,MAAM,CAACxtB,KAAK,CAAC,iEAAiEgwB,KAAK,CAAC1nB,WAAW,KAAK8N,CAAC,CAACzU,OAAO,GAAG,CAAC;IACrH;IAEA,OAAOqrB,SAAS;EACpB;EAEA,SAASe,QAAQA,CAACzlB,WAAW,EAAE2nB,UAAU,EAAE;IACvCF,0BAA0B,CAACznB,WAAW,CAAC,GAAG2nB,UAAU;EACxD;EAEA,SAASjC,UAAUA,CAAC1lB,WAAW,EAAE;IAC7B,OAAOynB,0BAA0B,CAACznB,WAAW,CAAC;EAClD;EAEA/I,QAAQ,GAAG;IACP0G,MAAM,EAAMA,MAAM;IAClB8nB,QAAQ,EAAIA,QAAQ;IACpBC,UAAU,EAAEA;EAChB,CAAC;EAED,OAAOzuB,QAAQ;AACnB;AAEAstB,gBAAgB,CAACrqB,qBAAqB,GAAG,kBAAkB;AAC3D,+DAAe/D,6DAAY,CAACiE,mBAAmB,CAACmqB,gBAAgB,CAAC;;;;;;;;;;;;;;;ACnFjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAE+D;AAC1B;AACwC;AACjB;AAE5D,SAASiD,YAAYA,CAAC7wB,MAAM,EAAE;EAC1BA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACrB,IAAIM,QAAQ;EAEZ,IAAIL,OAAO,GAAG,IAAI,CAACA,OAAO;EAC1B,IAAImxB,gBAAgB,EAChBC,qBAAqB,EACrBC,qBAAqB,EACrBC,4BAA4B,EAC5BC,iBAAiB,EACjBC,YAAY,EACZzF,eAAe;EAEnB,IAAI0F,kBAAkB,GAAG,IAAI;EAC7B,IAAIC,qCAAqC,GAAG,IAAI;EAChD,IAAIC,eAAe,GAAG,EAAE;EAExB,MAAMxF,gBAAgB,GAAGpsB,MAAM,CAACosB,gBAAgB;EAEhD,SAAS1rB,KAAKA,CAAA,EAAG;IACb0wB,gBAAgB,GAAGH,sEAAgB,CAAChxB,OAAO,CAAC,CAACE,WAAW,CAAC,CAAC;IAC1DmxB,qBAAqB,GAAGJ,yDAAG,CAACjxB,OAAO,CAAC,CAACE,WAAW,CAAC,CAAC;IAClDkxB,qBAAqB,GAAGF,4EAAqB,CAAClxB,OAAO,CAAC,CAACE,WAAW,CAAC,CAAC;IAEpE0xB,oBAAoB,CAAC,CAAC;EAC1B;EAEA,SAASC,YAAYA,CAAClb,GAAG,EAAEmb,SAAS,EAAEC,SAAS,EAAE;IAC7C,IAAIC,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;IAC9BD,GAAG,CAACE,eAAe,GAAGd,qBAAqB,CAACe,4BAA4B,CAAChG,gBAAgB,CAACiG,+BAA+B,CAAC;IAC1H,MAAMC,UAAU,GAAG,SAAAA,CAAA,EAAY;MAC3B,IAAIC,QAAQ,GAAGX,eAAe,CAACj3B,OAAO,CAACs3B,GAAG,CAAC;MAE3C,IAAIM,QAAQ,KAAK,CAAC,CAAC,EAAE;QACjB;MACJ,CAAC,MAAM;QACHX,eAAe,CAAC3sB,MAAM,CAACstB,QAAQ,EAAE,CAAC,CAAC;MACvC;MAEA,IAAKN,GAAG,CAACO,MAAM,IAAI,GAAG,IAAMP,GAAG,CAACO,MAAM,GAAG,GAAI,EAAE;QAC3C,IAAIT,SAAS,EAAE;UACXA,SAAS,CAAC,CAAC;QACf;MACJ,CAAC,MAAM;QACH,IAAIC,SAAS,EAAE;UACXA,SAAS,CAAC,CAAC;QACf;MACJ;IACJ,CAAC;IAEDJ,eAAe,CAAC1sB,IAAI,CAAC+sB,GAAG,CAAC;IAEzB,IAAI;MACAA,GAAG,CAACQ,IAAI,CAAC,KAAK,EAAE7b,GAAG,CAAC;MACpBqb,GAAG,CAACS,SAAS,GAAGJ,UAAU;MAC1BL,GAAG,CAACU,OAAO,GAAGL,UAAU;MACxBL,GAAG,CAACW,IAAI,CAAC,CAAC;IACd,CAAC,CAAC,OAAOzb,CAAC,EAAE;MACR8a,GAAG,CAACU,OAAO,CAAC,CAAC;IACjB;EACJ;EAEA,SAAS1E,MAAMA,CAACjqB,IAAI,EAAEkqB,GAAG,EAAE;IACvB,IAAI,CAACrsB,KAAK,CAACgxB,OAAO,CAAC3E,GAAG,CAAC,EAAE;MACrBA,GAAG,GAAG,CAACA,GAAG,CAAC;IACf;;IAEA;IACA;IACA;IACA;IACA,IAAIsD,iBAAiB,IAAIxF,eAAe,CAACwB,SAAS,CAAC,CAAC,EAAE;MAElD;MACA;MACAU,GAAG,CAACjrB,OAAO,CAAC,UAAU0sB,EAAE,EAAE;QACtB,IAAI/Y,GAAG,GAAGwa,gBAAgB,CAAC0B,SAAS,CAACnD,EAAE,CAAC;;QAExC;QACA,IAAI+B,kBAAkB,IAAK1tB,IAAI,KAAKooB,gBAAgB,CAACmE,UAAW,EAAE;UAC9D3Z,GAAG,GAAG,cAAc5S,IAAI,IAAI4S,GAAG,EAAE;QACrC;;QAEA;QACA;QACA;QACAA,GAAG,GAAG,GAAG6a,YAAY,IAAI7a,GAAG,EAAE;;QAE9B;QACA;QACAkb,YAAY,CAAClb,GAAG,EAAE,IAAI,EAAE,YAAY;UAChC;UACA;UACA;UACA;UACA;UACA;UACA4a,iBAAiB,GAAG,KAAK;QAC7B,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EAEA,SAAS/F,UAAUA,CAACsF,KAAK,EAAElB,EAAE,EAAE;IAC3B,IAAIkD,WAAW;IAEf/G,eAAe,GAAG6D,EAAE;IAEpB4B,YAAY,GAAGV,KAAK,CAACiC,eAAe;;IAEpC;IACA;IACA,IAAI,CAACvB,YAAY,EAAE;MACf,MAAM,IAAIptB,KAAK,CACX,+CACJ,CAAC;IACL;;IAEA;IACA;IACA;IACA,IAAI,CAACktB,4BAA4B,EAAE;MAC/BwB,WAAW,GAAGhC,KAAK,CAACkC,cAAc;MAClC;MACA;MACA;MACA;MACA;MACA,IAAIF,WAAW,KAAKA,WAAW,KAAK,IAAI,IAAMA,WAAW,GAAG,IAAI,IAAKzB,qBAAqB,CAACrY,MAAM,CAAC,CAAE,CAAC,EAAE;QACnGuY,iBAAiB,GAAG,IAAI;MAC5B;MAEAD,4BAA4B,GAAG,IAAI;IACvC;EACJ;EAEA,SAASM,oBAAoBA,CAAA,EAAG;IAC5BN,4BAA4B,GAAG,KAAK;IACpCC,iBAAiB,GAAG,KAAK;IACzBC,YAAY,GAAG,IAAI;IACnBzF,eAAe,GAAG,IAAI;EAC1B;EAEA,SAASnmB,KAAKA,CAAA,EAAG;IACb,IAAI,CAAC8rB,qCAAqC,EAAE;MACxCC,eAAe,CAAC3uB,OAAO,CAACgvB,GAAG,IAAIA,GAAG,CAACiB,KAAK,CAAC,CAAC,CAAC;MAC3CtB,eAAe,GAAG,EAAE;IACxB;IAEAC,oBAAoB,CAAC,CAAC;EAC1B;EAEAvxB,QAAQ,GAAG;IACP2tB,MAAM,EAAMA,MAAM;IAClBxC,UAAU,EAAEA,UAAU;IACtB5lB,KAAK,EAAOA;EAChB,CAAC;EAEDnF,KAAK,CAAC,CAAC;EAEP,OAAOJ,QAAQ;AACnB;AAEAuwB,YAAY,CAACttB,qBAAqB,GAAG,cAAc;AACnD,+DAAe/D,6DAAY,CAACsH,eAAe,CAAC+pB,YAAY,CAAC;;;;;;;;;;;;;;ACxMzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAE2C;AACuB;AACT;AAEzD,SAASsC,mBAAmBA,CAACnzB,MAAM,EAAE;EAEjCA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACrB,IAAIM,QAAQ,EACR8yB,GAAG;EACP,MAAMlzB,QAAQ,GAAGF,MAAM,CAACE,QAAQ;EAChC,MAAMmzB,WAAW,GAAGrzB,MAAM,CAACqzB,WAAW;EACtC,MAAMjH,gBAAgB,GAAGpsB,MAAM,CAACosB,gBAAgB;EAChD;EACA,MAAM7sB,MAAM,GAAGS,MAAM,CAACiK,MAAM;EAE5B,SAASgkB,MAAMA,CAAC0B,EAAE,EAAE;IAChB,IAAI2D,CAAC,GAAG,IAAIlF,wDAAS,CAAC,CAAC;IAEvB,IAAI,CAACgF,GAAG,EAAE;MACN;IACJ;IAEA,KAAK,MAAMrc,GAAG,IAAI4Y,EAAE,EAAE;MAClB,IAAIA,EAAE,CAACnqB,cAAc,CAACuR,GAAG,CAAC,EAAE;QACxBuc,CAAC,CAACvc,GAAG,CAAC,GAAG4Y,EAAE,CAAC5Y,GAAG,CAAC;MACpB;IACJ;IAEA,IAAI,CAACuc,CAAC,CAACC,MAAM,EAAE;MACXD,CAAC,CAACC,MAAM,GAAGH,GAAG,CAAC3Z,WAAW,IAAI2Z,GAAG,CAACxc,GAAG;IACzC;IAEA,IAAI,CAAC0c,CAAC,CAACE,MAAM,EAAE;MACXF,CAAC,CAACE,MAAM,GAAG,IAAI7yB,IAAI,CAAC,CAAC;IACzB;IAEA0yB,WAAW,CAACI,YAAY,CAACH,CAAC,CAAC;EAC/B;EAEA,SAASI,gBAAgBA,CAACvc,CAAC,EAAE;IACzB,IAAIA,CAAC,CAACpW,KAAK,EAAE;MACT;IACJ;IAEAqyB,GAAG,GAAGjc,CAAC,CAACmU,QAAQ;EACpB;EAEA,SAASqI,wBAAwBA,CAACxc,CAAC,EAAE;IACjC8W,MAAM,CAAC;MACH2F,SAAS,EAAExF,wDAAS,CAACyF,gBAAgB;MACrCC,eAAe,EAAE3c,CAAC,CAAC4Z;IACvB,CAAC,CAAC;EACN;EAEA,SAASgD,gBAAgBA,CAAA,EAAG;IACxB9F,MAAM,CAAC;MACH2F,SAAS,EAAExF,wDAAS,CAAC4F;IACzB,CAAC,CAAC;EACN;EAEA,SAASC,gBAAgBA,CAACtE,EAAE,EAAE;IAC1B,IAAKA,EAAE,CAACuE,YAAY,KAAK,CAAC;IAAK;IAC1BvE,EAAE,CAACuE,YAAY,IAAI,IAAK;IAAI;IAC5BvE,EAAE,CAACuE,YAAY,IAAI,GAAI;IAAI;IAC3BvE,EAAE,CAACuE,YAAY,GAAG,GAAI;IAAI;IAC1BvE,EAAE,CAACuE,YAAY,IAAI,GAAI,EAAE;MAAE;MAC5BjG,MAAM,CAAC;QACH2F,SAAS,EAAEjE,EAAE,CAACuE,YAAY,IAAI9F,wDAAS,CAAC+F,gBAAgB;QACxDvd,GAAG,EAAE+Y,EAAE,CAAC/Y,GAAG;QACX4c,MAAM,EAAE7D,EAAE,CAACyE,SAAS;QACpBN,eAAe,EAAEnE,EAAE,CAAC0E;MACxB,CAAC,CAAC;IACN;EACJ;EAEA,SAASC,aAAaA,CAACnd,CAAC,EAAE;IACtB,QAAQA,CAAC,CAACwV,MAAM;MACZ,KAAKP,gBAAgB,CAACwE,YAAY;QAC9BqD,gBAAgB,CAAC9c,CAAC,CAAC1V,KAAK,CAAC;QACzB;MACJ;QACI;IACR;EACJ;EAEA,SAAS8yB,eAAeA,CAACpd,CAAC,EAAE;IACxB,IAAIqd,MAAM,GAAGrd,CAAC,CAACpW,KAAK,GAAGoW,CAAC,CAACpW,KAAK,CAAC3N,IAAI,GAAG,CAAC;IACvC,IAAIwgC,SAAS;IAEb,QAAQY,MAAM;MACV,KAAKC,UAAU,CAACC,iBAAiB;QAC7Bd,SAAS,GAAGxF,wDAAS,CAAC+F,gBAAgB;QACtC;MACJ,KAAKM,UAAU,CAACE,gBAAgB;QAC5Bf,SAAS,GAAGxF,wDAAS,CAACwG,mBAAmB;QACzC;MACJ;QACI;IACR;IAEA3G,MAAM,CAAC;MACH2F,SAAS,EAAEA;IACf,CAAC,CAAC;EACN;EAEA,SAASnI,UAAUA,CAAA,EAAG;IAClBvrB,QAAQ,CAACiF,EAAE,CAAC5F,MAAM,CAAC6e,gBAAgB,EAAEsV,gBAAgB,EAAEpzB,QAAQ,CAAC;IAChEJ,QAAQ,CAACiF,EAAE,CACP5F,MAAM,CAACqf,2CAA2C,EAClD+U,wBAAwB,EACxBrzB,QACJ,CAAC;IACDJ,QAAQ,CAACiF,EAAE,CAAC5F,MAAM,CAAC8hB,YAAY,EAAEiT,aAAa,EAAEh0B,QAAQ,CAAC;IACzDJ,QAAQ,CAACiF,EAAE,CAAC5F,MAAM,CAAC+hB,cAAc,EAAEgT,aAAa,EAAEh0B,QAAQ,CAAC;IAC3DJ,QAAQ,CAACiF,EAAE,CAAC5F,MAAM,CAACwjB,cAAc,EAAEwR,eAAe,EAAEj0B,QAAQ,CAAC;IAC7DJ,QAAQ,CAACiF,EAAE,CACPolB,kEAAsB,CAACE,uBAAuB,EAC9CsJ,gBAAgB,EAChBzzB,QACJ,CAAC;EACL;EAEA,SAASuF,KAAKA,CAAA,EAAG;IACb3F,QAAQ,CAACmF,GAAG,CAAC9F,MAAM,CAAC6e,gBAAgB,EAAEsV,gBAAgB,EAAEpzB,QAAQ,CAAC;IACjEJ,QAAQ,CAACmF,GAAG,CACR9F,MAAM,CAACqf,2CAA2C,EAClD+U,wBAAwB,EACxBrzB,QACJ,CAAC;IACDJ,QAAQ,CAACmF,GAAG,CAAC9F,MAAM,CAAC8hB,YAAY,EAAEiT,aAAa,EAAEh0B,QAAQ,CAAC;IAC1DJ,QAAQ,CAACmF,GAAG,CAAC9F,MAAM,CAAC+hB,cAAc,EAAEgT,aAAa,EAAEh0B,QAAQ,CAAC;IAC5DJ,QAAQ,CAACmF,GAAG,CAAC9F,MAAM,CAACwjB,cAAc,EAAEwR,eAAe,EAAEj0B,QAAQ,CAAC;IAC9DJ,QAAQ,CAACmF,GAAG,CACRklB,kEAAsB,CAACE,uBAAuB,EAC9CsJ,gBAAgB,EAChBzzB,QACJ,CAAC;EACL;EAEAA,QAAQ,GAAG;IACPmrB,UAAU;IACV5lB;EACJ,CAAC;EAED,OAAOvF,QAAQ;AACnB;AAEA6yB,mBAAmB,CAAC5vB,qBAAqB,GAAG,qBAAqB;AACjE,+DAAe/D,6DAAY,CAACiE,mBAAmB,CAAC0vB,mBAAmB,CAAC;;;;;;;;;;;;ACnLpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEyD;;AAEzD;AACA;AACA;AACA,SAASnE,cAAcA,CAAA,EAAG;EACtB,OAAO;IACHgB,yBAAyB,EAAE,SAAAA,CAAUjZ,GAAG,EAAEf,CAAC,EAAEhS,IAAI,EAAE;MAC/C,IAAI6wB,EAAE,GAAG9d,GAAG;MAEZ,IAAIf,CAAC,EAAE;QACH6e,EAAE,IAAI,GAAG,GAAG7e,CAAC;QAEb,IAAIhS,IAAI,IAAIA,IAAI,CAAC1Q,MAAM,EAAE;UACrBuhC,EAAE,IAAI,GAAG,GAAG7wB,IAAI;QACpB;QAEA6wB,EAAE,IAAI,GAAG;MACb;MAEA,OAAOA,EAAE;IACb,CAAC;IAED9E,SAAS,EAAE,SAAAA,CAAUD,IAAI,EAAE;MACvB,IAAI,CAACA,IAAI,EAAE;QACP,MAAM,IAAIzrB,KAAK,CAAC,WAAW,CAAC;MAChC;MAEA,IAAIywB,KAAK,CAAChF,IAAI,CAAC,EAAE;QACb,MAAM,IAAIzrB,KAAK,CAAC,UAAU,CAAC;MAC/B;;MAEA;MACA;MACA,IAAIyrB,IAAI,GAAG,CAAC,EAAE;QACV,MAAM,IAAIzrB,KAAK,CAAC,oBAAoB,CAAC;MACzC;MAEA,OAAOyrB,IAAI;IACf;EACJ,CAAC;AACL;AAEAd,cAAc,CAACzrB,qBAAqB,GAAG,gBAAgB;AACvD,+DAAe/D,6DAAY,CAACiE,mBAAmB,CAACurB,cAAc,CAAC;;;;;;;;;;;;;;;AC3ExB;AACJ;AACQ;AACc;AAEzD,SAASnE,eAAeA,CAAE7qB,MAAM,EAAE;EAC9BA,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACrB,IAAIM,QAAQ;EACZ,IAAI6qB,OAAO,GAAGnrB,MAAM,CAACmrB,OAAO;EAC5B,MAAMC,SAAS,GAAGprB,MAAM,CAACorB,SAAS;EAElC,SAAS4J,wBAAwBA,CAAC1J,QAAQ,EAAE2J,OAAO,EAAEC,KAAK,EAAE;IACxD,IAAIC,SAAS,EACTC,kBAAkB;IACtB,IAAIC,qBAAqB,GAAG,CAAC;IAE7B,IAAIJ,OAAO,EAAE;MACT;MACA;MACA;MACA;MACAI,qBAAqB,GAAGlK,OAAO,CAACmK,wBAAwB,CAAChK,QAAQ,CAAC,GAAG,IAAI;IAC7E,CAAC,MAAM;MACH;MACA;MACA;MACA6J,SAAS,GAAGhK,OAAO,CAACoK,iBAAiB,CAACjK,QAAQ,CAAC;MAE/C,IAAI6J,SAAS,CAAC7hC,MAAM,EAAE;QAClB+hC,qBAAqB,GAAGF,SAAS,CAAC,CAAC,CAAC,CAAC/+B,KAAK;MAC9C;IACJ;;IAEA;IACA;IACA;IACAg/B,kBAAkB,GAAGC,qBAAqB;IAE1C,IAAIH,KAAK,IAAIA,KAAK,CAAC1vB,cAAc,CAAC4lB,SAAS,CAAC3E,UAAU,CAAC,EAAE;MACrD2O,kBAAkB,IAAIF,KAAK,CAAC/H,SAAS;IACzC;IAEA,OAAOiI,kBAAkB;EAC7B;EAEA,SAAS/J,UAAUA,CAACC,QAAQ,EAAE;IAC1B,IAAI/gB,OAAO,GAAG,EAAE;IAEhB,IAAI+gB,QAAQ,IAAIA,QAAQ,CAACyJ,OAAO,EAAE;MAC9BzJ,QAAQ,CAACyJ,OAAO,CAAC9xB,OAAO,CAAC0pB,MAAM,IAAI;QAC/B,IAAI6I,WAAW,GAAG,IAAIT,sDAAO,CAAC,CAAC;QAC/B,IAAIU,SAAS,GAAGtK,OAAO,CAACuK,YAAY,CAACpK,QAAQ,CAAC;QAE9C,IAAIqB,MAAM,CAACnnB,cAAc,CAAC,SAAS,CAAC,EAAE;UAClCgwB,WAAW,CAACjrB,OAAO,GAAGoiB,MAAM,CAACpiB,OAAO;QACxC,CAAC,MAAM;UACH;QACJ;QAEA,IAAIoiB,MAAM,CAACR,KAAK,EAAE;UACdQ,MAAM,CAACR,KAAK,CAAClpB,OAAO,CAACiyB,KAAK,IAAI;YAC1B,IAAIS,UAAU,GAAG,IAAIxJ,oDAAK,CAAC,CAAC;YAE5BwJ,UAAU,CAACxI,SAAS,GAChB6H,wBAAwB,CAAC1J,QAAQ,EAAEmK,SAAS,EAAEP,KAAK,CAAC;YAExD,IAAIA,KAAK,CAAC1vB,cAAc,CAAC,UAAU,CAAC,EAAE;cAClCmwB,UAAU,CAACvI,QAAQ,GAAG8H,KAAK,CAAC9H,QAAQ;YACxC,CAAC,MAAM;cACH;cACA;cACAuI,UAAU,CAACvI,QAAQ,GAAGjC,OAAO,CAACyK,WAAW,CAACtK,QAAQ,CAAC;YACvD;YAEAqK,UAAU,CAACrI,iBAAiB,GAAGmI,SAAS;YAExCD,WAAW,CAACrJ,KAAK,CAACjnB,IAAI,CAACywB,UAAU,CAAC;UACtC,CAAC,CAAC;QACN;QAEA,IAAIhJ,MAAM,CAACL,SAAS,EAAE;UAClBK,MAAM,CAACL,SAAS,CAACrpB,OAAO,CAAC8qB,SAAS,IAAI;YAClC,IAAI8H,cAAc,GAAG,IAAIvJ,wDAAS,CAAC,CAAC;YAEpC,IAAIyB,SAAS,CAACvoB,cAAc,CAAC4lB,SAAS,CAAC5E,aAAa,CAAC,EAAE;cACnDqP,cAAc,CAACxsB,WAAW,GAAG0kB,SAAS,CAAC1kB,WAAW;YACtD,CAAC,MAAM;cACH;cACA;YACJ;YAEA,IAAI0kB,SAAS,CAACvoB,cAAc,CAAC,OAAO,CAAC,EAAE;cACnCqwB,cAAc,CAACp0B,KAAK,GAAGssB,SAAS,CAACtsB,KAAK;YAC1C;YAEA,IAAIssB,SAAS,CAACvoB,cAAc,CAAC4lB,SAAS,CAACpD,iBAAiB,CAAC,EAAE;cACvD6N,cAAc,CAAC7C,eAAe,GAAGjF,SAAS,CAAC3C,SAAS,CAACpD,iBAAiB,CAAC;YAC3E;YAEA,IAAI+F,SAAS,CAACvoB,cAAc,CAAC4lB,SAAS,CAACnD,eAAe,CAAC,EAAE;cACrD4N,cAAc,CAAC5C,cAAc,GAAGlF,SAAS,CAAC3C,SAAS,CAACnD,eAAe,CAAC;YACxE;YAEAuN,WAAW,CAAClJ,SAAS,CAACpnB,IAAI,CAAC2wB,cAAc,CAAC;UAC9C,CAAC,CAAC;QACN,CAAC,MAAM;UACH;UACA;QACJ;QAEAtrB,OAAO,CAACrF,IAAI,CAACswB,WAAW,CAAC;MAC7B,CAAC,CAAC;IACN;IAEA,OAAOjrB,OAAO;EAClB;EAEAjK,QAAQ,GAAG;IACP+qB,UAAU,EAAEA;EAChB,CAAC;EAED,OAAO/qB,QAAQ;AACnB;AAEAuqB,eAAe,CAACtnB,qBAAqB,GAAG,iBAAiB;AACzD,+DAAe/D,6DAAY,CAACiE,mBAAmB,CAAConB,eAAe,CAAC;;;;;;;;;;;;AC7HhE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEyD;;AAEzD;AACA;AACA;AACA,SAASoG,gBAAgBA,CAAA,EAAG;EAExB;EACA;EACA;EACA,SAAS6B,SAASA,CAACnG,MAAM,EAAE;IACvB,IAAImJ,KAAK,GAAG,EAAE;IACd,IAAIzvB,GAAG,GAAG,EAAE;IACZ,IAAI0Q,GAAG,EACHtV,KAAK;;IAET;IACA;IACA;IACA;IACA;IACA,KAAKsV,GAAG,IAAI4V,MAAM,EAAE;MAChB,IAAIA,MAAM,CAACnnB,cAAc,CAACuR,GAAG,CAAC,IAAKA,GAAG,CAACpc,OAAO,CAAC,GAAG,CAAC,KAAK,CAAE,EAAE;QACxD8G,KAAK,GAAGkrB,MAAM,CAAC5V,GAAG,CAAC;;QAEnB;QACA;QACA,IAAKtV,KAAK,KAAKhN,SAAS,IAAMgN,KAAK,KAAK,IAAK,EAAE;UAC3CA,KAAK,GAAG,EAAE;QACd;;QAEA;QACA,IAAII,KAAK,CAACgxB,OAAO,CAACpxB,KAAK,CAAC,EAAE;UACtB;UACA,IAAI,CAACA,KAAK,CAACnO,MAAM,EAAE;YACf;UACJ;UAEA+S,GAAG,GAAG,EAAE;UAER5E,KAAK,CAACwB,OAAO,CAAC,UAAU8yB,CAAC,EAAE;YACvB,IAAIC,SAAS,GAAG/K,MAAM,CAACgL,SAAS,CAAC9c,QAAQ,CAACld,IAAI,CAAC85B,CAAC,CAAC,CAACriC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,QAAQ;YAE3E2S,GAAG,CAACnB,IAAI,CAAC8wB,SAAS,GAAGD,CAAC,GAAGjD,SAAS,CAACiD,CAAC,CAAC,CAAC;UAC1C,CAAC,CAAC;UAEFt0B,KAAK,GAAG4E,GAAG,CAACjK,GAAG,CAAC8a,kBAAkB,CAAC,CAACpiB,IAAI,CAAC,GAAG,CAAC;QACjD,CAAC,MAAM,IAAI,OAAO2M,KAAK,KAAK,QAAQ,EAAE;UAClCA,KAAK,GAAGyV,kBAAkB,CAACzV,KAAK,CAAC;QACrC,CAAC,MAAM,IAAIA,KAAK,YAAYd,IAAI,EAAE;UAC9Bc,KAAK,GAAGA,KAAK,CAACy0B,WAAW,CAAC,CAAC;QAC/B,CAAC,MAAM,IAAI,OAAOz0B,KAAK,KAAK,QAAQ,EAAE;UAClCA,KAAK,GAAGuX,IAAI,CAACmd,KAAK,CAAC10B,KAAK,CAAC;QAC7B;QAEAq0B,KAAK,CAAC5wB,IAAI,CAAC6R,GAAG,GAAG,GAAG,GAAGtV,KAAK,CAAC;MACjC;IACJ;;IAEA;IACA;IACA,OAAOq0B,KAAK,CAAChhC,IAAI,CAAC,GAAG,CAAC;EAC1B;EAEA,OAAO;IACHg+B,SAAS,EAAEA;EACf,CAAC;AACL;AAEA7B,gBAAgB,CAAC1tB,qBAAqB,GAAG,kBAAkB;AAC3D,+DAAe/D,6DAAY,CAACiE,mBAAmB,CAACwtB,gBAAgB,CAAC;;;;;;;;;;;;ACrGjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEyD;;AAEzD;AACA;AACA;AACA,SAASC,GAAGA,CAAA,EAAG;EAEX;EACA;EACA,IAAIkF,MAAM,GAAGl/B,MAAM,CAACk/B,MAAM,IAAIl/B,MAAM,CAACm/B,QAAQ;;EAE7C;EACA,IAAIC,SAAS,GAAGC,WAAW;EAC3B,IAAIC,SAAS,GAAGxd,IAAI,CAACyd,GAAG,CAAC,CAAC,EAAEH,SAAS,CAAC9a,iBAAiB,GAAG,CAAC,CAAC,GAAG,CAAC;;EAEhE;EACA;EACA;EACA,IAAIkb,kBAAkB,GAAG,EAAE;EAE3B,IAAIC,aAAa,EACb/wB,KAAK,EACLtF,QAAQ;EAEZ,SAASmrB,UAAUA,CAAA,EAAG;IAClB,IAAI2K,MAAM,EAAE;MACR,IAAI,CAACO,aAAa,EAAE;QAChBA,aAAa,GAAG,IAAIL,SAAS,CAACI,kBAAkB,CAAC;MACrD;MACAN,MAAM,CAACQ,eAAe,CAACD,aAAa,CAAC;MACrC/wB,KAAK,GAAG,CAAC;IACb;EACJ;EAEA,SAASixB,IAAIA,CAAChoB,GAAG,EAAEC,GAAG,EAAE;IACpB,IAAI2H,CAAC;IAEL,IAAI,CAAC5H,GAAG,EAAE;MACNA,GAAG,GAAG,CAAC;IACX;IAEA,IAAI,CAACC,GAAG,EAAE;MACNA,GAAG,GAAG,CAAC;IACX;IAEA,IAAIsnB,MAAM,EAAE;MACR,IAAIxwB,KAAK,KAAK+wB,aAAa,CAACrjC,MAAM,EAAE;QAChCm4B,UAAU,CAAC,CAAC;MAChB;MAEAhV,CAAC,GAAGkgB,aAAa,CAAC/wB,KAAK,CAAC,GAAG4wB,SAAS;MACpC5wB,KAAK,IAAI,CAAC;IACd,CAAC,MAAM;MACH6Q,CAAC,GAAGuC,IAAI,CAACC,MAAM,CAAC,CAAC;IACrB;IAEA,OAAQxC,CAAC,IAAI3H,GAAG,GAAGD,GAAG,CAAC,GAAIA,GAAG;EAClC;EAEAvO,QAAQ,GAAG;IACP2Y,MAAM,EAAE4d;EACZ,CAAC;EAEDpL,UAAU,CAAC,CAAC;EAEZ,OAAOnrB,QAAQ;AACnB;AAEA4wB,GAAG,CAAC3tB,qBAAqB,GAAG,KAAK;AACjC,+DAAe/D,6DAAY,CAACiE,mBAAmB,CAACytB,GAAG,CAAC;;;;;;;;;;;ACpGpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM9C,SAAS,CAAC;EACZpR,WAAWA,CAAA,EAAG;IACV,IAAI,CAACuW,MAAM,GAAG,IAAI;IAClB;IACA;;IAEA,IAAI,CAACK,SAAS,GAAG,IAAI;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,IAAI,CAACJ,MAAM,GAAG,IAAI;IAClB;IACA;;IAEA,IAAI,CAAC5c,GAAG,GAAG,IAAI;IACf;IACA;IACA;IACA;IACA;;IAEA,IAAI,CAACkgB,SAAS,GAAG,IAAI;IACrB;IACA;IACA;IACA;IACA;;IAEA,IAAI,CAAChD,eAAe,GAAG,IAAI;IAC3B;IACA;IACA;IACA;EACJ;AACJ;AAEA1F,SAAS,CAAC2I,4BAA4B,GAAG,KAAK;AAC9C3I,SAAS,CAAC4I,qBAAqB,GAAG,KAAK;AACvC5I,SAAS,CAAC6I,gBAAgB,GAAG,KAAK;AAClC7I,SAAS,CAAC8I,kBAAkB,GAAG,KAAK;AACpC9I,SAAS,CAAC+F,gBAAgB,GAAG,KAAK;AAClC/F,SAAS,CAAC+I,qBAAqB,GAAG,KAAK;AACvC/I,SAAS,CAACwG,mBAAmB,GAAG,KAAK;AACrCxG,SAAS,CAACyF,gBAAgB,GAAG,KAAK;AAClCzF,SAAS,CAAC4F,eAAe,GAAG,KAAK;AAEjC,+DAAe5F,SAAS;;;;;;;;;;;AChGxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2G,OAAO,CAAC;EACV/X,WAAWA,CAAA,EAAG;IAEV,IAAI,CAACzS,OAAO,GAAG,EAAE;IACjB,IAAI,CAAC4hB,KAAK,GAAG,EAAE;IACf,IAAI,CAACG,SAAS,GAAG,EAAE;EACvB;AACJ;AAEA,+DAAeyI,OAAO;;;;;;;;;;;AC3CtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM5I,KAAK,CAAC;EACRnP,WAAWA,CAAA,EAAG;IAEV;IACA,IAAI,CAACmQ,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,QAAQ,GAAGvR,QAAQ;;IAExB;IACA,IAAI,CAACyR,iBAAiB,GAAG,KAAK;EAClC;AACJ;AAEA,+DAAenB,KAAK;;;;;;;;;;;AC9CpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAMiL,uBAAuB,GAAG,IAAI;AAEpC,MAAM9K,SAAS,CAAC;EACZtP,WAAWA,CAAA,EAAG;IAEV,IAAI,CAAC3T,WAAW,GAAG,EAAE;IACrB,IAAI,CAAC5H,KAAK,GAAG,EAAE;;IAEf;IACA,IAAI,CAACuxB,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,cAAc,GAAGmE,uBAAuB;EACjD;AACJ;AAEA,+DAAe9K,SAAS;;;;;;;;;;;;;;;;AClDxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACmD;AACG;AACR;AACiB;AACb;AAElD,MAAMgL,4BAA4B,GAAG,KAAK;AAE1C,SAASnG,qBAAqBA,CAAA,EAAG;EAE7B,IAAI7wB,QAAQ,EACRi3B,gBAAgB,EAChBC,kBAAkB,EAClBC,mBAAmB,EACnBC,oBAAoB,EACpBC,qBAAqB,EACrBC,sBAAsB,EACtBC,yBAAyB,EACzBC,mCAAmC,EACnCC,cAAc;EAElB,MAAM93B,OAAO,GAAG,IAAI,CAACA,OAAO;EAC5B,MAAMG,QAAQ,GAAGuH,6DAAQ,CAAC1H,OAAO,CAAC,CAACE,WAAW,CAAC,CAAC;EAEhD,SAASO,KAAKA,CAAA,EAAG;IACb82B,kBAAkB,GAAG;MACjBQ,OAAO,EAAEV;IACb,CAAC;IACDW,qBAAqB,CAAC,CAAC;EAC3B;EAEA,SAASA,qBAAqBA,CAAA,EAAG;IAC7BR,mBAAmB,GAAG,EAAE;IACxBC,oBAAoB,GAAG,EAAE;IACzBC,qBAAqB,GAAG,EAAE;IAC1BC,sBAAsB,GAAG,EAAE;IAC3BC,yBAAyB,GAAG,EAAE;IAC9BE,cAAc,GAAG,EAAE;IACnBD,mCAAmC,GAAG,IAAI;IAC1CP,gBAAgB,GAAG,EAAE;EACzB;EAGA,SAAS1xB,KAAKA,CAAA,EAAG;IACboyB,qBAAqB,CAAC,CAAC;EAC3B;EAEA,SAASC,SAASA,CAAA,EAAG,CAErB;;EAEA;AACJ;AACA;AACA;EACI,SAASC,sCAAsCA,CAACC,UAAU,EAAE;IACxDN,mCAAmC,GAAGM,UAAU;EACpD;;EAEA;AACJ;AACA;EACI,SAASC,wCAAwCA,CAAA,EAAG;IAChDP,mCAAmC,GAAG,IAAI;EAC9C;;EAEA;AACJ;AACA;AACA;EACI,SAASQ,sCAAsCA,CAAA,EAAG;IAC9C,OAAOR,mCAAmC;EAC9C;;EAEA;AACJ;AACA;AACA;EACI,SAASS,wBAAwBA,CAAA,EAAG;IAChC,OAAOZ,qBAAqB;EAChC;;EAEA;AACJ;AACA;AACA;EACI,SAASa,yBAAyBA,CAAA,EAAG;IACjC,OAAOZ,sBAAsB;EACjC;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,SAASa,4BAA4BA,CAAC/yB,MAAM,EAAE;IAC1CiyB,qBAAqB,CAACzyB,IAAI,CAACQ,MAAM,CAAC;EACtC;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,SAASgzB,6BAA6BA,CAAChzB,MAAM,EAAE;IAC3CkyB,sBAAsB,CAAC1yB,IAAI,CAACQ,MAAM,CAAC;EACvC;;EAEA;AACJ;AACA;AACA;EACI,SAASizB,8BAA8BA,CAACjzB,MAAM,EAAE;IAC5CkzB,iBAAiB,CAACjB,qBAAqB,EAAEjyB,MAAM,CAAC;EACpD;;EAEA;AACJ;AACA;AACA;EACI,SAASmzB,+BAA+BA,CAACnzB,MAAM,EAAE;IAC7CkzB,iBAAiB,CAAChB,sBAAsB,EAAElyB,MAAM,CAAC;EACrD;;EAEA;AACJ;AACA;AACA;EACI,SAASozB,4BAA4BA,CAAA,EAAG;IACpC,OAAOjB,yBAAyB;EACpC;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,SAASkB,gCAAgCA,CAACrzB,MAAM,EAAE;IAC9CmyB,yBAAyB,CAAC3yB,IAAI,CAACQ,MAAM,CAAC;EAC1C;;EAEA;AACJ;AACA;AACA;EACI,SAASszB,kCAAkCA,CAACtzB,MAAM,EAAE;IAChDkzB,iBAAiB,CAACf,yBAAyB,EAAEnyB,MAAM,CAAC;EACxD;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,SAASkzB,iBAAiBA,CAACrzB,OAAO,EAAEG,MAAM,EAAE;IACxC,IAAIE,KAAK,GAAG,CAAC,CAAC;IACdL,OAAO,CAACR,IAAI,CAAC,CAAC7B,IAAI,EAAE7P,CAAC,KAAK;MACtB,IAAI6P,IAAI,KAAKwC,MAAM,EAAE;QACjBE,KAAK,GAAGvS,CAAC;QACT,OAAO,IAAI;MACf;IACJ,CAAC,CAAC;IACF,IAAIuS,KAAK,GAAG,CAAC,EAAE;MACX;IACJ;IACAL,OAAO,CAACN,MAAM,CAACW,KAAK,EAAE,CAAC,CAAC;EAC5B;;EAEA;AACJ;AACA;AACA;AACA;EACI,SAASqzB,uBAAuBA,CAACC,QAAQ,EAAE;IACvC,IAAI7lC,CAAC;IACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0kC,cAAc,CAACzkC,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,IAAI0kC,cAAc,CAAC1kC,CAAC,CAAC,CAAC6lC,QAAQ,KAAKA,QAAQ,EAAE;QACzC,OAAO7lC,CAAC;MACZ;IACJ;IACA,OAAO,CAAC,CAAC;EACb;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAS8lC,gBAAgBA,CAACn1B,IAAI,EAAEk1B,QAAQ,EAAEE,IAAI,EAAE;IAC5C,IAAI,OAAOp1B,IAAI,KAAK,QAAQ,IAAKA,IAAI,KAAKyD,+DAAS,CAACgiB,WAAW,CAACE,sBAAsB,IAAI3lB,IAAI,KAAKyD,+DAAS,CAACgiB,WAAW,CAACC,oBAAqB,IAC1I,OAAOwP,QAAQ,KAAK,QAAQ,EAAE;MAC9B,MAAMzxB,+DAAS,CAAC8d,kBAAkB;IACtC;IACA,IAAI3f,KAAK,GAAGqzB,uBAAuB,CAACC,QAAQ,CAAC;IAC7C,IAAItzB,KAAK,KAAK,CAAC,CAAC,EAAE;MACd;MACAmyB,cAAc,CAAC7yB,IAAI,CAAC;QAChBlB,IAAI,EAAEA,IAAI;QACVk1B,QAAQ,EAAEA,QAAQ;QAClBE,IAAI,EAAEA;MACV,CAAC,CAAC;IACN,CAAC,MAAM;MACH;MACArB,cAAc,CAACnyB,KAAK,CAAC,CAAC5B,IAAI,GAAGA,IAAI;MACjC+zB,cAAc,CAACnyB,KAAK,CAAC,CAACwzB,IAAI,GAAGA,IAAI;IACrC;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EACI,SAASC,mBAAmBA,CAACH,QAAQ,EAAE;IACnC,IAAIA,QAAQ,EAAE;MACV,IAAItzB,KAAK,GAAGqzB,uBAAuB,CAACC,QAAQ,CAAC;MAC7C;MACA,IAAItzB,KAAK,KAAK,CAAC,CAAC,EAAE;QACd;QACAmyB,cAAc,CAAC9yB,MAAM,CAACW,KAAK,EAAE,CAAC,CAAC;MACnC;IACJ,CAAC,MAAM;MACH;MACAmyB,cAAc,GAAG,EAAE;IACvB;EACJ;;EAEA;AACJ;AACA;EACI,SAASuB,sBAAsBA,CAAA,EAAG;IAC9BvB,cAAc,GAAG,EAAE;EACvB;;EAEA;AACJ;AACA;AACA;EACI,SAASwB,iBAAiBA,CAAA,EAAG;IACzB,OAAOxB,cAAc;EACzB;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,SAASyB,qBAAqBA,CAACC,WAAW,EAAE;IACxChC,mBAAmB,CAACvyB,IAAI,CAACu0B,WAAW,CAAC;EACzC;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,SAASC,sBAAsBA,CAACD,WAAW,EAAE;IACzC/B,oBAAoB,CAACxyB,IAAI,CAACu0B,WAAW,CAAC;EAC1C;;EAEA;AACJ;AACA;AACA;EACI,SAASE,wBAAwBA,CAACF,WAAW,EAAE;IAC3Cb,iBAAiB,CAACnB,mBAAmB,EAAEgC,WAAW,CAAC;EACvD;;EAEA;AACJ;AACA;AACA;EACI,SAASG,yBAAyBA,CAACH,WAAW,EAAE;IAC5Cb,iBAAiB,CAAClB,oBAAoB,EAAE+B,WAAW,CAAC;EACxD;;EAEA;AACJ;AACA;AACA;EACI,SAASI,sBAAsBA,CAAA,EAAG;IAC9B,OAAOpC,mBAAmB;EAC9B;;EAEA;AACJ;AACA;AACA;EACI,SAASqC,uBAAuBA,CAAA,EAAG;IAC/B,OAAOpC,oBAAoB;EAC/B;;EAEA;AACJ;AACA;AACA;AACA;EACI,SAASqC,kBAAkBA,CAAC1wB,WAAW,EAAE5H,KAAK,EAAE;IAC5Cu4B,qBAAqB,CAAC3wB,WAAW,EAAE5H,KAAK,CAAC,CAAC,CAAC;IAC3C,IAAIkuB,EAAE,GAAG,IAAI3P,6DAAS,CAAC,CAAC;IACxB2P,EAAE,CAACtmB,WAAW,GAAGA,WAAW;IAC5BsmB,EAAE,CAACluB,KAAK,GAAGA,KAAK;IAChB81B,gBAAgB,CAACryB,IAAI,CAACyqB,EAAE,CAAC;EAC7B;;EAEA;AACJ;AACA;AACA;EACI,SAASsK,mBAAmBA,CAAA,EAAG;IAC3B,OAAO1C,gBAAgB;EAC3B;;EAEA;AACJ;AACA;AACA;AACA;EACI,SAASyC,qBAAqBA,CAAC3wB,WAAW,EAAE5H,KAAK,EAAE;IAC/C41B,6EAAkB,CAAChuB,WAAW,EAAE,QAAQ,CAAC;IACzCguB,6EAAkB,CAAC51B,KAAK,EAAE,QAAQ,CAAC;IACnC81B,gBAAgB,CAACt0B,OAAO,CAAC,UAAUoD,GAAG,EAAErB,GAAG,EAAE;MACzC,IAAIqB,GAAG,CAACgD,WAAW,KAAKA,WAAW,IAAIhD,GAAG,CAAC5E,KAAK,KAAKA,KAAK,EAAE;QACxD81B,gBAAgB,CAACtyB,MAAM,CAACD,GAAG,EAAE,CAAC,CAAC;MACnC;IACJ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;EACI,SAASk1B,4BAA4BA,CAAA,EAAG;IACpC3C,gBAAgB,GAAG,EAAE;EACzB;;EAEA;AACJ;AACA;EACI,SAAS4C,8BAA8BA,CAAA,EAAG;IACtC,IAAIC,sBAAsB,GAAGh6B,QAAQ,CAAClB,GAAG,CAAC,CAAC,CAACoJ,SAAS,CAAC4E,kBAAkB,CAACS,mBAAmB;IAC5FosB,kBAAkB,CAACK,sBAAsB,CAACxsB,MAAM,EAAEwsB,sBAAsB,CAAC34B,KAAK,CAAC;EACnF;EAEA,SAAS44B,4BAA4BA,CAACr2B,IAAI,EAAEvC,KAAK,EAAE;IAC/C,IAAI,CAACuC,IAAI,EAAE;MACPinB,MAAM,CAACC,IAAI,CAACsM,kBAAkB,CAAC,CAACv0B,OAAO,CAAC8T,GAAG,IAAI;QAC3CsjB,4BAA4B,CAACtjB,GAAG,EAAEtV,KAAK,CAAC;MAC5C,CAAC,CAAC;IACN,CAAC,MAAM;MACH+1B,kBAAkB,CAACxzB,IAAI,CAAC,GAAG,CAAC,CAACvC,KAAK;IACtC;EACJ;EAEA,SAAS2wB,4BAA4BA,CAACpuB,IAAI,EAAE;IACxC,MAAMs2B,QAAQ,GAAG9C,kBAAkB,CAACxzB,IAAI,CAAC;IAEzC,OAAOs2B,QAAQ,KAAK7lC,SAAS,GAAG+iC,kBAAkB,CAACQ,OAAO,GAAGsC,QAAQ;EACzE;EAEAh6B,QAAQ,GAAG;IACP64B,gBAAgB;IAChBK,qBAAqB;IACrBE,sBAAsB;IACtBK,kBAAkB;IAClBG,4BAA4B;IAC5BX,iBAAiB;IACjBT,4BAA4B;IAC5BR,sCAAsC;IACtCC,wBAAwB;IACxBC,yBAAyB;IACzBqB,sBAAsB;IACtBC,uBAAuB;IACvBG,mBAAmB;IACnB7H,4BAA4B;IAC5B2G,gCAAgC;IAChCN,4BAA4B;IAC5BC,6BAA6B;IAC7BW,mBAAmB;IACnBC,sBAAsB;IACtBK,wBAAwB;IACxBC,yBAAyB;IACzBI,qBAAqB;IACrBn0B,KAAK;IACLwyB,wCAAwC;IACxC8B,8BAA8B;IAC9BjC,SAAS;IACTC,sCAAsC;IACtCkC,4BAA4B;IAC5BrB,kCAAkC;IAClCL,8BAA8B;IAC9BE;EACJ,CAAC;EAEDn4B,KAAK,CAAC,CAAC;EAEP,OAAOJ,QAAQ;AACnB;AAEA6wB,qBAAqB,CAAC5tB,qBAAqB,GAAG,uBAAuB;AACrE,+DAAe/D,6DAAY,CAACiE,mBAAmB,CAAC0tB,qBAAqB,CAAC;;;;;;;;;;;;;ACvbtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACsD;AACJ;AAElD,SAASpE,gBAAgBA,CAAC;AAAA,EAAY;EAClC,IAAIyN,oBAAoB,GAAG,EAAE;EAC7B,IAAIlnC,MAAM,GAAG,CAAC;EAEd,SAAS+5B,GAAGA,CAACj3B,KAAK,EAAEH,GAAG,EAAE;IACrB,IAAI5C,CAAC;;IAEL;IACA,KAAKA,CAAC,GAAG,CAAC,EAAGA,CAAC,GAAG,IAAI,CAACmnC,oBAAoB,CAAClnC,MAAM,IAAM8C,KAAK,GAAG,IAAI,CAACokC,oBAAoB,CAACnnC,CAAC,CAAC,CAAC+C,KAAM,EAAE/C,CAAC,EAAE,CAAE;IAEzG,IAAI,CAACmnC,oBAAoB,CAACv1B,MAAM,CAAC5R,CAAC,EAAE,CAAC,EAAE;MAAE+C,KAAK,EAAEA,KAAK;MAAEH,GAAG,EAAEA;IAAI,CAAC,CAAC;IAElE,KAAK5C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACmnC,oBAAoB,CAAClnC,MAAM,GAAG,CAAC,EAAED,CAAC,EAAE,EAAE;MACvD,IAAI,IAAI,CAAConC,WAAW,CAACpnC,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC,EAAE;QAC5BA,CAAC,EAAE;MACP;IACJ;IACA,IAAI,CAACC,MAAM,GAAG,IAAI,CAACknC,oBAAoB,CAAClnC,MAAM;EAClD;EAEA,SAASi6B,KAAKA,CAAA,EAAG;IACb,IAAI,CAACiN,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAAClnC,MAAM,GAAG,CAAC;EACnB;EAEA,SAASonC,MAAMA,CAACtkC,KAAK,EAAEH,GAAG,EAAE;IACxB,KAAK,IAAI5C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACmnC,oBAAoB,CAAClnC,MAAM,EAAED,CAAC,EAAE,EAAE;MACvD,IAAI+C,KAAK,IAAI,IAAI,CAACokC,oBAAoB,CAACnnC,CAAC,CAAC,CAAC+C,KAAK,IAAIH,GAAG,IAAI,IAAI,CAACukC,oBAAoB,CAACnnC,CAAC,CAAC,CAAC4C,GAAG,EAAE;QACxF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,CAACukC,oBAAoB,CAACv1B,MAAM,CAAC5R,CAAC,EAAE,CAAC,CAAC;QACtCA,CAAC,EAAE;MAEP,CAAC,MAAM,IAAI+C,KAAK,GAAG,IAAI,CAACokC,oBAAoB,CAACnnC,CAAC,CAAC,CAAC+C,KAAK,IAAIH,GAAG,GAAG,IAAI,CAACukC,oBAAoB,CAACnnC,CAAC,CAAC,CAAC4C,GAAG,EAAE;QAC7F;QACA;QACA,IAAI,CAACukC,oBAAoB,CAACv1B,MAAM,CAAC5R,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;UAAE+C,KAAK,EAAEH,GAAG;UAAEA,GAAG,EAAE,IAAI,CAACukC,oBAAoB,CAACnnC,CAAC,CAAC,CAAC4C;QAAI,CAAC,CAAC;QACjG,IAAI,CAACukC,oBAAoB,CAACnnC,CAAC,CAAC,CAAC4C,GAAG,GAAGG,KAAK;QACxC;MACJ,CAAC,MAAM,IAAIA,KAAK,GAAG,IAAI,CAACokC,oBAAoB,CAACnnC,CAAC,CAAC,CAAC+C,KAAK,IAAIA,KAAK,GAAG,IAAI,CAACokC,oBAAoB,CAACnnC,CAAC,CAAC,CAAC4C,GAAG,EAAE;QAC/F;QACA;QACA;QACA;QACA;QACA,IAAI,CAACukC,oBAAoB,CAACnnC,CAAC,CAAC,CAAC4C,GAAG,GAAGG,KAAK;MAC5C,CAAC,MAAM,IAAIH,GAAG,GAAG,IAAI,CAACukC,oBAAoB,CAACnnC,CAAC,CAAC,CAAC+C,KAAK,IAAIH,GAAG,GAAG,IAAI,CAACukC,oBAAoB,CAACnnC,CAAC,CAAC,CAAC4C,GAAG,EAAE;QAC3F;QACA;QACA;QACA;QACA;QACA,IAAI,CAACukC,oBAAoB,CAACnnC,CAAC,CAAC,CAAC+C,KAAK,GAAGH,GAAG;MAC5C;IACJ;IAEA,IAAI,CAAC3C,MAAM,GAAG,IAAI,CAACknC,oBAAoB,CAAClnC,MAAM;EAClD;EAEA,SAASmnC,WAAWA,CAACE,WAAW,EAAEC,WAAW,EAAE;IAC3C,IAAIC,MAAM,GAAG,IAAI,CAACL,oBAAoB,CAACG,WAAW,CAAC;IACnD,IAAIG,MAAM,GAAG,IAAI,CAACN,oBAAoB,CAACI,WAAW,CAAC;IAEnD,IAAIC,MAAM,CAACzkC,KAAK,IAAI0kC,MAAM,CAAC1kC,KAAK,IAAI0kC,MAAM,CAAC1kC,KAAK,IAAIykC,MAAM,CAAC5kC,GAAG,IAAI4kC,MAAM,CAAC5kC,GAAG,IAAI6kC,MAAM,CAAC7kC,GAAG,EAAE;MACxF;MACA;MACA4kC,MAAM,CAAC5kC,GAAG,GAAG6kC,MAAM,CAAC7kC,GAAG;MACvB,IAAI,CAACukC,oBAAoB,CAACv1B,MAAM,CAAC21B,WAAW,EAAE,CAAC,CAAC;MAChD,OAAO,IAAI;IAEf,CAAC,MAAM,IAAIE,MAAM,CAAC1kC,KAAK,IAAIykC,MAAM,CAACzkC,KAAK,IAAIykC,MAAM,CAACzkC,KAAK,IAAI0kC,MAAM,CAAC7kC,GAAG,IAAI6kC,MAAM,CAAC7kC,GAAG,IAAI4kC,MAAM,CAAC5kC,GAAG,EAAE;MAC/F;MACA;MACA4kC,MAAM,CAACzkC,KAAK,GAAG0kC,MAAM,CAAC1kC,KAAK;MAC3B,IAAI,CAACokC,oBAAoB,CAACv1B,MAAM,CAAC21B,WAAW,EAAE,CAAC,CAAC;MAChD,OAAO,IAAI;IACf,CAAC,MAAM,IAAIE,MAAM,CAAC1kC,KAAK,IAAIykC,MAAM,CAACzkC,KAAK,IAAIykC,MAAM,CAACzkC,KAAK,IAAI0kC,MAAM,CAAC7kC,GAAG,IAAI4kC,MAAM,CAAC5kC,GAAG,IAAI6kC,MAAM,CAAC7kC,GAAG,EAAE;MAC/F;MACA;MACA,IAAI,CAACukC,oBAAoB,CAACv1B,MAAM,CAAC01B,WAAW,EAAE,CAAC,CAAC;MAChD,OAAO,IAAI;IACf,CAAC,MAAM,IAAIE,MAAM,CAACzkC,KAAK,IAAI0kC,MAAM,CAAC1kC,KAAK,IAAI0kC,MAAM,CAAC1kC,KAAK,IAAIykC,MAAM,CAAC5kC,GAAG,IAAI6kC,MAAM,CAAC7kC,GAAG,IAAI4kC,MAAM,CAAC5kC,GAAG,EAAE;MAC/F;MACA;MACA,IAAI,CAACukC,oBAAoB,CAACv1B,MAAM,CAAC21B,WAAW,EAAE,CAAC,CAAC;MAChD,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EAEA,SAASxkC,KAAKA,CAACwP,KAAK,EAAE;IAClB20B,iEAAY,CAAC30B,KAAK,CAAC;IAEnB,IAAIA,KAAK,IAAI,IAAI,CAAC40B,oBAAoB,CAAClnC,MAAM,IAAIsS,KAAK,GAAG,CAAC,EAAE;MACxD,OAAO+E,GAAG;IACd;IAEA,OAAO,IAAI,CAAC6vB,oBAAoB,CAAC50B,KAAK,CAAC,CAACxP,KAAK;EACjD;EAEA,SAASH,GAAGA,CAAC2P,KAAK,EAAE;IAChB20B,iEAAY,CAAC30B,KAAK,CAAC;IAEnB,IAAIA,KAAK,IAAI,IAAI,CAAC40B,oBAAoB,CAAClnC,MAAM,IAAIsS,KAAK,GAAG,CAAC,EAAE;MACxD,OAAO+E,GAAG;IACd;IAEA,OAAO,IAAI,CAAC6vB,oBAAoB,CAAC50B,KAAK,CAAC,CAAC3P,GAAG;EAC/C;EAEA,OAAO;IACHukC,oBAAoB,EAAEA,oBAAoB;IAC1ClnC,MAAM,EAAEA,MAAM;IACd+5B,GAAG,EAAEA,GAAG;IACRE,KAAK,EAAEA,KAAK;IACZmN,MAAM,EAAEA,MAAM;IACdD,WAAW,EAAEA,WAAW;IACxBrkC,KAAK,EAAEA,KAAK;IACZH,GAAG,EAAEA;EACT,CAAC;AACL;AAEA82B,gBAAgB,CAACxpB,qBAAqB,GAAG,kBAAkB;AAC3D,+DAAe/D,6DAAY,CAACsH,eAAe,CAACimB,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;AClK7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACkD;AAE3C,SAASsK,kBAAkBA,CAAC0D,SAAS,EAAE/2B,IAAI,EAAE;EAChD,IAAI,OAAO+2B,SAAS,KAAK/2B,IAAI,EAAE;IAC3B,MAAMyD,+DAAS,CAAC8d,kBAAkB;EACtC;AACJ;AAEO,SAASgV,YAAYA,CAACQ,SAAS,EAAE;EACpC,MAAMC,KAAK,GAAGD,SAAS,KAAK,IAAI,IAAI,CAACjG,KAAK,CAACiG,SAAS,CAAC,IAAKA,SAAS,GAAG,CAAC,KAAK,CAAE;EAE9E,IAAI,CAACC,KAAK,EAAE;IACR,MAAMvzB,+DAAS,CAAC8d,kBAAkB,GAAG,+BAA+B;EACxE;AACJ;AAEO,SAAS0V,UAAUA,CAACF,SAAS,EAAElsB,GAAG,EAAEC,GAAG,EAAE;EAC5C,IAAIisB,SAAS,GAAGlsB,GAAG,IAAIksB,SAAS,GAAGjsB,GAAG,EAAE;IACpC,MAAMrH,+DAAS,CAAC8d,kBAAkB,GAAG,0BAA0B;EACnE;AACJ;AAEO,SAAS2V,uBAAuBA,CAACl3B,IAAI,EAAE;EAC1C,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAKA,IAAI,KAAKyD,+DAAS,CAACkd,KAAK,IAAI3gB,IAAI,KAAKyD,+DAAS,CAACid,KAAM,EAAE;IACpF,MAAMjd,+DAAS,CAAC8d,kBAAkB;EACtC;AACJ;;;;;;;;;;;;;;;ACxDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM7d,WAAW,CAAC;EACd;AACJ;AACA;EACIsV,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;IACQ,IAAI,CAACme,KAAK,GAAG,IAAI;IACjB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACn3B,IAAI,GAAG,IAAI;IAChB;AACR;AACA;AACA;IACQ,IAAI,CAAC4S,GAAG,GAAG,IAAI;IACf;AACR;AACA;AACA;IACQ,IAAI,CAACwkB,SAAS,GAAG,IAAI;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAAClG,KAAK,GAAG,IAAI;IACjB;AACR;AACA;AACA;IACQ,IAAI,CAACmG,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACjH,SAAS,GAAG,IAAI;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACF,YAAY,GAAG,IAAI;IACxB;AACR;AACA;AACA;IACQ,IAAI,CAAChF,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACoM,KAAK,GAAG,EAAE;IACf;AACR;AACA;AACA;IACQ,IAAI,CAACrmB,IAAI,GAAG,IAAI;;IAEhB;AACR;AACA;AACA;IACQ,IAAI,CAACsmB,OAAO,GAAG,IAAI;IACnB;AACR;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAACrH,gBAAgB,GAAG,IAAI;IAC5B;AACR;AACA;IACQ,IAAI,CAACsH,eAAe,GAAG,IAAI;IAC3B;AACR;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,IAAI;EACrC;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnB;AACJ;AACA;EACI7e,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;IACQ,IAAI,CAAC1G,CAAC,GAAG,IAAI;IACb;AACR;AACA;AACA;IACQ,IAAI,CAACwlB,CAAC,GAAG,IAAI;IACb;AACR;AACA;AACA;IACQ,IAAI,CAACrM,CAAC,GAAG,EAAE;EACf;AACJ;AAEA/nB,WAAW,CAACq0B,GAAG,GAAG,KAAK;AACvBr0B,WAAW,CAACs0B,IAAI,GAAG,MAAM;AACzBt0B,WAAW,CAAC0I,QAAQ,GAAG,KAAK;AAC5B1I,WAAW,CAAC2I,oBAAoB,GAAG,gBAAgB;AACnD3I,WAAW,CAAC6I,iBAAiB,GAAG,uBAAuB;AACvD7I,WAAW,CAAC+I,kBAAkB,GAAG,cAAc;AAC/C/I,WAAW,CAAC4I,kBAAkB,GAAG,cAAc;AAC/C5I,WAAW,CAAC8I,gCAAgC,GAAG,2BAA2B;AAC1E9I,WAAW,CAACgJ,8BAA8B,GAAG,qBAAqB;AAClEhJ,WAAW,CAACu0B,kBAAkB,GAAG,cAAc;AAC/Cv0B,WAAW,CAACiJ,OAAO,GAAG,SAAS;AAC/BjJ,WAAW,CAACw0B,qBAAqB,GAAG,iBAAiB;AACrDx0B,WAAW,CAACkJ,UAAU,GAAG,OAAO;;;;;;;SCpLhC;SACA;;SAEA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;SACA;;SAEA;SACA;;SAEA;SACA;SACA;;;;;UCtBA;;;;;UCAA;UACA;UACA;UACA;UACA,yCAAyC,wCAAwC;UACjF;UACA;UACA;;;;;UCPA,8CAA8C;;;;;UCA9C;UACA;UACA;UACA,uDAAuD,iBAAiB;UACxE;UACA,gDAAgD,aAAa;UAC7D;;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEiE;AACA;AACsB;AAChB;AACR;AAE/D,SAASurB,gBAAgBA,CAAA,EAAG;EAExB,IAAIl8B,OAAO,GAAG,IAAI,CAACA,OAAO;EAC1B,IAAIK,QAAQ,EACR87B,mBAAmB;;EAEvB;AACJ;AACA;AACA;AACA;EACI,SAASC,sBAAsBA,CAACr8B,MAAM,EAAE;IACpCo8B,mBAAmB,GAAGjJ,yEAAmB,CAAClzB,OAAO,CAAC,CAACE,WAAW,CAAC;MAC3DD,QAAQ,EAAEF,MAAM,CAACE,QAAQ;MACzBmzB,WAAW,EAAErzB,MAAM,CAACqzB,WAAW;MAC/BjH,gBAAgB,EAAEpsB,MAAM,CAACosB,gBAAgB;MACzCniB,MAAM,EAAEjK,MAAM,CAACiK;IACnB,CAAC,CAAC;IACFmyB,mBAAmB,CAAC3Q,UAAU,CAAC,CAAC;IAChC,OAAOX,uFAA2B,CAAC7qB,OAAO,CAAC,CAAC+G,MAAM,CAAChH,MAAM,CAAC;EAC9D;;EAEA;AACJ;AACA;AACA;EACI,SAASs8B,mBAAmBA,CAAA,EAAG;IAC3B,OAAO1O,0EAAgB,CAAC3tB,OAAO,CAAC,CAACE,WAAW,CAAC,CAAC;EAClD;;EAEA;AACJ;AACA;AACA;EACI,SAASo8B,wBAAwBA,CAAA,EAAG;IAChC,OAAOhQ,6EAAqB,CAACtsB,OAAO,CAAC,CAACE,WAAW,CAAC,CAAC;EACvD;EAEAG,QAAQ,GAAG;IACP+7B,sBAAsB,EAAMA,sBAAsB;IAClDC,mBAAmB,EAASA,mBAAmB;IAC/CC,wBAAwB,EAAIA;EAChC,CAAC;EAED,OAAOj8B,QAAQ;AACnB;AAEA67B,gBAAgB,CAAC54B,qBAAqB,GAAG,kBAAkB;AAC3D,MAAMC,OAAO,GAAGg5B,MAAM,CAACh9B,YAAY,CAACsH,eAAe,CAACq1B,gBAAgB,CAAC;AACrE34B,OAAO,CAACyG,MAAM,GAAGsgB,kEAAsB;AACvCiS,MAAM,CAACh9B,YAAY,CAACoH,kBAAkB,CAACu1B,gBAAgB,CAAC54B,qBAAqB,EAAEC,OAAO,CAAC;AACvF,+DAAeA,OAAO,E", "sources": ["webpack://dashjs/./node_modules/path-browserify/index.js", "webpack://dashjs/./node_modules/ua-parser-js/src/ua-parser.js", "webpack://dashjs/./src/core/Debug.js", "webpack://dashjs/./src/core/EventBus.js", "webpack://dashjs/./src/core/FactoryMaker.js", "webpack://dashjs/./src/core/Settings.js", "webpack://dashjs/./src/core/Utils.js", "webpack://dashjs/./src/core/events/CoreEvents.js", "webpack://dashjs/./src/core/events/Events.js", "webpack://dashjs/./src/core/events/EventsBase.js", "webpack://dashjs/./src/dash/vo/UTCTiming.js", "webpack://dashjs/./src/streaming/MediaPlayerEvents.js", "webpack://dashjs/./src/streaming/constants/Constants.js", "webpack://dashjs/./src/streaming/metrics/MetricsReportingEvents.js", "webpack://dashjs/./src/streaming/metrics/controllers/MetricsCollectionController.js", "webpack://dashjs/./src/streaming/metrics/controllers/MetricsController.js", "webpack://dashjs/./src/streaming/metrics/controllers/MetricsHandlersController.js", "webpack://dashjs/./src/streaming/metrics/controllers/RangeController.js", "webpack://dashjs/./src/streaming/metrics/controllers/ReportingController.js", "webpack://dashjs/./src/streaming/metrics/metrics/MetricsHandlerFactory.js", "webpack://dashjs/./src/streaming/metrics/metrics/handlers/BufferLevelHandler.js", "webpack://dashjs/./src/streaming/metrics/metrics/handlers/DVBErrorsHandler.js", "webpack://dashjs/./src/streaming/metrics/metrics/handlers/GenericMetricHandler.js", "webpack://dashjs/./src/streaming/metrics/metrics/handlers/HttpListHandler.js", "webpack://dashjs/./src/streaming/metrics/reporting/ReportingFactory.js", "webpack://dashjs/./src/streaming/metrics/reporting/reporters/DVBReporting.js", "webpack://dashjs/./src/streaming/metrics/utils/DVBErrorsTranslator.js", "webpack://dashjs/./src/streaming/metrics/utils/HandlerHelpers.js", "webpack://dashjs/./src/streaming/metrics/utils/ManifestParsing.js", "webpack://dashjs/./src/streaming/metrics/utils/MetricSerialiser.js", "webpack://dashjs/./src/streaming/metrics/utils/RNG.js", "webpack://dashjs/./src/streaming/metrics/vo/DVBErrors.js", "webpack://dashjs/./src/streaming/metrics/vo/Metrics.js", "webpack://dashjs/./src/streaming/metrics/vo/Range.js", "webpack://dashjs/./src/streaming/metrics/vo/Reporting.js", "webpack://dashjs/./src/streaming/models/CustomParametersModel.js", "webpack://dashjs/./src/streaming/utils/CustomTimeRanges.js", "webpack://dashjs/./src/streaming/utils/SupervisorTools.js", "webpack://dashjs/./src/streaming/vo/metrics/HTTPRequest.js", "webpack://dashjs/webpack/bootstrap", "webpack://dashjs/webpack/runtime/amd options", "webpack://dashjs/webpack/runtime/define property getters", "webpack://dashjs/webpack/runtime/hasOwnProperty shorthand", "webpack://dashjs/webpack/runtime/make namespace object", "webpack://dashjs/./src/streaming/metrics/MetricsReporting.js"], "sourcesContent": ["// 'path' module extracted from Node.js v8.11.1 (only the posix part)\n// transplited with Babel\n\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nfunction assertPath(path) {\n  if (typeof path !== 'string') {\n    throw new TypeError('Path must be a string. Received ' + JSON.stringify(path));\n  }\n}\n\n// Resolves . and .. elements in a path with directory names\nfunction normalizeStringPosix(path, allowAboveRoot) {\n  var res = '';\n  var lastSegmentLength = 0;\n  var lastSlash = -1;\n  var dots = 0;\n  var code;\n  for (var i = 0; i <= path.length; ++i) {\n    if (i < path.length)\n      code = path.charCodeAt(i);\n    else if (code === 47 /*/*/)\n      break;\n    else\n      code = 47 /*/*/;\n    if (code === 47 /*/*/) {\n      if (lastSlash === i - 1 || dots === 1) {\n        // NOOP\n      } else if (lastSlash !== i - 1 && dots === 2) {\n        if (res.length < 2 || lastSegmentLength !== 2 || res.charCodeAt(res.length - 1) !== 46 /*.*/ || res.charCodeAt(res.length - 2) !== 46 /*.*/) {\n          if (res.length > 2) {\n            var lastSlashIndex = res.lastIndexOf('/');\n            if (lastSlashIndex !== res.length - 1) {\n              if (lastSlashIndex === -1) {\n                res = '';\n                lastSegmentLength = 0;\n              } else {\n                res = res.slice(0, lastSlashIndex);\n                lastSegmentLength = res.length - 1 - res.lastIndexOf('/');\n              }\n              lastSlash = i;\n              dots = 0;\n              continue;\n            }\n          } else if (res.length === 2 || res.length === 1) {\n            res = '';\n            lastSegmentLength = 0;\n            lastSlash = i;\n            dots = 0;\n            continue;\n          }\n        }\n        if (allowAboveRoot) {\n          if (res.length > 0)\n            res += '/..';\n          else\n            res = '..';\n          lastSegmentLength = 2;\n        }\n      } else {\n        if (res.length > 0)\n          res += '/' + path.slice(lastSlash + 1, i);\n        else\n          res = path.slice(lastSlash + 1, i);\n        lastSegmentLength = i - lastSlash - 1;\n      }\n      lastSlash = i;\n      dots = 0;\n    } else if (code === 46 /*.*/ && dots !== -1) {\n      ++dots;\n    } else {\n      dots = -1;\n    }\n  }\n  return res;\n}\n\nfunction _format(sep, pathObject) {\n  var dir = pathObject.dir || pathObject.root;\n  var base = pathObject.base || (pathObject.name || '') + (pathObject.ext || '');\n  if (!dir) {\n    return base;\n  }\n  if (dir === pathObject.root) {\n    return dir + base;\n  }\n  return dir + sep + base;\n}\n\nvar posix = {\n  // path.resolve([from ...], to)\n  resolve: function resolve() {\n    var resolvedPath = '';\n    var resolvedAbsolute = false;\n    var cwd;\n\n    for (var i = arguments.length - 1; i >= -1 && !resolvedAbsolute; i--) {\n      var path;\n      if (i >= 0)\n        path = arguments[i];\n      else {\n        if (cwd === undefined)\n          cwd = process.cwd();\n        path = cwd;\n      }\n\n      assertPath(path);\n\n      // Skip empty entries\n      if (path.length === 0) {\n        continue;\n      }\n\n      resolvedPath = path + '/' + resolvedPath;\n      resolvedAbsolute = path.charCodeAt(0) === 47 /*/*/;\n    }\n\n    // At this point the path should be resolved to a full absolute path, but\n    // handle relative paths to be safe (might happen when process.cwd() fails)\n\n    // Normalize the path\n    resolvedPath = normalizeStringPosix(resolvedPath, !resolvedAbsolute);\n\n    if (resolvedAbsolute) {\n      if (resolvedPath.length > 0)\n        return '/' + resolvedPath;\n      else\n        return '/';\n    } else if (resolvedPath.length > 0) {\n      return resolvedPath;\n    } else {\n      return '.';\n    }\n  },\n\n  normalize: function normalize(path) {\n    assertPath(path);\n\n    if (path.length === 0) return '.';\n\n    var isAbsolute = path.charCodeAt(0) === 47 /*/*/;\n    var trailingSeparator = path.charCodeAt(path.length - 1) === 47 /*/*/;\n\n    // Normalize the path\n    path = normalizeStringPosix(path, !isAbsolute);\n\n    if (path.length === 0 && !isAbsolute) path = '.';\n    if (path.length > 0 && trailingSeparator) path += '/';\n\n    if (isAbsolute) return '/' + path;\n    return path;\n  },\n\n  isAbsolute: function isAbsolute(path) {\n    assertPath(path);\n    return path.length > 0 && path.charCodeAt(0) === 47 /*/*/;\n  },\n\n  join: function join() {\n    if (arguments.length === 0)\n      return '.';\n    var joined;\n    for (var i = 0; i < arguments.length; ++i) {\n      var arg = arguments[i];\n      assertPath(arg);\n      if (arg.length > 0) {\n        if (joined === undefined)\n          joined = arg;\n        else\n          joined += '/' + arg;\n      }\n    }\n    if (joined === undefined)\n      return '.';\n    return posix.normalize(joined);\n  },\n\n  relative: function relative(from, to) {\n    assertPath(from);\n    assertPath(to);\n\n    if (from === to) return '';\n\n    from = posix.resolve(from);\n    to = posix.resolve(to);\n\n    if (from === to) return '';\n\n    // Trim any leading backslashes\n    var fromStart = 1;\n    for (; fromStart < from.length; ++fromStart) {\n      if (from.charCodeAt(fromStart) !== 47 /*/*/)\n        break;\n    }\n    var fromEnd = from.length;\n    var fromLen = fromEnd - fromStart;\n\n    // Trim any leading backslashes\n    var toStart = 1;\n    for (; toStart < to.length; ++toStart) {\n      if (to.charCodeAt(toStart) !== 47 /*/*/)\n        break;\n    }\n    var toEnd = to.length;\n    var toLen = toEnd - toStart;\n\n    // Compare paths to find the longest common path from root\n    var length = fromLen < toLen ? fromLen : toLen;\n    var lastCommonSep = -1;\n    var i = 0;\n    for (; i <= length; ++i) {\n      if (i === length) {\n        if (toLen > length) {\n          if (to.charCodeAt(toStart + i) === 47 /*/*/) {\n            // We get here if `from` is the exact base path for `to`.\n            // For example: from='/foo/bar'; to='/foo/bar/baz'\n            return to.slice(toStart + i + 1);\n          } else if (i === 0) {\n            // We get here if `from` is the root\n            // For example: from='/'; to='/foo'\n            return to.slice(toStart + i);\n          }\n        } else if (fromLen > length) {\n          if (from.charCodeAt(fromStart + i) === 47 /*/*/) {\n            // We get here if `to` is the exact base path for `from`.\n            // For example: from='/foo/bar/baz'; to='/foo/bar'\n            lastCommonSep = i;\n          } else if (i === 0) {\n            // We get here if `to` is the root.\n            // For example: from='/foo'; to='/'\n            lastCommonSep = 0;\n          }\n        }\n        break;\n      }\n      var fromCode = from.charCodeAt(fromStart + i);\n      var toCode = to.charCodeAt(toStart + i);\n      if (fromCode !== toCode)\n        break;\n      else if (fromCode === 47 /*/*/)\n        lastCommonSep = i;\n    }\n\n    var out = '';\n    // Generate the relative path based on the path difference between `to`\n    // and `from`\n    for (i = fromStart + lastCommonSep + 1; i <= fromEnd; ++i) {\n      if (i === fromEnd || from.charCodeAt(i) === 47 /*/*/) {\n        if (out.length === 0)\n          out += '..';\n        else\n          out += '/..';\n      }\n    }\n\n    // Lastly, append the rest of the destination (`to`) path that comes after\n    // the common path parts\n    if (out.length > 0)\n      return out + to.slice(toStart + lastCommonSep);\n    else {\n      toStart += lastCommonSep;\n      if (to.charCodeAt(toStart) === 47 /*/*/)\n        ++toStart;\n      return to.slice(toStart);\n    }\n  },\n\n  _makeLong: function _makeLong(path) {\n    return path;\n  },\n\n  dirname: function dirname(path) {\n    assertPath(path);\n    if (path.length === 0) return '.';\n    var code = path.charCodeAt(0);\n    var hasRoot = code === 47 /*/*/;\n    var end = -1;\n    var matchedSlash = true;\n    for (var i = path.length - 1; i >= 1; --i) {\n      code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          if (!matchedSlash) {\n            end = i;\n            break;\n          }\n        } else {\n        // We saw the first non-path separator\n        matchedSlash = false;\n      }\n    }\n\n    if (end === -1) return hasRoot ? '/' : '.';\n    if (hasRoot && end === 1) return '//';\n    return path.slice(0, end);\n  },\n\n  basename: function basename(path, ext) {\n    if (ext !== undefined && typeof ext !== 'string') throw new TypeError('\"ext\" argument must be a string');\n    assertPath(path);\n\n    var start = 0;\n    var end = -1;\n    var matchedSlash = true;\n    var i;\n\n    if (ext !== undefined && ext.length > 0 && ext.length <= path.length) {\n      if (ext.length === path.length && ext === path) return '';\n      var extIdx = ext.length - 1;\n      var firstNonSlashEnd = -1;\n      for (i = path.length - 1; i >= 0; --i) {\n        var code = path.charCodeAt(i);\n        if (code === 47 /*/*/) {\n            // If we reached a path separator that was not part of a set of path\n            // separators at the end of the string, stop now\n            if (!matchedSlash) {\n              start = i + 1;\n              break;\n            }\n          } else {\n          if (firstNonSlashEnd === -1) {\n            // We saw the first non-path separator, remember this index in case\n            // we need it if the extension ends up not matching\n            matchedSlash = false;\n            firstNonSlashEnd = i + 1;\n          }\n          if (extIdx >= 0) {\n            // Try to match the explicit extension\n            if (code === ext.charCodeAt(extIdx)) {\n              if (--extIdx === -1) {\n                // We matched the extension, so mark this as the end of our path\n                // component\n                end = i;\n              }\n            } else {\n              // Extension does not match, so our result is the entire path\n              // component\n              extIdx = -1;\n              end = firstNonSlashEnd;\n            }\n          }\n        }\n      }\n\n      if (start === end) end = firstNonSlashEnd;else if (end === -1) end = path.length;\n      return path.slice(start, end);\n    } else {\n      for (i = path.length - 1; i >= 0; --i) {\n        if (path.charCodeAt(i) === 47 /*/*/) {\n            // If we reached a path separator that was not part of a set of path\n            // separators at the end of the string, stop now\n            if (!matchedSlash) {\n              start = i + 1;\n              break;\n            }\n          } else if (end === -1) {\n          // We saw the first non-path separator, mark this as the end of our\n          // path component\n          matchedSlash = false;\n          end = i + 1;\n        }\n      }\n\n      if (end === -1) return '';\n      return path.slice(start, end);\n    }\n  },\n\n  extname: function extname(path) {\n    assertPath(path);\n    var startDot = -1;\n    var startPart = 0;\n    var end = -1;\n    var matchedSlash = true;\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    var preDotState = 0;\n    for (var i = path.length - 1; i >= 0; --i) {\n      var code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          // If we reached a path separator that was not part of a set of path\n          // separators at the end of the string, stop now\n          if (!matchedSlash) {\n            startPart = i + 1;\n            break;\n          }\n          continue;\n        }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === 46 /*.*/) {\n          // If this is our first dot, mark it as the start of our extension\n          if (startDot === -1)\n            startDot = i;\n          else if (preDotState !== 1)\n            preDotState = 1;\n      } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n\n    if (startDot === -1 || end === -1 ||\n        // We saw a non-dot character immediately before the dot\n        preDotState === 0 ||\n        // The (right-most) trimmed path component is exactly '..'\n        preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n      return '';\n    }\n    return path.slice(startDot, end);\n  },\n\n  format: function format(pathObject) {\n    if (pathObject === null || typeof pathObject !== 'object') {\n      throw new TypeError('The \"pathObject\" argument must be of type Object. Received type ' + typeof pathObject);\n    }\n    return _format('/', pathObject);\n  },\n\n  parse: function parse(path) {\n    assertPath(path);\n\n    var ret = { root: '', dir: '', base: '', ext: '', name: '' };\n    if (path.length === 0) return ret;\n    var code = path.charCodeAt(0);\n    var isAbsolute = code === 47 /*/*/;\n    var start;\n    if (isAbsolute) {\n      ret.root = '/';\n      start = 1;\n    } else {\n      start = 0;\n    }\n    var startDot = -1;\n    var startPart = 0;\n    var end = -1;\n    var matchedSlash = true;\n    var i = path.length - 1;\n\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    var preDotState = 0;\n\n    // Get non-dir info\n    for (; i >= start; --i) {\n      code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          // If we reached a path separator that was not part of a set of path\n          // separators at the end of the string, stop now\n          if (!matchedSlash) {\n            startPart = i + 1;\n            break;\n          }\n          continue;\n        }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === 46 /*.*/) {\n          // If this is our first dot, mark it as the start of our extension\n          if (startDot === -1) startDot = i;else if (preDotState !== 1) preDotState = 1;\n        } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n\n    if (startDot === -1 || end === -1 ||\n    // We saw a non-dot character immediately before the dot\n    preDotState === 0 ||\n    // The (right-most) trimmed path component is exactly '..'\n    preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n      if (end !== -1) {\n        if (startPart === 0 && isAbsolute) ret.base = ret.name = path.slice(1, end);else ret.base = ret.name = path.slice(startPart, end);\n      }\n    } else {\n      if (startPart === 0 && isAbsolute) {\n        ret.name = path.slice(1, startDot);\n        ret.base = path.slice(1, end);\n      } else {\n        ret.name = path.slice(startPart, startDot);\n        ret.base = path.slice(startPart, end);\n      }\n      ret.ext = path.slice(startDot, end);\n    }\n\n    if (startPart > 0) ret.dir = path.slice(0, startPart - 1);else if (isAbsolute) ret.dir = '/';\n\n    return ret;\n  },\n\n  sep: '/',\n  delimiter: ':',\n  win32: null,\n  posix: null\n};\n\nposix.posix = posix;\n\nmodule.exports = posix;\n", "/////////////////////////////////////////////////////////////////////////////////\n/* UAParser.js v1.0.38\n   Copyright © 2012-2021 F<PERSON><PERSON> <<EMAIL>>\n   MIT License *//*\n   Detect Browser, Engine, OS, CPU, and Device type/model from User-Agent data.\n   Supports browser & node.js environment. \n   Demo   : https://faisalman.github.io/ua-parser-js\n   Source : https://github.com/faisalman/ua-parser-js */\n/////////////////////////////////////////////////////////////////////////////////\n\n(function (window, undefined) {\n\n    'use strict';\n\n    //////////////\n    // Constants\n    /////////////\n\n\n    var LIBVERSION  = '1.0.38',\n        EMPTY       = '',\n        UNKNOWN     = '?',\n        FUNC_TYPE   = 'function',\n        UNDEF_TYPE  = 'undefined',\n        OBJ_TYPE    = 'object',\n        STR_TYPE    = 'string',\n        MAJOR       = 'major',\n        MODEL       = 'model',\n        NAME        = 'name',\n        TYPE        = 'type',\n        VENDOR      = 'vendor',\n        VERSION     = 'version',\n        ARCHITECTURE= 'architecture',\n        CONSOLE     = 'console',\n        MOBILE      = 'mobile',\n        TABLET      = 'tablet',\n        SMARTTV     = 'smarttv',\n        WEARABLE    = 'wearable',\n        EMBEDDED    = 'embedded',\n        UA_MAX_LENGTH = 500;\n\n    var AMAZON  = 'Amazon',\n        APPLE   = 'Apple',\n        ASUS    = 'ASUS',\n        BLACKBERRY = 'BlackBerry',\n        BROWSER = 'Browser',\n        CHROME  = 'Chrome',\n        EDGE    = 'Edge',\n        FIREFOX = 'Firefox',\n        GOOGLE  = 'Google',\n        HUAWEI  = 'Huawei',\n        LG      = 'LG',\n        MICROSOFT = 'Microsoft',\n        MOTOROLA  = 'Motorola',\n        OPERA   = 'Opera',\n        SAMSUNG = 'Samsung',\n        SHARP   = 'Sharp',\n        SONY    = 'Sony',\n        XIAOMI  = 'Xiaomi',\n        ZEBRA   = 'Zebra',\n        FACEBOOK    = 'Facebook',\n        CHROMIUM_OS = 'Chromium OS',\n        MAC_OS  = 'Mac OS';\n\n    ///////////\n    // Helper\n    //////////\n\n    var extend = function (regexes, extensions) {\n            var mergedRegexes = {};\n            for (var i in regexes) {\n                if (extensions[i] && extensions[i].length % 2 === 0) {\n                    mergedRegexes[i] = extensions[i].concat(regexes[i]);\n                } else {\n                    mergedRegexes[i] = regexes[i];\n                }\n            }\n            return mergedRegexes;\n        },\n        enumerize = function (arr) {\n            var enums = {};\n            for (var i=0; i<arr.length; i++) {\n                enums[arr[i].toUpperCase()] = arr[i];\n            }\n            return enums;\n        },\n        has = function (str1, str2) {\n            return typeof str1 === STR_TYPE ? lowerize(str2).indexOf(lowerize(str1)) !== -1 : false;\n        },\n        lowerize = function (str) {\n            return str.toLowerCase();\n        },\n        majorize = function (version) {\n            return typeof(version) === STR_TYPE ? version.replace(/[^\\d\\.]/g, EMPTY).split('.')[0] : undefined;\n        },\n        trim = function (str, len) {\n            if (typeof(str) === STR_TYPE) {\n                str = str.replace(/^\\s\\s*/, EMPTY);\n                return typeof(len) === UNDEF_TYPE ? str : str.substring(0, UA_MAX_LENGTH);\n            }\n    };\n\n    ///////////////\n    // Map helper\n    //////////////\n\n    var rgxMapper = function (ua, arrays) {\n\n            var i = 0, j, k, p, q, matches, match;\n\n            // loop through all regexes maps\n            while (i < arrays.length && !matches) {\n\n                var regex = arrays[i],       // even sequence (0,2,4,..)\n                    props = arrays[i + 1];   // odd sequence (1,3,5,..)\n                j = k = 0;\n\n                // try matching uastring with regexes\n                while (j < regex.length && !matches) {\n\n                    if (!regex[j]) { break; }\n                    matches = regex[j++].exec(ua);\n\n                    if (!!matches) {\n                        for (p = 0; p < props.length; p++) {\n                            match = matches[++k];\n                            q = props[p];\n                            // check if given property is actually array\n                            if (typeof q === OBJ_TYPE && q.length > 0) {\n                                if (q.length === 2) {\n                                    if (typeof q[1] == FUNC_TYPE) {\n                                        // assign modified match\n                                        this[q[0]] = q[1].call(this, match);\n                                    } else {\n                                        // assign given value, ignore regex match\n                                        this[q[0]] = q[1];\n                                    }\n                                } else if (q.length === 3) {\n                                    // check whether function or regex\n                                    if (typeof q[1] === FUNC_TYPE && !(q[1].exec && q[1].test)) {\n                                        // call function (usually string mapper)\n                                        this[q[0]] = match ? q[1].call(this, match, q[2]) : undefined;\n                                    } else {\n                                        // sanitize match using given regex\n                                        this[q[0]] = match ? match.replace(q[1], q[2]) : undefined;\n                                    }\n                                } else if (q.length === 4) {\n                                        this[q[0]] = match ? q[3].call(this, match.replace(q[1], q[2])) : undefined;\n                                }\n                            } else {\n                                this[q] = match ? match : undefined;\n                            }\n                        }\n                    }\n                }\n                i += 2;\n            }\n        },\n\n        strMapper = function (str, map) {\n\n            for (var i in map) {\n                // check if current value is array\n                if (typeof map[i] === OBJ_TYPE && map[i].length > 0) {\n                    for (var j = 0; j < map[i].length; j++) {\n                        if (has(map[i][j], str)) {\n                            return (i === UNKNOWN) ? undefined : i;\n                        }\n                    }\n                } else if (has(map[i], str)) {\n                    return (i === UNKNOWN) ? undefined : i;\n                }\n            }\n            return str;\n    };\n\n    ///////////////\n    // String map\n    //////////////\n\n    // Safari < 3.0\n    var oldSafariMap = {\n            '1.0'   : '/8',\n            '1.2'   : '/1',\n            '1.3'   : '/3',\n            '2.0'   : '/412',\n            '2.0.2' : '/416',\n            '2.0.3' : '/417',\n            '2.0.4' : '/419',\n            '?'     : '/'\n        },\n        windowsVersionMap = {\n            'ME'        : '4.90',\n            'NT 3.11'   : 'NT3.51',\n            'NT 4.0'    : 'NT4.0',\n            '2000'      : 'NT 5.0',\n            'XP'        : ['NT 5.1', 'NT 5.2'],\n            'Vista'     : 'NT 6.0',\n            '7'         : 'NT 6.1',\n            '8'         : 'NT 6.2',\n            '8.1'       : 'NT 6.3',\n            '10'        : ['NT 6.4', 'NT 10.0'],\n            'RT'        : 'ARM'\n    };\n\n    //////////////\n    // Regex map\n    /////////////\n\n    var regexes = {\n\n        browser : [[\n\n            /\\b(?:crmo|crios)\\/([\\w\\.]+)/i                                      // Chrome for Android/iOS\n            ], [VERSION, [NAME, 'Chrome']], [\n            /edg(?:e|ios|a)?\\/([\\w\\.]+)/i                                       // Microsoft Edge\n            ], [VERSION, [NAME, 'Edge']], [\n\n            // Presto based\n            /(opera mini)\\/([-\\w\\.]+)/i,                                        // Opera Mini\n            /(opera [mobiletab]{3,6})\\b.+version\\/([-\\w\\.]+)/i,                 // Opera Mobi/Tablet\n            /(opera)(?:.+version\\/|[\\/ ]+)([\\w\\.]+)/i                           // Opera\n            ], [NAME, VERSION], [\n            /opios[\\/ ]+([\\w\\.]+)/i                                             // Opera mini on iphone >= 8.0\n            ], [VERSION, [NAME, OPERA+' Mini']], [\n            /\\bop(?:rg)?x\\/([\\w\\.]+)/i                                          // Opera GX\n            ], [VERSION, [NAME, OPERA+' GX']], [\n            /\\bopr\\/([\\w\\.]+)/i                                                 // Opera Webkit\n            ], [VERSION, [NAME, OPERA]], [\n\n            // Mixed\n            /\\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\\/ ]?([\\w\\.]+)/i            // Baidu\n            ], [VERSION, [NAME, 'Baidu']], [\n            /(kindle)\\/([\\w\\.]+)/i,                                             // Kindle\n            /(lunascape|maxthon|netfront|jasmine|blazer)[\\/ ]?([\\w\\.]*)/i,      // Lunascape/Maxthon/Netfront/Jasmine/Blazer\n            // Trident based\n            /(avant|iemobile|slim)\\s?(?:browser)?[\\/ ]?([\\w\\.]*)/i,             // Avant/IEMobile/SlimBrowser\n            /(?:ms|\\()(ie) ([\\w\\.]+)/i,                                         // Internet Explorer\n\n            // Webkit/KHTML based                                               // Flock/RockMelt/Midori/Epiphany/Silk/Skyfire/Bolt/Iron/Iridium/PhantomJS/Bowser/QupZilla/Falkon\n            /(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\\/([-\\w\\.]+)/i,\n                                                                                // Rekonq/Puffin/Brave/Whale/QQBrowserLite/QQ, aka ShouQ\n            /(heytap|ovi)browser\\/([\\d\\.]+)/i,                                  // Heytap/Ovi\n            /(weibo)__([\\d\\.]+)/i                                               // Weibo\n            ], [NAME, VERSION], [\n            /\\bddg\\/([\\w\\.]+)/i                                                 // DuckDuckGo\n            ], [VERSION, [NAME, 'DuckDuckGo']], [\n            /(?:\\buc? ?browser|(?:juc.+)ucweb)[\\/ ]?([\\w\\.]+)/i                 // UCBrowser\n            ], [VERSION, [NAME, 'UC'+BROWSER]], [\n            /microm.+\\bqbcore\\/([\\w\\.]+)/i,                                     // WeChat Desktop for Windows Built-in Browser\n            /\\bqbcore\\/([\\w\\.]+).+microm/i,\n            /micromessenger\\/([\\w\\.]+)/i                                        // WeChat\n            ], [VERSION, [NAME, 'WeChat']], [\n            /konqueror\\/([\\w\\.]+)/i                                             // Konqueror\n            ], [VERSION, [NAME, 'Konqueror']], [\n            /trident.+rv[: ]([\\w\\.]{1,9})\\b.+like gecko/i                       // IE11\n            ], [VERSION, [NAME, 'IE']], [\n            /ya(?:search)?browser\\/([\\w\\.]+)/i                                  // Yandex\n            ], [VERSION, [NAME, 'Yandex']], [\n            /slbrowser\\/([\\w\\.]+)/i                                             // Smart Lenovo Browser\n            ], [VERSION, [NAME, 'Smart Lenovo '+BROWSER]], [\n            /(avast|avg)\\/([\\w\\.]+)/i                                           // Avast/AVG Secure Browser\n            ], [[NAME, /(.+)/, '$1 Secure '+BROWSER], VERSION], [\n            /\\bfocus\\/([\\w\\.]+)/i                                               // Firefox Focus\n            ], [VERSION, [NAME, FIREFOX+' Focus']], [\n            /\\bopt\\/([\\w\\.]+)/i                                                 // Opera Touch\n            ], [VERSION, [NAME, OPERA+' Touch']], [\n            /coc_coc\\w+\\/([\\w\\.]+)/i                                            // Coc Coc Browser\n            ], [VERSION, [NAME, 'Coc Coc']], [\n            /dolfin\\/([\\w\\.]+)/i                                                // Dolphin\n            ], [VERSION, [NAME, 'Dolphin']], [\n            /coast\\/([\\w\\.]+)/i                                                 // Opera Coast\n            ], [VERSION, [NAME, OPERA+' Coast']], [\n            /miuibrowser\\/([\\w\\.]+)/i                                           // MIUI Browser\n            ], [VERSION, [NAME, 'MIUI '+BROWSER]], [\n            /fxios\\/([-\\w\\.]+)/i                                                // Firefox for iOS\n            ], [VERSION, [NAME, FIREFOX]], [\n            /\\bqihu|(qi?ho?o?|360)browser/i                                     // 360\n            ], [[NAME, '360 ' + BROWSER]], [\n            /(oculus|sailfish|huawei|vivo)browser\\/([\\w\\.]+)/i\n            ], [[NAME, /(.+)/, '$1 ' + BROWSER], VERSION], [                    // Oculus/Sailfish/HuaweiBrowser/VivoBrowser\n            /samsungbrowser\\/([\\w\\.]+)/i                                        // Samsung Internet\n            ], [VERSION, [NAME, SAMSUNG + ' Internet']], [\n            /(comodo_dragon)\\/([\\w\\.]+)/i                                       // Comodo Dragon\n            ], [[NAME, /_/g, ' '], VERSION], [\n            /metasr[\\/ ]?([\\d\\.]+)/i                                            // Sogou Explorer\n            ], [VERSION, [NAME, 'Sogou Explorer']], [\n            /(sogou)mo\\w+\\/([\\d\\.]+)/i                                          // Sogou Mobile\n            ], [[NAME, 'Sogou Mobile'], VERSION], [\n            /(electron)\\/([\\w\\.]+) safari/i,                                    // Electron-based App\n            /(tesla)(?: qtcarbrowser|\\/(20\\d\\d\\.[-\\w\\.]+))/i,                   // Tesla\n            /m?(qqbrowser|2345Explorer)[\\/ ]?([\\w\\.]+)/i                        // QQBrowser/2345 Browser\n            ], [NAME, VERSION], [\n            /(lbbrowser)/i,                                                     // LieBao Browser\n            /\\[(linkedin)app\\]/i                                                // LinkedIn App for iOS & Android\n            ], [NAME], [\n\n            // WebView\n            /((?:fban\\/fbios|fb_iab\\/fb4a)(?!.+fbav)|;fbav\\/([\\w\\.]+);)/i       // Facebook App for iOS & Android\n            ], [[NAME, FACEBOOK], VERSION], [\n            /(Klarna)\\/([\\w\\.]+)/i,                                             // Klarna Shopping Browser for iOS & Android\n            /(kakao(?:talk|story))[\\/ ]([\\w\\.]+)/i,                             // Kakao App\n            /(naver)\\(.*?(\\d+\\.[\\w\\.]+).*\\)/i,                                  // Naver InApp\n            /safari (line)\\/([\\w\\.]+)/i,                                        // Line App for iOS\n            /\\b(line)\\/([\\w\\.]+)\\/iab/i,                                        // Line App for Android\n            /(alipay)client\\/([\\w\\.]+)/i,                                       // Alipay\n            /(twitter)(?:and| f.+e\\/([\\w\\.]+))/i,                               // Twitter\n            /(chromium|instagram|snapchat)[\\/ ]([-\\w\\.]+)/i                     // Chromium/Instagram/Snapchat\n            ], [NAME, VERSION], [\n            /\\bgsa\\/([\\w\\.]+) .*safari\\//i                                      // Google Search Appliance on iOS\n            ], [VERSION, [NAME, 'GSA']], [\n            /musical_ly(?:.+app_?version\\/|_)([\\w\\.]+)/i                        // TikTok\n            ], [VERSION, [NAME, 'TikTok']], [\n\n            /headlesschrome(?:\\/([\\w\\.]+)| )/i                                  // Chrome Headless\n            ], [VERSION, [NAME, CHROME+' Headless']], [\n\n            / wv\\).+(chrome)\\/([\\w\\.]+)/i                                       // Chrome WebView\n            ], [[NAME, CHROME+' WebView'], VERSION], [\n\n            /droid.+ version\\/([\\w\\.]+)\\b.+(?:mobile safari|safari)/i           // Android Browser\n            ], [VERSION, [NAME, 'Android '+BROWSER]], [\n\n            /(chrome|omniweb|arora|[tizenoka]{5} ?browser)\\/v?([\\w\\.]+)/i       // Chrome/OmniWeb/Arora/Tizen/Nokia\n            ], [NAME, VERSION], [\n\n            /version\\/([\\w\\.\\,]+) .*mobile\\/\\w+ (safari)/i                      // Mobile Safari\n            ], [VERSION, [NAME, 'Mobile Safari']], [\n            /version\\/([\\w(\\.|\\,)]+) .*(mobile ?safari|safari)/i                // Safari & Safari Mobile\n            ], [VERSION, NAME], [\n            /webkit.+?(mobile ?safari|safari)(\\/[\\w\\.]+)/i                      // Safari < 3.0\n            ], [NAME, [VERSION, strMapper, oldSafariMap]], [\n\n            /(webkit|khtml)\\/([\\w\\.]+)/i\n            ], [NAME, VERSION], [\n\n            // Gecko based\n            /(navigator|netscape\\d?)\\/([-\\w\\.]+)/i                              // Netscape\n            ], [[NAME, 'Netscape'], VERSION], [\n            /mobile vr; rv:([\\w\\.]+)\\).+firefox/i                               // Firefox Reality\n            ], [VERSION, [NAME, FIREFOX+' Reality']], [\n            /ekiohf.+(flow)\\/([\\w\\.]+)/i,                                       // Flow\n            /(swiftfox)/i,                                                      // Swiftfox\n            /(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\\/ ]?([\\w\\.\\+]+)/i,\n                                                                                // IceDragon/Iceweasel/Camino/Chimera/Fennec/Maemo/Minimo/Conkeror/Klar\n            /(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\\/([-\\w\\.]+)$/i,\n                                                                                // Firefox/SeaMonkey/K-Meleon/IceCat/IceApe/Firebird/Phoenix\n            /(firefox)\\/([\\w\\.]+)/i,                                            // Other Firefox-based\n            /(mozilla)\\/([\\w\\.]+) .+rv\\:.+gecko\\/\\d+/i,                         // Mozilla\n\n            // Other\n            /(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\\. ]?browser)[-\\/ ]?v?([\\w\\.]+)/i,\n                                                                                // Polaris/Lynx/Dillo/iCab/Doris/Amaya/w3m/NetSurf/Sleipnir/Obigo/Mosaic/Go/ICE/UP.Browser\n            /(links) \\(([\\w\\.]+)/i,                                             // Links\n            /panasonic;(viera)/i                                                // Panasonic Viera\n            ], [NAME, VERSION], [\n            \n            /(cobalt)\\/([\\w\\.]+)/i                                              // Cobalt\n            ], [NAME, [VERSION, /master.|lts./, \"\"]]\n        ],\n\n        cpu : [[\n\n            /(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\\)]/i                     // AMD64 (x64)\n            ], [[ARCHITECTURE, 'amd64']], [\n\n            /(ia32(?=;))/i                                                      // IA32 (quicktime)\n            ], [[ARCHITECTURE, lowerize]], [\n\n            /((?:i[346]|x)86)[;\\)]/i                                            // IA32 (x86)\n            ], [[ARCHITECTURE, 'ia32']], [\n\n            /\\b(aarch64|arm(v?8e?l?|_?64))\\b/i                                 // ARM64\n            ], [[ARCHITECTURE, 'arm64']], [\n\n            /\\b(arm(?:v[67])?ht?n?[fl]p?)\\b/i                                   // ARMHF\n            ], [[ARCHITECTURE, 'armhf']], [\n\n            // PocketPC mistakenly identified as PowerPC\n            /windows (ce|mobile); ppc;/i\n            ], [[ARCHITECTURE, 'arm']], [\n\n            /((?:ppc|powerpc)(?:64)?)(?: mac|;|\\))/i                            // PowerPC\n            ], [[ARCHITECTURE, /ower/, EMPTY, lowerize]], [\n\n            /(sun4\\w)[;\\)]/i                                                    // SPARC\n            ], [[ARCHITECTURE, 'sparc']], [\n\n            /((?:avr32|ia64(?=;))|68k(?=\\))|\\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\\b|pa-risc)/i\n                                                                                // IA64, 68K, ARM/64, AVR/32, IRIX/64, MIPS/64, SPARC/64, PA-RISC\n            ], [[ARCHITECTURE, lowerize]]\n        ],\n\n        device : [[\n\n            //////////////////////////\n            // MOBILES & TABLETS\n            /////////////////////////\n\n            // Samsung\n            /\\b(sch-i[89]0\\d|shw-m380s|sm-[ptx]\\w{2,4}|gt-[pn]\\d{2,4}|sgh-t8[56]9|nexus 10)/i\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, TABLET]], [\n            /\\b((?:s[cgp]h|gt|sm)-\\w+|sc[g-]?[\\d]+a?|galaxy nexus)/i,\n            /samsung[- ]([-\\w]+)/i,\n            /sec-(sgh\\w+)/i\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, MOBILE]], [\n\n            // Apple\n            /(?:\\/|\\()(ip(?:hone|od)[\\w, ]*)(?:\\/|;)/i                          // iPod/iPhone\n            ], [MODEL, [VENDOR, APPLE], [TYPE, MOBILE]], [\n            /\\((ipad);[-\\w\\),; ]+apple/i,                                       // iPad\n            /applecoremedia\\/[\\w\\.]+ \\((ipad)/i,\n            /\\b(ipad)\\d\\d?,\\d\\d?[;\\]].+ios/i\n            ], [MODEL, [VENDOR, APPLE], [TYPE, TABLET]], [\n            /(macintosh);/i\n            ], [MODEL, [VENDOR, APPLE]], [\n\n            // Sharp\n            /\\b(sh-?[altvz]?\\d\\d[a-ekm]?)/i\n            ], [MODEL, [VENDOR, SHARP], [TYPE, MOBILE]], [\n\n            // Huawei\n            /\\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\\d{2})\\b(?!.+d\\/s)/i\n            ], [MODEL, [VENDOR, HUAWEI], [TYPE, TABLET]], [\n            /(?:huawei|honor)([-\\w ]+)[;\\)]/i,\n            /\\b(nexus 6p|\\w{2,4}e?-[atu]?[ln][\\dx][012359c][adn]?)\\b(?!.+d\\/s)/i\n            ], [MODEL, [VENDOR, HUAWEI], [TYPE, MOBILE]], [\n\n            // Xiaomi\n            /\\b(poco[\\w ]+|m2\\d{3}j\\d\\d[a-z]{2})(?: bui|\\))/i,                  // Xiaomi POCO\n            /\\b; (\\w+) build\\/hm\\1/i,                                           // Xiaomi Hongmi 'numeric' models\n            /\\b(hm[-_ ]?note?[_ ]?(?:\\d\\w)?) bui/i,                             // Xiaomi Hongmi\n            /\\b(redmi[\\-_ ]?(?:note|k)?[\\w_ ]+)(?: bui|\\))/i,                   // Xiaomi Redmi\n            /oid[^\\)]+; (m?[12][0-389][01]\\w{3,6}[c-y])( bui|; wv|\\))/i,        // Xiaomi Redmi 'numeric' models\n            /\\b(mi[-_ ]?(?:a\\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\\d?\\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\\))/i // Xiaomi Mi\n            ], [[MODEL, /_/g, ' '], [VENDOR, XIAOMI], [TYPE, MOBILE]], [\n            /oid[^\\)]+; (2\\d{4}(283|rpbf)[cgl])( bui|\\))/i,                     // Redmi Pad\n            /\\b(mi[-_ ]?(?:pad)(?:[\\w_ ]+))(?: bui|\\))/i                        // Mi Pad tablets\n            ],[[MODEL, /_/g, ' '], [VENDOR, XIAOMI], [TYPE, TABLET]], [\n\n            // OPPO\n            /; (\\w+) bui.+ oppo/i,\n            /\\b(cph[12]\\d{3}|p(?:af|c[al]|d\\w|e[ar])[mt]\\d0|x9007|a101op)\\b/i\n            ], [MODEL, [VENDOR, 'OPPO'], [TYPE, MOBILE]], [\n            /\\b(opd2\\d{3}a?) bui/i\n            ], [MODEL, [VENDOR, 'OPPO'], [TYPE, TABLET]], [\n\n            // Vivo\n            /vivo (\\w+)(?: bui|\\))/i,\n            /\\b(v[12]\\d{3}\\w?[at])(?: bui|;)/i\n            ], [MODEL, [VENDOR, 'Vivo'], [TYPE, MOBILE]], [\n\n            // Realme\n            /\\b(rmx[1-3]\\d{3})(?: bui|;|\\))/i\n            ], [MODEL, [VENDOR, 'Realme'], [TYPE, MOBILE]], [\n\n            // Motorola\n            /\\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\\b[\\w ]+build\\//i,\n            /\\bmot(?:orola)?[- ](\\w*)/i,\n            /((?:moto[\\w\\(\\) ]+|xt\\d{3,4}|nexus 6)(?= bui|\\)))/i\n            ], [MODEL, [VENDOR, MOTOROLA], [TYPE, MOBILE]], [\n            /\\b(mz60\\d|xoom[2 ]{0,2}) build\\//i\n            ], [MODEL, [VENDOR, MOTOROLA], [TYPE, TABLET]], [\n\n            // LG\n            /((?=lg)?[vl]k\\-?\\d{3}) bui| 3\\.[-\\w; ]{10}lg?-([06cv9]{3,4})/i\n            ], [MODEL, [VENDOR, LG], [TYPE, TABLET]], [\n            /(lm(?:-?f100[nv]?|-[\\w\\.]+)(?= bui|\\))|nexus [45])/i,\n            /\\blg[-e;\\/ ]+((?!browser|netcast|android tv)\\w+)/i,\n            /\\blg-?([\\d\\w]+) bui/i\n            ], [MODEL, [VENDOR, LG], [TYPE, MOBILE]], [\n\n            // Lenovo\n            /(ideatab[-\\w ]+)/i,\n            /lenovo ?(s[56]000[-\\w]+|tab(?:[\\w ]+)|yt[-\\d\\w]{6}|tb[-\\d\\w]{6})/i\n            ], [MODEL, [VENDOR, 'Lenovo'], [TYPE, TABLET]], [\n\n            // Nokia\n            /(?:maemo|nokia).*(n900|lumia \\d+)/i,\n            /nokia[-_ ]?([-\\w\\.]*)/i\n            ], [[MODEL, /_/g, ' '], [VENDOR, 'Nokia'], [TYPE, MOBILE]], [\n\n            // Google\n            /(pixel c)\\b/i                                                      // Google Pixel C\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, TABLET]], [\n            /droid.+; (pixel[\\daxl ]{0,6})(?: bui|\\))/i                         // Google Pixel\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, MOBILE]], [\n\n            // Sony\n            /droid.+ (a?\\d[0-2]{2}so|[c-g]\\d{4}|so[-gl]\\w+|xq-a\\w[4-7][12])(?= bui|\\).+chrome\\/(?![1-6]{0,1}\\d\\.))/i\n            ], [MODEL, [VENDOR, SONY], [TYPE, MOBILE]], [\n            /sony tablet [ps]/i,\n            /\\b(?:sony)?sgp\\w+(?: bui|\\))/i\n            ], [[MODEL, 'Xperia Tablet'], [VENDOR, SONY], [TYPE, TABLET]], [\n\n            // OnePlus\n            / (kb2005|in20[12]5|be20[12][59])\\b/i,\n            /(?:one)?(?:plus)? (a\\d0\\d\\d)(?: b|\\))/i\n            ], [MODEL, [VENDOR, 'OnePlus'], [TYPE, MOBILE]], [\n\n            // Amazon\n            /(alexa)webm/i,\n            /(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\\))/i,                             // Kindle Fire without Silk / Echo Show\n            /(kf[a-z]+)( bui|\\)).+silk\\//i                                      // Kindle Fire HD\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, TABLET]], [\n            /((?:sd|kf)[0349hijorstuw]+)( bui|\\)).+silk\\//i                     // Fire Phone\n            ], [[MODEL, /(.+)/g, 'Fire Phone $1'], [VENDOR, AMAZON], [TYPE, MOBILE]], [\n\n            // BlackBerry\n            /(playbook);[-\\w\\),; ]+(rim)/i                                      // BlackBerry PlayBook\n            ], [MODEL, VENDOR, [TYPE, TABLET]], [\n            /\\b((?:bb[a-f]|st[hv])100-\\d)/i,\n            /\\(bb10; (\\w+)/i                                                    // BlackBerry 10\n            ], [MODEL, [VENDOR, BLACKBERRY], [TYPE, MOBILE]], [\n\n            // Asus\n            /(?:\\b|asus_)(transfo[prime ]{4,10} \\w+|eeepc|slider \\w+|nexus 7|padfone|p00[cj])/i\n            ], [MODEL, [VENDOR, ASUS], [TYPE, TABLET]], [\n            / (z[bes]6[027][012][km][ls]|zenfone \\d\\w?)\\b/i\n            ], [MODEL, [VENDOR, ASUS], [TYPE, MOBILE]], [\n\n            // HTC\n            /(nexus 9)/i                                                        // HTC Nexus 9\n            ], [MODEL, [VENDOR, 'HTC'], [TYPE, TABLET]], [\n            /(htc)[-;_ ]{1,2}([\\w ]+(?=\\)| bui)|\\w+)/i,                         // HTC\n\n            // ZTE\n            /(zte)[- ]([\\w ]+?)(?: bui|\\/|\\))/i,\n            /(alcatel|geeksphone|nexian|panasonic(?!(?:;|\\.))|sony(?!-bra))[-_ ]?([-\\w]*)/i         // Alcatel/GeeksPhone/Nexian/Panasonic/Sony\n            ], [VENDOR, [MODEL, /_/g, ' '], [TYPE, MOBILE]], [\n\n            // Acer\n            /droid.+; ([ab][1-7]-?[0178a]\\d\\d?)/i\n            ], [MODEL, [VENDOR, 'Acer'], [TYPE, TABLET]], [\n\n            // Meizu\n            /droid.+; (m[1-5] note) bui/i,\n            /\\bmz-([-\\w]{2,})/i\n            ], [MODEL, [VENDOR, 'Meizu'], [TYPE, MOBILE]], [\n                \n            // Ulefone\n            /; ((?:power )?armor(?:[\\w ]{0,8}))(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Ulefone'], [TYPE, MOBILE]], [\n\n            // MIXED\n            /(blackberry|benq|palm(?=\\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno)[-_ ]?([-\\w]*)/i,\n                                                                                // BlackBerry/BenQ/Palm/Sony-Ericsson/Acer/Asus/Dell/Meizu/Motorola/Polytron\n            /(hp) ([\\w ]+\\w)/i,                                                 // HP iPAQ\n            /(asus)-?(\\w+)/i,                                                   // Asus\n            /(microsoft); (lumia[\\w ]+)/i,                                      // Microsoft Lumia\n            /(lenovo)[-_ ]?([-\\w]+)/i,                                          // Lenovo\n            /(jolla)/i,                                                         // Jolla\n            /(oppo) ?([\\w ]+) bui/i                                             // OPPO\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n\n            /(kobo)\\s(ereader|touch)/i,                                         // Kobo\n            /(archos) (gamepad2?)/i,                                            // Archos\n            /(hp).+(touchpad(?!.+tablet)|tablet)/i,                             // HP TouchPad\n            /(kindle)\\/([\\w\\.]+)/i,                                             // Kindle\n            /(nook)[\\w ]+build\\/(\\w+)/i,                                        // Nook\n            /(dell) (strea[kpr\\d ]*[\\dko])/i,                                   // Dell Streak\n            /(le[- ]+pan)[- ]+(\\w{1,9}) bui/i,                                  // Le Pan Tablets\n            /(trinity)[- ]*(t\\d{3}) bui/i,                                      // Trinity Tablets\n            /(gigaset)[- ]+(q\\w{1,9}) bui/i,                                    // Gigaset Tablets\n            /(vodafone) ([\\w ]+)(?:\\)| bui)/i                                   // Vodafone\n            ], [VENDOR, MODEL, [TYPE, TABLET]], [\n\n            /(surface duo)/i                                                    // Surface Duo\n            ], [MODEL, [VENDOR, MICROSOFT], [TYPE, TABLET]], [\n            /droid [\\d\\.]+; (fp\\du?)(?: b|\\))/i                                 // Fairphone\n            ], [MODEL, [VENDOR, 'Fairphone'], [TYPE, MOBILE]], [\n            /(u304aa)/i                                                         // AT&T\n            ], [MODEL, [VENDOR, 'AT&T'], [TYPE, MOBILE]], [\n            /\\bsie-(\\w*)/i                                                      // Siemens\n            ], [MODEL, [VENDOR, 'Siemens'], [TYPE, MOBILE]], [\n            /\\b(rct\\w+) b/i                                                     // RCA Tablets\n            ], [MODEL, [VENDOR, 'RCA'], [TYPE, TABLET]], [\n            /\\b(venue[\\d ]{2,7}) b/i                                            // Dell Venue Tablets\n            ], [MODEL, [VENDOR, 'Dell'], [TYPE, TABLET]], [\n            /\\b(q(?:mv|ta)\\w+) b/i                                              // Verizon Tablet\n            ], [MODEL, [VENDOR, 'Verizon'], [TYPE, TABLET]], [\n            /\\b(?:barnes[& ]+noble |bn[rt])([\\w\\+ ]*) b/i                       // Barnes & Noble Tablet\n            ], [MODEL, [VENDOR, 'Barnes & Noble'], [TYPE, TABLET]], [\n            /\\b(tm\\d{3}\\w+) b/i\n            ], [MODEL, [VENDOR, 'NuVision'], [TYPE, TABLET]], [\n            /\\b(k88) b/i                                                        // ZTE K Series Tablet\n            ], [MODEL, [VENDOR, 'ZTE'], [TYPE, TABLET]], [\n            /\\b(nx\\d{3}j) b/i                                                   // ZTE Nubia\n            ], [MODEL, [VENDOR, 'ZTE'], [TYPE, MOBILE]], [\n            /\\b(gen\\d{3}) b.+49h/i                                              // Swiss GEN Mobile\n            ], [MODEL, [VENDOR, 'Swiss'], [TYPE, MOBILE]], [\n            /\\b(zur\\d{3}) b/i                                                   // Swiss ZUR Tablet\n            ], [MODEL, [VENDOR, 'Swiss'], [TYPE, TABLET]], [\n            /\\b((zeki)?tb.*\\b) b/i                                              // Zeki Tablets\n            ], [MODEL, [VENDOR, 'Zeki'], [TYPE, TABLET]], [\n            /\\b([yr]\\d{2}) b/i,\n            /\\b(dragon[- ]+touch |dt)(\\w{5}) b/i                                // Dragon Touch Tablet\n            ], [[VENDOR, 'Dragon Touch'], MODEL, [TYPE, TABLET]], [\n            /\\b(ns-?\\w{0,9}) b/i                                                // Insignia Tablets\n            ], [MODEL, [VENDOR, 'Insignia'], [TYPE, TABLET]], [\n            /\\b((nxa|next)-?\\w{0,9}) b/i                                        // NextBook Tablets\n            ], [MODEL, [VENDOR, 'NextBook'], [TYPE, TABLET]], [\n            /\\b(xtreme\\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i                  // Voice Xtreme Phones\n            ], [[VENDOR, 'Voice'], MODEL, [TYPE, MOBILE]], [\n            /\\b(lvtel\\-)?(v1[12]) b/i                                           // LvTel Phones\n            ], [[VENDOR, 'LvTel'], MODEL, [TYPE, MOBILE]], [\n            /\\b(ph-1) /i                                                        // Essential PH-1\n            ], [MODEL, [VENDOR, 'Essential'], [TYPE, MOBILE]], [\n            /\\b(v(100md|700na|7011|917g).*\\b) b/i                               // Envizen Tablets\n            ], [MODEL, [VENDOR, 'Envizen'], [TYPE, TABLET]], [\n            /\\b(trio[-\\w\\. ]+) b/i                                              // MachSpeed Tablets\n            ], [MODEL, [VENDOR, 'MachSpeed'], [TYPE, TABLET]], [\n            /\\btu_(1491) b/i                                                    // Rotor Tablets\n            ], [MODEL, [VENDOR, 'Rotor'], [TYPE, TABLET]], [\n            /(shield[\\w ]+) b/i                                                 // Nvidia Shield Tablets\n            ], [MODEL, [VENDOR, 'Nvidia'], [TYPE, TABLET]], [\n            /(sprint) (\\w+)/i                                                   // Sprint Phones\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n            /(kin\\.[onetw]{3})/i                                                // Microsoft Kin\n            ], [[MODEL, /\\./g, ' '], [VENDOR, MICROSOFT], [TYPE, MOBILE]], [\n            /droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\\)/i             // Zebra\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, TABLET]], [\n            /droid.+; (ec30|ps20|tc[2-8]\\d[kx])\\)/i\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, MOBILE]], [\n\n            ///////////////////\n            // SMARTTVS\n            ///////////////////\n\n            /smart-tv.+(samsung)/i                                              // Samsung\n            ], [VENDOR, [TYPE, SMARTTV]], [\n            /hbbtv.+maple;(\\d+)/i\n            ], [[MODEL, /^/, 'SmartTV'], [VENDOR, SAMSUNG], [TYPE, SMARTTV]], [\n            /(nux; netcast.+smarttv|lg (netcast\\.tv-201\\d|android tv))/i        // LG SmartTV\n            ], [[VENDOR, LG], [TYPE, SMARTTV]], [\n            /(apple) ?tv/i                                                      // Apple TV\n            ], [VENDOR, [MODEL, APPLE+' TV'], [TYPE, SMARTTV]], [\n            /crkey/i                                                            // Google Chromecast\n            ], [[MODEL, CHROME+'cast'], [VENDOR, GOOGLE], [TYPE, SMARTTV]], [\n            /droid.+aft(\\w+)( bui|\\))/i                                         // Fire TV\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, SMARTTV]], [\n            /\\(dtv[\\);].+(aquos)/i,\n            /(aquos-tv[\\w ]+)\\)/i                                               // Sharp\n            ], [MODEL, [VENDOR, SHARP], [TYPE, SMARTTV]],[\n            /(bravia[\\w ]+)( bui|\\))/i                                              // Sony\n            ], [MODEL, [VENDOR, SONY], [TYPE, SMARTTV]], [\n            /(mitv-\\w{5}) bui/i                                                 // Xiaomi\n            ], [MODEL, [VENDOR, XIAOMI], [TYPE, SMARTTV]], [\n            /Hbbtv.*(technisat) (.*);/i                                         // TechniSAT\n            ], [VENDOR, MODEL, [TYPE, SMARTTV]], [\n            /\\b(roku)[\\dx]*[\\)\\/]((?:dvp-)?[\\d\\.]*)/i,                          // Roku\n            /hbbtv\\/\\d+\\.\\d+\\.\\d+ +\\([\\w\\+ ]*; *([\\w\\d][^;]*);([^;]*)/i         // HbbTV devices\n            ], [[VENDOR, trim], [MODEL, trim], [TYPE, SMARTTV]], [\n            /\\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\\b/i                   // SmartTV from Unidentified Vendors\n            ], [[TYPE, SMARTTV]], [\n\n            ///////////////////\n            // CONSOLES\n            ///////////////////\n\n            /(ouya)/i,                                                          // Ouya\n            /(nintendo) ([wids3utch]+)/i                                        // Nintendo\n            ], [VENDOR, MODEL, [TYPE, CONSOLE]], [\n            /droid.+; (shield) bui/i                                            // Nvidia\n            ], [MODEL, [VENDOR, 'Nvidia'], [TYPE, CONSOLE]], [\n            /(playstation [345portablevi]+)/i                                   // Playstation\n            ], [MODEL, [VENDOR, SONY], [TYPE, CONSOLE]], [\n            /\\b(xbox(?: one)?(?!; xbox))[\\); ]/i                                // Microsoft Xbox\n            ], [MODEL, [VENDOR, MICROSOFT], [TYPE, CONSOLE]], [\n\n            ///////////////////\n            // WEARABLES\n            ///////////////////\n\n            /((pebble))app/i                                                    // Pebble\n            ], [VENDOR, MODEL, [TYPE, WEARABLE]], [\n            /(watch)(?: ?os[,\\/]|\\d,\\d\\/)[\\d\\.]+/i                              // Apple Watch\n            ], [MODEL, [VENDOR, APPLE], [TYPE, WEARABLE]], [\n            /droid.+; (glass) \\d/i                                              // Google Glass\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, WEARABLE]], [\n            /droid.+; (wt63?0{2,3})\\)/i\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, WEARABLE]], [\n            /(quest( \\d| pro)?)/i                                               // Oculus Quest\n            ], [MODEL, [VENDOR, FACEBOOK], [TYPE, WEARABLE]], [\n\n            ///////////////////\n            // EMBEDDED\n            ///////////////////\n\n            /(tesla)(?: qtcarbrowser|\\/[-\\w\\.]+)/i                              // Tesla\n            ], [VENDOR, [TYPE, EMBEDDED]], [\n            /(aeobc)\\b/i                                                        // Echo Dot\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, EMBEDDED]], [\n\n            ////////////////////\n            // MIXED (GENERIC)\n            ///////////////////\n\n            /droid .+?; ([^;]+?)(?: bui|; wv\\)|\\) applew).+? mobile safari/i    // Android Phones from Unidentified Vendors\n            ], [MODEL, [TYPE, MOBILE]], [\n            /droid .+?; ([^;]+?)(?: bui|\\) applew).+?(?! mobile) safari/i       // Android Tablets from Unidentified Vendors\n            ], [MODEL, [TYPE, TABLET]], [\n            /\\b((tablet|tab)[;\\/]|focus\\/\\d(?!.+mobile))/i                      // Unidentifiable Tablet\n            ], [[TYPE, TABLET]], [\n            /(phone|mobile(?:[;\\/]| [ \\w\\/\\.]*safari)|pda(?=.+windows ce))/i    // Unidentifiable Mobile\n            ], [[TYPE, MOBILE]], [\n            /(android[-\\w\\. ]{0,9});.+buil/i                                    // Generic Android Device\n            ], [MODEL, [VENDOR, 'Generic']]\n        ],\n\n        engine : [[\n\n            /windows.+ edge\\/([\\w\\.]+)/i                                       // EdgeHTML\n            ], [VERSION, [NAME, EDGE+'HTML']], [\n\n            /webkit\\/537\\.36.+chrome\\/(?!27)([\\w\\.]+)/i                         // Blink\n            ], [VERSION, [NAME, 'Blink']], [\n\n            /(presto)\\/([\\w\\.]+)/i,                                             // Presto\n            /(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\\/([\\w\\.]+)/i, // WebKit/Trident/NetFront/NetSurf/Amaya/Lynx/w3m/Goanna\n            /ekioh(flow)\\/([\\w\\.]+)/i,                                          // Flow\n            /(khtml|tasman|links)[\\/ ]\\(?([\\w\\.]+)/i,                           // KHTML/Tasman/Links\n            /(icab)[\\/ ]([23]\\.[\\d\\.]+)/i,                                      // iCab\n            /\\b(libweb)/i\n            ], [NAME, VERSION], [\n\n            /rv\\:([\\w\\.]{1,9})\\b.+(gecko)/i                                     // Gecko\n            ], [VERSION, NAME]\n        ],\n\n        os : [[\n\n            // Windows\n            /microsoft (windows) (vista|xp)/i                                   // Windows (iTunes)\n            ], [NAME, VERSION], [\n            /(windows (?:phone(?: os)?|mobile))[\\/ ]?([\\d\\.\\w ]*)/i             // Windows Phone\n            ], [NAME, [VERSION, strMapper, windowsVersionMap]], [\n            /windows nt 6\\.2; (arm)/i,                                        // Windows RT\n            /windows[\\/ ]?([ntce\\d\\. ]+\\w)(?!.+xbox)/i,\n            /(?:win(?=3|9|n)|win 9x )([nt\\d\\.]+)/i\n            ], [[VERSION, strMapper, windowsVersionMap], [NAME, 'Windows']], [\n\n            // iOS/macOS\n            /ip[honead]{2,4}\\b(?:.*os ([\\w]+) like mac|; opera)/i,              // iOS\n            /(?:ios;fbsv\\/|iphone.+ios[\\/ ])([\\d\\.]+)/i,\n            /cfnetwork\\/.+darwin/i\n            ], [[VERSION, /_/g, '.'], [NAME, 'iOS']], [\n            /(mac os x) ?([\\w\\. ]*)/i,\n            /(macintosh|mac_powerpc\\b)(?!.+haiku)/i                             // Mac OS\n            ], [[NAME, MAC_OS], [VERSION, /_/g, '.']], [\n\n            // Mobile OSes\n            /droid ([\\w\\.]+)\\b.+(android[- ]x86|harmonyos)/i                    // Android-x86/HarmonyOS\n            ], [VERSION, NAME], [                                               // Android/WebOS/QNX/Bada/RIM/Maemo/MeeGo/Sailfish OS\n            /(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\\/ ]?([\\w\\.]*)/i,\n            /(blackberry)\\w*\\/([\\w\\.]*)/i,                                      // Blackberry\n            /(tizen|kaios)[\\/ ]([\\w\\.]+)/i,                                     // Tizen/KaiOS\n            /\\((series40);/i                                                    // Series 40\n            ], [NAME, VERSION], [\n            /\\(bb(10);/i                                                        // BlackBerry 10\n            ], [VERSION, [NAME, BLACKBERRY]], [\n            /(?:symbian ?os|symbos|s60(?=;)|series60)[-\\/ ]?([\\w\\.]*)/i         // Symbian\n            ], [VERSION, [NAME, 'Symbian']], [\n            /mozilla\\/[\\d\\.]+ \\((?:mobile|tablet|tv|mobile; [\\w ]+); rv:.+ gecko\\/([\\w\\.]+)/i // Firefox OS\n            ], [VERSION, [NAME, FIREFOX+' OS']], [\n            /web0s;.+rt(tv)/i,\n            /\\b(?:hp)?wos(?:browser)?\\/([\\w\\.]+)/i                              // WebOS\n            ], [VERSION, [NAME, 'webOS']], [\n            /watch(?: ?os[,\\/]|\\d,\\d\\/)([\\d\\.]+)/i                              // watchOS\n            ], [VERSION, [NAME, 'watchOS']], [\n\n            // Google Chromecast\n            /crkey\\/([\\d\\.]+)/i                                                 // Google Chromecast\n            ], [VERSION, [NAME, CHROME+'cast']], [\n            /(cros) [\\w]+(?:\\)| ([\\w\\.]+)\\b)/i                                  // Chromium OS\n            ], [[NAME, CHROMIUM_OS], VERSION],[\n\n            // Smart TVs\n            /panasonic;(viera)/i,                                               // Panasonic Viera\n            /(netrange)mmh/i,                                                   // Netrange\n            /(nettv)\\/(\\d+\\.[\\w\\.]+)/i,                                         // NetTV\n\n            // Console\n            /(nintendo|playstation) ([wids345portablevuch]+)/i,                 // Nintendo/Playstation\n            /(xbox); +xbox ([^\\);]+)/i,                                         // Microsoft Xbox (360, One, X, S, Series X, Series S)\n\n            // Other\n            /\\b(joli|palm)\\b ?(?:os)?\\/?([\\w\\.]*)/i,                            // Joli/Palm\n            /(mint)[\\/\\(\\) ]?(\\w*)/i,                                           // Mint\n            /(mageia|vectorlinux)[; ]/i,                                        // Mageia/VectorLinux\n            /([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\\/ ]?(?!chrom|package)([-\\w\\.]*)/i,\n                                                                                // Ubuntu/Debian/SUSE/Gentoo/Arch/Slackware/Fedora/Mandriva/CentOS/PCLinuxOS/RedHat/Zenwalk/Linpus/Raspbian/Plan9/Minix/RISCOS/Contiki/Deepin/Manjaro/elementary/Sabayon/Linspire\n            /(hurd|linux) ?([\\w\\.]*)/i,                                         // Hurd/Linux\n            /(gnu) ?([\\w\\.]*)/i,                                                // GNU\n            /\\b([-frentopcghs]{0,5}bsd|dragonfly)[\\/ ]?(?!amd|[ix346]{1,2}86)([\\w\\.]*)/i, // FreeBSD/NetBSD/OpenBSD/PC-BSD/GhostBSD/DragonFly\n            /(haiku) (\\w+)/i                                                    // Haiku\n            ], [NAME, VERSION], [\n            /(sunos) ?([\\w\\.\\d]*)/i                                             // Solaris\n            ], [[NAME, 'Solaris'], VERSION], [\n            /((?:open)?solaris)[-\\/ ]?([\\w\\.]*)/i,                              // Solaris\n            /(aix) ((\\d)(?=\\.|\\)| )[\\w\\.])*/i,                                  // AIX\n            /\\b(beos|os\\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i, // BeOS/OS2/AmigaOS/MorphOS/OpenVMS/Fuchsia/HP-UX/SerenityOS\n            /(unix) ?([\\w\\.]*)/i                                                // UNIX\n            ], [NAME, VERSION]\n        ]\n    };\n\n    /////////////////\n    // Constructor\n    ////////////////\n\n    var UAParser = function (ua, extensions) {\n\n        if (typeof ua === OBJ_TYPE) {\n            extensions = ua;\n            ua = undefined;\n        }\n\n        if (!(this instanceof UAParser)) {\n            return new UAParser(ua, extensions).getResult();\n        }\n\n        var _navigator = (typeof window !== UNDEF_TYPE && window.navigator) ? window.navigator : undefined;\n        var _ua = ua || ((_navigator && _navigator.userAgent) ? _navigator.userAgent : EMPTY);\n        var _uach = (_navigator && _navigator.userAgentData) ? _navigator.userAgentData : undefined;\n        var _rgxmap = extensions ? extend(regexes, extensions) : regexes;\n        var _isSelfNav = _navigator && _navigator.userAgent == _ua;\n\n        this.getBrowser = function () {\n            var _browser = {};\n            _browser[NAME] = undefined;\n            _browser[VERSION] = undefined;\n            rgxMapper.call(_browser, _ua, _rgxmap.browser);\n            _browser[MAJOR] = majorize(_browser[VERSION]);\n            // Brave-specific detection\n            if (_isSelfNav && _navigator && _navigator.brave && typeof _navigator.brave.isBrave == FUNC_TYPE) {\n                _browser[NAME] = 'Brave';\n            }\n            return _browser;\n        };\n        this.getCPU = function () {\n            var _cpu = {};\n            _cpu[ARCHITECTURE] = undefined;\n            rgxMapper.call(_cpu, _ua, _rgxmap.cpu);\n            return _cpu;\n        };\n        this.getDevice = function () {\n            var _device = {};\n            _device[VENDOR] = undefined;\n            _device[MODEL] = undefined;\n            _device[TYPE] = undefined;\n            rgxMapper.call(_device, _ua, _rgxmap.device);\n            if (_isSelfNav && !_device[TYPE] && _uach && _uach.mobile) {\n                _device[TYPE] = MOBILE;\n            }\n            // iPadOS-specific detection: identified as Mac, but has some iOS-only properties\n            if (_isSelfNav && _device[MODEL] == 'Macintosh' && _navigator && typeof _navigator.standalone !== UNDEF_TYPE && _navigator.maxTouchPoints && _navigator.maxTouchPoints > 2) {\n                _device[MODEL] = 'iPad';\n                _device[TYPE] = TABLET;\n            }\n            return _device;\n        };\n        this.getEngine = function () {\n            var _engine = {};\n            _engine[NAME] = undefined;\n            _engine[VERSION] = undefined;\n            rgxMapper.call(_engine, _ua, _rgxmap.engine);\n            return _engine;\n        };\n        this.getOS = function () {\n            var _os = {};\n            _os[NAME] = undefined;\n            _os[VERSION] = undefined;\n            rgxMapper.call(_os, _ua, _rgxmap.os);\n            if (_isSelfNav && !_os[NAME] && _uach && _uach.platform && _uach.platform != 'Unknown') {\n                _os[NAME] = _uach.platform  \n                                    .replace(/chrome os/i, CHROMIUM_OS)\n                                    .replace(/macos/i, MAC_OS);           // backward compatibility\n            }\n            return _os;\n        };\n        this.getResult = function () {\n            return {\n                ua      : this.getUA(),\n                browser : this.getBrowser(),\n                engine  : this.getEngine(),\n                os      : this.getOS(),\n                device  : this.getDevice(),\n                cpu     : this.getCPU()\n            };\n        };\n        this.getUA = function () {\n            return _ua;\n        };\n        this.setUA = function (ua) {\n            _ua = (typeof ua === STR_TYPE && ua.length > UA_MAX_LENGTH) ? trim(ua, UA_MAX_LENGTH) : ua;\n            return this;\n        };\n        this.setUA(_ua);\n        return this;\n    };\n\n    UAParser.VERSION = LIBVERSION;\n    UAParser.BROWSER =  enumerize([NAME, VERSION, MAJOR]);\n    UAParser.CPU = enumerize([ARCHITECTURE]);\n    UAParser.DEVICE = enumerize([MODEL, VENDOR, TYPE, CONSOLE, MOBILE, SMARTTV, TABLET, WEARABLE, EMBEDDED]);\n    UAParser.ENGINE = UAParser.OS = enumerize([NAME, VERSION]);\n\n    ///////////\n    // Export\n    //////////\n\n    // check js environment\n    if (typeof(exports) !== UNDEF_TYPE) {\n        // nodejs env\n        if (typeof module !== UNDEF_TYPE && module.exports) {\n            exports = module.exports = UAParser;\n        }\n        exports.UAParser = UAParser;\n    } else {\n        // requirejs env (optional)\n        if (typeof(define) === FUNC_TYPE && define.amd) {\n            define(function () {\n                return UAParser;\n            });\n        } else if (typeof window !== UNDEF_TYPE) {\n            // browser env\n            window.UAParser = UAParser;\n        }\n    }\n\n    // jQuery/Zepto specific (optional)\n    // Note:\n    //   In AMD env the global scope should be kept clean, but jQuery is an exception.\n    //   jQuery always exports to global scope, unless jQuery.noConflict(true) is used,\n    //   and we should catch that.\n    var $ = typeof window !== UNDEF_TYPE && (window.jQuery || window.Zepto);\n    if ($ && !$.ua) {\n        var parser = new UAParser();\n        $.ua = parser.getResult();\n        $.ua.get = function () {\n            return parser.getUA();\n        };\n        $.ua.set = function (ua) {\n            parser.setUA(ua);\n            var result = parser.getResult();\n            for (var prop in result) {\n                $.ua[prop] = result[prop];\n            }\n        };\n    }\n\n})(typeof window === 'object' ? window : this);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport EventBus from './EventBus.js';\nimport Events from './events/Events.js';\nimport FactoryMaker from './FactoryMaker.js';\n\nconst LOG_LEVEL_NONE = 0;\nconst LOG_LEVEL_FATAL = 1;\nconst LOG_LEVEL_ERROR = 2;\nconst LOG_LEVEL_WARNING = 3;\nconst LOG_LEVEL_INFO = 4;\nconst LOG_LEVEL_DEBUG = 5;\n\n/**\n * @module Debug\n * @param {object} config\n * @ignore\n */\nfunction Debug(config) {\n\n    config = config || {};\n    const context = this.context;\n    const eventBus = EventBus(context).getInstance();\n    const settings = config.settings;\n\n    const logFn = [];\n\n    let instance,\n        showLogTimestamp,\n        showCalleeName,\n        startTime;\n\n    function setup() {\n        showLogTimestamp = true;\n        showCalleeName = true;\n        startTime = new Date().getTime();\n\n        if (typeof window !== 'undefined' && window.console) {\n            logFn[LOG_LEVEL_FATAL] = getLogFn(window.console.error);\n            logFn[LOG_LEVEL_ERROR] = getLogFn(window.console.error);\n            logFn[LOG_LEVEL_WARNING] = getLogFn(window.console.warn);\n            logFn[LOG_LEVEL_INFO] = getLogFn(window.console.info);\n            logFn[LOG_LEVEL_DEBUG] = getLogFn(window.console.debug);\n        }\n    }\n\n    function getLogFn(fn) {\n        if (fn && fn.bind) {\n            return fn.bind(window.console);\n        }\n        // if not define, return the default function for reporting logs\n        return window.console.log.bind(window.console);\n    }\n\n    /**\n     * Retrieves a logger which can be used to write logging information in browser console.\n     * @param {object} instance Object for which the logger is created. It is used\n     * to include calle object information in log messages.\n     * @memberof module:Debug\n     * @returns {Logger}\n     * @instance\n     */\n    function getLogger(instance) {\n        return {\n            fatal: fatal.bind(instance),\n            error: error.bind(instance),\n            warn: warn.bind(instance),\n            info: info.bind(instance),\n            debug: debug.bind(instance)\n        };\n    }\n\n    /**\n     * Prepends a timestamp in milliseconds to each log message.\n     * @param {boolean} value Set to true if you want to see a timestamp in each log message.\n     * @default LOG_LEVEL_WARNING\n     * @memberof module:Debug\n     * @instance\n     */\n    function setLogTimestampVisible(value) {\n        showLogTimestamp = value;\n    }\n\n    /**\n     * Prepends the callee object name, and media type if available, to each log message.\n     * @param {boolean} value Set to true if you want to see the callee object name and media type in each log message.\n     * @default true\n     * @memberof module:Debug\n     * @instance\n     */\n    function setCalleeNameVisible(value) {\n        showCalleeName = value;\n    }\n\n    function fatal(...params) {\n        doLog(LOG_LEVEL_FATAL, this, ...params);\n    }\n\n    function error(...params) {\n        doLog(LOG_LEVEL_ERROR, this, ...params);\n    }\n\n    function warn(...params) {\n        doLog(LOG_LEVEL_WARNING, this, ...params);\n    }\n\n    function info(...params) {\n        doLog(LOG_LEVEL_INFO, this, ...params);\n    }\n\n    function debug(...params) {\n        doLog(LOG_LEVEL_DEBUG, this, ...params);\n    }\n\n    function doLog(level, _this, ...params) {\n        let message = '';\n        let logTime = null;\n\n        if (showLogTimestamp) {\n            logTime = new Date().getTime();\n            message += '[' + (logTime - startTime) + ']';\n        }\n\n        if (showCalleeName && _this && _this.getClassName) {\n            message += '[' + _this.getClassName() + ']';\n            if (_this.getType) {\n                message += '[' + _this.getType() + ']';\n            }\n        }\n\n        if (message.length > 0) {\n            message += ' ';\n        }\n\n        Array.apply(null, params).forEach(function (item) {\n            message += item + ' ';\n        });\n\n        // log to console if the log level is high enough\n        if (logFn[level] && settings && settings.get().debug.logLevel >= level) {\n            logFn[level](message);\n        }\n\n        // send log event regardless of log level\n        if (settings && settings.get().debug.dispatchEvent) {\n            eventBus.trigger(Events.LOG, { message: message, level: level });\n        }\n    }\n\n    instance = {\n        getLogger: getLogger,\n        setLogTimestampVisible: setLogTimestampVisible,\n        setCalleeNameVisible: setCalleeNameVisible\n    };\n\n    setup();\n\n    return instance;\n}\n\nDebug.__dashjs_factory_name = 'Debug';\n\nconst factory = FactoryMaker.getSingletonFactory(Debug);\nfactory.LOG_LEVEL_NONE = LOG_LEVEL_NONE;\nfactory.LOG_LEVEL_FATAL = LOG_LEVEL_FATAL;\nfactory.LOG_LEVEL_ERROR = LOG_LEVEL_ERROR;\nfactory.LOG_LEVEL_WARNING = LOG_LEVEL_WARNING;\nfactory.LOG_LEVEL_INFO = LOG_LEVEL_INFO;\nfactory.LOG_LEVEL_DEBUG = LOG_LEVEL_DEBUG;\nFactoryMaker.updateSingletonFactory(Debug.__dashjs_factory_name, factory);\nexport default factory;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport FactoryMaker from './FactoryMaker.js';\nimport MediaPlayerEvents from '../streaming/MediaPlayerEvents.js';\n\nconst EVENT_PRIORITY_LOW = 0;\nconst EVENT_PRIORITY_HIGH = 5000;\n\nfunction EventBus() {\n\n    let handlers = {};\n\n    function _commonOn(type, listener, scope, options = {}, executeOnlyOnce = false) {\n\n        if (!type) {\n            throw new Error('event type cannot be null or undefined');\n        }\n        if (!listener || typeof (listener) !== 'function') {\n            throw new Error('listener must be a function: ' + listener);\n        }\n\n        let priority = options.priority || EVENT_PRIORITY_LOW;\n\n        if (getHandlerIdx(type, listener, scope) >= 0) {\n            return;\n        }\n\n        handlers[type] = handlers[type] || [];\n\n        const handler = {\n            callback: listener,\n            scope,\n            priority,\n            executeOnlyOnce\n        };\n\n        if (scope && scope.getStreamId) {\n            handler.streamId = scope.getStreamId();\n        }\n        if (scope && scope.getType) {\n            handler.mediaType = scope.getType();\n        }\n        if (options && options.mode) {\n            handler.mode = options.mode;\n        }\n\n        const inserted = handlers[type].some((item, idx) => {\n            if (item && priority > item.priority) {\n                handlers[type].splice(idx, 0, handler);\n                return true;\n            }\n        });\n\n        if (!inserted) {\n            handlers[type].push(handler);\n        }\n    }\n\n    function on(type, listener, scope, options = {}) {\n        _commonOn(type, listener, scope, options);\n    }\n\n    function once(type, listener, scope, options = {}) {\n        _commonOn(type, listener, scope, options, true)\n    }\n\n    function off(type, listener, scope) {\n        if (!type || !listener || !handlers[type]) {\n            return;\n        }\n        const idx = getHandlerIdx(type, listener, scope);\n        if (idx < 0) {\n            return;\n        }\n        handlers[type][idx] = null;\n    }\n\n    function trigger(type, payload = {}, filters = {}) {\n        if (!type || !handlers[type]) {\n            return;\n        }\n\n        payload = payload || {};\n\n        if (payload.hasOwnProperty('type')) {\n            throw new Error('\\'type\\' is a reserved word for event dispatching');\n        }\n\n        payload.type = type;\n\n        if (filters.streamId) {\n            payload.streamId = filters.streamId;\n        }\n        if (filters.mediaType) {\n            payload.mediaType = filters.mediaType;\n        }\n\n        const handlersToRemove = [];\n        handlers[type]\n            .filter((handler) => {\n                if (!handler) {\n                    return false;\n                }\n                if (filters.streamId && handler.streamId && handler.streamId !== filters.streamId) {\n                    return false;\n                }\n                if (filters.mediaType && handler.mediaType && handler.mediaType !== filters.mediaType) {\n                    return false;\n                }\n                // This is used for dispatching DASH events. By default we use the onStart mode. Consequently we filter everything that has a non matching mode and the onReceive events for handlers that did not specify a mode.\n                if ((filters.mode && handler.mode && handler.mode !== filters.mode) || (!handler.mode && filters.mode && filters.mode === MediaPlayerEvents.EVENT_MODE_ON_RECEIVE)) {\n                    return false;\n                }\n                return true;\n            })\n            .forEach((handler) => {\n                handler && handler.callback.call(handler.scope, payload);\n                if (handler.executeOnlyOnce) {\n                    handlersToRemove.push(handler);\n                }\n            });\n\n        handlersToRemove.forEach((handler) => {\n            off(type, handler.callback, handler.scope);\n        })\n    }\n\n    function getHandlerIdx(type, listener, scope) {\n\n        let idx = -1;\n\n        if (!handlers[type]) {\n            return idx;\n        }\n\n        handlers[type].some((item, index) => {\n            if (item && item.callback === listener && (!scope || scope === item.scope)) {\n                idx = index;\n                return true;\n            }\n        });\n        return idx;\n    }\n\n    function reset() {\n        handlers = {};\n    }\n\n    const instance = {\n        on,\n        once,\n        off,\n        trigger,\n        reset\n    };\n\n    return instance;\n}\n\nEventBus.__dashjs_factory_name = 'EventBus';\nconst factory = FactoryMaker.getSingletonFactory(EventBus);\nfactory.EVENT_PRIORITY_LOW = EVENT_PRIORITY_LOW;\nfactory.EVENT_PRIORITY_HIGH = EVENT_PRIORITY_HIGH;\nFactoryMaker.updateSingletonFactory(EventBus.__dashjs_factory_name, factory);\nexport default factory;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @module FactoryMaker\n * @ignore\n */\nconst FactoryMaker = (function () {\n\n    let instance;\n    let singletonContexts = [];\n    const singletonFactories = {};\n    const classFactories = {};\n\n    function extend(name, childInstance, override, context) {\n        if (!context[name] && childInstance) {\n            context[name] = {\n                instance: childInstance,\n                override: override\n            };\n        }\n    }\n\n    /**\n     * Use this method from your extended object.  this.factory is injected into your object.\n     * this.factory.getSingletonInstance(this.context, 'VideoModel')\n     * will return the video model for use in the extended object.\n     *\n     * @param {Object} context - injected into extended object as this.context\n     * @param {string} className - string name found in all dash.js objects\n     * with name __dashjs_factory_name Will be at the bottom. Will be the same as the object's name.\n     * @returns {*} Context aware instance of specified singleton name.\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function getSingletonInstance(context, className) {\n        for (const i in singletonContexts) {\n            const obj = singletonContexts[i];\n            if (obj.context === context && obj.name === className) {\n                return obj.instance;\n            }\n        }\n        return null;\n    }\n\n    /**\n     * Use this method to add an singleton instance to the system.  Useful for unit testing to mock objects etc.\n     *\n     * @param {Object} context\n     * @param {string} className\n     * @param {Object} instance\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function setSingletonInstance(context, className, instance) {\n        for (const i in singletonContexts) {\n            const obj = singletonContexts[i];\n            if (obj.context === context && obj.name === className) {\n                singletonContexts[i].instance = instance;\n                return;\n            }\n        }\n        singletonContexts.push({\n            name: className,\n            context: context,\n            instance: instance\n        });\n    }\n\n    /**\n     * Use this method to remove all singleton instances associated with a particular context.\n     *\n     * @param {Object} context\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function deleteSingletonInstances(context) {\n        singletonContexts = singletonContexts.filter(x => x.context !== context);\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Factories storage Management\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function getFactoryByName(name, factoriesArray) {\n        return factoriesArray[name];\n    }\n\n    function updateFactory(name, factory, factoriesArray) {\n        if (name in factoriesArray) {\n            factoriesArray[name] = factory;\n        }\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Class Factories Management\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function updateClassFactory(name, factory) {\n        updateFactory(name, factory, classFactories);\n    }\n\n    function getClassFactoryByName(name) {\n        return getFactoryByName(name, classFactories);\n    }\n\n    function getClassFactory(classConstructor) {\n        let factory = getFactoryByName(classConstructor.__dashjs_factory_name, classFactories);\n\n        if (!factory) {\n            factory = function (context) {\n                if (context === undefined) {\n                    context = {};\n                }\n                return {\n                    create: function () {\n                        return merge(classConstructor, context, arguments);\n                    }\n                };\n            };\n\n            classFactories[classConstructor.__dashjs_factory_name] = factory; // store factory\n        }\n        return factory;\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Singleton Factory MAangement\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function updateSingletonFactory(name, factory) {\n        updateFactory(name, factory, singletonFactories);\n    }\n\n    function getSingletonFactoryByName(name) {\n        return getFactoryByName(name, singletonFactories);\n    }\n\n    function getSingletonFactory(classConstructor) {\n        let factory = getFactoryByName(classConstructor.__dashjs_factory_name, singletonFactories);\n        if (!factory) {\n            factory = function (context) {\n                let instance;\n                if (context === undefined) {\n                    context = {};\n                }\n                return {\n                    getInstance: function () {\n                        // If we don't have an instance yet check for one on the context\n                        if (!instance) {\n                            instance = getSingletonInstance(context, classConstructor.__dashjs_factory_name);\n                        }\n                        // If there's no instance on the context then create one\n                        if (!instance) {\n                            instance = merge(classConstructor, context, arguments);\n                            singletonContexts.push({\n                                name: classConstructor.__dashjs_factory_name,\n                                context: context,\n                                instance: instance\n                            });\n                        }\n                        return instance;\n                    }\n                };\n            };\n            singletonFactories[classConstructor.__dashjs_factory_name] = factory; // store factory\n        }\n\n        return factory;\n    }\n\n    function merge(classConstructor, context, args) {\n\n        let classInstance;\n        const className = classConstructor.__dashjs_factory_name;\n        const extensionObject = context[className];\n\n        if (extensionObject) {\n\n            let extension = extensionObject.instance;\n\n            if (extensionObject.override) { //Override public methods in parent but keep parent.\n\n                classInstance = classConstructor.apply({context}, args);\n                extension = extension.apply({\n                    context,\n                    factory: instance,\n                    parent: classInstance\n                }, args);\n\n                for (const prop in extension) {\n                    if (classInstance.hasOwnProperty(prop)) {\n                        classInstance[prop] = extension[prop];\n                    }\n                }\n\n            } else { //replace parent object completely with new object. Same as dijon.\n\n                return extension.apply({\n                    context,\n                    factory: instance\n                }, args);\n\n            }\n        } else {\n            // Create new instance of the class\n            classInstance = classConstructor.apply({context}, args);\n        }\n\n        // Add getClassName function to class instance prototype (used by Debug)\n        classInstance.getClassName = function () {return className;};\n\n        return classInstance;\n    }\n\n    instance = {\n        extend: extend,\n        getSingletonInstance: getSingletonInstance,\n        setSingletonInstance: setSingletonInstance,\n        deleteSingletonInstances: deleteSingletonInstances,\n        getSingletonFactory: getSingletonFactory,\n        getSingletonFactoryByName: getSingletonFactoryByName,\n        updateSingletonFactory: updateSingletonFactory,\n        getClassFactory: getClassFactory,\n        getClassFactoryByName: getClassFactoryByName,\n        updateClassFactory: updateClassFactory\n    };\n\n    return instance;\n\n}());\n\nexport default FactoryMaker;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport FactoryMaker from './FactoryMaker.js';\nimport Utils from './Utils.js';\nimport Debug from '../core/Debug.js';\nimport Constants from '../streaming/constants/Constants.js';\nimport {HTTPRequest} from '../streaming/vo/metrics/HTTPRequest.js';\nimport EventBus from './EventBus.js';\nimport Events from './events/Events.js';\n\n/** @module Settings\n * @description Define the configuration parameters of Dash.js MediaPlayer.\n * @see {@link module:Settings~PlayerSettings PlayerSettings} for further information about the supported configuration properties.\n */\n\n\n/**\n * @typedef {Object} PlayerSettings\n * @property {module:Settings~DebugSettings} [debug]\n * Debug related settings.\n * @property {module:Settings~ErrorSettings} [errors]\n * Error related settings\n * @property {module:Settings~StreamingSettings} [streaming]\n * Streaming related settings.\n * @example\n *\n * // Full settings object\n * settings = {\n *        debug: {\n *            logLevel: Debug.LOG_LEVEL_WARNING,\n *            dispatchEvent: false\n *        },\n *        streaming: {\n *            abandonLoadTimeout: 10000,\n *            wallclockTimeUpdateInterval: 100,\n *            manifestUpdateRetryInterval: 100,\n *            liveUpdateTimeThresholdInMilliseconds: 0,\n *            cacheInitSegments: false,\n *            applyServiceDescription: true,\n *            applyProducerReferenceTime: true,\n *            applyContentSteering: true,\n *            enableManifestDurationMismatchFix: true,\n *            parseInbandPrft: false,\n *            enableManifestTimescaleMismatchFix: false,\n *            capabilities: {\n *               filterUnsupportedEssentialProperties: true,\n *               supportedEssentialProperties: [\n *                   { schemeIdUri: Constants.FONT_DOWNLOAD_DVB_SCHEME },\n *                   { schemeIdUri: Constants.COLOUR_PRIMARIES_SCHEME_ID_URI, value: /1|5|6|7/ },\n *                   { schemeIdUri: Constants.URL_QUERY_INFO_SCHEME },\n *                   { schemeIdUri: Constants.EXT_URL_QUERY_INFO_SCHEME },\n *                   { schemeIdUri: Constants.MATRIX_COEFFICIENTS_SCHEME_ID_URI, value: /0|1|5|6/ },\n *                   { schemeIdUri: Constants.TRANSFER_CHARACTERISTICS_SCHEME_ID_URI, value: /1|6|13|14|15/ },\n *                   ...Constants.THUMBNAILS_SCHEME_ID_URIS.map(ep => { return { 'schemeIdUri': ep }; })\n *               ],\n *               useMediaCapabilitiesApi: true,\n *               filterVideoColorimetryEssentialProperties: false,\n *               filterHDRMetadataFormatEssentialProperties: false\n *            },\n *            events: {\n *              eventControllerRefreshDelay: 100,\n *              deleteEventMessageDataTimeout: 10000\n *            }\n *            timeShiftBuffer: {\n *                calcFromSegmentTimeline: false,\n *                fallbackToSegmentTimeline: true\n *            },\n *            metrics: {\n *              maxListDepth: 100\n *            },\n *            delay: {\n *                liveDelayFragmentCount: NaN,\n *                liveDelay: NaN,\n *                useSuggestedPresentationDelay: true\n *            },\n *            protection: {\n *                keepProtectionMediaKeys: false,\n *                ignoreEmeEncryptedEvent: false,\n *                detectPlayreadyMessageFormat: true,\n *                ignoreKeyStatuses: false\n *            },\n *            buffer: {\n *                enableSeekDecorrelationFix: false,\n *                fastSwitchEnabled: true,\n *                flushBufferAtTrackSwitch: false,\n *                reuseExistingSourceBuffers: true,\n *                bufferPruningInterval: 10,\n *                bufferToKeep: 20,\n *                bufferTimeAtTopQuality: 30,\n *                bufferTimeAtTopQualityLongForm: 60,\n *                initialBufferLevel: NaN,\n *                bufferTimeDefault: 18,\n *                longFormContentDurationThreshold: 600,\n *                stallThreshold: 0.3,\n *                lowLatencyStallThreshold: 0.3,\n *                useAppendWindow: true,\n *                setStallState: true,\n *                avoidCurrentTimeRangePruning: false,\n *                useChangeType: true,\n *                mediaSourceDurationInfinity: true,\n *                resetSourceBuffersForTrackSwitch: false,\n *                syntheticStallEvents: {\n *                    enabled: false,\n *                    ignoreReadyState: false\n *                }\n *            },\n *            gaps: {\n *                jumpGaps: true,\n *                jumpLargeGaps: true,\n *                smallGapLimit: 1.5,\n *                threshold: 0.3,\n *                enableSeekFix: true,\n *                enableStallFix: false,\n *                stallSeek: 0.1\n *            },\n *            utcSynchronization: {\n *                enabled: true,\n *                useManifestDateHeaderTimeSource: true,\n *                backgroundAttempts: 2,\n *                timeBetweenSyncAttempts: 30,\n *                maximumTimeBetweenSyncAttempts: 600,\n *                minimumTimeBetweenSyncAttempts: 2,\n *                timeBetweenSyncAttemptsAdjustmentFactor: 2,\n *                maximumAllowedDrift: 100,\n *                enableBackgroundSyncAfterSegmentDownloadError: true,\n *                defaultTimingSource: {\n *                    scheme: 'urn:mpeg:dash:utc:http-xsdate:2014',\n *                    value: 'http://time.akamai.com/?iso&ms'\n *                }\n *            },\n *            scheduling: {\n *                defaultTimeout: 500,\n *                lowLatencyTimeout: 0,\n *                scheduleWhilePaused: true\n *            },\n *            text: {\n *                defaultEnabled: true,\n *                dispatchForManualRendering: false,\n *                extendSegmentedCues: true,\n *                imsc: {\n *                    displayForcedOnlyMode: false,\n *                    enableRollUp: true\n *                },\n *                webvtt: {\n *                    customRenderingEnabled: false\n *                }\n *            },\n *            liveCatchup: {\n *                maxDrift: NaN,\n *                playbackRate: {min: NaN, max: NaN},\n *                playbackBufferMin: 0.5,\n *                enabled: null,\n *                mode: Constants.LIVE_CATCHUP_MODE_DEFAULT\n *            },\n *            lastBitrateCachingInfo: { enabled: true, ttl: 360000 },\n *            lastMediaSettingsCachingInfo: { enabled: true, ttl: 360000 },\n *            saveLastMediaSettingsForCurrentStreamingSession: true,\n *            cacheLoadThresholds: { video: 10, audio: 5 },\n *            trackSwitchMode: {\n *                audio: Constants.TRACK_SWITCH_MODE_ALWAYS_REPLACE,\n *                video: Constants.TRACK_SWITCH_MODE_NEVER_REPLACE\n *            },\n *            ignoreSelectionPriority: false,\n *            prioritizeRoleMain: true,\n *            assumeDefaultRoleAsMain: true,\n *            selectionModeForInitialTrack: Constants.TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY,\n *            fragmentRequestTimeout: 20000,\n *            fragmentRequestProgressTimeout: -1,\n *            manifestRequestTimeout: 10000,\n *            retryIntervals: {\n *                [HTTPRequest.MPD_TYPE]: 500,\n *                [HTTPRequest.XLINK_EXPANSION_TYPE]: 500,\n *                [HTTPRequest.MEDIA_SEGMENT_TYPE]: 1000,\n *                [HTTPRequest.INIT_SEGMENT_TYPE]: 1000,\n *                [HTTPRequest.BITSTREAM_SWITCHING_SEGMENT_TYPE]: 1000,\n *                [HTTPRequest.INDEX_SEGMENT_TYPE]: 1000,\n *                [HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE]: 1000,\n *                [HTTPRequest.LICENSE]: 1000,\n *                [HTTPRequest.OTHER_TYPE]: 1000,\n *                lowLatencyReductionFactor: 10\n *            },\n *            retryAttempts: {\n *                [HTTPRequest.MPD_TYPE]: 3,\n *                [HTTPRequest.XLINK_EXPANSION_TYPE]: 1,\n *                [HTTPRequest.MEDIA_SEGMENT_TYPE]: 3,\n *                [HTTPRequest.INIT_SEGMENT_TYPE]: 3,\n *                [HTTPRequest.BITSTREAM_SWITCHING_SEGMENT_TYPE]: 3,\n *                [HTTPRequest.INDEX_SEGMENT_TYPE]: 3,\n *                [HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE]: 3,\n *                [HTTPRequest.LICENSE]: 3,\n *                [HTTPRequest.OTHER_TYPE]: 3,\n *                lowLatencyMultiplyFactor: 5\n *            },\n *             abr: {\n *                 limitBitrateByPortal: false,\n *                 usePixelRatioInLimitBitrateByPortal: false,\n *                rules: {\n *                     throughputRule: {\n *                         active: true\n *                     },\n *                     bolaRule: {\n *                         active: true\n *                     },\n *                     insufficientBufferRule: {\n *                         active: true,\n *                         parameters: {\n *                             throughputSafetyFactor: 0.7,\n *                             segmentIgnoreCount: 2\n *                         }\n *                     },\n *                     switchHistoryRule: {\n *                         active: true,\n *                         parameters: {\n *                             sampleSize: 8,\n *                             switchPercentageThreshold: 0.075\n *                         }\n *                     },\n *                     droppedFramesRule: {\n *                         active: true,\n *                         parameters: {\n *                             minimumSampleSize: 375,\n *                             droppedFramesPercentageThreshold: 0.15\n *                         }\n *                     },\n *                     abandonRequestsRule: {\n *                         active: true,\n *                         parameters: {\n *                             abandonDurationMultiplier: 1.8,\n *                             minSegmentDownloadTimeThresholdInMs: 500,\n *                             minThroughputSamplesThreshold: 6\n *                         }\n *                     },\n *                     l2ARule: {\n *                         active: false\n *                     },\n *                     loLPRule: {\n *                         active: false\n *                     }\n *                 },\n *                 throughput: {\n *                     averageCalculationMode: Constants.THROUGHPUT_CALCULATION_MODES.EWMA,\n *                     lowLatencyDownloadTimeCalculationMode: Constants.LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE.MOOF_PARSING,\n *                     useResourceTimingApi: true,\n *                     useNetworkInformationApi: {\n *                         xhr: false,\n *                         fetch: false\n *                     },\n *                     useDeadTimeLatency: true,\n *                     bandwidthSafetyFactor: 0.9,\n *                     sampleSettings: {\n *                         live: 3,\n *                         vod: 4,\n *                         enableSampleSizeAdjustment: true,\n *                         decreaseScale: 0.7,\n *                         increaseScale: 1.3,\n *                         maxMeasurementsToKeep: 20,\n *                         averageLatencySampleAmount: 4,\n *                     },\n *                     ewma: {\n *                         throughputSlowHalfLifeSeconds: 8,\n *                         throughputFastHalfLifeSeconds: 3,\n *                         latencySlowHalfLifeCount: 2,\n *                         latencyFastHalfLifeCount: 1,\n *                         weightDownloadTimeMultiplicationFactor: 0.0015\n *                     }\n *                 },\n *                 maxBitrate: {\n *                     audio: -1,\n *                     video: -1\n *                 },\n *                 minBitrate: {\n *                     audio: -1,\n *                     video: -1\n *                 },\n *                 initialBitrate: {\n *                     audio: -1,\n *                     video: -1\n *                 },\n *                 autoSwitchBitrate: {\n *                     audio: true,\n *                     video: true\n *                 }\n *             },\n *            cmcd: {\n *                enabled: false,\n *                sid: null,\n *                cid: null,\n *                rtp: null,\n *                rtpSafetyFactor: 5,\n *                mode: Constants.CMCD_MODE_QUERY,\n *                enabledKeys: ['br', 'd', 'ot', 'tb' , 'bl', 'dl', 'mtp', 'nor', 'nrr', 'su' , 'bs', 'rtp' , 'cid', 'pr', 'sf', 'sid', 'st', 'v']\n *                includeInRequests: ['segment', 'mpd'],\n *                version: 1\n *            },\n *            cmsd: {\n *                enabled: false,\n *                abr: {\n *                    applyMb: false,\n *                    etpWeightRatio: 0\n *                }\n *            },\n *            defaultSchemeIdUri: {\n *                viewpoint: '',\n *                audioChannelConfiguration: 'urn:mpeg:mpegB:cicp:ChannelConfiguration',\n *                role: 'urn:mpeg:dash:role:2011',\n *                accessibility: 'urn:mpeg:dash:role:2011'\n *            }\n *          },\n *          errors: {\n *            recoverAttempts: {\n *                mediaErrorDecode: 5\n *             }\n *          }\n * }\n */\n\n/**\n * @typedef {Object} TimeShiftBuffer\n * @property {boolean} [calcFromSegmentTimeline=false]\n * Enable calculation of the DVR window for SegmentTimeline manifests based on the entries in \\<SegmentTimeline\\>.\n *  * @property {boolean} [fallbackToSegmentTimeline=true]\n * In case the MPD uses \\<SegmentTimeline\\ and no segment is found within the DVR window the DVR window is calculated based on the entries in \\<SegmentTimeline\\>.\n */\n\n/**\n * @typedef {Object} EventSettings\n * @property {number} [eventControllerRefreshDelay=100]\n * Interval timer used by the EventController to check if events need to be triggered or removed.\n * @property {number} [deleteEventMessageDataTimeout=10000]\n * If this value is larger than -1 the EventController will delete the message data attributes of events after they have been started and dispatched to the application.\n * This is to save memory in case events have a long duration and need to be persisted in the EventController.\n * This parameter defines the time in milliseconds between the start of an event and when the message data is deleted.\n * If an event is dispatched for the second time (e.g. when the user seeks back) the message data will not be included in the dispatched event if it has been deleted already.\n * Set this value to -1 to not delete any message data.\n */\n\n/**\n * @typedef {Object} LiveDelay\n * @property {number} [liveDelayFragmentCount=NaN]\n * Changing this value will lower or increase live stream latency.\n *\n * The detected segment duration will be multiplied by this value to define a time in seconds to delay a live stream from the live edge.\n *\n * Lowering this value will lower latency but may decrease the player's ability to build a stable buffer.\n * @property {number} [liveDelay=NaN]\n * Equivalent in seconds of setLiveDelayFragmentCount.\n *\n * Lowering this value will lower latency but may decrease the player's ability to build a stable buffer.\n *\n * This value should be less than the manifest duration by a couple of segment durations to avoid playback issues.\n *\n * If set, this parameter will take precedence over setLiveDelayFragmentCount and manifest info.\n * @property {boolean} [useSuggestedPresentationDelay=true]\n * Set to true if you would like to overwrite the default live delay and honor the SuggestedPresentationDelay attribute in by the manifest.\n */\n\n/**\n * @typedef {Object} Buffer\n * @property {boolean} [enableSeekDecorrelationFix=false]\n * Enables a workaround for playback start on some devices, e.g. WebOS 4.9.\n * It is necessary because some browsers do not support setting currentTime on video element to a value that is outside of current buffer.\n *\n * If you experience unexpected seeking triggered by BufferController, you can try setting this value to false.\n\n * @property {boolean} [fastSwitchEnabled=true]\n * When enabled, after an ABR up-switch in quality, instead of requesting and appending the next fragment at the end of the current buffer range it is requested and appended closer to the current time.\n *\n * When enabled, The maximum time to render a higher quality is current time + (1.5 * fragment duration).\n *\n * Note, When ABR down-switch is detected, we appended the lower quality at the end of the buffer range to preserve the\n * higher quality media for as long as possible.\n *\n * If enabled, it should be noted there are a few cases when the client will not replace inside buffer range but rather just append at the end.\n * 1. When the buffer level is less than one fragment duration.\n * 2. The client is in an Abandonment State due to recent fragment abandonment event.\n *\n * Known issues:\n * 1. In IE11 with auto switching off, if a user switches to a quality they can not download in time the fragment may be appended in the same range as the playhead or even in the past, in IE11 it may cause a stutter or stall in playback.\n * @property {boolean} [flushBufferAtTrackSwitch=false]\n * When enabled, after a track switch and in case buffer is being replaced, the video element is flushed (seek at current playback time) once a segment of the new track is appended in buffer in order to force video decoder to play new track.\n *\n * This can be required on some devices like GoogleCast devices to make track switching functional.\n *\n * Otherwise, track switching will be effective only once after previous buffered track is fully consumed.\n * @property {boolean} [reuseExistingSourceBuffers=true]\n * Enable reuse of existing MediaSource Sourcebuffers during period transition.\n * @property {number} [bufferPruningInterval=10]\n * The interval of pruning buffer in seconds.\n * @property {number} [bufferToKeep=20]\n * This value influences the buffer pruning logic.\n *\n * Allows you to modify the buffer that is kept in source buffer in seconds.\n * 0|-----------bufferToPrune-----------|-----bufferToKeep-----|currentTime|\n * @property {number} [bufferTimeDefault=18]\n * The time that the internal buffer target will be set to when not playing at the top quality.\n * @property {number} [bufferTimeAtTopQuality=30]\n * The time that the internal buffer target will be set to once playing the top quality.\n *\n * If there are multiple bitrates in your adaptation, and the media is playing at the highest bitrate, then we try to build a larger buffer at the top quality to increase stability and to maintain media quality.\n * @property {number} [bufferTimeAtTopQualityLongForm=60]\n * The time that the internal buffer target will be set to once playing the top quality for long form content.\n * @property {number} [longFormContentDurationThreshold=600]\n * The threshold which defines if the media is considered long form content.\n *\n * This will directly affect the buffer targets when playing back at the top quality.\n * @property {number} [initialBufferLevel=NaN]\n * Initial buffer level before playback starts\n * @property {number} [stallThreshold=0.3]\n * Stall threshold used in BufferController.js to determine whether a track should still be changed and which buffer range to prune.\n * @property {number} [lowLatencyStallThreshold=0.3]\n * Low Latency stall threshold used in BufferController.js to determine whether a track should still be changed and which buffer range to prune.\n * @property {boolean} [useAppendWindow=true]\n * Specifies if the appendWindow attributes of the MSE SourceBuffers should be set according to content duration from manifest.\n * @property {boolean} [setStallState=true]\n * Specifies if we fire manual waiting events once the stall threshold is reached.\n * @property {module:Settings~SyntheticStallSettings} [syntheticStallEvents]\n * Specifies if manual stall events are to be fired once the stall threshold is reached.\n * @property {boolean} [avoidCurrentTimeRangePruning=false]\n * Avoids pruning of the buffered range that contains the current playback time.\n *\n * That buffered range is likely to have been enqueued for playback. Pruning it causes a flush and reenqueue in WPE and WebKitGTK based browsers. This stresses the video decoder and can cause stuttering on embedded platforms.\n * @property {boolean} [useChangeType=true]\n * If this flag is set to true then dash.js will use the MSE v.2 API call \"changeType()\" before switching to a different codec family.\n * Note that some platforms might not implement the changeType function. dash.js is checking for the availability before trying to call it.\n * @property {boolean} [mediaSourceDurationInfinity=true]\n * If this flag is set to true then dash.js will allow `Infinity` to be set as the MediaSource duration otherwise the duration will be set to `Math.pow(2,32)` instead of `Infinity` to allow appending segments indefinitely.\n * Some platforms such as WebOS 4.x have issues with seeking when duration is set to `Infinity`, setting this flag to false resolve this.\n * @property {boolean} [resetSourceBuffersForTrackSwitch=false]\n * When switching to a track that is not compatible with the currently active MSE SourceBuffers, MSE will be reset. This happens when we switch codecs on a system\n * that does not properly implement \"changeType()\", such as webOS 4.0 and before.\n */\n\n/**\n * @typedef {Object} module:Settings~AudioVideoSettings\n * @property {number|boolean|string} [audio]\n * Configuration for audio media type of tracks.\n * @property {number|boolean|string} [video]\n * Configuration for video media type of tracks.\n */\n\n/**\n * @typedef {Object} module:Settings~SyntheticStallSettings\n * @property {boolean} [enabled]\n * Enables manual stall events and sets the playback rate to 0 once the stall threshold is reached.\n * @property {boolean} [ignoreReadyState]\n * Ignore the media element's ready state when entering or exiting a stall.\n * Enable this when either of these scenarios still occur with synthetic stalls enabled:\n * - If the buffer is empty, but playback is not stalled.\n * - If playback resumes, but a playing event isn't reported.\n */\n\n/**\n * @typedef {Object} DebugSettings\n * @property {number} [logLevel=dashjs.Debug.LOG_LEVEL_WARNING]\n * Sets up the log level. The levels are cumulative.\n *\n * For example, if you set the log level to dashjs.Debug.LOG_LEVEL_WARNING all warnings, errors and fatals will be logged.\n *\n * Possible values.\n *\n * - dashjs.Debug.LOG_LEVEL_NONE\n * No message is written in the browser console.\n *\n * - dashjs.Debug.LOG_LEVEL_FATAL\n * Log fatal errors.\n * An error is considered fatal when it causes playback to fail completely.\n *\n * - dashjs.Debug.LOG_LEVEL_ERROR\n * Log error messages.\n *\n * - dashjs.Debug.LOG_LEVEL_WARNING\n * Log warning messages.\n *\n * - dashjs.Debug.LOG_LEVEL_INFO\n * Log info messages.\n *\n * - dashjs.Debug.LOG_LEVEL_DEBUG\n * Log debug messages.\n * @property {boolean} [dispatchEvent=false]\n * Enable to trigger a Events.LOG event whenever log output is generated.\n *\n * Note this will be dispatched regardless of log level.\n */\n\n/**\n * @typedef {Object} module:Settings~ErrorSettings\n * @property {object} [recoverAttempts={mediaErrorDecode: 5}]\n * Defines the maximum number of recover attempts for specific media errors.\n *\n * For mediaErrorDecode the player will reset the MSE and skip the blacklisted segment that caused the decode error. The resulting gap will be handled by the GapController.\n */\n\n/**\n * @typedef {Object} CachingInfoSettings\n * @property {boolean} [enable]\n * Enable or disable the caching feature.\n * @property {number} [ttl]\n * Time to live.\n *\n * A value defined in milliseconds representing how log to cache the settings for.\n */\n\n/**\n * @typedef {Object} Gaps\n * @property {boolean} [jumpGaps=true]\n * Sets whether player should jump small gaps (discontinuities) in the buffer.\n * @property {boolean} [jumpLargeGaps=true]\n * Sets whether player should jump large gaps (discontinuities) in the buffer.\n * @property {number} [smallGapLimit=1.5]\n * Time in seconds for a gap to be considered small.\n * @property {number} [threshold=0.3]\n * Threshold at which the gap handling is executed. If currentRangeEnd - currentTime < threshold the gap jump will be triggered.\n * For live stream the jump might be delayed to keep a consistent live edge.\n * Note that the amount of buffer at which platforms automatically stall might differ.\n * @property {boolean} [enableSeekFix=true]\n * Enables the adjustment of the seek target once no valid segment request could be generated for a specific seek time. This can happen if the user seeks to a position for which there is a gap in the timeline.\n * @property {boolean} [enableStallFix=false]\n * If playback stalled in a buffered range this fix will perform a seek by the value defined in stallSeek to trigger playback again.\n * @property {number} [stallSeek=0.1]\n * Value to be used in case enableStallFix is set to true\n */\n\n/**\n * @typedef {Object} UtcSynchronizationSettings\n * @property {boolean} [enabled=true]\n * Enables or disables the UTC clock synchronization\n * @property {boolean} [useManifestDateHeaderTimeSource=true]\n * Allows you to enable the use of the Date Header, if exposed with CORS, as a timing source for live edge detection.\n *\n * The use of the date header will happen only after the other timing source that take precedence fail or are omitted as described.\n * @property {number} [backgroundAttempts=2]\n * Number of synchronization attempts to perform in the background after an initial synchronization request has been done. This is used to verify that the derived client-server offset is correct.\n *\n * The background requests are async and done in parallel to the start of the playback.\n *\n * This value is also used to perform a resync after 404 errors on segments.\n * @property {number} [timeBetweenSyncAttempts=30]\n * The time in seconds between two consecutive sync attempts.\n *\n * Note: This value is used as an initial starting value. The internal value of the TimeSyncController is adjusted during playback based on the drift between two consecutive synchronization attempts.\n *\n * Note: A sync is only performed after an MPD update. In case the @minimumUpdatePeriod is larger than this value the sync will be delayed until the next MPD update.\n * @property {number} [maximumTimeBetweenSyncAttempts=600]\n * The maximum time in seconds between two consecutive sync attempts.\n *\n * @property {number} [minimumTimeBetweenSyncAttempts=2]\n * The minimum time in seconds between two consecutive sync attempts.\n *\n * @property {number} [timeBetweenSyncAttemptsAdjustmentFactor=2]\n * The factor used to multiply or divide the timeBetweenSyncAttempts parameter after a sync. The maximumAllowedDrift defines whether this value is used as a factor or a dividend.\n *\n * @property {number} [maximumAllowedDrift=100]\n * The maximum allowed drift specified in milliseconds between two consecutive synchronization attempts.\n *\n * @property {boolean} [enableBackgroundSyncAfterSegmentDownloadError=true]\n * Enables or disables the background sync after the player ran into a segment download error.\n *\n * @property {object} [defaultTimingSource={scheme:'urn:mpeg:dash:utc:http-xsdate:2014',value: 'http://time.akamai.com/?iso&ms'}]\n * The default timing source to be used. The timing sources in the MPD take precedence over this one.\n */\n\n/**\n * @typedef {Object} Scheduling\n * @property {number} [defaultTimeout=500]\n * Default timeout between two consecutive segment scheduling attempts\n * @property {number} [lowLatencyTimeout=0]\n * Default timeout between two consecutive low-latency segment scheduling attempts\n * @property {boolean} [scheduleWhilePaused=true]\n * Set to true if you would like dash.js to keep downloading fragments in the background when the video element is paused.\n */\n\n/**\n * @typedef {Object} Text\n * @property {boolean} [defaultEnabled=true]\n * Enable/disable subtitle rendering by default.\n * @property {boolean} [dispatchForManualRendering=false]\n * Enable/disable firing of CueEnter/CueExt events. This will disable the display of subtitles and should be used when you want to have full control about rendering them.\n * @property {boolean} [extendSegmentedCues=true]\n * Enable/disable patching of segmented cues in order to merge as a single cue by extending cue end time.\n * @property {boolean} [imsc.displayForcedOnlyMode=false]\n * Enable/disable forced only mode in IMSC captions.\n * When true, only those captions where itts:forcedDisplay=\"true\" will be displayed.\n * @property {boolean} [imsc.enableRollUp=true]\n * Enable/disable rollUp style display of IMSC captions.\n * @property {object} [webvtt.customRenderingEnabled=false]\n * Enables the custom rendering for WebVTT captions. For details refer to the \"Subtitles and Captions\" sample section of dash.js.\n * Custom WebVTT rendering requires the external library vtt.js that can be found in the contrib folder.\n */\n\n/**\n * @typedef {Object} LiveCatchupSettings\n * @property {number} [maxDrift=NaN]\n * Use this method to set the maximum latency deviation allowed before dash.js to do a seeking to live position.\n *\n * In low latency mode, when the difference between the measured latency and the target one, as an absolute number, is higher than the one sets with this method, then dash.js does a seek to live edge position minus the target live delay.\n *\n * LowLatencyMaxDriftBeforeSeeking should be provided in seconds.\n *\n * If 0, then seeking operations won't be used for fixing latency deviations.\n *\n * Note: Catch-up mechanism is only applied when playing low latency live streams.\n * @property {number} [playbackRate={min: NaN, max: NaN}]\n * Use this parameter to set the minimum and maximum catch up rates, as percentages, for low latency live streams.\n *\n * In low latency mode, when measured latency is higher/lower than the target one, dash.js increases/decreases playback rate respectively up to (+/-) the percentage defined with this method until target is reached.\n *\n * Valid values for min catch up rate are in the range -0.5 to 0 (-50% to 0% playback rate decrease)\n *\n * Valid values for max catch up rate are in the range 0 to 1 (0% to 100% playback rate increase).\n *\n * Set min and max to NaN to turn off live catch up feature.\n *\n * These playback rate limits take precedence over any PlaybackRate values in ServiceDescription elements in an MPD. If only one of the min/max properties is given a value, the property without a value will not fall back to a ServiceDescription value. Its default value of NaN will be used.\n *\n * Note: Catch-up mechanism is only applied when playing low latency live streams.\n * @property {number} [playbackBufferMin=0.5]\n * Use this parameter to specify the minimum buffer which is used for LoL+ based playback rate reduction.\n *\n *\n * @property {boolean} [enabled=null]\n * Use this parameter to enable the catchup mode for non low-latency streams.\n *\n * @property {string} [mode=\"liveCatchupModeDefault\"]\n * Use this parameter to switch between different catchup modes.\n *\n * Options: \"liveCatchupModeDefault\" or \"liveCatchupModeLOLP\".\n *\n * Note: Catch-up mechanism is automatically applied when playing low latency live streams.\n */\n\n/**\n * @typedef {Object} RequestTypeSettings\n * @property {number} [MPD]\n * Manifest type of requests.\n * @property {number} [XLinkExpansion]\n * XLink expansion type of requests.\n * @property {number} [InitializationSegment]\n * Request to retrieve an initialization segment.\n * @property {number} [IndexSegment]\n * Request to retrieve an index segment (SegmentBase).\n * @property {number} [MediaSegment]\n * Request to retrieve a media segment (video/audio/image/text chunk).\n * @property {number} [BitstreamSwitchingSegment]\n * Bitrate stream switching type of request.\n * @property {number} [FragmentInfoSegment]\n * Request to retrieve a FragmentInfo segment (specific to Smooth Streaming live streams).\n * @property {number} [other]\n * Other type of request.\n * @property {number} [lowLatencyReductionFactor]\n * For low latency mode, values of type of request are divided by lowLatencyReductionFactor.\n *\n * Note: It's not type of request.\n * @property {number} [lowLatencyMultiplyFactor]\n * For low latency mode, values of type of request are multiplied by lowLatencyMultiplyFactor.\n *\n * Note: It's not type of request.\n */\n\n\n/**\n * @typedef {Object} Protection\n * @property {boolean} [keepProtectionMediaKeys=false]\n * Set the value for the ProtectionController and MediaKeys life cycle.\n *\n * If true, the ProtectionController and then created MediaKeys and MediaKeySessions will be preserved during the MediaPlayer lifetime.\n * @property {boolean} [ignoreEmeEncryptedEvent=false]\n * If set to true the player will ignore \"encrypted\" and \"needkey\" events thrown by the EME.\n *\n * @property {boolean} [detectPlayreadyMessageFormat=true]\n * If set to true the player will use the raw unwrapped message from the Playready CDM\n *\n * @property {boolean} [ignoreKeyStatuses=false]\n * If set to true the player will ignore the status of a key and try to play the corresponding track regardless whether the key is usable or not.\n */\n\n/**\n * @typedef {Object} Capabilities\n * @property {boolean} [filterUnsupportedEssentialProperties=true]\n * Enable to filter all the AdaptationSets and Representations which contain an unsupported \\<EssentialProperty\\> element.\n * @property {Array.<string>} [supportedEssentialProperties]\n * List of supported \\<EssentialProperty\\> elements\n * @property {boolean} [useMediaCapabilitiesApi=true]\n * Enable to use the MediaCapabilities API to check whether codecs are supported. If disabled MSE.isTypeSupported will be used instead.\n * @property {boolean} [filterVideoColorimetryEssentialProperties=false]\n * Enable dash.js to query MediaCapabilities API for signalled Colorimetry EssentialProperties (per schemeIdUris: 'urn:mpeg:mpegB:cicp:ColourPrimaries', 'urn:mpeg:mpegB:cicp:TransferCharacteristics').\n * If disabled, registered properties per supportedEssentialProperties will be allowed without any further checking (including 'urn:mpeg:mpegB:cicp:MatrixCoefficients').\n * @property {boolean} [filterHDRMetadataFormatEssentialProperties=false]\n * Enable dash.js to query MediaCapabilities API for signalled HDR-MetadataFormat EssentialProperty (per schemeIdUri:'urn:dvb:dash:hdr-dmi').\n */\n\n/**\n * @typedef {Object} AbrSettings\n * @property {boolean} [limitBitrateByPortal=false]\n * If true, the size of the video portal will limit the max chosen video resolution.\n * @property {boolean} [usePixelRatioInLimitBitrateByPortal=false]\n * Sets whether to take into account the device's pixel ratio when defining the portal dimensions.\n *\n * Useful on, for example, retina displays.\n * @property {module:Settings~AbrRules} [rules]\n * Enable/Disable individual ABR rules. Note that if the throughputRule and the bolaRule are activated at the same time we switch to a dynamic mode.\n * In the dynamic mode either ThroughputRule or BolaRule are active but not both at the same time.\n *\n * l2ARule and loLPRule are ABR rules that are designed for low latency streams. They are tested as standalone rules meaning the other rules should be deactivated when choosing these rules.\n * @property {module:Settings~ThroughputSettings} [throughput]\n * Settings related to throughput calculation\n * @property {module:Settings~AudioVideoSettings} [maxBitrate={audio: -1, video: -1}]\n * The maximum bitrate that the ABR algorithms will choose. This value is specified in kbps.\n *\n * Use -1 for no limit.\n * @property {module:Settings~AudioVideoSettings} [minBitrate={audio: -1, video: -1}]\n * The minimum bitrate that the ABR algorithms will choose. This value is specified in kbps.\n *\n * Use -1 for no limit.\n * @property {module:Settings~AudioVideoSettings} [initialBitrate={audio: -1, video: -1}]\n * Explicitly set the starting bitrate for audio or video. This value is specified in kbps.\n *\n * Use -1 to let the player decide.\n * @property {module:Settings~AudioVideoSettings} [autoSwitchBitrate={audio: true, video: true}]\n * Indicates whether the player should enable ABR algorithms to switch the bitrate.\n */\n\n/**\n * @typedef {Object} AbrRules\n * @property {module:Settings~ThroughputRule} [throughputRule]\n * Configuration of the Throughput rule\n * @property {module:Settings~BolaRule} [bolaRule]\n * Configuration of the BOLA rule\n * @property {module:Settings~InsufficientBufferRule} [insufficientBufferRule]\n * Configuration of the Insufficient Buffer rule\n * @property {module:Settings~SwitchHistoryRule} [switchHistoryRule]\n * Configuration of the Switch History rule\n * @property {module:Settings~DroppedFramesRule} [droppedFramesRule]\n * Configuration of the Dropped Frames rule\n * @property {module:Settings~AbandonRequestsRule} [abandonRequestsRule]\n * Configuration of the Abandon Requests rule\n * @property {module:Settings~L2ARule} [l2ARule]\n * Configuration of the L2A rule\n * @property {module:Settings~LoLPRule} [loLPRule]\n * Configuration of the LoLP rule\n */\n\n/**\n * @typedef {Object} ThroughputRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n */\n\n/**\n * @typedef {Object} BolaRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n */\n\n/**\n * @typedef {Object} InsufficientBufferRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n * @property {object} [parameters={throughputSafetyFactor=0.7, segmentIgnoreCount=2}]\n * Configures the rule specific parameters.\n *\n * - `throughputSafetyFactor`: The safety factor that is applied to the derived throughput, see example in the Description.\n * - `segmentIgnoreCount`: This rule is not taken into account until the first segmentIgnoreCount media segments have been appended to the buffer.\n */\n\n/**\n * @typedef {Object} SwitchHistoryRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n * @property {object} [parameters={sampleSize=8, switchPercentageThreshold=0.075}]\n * Configures the rule specific parameters.\n *\n * - `sampleSize`: Number of switch requests (\"no switch\", because of the selected Representation is already playing or \"actual switches\") required before the rule is applied\n * - `switchPercentageThreshold`: Ratio of actual quality drops compared to no drops before a quality down-switch is triggered\n */\n\n/**\n * @typedef {Object} DroppedFramesRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n * @property {object} [parameters={minimumSampleSize=375, droppedFramesPercentageThreshold=0.15}]\n * Configures the rule specific parameters.\n *\n * - `minimumSampleSize`: Sum of rendered and dropped frames required for each Representation before the rule kicks in.\n * - `droppedFramesPercentageThreshold`: Minimum percentage of dropped frames to trigger a quality down switch. Values are defined in the range of 0 - 1.\n */\n\n/**\n * @typedef {Object} AbandonRequestsRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n * @property {object} [parameters={abandonDurationMultiplier=1.8, minSegmentDownloadTimeThresholdInMs=500, minThroughputSamplesThreshold=6}]\n * Configures the rule specific parameters.\n *\n * - `abandonDurationMultiplier`: Factor to multiply with the segment duration to compare against the estimated remaining download time of the current segment. See code example above.\n * - `minSegmentDownloadTimeThresholdInMs`: The AbandonRequestRule only kicks if the download time of the current segment exceeds this value.\n * - `minThroughputSamplesThreshold`: Minimum throughput samples (equivalent to number of progress events) required before the AbandonRequestRule kicks in.\n */\n\n/**\n * @typedef {Object} L2ARule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n */\n\n/**\n * @typedef {Object} LoLPRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n */\n\n/**\n * @typedef {Object} ThroughputSettings\n * @property {string} [averageCalculationMode=Constants.THROUGHPUT_CALCULATION_MODES.EWMA]\n * Defines the default mode for calculating the throughput based on the samples collected during playback.\n *\n * For arithmetic and harmonic mean calculations we use a sliding window with the values defined in \"sampleSettings\"\n *\n * For exponential weighted moving average calculation the default values can be changed in \"ewma\"\n * @property {string} [lowLatencyDownloadTimeCalculationMode=Constants.LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE.MOOF_PARSING]\n * Defines the effective download time estimation method we use for low latency streams that utilize the Fetch API and chunked transfer coding\n * @property {boolean} [useResourceTimingApi=true]\n * If set to true the ResourceTimingApi is used to derive the download time and the number of downloaded bytes.\n * This option has no effect for low latency streaming as the download time equals the segment duration in most of the cases and therefor does not provide reliable values\n * @property {object} [useNetworkInformationApi = { xhr=false, fetch=false}]\n * If set to true the NetworkInformationApi is used to derive the current throughput. Browser support is limited, only available in Chrome and Edge.\n * Applies to standard (XHR requests) and/or low latency streaming (Fetch API requests).\n * @property {boolean} [useDeadTimeLatency=true]\n * If true, only the download portion will be considered part of the download bitrate and latency will be regarded as static.\n *\n * If false, the reciprocal of the whole transfer time will be used.\n * @property {number} [bandwidthSafetyFactor=0.9]\n * Standard ABR throughput rules multiply the throughput by this value.\n *\n * It should be between 0 and 1, with lower values giving less rebuffering (but also lower quality)\n * @property {object} [sampleSettings = {live=3,vod=4,enableSampleSizeAdjustment=true,decreaseScale=0.7,increaseScale=1.3,maxMeasurementsToKeep=20,averageLatencySampleAmount=4}]\n * When deriving the throughput based on the arithmetic or harmonic mean these settings define:\n * - `live`: Number of throughput samples to use (sample size) for live streams\n * - `vod`: Number of throughput samples to use (sample size) for VoD streams\n * - `enableSampleSizeAdjustment`: Adjust the sample sizes if throughput samples vary a lot\n * - `decreaseScale`: Increase sample size by one if the ratio of current and previous sample is below or equal this value\n * - `increaseScale`: Increase sample size by one if the ratio of current and previous sample is higher or equal this value\n * - `maxMeasurementsToKeep`: Number of samples to keep before sliding samples out of the window\n * - `averageLatencySampleAmount`: Number of latency samples to use (sample size)\n * @property {object} [ewma={throughputSlowHalfLifeSeconds=8,throughputFastHalfLifeSeconds=3,latencySlowHalfLifeCount=2,latencyFastHalfLifeCount=1, weightDownloadTimeMultiplicationFactor=0.0015}]\n * When deriving the throughput based on the exponential weighted moving average these settings define:\n * - `throughputSlowHalfLifeSeconds`: Number by which the weight of the current throughput measurement is divided, see ThroughputModel._updateEwmaValues\n * - `throughputFastHalfLifeSeconds`: Number by which the weight of the current throughput measurement is divided, see ThroughputModel._updateEwmaValues\n * - `latencySlowHalfLifeCount`: Number by which the weight of the current latency is divided, see ThroughputModel._updateEwmaValues\n * - `latencyFastHalfLifeCount`: Number by which the weight of the current latency is divided, see ThroughputModel._updateEwmaValues\n * - `weightDownloadTimeMultiplicationFactor`: This value is multiplied with the download time in milliseconds to derive the weight for the EWMA calculation.\n */\n\n/**\n * @typedef {Object} CmcdSettings\n * @property {boolean} [applyParametersFromMpd=true]\n * Set to true if dash.js should use the CMCD parameters defined in the MPD.\n * @property {boolean} [enable=false]\n * Enable or disable the CMCD reporting.\n * @property {string} [sid]\n * GUID identifying the current playback session.\n *\n * Should be in UUID format.\n *\n * If not specified a UUID will be automatically generated.\n * @property {string} [cid]\n * A unique string to identify the current content.\n *\n * If not specified it will be a hash of the MPD url.\n * @property {number} [rtp]\n * The requested maximum throughput that the client considers sufficient for delivery of the asset.\n *\n * If not specified this value will be dynamically calculated in the CMCDModel based on the current buffer level.\n * @property {number} [rtpSafetyFactor=5]\n * This value is used as a factor for the rtp value calculation: rtp = minBandwidth * rtpSafetyFactor\n *\n * If not specified this value defaults to 5. Note that this value is only used when no static rtp value is defined.\n * @property {number} [mode=\"query\"]\n * The method to use to attach cmcd metrics to the requests. 'query' to use query parameters, 'header' to use http headers.\n *\n * If not specified this value defaults to 'query'.\n * @property {Array.<string>} [enabledKeys]\n * This value is used to specify the desired CMCD parameters. Parameters not included in this list are not reported.\n * @property {Array.<string>} [includeInRequests]\n * Specifies which HTTP GET requests shall carry parameters.\n *\n * If not specified this value defaults to ['segment', 'mpd].\n * @property {number} [version=1]\n * The version of the CMCD to use.\n *\n * If not specified this value defaults to 1.\n */\n\n/**\n * @typedef {Object} module:Settings~CmsdSettings\n * @property {boolean} [enabled=false]\n * Enable or disable the CMSD response headers parsing.\n * @property {module:Settings~CmsdAbrSettings} [abr]\n * Sets additional ABR rules based on CMSD response headers.\n */\n\n/**\n * @typedef {Object} CmsdAbrSettings\n * @property {boolean} [applyMb=false]\n * Set to true if dash.js should apply CMSD maximum suggested bitrate in ABR logic.\n * @property {number} [etpWeightRatio=0]\n * Sets the weight ratio (between 0 and 1) that shall be applied on CMSD estimated throuhgput compared to measured throughput when calculating throughput.\n */\n\n/**\n * @typedef {Object} Metrics\n * @property {number} [metricsMaxListDepth=100]\n * Maximum number of metrics that are persisted per type.\n */\n\n/**\n * @typedef {Object} StreamingSettings\n * @property {number} [abandonLoadTimeout=10000]\n * A timeout value in seconds, which during the ABRController will block switch-up events.\n *\n * This will only take effect after an abandoned fragment event occurs.\n * @property {number} [wallclockTimeUpdateInterval=100]\n * How frequently the wallclockTimeUpdated internal event is triggered (in milliseconds).\n * @property {number} [manifestUpdateRetryInterval=100]\n * For live streams, set the interval-frequency in milliseconds at which dash.js will check if the current manifest is still processed before downloading the next manifest once the minimumUpdatePeriod time has.\n * @property {number} [liveUpdateTimeThresholdInMilliseconds=0]\n * For live streams, postpone syncing time updates until the threshold is passed. Increase if problems occurs during live streams on low end devices.\n * @property {boolean} [cacheInitSegments=false]\n * Enables the caching of init segments to avoid requesting the init segments before each representation switch.\n * @property {boolean} [applyServiceDescription=true]\n * Set to true if dash.js should use the parameters defined in ServiceDescription elements\n * @property {boolean} [applyProducerReferenceTime=true]\n * Set to true if dash.js should use the parameters defined in ProducerReferenceTime elements in combination with ServiceDescription elements.\n * @property {boolean} [applyContentSteering=true]\n * Set to true if dash.js should apply content steering during playback.\n * @property {boolean} [enableManifestDurationMismatchFix=true]\n * For multi-period streams, overwrite the manifest mediaPresentationDuration attribute with the sum of period durations if the manifest mediaPresentationDuration is greater than the sum of period durations\n * @property {boolean} [enableManifestTimescaleMismatchFix=false]\n * Overwrite the manifest segments base information timescale attributes with the timescale set in initialization segments\n * @property {boolean} [parseInbandPrft=false]\n * Set to true if dash.js should parse inband prft boxes (ProducerReferenceTime) and trigger events.\n * @property {module:Settings~Metrics} metrics Metric settings\n * @property {module:Settings~LiveDelay} delay Live Delay settings\n * @property {module:Settings~EventSettings} events Event settings\n * @property {module:Settings~TimeShiftBuffer} timeShiftBuffer TimeShiftBuffer settings\n * @property {module:Settings~Protection} protection DRM related settings\n * @property {module:Settings~Capabilities} capabilities Capability related settings\n * @property {module:Settings~Buffer}  buffer Buffer related settings\n * @property {module:Settings~Gaps}  gaps Gap related settings\n * @property {module:Settings~UtcSynchronizationSettings} utcSynchronization Settings related to UTC clock synchronization\n * @property {module:Settings~Scheduling} scheduling Settings related to segment scheduling\n * @property {module:Settings~Text} text Settings related to Subtitles and captions\n * @property {module:Settings~LiveCatchupSettings} liveCatchup  Settings related to live catchup.\n * @property {module:Settings~CachingInfoSettings} [lastBitrateCachingInfo={enabled: true, ttl: 360000}]\n * Set to false if you would like to disable the last known bit rate from being stored during playback and used to set the initial bit rate for subsequent playback within the expiration window.\n *\n * The default expiration is one hour, defined in milliseconds.\n *\n * If expired, the default initial bit rate (closest to 1000 kbps) will be used for that session and a new bit rate will be stored during that session.\n * @property {module:Settings~CachingInfoSettings} [lastMediaSettingsCachingInfo={enabled: true, ttl: 360000}]\n * Set to false if you would like to disable the last media settings from being stored to localStorage during playback and used to set the initial track for subsequent playback within the expiration window.\n *\n * The default expiration is one hour, defined in milliseconds.\n * @property {boolean} [saveLastMediaSettingsForCurrentStreamingSession=true]\n * Set to true if dash.js should save media settings from last selected track for incoming track selection during current streaming session.\n * @property {module:Settings~AudioVideoSettings} [cacheLoadThresholds={video: 10, audio: 5}]\n * For a given media type, the threshold which defines if the response to a fragment request is coming from browser cache or not.\n * @property {module:Settings~AudioVideoSettings} [trackSwitchMode={video: \"neverReplace\", audio: \"alwaysReplace\"}]\n * For a given media type defines if existing segments in the buffer should be overwritten once the track is switched. For instance if the user switches the audio language the existing segments in the audio buffer will be replaced when setting this value to \"alwaysReplace\".\n *\n * Possible values\n *\n * - Constants.TRACK_SWITCH_MODE_ALWAYS_REPLACE\n * Replace existing segments in the buffer\n *\n * - Constants.TRACK_SWITCH_MODE_NEVER_REPLACE\n * Do not replace existing segments in the buffer\n *\n * @property {} [ignoreSelectionPriority: false]\n * provides the option to disregard any signalled selectionPriority attribute. If disabled and if no initial media settings are set, track selection is accomplished as defined by selectionModeForInitialTrack.\n *\n * @property {} [prioritizeRoleMain: true]\n * provides the option to disable prioritization of AdaptationSets with their Role set to Main\n *\n * @property {} [assumeDefaultRoleAsMain: true]\n * when no Role descriptor is present, assume main per default\n * \n * @property {string} [selectionModeForInitialTrack=\"highestEfficiency\"]\n * Sets the selection mode for the initial track. This mode defines how the initial track will be selected if no initial media settings are set. If initial media settings are set this parameter will be ignored. Available options are:\n *\n * Possible values\n *\n * - Constants.TRACK_SELECTION_MODE_HIGHEST_BITRATE\n * This mode makes the player select the track with a highest bitrate.\n *\n * - Constants.TRACK_SELECTION_MODE_FIRST_TRACK\n * This mode makes the player select the first track found in the manifest.\n *\n * - Constants.TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY\n * This mode makes the player select the track with the lowest bitrate per pixel average.\n *\n * - Constants.TRACK_SELECTION_MODE_WIDEST_RANGE\n * This mode makes the player select the track with a widest range of bitrates.\n *\n *\n * @property {number} [fragmentRequestTimeout=20000]\n * Time in milliseconds before timing out on loading a media fragment.\n *\n * @property {number} [fragmentRequestProgressTimeout=-1]\n * Time in milliseconds before timing out on loading progress of a media fragment.\n *\n * @property {number} [manifestRequestTimeout=10000]\n * Time in milliseconds before timing out on loading a manifest.\n *\n * Fragments that timeout are retried as if they failed.\n * @property {module:Settings~RequestTypeSettings} [retryIntervals]\n * Time in milliseconds of which to reload a failed file load attempt.\n *\n * For low latency mode these values are divided by lowLatencyReductionFactor.\n * @property {module:Settings~RequestTypeSettings} [retryAttempts]\n * Total number of retry attempts that will occur on a file load before it fails.\n *\n * For low latency mode these values are multiplied by lowLatencyMultiplyFactor.\n * @property {module:Settings~AbrSettings} abr\n * Adaptive Bitrate algorithm related settings.\n * @property {module:Settings~CmcdSettings} cmcd\n * Settings related to Common Media Client Data reporting.\n * @property {module:Settings~CmsdSettings} cmsd\n * Settings related to Common Media Server Data parsing.\n * @property {module:Settings~defaultSchemeIdUri} defaultSchemeIdUri\n * Default schemeIdUri for descriptor type elements\n * These strings are used when not provided with setInitialMediaSettingsFor()\n */\n\n\n/**\n * @class\n * @ignore\n */\nfunction Settings() {\n    let instance;\n    const context = this.context;\n    const eventBus = EventBus(context).getInstance();\n    const DISPATCH_KEY_MAP = {\n        'streaming.delay.liveDelay': Events.SETTING_UPDATED_LIVE_DELAY,\n        'streaming.delay.liveDelayFragmentCount': Events.SETTING_UPDATED_LIVE_DELAY_FRAGMENT_COUNT,\n        'streaming.liveCatchup.enabled': Events.SETTING_UPDATED_CATCHUP_ENABLED,\n        'streaming.liveCatchup.playbackRate.min': Events.SETTING_UPDATED_PLAYBACK_RATE_MIN,\n        'streaming.liveCatchup.playbackRate.max': Events.SETTING_UPDATED_PLAYBACK_RATE_MAX,\n        'streaming.abr.rules.throughputRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.bolaRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.insufficientBufferRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.switchHistoryRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.droppedFramesRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.abandonRequestsRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.l2ARule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.loLPRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.maxBitrate.video': Events.SETTING_UPDATED_MAX_BITRATE,\n        'streaming.abr.maxBitrate.audio': Events.SETTING_UPDATED_MAX_BITRATE,\n        'streaming.abr.minBitrate.video': Events.SETTING_UPDATED_MIN_BITRATE,\n        'streaming.abr.minBitrate.audio': Events.SETTING_UPDATED_MIN_BITRATE,\n    };\n\n    /**\n     * @const {PlayerSettings} defaultSettings\n     * @ignore\n     */\n    const defaultSettings = {\n        debug: {\n            logLevel: Debug.LOG_LEVEL_WARNING,\n            dispatchEvent: false\n        },\n        streaming: {\n            abandonLoadTimeout: 10000,\n            wallclockTimeUpdateInterval: 100,\n            manifestUpdateRetryInterval: 100,\n            liveUpdateTimeThresholdInMilliseconds: 0,\n            cacheInitSegments: false,\n            applyServiceDescription: true,\n            applyProducerReferenceTime: true,\n            applyContentSteering: true,\n            enableManifestDurationMismatchFix: true,\n            parseInbandPrft: false,\n            enableManifestTimescaleMismatchFix: false,\n            capabilities: {\n                filterUnsupportedEssentialProperties: true,\n                supportedEssentialProperties: [\n                    { schemeIdUri: Constants.FONT_DOWNLOAD_DVB_SCHEME },\n                    { schemeIdUri: Constants.COLOUR_PRIMARIES_SCHEME_ID_URI, value: /1|5|6|7/ },\n                    { schemeIdUri: Constants.URL_QUERY_INFO_SCHEME },\n                    { schemeIdUri: Constants.EXT_URL_QUERY_INFO_SCHEME },\n                    { schemeIdUri: Constants.MATRIX_COEFFICIENTS_SCHEME_ID_URI, value: /0|1|5|6/ },\n                    { schemeIdUri: Constants.TRANSFER_CHARACTERISTICS_SCHEME_ID_URI, value: /1|6|13|14|15/ },\n                    ...Constants.THUMBNAILS_SCHEME_ID_URIS.map(ep => {\n                        return { 'schemeIdUri': ep };\n                    })\n                ],\n                useMediaCapabilitiesApi: true,\n                filterVideoColorimetryEssentialProperties: false,\n                filterHDRMetadataFormatEssentialProperties: false\n            },\n            events: {\n                eventControllerRefreshDelay: 100,\n                deleteEventMessageDataTimeout: 10000\n            },\n            timeShiftBuffer: {\n                calcFromSegmentTimeline: false,\n                fallbackToSegmentTimeline: true\n            },\n            metrics: {\n                maxListDepth: 100\n            },\n            delay: {\n                liveDelayFragmentCount: NaN,\n                liveDelay: NaN,\n                useSuggestedPresentationDelay: true\n            },\n            protection: {\n                keepProtectionMediaKeys: false,\n                ignoreEmeEncryptedEvent: false,\n                detectPlayreadyMessageFormat: true,\n                ignoreKeyStatuses: false\n            },\n            buffer: {\n                enableSeekDecorrelationFix: false,\n                fastSwitchEnabled: null,\n                flushBufferAtTrackSwitch: false,\n                reuseExistingSourceBuffers: true,\n                bufferPruningInterval: 10,\n                bufferToKeep: 20,\n                bufferTimeAtTopQuality: 30,\n                bufferTimeAtTopQualityLongForm: 60,\n                initialBufferLevel: NaN,\n                bufferTimeDefault: 18,\n                longFormContentDurationThreshold: 600,\n                stallThreshold: 0.3,\n                lowLatencyStallThreshold: 0.3,\n                useAppendWindow: true,\n                setStallState: true,\n                avoidCurrentTimeRangePruning: false,\n                useChangeType: true,\n                mediaSourceDurationInfinity: true,\n                resetSourceBuffersForTrackSwitch: false,\n                syntheticStallEvents: {\n                    enabled: false,\n                    ignoreReadyState: false\n                }\n            },\n            gaps: {\n                jumpGaps: true,\n                jumpLargeGaps: true,\n                smallGapLimit: 1.5,\n                threshold: 0.3,\n                enableSeekFix: true,\n                enableStallFix: false,\n                stallSeek: 0.1\n            },\n            utcSynchronization: {\n                enabled: true,\n                useManifestDateHeaderTimeSource: true,\n                backgroundAttempts: 2,\n                timeBetweenSyncAttempts: 30,\n                maximumTimeBetweenSyncAttempts: 600,\n                minimumTimeBetweenSyncAttempts: 2,\n                timeBetweenSyncAttemptsAdjustmentFactor: 2,\n                maximumAllowedDrift: 100,\n                enableBackgroundSyncAfterSegmentDownloadError: true,\n                defaultTimingSource: {\n                    scheme: 'urn:mpeg:dash:utc:http-xsdate:2014',\n                    value: 'https://time.akamai.com/?iso&ms'\n                }\n            },\n            scheduling: {\n                defaultTimeout: 500,\n                lowLatencyTimeout: 0,\n                scheduleWhilePaused: true\n            },\n            text: {\n                defaultEnabled: true,\n                dispatchForManualRendering: false,\n                extendSegmentedCues: true,\n                imsc: {\n                    displayForcedOnlyMode: false,\n                    enableRollUp: true\n                },\n                webvtt: {\n                    customRenderingEnabled: false\n                }\n            },\n            liveCatchup: {\n                maxDrift: NaN,\n                playbackRate: {\n                    min: NaN,\n                    max: NaN\n                },\n                playbackBufferMin: 0.5,\n                enabled: null,\n                mode: Constants.LIVE_CATCHUP_MODE_DEFAULT\n            },\n            lastBitrateCachingInfo: {\n                enabled: true,\n                ttl: 360000\n            },\n            lastMediaSettingsCachingInfo: {\n                enabled: true,\n                ttl: 360000\n            },\n            saveLastMediaSettingsForCurrentStreamingSession: true,\n            cacheLoadThresholds: {\n                video: 10,\n                audio: 5\n            },\n            trackSwitchMode: {\n                audio: Constants.TRACK_SWITCH_MODE_ALWAYS_REPLACE,\n                video: Constants.TRACK_SWITCH_MODE_NEVER_REPLACE\n            },\n            ignoreSelectionPriority: false,\n            prioritizeRoleMain: true,\n            assumeDefaultRoleAsMain: true,\n            selectionModeForInitialTrack: Constants.TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY,\n            fragmentRequestTimeout: 20000,\n            fragmentRequestProgressTimeout: -1,\n            manifestRequestTimeout: 10000,\n            retryIntervals: {\n                [HTTPRequest.MPD_TYPE]: 500,\n                [HTTPRequest.XLINK_EXPANSION_TYPE]: 500,\n                [HTTPRequest.MEDIA_SEGMENT_TYPE]: 1000,\n                [HTTPRequest.INIT_SEGMENT_TYPE]: 1000,\n                [HTTPRequest.BITSTREAM_SWITCHING_SEGMENT_TYPE]: 1000,\n                [HTTPRequest.INDEX_SEGMENT_TYPE]: 1000,\n                [HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE]: 1000,\n                [HTTPRequest.LICENSE]: 1000,\n                [HTTPRequest.OTHER_TYPE]: 1000,\n                lowLatencyReductionFactor: 10\n            },\n            retryAttempts: {\n                [HTTPRequest.MPD_TYPE]: 3,\n                [HTTPRequest.XLINK_EXPANSION_TYPE]: 1,\n                [HTTPRequest.MEDIA_SEGMENT_TYPE]: 3,\n                [HTTPRequest.INIT_SEGMENT_TYPE]: 3,\n                [HTTPRequest.BITSTREAM_SWITCHING_SEGMENT_TYPE]: 3,\n                [HTTPRequest.INDEX_SEGMENT_TYPE]: 3,\n                [HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE]: 3,\n                [HTTPRequest.LICENSE]: 3,\n                [HTTPRequest.OTHER_TYPE]: 3,\n                lowLatencyMultiplyFactor: 5\n            },\n            abr: {\n                limitBitrateByPortal: false,\n                usePixelRatioInLimitBitrateByPortal: false,\n                enableSupplementalPropertyAdaptationSetSwitching: true,\n                rules: {\n                    throughputRule: {\n                        active: true\n                    },\n                    bolaRule: {\n                        active: true\n                    },\n                    insufficientBufferRule: {\n                        active: true,\n                        parameters: {\n                            throughputSafetyFactor: 0.7,\n                            segmentIgnoreCount: 2\n                        }\n                    },\n                    switchHistoryRule: {\n                        active: true,\n                        parameters: {\n                            sampleSize: 8,\n                            switchPercentageThreshold: 0.075\n                        }\n                    },\n                    droppedFramesRule: {\n                        active: false,\n                        parameters: {\n                            minimumSampleSize: 375,\n                            droppedFramesPercentageThreshold: 0.15\n                        }\n                    },\n                    abandonRequestsRule: {\n                        active: true,\n                        parameters: {\n                            abandonDurationMultiplier: 1.8,\n                            minSegmentDownloadTimeThresholdInMs: 500,\n                            minThroughputSamplesThreshold: 6\n                        }\n                    },\n                    l2ARule: {\n                        active: false\n                    },\n                    loLPRule: {\n                        active: false\n                    }\n                },\n                throughput: {\n                    averageCalculationMode: Constants.THROUGHPUT_CALCULATION_MODES.EWMA,\n                    lowLatencyDownloadTimeCalculationMode: Constants.LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE.MOOF_PARSING,\n                    useResourceTimingApi: true,\n                    useNetworkInformationApi: {\n                        xhr: false,\n                        fetch: false\n                    },\n                    useDeadTimeLatency: true,\n                    bandwidthSafetyFactor: 0.9,\n                    sampleSettings: {\n                        live: 3,\n                        vod: 4,\n                        enableSampleSizeAdjustment: true,\n                        decreaseScale: 0.7,\n                        increaseScale: 1.3,\n                        maxMeasurementsToKeep: 20,\n                        averageLatencySampleAmount: 4,\n                    },\n                    ewma: {\n                        throughputSlowHalfLifeSeconds: 8,\n                        throughputFastHalfLifeSeconds: 3,\n                        latencySlowHalfLifeCount: 2,\n                        latencyFastHalfLifeCount: 1,\n                        weightDownloadTimeMultiplicationFactor: 0.0015\n                    }\n                },\n                maxBitrate: {\n                    audio: -1,\n                    video: -1\n                },\n                minBitrate: {\n                    audio: -1,\n                    video: -1\n                },\n                initialBitrate: {\n                    audio: -1,\n                    video: -1\n                },\n                autoSwitchBitrate: {\n                    audio: true,\n                    video: true\n                }\n            },\n            cmcd: {\n                applyParametersFromMpd: true,\n                enabled: false,\n                sid: null,\n                cid: null,\n                rtp: null,\n                rtpSafetyFactor: 5,\n                mode: Constants.CMCD_MODE_QUERY,\n                enabledKeys: Constants.CMCD_AVAILABLE_KEYS,\n                includeInRequests: ['segment', 'mpd'],\n                version: 1\n            },\n            cmsd: {\n                enabled: false,\n                abr: {\n                    applyMb: false,\n                    etpWeightRatio: 0\n                }\n            },\n            defaultSchemeIdUri: {\n                viewpoint: '',\n                audioChannelConfiguration: 'urn:mpeg:mpegB:cicp:ChannelConfiguration',\n                role: 'urn:mpeg:dash:role:2011',\n                accessibility: 'urn:mpeg:dash:role:2011'\n            }\n        },\n        errors: {\n            recoverAttempts: {\n                mediaErrorDecode: 5\n            }\n        }\n    };\n\n    let settings = Utils.clone(defaultSettings);\n\n    //Merge in the settings. If something exists in the new config that doesn't match the schema of the default config,\n    //regard it as an error and log it.\n    function mixinSettings(source, dest, path) {\n        for (let n in source) {\n            if (source.hasOwnProperty(n)) {\n                if (dest.hasOwnProperty(n)) {\n                    if (typeof source[n] === 'object' && !(source[n] instanceof RegExp) && !(source[n] instanceof Array) && source[n] !== null) {\n                        mixinSettings(source[n], dest[n], path.slice() + n + '.');\n                    } else {\n                        dest[n] = Utils.clone(source[n]);\n                        if (DISPATCH_KEY_MAP[path + n]) {\n                            eventBus.trigger(DISPATCH_KEY_MAP[path + n]);\n                        }\n                    }\n                } else {\n                    console.error('Settings parameter ' + path + n + ' is not supported');\n                }\n            }\n        }\n    }\n\n    /**\n     * Return the settings object. Don't copy/store this object, you won't get updates.\n     * @func\n     * @instance\n     */\n    function get() {\n        return settings;\n    }\n\n    /**\n     * @func\n     * @instance\n     * @param {object} settingsObj - This should be a partial object of the Settings.Schema type. That is, fields defined should match the path (e.g.\n     * settingsObj.streaming.abr.autoSwitchBitrate.audio -> defaultSettings.streaming.abr.autoSwitchBitrate.audio). Where an element's path does\n     * not match it is ignored, and a warning is logged.\n     *\n     * Use to change the settings object. Any new values defined will overwrite the settings and anything undefined will not change.\n     * Implementers of new settings should add it in an approriate namespace to the defaultSettings object and give it a default value (that is not undefined).\n     *\n     */\n    function update(settingsObj) {\n        if (typeof settingsObj === 'object') {\n            mixinSettings(settingsObj, settings, '');\n        }\n    }\n\n    /**\n     * Resets the settings object. Everything is set to its default value.\n     * @func\n     * @instance\n     *\n     */\n    function reset() {\n        settings = Utils.clone(defaultSettings);\n    }\n\n    instance = {\n        get,\n        update,\n        reset\n    };\n\n    return instance;\n}\n\n\nSettings.__dashjs_factory_name = 'Settings';\nlet factory = FactoryMaker.getSingletonFactory(Settings);\nexport default factory;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * @class\n * @ignore\n */\n\nimport path from 'path-browserify'\nimport {UAParser} from 'ua-parser-js'\nimport Constants from '../streaming/constants/Constants.js';\n\nclass Utils {\n    static mixin(dest, source, copy) {\n        let s;\n        let empty = {};\n        if (dest) {\n            for (let name in source) {\n                if (source.hasOwnProperty(name)) {\n                    s = source[name];\n                    if (!(name in dest) || (dest[name] !== s && (!(name in empty) || empty[name] !== s))) {\n                        if (typeof dest[name] === 'object' && dest[name] !== null) {\n                            dest[name] = Utils.mixin(dest[name], s, copy);\n                        } else {\n                            dest[name] = copy(s);\n                        }\n                    }\n                }\n            }\n        }\n        return dest;\n    }\n\n    static clone(src) {\n        if (!src || typeof src !== 'object') {\n            return src; // anything\n        }\n        if (src instanceof RegExp) {\n            return new RegExp(src);\n        }\n        let r;\n        if (src instanceof Array) {\n            // array\n            r = [];\n            for (let i = 0, l = src.length; i < l; ++i) {\n                if (i in src) {\n                    r.push(Utils.clone(src[i]));\n                }\n            }\n        } else {\n            r = {};\n        }\n        return Utils.mixin(r, src, Utils.clone);\n    }\n\n    static addAdditionalQueryParameterToUrl(url, params) {\n        try {\n            if (!params || params.length === 0) {\n                return url;\n            }\n\n            let updatedUrl = url;\n            params.forEach(({ key, value }) => {\n                const separator = updatedUrl.includes('?') ? '&' : '?';\n                updatedUrl += `${separator}${(encodeURIComponent(key))}=${(encodeURIComponent(value))}`;\n            });\n            return updatedUrl;\n        } catch (e) {\n            return url;\n        }\n    }\n\n    static removeQueryParameterFromUrl(url, queryParameter) {\n        if (!url || !queryParameter) {\n            return url;\n        }\n        // Parse the URL\n        const parsedUrl = new URL(url);\n\n        // Get the search parameters\n        const params = new URLSearchParams(parsedUrl.search);\n\n        if (!params || params.size === 0 || !params.has(queryParameter)) {\n            return url;\n        }\n\n        // Remove the queryParameter\n        params.delete(queryParameter);\n\n        // Manually reconstruct the query string without re-encoding\n        const queryString = Array.from(params.entries())\n            .map(([key, value]) => `${key}=${value}`)\n            .join('&');\n\n        // Reconstruct the URL\n        const baseUrl = `${parsedUrl.origin}${parsedUrl.pathname}`;\n        return queryString ? `${baseUrl}?${queryString}` : baseUrl;\n    }\n\n    static parseHttpHeaders(headerStr) {\n        let headers = {};\n        if (!headerStr) {\n            return headers;\n        }\n\n        // Trim headerStr to fix a MS Edge bug with xhr.getAllResponseHeaders method\n        // which send a string starting with a \"\\n\" character\n        let headerPairs = headerStr.trim().split('\\u000d\\u000a');\n        for (let i = 0, ilen = headerPairs.length; i < ilen; i++) {\n            let headerPair = headerPairs[i];\n            let index = headerPair.indexOf('\\u003a\\u0020');\n            if (index > 0) {\n                headers[headerPair.substring(0, index)] = headerPair.substring(index + 2);\n            }\n        }\n        return headers;\n    }\n\n    /**\n     * Parses query parameters from a string and returns them as an array of key-value pairs.\n     * @param {string} queryParamString - A string containing the query parameters.\n     * @return {Array<{key: string, value: string}>} An array of objects representing the query parameters.\n     */\n    static parseQueryParams(queryParamString) {\n        const params = [];\n        const searchParams = new URLSearchParams(queryParamString);\n        for (const [key, value] of searchParams.entries()) {\n            params.push({ key: decodeURIComponent(key), value: decodeURIComponent(value) });\n        }\n        return params;\n    }\n\n    static generateUuid() {\n        let dt = new Date().getTime();\n        const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n            const r = (dt + Math.random() * 16) % 16 | 0;\n            dt = Math.floor(dt / 16);\n            return (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16);\n        });\n        return uuid;\n    }\n\n    static generateHashCode(string) {\n        let hash = 0;\n\n        if (string.length === 0) {\n            return hash;\n        }\n\n        for (let i = 0; i < string.length; i++) {\n            const chr = string.charCodeAt(i);\n            hash = ((hash << 5) - hash) + chr;\n            hash |= 0;\n        }\n        return hash;\n    }\n\n    /**\n     * Compares both urls and returns a relative url (target relative to original)\n     * @param {string} originalUrl\n     * @param {string} targetUrl\n     * @return {string|*}\n     */\n    static getRelativeUrl(originalUrl, targetUrl) {\n        try {\n            const original = new URL(originalUrl);\n            const target = new URL(targetUrl);\n\n            // Unify the protocol to compare the origins\n            original.protocol = target.protocol;\n            if (original.origin !== target.origin) {\n                return targetUrl;\n            }\n\n            // Use the relative path implementation of the path library. We need to cut off the actual filename in the end to get the relative path\n            let relativePath = path.relative(original.pathname.substr(0, original.pathname.lastIndexOf('/')), target.pathname.substr(0, target.pathname.lastIndexOf('/')));\n\n            // In case the relative path is empty (both path are equal) return the filename only. Otherwise add a slash in front of the filename\n            const startIndexOffset = relativePath.length === 0 ? 1 : 0;\n            relativePath += target.pathname.substr(target.pathname.lastIndexOf('/') + startIndexOffset, target.pathname.length - 1);\n\n            // Build the other candidate, e.g. the 'host relative' path that starts with \"/\", and return the shortest of the two candidates.\n            if (target.pathname.length < relativePath.length) {\n                return target.pathname;\n            }\n            return relativePath;\n        } catch (e) {\n            return targetUrl\n        }\n    }\n\n    static getHostFromUrl(urlString) {\n        try {\n            const url = new URL(urlString);\n\n            return url.host\n        } catch (e) {\n            return null\n        }\n    }\n\n    static parseUserAgent(ua = null) {\n        try {\n            const uaString = ua === null ? typeof navigator !== 'undefined' ? navigator.userAgent.toLowerCase() : '' : '';\n\n            return UAParser(uaString);\n        } catch (e) {\n            return {};\n        }\n    }\n\n    /**\n     * Checks for existence of \"http\" or \"https\" in a string\n     * @param string\n     * @returns {boolean}\n     */\n    static stringHasProtocol(string) {\n        return (/(http(s?)):\\/\\//i.test(string))\n    }\n\n    static bufferSourceToDataView(bufferSource) {\n        return Utils.toDataView(bufferSource, DataView);\n    }\n\n    static bufferSourceToInt8(bufferSource) {\n        return Utils.toDataView(bufferSource, Uint8Array)\n    }\n\n    static uint8ArrayToString(uint8Array) {\n        const decoder = new TextDecoder('utf-8');\n        return decoder.decode(uint8Array);\n    }\n\n    static bufferSourceToHex(data) {\n        const arr = Utils.bufferSourceToInt8(data)\n        let hex = '';\n        for (let value of arr) {\n            value = value.toString(16);\n            if (value.length === 1) {\n                value = '0' + value;\n            }\n            hex += value;\n        }\n        return hex;\n    }\n\n    static toDataView(bufferSource, Type) {\n        const buffer = Utils.getArrayBuffer(bufferSource);\n        let bytesPerElement = 1;\n        if ('BYTES_PER_ELEMENT' in DataView) {\n            bytesPerElement = DataView.BYTES_PER_ELEMENT;\n        }\n\n        const dataEnd = ((bufferSource.byteOffset || 0) + bufferSource.byteLength) /\n            bytesPerElement;\n        const rawStart = ((bufferSource.byteOffset || 0)) / bytesPerElement;\n        const start = Math.floor(Math.max(0, Math.min(rawStart, dataEnd)));\n        const end = Math.floor(Math.min(start + Math.max(Infinity, 0), dataEnd));\n        return new Type(buffer, start, end - start);\n    }\n\n    static getArrayBuffer(view) {\n        if (view instanceof ArrayBuffer) {\n            return view;\n        } else {\n            return view.buffer;\n        }\n    }\n\n    static getCodecFamily(codecString) {\n        const { base, profile } = Utils._getCodecParts(codecString)\n\n        switch (base) {\n            case 'mp4a':\n                switch (profile) {\n                    case '69':\n                    case '6b':\n                    case '40.34':\n                        return Constants.CODEC_FAMILIES.MP3\n                    case '66':\n                    case '67':\n                    case '68':\n                    case '40.2':\n                    case '40.02':\n                    case '40.5':\n                    case '40.05':\n                    case '40.29':\n                    case '40.42':\n                        return Constants.CODEC_FAMILIES.AAC\n                    case 'a5':\n                        return Constants.CODEC_FAMILIES.AC3\n                    case 'e6':\n                        return Constants.CODEC_FAMILIES.EC3\n                    case 'b2':\n                        return Constants.CODEC_FAMILIES.DTSX\n                    case 'a9':\n                        return Constants.CODEC_FAMILIES.DTSC\n                }\n                break;\n            case 'avc1':\n            case 'avc3':\n                return Constants.CODEC_FAMILIES.AVC\n            case 'hvc1':\n            case 'hvc3':\n                return Constants.CODEC_FAMILIES.HEVC\n            default:\n                return base\n        }\n\n        return base;\n    }\n\n    static _getCodecParts(codecString) {\n        const [base, ...rest] = codecString.split('.');\n        const profile = rest.join('.');\n        return { base, profile };\n    }\n\n}\n\nexport default Utils;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport EventsBase from './EventsBase.js';\n\n/**\n * These are internal events that should not be needed at the player level.\n * If you find and event in here that you would like access to from MediaPlayer level\n * please add an issue at https://github.com/Dash-Industry-Forum/dash.js/issues/new\n * @class\n * @ignore\n */\nclass CoreEvents extends EventsBase {\n    constructor () {\n        super();\n        this.ATTEMPT_BACKGROUND_SYNC = 'attemptBackgroundSync';\n        this.BUFFERING_COMPLETED = 'bufferingCompleted';\n        this.BUFFER_CLEARED = 'bufferCleared';\n        this.BYTES_APPENDED_END_FRAGMENT = 'bytesAppendedEndFragment';\n        this.BUFFER_REPLACEMENT_STARTED = 'bufferReplacementStarted';\n        this.CHECK_FOR_EXISTENCE_COMPLETED = 'checkForExistenceCompleted';\n        this.CMSD_STATIC_HEADER = 'cmsdStaticHeader';\n        this.CURRENT_TRACK_CHANGED = 'currentTrackChanged';\n        this.DATA_UPDATE_COMPLETED = 'dataUpdateCompleted';\n        this.INBAND_EVENTS = 'inbandEvents';\n        this.INITIAL_STREAM_SWITCH = 'initialStreamSwitch';\n        this.INIT_FRAGMENT_LOADED = 'initFragmentLoaded';\n        this.INIT_FRAGMENT_NEEDED = 'initFragmentNeeded';\n        this.INTERNAL_MANIFEST_LOADED = 'internalManifestLoaded';\n        this.ORIGINAL_MANIFEST_LOADED = 'originalManifestLoaded';\n        this.LOADING_COMPLETED = 'loadingCompleted';\n        this.LOADING_PROGRESS = 'loadingProgress';\n        this.LOADING_DATA_PROGRESS = 'loadingDataProgress';\n        this.LOADING_ABANDONED = 'loadingAborted';\n        this.MANIFEST_UPDATED = 'manifestUpdated';\n        this.MEDIA_FRAGMENT_LOADED = 'mediaFragmentLoaded';\n        this.MEDIA_FRAGMENT_NEEDED = 'mediaFragmentNeeded';\n        this.MEDIAINFO_UPDATED = 'mediaInfoUpdated';\n        this.QUOTA_EXCEEDED = 'quotaExceeded';\n        this.SEGMENT_LOCATION_BLACKLIST_ADD = 'segmentLocationBlacklistAdd';\n        this.SEGMENT_LOCATION_BLACKLIST_CHANGED = 'segmentLocationBlacklistChanged';\n        this.SERVICE_LOCATION_BASE_URL_BLACKLIST_ADD = 'serviceLocationBlacklistAdd';\n        this.SERVICE_LOCATION_BASE_URL_BLACKLIST_CHANGED = 'serviceLocationBlacklistChanged';\n        this.SERVICE_LOCATION_LOCATION_BLACKLIST_ADD = 'serviceLocationLocationBlacklistAdd';\n        this.SERVICE_LOCATION_LOCATION_BLACKLIST_CHANGED = 'serviceLocationLocationBlacklistChanged';\n        this.SET_FRAGMENTED_TEXT_AFTER_DISABLED = 'setFragmentedTextAfterDisabled';\n        this.SET_NON_FRAGMENTED_TEXT = 'setNonFragmentedText';\n        this.SOURCE_BUFFER_ERROR = 'sourceBufferError';\n        this.STREAMS_COMPOSED = 'streamsComposed';\n        this.STREAM_BUFFERING_COMPLETED = 'streamBufferingCompleted';\n        this.STREAM_REQUESTING_COMPLETED = 'streamRequestingCompleted';\n        this.TEXT_TRACKS_QUEUE_INITIALIZED = 'textTracksQueueInitialized';\n        this.TIME_SYNCHRONIZATION_COMPLETED = 'timeSynchronizationComplete';\n        this.UPDATE_TIME_SYNC_OFFSET = 'updateTimeSyncOffset';\n        this.URL_RESOLUTION_FAILED = 'urlResolutionFailed';\n        this.VIDEO_CHUNK_RECEIVED = 'videoChunkReceived';\n        this.WALLCLOCK_TIME_UPDATED = 'wallclockTimeUpdated';\n        this.XLINK_ELEMENT_LOADED = 'xlinkElementLoaded';\n        this.XLINK_READY = 'xlinkReady';\n        this.SEEK_TARGET = 'seekTarget';\n        this.SETTING_UPDATED_LIVE_DELAY = 'settingUpdatedLiveDelay';\n        this.SETTING_UPDATED_LIVE_DELAY_FRAGMENT_COUNT = 'settingUpdatedLiveDelayFragmentCount';\n        this.SETTING_UPDATED_CATCHUP_ENABLED = 'settingUpdatedCatchupEnabled';\n        this.SETTING_UPDATED_PLAYBACK_RATE_MIN = 'settingUpdatedPlaybackRateMin';\n        this.SETTING_UPDATED_PLAYBACK_RATE_MAX = 'settingUpdatedPlaybackRateMax';\n        this.SETTING_UPDATED_ABR_ACTIVE_RULES = 'settingUpdatedAbrActiveRules';\n        this.SETTING_UPDATED_MAX_BITRATE = 'settingUpdatedMaxBitrate';\n        this.SETTING_UPDATED_MIN_BITRATE = 'settingUpdatedMinBitrate';\n    }\n}\n\nexport default CoreEvents;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nimport CoreEvents from './CoreEvents.js';\n\nclass Events extends CoreEvents {\n}\n\nlet events = new Events();\nexport default events;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass EventsBase {\n    extend(events, config) {\n        if (!events) {\n            return;\n        }\n\n        let override = config ? config.override : false;\n        let publicOnly = config ? config.publicOnly : false;\n\n\n        for (const evt in events) {\n            if (!events.hasOwnProperty(evt) || (this[evt] && !override)) {\n                continue;\n            }\n            if (publicOnly && events[evt].indexOf('public_') === -1) {\n                continue;\n            }\n            this[evt] = events[evt];\n\n        }\n    }\n}\n\nexport default EventsBase;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass UTCTiming {\n    constructor() {\n        // UTCTiming is a DescriptorType and doesn't have any additional fields\n        this.schemeIdUri = '';\n        this.value = '';\n    }\n}\n\nexport default UTCTiming;", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport EventsBase from '../core/events/EventsBase.js';\n\n/**\n * @class\n * @implements EventsBase\n */\nclass MediaPlayerEvents extends EventsBase {\n\n    /**\n     * @description Public facing external events to be used when developing a player that implements dash.js.\n     */\n    constructor() {\n        super();\n        /**\n         * Triggered when playback will not start yet\n         * as the MPD's availabilityStartTime is in the future.\n         * Check delay property in payload to determine time before playback will start.\n         * @event MediaPlayerEvents#AST_IN_FUTURE\n         */\n        this.AST_IN_FUTURE = 'astInFuture';\n\n        /**\n         * Triggered when the BaseURLs have been updated.\n         * @event MediaPlayerEvents#BASE_URLS_UPDATED\n         */\n        this.BASE_URLS_UPDATED = 'baseUrlsUpdated';\n\n        /**\n         * Triggered when the video element's buffer state changes to stalled.\n         * Check mediaType in payload to determine type (Video, Audio, FragmentedText).\n         * @event MediaPlayerEvents#BUFFER_EMPTY\n         */\n        this.BUFFER_EMPTY = 'bufferStalled';\n\n        /**\n         * Triggered when the video element's buffer state changes to loaded.\n         * Check mediaType in payload to determine type (Video, Audio, FragmentedText).\n         * @event MediaPlayerEvents#BUFFER_LOADED\n         */\n        this.BUFFER_LOADED = 'bufferLoaded';\n\n        /**\n         * Triggered when the video element's buffer state changes, either stalled or loaded. Check payload for state.\n         * @event MediaPlayerEvents#BUFFER_LEVEL_STATE_CHANGED\n         */\n        this.BUFFER_LEVEL_STATE_CHANGED = 'bufferStateChanged';\n\n        /**\n         * Triggered when the buffer level of a media type has been updated\n         * @event MediaPlayerEvents#BUFFER_LEVEL_UPDATED\n         */\n        this.BUFFER_LEVEL_UPDATED = 'bufferLevelUpdated';\n\n        /**\n         * Triggered when a font signalled by a DVB Font Download has been added to the document FontFaceSet interface.\n         * @event MediaPlayerEvents#DVB_FONT_DOWNLOAD_ADDED\n         */\n        this.DVB_FONT_DOWNLOAD_ADDED = 'dvbFontDownloadAdded';\n\n        /**\n         * Triggered when a font signalled by a DVB Font Download has successfully downloaded and the FontFace can be used.\n         * @event MediaPlayerEvents#DVB_FONT_DOWNLOAD_COMPLETE\n         */\n        this.DVB_FONT_DOWNLOAD_COMPLETE = 'dvbFontDownloadComplete';\n\n        /**\n         * Triggered when a font signalled by a DVB Font Download could not be successfully downloaded, so the FontFace will not be used.\n         * @event MediaPlayerEvents#DVB_FONT_DOWNLOAD_FAILED\n         */\n        this.DVB_FONT_DOWNLOAD_FAILED = 'dvbFontDownloadFailed';\n\n        /**\n         * Triggered when a dynamic stream changed to static (transition phase between Live and On-Demand).\n         * @event MediaPlayerEvents#DYNAMIC_TO_STATIC\n         */\n        this.DYNAMIC_TO_STATIC = 'dynamicToStatic';\n\n        /**\n         * Triggered when there is an error from the element or MSE source buffer.\n         * @event MediaPlayerEvents#ERROR\n         */\n        this.ERROR = 'error';\n        /**\n         * Triggered when a fragment download has completed.\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_COMPLETED\n         */\n        this.FRAGMENT_LOADING_COMPLETED = 'fragmentLoadingCompleted';\n\n        /**\n         * Triggered when a partial fragment download has completed.\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_PROGRESS\n         */\n        this.FRAGMENT_LOADING_PROGRESS = 'fragmentLoadingProgress';\n        /**\n         * Triggered when a fragment download has started.\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_STARTED\n         */\n        this.FRAGMENT_LOADING_STARTED = 'fragmentLoadingStarted';\n\n        /**\n         * Triggered when a fragment download is abandoned due to detection of slow download base on the ABR abandon rule..\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_ABANDONED\n         */\n        this.FRAGMENT_LOADING_ABANDONED = 'fragmentLoadingAbandoned';\n\n        /**\n         * Triggered when {@link module:Debug} logger methods are called.\n         * @event MediaPlayerEvents#LOG\n         */\n        this.LOG = 'log';\n\n        /**\n         * Triggered when the manifest load is started\n         * @event MediaPlayerEvents#MANIFEST_LOADING_STARTED\n         */\n        this.MANIFEST_LOADING_STARTED = 'manifestLoadingStarted';\n\n        /**\n         * Triggered when the manifest loading is finished, providing the request object information\n         * @event MediaPlayerEvents#MANIFEST_LOADING_FINISHED\n         */\n        this.MANIFEST_LOADING_FINISHED = 'manifestLoadingFinished';\n\n        /**\n         * Triggered when the manifest load is complete, providing the payload\n         * @event MediaPlayerEvents#MANIFEST_LOADED\n         */\n        this.MANIFEST_LOADED = 'manifestLoaded';\n\n        /**\n         * Triggered anytime there is a change to the overall metrics.\n         * @event MediaPlayerEvents#METRICS_CHANGED\n         */\n        this.METRICS_CHANGED = 'metricsChanged';\n\n        /**\n         * Triggered when an individual metric is added, updated or cleared.\n         * @event MediaPlayerEvents#METRIC_CHANGED\n         */\n        this.METRIC_CHANGED = 'metricChanged';\n\n        /**\n         * Triggered every time a new metric is added.\n         * @event MediaPlayerEvents#METRIC_ADDED\n         */\n        this.METRIC_ADDED = 'metricAdded';\n\n        /**\n         * Triggered every time a metric is updated.\n         * @event MediaPlayerEvents#METRIC_UPDATED\n         */\n        this.METRIC_UPDATED = 'metricUpdated';\n\n        /**\n         * Triggered when a new stream (period) starts.\n         * @event MediaPlayerEvents#PERIOD_SWITCH_STARTED\n         */\n        this.PERIOD_SWITCH_STARTED = 'periodSwitchStarted';\n\n        /**\n         * Triggered at the stream end of a period.\n         * @event MediaPlayerEvents#PERIOD_SWITCH_COMPLETED\n         */\n        this.PERIOD_SWITCH_COMPLETED = 'periodSwitchCompleted';\n\n        /**\n         * Triggered when an ABR up /down switch is initiated; either by user in manual mode or auto mode via ABR rules.\n         * @event MediaPlayerEvents#QUALITY_CHANGE_REQUESTED\n         */\n        this.QUALITY_CHANGE_REQUESTED = 'qualityChangeRequested';\n\n        /**\n         * Triggered when the new ABR quality is being rendered on-screen.\n         * @event MediaPlayerEvents#QUALITY_CHANGE_RENDERED\n         */\n        this.QUALITY_CHANGE_RENDERED = 'qualityChangeRendered';\n\n        /**\n         * Triggered when the new track is being selected\n         * @event MediaPlayerEvents#NEW_TRACK_SELECTED\n         */\n        this.NEW_TRACK_SELECTED = 'newTrackSelected';\n\n        /**\n         * Triggered when the new track is being rendered.\n         * @event MediaPlayerEvents#TRACK_CHANGE_RENDERED\n         */\n        this.TRACK_CHANGE_RENDERED = 'trackChangeRendered';\n\n        /**\n         * Triggered when a stream (period) is being loaded\n         * @event MediaPlayerEvents#STREAM_INITIALIZING\n         */\n        this.STREAM_INITIALIZING = 'streamInitializing';\n\n        /**\n         * Triggered when a stream (period) is loaded\n         * @event MediaPlayerEvents#STREAM_UPDATED\n         */\n        this.STREAM_UPDATED = 'streamUpdated';\n\n        /**\n         * Triggered when a stream (period) is activated\n         * @event MediaPlayerEvents#STREAM_ACTIVATED\n         */\n        this.STREAM_ACTIVATED = 'streamActivated';\n\n        /**\n         * Triggered when a stream (period) is deactivated\n         * @event MediaPlayerEvents#STREAM_DEACTIVATED\n         */\n        this.STREAM_DEACTIVATED = 'streamDeactivated';\n\n        /**\n         * Triggered when a stream (period) is activated\n         * @event MediaPlayerEvents#STREAM_INITIALIZED\n         */\n        this.STREAM_INITIALIZED = 'streamInitialized';\n\n        /**\n         * Triggered when the player has been reset.\n         * @event MediaPlayerEvents#STREAM_TEARDOWN_COMPLETE\n         */\n        this.STREAM_TEARDOWN_COMPLETE = 'streamTeardownComplete';\n\n        /**\n         * Triggered once all text tracks detected in the MPD are added to the video element.\n         * @event MediaPlayerEvents#TEXT_TRACKS_ADDED\n         */\n        this.TEXT_TRACKS_ADDED = 'allTextTracksAdded';\n\n        /**\n         * Triggered when a text track is added to the video element's TextTrackList\n         * @event MediaPlayerEvents#TEXT_TRACK_ADDED\n         */\n        this.TEXT_TRACK_ADDED = 'textTrackAdded';\n\n        /**\n         * Triggered when a text track should be shown\n         * @event MediaPlayerEvents#CUE_ENTER\n         */\n        this.CUE_ENTER = 'cueEnter'\n\n        /**\n         * Triggered when a text track should be hidden\n         * @event MediaPlayerEvents#CUE_ENTER\n         */\n        this.CUE_EXIT = 'cueExit'\n\n        /**\n         * Triggered when a throughput measurement based on the last segment request has been stored\n         * @event MediaPlayerEvents#THROUGHPUT_MEASUREMENT_STORED\n         */\n        this.THROUGHPUT_MEASUREMENT_STORED = 'throughputMeasurementStored';\n\n        /**\n         * Triggered when a ttml chunk is parsed.\n         * @event MediaPlayerEvents#TTML_PARSED\n         */\n        this.TTML_PARSED = 'ttmlParsed';\n\n        /**\n         * Triggered when a ttml chunk has to be parsed.\n         * @event MediaPlayerEvents#TTML_TO_PARSE\n         */\n        this.TTML_TO_PARSE = 'ttmlToParse';\n\n        /**\n         * Triggered when a caption is rendered.\n         * @event MediaPlayerEvents#CAPTION_RENDERED\n         */\n        this.CAPTION_RENDERED = 'captionRendered';\n\n        /**\n         * Triggered when the caption container is resized.\n         * @event MediaPlayerEvents#CAPTION_CONTAINER_RESIZE\n         */\n        this.CAPTION_CONTAINER_RESIZE = 'captionContainerResize';\n\n        /**\n         * Sent when enough data is available that the media can be played,\n         * at least for a couple of frames.  This corresponds to the\n         * HAVE_ENOUGH_DATA readyState.\n         * @event MediaPlayerEvents#CAN_PLAY\n         */\n        this.CAN_PLAY = 'canPlay';\n\n        /**\n         * This corresponds to the CAN_PLAY_THROUGH readyState.\n         * @event MediaPlayerEvents#CAN_PLAY_THROUGH\n         */\n        this.CAN_PLAY_THROUGH = 'canPlayThrough';\n\n        /**\n         * Sent when playback completes.\n         * @event MediaPlayerEvents#PLAYBACK_ENDED\n         */\n        this.PLAYBACK_ENDED = 'playbackEnded';\n\n        /**\n         * Sent when an error occurs.  The element's error\n         * attribute contains more information.\n         * @event MediaPlayerEvents#PLAYBACK_ERROR\n         */\n        this.PLAYBACK_ERROR = 'playbackError';\n\n        /**\n         * This event is fired once the playback has been initialized by MediaPlayer.js.\n         * After that event methods such as setTextTrack() can be used.\n         * @event MediaPlayerEvents#PLAYBACK_INITIALIZED\n         */\n        this.PLAYBACK_INITIALIZED = 'playbackInitialized';\n\n        /**\n         * Sent when playback is not allowed (for example if user gesture is needed).\n         * @event MediaPlayerEvents#PLAYBACK_NOT_ALLOWED\n         */\n        this.PLAYBACK_NOT_ALLOWED = 'playbackNotAllowed';\n\n        /**\n         * The media's metadata has finished loading; all attributes now\n         * contain as much useful information as they're going to.\n         * @event MediaPlayerEvents#PLAYBACK_METADATA_LOADED\n         */\n        this.PLAYBACK_METADATA_LOADED = 'playbackMetaDataLoaded';\n\n        /**\n         * The event is fired when the frame at the current playback position of the media has finished loading;\n         * often the first frame\n         * @event MediaPlayerEvents#PLAYBACK_LOADED_DATA\n         */\n        this.PLAYBACK_LOADED_DATA = 'playbackLoadedData';\n\n        /**\n         * Sent when playback is paused.\n         * @event MediaPlayerEvents#PLAYBACK_PAUSED\n         */\n        this.PLAYBACK_PAUSED = 'playbackPaused';\n\n        /**\n         * Sent when the media begins to play (either for the first time, after having been paused,\n         * or after ending and then restarting).\n         *\n         * @event MediaPlayerEvents#PLAYBACK_PLAYING\n         */\n        this.PLAYBACK_PLAYING = 'playbackPlaying';\n\n        /**\n         * Sent periodically to inform interested parties of progress downloading\n         * the media. Information about the current amount of the media that has\n         * been downloaded is available in the media element's buffered attribute.\n         * @event MediaPlayerEvents#PLAYBACK_PROGRESS\n         */\n        this.PLAYBACK_PROGRESS = 'playbackProgress';\n\n        /**\n         * Sent when the playback speed changes.\n         * @event MediaPlayerEvents#PLAYBACK_RATE_CHANGED\n         */\n        this.PLAYBACK_RATE_CHANGED = 'playbackRateChanged';\n\n        /**\n         * Sent when a seek operation completes.\n         * @event MediaPlayerEvents#PLAYBACK_SEEKED\n         */\n        this.PLAYBACK_SEEKED = 'playbackSeeked';\n\n        /**\n         * Sent when a seek operation begins.\n         * @event MediaPlayerEvents#PLAYBACK_SEEKING\n         */\n        this.PLAYBACK_SEEKING = 'playbackSeeking';\n\n        /**\n         * Sent when the video element reports stalled\n         * @event MediaPlayerEvents#PLAYBACK_STALLED\n         */\n        this.PLAYBACK_STALLED = 'playbackStalled';\n\n        /**\n         * Sent when playback of the media starts after having been paused;\n         * that is, when playback is resumed after a prior pause event.\n         *\n         * @event MediaPlayerEvents#PLAYBACK_STARTED\n         */\n        this.PLAYBACK_STARTED = 'playbackStarted';\n\n        /**\n         * The time indicated by the element's currentTime attribute has changed.\n         * @event MediaPlayerEvents#PLAYBACK_TIME_UPDATED\n         */\n        this.PLAYBACK_TIME_UPDATED = 'playbackTimeUpdated';\n\n        /**\n         * Sent when the video element reports that the volume has changed\n         * @event MediaPlayerEvents#PLAYBACK_VOLUME_CHANGED\n         */\n        this.PLAYBACK_VOLUME_CHANGED = 'playbackVolumeChanged';\n\n        /**\n         * Sent when the media playback has stopped because of a temporary lack of data.\n         *\n         * @event MediaPlayerEvents#PLAYBACK_WAITING\n         */\n        this.PLAYBACK_WAITING = 'playbackWaiting';\n\n        /**\n         * Manifest validity changed - As a result of an MPD validity expiration event.\n         * @event MediaPlayerEvents#MANIFEST_VALIDITY_CHANGED\n         */\n        this.MANIFEST_VALIDITY_CHANGED = 'manifestValidityChanged';\n\n        /**\n         * Dash events are triggered at their respective start points on the timeline.\n         * @event MediaPlayerEvents#EVENT_MODE_ON_START\n         */\n        this.EVENT_MODE_ON_START = 'eventModeOnStart';\n\n        /**\n         * Dash events are triggered as soon as they were parsed.\n         * @event MediaPlayerEvents#EVENT_MODE_ON_RECEIVE\n         */\n        this.EVENT_MODE_ON_RECEIVE = 'eventModeOnReceive';\n\n        /**\n         * Event that is dispatched whenever the player encounters a potential conformance validation that might lead to unexpected/not optimal behavior\n         * @event MediaPlayerEvents#CONFORMANCE_VIOLATION\n         */\n        this.CONFORMANCE_VIOLATION = 'conformanceViolation';\n\n        /**\n         * Event that is dispatched whenever the player switches to a different representation\n         * @event MediaPlayerEvents#REPRESENTATION_SWITCH\n         */\n        this.REPRESENTATION_SWITCH = 'representationSwitch';\n\n        /**\n         * Event that is dispatched whenever an adaptation set is removed due to all representations not being supported.\n         * @event MediaPlayerEvents#ADAPTATION_SET_REMOVED_NO_CAPABILITIES\n         */\n        this.ADAPTATION_SET_REMOVED_NO_CAPABILITIES = 'adaptationSetRemovedNoCapabilities';\n\n        /**\n         * Triggered when a content steering request has completed.\n         * @event MediaPlayerEvents#CONTENT_STEERING_REQUEST_COMPLETED\n         */\n        this.CONTENT_STEERING_REQUEST_COMPLETED = 'contentSteeringRequestCompleted';\n\n        /**\n         * Triggered when an inband prft (ProducerReferenceTime) boxes has been received.\n         * @event MediaPlayerEvents#INBAND_PRFT\n         */\n        this.INBAND_PRFT = 'inbandPrft';\n\n        /**\n         * The streaming attribute of the Managed Media Source is true\n         * @type {string}\n         */\n        this.MANAGED_MEDIA_SOURCE_START_STREAMING = 'managedMediaSourceStartStreaming';\n\n        /**\n         * The streaming attribute of the Managed Media Source is false\n         * @type {string}\n         */\n        this.MANAGED_MEDIA_SOURCE_END_STREAMING = 'managedMediaSourceEndStreaming';\n    }\n}\n\nlet mediaPlayerEvents = new MediaPlayerEvents();\nexport default mediaPlayerEvents;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * Constants declaration\n */\nexport default {\n    /**\n     *  @constant {string} STREAM Stream media type. Mainly used to report metrics relative to the full stream\n     *  @memberof Constants#\n     *  @static\n     */\n    STREAM: 'stream',\n\n    /**\n     *  @constant {string} VIDEO Video media type\n     *  @memberof Constants#\n     *  @static\n     */\n    VIDEO: 'video',\n\n    /**\n     *  @constant {string} AUDIO Audio media type\n     *  @memberof Constants#\n     *  @static\n     */\n    AUDIO: 'audio',\n\n    /**\n     *  @constant {string} TEXT Text media type\n     *  @memberof Constants#\n     *  @static\n     */\n    TEXT: 'text',\n\n    /**\n     *  @constant {string} MUXED Muxed (video/audio in the same chunk) media type\n     *  @memberof Constants#\n     *  @static\n     */\n    MUXED: 'muxed',\n\n    /**\n     *  @constant {string} IMAGE Image media type\n     *  @memberof Constants#\n     *  @static\n     */\n    IMAGE: 'image',\n\n    /**\n     *  @constant {string} STPP STTP Subtitles format\n     *  @memberof Constants#\n     *  @static\n     */\n    STPP: 'stpp',\n\n    /**\n     *  @constant {string} TTML STTP Subtitles format\n     *  @memberof Constants#\n     *  @static\n     */\n    TTML: 'ttml',\n\n    /**\n     *  @constant {string} VTT STTP Subtitles format\n     *  @memberof Constants#\n     *  @static\n     */\n    VTT: 'vtt',\n\n    /**\n     *  @constant {string} WVTT STTP Subtitles format\n     *  @memberof Constants#\n     *  @static\n     */\n    WVTT: 'wvtt',\n\n    /**\n     *  @constant {string} Content Steering\n     *  @memberof Constants#\n     *  @static\n     */\n    CONTENT_STEERING: 'contentSteering',\n\n    /**\n     *  @constant {string} LIVE_CATCHUP_MODE_DEFAULT Throughput calculation based on moof parsing\n     *  @memberof Constants#\n     *  @static\n     */\n    LIVE_CATCHUP_MODE_DEFAULT: 'liveCatchupModeDefault',\n\n    /**\n     *  @constant {string} LIVE_CATCHUP_MODE_LOLP Throughput calculation based on moof parsing\n     *  @memberof Constants#\n     *  @static\n     */\n    LIVE_CATCHUP_MODE_LOLP: 'liveCatchupModeLoLP',\n\n    /**\n     *  @constant {string} MOVING_AVERAGE_SLIDING_WINDOW Moving average sliding window\n     *  @memberof Constants#\n     *  @static\n     */\n    MOVING_AVERAGE_SLIDING_WINDOW: 'slidingWindow',\n\n    /**\n     *  @constant {string} EWMA Exponential moving average\n     *  @memberof Constants#\n     *  @static\n     */\n    MOVING_AVERAGE_EWMA: 'ewma',\n\n    /**\n     *  @constant {string} BAD_ARGUMENT_ERROR Invalid Arguments type of error\n     *  @memberof Constants#\n     *  @static\n     */\n    BAD_ARGUMENT_ERROR: 'Invalid Arguments',\n\n    /**\n     *  @constant {string} MISSING_CONFIG_ERROR Missing configuration parameters type of error\n     *  @memberof Constants#\n     *  @static\n     */\n    MISSING_CONFIG_ERROR: 'Missing config parameter(s)',\n\n    /**\n     *  @constant {string} TRACK_SWITCH_MODE_ALWAYS_REPLACE used to clear the buffered data (prior to current playback position) after track switch. Default for audio\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SWITCH_MODE_ALWAYS_REPLACE: 'alwaysReplace',\n\n    /**\n     *  @constant {string} TRACK_SWITCH_MODE_NEVER_REPLACE used to forbid clearing the buffered data (prior to current playback position) after track switch. Defers to fastSwitchEnabled for placement of new data. Default for video\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SWITCH_MODE_NEVER_REPLACE: 'neverReplace',\n\n    /**\n     *  @constant {string} TRACK_SELECTION_MODE_FIRST_TRACK makes the player select the first track found in the manifest.\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SELECTION_MODE_FIRST_TRACK: 'firstTrack',\n\n    /**\n     *  @constant {string} TRACK_SELECTION_MODE_HIGHEST_BITRATE makes the player select the track with a highest bitrate. This mode is a default mode.\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SELECTION_MODE_HIGHEST_BITRATE: 'highestBitrate',\n\n    /**\n     *  @constant {string} TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY makes the player select the track with the lowest bitrate per pixel average.\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY: 'highestEfficiency',\n\n    /**\n     *  @constant {string} TRACK_SELECTION_MODE_WIDEST_RANGE makes the player select the track with a widest range of bitrates.\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SELECTION_MODE_WIDEST_RANGE: 'widestRange',\n\n    /**\n     *  @constant {string} CMCD_QUERY_KEY specifies the key that is used for the CMCD query parameter.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_QUERY_KEY: 'CMCD',\n\n    /**\n     *  @constant {string} CMCD_MODE_QUERY specifies to attach CMCD metrics as query parameters.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_MODE_QUERY: 'query',\n\n    /**\n     *  @constant {string} CMCD_MODE_HEADER specifies to attach CMCD metrics as HTTP headers.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_MODE_HEADER: 'header',\n\n    /**\n     *  @constant {string} CMCD_AVAILABLE_KEYS specifies all the available keys for CMCD metrics.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_AVAILABLE_KEYS: ['br', 'd', 'ot', 'tb', 'bl', 'dl', 'mtp', 'nor', 'nrr', 'su', 'bs', 'rtp', 'cid', 'pr', 'sf', 'sid', 'st', 'v'],\n    /**\n     *  @constant {string} CMCD_AVAILABLE_KEYS_V2 specifies all the available keys for CMCD version 2 metrics.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_V2_AVAILABLE_KEYS: ['msd', 'ltc'],\n\n    /**\n     *  @constant {string} CMCD_AVAILABLE_REQUESTS specifies all the available requests type for CMCD metrics.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_AVAILABLE_REQUESTS: ['segment', 'mpd', 'xlink', 'steering', 'other'],\n\n\n    INITIALIZE: 'initialize',\n    TEXT_SHOWING: 'showing',\n    TEXT_HIDDEN: 'hidden',\n    TEXT_DISABLED: 'disabled',\n    ACCESSIBILITY_CEA608_SCHEME: 'urn:scte:dash:cc:cea-608:2015',\n    CC1: 'CC1',\n    CC3: 'CC3',\n    UTF8: 'utf-8',\n    SCHEME_ID_URI: 'schemeIdUri',\n    START_TIME: 'starttime',\n    SERVICE_DESCRIPTION_DVB_LL_SCHEME: 'urn:dvb:dash:lowlatency:scope:2019',\n    SUPPLEMENTAL_PROPERTY_DVB_LL_SCHEME: 'urn:dvb:dash:lowlatency:critical:2019',\n    CTA_5004_2023_SCHEME: 'urn:mpeg:dash:cta-5004:2023',\n    THUMBNAILS_SCHEME_ID_URIS: ['http://dashif.org/thumbnail_tile', 'http://dashif.org/guidelines/thumbnail_tile'],\n    FONT_DOWNLOAD_DVB_SCHEME: 'urn:dvb:dash:fontdownload:2014',\n    COLOUR_PRIMARIES_SCHEME_ID_URI: 'urn:mpeg:mpegB:cicp:ColourPrimaries',\n    URL_QUERY_INFO_SCHEME: 'urn:mpeg:dash:urlparam:2014',\n    EXT_URL_QUERY_INFO_SCHEME: 'urn:mpeg:dash:urlparam:2016',\n    MATRIX_COEFFICIENTS_SCHEME_ID_URI: 'urn:mpeg:mpegB:cicp:MatrixCoefficients',\n    TRANSFER_CHARACTERISTICS_SCHEME_ID_URI: 'urn:mpeg:mpegB:cicp:TransferCharacteristics',\n    HDR_METADATA_FORMAT_SCHEME_ID_URI: 'urn:dvb:dash:hdr-dmi',\n    HDR_METADATA_FORMAT_VALUES: {\n        ST2094_10: 'ST2094-10',\n        SL_HDR2: 'SL-HDR2',\n        ST2094_40: 'ST2094-40'\n    },\n    MEDIA_CAPABILITIES_API: {\n        COLORGAMUT: {\n            SRGB: 'srgb',\n            P3: 'p3',\n            REC2020: 'rec2020'\n        },\n        TRANSFERFUNCTION: {\n            SRGB: 'srgb',\n            PQ: 'pq',\n            HLG: 'hlg'\n        },\n        HDR_METADATATYPE: {\n            SMPTE_ST_2094_10: 'smpteSt2094-10',\n            SLHDR2: 'slhdr2',\n            SMPTE_ST_2094_40: 'smpteSt2094-40'\n        }\n    },\n    XML: 'XML',\n    ARRAY_BUFFER: 'ArrayBuffer',\n    DVB_REPORTING_URL: 'dvb:reportingUrl',\n    DVB_PROBABILITY: 'dvb:probability',\n    OFF_MIMETYPE: 'application/font-sfnt',\n    WOFF_MIMETYPE: 'application/font-woff',\n    VIDEO_ELEMENT_READY_STATES: {\n        HAVE_NOTHING: 0,\n        HAVE_METADATA: 1,\n        HAVE_CURRENT_DATA: 2,\n        HAVE_FUTURE_DATA: 3,\n        HAVE_ENOUGH_DATA: 4\n    },\n    FILE_LOADER_TYPES: {\n        FETCH: 'fetch_loader',\n        XHR: 'xhr_loader'\n    },\n    THROUGHPUT_TYPES: {\n        LATENCY: 'throughput_type_latency',\n        BANDWIDTH: 'throughput_type_bandwidth'\n    },\n    THROUGHPUT_CALCULATION_MODES: {\n        EWMA: 'throughputCalculationModeEwma',\n        ZLEMA: 'throughputCalculationModeZlema',\n        ARITHMETIC_MEAN: 'throughputCalculationModeArithmeticMean',\n        BYTE_SIZE_WEIGHTED_ARITHMETIC_MEAN: 'throughputCalculationModeByteSizeWeightedArithmeticMean',\n        DATE_WEIGHTED_ARITHMETIC_MEAN: 'throughputCalculationModeDateWeightedArithmeticMean',\n        HARMONIC_MEAN: 'throughputCalculationModeHarmonicMean',\n        BYTE_SIZE_WEIGHTED_HARMONIC_MEAN: 'throughputCalculationModeByteSizeWeightedHarmonicMean',\n        DATE_WEIGHTED_HARMONIC_MEAN: 'throughputCalculationModeDateWeightedHarmonicMean',\n    },\n    LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE: {\n        MOOF_PARSING: 'lowLatencyDownloadTimeCalculationModeMoofParsing',\n        DOWNLOADED_DATA: 'lowLatencyDownloadTimeCalculationModeDownloadedData',\n        AAST: 'lowLatencyDownloadTimeCalculationModeAast',\n    },\n    RULES_TYPES: {\n        QUALITY_SWITCH_RULES: 'qualitySwitchRules',\n        ABANDON_FRAGMENT_RULES: 'abandonFragmentRules'\n    },\n    QUALITY_SWITCH_RULES: {\n        BOLA_RULE: 'BolaRule',\n        THROUGHPUT_RULE: 'ThroughputRule',\n        INSUFFICIENT_BUFFER_RULE: 'InsufficientBufferRule',\n        SWITCH_HISTORY_RULE: 'SwitchHistoryRule',\n        DROPPED_FRAMES_RULE: 'DroppedFramesRule',\n        LEARN_TO_ADAPT_RULE: 'L2ARule',\n        LOL_PLUS_RULE: 'LoLPRule'\n    },\n    ABANDON_FRAGMENT_RULES: {\n        ABANDON_REQUEST_RULE: 'AbandonRequestsRule'\n    },\n\n    /**\n     *  @constant {string} ID3_SCHEME_ID_URI specifies scheme ID URI for ID3 timed metadata\n     *  @memberof Constants#\n     *  @static\n     */\n    ID3_SCHEME_ID_URI: 'https://aomedia.org/emsg/ID3',\n    COMMON_ACCESS_TOKEN_HEADER: 'common-access-token',\n    DASH_ROLE_SCHEME_ID : 'urn:mpeg:dash:role:2011',\n    CODEC_FAMILIES: {\n        MP3: 'mp3',\n        AAC: 'aac',\n        AC3: 'ac3',\n        EC3: 'ec3',\n        DTSX: 'dtsx',\n        DTSC: 'dtsc',\n        AVC: 'avc',\n        HEVC: 'hevc'\n    }\n}\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport EventsBase from '../../core/events/EventsBase.js';\n\n/**\n * @class\n * @implements EventsBase\n */\nclass MetricsReportingEvents extends EventsBase {\n    constructor () {\n        super();\n\n        this.METRICS_INITIALISATION_COMPLETE = 'internal_metricsReportingInitialized';\n        this.BECAME_REPORTING_PLAYER = 'internal_becameReportingPlayer';\n\n        /**\n         * Triggered when CMCD data was generated for a HTTP request\n         * @event MetricsReportingEvents#CMCD_DATA_GENERATED\n         */\n        this.CMCD_DATA_GENERATED = 'cmcdDataGenerated';\n    }\n}\n\nlet metricsReportingEvents = new MetricsReportingEvents();\nexport default metricsReportingEvents;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport MetricsController from './MetricsController.js';\nimport ManifestParsing from '../utils/ManifestParsing.js';\nimport MetricsReportingEvents from '../MetricsReportingEvents.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction MetricsCollectionController(config) {\n\n    config = config || {};\n    let instance;\n    let metricsControllers = {};\n    let context = this.context;\n    let eventBus = config.eventBus;\n    const events = config.events;\n\n    function update(e) {\n        if (e.error) {\n            return;\n        }\n\n        // start by assuming all existing controllers need removing\n        let controllersToRemove = Object.keys(metricsControllers);\n\n        const metrics = ManifestParsing(context).getInstance({\n            adapter: config.adapter,\n            constants: config.constants\n        }).getMetrics(e.manifest);\n\n        metrics.forEach(m => {\n            const key = JSON.stringify(m);\n\n            if (!metricsControllers.hasOwnProperty(key)) {\n                try {\n                    let controller = MetricsController(context).create(config);\n                    controller.initialize(m);\n                    metricsControllers[key] = controller;\n                } catch (e) {\n                    // fail quietly\n                }\n            } else {\n                // we still need this controller - delete from removal list\n                controllersToRemove.splice(key, 1);\n            }\n        });\n\n        // now remove the unwanted controllers\n        controllersToRemove.forEach(c => {\n            metricsControllers[c].reset();\n            delete metricsControllers[c];\n        });\n\n        eventBus.trigger(MetricsReportingEvents.METRICS_INITIALISATION_COMPLETE);\n    }\n\n    function resetMetricsControllers() {\n        Object.keys(metricsControllers).forEach(key => {\n            metricsControllers[key].reset();\n        });\n\n        metricsControllers = {};\n    }\n\n    function setup() {\n        eventBus.on(events.MANIFEST_UPDATED, update, instance);\n        eventBus.on(events.STREAM_TEARDOWN_COMPLETE, resetMetricsControllers, instance);\n    }\n\n    function reset() {\n        eventBus.off(events.MANIFEST_UPDATED, update, instance);\n        eventBus.off(events.STREAM_TEARDOWN_COMPLETE, resetMetricsControllers, instance);\n    }\n\n    instance = {\n        reset: reset\n    };\n\n    setup();\n    return instance;\n}\n\nMetricsCollectionController.__dashjs_factory_name = 'MetricsCollectionController';\nexport default FactoryMaker.getClassFactory(MetricsCollectionController); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport RangeController from './RangeController.js';\nimport ReportingController from './ReportingController.js';\nimport MetricsHandlersController from './MetricsHandlersController.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction MetricsController(config) {\n\n    config = config || {};\n    let metricsHandlersController,\n        reportingController,\n        rangeController,\n        instance;\n\n    let context = this.context;\n\n    function initialize(metricsEntry) {\n        try {\n            rangeController = RangeController(context).create({\n                mediaElement: config.mediaElement\n            });\n\n            rangeController.initialize(metricsEntry.Range);\n\n            reportingController = ReportingController(context).create({\n                debug: config.debug,\n                metricsConstants: config.metricsConstants,\n                mediaPlayerModel: config.mediaPlayerModel\n            });\n\n            reportingController.initialize(metricsEntry.Reporting, rangeController);\n\n            metricsHandlersController = MetricsHandlersController(context).create({\n                debug: config.debug,\n                eventBus: config.eventBus,\n                metricsConstants: config.metricsConstants,\n                events: config.events\n            });\n\n            metricsHandlersController.initialize(metricsEntry.metrics, reportingController);\n        } catch (e) {\n            reset();\n            throw e;\n        }\n    }\n\n    function reset() {\n        if (metricsHandlersController) {\n            metricsHandlersController.reset();\n        }\n\n        if (reportingController) {\n            reportingController.reset();\n        }\n\n        if (rangeController) {\n            rangeController.reset();\n        }\n    }\n\n    instance = {\n        initialize: initialize,\n        reset:      reset\n    };\n\n    return instance;\n}\n\nMetricsController.__dashjs_factory_name = 'MetricsController';\nexport default FactoryMaker.getClassFactory(MetricsController); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport MetricsHandlerFactory from '../metrics/MetricsHandlerFactory.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction MetricsHandlersController(config) {\n\n    config = config || {};\n    let handlers = [];\n\n    let instance;\n    const context = this.context;\n    const eventBus = config.eventBus;\n    const Events = config.events;\n\n    let metricsHandlerFactory = MetricsHandlerFactory(context).getInstance({\n        debug: config.debug,\n        eventBus: config.eventBus,\n        metricsConstants: config.metricsConstants\n    });\n\n    function handle(e) {\n        handlers.forEach(handler => {\n            handler.handleNewMetric(e.metric, e.value, e.mediaType);\n        });\n    }\n\n    function initialize(metrics, reportingController) {\n        metrics.split(',').forEach(\n            (m, midx, ms) => {\n                let handler;\n\n                // there is a bug in ISO23009-1 where the metrics attribute\n                // is a comma-separated list but HttpList key can contain a\n                // comma enclosed by ().\n                if ((m.indexOf('(') !== -1) && m.indexOf(')') === -1) {\n                    let nextm = ms[midx + 1];\n\n                    if (nextm &&\n                            (nextm.indexOf('(') === -1) &&\n                            (nextm.indexOf(')') !== -1)) {\n                        m += ',' + nextm;\n\n                        // delete the next metric so forEach does not visit.\n                        delete ms[midx + 1];\n                    }\n                }\n\n                handler = metricsHandlerFactory.create(\n                    m,\n                    reportingController\n                );\n\n                if (handler) {\n                    handlers.push(handler);\n                }\n            }\n        );\n\n        eventBus.on(\n            Events.METRIC_ADDED,\n            handle,\n            instance\n        );\n\n        eventBus.on(\n            Events.METRIC_UPDATED,\n            handle,\n            instance\n        );\n    }\n\n    function reset() {\n        eventBus.off(\n            Events.METRIC_ADDED,\n            handle,\n            instance\n        );\n\n        eventBus.off(\n            Events.METRIC_UPDATED,\n            handle,\n            instance\n        );\n\n        handlers.forEach(handler => handler.reset());\n\n        handlers = [];\n    }\n\n    instance = {\n        initialize: initialize,\n        reset:      reset\n    };\n\n    return instance;\n}\n\nMetricsHandlersController.__dashjs_factory_name = 'MetricsHandlersController';\nexport default FactoryMaker.getClassFactory(MetricsHandlersController); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport CustomTimeRanges from '../../utils/CustomTimeRanges.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction RangeController(config) {\n\n    config = config || {};\n    let useWallClockTime = false;\n    let context = this.context;\n    let instance,\n        ranges;\n\n    let mediaElement = config.mediaElement;\n\n    function initialize(rs) {\n        if (rs && rs.length) {\n            rs.forEach(r => {\n                let start = r.starttime;\n                let end = start + r.duration;\n\n                ranges.add(start, end);\n            });\n\n            useWallClockTime = !!rs[0]._useWallClockTime;\n        }\n    }\n\n    function reset() {\n        ranges.clear();\n    }\n\n    function setup() {\n        ranges = CustomTimeRanges(context).create();\n    }\n\n    function isEnabled() {\n        let numRanges = ranges.length;\n        let time;\n\n        if (!numRanges) {\n            return true;\n        }\n\n        // When not present, DASH Metrics reporting is requested\n        // for the whole duration of the content.\n        time = useWallClockTime ?\n            (new Date().getTime() / 1000) :\n            mediaElement.currentTime;\n\n        for (let i = 0; i < numRanges; i += 1) {\n            let start = ranges.start(i);\n            let end = ranges.end(i);\n\n            if ((start <= time) && (time < end)) {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    instance = {\n        initialize: initialize,\n        reset: reset,\n        isEnabled: isEnabled\n    };\n\n    setup();\n\n    return instance;\n}\n\nRangeController.__dashjs_factory_name = 'RangeController';\nexport default FactoryMaker.getClassFactory(RangeController); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport ReportingFactory from '../reporting/ReportingFactory.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction ReportingController(config) {\n\n    let reporters = [];\n    let instance;\n\n    const reportingFactory = ReportingFactory(this.context).getInstance(config);\n\n    function initialize(reporting, rangeController) {\n        // \"if multiple Reporting elements are present, it is expected that\n        // the client processes one of the recognized reporting schemes.\"\n        // to ignore this, and support multiple Reporting per Metric,\n        // simply change the 'some' below to 'forEach'\n        reporting.some(r => {\n            let reporter = reportingFactory.create(r, rangeController);\n\n            if (reporter) {\n                reporters.push(reporter);\n                return true;\n            }\n        });\n    }\n\n    function reset() {\n        reporters.forEach(r => r.reset());\n        reporters = [];\n    }\n\n    function report(type, vos) {\n        reporters.forEach(r => r.report(type, vos));\n    }\n\n    instance = {\n        initialize: initialize,\n        reset:      reset,\n        report:     report\n    };\n\n    return instance;\n}\n\nReportingController.__dashjs_factory_name = 'ReportingController';\nexport default FactoryMaker.getClassFactory(ReportingController); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport BufferLevel from './handlers/BufferLevelHandler.js';\nimport DVBErrors from './handlers/DVBErrorsHandler.js';\nimport HttpList from './handlers/HttpListHandler.js';\nimport GenericMetricHandler from './handlers/GenericMetricHandler.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction MetricsHandlerFactory(config) {\n\n    config = config || {};\n    let instance;\n    const logger = config.debug ? config.debug.getLogger(instance) : {};\n\n    // group 1: key, [group 3: n [, group 5: type]]\n    let keyRegex = /([a-zA-Z]*)(\\(([0-9]*)(\\,\\s*([a-zA-Z]*))?\\))?/;\n\n    const context = this.context;\n    let knownFactoryProducts = {\n        BufferLevel:    BufferLevel,\n        DVBErrors:      DVBErrors,\n        HttpList:       HttpList,\n        PlayList:       GenericMetricHandler,\n        RepSwitchList:  GenericMetricHandler,\n        TcpList:        GenericMetricHandler\n    };\n\n    function create(listType, reportingController) {\n        var matches = listType.match(keyRegex);\n        var handler;\n\n        if (!matches) {\n            return;\n        }\n\n        try {\n            handler = knownFactoryProducts[matches[1]](context).create({\n                eventBus: config.eventBus,\n                metricsConstants: config.metricsConstants\n            });\n\n            handler.initialize(\n                matches[1],\n                reportingController,\n                matches[3],\n                matches[5]\n            );\n        } catch (e) {\n            handler = null;\n            logger.error(`MetricsHandlerFactory: Could not create handler for type ${matches[1]} with args ${matches[3]}, ${matches[5]} (${e.message})`);\n        }\n\n        return handler;\n    }\n\n    function register(key, handler) {\n        knownFactoryProducts[key] = handler;\n    }\n\n    function unregister(key) {\n        delete knownFactoryProducts[key];\n    }\n\n    instance = {\n        create:     create,\n        register:   register,\n        unregister: unregister\n    };\n\n    return instance;\n}\n\nMetricsHandlerFactory.__dashjs_factory_name = 'MetricsHandlerFactory';\nexport default FactoryMaker.getSingletonFactory(MetricsHandlerFactory); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport HandlerHelpers from '../../utils/HandlerHelpers.js';\nimport FactoryMaker from '../../../../core/FactoryMaker.js';\n\nfunction BufferLevelHandler(config) {\n\n    config = config || {};\n    let instance,\n        reportingController,\n        n,\n        name,\n        interval,\n        lastReportedTime;\n\n    let context = this.context;\n    let handlerHelpers = HandlerHelpers(context).getInstance();\n\n    let storedVOs = [];\n\n    const metricsConstants = config.metricsConstants;\n\n    function getLowestBufferLevelVO() {\n        try {\n            return Object.keys(storedVOs).map(\n                key => storedVOs[key]\n            ).reduce(\n                (a, b) => {\n                    return (a.level < b.level) ? a : b;\n                }\n            );\n        } catch (e) {\n            return;\n        }\n    }\n\n    function intervalCallback() {\n        let vo = getLowestBufferLevelVO();\n\n        if (vo) {\n            if (lastReportedTime !== vo.t) {\n                lastReportedTime = vo.t;\n                reportingController.report(name, vo);\n            }\n        }\n    }\n\n    function initialize(basename, rc, n_ms) {\n        if (rc) {\n            // this will throw if n is invalid, to be\n            // caught by the initialize caller.\n            n = handlerHelpers.validateN(n_ms);\n            reportingController = rc;\n            name = handlerHelpers.reconstructFullMetricName(basename, n_ms);\n            interval = setInterval(intervalCallback, n);\n        }\n    }\n\n    function reset() {\n        clearInterval(interval);\n        interval = null;\n        n = 0;\n        reportingController = null;\n        lastReportedTime = null;\n    }\n\n    function handleNewMetric(metric, vo, type) {\n        if (metric === metricsConstants.BUFFER_LEVEL) {\n            storedVOs[type] = vo;\n        }\n    }\n\n    instance = {\n        initialize:         initialize,\n        reset:              reset,\n        handleNewMetric:    handleNewMetric\n    };\n\n    return instance;\n}\n\nBufferLevelHandler.__dashjs_factory_name = 'BufferLevelHandler';\nexport default FactoryMaker.getClassFactory(BufferLevelHandler); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport MetricsReportingEvents from '../../MetricsReportingEvents.js';\nimport FactoryMaker from '../../../../core/FactoryMaker.js';\n\nfunction DVBErrorsHandler(config) {\n\n    config = config || {};\n    let instance,\n        reportingController;\n\n    let eventBus = config.eventBus;\n    const metricsConstants = config.metricsConstants;\n\n    function onInitialisationComplete() {\n        // we only want to report this once per call to initialize\n        eventBus.off(\n            MetricsReportingEvents.METRICS_INITIALISATION_COMPLETE,\n            onInitialisationComplete,\n            this\n        );\n\n        // Note: A Player becoming a reporting Player is itself\n        // something which is recorded by the DVBErrors metric.\n        eventBus.trigger(MetricsReportingEvents.BECAME_REPORTING_PLAYER);\n    }\n\n    function initialize(unused, rc) {\n        if (rc) {\n            reportingController = rc;\n\n            eventBus.on(\n                MetricsReportingEvents.METRICS_INITIALISATION_COMPLETE,\n                onInitialisationComplete,\n                this\n            );\n        }\n    }\n\n    function reset() {\n        reportingController = null;\n    }\n\n    function handleNewMetric(metric, vo) {\n        // simply pass metric straight through\n        if (metric === metricsConstants.DVB_ERRORS) {\n            if (reportingController) {\n                reportingController.report(metric, vo);\n            }\n        }\n    }\n\n    instance = {\n        initialize:         initialize,\n        reset:              reset,\n        handleNewMetric:    handleNewMetric\n    };\n\n    return instance;\n}\n\nexport default FactoryMaker.getClassFactory(DVBErrorsHandler); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport FactoryMaker from '../../../../core/FactoryMaker.js';\n\n/**\n * @ignore\n */\nfunction GenericMetricHandler() {\n\n    let instance,\n        metricName,\n        reportingController;\n\n    function initialize(name, rc) {\n        metricName = name;\n        reportingController = rc;\n    }\n\n    function reset() {\n        reportingController = null;\n        metricName = undefined;\n    }\n\n    function handleNewMetric(metric, vo) {\n        // simply pass metric straight through\n        if (metric === metricName) {\n            if (reportingController) {\n                reportingController.report(metricName, vo);\n            }\n        }\n    }\n\n    instance = {\n        initialize: initialize,\n        reset: reset,\n        handleNewMetric: handleNewMetric\n    };\n\n    return instance;\n}\n\nGenericMetricHandler.__dashjs_factory_name = 'GenericMetricHandler';\nexport default FactoryMaker.getClassFactory(GenericMetricHandler); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport HandlerHelpers from '../../utils/HandlerHelpers.js';\nimport FactoryMaker from '../../../../core/FactoryMaker.js';\n\nfunction HttpListHandler(config) {\n\n    config = config || {};\n    let instance,\n        reportingController,\n        n,\n        type,\n        name,\n        interval;\n\n    let storedVos = [];\n\n    let handlerHelpers = HandlerHelpers(this.context).getInstance();\n\n    const metricsConstants = config.metricsConstants;\n\n    function intervalCallback() {\n        var vos = storedVos;\n\n        if (vos.length) {\n            if (reportingController) {\n                reportingController.report(name, vos);\n            }\n        }\n\n        storedVos = [];\n    }\n\n    function initialize(basename, rc, n_ms, requestType) {\n        if (rc) {\n\n            // this will throw if n is invalid, to be\n            // caught by the initialize caller.\n            n = handlerHelpers.validateN(n_ms);\n\n            reportingController = rc;\n\n            if (requestType && requestType.length) {\n                type = requestType;\n            }\n\n            name = handlerHelpers.reconstructFullMetricName(\n                basename,\n                n_ms,\n                requestType\n            );\n\n            interval = setInterval(intervalCallback, n);\n        }\n    }\n\n    function reset() {\n        clearInterval(interval);\n        interval = null;\n        n = null;\n        type = null;\n        storedVos = [];\n        reportingController = null;\n    }\n\n    function handleNewMetric(metric, vo) {\n        if (metric === metricsConstants.HTTP_REQUEST) {\n            if (!type || (type === vo.type)) {\n                storedVos.push(vo);\n            }\n        }\n    }\n\n    instance = {\n        initialize:         initialize,\n        reset:              reset,\n        handleNewMetric:    handleNewMetric\n    };\n\n    return instance;\n}\n\nHttpListHandler.__dashjs_factory_name = 'HttpListHandler';\nexport default FactoryMaker.getClassFactory(HttpListHandler); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport DVBReporting from './reporters/DVBReporting.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction ReportingFactory(config) {\n    config = config || {};\n\n    const knownReportingSchemeIdUris = {\n        'urn:dvb:dash:reporting:2014': DVBReporting\n    };\n\n    const context = this.context;\n    let instance;\n    const logger = config.debug ? config.debug.getLogger(instance) : {};\n    const metricsConstants = config.metricsConstants;\n    const mediaPlayerModel = config.mediaPlayerModel || {};\n\n    function create(entry, rangeController) {\n        let reporting;\n\n        try {\n            reporting = knownReportingSchemeIdUris[entry.schemeIdUri](context).create({\n                metricsConstants: metricsConstants,\n                mediaPlayerModel: mediaPlayerModel\n            });\n\n            reporting.initialize(entry, rangeController);\n        } catch (e) {\n            reporting = null;\n            logger.error(`ReportingFactory: could not create Reporting with schemeIdUri ${entry.schemeIdUri} (${e.message})`);\n        }\n\n        return reporting;\n    }\n\n    function register(schemeIdUri, moduleName) {\n        knownReportingSchemeIdUris[schemeIdUri] = moduleName;\n    }\n\n    function unregister(schemeIdUri) {\n        delete knownReportingSchemeIdUris[schemeIdUri];\n    }\n\n    instance = {\n        create:     create,\n        register:   register,\n        unregister: unregister\n    };\n\n    return instance;\n}\n\nReportingFactory.__dashjs_factory_name = 'ReportingFactory';\nexport default FactoryMaker.getSingletonFactory(ReportingFactory); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport MetricSerialiser from '../../utils/MetricSerialiser.js';\nimport RNG from '../../utils/RNG.js';\nimport CustomParametersModel from '../../../models/CustomParametersModel.js';\nimport FactoryMaker from '../../../../core/FactoryMaker.js';\n\nfunction DVBReporting(config) {\n    config = config || {};\n    let instance;\n\n    let context = this.context;\n    let metricSerialiser,\n        customParametersModel,\n        randomNumberGenerator,\n        reportingPlayerStatusDecided,\n        isReportingPlayer,\n        reportingUrl,\n        rangeController;\n\n    let USE_DRAFT_DVB_SPEC = true;\n    let allowPendingRequestsToCompleteOnReset = true;\n    let pendingRequests = [];\n\n    const metricsConstants = config.metricsConstants;\n\n    function setup() {\n        metricSerialiser = MetricSerialiser(context).getInstance();\n        randomNumberGenerator = RNG(context).getInstance();\n        customParametersModel = CustomParametersModel(context).getInstance();\n\n        resetInitialSettings();\n    }\n\n    function doGetRequest(url, successCB, failureCB) {\n        let req = new XMLHttpRequest();\n        req.withCredentials = customParametersModel.getXHRWithCredentialsForType(metricsConstants.HTTP_REQUEST_DVB_REPORTING_TYPE);\n        const oncomplete = function () {\n            let reqIndex = pendingRequests.indexOf(req);\n\n            if (reqIndex === -1) {\n                return;\n            } else {\n                pendingRequests.splice(reqIndex, 1);\n            }\n\n            if ((req.status >= 200) && (req.status < 300)) {\n                if (successCB) {\n                    successCB();\n                }\n            } else {\n                if (failureCB) {\n                    failureCB();\n                }\n            }\n        };\n\n        pendingRequests.push(req);\n\n        try {\n            req.open('GET', url);\n            req.onloadend = oncomplete;\n            req.onerror = oncomplete;\n            req.send();\n        } catch (e) {\n            req.onerror();\n        }\n    }\n\n    function report(type, vos) {\n        if (!Array.isArray(vos)) {\n            vos = [vos];\n        }\n\n        // If the Player is not a reporting Player, then the Player shall\n        // not report any errors.\n        // ... In addition to any time restrictions specified by a Range\n        // element within the Metrics element.\n        if (isReportingPlayer && rangeController.isEnabled()) {\n\n            // This reporting mechanism operates by creating one HTTP GET\n            // request for every entry in the top level list of the metric.\n            vos.forEach(function (vo) {\n                let url = metricSerialiser.serialise(vo);\n\n                // this has been proposed for errata\n                if (USE_DRAFT_DVB_SPEC && (type !== metricsConstants.DVB_ERRORS)) {\n                    url = `metricname=${type}&${url}`;\n                }\n\n                // Take the value of the @reportingUrl attribute, append a\n                // question mark ('?') character and then append the string\n                // created in the previous step.\n                url = `${reportingUrl}?${url}`;\n\n                // Make an HTTP GET request to the URL contained within the\n                // string created in the previous step.\n                doGetRequest(url, null, function () {\n                    // If the Player is unable to make the report, for\n                    // example because the @reportingUrl is invalid, the\n                    // host cannot be reached, or an HTTP status code other\n                    // than one in the 200 series is received, the Player\n                    // shall cease being a reporting Player for the\n                    // duration of the MPD.\n                    isReportingPlayer = false;\n                });\n            });\n        }\n    }\n\n    function initialize(entry, rc) {\n        let probability;\n\n        rangeController = rc;\n\n        reportingUrl = entry.dvbReportingUrl;\n\n        // If a required attribute is missing, the Reporting descriptor may\n        // be ignored by the Player\n        if (!reportingUrl) {\n            throw new Error(\n                'required parameter missing (dvb:reportingUrl)'\n            );\n        }\n\n        // A Player's status, as a reporting Player or not, shall remain\n        // static for the duration of the MPD, regardless of MPD updates.\n        // (i.e. only calling reset (or failure) changes this state)\n        if (!reportingPlayerStatusDecided) {\n            probability = entry.dvbProbability;\n            // TS 103 285 Clause *********\n            // If the @probability attribute is set to 1000, it shall be a reporting Player.\n            // If the @probability attribute is absent it will take the default value of 1000.\n            // For any other value of the @probability attribute, it shall decide at random whether to be a\n            // reporting Player, such that the probability of being one is @probability/1000.\n            if (probability && (probability === 1000 || ((probability / 1000) >= randomNumberGenerator.random()))) {\n                isReportingPlayer = true;\n            }\n\n            reportingPlayerStatusDecided = true;\n        }\n    }\n\n    function resetInitialSettings() {\n        reportingPlayerStatusDecided = false;\n        isReportingPlayer = false;\n        reportingUrl = null;\n        rangeController = null;\n    }\n\n    function reset() {\n        if (!allowPendingRequestsToCompleteOnReset) {\n            pendingRequests.forEach(req => req.abort());\n            pendingRequests = [];\n        }\n\n        resetInitialSettings();\n    }\n\n    instance = {\n        report:     report,\n        initialize: initialize,\n        reset:      reset\n    };\n\n    setup();\n\n    return instance;\n}\n\nDVBReporting.__dashjs_factory_name = 'DVBReporting';\nexport default FactoryMaker.getClassFactory(DVBReporting); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport DVBErrors from '../vo/DVBErrors.js';\nimport MetricsReportingEvents from '../MetricsReportingEvents.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction DVBErrorsTranslator(config) {\n\n    config = config || {};\n    let instance,\n        mpd;\n    const eventBus = config.eventBus;\n    const dashMetrics = config.dashMetrics;\n    const metricsConstants = config.metricsConstants;\n    //MediaPlayerEvents have been added to Events in MediaPlayer class\n    const Events = config.events;\n\n    function report(vo) {\n        let o = new DVBErrors();\n\n        if (!mpd) {\n            return;\n        }\n\n        for (const key in vo) {\n            if (vo.hasOwnProperty(key)) {\n                o[key] = vo[key];\n            }\n        }\n\n        if (!o.mpdurl) {\n            o.mpdurl = mpd.originalUrl || mpd.url;\n        }\n\n        if (!o.terror) {\n            o.terror = new Date();\n        }\n\n        dashMetrics.addDVBErrors(o);\n    }\n\n    function onManifestUpdate(e) {\n        if (e.error) {\n            return;\n        }\n\n        mpd = e.manifest;\n    }\n\n    function onServiceLocationChanged(e) {\n        report({\n            errorcode: DVBErrors.BASE_URL_CHANGED,\n            servicelocation: e.entry\n        });\n    }\n\n    function onBecameReporter() {\n        report({\n            errorcode: DVBErrors.BECAME_REPORTER\n        });\n    }\n\n    function handleHttpMetric(vo) {\n        if ((vo.responsecode === 0) || // connection failure - unknown\n            (vo.responsecode == null) || // Generated on .catch() and when uninitialized\n            (vo.responsecode >= 400) || // HTTP error status code\n            (vo.responsecode < 100) || // unknown status codes\n            (vo.responsecode >= 600)) { // unknown status codes\n            report({\n                errorcode: vo.responsecode || DVBErrors.CONNECTION_ERROR,\n                url: vo.url,\n                terror: vo.tresponse,\n                servicelocation: vo._serviceLocation\n            });\n        }\n    }\n\n    function onMetricEvent(e) {\n        switch (e.metric) {\n            case metricsConstants.HTTP_REQUEST:\n                handleHttpMetric(e.value);\n                break;\n            default:\n                break;\n        }\n    }\n\n    function onPlaybackError(e) {\n        let reason = e.error ? e.error.code : 0;\n        let errorcode;\n\n        switch (reason) {\n            case MediaError.MEDIA_ERR_NETWORK:\n                errorcode = DVBErrors.CONNECTION_ERROR;\n                break;\n            case MediaError.MEDIA_ERR_DECODE:\n                errorcode = DVBErrors.CORRUPT_MEDIA_OTHER;\n                break;\n            default:\n                return;\n        }\n\n        report({\n            errorcode: errorcode\n        });\n    }\n\n    function initialize() {\n        eventBus.on(Events.MANIFEST_UPDATED, onManifestUpdate, instance);\n        eventBus.on(\n            Events.SERVICE_LOCATION_BASE_URL_BLACKLIST_CHANGED,\n            onServiceLocationChanged,\n            instance\n        );\n        eventBus.on(Events.METRIC_ADDED, onMetricEvent, instance);\n        eventBus.on(Events.METRIC_UPDATED, onMetricEvent, instance);\n        eventBus.on(Events.PLAYBACK_ERROR, onPlaybackError, instance);\n        eventBus.on(\n            MetricsReportingEvents.BECAME_REPORTING_PLAYER,\n            onBecameReporter,\n            instance\n        );\n    }\n\n    function reset() {\n        eventBus.off(Events.MANIFEST_UPDATED, onManifestUpdate, instance);\n        eventBus.off(\n            Events.SERVICE_LOCATION_BASE_URL_BLACKLIST_CHANGED,\n            onServiceLocationChanged,\n            instance\n        );\n        eventBus.off(Events.METRIC_ADDED, onMetricEvent, instance);\n        eventBus.off(Events.METRIC_UPDATED, onMetricEvent, instance);\n        eventBus.off(Events.PLAYBACK_ERROR, onPlaybackError, instance);\n        eventBus.off(\n            MetricsReportingEvents.BECAME_REPORTING_PLAYER,\n            onBecameReporter,\n            instance\n        );\n    }\n\n    instance = {\n        initialize,\n        reset\n    };\n\n    return instance;\n}\n\nDVBErrorsTranslator.__dashjs_factory_name = 'DVBErrorsTranslator';\nexport default FactoryMaker.getSingletonFactory(DVBErrorsTranslator); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\n/**\n * @ignore\n */\nfunction HandlerHelpers() {\n    return {\n        reconstructFullMetricName: function (key, n, type) {\n            let mn = key;\n\n            if (n) {\n                mn += '(' + n;\n\n                if (type && type.length) {\n                    mn += ',' + type;\n                }\n\n                mn += ')';\n            }\n\n            return mn;\n        },\n\n        validateN: function (n_ms) {\n            if (!n_ms) {\n                throw new Error('missing n');\n            }\n\n            if (isNaN(n_ms)) {\n                throw new Error('n is NaN');\n            }\n\n            // n is a positive integer is defined to refer to the metric\n            // in which the buffer level is recorded every n ms.\n            if (n_ms < 0) {\n                throw new Error('n must be positive');\n            }\n\n            return n_ms;\n        }\n    };\n}\n\nHandlerHelpers.__dashjs_factory_name = 'HandlerHelpers';\nexport default FactoryMaker.getSingletonFactory(HandlerHelpers); \n", "import Metrics from '../vo/Metrics.js';\nimport Range from '../vo/Range.js';\nimport Reporting from '../vo/Reporting.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction ManifestParsing (config) {\n    config = config || {};\n    let instance;\n    let adapter = config.adapter;\n    const constants = config.constants;\n\n    function getMetricsRangeStartTime(manifest, dynamic, range) {\n        let voPeriods,\n            reportingStartTime;\n        let presentationStartTime = 0;\n\n        if (dynamic) {\n            // For services with MPD@type='dynamic', the start time is\n            // indicated in wall clock time by adding the value of this\n            // attribute to the value of the MPD@availabilityStartTime\n            // attribute.\n            presentationStartTime = adapter.getAvailabilityStartTime(manifest) / 1000;\n        } else {\n            // For services with MPD@type='static', the start time is indicated\n            // in Media Presentation time and is relative to the PeriodStart\n            // time of the first Period in this MPD.\n            voPeriods = adapter.getRegularPeriods(manifest);\n\n            if (voPeriods.length) {\n                presentationStartTime = voPeriods[0].start;\n            }\n        }\n\n        // When not present, DASH Metrics collection is\n        // requested from the beginning of content\n        // consumption.\n        reportingStartTime = presentationStartTime;\n\n        if (range && range.hasOwnProperty(constants.START_TIME)) {\n            reportingStartTime += range.starttime;\n        }\n\n        return reportingStartTime;\n    }\n\n    function getMetrics(manifest) {\n        let metrics = [];\n\n        if (manifest && manifest.Metrics) {\n            manifest.Metrics.forEach(metric => {\n                var metricEntry = new Metrics();\n                var isDynamic = adapter.getIsDynamic(manifest);\n\n                if (metric.hasOwnProperty('metrics')) {\n                    metricEntry.metrics = metric.metrics;\n                } else {\n                    return;\n                }\n\n                if (metric.Range) {\n                    metric.Range.forEach(range => {\n                        var rangeEntry = new Range();\n\n                        rangeEntry.starttime =\n                            getMetricsRangeStartTime(manifest, isDynamic, range);\n\n                        if (range.hasOwnProperty('duration')) {\n                            rangeEntry.duration = range.duration;\n                        } else {\n                            // if not present, the value is identical to the\n                            // Media Presentation duration.\n                            rangeEntry.duration = adapter.getDuration(manifest);\n                        }\n\n                        rangeEntry._useWallClockTime = isDynamic;\n\n                        metricEntry.Range.push(rangeEntry);\n                    });\n                }\n\n                if (metric.Reporting) {\n                    metric.Reporting.forEach(reporting => {\n                        var reportingEntry = new Reporting();\n\n                        if (reporting.hasOwnProperty(constants.SCHEME_ID_URI)) {\n                            reportingEntry.schemeIdUri = reporting.schemeIdUri;\n                        } else {\n                            // Invalid Reporting. schemeIdUri must be set. Ignore.\n                            return;\n                        }\n\n                        if (reporting.hasOwnProperty('value')) {\n                            reportingEntry.value = reporting.value;\n                        }\n\n                        if (reporting.hasOwnProperty(constants.DVB_REPORTING_URL)) {\n                            reportingEntry.dvbReportingUrl = reporting[constants.DVB_REPORTING_URL];\n                        }\n\n                        if (reporting.hasOwnProperty(constants.DVB_PROBABILITY)) {\n                            reportingEntry.dvbProbability = reporting[constants.DVB_PROBABILITY];\n                        }\n\n                        metricEntry.Reporting.push(reportingEntry);\n                    });\n                } else {\n                    // Invalid Metrics. At least one reporting must be present. Ignore\n                    return;\n                }\n\n                metrics.push(metricEntry);\n            });\n        }\n\n        return metrics;\n    }\n\n    instance = {\n        getMetrics: getMetrics\n    };\n\n    return instance;\n}\n\nManifestParsing.__dashjs_factory_name = 'ManifestParsing';\nexport default FactoryMaker.getSingletonFactory(ManifestParsing); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\n/**\n * @ignore\n */\nfunction MetricSerialiser() {\n\n    // For each entry in the top level list within the metric (in the case\n    // of the DVBErrors metric each entry corresponds to an \"error event\"\n    // described in clause 10.8.4) the Player shall:\n    function serialise(metric) {\n        let pairs = [];\n        let obj = [];\n        let key,\n            value;\n\n        // Take each (key, value) pair from the metric entry and create a\n        // string consisting of the name of the key, followed by an equals\n        // ('=') character, followed by the string representation of the\n        // value. The string representation of the value is created based\n        // on the type of the value following the instructions in Table 22.\n        for (key in metric) {\n            if (metric.hasOwnProperty(key) && (key.indexOf('_') !== 0)) {\n                value = metric[key];\n\n                // we want to ensure that keys still end up in the report\n                // even if there is no value\n                if ((value === undefined) || (value === null)) {\n                    value = '';\n                }\n\n                // DVB A168 10.12.4 Table 22\n                if (Array.isArray(value)) {\n                    // if trace or similar is null, do not include in output\n                    if (!value.length) {\n                        continue;\n                    }\n\n                    obj = [];\n\n                    value.forEach(function (v) {\n                        let isBuiltIn = Object.prototype.toString.call(v).slice(8, -1) !== 'Object';\n\n                        obj.push(isBuiltIn ? v : serialise(v));\n                    });\n\n                    value = obj.map(encodeURIComponent).join(',');\n                } else if (typeof value === 'string') {\n                    value = encodeURIComponent(value);\n                } else if (value instanceof Date) {\n                    value = value.toISOString();\n                } else if (typeof value === 'number') {\n                    value = Math.round(value);\n                }\n\n                pairs.push(key + '=' + value);\n            }\n        }\n\n        // Concatenate the strings created in the previous step with an\n        // ampersand ('&') character between each one.\n        return pairs.join('&');\n    }\n\n    return {\n        serialise: serialise\n    };\n}\n\nMetricSerialiser.__dashjs_factory_name = 'MetricSerialiser';\nexport default FactoryMaker.getSingletonFactory(MetricSerialiser); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\n/**\n * @ignore\n */\nfunction RNG() {\n\n    // check whether secure random numbers are available. if not, revert to\n    // using Math.random\n    let crypto = window.crypto || window.msCrypto;\n\n    // could just as easily use any other array type by changing line below\n    let ArrayType = Uint32Array;\n    let MAX_VALUE = Math.pow(2, ArrayType.BYTES_PER_ELEMENT * 8) - 1;\n\n    // currently there is only one client for this code, and that only uses\n    // a single random number per initialisation. may want to increase this\n    // number if more consumers in the future\n    let NUM_RANDOM_NUMBERS = 10;\n\n    let randomNumbers,\n        index,\n        instance;\n\n    function initialize() {\n        if (crypto) {\n            if (!randomNumbers) {\n                randomNumbers = new ArrayType(NUM_RANDOM_NUMBERS);\n            }\n            crypto.getRandomValues(randomNumbers);\n            index = 0;\n        }\n    }\n\n    function rand(min, max) {\n        let r;\n\n        if (!min) {\n            min = 0;\n        }\n\n        if (!max) {\n            max = 1;\n        }\n\n        if (crypto) {\n            if (index === randomNumbers.length) {\n                initialize();\n            }\n\n            r = randomNumbers[index] / MAX_VALUE;\n            index += 1;\n        } else {\n            r = Math.random();\n        }\n\n        return (r * (max - min)) + min;\n    }\n\n    instance = {\n        random: rand\n    };\n\n    initialize();\n\n    return instance;\n}\n\nRNG.__dashjs_factory_name = 'RNG';\nexport default FactoryMaker.getSingletonFactory(RNG); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass DVBErrors {\n    constructor() {\n        this.mpdurl = null;\n        // String - Absolute URL from which the MPD was originally\n        // retrieved (MPD updates will not change this value).\n\n        this.errorcode = null;\n        // String - The value of errorcode depends upon the type\n        // of error being reported. For an error listed in the\n        // ErrorType column below the value is as described in the\n        // Value column.\n        //\n        // ErrorType                                            Value\n        // ---------                                            -----\n        // HTTP error status code                               HTTP status code\n        // Unknown HTTP status code                             HTTP status code\n        // SSL connection failed                                \"SSL\" followed by SSL alert value\n        // DNS resolution failed                                \"C00\"\n        // Host unreachable                                     \"C01\"\n        // Connection refused                                   \"C02\"\n        // Connection error – Not otherwise specified           \"C03\"\n        // Corrupt media – ISO BMFF container cannot be parsed  \"M00\"\n        // Corrupt media – Not otherwise specified              \"M01\"\n        // Changing Base URL in use due to errors               \"F00\"\n        // Becoming an error reporting Player                   \"S00\"\n\n        this.terror = null;\n        // Real-Time - Date and time at which error occurred in UTC,\n        // formatted as a combined date and time according to ISO 8601.\n\n        this.url = null;\n        // String - Absolute URL from which data was being requested\n        // when this error occurred. If the error report is in relation\n        // to corrupt media or changing BaseURL, this may be a null\n        // string if the URL from which the media was obtained or\n        // which led to the change of BaseURL is no longer known.\n\n        this.ipaddress = null;\n        // String - IP Address which the host name in \"url\" resolved to.\n        // If the error report is in relation to corrupt media or\n        // changing BaseURL, this may be a null string if the URL\n        // from which the media was obtained or which led to the\n        // change of BaseURL is no longer known.\n\n        this.servicelocation = null;\n        // String - The value of the serviceLocation field in the\n        // BaseURL being used. In the event of this report indicating\n        // a change of BaseURL this is the value from the BaseURL\n        // being moved from.\n    }\n}\n\nDVBErrors.SSL_CONNECTION_FAILED_PREFIX = 'SSL';\nDVBErrors.DNS_RESOLUTION_FAILED = 'C00';\nDVBErrors.HOST_UNREACHABLE = 'C01';\nDVBErrors.CONNECTION_REFUSED = 'C02';\nDVBErrors.CONNECTION_ERROR = 'C03';\nDVBErrors.CORRUPT_MEDIA_ISOBMFF = 'M00';\nDVBErrors.CORRUPT_MEDIA_OTHER = 'M01';\nDVBErrors.BASE_URL_CHANGED = 'F00';\nDVBErrors.BECAME_REPORTER = 'S00';\n\nexport default DVBErrors;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass Metrics {\n    constructor() {\n\n        this.metrics = '';\n        this.Range = [];\n        this.Reporting = [];\n    }\n}\n\nexport default Metrics;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass Range {\n    constructor() {\n\n        // as defined in ISO23009-1\n        this.starttime = 0;\n        this.duration = Infinity;\n\n        // for internal use\n        this._useWallClockTime = false;\n    }\n}\n\nexport default Range;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\n\n// TS 103 285 Clause 10.12.3.3\nconst DEFAULT_DVB_PROBABILITY = 1000;\n\nclass Reporting {\n    constructor() {\n\n        this.schemeIdUri = '';\n        this.value = '';\n\n        // DVB Extensions\n        this.dvbReportingUrl = '';\n        this.dvbProbability = DEFAULT_DVB_PROBABILITY;\n    }\n}\n\nexport default Reporting;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport UTCTiming from '../../dash/vo/UTCTiming.js';\nimport FactoryMaker from '../../core/FactoryMaker.js';\nimport Settings from '../../core/Settings.js';\nimport {checkParameterType} from '../utils/SupervisorTools.js';\nimport Constants from '../constants/Constants.js';\n\nconst DEFAULT_XHR_WITH_CREDENTIALS = false;\n\nfunction CustomParametersModel() {\n\n    let instance,\n        utcTimingSources,\n        xhrWithCredentials,\n        requestInterceptors,\n        responseInterceptors,\n        licenseRequestFilters,\n        licenseResponseFilters,\n        customCapabilitiesFilters,\n        customInitialTrackSelectionFunction,\n        customAbrRules;\n\n    const context = this.context;\n    const settings = Settings(context).getInstance();\n\n    function setup() {\n        xhrWithCredentials = {\n            default: DEFAULT_XHR_WITH_CREDENTIALS\n        };\n        _resetInitialSettings();\n    }\n\n    function _resetInitialSettings() {\n        requestInterceptors = [];\n        responseInterceptors = [];\n        licenseRequestFilters = [];\n        licenseResponseFilters = [];\n        customCapabilitiesFilters = [];\n        customAbrRules = [];\n        customInitialTrackSelectionFunction = null;\n        utcTimingSources = [];\n    }\n\n\n    function reset() {\n        _resetInitialSettings();\n    }\n\n    function setConfig() {\n\n    }\n\n    /**\n     * Registers a custom initial track selection function. Only one function is allowed. Calling this method will overwrite a potentially existing function.\n     * @param {function} customFunc - the custom function that returns the initial track\n     */\n    function setCustomInitialTrackSelectionFunction(customFunc) {\n        customInitialTrackSelectionFunction = customFunc;\n    }\n\n    /**\n     * Resets the custom initial track selection\n     */\n    function resetCustomInitialTrackSelectionFunction() {\n        customInitialTrackSelectionFunction = null;\n    }\n\n    /**\n     * Returns the initial track selection function\n     * @return {function}\n     */\n    function getCustomInitialTrackSelectionFunction() {\n        return customInitialTrackSelectionFunction;\n    }\n\n    /**\n     * Returns all license request filters\n     * @return {array}\n     */\n    function getLicenseRequestFilters() {\n        return licenseRequestFilters;\n    }\n\n    /**\n     * Returns all license response filters\n     * @return {array}\n     */\n    function getLicenseResponseFilters() {\n        return licenseResponseFilters;\n    }\n\n    /**\n     * Registers a license request filter. This enables application to manipulate/overwrite any request parameter and/or request data.\n     * The provided callback function shall return a promise that shall be resolved once the filter process is completed.\n     * The filters are applied in the order they are registered.\n     * @param {function} filter - the license request filter callback\n     */\n    function registerLicenseRequestFilter(filter) {\n        licenseRequestFilters.push(filter);\n    }\n\n    /**\n     * Registers a license response filter. This enables application to manipulate/overwrite the response data\n     * The provided callback function shall return a promise that shall be resolved once the filter process is completed.\n     * The filters are applied in the order they are registered.\n     * @param {function} filter - the license response filter callback\n     */\n    function registerLicenseResponseFilter(filter) {\n        licenseResponseFilters.push(filter);\n    }\n\n    /**\n     * Unregisters a license request filter.\n     * @param {function} filter - the license request filter callback\n     */\n    function unregisterLicenseRequestFilter(filter) {\n        _unregisterFilter(licenseRequestFilters, filter);\n    }\n\n    /**\n     * Unregisters a license response filter.\n     * @param {function} filter - the license response filter callback\n     */\n    function unregisterLicenseResponseFilter(filter) {\n        _unregisterFilter(licenseResponseFilters, filter);\n    }\n\n    /**\n     * Returns all custom capabilities filter\n     * @return {array}\n     */\n    function getCustomCapabilitiesFilters() {\n        return customCapabilitiesFilters;\n    }\n\n    /**\n     * Registers a custom capabilities filter. This enables application to filter representations to use.\n     * The provided callback function shall return a boolean or promise resolving to a boolean based on whether or not to use the representation.\n     * The filters are applied in the order they are registered.\n     * @param {function} filter - the custom capabilities filter callback\n     */\n    function registerCustomCapabilitiesFilter(filter) {\n        customCapabilitiesFilters.push(filter);\n    }\n\n    /**\n     * Unregisters a custom capabilities filter.\n     * @param {function} filter - the custom capabilities filter callback\n     */\n    function unregisterCustomCapabilitiesFilter(filter) {\n        _unregisterFilter(customCapabilitiesFilters, filter);\n    }\n\n    /**\n     * Unregister a filter from the list of existing filers.\n     * @param {array} filters\n     * @param {function} filter\n     * @private\n     */\n    function _unregisterFilter(filters, filter) {\n        let index = -1;\n        filters.some((item, i) => {\n            if (item === filter) {\n                index = i;\n                return true;\n            }\n        });\n        if (index < 0) {\n            return;\n        }\n        filters.splice(index, 1);\n    }\n\n    /**\n     * Iterate through the list of custom ABR rules and find the right rule by name\n     * @param {string} rulename\n     * @return {number} rule number\n     */\n    function _findAbrCustomRuleIndex(rulename) {\n        let i;\n        for (i = 0; i < customAbrRules.length; i++) {\n            if (customAbrRules[i].rulename === rulename) {\n                return i;\n            }\n        }\n        return -1;\n    }\n\n    /**\n     * Add a custom ABR Rule\n     * Rule will be apply on next stream if a stream is being played\n     *\n     * @param {string} type - rule type (one of ['qualitySwitchRules','abandonFragmentRules'])\n     * @param {string} rulename - name of rule (used to identify custom rule). If one rule of same name has been added, then existing rule will be updated\n     * @param {object} rule - the rule object instance\n     * @throws {@link Constants#BAD_ARGUMENT_ERROR BAD_ARGUMENT_ERROR} if called with invalid arguments.\n     */\n    function addAbrCustomRule(type, rulename, rule) {\n        if (typeof type !== 'string' || (type !== Constants.RULES_TYPES.ABANDON_FRAGMENT_RULES && type !== Constants.RULES_TYPES.QUALITY_SWITCH_RULES) ||\n            typeof rulename !== 'string') {\n            throw Constants.BAD_ARGUMENT_ERROR;\n        }\n        let index = _findAbrCustomRuleIndex(rulename);\n        if (index === -1) {\n            // add rule\n            customAbrRules.push({\n                type: type,\n                rulename: rulename,\n                rule: rule\n            });\n        } else {\n            // update rule\n            customAbrRules[index].type = type;\n            customAbrRules[index].rule = rule;\n        }\n    }\n\n    /**\n     * Remove a custom ABR Rule\n     *\n     * @param {string} rulename - name of the rule to be removed\n     */\n    function removeAbrCustomRule(rulename) {\n        if (rulename) {\n            let index = _findAbrCustomRuleIndex(rulename);\n            //if no rulename custom rule has been found, do nothing\n            if (index !== -1) {\n                // remove rule\n                customAbrRules.splice(index, 1);\n            }\n        } else {\n            //if no rulename is defined, remove all ABR custome rules\n            customAbrRules = [];\n        }\n    }\n\n    /**\n     * Remove all custom rules\n     */\n    function removeAllAbrCustomRule() {\n        customAbrRules = [];\n    }\n\n    /**\n     * Return all ABR custom rules\n     * @return {array}\n     */\n    function getAbrCustomRules() {\n        return customAbrRules;\n    }\n\n    /**\n     * Adds a request interceptor. This enables application to monitor, manipulate, overwrite any request parameter and/or request data.\n     * The provided callback function shall return a promise with updated request that shall be resolved once the process of the request is completed.\n     * The interceptors are applied in the order they are added.\n     * @param {function} interceptor - the request interceptor callback\n     */\n    function addRequestInterceptor(interceptor) {\n        requestInterceptors.push(interceptor);\n    }\n\n    /**\n     * Adds a response interceptor. This enables application to monitor, manipulate, overwrite the response data\n     * The provided callback function shall return a promise with updated response that shall be resolved once the process of the response is completed.\n     * The interceptors are applied in the order they are added.\n     * @param {function} interceptor - the response interceptor callback\n     */\n    function addResponseInterceptor(interceptor) {\n        responseInterceptors.push(interceptor);\n    }\n\n    /**\n     * Unregisters a request interceptor.\n     * @param {function} interceptor - the request interceptor callback\n     */\n    function removeRequestInterceptor(interceptor) {\n        _unregisterFilter(requestInterceptors, interceptor);\n    }\n\n    /**\n     * Unregisters a response interceptor.\n     * @param {function} interceptor - the request interceptor callback\n     */\n    function removeResponseInterceptor(interceptor) {\n        _unregisterFilter(responseInterceptors, interceptor);\n    }\n\n    /**\n     * Returns all request interceptors\n     * @return {array}\n     */\n    function getRequestInterceptors() {\n        return requestInterceptors;\n    }\n\n    /**\n     * Returns all response interceptors\n     * @return {array}\n     */\n    function getResponseInterceptors() {\n        return responseInterceptors;\n    }\n\n    /**\n     * Add a UTC timing source at the top of the list\n     * @param {string} schemeIdUri\n     * @param {string} value\n     */\n    function addUTCTimingSource(schemeIdUri, value) {\n        removeUTCTimingSource(schemeIdUri, value); //check if it already exists and remove if so.\n        let vo = new UTCTiming();\n        vo.schemeIdUri = schemeIdUri;\n        vo.value = value;\n        utcTimingSources.push(vo);\n    }\n\n    /**\n     * Return all UTC timing sources\n     * @return {array}\n     */\n    function getUTCTimingSources() {\n        return utcTimingSources;\n    }\n\n    /**\n     * Remove a specific timing source from the array\n     * @param {string} schemeIdUri\n     * @param {string} value\n     */\n    function removeUTCTimingSource(schemeIdUri, value) {\n        checkParameterType(schemeIdUri, 'string');\n        checkParameterType(value, 'string');\n        utcTimingSources.forEach(function (obj, idx) {\n            if (obj.schemeIdUri === schemeIdUri && obj.value === value) {\n                utcTimingSources.splice(idx, 1);\n            }\n        });\n    }\n\n    /**\n     * Remove all timing sources\n     */\n    function clearDefaultUTCTimingSources() {\n        utcTimingSources = [];\n    }\n\n    /**\n     * Add the default timing source to the list\n     */\n    function restoreDefaultUTCTimingSources() {\n        let defaultUtcTimingSource = settings.get().streaming.utcSynchronization.defaultTimingSource;\n        addUTCTimingSource(defaultUtcTimingSource.scheme, defaultUtcTimingSource.value);\n    }\n\n    function setXHRWithCredentialsForType(type, value) {\n        if (!type) {\n            Object.keys(xhrWithCredentials).forEach(key => {\n                setXHRWithCredentialsForType(key, value);\n            });\n        } else {\n            xhrWithCredentials[type] = !!value;\n        }\n    }\n\n    function getXHRWithCredentialsForType(type) {\n        const useCreds = xhrWithCredentials[type];\n\n        return useCreds === undefined ? xhrWithCredentials.default : useCreds;\n    }\n\n    instance = {\n        addAbrCustomRule,\n        addRequestInterceptor,\n        addResponseInterceptor,\n        addUTCTimingSource,\n        clearDefaultUTCTimingSources,\n        getAbrCustomRules,\n        getCustomCapabilitiesFilters,\n        getCustomInitialTrackSelectionFunction,\n        getLicenseRequestFilters,\n        getLicenseResponseFilters,\n        getRequestInterceptors,\n        getResponseInterceptors,\n        getUTCTimingSources,\n        getXHRWithCredentialsForType,\n        registerCustomCapabilitiesFilter,\n        registerLicenseRequestFilter,\n        registerLicenseResponseFilter,\n        removeAbrCustomRule,\n        removeAllAbrCustomRule,\n        removeRequestInterceptor,\n        removeResponseInterceptor,\n        removeUTCTimingSource,\n        reset,\n        resetCustomInitialTrackSelectionFunction,\n        restoreDefaultUTCTimingSources,\n        setConfig,\n        setCustomInitialTrackSelectionFunction,\n        setXHRWithCredentialsForType,\n        unregisterCustomCapabilitiesFilter,\n        unregisterLicenseRequestFilter,\n        unregisterLicenseResponseFilter,\n    };\n\n    setup();\n\n    return instance;\n}\n\nCustomParametersModel.__dashjs_factory_name = 'CustomParametersModel';\nexport default FactoryMaker.getSingletonFactory(CustomParametersModel);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport FactoryMaker from '../../core/FactoryMaker.js';\nimport {checkInteger} from './SupervisorTools.js';\n\nfunction CustomTimeRanges(/*config*/) {\n    let customTimeRangeArray = [];\n    let length = 0;\n\n    function add(start, end) {\n        let i;\n\n        // eslint-disable-next-line curly\n        for (i = 0; (i < this.customTimeRangeArray.length) && (start > this.customTimeRangeArray[i].start); i++) ;\n\n        this.customTimeRangeArray.splice(i, 0, { start: start, end: end });\n\n        for (i = 0; i < this.customTimeRangeArray.length - 1; i++) {\n            if (this.mergeRanges(i, i + 1)) {\n                i--;\n            }\n        }\n        this.length = this.customTimeRangeArray.length;\n    }\n\n    function clear() {\n        this.customTimeRangeArray = [];\n        this.length = 0;\n    }\n\n    function remove(start, end) {\n        for (let i = 0; i < this.customTimeRangeArray.length; i++) {\n            if (start <= this.customTimeRangeArray[i].start && end >= this.customTimeRangeArray[i].end) {\n                //      |--------------Range i-------|\n                //|---------------Range to remove ---------------|\n                //    or\n                //|--------------Range i-------|\n                //|--------------Range to remove ---------------|\n                //    or\n                //                 |--------------Range i-------|\n                //|--------------Range to remove ---------------|\n                this.customTimeRangeArray.splice(i, 1);\n                i--;\n\n            } else if (start > this.customTimeRangeArray[i].start && end < this.customTimeRangeArray[i].end) {\n                //|-----------------Range i----------------|\n                //        |-------Range to remove -----|\n                this.customTimeRangeArray.splice(i + 1, 0, { start: end, end: this.customTimeRangeArray[i].end });\n                this.customTimeRangeArray[i].end = start;\n                break;\n            } else if (start > this.customTimeRangeArray[i].start && start < this.customTimeRangeArray[i].end) {\n                //|-----------Range i----------|\n                //                    |---------Range to remove --------|\n                //    or\n                //|-----------------Range i----------------|\n                //            |-------Range to remove -----|\n                this.customTimeRangeArray[i].end = start;\n            } else if (end > this.customTimeRangeArray[i].start && end < this.customTimeRangeArray[i].end) {\n                //                     |-----------Range i----------|\n                //|---------Range to remove --------|\n                //            or\n                //|-----------------Range i----------------|\n                //|-------Range to remove -----|\n                this.customTimeRangeArray[i].start = end;\n            }\n        }\n\n        this.length = this.customTimeRangeArray.length;\n    }\n\n    function mergeRanges(rangeIndex1, rangeIndex2) {\n        let range1 = this.customTimeRangeArray[rangeIndex1];\n        let range2 = this.customTimeRangeArray[rangeIndex2];\n\n        if (range1.start <= range2.start && range2.start <= range1.end && range1.end <= range2.end) {\n            //|-----------Range1----------|\n            //                    |-----------Range2----------|\n            range1.end = range2.end;\n            this.customTimeRangeArray.splice(rangeIndex2, 1);\n            return true;\n\n        } else if (range2.start <= range1.start && range1.start <= range2.end && range2.end <= range1.end) {\n            //                |-----------Range1----------|\n            //|-----------Range2----------|\n            range1.start = range2.start;\n            this.customTimeRangeArray.splice(rangeIndex2, 1);\n            return true;\n        } else if (range2.start <= range1.start && range1.start <= range2.end && range1.end <= range2.end) {\n            //      |--------Range1-------|\n            //|---------------Range2--------------|\n            this.customTimeRangeArray.splice(rangeIndex1, 1);\n            return true;\n        } else if (range1.start <= range2.start && range2.start <= range1.end && range2.end <= range1.end) {\n            //|-----------------Range1--------------|\n            //        |-----------Range2----------|\n            this.customTimeRangeArray.splice(rangeIndex2, 1);\n            return true;\n        }\n        return false;\n    }\n\n    function start(index) {\n        checkInteger(index);\n\n        if (index >= this.customTimeRangeArray.length || index < 0) {\n            return NaN;\n        }\n\n        return this.customTimeRangeArray[index].start;\n    }\n\n    function end(index) {\n        checkInteger(index);\n\n        if (index >= this.customTimeRangeArray.length || index < 0) {\n            return NaN;\n        }\n\n        return this.customTimeRangeArray[index].end;\n    }\n\n    return {\n        customTimeRangeArray: customTimeRangeArray,\n        length: length,\n        add: add,\n        clear: clear,\n        remove: remove,\n        mergeRanges: mergeRanges,\n        start: start,\n        end: end\n    };\n}\n\nCustomTimeRanges.__dashjs_factory_name = 'CustomTimeRanges';\nexport default FactoryMaker.getClassFactory(CustomTimeRanges);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport Constants from '../constants/Constants.js';\n\nexport function checkParameterType(parameter, type) {\n    if (typeof parameter !== type) {\n        throw Constants.BAD_ARGUMENT_ERROR;\n    }\n}\n\nexport function checkInteger(parameter) {\n    const isInt = parameter !== null && !isNaN(parameter) && (parameter % 1 === 0);\n\n    if (!isInt) {\n        throw Constants.BAD_ARGUMENT_ERROR + ' : argument is not an integer';\n    }\n}\n\nexport function checkRange(parameter, min, max) {\n    if (parameter < min || parameter > max) {\n        throw Constants.BAD_ARGUMENT_ERROR + ' : argument out of range';\n    }\n}\n\nexport function checkIsVideoOrAudioType(type) {\n    if (typeof type !== 'string' || (type !== Constants.AUDIO && type !== Constants.VIDEO)) {\n        throw Constants.BAD_ARGUMENT_ERROR;\n    }\n}\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @classdesc This Object holds reference to the HTTPRequest for manifest, fragment and xlink loading.\n * Members which are not defined in ISO23009-1 Annex D should be prefixed by a _ so that they are ignored\n * by Metrics Reporting code.\n * @ignore\n */\nclass HTTPRequest {\n    /**\n     * @class\n     */\n    constructor() {\n        /**\n         * Identifier of the TCP connection on which the HTTP request was sent.\n         * @public\n         */\n        this.tcpid = null;\n        /**\n         * This is an optional parameter and should not be included in HTTP request/response transactions for progressive download.\n         * The type of the request:\n         * - MPD\n         * - XLink expansion\n         * - Initialization Fragment\n         * - Index Fragment\n         * - Media Fragment\n         * - Bitstream Switching Fragment\n         * - other\n         * @public\n         */\n        this.type = null;\n        /**\n         * The original URL (before any redirects or failures)\n         * @public\n         */\n        this.url = null;\n        /**\n         * The actual URL requested, if different from above\n         * @public\n         */\n        this.actualurl = null;\n        /**\n         * The contents of the byte-range-spec part of the HTTP Range header.\n         * @public\n         */\n        this.range = null;\n        /**\n         * Real-Time | The real time at which the request was sent.\n         * @public\n         */\n        this.trequest = null;\n        /**\n         * Real-Time | The real time at which the first byte of the response was received.\n         * @public\n         */\n        this.tresponse = null;\n        /**\n         * The HTTP response code.\n         * @public\n         */\n        this.responsecode = null;\n        /**\n         * The duration of the throughput trace intervals (ms), for successful requests only.\n         * @public\n         */\n        this.interval = null;\n        /**\n         * Throughput traces, for successful requests only.\n         * @public\n         */\n        this.trace = [];\n        /**\n         * The CMSD static and dynamic values retrieved from CMSD response headers.\n         * @public\n         */\n        this.cmsd = null;\n\n        /**\n         * Type of stream (\"audio\" | \"video\" etc..)\n         * @public\n         */\n        this._stream = null;\n        /**\n         * Real-Time | The real time at which the request finished.\n         * @public\n         */\n        this._tfinish = null;\n        /**\n         * The duration of the media requests, if available, in seconds.\n         * @public\n         */\n        this._mediaduration = null;\n        /**\n         * all the response headers from request.\n         * @public\n         */\n        this._responseHeaders = null;\n        /**\n         * The selected service location for the request. string.\n         * @public\n         */\n        this._serviceLocation = null;\n        /**\n         * The type of the loader that was used. Distinguish between fetch loader and xhr loader\n         */\n        this._fileLoaderType = null;\n        /**\n         * The values derived from the ResourceTimingAPI.\n         */\n        this._resourceTimingValues = null;\n    }\n}\n\n/**\n * @classdesc This Object holds reference to the progress of the HTTPRequest.\n * @ignore\n */\nclass HTTPRequestTrace {\n    /**\n     * @class\n     */\n    constructor() {\n        /**\n         * Real-Time | Measurement stream start.\n         * @public\n         */\n        this.s = null;\n        /**\n         * Measurement stream duration (ms).\n         * @public\n         */\n        this.d = null;\n        /**\n         * List of integers counting the bytes received in each trace interval within the measurement stream.\n         * @public\n         */\n        this.b = [];\n    }\n}\n\nHTTPRequest.GET = 'GET';\nHTTPRequest.HEAD = 'HEAD';\nHTTPRequest.MPD_TYPE = 'MPD';\nHTTPRequest.XLINK_EXPANSION_TYPE = 'XLinkExpansion';\nHTTPRequest.INIT_SEGMENT_TYPE = 'InitializationSegment';\nHTTPRequest.INDEX_SEGMENT_TYPE = 'IndexSegment';\nHTTPRequest.MEDIA_SEGMENT_TYPE = 'MediaSegment';\nHTTPRequest.BITSTREAM_SWITCHING_SEGMENT_TYPE = 'BitstreamSwitchingSegment';\nHTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE = 'FragmentInfoSegment';\nHTTPRequest.DVB_REPORTING_TYPE = 'DVBReporting';\nHTTPRequest.LICENSE = 'license';\nHTTPRequest.CONTENT_STEERING_TYPE = 'ContentSteering';\nHTTPRequest.OTHER_TYPE = 'other';\n\nexport {HTTPRequest, HTTPRequestTrace};\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "__webpack_require__.amdO = {};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport DVBErrorsTranslator from './utils/DVBErrorsTranslator.js';\nimport MetricsReportingEvents from './MetricsReportingEvents.js';\nimport MetricsCollectionController from './controllers/MetricsCollectionController.js';\nimport MetricsHandlerFactory from './metrics/MetricsHandlerFactory.js';\nimport ReportingFactory from './reporting/ReportingFactory.js';\n\nfunction MetricsReporting() {\n\n    let context = this.context;\n    let instance,\n        dvbErrorsTranslator;\n\n    /**\n     * Create a MetricsCollectionController, and a DVBErrorsTranslator\n     * @param {Object} config - dependancies from owner\n     * @return {MetricsCollectionController} Metrics Collection Controller\n     */\n    function createMetricsReporting(config) {\n        dvbErrorsTranslator = DVBErrorsTranslator(context).getInstance({\n            eventBus: config.eventBus,\n            dashMetrics: config.dashMetrics,\n            metricsConstants: config.metricsConstants,\n            events: config.events\n        });\n        dvbErrorsTranslator.initialize();\n        return MetricsCollectionController(context).create(config);\n    }\n\n    /**\n     * Get the ReportingFactory to allow new reporters to be registered\n     * @return {ReportingFactory} Reporting Factory\n     */\n    function getReportingFactory() {\n        return ReportingFactory(context).getInstance();\n    }\n\n    /**\n     * Get the MetricsHandlerFactory to allow new handlers to be registered\n     * @return {MetricsHandlerFactory} Metrics Handler Factory\n     */\n    function getMetricsHandlerFactory() {\n        return MetricsHandlerFactory(context).getInstance();\n    }\n\n    instance = {\n        createMetricsReporting:     createMetricsReporting,\n        getReportingFactory:        getReportingFactory,\n        getMetricsHandlerFactory:   getMetricsHandlerFactory\n    };\n\n    return instance;\n}\n\nMetricsReporting.__dashjs_factory_name = 'MetricsReporting';\nconst factory = dashjs.FactoryMaker.getClassFactory(MetricsReporting); \nfactory.events = MetricsReportingEvents;\ndashjs.FactoryMaker.updateClassFactory(MetricsReporting.__dashjs_factory_name, factory); \nexport default factory;\n"], "names": ["assertPath", "path", "TypeError", "JSON", "stringify", "normalizeStringPosix", "allowAboveRoot", "res", "lastSegmentLength", "lastSlash", "dots", "code", "i", "length", "charCodeAt", "lastSlashIndex", "lastIndexOf", "slice", "_format", "sep", "pathObject", "dir", "root", "base", "name", "ext", "posix", "resolve", "<PERSON><PERSON><PERSON>", "resolvedAbsolute", "cwd", "arguments", "undefined", "process", "normalize", "isAbsolute", "trailingSeparator", "join", "joined", "arg", "relative", "from", "to", "fromStart", "fromEnd", "fromLen", "toStart", "toEnd", "toLen", "lastCommonSep", "fromCode", "toCode", "out", "_makeLong", "dirname", "hasRoot", "end", "matchedSlash", "basename", "start", "extIdx", "firstNonSlashEnd", "extname", "startDot", "startPart", "preDotState", "format", "parse", "ret", "delimiter", "win32", "module", "exports", "window", "LIBVERSION", "EMPTY", "UNKNOWN", "FUNC_TYPE", "UNDEF_TYPE", "OBJ_TYPE", "STR_TYPE", "MAJOR", "MODEL", "NAME", "TYPE", "VENDOR", "VERSION", "ARCHITECTURE", "CONSOLE", "MOBILE", "TABLET", "SMARTTV", "WEARABLE", "EMBEDDED", "UA_MAX_LENGTH", "AMAZON", "APPLE", "ASUS", "BLACKBERRY", "BROWSER", "CHROME", "EDGE", "FIREFOX", "GOOGLE", "HUAWEI", "LG", "MICROSOFT", "MOTOROLA", "OPERA", "SAMSUNG", "SHARP", "SONY", "XIAOMI", "ZEBRA", "FACEBOOK", "CHROMIUM_OS", "MAC_OS", "extend", "regexes", "extensions", "mergedRegexes", "concat", "enumerize", "arr", "enums", "toUpperCase", "has", "str1", "str2", "lowerize", "indexOf", "str", "toLowerCase", "majorize", "version", "replace", "split", "trim", "len", "substring", "rgxMapper", "ua", "arrays", "j", "k", "p", "q", "matches", "match", "regex", "props", "exec", "call", "test", "strMapper", "map", "oldSafariMap", "windowsVersionMap", "browser", "cpu", "device", "engine", "os", "<PERSON><PERSON><PERSON><PERSON>", "getResult", "_navigator", "navigator", "_ua", "userAgent", "_uach", "userAgentData", "_rgxmap", "_isSelfNav", "<PERSON><PERSON><PERSON><PERSON>", "_browser", "brave", "isBrave", "getCPU", "_cpu", "getDevice", "_device", "mobile", "standalone", "maxTouchPoints", "getEngine", "_engine", "getOS", "_os", "platform", "getUA", "setUA", "CPU", "DEVICE", "ENGINE", "OS", "define", "amd", "$", "j<PERSON><PERSON><PERSON>", "Zepto", "parser", "get", "set", "result", "prop", "EventBus", "Events", "FactoryMaker", "LOG_LEVEL_NONE", "LOG_LEVEL_FATAL", "LOG_LEVEL_ERROR", "LOG_LEVEL_WARNING", "LOG_LEVEL_INFO", "LOG_LEVEL_DEBUG", "Debug", "config", "context", "eventBus", "getInstance", "settings", "logFn", "instance", "showLogTimestamp", "showCalleeName", "startTime", "setup", "Date", "getTime", "console", "getLogFn", "error", "warn", "info", "debug", "fn", "bind", "log", "<PERSON><PERSON><PERSON><PERSON>", "fatal", "setLogTimestampVisible", "value", "setCalleeNameVisible", "_len", "params", "Array", "_key", "doLog", "_len2", "_key2", "_len3", "_key3", "_len4", "_key4", "_len5", "_key5", "level", "_this", "message", "logTime", "getClassName", "getType", "_len6", "_key6", "apply", "for<PERSON>ach", "item", "logLevel", "dispatchEvent", "trigger", "LOG", "__dashjs_factory_name", "factory", "getSingletonFactory", "updateSingletonFactory", "MediaPlayerEvents", "EVENT_PRIORITY_LOW", "EVENT_PRIORITY_HIGH", "handlers", "_commonOn", "type", "listener", "scope", "options", "executeOnlyOnce", "Error", "priority", "getHandlerIdx", "handler", "callback", "getStreamId", "streamId", "mediaType", "mode", "inserted", "some", "idx", "splice", "push", "on", "once", "off", "payload", "filters", "hasOwnProperty", "handlersToRemove", "filter", "EVENT_MODE_ON_RECEIVE", "index", "reset", "singletonContexts", "singletonFactories", "classFactories", "childInstance", "override", "getSingletonInstance", "className", "obj", "setSingletonInstance", "deleteSingletonInstances", "x", "getFactoryByName", "factoriesArray", "updateFactory", "updateClassFactory", "getClassFactoryByName", "getClassFactory", "classConstructor", "create", "merge", "getSingletonFactoryByName", "args", "classInstance", "extensionObject", "extension", "parent", "Utils", "Constants", "HTTPRequest", "Settings", "DISPATCH_KEY_MAP", "SETTING_UPDATED_LIVE_DELAY", "SETTING_UPDATED_LIVE_DELAY_FRAGMENT_COUNT", "SETTING_UPDATED_CATCHUP_ENABLED", "SETTING_UPDATED_PLAYBACK_RATE_MIN", "SETTING_UPDATED_PLAYBACK_RATE_MAX", "SETTING_UPDATED_ABR_ACTIVE_RULES", "SETTING_UPDATED_MAX_BITRATE", "SETTING_UPDATED_MIN_BITRATE", "defaultSettings", "streaming", "abandonLoadTimeout", "wallclockTimeUpdateInterval", "manifestUpdateRetryInterval", "liveUpdateTimeThresholdInMilliseconds", "cacheInitSegments", "applyServiceDescription", "applyProducerReferenceTime", "applyContentSteering", "enableManifestDurationMismatchFix", "parseInbandPrft", "enableManifestTimescaleMismatchFix", "capabilities", "filterUnsupportedEssentialProperties", "supportedEssentialProperties", "schemeIdUri", "FONT_DOWNLOAD_DVB_SCHEME", "COLOUR_PRIMARIES_SCHEME_ID_URI", "URL_QUERY_INFO_SCHEME", "EXT_URL_QUERY_INFO_SCHEME", "MATRIX_COEFFICIENTS_SCHEME_ID_URI", "TRANSFER_CHARACTERISTICS_SCHEME_ID_URI", "THUMBNAILS_SCHEME_ID_URIS", "ep", "useMediaCapabilitiesApi", "filterVideoColorimetryEssentialProperties", "filterHDRMetadataFormatEssentialProperties", "events", "eventControllerRefreshDelay", "deleteEventMessageDataTimeout", "timeShiftBuffer", "calcFromSegmentTimeline", "fallbackToSegmentTimeline", "metrics", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "delay", "liveDelayFragmentCount", "NaN", "liveDelay", "useSuggestedPresentationDelay", "protection", "keepProtectionMediaKeys", "ignoreEmeEncryptedEvent", "detectPlayreadyMessageFormat", "ignoreKeyStatuses", "buffer", "enableSeekDecorrelationFix", "fastSwitchEnabled", "flushBufferAtTrackSwitch", "reuseExistingSourceBuffers", "bufferPruningInterval", "bufferToKeep", "bufferTimeAtTopQuality", "bufferTimeAtTopQualityLongForm", "initialBufferLevel", "bufferTimeDefault", "longFormContentDurationThreshold", "stallThreshold", "lowLatencyStallThreshold", "useAppendWindow", "setStallState", "avoidCurrentTimeRangePruning", "useChangeType", "mediaSourceDurationInfinity", "resetSourceBuffersForTrackSwitch", "syntheticStallEvents", "enabled", "ignoreReadyState", "gaps", "jumpGaps", "jumpLargeGaps", "smallGapLimit", "threshold", "enableSeekFix", "enableStallFix", "stallSeek", "utcSynchronization", "useManifestDateHeaderTimeSource", "backgroundAttempts", "timeBetweenSyncAttempts", "maximumTimeBetweenSyncAttempts", "minimumTimeBetweenSyncAttempts", "timeBetweenSyncAttemptsAdjustmentFactor", "maximumAllowedDrift", "enableBackgroundSyncAfterSegmentDownloadError", "defaultTimingSource", "scheme", "scheduling", "defaultTimeout", "lowLatencyTimeout", "scheduleWhilePaused", "text", "defaultEnabled", "dispatchForManualRendering", "extendSegmentedCues", "imsc", "displayForcedOnlyMode", "enableRollUp", "webvtt", "customRenderingEnabled", "liveCatchup", "maxDrift", "playbackRate", "min", "max", "playbackBufferMin", "LIVE_CATCHUP_MODE_DEFAULT", "lastBitrateCachingInfo", "ttl", "lastMediaSettingsCachingInfo", "saveLastMediaSettingsForCurrentStreamingSession", "cacheLoadThresholds", "video", "audio", "trackSwitchMode", "TRACK_SWITCH_MODE_ALWAYS_REPLACE", "TRACK_SWITCH_MODE_NEVER_REPLACE", "ignoreSelectionPriority", "prioritizeRoleMain", "assumeDefaultRoleAsMain", "selectionModeForInitialTrack", "TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY", "fragmentRequestTimeout", "fragmentRequestProgressTimeout", "manifestRequestTimeout", "retryIntervals", "MPD_TYPE", "XLINK_EXPANSION_TYPE", "MEDIA_SEGMENT_TYPE", "INIT_SEGMENT_TYPE", "BITSTREAM_SWITCHING_SEGMENT_TYPE", "INDEX_SEGMENT_TYPE", "MSS_FRAGMENT_INFO_SEGMENT_TYPE", "LICENSE", "OTHER_TYPE", "lowLatencyReductionFactor", "retryAttempts", "lowLatencyMultiplyFactor", "abr", "limitBitrateByPortal", "usePixelRatioInLimitBitrateByPortal", "enableSupplementalPropertyAdaptationSetSwitching", "rules", "throughputRule", "active", "bolaRule", "insufficientBufferRule", "parameters", "throughputSafetyFactor", "segmentIgnoreCount", "switchHistoryRule", "sampleSize", "switchPercentageThreshold", "droppedFramesRule", "minimumSampleSize", "droppedFramesPercentageThreshold", "abandonRequestsRule", "abandonDurationMultiplier", "minSegmentDownloadTimeThresholdInMs", "minThroughputSamplesThreshold", "l2ARule", "loLPRule", "throughput", "averageCalculationMode", "THROUGHPUT_CALCULATION_MODES", "EWMA", "lowLatencyDownloadTimeCalculationMode", "LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE", "MOOF_PARSING", "useResourceTimingApi", "useNetworkInformationApi", "xhr", "fetch", "useDeadTimeLatency", "bandwidthSafetyFactor", "sampleSettings", "live", "vod", "enableSampleSizeAdjustment", "decreaseScale", "increaseScale", "maxMeasurementsToKeep", "averageLatencySampleAmount", "ewma", "throughputSlowHalfLifeSeconds", "throughputFastHalfLifeSeconds", "latencySlowHalfLifeCount", "latencyFastHalfLifeCount", "weightDownloadTimeMultiplicationFactor", "maxBitrate", "minBitrate", "initialBitrate", "autoSwitchBitrate", "cmcd", "applyParametersFromMpd", "sid", "cid", "rtp", "rtpSafetyFactor", "CMCD_MODE_QUERY", "<PERSON><PERSON><PERSON><PERSON>", "CMCD_AVAILABLE_KEYS", "includeInRequests", "cmsd", "applyMb", "etpWeightRatio", "defaultSchemeIdUri", "viewpoint", "audioChannelConfiguration", "role", "accessibility", "errors", "recoverAttempts", "mediaErrorDecode", "clone", "mixinSettings", "source", "dest", "n", "RegExp", "update", "settingsObj", "mixin", "copy", "s", "empty", "src", "r", "l", "addAdditionalQueryParameterToUrl", "url", "updatedUrl", "_ref", "key", "separator", "includes", "encodeURIComponent", "e", "removeQueryParameterFromUrl", "queryParameter", "parsedUrl", "URL", "URLSearchParams", "search", "size", "delete", "queryString", "entries", "_ref2", "baseUrl", "origin", "pathname", "parseHttpHeaders", "headerStr", "headers", "headerPairs", "ilen", "headerPair", "parseQueryParams", "queryParamString", "searchParams", "decodeURIComponent", "generateUuid", "dt", "uuid", "c", "Math", "random", "floor", "toString", "generateHashCode", "string", "hash", "chr", "getRelativeUrl", "originalUrl", "targetUrl", "original", "target", "protocol", "relativePath", "substr", "startIndexOffset", "getHostFromUrl", "urlString", "host", "parseUserAgent", "uaString", "stringHasProtocol", "bufferSourceToDataView", "bufferSource", "toDataView", "DataView", "bufferSourceToInt8", "Uint8Array", "uint8ArrayToString", "uint8Array", "decoder", "TextDecoder", "decode", "bufferSourceToHex", "data", "hex", "Type", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bytesPerElement", "BYTES_PER_ELEMENT", "dataEnd", "byteOffset", "byteLength", "rawStart", "Infinity", "view", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getCodecFamily", "codecString", "profile", "_getCodecParts", "CODEC_FAMILIES", "MP3", "AAC", "AC3", "EC3", "DTSX", "DTSC", "AVC", "HEVC", "rest", "EventsBase", "CoreEvents", "constructor", "ATTEMPT_BACKGROUND_SYNC", "BUFFERING_COMPLETED", "BUFFER_CLEARED", "BYTES_APPENDED_END_FRAGMENT", "BUFFER_REPLACEMENT_STARTED", "CHECK_FOR_EXISTENCE_COMPLETED", "CMSD_STATIC_HEADER", "CURRENT_TRACK_CHANGED", "DATA_UPDATE_COMPLETED", "INBAND_EVENTS", "INITIAL_STREAM_SWITCH", "INIT_FRAGMENT_LOADED", "INIT_FRAGMENT_NEEDED", "INTERNAL_MANIFEST_LOADED", "ORIGINAL_MANIFEST_LOADED", "LOADING_COMPLETED", "LOADING_PROGRESS", "LOADING_DATA_PROGRESS", "LOADING_ABANDONED", "MANIFEST_UPDATED", "MEDIA_FRAGMENT_LOADED", "MEDIA_FRAGMENT_NEEDED", "MEDIAINFO_UPDATED", "QUOTA_EXCEEDED", "SEGMENT_LOCATION_BLACKLIST_ADD", "SEGMENT_LOCATION_BLACKLIST_CHANGED", "SERVICE_LOCATION_BASE_URL_BLACKLIST_ADD", "SERVICE_LOCATION_BASE_URL_BLACKLIST_CHANGED", "SERVICE_LOCATION_LOCATION_BLACKLIST_ADD", "SERVICE_LOCATION_LOCATION_BLACKLIST_CHANGED", "SET_FRAGMENTED_TEXT_AFTER_DISABLED", "SET_NON_FRAGMENTED_TEXT", "SOURCE_BUFFER_ERROR", "STREAMS_COMPOSED", "STREAM_BUFFERING_COMPLETED", "STREAM_REQUESTING_COMPLETED", "TEXT_TRACKS_QUEUE_INITIALIZED", "TIME_SYNCHRONIZATION_COMPLETED", "UPDATE_TIME_SYNC_OFFSET", "URL_RESOLUTION_FAILED", "VIDEO_CHUNK_RECEIVED", "WALLCLOCK_TIME_UPDATED", "XLINK_ELEMENT_LOADED", "XLINK_READY", "SEEK_TARGET", "publicOnly", "evt", "UTCTiming", "AST_IN_FUTURE", "BASE_URLS_UPDATED", "BUFFER_EMPTY", "BUFFER_LOADED", "BUFFER_LEVEL_STATE_CHANGED", "BUFFER_LEVEL_UPDATED", "DVB_FONT_DOWNLOAD_ADDED", "DVB_FONT_DOWNLOAD_COMPLETE", "DVB_FONT_DOWNLOAD_FAILED", "DYNAMIC_TO_STATIC", "ERROR", "FRAGMENT_LOADING_COMPLETED", "FRAGMENT_LOADING_PROGRESS", "FRAGMENT_LOADING_STARTED", "FRAGMENT_LOADING_ABANDONED", "MANIFEST_LOADING_STARTED", "MANIFEST_LOADING_FINISHED", "MANIFEST_LOADED", "METRICS_CHANGED", "METRIC_CHANGED", "METRIC_ADDED", "METRIC_UPDATED", "PERIOD_SWITCH_STARTED", "PERIOD_SWITCH_COMPLETED", "QUALITY_CHANGE_REQUESTED", "QUALITY_CHANGE_RENDERED", "NEW_TRACK_SELECTED", "TRACK_CHANGE_RENDERED", "STREAM_INITIALIZING", "STREAM_UPDATED", "STREAM_ACTIVATED", "STREAM_DEACTIVATED", "STREAM_INITIALIZED", "STREAM_TEARDOWN_COMPLETE", "TEXT_TRACKS_ADDED", "TEXT_TRACK_ADDED", "CUE_ENTER", "CUE_EXIT", "THROUGHPUT_MEASUREMENT_STORED", "TTML_PARSED", "TTML_TO_PARSE", "CAPTION_RENDERED", "CAPTION_CONTAINER_RESIZE", "CAN_PLAY", "CAN_PLAY_THROUGH", "PLAYBACK_ENDED", "PLAYBACK_ERROR", "PLAYBACK_INITIALIZED", "PLAYBACK_NOT_ALLOWED", "PLAYBACK_METADATA_LOADED", "PLAYBACK_LOADED_DATA", "PLAYBACK_PAUSED", "PLAYBACK_PLAYING", "PLAYBACK_PROGRESS", "PLAYBACK_RATE_CHANGED", "PLAYBACK_SEEKED", "PLAYBACK_SEEKING", "PLAYBACK_STALLED", "PLAYBACK_STARTED", "PLAYBACK_TIME_UPDATED", "PLAYBACK_VOLUME_CHANGED", "PLAYBACK_WAITING", "MANIFEST_VALIDITY_CHANGED", "EVENT_MODE_ON_START", "CONFORMANCE_VIOLATION", "REPRESENTATION_SWITCH", "ADAPTATION_SET_REMOVED_NO_CAPABILITIES", "CONTENT_STEERING_REQUEST_COMPLETED", "INBAND_PRFT", "MANAGED_MEDIA_SOURCE_START_STREAMING", "MANAGED_MEDIA_SOURCE_END_STREAMING", "mediaPlayerEvents", "STREAM", "VIDEO", "AUDIO", "TEXT", "MUXED", "IMAGE", "STPP", "TTML", "VTT", "WVTT", "CONTENT_STEERING", "LIVE_CATCHUP_MODE_LOLP", "MOVING_AVERAGE_SLIDING_WINDOW", "MOVING_AVERAGE_EWMA", "BAD_ARGUMENT_ERROR", "MISSING_CONFIG_ERROR", "TRACK_SELECTION_MODE_FIRST_TRACK", "TRACK_SELECTION_MODE_HIGHEST_BITRATE", "TRACK_SELECTION_MODE_WIDEST_RANGE", "CMCD_QUERY_KEY", "CMCD_MODE_HEADER", "CMCD_V2_AVAILABLE_KEYS", "CMCD_AVAILABLE_REQUESTS", "INITIALIZE", "TEXT_SHOWING", "TEXT_HIDDEN", "TEXT_DISABLED", "ACCESSIBILITY_CEA608_SCHEME", "CC1", "CC3", "UTF8", "SCHEME_ID_URI", "START_TIME", "SERVICE_DESCRIPTION_DVB_LL_SCHEME", "SUPPLEMENTAL_PROPERTY_DVB_LL_SCHEME", "CTA_5004_2023_SCHEME", "HDR_METADATA_FORMAT_SCHEME_ID_URI", "HDR_METADATA_FORMAT_VALUES", "ST2094_10", "SL_HDR2", "ST2094_40", "MEDIA_CAPABILITIES_API", "COLORGAMUT", "SRGB", "P3", "REC2020", "TRANSFERFUNCTION", "PQ", "HLG", "HDR_METADATATYPE", "SMPTE_ST_2094_10", "SLHDR2", "SMPTE_ST_2094_40", "XML", "ARRAY_BUFFER", "DVB_REPORTING_URL", "DVB_PROBABILITY", "OFF_MIMETYPE", "WOFF_MIMETYPE", "VIDEO_ELEMENT_READY_STATES", "HAVE_NOTHING", "HAVE_METADATA", "HAVE_CURRENT_DATA", "HAVE_FUTURE_DATA", "HAVE_ENOUGH_DATA", "FILE_LOADER_TYPES", "FETCH", "XHR", "THROUGHPUT_TYPES", "LATENCY", "BANDWIDTH", "ZLEMA", "ARITHMETIC_MEAN", "BYTE_SIZE_WEIGHTED_ARITHMETIC_MEAN", "DATE_WEIGHTED_ARITHMETIC_MEAN", "HARMONIC_MEAN", "BYTE_SIZE_WEIGHTED_HARMONIC_MEAN", "DATE_WEIGHTED_HARMONIC_MEAN", "DOWNLOADED_DATA", "AAST", "RULES_TYPES", "QUALITY_SWITCH_RULES", "ABANDON_FRAGMENT_RULES", "BOLA_RULE", "THROUGHPUT_RULE", "INSUFFICIENT_BUFFER_RULE", "SWITCH_HISTORY_RULE", "DROPPED_FRAMES_RULE", "LEARN_TO_ADAPT_RULE", "LOL_PLUS_RULE", "ABANDON_REQUEST_RULE", "ID3_SCHEME_ID_URI", "COMMON_ACCESS_TOKEN_HEADER", "DASH_ROLE_SCHEME_ID", "MetricsReportingEvents", "METRICS_INITIALISATION_COMPLETE", "BECAME_REPORTING_PLAYER", "CMCD_DATA_GENERATED", "metricsReportingEvents", "MetricsController", "ManifestParsing", "MetricsCollectionController", "metricsControllers", "controllersToRemove", "Object", "keys", "adapter", "constants", "getMetrics", "manifest", "m", "controller", "initialize", "resetMetricsControllers", "RangeController", "ReportingController", "MetricsHandlersController", "metricsHandlersController", "reportingController", "rangeController", "metricsEntry", "mediaElement", "Range", "metricsConstants", "mediaPlayerModel", "Reporting", "MetricsHandlerFactory", "metricsHandlerFactory", "handle", "handleNewMetric", "metric", "midx", "ms", "nextm", "CustomTimeRanges", "useWallClockTime", "ranges", "rs", "starttime", "duration", "add", "_useWallClockTime", "clear", "isEnabled", "numRanges", "time", "currentTime", "ReportingFactory", "reporters", "reportingFactory", "reporting", "reporter", "report", "vos", "BufferLevel", "DVBErrors", "HttpList", "GenericMetricHandler", "logger", "keyRegex", "knownFactoryProducts", "PlayList", "RepSwitchList", "TcpList", "listType", "register", "unregister", "HandlerHelpers", "BufferLevelHandler", "interval", "lastReportedTime", "handlerHelpers", "storedVOs", "getLowestBufferLevelVO", "reduce", "a", "b", "intervalCallback", "vo", "t", "rc", "n_ms", "validateN", "reconstructFullMetricName", "setInterval", "clearInterval", "BUFFER_LEVEL", "DVBErrorsHandler", "onInitialisationComplete", "unused", "DVB_ERRORS", "metricName", "HttpListHandler", "storedVos", "requestType", "HTTP_REQUEST", "DVBReporting", "knownReportingSchemeIdUris", "entry", "moduleName", "MetricSerialiser", "RNG", "CustomParametersModel", "metricSerialiser", "customParametersModel", "randomNumberGenerator", "reportingPlayerStatusDecided", "isReportingPlayer", "reportingUrl", "USE_DRAFT_DVB_SPEC", "allowPendingRequestsToCompleteOnReset", "pendingRequests", "resetInitialSettings", "doGetRequest", "successCB", "failureCB", "req", "XMLHttpRequest", "withCredentials", "getXHRWithCredentialsForType", "HTTP_REQUEST_DVB_REPORTING_TYPE", "oncomplete", "reqIndex", "status", "open", "onloadend", "onerror", "send", "isArray", "serialise", "probability", "dvbReportingUrl", "dvbProbability", "abort", "DVBErrorsTranslator", "mpd", "dashMetrics", "o", "mpdurl", "terror", "addDVBErrors", "onManifestUpdate", "onServiceLocationChanged", "errorcode", "BASE_URL_CHANGED", "servicelocation", "onBecameReporter", "BECAME_REPORTER", "handleHttpMetric", "responsecode", "CONNECTION_ERROR", "tresponse", "_serviceLocation", "onMetricEvent", "onPlaybackError", "reason", "MediaError", "MEDIA_ERR_NETWORK", "MEDIA_ERR_DECODE", "CORRUPT_MEDIA_OTHER", "mn", "isNaN", "Metrics", "getMetricsRangeStartTime", "dynamic", "range", "voPeriods", "reportingStartTime", "presentationStartTime", "getAvailabilityStartTime", "getRegularPeriods", "metricEntry", "isDynamic", "getIsDynamic", "rangeEntry", "getDuration", "reportingEntry", "pairs", "v", "isBuiltIn", "prototype", "toISOString", "round", "crypto", "msCrypto", "ArrayType", "Uint32Array", "MAX_VALUE", "pow", "NUM_RANDOM_NUMBERS", "randomNumbers", "getRandomValues", "rand", "ipaddress", "SSL_CONNECTION_FAILED_PREFIX", "DNS_RESOLUTION_FAILED", "HOST_UNREACHABLE", "CONNECTION_REFUSED", "CORRUPT_MEDIA_ISOBMFF", "DEFAULT_DVB_PROBABILITY", "checkParameterType", "DEFAULT_XHR_WITH_CREDENTIALS", "utcTimingSources", "xhrWithCredentials", "requestInterceptors", "responseInterceptors", "licenseRequestFilters", "licenseResponseFilters", "customCapabilitiesFilters", "customInitialTrackSelectionFunction", "customAbrRules", "default", "_resetInitialSettings", "setConfig", "setCustomInitialTrackSelectionFunction", "customFunc", "resetCustomInitialTrackSelectionFunction", "getCustomInitialTrackSelectionFunction", "getLicenseRequestFilters", "getLicenseResponseFilters", "registerLicenseRequestFilter", "registerLicenseResponseFilter", "unregisterLicenseRequestFilter", "_unregisterFilter", "unregisterLicenseResponseFilter", "getCustomCapabilitiesFilters", "registerCustomCapabilitiesFilter", "unregisterCustomCapabilitiesFilter", "_findAbrCustomRuleIndex", "rulename", "addAbrCustomRule", "rule", "removeAbrCustomRule", "removeAllAbrCustomRule", "getAbrCustomRules", "addRequestInterceptor", "interceptor", "addResponseInterceptor", "removeRequestInterceptor", "removeResponseInterceptor", "getRequestInterceptors", "getResponseInterceptors", "addUTCTimingSource", "removeUTCTimingSource", "getUTCTimingSources", "clearDefaultUTCTimingSources", "restoreDefaultUTCTimingSources", "defaultUtcTimingSource", "setXHRWithCredentialsForType", "useCreds", "checkInteger", "customTimeRangeArray", "mergeRanges", "remove", "rangeIndex1", "rangeIndex2", "range1", "range2", "parameter", "isInt", "checkRange", "checkIsVideoOrAudioType", "tcpid", "<PERSON><PERSON><PERSON>", "trequest", "trace", "_stream", "_tfinish", "_mediaduration", "_responseHeaders", "_fileLoaderType", "_resourceTimingValues", "HTTPRequestTrace", "d", "GET", "HEAD", "DVB_REPORTING_TYPE", "CONTENT_STEERING_TYPE", "MetricsReporting", "dvbErrorsTranslator", "createMetricsReporting", "getReportingFactory", "getMetricsHandlerFactory", "dashjs"], "sourceRoot": ""}