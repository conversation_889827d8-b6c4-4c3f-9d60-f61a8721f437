{"version": 3, "file": "dash.mss.min.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAgB,OAAID,IAEpBD,EAAa,OAAIC,GAClB,CATD,CASGK,MAAM,WACT,O,wBCTA,IAAIC,EAAsB,CCA1BA,EAAwB,SAASL,EAASM,GACzC,IAAI,IAAIC,KAAOD,EACXD,EAAoBG,EAAEF,EAAYC,KAASF,EAAoBG,EAAER,EAASO,IAC5EE,OAAOC,eAAeV,EAASO,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAG3E,ECPAF,EAAwB,SAASQ,EAAKC,GAAQ,OAAOL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,EAAO,G,2CCkDtG,MAfA,MAEII,WAAAA,GACIC,KAAKC,SAAW,KAChBD,KAAKE,YAAc,KACnBF,KAAKG,MAAQC,IACbJ,KAAKK,MAAQ,KACbL,KAAKM,MAAQF,IACbJ,KAAKO,IAAMH,IACXJ,KAAKQ,SAAWJ,IAChBJ,KAAKS,eAAiB,KACtBT,KAAKU,YAAc,IACvB,GCXJ,MAAMC,EAIFZ,WAAAA,GAKIC,KAAKY,MAAQ,KAabZ,KAAKa,KAAO,KAKZb,KAAKc,IAAM,KAKXd,KAAKe,UAAY,KAKjBf,KAAKgB,MAAQ,KAKbhB,KAAKiB,SAAW,KAKhBjB,KAAKkB,UAAY,KAKjBlB,KAAKmB,aAAe,KAKpBnB,KAAKoB,SAAW,KAKhBpB,KAAKqB,MAAQ,GAKbrB,KAAKsB,KAAO,KAMZtB,KAAKuB,QAAU,KAKfvB,KAAKwB,SAAW,KAKhBxB,KAAKyB,eAAiB,KAKtBzB,KAAK0B,iBAAmB,KAKxB1B,KAAK2B,iBAAmB,KAIxB3B,KAAK4B,gBAAkB,KAIvB5B,KAAK6B,sBAAwB,IACjC,EA8BJlB,EAAYmB,IAAM,MAClBnB,EAAYoB,KAAO,OACnBpB,EAAYqB,SAAW,MACvBrB,EAAYsB,qBAAuB,iBACnCtB,EAAYuB,kBAAoB,wBAChCvB,EAAYwB,mBAAqB,eACjCxB,EAAYyB,mBAAqB,eACjCzB,EAAY0B,iCAAmC,4BAC/C1B,EAAY2B,+BAAiC,sBAC7C3B,EAAY4B,mBAAqB,eACjC5B,EAAY6B,QAAU,UACtB7B,EAAY8B,sBAAwB,kBACpC9B,EAAY+B,WAAa,QC/IzB,MAAMC,EACF5C,WAAAA,CAAYe,GACRd,KAAK4C,OAASD,EAAgBE,gBAC9B7C,KAAK8C,oBAAsB,KAC3B9C,KAAK+C,sBAAwB,KAC7B/C,KAAKgD,UAAY5C,IACjBJ,KAAKiD,YAAc7C,IACnBJ,KAAKkD,WAAa9C,IAClBJ,KAAKmD,iBAAmB/C,IACxBJ,KAAKQ,SAAWJ,IAChBJ,KAAKoD,QAAU,KACfpD,KAAKqD,cAAgB,KACrBrD,KAAKG,MAAQC,IACbJ,KAAKsD,eAAiBlD,IACtBJ,KAAKuD,UAAY,KACjBvD,KAAKgB,MAAQ,KACbhB,KAAKS,eAAiB,KACtBT,KAAKwD,aAAe,cACpBxD,KAAKyD,cAAgB,EACrBzD,KAAK0D,gBAAkB,KACvB1D,KAAK2D,UAAY,KACjB3D,KAAK4D,UAAYxD,IACjBJ,KAAK6D,UAAYzD,IACjBJ,KAAKa,KAAO,KACZb,KAAKc,IAAMA,GAAO,KAClBd,KAAK8D,cAAgB,IACzB,CAEAC,uBAAAA,GACI,OAAQ/D,KAAKa,MAAQb,KAAKa,OAASF,EAAYuB,iBACnD,CAEA8B,OAAAA,CAAQC,GACJjE,KAAKa,KAAOoD,GAAQA,EAAKC,KAAOvD,EAAYuB,kBAAoBvB,EAAYyB,mBAC5EpC,KAAKc,IAAMmD,GAAQA,EAAKnD,IAAMmD,EAAKnD,IAAM,KACzCd,KAAKgB,MAAQiD,GAAQA,EAAKjD,MAAQiD,EAAKjD,MAAMV,MAAQ,IAAM2D,EAAKjD,MAAMT,IAAM,KAC5EP,KAAKuD,UAAYU,GAAQA,EAAKV,UAAYU,EAAKV,UAAY,KAC3DvD,KAAKS,eAAiBwD,GAAQA,EAAKxD,eAAiBwD,EAAKxD,eAAiB,IAC9E,EAGJkC,EAAgBE,gBAAkB,WAClCF,EAAgBwB,gBAAkB,WAElC,QC/CA,MAAMC,EAAgB,WAElB,IAAIC,EACAC,EAAoB,GACxB,MAAMC,EAAqB,CAAC,EACtBC,EAAiB,CAAC,EAuBxB,SAASC,EAAqBC,EAASC,GACnC,IAAK,MAAMC,KAAKN,EAAmB,CAC/B,MAAM5E,EAAM4E,EAAkBM,GAC9B,GAAIlF,EAAIgF,UAAYA,GAAWhF,EAAImF,OAASF,EACxC,OAAOjF,EAAI2E,QAEnB,CACA,OAAO,IACX,CA2CA,SAASS,EAAiBD,EAAME,GAC5B,OAAOA,EAAeF,EAC1B,CAEA,SAASG,EAAcH,EAAMjG,EAASmG,GAC9BF,KAAQE,IACRA,EAAeF,GAAQjG,EAE/B,CAmFA,SAASqG,EAAMC,EAAkBR,EAASS,GAEtC,IAAIC,EACJ,MAAMT,EAAYO,EAAiBG,sBAC7BC,EAAkBZ,EAAQC,GAEhC,GAAIW,EAAiB,CAEjB,IAAIC,EAAYD,EAAgBjB,SAEhC,IAAIiB,EAAgBE,SAiBhB,OAAOD,EAAUE,MAAM,CACnBf,UACA9F,QAASyF,GACVc,GAlBHC,EAAgBF,EAAiBO,MAAM,CAACf,WAAUS,GAClDI,EAAYA,EAAUE,MAAM,CACxBf,UACA9F,QAASyF,EACTqB,OAAQN,GACTD,GAEH,IAAK,MAAMxF,KAAQ4F,EACXH,EAAcvF,eAAeF,KAC7ByF,EAAczF,GAAQ4F,EAAU5F,GAYhD,MAEIyF,EAAgBF,EAAiBO,MAAM,CAACf,WAAUS,GAMtD,OAFAC,EAAcO,aAAe,WAAa,OAAOhB,CAAU,EAEpDS,CACX,CAeA,OAbAf,EAAW,CACPuB,OAhNJ,SAAgBf,EAAMgB,EAAeL,EAAUd,IACtCA,EAAQG,IAASgB,IAClBnB,EAAQG,GAAQ,CACZR,SAAUwB,EACVL,SAAUA,GAGtB,EA0MIf,qBAAsBA,EACtBqB,qBA1KJ,SAA8BpB,EAASC,EAAWN,GAC9C,IAAK,MAAMO,KAAKN,EAAmB,CAC/B,MAAM5E,EAAM4E,EAAkBM,GAC9B,GAAIlF,EAAIgF,UAAYA,GAAWhF,EAAImF,OAASF,EAExC,YADAL,EAAkBM,GAAGP,SAAWA,EAGxC,CACAC,EAAkByB,KAAK,CACnBlB,KAAMF,EACND,QAASA,EACTL,SAAUA,GAElB,EA8JI2B,yBArJJ,SAAkCtB,GAC9BJ,EAAoBA,EAAkB2B,QAAOC,GAAKA,EAAExB,UAAYA,GACpE,EAoJIyB,oBAlFJ,SAA6BjB,GACzB,IAAItG,EAAUkG,EAAiBI,EAAiBG,sBAAuBd,GA6BvE,OA5BK3F,IACDA,EAAU,SAAU8F,GAChB,IAAIL,EAIJ,YAHgB+B,IAAZ1B,IACAA,EAAU,CAAC,GAER,CACH2B,YAAa,WAcT,OAZKhC,IACDA,EAAWI,EAAqBC,EAASQ,EAAiBG,wBAGzDhB,IACDA,EAAWY,EAAMC,EAAkBR,EAAS4B,WAC5ChC,EAAkByB,KAAK,CACnBlB,KAAMK,EAAiBG,sBACvBX,QAASA,EACTL,SAAUA,KAGXA,CACX,EAER,EACAE,EAAmBW,EAAiBG,uBAAyBzG,GAG1DA,CACX,EAoDI2H,0BAvFJ,SAAmC1B,GAC/B,OAAOC,EAAiBD,EAAMN,EAClC,EAsFIiC,uBA5FJ,SAAgC3B,EAAMjG,GAClCoG,EAAcH,EAAMjG,EAAS2F,EACjC,EA2FIkC,gBAvHJ,SAAyBvB,GACrB,IAAItG,EAAUkG,EAAiBI,EAAiBG,sBAAuBb,GAgBvE,OAdK5F,IACDA,EAAU,SAAU8F,GAIhB,YAHgB0B,IAAZ1B,IACAA,EAAU,CAAC,GAER,CACHgC,OAAQ,WACJ,OAAOzB,EAAMC,EAAkBR,EAAS4B,UAC5C,EAER,EAEA9B,EAAeU,EAAiBG,uBAAyBzG,GAEtDA,CACX,EAsGI+H,sBA5HJ,SAA+B9B,GAC3B,OAAOC,EAAiBD,EAAML,EAClC,EA2HIoC,mBAjIJ,SAA4B/B,EAAMjG,GAC9BoG,EAAcH,EAAMjG,EAAS4F,EACjC,GAkIOH,CAEX,CArOsB,GAuOtB,QCtOA,SAASwC,EAA0BC,GAI/B,IAAIzC,EACA0C,EACAC,EACAC,EACApG,EACAqG,EACAtD,EACAuD,EACAhH,EAEJ,MAAMiH,GAZNN,EAASA,GAAU,CAAC,GAYWM,gBACzBC,EAAoBP,EAAOO,kBAC3BC,EAAQR,EAAOQ,MA6BrB,SAASC,IACAN,IAILF,EAAOO,MAAM,QAEbE,aAAaN,GACbD,GAAU,EACVrD,EAAY,KACZuD,EAAoB,KACxB,CAMA,SAASM,IACL,IAAKR,EACD,OAIJ,MAAMxG,EAyC2B2G,EAAgBM,8BACDC,2BAxC1CC,EADWnH,EAAemH,WAAWC,OAAOC,IAAIC,SAC1BC,OAAOvH,EAAemH,WAAWC,OAAO1H,OAAO8H,cAAcxH,EAAemH,WAAWzH,OAC7G+H,EAAWN,EAAWO,gBAAgBC,gBAAgBC,EAMtDC,EAMV,SAA8BV,EAAYnH,EAAgB8H,GACtD,IAAI1E,EAAY+D,EAAWO,gBAAgBtE,UACvCyE,EAAU,IAAI3F,EAoBlB,OAlBA2F,EAAQ/E,UAAY1C,EACpByH,EAAQzH,KAAOF,EAAY2B,+BAE3BgG,EAAQ1E,UAAY2E,EAAQC,EAAI3E,EAChCyE,EAAQ9H,SAAW+H,EAAQE,EAAI5E,EAC/ByE,EAAQzE,UAAYA,EAIpByE,EAAQtF,UAAYvC,EAAeuC,UACnCsF,EAAQnI,MAAQA,IAChBmI,EAAQI,gBAAkBjI,EAAemH,WAAWzH,MACpDmI,EAAQ7H,eAAiBA,EACzB6H,EAAQxH,IAAMuG,EAAkBsB,QAAQlI,EAAemI,MAAM9H,IAAM8G,EAAWO,gBAAgBU,MAC9FP,EAAQxH,IAAMwH,EAAQxH,IAAIgI,QAAQ,cAAerI,EAAeuC,WAChEsF,EAAQxH,IAAMwH,EAAQxH,IAAIgI,QAAQ,SAAUP,EAAQQ,UAAYR,EAAQQ,UAAYR,EAAQC,GAC5FF,EAAQxH,IAAMwH,EAAQxH,IAAIgI,QAAQ,cAAe,kBAE1CR,CACX,CA7BoBU,CAAqBpB,EAAYnH,EALjCyH,EAASA,EAASe,OAAS,IAQ3CC,EAAgBpJ,KAAKE,KAAMsI,EAC/B,CAiCA,SAASY,EAAgBZ,GAErB,GAAIlB,EAAgB+B,mBAAmBC,0BAA0Bd,GAI7D,OAFAvB,EAAOO,MAAM,wBACbC,IAIJP,EAAcqC,eAAef,EACjC,CAuDA,OAXAjE,EAAW,CACPiF,WAlJJ,WACIzI,EAAOuG,EAAgBmC,UACvBvC,EAAgBI,EAAgB+B,mBAEhClC,GAAU,EACVrD,EAAY,KACZuD,EAAoB,IACxB,EA4IIqC,eAzJmB,4BA0JnBlJ,MA3IJ,WACQ2G,IAIJF,EAAOO,MAAM,SAEbL,GAAU,EACV9G,EAAQ,EAERsH,IACJ,EAiIIgC,mBA9CJ,SAA4BC,GACxB,IAAKzC,EACD,OAGJ,MAAMqB,EAAUoB,EAAEpB,QAClB,IAAKoB,EAAEC,SAEH,YADA5C,EAAO6C,MAAM,aAActB,EAAQxH,KAIvC,IAAI+I,EACAC,EACAC,EAIc,OAAdnG,IACAA,GAAY,IAAIoG,MAAOC,WAGtB9C,IACDA,EAAoBmB,EAAQ1E,WAIhCkG,IAAa,IAAIE,MAAOC,UAAYrG,GAAa,IACjDiG,EAAqBvB,EAAQ1E,UAAY0E,EAAQ9H,SAAY2G,EAC7D4C,EAAQG,KAAKC,IAAI,EAAIN,EAAoBC,GAGzCtC,aAAaN,GACbA,EAAsBkD,YAAW,WAC7BlD,EAAsB,KACtBO,GACJ,GAAW,IAARsC,EACP,EAWIR,QATJ,WACI,OAAO1I,CACX,EAQIwJ,MApHJ,WACI9C,GACJ,GAxCIR,EAASO,EAAMgD,UAAUjG,GA+JtBA,CACX,CAEAwC,EAA0BxB,sBAAwB,4BAClD,MAAejB,EAAaqC,gBAAgBI,GChL5C,EARA,MACI9G,WAAAA,CAAYwK,EAAMC,EAASC,GACvBzK,KAAKuK,KAAOA,GAAQ,KACpBvK,KAAKwK,QAAUA,GAAW,KAC1BxK,KAAKyK,KAAOA,GAAQ,IACxB,GCkBJ,EAvBA,MACI7E,MAAAA,CAAO8E,EAAQ5D,GACX,IAAK4D,EACD,OAGJ,IAAIlF,IAAWsB,GAASA,EAAOtB,SAC3BmF,IAAa7D,GAASA,EAAO6D,WAGjC,IAAK,MAAMC,KAAOF,GACTA,EAAO7K,eAAe+K,IAAS5K,KAAK4K,KAASpF,GAG9CmF,IAAkD,IAApCD,EAAOE,GAAKC,QAAQ,aAGtC7K,KAAK4K,GAAOF,EAAOE,GAG3B,GCCJ,EADgB,IAlBhB,cAAwBE,EACpB/K,WAAAA,GACIgL,QAIA/K,KAAKgL,iBAAmB,IAKxBhL,KAAKiL,2BAA6B,IAElCjL,KAAKkL,oBAAsB,qCAC3BlL,KAAKmL,8BAAgC,mBACzC,GCMJ,EAvBA,MACIvF,MAAAA,CAAOwF,EAAQtE,GACX,IAAKsE,EACD,OAGJ,IAAI5F,IAAWsB,GAASA,EAAOtB,SAC3BmF,IAAa7D,GAASA,EAAO6D,WAGjC,IAAK,MAAMU,KAAOD,GACTA,EAAOvL,eAAewL,IAASrL,KAAKqL,KAAS7F,GAG9CmF,IAAkD,IAApCS,EAAOC,GAAKR,QAAQ,aAGtC7K,KAAKqL,GAAOD,EAAOC,GAG3B,GC6bJ,EADwB,IA9cxB,cAAgCC,EAK5BvL,WAAAA,GACIgL,QAOA/K,KAAKuL,cAAgB,cAMrBvL,KAAKwL,kBAAoB,kBAOzBxL,KAAKyL,aAAe,gBAOpBzL,KAAK0L,cAAgB,eAMrB1L,KAAK2L,2BAA6B,qBAMlC3L,KAAK4L,qBAAuB,qBAM5B5L,KAAK6L,wBAA0B,uBAM/B7L,KAAK8L,2BAA6B,0BAMlC9L,KAAK+L,yBAA2B,wBAMhC/L,KAAKgM,kBAAoB,kBAMzBhM,KAAKiM,MAAQ,QAKbjM,KAAKkM,2BAA6B,2BAMlClM,KAAKmM,0BAA4B,0BAKjCnM,KAAKoM,yBAA2B,yBAMhCpM,KAAKqM,2BAA6B,2BAMlCrM,KAAKsM,IAAM,MAMXtM,KAAKuM,yBAA2B,yBAMhCvM,KAAKwM,0BAA4B,0BAMjCxM,KAAKyM,gBAAkB,iBAMvBzM,KAAK0M,gBAAkB,iBAMvB1M,KAAK2M,eAAiB,gBAMtB3M,KAAK4M,aAAe,cAMpB5M,KAAK6M,eAAiB,gBAMtB7M,KAAK8M,sBAAwB,sBAM7B9M,KAAK+M,wBAA0B,wBAM/B/M,KAAKgN,yBAA2B,yBAMhChN,KAAKiN,wBAA0B,wBAM/BjN,KAAKkN,mBAAqB,mBAM1BlN,KAAKmN,sBAAwB,sBAM7BnN,KAAKoN,oBAAsB,qBAM3BpN,KAAKqN,eAAiB,gBAMtBrN,KAAKsN,iBAAmB,kBAMxBtN,KAAKuN,mBAAqB,oBAM1BvN,KAAKwN,mBAAqB,oBAM1BxN,KAAKyN,yBAA2B,yBAMhCzN,KAAK0N,kBAAoB,qBAMzB1N,KAAK2N,iBAAmB,iBAMxB3N,KAAK4N,UAAY,WAMjB5N,KAAK6N,SAAW,UAMhB7N,KAAK8N,8BAAgC,8BAMrC9N,KAAK+N,YAAc,aAMnB/N,KAAKgO,cAAgB,cAMrBhO,KAAKiO,iBAAmB,kBAMxBjO,KAAKkO,yBAA2B,yBAQhClO,KAAKmO,SAAW,UAMhBnO,KAAKoO,iBAAmB,iBAMxBpO,KAAKqO,eAAiB,gBAOtBrO,KAAKsO,eAAiB,gBAOtBtO,KAAKuO,qBAAuB,sBAM5BvO,KAAKwO,qBAAuB,qBAO5BxO,KAAKyO,yBAA2B,yBAOhCzO,KAAK0O,qBAAuB,qBAM5B1O,KAAK2O,gBAAkB,iBAQvB3O,KAAK4O,iBAAmB,kBAQxB5O,KAAK6O,kBAAoB,mBAMzB7O,KAAK8O,sBAAwB,sBAM7B9O,KAAK+O,gBAAkB,iBAMvB/O,KAAKgP,iBAAmB,kBAMxBhP,KAAKiP,iBAAmB,kBAQxBjP,KAAKkP,iBAAmB,kBAMxBlP,KAAKmP,sBAAwB,sBAM7BnP,KAAKoP,wBAA0B,wBAO/BpP,KAAKqP,iBAAmB,kBAMxBrP,KAAKsP,0BAA4B,0BAMjCtP,KAAKuP,oBAAsB,mBAM3BvP,KAAKwP,sBAAwB,qBAM7BxP,KAAKyP,sBAAwB,uBAM7BzP,KAAK0P,sBAAwB,uBAM7B1P,KAAK2P,uCAAyC,qCAM9C3P,KAAK4P,mCAAqC,kCAM1C5P,KAAK6P,YAAc,aAMnB7P,KAAK8P,qCAAuC,mCAM5C9P,KAAK+P,mCAAqC,gCAC9C,GCtcJ,SAASC,EAAyBlJ,GAG9B,IAAIzC,EACAxD,EACAkG,EACJ,MAAMkJ,GAJNnJ,EAASA,GAAU,CAAC,GAIOmJ,YACrBC,EAAqBpJ,EAAOoJ,mBAC5BC,EAAerJ,EAAOsJ,WACtBC,EAAWvJ,EAAOuJ,SAClBC,EAAWxJ,EAAOwJ,SAClBhJ,EAAQR,EAAOQ,MAOrB,SAASiJ,EAAYjI,EAASkI,EAAMC,EAAMrJ,GACtC,MACM3G,EAD2B2G,EAAgBM,8BACDC,2BAE1CI,EAAWtH,EAAemH,WAAWC,OAAOC,IAAIC,SAChDH,EAAaG,EAASC,OAAOvH,EAAemH,WAAWC,OAAO1H,OAAO8H,cAAcxH,EAAemH,WAAWzH,OAC7G0D,EAAY+D,EAAWO,gBAAgBtE,UAK7C,GAHAhD,EAAOuG,EAAgBmC,UAGD,YAAlBxB,EAASlH,OAAuBkH,EAAS2I,qBACzC,OAGJ,IAAKF,EAED,YADAL,EAAavG,MAAM,IAAI+G,EAAYC,EAAU5F,iBAAkB4F,EAAU1F,sBAK7E,MAAMhD,EAAWN,EAAWO,gBAAgBC,gBAAgBC,EACtDwI,EAAUL,EAAKM,MACrB,IAAIA,EACAC,EACA/P,EAGAgQ,EAFAzI,EAAU,KACVC,EAAI,EAEJzF,EAAwB,KAE5B,GAAuB,IAAnB8N,EAAQ5H,OACR,OAQJ,GAJA6H,EAAQD,EAAQ,GAIM,WAAlB9I,EAASlH,OAETkQ,EAAc7I,EAAS,GAAGa,UAAYkI,WAAW/I,EAAS,GAAGa,WAAab,EAAS,GAAGM,EAClFsI,EAAMI,uBAA0BH,EAAehJ,EAAS2I,qBAAuB7M,GAC/E,OAWR,GAJAkN,EAAc7I,EAASA,EAASe,OAAS,GAAGF,UAAYkI,WAAW/I,EAASA,EAASe,OAAS,GAAGF,WAAab,EAASA,EAASe,OAAS,GAAGT,EAIxIsI,EAAMI,wBAA0BH,EAQhC,OANA/P,EAAQ,CACJV,MAAO4H,EAAS,GAAGM,EAAI3E,EACvBtD,IAAMkQ,EAAKU,oBAAsBtN,EAAayE,EAAQ9H,eAG1D4Q,EAAU9I,EAAQ/E,UAAWvC,EAAOoG,EAAgBiK,gBAAgBC,cAKxE/I,EAAU,CAAC,EACXA,EAAQC,EAAIsI,EAAMI,uBAClB3I,EAAQE,EAAIqI,EAAMS,kBAEdrJ,EAAS,GAAGa,YACZR,EAAQC,GAAKyI,WAAW/I,EAAS,GAAGa,WAAab,EAAS,GAAGM,EAC7DD,EAAQQ,UAAY+H,EAAMI,wBAI9B,IAAIM,EAActJ,EAASA,EAASe,OAAS,GAS7C,GARIuI,EAAYhJ,EAAIgJ,EAAY/I,IAAMF,EAAQC,IAC1CzB,EAAOO,MAAM,gCAAiCkK,EAAYhJ,EAAI,SAAWgJ,EAAY/I,EAAI,QAAUF,EAAQC,EAAIgJ,EAAYhJ,IAC3HgJ,EAAY/I,EAAIF,EAAQC,EAAIgJ,EAAYhJ,GAG5CN,EAASnC,KAAKwC,GAGQ,WAAlBR,EAASlH,KAAb,CAWI,GAAIkH,EAAS2I,sBAAwB3I,EAAS2I,qBAAuB,EAWjE,IATAnI,EAAUL,EAASA,EAASe,OAAS,GACrCT,EAAID,EAAQC,EAGZzF,GAAyByF,EAAKT,EAAS2I,qBAAuB7M,GAAcA,EAG5E0E,EAAUL,EAAS,GACnB8I,GAAWzI,EAAQC,EAAID,EAAQE,GAAK5E,EAC7BmN,EAAUjO,IAERmN,EAAmBuB,cAAcvB,EAAmBjG,UAAY+G,KAIrE9I,EAASwJ,OAAO,EAAG,GACnBnJ,EAAUL,EAAS,GACnB8I,GAAWzI,EAAQC,EAAID,EAAQE,GAAK5E,EAK5C7C,EAAQ,CACJV,MAAO4H,EAAS,GAAGM,EAAI3E,EACvBtD,IAAMkQ,EAAKU,oBAAsBtN,EAAayE,EAAQ9H,UAG1D4Q,EAAUvQ,EAAMG,EAAOoG,EAAgBiK,gBAAgBC,aAC3D,KAxCiB,UAATzQ,IACA0H,EAAUL,EAASA,EAASe,OAAS,GACrC+H,GAAWzI,EAAQC,EAAID,EAAQE,GAAK5E,EAChCmN,EAAUvQ,EAAemH,WAAWC,OAAOrH,UAC3C6P,EAASsB,QAAQC,EAAOtC,0BAA2B,CAAEuC,OAAQ7R,KAAM8R,YAAad,IAsChG,CAEA,SAASI,EAAUvQ,EAAMG,EAAOsQ,GAC5B,GAAa,UAATzQ,GAA6B,UAATA,EACpB,OAEJ,MAAMkR,EAAW9B,EAAY+B,kBAAkBnR,KAC1CkR,GAAa/Q,EAAMT,IAAMwR,EAAS/Q,MAAMT,OACzCwG,EAAOO,MAAM,sBAAwBtG,EAAMV,MAAQ,MAAQU,EAAMT,IAAM,KACvE0P,EAAYgC,WAAWpR,EAAMqP,EAAmBjG,UAAWqH,EAActQ,GACzEkP,EAAmBgC,kBAAkBrR,GAE7C,CAGA,SAASsR,EAAazM,EAAQ7E,GAC1B,IAAIuR,EAAS,EACTxN,EAAI,EAER,IAAKA,EAAI,EAAGA,EAAIc,EAAO2M,MAAMpJ,OAAQrE,IAAK,CACtC,GAAIc,EAAO2M,MAAMzN,GAAG/D,OAASA,EACzB,OAAOuR,EAEXA,GAAU1M,EAAO2M,MAAMzN,GAAG0N,IAC9B,CACA,OAAOF,CACX,CA2IA,OAPA/N,EAAW,CACPkO,gBAnIJ,SAAyB7I,EAAGtC,GACxB,IAAIxC,EAIJ,MAAM4N,EAAUlC,EAASmC,YAAY/I,EAAEC,UAEjC+I,EAAOF,EAAQG,MAAM,QAC3BD,EAAKE,SAAWlJ,EAAEpB,QAAQ7H,eAAeoS,UAAU1S,MAAQ,EAG3D,IAAIsQ,EAAO+B,EAAQG,MAAM,QACzB,MAAMG,EAAON,EAAQG,MAAM,QACd,OAATlC,IACAA,EAAOH,EAASyC,cAAc,OAAQD,EAAMJ,GAC5CjC,EAAKuC,QAAU,EACfvC,EAAKwC,MAAQ,EACbxC,EAAKU,oBAAsBjH,KAAKgJ,MAAMxJ,EAAEpB,QAAQ1E,UAAY8F,EAAEpB,QAAQzE,YAG1E,MAAMsP,EAAOX,EAAQG,MAAM,QAI3B,IAAIS,EAAOZ,EAAQG,MAAM,QACrBS,IACAA,EAAKC,QAAQhB,MAAMX,OAAO0B,EAAKC,QAAQhB,MAAMxH,QAAQuI,GAAO,GAC5DA,EAAO,MAEX,IAAI5C,EAAOgC,EAAQG,MAAM,QACzBpC,EAAY7G,EAAEpB,QAASkI,EAAMC,EAAMrJ,GAC/BoJ,IACAA,EAAK6C,QAAQhB,MAAMX,OAAOlB,EAAK6C,QAAQhB,MAAMxH,QAAQ2F,GAAO,GAC5DA,EAAO,MAMX,MAAM8C,EAASd,EAAQG,MAAM,UAC7B,GAAe,OAAXW,EAAiB,CACjBA,EAAOzS,KAAO,OACdyS,EAAOC,cAAWnN,EAElB,IAAIoN,EAAOhB,EAAQG,MAAM,QACzB,GAAa,OAATa,EAAe,CAEfA,EAAOlD,EAASyC,cAAc,OAAQD,GACtCU,EAAKR,QAAU,EACfQ,EAAKP,MAAQ,EACbO,EAAKC,YAAc,EACnBD,EAAKpB,OAAS,CAAC,GAEf,MAAMsB,EAAOpD,EAASyC,cAAc,OAAQD,GAO5C,GANAY,EAAKV,QAAU,EACfU,EAAKT,MAAQ,EACbS,EAAKC,aAAeL,EAAOK,aAC3BD,EAAKE,yBAA2B,EAChCF,EAAKG,iBAAmB,GAEL,EAAfP,EAAOL,MAEP,IAAKrO,EAAI,EAAGA,EAAI0O,EAAOK,aAAc/O,GAAK,EAGtC8O,EAAKG,iBAAiBjP,GAAK,GAAM,EAAI0O,EAAOxC,MAAMlM,GAAGkP,qBAIzDJ,EAAKE,yBAA2B,CAExC,CACJ,CAEAlB,EAAKO,OAAS,SACdP,EAAKO,OAAS,OACdE,EAAKF,OAAS,EAGd,MAAMc,EAAOvB,EAAQG,MAAM,QAC3B,IAAI1J,EAAS8K,EAAKC,YAClBb,EAAKc,YAAchL,EAAS,EAG5B,IAAIuK,EAAOhB,EAAQG,MAAM,QACzB,GAAa,OAATa,EAAe,CACf,IAAIU,EAAgB/B,EAAa4B,EAAM,QACnCI,EAAgBhC,EAAaW,EAAM,QAEvCU,EAAKpB,OAAO,GAAK8B,EAAgBC,EAAgB,EACrD,CAGAzK,EAAEC,SAAW6I,EAAQ4B,OACzB,EAsCIC,kBApCJ,SAA2B3K,EAAGtC,GAG1B,IAAKsC,EAAEC,SACH,MAAM,IAAI2K,MAAM,mCAGpB,MAAM9B,EAAUlC,EAASmC,YAAY/I,EAAEC,UAEjC+I,EAAOF,EAAQG,MAAM,QAC3BD,EAAKE,SAAWlJ,EAAEpB,QAAQ7H,eAAeoS,UAAU1S,MAAQ,EAG3D,IAAIsQ,EAAO+B,EAAQG,MAAM,QACrBG,EAAON,EAAQG,MAAM,QACZ,OAATlC,IACAA,EAAOH,EAASyC,cAAc,OAAQD,EAAMJ,GAC5CjC,EAAKuC,QAAU,EACfvC,EAAKwC,MAAQ,EACbxC,EAAKU,oBAAsBjH,KAAKgJ,MAAMxJ,EAAEpB,QAAQ1E,UAAY8F,EAAEpB,QAAQzE,YAG1E,IAAI2M,EAAOgC,EAAQG,MAAM,QACzBpC,EAAY7G,EAAEpB,QAASkI,EAAMC,EAAMrJ,GAC/BoJ,IACAA,EAAK6C,QAAQhB,MAAMX,OAAOlB,EAAK6C,QAAQhB,MAAMxH,QAAQ2F,GAAO,GAC5DA,EAAO,KAEf,EASIjH,QAPJ,WACI,OAAO1I,CACX,GAjSIkG,EAASO,EAAMgD,UAAUjG,GACzBxD,EAAO,GAySJwD,CACX,CAEA2L,EAAyB3K,sBAAwB,2BACjD,MAAejB,EAAaqC,gBAAgBuJ,GC/T5C,SAASuE,EAAyBzN,GAE9B,MAEM0N,GAHN1N,EAASA,GAAU,CAAC,GAGK0N,UACnBlE,EAAWxJ,EAAOwJ,SAExB,IACIjM,EACAwD,EACA4M,EACAhU,EACAiU,EACA7Q,EACA8Q,EAPAC,EAAuB9N,EAAO8N,qBA8elC,SAASC,EAAwBC,EAAMC,GACxBzE,EAAS0E,UAAU,OAAQF,GACjCG,YAuET,SAA0BC,GACtB,IACItQ,EADA2F,EAAO,EAGX,IAAK3F,EAAI,EAAGA,EAAIsQ,EAAIjM,OAAQrE,GAAK,EAC7B2F,GAAQ2K,EAAIC,WAAWvQ,IAA8B,GAAtBsQ,EAAIjM,OAASrE,EAAI,GAEpD,OAAO2F,CACX,CA/EuB6K,CAAiBL,EACxC,CAEA,SAASM,EAAoBP,GACzB,IAAIQ,EAAOhF,EAASyC,cAAc,OAAQ+B,GAE1CQ,EAAKrC,MAAQ,EACbqC,EAAKtC,QAAU,EACfsC,EAAKC,YAAc,WACnBD,EAAKE,eAAiB,KAC1B,CAEA,SAASC,EAA2BX,IAyBpC,SAAkCY,GAC9B,IAAIC,EAAOrF,EAASyC,cAAc,OAAQ2C,GAE1CC,EAAK1C,MAAQ,EACb0C,EAAK3C,QAAU,EAEf2C,EAAKC,oBAAsB,EAC3BD,EAAKE,gBAAkB,EACvBF,EAAKG,YAAepB,GAAsBA,EAAkBzL,OAAU,GAAKyL,EAAkB,GAAG,oBAC5FA,EAAkB,GAAG,oBAAsB,CAAC,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAAK,EAC/H,CA/BIqB,CAHWzF,EAAS0E,UAAU,OAAQF,GAI1C,CA4CA,SAASkB,EAAkBd,GACvB,IACItQ,EADAqR,EAAM,IAAIC,WAAWhB,EAAIjM,OAAS,GAGtC,IAAKrE,EAAI,EAAGA,EAAIsQ,EAAIjM,OAAS,EAAGrE,GAAK,EACjCqR,EAAIrR,GAAKuR,SAAS,GAAKjB,EAAQ,EAAJtQ,GAASsQ,EAAQ,EAAJtQ,EAAQ,GAAI,IAExD,OAAOqR,CACX,CA0CA,OAJA5R,EAAW,CACP+R,aA3BJ,SAAsBC,GAClB,IAAKA,IAAQA,EAAIzO,WACb,OAGJ,IAAI4K,EACA8D,EAiBJ,OAfA7V,EAAiB4V,EACjB5B,EAAgBhU,EAAemH,WAE/BC,EAAS4M,EAAc5M,OACvB8M,EAAUF,EAActU,MAAQ,EAChCuU,EAAoB7M,EAAOC,IAAIC,SAASC,OAAOH,EAAO1H,OAAO8H,cAAcwM,EAActU,OAAOoW,kBAEhG1S,EAAYgE,EAAOC,IAAIC,SAASC,OAAOH,EAAO1H,OAAO8H,cAAcwM,EAActU,OAAOgI,gBAAgBtE,UAExG2O,EAAUlC,EAASkG,aAzkBvB,SAAuBhE,GACnB,IAAIiE,EAAOnG,EAAS0E,UAAU,OAAQxC,GACtCiE,EAAKC,YAAc,OACnBD,EAAKE,cAAgB,EACrBF,EAAKG,kBAAoB,GACzBH,EAAKG,kBAAkB,GAAK,OAC5BH,EAAKG,kBAAkB,GAAK,OAC5BH,EAAKG,kBAAkB,GAAK,MAGhC,CAgkBIC,CAAcrE,GA9jBlB,SAAuBA,GAGnB,IAAIsE,EAAOxG,EAAS0E,UAAU,OAAQxC,IA+E1C,SAAuBsE,GAEnB,IAAIC,EAAOzG,EAASyC,cAAc,OAAQ+D,GAE1CC,EAAK/D,QAAU,EAEf+D,EAAKC,cAAgB,EACrBD,EAAKE,kBAAoB,EACzBF,EAAKlT,UAAYA,EACjBkT,EAAKvW,SAAWqH,EAAOrH,WAAa0W,IAAW,oBAAqBhN,KAAKiN,MAAMtP,EAAOrH,SAAWqD,GACjGkT,EAAKK,KAAO,EACZL,EAAKM,OAAS,EACdN,EAAKO,UAAY,EACjBP,EAAKQ,UAAY,CAAC,EAAK,GACvBR,EAAKS,OAAS,CACV,EAAG,EAAG,EACN,EAAG,EAAG,EACN,EAAG,EAAG,OAEVT,EAAKU,YAAc,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,GACnCV,EAAKW,cAAgB/C,EAAU,CAGnC,CAnGIgD,CAAcb,GAGd,IAAIc,EAAOtH,EAAS0E,UAAU,OAAQ8B,IAkG1C,SAAuBc,GAEnB,IAAIC,EAAOvH,EAASyC,cAAc,OAAQ6E,GAE1CC,EAAK7E,QAAU,EACf6E,EAAK5E,MAAQ,EAIb4E,EAAKb,cAAgB,EACrBa,EAAKZ,kBAAoB,EACzBY,EAAKjF,SAAW+B,EAChBkD,EAAKP,UAAY,EACjBO,EAAKrX,SAAWqH,EAAOrH,WAAa0W,IAAW,oBAAqBhN,KAAKiN,MAAMtP,EAAOrH,SAAWqD,GACjGgU,EAAKN,UAAY,CAAC,EAAK,GACvBM,EAAKC,MAAQ,EACbD,EAAKE,gBAAkB,EACvBF,EAAKR,OAAS,EACdQ,EAAKG,UAAY,EACjBH,EAAKL,OAAS,CACV,EAAG,EAAG,EACN,EAAG,EAAG,EACN,EAAG,EAAG,OAEVK,EAAKI,MAAQxX,EAAewX,MAC5BJ,EAAKK,OAASzX,EAAeyX,MAGjC,CA3HIC,CAAcP,GAGd,IAAIQ,EAAO9H,EAAS0E,UAAU,OAAQ4C,IA0H1C,SAAuBQ,GAEnB,IAAIC,EAAO/H,EAASyC,cAAc,OAAQqF,GAE1CC,EAAKrF,QAAU,EAEfqF,EAAKrB,cAAgB,EACrBqB,EAAKpB,kBAAoB,EACzBoB,EAAKxU,UAAYA,EACjBwU,EAAK7X,SAAWqH,EAAOrH,WAAa0W,IAAW,oBAAqBhN,KAAKiN,MAAMtP,EAAOrH,SAAWqD,GACjGwU,EAAKC,SAAW7D,EAAc8D,MAAQ,MACtCF,EAAKZ,YAAc,CAGvB,CArIIe,CAAcJ,GAuIlB,SAAuBA,GAEnB,IAAIK,EAAOnI,EAASyC,cAAc,OAAQqF,GAG1C,OADAK,EAAKhB,YAAc,EACXhD,EAAc5T,MAClB,KAAK2T,EAAUkE,MACXD,EAAKE,aAAe,OACpB,MACJ,KAAKnE,EAAUoE,MACXH,EAAKE,aAAe,OACpB,MACJ,QACIF,EAAKE,aAAe,OAG5BF,EAAK5T,KAAOpE,EAAeoY,GAC3BJ,EAAKK,SAAW,CAAC,EAAG,EAAG,EAG3B,CAxJIC,CAAcX,GAGd,IAAIY,EAAO1I,EAAS0E,UAAU,OAAQoD,GAEtC,OAAQ3D,EAAc5T,MAClB,KAAK2T,EAAUkE,OAoJvB,SAAuBM,GAEnB,IAAIC,EAAO3I,EAASyC,cAAc,OAAQiG,GAE1CC,EAAKhG,MAAQ,EAEbgG,EAAKC,aAAe,EACpBD,EAAKE,QAAU,CAAC,EAAG,EAAG,EAG1B,CA5JYC,CAAcJ,GACd,MACJ,KAAKxE,EAAUoE,OA4JvB,SAAuBI,GAEnB,IAAIK,EAAO/I,EAASyC,cAAc,OAAQiG,GAE1CK,EAAKpG,MAAQ,EAEboG,EAAKC,QAAU,EACfD,EAAKP,SAAW,CAGpB,CApKYS,CAAcP,IAsK1B,SAAuBQ,GAEnB,IAAIC,EAAOnJ,EAASyC,cAAc,OAAQyG,GAE1CC,EAAKhG,YAAc,EACnBgG,EAAK5I,QAAU,GAEf,IAAI/P,EAAMwP,EAASyC,cAAc,OAAQ0G,GAAM,GAC/C3Y,EAAI4Y,SAAW,GACf5Y,EAAImS,MAAQ,EAEZwG,EAAK5I,QAAQ9K,KAAKjF,EAGtB,CA1KI6Y,CAHWrJ,EAAS0E,UAAU,OAAQgE,IAMtC,IAAIY,EAAOtJ,EAAS0E,UAAU,OAAQgE,GAM3B1I,EAASyC,cAAc,OAAQ6G,GACrCC,MAAQ,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAGxBvJ,EAASyC,cAAc,OAAQ6G,GACrCC,MAAQ,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAGxBvJ,EAASyC,cAAc,OAAQ6G,GACrCC,MAAQ,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAGxBvJ,EAASyC,cAAc,OAAQ6G,GACrCC,MAAQ,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAsJnD,SAAuBD,GAEnB,IAAIE,EAAOxJ,EAASyC,cAAc,OAAQ6G,GAG1C,OADAE,EAAKjJ,QAAU,GACP4D,EAAc5T,MAClB,KAAK2T,EAAUkE,MACf,KAAKlE,EAAUoE,MACXkB,EAAKjJ,QAAQ9K,KAUzB,SAA2B+T,GACvB,IAAI/E,EAAQtU,EAAesZ,OAAOC,UAAU,EAAGvZ,EAAesZ,OAAOlP,QAAQ,MAE7E,OAAQkK,GACJ,IAAK,OACD,OAcZ,SAAoC+E,EAAM/E,GACtC,IAAIkF,EA+BJ,GA5BIA,EADAvF,EACOpE,EAAS0E,UAAU,OAAQ8E,GAAM,GAEjCxJ,EAAS0E,UAAU,OAAQ8E,GAAM,GAI5CG,EAAK3C,UAAY,CAAC,EAAK,EAAK,EAAK,EAAK,EAAK,GAC3C2C,EAAKC,qBAAuB,EAG5BD,EAAKE,aAAe,EACpBF,EAAK1C,UAAY,EACjB0C,EAAKG,aAAe,CAAC,EAAG,EAAG,GAC3BH,EAAK/B,OAASzX,EAAeyX,OAC7B+B,EAAKhC,MAAQxX,EAAewX,MAC5BgC,EAAKI,gBAAkB,GACvBJ,EAAKK,eAAiB,GACtBL,EAAKjC,UAAY,EACjBiC,EAAKM,YAAc,EACnBN,EAAKO,eAAiB,CAClB,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,IAAM,IAC1C,IAAM,IAAM,IAAM,EAAM,EAAM,EAAM,EAAM,EAC1C,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAC1C,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,GAE9CP,EAAKQ,MAAQ,GACbR,EAAKS,aAAe,MACpBT,EAAKnT,OAkBT,WAEI,IAWI6T,EAAWC,EAXXC,EAAO,KACPC,EAAa,GAGbC,EAAM,GACNC,EAAM,GACNC,EAAuB,EACvBC,EAAqB,EACrBC,EAAwB,EAExBC,EAAQ3a,EAAe4a,iBAAiBC,MAAM,YAAYC,MAAM,GAGpE,IAAK,IAAI3W,EAAI,EAAGA,EAAIwW,EAAMnS,OAAQrE,IAK9B,OAJA+V,EAAY3E,EAAkBoF,EAAMxW,IAEpCgW,EAA0B,GAAfD,EAAU,GAEbC,GACJ,KA5VS,EA6VLG,EAAIhV,KAAK4U,GACTG,GAAcH,EAAU1R,OAAS,EACjC,MACJ,KA/VS,EAgWL+R,EAAIjV,KAAK4U,GACTG,GAAcH,EAAU1R,OAAS,EAQzC8R,EAAI9R,OAAS,IACbgS,EAAuBF,EAAI,GAAG,GAC9BI,EAAwBJ,EAAI,GAAG,GAC/BG,EAAqBH,EAAI,GAAG,IAIhCF,EAAO,IAAI3E,WAAW4E,GAEtB,IAAIlW,EAAI,EAERiW,EAAKjW,MAAqB,WAAbkW,IAA4B,GACzCD,EAAKjW,MAAqB,SAAbkW,IAA4B,GACzCD,EAAKjW,MAAqB,MAAbkW,IAA4B,EACzCD,EAAKjW,KAAqB,IAAbkW,EACbD,EAAKW,IAAI,CAAC,GAAM,IAAM,GAAM,IAAO5W,GACnCA,GAAK,EACLiW,EAAKjW,KAAO,EACZiW,EAAKjW,KAAOqW,EACZJ,EAAKjW,KAAOuW,EACZN,EAAKjW,KAAOsW,EACZL,EAAKjW,KAAO,IACZiW,EAAKjW,KAAO,IAAOmW,EAAI9R,OACvB,IAAK,IAAIwS,EAAI,EAAGA,EAAIV,EAAI9R,OAAQwS,IAC5BZ,EAAKjW,MAAwB,MAAhBmW,EAAIU,GAAGxS,SAAoB,EACxC4R,EAAKjW,KAAwB,IAAhBmW,EAAIU,GAAGxS,OACpB4R,EAAKW,IAAIT,EAAIU,GAAI7W,GACjBA,GAAKmW,EAAIU,GAAGxS,OAEhB4R,EAAKjW,KAAOoW,EAAI/R,OAChB,IAAK,IAAIwS,EAAI,EAAGA,EAAIT,EAAI/R,OAAQwS,IAC5BZ,EAAKjW,MAAwB,MAAhBoW,EAAIS,GAAGxS,SAAoB,EACxC4R,EAAKjW,KAAwB,IAAhBoW,EAAIS,GAAGxS,OACpB4R,EAAKW,IAAIR,EAAIS,GAAI7W,GACjBA,GAAKoW,EAAIS,GAAGxS,OAGhB,OAAO4R,CACX,CA3FkBa,GACVhH,EAAmB,CAEnB,IAAII,EAAOxE,EAAS0E,UAAU,OAAQiF,GAGtCpF,EAAwBC,EAAMC,GAG9BM,EAAoBP,GAGpBW,EAA2BX,EAC/B,CAEA,OAAOmF,CACX,CA7DmB0B,CAA2B7B,EAAM/E,GAC5C,IAAK,OACD,OAwIZ,SAAmC+E,EAAM/E,GACrC,IAAI6G,EAsBJ,GAnBIA,EADAlH,EACOpE,EAAS0E,UAAU,OAAQ8E,GAAM,GAEjCxJ,EAAS0E,UAAU,OAAQ8E,GAAM,GAI5C8B,EAAKtE,UAAY,CAAC,EAAK,EAAK,EAAK,EAAK,EAAK,GAC3CsE,EAAK1B,qBAAuB,EAG5B0B,EAAKrE,UAAY,CAAC,EAAK,GACvBqE,EAAKC,aAAepb,EAAeqb,cACnCF,EAAKG,WAAa,GAClBH,EAAKnE,YAAc,EACnBmE,EAAKI,WAAa,EAClBJ,EAAKK,WAAaxb,EAAeyb,mBAAqB,GAEtDN,EAAKO,KAmBT,WAGI,IAAIC,EAAsBpG,EAAkBvV,EAAe4a,kBAOvDgB,EAAa,GAAKD,EAAoBnT,OACtCkT,EAAO,IAAIjG,WAAWmG,GAEtBzX,EAAI,EAyCR,OAvCAuX,EAAKvX,MAAqB,WAAbyX,IAA4B,GACzCF,EAAKvX,MAAqB,SAAbyX,IAA4B,GACzCF,EAAKvX,MAAqB,MAAbyX,IAA4B,EACzCF,EAAKvX,KAAqB,IAAbyX,EACbF,EAAKX,IAAI,CAAC,IAAM,IAAM,IAAM,KAAO5W,GACnCA,GAAK,EACLuX,EAAKX,IAAI,CAAC,EAAG,EAAG,EAAG,GAAI5W,GACvBA,GAAK,EAELuX,EAAKvX,KAAO,EACZuX,EAAKvX,KAAO,GAAKwX,EAAoBnT,OACrCkT,EAAKvX,MAAkB,MAAV+P,IAAqB,EAClCwH,EAAKvX,KAAkB,IAAV+P,EACbwH,EAAKvX,KAAO,EAGZuX,EAAKvX,KAAO,EACZuX,EAAKvX,KAAO,GAAKwX,EAAoBnT,OACrCkT,EAAKvX,KAAO,GACZuX,EAAKvX,GAAK,GACVuX,EAAKvX,IAAM,EACXuX,EAAKvX,MAAQ,EACbuX,EAAKvX,KAAO,IACZuX,EAAKvX,KAAO,IACZuX,EAAKvX,KAAO,IACZuX,EAAKvX,MAAmC,WAA3BnE,EAAeuC,YAA2B,GACvDmZ,EAAKvX,MAAmC,SAA3BnE,EAAeuC,YAA2B,GACvDmZ,EAAKvX,MAAmC,MAA3BnE,EAAeuC,YAA2B,EACvDmZ,EAAKvX,KAAmC,IAA3BnE,EAAeuC,UAC5BmZ,EAAKvX,MAAmC,WAA3BnE,EAAeuC,YAA2B,GACvDmZ,EAAKvX,MAAmC,SAA3BnE,EAAeuC,YAA2B,GACvDmZ,EAAKvX,MAAmC,MAA3BnE,EAAeuC,YAA2B,EACvDmZ,EAAKvX,KAAmC,IAA3BnE,EAAeuC,UAG5BmZ,EAAKvX,KAAO,EACZuX,EAAKvX,KAAOwX,EAAoBnT,OAChCkT,EAAKX,IAAIY,EAAqBxX,GAEvBuX,CACX,CA1EgBG,GAER5H,EAAmB,CAEnB,IAAII,EAAOxE,EAAS0E,UAAU,OAAQ4G,GAGtC/G,EAAwBC,EAAMC,GAG9BM,EAAoBP,GAGpBW,EAA2BX,EAC/B,CAEA,OAAO8G,CACX,CA9KmBW,CAA0BzC,EAAM/E,GAC3C,QACI,KAAM,CACFxK,KAAMqG,EAAU3F,2BAChBT,QAASoG,EAAUzF,8BACnBV,KAAM,CACFsK,MAAOA,IAI3B,CA3B8ByH,CAAkB1C,IAM5CA,EAAKrG,YAAcqG,EAAKjJ,QAAQ5H,MAEpC,CAnKIwT,CAAc7C,GAwclB,SAAuB9C,GACnB,IAAI4F,EAAOpM,EAASyC,cAAc,OAAQ+D,GAE1C4F,EAAK9J,SAAW+B,EAChB+H,EAAKC,iCAAmC,EACxCD,EAAKE,wBAA0B,EAC/BF,EAAKG,oBAAsB,EAC3BH,EAAKI,qBAAuB,CAGhC,CA5cIC,CAHWzM,EAAS0E,UAAU,OAAQ8B,IAKlCpC,GAAqBE,GAka7B,SAAiDkC,EAAMkG,GACnD,IAAIC,EACAC,EACAtY,EACAuY,EAEJ,IAAKvY,EAAI,EAAGA,EAAIoY,EAAW/T,OAAQrE,GAAK,EACpCqY,EAAaD,EAAWpY,GAAGwY,SACvBH,IACAE,EAAe7M,EAASmC,YAAYwK,GACpCC,EAAOC,EAAaxK,MAAM,QACtBuK,GACA5M,EAAS+M,MAAMC,UAAUxG,EAAMoG,GAI/C,CAhbQK,CAAwCzG,EADtBlC,EAAqB4I,mDAAmD9I,GAGlG,CA+eI+I,CAAcjL,GAEd8D,EAAc9D,EAAQ4B,QAEfkC,CACX,GAMOjS,CACX,CAEAkQ,EAAyBlP,sBAAwB,2BACjD,MAAejB,EAAaqC,gBAAgB8N,GCzmB5C,SAASmJ,EAAWC,EAAMC,GACtB,OAAQD,EAAK1U,SAAW2U,EAAK3U,QAAW0U,EAAKE,OAAM,SAAUC,EAAS3d,GAClE,OAAO2d,IAAYF,EAAKzd,EAC5B,GACJ,CAEA,SAAS4d,IACL/d,KAAKge,eACY,EAAbhe,KAAKiT,QACLjT,KAAKie,WAAW,gBAAiB,OAAQ,IACzCje,KAAKie,WAAW,0BAA2B,OAAQ,KAEvDje,KAAKie,WAAW,cAAe,OAAQ,IACvCje,KAAKke,gBAAgB,SAAUle,KAAKyT,YAAa,OAA0B,IAAjBzT,KAAKgT,QAAiB,GAAK,GACzF,CAEA,SAASmL,IACLne,KAAKge,eACY,EAAbhe,KAAKiT,QACLjT,KAAKie,WAAW,gBAAiB,OAAQ,IACzCje,KAAKie,WAAW,0BAA2B,OAAQ,KAEvDje,KAAKie,WAAW,2BAA4B,OAAQ,GACpDje,KAAKie,WAAW,eAAgB,OAAQ,IACF,IAAlCje,KAAK4T,0BACL5T,KAAKke,gBAAgB,mBAAoBle,KAAK2T,aAAc,OAAQ,EAE5E,CAEA,SAASyK,IACLpe,KAAKge,eACLhe,KAAKie,WAAW,eAAgB,OAAQ,IACvB,EAAbje,KAAKiT,OACLjT,KAAKie,WAAW,UAAW,OAAQ,GAEvCje,KAAKqe,aAAa,QAASre,KAAK2T,cAAc,SAAU7C,GACpD9Q,KAAKse,gBAAgBxN,EAAO,uBAAwB,OAAQ,GAC3C,EAAb9Q,KAAKiT,QACLjT,KAAKse,gBAAgBxN,EAAO,kBAAmB,OAAQ,IACvD9Q,KAAKue,gBAAgBzN,EAAO,sBAAuBA,EAAMgD,iBAAiB,SAAU0K,GAChFxe,KAAKse,gBAAgBE,EAAqB,mBAAoB,OAAQ,IACtExe,KAAKse,gBAAgBE,EAAqB,uBAAwB,OAAQ,GAC9E,IAER,GACJ,CAEA,SAASC,IAKDf,EAAW1d,KAAKuT,SAJD,CAAC,IAAM,GAAM,IAAM,EAAM,GAAM,IAAM,GAAM,IAAM,IAAM,IAAM,GAAM,GAAM,IAAM,IAAM,GAAM,QAK1GvT,KAAKge,eACDhe,KAAK0e,WACL1e,KAAKa,KAAO,QAEhBb,KAAKie,WAAW,yBAA0B,OAA0B,IAAjBje,KAAKgT,QAAiB,GAAK,IAC9EhT,KAAKie,WAAW,oBAAqB,OAA0B,IAAjBje,KAAKgT,QAAiB,GAAK,KAGzE0K,EAAW1d,KAAKuT,SAZD,CAAC,IAAM,IAAM,IAAM,IAAM,IAAM,GAAM,GAAM,IAAM,IAAM,GAAM,GAAM,IAAM,IAAM,GAAM,IAAM,QAa1GvT,KAAKge,eACDhe,KAAK0e,WACL1e,KAAKa,KAAO,QAEhBb,KAAKie,WAAW,iBAAkB,OAAQ,GAC1Cje,KAAKqe,aAAa,QAASre,KAAK2e,gBAAgB,SAAU7N,GACtD9Q,KAAKse,gBAAgBxN,EAAO,yBAA0B,OAA0B,IAAjB9Q,KAAKgT,QAAiB,GAAK,IAC1FhT,KAAKse,gBAAgBxN,EAAO,oBAAqB,OAA0B,IAAjB9Q,KAAKgT,QAAiB,GAAK,GACzF,KAGA0K,EAAW1d,KAAKuT,SAvBC,CAAC,IAAM,GAAM,GAAM,GAAM,GAAM,IAAM,GAAM,GAAM,IAAM,GAAM,IAAM,GAAM,IAAM,IAAM,IAAM,QAwBxGvT,KAAK0e,WACL1e,KAAKa,KAAO,UAEhBud,EAActe,KAAKE,MAE3B,CAEA,SAAS4e,EAAqB9X,GAE1BA,EAASA,GAAU,CAAC,EACpB,MAAMpC,EAAU1E,KAAK0E,QACfuL,EAAcnJ,EAAOmJ,YACrBC,EAAqBpJ,EAAOoJ,mBAC5BG,EAAWvJ,EAAOuJ,SAClBuE,EAAuB9N,EAAO8N,qBAC9BtE,EAAWxJ,EAAOwJ,SAClBhJ,EAAQR,EAAOQ,MACrB,IAAIuX,EACAC,EACAza,EAoDJ,OAPAA,EAAW,CACP+R,aAvBJ,SAAsBC,GAClB,OAAOwI,EAAyBzI,aAAaC,EACjD,EAsBI0I,gBApBJ,SAAyBrV,EAAGtC,GACxB,IAAKsC,IAAMA,EAAEpB,UAAYoB,EAAEC,SACvB,MAAM,IAAI2K,MAAM,uCAGG,iBAAnB5K,EAAEpB,QAAQzH,KAEVie,EAAyBvM,gBAAgB7I,EAAGtC,GAErCsC,EAAEpB,QAAQzH,OAASF,EAAY2B,iCAEtCwc,EAAyBzK,kBAAkB3K,EAAGtC,GAG9CsC,EAAEmI,OAAS,KAEnB,GAxCIvB,EAAS0O,gBAAgB,OAAQP,GACjCnO,EAAS0O,gBAAgB,OAAQjB,GACjCzN,EAAS0O,gBAAgB,OAAQb,GACjC7N,EAAS0O,gBAAgB,OAAQZ,GAEjCS,EAA2BtK,EAAyB7P,GAASgC,OAAO,CAChEkO,qBAAsBA,EACtBJ,UAAW1N,EAAO0N,UAClBlE,SAAUA,IAEdwO,EAA2B9O,EAAyBtL,GAASgC,OAAO,CAChEuJ,YAAaA,EACbC,mBAAoBA,EACpBI,SAAUA,EACVD,SAAUA,EACV/I,MAAOA,EACP8I,WAAYtJ,EAAOsJ,aAiCpB/L,CACX,CAEAua,EAAqBvZ,sBAAwB,uBAC7C,MAAejB,EAAaqC,gBAAgBmY,GC5LxCK,EAAS,SAAU7Y,GAEnB,IAAI8Y,EAAO,IAAKC,EAAW,EAAGC,EAAU,iBAAkBC,EAAcC,EAAaF,GACjFG,EAAmB,uCACnBC,EAAyC,mBAAXC,OAElC,SAASC,EAAQC,EAAGC,EAAOC,EAAUC,GACjC,YAAiB,IAANH,EAA0BD,EAAQ,QACxB,IAAVE,GAAyC,KAAVA,IAAiBC,EACpDE,EAAWJ,GADoEK,EAAUL,EAAGC,EAAOC,EAAUC,EAExH,CAEA,SAASG,EAAWC,EAAOC,GACvBngB,KAAKkgB,MAAQA,EACblgB,KAAKmgB,KAAOA,EACZngB,KAAKogB,SAAU,CACnB,CAIA,SAASC,EAAaH,GAClBlgB,KAAKkgB,MAAQA,EACblgB,KAAKmgB,KAAOD,EAAQ,EACpBlgB,KAAKogB,SAAU,CACnB,CAIA,SAASE,EAAaJ,GAClBlgB,KAAKkgB,MAAQA,CACjB,CAIA,SAASK,EAAU9E,GACf,OAAQ2D,EAAU3D,GAAKA,EAAI2D,CAC/B,CAEA,SAASE,EAAa7D,GAClB,OAAIA,EAAI,IAAY,CAACA,GACjBA,EAAI,KAAa,CAACA,EAAI,IAAKvR,KAAKgJ,MAAMuI,EAAI,MACvC,CAACA,EAAI,IAAKvR,KAAKgJ,MAAMuI,EAAI,KAAO,IAAKvR,KAAKgJ,MAAMuI,EAAI,MAC/D,CAEA,SAAS+E,EAAaC,GAClBC,EAAKD,GACL,IAAIxX,EAASwX,EAAIxX,OACjB,GAAIA,EAAS,GAAK0X,EAAWF,EAAKpB,GAAe,EAC7C,OAAQpW,GACJ,KAAK,EACD,OAAO,EACX,KAAK,EACD,OAAOwX,EAAI,GACf,KAAK,EACD,OAAOA,EAAI,GAAKA,EAAI,GAAKvB,EAC7B,QACI,OAAOuB,EAAI,IAAMA,EAAI,GAAKA,EAAI,GAAKvB,GAAQA,EAGvD,OAAOuB,CACX,CAEA,SAASC,EAAKf,GAEV,IADA,IAAI/a,EAAI+a,EAAE1W,OACQ,IAAX0W,IAAI/a,KACX+a,EAAE1W,OAASrE,EAAI,CACnB,CAEA,SAASgc,EAAY3X,GAGjB,IAFA,IAAI/C,EAAI,IAAI2a,MAAM5X,GACdrE,GAAK,IACAA,EAAIqE,GACT/C,EAAEtB,GAAK,EAEX,OAAOsB,CACX,CAEA,SAAS4a,EAASrF,GACd,OAAIA,EAAI,EAAUvR,KAAKgJ,MAAMuI,GACtBvR,KAAK6W,KAAKtF,EACrB,CAEA,SAASuF,EAAIC,EAAGC,GACZ,IAAgFC,EAAKvc,EAAjFwc,EAAMH,EAAEhY,OAAQoY,EAAMH,EAAEjY,OAAQqY,EAAI,IAAIT,MAAMO,GAAMG,EAAQ,EAAGC,EAAOtC,EAC1E,IAAKta,EAAI,EAAGA,EAAIyc,EAAKzc,IAEjB2c,GADAJ,EAAMF,EAAErc,GAAKsc,EAAEtc,GAAK2c,IACLC,EAAO,EAAI,EAC1BF,EAAE1c,GAAKuc,EAAMI,EAAQC,EAEzB,KAAO5c,EAAIwc,GAEPG,GADAJ,EAAMF,EAAErc,GAAK2c,KACGC,EAAO,EAAI,EAC3BF,EAAE1c,KAAOuc,EAAMI,EAAQC,EAG3B,OADID,EAAQ,GAAGD,EAAEvb,KAAKwb,GACfD,CACX,CAEA,SAASG,EAAOR,EAAGC,GACf,OAAID,EAAEhY,QAAUiY,EAAEjY,OAAe+X,EAAIC,EAAGC,GACjCF,EAAIE,EAAGD,EAClB,CAEA,SAASS,EAAST,EAAGM,GACjB,IAAiDJ,EAAKvc,EAAlD+c,EAAIV,EAAEhY,OAAQqY,EAAI,IAAIT,MAAMc,GAAIH,EAAOtC,EAC3C,IAAKta,EAAI,EAAGA,EAAI+c,EAAG/c,IACfuc,EAAMF,EAAErc,GAAK4c,EAAOD,EACpBA,EAAQrX,KAAKgJ,MAAMiO,EAAMK,GACzBF,EAAE1c,GAAKuc,EAAMI,EAAQC,EACrBD,GAAS,EAEb,KAAOA,EAAQ,GACXD,EAAE1c,KAAO2c,EAAQC,EACjBD,EAAQrX,KAAKgJ,MAAMqO,EAAQC,GAE/B,OAAOF,CACX,CAiCA,SAASM,EAASX,EAAGC,GACjB,IAAiFtc,EAAGid,EAAhFC,EAAMb,EAAEhY,OAAQ8Y,EAAMb,EAAEjY,OAAQqY,EAAI,IAAIT,MAAMiB,GAAME,EAAS,EAAGR,EAAOtC,EAC3E,IAAKta,EAAI,EAAGA,EAAImd,EAAKnd,KACjBid,EAAaZ,EAAErc,GAAKod,EAASd,EAAEtc,IACd,GACbid,GAAcL,EACdQ,EAAS,GACNA,EAAS,EAChBV,EAAE1c,GAAKid,EAEX,IAAKjd,EAAImd,EAAKnd,EAAIkd,EAAKld,IAAK,CAExB,MADAid,EAAaZ,EAAErc,GAAKod,GACH,GAA4B,CACzCV,EAAE1c,KAAOid,EACT,KACJ,CAHoBA,GAAcL,EAIlCF,EAAE1c,GAAKid,CACX,CACA,KAAOjd,EAAIkd,EAAKld,IACZ0c,EAAE1c,GAAKqc,EAAErc,GAGb,OADA8b,EAAKY,GACEA,CACX,CAkBA,SAASW,EAAchB,EAAGC,EAAGf,GACzB,IAA6Dvb,EAAGid,EAA5DF,EAAIV,EAAEhY,OAAQqY,EAAI,IAAIT,MAAMc,GAAIJ,GAASL,EAAGM,EAAOtC,EACvD,IAAKta,EAAI,EAAGA,EAAI+c,EAAG/c,IACfid,EAAaZ,EAAErc,GAAK2c,EACpBA,EAAQrX,KAAKgJ,MAAM2O,EAAaL,GAChCK,GAAcL,EACdF,EAAE1c,GAAKid,EAAa,EAAIA,EAAaL,EAAOK,EAGhD,MAAiB,iBADjBP,EAAId,EAAac,KAETnB,IAAMmB,GAAKA,GACR,IAAIjB,EAAaiB,IAErB,IAAIrB,EAAWqB,EAAGnB,EAC7B,CAmDA,SAAS+B,EAAajB,EAAGC,GACrB,IAAoFiB,EAASZ,EAAO3c,EAAGwd,EAAnGN,EAAMb,EAAEhY,OAAQ8Y,EAAMb,EAAEjY,OAAuBqY,EAAIV,EAAfkB,EAAMC,GAAyBP,EAAOtC,EAC9E,IAAKta,EAAI,EAAGA,EAAIkd,IAAOld,EAAG,CACtBwd,EAAMnB,EAAErc,GACR,IAAK,IAAIyd,EAAI,EAAGA,EAAIN,IAAOM,EAEvBF,EAAUC,EADJlB,EAAEmB,GACcf,EAAE1c,EAAIyd,GAC5Bd,EAAQrX,KAAKgJ,MAAMiP,EAAUX,GAC7BF,EAAE1c,EAAIyd,GAAKF,EAAUZ,EAAQC,EAC7BF,EAAE1c,EAAIyd,EAAI,IAAMd,CAExB,CAEA,OADAb,EAAKY,GACEA,CACX,CAEA,SAASgB,EAAcrB,EAAGC,GACtB,IAA4DiB,EAASvd,EAAjE+c,EAAIV,EAAEhY,OAAQqY,EAAI,IAAIT,MAAMc,GAAIH,EAAOtC,EAAMqC,EAAQ,EACzD,IAAK3c,EAAI,EAAGA,EAAI+c,EAAG/c,IACfud,EAAUlB,EAAErc,GAAKsc,EAAIK,EACrBA,EAAQrX,KAAKgJ,MAAMiP,EAAUX,GAC7BF,EAAE1c,GAAKud,EAAUZ,EAAQC,EAE7B,KAAOD,EAAQ,GACXD,EAAE1c,KAAO2c,EAAQC,EACjBD,EAAQrX,KAAKgJ,MAAMqO,EAAQC,GAE/B,OAAOF,CACX,CAEA,SAASiB,EAAUrc,EAAGuV,GAElB,IADA,IAAI6F,EAAI,GACD7F,KAAM,GAAG6F,EAAEvb,KAAK,GACvB,OAAOub,EAAEkB,OAAOtc,EACpB,CAEA,SAASuc,EAAkBvc,EAAGwc,GAC1B,IAAIjH,EAAIvR,KAAKC,IAAIjE,EAAE+C,OAAQyZ,EAAEzZ,QAC7B,GAAIwS,GAAK,GAAI,OAAOyG,EAAahc,EAAGwc,GACpCjH,EAAIvR,KAAK6W,KAAKtF,EAAI,GAClB,IAAIyF,EAAIhb,EAAEqV,MAAME,GAAIwF,EAAI/a,EAAEqV,MAAM,EAAGE,GAAIhT,EAAIia,EAAEnH,MAAME,GAAIkH,EAAID,EAAEnH,MAAM,EAAGE,GAClEmH,EAAKH,EAAkBxB,EAAG0B,GAAIE,EAAKJ,EAAkBvB,EAAGzY,GACxDqa,EAAOL,EAAkBhB,EAAOR,EAAGC,GAAIO,EAAOkB,EAAGla,IACjD0Z,EAAUV,EAAOA,EAAOmB,EAAIL,EAAUX,EAASA,EAASkB,EAAMF,GAAKC,GAAKpH,IAAK8G,EAAUM,EAAI,EAAIpH,IAEnG,OADAiF,EAAKyB,GACEA,CACX,CAuBA,SAASY,EAAsB9B,EAAGC,EAAGf,GACjC,OACW,IAAIF,EADXgB,EAAI/B,EACkBoD,EAAcpB,EAAGD,GAErBiB,EAAahB,EAAG5B,EAAa2B,IAFJd,EAGnD,CAuBA,SAAS6C,EAAO/B,GACZ,IAAuDkB,EAASZ,EAAO3c,EAAGwd,EAAtET,EAAIV,EAAEhY,OAAQqY,EAAIV,EAAYe,EAAIA,GAAIH,EAAOtC,EACjD,IAAKta,EAAI,EAAGA,EAAI+c,EAAG/c,IAAK,CAEpB2c,EAAQ,GADRa,EAAMnB,EAAErc,IACUwd,EAClB,IAAK,IAAIC,EAAIzd,EAAGyd,EAAIV,EAAGU,IAEnBF,EAAeC,EADTnB,EAAEoB,GACE,EAAkBf,EAAE1c,EAAIyd,GAAKd,EACvCA,EAAQrX,KAAKgJ,MAAMiP,EAAUX,GAC7BF,EAAE1c,EAAIyd,GAAKF,EAAUZ,EAAQC,EAEjCF,EAAE1c,EAAI+c,GAAKJ,CACf,CAEA,OADAb,EAAKY,GACEA,CACX,CA4FA,SAAS2B,EAAY/C,EAAOgD,GACxB,IAAwEte,EAAGue,EAAGC,EAAWC,EAArFpa,EAASiX,EAAMjX,OAAQqa,EAAW1C,EAAY3X,GAASuY,EAAOtC,EAElE,IADAkE,EAAY,EACPxe,EAAIqE,EAAS,EAAGrE,GAAK,IAAKA,EAG3Bwe,GAFAC,EAAUD,EAAY5B,EAAOtB,EAAMtb,KACnCue,EAAIrC,EAASuC,EAAUH,IACGA,EAC1BI,EAAS1e,GAAS,EAAJue,EAElB,MAAO,CAACG,EAAsB,EAAZF,EACtB,CAEA,SAASG,EAAUtkB,EAAM0gB,GACrB,IAAIO,EAAOzE,EAAIsE,EAAWJ,GAC1B,GAAIH,EACA,MAAO,CAAC,IAAIc,EAAarhB,EAAKihB,MAAQzE,EAAEyE,OAAQ,IAAII,EAAarhB,EAAKihB,MAAQzE,EAAEyE,QAEpF,IACIoD,EADArC,EAAIhiB,EAAKihB,MAAOgB,EAAIzF,EAAEyE,MAE1B,GAAU,IAANgB,EAAS,MAAM,IAAI5M,MAAM,yBAC7B,GAAIrV,EAAKmhB,QACL,OAAI3E,EAAE2E,QACK,CAAC,IAAIC,EAAaS,EAASG,EAAIC,IAAK,IAAIb,EAAaY,EAAIC,IAE7D,CAACxB,EAAQ,GAAIzgB,GAExB,GAAIwc,EAAE2E,QAAS,CACX,GAAU,IAANc,EAAS,MAAO,CAACjiB,EAAMygB,EAAQ,IACnC,IAAU,GAANwB,EAAS,MAAO,CAACjiB,EAAKukB,SAAU9D,EAAQ,IAC5C,IAAI+D,EAAMvZ,KAAKuZ,IAAIvC,GACnB,GAAIuC,EAAMvE,EAAM,CAEZoE,EAAW9C,GADXN,EAAQ+C,EAAYhC,EAAGwC,IACO,IAC9B,IAAIL,EAAYlD,EAAM,GAEtB,OADIjhB,EAAKkhB,OAAMiD,GAAaA,GACJ,iBAAbE,GACHrkB,EAAKkhB,OAAS1E,EAAE0E,OAAMmD,GAAYA,GAC/B,CAAC,IAAIjD,EAAaiD,GAAW,IAAIjD,EAAa+C,KAElD,CAAC,IAAInD,EAAWqD,EAAUrkB,EAAKkhB,OAAS1E,EAAE0E,MAAO,IAAIE,EAAa+C,GAC7E,CACAlC,EAAI5B,EAAamE,EACrB,CACA,IAAIC,EAAa/C,EAAWM,EAAGC,GAC/B,IAAoB,IAAhBwC,EAAmB,MAAO,CAAChE,EAAQ,GAAIzgB,GAC3C,GAAmB,IAAfykB,EAAkB,MAAO,CAAChE,EAAQzgB,EAAKkhB,OAAS1E,EAAE0E,KAAO,GAAK,GAAIT,EAAQ,IAC9CQ,EAA5Be,EAAEhY,OAASiY,EAAEjY,QAAU,IA5H/B,SAAiBgY,EAAGC,GAChB,IAE8EyC,EAAeC,EAAOrC,EAChGS,EAAQpd,EAAG+c,EAAGwB,EAHdrB,EAAMb,EAAEhY,OAAQ8Y,EAAMb,EAAEjY,OAAQuY,EAAOtC,EAAM2E,EAASjD,EAAYM,EAAEjY,QACpE6a,EAA8B5C,EAAEa,EAAM,GAAImB,EAAShZ,KAAK6W,KAAKS,GAAQ,EAAIsC,IACzEV,EAAYd,EAAcrB,EAAGiC,GAASG,EAAUf,EAAcpB,EAAGgC,GAKrE,IAHIE,EAAUna,QAAU6Y,GAAKsB,EAAUrd,KAAK,GAC5Csd,EAAQtd,KAAK,GACb+d,EAA8BT,EAAQtB,EAAM,GACvC6B,EAAQ9B,EAAMC,EAAK6B,GAAS,EAAGA,IAAS,CAQzC,IAPAD,EAAgBnC,EAAO,EACnB4B,EAAUQ,EAAQ7B,KAAS+B,IAC3BH,EAAgBzZ,KAAKgJ,OAAOkQ,EAAUQ,EAAQ7B,GAAOP,EAAO4B,EAAUQ,EAAQ7B,EAAM,IAAM+B,IAE9FvC,EAAQ,EACRS,EAAS,EACTL,EAAI0B,EAAQpa,OACPrE,EAAI,EAAGA,EAAI+c,EAAG/c,IACf2c,GAASoC,EAAgBN,EAAQze,GACjCue,EAAIjZ,KAAKgJ,MAAMqO,EAAQC,GACvBQ,GAAUoB,EAAUQ,EAAQhf,IAAM2c,EAAQ4B,EAAI3B,GAC9CD,EAAQ4B,EACJnB,EAAS,GACToB,EAAUQ,EAAQhf,GAAKod,EAASR,EAChCQ,GAAU,IAEVoB,EAAUQ,EAAQhf,GAAKod,EACvBA,EAAS,GAGjB,KAAkB,IAAXA,GAAc,CAGjB,IAFA2B,GAAiB,EACjBpC,EAAQ,EACH3c,EAAI,EAAGA,EAAI+c,EAAG/c,KACf2c,GAAS6B,EAAUQ,EAAQhf,GAAK4c,EAAO6B,EAAQze,IACnC,GACRwe,EAAUQ,EAAQhf,GAAK2c,EAAQC,EAC/BD,EAAQ,IAER6B,EAAUQ,EAAQhf,GAAK2c,EACvBA,EAAQ,GAGhBS,GAAUT,CACd,CACAsC,EAAOD,GAASD,CACpB,CAEA,OADAP,EAAYH,EAAYG,EAAWF,GAAQ,GACpC,CAAC1C,EAAaqD,GAASrD,EAAa4C,GAC/C,CA4E4CW,CAAQ9C,EAAGC,GA1EvD,SAAiBD,EAAGC,GAEhB,IADA,IAAyE8C,EAAOC,EAAMC,EAAOC,EAAOC,EAAhGtC,EAAMb,EAAEhY,OAAQ8Y,EAAMb,EAAEjY,OAAQ4a,EAAS,GAAIQ,EAAO,GAAI7C,EAAOtC,EAC5D4C,GAGH,GAFAuC,EAAKC,QAAQrD,IAAIa,IACjBpB,EAAK2D,GACD1D,EAAW0D,EAAMnD,GAAK,EACtB2C,EAAO9d,KAAK,OADhB,CAKAme,EAAQG,GADRJ,EAAOI,EAAKpb,QACQ,GAAKuY,EAAO6C,EAAKJ,EAAO,GAC5CE,EAAQjD,EAAEa,EAAM,GAAKP,EAAON,EAAEa,EAAM,GAChCkC,EAAOlC,IACPmC,GAASA,EAAQ,GAAK1C,GAE1BwC,EAAQ9Z,KAAK6W,KAAKmD,EAAQC,GAC1B,EAAG,CAEC,GAAIxD,EADJyD,EAAQ9B,EAAcpB,EAAG8C,GACHK,IAAS,EAAG,MAClCL,GACJ,OAASA,GACTH,EAAO9d,KAAKie,GACZK,EAAOzC,EAASyC,EAAMD,EAdtB,CAiBJ,OADAP,EAAOU,UACA,CAAC/D,EAAaqD,GAASrD,EAAa6D,GAC/C,CAgDwEG,CAAQvD,EAAGC,GAC/EoC,EAAWpD,EAAM,GACjB,IAAIuE,EAAQxlB,EAAKkhB,OAAS1E,EAAE0E,KAAMuE,EAAMxE,EAAM,GAAIyE,EAAQ1lB,EAAKkhB,KAS/D,MARwB,iBAAbmD,GACHmB,IAAOnB,GAAYA,GACvBA,EAAW,IAAIjD,EAAaiD,IACzBA,EAAW,IAAIrD,EAAWqD,EAAUmB,GACxB,iBAARC,GACHC,IAAOD,GAAOA,GAClBA,EAAM,IAAIrE,EAAaqE,IACpBA,EAAM,IAAIzE,EAAWyE,EAAKC,GAC1B,CAACrB,EAAUoB,EACtB,CAqFA,SAAS/D,EAAWM,EAAGC,GACnB,GAAID,EAAEhY,SAAWiY,EAAEjY,OACf,OAAOgY,EAAEhY,OAASiY,EAAEjY,OAAS,GAAK,EAEtC,IAAK,IAAIrE,EAAIqc,EAAEhY,OAAS,EAAGrE,GAAK,EAAGA,IAC/B,GAAIqc,EAAErc,KAAOsc,EAAEtc,GAAI,OAAOqc,EAAErc,GAAKsc,EAAEtc,GAAK,GAAK,EAEjD,OAAO,CACX,CAuJA,SAASggB,EAAajF,GAClB,IAAIlE,EAAIkE,EAAE8D,MACV,OAAIhI,EAAEoJ,cACFpJ,EAAEqJ,OAAO,IAAMrJ,EAAEqJ,OAAO,IAAMrJ,EAAEqJ,OAAO,OACvCrJ,EAAEsJ,UAAYtJ,EAAEuJ,cAAc,IAAMvJ,EAAEuJ,cAAc,QACpDvJ,EAAEwJ,OAAO,UAAb,GACJ,CAEA,SAASC,EAAgBzJ,EAAGwF,GAExB,IADA,IAAwCxY,EAAM7D,EAAGsB,EAA7Cif,EAAQ1J,EAAE2J,OAAQlE,EAAIiE,EAAO7D,EAAI,EAC9BJ,EAAE6D,UAAU7D,EAAIA,EAAEmE,OAAO,GAAI/D,IACpCgE,EAAK,IAAK1gB,EAAI,EAAGA,EAAIqc,EAAEhY,OAAQrE,IAC3B,IAAI6W,EAAEwJ,OAAOhE,EAAErc,OACfsB,EAAI+Y,EAAOgC,EAAErc,IAAI2gB,OAAOrE,EAAGzF,IACrBoJ,WAAY3e,EAAE4e,OAAOK,GAA3B,CACA,IAAK1c,EAAI6Y,EAAI,EAAQ,GAAL7Y,EAAQA,IAAK,CAEzB,IADAvC,EAAIA,EAAE8c,SAAS0B,IAAIjJ,IACboJ,SAAU,OAAO,EACvB,GAAI3e,EAAE4e,OAAOK,GAAQ,SAASG,CAClC,CACA,OAAO,CANoC,CAQ/C,OAAO,CACX,CA9vBArF,EAAWrgB,UAAYN,OAAOoH,OAAOgZ,EAAQ9f,WAQ7CygB,EAAazgB,UAAYN,OAAOoH,OAAOgZ,EAAQ9f,WAM/C0gB,EAAa1gB,UAAYN,OAAOoH,OAAOgZ,EAAQ9f,WAsF/CqgB,EAAWrgB,UAAUohB,IAAM,SAAUrB,GACjC,IAAIlE,EAAIsE,EAAWJ,GACnB,GAAI3f,KAAKmgB,OAAS1E,EAAE0E,KAChB,OAAOngB,KAAK4hB,SAASnG,EAAE+H,UAE3B,IAAIvC,EAAIjhB,KAAKkgB,MAAOgB,EAAIzF,EAAEyE,MAC1B,OAAIzE,EAAE2E,QACK,IAAIH,EAAWyB,EAAST,EAAG/W,KAAKuZ,IAAIvC,IAAKlhB,KAAKmgB,MAElD,IAAIF,EAAWwB,EAAOR,EAAGC,GAAIlhB,KAAKmgB,KAC7C,EACAF,EAAWrgB,UAAU4lB,KAAOvF,EAAWrgB,UAAUohB,IACjDX,EAAazgB,UAAUohB,IAAM,SAAUrB,GACnC,IAAIlE,EAAIsE,EAAWJ,GACfsB,EAAIjhB,KAAKkgB,MACb,GAAIe,EAAI,IAAMxF,EAAE0E,KACZ,OAAOngB,KAAK4hB,SAASnG,EAAE+H,UAE3B,IAAItC,EAAIzF,EAAEyE,MACV,GAAIzE,EAAE2E,QAAS,CACX,GAAIG,EAAUU,EAAIC,GAAI,OAAO,IAAIb,EAAaY,EAAIC,GAClDA,EAAI5B,EAAapV,KAAKuZ,IAAIvC,GAC9B,CACA,OAAO,IAAIjB,EAAWyB,EAASR,EAAGhX,KAAKuZ,IAAIxC,IAAKA,EAAI,EACxD,EACAZ,EAAazgB,UAAU4lB,KAAOnF,EAAazgB,UAAUohB,IACrDV,EAAa1gB,UAAUohB,IAAM,SAAUrB,GACnC,OAAO,IAAIW,EAAatgB,KAAKkgB,MAAQH,EAAWJ,GAAGO,MACvD,EACAI,EAAa1gB,UAAU4lB,KAAOlF,EAAa1gB,UAAUohB,IA2DrDf,EAAWrgB,UAAUgiB,SAAW,SAAUjC,GACtC,IAAIlE,EAAIsE,EAAWJ,GACnB,GAAI3f,KAAKmgB,OAAS1E,EAAE0E,KAChB,OAAOngB,KAAKghB,IAAIvF,EAAE+H,UAEtB,IAAIvC,EAAIjhB,KAAKkgB,MAAOgB,EAAIzF,EAAEyE,MAC1B,OAAIzE,EAAE2E,QAAgB6B,EAAchB,EAAG/W,KAAKuZ,IAAIvC,GAAIlhB,KAAKmgB,MAtC7D,SAAqBc,EAAGC,EAAGf,GACvB,IAAID,EAQJ,OAPIS,EAAWM,EAAGC,IAAM,EACpBhB,EAAQ0B,EAASX,EAAGC,IAEpBhB,EAAQ0B,EAASV,EAAGD,GACpBd,GAAQA,GAGS,iBADrBD,EAAQM,EAAaN,KAEbC,IAAMD,GAASA,GACZ,IAAIG,EAAaH,IAErB,IAAID,EAAWC,EAAOC,EACjC,CAyBWsF,CAAYxE,EAAGC,EAAGlhB,KAAKmgB,KAClC,EACAF,EAAWrgB,UAAU8lB,MAAQzF,EAAWrgB,UAAUgiB,SAClDvB,EAAazgB,UAAUgiB,SAAW,SAAUjC,GACxC,IAAIlE,EAAIsE,EAAWJ,GACfsB,EAAIjhB,KAAKkgB,MACb,GAAIe,EAAI,IAAMxF,EAAE0E,KACZ,OAAOngB,KAAKghB,IAAIvF,EAAE+H,UAEtB,IAAItC,EAAIzF,EAAEyE,MACV,OAAIzE,EAAE2E,QACK,IAAIC,EAAaY,EAAIC,GAEzBe,EAAcf,EAAGhX,KAAKuZ,IAAIxC,GAAIA,GAAK,EAC9C,EACAZ,EAAazgB,UAAU8lB,MAAQrF,EAAazgB,UAAUgiB,SACtDtB,EAAa1gB,UAAUgiB,SAAW,SAAUjC,GACxC,OAAO,IAAIW,EAAatgB,KAAKkgB,MAAQH,EAAWJ,GAAGO,MACvD,EACAI,EAAa1gB,UAAU8lB,MAAQpF,EAAa1gB,UAAUgiB,SACtD3B,EAAWrgB,UAAU4jB,OAAS,WAC1B,OAAO,IAAIvD,EAAWjgB,KAAKkgB,OAAQlgB,KAAKmgB,KAC5C,EACAE,EAAazgB,UAAU4jB,OAAS,WAC5B,IAAIrD,EAAOngB,KAAKmgB,KACZwF,EAAQ,IAAItF,GAAcrgB,KAAKkgB,OAEnC,OADAyF,EAAMxF,MAAQA,EACPwF,CACX,EACArF,EAAa1gB,UAAU4jB,OAAS,WAC5B,OAAO,IAAIlD,GAActgB,KAAKkgB,MAClC,EACAD,EAAWrgB,UAAU6jB,IAAM,WACvB,OAAO,IAAIxD,EAAWjgB,KAAKkgB,OAAO,EACtC,EACAG,EAAazgB,UAAU6jB,IAAM,WACzB,OAAO,IAAIpD,EAAanW,KAAKuZ,IAAIzjB,KAAKkgB,OAC1C,EACAI,EAAa1gB,UAAU6jB,IAAM,WACzB,OAAO,IAAInD,EAAatgB,KAAKkgB,OAAS,EAAIlgB,KAAKkgB,OAASlgB,KAAKkgB,MACjE,EAsDAD,EAAWrgB,UAAUgmB,SAAW,SAAUjG,GACtC,IAAiF8D,EAL/DoC,EAAIC,EAKlBrK,EAAIsE,EAAWJ,GAAIsB,EAAIjhB,KAAKkgB,MAAOgB,EAAIzF,EAAEyE,MAAOC,EAAOngB,KAAKmgB,OAAS1E,EAAE0E,KAC3E,GAAI1E,EAAE2E,QAAS,CACX,GAAU,IAANc,EAAS,OAAOxB,EAAQ,GAC5B,GAAU,IAANwB,EAAS,OAAOlhB,KACpB,IAAW,IAAPkhB,EAAU,OAAOlhB,KAAKwjB,SAE1B,IADAC,EAAMvZ,KAAKuZ,IAAIvC,IACLhC,EACN,OAAO,IAAIe,EAAWqC,EAAcrB,EAAGwC,GAAMtD,GAEjDe,EAAI5B,EAAamE,EACrB,CACA,OAA6C,IAAIxD,GAfzC,MADU4F,EAgBD5E,EAAEhY,QAfC,MADE6c,EAgBK5E,EAAEjY,QAfG,MAAQ4c,EAAKC,EAAK,EAeUrD,EAAkBxB,EAAGC,GAC3DgB,EAAajB,EAAGC,GAD+Cf,EAEzF,EACAF,EAAWrgB,UAAUmmB,MAAQ9F,EAAWrgB,UAAUgmB,SASlDvF,EAAazgB,UAAUomB,iBAAmB,SAAU/E,GAChD,OAAIV,EAAUU,EAAEf,MAAQlgB,KAAKkgB,OAClB,IAAIG,EAAaY,EAAEf,MAAQlgB,KAAKkgB,OAEpC6C,EAAsB7Y,KAAKuZ,IAAIxC,EAAEf,OAAQZ,EAAapV,KAAKuZ,IAAIzjB,KAAKkgB,QAASlgB,KAAKmgB,OAASc,EAAEd,KACxG,EACAF,EAAWrgB,UAAUomB,iBAAmB,SAAU/E,GAC9C,OAAgB,IAAZA,EAAEf,MAAoBR,EAAQ,GAClB,IAAZuB,EAAEf,MAAoBlgB,MACT,IAAbihB,EAAEf,MAAqBlgB,KAAKwjB,SACzBT,EAAsB7Y,KAAKuZ,IAAIxC,EAAEf,OAAQlgB,KAAKkgB,MAAOlgB,KAAKmgB,OAASc,EAAEd,KAChF,EACAE,EAAazgB,UAAUgmB,SAAW,SAAUjG,GACxC,OAAOI,EAAWJ,GAAGqG,iBAAiBhmB,KAC1C,EACAqgB,EAAazgB,UAAUmmB,MAAQ1F,EAAazgB,UAAUgmB,SACtDtF,EAAa1gB,UAAUgmB,SAAW,SAAUjG,GACxC,OAAO,IAAIW,EAAatgB,KAAKkgB,MAAQH,EAAWJ,GAAGO,MACvD,EACAI,EAAa1gB,UAAUmmB,MAAQzF,EAAa1gB,UAAUgmB,SAmBtD3F,EAAWrgB,UAAUojB,OAAS,WAC1B,OAAO,IAAI/C,EAAW+C,EAAOhjB,KAAKkgB,QAAQ,EAC9C,EACAG,EAAazgB,UAAUojB,OAAS,WAC5B,IAAI9C,EAAQlgB,KAAKkgB,MAAQlgB,KAAKkgB,MAC9B,OAAIK,EAAUL,GAAe,IAAIG,EAAaH,GACvC,IAAID,EAAW+C,EAAO1D,EAAapV,KAAKuZ,IAAIzjB,KAAKkgB,UAAU,EACtE,EACAI,EAAa1gB,UAAUojB,OAAS,SAAUrD,GACtC,OAAO,IAAIW,EAAatgB,KAAKkgB,MAAQlgB,KAAKkgB,MAC9C,EA4IAD,EAAWrgB,UAAUqmB,OAAS,SAAUtG,GACpC,IAAIkE,EAASN,EAAUvjB,KAAM2f,GAC7B,MAAO,CAAE2D,SAAUO,EAAO,GAAIT,UAAWS,EAAO,GACpD,EACAvD,EAAa1gB,UAAUqmB,OAAS5F,EAAazgB,UAAUqmB,OAAShG,EAAWrgB,UAAUqmB,OACrFhG,EAAWrgB,UAAUylB,OAAS,SAAU1F,GACpC,OAAO4D,EAAUvjB,KAAM2f,GAAG,EAC9B,EACAW,EAAa1gB,UAAUsmB,KAAO5F,EAAa1gB,UAAUylB,OAAS,SAAU1F,GACpE,OAAO,IAAIW,EAAatgB,KAAKkgB,MAAQH,EAAWJ,GAAGO,MACvD,EACAG,EAAazgB,UAAUsmB,KAAO7F,EAAazgB,UAAUylB,OAASpF,EAAWrgB,UAAUsmB,KAAOjG,EAAWrgB,UAAUylB,OAC/GpF,EAAWrgB,UAAU8kB,IAAM,SAAU/E,GACjC,OAAO4D,EAAUvjB,KAAM2f,GAAG,EAC9B,EACAW,EAAa1gB,UAAU8kB,IAAMpE,EAAa1gB,UAAUwjB,UAAY,SAAUzD,GACtE,OAAO,IAAIW,EAAatgB,KAAKkgB,MAAQH,EAAWJ,GAAGO,MACvD,EACAG,EAAazgB,UAAUwjB,UAAY/C,EAAazgB,UAAU8kB,IAAMzE,EAAWrgB,UAAUwjB,UAAYnD,EAAWrgB,UAAU8kB,IACtHzE,EAAWrgB,UAAUumB,IAAM,SAAUxG,GACjC,IAAoDO,EAAOha,EAAGwc,EAA1DjH,EAAIsE,EAAWJ,GAAIsB,EAAIjhB,KAAKkgB,MAAOgB,EAAIzF,EAAEyE,MAC7C,GAAU,IAANgB,EAAS,OAAOxB,EAAQ,GAC5B,GAAU,IAANuB,EAAS,OAAOvB,EAAQ,GAC5B,GAAU,IAANuB,EAAS,OAAOvB,EAAQ,GAC5B,IAAW,IAAPuB,EAAU,OAAOxF,EAAEsJ,SAAWrF,EAAQ,GAAKA,GAAS,GACxD,GAAIjE,EAAE0E,KACF,OAAOT,EAAQ,GAEnB,IAAKjE,EAAE2E,QAAS,MAAM,IAAI9L,MAAM,gBAAkBmH,EAAE2K,WAAa,kBACjE,GAAIpmB,KAAKogB,SACDG,EAAUL,EAAQhW,KAAKic,IAAIlF,EAAGC,IAAK,OAAO,IAAIb,EAAaS,EAASZ,IAI5E,IAFAha,EAAIlG,KACJ0iB,EAAIhD,EAAQ,IAEA,EAAJwB,IACAwB,EAAIA,EAAEqD,MAAM7f,KACVgb,GAEI,IAANA,GACJA,GAAK,EACLhb,EAAIA,EAAE8c,SAEV,OAAON,CACX,EACArC,EAAazgB,UAAUumB,IAAMlG,EAAWrgB,UAAUumB,IAClD7F,EAAa1gB,UAAUumB,IAAM,SAAUxG,GACnC,IAAIlE,EAAIsE,EAAWJ,GACfsB,EAAIjhB,KAAKkgB,MAAOgB,EAAIzF,EAAEyE,MACtBmG,EAAK5G,OAAO,GAAI6G,EAAK7G,OAAO,GAAI8G,EAAK9G,OAAO,GAChD,GAAIyB,IAAMmF,EAAI,OAAO3G,EAAQ,GAC7B,GAAIuB,IAAMoF,EAAI,OAAO3G,EAAQ,GAC7B,GAAIuB,IAAMqF,EAAI,OAAO5G,EAAQ,GAC7B,GAAIuB,IAAMxB,QAAQ,GAAI,OAAOhE,EAAEsJ,SAAWrF,EAAQ,GAAKA,GAAS,GAChE,GAAIjE,EAAE+K,aAAc,OAAO,IAAIlG,EAAa+F,GAG5C,IAFA,IAAIngB,EAAIlG,KACJ0iB,EAAIhD,EAAQ,IAEPwB,EAAIoF,KAAQA,IACb5D,EAAIA,EAAEqD,MAAM7f,KACVgb,GAEFA,IAAMmF,GACVnF,GAAKqF,EACLrgB,EAAIA,EAAE8c,SAEV,OAAON,CACX,EACAzC,EAAWrgB,UAAU2lB,OAAS,SAAUkB,EAAK/B,GAGzC,GAFA+B,EAAM1G,EAAW0G,IACjB/B,EAAM3E,EAAW2E,IACTgC,SAAU,MAAM,IAAIpS,MAAM,qCAElC,IADA,IAAIgN,EAAI5B,EAAQ,GAAI8B,EAAOxhB,KAAK0kB,IAAIA,GAC7B+B,EAAIE,cAAc,CACrB,GAAInF,EAAKkF,SAAU,OAAOhH,EAAQ,GAC9B+G,EAAIG,UAAStF,EAAIA,EAAEsE,SAASpE,GAAMkD,IAAIA,IAC1C+B,EAAMA,EAAIpB,OAAO,GACjB7D,EAAOA,EAAKwB,SAAS0B,IAAIA,EAC7B,CACA,OAAOpD,CACX,EACAhB,EAAa1gB,UAAU2lB,OAASlF,EAAazgB,UAAU2lB,OAAStF,EAAWrgB,UAAU2lB,OAYrFtF,EAAWrgB,UAAU+gB,WAAa,SAAUhB,GACxC,IAAIlE,EAAIsE,EAAWJ,GAAIsB,EAAIjhB,KAAKkgB,MAAOgB,EAAIzF,EAAEyE,MAC7C,OAAIzE,EAAE2E,QAAgB,EACfO,EAAWM,EAAGC,EACzB,EACAb,EAAazgB,UAAU+gB,WAAa,SAAUhB,GAC1C,IAAIlE,EAAIsE,EAAWJ,GAAIsB,EAAI/W,KAAKuZ,IAAIzjB,KAAKkgB,OAAQgB,EAAIzF,EAAEyE,MACvD,OAAIzE,EAAE2E,QAEKa,KADPC,EAAIhX,KAAKuZ,IAAIvC,IACI,EAAID,EAAIC,EAAI,GAAK,GAE9B,CACZ,EACAZ,EAAa1gB,UAAU+gB,WAAa,SAAUhB,GAC1C,IAAIsB,EAAIjhB,KAAKkgB,MACTgB,EAAInB,EAAWJ,GAAGO,MAGtB,OAFAe,EAAIA,GAAK,EAAIA,GAAKA,MAClBC,EAAIA,GAAK,EAAIA,GAAKA,GACD,EAAID,EAAIC,EAAI,GAAK,CACtC,EACAjB,EAAWrgB,UAAUinB,QAAU,SAAUlH,GACrC,GAAIA,IAAMzI,IACN,OAAQ,EAEZ,GAAIyI,KAAM,IACN,OAAO,EAEX,IAAIlE,EAAIsE,EAAWJ,GAAIsB,EAAIjhB,KAAKkgB,MAAOgB,EAAIzF,EAAEyE,MAC7C,OAAIlgB,KAAKmgB,OAAS1E,EAAE0E,KACT1E,EAAE0E,KAAO,GAAK,EAErB1E,EAAE2E,QACKpgB,KAAKmgB,MAAQ,EAAI,EAErBQ,EAAWM,EAAGC,IAAMlhB,KAAKmgB,MAAQ,EAAI,EAChD,EACAF,EAAWrgB,UAAUknB,UAAY7G,EAAWrgB,UAAUinB,QACtDxG,EAAazgB,UAAUinB,QAAU,SAAUlH,GACvC,GAAIA,IAAMzI,IACN,OAAQ,EAEZ,GAAIyI,KAAM,IACN,OAAO,EAEX,IAAIlE,EAAIsE,EAAWJ,GAAIsB,EAAIjhB,KAAKkgB,MAAOgB,EAAIzF,EAAEyE,MAC7C,OAAIzE,EAAE2E,QACKa,GAAKC,EAAI,EAAID,EAAIC,EAAI,GAAK,EAEjCD,EAAI,IAAMxF,EAAE0E,KACLc,EAAI,GAAK,EAAI,EAEjBA,EAAI,EAAI,GAAK,CACxB,EACAZ,EAAazgB,UAAUknB,UAAYzG,EAAazgB,UAAUinB,QAC1DvG,EAAa1gB,UAAUinB,QAAU,SAAUlH,GACvC,GAAIA,IAAMzI,IACN,OAAQ,EAEZ,GAAIyI,KAAM,IACN,OAAO,EAEX,IAAIsB,EAAIjhB,KAAKkgB,MACTgB,EAAInB,EAAWJ,GAAGO,MACtB,OAAOe,IAAMC,EAAI,EAAID,EAAIC,EAAI,GAAK,CACtC,EACAZ,EAAa1gB,UAAUknB,UAAYxG,EAAa1gB,UAAUinB,QAC1D5G,EAAWrgB,UAAUklB,OAAS,SAAUnF,GACpC,OAA2B,IAApB3f,KAAK6mB,QAAQlH,EACxB,EACAW,EAAa1gB,UAAUmnB,GAAKzG,EAAa1gB,UAAUklB,OAASzE,EAAazgB,UAAUmnB,GAAK1G,EAAazgB,UAAUklB,OAAS7E,EAAWrgB,UAAUmnB,GAAK9G,EAAWrgB,UAAUklB,OACvK7E,EAAWrgB,UAAUonB,UAAY,SAAUrH,GACvC,OAA2B,IAApB3f,KAAK6mB,QAAQlH,EACxB,EACAW,EAAa1gB,UAAUqnB,IAAM3G,EAAa1gB,UAAUonB,UAAY3G,EAAazgB,UAAUqnB,IAAM5G,EAAazgB,UAAUonB,UAAY/G,EAAWrgB,UAAUqnB,IAAMhH,EAAWrgB,UAAUonB,UAChL/G,EAAWrgB,UAAUsnB,QAAU,SAAUvH,GACrC,OAAO3f,KAAK6mB,QAAQlH,GAAK,CAC7B,EACAW,EAAa1gB,UAAUunB,GAAK7G,EAAa1gB,UAAUsnB,QAAU7G,EAAazgB,UAAUunB,GAAK9G,EAAazgB,UAAUsnB,QAAUjH,EAAWrgB,UAAUunB,GAAKlH,EAAWrgB,UAAUsnB,QACzKjH,EAAWrgB,UAAUqlB,OAAS,SAAUtF,GACpC,OAAO3f,KAAK6mB,QAAQlH,GAAK,CAC7B,EACAW,EAAa1gB,UAAUwnB,GAAK9G,EAAa1gB,UAAUqlB,OAAS5E,EAAazgB,UAAUwnB,GAAK/G,EAAazgB,UAAUqlB,OAAShF,EAAWrgB,UAAUwnB,GAAKnH,EAAWrgB,UAAUqlB,OACvKhF,EAAWrgB,UAAUynB,gBAAkB,SAAU1H,GAC7C,OAAO3f,KAAK6mB,QAAQlH,IAAM,CAC9B,EACAW,EAAa1gB,UAAU0nB,IAAMhH,EAAa1gB,UAAUynB,gBAAkBhH,EAAazgB,UAAU0nB,IAAMjH,EAAazgB,UAAUynB,gBAAkBpH,EAAWrgB,UAAU0nB,IAAMrH,EAAWrgB,UAAUynB,gBAC5LpH,EAAWrgB,UAAU2nB,eAAiB,SAAU5H,GAC5C,OAAO3f,KAAK6mB,QAAQlH,IAAM,CAC9B,EACAW,EAAa1gB,UAAU4nB,IAAMlH,EAAa1gB,UAAU2nB,eAAiBlH,EAAazgB,UAAU4nB,IAAMnH,EAAazgB,UAAU2nB,eAAiBtH,EAAWrgB,UAAU4nB,IAAMvH,EAAWrgB,UAAU2nB,eAC1LtH,EAAWrgB,UAAUmlB,OAAS,WAC1B,QAAwB,EAAhB/kB,KAAKkgB,MAAM,GACvB,EACAG,EAAazgB,UAAUmlB,OAAS,WAC5B,QAAqB,EAAb/kB,KAAKkgB,MACjB,EACAI,EAAa1gB,UAAUmlB,OAAS,WAC5B,OAAQ/kB,KAAKkgB,MAAQT,OAAO,MAAQA,OAAO,EAC/C,EACAQ,EAAWrgB,UAAUgnB,MAAQ,WACzB,QAA+B,GAAvB5mB,KAAKkgB,MAAM,GACvB,EACAG,EAAazgB,UAAUgnB,MAAQ,WAC3B,QAA4B,GAApB5mB,KAAKkgB,MACjB,EACAI,EAAa1gB,UAAUgnB,MAAQ,WAC3B,OAAQ5mB,KAAKkgB,MAAQT,OAAO,MAAQA,OAAO,EAC/C,EACAQ,EAAWrgB,UAAU+mB,WAAa,WAC9B,OAAQ3mB,KAAKmgB,IACjB,EACAE,EAAazgB,UAAU+mB,WAAa,WAChC,OAAO3mB,KAAKkgB,MAAQ,CACxB,EACAI,EAAa1gB,UAAU+mB,WAAatG,EAAazgB,UAAU+mB,WAC3D1G,EAAWrgB,UAAU4mB,WAAa,WAC9B,OAAOxmB,KAAKmgB,IAChB,EACAE,EAAazgB,UAAU4mB,WAAa,WAChC,OAAOxmB,KAAKkgB,MAAQ,CACxB,EACAI,EAAa1gB,UAAU4mB,WAAanG,EAAazgB,UAAU4mB,WAC3DvG,EAAWrgB,UAAUilB,OAAS,WAC1B,OAAO,CACX,EACAxE,EAAazgB,UAAUilB,OAAS,WAC5B,OAAgC,IAAzB3a,KAAKuZ,IAAIzjB,KAAKkgB,MACzB,EACAI,EAAa1gB,UAAUilB,OAAS,WAC5B,OAAO7kB,KAAKyjB,MAAMvD,QAAUT,OAAO,EACvC,EACAQ,EAAWrgB,UAAU8mB,OAAS,WAC1B,OAAO,CACX,EACArG,EAAazgB,UAAU8mB,OAAS,WAC5B,OAAsB,IAAf1mB,KAAKkgB,KAChB,EACAI,EAAa1gB,UAAU8mB,OAAS,WAC5B,OAAO1mB,KAAKkgB,QAAUT,OAAO,EACjC,EACAQ,EAAWrgB,UAAUolB,cAAgB,SAAUrF,GAC3C,IAAIlE,EAAIsE,EAAWJ,GACnB,OAAIlE,EAAEiL,aACFjL,EAAEoJ,WACkB,IAApBpJ,EAAEkF,WAAW,GAAiB3gB,KAAK+kB,SAChC/kB,KAAK0kB,IAAIjJ,GAAGiL,UACvB,EACApG,EAAa1gB,UAAUolB,cAAgB3E,EAAazgB,UAAUolB,cAAgB/E,EAAWrgB,UAAUolB,cA2BnG/E,EAAWrgB,UAAU6nB,QAAU,SAAUC,GACrC,IAAID,EAAU7C,EAAa5kB,MAC3B,GAAIynB,IAAYrhB,EAAW,OAAOqhB,EAClC,IAAIhM,EAAIzb,KAAKyjB,MACTkE,EAAOlM,EAAEmM,YACb,GAAID,GAAQ,GAAI,OAAOzC,EAAgBzJ,EAAG,CAAC,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,KAGnF,IAFA,IAAIoM,EAAO3d,KAAK4d,IAAI,GAAKH,EAAKI,aAC1Bvf,EAAI0B,KAAK6W,MAAgB,IAAX2G,EAAkB,EAAIxd,KAAKic,IAAI0B,EAAM,GAAKA,GACnD5G,EAAI,GAAIrc,EAAI,EAAGA,EAAI4D,EAAG5D,IAC3Bqc,EAAElb,KAAKkZ,EAAOra,EAAI,IAEtB,OAAOsgB,EAAgBzJ,EAAGwF,EAC9B,EACAX,EAAa1gB,UAAU6nB,QAAUpH,EAAazgB,UAAU6nB,QAAUxH,EAAWrgB,UAAU6nB,QACvFxH,EAAWrgB,UAAUooB,gBAAkB,SAAUC,GAC7C,IAAIR,EAAU7C,EAAa5kB,MAC3B,GAAIynB,IAAYrhB,EAAW,OAAOqhB,EAGlC,IAFA,IAAIhM,EAAIzb,KAAKyjB,MACTjb,EAAIyf,IAAe7hB,EAAY,EAAI6hB,EAC9BhH,EAAI,GAAIrc,EAAI,EAAGA,EAAI4D,EAAG5D,IAC3Bqc,EAAElb,KAAKkZ,EAAOiJ,YAAY,EAAGzM,EAAEiK,MAAM,KAEzC,OAAOR,EAAgBzJ,EAAGwF,EAC9B,EACAX,EAAa1gB,UAAUooB,gBAAkB3H,EAAazgB,UAAUooB,gBAAkB/H,EAAWrgB,UAAUooB,gBACvG/H,EAAWrgB,UAAUuoB,OAAS,SAAU1M,GAEpC,IADA,IAA8E0H,EAAGiF,EAAOC,EAApF7f,EAAIyW,EAAOqJ,KAAMC,EAAOtJ,EAAOuJ,IAAKlH,EAAIvB,EAAWtE,GAAIgN,EAAOzoB,KAAKyjB,OAC/DgF,EAAK/B,UACTvD,EAAI7B,EAAE+D,OAAOoD,GACbL,EAAQ5f,EACR6f,EAAQ/G,EACR9Y,EAAI+f,EACJjH,EAAImH,EACJF,EAAOH,EAAMxG,SAASuB,EAAEyC,SAAS2C,IACjCE,EAAOJ,EAAMzG,SAASuB,EAAEyC,SAAS6C,IAErC,IAAKnH,EAAEuD,SAAU,MAAM,IAAIvQ,MAAMtU,KAAKomB,WAAa,QAAU3K,EAAE2K,WAAa,qBAI5E,OAHsB,IAAlB5d,EAAEqe,QAAQ,KACVre,EAAIA,EAAEwY,IAAIvF,IAEVzb,KAAKwmB,aACEhe,EAAEgb,SAENhb,CACX,EACA8X,EAAa1gB,UAAUuoB,OAAS9H,EAAazgB,UAAUuoB,OAASlI,EAAWrgB,UAAUuoB,OACrFlI,EAAWrgB,UAAU0lB,KAAO,WACxB,IAAIpF,EAAQlgB,KAAKkgB,MACjB,OAAIlgB,KAAKmgB,KACE8B,EAAc/B,EAAO,EAAGlgB,KAAKmgB,MAEjC,IAAIF,EAAWyB,EAASxB,EAAO,GAAIlgB,KAAKmgB,KACnD,EACAE,EAAazgB,UAAU0lB,KAAO,WAC1B,IAAIpF,EAAQlgB,KAAKkgB,MACjB,OAAIA,EAAQ,EAAId,EAAgB,IAAIiB,EAAaH,EAAQ,GAClD,IAAID,EAAWZ,GAAa,EACvC,EACAiB,EAAa1gB,UAAU0lB,KAAO,WAC1B,OAAO,IAAIhF,EAAatgB,KAAKkgB,MAAQT,OAAO,GAChD,EACAQ,EAAWrgB,UAAUwlB,KAAO,WACxB,IAAIlF,EAAQlgB,KAAKkgB,MACjB,OAAIlgB,KAAKmgB,KACE,IAAIF,EAAWyB,EAASxB,EAAO,IAAI,GAEvC+B,EAAc/B,EAAO,EAAGlgB,KAAKmgB,KACxC,EACAE,EAAazgB,UAAUwlB,KAAO,WAC1B,IAAIlF,EAAQlgB,KAAKkgB,MACjB,OAAIA,EAAQ,GAAKd,EAAgB,IAAIiB,EAAaH,EAAQ,GACnD,IAAID,EAAWZ,GAAa,EACvC,EACAiB,EAAa1gB,UAAUwlB,KAAO,WAC1B,OAAO,IAAI9E,EAAatgB,KAAKkgB,MAAQT,OAAO,GAChD,EAEA,IADA,IAAIiJ,EAAc,CAAC,GACZ,EAAIA,EAAYA,EAAYzf,OAAS,IAAMiW,GAAMwJ,EAAY3iB,KAAK,EAAI2iB,EAAYA,EAAYzf,OAAS,IAC9G,IAAI0f,EAAgBD,EAAYzf,OAAQ2f,EAAgBF,EAAYC,EAAgB,GAEpF,SAASE,EAAcpN,GACnB,OAAOvR,KAAKuZ,IAAIhI,IAAMyD,CAC1B,CAoCA,SAAS4J,EAAQ5iB,EAAGwc,EAAGqG,GACnBrG,EAAI3C,EAAW2C,GAMf,IALA,IAAIsG,EAAQ9iB,EAAEsgB,aAAcyC,EAAQvG,EAAE8D,aAClC0C,EAAOF,EAAQ9iB,EAAEijB,MAAQjjB,EAAGkjB,EAAOH,EAAQvG,EAAEyG,MAAQzG,EACrD2G,EAAS,EAAGC,EAAS,EACrBC,EAAU,KAAMC,EAAU,KAC1B3F,EAAS,IACLqF,EAAKxC,WAAa0C,EAAK1C,UAE3B2C,GADAE,EAAUhG,EAAU2F,EAAMN,IACT,GAAGb,aAChBiB,IACAK,EAAST,EAAgB,EAAIS,GAGjCC,GADAE,EAAUjG,EAAU6F,EAAMR,IACT,GAAGb,aAChBkB,IACAK,EAASV,EAAgB,EAAIU,GAEjCJ,EAAOK,EAAQ,GACfH,EAAOI,EAAQ,GACf3F,EAAO9d,KAAKgjB,EAAGM,EAAQC,IAG3B,IADA,IAAInI,EAA2C,IAArC4H,EAAGC,EAAQ,EAAI,EAAGC,EAAQ,EAAI,GAAWhK,GAAQ,GAAKA,EAAO,GAC9Dra,EAAIif,EAAO5a,OAAS,EAAGrE,GAAK,EAAGA,GAAK,EACzCuc,EAAMA,EAAIyE,SAASgD,GAAe5H,IAAI/B,EAAO4E,EAAOjf,KAExD,OAAOuc,CACX,CA7DAlB,EAAWrgB,UAAU2iB,UAAY,SAAU5C,GACvC,IAAIlE,EAAIsE,EAAWJ,GAAGoI,aACtB,IAAKc,EAAcpN,GACf,MAAM,IAAInH,MAAMmV,OAAOhO,GAAK,+BAEhC,GAAIA,EAAI,EAAG,OAAOzb,KAAK0pB,YAAYjO,GACnC,IAAIoI,EAAS7jB,KACb,GAAI6jB,EAAO6C,SAAU,OAAO7C,EAC5B,KAAOpI,GAAKkN,GACR9E,EAASA,EAAO+B,SAASgD,GACzBnN,GAAKkN,EAAgB,EAEzB,OAAO9E,EAAO+B,SAAS8C,EAAYjN,GACvC,EACA6E,EAAa1gB,UAAU2iB,UAAYlC,EAAazgB,UAAU2iB,UAAYtC,EAAWrgB,UAAU2iB,UAC3FtC,EAAWrgB,UAAU8pB,WAAa,SAAU/J,GACxC,IAAIgK,EACAlO,EAAIsE,EAAWJ,GAAGoI,aACtB,IAAKc,EAAcpN,GACf,MAAM,IAAInH,MAAMmV,OAAOhO,GAAK,+BAEhC,GAAIA,EAAI,EAAG,OAAOzb,KAAKuiB,WAAW9G,GAElC,IADA,IAAIoI,EAAS7jB,KACNyb,GAAKkN,GAAe,CACvB,GAAI9E,EAAO6C,UAAY7C,EAAO2C,cAAgB3C,EAAOgB,SAAU,OAAOhB,EAEtEA,GADA8F,EAASpG,EAAUM,EAAQ+E,IACX,GAAGpC,aAAemD,EAAO,GAAGvE,OAASuE,EAAO,GAC5DlO,GAAKkN,EAAgB,CACzB,CAEA,OADAgB,EAASpG,EAAUM,EAAQ6E,EAAYjN,KACzB,GAAG+K,aAAemD,EAAO,GAAGvE,OAASuE,EAAO,EAC9D,EACArJ,EAAa1gB,UAAU8pB,WAAarJ,EAAazgB,UAAU8pB,WAAazJ,EAAWrgB,UAAU8pB,WA+B7FzJ,EAAWrgB,UAAUupB,IAAM,WACvB,OAAOnpB,KAAKwjB,SAAS4B,MACzB,EACA9E,EAAa1gB,UAAUupB,IAAM9I,EAAazgB,UAAUupB,IAAMlJ,EAAWrgB,UAAUupB,IAC/ElJ,EAAWrgB,UAAUgqB,IAAM,SAAUnO,GACjC,OAAOqN,EAAQ9oB,KAAMyb,GAAG,SAAUwF,EAAGC,GACjC,OAAOD,EAAIC,CACf,GACJ,EACAZ,EAAa1gB,UAAUgqB,IAAMvJ,EAAazgB,UAAUgqB,IAAM3J,EAAWrgB,UAAUgqB,IAC/E3J,EAAWrgB,UAAUiqB,GAAK,SAAUpO,GAChC,OAAOqN,EAAQ9oB,KAAMyb,GAAG,SAAUwF,EAAGC,GACjC,OAAOD,EAAIC,CACf,GACJ,EACAZ,EAAa1gB,UAAUiqB,GAAKxJ,EAAazgB,UAAUiqB,GAAK5J,EAAWrgB,UAAUiqB,GAC7E5J,EAAWrgB,UAAUkqB,IAAM,SAAUrO,GACjC,OAAOqN,EAAQ9oB,KAAMyb,GAAG,SAAUwF,EAAGC,GACjC,OAAOD,EAAIC,CACf,GACJ,EACAZ,EAAa1gB,UAAUkqB,IAAMzJ,EAAazgB,UAAUkqB,IAAM7J,EAAWrgB,UAAUkqB,IAC/E,IAAIC,EAAY,GAAK,GAAIC,GAAc9K,GAAQA,IAASA,GAAQA,GAAQ6K,EAExE,SAASE,EAASxO,GACd,IAAIkE,EAAIlE,EAAEyE,MACNha,EAAiB,iBAANyZ,EAAiBA,EAAIoK,EAAyB,iBAANpK,EAAiBA,EAAIF,OAAOsK,GAAapK,EAAE,GAAKA,EAAE,GAAKT,EAAO8K,EACrH,OAAO9jB,GAAKA,CAChB,CAEA,SAASgkB,EAAiBhK,EAAOsB,GAC7B,GAAIA,EAAKsF,UAAU5G,IAAU,EAAG,CAC5B,IAAIiK,EAAMD,EAAiBhK,EAAOsB,EAAKwB,OAAOxB,IAC1C4I,EAAID,EAAIC,EACR1gB,EAAIygB,EAAIzgB,EACRlB,EAAI4hB,EAAExE,SAASpE,GACnB,OAAOhZ,EAAEse,UAAU5G,IAAU,EAAI,CAAEkK,EAAG5hB,EAAGkB,EAAO,EAAJA,EAAQ,GAAM,CAAE0gB,EAAGA,EAAG1gB,EAAO,EAAJA,EACzE,CACA,MAAO,CAAE0gB,EAAGnL,EAAO,GAAIvV,EAAG,EAC9B,CAcA,SAASS,EAAI8W,EAAGC,GAGZ,OAFAD,EAAIlB,EAAWkB,GACfC,EAAInB,EAAWmB,GACRD,EAAEiG,QAAQhG,GAAKD,EAAIC,CAC9B,CAEA,SAASmJ,EAAIpJ,EAAGC,GAGZ,OAFAD,EAAIlB,EAAWkB,GACfC,EAAInB,EAAWmB,GACRD,EAAEgE,OAAO/D,GAAKD,EAAIC,CAC7B,CAEA,SAASoJ,EAAIrJ,EAAGC,GAGZ,GAFAD,EAAIlB,EAAWkB,GAAGwC,MAClBvC,EAAInB,EAAWmB,GAAGuC,MACdxC,EAAE6D,OAAO5D,GAAI,OAAOD,EACxB,GAAIA,EAAEyF,SAAU,OAAOxF,EACvB,GAAIA,EAAEwF,SAAU,OAAOzF,EAEvB,IADA,IAAoBxY,EAAGD,EAAnBma,EAAIjD,EAAQ,GACTuB,EAAE8D,UAAY7D,EAAE6D,UACnBtc,EAAI4hB,EAAIJ,EAAShJ,GAAIgJ,EAAS/I,IAC9BD,EAAIA,EAAEoE,OAAO5c,GACbyY,EAAIA,EAAEmE,OAAO5c,GACbka,EAAIA,EAAEiD,SAASnd,GAEnB,KAAOwY,EAAE8D,UACL9D,EAAIA,EAAEoE,OAAO4E,EAAShJ,IAE1B,EAAG,CACC,KAAOC,EAAE6D,UACL7D,EAAIA,EAAEmE,OAAO4E,EAAS/I,IAEtBD,EAAEiG,QAAQhG,KACV1Y,EAAI0Y,EACJA,EAAID,EACJA,EAAIzY,GAER0Y,EAAIA,EAAEU,SAASX,EACnB,QAAUC,EAAEwF,UACZ,OAAO/D,EAAEkC,SAAW5D,EAAIA,EAAE2E,SAASjD,EACvC,CApDA1C,EAAWrgB,UAAUgoB,UAAY,WAC7B,IAAInM,EAAIzb,KAIR,OAHIyb,EAAEqL,UAAU7H,EAAO,IAAM,IACzBxD,EAAIA,EAAE+H,SAAS5B,SAAS3C,EAAO,KAEJ,IAA3BxD,EAAEqL,UAAU7H,EAAO,IACZA,EAAO,GAEXA,EAAOiL,EAAiBzO,EAAGwD,EAAO,IAAIvV,GAAGsX,IAAI/B,EAAO,GAC/D,EACAqB,EAAa1gB,UAAUgoB,UAAYvH,EAAazgB,UAAUgoB,UAAY3H,EAAWrgB,UAAUgoB,UAmE3F,IAAI5H,EAAY,SAAUuK,EAAM/I,EAAM3B,EAAUC,GAC5CD,EAAWA,GAAYN,EACvBgL,EAAOd,OAAOc,GACTzK,IACDyK,EAAOA,EAAKC,cACZ3K,EAAWA,EAAS2K,eAExB,IACI5lB,EADAqE,EAASshB,EAAKthB,OAEdwhB,EAAUvgB,KAAKuZ,IAAIjC,GACnBkJ,EAAiB,CAAC,EACtB,IAAK9lB,EAAI,EAAGA,EAAIib,EAAS5W,OAAQrE,IAC7B8lB,EAAe7K,EAASjb,IAAMA,EAElC,IAAKA,EAAI,EAAGA,EAAIqE,EAAQrE,IAEpB,GAAU,OADN+d,EAAI4H,EAAK3lB,KAET+d,KAAK+H,GACDA,EAAe/H,IAAM8H,EAAS,CAC9B,GAAU,MAAN9H,GAAyB,IAAZ8H,EAAe,SAChC,MAAM,IAAInW,MAAMqO,EAAI,iCAAmCnB,EAAO,IAClE,CAGRA,EAAOzB,EAAWyB,GAClB,IAAImJ,EAAS,GACTnE,EAAyB,MAAZ+D,EAAK,GACtB,IAAK3lB,EAAI4hB,EAAa,EAAI,EAAG5hB,EAAI2lB,EAAKthB,OAAQrE,IAAK,CAC/C,IAAI+d,EACJ,IADIA,EAAI4H,EAAK3lB,MACJ8lB,EAAgBC,EAAO5kB,KAAKga,EAAW2K,EAAe/H,SAAW,IAAU,MAANA,EAMvE,MAAM,IAAIrO,MAAMqO,EAAI,6BALvB,IAAIriB,EAAQsE,EACZ,GACIA,UACiB,MAAZ2lB,EAAK3lB,IAAcA,EAAI2lB,EAAKthB,QACrC0hB,EAAO5kB,KAAKga,EAAWwK,EAAKhP,MAAMjb,EAAQ,EAAGsE,IACM,CAC3D,CACA,OAAOgmB,EAAmBD,EAAQnJ,EAAMgF,EAC5C,EAEA,SAASoE,EAAmBD,EAAQnJ,EAAMgF,GACtC,IAAwC5hB,EAApCimB,EAAMnL,EAAQ,GAAIyG,EAAMzG,EAAQ,GACpC,IAAK9a,EAAI+lB,EAAO1hB,OAAS,EAAGrE,GAAK,EAAGA,IAChCimB,EAAMA,EAAI7J,IAAI2J,EAAO/lB,GAAGmhB,MAAMI,IAC9BA,EAAMA,EAAIJ,MAAMvE,GAEpB,OAAOgF,EAAaqE,EAAIrH,SAAWqH,CACvC,CAUA,SAASC,EAAOrP,EAAG+F,GAEf,IADAA,EAAOvC,EAAOuC,IACLkF,SAAU,CACf,GAAIjL,EAAEiL,SAAU,MAAO,CAAExG,MAAO,CAAC,GAAIsG,YAAY,GACjD,MAAM,IAAIlS,MAAM,4CACpB,CACA,GAAIkN,EAAKsD,QAAQ,GAAI,CACjB,GAAIrJ,EAAEiL,SAAU,MAAO,CAAExG,MAAO,CAAC,GAAIsG,YAAY,GACjD,GAAI/K,EAAE+K,aAAc,MAAO,CACvBtG,MAAO,GAAGsC,OAAO/c,MAAM,GAAIob,MAAMpb,MAAM,KAAMob,OAAOpF,EAAEsM,eAAegD,IAAIlK,MAAMjhB,UAAUorB,QAAS,CAAC,EAAG,KACtGxE,YAAY,GAEhB,IAAI/F,EAAMI,MAAMpb,MAAM,KAAMob,MAAMpF,EAAEsM,aAAe,IAAIgD,IAAIlK,MAAMjhB,UAAUorB,QAAS,CAAC,EAAG,IAExF,OADAvK,EAAI6D,QAAQ,CAAC,IACN,CAAEpE,MAAO,GAAGsC,OAAO/c,MAAM,GAAIgb,GAAM+F,YAAY,EAC1D,CACA,IAAIyE,GAAM,EAKV,GAJIxP,EAAE+K,cAAgBhF,EAAKmF,eACvBsE,GAAM,EACNxP,EAAIA,EAAEgI,OAENjC,EAAKqD,SACL,OAAIpJ,EAAEiL,SAAiB,CAAExG,MAAO,CAAC,GAAIsG,YAAY,GAC1C,CAAEtG,MAAOW,MAAMpb,MAAM,KAAMob,MAAMpF,EAAEsM,eAAegD,IAAIG,OAAOtrB,UAAUorB,QAAS,GAAIxE,WAAYyE,GAI3G,IAFA,IACchF,EADVkF,EAAM,GACNC,EAAO3P,EACJ2P,EAAK5E,cAAgB4E,EAAKzK,WAAWa,IAAS,GAAG,CACpDyE,EAASmF,EAAKnF,OAAOzE,GACrB4J,EAAOnF,EAAO3C,SACd,IAAI+H,EAAQpF,EAAO7C,UACfiI,EAAM7E,eACN6E,EAAQ7J,EAAKkE,MAAM2F,GAAO5H,MAC1B2H,EAAOA,EAAK9F,QAEhB6F,EAAIplB,KAAKslB,EAAMtD,aACnB,CAEA,OADAoD,EAAIplB,KAAKqlB,EAAKrD,cACP,CAAE7H,MAAOiL,EAAI5G,UAAWiC,WAAYyE,EAC/C,CAEA,SAASK,EAAa7P,EAAG+F,EAAM3B,GAC3B,IAAIY,EAAMqK,EAAOrP,EAAG+F,GACpB,OAAQf,EAAI+F,WAAa,IAAM,IAAM/F,EAAIP,MAAM6K,KAAI,SAAU7kB,GACzD,OApDR,SAAmBmlB,EAAOxL,GAEtB,OAAIwL,GADJxL,EAAWA,GAAYN,GACFtW,OACV4W,EAASwL,GAEb,IAAMA,EAAQ,GACzB,CA8CeE,CAAUrlB,EAAG2Z,EACxB,IAAG2L,KAAK,GACZ,CA2CA,SAASC,EAAiB9L,GACtB,GAAIY,GAAWZ,GAAI,CACf,IAAIzZ,GAAKyZ,EACT,GAAIzZ,IAAM4a,EAAS5a,GAAI,OAAOsZ,EAAuB,IAAIc,EAAab,OAAOvZ,IAAM,IAAIma,EAAana,GACpG,MAAM,IAAIoO,MAAM,oBAAsBqL,EAC1C,CACA,IAAIQ,EAAgB,MAATR,EAAE,GACTQ,IAAMR,EAAIA,EAAEpE,MAAM,IACtB,IAAID,EAAQqE,EAAErE,MAAM,MACpB,GAAIA,EAAMrS,OAAS,EAAG,MAAM,IAAIqL,MAAM,oBAAsBgH,EAAMkQ,KAAK,MACvE,GAAqB,IAAjBlQ,EAAMrS,OAAc,CACpB,IAAIwd,EAAMnL,EAAM,GAGhB,GAFe,MAAXmL,EAAI,KAAYA,EAAMA,EAAIlL,MAAM,KACpCkL,GAAOA,KACK3F,EAAS2F,KAASlG,EAAUkG,GAAM,MAAM,IAAInS,MAAM,oBAAsBmS,EAAM,6BAC1F,IAAI8D,EAAOjP,EAAM,GACboQ,EAAenB,EAAK1f,QAAQ,KAKhC,GAJI6gB,GAAgB,IAChBjF,GAAO8D,EAAKthB,OAASyiB,EAAe,EACpCnB,EAAOA,EAAKhP,MAAM,EAAGmQ,GAAgBnB,EAAKhP,MAAMmQ,EAAe,IAE/DjF,EAAM,EAAG,MAAM,IAAInS,MAAM,sDAE7BqL,EADA4K,GAAQ,IAAI1J,MAAM4F,EAAM,GAAG+E,KAAK,IAEpC,CAEA,IADc,kBAAkBG,KAAKhM,GACvB,MAAM,IAAIrL,MAAM,oBAAsBqL,GACpD,GAAIH,EACA,OAAO,IAAIc,EAAab,OAAOU,EAAO,IAAMR,EAAIA,IAGpD,IADA,IAAI2B,EAAI,GAAInX,EAAMwV,EAAE1W,OAAQ0Y,EAAIxC,EAAUkL,EAAMlgB,EAAMwX,EAC/CxX,EAAM,GACTmX,EAAEvb,MAAM4Z,EAAEpE,MAAM8O,EAAKlgB,KACrBkgB,GAAO1I,GACG,IAAG0I,EAAM,GACnBlgB,GAAOwX,EAGX,OADAjB,EAAKY,GACE,IAAIrB,EAAWqB,EAAGnB,EAC7B,CAaA,SAASJ,EAAWJ,GAChB,MAAiB,iBAANA,EAZf,SAA0BA,GACtB,GAAIH,EACA,OAAO,IAAIc,EAAab,OAAOE,IAEnC,GAAIY,EAAUZ,GAAI,CACd,GAAIA,IAAMmB,EAASnB,GAAI,MAAM,IAAIrL,MAAMqL,EAAI,uBAC3C,OAAO,IAAIU,EAAaV,EAC5B,CACA,OAAO8L,EAAiB9L,EAAEyG,WAC9B,CAIewF,CAAiBjM,GAEX,iBAANA,EACA8L,EAAiB9L,GAEX,iBAANA,EACA,IAAIW,EAAaX,GAErBA,CACX,CAxGAM,EAAWrgB,UAAUisB,QAAU,SAAUjM,GACrC,OAAOkL,EAAO9qB,KAAM4f,EACxB,EACAS,EAAazgB,UAAUisB,QAAU,SAAUjM,GACvC,OAAOkL,EAAO9qB,KAAM4f,EACxB,EACAU,EAAa1gB,UAAUisB,QAAU,SAAUjM,GACvC,OAAOkL,EAAO9qB,KAAM4f,EACxB,EACAK,EAAWrgB,UAAUwmB,SAAW,SAAUxG,EAAOC,GAE7C,GADID,IAAUxZ,IAAWwZ,EAAQ,IACnB,KAAVA,EAAc,OAAO0L,EAAatrB,KAAM4f,EAAOC,GAEnD,IADA,IAA2EwL,EAAvE1L,EAAI3f,KAAKkgB,MAAOyB,EAAIhC,EAAE1W,OAAQiM,EAAMuU,OAAO9J,IAAIgC,MAC1CA,GAAK,GACV0J,EAAQ5B,OAAO9J,EAAEgC,IACjBzM,GAH4D,UAG/CqG,MAAM8P,EAAMpiB,QAAUoiB,EAGvC,OADWrrB,KAAKmgB,KAAO,IAAM,IACfjL,CAClB,EACAmL,EAAazgB,UAAUwmB,SAAW,SAAUxG,EAAOC,GAE/C,OADID,IAAUxZ,IAAWwZ,EAAQ,IACpB,IAATA,EAAoB0L,EAAatrB,KAAM4f,EAAOC,GAC3C4J,OAAOzpB,KAAKkgB,MACvB,EACAI,EAAa1gB,UAAUwmB,SAAW/F,EAAazgB,UAAUwmB,SACzD9F,EAAa1gB,UAAUksB,OAAS7L,EAAWrgB,UAAUksB,OAASzL,EAAazgB,UAAUksB,OAAS,WAC1F,OAAO9rB,KAAKomB,UAChB,EACAnG,EAAWrgB,UAAUorB,QAAU,WAC3B,OAAO7U,SAASnW,KAAKomB,WAAY,GACrC,EACAnG,EAAWrgB,UAAUmoB,WAAa9H,EAAWrgB,UAAUorB,QACvD3K,EAAazgB,UAAUorB,QAAU,WAC7B,OAAOhrB,KAAKkgB,KAChB,EACAG,EAAazgB,UAAUmoB,WAAa1H,EAAazgB,UAAUorB,QAC3D1K,EAAa1gB,UAAUorB,QAAU1K,EAAa1gB,UAAUmoB,WAAa,WACjE,OAAO5R,SAASnW,KAAKomB,WAAY,GACrC,EAmEA,IAAK,IAAIxhB,EAAI,EAAGA,EAAI,IAAKA,IACrB8a,EAAQ9a,GAAKmb,EAAWnb,GACpBA,EAAI,IAAG8a,GAAS9a,GAAKmb,GAAYnb,IAgBzC,OAdA8a,EAAQ8I,IAAM9I,EAAQ,GACtBA,EAAQ4I,KAAO5I,EAAQ,GACvBA,EAAQqM,SAAWrM,GAAS,GAC5BA,EAAQvV,IAAMA,EACduV,EAAQ2K,IAAMA,EACd3K,EAAQ4K,IAAMA,EACd5K,EAAQsM,IApPR,SAAa/K,EAAGC,GAGZ,OAFAD,EAAIlB,EAAWkB,GAAGwC,MAClBvC,EAAInB,EAAWmB,GAAGuC,MACXxC,EAAEoE,OAAOiF,EAAIrJ,EAAGC,IAAI0E,SAAS1E,EACxC,EAiPAxB,EAAQuM,WAAa,SAAU/lB,GAC3B,OAAOA,aAAa+Z,GAAc/Z,aAAama,GAAgBna,aAAaoa,CAChF,EACAZ,EAAQwI,YAlPR,SAAqBjH,EAAGC,GAGpB,IAAIgL,EAAM7B,EAFVpJ,EAAIlB,EAAWkB,GACfC,EAAInB,EAAWmB,IAEXlgB,EADwBmJ,EAAI8W,EAAGC,GAClBU,SAASsK,GAAKlL,IAAI,GACnC,GAAIhgB,EAAMof,QAAS,OAAO8L,EAAIlL,IAAI9W,KAAKgJ,MAAMhJ,KAAKiiB,SAAWnrB,IAG7D,IAFA,IAAI2pB,EAASG,EAAO9pB,EAAOke,GAAMgB,MAC7B2D,EAAS,GAAIuI,GAAa,EACrBxnB,EAAI,EAAGA,EAAI+lB,EAAO1hB,OAAQrE,IAAK,CACpC,IAAIynB,EAAMD,EAAazB,EAAO/lB,GAAKsa,EAC/BmM,EAAQvK,EAAS5W,KAAKiiB,SAAWE,GACrCxI,EAAO9d,KAAKslB,GACRA,EAAQgB,IAAKD,GAAa,EAClC,CACA,OAAOF,EAAIlL,IAAItB,EAAQ4M,UAAUzI,EAAQ3E,GAAM,GACnD,EAoOAQ,EAAQ4M,UAAY,SAAU3B,EAAQnJ,EAAMgF,GACxC,OAAOoE,EAAmBD,EAAOI,IAAIhL,GAAaA,EAAWyB,GAAQ,IAAKgF,EAC9E,EACO9G,CACX,CAjwCa,GAmwCS,mBAAX3gB,QAAyBA,OAAOC,KACvCD,OAAO,cAAe,IAAI,WACtB,OAAOkgB,CACX,IAGJ,QChuCA,SAASsN,EAAUzlB,GAEf,MAAM0lB,GADN1lB,EAASA,GAAU,CAAC,GACE0lB,OAChBllB,EAAQR,EAAOQ,MACfkN,EAAY1N,EAAO0N,UACnBiY,EAAgB3lB,EAAO2lB,cACvBC,EAAW5lB,EAAO4lB,SAGlBC,EAAmB,CAAC,MAAO,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QAE3EC,EAAO,CACT,KAAQ,OACR,KAAQ,YACR,KAAQ,QAENC,EAAgB,CAClB,KAAQ,KAENC,EAAyB,CAC3B,KAAO,EACP,MAAO,EACP,KAAO,EACP,KAAO,EACP,MAAO,EACP,KAAO,EACP,KAAO,EACP,MAAO,EACP,KAAO,EACP,KAAO,EACP,MAAO,GACP,IAAM,GACN,KAAM,IAEJC,EAAc,CAChB,MAAS,YACT,MAAS,YACT,KAAQ,mBAGZ,IAAI1oB,EACA0C,EACAimB,EAOJ,SAASC,EAAsBC,EAAMC,GACjC,MAAMjN,EAAQgN,EAAKE,aAAaD,GAChC,QAAKjN,GAG0B,SAAxBA,EAAMsK,aACjB,CAoBA,SAAS6C,EAAiBC,EAAazpB,GACnC,MAAM4Q,EAAgB,CAAC,EACjB8Y,EAAkB,GACxB,IAAIC,EACAC,EACAhtB,EACAmE,EACAzE,EAEJ,MAAM0E,EAAOyoB,EAAYF,aAAa,QAChCvsB,EAAOysB,EAAYF,aAAa,QAChC7U,EAAO+U,EAAYF,aAAa,YAChCM,EAAanV,EAAO1X,EAAO,IAAM0X,EAAO1X,EA+B9C,IA7BA4T,EAAcoE,GAAKhU,GAAQ6oB,EAC3BjZ,EAAckZ,YAAc9sB,EAC5B4T,EAAc8D,KAAOA,GAAQ,MAC7B9D,EAAcmZ,SAAWb,EAAYlsB,GACrC4T,EAAcoZ,QAAUP,EAAYF,aAAa,WACjD3Y,EAAcqZ,SAAWR,EAAYF,aAAa,YAClD3Y,EAAcsZ,UAAYT,EAAYF,aAAa,aAG/C3Y,EAAcoZ,UACVjB,EAAKnY,EAAcoZ,WACnBpZ,EAAcuZ,KAAO,CAAC,CAClBC,YAAa,0BACb/N,MAAO0M,EAAKnY,EAAcoZ,YAG9BhB,EAAcpY,EAAcoZ,WAC5BpZ,EAAcyZ,cAAgB,CAAC,CAC3BD,YAAa,0CACb/N,MAAO2M,EAAcpY,EAAcoZ,aAM/CL,EAiLJ,SAA4BF,EAAazpB,GACrC,MAAM2pB,EAAkB,CAAC,EACzB,IAAIW,EACAC,EACAttB,EAiBJ,OAfAA,EAAMwsB,EAAYF,aAAa,OAC/Be,EAAWrtB,EAAMA,EAAIgI,QAAQ,YAAa,eAAiB,KAC3DqlB,EAAWA,EAAWA,EAASrlB,QAAQ,eAAgB,UAAY,KAEnEslB,EAAuBd,EAAYF,aAAa,aAChDgB,EAAuBA,EAAuBnd,WAAWmd,GAAwBvqB,EAEjF2pB,EAAgB3kB,MAAQslB,EACxBX,EAAgB3pB,UAAYuqB,EAE5BZ,EAAgBplB,gBAQpB,SAA4BklB,EAAazpB,GACrC,MAAMwqB,EAAkB,CAAC,EACnBC,EAAShB,EAAYiB,qBAAqB,KAC1CrmB,EAAW,GACjB,IAAIK,EACAimB,EACAzlB,EACAnE,EAAGyd,EAAGf,EACN9gB,EAAW,EAEf,IAAKoE,EAAI,EAAGA,EAAI0pB,EAAOrlB,OAAQrE,IAoD3B,GAnDA2D,EAAU,CAAC,EAGXQ,EAAYulB,EAAO1pB,GAAGwoB,aAAa,KAI/BrkB,GAAa0W,EAAO1W,GAAWme,QAAQzH,EAAOyL,OAAOuD,qBACrDlmB,EAAQQ,UAAYA,GAExBR,EAAQC,EAAIyI,WAAWlI,GAGvBR,EAAQE,EAAIwI,WAAWqd,EAAO1pB,GAAGwoB,aAAa,MAGnC,IAANxoB,GAAa2D,EAAQC,IACtBD,EAAQC,EAAI,GAGZ5D,EAAI,IACJ4pB,EAActmB,EAASA,EAASe,OAAS,GAEpCulB,EAAY/lB,IACT+lB,EAAYzlB,UACZylB,EAAY/lB,EAAIgX,EAAO1W,GAAW6Y,SAASnC,EAAO+O,EAAYzlB,YAAYgf,aAE1EyG,EAAY/lB,EAAIF,EAAQC,EAAIgmB,EAAYhmB,EAE5ChI,GAAYguB,EAAY/lB,GAGvBF,EAAQC,IACLgmB,EAAYzlB,WACZR,EAAQQ,UAAY0W,EAAO+O,EAAYzlB,WAAWiY,IAAIvB,EAAO+O,EAAY/lB,IAAI2d,WAC7E7d,EAAQC,EAAIyI,WAAW1I,EAAQQ,YAE/BR,EAAQC,EAAIgmB,EAAYhmB,EAAIgmB,EAAY/lB,IAKhDF,EAAQE,IACRjI,GAAY+H,EAAQE,GAIxBP,EAASnC,KAAKwC,GAGd+Y,EAAIrQ,WAAWqd,EAAO1pB,GAAGwoB,aAAa,MAClC9L,EAEA,IAAKe,EAAI,EAAGA,EAAKf,EAAI,EAAIe,IACrBmM,EAActmB,EAASA,EAASe,OAAS,GACzCV,EAAU,CAAC,EACXA,EAAQC,EAAIgmB,EAAYhmB,EAAIgmB,EAAY/lB,EACxCF,EAAQE,EAAI+lB,EAAY/lB,EACpB+lB,EAAYzlB,YACZR,EAAQQ,UAAY0W,EAAO+O,EAAYzlB,WAAWiY,IAAIvB,EAAO+O,EAAY/lB,IAAI2d,YAEjF5lB,GAAY+H,EAAQE,EACpBP,EAASnC,KAAKwC,GAQ1B,OAHA8lB,EAAgBhmB,EAAIH,EACpBmmB,EAAgB7tB,SAAWA,EAAWqD,EAE/BwqB,CACX,CA1FsCK,CAAmBpB,EAAaE,EAAgB3pB,WAGlF2pB,EAAgBmB,uBAAyB,MAElCnB,CACX,CAvMsBoB,CAAmBtB,EAAazpB,GAElD4pB,EAAgBH,EAAYiB,qBAAqB,gBAE5C3pB,EAAI,EAAGA,EAAI6oB,EAAcxkB,OAAQrE,IAElC6oB,EAAc7oB,GAAGiqB,QAAUpa,EAAcoa,QACzCpB,EAAc7oB,GAAGgpB,SAAWnZ,EAAcmZ,SAG1CztB,EAAQstB,EAAc7oB,GAAGwoB,aAAa,SACtCK,EAAc7oB,GAAGkqB,GAAKra,EAAcoE,IAAiB,OAAV1Y,EAAmB,IAAMA,EAAS,IAG7EM,EAAiBsuB,EAAkBtB,EAAc7oB,GAAI0oB,GAE9B,OAAnB7sB,IAEAA,EAAe0H,gBAAkBqlB,EAEjCD,EAAgBxnB,KAAKtF,IAI7B,OAA+B,IAA3B8sB,EAAgBtkB,OACT,MAGXwL,EAAcua,eAAiBzB,EAG/B9Y,EAActM,gBAAkBqlB,EAEzB/Y,EACX,CAEA,SAASsa,EAAkBE,EAAc3B,GACrC,MAAM7sB,EAAiB,CAAC,EAClBI,EAAOysB,EAAYF,aAAa,QACtC,IAAI8B,EAAc,KACdjX,EAAQ,KACRC,EAAS,KAyBb,GAvBAzX,EAAeoY,GAAKoW,EAAaH,GACjCruB,EAAeuC,UAAYmT,SAAS8Y,EAAa7B,aAAa,WAAY,IAC1E3sB,EAAemtB,SAAWqB,EAAarB,SAEvC3V,EAAQ9B,SAAS8Y,EAAa7B,aAAa,YAAa,IACxDlV,EAAS/B,SAAS8Y,EAAa7B,aAAa,aAAc,IACrD+B,MAAMlX,KACPxX,EAAewX,MAAQA,GAEtBkX,MAAMjX,KACPzX,EAAeyX,OAASA,GAI5BgX,EAAcD,EAAa7B,aAAa,UAGpB,OAAhB8B,GAAwC,KAAhBA,IACxBA,EAAc5B,EAAYF,aAAa,WAKvB,OAAhB8B,GAAwC,KAAhBA,EACxB,GAAIruB,IAAS2T,EAAUoE,MACnBsW,EAAc,WACX,GAAIruB,IAAS2T,EAAUkE,MAE1B,OADA3R,EAAOO,MAAM,6GACN,KAKf,OAA6D,IAAzDqlB,EAAiB9hB,QAAQqkB,EAAYE,gBAErCroB,EAAOsoB,KAAK,wBAA0BH,GAC/B,OAIS,SAAhBA,GAA0C,SAAhBA,EAC1BzuB,EAAesZ,OAevB,SAAsBkV,GAClB,IACIK,EACAC,EAFAlU,EAAmB4T,EAAa7B,aAAa,oBAAoBhH,WAYrE,OAJAkJ,EAAY,iBAAiBE,KAAKnU,GAElCkU,EAASD,GAAaA,EAAU,GAAMjU,EAAiBoU,OAAOpU,EAAiBxQ,QAAQykB,EAAU,IAAM,GAAI,QAAMlpB,EAE1G,QAAUmpB,CACrB,CA7BgCG,CAAaT,GAC9BC,EAAYrkB,QAAQ,QAAU,GACrCpK,EAAesZ,OA6BvB,SAAqBkV,EAAcC,GAC/B,MAAMS,EAAexZ,SAAS8Y,EAAa7B,aAAa,gBAAiB,IACzE,IAEIwC,EACAC,EACAC,EACAC,EALA1U,EAAmB4T,EAAa7B,aAAa,oBAAoBhH,WACjE4J,EAAa,EAwDjB,MAhDoB,SAAhBd,IACAc,EAAa,QAGQ5pB,IAArBiV,GAAuD,KAArBA,GAClC2U,EAAa,EACbF,EAAYhD,EAAuB6C,GACf,SAAhBT,GAGAc,EAAa,EACb3U,EAAmB,IAAInF,WAAW,GAClC6Z,EAAkCjD,EAAsC,EAAf6C,GAGzDtU,EAAiB,GAAM2U,GAAc,EAAMF,GAAa,EACxDzU,EAAiB,GAAMyU,GAAa,EAAMb,EAAagB,UAAY,EAAMF,GAAmC,EAC5G1U,EAAiB,GAAM0U,GAAmC,EAAM,EAChE1U,EAAiB,GAAK,EAEtBwU,EAAQ,IAAIK,YAAY,GACxBL,EAAM,IAAMxU,EAAiB,IAAM,GAAKA,EAAiB,GACzDwU,EAAM,IAAMxU,EAAiB,IAAM,GAAKA,EAAiB,GAEzDuU,EAAsBC,EAAM,GAAGzJ,SAAS,IACxCwJ,EAAsBC,EAAM,GAAGzJ,SAAS,IAAMyJ,EAAM,GAAGzJ,SAAS,MAKhE/K,EAAmB,IAAInF,WAAW,GAElCmF,EAAiB,GAAM2U,GAAc,EAAMF,GAAa,EACxDzU,EAAiB,GAAMyU,GAAa,EAAM3Z,SAAS8Y,EAAa7B,aAAa,YAAa,KAAO,EAEjGyC,EAAQ,IAAIK,YAAY,GACxBL,EAAM,IAAMxU,EAAiB,IAAM,GAAKA,EAAiB,GAEzDuU,EAAsBC,EAAM,GAAGzJ,SAAS,KAG5C/K,EAAmB,GAAKuU,EACxBvU,EAAmBA,EAAiB+T,cACpCH,EAAakB,aAAa,mBAAoB9U,IACxB,IAAf2U,IACPA,GAA4D,IAA9C7Z,SAASkF,EAAiBoU,OAAO,EAAG,GAAI,MAAe,GAGlE,WAAaO,CACxB,CAzFgCI,CAAYnB,EAAcC,GAClDzuB,EAAeyb,kBAAoB/F,SAAS8Y,EAAa7B,aAAa,gBAAiB,IACvF3sB,EAAeqb,cAAgB3F,SAAS8Y,EAAa7B,aAAa,YAAa,MACxE8B,EAAYrkB,QAAQ,SAAWqkB,EAAYrkB,QAAQ,WAC1DpK,EAAesZ,OAASvF,EAAU6b,MAGtC5vB,EAAe4a,iBAAmB,GAAK4T,EAAa7B,aAAa,oBACjE3sB,EAAeouB,QAAUI,EAAaJ,QAE/BpuB,EACX,CA6QA,SAAS6vB,EAAUjwB,EAAOkwB,EAAMC,GAC5B,MAAMC,EAAOpwB,EAAMkwB,GACnBlwB,EAAMkwB,GAAQlwB,EAAMmwB,GACpBnwB,EAAMmwB,GAAQC,CAClB,CAkEA,SAASC,EAAgBC,GACrB,MAAM5oB,EAAW,CAAC,EACZ6oB,EAAqB,GACrBC,EAAuBF,EAAOpC,qBAAqB,wBAAwB,GAC3EuC,EAAaH,EAAOpC,qBAAqB,cAAc,GAC7D,IACI1mB,EACAkpB,EACArc,EACAsc,EACAC,EACArtB,EACAsE,EACArE,EACAqtB,EACAtsB,EAAGyd,EAVH8O,EAAmB,KAavBppB,EAASqpB,SAAW,MACpBrpB,EAASspB,SAAW,wCACpBtpB,EAASlH,KAAOosB,EAAsB4D,EAAsB,UAAY,UAAY,SACpFhtB,EAAYgtB,EAAqBzD,aAAa,aAC9CrlB,EAASlE,UAAYA,EAAYoN,WAAWpN,GApjBrB,IAqjBvB,IAAIytB,EAAkBrgB,WAAW4f,EAAqBzD,aAAa,oBAE7C,YAAlBrlB,EAASlH,MAA2C,IAApBywB,IAAyBnC,MAAMmC,KAC/DA,EAAkBpa,KAGE,IAApBoa,GAAyBrE,EAAsB4D,EAAsB,aACrES,EAAkBpa,KAGlBoa,EAAkB,IAClBvpB,EAAS2I,qBAAuB4gB,EAAkBvpB,EAASlE,WAG/D,IAAIrD,EAAWyQ,WAAW4f,EAAqBzD,aAAa,aA6D5D,IA5DArlB,EAASwpB,0BAA0C,IAAb/wB,EAAkB0W,IAAW1W,EAAWuH,EAASlE,UAEvFkE,EAASypB,cAAgB,EACzBzpB,EAAS0pB,oBAAqB,EAGR,YAAlB1pB,EAASlH,MAAsBL,EAAW,IAC1CuH,EAASlH,KAAO,SAEhBkH,EAAS2I,qBAAuBlQ,EAAWuH,EAASlE,WAIlC,YAAlBkE,EAASlH,OACTkH,EAAS2pB,8BAA+B,EACxC3pB,EAAS4pB,qCAAsC,EAC/C5pB,EAAS6pB,0BAA2B,EACpC7pB,EAAShF,sBAAwB,IAAIiH,KAAK,OAI9CnC,EAxiBJ,SAAmBgpB,EAAsBhtB,GACrC,MAAMgE,EAAS,CAAC,EAChB,IAAIgqB,EACAjqB,EAGJC,EAAOI,cAAgB,GACvB4pB,EAAUhB,EAAqBtC,qBAAqB,eACpD,IAAK,IAAI3pB,EAAI,EAAGA,EAAIitB,EAAQ5oB,OAAQrE,IAChCgD,EAAaylB,EAAiBwE,EAAQjtB,GAAIf,GACvB,OAAf+D,GACAC,EAAOI,cAAclC,KAAK6B,GAIlC,OAAOC,CACX,CAwhBaiqB,CAAUjB,EAAsB9oB,EAASlE,WAClDkE,EAASC,OAAS,CAACH,GAGnBA,EAAOvH,MAAQ,OAUI8F,IAAf0qB,IACAK,EAAmBR,EAAOpC,qBAAqB,oBAAoB,GAInE4C,EAAiBY,WAAWtnB,KAAO0mB,EAAiBY,WAAWtnB,KAAK3B,QAAQ,SAAU,IAGtFkoB,EAxOR,SAAoCG,GAChC,IAAIa,EACAC,EACAC,EACAlB,EAsER,IAA+BmB,EA5C3B,OAvBAH,EAAWxF,EAAO4F,YAAYjB,EAAiBY,WAAWtnB,MAG1DwnB,EAuBJ,SAAkCD,GAC9B,IAAI/oB,EACAopB,EACAC,EACAC,EACAC,EACA5tB,EAAI,EAaR,IARAqE,GAAU+oB,EAASptB,EAAI,IAAM,KAAOotB,EAASptB,EAAI,IAAM,KAAOotB,EAASptB,EAAI,IAAM,GAAKotB,EAASptB,GAC/FA,GAAK,EAGLytB,GAAeL,EAASptB,EAAI,IAAM,GAAKotB,EAASptB,GAChDA,GAAK,EAGEA,EAAIotB,EAAS/oB,QAMhB,GAJAqpB,GAAcN,EAASptB,EAAI,IAAM,GAAKotB,EAASptB,GAC/CA,GAAK,EAGc,IAAf0tB,EASA,OANAC,GAAgBP,EAASptB,EAAI,IAAM,GAAKotB,EAASptB,GACjDA,GAAK,EAGL4tB,EAAc,IAAItc,WAAWqc,GAC7BC,EAAYhX,IAAIwW,EAASS,SAAS7tB,EAAGA,EAAI2tB,IAClCC,EAIf,OAAO,IACX,CA9DgBE,CAAyBV,GAEjCC,IAEAA,EAAY,IAAI/B,YAAY+B,EAAUU,QAGtCV,EAAYxI,OAAOmJ,aAAantB,MAAM,KAAMwsB,GAG5CC,GAAa,IAAIW,WAAaC,gBAAgBb,EAAW,mBACzDjB,EAAMkB,EAAUa,cAAc,OAAOC,YAGrChC,EAAMxE,EAAO4F,YAAYpB,GAmD7BV,EAD2B6B,EA/CDnB,EAgDV,EAAG,GACnBV,EAAU6B,EAAM,EAAG,GACnB7B,EAAU6B,EAAM,EAAG,GACnB7B,EAAU6B,EAAM,EAAG,IAhDZnB,CACX,CAyMciC,CAA2B9B,GAGjCzc,EAnJR,SAAmCyc,GAK/B,MAAO,CACHlD,YAAa,gDACb/N,MCjfkB,0BDkflBgT,IAPM,CACNC,OAAQhC,EAAiBY,WAAWtnB,KACpC2oB,SAAU,QAOlB,CAyI4BC,CAA0BlC,GAC9Czc,EAAkB,oBAAsBsc,EACxCJ,EAAmB7qB,KAAK2O,GAGxBA,EA5IR,SAAyCsc,GACrC,IAAIsC,EAAa,CACbrF,YAAa,gDACb/N,MC1fiB,sBD4frB,IAAK8Q,EACD,OAAOsC,EAGX,MAAMC,EAAe,IAAIrd,WAAW,EAAI8a,EAAI/nB,QAC5CsqB,EAAa,GAAK,GAClBA,EAAa,GAAK,GAClBA,EAAa/X,IAAIwV,EAAK,GAGtB,MAAM/nB,EAAS,GAAyFsqB,EAAatqB,OACrH,IAAIiU,EAAO,IAAIhH,WAAWjN,GACtBrE,EAAI,EA+BR,OA5BAsY,EAAKtY,MAAiB,WAATqE,IAAwB,GACrCiU,EAAKtY,MAAiB,SAATqE,IAAwB,GACrCiU,EAAKtY,MAAiB,MAATqE,IAAwB,EACrCiU,EAAKtY,KAAiB,IAATqE,EAGbiU,EAAK1B,IAAI,CAAC,IAAM,IAAM,IAAM,IAAM,EAAM,EAAM,EAAM,GAAO5W,GAC3DA,GAAK,EAGLsY,EAAK1B,IAAI,CAAC,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,GAAM,IAAM,IAAM,IAAM,GAAM,IAAM,IAAM,GAAM,GAAM,KAAO5W,GAC3GA,GAAK,GAGLsY,EAAKtY,MAA8B,WAAtB2uB,EAAatqB,SAAwB,GAClDiU,EAAKtY,MAA8B,SAAtB2uB,EAAatqB,SAAwB,GAClDiU,EAAKtY,MAA8B,MAAtB2uB,EAAatqB,SAAwB,EAClDiU,EAAKtY,KAA8B,IAAtB2uB,EAAatqB,OAG1BiU,EAAK1B,IAAI+X,EAAc3uB,GAGvBsY,EAAOuM,OAAOmJ,aAAantB,MAAM,KAAMyX,GACvCA,EAAOsP,EAAOgH,YAAYtW,GAE1BoW,EAAWpW,KAAO,CAAEiW,OAAQjW,GAErBoW,CACX,CA2F4BG,CAAgCzC,GACpDtc,EAAkB,oBAAsBsc,EACxCJ,EAAmB7qB,KAAK2O,GAExB3M,EAASwO,kBAAoBqa,GAGjCG,EAAclpB,EAAOI,cAEhBrD,EAAI,EAAGA,EAAImsB,EAAY9nB,OAAQrE,GAAK,EACrCmsB,EAAYnsB,GAAGuD,gBAAgBurB,eAAiB,mBAEbttB,IAA/B2B,EAASwO,oBACTwa,EAAYnsB,GAAG2R,kBAAoBxO,EAASwO,kBAC5Cwa,EAAYnsB,GAAG2R,kBAAoBxO,EAASwO,mBAGb,UAA/Bwa,EAAYnsB,GAAG+oB,cAEfuD,EAAkBH,EAAYnsB,GAAGuD,gBAAgBC,gBAAgBC,EAAE,GAAGI,EAAIsoB,EAAYnsB,GAAGuD,gBAAgBtE,UAEzGkE,EAASypB,cAAgBN,EAEH,YAAlBnpB,EAASlH,MAELkH,EAAS2I,qBAAuB,GAChC3I,EAAS2I,uBAAyBwG,KAClCnP,EAAS2I,qBAAuBqgB,EAAYnsB,GAAGuD,gBAAgBC,gBAAgB5H,WAC/EuH,EAAS2I,qBAAuBqgB,EAAYnsB,GAAGuD,gBAAgBC,gBAAgB5H,WAa/F,GANAuH,EAASypB,cAAgBtnB,KAAKmgB,IAAItiB,EAASypB,cAAgBzpB,EAAS2I,qBAAuB3I,EAAS2I,qBAAuBwG,KAMrG,YAAlBnP,EAASlH,KAAoB,CAC7B,IAAI8yB,EAAkBjH,EAASjtB,MAAMm0B,UAAU7pB,MAAM8pB,UAChDF,IAEDA,EAAkBzC,GADuE,OAA1DxE,EAASjtB,MAAMm0B,UAAU7pB,MAAM+pB,wBAAoC3E,MAAMzC,EAASjtB,MAAMm0B,UAAU7pB,MAAM+pB,wBAAkF,EAAxDpH,EAASjtB,MAAMm0B,UAAU7pB,MAAM+pB,yBAGpM,IAAIC,EAAqB7pB,KAAKC,IAAIpC,EAAS2I,qBAAuB,GAA+B3I,EAAS2I,qBAAuB,GAC7HmjB,EAAY3pB,KAAKmgB,IAAI0J,EAAoBJ,GAEzCK,EAAaH,EAA+B,IAAlB3C,EAG9BlE,EAAwB,CACpB,UAAa,CACT,OAAU,CACN,kBAAqBN,EAASjtB,MAAMm0B,UAAUjB,OAAOsB,kBACrD,uBAA0BvH,EAASjtB,MAAMm0B,UAAUjB,OAAOuB,uBAC1D,+BAAkCxH,EAASjtB,MAAMm0B,UAAUjB,OAAOwB,gCAEtE,gBAAmB,CACfC,wBAAyB1H,EAASjtB,MAAMm0B,UAAUS,gBAAgBD,yBAEtE,MAAS,CACL,UAAa1H,EAASjtB,MAAMm0B,UAAU7pB,MAAM8pB,aAKxDnH,EAAS4H,OAAO,CACZ,UAAa,CACT,OAAU,CACN,kBAAqBN,EACrB,uBAA0BA,EAC1B,+BAAkCA,GAEtC,gBAAmB,CACfI,yBAAyB,GAE7B,MAAS,CACL,UAAaP,KAI7B,CAQA,UALO9rB,EAASwO,kBAKM,WAAlBxO,EAASlH,KAAmB,CAG5B,IAAI0zB,EAAe9H,EAAc+H,WACjC,GAAID,GAAgBA,EAAatD,gBAC7BA,EAAkBsD,EAAatD,qBAE/B,IAAKrsB,EAAI,EAAGA,EAAImsB,EAAY9nB,OAAQrE,IAC5BmsB,EAAYnsB,GAAG+oB,cAAgBnZ,EAAUoE,OAASmY,EAAYnsB,GAAG+oB,cAAgBnZ,EAAUkE,QAC3FxQ,EAAW6oB,EAAYnsB,GAAGuD,gBAAgBC,gBAAgBC,EAC1DzE,EAAYsE,EAAS,GAAGM,OACApC,IAApB6qB,IACAA,EAAkBrtB,GAEtBqtB,EAAkB/mB,KAAKmgB,IAAI4G,EAAiBrtB,GAG5CmE,EAASwpB,0BAA4BrnB,KAAKmgB,IAAItiB,EAASwpB,0BAA2BR,EAAYnsB,GAAGuD,gBAAgBC,gBAAgB5H,WAI7I,GAAIywB,EAAkB,EAAG,CAGrB,IADAlpB,EAASkpB,gBAAkBA,EACtBrsB,EAAI,EAAGA,EAAImsB,EAAY9nB,OAAQrE,IAAK,CAErC,IADAsD,EAAW6oB,EAAYnsB,GAAGuD,gBAAgBC,gBAAgBC,EACrDga,EAAI,EAAGA,EAAIna,EAASe,OAAQoZ,IACxBna,EAASma,GAAGtZ,YACbb,EAASma,GAAGtZ,UAAYb,EAASma,GAAG7Z,EAAE4d,YAE1Cle,EAASma,GAAG7Z,GAAKyoB,EAEjBF,EAAYnsB,GAAG+oB,cAAgBnZ,EAAUoE,OAASmY,EAAYnsB,GAAG+oB,cAAgBnZ,EAAUkE,QAC3F7Q,EAAOvH,MAAQ4J,KAAKC,IAAIjC,EAAS,GAAGM,EAAGX,EAAOvH,OAC9CywB,EAAYnsB,GAAGuD,gBAAgBssB,uBAAyB5sB,EAAOvH,MAEvE,CACAuH,EAAOvH,OAASyH,EAASlE,SAC7B,CACJ,CAOA,OAHAkE,EAASwpB,0BAA4BrnB,KAAKgJ,MAA2C,IAArCnL,EAASwpB,2BAAoC,IAC7F1pB,EAAOrH,SAAWuH,EAASwpB,0BAEpBxpB,CACX,CA6DA,OARA1D,EAAW,CACPqwB,MAjCJ,SAAuBjqB,GACnB,IAAIkmB,EAAS,KACT5oB,EAAW,KAEf,MAAMnE,EAAY+wB,OAAOC,YAAYC,MAGrClE,EA1BJ,SAAkBlmB,GACd,IAAIkmB,EAAS,KAEb,GAAIgE,OAAO9B,YAGPlC,GAFe,IAAIgE,OAAO9B,WAEVC,gBAAgBroB,EAAM,YAClCkmB,EAAOpC,qBAAqB,eAAetlB,OAAS,GACpD,MAAM,IAAIqL,MAAM,+BAIxB,OAAOqc,CACX,CAaamE,CAASrqB,GAElB,MAAMsqB,EAAeJ,OAAOC,YAAYC,MAExC,GAAe,OAAXlE,EACA,OAAO,KAIX5oB,EAAW2oB,EAAgBC,EAAQ,IAAI3mB,MAEvC,MAAMgrB,EAAeL,OAAOC,YAAYC,MAIxC,OAFA9tB,EAAO9C,KAAK,mCAAqC8wB,EAAenxB,GAAWqxB,YAAY,GAAK,kBAAoBD,EAAeD,GAAcE,YAAY,GAAK,gBAAkBD,EAAepxB,GAAa,KAAMqxB,YAAY,GAAK,MAE5NltB,CACX,EAWImtB,QAtCJ,WACI,OAAO,IACX,EAqCI7qB,MAVJ,WAEQ2iB,GACAN,EAAS4H,OAAOtH,EAExB,GA/wBIjmB,EAASO,EAAMgD,UAAUjG,GAyxBtBA,CACX,CAEAkoB,EAAUlnB,sBAAwB,YAClC,MAAejB,EAAaqC,gBAAgB8lB,GE50B5C,SAAS4I,EAAWruB,GAEhBA,EAASA,GAAU,CAAC,EACpB,MAAMpC,EAAU1E,KAAK0E,QACf2L,EAAWvJ,EAAOuJ,SAClBjF,EAAStE,EAAOsE,OAChBoJ,EAAY1N,EAAO0N,UACnB4gB,EAAkBtuB,EAAOsuB,gBACzBllB,EAAqBpJ,EAAOoJ,mBAC5BmlB,EAAmBvuB,EAAOuuB,iBAChC,IAAIC,EACAC,EACAC,EACAnxB,EAUJ,SAASoxB,EAAmB50B,GACxB,OAAOw0B,EAAiBK,4BAA4BzvB,QAAO0vB,GAChDA,EAAUpsB,YAAc1I,IAChC,EACP,CAEA,SAAS+0B,EAA0B/0B,GAC/B,OAAO20B,EAAwBvvB,QAAO4vB,GAC1BA,EAAWtsB,YAAc1I,IAClC,EACP,CAkBA,SAASi1B,IAGYT,EAAiBK,4BACvBK,SAAQ,SAAUJ,GACzB,GAAIA,EAAUpsB,YAAciL,EAAUkE,OAClCid,EAAUpsB,YAAciL,EAAUoE,OAClC+c,EAAUpsB,YAAciL,EAAUwhB,KAAM,CAExC,IAAIC,EAAyBL,EAA0BD,EAAUpsB,WAC5D0sB,IACDA,EAAyBpvB,EAA0BnC,GAASgC,OAAO,CAC/DU,gBAAiBuuB,EACjBtuB,kBAAmBP,EAAOO,kBAC1BC,MAAOR,EAAOQ,QAElB2uB,EAAuB3sB,aACvBksB,EAAwBzvB,KAAKkwB,IAEjCA,EAAuB31B,OAC3B,CACJ,GACJ,CASA,SAAS41B,EAAqBxsB,GAC1B,IAAItC,EAAkBquB,EAAmB/rB,EAAEnG,WAC3C,IAAK6D,EACD,OAIJ,IACI3G,EAD2B2G,EAAgBM,8BACDC,2BAC1CkL,EAAYzL,EAAgB+uB,eAE5B7tB,EAAU,IAAI3F,EAClB2F,EAAQ/E,UAAY9C,EAAemH,WAAW/G,KAC9CyH,EAAQzH,KAAOu0B,EACf9sB,EAAQtH,MAAQP,EAAeO,MAC/BsH,EAAQtF,UAAYvC,EAAeuC,UACnCsF,EAAQ7H,eAAiBA,EAEzB,MAAM21B,EAjEV,SAAyB9tB,EAASrI,EAAUS,GACxC,MAAM01B,EAAQ,IAAIC,EAYlB,OAVAD,EAAMn2B,SAAWA,EACjBm2B,EAAMl2B,YAAcoI,EAAQzH,KAC5Bu1B,EAAM91B,MAAQgI,EAAQ1E,UACtBwyB,EAAM51B,SAAW8H,EAAQ9H,SACzB41B,EAAM71B,IAAM61B,EAAM91B,MAAQ81B,EAAM51B,SAChC41B,EAAMj2B,MAAQmI,EAAQnI,MACtBi2B,EAAMpzB,UAAYsF,EAAQtF,UAC1BozB,EAAM31B,eAAiB6H,EAAQ7H,eAC/B21B,EAAM11B,YAAcA,EAEb01B,CACX,CAmDkBE,CAAgBhuB,EAASuK,EAAU0jB,WAAW1d,GAAInP,EAAE7I,OAASuK,EAAOe,2BAElF,IAEIiqB,EAAM/1B,MAAQk1B,EAAqBnf,aAAa3V,GAGhD4P,EAASsB,QAAQvG,EAAOorB,qBACpB,CAAEJ,MAAOA,GACT,CAAEn2B,SAAU4S,EAAU0jB,WAAW1d,GAAItV,UAAW9C,EAAemH,WAAW/G,MAElF,CAAE,MAAO6I,GACL5C,EAAOsJ,WAAWxG,MAAM,IAAI+G,EAAYjH,EAAEa,KAAMb,EAAEc,QAASd,EAAEe,MACjE,CAGAf,EAAEmI,OAAS,IACf,CAEA,SAAS4kB,EAAqB/sB,GAC1B,GAAIA,EAAEE,MACF,OAGJ,IAAIxC,EAAkBquB,EAAmB/rB,EAAEpB,QAAQ/E,WACnD,IAAK6D,EACD,OAMJ,GAFAmuB,EAAqBxW,gBAAgBrV,EAAGtC,GAEpCsC,EAAEpB,QAAQzH,OAASF,EAAY2B,+BAAgC,CAE/D,IAAI2zB,EAAyBL,EAA0BlsB,EAAEpB,QAAQ/E,WAC7D0yB,GACAA,EAAuBxsB,mBAAmBC,EAElD,CAGA,IAAI4H,EAAe5H,EAAEpB,QAAQ7H,eAAeoS,UAAU0jB,WAAWjlB,aAC5DA,EAAaolB,WAAaplB,EAAaqlB,gBAAkBzf,KAC1D4e,GAER,CAEA,SAASc,IACD1mB,EAAmB2mB,gBAAmD,IAAjC3mB,EAAmBjG,WACxD6rB,GAER,CAEA,SAASgB,IACD5mB,EAAmB2mB,gBAAmD,IAAjC3mB,EAAmBjG,WACxD6rB,GAER,CAEA,SAASiB,EAAiBC,GACjBA,GAAkBA,EAAcvsB,OAIrCusB,EAAcvsB,KAAOusB,EAAcvsB,KAAK3B,QAAQ,yCAA0C,6BAC9F,CAwCA,OATAzE,EAAW,CACPgG,MAtBJ,WACQirB,IACAA,EAAUjrB,QACVirB,OAAYlvB,GAGhBiK,EAAS4mB,IAAI7rB,EAAO8rB,qBAAsBhB,EAAsBl2B,MAChEqQ,EAAS4mB,IAAI7rB,EAAOuD,gBAAiBioB,EAAkB52B,MACvDqQ,EAAS4mB,IAAI7rB,EAAO4D,iBAAkB8nB,EAAmB92B,MACzDqQ,EAAS4mB,IAAI7rB,EAAOc,2BAA4BuqB,EAAsBz2B,MACtEqQ,EAAS4mB,IAAI7rB,EAAO4C,cAAe+oB,EAAkB/2B,MA7GrDw1B,EAAwBO,SAAQpT,IAC5BA,EAAEtY,OAAO,IAEbmrB,EAA0B,EA8G9B,EASI2B,gBAPJ,WAEI,OADA7B,EAAY/I,EAAU7nB,GAASgC,OAAOI,GAC/BwuB,CACX,EAKI8B,2BApLJ,WACI7B,EAAuB3W,EAAqBla,GAASgC,OAAOI,EAChE,EAmLIuwB,eAjCJ,WACIhnB,EAASinB,GAAGlsB,EAAO8rB,qBAAsBhB,EAAsB7xB,EAAU,CAAEkzB,SAAUC,OAAOpzB,aAAamC,0BAA0B8J,EAAS1K,gBAAgB8xB,sBAC5JpnB,EAASinB,GAAGlsB,EAAOuD,gBAAiBioB,EAAkBvyB,EAAU,CAAEkzB,SAAUC,OAAOpzB,aAAamC,0BAA0B8J,EAAS1K,gBAAgB8xB,sBACnJpnB,EAASinB,GAAGlsB,EAAO4D,iBAAkB8nB,EAAmBzyB,EAAU,CAAEkzB,SAAUC,OAAOpzB,aAAamC,0BAA0B8J,EAAS1K,gBAAgB8xB,sBACrJpnB,EAASinB,GAAGlsB,EAAOc,2BAA4BuqB,EAAsBpyB,EAAU,CAAEkzB,SAAUC,OAAOpzB,aAAamC,0BAA0B8J,EAAS1K,gBAAgB8xB,sBAClKpnB,EAASinB,GAAGlsB,EAAO4C,cAAe+oB,EAAkB1yB,EACxD,GA7JImxB,EAA0B,GA6LvBnxB,CACX,CAEA8wB,EAAW9vB,sBAAwB,aACnC,MAAMzG,EAAU44B,OAAOpzB,aAAaqC,gBAAgB0uB,GACpDv2B,EAAQ8L,OAASkG,EACjB4mB,OAAOpzB,aAAawC,mBAAmBuuB,EAAW9vB,sBAAuBzG,GACzE,QC1NI8F,EAA6B,oBAAXiwB,QAA0BA,QAAW+C,OAEvDF,EAAS9yB,EAAQ8yB,OAChBA,IACDA,EAAS9yB,EAAQ8yB,OAAS,CAAC,GAG/BA,EAAOrC,WAAaA,EAEpB,Q", "sources": ["webpack://dashjs/webpack/universalModuleDefinition", "webpack://dashjs/webpack/bootstrap", "webpack://dashjs/webpack/runtime/define property getters", "webpack://dashjs/webpack/runtime/hasOwnProperty shorthand", "webpack://dashjs/./src/streaming/vo/DataChunk.js", "webpack://dashjs/./src/streaming/vo/metrics/HTTPRequest.js", "webpack://dashjs/./src/streaming/vo/FragmentRequest.js", "webpack://dashjs/./src/core/FactoryMaker.js", "webpack://dashjs/./src/mss/MssFragmentInfoController.js", "webpack://dashjs/./src/streaming/vo/DashJSError.js", "webpack://dashjs/./src/core/errors/ErrorsBase.js", "webpack://dashjs/./src/mss/errors/MssErrors.js", "webpack://dashjs/./src/core/events/EventsBase.js", "webpack://dashjs/./src/streaming/MediaPlayerEvents.js", "webpack://dashjs/./src/mss/MssFragmentMoofProcessor.js", "webpack://dashjs/./src/mss/MssFragmentMoovProcessor.js", "webpack://dashjs/./src/mss/MssFragmentProcessor.js", "webpack://dashjs/./externals/BigInteger.js", "webpack://dashjs/./src/mss/parser/MssParser.js", "webpack://dashjs/./src/streaming/constants/ProtectionConstants.js", "webpack://dashjs/./src/mss/MssHandler.js", "webpack://dashjs/./src/mss/index.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"dashjs\"] = factory();\n\telse\n\t\troot[\"dashjs\"] = factory();\n})(self, function() {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * @class\n * @ignore\n */\nclass DataChunk {\n    //Represents a data structure that keep all the necessary info about a single init/media segment\n    constructor() {\n        this.streamId = null;\n        this.segmentType = null;\n        this.index = NaN;\n        this.bytes = null;\n        this.start = NaN;\n        this.end = NaN;\n        this.duration = NaN;\n        this.representation = null;\n        this.endFragment = null;\n    }\n}\n\nexport default DataChunk;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @classdesc This Object holds reference to the HTTPRequest for manifest, fragment and xlink loading.\n * Members which are not defined in ISO23009-1 Annex D should be prefixed by a _ so that they are ignored\n * by Metrics Reporting code.\n * @ignore\n */\nclass HTTPRequest {\n    /**\n     * @class\n     */\n    constructor() {\n        /**\n         * Identifier of the TCP connection on which the HTTP request was sent.\n         * @public\n         */\n        this.tcpid = null;\n        /**\n         * This is an optional parameter and should not be included in HTTP request/response transactions for progressive download.\n         * The type of the request:\n         * - MPD\n         * - XLink expansion\n         * - Initialization Fragment\n         * - Index Fragment\n         * - Media Fragment\n         * - Bitstream Switching Fragment\n         * - other\n         * @public\n         */\n        this.type = null;\n        /**\n         * The original URL (before any redirects or failures)\n         * @public\n         */\n        this.url = null;\n        /**\n         * The actual URL requested, if different from above\n         * @public\n         */\n        this.actualurl = null;\n        /**\n         * The contents of the byte-range-spec part of the HTTP Range header.\n         * @public\n         */\n        this.range = null;\n        /**\n         * Real-Time | The real time at which the request was sent.\n         * @public\n         */\n        this.trequest = null;\n        /**\n         * Real-Time | The real time at which the first byte of the response was received.\n         * @public\n         */\n        this.tresponse = null;\n        /**\n         * The HTTP response code.\n         * @public\n         */\n        this.responsecode = null;\n        /**\n         * The duration of the throughput trace intervals (ms), for successful requests only.\n         * @public\n         */\n        this.interval = null;\n        /**\n         * Throughput traces, for successful requests only.\n         * @public\n         */\n        this.trace = [];\n        /**\n         * The CMSD static and dynamic values retrieved from CMSD response headers.\n         * @public\n         */\n        this.cmsd = null;\n\n        /**\n         * Type of stream (\"audio\" | \"video\" etc..)\n         * @public\n         */\n        this._stream = null;\n        /**\n         * Real-Time | The real time at which the request finished.\n         * @public\n         */\n        this._tfinish = null;\n        /**\n         * The duration of the media requests, if available, in seconds.\n         * @public\n         */\n        this._mediaduration = null;\n        /**\n         * all the response headers from request.\n         * @public\n         */\n        this._responseHeaders = null;\n        /**\n         * The selected service location for the request. string.\n         * @public\n         */\n        this._serviceLocation = null;\n        /**\n         * The type of the loader that was used. Distinguish between fetch loader and xhr loader\n         */\n        this._fileLoaderType = null;\n        /**\n         * The values derived from the ResourceTimingAPI.\n         */\n        this._resourceTimingValues = null;\n    }\n}\n\n/**\n * @classdesc This Object holds reference to the progress of the HTTPRequest.\n * @ignore\n */\nclass HTTPRequestTrace {\n    /**\n     * @class\n     */\n    constructor() {\n        /**\n         * Real-Time | Measurement stream start.\n         * @public\n         */\n        this.s = null;\n        /**\n         * Measurement stream duration (ms).\n         * @public\n         */\n        this.d = null;\n        /**\n         * List of integers counting the bytes received in each trace interval within the measurement stream.\n         * @public\n         */\n        this.b = [];\n    }\n}\n\nHTTPRequest.GET = 'GET';\nHTTPRequest.HEAD = 'HEAD';\nHTTPRequest.MPD_TYPE = 'MPD';\nHTTPRequest.XLINK_EXPANSION_TYPE = 'XLinkExpansion';\nHTTPRequest.INIT_SEGMENT_TYPE = 'InitializationSegment';\nHTTPRequest.INDEX_SEGMENT_TYPE = 'IndexSegment';\nHTTPRequest.MEDIA_SEGMENT_TYPE = 'MediaSegment';\nHTTPRequest.BITSTREAM_SWITCHING_SEGMENT_TYPE = 'BitstreamSwitchingSegment';\nHTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE = 'FragmentInfoSegment';\nHTTPRequest.DVB_REPORTING_TYPE = 'DVBReporting';\nHTTPRequest.LICENSE = 'license';\nHTTPRequest.CONTENT_STEERING_TYPE = 'ContentSteering';\nHTTPRequest.OTHER_TYPE = 'other';\n\nexport {HTTPRequest, HTTPRequestTrace};\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport {HTTPRequest} from './metrics/HTTPRequest.js';\n\n/**\n * @class\n * @ignore\n */\nclass FragmentRequest {\n    constructor(url) {\n        this.action = FragmentRequest.ACTION_DOWNLOAD;\n        this.availabilityEndTime = null;\n        this.availabilityStartTime = null;\n        this.bandwidth = NaN;\n        this.bytesLoaded = NaN;\n        this.bytesTotal = NaN;\n        this.delayLoadingTime = NaN;\n        this.duration = NaN;\n        this.endDate = null;\n        this.firstByteDate = null;\n        this.index = NaN;\n        this.mediaStartTime = NaN;\n        this.mediaType = null;\n        this.range = null;\n        this.representation = null;\n        this.responseType = 'arraybuffer';\n        this.retryAttempts = 0;\n        this.serviceLocation = null;\n        this.startDate = null;\n        this.startTime = NaN;\n        this.timescale = NaN;\n        this.type = null;\n        this.url = url || null;\n        this.wallStartTime = null;\n    }\n\n    isInitializationRequest() {\n        return (this.type && this.type === HTTPRequest.INIT_SEGMENT_TYPE);\n    }\n\n    setInfo(info) {\n        this.type = info && info.init ? HTTPRequest.INIT_SEGMENT_TYPE : HTTPRequest.MEDIA_SEGMENT_TYPE;\n        this.url = info && info.url ? info.url : null;\n        this.range = info && info.range ? info.range.start + '-' + info.range.end : null;\n        this.mediaType = info && info.mediaType ? info.mediaType : null;\n        this.representation = info && info.representation ? info.representation : null;\n    }\n}\n\nFragmentRequest.ACTION_DOWNLOAD = 'download';\nFragmentRequest.ACTION_COMPLETE = 'complete';\n\nexport default FragmentRequest;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @module FactoryMaker\n * @ignore\n */\nconst FactoryMaker = (function () {\n\n    let instance;\n    let singletonContexts = [];\n    const singletonFactories = {};\n    const classFactories = {};\n\n    function extend(name, childInstance, override, context) {\n        if (!context[name] && childInstance) {\n            context[name] = {\n                instance: childInstance,\n                override: override\n            };\n        }\n    }\n\n    /**\n     * Use this method from your extended object.  this.factory is injected into your object.\n     * this.factory.getSingletonInstance(this.context, 'VideoModel')\n     * will return the video model for use in the extended object.\n     *\n     * @param {Object} context - injected into extended object as this.context\n     * @param {string} className - string name found in all dash.js objects\n     * with name __dashjs_factory_name Will be at the bottom. Will be the same as the object's name.\n     * @returns {*} Context aware instance of specified singleton name.\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function getSingletonInstance(context, className) {\n        for (const i in singletonContexts) {\n            const obj = singletonContexts[i];\n            if (obj.context === context && obj.name === className) {\n                return obj.instance;\n            }\n        }\n        return null;\n    }\n\n    /**\n     * Use this method to add an singleton instance to the system.  Useful for unit testing to mock objects etc.\n     *\n     * @param {Object} context\n     * @param {string} className\n     * @param {Object} instance\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function setSingletonInstance(context, className, instance) {\n        for (const i in singletonContexts) {\n            const obj = singletonContexts[i];\n            if (obj.context === context && obj.name === className) {\n                singletonContexts[i].instance = instance;\n                return;\n            }\n        }\n        singletonContexts.push({\n            name: className,\n            context: context,\n            instance: instance\n        });\n    }\n\n    /**\n     * Use this method to remove all singleton instances associated with a particular context.\n     *\n     * @param {Object} context\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function deleteSingletonInstances(context) {\n        singletonContexts = singletonContexts.filter(x => x.context !== context);\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Factories storage Management\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function getFactoryByName(name, factoriesArray) {\n        return factoriesArray[name];\n    }\n\n    function updateFactory(name, factory, factoriesArray) {\n        if (name in factoriesArray) {\n            factoriesArray[name] = factory;\n        }\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Class Factories Management\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function updateClassFactory(name, factory) {\n        updateFactory(name, factory, classFactories);\n    }\n\n    function getClassFactoryByName(name) {\n        return getFactoryByName(name, classFactories);\n    }\n\n    function getClassFactory(classConstructor) {\n        let factory = getFactoryByName(classConstructor.__dashjs_factory_name, classFactories);\n\n        if (!factory) {\n            factory = function (context) {\n                if (context === undefined) {\n                    context = {};\n                }\n                return {\n                    create: function () {\n                        return merge(classConstructor, context, arguments);\n                    }\n                };\n            };\n\n            classFactories[classConstructor.__dashjs_factory_name] = factory; // store factory\n        }\n        return factory;\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Singleton Factory MAangement\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function updateSingletonFactory(name, factory) {\n        updateFactory(name, factory, singletonFactories);\n    }\n\n    function getSingletonFactoryByName(name) {\n        return getFactoryByName(name, singletonFactories);\n    }\n\n    function getSingletonFactory(classConstructor) {\n        let factory = getFactoryByName(classConstructor.__dashjs_factory_name, singletonFactories);\n        if (!factory) {\n            factory = function (context) {\n                let instance;\n                if (context === undefined) {\n                    context = {};\n                }\n                return {\n                    getInstance: function () {\n                        // If we don't have an instance yet check for one on the context\n                        if (!instance) {\n                            instance = getSingletonInstance(context, classConstructor.__dashjs_factory_name);\n                        }\n                        // If there's no instance on the context then create one\n                        if (!instance) {\n                            instance = merge(classConstructor, context, arguments);\n                            singletonContexts.push({\n                                name: classConstructor.__dashjs_factory_name,\n                                context: context,\n                                instance: instance\n                            });\n                        }\n                        return instance;\n                    }\n                };\n            };\n            singletonFactories[classConstructor.__dashjs_factory_name] = factory; // store factory\n        }\n\n        return factory;\n    }\n\n    function merge(classConstructor, context, args) {\n\n        let classInstance;\n        const className = classConstructor.__dashjs_factory_name;\n        const extensionObject = context[className];\n\n        if (extensionObject) {\n\n            let extension = extensionObject.instance;\n\n            if (extensionObject.override) { //Override public methods in parent but keep parent.\n\n                classInstance = classConstructor.apply({context}, args);\n                extension = extension.apply({\n                    context,\n                    factory: instance,\n                    parent: classInstance\n                }, args);\n\n                for (const prop in extension) {\n                    if (classInstance.hasOwnProperty(prop)) {\n                        classInstance[prop] = extension[prop];\n                    }\n                }\n\n            } else { //replace parent object completely with new object. Same as dijon.\n\n                return extension.apply({\n                    context,\n                    factory: instance\n                }, args);\n\n            }\n        } else {\n            // Create new instance of the class\n            classInstance = classConstructor.apply({context}, args);\n        }\n\n        // Add getClassName function to class instance prototype (used by Debug)\n        classInstance.getClassName = function () {return className;};\n\n        return classInstance;\n    }\n\n    instance = {\n        extend: extend,\n        getSingletonInstance: getSingletonInstance,\n        setSingletonInstance: setSingletonInstance,\n        deleteSingletonInstances: deleteSingletonInstances,\n        getSingletonFactory: getSingletonFactory,\n        getSingletonFactoryByName: getSingletonFactoryByName,\n        updateSingletonFactory: updateSingletonFactory,\n        getClassFactory: getClassFactory,\n        getClassFactoryByName: getClassFactoryByName,\n        updateClassFactory: updateClassFactory\n    };\n\n    return instance;\n\n}());\n\nexport default FactoryMaker;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport FragmentRequest from '../streaming/vo/FragmentRequest.js';\nimport {HTTPRequest} from '../streaming/vo/metrics/HTTPRequest.js';\nimport FactoryMaker from '../core/FactoryMaker.js';\n\nfunction MssFragmentInfoController(config) {\n\n    config = config || {};\n\n    let instance,\n        logger,\n        fragmentModel,\n        started,\n        type,\n        loadFragmentTimeout,\n        startTime,\n        startFragmentTime,\n        index;\n\n    const streamProcessor = config.streamProcessor;\n    const baseURLController = config.baseURLController;\n    const debug = config.debug;\n    const controllerType = 'MssFragmentInfoController';\n\n    function setup() {\n        logger = debug.getLogger(instance);\n    }\n\n    function initialize() {\n        type = streamProcessor.getType();\n        fragmentModel = streamProcessor.getFragmentModel();\n\n        started = false;\n        startTime = null;\n        startFragmentTime = null;\n    }\n\n    function start() {\n        if (started) {\n            return;\n        }\n\n        logger.debug('Start');\n\n        started = true;\n        index = 0;\n\n        loadNextFragmentInfo();\n    }\n\n    function stop() {\n        if (!started) {\n            return;\n        }\n\n        logger.debug('Stop');\n\n        clearTimeout(loadFragmentTimeout);\n        started = false;\n        startTime = null;\n        startFragmentTime = null;\n    }\n\n    function reset() {\n        stop();\n    }\n\n    function loadNextFragmentInfo() {\n        if (!started) {\n            return;\n        }\n\n        // Get last segment from SegmentTimeline\n        const representation = getCurrentRepresentation();\n        const manifest = representation.adaptation.period.mpd.manifest;\n        const adaptation = manifest.Period[representation.adaptation.period.index].AdaptationSet[representation.adaptation.index];\n        const segments = adaptation.SegmentTemplate.SegmentTimeline.S;\n        const segment = segments[segments.length - 1];\n\n        // logger.debug('Last fragment time: ' + (segment.t / adaptation.SegmentTemplate.timescale));\n\n        // Generate segment request\n        const request = getRequestForSegment(adaptation, representation, segment);\n\n        // Send segment request\n        requestFragment.call(this, request);\n    }\n\n    function getRequestForSegment(adaptation, representation, segment) {\n        let timescale = adaptation.SegmentTemplate.timescale;\n        let request = new FragmentRequest();\n\n        request.mediaType = type;\n        request.type = HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE;\n        // request.range = segment.mediaRange;\n        request.startTime = segment.t / timescale;\n        request.duration = segment.d / timescale;\n        request.timescale = timescale;\n        // request.availabilityStartTime = segment.availabilityStartTime;\n        // request.availabilityEndTime = segment.availabilityEndTime;\n        // request.wallStartTime = segment.wallStartTime;\n        request.bandwidth = representation.bandwidth;\n        request.index = index++;\n        request.adaptationIndex = representation.adaptation.index;\n        request.representation = representation;\n        request.url = baseURLController.resolve(representation.path).url + adaptation.SegmentTemplate.media;\n        request.url = request.url.replace('$Bandwidth$', representation.bandwidth);\n        request.url = request.url.replace('$Time$', segment.tManifest ? segment.tManifest : segment.t);\n        request.url = request.url.replace('/Fragments(', '/FragmentInfo(');\n\n        return request;\n    }\n\n    function getCurrentRepresentation() {\n        const representationController = streamProcessor.getRepresentationController();\n        const representation = representationController.getCurrentRepresentation();\n        return representation;\n    }\n\n    function requestFragment(request) {\n        // logger.debug('Load FragmentInfo for time: ' + request.startTime);\n        if (streamProcessor.getFragmentModel().isFragmentLoadedOrPending(request)) {\n            // We may have reached end of timeline in case of start-over streams\n            logger.debug('End of timeline');\n            stop();\n            return;\n        }\n\n        fragmentModel.executeRequest(request);\n    }\n\n    function fragmentInfoLoaded(e) {\n        if (!started) {\n            return;\n        }\n\n        const request = e.request;\n        if (!e.response) {\n            logger.error('Load error', request.url);\n            return;\n        }\n\n        let deltaFragmentTime,\n            deltaTime,\n            delay;\n\n        // logger.debug('FragmentInfo loaded: ', request.url);\n\n        if (startTime === null) {\n            startTime = new Date().getTime();\n        }\n\n        if (!startFragmentTime) {\n            startFragmentTime = request.startTime;\n        }\n\n        // Determine delay before requesting next FragmentInfo\n        deltaTime = (new Date().getTime() - startTime) / 1000;\n        deltaFragmentTime = (request.startTime + request.duration) - startFragmentTime;\n        delay = Math.max(0, (deltaFragmentTime - deltaTime));\n\n        // Set timeout for requesting next FragmentInfo\n        clearTimeout(loadFragmentTimeout);\n        loadFragmentTimeout = setTimeout(function () {\n            loadFragmentTimeout = null;\n            loadNextFragmentInfo();\n        }, delay * 1000);\n    }\n\n    function getType() {\n        return type;\n    }\n\n    instance = {\n        initialize: initialize,\n        controllerType: controllerType,\n        start: start,\n        fragmentInfoLoaded: fragmentInfoLoaded,\n        getType: getType,\n        reset: reset\n    };\n\n    setup();\n\n    return instance;\n}\n\nMssFragmentInfoController.__dashjs_factory_name = 'MssFragmentInfoController';\nexport default FactoryMaker.getClassFactory(MssFragmentInfoController);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass DashJSError {\n    constructor(code, message, data) {\n        this.code = code || null;\n        this.message = message || null;\n        this.data = data || null;\n    }\n}\n\nexport default DashJSError;", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass ErrorsBase {\n    extend(errors, config) {\n        if (!errors) {\n            return;\n        }\n\n        let override = config ? config.override : false;\n        let publicOnly = config ? config.publicOnly : false;\n\n\n        for (const err in errors) {\n            if (!errors.hasOwnProperty(err) || (this[err] && !override)) {\n                continue;\n            }\n            if (publicOnly && errors[err].indexOf('public_') === -1) {\n                continue;\n            }\n            this[err] = errors[err];\n\n        }\n    }\n}\n\nexport default ErrorsBase;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport ErrorsBase from '../../core/errors/ErrorsBase.js';\n\n/**\n * @class\n *\n */\nclass MssErrors extends ErrorsBase {\n    constructor() {\n        super();\n        /**\n         * Error code returned when no tfrf box is detected in MSS live stream\n         */\n        this.MSS_NO_TFRF_CODE = 200;\n\n        /**\n         * Error code returned when one of the codecs defined in the manifest is not supported\n         */\n        this.MSS_UNSUPPORTED_CODEC_CODE = 201;\n\n        this.MSS_NO_TFRF_MESSAGE = 'Missing tfrf in live media segment';\n        this.MSS_UNSUPPORTED_CODEC_MESSAGE = 'Unsupported codec';\n    }\n}\n\nlet mssErrors = new MssErrors();\nexport default mssErrors;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass EventsBase {\n    extend(events, config) {\n        if (!events) {\n            return;\n        }\n\n        let override = config ? config.override : false;\n        let publicOnly = config ? config.publicOnly : false;\n\n\n        for (const evt in events) {\n            if (!events.hasOwnProperty(evt) || (this[evt] && !override)) {\n                continue;\n            }\n            if (publicOnly && events[evt].indexOf('public_') === -1) {\n                continue;\n            }\n            this[evt] = events[evt];\n\n        }\n    }\n}\n\nexport default EventsBase;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport EventsBase from '../core/events/EventsBase.js';\n\n/**\n * @class\n * @implements EventsBase\n */\nclass MediaPlayerEvents extends EventsBase {\n\n    /**\n     * @description Public facing external events to be used when developing a player that implements dash.js.\n     */\n    constructor() {\n        super();\n        /**\n         * Triggered when playback will not start yet\n         * as the MPD's availabilityStartTime is in the future.\n         * Check delay property in payload to determine time before playback will start.\n         * @event MediaPlayerEvents#AST_IN_FUTURE\n         */\n        this.AST_IN_FUTURE = 'astInFuture';\n\n        /**\n         * Triggered when the BaseURLs have been updated.\n         * @event MediaPlayerEvents#BASE_URLS_UPDATED\n         */\n        this.BASE_URLS_UPDATED = 'baseUrlsUpdated';\n\n        /**\n         * Triggered when the video element's buffer state changes to stalled.\n         * Check mediaType in payload to determine type (Video, Audio, FragmentedText).\n         * @event MediaPlayerEvents#BUFFER_EMPTY\n         */\n        this.BUFFER_EMPTY = 'bufferStalled';\n\n        /**\n         * Triggered when the video element's buffer state changes to loaded.\n         * Check mediaType in payload to determine type (Video, Audio, FragmentedText).\n         * @event MediaPlayerEvents#BUFFER_LOADED\n         */\n        this.BUFFER_LOADED = 'bufferLoaded';\n\n        /**\n         * Triggered when the video element's buffer state changes, either stalled or loaded. Check payload for state.\n         * @event MediaPlayerEvents#BUFFER_LEVEL_STATE_CHANGED\n         */\n        this.BUFFER_LEVEL_STATE_CHANGED = 'bufferStateChanged';\n\n        /**\n         * Triggered when the buffer level of a media type has been updated\n         * @event MediaPlayerEvents#BUFFER_LEVEL_UPDATED\n         */\n        this.BUFFER_LEVEL_UPDATED = 'bufferLevelUpdated';\n\n        /**\n         * Triggered when a font signalled by a DVB Font Download has been added to the document FontFaceSet interface.\n         * @event MediaPlayerEvents#DVB_FONT_DOWNLOAD_ADDED\n         */\n        this.DVB_FONT_DOWNLOAD_ADDED = 'dvbFontDownloadAdded';\n\n        /**\n         * Triggered when a font signalled by a DVB Font Download has successfully downloaded and the FontFace can be used.\n         * @event MediaPlayerEvents#DVB_FONT_DOWNLOAD_COMPLETE\n         */\n        this.DVB_FONT_DOWNLOAD_COMPLETE = 'dvbFontDownloadComplete';\n\n        /**\n         * Triggered when a font signalled by a DVB Font Download could not be successfully downloaded, so the FontFace will not be used.\n         * @event MediaPlayerEvents#DVB_FONT_DOWNLOAD_FAILED\n         */\n        this.DVB_FONT_DOWNLOAD_FAILED = 'dvbFontDownloadFailed';\n\n        /**\n         * Triggered when a dynamic stream changed to static (transition phase between Live and On-Demand).\n         * @event MediaPlayerEvents#DYNAMIC_TO_STATIC\n         */\n        this.DYNAMIC_TO_STATIC = 'dynamicToStatic';\n\n        /**\n         * Triggered when there is an error from the element or MSE source buffer.\n         * @event MediaPlayerEvents#ERROR\n         */\n        this.ERROR = 'error';\n        /**\n         * Triggered when a fragment download has completed.\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_COMPLETED\n         */\n        this.FRAGMENT_LOADING_COMPLETED = 'fragmentLoadingCompleted';\n\n        /**\n         * Triggered when a partial fragment download has completed.\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_PROGRESS\n         */\n        this.FRAGMENT_LOADING_PROGRESS = 'fragmentLoadingProgress';\n        /**\n         * Triggered when a fragment download has started.\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_STARTED\n         */\n        this.FRAGMENT_LOADING_STARTED = 'fragmentLoadingStarted';\n\n        /**\n         * Triggered when a fragment download is abandoned due to detection of slow download base on the ABR abandon rule..\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_ABANDONED\n         */\n        this.FRAGMENT_LOADING_ABANDONED = 'fragmentLoadingAbandoned';\n\n        /**\n         * Triggered when {@link module:Debug} logger methods are called.\n         * @event MediaPlayerEvents#LOG\n         */\n        this.LOG = 'log';\n\n        /**\n         * Triggered when the manifest load is started\n         * @event MediaPlayerEvents#MANIFEST_LOADING_STARTED\n         */\n        this.MANIFEST_LOADING_STARTED = 'manifestLoadingStarted';\n\n        /**\n         * Triggered when the manifest loading is finished, providing the request object information\n         * @event MediaPlayerEvents#MANIFEST_LOADING_FINISHED\n         */\n        this.MANIFEST_LOADING_FINISHED = 'manifestLoadingFinished';\n\n        /**\n         * Triggered when the manifest load is complete, providing the payload\n         * @event MediaPlayerEvents#MANIFEST_LOADED\n         */\n        this.MANIFEST_LOADED = 'manifestLoaded';\n\n        /**\n         * Triggered anytime there is a change to the overall metrics.\n         * @event MediaPlayerEvents#METRICS_CHANGED\n         */\n        this.METRICS_CHANGED = 'metricsChanged';\n\n        /**\n         * Triggered when an individual metric is added, updated or cleared.\n         * @event MediaPlayerEvents#METRIC_CHANGED\n         */\n        this.METRIC_CHANGED = 'metricChanged';\n\n        /**\n         * Triggered every time a new metric is added.\n         * @event MediaPlayerEvents#METRIC_ADDED\n         */\n        this.METRIC_ADDED = 'metricAdded';\n\n        /**\n         * Triggered every time a metric is updated.\n         * @event MediaPlayerEvents#METRIC_UPDATED\n         */\n        this.METRIC_UPDATED = 'metricUpdated';\n\n        /**\n         * Triggered when a new stream (period) starts.\n         * @event MediaPlayerEvents#PERIOD_SWITCH_STARTED\n         */\n        this.PERIOD_SWITCH_STARTED = 'periodSwitchStarted';\n\n        /**\n         * Triggered at the stream end of a period.\n         * @event MediaPlayerEvents#PERIOD_SWITCH_COMPLETED\n         */\n        this.PERIOD_SWITCH_COMPLETED = 'periodSwitchCompleted';\n\n        /**\n         * Triggered when an ABR up /down switch is initiated; either by user in manual mode or auto mode via ABR rules.\n         * @event MediaPlayerEvents#QUALITY_CHANGE_REQUESTED\n         */\n        this.QUALITY_CHANGE_REQUESTED = 'qualityChangeRequested';\n\n        /**\n         * Triggered when the new ABR quality is being rendered on-screen.\n         * @event MediaPlayerEvents#QUALITY_CHANGE_RENDERED\n         */\n        this.QUALITY_CHANGE_RENDERED = 'qualityChangeRendered';\n\n        /**\n         * Triggered when the new track is being selected\n         * @event MediaPlayerEvents#NEW_TRACK_SELECTED\n         */\n        this.NEW_TRACK_SELECTED = 'newTrackSelected';\n\n        /**\n         * Triggered when the new track is being rendered.\n         * @event MediaPlayerEvents#TRACK_CHANGE_RENDERED\n         */\n        this.TRACK_CHANGE_RENDERED = 'trackChangeRendered';\n\n        /**\n         * Triggered when a stream (period) is being loaded\n         * @event MediaPlayerEvents#STREAM_INITIALIZING\n         */\n        this.STREAM_INITIALIZING = 'streamInitializing';\n\n        /**\n         * Triggered when a stream (period) is loaded\n         * @event MediaPlayerEvents#STREAM_UPDATED\n         */\n        this.STREAM_UPDATED = 'streamUpdated';\n\n        /**\n         * Triggered when a stream (period) is activated\n         * @event MediaPlayerEvents#STREAM_ACTIVATED\n         */\n        this.STREAM_ACTIVATED = 'streamActivated';\n\n        /**\n         * Triggered when a stream (period) is deactivated\n         * @event MediaPlayerEvents#STREAM_DEACTIVATED\n         */\n        this.STREAM_DEACTIVATED = 'streamDeactivated';\n\n        /**\n         * Triggered when a stream (period) is activated\n         * @event MediaPlayerEvents#STREAM_INITIALIZED\n         */\n        this.STREAM_INITIALIZED = 'streamInitialized';\n\n        /**\n         * Triggered when the player has been reset.\n         * @event MediaPlayerEvents#STREAM_TEARDOWN_COMPLETE\n         */\n        this.STREAM_TEARDOWN_COMPLETE = 'streamTeardownComplete';\n\n        /**\n         * Triggered once all text tracks detected in the MPD are added to the video element.\n         * @event MediaPlayerEvents#TEXT_TRACKS_ADDED\n         */\n        this.TEXT_TRACKS_ADDED = 'allTextTracksAdded';\n\n        /**\n         * Triggered when a text track is added to the video element's TextTrackList\n         * @event MediaPlayerEvents#TEXT_TRACK_ADDED\n         */\n        this.TEXT_TRACK_ADDED = 'textTrackAdded';\n\n        /**\n         * Triggered when a text track should be shown\n         * @event MediaPlayerEvents#CUE_ENTER\n         */\n        this.CUE_ENTER = 'cueEnter'\n\n        /**\n         * Triggered when a text track should be hidden\n         * @event MediaPlayerEvents#CUE_ENTER\n         */\n        this.CUE_EXIT = 'cueExit'\n\n        /**\n         * Triggered when a throughput measurement based on the last segment request has been stored\n         * @event MediaPlayerEvents#THROUGHPUT_MEASUREMENT_STORED\n         */\n        this.THROUGHPUT_MEASUREMENT_STORED = 'throughputMeasurementStored';\n\n        /**\n         * Triggered when a ttml chunk is parsed.\n         * @event MediaPlayerEvents#TTML_PARSED\n         */\n        this.TTML_PARSED = 'ttmlParsed';\n\n        /**\n         * Triggered when a ttml chunk has to be parsed.\n         * @event MediaPlayerEvents#TTML_TO_PARSE\n         */\n        this.TTML_TO_PARSE = 'ttmlToParse';\n\n        /**\n         * Triggered when a caption is rendered.\n         * @event MediaPlayerEvents#CAPTION_RENDERED\n         */\n        this.CAPTION_RENDERED = 'captionRendered';\n\n        /**\n         * Triggered when the caption container is resized.\n         * @event MediaPlayerEvents#CAPTION_CONTAINER_RESIZE\n         */\n        this.CAPTION_CONTAINER_RESIZE = 'captionContainerResize';\n\n        /**\n         * Sent when enough data is available that the media can be played,\n         * at least for a couple of frames.  This corresponds to the\n         * HAVE_ENOUGH_DATA readyState.\n         * @event MediaPlayerEvents#CAN_PLAY\n         */\n        this.CAN_PLAY = 'canPlay';\n\n        /**\n         * This corresponds to the CAN_PLAY_THROUGH readyState.\n         * @event MediaPlayerEvents#CAN_PLAY_THROUGH\n         */\n        this.CAN_PLAY_THROUGH = 'canPlayThrough';\n\n        /**\n         * Sent when playback completes.\n         * @event MediaPlayerEvents#PLAYBACK_ENDED\n         */\n        this.PLAYBACK_ENDED = 'playbackEnded';\n\n        /**\n         * Sent when an error occurs.  The element's error\n         * attribute contains more information.\n         * @event MediaPlayerEvents#PLAYBACK_ERROR\n         */\n        this.PLAYBACK_ERROR = 'playbackError';\n\n        /**\n         * This event is fired once the playback has been initialized by MediaPlayer.js.\n         * After that event methods such as setTextTrack() can be used.\n         * @event MediaPlayerEvents#PLAYBACK_INITIALIZED\n         */\n        this.PLAYBACK_INITIALIZED = 'playbackInitialized';\n\n        /**\n         * Sent when playback is not allowed (for example if user gesture is needed).\n         * @event MediaPlayerEvents#PLAYBACK_NOT_ALLOWED\n         */\n        this.PLAYBACK_NOT_ALLOWED = 'playbackNotAllowed';\n\n        /**\n         * The media's metadata has finished loading; all attributes now\n         * contain as much useful information as they're going to.\n         * @event MediaPlayerEvents#PLAYBACK_METADATA_LOADED\n         */\n        this.PLAYBACK_METADATA_LOADED = 'playbackMetaDataLoaded';\n\n        /**\n         * The event is fired when the frame at the current playback position of the media has finished loading;\n         * often the first frame\n         * @event MediaPlayerEvents#PLAYBACK_LOADED_DATA\n         */\n        this.PLAYBACK_LOADED_DATA = 'playbackLoadedData';\n\n        /**\n         * Sent when playback is paused.\n         * @event MediaPlayerEvents#PLAYBACK_PAUSED\n         */\n        this.PLAYBACK_PAUSED = 'playbackPaused';\n\n        /**\n         * Sent when the media begins to play (either for the first time, after having been paused,\n         * or after ending and then restarting).\n         *\n         * @event MediaPlayerEvents#PLAYBACK_PLAYING\n         */\n        this.PLAYBACK_PLAYING = 'playbackPlaying';\n\n        /**\n         * Sent periodically to inform interested parties of progress downloading\n         * the media. Information about the current amount of the media that has\n         * been downloaded is available in the media element's buffered attribute.\n         * @event MediaPlayerEvents#PLAYBACK_PROGRESS\n         */\n        this.PLAYBACK_PROGRESS = 'playbackProgress';\n\n        /**\n         * Sent when the playback speed changes.\n         * @event MediaPlayerEvents#PLAYBACK_RATE_CHANGED\n         */\n        this.PLAYBACK_RATE_CHANGED = 'playbackRateChanged';\n\n        /**\n         * Sent when a seek operation completes.\n         * @event MediaPlayerEvents#PLAYBACK_SEEKED\n         */\n        this.PLAYBACK_SEEKED = 'playbackSeeked';\n\n        /**\n         * Sent when a seek operation begins.\n         * @event MediaPlayerEvents#PLAYBACK_SEEKING\n         */\n        this.PLAYBACK_SEEKING = 'playbackSeeking';\n\n        /**\n         * Sent when the video element reports stalled\n         * @event MediaPlayerEvents#PLAYBACK_STALLED\n         */\n        this.PLAYBACK_STALLED = 'playbackStalled';\n\n        /**\n         * Sent when playback of the media starts after having been paused;\n         * that is, when playback is resumed after a prior pause event.\n         *\n         * @event MediaPlayerEvents#PLAYBACK_STARTED\n         */\n        this.PLAYBACK_STARTED = 'playbackStarted';\n\n        /**\n         * The time indicated by the element's currentTime attribute has changed.\n         * @event MediaPlayerEvents#PLAYBACK_TIME_UPDATED\n         */\n        this.PLAYBACK_TIME_UPDATED = 'playbackTimeUpdated';\n\n        /**\n         * Sent when the video element reports that the volume has changed\n         * @event MediaPlayerEvents#PLAYBACK_VOLUME_CHANGED\n         */\n        this.PLAYBACK_VOLUME_CHANGED = 'playbackVolumeChanged';\n\n        /**\n         * Sent when the media playback has stopped because of a temporary lack of data.\n         *\n         * @event MediaPlayerEvents#PLAYBACK_WAITING\n         */\n        this.PLAYBACK_WAITING = 'playbackWaiting';\n\n        /**\n         * Manifest validity changed - As a result of an MPD validity expiration event.\n         * @event MediaPlayerEvents#MANIFEST_VALIDITY_CHANGED\n         */\n        this.MANIFEST_VALIDITY_CHANGED = 'manifestValidityChanged';\n\n        /**\n         * Dash events are triggered at their respective start points on the timeline.\n         * @event MediaPlayerEvents#EVENT_MODE_ON_START\n         */\n        this.EVENT_MODE_ON_START = 'eventModeOnStart';\n\n        /**\n         * Dash events are triggered as soon as they were parsed.\n         * @event MediaPlayerEvents#EVENT_MODE_ON_RECEIVE\n         */\n        this.EVENT_MODE_ON_RECEIVE = 'eventModeOnReceive';\n\n        /**\n         * Event that is dispatched whenever the player encounters a potential conformance validation that might lead to unexpected/not optimal behavior\n         * @event MediaPlayerEvents#CONFORMANCE_VIOLATION\n         */\n        this.CONFORMANCE_VIOLATION = 'conformanceViolation';\n\n        /**\n         * Event that is dispatched whenever the player switches to a different representation\n         * @event MediaPlayerEvents#REPRESENTATION_SWITCH\n         */\n        this.REPRESENTATION_SWITCH = 'representationSwitch';\n\n        /**\n         * Event that is dispatched whenever an adaptation set is removed due to all representations not being supported.\n         * @event MediaPlayerEvents#ADAPTATION_SET_REMOVED_NO_CAPABILITIES\n         */\n        this.ADAPTATION_SET_REMOVED_NO_CAPABILITIES = 'adaptationSetRemovedNoCapabilities';\n\n        /**\n         * Triggered when a content steering request has completed.\n         * @event MediaPlayerEvents#CONTENT_STEERING_REQUEST_COMPLETED\n         */\n        this.CONTENT_STEERING_REQUEST_COMPLETED = 'contentSteeringRequestCompleted';\n\n        /**\n         * Triggered when an inband prft (ProducerReferenceTime) boxes has been received.\n         * @event MediaPlayerEvents#INBAND_PRFT\n         */\n        this.INBAND_PRFT = 'inbandPrft';\n\n        /**\n         * The streaming attribute of the Managed Media Source is true\n         * @type {string}\n         */\n        this.MANAGED_MEDIA_SOURCE_START_STREAMING = 'managedMediaSourceStartStreaming';\n\n        /**\n         * The streaming attribute of the Managed Media Source is false\n         * @type {string}\n         */\n        this.MANAGED_MEDIA_SOURCE_END_STREAMING = 'managedMediaSourceEndStreaming';\n    }\n}\n\nlet mediaPlayerEvents = new MediaPlayerEvents();\nexport default mediaPlayerEvents;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport DashJSError from '../streaming/vo/DashJSError.js';\nimport MssErrors from './errors/MssErrors.js';\n\nimport Events from '../streaming/MediaPlayerEvents.js';\nimport FactoryMaker from '../core/FactoryMaker.js';\n\n/**\n * @module MssFragmentMoofProcessor\n * @ignore\n * @param {Object} config object\n */\nfunction MssFragmentMoofProcessor(config) {\n\n    config = config || {};\n    let instance,\n        type,\n        logger;\n    const dashMetrics = config.dashMetrics;\n    const playbackController = config.playbackController;\n    const errorHandler = config.errHandler;\n    const eventBus = config.eventBus;\n    const ISOBoxer = config.ISOBoxer;\n    const debug = config.debug;\n\n    function setup() {\n        logger = debug.getLogger(instance);\n        type = '';\n    }\n\n    function processTfrf(request, tfrf, tfdt, streamProcessor) {\n        const representationController = streamProcessor.getRepresentationController();\n        const representation = representationController.getCurrentRepresentation();\n\n        const manifest = representation.adaptation.period.mpd.manifest;\n        const adaptation = manifest.Period[representation.adaptation.period.index].AdaptationSet[representation.adaptation.index];\n        const timescale = adaptation.SegmentTemplate.timescale;\n\n        type = streamProcessor.getType();\n\n        // Process tfrf only for live streams or start-over static streams (timeShiftBufferDepth > 0)\n        if (manifest.type !== 'dynamic' && !manifest.timeShiftBufferDepth) {\n            return;\n        }\n\n        if (!tfrf) {\n            errorHandler.error(new DashJSError(MssErrors.MSS_NO_TFRF_CODE, MssErrors.MSS_NO_TFRF_MESSAGE));\n            return;\n        }\n\n        // Get adaptation's segment timeline (always a SegmentTimeline in Smooth Streaming use case)\n        const segments = adaptation.SegmentTemplate.SegmentTimeline.S;\n        const entries = tfrf.entry;\n        let entry,\n            segmentTime,\n            range;\n        let segment = null;\n        let t = 0;\n        let endTime;\n        let availabilityStartTime = null;\n\n        if (entries.length === 0) {\n            return;\n        }\n\n        // Consider only first tfrf entry (to avoid pre-condition failure on fragment info requests)\n        entry = entries[0];\n\n        // In case of start-over streams, check if we have reached end of original manifest duration (set in timeShiftBufferDepth)\n        // => then do not update anymore timeline\n        if (manifest.type === 'static') {\n            // Get first segment time\n            segmentTime = segments[0].tManifest ? parseFloat(segments[0].tManifest) : segments[0].t;\n            if (entry.fragment_absolute_time > (segmentTime + (manifest.timeShiftBufferDepth * timescale))) {\n                return;\n            }\n        }\n\n        // logger.debug('entry - t = ', (entry.fragment_absolute_time / timescale));\n\n        // Get last segment time\n        segmentTime = segments[segments.length - 1].tManifest ? parseFloat(segments[segments.length - 1].tManifest) : segments[segments.length - 1].t;\n        // logger.debug('Last segment - t = ', (segmentTime / timescale));\n\n        // Check if we have to append new segment to timeline\n        if (entry.fragment_absolute_time <= segmentTime) {\n            // Update DVR window range => set range end to end time of current segment\n            range = {\n                start: segments[0].t / timescale,\n                end: (tfdt.baseMediaDecodeTime / timescale) + request.duration\n            };\n\n            updateDVR(request.mediaType, range, streamProcessor.getStreamInfo().manifestInfo);\n            return;\n        }\n\n        // logger.debug('Add new segment - t = ', (entry.fragment_absolute_time / timescale));\n        segment = {};\n        segment.t = entry.fragment_absolute_time;\n        segment.d = entry.fragment_duration;\n        // If timestamps starts at 0 relative to 1st segment (dynamic to static) then update segment time\n        if (segments[0].tManifest) {\n            segment.t -= parseFloat(segments[0].tManifest) - segments[0].t;\n            segment.tManifest = entry.fragment_absolute_time;\n        }\n\n        // Patch previous segment duration\n        let lastSegment = segments[segments.length - 1];\n        if (lastSegment.t + lastSegment.d !== segment.t) {\n            logger.debug('Patch segment duration - t = ', lastSegment.t + ', d = ' + lastSegment.d + ' => ' + (segment.t - lastSegment.t));\n            lastSegment.d = segment.t - lastSegment.t;\n        }\n\n        segments.push(segment);\n\n        // In case of static start-over streams, update content duration\n        if (manifest.type === 'static') {\n            if (type === 'video') {\n                segment = segments[segments.length - 1];\n                endTime = (segment.t + segment.d) / timescale;\n                if (endTime > representation.adaptation.period.duration) {\n                    eventBus.trigger(Events.MANIFEST_VALIDITY_CHANGED, { sender: this, newDuration: endTime });\n                }\n            }\n            return;\n        } else {\n            // In case of live streams, update segment timeline according to DVR window\n            if (manifest.timeShiftBufferDepth && manifest.timeShiftBufferDepth > 0) {\n                // Get timestamp of the last segment\n                segment = segments[segments.length - 1];\n                t = segment.t;\n\n                // Determine the segments' availability start time\n                availabilityStartTime = (t - (manifest.timeShiftBufferDepth * timescale)) / timescale;\n\n                // Remove segments prior to availability start time\n                segment = segments[0];\n                endTime = (segment.t + segment.d) / timescale;\n                while (endTime < availabilityStartTime) {\n                    // Check if not currently playing the segment to be removed\n                    if (!playbackController.isPaused() && playbackController.getTime() < endTime) {\n                        break;\n                    }\n                    // logger.debug('Remove segment  - t = ' + (segment.t / timescale));\n                    segments.splice(0, 1);\n                    segment = segments[0];\n                    endTime = (segment.t + segment.d) / timescale;\n                }\n            }\n\n            // Update DVR window range => set range end to end time of current segment\n            range = {\n                start: segments[0].t / timescale,\n                end: (tfdt.baseMediaDecodeTime / timescale) + request.duration\n            };\n\n            updateDVR(type, range, streamProcessor.getStreamInfo().manifestInfo);\n        }\n\n    }\n\n    function updateDVR(type, range, manifestInfo) {\n        if (type !== 'video' && type !== 'audio') {\n            return;\n        }\n        const dvrInfos = dashMetrics.getCurrentDVRInfo(type);\n        if (!dvrInfos || (range.end > dvrInfos.range.end)) {\n            logger.debug('Update DVR range: [' + range.start + ' - ' + range.end + ']');\n            dashMetrics.addDVRInfo(type, playbackController.getTime(), manifestInfo, range);\n            playbackController.updateCurrentTime(type);\n        }\n    }\n\n    // This function returns the offset of the 1st byte of a child box within a container box\n    function getBoxOffset(parent, type) {\n        let offset = 8;\n        let i = 0;\n\n        for (i = 0; i < parent.boxes.length; i++) {\n            if (parent.boxes[i].type === type) {\n                return offset;\n            }\n            offset += parent.boxes[i].size;\n        }\n        return offset;\n    }\n\n    function convertFragment(e, streamProcessor) {\n        let i;\n\n        // e.request contains request description object\n        // e.response contains fragment bytes\n        const isoFile = ISOBoxer.parseBuffer(e.response);\n        // Update track_Id in tfhd box\n        const tfhd = isoFile.fetch('tfhd');\n        tfhd.track_ID = e.request.representation.mediaInfo.index + 1;\n\n        // Add tfdt box\n        let tfdt = isoFile.fetch('tfdt');\n        const traf = isoFile.fetch('traf');\n        if (tfdt === null) {\n            tfdt = ISOBoxer.createFullBox('tfdt', traf, tfhd);\n            tfdt.version = 1;\n            tfdt.flags = 0;\n            tfdt.baseMediaDecodeTime = Math.floor(e.request.startTime * e.request.timescale);\n        }\n\n        const trun = isoFile.fetch('trun');\n\n        // Process tfxd boxes\n        // This box provide absolute timestamp but we take the segment start time for tfdt\n        let tfxd = isoFile.fetch('tfxd');\n        if (tfxd) {\n            tfxd._parent.boxes.splice(tfxd._parent.boxes.indexOf(tfxd), 1);\n            tfxd = null;\n        }\n        let tfrf = isoFile.fetch('tfrf');\n        processTfrf(e.request, tfrf, tfdt, streamProcessor);\n        if (tfrf) {\n            tfrf._parent.boxes.splice(tfrf._parent.boxes.indexOf(tfrf), 1);\n            tfrf = null;\n        }\n\n        // If protected content in PIFF1.1 format (sepiff box = Sample Encryption PIFF)\n        // => convert sepiff box it into a senc box\n        // => create saio and saiz boxes (if not already present)\n        const sepiff = isoFile.fetch('sepiff');\n        if (sepiff !== null) {\n            sepiff.type = 'senc';\n            sepiff.usertype = undefined;\n\n            let saio = isoFile.fetch('saio');\n            if (saio === null) {\n                // Create Sample Auxiliary Information Offsets Box box (saio)\n                saio = ISOBoxer.createFullBox('saio', traf);\n                saio.version = 0;\n                saio.flags = 0;\n                saio.entry_count = 1;\n                saio.offset = [0];\n\n                const saiz = ISOBoxer.createFullBox('saiz', traf);\n                saiz.version = 0;\n                saiz.flags = 0;\n                saiz.sample_count = sepiff.sample_count;\n                saiz.default_sample_info_size = 0;\n                saiz.sample_info_size = [];\n\n                if (sepiff.flags & 0x02) {\n                    // Sub-sample encryption => set sample_info_size for each sample\n                    for (i = 0; i < sepiff.sample_count; i += 1) {\n                        // 10 = 8 (InitializationVector field size) + 2 (subsample_count field size)\n                        // 6 = 2 (BytesOfClearData field size) + 4 (BytesOfEncryptedData field size)\n                        saiz.sample_info_size[i] = 10 + (6 * sepiff.entry[i].NumberOfEntries);\n                    }\n                } else {\n                    // No sub-sample encryption => set default sample_info_size = InitializationVector field size (8)\n                    saiz.default_sample_info_size = 8;\n                }\n            }\n        }\n\n        tfhd.flags &= 0xFFFFFE; // set tfhd.base-data-offset-present to false\n        tfhd.flags |= 0x020000; // set tfhd.default-base-is-moof to true\n        trun.flags |= 0x000001; // set trun.data-offset-present to true\n\n        // Update trun.data_offset field that corresponds to first data byte (inside mdat box)\n        const moof = isoFile.fetch('moof');\n        let length = moof.getLength();\n        trun.data_offset = length + 8;\n\n        // Update saio box offset field according to new senc box offset\n        let saio = isoFile.fetch('saio');\n        if (saio !== null) {\n            let trafPosInMoof = getBoxOffset(moof, 'traf');\n            let sencPosInTraf = getBoxOffset(traf, 'senc');\n            // Set offset from begin fragment to the first IV field in senc box\n            saio.offset[0] = trafPosInMoof + sencPosInTraf + 16; // 16 = box header (12) + sample_count field size (4)\n        }\n\n        // Write transformed/processed fragment into request reponse data\n        e.response = isoFile.write();\n    }\n\n    function updateSegmentList(e, streamProcessor) {\n        // e.request contains request description object\n        // e.response contains fragment bytes\n        if (!e.response) {\n            throw new Error('e.response parameter is missing');\n        }\n\n        const isoFile = ISOBoxer.parseBuffer(e.response);\n        // Update track_Id in tfhd box\n        const tfhd = isoFile.fetch('tfhd');\n        tfhd.track_ID = e.request.representation.mediaInfo.index + 1;\n\n        // Add tfdt box\n        let tfdt = isoFile.fetch('tfdt');\n        let traf = isoFile.fetch('traf');\n        if (tfdt === null) {\n            tfdt = ISOBoxer.createFullBox('tfdt', traf, tfhd);\n            tfdt.version = 1;\n            tfdt.flags = 0;\n            tfdt.baseMediaDecodeTime = Math.floor(e.request.startTime * e.request.timescale);\n        }\n\n        let tfrf = isoFile.fetch('tfrf');\n        processTfrf(e.request, tfrf, tfdt, streamProcessor);\n        if (tfrf) {\n            tfrf._parent.boxes.splice(tfrf._parent.boxes.indexOf(tfrf), 1);\n            tfrf = null;\n        }\n    }\n\n    function getType() {\n        return type;\n    }\n\n    instance = {\n        convertFragment,\n        updateSegmentList,\n        getType\n    };\n\n    setup();\n    return instance;\n}\n\nMssFragmentMoofProcessor.__dashjs_factory_name = 'MssFragmentMoofProcessor';\nexport default FactoryMaker.getClassFactory(MssFragmentMoofProcessor);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport MssErrors from './errors/MssErrors.js';\nimport FactoryMaker from '../core/FactoryMaker.js';\n\n/**\n * @module MssFragmentMoovProcessor\n * @ignore\n * @param {Object} config object\n */\nfunction MssFragmentMoovProcessor(config) {\n    config = config || {};\n    const NALUTYPE_SPS = 7;\n    const NALUTYPE_PPS = 8;\n    const constants = config.constants;\n    const ISOBoxer = config.ISOBoxer;\n\n    let protectionController = config.protectionController;\n    let instance,\n        period,\n        adaptationSet,\n        representation,\n        contentProtection,\n        timescale,\n        trackId;\n\n    function createFtypBox(isoFile) {\n        let ftyp = ISOBoxer.createBox('ftyp', isoFile);\n        ftyp.major_brand = 'iso6';\n        ftyp.minor_version = 1; // is an informative integer for the minor version of the major brand\n        ftyp.compatible_brands = []; //is a list, to the end of the box, of brands isom, iso6 and msdh\n        ftyp.compatible_brands[0] = 'isom'; // => decimal ASCII value for isom\n        ftyp.compatible_brands[1] = 'iso6'; // => decimal ASCII value for iso6\n        ftyp.compatible_brands[2] = 'msdh'; // => decimal ASCII value for msdh\n\n        return ftyp;\n    }\n\n    function createMoovBox(isoFile) {\n\n        // moov box\n        let moov = ISOBoxer.createBox('moov', isoFile);\n\n        // moov/mvhd\n        createMvhdBox(moov);\n\n        // moov/trak\n        let trak = ISOBoxer.createBox('trak', moov);\n\n        // moov/trak/tkhd\n        createTkhdBox(trak);\n\n        // moov/trak/mdia\n        let mdia = ISOBoxer.createBox('mdia', trak);\n\n        // moov/trak/mdia/mdhd\n        createMdhdBox(mdia);\n\n        // moov/trak/mdia/hdlr\n        createHdlrBox(mdia);\n\n        // moov/trak/mdia/minf\n        let minf = ISOBoxer.createBox('minf', mdia);\n\n        switch (adaptationSet.type) {\n            case constants.VIDEO:\n                // moov/trak/mdia/minf/vmhd\n                createVmhdBox(minf);\n                break;\n            case constants.AUDIO:\n                // moov/trak/mdia/minf/smhd\n                createSmhdBox(minf);\n                break;\n            default:\n                break;\n        }\n\n        // moov/trak/mdia/minf/dinf\n        let dinf = ISOBoxer.createBox('dinf', minf);\n\n        // moov/trak/mdia/minf/dinf/dref\n        createDrefBox(dinf);\n\n        // moov/trak/mdia/minf/stbl\n        let stbl = ISOBoxer.createBox('stbl', minf);\n\n        // Create empty stts, stsc, stco and stsz boxes\n        // Use data field as for codem-isoboxer unknown boxes for setting fields value\n\n        // moov/trak/mdia/minf/stbl/stts\n        let stts = ISOBoxer.createFullBox('stts', stbl);\n        stts._data = [0, 0, 0, 0, 0, 0, 0, 0]; // version = 0, flags = 0, entry_count = 0\n\n        // moov/trak/mdia/minf/stbl/stsc\n        let stsc = ISOBoxer.createFullBox('stsc', stbl);\n        stsc._data = [0, 0, 0, 0, 0, 0, 0, 0]; // version = 0, flags = 0, entry_count = 0\n\n        // moov/trak/mdia/minf/stbl/stco\n        let stco = ISOBoxer.createFullBox('stco', stbl);\n        stco._data = [0, 0, 0, 0, 0, 0, 0, 0]; // version = 0, flags = 0, entry_count = 0\n\n        // moov/trak/mdia/minf/stbl/stsz\n        let stsz = ISOBoxer.createFullBox('stsz', stbl);\n        stsz._data = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]; // version = 0, flags = 0, sample_size = 0, sample_count = 0\n\n        // moov/trak/mdia/minf/stbl/stsd\n        createStsdBox(stbl);\n\n        // moov/mvex\n        let mvex = ISOBoxer.createBox('mvex', moov);\n\n        // moov/mvex/trex\n        createTrexBox(mvex);\n\n        if (contentProtection && protectionController) {\n            let supportedKS = protectionController.getSupportedKeySystemMetadataFromContentProtection(contentProtection);\n            createProtectionSystemSpecificHeaderBox(moov, supportedKS);\n        }\n    }\n\n    function createMvhdBox(moov) {\n\n        let mvhd = ISOBoxer.createFullBox('mvhd', moov);\n\n        mvhd.version = 1; // version = 1  in order to have 64bits duration value\n\n        mvhd.creation_time = 0; // the creation time of the presentation => ignore (set to 0)\n        mvhd.modification_time = 0; // the most recent time the presentation was modified => ignore (set to 0)\n        mvhd.timescale = timescale; // the time-scale for the entire presentation => 10000000 for MSS\n        mvhd.duration = period.duration === Infinity ? 0xFFFFFFFFFFFFFFFF : Math.round(period.duration * timescale); // the length of the presentation (in the indicated timescale) =>  take duration of period\n        mvhd.rate = 1.0; // 16.16 number, '1.0' = normal playback\n        mvhd.volume = 1.0; // 8.8 number, '1.0' = full volume\n        mvhd.reserved1 = 0;\n        mvhd.reserved2 = [0x0, 0x0];\n        mvhd.matrix = [\n            1, 0, 0, // provides a transformation matrix for the video;\n            0, 1, 0, // (u,v,w) are restricted here to (0,0,1)\n            0, 0, 16384\n        ];\n        mvhd.pre_defined = [0, 0, 0, 0, 0, 0];\n        mvhd.next_track_ID = trackId + 1; // indicates a value to use for the track ID of the next track to be added to this presentation\n\n        return mvhd;\n    }\n\n    function createTkhdBox(trak) {\n\n        let tkhd = ISOBoxer.createFullBox('tkhd', trak);\n\n        tkhd.version = 1; // version = 1  in order to have 64bits duration value\n        tkhd.flags = 0x1 | // Track_enabled (0x000001): Indicates that the track is enabled\n            0x2 | // Track_in_movie (0x000002):  Indicates that the track is used in the presentation\n            0x4; // Track_in_preview (0x000004):  Indicates that the track is used when previewing the presentation\n\n        tkhd.creation_time = 0; // the creation time of the presentation => ignore (set to 0)\n        tkhd.modification_time = 0; // the most recent time the presentation was modified => ignore (set to 0)\n        tkhd.track_ID = trackId; // uniquely identifies this track over the entire life-time of this presentation\n        tkhd.reserved1 = 0;\n        tkhd.duration = period.duration === Infinity ? 0xFFFFFFFFFFFFFFFF : Math.round(period.duration * timescale); // the duration of this track (in the timescale indicated in the Movie Header Box) =>  take duration of period\n        tkhd.reserved2 = [0x0, 0x0];\n        tkhd.layer = 0; // specifies the front-to-back ordering of video tracks; tracks with lower numbers are closer to the viewer => 0 since only one video track\n        tkhd.alternate_group = 0; // specifies a group or collection of tracks => ignore\n        tkhd.volume = 1.0; // '1.0' = full volume\n        tkhd.reserved3 = 0;\n        tkhd.matrix = [\n            1, 0, 0, // provides a transformation matrix for the video;\n            0, 1, 0, // (u,v,w) are restricted here to (0,0,1)\n            0, 0, 16384\n        ];\n        tkhd.width = representation.width; // visual presentation width\n        tkhd.height = representation.height; // visual presentation height\n\n        return tkhd;\n    }\n\n    function createMdhdBox(mdia) {\n\n        let mdhd = ISOBoxer.createFullBox('mdhd', mdia);\n\n        mdhd.version = 1; // version = 1  in order to have 64bits duration value\n\n        mdhd.creation_time = 0; // the creation time of the presentation => ignore (set to 0)\n        mdhd.modification_time = 0; // the most recent time the presentation was modified => ignore (set to 0)\n        mdhd.timescale = timescale; // the time-scale for the entire presentation\n        mdhd.duration = period.duration === Infinity ? 0xFFFFFFFFFFFFFFFF : Math.round(period.duration * timescale); // the duration of this media (in the scale of the timescale). If the duration cannot be determined then duration is set to all 1s.\n        mdhd.language = adaptationSet.lang || 'und'; // declares the language code for this media\n        mdhd.pre_defined = 0;\n\n        return mdhd;\n    }\n\n    function createHdlrBox(mdia) {\n\n        let hdlr = ISOBoxer.createFullBox('hdlr', mdia);\n\n        hdlr.pre_defined = 0;\n        switch (adaptationSet.type) {\n            case constants.VIDEO:\n                hdlr.handler_type = 'vide';\n                break;\n            case constants.AUDIO:\n                hdlr.handler_type = 'soun';\n                break;\n            default:\n                hdlr.handler_type = 'meta';\n                break;\n        }\n        hdlr.name = representation.id;\n        hdlr.reserved = [0, 0, 0];\n\n        return hdlr;\n    }\n\n    function createVmhdBox(minf) {\n\n        let vmhd = ISOBoxer.createFullBox('vmhd', minf);\n\n        vmhd.flags = 1;\n\n        vmhd.graphicsmode = 0; // specifies a composition mode for this video track, from the following enumerated set, which may be extended by derived specifications: copy = 0 copy over the existing image\n        vmhd.opcolor = [0, 0, 0]; // is a set of 3 colour values (red, green, blue) available for use by graphics modes\n\n        return vmhd;\n    }\n\n    function createSmhdBox(minf) {\n\n        let smhd = ISOBoxer.createFullBox('smhd', minf);\n\n        smhd.flags = 1;\n\n        smhd.balance = 0; // is a fixed-point 8.8 number that places mono audio tracks in a stereo space; 0 is centre (the normal value); full left is -1.0 and full right is 1.0.\n        smhd.reserved = 0;\n\n        return smhd;\n    }\n\n    function createDrefBox(dinf) {\n\n        let dref = ISOBoxer.createFullBox('dref', dinf);\n\n        dref.entry_count = 1;\n        dref.entries = [];\n\n        let url = ISOBoxer.createFullBox('url ', dref, false);\n        url.location = '';\n        url.flags = 1;\n\n        dref.entries.push(url);\n\n        return dref;\n    }\n\n    function createStsdBox(stbl) {\n\n        let stsd = ISOBoxer.createFullBox('stsd', stbl);\n\n        stsd.entries = [];\n        switch (adaptationSet.type) {\n            case constants.VIDEO:\n            case constants.AUDIO:\n                stsd.entries.push(createSampleEntry(stsd));\n                break;\n            default:\n                break;\n        }\n\n        stsd.entry_count = stsd.entries.length; // is an integer that counts the actual entries\n        return stsd;\n    }\n\n    function createSampleEntry(stsd) {\n        let codec = representation.codecs.substring(0, representation.codecs.indexOf('.'));\n\n        switch (codec) {\n            case 'avc1':\n                return createAVCVisualSampleEntry(stsd, codec);\n            case 'mp4a':\n                return createMP4AudioSampleEntry(stsd, codec);\n            default:\n                throw {\n                    code: MssErrors.MSS_UNSUPPORTED_CODEC_CODE,\n                    message: MssErrors.MSS_UNSUPPORTED_CODEC_MESSAGE,\n                    data: {\n                        codec: codec\n                    }\n                };\n        }\n    }\n\n    function createAVCVisualSampleEntry(stsd, codec) {\n        let avc1;\n\n        if (contentProtection) {\n            avc1 = ISOBoxer.createBox('encv', stsd, false);\n        } else {\n            avc1 = ISOBoxer.createBox('avc1', stsd, false);\n        }\n\n        // SampleEntry fields\n        avc1.reserved1 = [0x0, 0x0, 0x0, 0x0, 0x0, 0x0];\n        avc1.data_reference_index = 1;\n\n        // VisualSampleEntry fields\n        avc1.pre_defined1 = 0;\n        avc1.reserved2 = 0;\n        avc1.pre_defined2 = [0, 0, 0];\n        avc1.height = representation.height;\n        avc1.width = representation.width;\n        avc1.horizresolution = 72; // 72 dpi\n        avc1.vertresolution = 72; // 72 dpi\n        avc1.reserved3 = 0;\n        avc1.frame_count = 1; // 1 compressed video frame per sample\n        avc1.compressorname = [\n            0x0A, 0x41, 0x56, 0x43, 0x20, 0x43, 0x6F, 0x64, // = 'AVC Coding';\n            0x69, 0x6E, 0x67, 0x00, 0x00, 0x00, 0x00, 0x00,\n            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,\n            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00\n        ];\n        avc1.depth = 0x0018; // 0x0018 – images are in colour with no alpha.\n        avc1.pre_defined3 = 65535;\n        avc1.config = createAVC1ConfigurationRecord();\n        if (contentProtection) {\n            // Create and add Protection Scheme Info Box\n            let sinf = ISOBoxer.createBox('sinf', avc1);\n\n            // Create and add Original Format Box => indicate codec type of the encrypted content\n            createOriginalFormatBox(sinf, codec);\n\n            // Create and add Scheme Type box\n            createSchemeTypeBox(sinf);\n\n            // Create and add Scheme Information Box\n            createSchemeInformationBox(sinf);\n        }\n\n        return avc1;\n    }\n\n    function createAVC1ConfigurationRecord() {\n\n        let avcC = null;\n        let avcCLength = 15; // length = 15 by default (0 SPS and 0 PPS)\n\n        // First get all SPS and PPS from codecPrivateData\n        let sps = [];\n        let pps = [];\n        let AVCProfileIndication = 0;\n        let AVCLevelIndication = 0;\n        let profile_compatibility = 0;\n\n        let nalus = representation.codecPrivateData.split('00000001').slice(1);\n        let naluBytes, naluType;\n\n        for (let i = 0; i < nalus.length; i++) {\n            naluBytes = hexStringtoBuffer(nalus[i]);\n\n            naluType = naluBytes[0] & 0x1F;\n\n            switch (naluType) {\n                case NALUTYPE_SPS:\n                    sps.push(naluBytes);\n                    avcCLength += naluBytes.length + 2; // 2 = sequenceParameterSetLength field length\n                    break;\n                case NALUTYPE_PPS:\n                    pps.push(naluBytes);\n                    avcCLength += naluBytes.length + 2; // 2 = pictureParameterSetLength field length\n                    break;\n                default:\n                    break;\n            }\n        }\n\n        // Get profile and level from SPS\n        if (sps.length > 0) {\n            AVCProfileIndication = sps[0][1];\n            profile_compatibility = sps[0][2];\n            AVCLevelIndication = sps[0][3];\n        }\n\n        // Generate avcC buffer\n        avcC = new Uint8Array(avcCLength);\n\n        let i = 0;\n        // length\n        avcC[i++] = (avcCLength & 0xFF000000) >> 24;\n        avcC[i++] = (avcCLength & 0x00FF0000) >> 16;\n        avcC[i++] = (avcCLength & 0x0000FF00) >> 8;\n        avcC[i++] = (avcCLength & 0x000000FF);\n        avcC.set([0x61, 0x76, 0x63, 0x43], i); // type = 'avcC'\n        i += 4;\n        avcC[i++] = 1; // configurationVersion = 1\n        avcC[i++] = AVCProfileIndication;\n        avcC[i++] = profile_compatibility;\n        avcC[i++] = AVCLevelIndication;\n        avcC[i++] = 0xFF; // '11111' + lengthSizeMinusOne = 3\n        avcC[i++] = 0xE0 | sps.length; // '111' + numOfSequenceParameterSets\n        for (let n = 0; n < sps.length; n++) {\n            avcC[i++] = (sps[n].length & 0xFF00) >> 8;\n            avcC[i++] = (sps[n].length & 0x00FF);\n            avcC.set(sps[n], i);\n            i += sps[n].length;\n        }\n        avcC[i++] = pps.length; // numOfPictureParameterSets\n        for (let n = 0; n < pps.length; n++) {\n            avcC[i++] = (pps[n].length & 0xFF00) >> 8;\n            avcC[i++] = (pps[n].length & 0x00FF);\n            avcC.set(pps[n], i);\n            i += pps[n].length;\n        }\n\n        return avcC;\n    }\n\n    function createMP4AudioSampleEntry(stsd, codec) {\n        let mp4a;\n\n        if (contentProtection) {\n            mp4a = ISOBoxer.createBox('enca', stsd, false);\n        } else {\n            mp4a = ISOBoxer.createBox('mp4a', stsd, false);\n        }\n\n        // SampleEntry fields\n        mp4a.reserved1 = [0x0, 0x0, 0x0, 0x0, 0x0, 0x0];\n        mp4a.data_reference_index = 1;\n\n        // AudioSampleEntry fields\n        mp4a.reserved2 = [0x0, 0x0];\n        mp4a.channelcount = representation.audioChannels;\n        mp4a.samplesize = 16;\n        mp4a.pre_defined = 0;\n        mp4a.reserved_3 = 0;\n        mp4a.samplerate = representation.audioSamplingRate << 16;\n\n        mp4a.esds = createMPEG4AACESDescriptor();\n\n        if (contentProtection) {\n            // Create and add Protection Scheme Info Box\n            let sinf = ISOBoxer.createBox('sinf', mp4a);\n\n            // Create and add Original Format Box => indicate codec type of the encrypted content\n            createOriginalFormatBox(sinf, codec);\n\n            // Create and add Scheme Type box\n            createSchemeTypeBox(sinf);\n\n            // Create and add Scheme Information Box\n            createSchemeInformationBox(sinf);\n        }\n\n        return mp4a;\n    }\n\n    function createMPEG4AACESDescriptor() {\n\n        // AudioSpecificConfig (see ISO/IEC 14496-3, subpart 1) => corresponds to hex bytes contained in 'codecPrivateData' field\n        let audioSpecificConfig = hexStringtoBuffer(representation.codecPrivateData);\n\n        // ESDS length = esds box header length (= 12) +\n        //               ES_Descriptor header length (= 5) +\n        //               DecoderConfigDescriptor header length (= 15) +\n        //               decoderSpecificInfo header length (= 2) +\n        //               AudioSpecificConfig length (= codecPrivateData length)\n        let esdsLength = 34 + audioSpecificConfig.length;\n        let esds = new Uint8Array(esdsLength);\n\n        let i = 0;\n        // esds box\n        esds[i++] = (esdsLength & 0xFF000000) >> 24; // esds box length\n        esds[i++] = (esdsLength & 0x00FF0000) >> 16; // ''\n        esds[i++] = (esdsLength & 0x0000FF00) >> 8; // ''\n        esds[i++] = (esdsLength & 0x000000FF); // ''\n        esds.set([0x65, 0x73, 0x64, 0x73], i); // type = 'esds'\n        i += 4;\n        esds.set([0, 0, 0, 0], i); // version = 0, flags = 0\n        i += 4;\n        // ES_Descriptor (see ISO/IEC 14496-1 (Systems))\n        esds[i++] = 0x03; // tag = 0x03 (ES_DescrTag)\n        esds[i++] = 20 + audioSpecificConfig.length; // size\n        esds[i++] = (trackId & 0xFF00) >> 8; // ES_ID = track_id\n        esds[i++] = (trackId & 0x00FF); // ''\n        esds[i++] = 0; // flags and streamPriority\n\n        // DecoderConfigDescriptor (see ISO/IEC 14496-1 (Systems))\n        esds[i++] = 0x04; // tag = 0x04 (DecoderConfigDescrTag)\n        esds[i++] = 15 + audioSpecificConfig.length; // size\n        esds[i++] = 0x40; // objectTypeIndication = 0x40 (MPEG-4 AAC)\n        esds[i] = 0x05 << 2; // streamType = 0x05 (Audiostream)\n        esds[i] |= 0 << 1; // upStream = 0\n        esds[i++] |= 1; // reserved = 1\n        esds[i++] = 0xFF; // buffersizeDB = undefined\n        esds[i++] = 0xFF; // ''\n        esds[i++] = 0xFF; // ''\n        esds[i++] = (representation.bandwidth & 0xFF000000) >> 24; // maxBitrate\n        esds[i++] = (representation.bandwidth & 0x00FF0000) >> 16; // ''\n        esds[i++] = (representation.bandwidth & 0x0000FF00) >> 8; // ''\n        esds[i++] = (representation.bandwidth & 0x000000FF); // ''\n        esds[i++] = (representation.bandwidth & 0xFF000000) >> 24; // avgbitrate\n        esds[i++] = (representation.bandwidth & 0x00FF0000) >> 16; // ''\n        esds[i++] = (representation.bandwidth & 0x0000FF00) >> 8; // ''\n        esds[i++] = (representation.bandwidth & 0x000000FF); // ''\n\n        // DecoderSpecificInfo (see ISO/IEC 14496-1 (Systems))\n        esds[i++] = 0x05; // tag = 0x05 (DecSpecificInfoTag)\n        esds[i++] = audioSpecificConfig.length; // size\n        esds.set(audioSpecificConfig, i); // AudioSpecificConfig bytes\n\n        return esds;\n    }\n\n    function createOriginalFormatBox(sinf, codec) {\n        let frma = ISOBoxer.createBox('frma', sinf);\n        frma.data_format = stringToCharCode(codec);\n    }\n\n    function createSchemeTypeBox(sinf) {\n        let schm = ISOBoxer.createFullBox('schm', sinf);\n\n        schm.flags = 0;\n        schm.version = 0;\n        schm.scheme_type = 0x63656E63; // 'cenc' => common encryption\n        schm.scheme_version = 0x00010000; // version set to 0x00010000 (Major version 1, Minor version 0)\n    }\n\n    function createSchemeInformationBox(sinf) {\n        let schi = ISOBoxer.createBox('schi', sinf);\n\n        // Create and add Track Encryption Box\n        createTrackEncryptionBox(schi);\n    }\n\n    function createProtectionSystemSpecificHeaderBox(moov, keySystems) {\n        let pssh_bytes,\n            pssh,\n            i,\n            parsedBuffer;\n\n        for (i = 0; i < keySystems.length; i += 1) {\n            pssh_bytes = keySystems[i].initData;\n            if (pssh_bytes) {\n                parsedBuffer = ISOBoxer.parseBuffer(pssh_bytes);\n                pssh = parsedBuffer.fetch('pssh');\n                if (pssh) {\n                    ISOBoxer.Utils.appendBox(moov, pssh);\n                }\n            }\n        }\n    }\n\n    function createTrackEncryptionBox(schi) {\n        let tenc = ISOBoxer.createFullBox('tenc', schi);\n\n        tenc.flags = 0;\n        tenc.version = 0;\n\n        tenc.default_IsEncrypted = 0x1;\n        tenc.default_IV_size = 8;\n        tenc.default_KID = (contentProtection && (contentProtection.length) > 0 && contentProtection[0]['cenc:default_KID']) ?\n            contentProtection[0]['cenc:default_KID'] : [0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0];\n    }\n\n    function createTrexBox(moov) {\n        let trex = ISOBoxer.createFullBox('trex', moov);\n\n        trex.track_ID = trackId;\n        trex.default_sample_description_index = 1;\n        trex.default_sample_duration = 0;\n        trex.default_sample_size = 0;\n        trex.default_sample_flags = 0;\n\n        return trex;\n    }\n\n    function hexStringtoBuffer(str) {\n        let buf = new Uint8Array(str.length / 2);\n        let i;\n\n        for (i = 0; i < str.length / 2; i += 1) {\n            buf[i] = parseInt('' + str[i * 2] + str[i * 2 + 1], 16);\n        }\n        return buf;\n    }\n\n    function stringToCharCode(str) {\n        let code = 0;\n        let i;\n\n        for (i = 0; i < str.length; i += 1) {\n            code |= str.charCodeAt(i) << ((str.length - i - 1) * 8);\n        }\n        return code;\n    }\n\n    function generateMoov(rep) {\n        if (!rep || !rep.adaptation) {\n            return;\n        }\n\n        let isoFile,\n            arrayBuffer;\n\n        representation = rep;\n        adaptationSet = representation.adaptation;\n\n        period = adaptationSet.period;\n        trackId = adaptationSet.index + 1;\n        contentProtection = period.mpd.manifest.Period[period.index].AdaptationSet[adaptationSet.index].ContentProtection;\n\n        timescale = period.mpd.manifest.Period[period.index].AdaptationSet[adaptationSet.index].SegmentTemplate.timescale;\n\n        isoFile = ISOBoxer.createFile();\n        createFtypBox(isoFile);\n        createMoovBox(isoFile);\n\n        arrayBuffer = isoFile.write();\n\n        return arrayBuffer;\n    }\n\n    instance = {\n        generateMoov: generateMoov\n    };\n\n    return instance;\n}\n\nMssFragmentMoovProcessor.__dashjs_factory_name = 'MssFragmentMoovProcessor';\nexport default FactoryMaker.getClassFactory(MssFragmentMoovProcessor);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport MssFragmentMoofProcessor from './MssFragmentMoofProcessor.js';\nimport MssFragmentMoovProcessor from './MssFragmentMoovProcessor.js';\nimport {HTTPRequest} from '../streaming/vo/metrics/HTTPRequest.js';\nimport FactoryMaker from '../core/FactoryMaker.js';\n\n\n// Add specific box processors not provided by codem-isoboxer library\n\nfunction arrayEqual(arr1, arr2) {\n    return (arr1.length === arr2.length) && arr1.every(function (element, index) {\n        return element === arr2[index];\n    });\n}\n\nfunction saioProcessor() {\n    this._procFullBox();\n    if (this.flags & 1) {\n        this._procField('aux_info_type', 'uint', 32);\n        this._procField('aux_info_type_parameter', 'uint', 32);\n    }\n    this._procField('entry_count', 'uint', 32);\n    this._procFieldArray('offset', this.entry_count, 'uint', (this.version === 1) ? 64 : 32);\n}\n\nfunction saizProcessor() {\n    this._procFullBox();\n    if (this.flags & 1) {\n        this._procField('aux_info_type', 'uint', 32);\n        this._procField('aux_info_type_parameter', 'uint', 32);\n    }\n    this._procField('default_sample_info_size', 'uint', 8);\n    this._procField('sample_count', 'uint', 32);\n    if (this.default_sample_info_size === 0) {\n        this._procFieldArray('sample_info_size', this.sample_count, 'uint', 8);\n    }\n}\n\nfunction sencProcessor() {\n    this._procFullBox();\n    this._procField('sample_count', 'uint', 32);\n    if (this.flags & 1) {\n        this._procField('IV_size', 'uint', 8);\n    }\n    this._procEntries('entry', this.sample_count, function (entry) {\n        this._procEntryField(entry, 'InitializationVector', 'data', 8);\n        if (this.flags & 2) {\n            this._procEntryField(entry, 'NumberOfEntries', 'uint', 16);\n            this._procSubEntries(entry, 'clearAndCryptedData', entry.NumberOfEntries, function (clearAndCryptedData) {\n                this._procEntryField(clearAndCryptedData, 'BytesOfClearData', 'uint', 16);\n                this._procEntryField(clearAndCryptedData, 'BytesOfEncryptedData', 'uint', 32);\n            });\n        }\n    });\n}\n\nfunction uuidProcessor() {\n    let tfxdUserType = [0x6D, 0x1D, 0x9B, 0x05, 0x42, 0xD5, 0x44, 0xE6, 0x80, 0xE2, 0x14, 0x1D, 0xAF, 0xF7, 0x57, 0xB2];\n    let tfrfUserType = [0xD4, 0x80, 0x7E, 0xF2, 0xCA, 0x39, 0x46, 0x95, 0x8E, 0x54, 0x26, 0xCB, 0x9E, 0x46, 0xA7, 0x9F];\n    let sepiffUserType = [0xA2, 0x39, 0x4F, 0x52, 0x5A, 0x9B, 0x4f, 0x14, 0xA2, 0x44, 0x6C, 0x42, 0x7C, 0x64, 0x8D, 0xF4];\n\n    if (arrayEqual(this.usertype, tfxdUserType)) {\n        this._procFullBox();\n        if (this._parsing) {\n            this.type = 'tfxd';\n        }\n        this._procField('fragment_absolute_time', 'uint', (this.version === 1) ? 64 : 32);\n        this._procField('fragment_duration', 'uint', (this.version === 1) ? 64 : 32);\n    }\n\n    if (arrayEqual(this.usertype, tfrfUserType)) {\n        this._procFullBox();\n        if (this._parsing) {\n            this.type = 'tfrf';\n        }\n        this._procField('fragment_count', 'uint', 8);\n        this._procEntries('entry', this.fragment_count, function (entry) {\n            this._procEntryField(entry, 'fragment_absolute_time', 'uint', (this.version === 1) ? 64 : 32);\n            this._procEntryField(entry, 'fragment_duration', 'uint', (this.version === 1) ? 64 : 32);\n        });\n    }\n\n    if (arrayEqual(this.usertype, sepiffUserType)) {\n        if (this._parsing) {\n            this.type = 'sepiff';\n        }\n        sencProcessor.call(this);\n    }\n}\n\nfunction MssFragmentProcessor(config) {\n\n    config = config || {};\n    const context = this.context;\n    const dashMetrics = config.dashMetrics;\n    const playbackController = config.playbackController;\n    const eventBus = config.eventBus;\n    const protectionController = config.protectionController;\n    const ISOBoxer = config.ISOBoxer;\n    const debug = config.debug;\n    let mssFragmentMoovProcessor,\n        mssFragmentMoofProcessor,\n        instance;\n\n    function setup() {\n        ISOBoxer.addBoxProcessor('uuid', uuidProcessor);\n        ISOBoxer.addBoxProcessor('saio', saioProcessor);\n        ISOBoxer.addBoxProcessor('saiz', saizProcessor);\n        ISOBoxer.addBoxProcessor('senc', sencProcessor);\n\n        mssFragmentMoovProcessor = MssFragmentMoovProcessor(context).create({\n            protectionController: protectionController,\n            constants: config.constants,\n            ISOBoxer: ISOBoxer});\n\n        mssFragmentMoofProcessor = MssFragmentMoofProcessor(context).create({\n            dashMetrics: dashMetrics,\n            playbackController: playbackController,\n            ISOBoxer: ISOBoxer,\n            eventBus: eventBus,\n            debug: debug,\n            errHandler: config.errHandler\n        });\n    }\n\n    function generateMoov(rep) {\n        return mssFragmentMoovProcessor.generateMoov(rep);\n    }\n\n    function processFragment(e, streamProcessor) {\n        if (!e || !e.request || !e.response) {\n            throw new Error('e parameter is missing or malformed');\n        }\n\n        if (e.request.type === 'MediaSegment') {\n            // MediaSegment => convert to Smooth Streaming moof format\n            mssFragmentMoofProcessor.convertFragment(e, streamProcessor);\n\n        } else if (e.request.type === HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE) {\n            // FragmentInfo (live) => update segments list\n            mssFragmentMoofProcessor.updateSegmentList(e, streamProcessor);\n\n            // Stop event propagation (FragmentInfo must not be added to buffer)\n            e.sender = null;\n        }\n    }\n\n    instance = {\n        generateMoov,\n        processFragment\n    };\n\n    setup();\n\n    return instance;\n}\n\nMssFragmentProcessor.__dashjs_factory_name = 'MssFragmentProcessor';\nexport default FactoryMaker.getClassFactory(MssFragmentProcessor);\n", "var bigInt = function (undefined) {\n    'use strict';\n    var BASE = 1e7, LOG_BASE = 7, MAX_INT = 9007199254740992, MAX_INT_ARR = smallToArray(MAX_INT),\n        DEFAULT_ALPHABET = '0123456789abcdefghijklmnopqrstuvwxyz';\n    var supportsNativeBigInt = typeof BigInt === 'function';\n\n    function Integer(v, radix, alphabet, caseSensitive) {\n        if (typeof v === 'undefined') return Integer[0];\n        if (typeof radix !== 'undefined') return +radix === 10 && !alphabet ? parseValue(v) : parseBase(v, radix, alphabet, caseSensitive);\n        return parseValue(v)\n    }\n\n    function BigInteger(value, sign) {\n        this.value = value;\n        this.sign = sign;\n        this.isSmall = false\n    }\n\n    BigInteger.prototype = Object.create(Integer.prototype);\n\n    function SmallInteger(value) {\n        this.value = value;\n        this.sign = value < 0;\n        this.isSmall = true\n    }\n\n    SmallInteger.prototype = Object.create(Integer.prototype);\n\n    function NativeBigInt(value) {\n        this.value = value\n    }\n\n    NativeBigInt.prototype = Object.create(Integer.prototype);\n\n    function isPrecise(n) {\n        return -MAX_INT < n && n < MAX_INT\n    }\n\n    function smallToArray(n) {\n        if (n < 1e7) return [n];\n        if (n < 1e14) return [n % 1e7, Math.floor(n / 1e7)];\n        return [n % 1e7, Math.floor(n / 1e7) % 1e7, Math.floor(n / 1e14)]\n    }\n\n    function arrayToSmall(arr) {\n        trim(arr);\n        var length = arr.length;\n        if (length < 4 && compareAbs(arr, MAX_INT_ARR) < 0) {\n            switch (length) {\n                case 0:\n                    return 0;\n                case 1:\n                    return arr[0];\n                case 2:\n                    return arr[0] + arr[1] * BASE;\n                default:\n                    return arr[0] + (arr[1] + arr[2] * BASE) * BASE\n            }\n        }\n        return arr\n    }\n\n    function trim(v) {\n        var i = v.length;\n        while (v[--i] === 0) ;\n        v.length = i + 1\n    }\n\n    function createArray(length) {\n        var x = new Array(length);\n        var i = -1;\n        while (++i < length) {\n            x[i] = 0\n        }\n        return x\n    }\n\n    function truncate(n) {\n        if (n > 0) return Math.floor(n);\n        return Math.ceil(n)\n    }\n\n    function add(a, b) {\n        var l_a = a.length, l_b = b.length, r = new Array(l_a), carry = 0, base = BASE, sum, i;\n        for (i = 0; i < l_b; i++) {\n            sum = a[i] + b[i] + carry;\n            carry = sum >= base ? 1 : 0;\n            r[i] = sum - carry * base\n        }\n        while (i < l_a) {\n            sum = a[i] + carry;\n            carry = sum === base ? 1 : 0;\n            r[i++] = sum - carry * base\n        }\n        if (carry > 0) r.push(carry);\n        return r\n    }\n\n    function addAny(a, b) {\n        if (a.length >= b.length) return add(a, b);\n        return add(b, a)\n    }\n\n    function addSmall(a, carry) {\n        var l = a.length, r = new Array(l), base = BASE, sum, i;\n        for (i = 0; i < l; i++) {\n            sum = a[i] - base + carry;\n            carry = Math.floor(sum / base);\n            r[i] = sum - carry * base;\n            carry += 1\n        }\n        while (carry > 0) {\n            r[i++] = carry % base;\n            carry = Math.floor(carry / base)\n        }\n        return r\n    }\n\n    BigInteger.prototype.add = function (v) {\n        var n = parseValue(v);\n        if (this.sign !== n.sign) {\n            return this.subtract(n.negate())\n        }\n        var a = this.value, b = n.value;\n        if (n.isSmall) {\n            return new BigInteger(addSmall(a, Math.abs(b)), this.sign)\n        }\n        return new BigInteger(addAny(a, b), this.sign)\n    };\n    BigInteger.prototype.plus = BigInteger.prototype.add;\n    SmallInteger.prototype.add = function (v) {\n        var n = parseValue(v);\n        var a = this.value;\n        if (a < 0 !== n.sign) {\n            return this.subtract(n.negate())\n        }\n        var b = n.value;\n        if (n.isSmall) {\n            if (isPrecise(a + b)) return new SmallInteger(a + b);\n            b = smallToArray(Math.abs(b))\n        }\n        return new BigInteger(addSmall(b, Math.abs(a)), a < 0)\n    };\n    SmallInteger.prototype.plus = SmallInteger.prototype.add;\n    NativeBigInt.prototype.add = function (v) {\n        return new NativeBigInt(this.value + parseValue(v).value)\n    };\n    NativeBigInt.prototype.plus = NativeBigInt.prototype.add;\n\n    function subtract(a, b) {\n        var a_l = a.length, b_l = b.length, r = new Array(a_l), borrow = 0, base = BASE, i, difference;\n        for (i = 0; i < b_l; i++) {\n            difference = a[i] - borrow - b[i];\n            if (difference < 0) {\n                difference += base;\n                borrow = 1\n            } else borrow = 0;\n            r[i] = difference\n        }\n        for (i = b_l; i < a_l; i++) {\n            difference = a[i] - borrow;\n            if (difference < 0) difference += base; else {\n                r[i++] = difference;\n                break\n            }\n            r[i] = difference\n        }\n        for (; i < a_l; i++) {\n            r[i] = a[i]\n        }\n        trim(r);\n        return r\n    }\n\n    function subtractAny(a, b, sign) {\n        var value;\n        if (compareAbs(a, b) >= 0) {\n            value = subtract(a, b)\n        } else {\n            value = subtract(b, a);\n            sign = !sign\n        }\n        value = arrayToSmall(value);\n        if (typeof value === 'number') {\n            if (sign) value = -value;\n            return new SmallInteger(value)\n        }\n        return new BigInteger(value, sign)\n    }\n\n    function subtractSmall(a, b, sign) {\n        var l = a.length, r = new Array(l), carry = -b, base = BASE, i, difference;\n        for (i = 0; i < l; i++) {\n            difference = a[i] + carry;\n            carry = Math.floor(difference / base);\n            difference %= base;\n            r[i] = difference < 0 ? difference + base : difference\n        }\n        r = arrayToSmall(r);\n        if (typeof r === 'number') {\n            if (sign) r = -r;\n            return new SmallInteger(r)\n        }\n        return new BigInteger(r, sign)\n    }\n\n    BigInteger.prototype.subtract = function (v) {\n        var n = parseValue(v);\n        if (this.sign !== n.sign) {\n            return this.add(n.negate())\n        }\n        var a = this.value, b = n.value;\n        if (n.isSmall) return subtractSmall(a, Math.abs(b), this.sign);\n        return subtractAny(a, b, this.sign)\n    };\n    BigInteger.prototype.minus = BigInteger.prototype.subtract;\n    SmallInteger.prototype.subtract = function (v) {\n        var n = parseValue(v);\n        var a = this.value;\n        if (a < 0 !== n.sign) {\n            return this.add(n.negate())\n        }\n        var b = n.value;\n        if (n.isSmall) {\n            return new SmallInteger(a - b)\n        }\n        return subtractSmall(b, Math.abs(a), a >= 0)\n    };\n    SmallInteger.prototype.minus = SmallInteger.prototype.subtract;\n    NativeBigInt.prototype.subtract = function (v) {\n        return new NativeBigInt(this.value - parseValue(v).value)\n    };\n    NativeBigInt.prototype.minus = NativeBigInt.prototype.subtract;\n    BigInteger.prototype.negate = function () {\n        return new BigInteger(this.value, !this.sign)\n    };\n    SmallInteger.prototype.negate = function () {\n        var sign = this.sign;\n        var small = new SmallInteger(-this.value);\n        small.sign = !sign;\n        return small\n    };\n    NativeBigInt.prototype.negate = function () {\n        return new NativeBigInt(-this.value)\n    };\n    BigInteger.prototype.abs = function () {\n        return new BigInteger(this.value, false)\n    };\n    SmallInteger.prototype.abs = function () {\n        return new SmallInteger(Math.abs(this.value))\n    };\n    NativeBigInt.prototype.abs = function () {\n        return new NativeBigInt(this.value >= 0 ? this.value : -this.value)\n    };\n\n    function multiplyLong(a, b) {\n        var a_l = a.length, b_l = b.length, l = a_l + b_l, r = createArray(l), base = BASE, product, carry, i, a_i, b_j;\n        for (i = 0; i < a_l; ++i) {\n            a_i = a[i];\n            for (var j = 0; j < b_l; ++j) {\n                b_j = b[j];\n                product = a_i * b_j + r[i + j];\n                carry = Math.floor(product / base);\n                r[i + j] = product - carry * base;\n                r[i + j + 1] += carry\n            }\n        }\n        trim(r);\n        return r\n    }\n\n    function multiplySmall(a, b) {\n        var l = a.length, r = new Array(l), base = BASE, carry = 0, product, i;\n        for (i = 0; i < l; i++) {\n            product = a[i] * b + carry;\n            carry = Math.floor(product / base);\n            r[i] = product - carry * base\n        }\n        while (carry > 0) {\n            r[i++] = carry % base;\n            carry = Math.floor(carry / base)\n        }\n        return r\n    }\n\n    function shiftLeft(x, n) {\n        var r = [];\n        while (n-- > 0) r.push(0);\n        return r.concat(x)\n    }\n\n    function multiplyKaratsuba(x, y) {\n        var n = Math.max(x.length, y.length);\n        if (n <= 30) return multiplyLong(x, y);\n        n = Math.ceil(n / 2);\n        var b = x.slice(n), a = x.slice(0, n), d = y.slice(n), c = y.slice(0, n);\n        var ac = multiplyKaratsuba(a, c), bd = multiplyKaratsuba(b, d),\n            abcd = multiplyKaratsuba(addAny(a, b), addAny(c, d));\n        var product = addAny(addAny(ac, shiftLeft(subtract(subtract(abcd, ac), bd), n)), shiftLeft(bd, 2 * n));\n        trim(product);\n        return product\n    }\n\n    function useKaratsuba(l1, l2) {\n        return -.012 * l1 - .012 * l2 + 15e-6 * l1 * l2 > 0\n    }\n\n    BigInteger.prototype.multiply = function (v) {\n        var n = parseValue(v), a = this.value, b = n.value, sign = this.sign !== n.sign, abs;\n        if (n.isSmall) {\n            if (b === 0) return Integer[0];\n            if (b === 1) return this;\n            if (b === -1) return this.negate();\n            abs = Math.abs(b);\n            if (abs < BASE) {\n                return new BigInteger(multiplySmall(a, abs), sign)\n            }\n            b = smallToArray(abs)\n        }\n        if (useKaratsuba(a.length, b.length)) return new BigInteger(multiplyKaratsuba(a, b), sign);\n        return new BigInteger(multiplyLong(a, b), sign)\n    };\n    BigInteger.prototype.times = BigInteger.prototype.multiply;\n\n    function multiplySmallAndArray(a, b, sign) {\n        if (a < BASE) {\n            return new BigInteger(multiplySmall(b, a), sign)\n        }\n        return new BigInteger(multiplyLong(b, smallToArray(a)), sign)\n    }\n\n    SmallInteger.prototype._multiplyBySmall = function (a) {\n        if (isPrecise(a.value * this.value)) {\n            return new SmallInteger(a.value * this.value)\n        }\n        return multiplySmallAndArray(Math.abs(a.value), smallToArray(Math.abs(this.value)), this.sign !== a.sign)\n    };\n    BigInteger.prototype._multiplyBySmall = function (a) {\n        if (a.value === 0) return Integer[0];\n        if (a.value === 1) return this;\n        if (a.value === -1) return this.negate();\n        return multiplySmallAndArray(Math.abs(a.value), this.value, this.sign !== a.sign)\n    };\n    SmallInteger.prototype.multiply = function (v) {\n        return parseValue(v)._multiplyBySmall(this)\n    };\n    SmallInteger.prototype.times = SmallInteger.prototype.multiply;\n    NativeBigInt.prototype.multiply = function (v) {\n        return new NativeBigInt(this.value * parseValue(v).value)\n    };\n    NativeBigInt.prototype.times = NativeBigInt.prototype.multiply;\n\n    function square(a) {\n        var l = a.length, r = createArray(l + l), base = BASE, product, carry, i, a_i, a_j;\n        for (i = 0; i < l; i++) {\n            a_i = a[i];\n            carry = 0 - a_i * a_i;\n            for (var j = i; j < l; j++) {\n                a_j = a[j];\n                product = 2 * (a_i * a_j) + r[i + j] + carry;\n                carry = Math.floor(product / base);\n                r[i + j] = product - carry * base\n            }\n            r[i + l] = carry\n        }\n        trim(r);\n        return r\n    }\n\n    BigInteger.prototype.square = function () {\n        return new BigInteger(square(this.value), false)\n    };\n    SmallInteger.prototype.square = function () {\n        var value = this.value * this.value;\n        if (isPrecise(value)) return new SmallInteger(value);\n        return new BigInteger(square(smallToArray(Math.abs(this.value))), false)\n    };\n    NativeBigInt.prototype.square = function (v) {\n        return new NativeBigInt(this.value * this.value)\n    };\n\n    function divMod1(a, b) {\n        var a_l = a.length, b_l = b.length, base = BASE, result = createArray(b.length),\n            divisorMostSignificantDigit = b[b_l - 1], lambda = Math.ceil(base / (2 * divisorMostSignificantDigit)),\n            remainder = multiplySmall(a, lambda), divisor = multiplySmall(b, lambda), quotientDigit, shift, carry,\n            borrow, i, l, q;\n        if (remainder.length <= a_l) remainder.push(0);\n        divisor.push(0);\n        divisorMostSignificantDigit = divisor[b_l - 1];\n        for (shift = a_l - b_l; shift >= 0; shift--) {\n            quotientDigit = base - 1;\n            if (remainder[shift + b_l] !== divisorMostSignificantDigit) {\n                quotientDigit = Math.floor((remainder[shift + b_l] * base + remainder[shift + b_l - 1]) / divisorMostSignificantDigit)\n            }\n            carry = 0;\n            borrow = 0;\n            l = divisor.length;\n            for (i = 0; i < l; i++) {\n                carry += quotientDigit * divisor[i];\n                q = Math.floor(carry / base);\n                borrow += remainder[shift + i] - (carry - q * base);\n                carry = q;\n                if (borrow < 0) {\n                    remainder[shift + i] = borrow + base;\n                    borrow = -1\n                } else {\n                    remainder[shift + i] = borrow;\n                    borrow = 0\n                }\n            }\n            while (borrow !== 0) {\n                quotientDigit -= 1;\n                carry = 0;\n                for (i = 0; i < l; i++) {\n                    carry += remainder[shift + i] - base + divisor[i];\n                    if (carry < 0) {\n                        remainder[shift + i] = carry + base;\n                        carry = 0\n                    } else {\n                        remainder[shift + i] = carry;\n                        carry = 1\n                    }\n                }\n                borrow += carry\n            }\n            result[shift] = quotientDigit\n        }\n        remainder = divModSmall(remainder, lambda)[0];\n        return [arrayToSmall(result), arrayToSmall(remainder)]\n    }\n\n    function divMod2(a, b) {\n        var a_l = a.length, b_l = b.length, result = [], part = [], base = BASE, guess, xlen, highx, highy, check;\n        while (a_l) {\n            part.unshift(a[--a_l]);\n            trim(part);\n            if (compareAbs(part, b) < 0) {\n                result.push(0);\n                continue\n            }\n            xlen = part.length;\n            highx = part[xlen - 1] * base + part[xlen - 2];\n            highy = b[b_l - 1] * base + b[b_l - 2];\n            if (xlen > b_l) {\n                highx = (highx + 1) * base\n            }\n            guess = Math.ceil(highx / highy);\n            do {\n                check = multiplySmall(b, guess);\n                if (compareAbs(check, part) <= 0) break;\n                guess--\n            } while (guess);\n            result.push(guess);\n            part = subtract(part, check)\n        }\n        result.reverse();\n        return [arrayToSmall(result), arrayToSmall(part)]\n    }\n\n    function divModSmall(value, lambda) {\n        var length = value.length, quotient = createArray(length), base = BASE, i, q, remainder, divisor;\n        remainder = 0;\n        for (i = length - 1; i >= 0; --i) {\n            divisor = remainder * base + value[i];\n            q = truncate(divisor / lambda);\n            remainder = divisor - q * lambda;\n            quotient[i] = q | 0\n        }\n        return [quotient, remainder | 0]\n    }\n\n    function divModAny(self, v) {\n        var value, n = parseValue(v);\n        if (supportsNativeBigInt) {\n            return [new NativeBigInt(self.value / n.value), new NativeBigInt(self.value % n.value)]\n        }\n        var a = self.value, b = n.value;\n        var quotient;\n        if (b === 0) throw new Error('Cannot divide by zero');\n        if (self.isSmall) {\n            if (n.isSmall) {\n                return [new SmallInteger(truncate(a / b)), new SmallInteger(a % b)]\n            }\n            return [Integer[0], self]\n        }\n        if (n.isSmall) {\n            if (b === 1) return [self, Integer[0]];\n            if (b == -1) return [self.negate(), Integer[0]];\n            var abs = Math.abs(b);\n            if (abs < BASE) {\n                value = divModSmall(a, abs);\n                quotient = arrayToSmall(value[0]);\n                var remainder = value[1];\n                if (self.sign) remainder = -remainder;\n                if (typeof quotient === 'number') {\n                    if (self.sign !== n.sign) quotient = -quotient;\n                    return [new SmallInteger(quotient), new SmallInteger(remainder)]\n                }\n                return [new BigInteger(quotient, self.sign !== n.sign), new SmallInteger(remainder)]\n            }\n            b = smallToArray(abs)\n        }\n        var comparison = compareAbs(a, b);\n        if (comparison === -1) return [Integer[0], self];\n        if (comparison === 0) return [Integer[self.sign === n.sign ? 1 : -1], Integer[0]];\n        if (a.length + b.length <= 200) value = divMod1(a, b); else value = divMod2(a, b);\n        quotient = value[0];\n        var qSign = self.sign !== n.sign, mod = value[1], mSign = self.sign;\n        if (typeof quotient === 'number') {\n            if (qSign) quotient = -quotient;\n            quotient = new SmallInteger(quotient)\n        } else quotient = new BigInteger(quotient, qSign);\n        if (typeof mod === 'number') {\n            if (mSign) mod = -mod;\n            mod = new SmallInteger(mod)\n        } else mod = new BigInteger(mod, mSign);\n        return [quotient, mod]\n    }\n\n    BigInteger.prototype.divmod = function (v) {\n        var result = divModAny(this, v);\n        return { quotient: result[0], remainder: result[1] }\n    };\n    NativeBigInt.prototype.divmod = SmallInteger.prototype.divmod = BigInteger.prototype.divmod;\n    BigInteger.prototype.divide = function (v) {\n        return divModAny(this, v)[0]\n    };\n    NativeBigInt.prototype.over = NativeBigInt.prototype.divide = function (v) {\n        return new NativeBigInt(this.value / parseValue(v).value)\n    };\n    SmallInteger.prototype.over = SmallInteger.prototype.divide = BigInteger.prototype.over = BigInteger.prototype.divide;\n    BigInteger.prototype.mod = function (v) {\n        return divModAny(this, v)[1]\n    };\n    NativeBigInt.prototype.mod = NativeBigInt.prototype.remainder = function (v) {\n        return new NativeBigInt(this.value % parseValue(v).value)\n    };\n    SmallInteger.prototype.remainder = SmallInteger.prototype.mod = BigInteger.prototype.remainder = BigInteger.prototype.mod;\n    BigInteger.prototype.pow = function (v) {\n        var n = parseValue(v), a = this.value, b = n.value, value, x, y;\n        if (b === 0) return Integer[1];\n        if (a === 0) return Integer[0];\n        if (a === 1) return Integer[1];\n        if (a === -1) return n.isEven() ? Integer[1] : Integer[-1];\n        if (n.sign) {\n            return Integer[0]\n        }\n        if (!n.isSmall) throw new Error('The exponent ' + n.toString() + ' is too large.');\n        if (this.isSmall) {\n            if (isPrecise(value = Math.pow(a, b))) return new SmallInteger(truncate(value))\n        }\n        x = this;\n        y = Integer[1];\n        while (true) {\n            if (b & 1 === 1) {\n                y = y.times(x);\n                --b\n            }\n            if (b === 0) break;\n            b /= 2;\n            x = x.square()\n        }\n        return y\n    };\n    SmallInteger.prototype.pow = BigInteger.prototype.pow;\n    NativeBigInt.prototype.pow = function (v) {\n        var n = parseValue(v);\n        var a = this.value, b = n.value;\n        var _0 = BigInt(0), _1 = BigInt(1), _2 = BigInt(2);\n        if (b === _0) return Integer[1];\n        if (a === _0) return Integer[0];\n        if (a === _1) return Integer[1];\n        if (a === BigInt(-1)) return n.isEven() ? Integer[1] : Integer[-1];\n        if (n.isNegative()) return new NativeBigInt(_0);\n        var x = this;\n        var y = Integer[1];\n        while (true) {\n            if ((b & _1) === _1) {\n                y = y.times(x);\n                --b\n            }\n            if (b === _0) break;\n            b /= _2;\n            x = x.square()\n        }\n        return y\n    };\n    BigInteger.prototype.modPow = function (exp, mod) {\n        exp = parseValue(exp);\n        mod = parseValue(mod);\n        if (mod.isZero()) throw new Error('Cannot take modPow with modulus 0');\n        var r = Integer[1], base = this.mod(mod);\n        while (exp.isPositive()) {\n            if (base.isZero()) return Integer[0];\n            if (exp.isOdd()) r = r.multiply(base).mod(mod);\n            exp = exp.divide(2);\n            base = base.square().mod(mod)\n        }\n        return r\n    };\n    NativeBigInt.prototype.modPow = SmallInteger.prototype.modPow = BigInteger.prototype.modPow;\n\n    function compareAbs(a, b) {\n        if (a.length !== b.length) {\n            return a.length > b.length ? 1 : -1\n        }\n        for (var i = a.length - 1; i >= 0; i--) {\n            if (a[i] !== b[i]) return a[i] > b[i] ? 1 : -1\n        }\n        return 0\n    }\n\n    BigInteger.prototype.compareAbs = function (v) {\n        var n = parseValue(v), a = this.value, b = n.value;\n        if (n.isSmall) return 1;\n        return compareAbs(a, b)\n    };\n    SmallInteger.prototype.compareAbs = function (v) {\n        var n = parseValue(v), a = Math.abs(this.value), b = n.value;\n        if (n.isSmall) {\n            b = Math.abs(b);\n            return a === b ? 0 : a > b ? 1 : -1\n        }\n        return -1\n    };\n    NativeBigInt.prototype.compareAbs = function (v) {\n        var a = this.value;\n        var b = parseValue(v).value;\n        a = a >= 0 ? a : -a;\n        b = b >= 0 ? b : -b;\n        return a === b ? 0 : a > b ? 1 : -1\n    };\n    BigInteger.prototype.compare = function (v) {\n        if (v === Infinity) {\n            return -1\n        }\n        if (v === -Infinity) {\n            return 1\n        }\n        var n = parseValue(v), a = this.value, b = n.value;\n        if (this.sign !== n.sign) {\n            return n.sign ? 1 : -1\n        }\n        if (n.isSmall) {\n            return this.sign ? -1 : 1\n        }\n        return compareAbs(a, b) * (this.sign ? -1 : 1)\n    };\n    BigInteger.prototype.compareTo = BigInteger.prototype.compare;\n    SmallInteger.prototype.compare = function (v) {\n        if (v === Infinity) {\n            return -1\n        }\n        if (v === -Infinity) {\n            return 1\n        }\n        var n = parseValue(v), a = this.value, b = n.value;\n        if (n.isSmall) {\n            return a == b ? 0 : a > b ? 1 : -1\n        }\n        if (a < 0 !== n.sign) {\n            return a < 0 ? -1 : 1\n        }\n        return a < 0 ? 1 : -1\n    };\n    SmallInteger.prototype.compareTo = SmallInteger.prototype.compare;\n    NativeBigInt.prototype.compare = function (v) {\n        if (v === Infinity) {\n            return -1\n        }\n        if (v === -Infinity) {\n            return 1\n        }\n        var a = this.value;\n        var b = parseValue(v).value;\n        return a === b ? 0 : a > b ? 1 : -1\n    };\n    NativeBigInt.prototype.compareTo = NativeBigInt.prototype.compare;\n    BigInteger.prototype.equals = function (v) {\n        return this.compare(v) === 0\n    };\n    NativeBigInt.prototype.eq = NativeBigInt.prototype.equals = SmallInteger.prototype.eq = SmallInteger.prototype.equals = BigInteger.prototype.eq = BigInteger.prototype.equals;\n    BigInteger.prototype.notEquals = function (v) {\n        return this.compare(v) !== 0\n    };\n    NativeBigInt.prototype.neq = NativeBigInt.prototype.notEquals = SmallInteger.prototype.neq = SmallInteger.prototype.notEquals = BigInteger.prototype.neq = BigInteger.prototype.notEquals;\n    BigInteger.prototype.greater = function (v) {\n        return this.compare(v) > 0\n    };\n    NativeBigInt.prototype.gt = NativeBigInt.prototype.greater = SmallInteger.prototype.gt = SmallInteger.prototype.greater = BigInteger.prototype.gt = BigInteger.prototype.greater;\n    BigInteger.prototype.lesser = function (v) {\n        return this.compare(v) < 0\n    };\n    NativeBigInt.prototype.lt = NativeBigInt.prototype.lesser = SmallInteger.prototype.lt = SmallInteger.prototype.lesser = BigInteger.prototype.lt = BigInteger.prototype.lesser;\n    BigInteger.prototype.greaterOrEquals = function (v) {\n        return this.compare(v) >= 0\n    };\n    NativeBigInt.prototype.geq = NativeBigInt.prototype.greaterOrEquals = SmallInteger.prototype.geq = SmallInteger.prototype.greaterOrEquals = BigInteger.prototype.geq = BigInteger.prototype.greaterOrEquals;\n    BigInteger.prototype.lesserOrEquals = function (v) {\n        return this.compare(v) <= 0\n    };\n    NativeBigInt.prototype.leq = NativeBigInt.prototype.lesserOrEquals = SmallInteger.prototype.leq = SmallInteger.prototype.lesserOrEquals = BigInteger.prototype.leq = BigInteger.prototype.lesserOrEquals;\n    BigInteger.prototype.isEven = function () {\n        return (this.value[0] & 1) === 0\n    };\n    SmallInteger.prototype.isEven = function () {\n        return (this.value & 1) === 0\n    };\n    NativeBigInt.prototype.isEven = function () {\n        return (this.value & BigInt(1)) === BigInt(0)\n    };\n    BigInteger.prototype.isOdd = function () {\n        return (this.value[0] & 1) === 1\n    };\n    SmallInteger.prototype.isOdd = function () {\n        return (this.value & 1) === 1\n    };\n    NativeBigInt.prototype.isOdd = function () {\n        return (this.value & BigInt(1)) === BigInt(1)\n    };\n    BigInteger.prototype.isPositive = function () {\n        return !this.sign\n    };\n    SmallInteger.prototype.isPositive = function () {\n        return this.value > 0\n    };\n    NativeBigInt.prototype.isPositive = SmallInteger.prototype.isPositive;\n    BigInteger.prototype.isNegative = function () {\n        return this.sign\n    };\n    SmallInteger.prototype.isNegative = function () {\n        return this.value < 0\n    };\n    NativeBigInt.prototype.isNegative = SmallInteger.prototype.isNegative;\n    BigInteger.prototype.isUnit = function () {\n        return false\n    };\n    SmallInteger.prototype.isUnit = function () {\n        return Math.abs(this.value) === 1\n    };\n    NativeBigInt.prototype.isUnit = function () {\n        return this.abs().value === BigInt(1)\n    };\n    BigInteger.prototype.isZero = function () {\n        return false\n    };\n    SmallInteger.prototype.isZero = function () {\n        return this.value === 0\n    };\n    NativeBigInt.prototype.isZero = function () {\n        return this.value === BigInt(0)\n    };\n    BigInteger.prototype.isDivisibleBy = function (v) {\n        var n = parseValue(v);\n        if (n.isZero()) return false;\n        if (n.isUnit()) return true;\n        if (n.compareAbs(2) === 0) return this.isEven();\n        return this.mod(n).isZero()\n    };\n    NativeBigInt.prototype.isDivisibleBy = SmallInteger.prototype.isDivisibleBy = BigInteger.prototype.isDivisibleBy;\n\n    function isBasicPrime(v) {\n        var n = v.abs();\n        if (n.isUnit()) return false;\n        if (n.equals(2) || n.equals(3) || n.equals(5)) return true;\n        if (n.isEven() || n.isDivisibleBy(3) || n.isDivisibleBy(5)) return false;\n        if (n.lesser(49)) return true\n    }\n\n    function millerRabinTest(n, a) {\n        var nPrev = n.prev(), b = nPrev, r = 0, d, t, i, x;\n        while (b.isEven()) b = b.divide(2), r++;\n        next:for (i = 0; i < a.length; i++) {\n            if (n.lesser(a[i])) continue;\n            x = bigInt(a[i]).modPow(b, n);\n            if (x.isUnit() || x.equals(nPrev)) continue;\n            for (d = r - 1; d != 0; d--) {\n                x = x.square().mod(n);\n                if (x.isUnit()) return false;\n                if (x.equals(nPrev)) continue next\n            }\n            return false\n        }\n        return true\n    }\n\n    BigInteger.prototype.isPrime = function (strict) {\n        var isPrime = isBasicPrime(this);\n        if (isPrime !== undefined) return isPrime;\n        var n = this.abs();\n        var bits = n.bitLength();\n        if (bits <= 64) return millerRabinTest(n, [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37]);\n        var logN = Math.log(2) * bits.toJSNumber();\n        var t = Math.ceil(strict === true ? 2 * Math.pow(logN, 2) : logN);\n        for (var a = [], i = 0; i < t; i++) {\n            a.push(bigInt(i + 2))\n        }\n        return millerRabinTest(n, a)\n    };\n    NativeBigInt.prototype.isPrime = SmallInteger.prototype.isPrime = BigInteger.prototype.isPrime;\n    BigInteger.prototype.isProbablePrime = function (iterations) {\n        var isPrime = isBasicPrime(this);\n        if (isPrime !== undefined) return isPrime;\n        var n = this.abs();\n        var t = iterations === undefined ? 5 : iterations;\n        for (var a = [], i = 0; i < t; i++) {\n            a.push(bigInt.randBetween(2, n.minus(2)))\n        }\n        return millerRabinTest(n, a)\n    };\n    NativeBigInt.prototype.isProbablePrime = SmallInteger.prototype.isProbablePrime = BigInteger.prototype.isProbablePrime;\n    BigInteger.prototype.modInv = function (n) {\n        var t = bigInt.zero, newT = bigInt.one, r = parseValue(n), newR = this.abs(), q, lastT, lastR;\n        while (!newR.isZero()) {\n            q = r.divide(newR);\n            lastT = t;\n            lastR = r;\n            t = newT;\n            r = newR;\n            newT = lastT.subtract(q.multiply(newT));\n            newR = lastR.subtract(q.multiply(newR))\n        }\n        if (!r.isUnit()) throw new Error(this.toString() + ' and ' + n.toString() + ' are not co-prime');\n        if (t.compare(0) === -1) {\n            t = t.add(n)\n        }\n        if (this.isNegative()) {\n            return t.negate()\n        }\n        return t\n    };\n    NativeBigInt.prototype.modInv = SmallInteger.prototype.modInv = BigInteger.prototype.modInv;\n    BigInteger.prototype.next = function () {\n        var value = this.value;\n        if (this.sign) {\n            return subtractSmall(value, 1, this.sign)\n        }\n        return new BigInteger(addSmall(value, 1), this.sign)\n    };\n    SmallInteger.prototype.next = function () {\n        var value = this.value;\n        if (value + 1 < MAX_INT) return new SmallInteger(value + 1);\n        return new BigInteger(MAX_INT_ARR, false)\n    };\n    NativeBigInt.prototype.next = function () {\n        return new NativeBigInt(this.value + BigInt(1))\n    };\n    BigInteger.prototype.prev = function () {\n        var value = this.value;\n        if (this.sign) {\n            return new BigInteger(addSmall(value, 1), true)\n        }\n        return subtractSmall(value, 1, this.sign)\n    };\n    SmallInteger.prototype.prev = function () {\n        var value = this.value;\n        if (value - 1 > -MAX_INT) return new SmallInteger(value - 1);\n        return new BigInteger(MAX_INT_ARR, true)\n    };\n    NativeBigInt.prototype.prev = function () {\n        return new NativeBigInt(this.value - BigInt(1))\n    };\n    var powersOfTwo = [1];\n    while (2 * powersOfTwo[powersOfTwo.length - 1] <= BASE) powersOfTwo.push(2 * powersOfTwo[powersOfTwo.length - 1]);\n    var powers2Length = powersOfTwo.length, highestPower2 = powersOfTwo[powers2Length - 1];\n\n    function shift_isSmall(n) {\n        return Math.abs(n) <= BASE\n    }\n\n    BigInteger.prototype.shiftLeft = function (v) {\n        var n = parseValue(v).toJSNumber();\n        if (!shift_isSmall(n)) {\n            throw new Error(String(n) + ' is too large for shifting.')\n        }\n        if (n < 0) return this.shiftRight(-n);\n        var result = this;\n        if (result.isZero()) return result;\n        while (n >= powers2Length) {\n            result = result.multiply(highestPower2);\n            n -= powers2Length - 1\n        }\n        return result.multiply(powersOfTwo[n])\n    };\n    NativeBigInt.prototype.shiftLeft = SmallInteger.prototype.shiftLeft = BigInteger.prototype.shiftLeft;\n    BigInteger.prototype.shiftRight = function (v) {\n        var remQuo;\n        var n = parseValue(v).toJSNumber();\n        if (!shift_isSmall(n)) {\n            throw new Error(String(n) + ' is too large for shifting.')\n        }\n        if (n < 0) return this.shiftLeft(-n);\n        var result = this;\n        while (n >= powers2Length) {\n            if (result.isZero() || result.isNegative() && result.isUnit()) return result;\n            remQuo = divModAny(result, highestPower2);\n            result = remQuo[1].isNegative() ? remQuo[0].prev() : remQuo[0];\n            n -= powers2Length - 1\n        }\n        remQuo = divModAny(result, powersOfTwo[n]);\n        return remQuo[1].isNegative() ? remQuo[0].prev() : remQuo[0]\n    };\n    NativeBigInt.prototype.shiftRight = SmallInteger.prototype.shiftRight = BigInteger.prototype.shiftRight;\n\n    function bitwise(x, y, fn) {\n        y = parseValue(y);\n        var xSign = x.isNegative(), ySign = y.isNegative();\n        var xRem = xSign ? x.not() : x, yRem = ySign ? y.not() : y;\n        var xDigit = 0, yDigit = 0;\n        var xDivMod = null, yDivMod = null;\n        var result = [];\n        while (!xRem.isZero() || !yRem.isZero()) {\n            xDivMod = divModAny(xRem, highestPower2);\n            xDigit = xDivMod[1].toJSNumber();\n            if (xSign) {\n                xDigit = highestPower2 - 1 - xDigit\n            }\n            yDivMod = divModAny(yRem, highestPower2);\n            yDigit = yDivMod[1].toJSNumber();\n            if (ySign) {\n                yDigit = highestPower2 - 1 - yDigit\n            }\n            xRem = xDivMod[0];\n            yRem = yDivMod[0];\n            result.push(fn(xDigit, yDigit))\n        }\n        var sum = fn(xSign ? 1 : 0, ySign ? 1 : 0) !== 0 ? bigInt(-1) : bigInt(0);\n        for (var i = result.length - 1; i >= 0; i -= 1) {\n            sum = sum.multiply(highestPower2).add(bigInt(result[i]))\n        }\n        return sum\n    }\n\n    BigInteger.prototype.not = function () {\n        return this.negate().prev()\n    };\n    NativeBigInt.prototype.not = SmallInteger.prototype.not = BigInteger.prototype.not;\n    BigInteger.prototype.and = function (n) {\n        return bitwise(this, n, function (a, b) {\n            return a & b\n        })\n    };\n    NativeBigInt.prototype.and = SmallInteger.prototype.and = BigInteger.prototype.and;\n    BigInteger.prototype.or = function (n) {\n        return bitwise(this, n, function (a, b) {\n            return a | b\n        })\n    };\n    NativeBigInt.prototype.or = SmallInteger.prototype.or = BigInteger.prototype.or;\n    BigInteger.prototype.xor = function (n) {\n        return bitwise(this, n, function (a, b) {\n            return a ^ b\n        })\n    };\n    NativeBigInt.prototype.xor = SmallInteger.prototype.xor = BigInteger.prototype.xor;\n    var LOBMASK_I = 1 << 30, LOBMASK_BI = (BASE & -BASE) * (BASE & -BASE) | LOBMASK_I;\n\n    function roughLOB(n) {\n        var v = n.value,\n            x = typeof v === 'number' ? v | LOBMASK_I : typeof v === 'bigint' ? v | BigInt(LOBMASK_I) : v[0] + v[1] * BASE | LOBMASK_BI;\n        return x & -x\n    }\n\n    function integerLogarithm(value, base) {\n        if (base.compareTo(value) <= 0) {\n            var tmp = integerLogarithm(value, base.square(base));\n            var p = tmp.p;\n            var e = tmp.e;\n            var t = p.multiply(base);\n            return t.compareTo(value) <= 0 ? { p: t, e: e * 2 + 1 } : { p: p, e: e * 2 }\n        }\n        return { p: bigInt(1), e: 0 }\n    }\n\n    BigInteger.prototype.bitLength = function () {\n        var n = this;\n        if (n.compareTo(bigInt(0)) < 0) {\n            n = n.negate().subtract(bigInt(1))\n        }\n        if (n.compareTo(bigInt(0)) === 0) {\n            return bigInt(0)\n        }\n        return bigInt(integerLogarithm(n, bigInt(2)).e).add(bigInt(1))\n    };\n    NativeBigInt.prototype.bitLength = SmallInteger.prototype.bitLength = BigInteger.prototype.bitLength;\n\n    function max(a, b) {\n        a = parseValue(a);\n        b = parseValue(b);\n        return a.greater(b) ? a : b\n    }\n\n    function min(a, b) {\n        a = parseValue(a);\n        b = parseValue(b);\n        return a.lesser(b) ? a : b\n    }\n\n    function gcd(a, b) {\n        a = parseValue(a).abs();\n        b = parseValue(b).abs();\n        if (a.equals(b)) return a;\n        if (a.isZero()) return b;\n        if (b.isZero()) return a;\n        var c = Integer[1], d, t;\n        while (a.isEven() && b.isEven()) {\n            d = min(roughLOB(a), roughLOB(b));\n            a = a.divide(d);\n            b = b.divide(d);\n            c = c.multiply(d)\n        }\n        while (a.isEven()) {\n            a = a.divide(roughLOB(a))\n        }\n        do {\n            while (b.isEven()) {\n                b = b.divide(roughLOB(b))\n            }\n            if (a.greater(b)) {\n                t = b;\n                b = a;\n                a = t\n            }\n            b = b.subtract(a)\n        } while (!b.isZero());\n        return c.isUnit() ? a : a.multiply(c)\n    }\n\n    function lcm(a, b) {\n        a = parseValue(a).abs();\n        b = parseValue(b).abs();\n        return a.divide(gcd(a, b)).multiply(b)\n    }\n\n    function randBetween(a, b) {\n        a = parseValue(a);\n        b = parseValue(b);\n        var low = min(a, b), high = max(a, b);\n        var range = high.subtract(low).add(1);\n        if (range.isSmall) return low.add(Math.floor(Math.random() * range));\n        var digits = toBase(range, BASE).value;\n        var result = [], restricted = true;\n        for (var i = 0; i < digits.length; i++) {\n            var top = restricted ? digits[i] : BASE;\n            var digit = truncate(Math.random() * top);\n            result.push(digit);\n            if (digit < top) restricted = false\n        }\n        return low.add(Integer.fromArray(result, BASE, false))\n    }\n\n    var parseBase = function (text, base, alphabet, caseSensitive) {\n        alphabet = alphabet || DEFAULT_ALPHABET;\n        text = String(text);\n        if (!caseSensitive) {\n            text = text.toLowerCase();\n            alphabet = alphabet.toLowerCase()\n        }\n        var length = text.length;\n        var i;\n        var absBase = Math.abs(base);\n        var alphabetValues = {};\n        for (i = 0; i < alphabet.length; i++) {\n            alphabetValues[alphabet[i]] = i\n        }\n        for (i = 0; i < length; i++) {\n            var c = text[i];\n            if (c === '-') continue;\n            if (c in alphabetValues) {\n                if (alphabetValues[c] >= absBase) {\n                    if (c === '1' && absBase === 1) continue;\n                    throw new Error(c + ' is not a valid digit in base ' + base + '.')\n                }\n            }\n        }\n        base = parseValue(base);\n        var digits = [];\n        var isNegative = text[0] === '-';\n        for (i = isNegative ? 1 : 0; i < text.length; i++) {\n            var c = text[i];\n            if (c in alphabetValues) digits.push(parseValue(alphabetValues[c])); else if (c === '<') {\n                var start = i;\n                do {\n                    i++\n                } while (text[i] !== '>' && i < text.length);\n                digits.push(parseValue(text.slice(start + 1, i)))\n            } else throw new Error(c + ' is not a valid character')\n        }\n        return parseBaseFromArray(digits, base, isNegative)\n    };\n\n    function parseBaseFromArray(digits, base, isNegative) {\n        var val = Integer[0], pow = Integer[1], i;\n        for (i = digits.length - 1; i >= 0; i--) {\n            val = val.add(digits[i].times(pow));\n            pow = pow.times(base)\n        }\n        return isNegative ? val.negate() : val\n    }\n\n    function stringify(digit, alphabet) {\n        alphabet = alphabet || DEFAULT_ALPHABET;\n        if (digit < alphabet.length) {\n            return alphabet[digit]\n        }\n        return '<' + digit + '>'\n    }\n\n    function toBase(n, base) {\n        base = bigInt(base);\n        if (base.isZero()) {\n            if (n.isZero()) return { value: [0], isNegative: false };\n            throw new Error('Cannot convert nonzero numbers to base 0.')\n        }\n        if (base.equals(-1)) {\n            if (n.isZero()) return { value: [0], isNegative: false };\n            if (n.isNegative()) return {\n                value: [].concat.apply([], Array.apply(null, Array(-n.toJSNumber())).map(Array.prototype.valueOf, [1, 0])),\n                isNegative: false\n            };\n            var arr = Array.apply(null, Array(n.toJSNumber() - 1)).map(Array.prototype.valueOf, [0, 1]);\n            arr.unshift([1]);\n            return { value: [].concat.apply([], arr), isNegative: false }\n        }\n        var neg = false;\n        if (n.isNegative() && base.isPositive()) {\n            neg = true;\n            n = n.abs()\n        }\n        if (base.isUnit()) {\n            if (n.isZero()) return { value: [0], isNegative: false };\n            return { value: Array.apply(null, Array(n.toJSNumber())).map(Number.prototype.valueOf, 1), isNegative: neg }\n        }\n        var out = [];\n        var left = n, divmod;\n        while (left.isNegative() || left.compareAbs(base) >= 0) {\n            divmod = left.divmod(base);\n            left = divmod.quotient;\n            var digit = divmod.remainder;\n            if (digit.isNegative()) {\n                digit = base.minus(digit).abs();\n                left = left.next()\n            }\n            out.push(digit.toJSNumber())\n        }\n        out.push(left.toJSNumber());\n        return { value: out.reverse(), isNegative: neg }\n    }\n\n    function toBaseString(n, base, alphabet) {\n        var arr = toBase(n, base);\n        return (arr.isNegative ? '-' : '') + arr.value.map(function (x) {\n            return stringify(x, alphabet)\n        }).join('')\n    }\n\n    BigInteger.prototype.toArray = function (radix) {\n        return toBase(this, radix)\n    };\n    SmallInteger.prototype.toArray = function (radix) {\n        return toBase(this, radix)\n    };\n    NativeBigInt.prototype.toArray = function (radix) {\n        return toBase(this, radix)\n    };\n    BigInteger.prototype.toString = function (radix, alphabet) {\n        if (radix === undefined) radix = 10;\n        if (radix !== 10) return toBaseString(this, radix, alphabet);\n        var v = this.value, l = v.length, str = String(v[--l]), zeros = '0000000', digit;\n        while (--l >= 0) {\n            digit = String(v[l]);\n            str += zeros.slice(digit.length) + digit\n        }\n        var sign = this.sign ? '-' : '';\n        return sign + str\n    };\n    SmallInteger.prototype.toString = function (radix, alphabet) {\n        if (radix === undefined) radix = 10;\n        if (radix != 10) return toBaseString(this, radix, alphabet);\n        return String(this.value)\n    };\n    NativeBigInt.prototype.toString = SmallInteger.prototype.toString;\n    NativeBigInt.prototype.toJSON = BigInteger.prototype.toJSON = SmallInteger.prototype.toJSON = function () {\n        return this.toString()\n    };\n    BigInteger.prototype.valueOf = function () {\n        return parseInt(this.toString(), 10)\n    };\n    BigInteger.prototype.toJSNumber = BigInteger.prototype.valueOf;\n    SmallInteger.prototype.valueOf = function () {\n        return this.value\n    };\n    SmallInteger.prototype.toJSNumber = SmallInteger.prototype.valueOf;\n    NativeBigInt.prototype.valueOf = NativeBigInt.prototype.toJSNumber = function () {\n        return parseInt(this.toString(), 10)\n    };\n\n    function parseStringValue(v) {\n        if (isPrecise(+v)) {\n            var x = +v;\n            if (x === truncate(x)) return supportsNativeBigInt ? new NativeBigInt(BigInt(x)) : new SmallInteger(x);\n            throw new Error('Invalid integer: ' + v)\n        }\n        var sign = v[0] === '-';\n        if (sign) v = v.slice(1);\n        var split = v.split(/e/i);\n        if (split.length > 2) throw new Error('Invalid integer: ' + split.join('e'));\n        if (split.length === 2) {\n            var exp = split[1];\n            if (exp[0] === '+') exp = exp.slice(1);\n            exp = +exp;\n            if (exp !== truncate(exp) || !isPrecise(exp)) throw new Error('Invalid integer: ' + exp + ' is not a valid exponent.');\n            var text = split[0];\n            var decimalPlace = text.indexOf('.');\n            if (decimalPlace >= 0) {\n                exp -= text.length - decimalPlace - 1;\n                text = text.slice(0, decimalPlace) + text.slice(decimalPlace + 1)\n            }\n            if (exp < 0) throw new Error('Cannot include negative exponent part for integers');\n            text += new Array(exp + 1).join('0');\n            v = text\n        }\n        var isValid = /^([0-9][0-9]*)$/.test(v);\n        if (!isValid) throw new Error('Invalid integer: ' + v);\n        if (supportsNativeBigInt) {\n            return new NativeBigInt(BigInt(sign ? '-' + v : v))\n        }\n        var r = [], max = v.length, l = LOG_BASE, min = max - l;\n        while (max > 0) {\n            r.push(+v.slice(min, max));\n            min -= l;\n            if (min < 0) min = 0;\n            max -= l\n        }\n        trim(r);\n        return new BigInteger(r, sign)\n    }\n\n    function parseNumberValue(v) {\n        if (supportsNativeBigInt) {\n            return new NativeBigInt(BigInt(v))\n        }\n        if (isPrecise(v)) {\n            if (v !== truncate(v)) throw new Error(v + ' is not an integer.');\n            return new SmallInteger(v)\n        }\n        return parseStringValue(v.toString())\n    }\n\n    function parseValue(v) {\n        if (typeof v === 'number') {\n            return parseNumberValue(v)\n        }\n        if (typeof v === 'string') {\n            return parseStringValue(v)\n        }\n        if (typeof v === 'bigint') {\n            return new NativeBigInt(v)\n        }\n        return v\n    }\n\n    for (var i = 0; i < 1e3; i++) {\n        Integer[i] = parseValue(i);\n        if (i > 0) Integer[-i] = parseValue(-i)\n    }\n    Integer.one = Integer[1];\n    Integer.zero = Integer[0];\n    Integer.minusOne = Integer[-1];\n    Integer.max = max;\n    Integer.min = min;\n    Integer.gcd = gcd;\n    Integer.lcm = lcm;\n    Integer.isInstance = function (x) {\n        return x instanceof BigInteger || x instanceof SmallInteger || x instanceof NativeBigInt\n    };\n    Integer.randBetween = randBetween;\n    Integer.fromArray = function (digits, base, isNegative) {\n        return parseBaseFromArray(digits.map(parseValue), parseValue(base || 10), isNegative)\n    };\n    return Integer\n}();\n\nif (typeof define === 'function' && define.amd) {\n    define('big-integer', [], function () {\n        return bigInt\n    })\n}\n\nexport default bigInt\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * @module MssParser\n * @ignore\n * @param {Object} config object\n */\n\nimport BigInt from '../../../externals/BigInteger.js';\nimport FactoryMaker from '../../core/FactoryMaker.js';\nimport ProtectionConstants from '../../streaming/constants/ProtectionConstants.js';\n\nfunction MssParser(config) {\n    config = config || {};\n    const BASE64 = config.BASE64;\n    const debug = config.debug;\n    const constants = config.constants;\n    const manifestModel = config.manifestModel;\n    const settings = config.settings;\n\n    const DEFAULT_TIME_SCALE = 10000000.0;\n    const SUPPORTED_CODECS = ['AAC', 'AACL', 'AACH', 'AACP', 'AVC1', 'H264', 'TTML', 'DFXP'];\n    // MPEG-DASH Role and accessibility mapping for text tracks according to ETSI TS 103 285 v1.1.1 (section 7.1.2)\n    const ROLE = {\n        'CAPT': 'main',\n        'SUBT': 'alternate',\n        'DESC': 'main'\n    };\n    const ACCESSIBILITY = {\n        'DESC': '2'\n    };\n    const samplingFrequencyIndex = {\n        96000: 0x0,\n        88200: 0x1,\n        64000: 0x2,\n        48000: 0x3,\n        44100: 0x4,\n        32000: 0x5,\n        24000: 0x6,\n        22050: 0x7,\n        16000: 0x8,\n        12000: 0x9,\n        11025: 0xA,\n        8000: 0xB,\n        7350: 0xC\n    };\n    const mimeTypeMap = {\n        'video': 'video/mp4',\n        'audio': 'audio/mp4',\n        'text': 'application/mp4'\n    };\n\n    let instance,\n        logger,\n        initialBufferSettings;\n\n\n    function setup() {\n        logger = debug.getLogger(instance);\n    }\n\n    function getAttributeAsBoolean(node, attrName) {\n        const value = node.getAttribute(attrName);\n        if (!value) {\n            return false;\n        }\n        return value.toLowerCase() === 'true';\n    }\n\n    function mapPeriod(smoothStreamingMedia, timescale) {\n        const period = {};\n        let streams,\n            adaptation;\n\n        // For each StreamIndex node, create an AdaptationSet element\n        period.AdaptationSet = [];\n        streams = smoothStreamingMedia.getElementsByTagName('StreamIndex');\n        for (let i = 0; i < streams.length; i++) {\n            adaptation = mapAdaptationSet(streams[i], timescale);\n            if (adaptation !== null) {\n                period.AdaptationSet.push(adaptation);\n            }\n        }\n\n        return period;\n    }\n\n    function mapAdaptationSet(streamIndex, timescale) {\n        const adaptationSet = {};\n        const representations = [];\n        let segmentTemplate;\n        let qualityLevels,\n            representation,\n            i,\n            index;\n\n        const name = streamIndex.getAttribute('Name');\n        const type = streamIndex.getAttribute('Type');\n        const lang = streamIndex.getAttribute('Language');\n        const fallBackId = lang ? type + '_' + lang : type;\n\n        adaptationSet.id = name || fallBackId;\n        adaptationSet.contentType = type;\n        adaptationSet.lang = lang || 'und';\n        adaptationSet.mimeType = mimeTypeMap[type];\n        adaptationSet.subType = streamIndex.getAttribute('Subtype');\n        adaptationSet.maxWidth = streamIndex.getAttribute('MaxWidth');\n        adaptationSet.maxHeight = streamIndex.getAttribute('MaxHeight');\n\n        // Map text tracks subTypes to MPEG-DASH AdaptationSet role and accessibility (see ETSI TS 103 285 v1.1.1, section 7.1.2)\n        if (adaptationSet.subType) {\n            if (ROLE[adaptationSet.subType]) {\n                adaptationSet.Role = [{\n                    schemeIdUri: 'urn:mpeg:dash:role:2011',\n                    value: ROLE[adaptationSet.subType]\n                }];\n            }\n            if (ACCESSIBILITY[adaptationSet.subType]) {\n                adaptationSet.Accessibility = [{\n                    schemeIdUri: 'urn:tva:metadata:cs:AudioPurposeCS:2007',\n                    value: ACCESSIBILITY[adaptationSet.subType]\n                }];\n            }\n        }\n\n        // Create a SegmentTemplate with a SegmentTimeline\n        segmentTemplate = mapSegmentTemplate(streamIndex, timescale);\n\n        qualityLevels = streamIndex.getElementsByTagName('QualityLevel');\n        // For each QualityLevel node, create a Representation element\n        for (i = 0; i < qualityLevels.length; i++) {\n            // Propagate BaseURL and mimeType\n            qualityLevels[i].BaseURL = adaptationSet.BaseURL;\n            qualityLevels[i].mimeType = adaptationSet.mimeType;\n\n            // Set quality level id\n            index = qualityLevels[i].getAttribute('Index');\n            qualityLevels[i].Id = adaptationSet.id + ((index !== null) ? ('_' + index) : '');\n\n            // Map Representation to QualityLevel\n            representation = mapRepresentation(qualityLevels[i], streamIndex);\n\n            if (representation !== null) {\n                // Copy SegmentTemplate into Representation\n                representation.SegmentTemplate = segmentTemplate;\n\n                representations.push(representation);\n            }\n        }\n\n        if (representations.length === 0) {\n            return null;\n        }\n\n        adaptationSet.Representation = representations;\n\n        // Set SegmentTemplate\n        adaptationSet.SegmentTemplate = segmentTemplate;\n\n        return adaptationSet;\n    }\n\n    function mapRepresentation(qualityLevel, streamIndex) {\n        const representation = {};\n        const type = streamIndex.getAttribute('Type');\n        let fourCCValue = null;\n        let width = null;\n        let height = null;\n\n        representation.id = qualityLevel.Id;\n        representation.bandwidth = parseInt(qualityLevel.getAttribute('Bitrate'), 10);\n        representation.mimeType = qualityLevel.mimeType;\n\n        width = parseInt(qualityLevel.getAttribute('MaxWidth'), 10);\n        height = parseInt(qualityLevel.getAttribute('MaxHeight'), 10);\n        if (!isNaN(width)) {\n            representation.width = width;\n        }\n        if (!isNaN(height)) {\n            representation.height = height;\n        }\n\n\n        fourCCValue = qualityLevel.getAttribute('FourCC');\n\n        // If FourCC not defined at QualityLevel level, then get it from StreamIndex level\n        if (fourCCValue === null || fourCCValue === '') {\n            fourCCValue = streamIndex.getAttribute('FourCC');\n        }\n\n        // If still not defined (optionnal for audio stream, see https://msdn.microsoft.com/en-us/library/ff728116%28v=vs.95%29.aspx),\n        // then we consider the stream is an audio AAC stream\n        if (fourCCValue === null || fourCCValue === '') {\n            if (type === constants.AUDIO) {\n                fourCCValue = 'AAC';\n            } else if (type === constants.VIDEO) {\n                logger.debug('FourCC is not defined whereas it is required for a QualityLevel element for a StreamIndex of type \"video\"');\n                return null;\n            }\n        }\n\n        // Check if codec is supported\n        if (SUPPORTED_CODECS.indexOf(fourCCValue.toUpperCase()) === -1) {\n            // Do not send warning\n            logger.warn('Codec not supported: ' + fourCCValue);\n            return null;\n        }\n\n        // Get codecs value according to FourCC field\n        if (fourCCValue === 'H264' || fourCCValue === 'AVC1') {\n            representation.codecs = getH264Codec(qualityLevel);\n        } else if (fourCCValue.indexOf('AAC') >= 0) {\n            representation.codecs = getAACCodec(qualityLevel, fourCCValue);\n            representation.audioSamplingRate = parseInt(qualityLevel.getAttribute('SamplingRate'), 10);\n            representation.audioChannels = parseInt(qualityLevel.getAttribute('Channels'), 10);\n        } else if (fourCCValue.indexOf('TTML') || fourCCValue.indexOf('DFXP')) {\n            representation.codecs = constants.STPP;\n        }\n\n        representation.codecPrivateData = '' + qualityLevel.getAttribute('CodecPrivateData');\n        representation.BaseURL = qualityLevel.BaseURL;\n\n        return representation;\n    }\n\n    function getH264Codec(qualityLevel) {\n        let codecPrivateData = qualityLevel.getAttribute('CodecPrivateData').toString();\n        let nalHeader,\n            avcoti;\n\n\n        // Extract from the CodecPrivateData field the hexadecimal representation of the following\n        // three bytes in the sequence parameter set NAL unit.\n        // => Find the SPS nal header\n        nalHeader = /00000001[0-9]7/.exec(codecPrivateData);\n        // => Find the 6 characters after the SPS nalHeader (if it exists)\n        avcoti = nalHeader && nalHeader[0] ? (codecPrivateData.substr(codecPrivateData.indexOf(nalHeader[0]) + 10, 6)) : undefined;\n\n        return 'avc1.' + avcoti;\n    }\n\n    function getAACCodec(qualityLevel, fourCCValue) {\n        const samplingRate = parseInt(qualityLevel.getAttribute('SamplingRate'), 10);\n        let codecPrivateData = qualityLevel.getAttribute('CodecPrivateData').toString();\n        let objectType = 0;\n        let codecPrivateDataHex,\n            arr16,\n            indexFreq,\n            extensionSamplingFrequencyIndex;\n\n        //chrome problem, in implicit AAC HE definition, so when AACH is detected in FourCC\n        //set objectType to 5 => strange, it should be 2\n        if (fourCCValue === 'AACH') {\n            objectType = 0x05;\n        }\n        //if codecPrivateData is empty, build it :\n        if (codecPrivateData === undefined || codecPrivateData === '') {\n            objectType = 0x02; //AAC Main Low Complexity => object Type = 2\n            indexFreq = samplingFrequencyIndex[samplingRate];\n            if (fourCCValue === 'AACH') {\n                // 4 bytes :     XXXXX         XXXX          XXXX             XXXX                  XXXXX      XXX   XXXXXXX\n                //           ' ObjectType' 'Freq Index' 'Channels value'   'Extens Sampl Freq'  'ObjectType'  'GAS' 'alignment = 0'\n                objectType = 0x05; // High Efficiency AAC Profile = object Type = 5 SBR\n                codecPrivateData = new Uint8Array(4);\n                extensionSamplingFrequencyIndex = samplingFrequencyIndex[samplingRate * 2]; // in HE AAC Extension Sampling frequence\n                // equals to SamplingRate*2\n                //Freq Index is present for 3 bits in the first byte, last bit is in the second\n                codecPrivateData[0] = (objectType << 3) | (indexFreq >> 1);\n                codecPrivateData[1] = (indexFreq << 7) | (qualityLevel.Channels << 3) | (extensionSamplingFrequencyIndex >> 1);\n                codecPrivateData[2] = (extensionSamplingFrequencyIndex << 7) | (0x02 << 2); // origin object type equals to 2 => AAC Main Low Complexity\n                codecPrivateData[3] = 0x0; //alignment bits\n\n                arr16 = new Uint16Array(2);\n                arr16[0] = (codecPrivateData[0] << 8) + codecPrivateData[1];\n                arr16[1] = (codecPrivateData[2] << 8) + codecPrivateData[3];\n                //convert decimal to hex value\n                codecPrivateDataHex = arr16[0].toString(16);\n                codecPrivateDataHex = arr16[0].toString(16) + arr16[1].toString(16);\n\n            } else {\n                // 2 bytes :     XXXXX         XXXX          XXXX              XXX\n                //           ' ObjectType' 'Freq Index' 'Channels value'   'GAS = 000'\n                codecPrivateData = new Uint8Array(2);\n                //Freq Index is present for 3 bits in the first byte, last bit is in the second\n                codecPrivateData[0] = (objectType << 3) | (indexFreq >> 1);\n                codecPrivateData[1] = (indexFreq << 7) | (parseInt(qualityLevel.getAttribute('Channels'), 10) << 3);\n                // put the 2 bytes in an 16 bits array\n                arr16 = new Uint16Array(1);\n                arr16[0] = (codecPrivateData[0] << 8) + codecPrivateData[1];\n                //convert decimal to hex value\n                codecPrivateDataHex = arr16[0].toString(16);\n            }\n\n            codecPrivateData = '' + codecPrivateDataHex;\n            codecPrivateData = codecPrivateData.toUpperCase();\n            qualityLevel.setAttribute('CodecPrivateData', codecPrivateData);\n        } else if (objectType === 0) {\n            objectType = (parseInt(codecPrivateData.substr(0, 2), 16) & 0xF8) >> 3;\n        }\n\n        return 'mp4a.40.' + objectType;\n    }\n\n    function mapSegmentTemplate(streamIndex, timescale) {\n        const segmentTemplate = {};\n        let mediaUrl,\n            streamIndexTimeScale,\n            url;\n\n        url = streamIndex.getAttribute('Url');\n        mediaUrl = url ? url.replace('{bitrate}', '$Bandwidth$') : null;\n        mediaUrl = mediaUrl ? mediaUrl.replace('{start time}', '$Time$') : null;\n\n        streamIndexTimeScale = streamIndex.getAttribute('TimeScale');\n        streamIndexTimeScale = streamIndexTimeScale ? parseFloat(streamIndexTimeScale) : timescale;\n\n        segmentTemplate.media = mediaUrl;\n        segmentTemplate.timescale = streamIndexTimeScale;\n\n        segmentTemplate.SegmentTimeline = mapSegmentTimeline(streamIndex, segmentTemplate.timescale);\n\n        // Patch: set availabilityTimeOffset to Infinity since segments are available as long as they are present in timeline\n        segmentTemplate.availabilityTimeOffset = 'INF';\n\n        return segmentTemplate;\n    }\n\n    function mapSegmentTimeline(streamIndex, timescale) {\n        const segmentTimeline = {};\n        const chunks = streamIndex.getElementsByTagName('c');\n        const segments = [];\n        let segment,\n            prevSegment,\n            tManifest,\n            i, j, r;\n        let duration = 0;\n\n        for (i = 0; i < chunks.length; i++) {\n            segment = {};\n\n            // Get time 't' attribute value\n            tManifest = chunks[i].getAttribute('t');\n\n            // => segment.tManifest = original timestamp value as a string (for constructing the fragment request url, see DashHandler)\n            // => segment.t = number value of timestamp (maybe rounded value, but only for 0.1 microsecond)\n            if (tManifest && BigInt(tManifest).greater(BigInt(Number.MAX_SAFE_INTEGER))) {\n                segment.tManifest = tManifest;\n            }\n            segment.t = parseFloat(tManifest);\n\n            // Get duration 'd' attribute value\n            segment.d = parseFloat(chunks[i].getAttribute('d'));\n\n            // If 't' not defined for first segment then t=0\n            if ((i === 0) && !segment.t) {\n                segment.t = 0;\n            }\n\n            if (i > 0) {\n                prevSegment = segments[segments.length - 1];\n                // Update previous segment duration if not defined\n                if (!prevSegment.d) {\n                    if (prevSegment.tManifest) {\n                        prevSegment.d = BigInt(tManifest).subtract(BigInt(prevSegment.tManifest)).toJSNumber();\n                    } else {\n                        prevSegment.d = segment.t - prevSegment.t;\n                    }\n                    duration += prevSegment.d;\n                }\n                // Set segment absolute timestamp if not set in manifest\n                if (!segment.t) {\n                    if (prevSegment.tManifest) {\n                        segment.tManifest = BigInt(prevSegment.tManifest).add(BigInt(prevSegment.d)).toString();\n                        segment.t = parseFloat(segment.tManifest);\n                    } else {\n                        segment.t = prevSegment.t + prevSegment.d;\n                    }\n                }\n            }\n\n            if (segment.d) {\n                duration += segment.d;\n            }\n\n            // Create new segment\n            segments.push(segment);\n\n            // Support for 'r' attribute (i.e. \"repeat\" as in MPEG-DASH)\n            r = parseFloat(chunks[i].getAttribute('r'));\n            if (r) {\n\n                for (j = 0; j < (r - 1); j++) {\n                    prevSegment = segments[segments.length - 1];\n                    segment = {};\n                    segment.t = prevSegment.t + prevSegment.d;\n                    segment.d = prevSegment.d;\n                    if (prevSegment.tManifest) {\n                        segment.tManifest = BigInt(prevSegment.tManifest).add(BigInt(prevSegment.d)).toString();\n                    }\n                    duration += segment.d;\n                    segments.push(segment);\n                }\n            }\n        }\n\n        segmentTimeline.S = segments;\n        segmentTimeline.duration = duration / timescale;\n\n        return segmentTimeline;\n    }\n\n    function getKIDFromProtectionHeader(protectionHeader) {\n        let prHeader,\n            wrmHeader,\n            xmlReader,\n            KID;\n\n        // Get PlayReady header as byte array (base64 decoded)\n        prHeader = BASE64.decodeArray(protectionHeader.firstChild.data);\n\n        // Get Right Management header (WRMHEADER) from PlayReady header\n        wrmHeader = getWRMHeaderFromPRHeader(prHeader);\n\n        if (wrmHeader) {\n            // Convert from multi-byte to unicode\n            wrmHeader = new Uint16Array(wrmHeader.buffer);\n\n            // Convert to string\n            wrmHeader = String.fromCharCode.apply(null, wrmHeader);\n\n            // Parse <WRMHeader> to get KID field value\n            xmlReader = (new DOMParser()).parseFromString(wrmHeader, 'application/xml');\n            KID = xmlReader.querySelector('KID').textContent;\n\n            // Get KID (base64 decoded) as byte array\n            KID = BASE64.decodeArray(KID);\n\n            // Convert UUID from little-endian to big-endian\n            convertUuidEndianness(KID);\n        }\n\n        return KID;\n    }\n\n    function getWRMHeaderFromPRHeader(prHeader) {\n        let length,\n            recordCount,\n            recordType,\n            recordLength,\n            recordValue;\n        let i = 0;\n\n        // Parse PlayReady header\n\n        // Length - 32 bits (LE format)\n        length = (prHeader[i + 3] << 24) + (prHeader[i + 2] << 16) + (prHeader[i + 1] << 8) + prHeader[i]; // eslint-disable-line\n        i += 4;\n\n        // Record count - 16 bits (LE format)\n        recordCount = (prHeader[i + 1] << 8) + prHeader[i]; // eslint-disable-line\n        i += 2;\n\n        // Parse records\n        while (i < prHeader.length) {\n            // Record type - 16 bits (LE format)\n            recordType = (prHeader[i + 1] << 8) + prHeader[i];\n            i += 2;\n\n            // Check if Rights Management header (record type = 0x01)\n            if (recordType === 0x01) {\n\n                // Record length - 16 bits (LE format)\n                recordLength = (prHeader[i + 1] << 8) + prHeader[i];\n                i += 2;\n\n                // Record value => contains <WRMHEADER>\n                recordValue = new Uint8Array(recordLength);\n                recordValue.set(prHeader.subarray(i, i + recordLength));\n                return recordValue;\n            }\n        }\n\n        return null;\n    }\n\n    function convertUuidEndianness(uuid) {\n        swapBytes(uuid, 0, 3);\n        swapBytes(uuid, 1, 2);\n        swapBytes(uuid, 4, 5);\n        swapBytes(uuid, 6, 7);\n    }\n\n    function swapBytes(bytes, pos1, pos2) {\n        const temp = bytes[pos1];\n        bytes[pos1] = bytes[pos2];\n        bytes[pos2] = temp;\n    }\n\n\n    function createPRContentProtection(protectionHeader) {\n        let pro = {\n            __text: protectionHeader.firstChild.data,\n            __prefix: 'mspr'\n        };\n        return {\n            schemeIdUri: 'urn:uuid:' + ProtectionConstants.PLAYREADY_UUID,\n            value: ProtectionConstants.PLAYREADY_KEYSTEM_STRING,\n            pro: pro\n        };\n    }\n\n    function createWidevineContentProtection(KID) {\n        let widevineCP = {\n            schemeIdUri: 'urn:uuid:' + ProtectionConstants.WIDEVINE_UUID,\n            value: ProtectionConstants.WIDEVINE_KEYSTEM_STRING\n        };\n        if (!KID) {\n            return widevineCP;\n        }\n        // Create Widevine CENC header (Protocol Buffer) with KID value\n        const wvCencHeader = new Uint8Array(2 + KID.length);\n        wvCencHeader[0] = 0x12;\n        wvCencHeader[1] = 0x10;\n        wvCencHeader.set(KID, 2);\n\n        // Create a pssh box\n        const length = 12 /* box length, type, version and flags */ + 16 /* SystemID */ + 4 /* data length */ + wvCencHeader.length;\n        let pssh = new Uint8Array(length);\n        let i = 0;\n\n        // Set box length value\n        pssh[i++] = (length & 0xFF000000) >> 24;\n        pssh[i++] = (length & 0x00FF0000) >> 16;\n        pssh[i++] = (length & 0x0000FF00) >> 8;\n        pssh[i++] = (length & 0x000000FF);\n\n        // Set type ('pssh'), version (0) and flags (0)\n        pssh.set([0x70, 0x73, 0x73, 0x68, 0x00, 0x00, 0x00, 0x00], i);\n        i += 8;\n\n        // Set SystemID ('edef8ba9-79d6-4ace-a3c8-27dcd51d21ed')\n        pssh.set([0xed, 0xef, 0x8b, 0xa9, 0x79, 0xd6, 0x4a, 0xce, 0xa3, 0xc8, 0x27, 0xdc, 0xd5, 0x1d, 0x21, 0xed], i);\n        i += 16;\n\n        // Set data length value\n        pssh[i++] = (wvCencHeader.length & 0xFF000000) >> 24;\n        pssh[i++] = (wvCencHeader.length & 0x00FF0000) >> 16;\n        pssh[i++] = (wvCencHeader.length & 0x0000FF00) >> 8;\n        pssh[i++] = (wvCencHeader.length & 0x000000FF);\n\n        // Copy Widevine CENC header\n        pssh.set(wvCencHeader, i);\n\n        // Convert to BASE64 string\n        pssh = String.fromCharCode.apply(null, pssh);\n        pssh = BASE64.encodeASCII(pssh);\n\n        widevineCP.pssh = { __text: pssh };\n\n        return widevineCP;\n    }\n\n    function processManifest(xmlDoc) {\n        const manifest = {};\n        const contentProtections = [];\n        const smoothStreamingMedia = xmlDoc.getElementsByTagName('SmoothStreamingMedia')[0];\n        const protection = xmlDoc.getElementsByTagName('Protection')[0];\n        let protectionHeader = null;\n        let period,\n            adaptations,\n            contentProtection,\n            KID,\n            timestampOffset,\n            startTime,\n            segments,\n            timescale,\n            segmentDuration,\n            i, j;\n\n        // Set manifest node properties\n        manifest.protocol = 'MSS';\n        manifest.profiles = 'urn:mpeg:dash:profile:isoff-live:2011';\n        manifest.type = getAttributeAsBoolean(smoothStreamingMedia, 'IsLive') ? 'dynamic' : 'static';\n        timescale = smoothStreamingMedia.getAttribute('TimeScale');\n        manifest.timescale = timescale ? parseFloat(timescale) : DEFAULT_TIME_SCALE;\n        let dvrWindowLength = parseFloat(smoothStreamingMedia.getAttribute('DVRWindowLength'));\n        // If the DVRWindowLength field is omitted for a live presentation or set to 0, the DVR window is effectively infinite\n        if (manifest.type === 'dynamic' && (dvrWindowLength === 0 || isNaN(dvrWindowLength))) {\n            dvrWindowLength = Infinity;\n        }\n        // Star-over\n        if (dvrWindowLength === 0 && getAttributeAsBoolean(smoothStreamingMedia, 'CanSeek')) {\n            dvrWindowLength = Infinity;\n        }\n\n        if (dvrWindowLength > 0) {\n            manifest.timeShiftBufferDepth = dvrWindowLength / manifest.timescale;\n        }\n\n        let duration = parseFloat(smoothStreamingMedia.getAttribute('Duration'));\n        manifest.mediaPresentationDuration = (duration === 0) ? Infinity : duration / manifest.timescale;\n        // By default, set minBufferTime to 2 sec. (but set below according to video segment duration)\n        manifest.minBufferTime = 2;\n        manifest.ttmlTimeIsRelative = true;\n\n        // Live manifest with Duration = start-over\n        if (manifest.type === 'dynamic' && duration > 0) {\n            manifest.type = 'static';\n            // We set timeShiftBufferDepth to initial duration, to be used by MssFragmentController to update segment timeline\n            manifest.timeShiftBufferDepth = duration / manifest.timescale;\n            // Duration will be set according to current segment timeline duration (see below)\n        }\n\n        if (manifest.type === 'dynamic') {\n            manifest.refreshManifestOnSwitchTrack = true; // Refresh manifest when switching tracks\n            manifest.doNotUpdateDVRWindowOnBufferUpdated = true; // DVRWindow is update by MssFragmentMoofPocessor based on tfrf boxes\n            manifest.ignorePostponeTimePeriod = true; // Never update manifest\n            manifest.availabilityStartTime = new Date(null); // Returns 1970\n        }\n\n        // Map period node to manifest root node\n        period = mapPeriod(smoothStreamingMedia, manifest.timescale);\n        manifest.Period = [period];\n\n        // Initialize period start time\n        period.start = 0;\n\n        // Uncomment to test live to static manifests\n        // if (manifest.type !== 'static') {\n        //     manifest.type = 'static';\n        //     manifest.mediaPresentationDuration = manifest.timeShiftBufferDepth;\n        //     manifest.timeShiftBufferDepth = null;\n        // }\n\n        // ContentProtection node\n        if (protection !== undefined) {\n            protectionHeader = xmlDoc.getElementsByTagName('ProtectionHeader')[0];\n\n            // Some packagers put newlines into the ProtectionHeader base64 string, which is not good\n            // because this cannot be correctly parsed. Let's just filter out any newlines found in there.\n            protectionHeader.firstChild.data = protectionHeader.firstChild.data.replace(/\\n|\\r/g, '');\n\n            // Get KID (in CENC format) from protection header\n            KID = getKIDFromProtectionHeader(protectionHeader);\n\n            // Create ContentProtection for PlayReady\n            contentProtection = createPRContentProtection(protectionHeader);\n            contentProtection['cenc:default_KID'] = KID;\n            contentProtections.push(contentProtection);\n\n            // Create ContentProtection for Widevine (as a CENC protection)\n            contentProtection = createWidevineContentProtection(KID);\n            contentProtection['cenc:default_KID'] = KID;\n            contentProtections.push(contentProtection);\n\n            manifest.ContentProtection = contentProtections;\n        }\n\n        adaptations = period.AdaptationSet;\n\n        for (i = 0; i < adaptations.length; i += 1) {\n            adaptations[i].SegmentTemplate.initialization = '$Bandwidth$';\n            // Propagate content protection information into each adaptation\n            if (manifest.ContentProtection !== undefined) {\n                adaptations[i].ContentProtection = manifest.ContentProtection;\n                adaptations[i].ContentProtection = manifest.ContentProtection;\n            }\n\n            if (adaptations[i].contentType === 'video') {\n                // Get video segment duration\n                segmentDuration = adaptations[i].SegmentTemplate.SegmentTimeline.S[0].d / adaptations[i].SegmentTemplate.timescale;\n                // Set minBufferTime to one segment duration\n                manifest.minBufferTime = segmentDuration;\n\n                if (manifest.type === 'dynamic') {\n                    // Match timeShiftBufferDepth to video segment timeline duration\n                    if (manifest.timeShiftBufferDepth > 0 &&\n                        manifest.timeShiftBufferDepth !== Infinity &&\n                        manifest.timeShiftBufferDepth > adaptations[i].SegmentTemplate.SegmentTimeline.duration) {\n                        manifest.timeShiftBufferDepth = adaptations[i].SegmentTemplate.SegmentTimeline.duration;\n                    }\n                }\n            }\n        }\n\n        // Cap minBufferTime to timeShiftBufferDepth\n        manifest.minBufferTime = Math.min(manifest.minBufferTime, (manifest.timeShiftBufferDepth ? manifest.timeShiftBufferDepth : Infinity));\n\n        // In case of live streams:\n        // 1- configure player buffering properties according to target live delay\n        // 2- adapt live delay and then buffers length in case timeShiftBufferDepth is too small compared to target live delay (see PlaybackController.computeLiveDelay())\n        // 3- Set retry attempts and intervals for FragmentInfo requests\n        if (manifest.type === 'dynamic') {\n            let targetLiveDelay = settings.get().streaming.delay.liveDelay;\n            if (!targetLiveDelay) {\n                const liveDelayFragmentCount = settings.get().streaming.delay.liveDelayFragmentCount !== null && !isNaN(settings.get().streaming.delay.liveDelayFragmentCount) ? settings.get().streaming.delay.liveDelayFragmentCount : 4;\n                targetLiveDelay = segmentDuration * liveDelayFragmentCount;\n            }\n            let targetDelayCapping = Math.max(manifest.timeShiftBufferDepth - 10/*END_OF_PLAYLIST_PADDING*/, manifest.timeShiftBufferDepth / 2);\n            let liveDelay = Math.min(targetDelayCapping, targetLiveDelay);\n            // Consider a margin of more than one segment in order to avoid Precondition Failed errors (412), for example if audio and video are not correctly synchronized\n            let bufferTime = liveDelay - (segmentDuration * 1.5);\n\n            // Store initial buffer settings\n            initialBufferSettings = {\n                'streaming': {\n                    'buffer': {\n                        'bufferTimeDefault': settings.get().streaming.buffer.bufferTimeDefault,\n                        'bufferTimeAtTopQuality': settings.get().streaming.buffer.bufferTimeAtTopQuality,\n                        'bufferTimeAtTopQualityLongForm': settings.get().streaming.buffer.bufferTimeAtTopQualityLongForm\n                    },\n                    'timeShiftBuffer': {\n                        calcFromSegmentTimeline: settings.get().streaming.timeShiftBuffer.calcFromSegmentTimeline\n                    },\n                    'delay': {\n                        'liveDelay': settings.get().streaming.delay.liveDelay\n                    }\n                }\n            };\n\n            settings.update({\n                'streaming': {\n                    'buffer': {\n                        'bufferTimeDefault': bufferTime,\n                        'bufferTimeAtTopQuality': bufferTime,\n                        'bufferTimeAtTopQualityLongForm': bufferTime\n                    },\n                    'timeShiftBuffer': {\n                        calcFromSegmentTimeline: true\n                    },\n                    'delay': {\n                        'liveDelay': liveDelay\n                    }\n                }\n            });\n        }\n\n        // Delete Content Protection under root manifest node\n        delete manifest.ContentProtection;\n\n        // In case of VOD streams, check if start time is greater than 0\n        // Then determine timestamp offset according to higher audio/video start time\n        // (use case = live stream delinearization)\n        if (manifest.type === 'static') {\n            // In case of start-over stream and manifest reloading (due to track switch)\n            // we consider previous timestampOffset to keep timelines synchronized\n            var prevManifest = manifestModel.getValue();\n            if (prevManifest && prevManifest.timestampOffset) {\n                timestampOffset = prevManifest.timestampOffset;\n            } else {\n                for (i = 0; i < adaptations.length; i++) {\n                    if (adaptations[i].contentType === constants.AUDIO || adaptations[i].contentType === constants.VIDEO) {\n                        segments = adaptations[i].SegmentTemplate.SegmentTimeline.S;\n                        startTime = segments[0].t;\n                        if (timestampOffset === undefined) {\n                            timestampOffset = startTime;\n                        }\n                        timestampOffset = Math.min(timestampOffset, startTime);\n                        // Correct content duration according to minimum adaptation's segment timeline duration\n                        // in order to force <video> element sending 'ended' event\n                        manifest.mediaPresentationDuration = Math.min(manifest.mediaPresentationDuration, adaptations[i].SegmentTemplate.SegmentTimeline.duration);\n                    }\n                }\n            }\n            if (timestampOffset > 0) {\n                // Patch segment templates timestamps and determine period start time (since audio/video should not be aligned to 0)\n                manifest.timestampOffset = timestampOffset;\n                for (i = 0; i < adaptations.length; i++) {\n                    segments = adaptations[i].SegmentTemplate.SegmentTimeline.S;\n                    for (j = 0; j < segments.length; j++) {\n                        if (!segments[j].tManifest) {\n                            segments[j].tManifest = segments[j].t.toString();\n                        }\n                        segments[j].t -= timestampOffset;\n                    }\n                    if (adaptations[i].contentType === constants.AUDIO || adaptations[i].contentType === constants.VIDEO) {\n                        period.start = Math.max(segments[0].t, period.start);\n                        adaptations[i].SegmentTemplate.presentationTimeOffset = period.start;\n                    }\n                }\n                period.start /= manifest.timescale;\n            }\n        }\n\n        // Floor the duration to get around precision differences between segments timestamps and MSE buffer timestamps\n        // and then avoid 'ended' event not being raised\n        manifest.mediaPresentationDuration = Math.floor(manifest.mediaPresentationDuration * 1000) / 1000;\n        period.duration = manifest.mediaPresentationDuration;\n\n        return manifest;\n    }\n\n    function parseDOM(data) {\n        let xmlDoc = null;\n\n        if (window.DOMParser) {\n            const parser = new window.DOMParser();\n\n            xmlDoc = parser.parseFromString(data, 'text/xml');\n            if (xmlDoc.getElementsByTagName('parsererror').length > 0) {\n                throw new Error('parsing the manifest failed');\n            }\n        }\n\n        return xmlDoc;\n    }\n\n    function getIron() {\n        return null;\n    }\n\n    function internalParse(data) {\n        let xmlDoc = null;\n        let manifest = null;\n\n        const startTime = window.performance.now();\n\n        // Parse the MSS XML manifest\n        xmlDoc = parseDOM(data);\n\n        const xmlParseTime = window.performance.now();\n\n        if (xmlDoc === null) {\n            return null;\n        }\n\n        // Convert MSS manifest into DASH manifest\n        manifest = processManifest(xmlDoc, new Date());\n\n        const mss2dashTime = window.performance.now();\n\n        logger.info('Parsing complete: (xmlParsing: ' + (xmlParseTime - startTime).toPrecision(3) + 'ms, mss2dash: ' + (mss2dashTime - xmlParseTime).toPrecision(3) + 'ms, total: ' + ((mss2dashTime - startTime) / 1000).toPrecision(3) + 's)');\n\n        return manifest;\n    }\n\n    function reset() {\n        // Restore initial buffer settings\n        if (initialBufferSettings) {\n            settings.update(initialBufferSettings);\n        }\n    }\n\n    instance = {\n        parse: internalParse,\n        getIron: getIron,\n        reset: reset\n    };\n\n    setup();\n\n    return instance;\n}\n\nMssParser.__dashjs_factory_name = 'MssParser';\nexport default FactoryMaker.getClassFactory(MssParser);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES, LOSS OF USE, DATA, OR\n *  PROFITS, OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * Protection Constants declaration\n * @ignore\n */\nexport default {\n    CLEARKEY_KEYSTEM_STRING: 'org.w3.clearkey',\n    WIDEVINE_KEYSTEM_STRING: 'com.widevine.alpha',\n    PLAYREADY_KEYSTEM_STRING: 'com.microsoft.playready',\n    PLAYREADY_RECOMMENDATION_KEYSTEM_STRING: 'com.microsoft.playready.recommendation',\n    WIDEVINE_UUID: 'edef8ba9-79d6-4ace-a3c8-27dcd51d21ed',\n    PLAYREADY_UUID: '9a04f079-9840-4286-ab92-e65be0885f95',\n    CLEARKEY_UUID: 'e2719d58-a985-b3c9-781a-b030af78d30e',\n    W3C_CLEARKEY_UUID: '1077efec-c0b2-4d02-ace3-3c1e52e2fb4b',\n    INITIALIZATION_DATA_TYPE_CENC: 'cenc',\n    INITIALIZATION_DATA_TYPE_KEYIDS: 'keyids',\n    INITIALIZATION_DATA_TYPE_WEBM: 'webm',\n    ENCRYPTION_SCHEME_CENC: 'cenc',\n    ENCRYPTION_SCHEME_CBCS: 'cbcs',\n    MEDIA_KEY_MESSAGE_TYPES: {\n        LICENSE_REQUEST: 'license-request',\n        LICENSE_RENEWAL: 'license-renewal',\n        LICENSE_RELEASE: 'license-release',\n        INDIVIDUALIZATION_REQUEST: 'individualization-request',\n    },\n    ROBUSTNESS_STRINGS: {\n        WIDEVINE: {\n            SW_SECURE_CRYPTO: 'SW_SECURE_CRYPTO',\n            SW_SECURE_DECODE: 'SW_SECURE_DECODE',\n            HW_SECURE_CRYPTO: 'HW_SECURE_CRYPTO',\n            HW_SECURE_DECODE: 'HW_SECURE_DECODE',\n            HW_SECURE_ALL: 'HW_SECURE_ALL'\n        }\n    },\n    MEDIA_KEY_STATUSES: {\n        USABLE: 'usable',\n        EXPIRED: 'expired',\n        RELEASED: 'released',\n        OUTPUT_RESTRICTED: 'output-restricted',\n        OUTPUT_DOWNSCALED: 'output-downscaled',\n        STATUS_PENDING: 'status-pending',\n        INTERNAL_ERROR: 'internal-error',\n    }\n}\n\n\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport DataChunk from '../streaming/vo/DataChunk.js';\nimport FragmentRequest from '../streaming/vo/FragmentRequest.js';\nimport MssFragmentInfoController from './MssFragmentInfoController.js';\nimport MssFragmentProcessor from './MssFragmentProcessor.js';\nimport MssParser from './parser/MssParser.js';\nimport MssErrors from './errors/MssErrors.js';\nimport DashJSError from '../streaming/vo/DashJSError.js';\nimport {HTTPRequest} from '../streaming/vo/metrics/HTTPRequest.js';\n\nfunction MssHandler(config) {\n\n    config = config || {};\n    const context = this.context;\n    const eventBus = config.eventBus;\n    const events = config.events;\n    const constants = config.constants;\n    const initSegmentType = config.initSegmentType;\n    const playbackController = config.playbackController;\n    const streamController = config.streamController;\n    let mssParser,\n        mssFragmentProcessor,\n        fragmentInfoControllers,\n        instance;\n\n    function setup() {\n        fragmentInfoControllers = [];\n    }\n\n    function createMssFragmentProcessor() {\n        mssFragmentProcessor = MssFragmentProcessor(context).create(config);\n    }\n\n    function getStreamProcessor(type) {\n        return streamController.getActiveStreamProcessors().filter(processor => {\n            return processor.getType() === type;\n        })[0];\n    }\n\n    function getFragmentInfoController(type) {\n        return fragmentInfoControllers.filter(controller => {\n            return (controller.getType() === type);\n        })[0];\n    }\n\n    function createDataChunk(request, streamId, endFragment) {\n        const chunk = new DataChunk();\n\n        chunk.streamId = streamId;\n        chunk.segmentType = request.type;\n        chunk.start = request.startTime;\n        chunk.duration = request.duration;\n        chunk.end = chunk.start + chunk.duration;\n        chunk.index = request.index;\n        chunk.bandwidth = request.bandwidth;\n        chunk.representation = request.representation;\n        chunk.endFragment = endFragment;\n\n        return chunk;\n    }\n\n    function startFragmentInfoControllers() {\n\n        // Create MssFragmentInfoControllers for each StreamProcessor of active stream (only for audio, video or text)\n        let processors = streamController.getActiveStreamProcessors();\n        processors.forEach(function (processor) {\n            if (processor.getType() === constants.VIDEO ||\n                processor.getType() === constants.AUDIO ||\n                processor.getType() === constants.TEXT) {\n\n                let fragmentInfoController = getFragmentInfoController(processor.getType());\n                if (!fragmentInfoController) {\n                    fragmentInfoController = MssFragmentInfoController(context).create({\n                        streamProcessor: processor,\n                        baseURLController: config.baseURLController,\n                        debug: config.debug\n                    });\n                    fragmentInfoController.initialize();\n                    fragmentInfoControllers.push(fragmentInfoController);\n                }\n                fragmentInfoController.start();\n            }\n        });\n    }\n\n    function stopFragmentInfoControllers() {\n        fragmentInfoControllers.forEach(c => {\n            c.reset();\n        });\n        fragmentInfoControllers = [];\n    }\n\n    function onInitFragmentNeeded(e) {\n        let streamProcessor = getStreamProcessor(e.mediaType);\n        if (!streamProcessor) {\n            return;\n        }\n\n        // Create init segment request\n        let representationController = streamProcessor.getRepresentationController();\n        let representation = representationController.getCurrentRepresentation();\n        let mediaInfo = streamProcessor.getMediaInfo();\n\n        let request = new FragmentRequest();\n        request.mediaType = representation.adaptation.type;\n        request.type = initSegmentType;\n        request.range = representation.range;\n        request.bandwidth = representation.bandwidth;\n        request.representation = representation;\n\n        const chunk = createDataChunk(request, mediaInfo.streamInfo.id, e.type !== events.FRAGMENT_LOADING_PROGRESS);\n\n        try {\n            // Generate init segment (moov)\n            chunk.bytes = mssFragmentProcessor.generateMoov(representation);\n\n            // Notify init segment has been loaded\n            eventBus.trigger(events.INIT_FRAGMENT_LOADED,\n                { chunk: chunk },\n                { streamId: mediaInfo.streamInfo.id, mediaType: representation.adaptation.type }\n            );\n        } catch (e) {\n            config.errHandler.error(new DashJSError(e.code, e.message, e.data));\n        }\n\n        // Change the sender value to stop event to be propagated\n        e.sender = null;\n    }\n\n    function onSegmentMediaLoaded(e) {\n        if (e.error) {\n            return;\n        }\n\n        let streamProcessor = getStreamProcessor(e.request.mediaType);\n        if (!streamProcessor) {\n            return;\n        }\n\n        // Process moof to transcode it from MSS to DASH (or to update segment timeline for SegmentInfo fragments)\n        mssFragmentProcessor.processFragment(e, streamProcessor);\n\n        if (e.request.type === HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE) {\n            // If FragmentInfo loaded, then notify corresponding MssFragmentInfoController\n            let fragmentInfoController = getFragmentInfoController(e.request.mediaType);\n            if (fragmentInfoController) {\n                fragmentInfoController.fragmentInfoLoaded(e);\n            }\n        }\n\n        // Start MssFragmentInfoControllers in case of start-over streams\n        let manifestInfo = e.request.representation.mediaInfo.streamInfo.manifestInfo;\n        if (!manifestInfo.isDynamic && manifestInfo.dvrWindowSize !== Infinity) {\n            startFragmentInfoControllers();\n        }\n    }\n\n    function onPlaybackPaused() {\n        if (playbackController.getIsDynamic() && playbackController.getTime() !== 0) {\n            startFragmentInfoControllers();\n        }\n    }\n\n    function onPlaybackSeeking() {\n        if (playbackController.getIsDynamic() && playbackController.getTime() !== 0) {\n            startFragmentInfoControllers();\n        }\n    }\n\n    function onTTMLPreProcess(ttmlSubtitles) {\n        if (!ttmlSubtitles || !ttmlSubtitles.data) {\n            return;\n        }\n\n        ttmlSubtitles.data = ttmlSubtitles.data.replace(/http:\\/\\/www.w3.org\\/2006\\/10\\/ttaf1/gi, 'http://www.w3.org/ns/ttml');\n    }\n\n    function registerEvents() {\n        eventBus.on(events.INIT_FRAGMENT_NEEDED, onInitFragmentNeeded, instance, { priority: dashjs.FactoryMaker.getSingletonFactoryByName(eventBus.getClassName()).EVENT_PRIORITY_HIGH });\n        eventBus.on(events.PLAYBACK_PAUSED, onPlaybackPaused, instance, { priority: dashjs.FactoryMaker.getSingletonFactoryByName(eventBus.getClassName()).EVENT_PRIORITY_HIGH });\n        eventBus.on(events.PLAYBACK_SEEKING, onPlaybackSeeking, instance, { priority: dashjs.FactoryMaker.getSingletonFactoryByName(eventBus.getClassName()).EVENT_PRIORITY_HIGH });\n        eventBus.on(events.FRAGMENT_LOADING_COMPLETED, onSegmentMediaLoaded, instance, { priority: dashjs.FactoryMaker.getSingletonFactoryByName(eventBus.getClassName()).EVENT_PRIORITY_HIGH });\n        eventBus.on(events.TTML_TO_PARSE, onTTMLPreProcess, instance);\n    }\n\n    function reset() {\n        if (mssParser) {\n            mssParser.reset();\n            mssParser = undefined;\n        }\n\n        eventBus.off(events.INIT_FRAGMENT_NEEDED, onInitFragmentNeeded, this);\n        eventBus.off(events.PLAYBACK_PAUSED, onPlaybackPaused, this);\n        eventBus.off(events.PLAYBACK_SEEKING, onPlaybackSeeking, this);\n        eventBus.off(events.FRAGMENT_LOADING_COMPLETED, onSegmentMediaLoaded, this);\n        eventBus.off(events.TTML_TO_PARSE, onTTMLPreProcess, this);\n\n        // Reset FragmentInfoControllers\n        stopFragmentInfoControllers();\n    }\n\n    function createMssParser() {\n        mssParser = MssParser(context).create(config);\n        return mssParser;\n    }\n\n    instance = {\n        reset,\n        createMssParser,\n        createMssFragmentProcessor,\n        registerEvents\n    };\n\n    setup();\n\n    return instance;\n}\n\nMssHandler.__dashjs_factory_name = 'MssHandler';\nconst factory = dashjs.FactoryMaker.getClassFactory(MssHandler);\nfactory.errors = MssErrors;\ndashjs.FactoryMaker.updateClassFactory(MssHandler.__dashjs_factory_name, factory);\nexport default factory;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport MssHandler from './MssHandler.js';\n\n// Shove both of these into the global scope\nvar context = (typeof window !== 'undefined' && window) || global;\n\nvar dashjs = context.dashjs;\nif (!dashjs) {\n    dashjs = context.dashjs = {};\n}\n\ndashjs.MssHandler = MssHandler;\n\nexport default dashjs;\nexport { MssHandler };\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "__webpack_require__", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "constructor", "this", "streamId", "segmentType", "index", "NaN", "bytes", "start", "end", "duration", "representation", "endFragment", "HTTPRequest", "tcpid", "type", "url", "<PERSON><PERSON><PERSON>", "range", "trequest", "tresponse", "responsecode", "interval", "trace", "cmsd", "_stream", "_tfinish", "_mediaduration", "_responseHeaders", "_serviceLocation", "_fileLoaderType", "_resourceTimingValues", "GET", "HEAD", "MPD_TYPE", "XLINK_EXPANSION_TYPE", "INIT_SEGMENT_TYPE", "INDEX_SEGMENT_TYPE", "MEDIA_SEGMENT_TYPE", "BITSTREAM_SWITCHING_SEGMENT_TYPE", "MSS_FRAGMENT_INFO_SEGMENT_TYPE", "DVB_REPORTING_TYPE", "LICENSE", "CONTENT_STEERING_TYPE", "OTHER_TYPE", "FragmentRequest", "action", "ACTION_DOWNLOAD", "availabilityEndTime", "availabilityStartTime", "bandwidth", "bytesLoaded", "bytesTotal", "delayLoadingTime", "endDate", "firstByteDate", "mediaStartTime", "mediaType", "responseType", "retryAttempts", "serviceLocation", "startDate", "startTime", "timescale", "wallStartTime", "isInitializationRequest", "setInfo", "info", "init", "ACTION_COMPLETE", "FactoryMaker", "instance", "singletonContexts", "singletonFactories", "classFactories", "getSingletonInstance", "context", "className", "i", "name", "getFactoryByName", "factoriesArray", "updateFactory", "merge", "classConstructor", "args", "classInstance", "__dashjs_factory_name", "extensionObject", "extension", "override", "apply", "parent", "getClassName", "extend", "childInstance", "setSingletonInstance", "push", "deleteSingletonInstances", "filter", "x", "getSingletonFactory", "undefined", "getInstance", "arguments", "getSingletonFactoryByName", "updateSingletonFactory", "getClassFactory", "create", "getClassFactoryByName", "updateClassFactory", "MssFragmentInfoController", "config", "logger", "fragmentModel", "started", "loadFragmentTimeout", "startFragmentTime", "streamProcessor", "baseURLController", "debug", "stop", "clearTimeout", "loadNextFragmentInfo", "getRepresentationController", "getCurrentRepresentation", "adaptation", "period", "mpd", "manifest", "Period", "AdaptationSet", "segments", "SegmentTemplate", "SegmentTimeline", "S", "request", "segment", "t", "d", "adaptationIndex", "resolve", "path", "media", "replace", "tManifest", "getRequestForSegment", "length", "requestFragment", "getFragmentModel", "isFragmentLoadedOrPending", "executeRequest", "initialize", "getType", "controllerType", "fragmentInfoLoaded", "e", "response", "error", "deltaFragmentTime", "deltaTime", "delay", "Date", "getTime", "Math", "max", "setTimeout", "reset", "<PERSON><PERSON><PERSON><PERSON>", "code", "message", "data", "errors", "publicOnly", "err", "indexOf", "ErrorsBase", "super", "MSS_NO_TFRF_CODE", "MSS_UNSUPPORTED_CODEC_CODE", "MSS_NO_TFRF_MESSAGE", "MSS_UNSUPPORTED_CODEC_MESSAGE", "events", "evt", "EventsBase", "AST_IN_FUTURE", "BASE_URLS_UPDATED", "BUFFER_EMPTY", "BUFFER_LOADED", "BUFFER_LEVEL_STATE_CHANGED", "BUFFER_LEVEL_UPDATED", "DVB_FONT_DOWNLOAD_ADDED", "DVB_FONT_DOWNLOAD_COMPLETE", "DVB_FONT_DOWNLOAD_FAILED", "DYNAMIC_TO_STATIC", "ERROR", "FRAGMENT_LOADING_COMPLETED", "FRAGMENT_LOADING_PROGRESS", "FRAGMENT_LOADING_STARTED", "FRAGMENT_LOADING_ABANDONED", "LOG", "MANIFEST_LOADING_STARTED", "MANIFEST_LOADING_FINISHED", "MANIFEST_LOADED", "METRICS_CHANGED", "METRIC_CHANGED", "METRIC_ADDED", "METRIC_UPDATED", "PERIOD_SWITCH_STARTED", "PERIOD_SWITCH_COMPLETED", "QUALITY_CHANGE_REQUESTED", "QUALITY_CHANGE_RENDERED", "NEW_TRACK_SELECTED", "TRACK_CHANGE_RENDERED", "STREAM_INITIALIZING", "STREAM_UPDATED", "STREAM_ACTIVATED", "STREAM_DEACTIVATED", "STREAM_INITIALIZED", "STREAM_TEARDOWN_COMPLETE", "TEXT_TRACKS_ADDED", "TEXT_TRACK_ADDED", "CUE_ENTER", "CUE_EXIT", "THROUGHPUT_MEASUREMENT_STORED", "TTML_PARSED", "TTML_TO_PARSE", "CAPTION_RENDERED", "CAPTION_CONTAINER_RESIZE", "CAN_PLAY", "CAN_PLAY_THROUGH", "PLAYBACK_ENDED", "PLAYBACK_ERROR", "PLAYBACK_INITIALIZED", "PLAYBACK_NOT_ALLOWED", "PLAYBACK_METADATA_LOADED", "PLAYBACK_LOADED_DATA", "PLAYBACK_PAUSED", "PLAYBACK_PLAYING", "PLAYBACK_PROGRESS", "PLAYBACK_RATE_CHANGED", "PLAYBACK_SEEKED", "PLAYBACK_SEEKING", "PLAYBACK_STALLED", "PLAYBACK_STARTED", "PLAYBACK_TIME_UPDATED", "PLAYBACK_VOLUME_CHANGED", "PLAYBACK_WAITING", "MANIFEST_VALIDITY_CHANGED", "EVENT_MODE_ON_START", "EVENT_MODE_ON_RECEIVE", "CONFORMANCE_VIOLATION", "REPRESENTATION_SWITCH", "ADAPTATION_SET_REMOVED_NO_CAPABILITIES", "CONTENT_STEERING_REQUEST_COMPLETED", "INBAND_PRFT", "MANAGED_MEDIA_SOURCE_START_STREAMING", "MANAGED_MEDIA_SOURCE_END_STREAMING", "MssFragmentMoofProcessor", "dashMetrics", "playbackController", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eventBus", "ISOBoxer", "processTfrf", "tfrf", "tfdt", "timeShiftBufferDepth", "DashJSError", "MssErrors", "entries", "entry", "segmentTime", "endTime", "parseFloat", "fragment_absolute_time", "baseMediaDecodeTime", "updateDVR", "getStreamInfo", "manifestInfo", "fragment_duration", "lastSegment", "isPaused", "splice", "trigger", "Events", "sender", "newDuration", "dvrInfos", "getCurrentDVRInfo", "addDVRInfo", "updateCurrentTime", "getBoxOffset", "offset", "boxes", "size", "convertFragment", "isoFile", "parse<PERSON><PERSON>er", "tfhd", "fetch", "track_ID", "mediaInfo", "traf", "createFullBox", "version", "flags", "floor", "trun", "tfxd", "_parent", "sepiff", "usertype", "saio", "entry_count", "saiz", "sample_count", "default_sample_info_size", "sample_info_size", "NumberOfEntries", "moof", "<PERSON><PERSON><PERSON><PERSON>", "data_offset", "trafPosInMoof", "sencPosInTraf", "write", "updateSegmentList", "Error", "MssFragmentMoovProcessor", "constants", "adaptationSet", "contentProtection", "trackId", "protectionController", "createOriginalFormatBox", "sinf", "codec", "createBox", "data_format", "str", "charCodeAt", "stringToCharCode", "createSchemeTypeBox", "schm", "scheme_type", "scheme_version", "createSchemeInformationBox", "schi", "tenc", "default_IsEncrypted", "default_IV_size", "default_KID", "createTrackEncryptionBox", "hexStringto<PERSON>uffer", "buf", "Uint8Array", "parseInt", "generateMoov", "rep", "arrayBuffer", "ContentProtection", "createFile", "ftyp", "major_brand", "minor_version", "compatible_brands", "createFtypBox", "moov", "mvhd", "creation_time", "modification_time", "Infinity", "round", "rate", "volume", "reserved1", "reserved2", "matrix", "pre_defined", "next_track_ID", "createMvhdBox", "trak", "tkhd", "layer", "alternate_group", "reserved3", "width", "height", "createTkhdBox", "mdia", "mdhd", "language", "lang", "createMdhdBox", "hdlr", "VIDEO", "handler_type", "AUDIO", "id", "reserved", "createHdlrBox", "minf", "vmhd", "graphicsmode", "opcolor", "createVmhdBox", "smhd", "balance", "createSmhdBox", "dinf", "dref", "location", "createDrefBox", "stbl", "_data", "stsd", "codecs", "substring", "avc1", "data_reference_index", "pre_defined1", "pre_defined2", "horizresolution", "vertresolution", "frame_count", "compressorname", "depth", "pre_defined3", "naluBytes", "naluType", "avcC", "avc<PERSON><PERSON>th", "sps", "pps", "AVCProfileIndication", "AVCLevelIndication", "profile_compatibility", "nalus", "codecPrivateData", "split", "slice", "set", "n", "createAVC1ConfigurationRecord", "createAVCVisualSampleEntry", "mp4a", "channelcount", "audioChannels", "samplesize", "reserved_3", "samplerate", "audioSamplingRate", "esds", "audioSpecificConfig", "esdsLength", "createMPEG4AACESDescriptor", "createMP4AudioSampleEntry", "createSampleEntry", "createStsdBox", "trex", "default_sample_description_index", "default_sample_duration", "default_sample_size", "default_sample_flags", "createTrexBox", "keySystems", "pssh_bytes", "pssh", "parsed<PERSON><PERSON><PERSON>", "initData", "Utils", "appendBox", "createProtectionSystemSpecificHeaderBox", "getSupportedKeySystemMetadataFromContentProtection", "createMoovBox", "arrayEqual", "arr1", "arr2", "every", "element", "saioProcessor", "_procFullBox", "_procField", "_procFieldArray", "saizProcessor", "sencProcessor", "_procEntries", "_procEntryField", "_procSubEntries", "clearAndCryptedData", "uuidProcessor", "_parsing", "fragment_count", "MssFragmentProcessor", "mssFragmentMoovProcessor", "mssFragmentMoofProcessor", "processFragment", "addBoxProcessor", "bigInt", "BASE", "LOG_BASE", "MAX_INT", "MAX_INT_ARR", "smallToArray", "DEFAULT_ALPHABET", "supportsNativeBigInt", "BigInt", "Integer", "v", "radix", "alphabet", "caseSensitive", "parseValue", "parseBase", "BigInteger", "value", "sign", "isSmall", "SmallInteger", "NativeBigInt", "isPrecise", "arrayToSmall", "arr", "trim", "compareAbs", "createArray", "Array", "truncate", "ceil", "add", "a", "b", "sum", "l_a", "l_b", "r", "carry", "base", "addAny", "addSmall", "l", "subtract", "difference", "a_l", "b_l", "borrow", "subtractSmall", "multiplyLong", "product", "a_i", "j", "multiplySmall", "shiftLeft", "concat", "multiplyK<PERSON><PERSON><PERSON>", "y", "c", "ac", "bd", "abcd", "multiplySmallAndArray", "square", "divModSmall", "lambda", "q", "remainder", "divisor", "quotient", "divModAny", "negate", "abs", "comparison", "quotientDigit", "shift", "result", "divisorMostSignificantDigit", "divMod1", "guess", "xlen", "highx", "highy", "check", "part", "unshift", "reverse", "divMod2", "qSign", "mod", "mSign", "isBasicPrime", "isUnit", "equals", "isEven", "isDivisibleBy", "lesser", "millerRabinTest", "nPrev", "prev", "divide", "next", "modPow", "plus", "subtractAny", "minus", "small", "multiply", "l1", "l2", "times", "_multiplyBySmall", "divmod", "over", "pow", "toString", "_0", "_1", "_2", "isNegative", "exp", "isZero", "isPositive", "isOdd", "compare", "compareTo", "eq", "notEquals", "neq", "greater", "gt", "lt", "greaterOrEquals", "geq", "lesserOrEquals", "leq", "isPrime", "strict", "bits", "bitLength", "logN", "log", "toJSNumber", "isProbablePrime", "iterations", "randBetween", "modInv", "lastT", "lastR", "zero", "newT", "one", "newR", "powersOfTwo", "powers2Length", "highestPower2", "shift_isSmall", "bitwise", "fn", "xSign", "ySign", "xRem", "not", "yRem", "xDigit", "yDigit", "xDivMod", "yDivMod", "String", "shiftRight", "remQuo", "and", "or", "xor", "LOBMASK_I", "LOBMASK_BI", "roughLOB", "integerLogarithm", "tmp", "p", "min", "gcd", "text", "toLowerCase", "absBase", "alphabetValues", "digits", "parseBaseFromArray", "val", "toBase", "map", "valueOf", "neg", "Number", "out", "left", "digit", "toBaseString", "stringify", "join", "parseStringValue", "decimalPlace", "test", "parseNumberValue", "toArray", "toJSON", "minusOne", "lcm", "isInstance", "low", "random", "restricted", "top", "fromArray", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BASE64", "manifestModel", "settings", "SUPPORTED_CODECS", "ROLE", "ACCESSIBILITY", "samplingFrequencyIndex", "mimeTypeMap", "initialBufferSettings", "getAttributeAsBoolean", "node", "attrName", "getAttribute", "mapAdaptationSet", "streamIndex", "representations", "segmentTemplate", "qualityLevels", "fallBackId", "contentType", "mimeType", "subType", "max<PERSON><PERSON><PERSON>", "maxHeight", "Role", "schemeIdUri", "Accessibility", "mediaUrl", "streamIndexTimeScale", "segmentTimeline", "chunks", "getElementsByTagName", "prevSegment", "MAX_SAFE_INTEGER", "mapSegmentTimeline", "availabilityTimeOffset", "mapSegmentTemplate", "BaseURL", "Id", "mapRepresentation", "Representation", "qualityLevel", "fourCCValue", "isNaN", "toUpperCase", "warn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "avcoti", "exec", "substr", "getH264Codec", "samplingRate", "codecPrivateDataHex", "arr16", "indexFreq", "extensionSamplingFrequencyIndex", "objectType", "Channels", "Uint16Array", "setAttribute", "getAACCodec", "STPP", "swapBytes", "pos1", "pos2", "temp", "processManifest", "xmlDoc", "contentProtections", "smoothStreamingMedia", "protection", "adaptations", "KID", "timestampOffset", "segmentDuration", "protectionHeader", "protocol", "profiles", "dvr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mediaPresentationDuration", "minBufferTime", "ttmlTimeIsRelative", "refreshManifestOnSwitchTrack", "doNotUpdateDVRWindowOnBufferUpdated", "ignorePostponeTimePeriod", "streams", "mapPeriod", "<PERSON><PERSON><PERSON><PERSON>", "pr<PERSON><PERSON><PERSON>", "wrm<PERSON><PERSON>er", "xmlReader", "uuid", "decodeArray", "recordCount", "recordType", "recordLength", "recordValue", "subarray", "getWRMHeaderFromPRHeader", "buffer", "fromCharCode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "querySelector", "textContent", "getKIDFromProtectionHeader", "pro", "__text", "__prefix", "createPRContentProtection", "widevineCP", "wv<PERSON>en<PERSON><PERSON>eader", "encodeASCII", "createWidevineContentProtection", "initialization", "targetLiveDelay", "streaming", "liveDelay", "liveDelayFragmentCount", "targetDelayCapping", "bufferTime", "bufferTimeDefault", "bufferTimeAtTopQuality", "bufferTimeAtTopQualityLongForm", "calcFromSegmentTimeline", "timeShiftBuffer", "update", "prevManifest", "getValue", "presentationTimeOffset", "parse", "window", "performance", "now", "parseDOM", "xmlParseTime", "mss2dashTime", "toPrecision", "getIron", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initSegmentType", "streamController", "mss<PERSON><PERSON><PERSON>", "mssFragmentProcessor", "fragmentInfoControllers", "getStreamProcessor", "getActiveStreamProcessors", "processor", "getFragmentInfoController", "controller", "startFragmentInfoControllers", "for<PERSON>ach", "TEXT", "fragmentInfoController", "onInitFragmentNeeded", "getMediaInfo", "chunk", "DataChunk", "createDataChunk", "streamInfo", "INIT_FRAGMENT_LOADED", "onSegmentMediaLoaded", "isDynamic", "dvrWindowSize", "onPlaybackPaused", "getIsDynamic", "onPlaybackSeeking", "onTTMLPreProcess", "ttmlSubtitles", "off", "INIT_FRAGMENT_NEEDED", "createMssParser", "createMssFragmentProcessor", "registerEvents", "on", "priority", "dashjs", "EVENT_PRIORITY_HIGH", "global"], "sourceRoot": ""}