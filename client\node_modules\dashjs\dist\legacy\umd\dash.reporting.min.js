!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.dashjs=e():t.dashjs=e()}(self,(function(){return function(){var t={3282:function(t,e,r){"use strict";var n=r(5429);function i(t){if("string"!=typeof t)throw new TypeError("Path must be a string. Received "+JSON.stringify(t))}function o(t,e){for(var r,n="",i=0,o=-1,a=0,u=0;u<=t.length;++u){if(u<t.length)r=t.charCodeAt(u);else{if(47===r)break;r=47}if(47===r){if(o===u-1||1===a);else if(o!==u-1&&2===a){if(n.length<2||2!==i||46!==n.charCodeAt(n.length-1)||46!==n.charCodeAt(n.length-2))if(n.length>2){var s=n.lastIndexOf("/");if(s!==n.length-1){-1===s?(n="",i=0):i=(n=n.slice(0,s)).length-1-n.lastIndexOf("/"),o=u,a=0;continue}}else if(2===n.length||1===n.length){n="",i=0,o=u,a=0;continue}e&&(n.length>0?n+="/..":n="..",i=2)}else n.length>0?n+="/"+t.slice(o+1,u):n=t.slice(o+1,u),i=u-o-1;o=u,a=0}else 46===r&&-1!==a?++a:a=-1}return n}r(6280),r(4782),r(2010),r(3110);var a={resolve:function(){for(var t,e="",r=!1,n=arguments.length-1;n>=-1&&!r;n--){var a;n>=0?a=arguments[n]:(void 0===t&&(t=process.cwd()),a=t),i(a),0!==a.length&&(e=a+"/"+e,r=47===a.charCodeAt(0))}return e=o(e,!r),r?e.length>0?"/"+e:"/":e.length>0?e:"."},normalize:function(t){if(i(t),0===t.length)return".";var e=47===t.charCodeAt(0),r=47===t.charCodeAt(t.length-1);return 0!==(t=o(t,!e)).length||e||(t="."),t.length>0&&r&&(t+="/"),e?"/"+t:t},isAbsolute:function(t){return i(t),t.length>0&&47===t.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var t,e=0;e<arguments.length;++e){var r=arguments[e];i(r),r.length>0&&(void 0===t?t=r:t+="/"+r)}return void 0===t?".":a.normalize(t)},relative:function(t,e){if(i(t),i(e),t===e)return"";if((t=a.resolve(t))===(e=a.resolve(e)))return"";for(var r=1;r<t.length&&47===t.charCodeAt(r);++r);for(var n=t.length,o=n-r,u=1;u<e.length&&47===e.charCodeAt(u);++u);for(var s=e.length-u,c=o<s?o:s,f=-1,l=0;l<=c;++l){if(l===c){if(s>c){if(47===e.charCodeAt(u+l))return e.slice(u+l+1);if(0===l)return e.slice(u+l)}else o>c&&(47===t.charCodeAt(r+l)?f=l:0===l&&(f=0));break}var h=t.charCodeAt(r+l);if(h!==e.charCodeAt(u+l))break;47===h&&(f=l)}var d="";for(l=r+f+1;l<=n;++l)l!==n&&47!==t.charCodeAt(l)||(0===d.length?d+="..":d+="/..");return d.length>0?d+e.slice(u+f):(u+=f,47===e.charCodeAt(u)&&++u,e.slice(u))},_makeLong:function(t){return t},dirname:function(t){if(i(t),0===t.length)return".";for(var e=t.charCodeAt(0),r=47===e,n=-1,o=!0,a=t.length-1;a>=1;--a)if(47===(e=t.charCodeAt(a))){if(!o){n=a;break}}else o=!1;return-1===n?r?"/":".":r&&1===n?"//":t.slice(0,n)},basename:function(t,e){if(void 0!==e&&"string"!=typeof e)throw new TypeError('"ext" argument must be a string');i(t);var r,n=0,o=-1,a=!0;if(void 0!==e&&e.length>0&&e.length<=t.length){if(e.length===t.length&&e===t)return"";var u=e.length-1,s=-1;for(r=t.length-1;r>=0;--r){var c=t.charCodeAt(r);if(47===c){if(!a){n=r+1;break}}else-1===s&&(a=!1,s=r+1),u>=0&&(c===e.charCodeAt(u)?-1==--u&&(o=r):(u=-1,o=s))}return n===o?o=s:-1===o&&(o=t.length),t.slice(n,o)}for(r=t.length-1;r>=0;--r)if(47===t.charCodeAt(r)){if(!a){n=r+1;break}}else-1===o&&(a=!1,o=r+1);return-1===o?"":t.slice(n,o)},extname:function(t){i(t);for(var e=-1,r=0,n=-1,o=!0,a=0,u=t.length-1;u>=0;--u){var s=t.charCodeAt(u);if(47!==s)-1===n&&(o=!1,n=u+1),46===s?-1===e?e=u:1!==a&&(a=1):-1!==e&&(a=-1);else if(!o){r=u+1;break}}return-1===e||-1===n||0===a||1===a&&e===n-1&&e===r+1?"":t.slice(e,n)},format:function(t){if(null===t||"object"!==n(t))throw new TypeError('The "pathObject" argument must be of type Object. Received type '+n(t));return function(t,e){var r=e.dir||e.root,n=e.base||(e.name||"")+(e.ext||"");return r?r===e.root?r+n:r+"/"+n:n}(0,t)},parse:function(t){i(t);var e={root:"",dir:"",base:"",ext:"",name:""};if(0===t.length)return e;var r,n=t.charCodeAt(0),o=47===n;o?(e.root="/",r=1):r=0;for(var a=-1,u=0,s=-1,c=!0,f=t.length-1,l=0;f>=r;--f)if(47!==(n=t.charCodeAt(f)))-1===s&&(c=!1,s=f+1),46===n?-1===a?a=f:1!==l&&(l=1):-1!==a&&(l=-1);else if(!c){u=f+1;break}return-1===a||-1===s||0===l||1===l&&a===s-1&&a===u+1?-1!==s&&(e.base=e.name=0===u&&o?t.slice(1,s):t.slice(u,s)):(0===u&&o?(e.name=t.slice(1,a),e.base=t.slice(1,s)):(e.name=t.slice(u,a),e.base=t.slice(u,s)),e.ext=t.slice(a,s)),u>0?e.dir=t.slice(0,u-1):o&&(e.dir="/"),e},sep:"/",delimiter:":",win32:null,posix:null};a.posix=a,t.exports=a},8571:function(t,e,r){var n;t=r.nmd(t);var i=r(5429);r(8706),r(2010),r(7495),r(906),r(5440),function(o,a){"use strict";var u="function",s="undefined",c="object",f="string",l="major",h="model",d="name",p="type",v="vendor",g="version",y="architecture",E="console",A="mobile",m="tablet",T="smarttv",_="wearable",b="embedded",w="Amazon",R="Apple",S="ASUS",I="BlackBerry",C="Browser",x="Chrome",O="Firefox",D="Google",L="Huawei",N="LG",M="Microsoft",P="Motorola",U="Opera",k="Samsung",F="Sharp",B="Sony",G="Xiaomi",H="Zebra",j="Facebook",V="Chromium OS",Y="Mac OS",W=function(t){for(var e={},r=0;r<t.length;r++)e[t[r].toUpperCase()]=t[r];return e},z=function(t,e){return i(t)===f&&-1!==q(e).indexOf(q(t))},q=function(t){return t.toLowerCase()},K=function(t,e){if(i(t)===f)return t=t.replace(/^\s\s*/,""),i(e)===s?t:t.substring(0,500)},X=function(t,e){for(var r,n,o,s,f,l,h=0;h<e.length&&!f;){var d=e[h],p=e[h+1];for(r=n=0;r<d.length&&!f&&d[r];)if(f=d[r++].exec(t))for(o=0;o<p.length;o++)l=f[++n],s=p[o],i(s)===c&&s.length>0?2===s.length?i(s[1])==u?this[s[0]]=s[1].call(this,l):this[s[0]]=s[1]:3===s.length?i(s[1])!==u||s[1].exec&&s[1].test?this[s[0]]=l?l.replace(s[1],s[2]):a:this[s[0]]=l?s[1].call(this,l,s[2]):a:4===s.length&&(this[s[0]]=l?s[3].call(this,l.replace(s[1],s[2])):a):this[s]=l||a;h+=2}},Q=function(t,e){for(var r in e)if(i(e[r])===c&&e[r].length>0){for(var n=0;n<e[r].length;n++)if(z(e[r][n],t))return"?"===r?a:r}else if(z(e[r],t))return"?"===r?a:r;return t},$={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Z={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[g,[d,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[g,[d,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[d,g],[/opios[\/ ]+([\w\.]+)/i],[g,[d,U+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[g,[d,U+" GX"]],[/\bopr\/([\w\.]+)/i],[g,[d,U]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[g,[d,"Baidu"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim)\s?(?:browser)?[\/ ]?([\w\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[d,g],[/\bddg\/([\w\.]+)/i],[g,[d,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[g,[d,"UC"+C]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[g,[d,"WeChat"]],[/konqueror\/([\w\.]+)/i],[g,[d,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[g,[d,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[g,[d,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[g,[d,"Smart Lenovo "+C]],[/(avast|avg)\/([\w\.]+)/i],[[d,/(.+)/,"$1 Secure "+C],g],[/\bfocus\/([\w\.]+)/i],[g,[d,O+" Focus"]],[/\bopt\/([\w\.]+)/i],[g,[d,U+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[g,[d,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[g,[d,"Dolphin"]],[/coast\/([\w\.]+)/i],[g,[d,U+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[g,[d,"MIUI "+C]],[/fxios\/([-\w\.]+)/i],[g,[d,O]],[/\bqihu|(qi?ho?o?|360)browser/i],[[d,"360 "+C]],[/(oculus|sailfish|huawei|vivo)browser\/([\w\.]+)/i],[[d,/(.+)/,"$1 "+C],g],[/samsungbrowser\/([\w\.]+)/i],[g,[d,k+" Internet"]],[/(comodo_dragon)\/([\w\.]+)/i],[[d,/_/g," "],g],[/metasr[\/ ]?([\d\.]+)/i],[g,[d,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[d,"Sogou Mobile"],g],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345Explorer)[\/ ]?([\w\.]+)/i],[d,g],[/(lbbrowser)/i,/\[(linkedin)app\]/i],[d],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[d,j],g],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[d,g],[/\bgsa\/([\w\.]+) .*safari\//i],[g,[d,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[g,[d,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[g,[d,x+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[d,x+" WebView"],g],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[g,[d,"Android "+C]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[d,g],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[g,[d,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[g,d],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[d,[g,Q,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[d,g],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[d,"Netscape"],g],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[g,[d,O+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[d,g],[/(cobalt)\/([\w\.]+)/i],[d,[g,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[y,"amd64"]],[/(ia32(?=;))/i],[[y,q]],[/((?:i[346]|x)86)[;\)]/i],[[y,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[y,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[y,"armhf"]],[/windows (ce|mobile); ppc;/i],[[y,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[y,/ower/,"",q]],[/(sun4\w)[;\)]/i],[[y,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[y,q]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[h,[v,k],[p,m]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[h,[v,k],[p,A]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[h,[v,R],[p,A]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[h,[v,R],[p,m]],[/(macintosh);/i],[h,[v,R]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[h,[v,F],[p,A]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[h,[v,L],[p,m]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[h,[v,L],[p,A]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[h,/_/g," "],[v,G],[p,A]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[h,/_/g," "],[v,G],[p,m]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[h,[v,"OPPO"],[p,A]],[/\b(opd2\d{3}a?) bui/i],[h,[v,"OPPO"],[p,m]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[h,[v,"Vivo"],[p,A]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[h,[v,"Realme"],[p,A]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[h,[v,P],[p,A]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[h,[v,P],[p,m]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[h,[v,N],[p,m]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[h,[v,N],[p,A]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[h,[v,"Lenovo"],[p,m]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[h,/_/g," "],[v,"Nokia"],[p,A]],[/(pixel c)\b/i],[h,[v,D],[p,m]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[h,[v,D],[p,A]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[h,[v,B],[p,A]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[h,"Xperia Tablet"],[v,B],[p,m]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[h,[v,"OnePlus"],[p,A]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[h,[v,w],[p,m]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[h,/(.+)/g,"Fire Phone $1"],[v,w],[p,A]],[/(playbook);[-\w\),; ]+(rim)/i],[h,v,[p,m]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[h,[v,I],[p,A]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[h,[v,S],[p,m]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[h,[v,S],[p,A]],[/(nexus 9)/i],[h,[v,"HTC"],[p,m]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[v,[h,/_/g," "],[p,A]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[h,[v,"Acer"],[p,m]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[h,[v,"Meizu"],[p,A]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[h,[v,"Ulefone"],[p,A]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[v,h,[p,A]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[v,h,[p,m]],[/(surface duo)/i],[h,[v,M],[p,m]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[h,[v,"Fairphone"],[p,A]],[/(u304aa)/i],[h,[v,"AT&T"],[p,A]],[/\bsie-(\w*)/i],[h,[v,"Siemens"],[p,A]],[/\b(rct\w+) b/i],[h,[v,"RCA"],[p,m]],[/\b(venue[\d ]{2,7}) b/i],[h,[v,"Dell"],[p,m]],[/\b(q(?:mv|ta)\w+) b/i],[h,[v,"Verizon"],[p,m]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[h,[v,"Barnes & Noble"],[p,m]],[/\b(tm\d{3}\w+) b/i],[h,[v,"NuVision"],[p,m]],[/\b(k88) b/i],[h,[v,"ZTE"],[p,m]],[/\b(nx\d{3}j) b/i],[h,[v,"ZTE"],[p,A]],[/\b(gen\d{3}) b.+49h/i],[h,[v,"Swiss"],[p,A]],[/\b(zur\d{3}) b/i],[h,[v,"Swiss"],[p,m]],[/\b((zeki)?tb.*\b) b/i],[h,[v,"Zeki"],[p,m]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[v,"Dragon Touch"],h,[p,m]],[/\b(ns-?\w{0,9}) b/i],[h,[v,"Insignia"],[p,m]],[/\b((nxa|next)-?\w{0,9}) b/i],[h,[v,"NextBook"],[p,m]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[v,"Voice"],h,[p,A]],[/\b(lvtel\-)?(v1[12]) b/i],[[v,"LvTel"],h,[p,A]],[/\b(ph-1) /i],[h,[v,"Essential"],[p,A]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[h,[v,"Envizen"],[p,m]],[/\b(trio[-\w\. ]+) b/i],[h,[v,"MachSpeed"],[p,m]],[/\btu_(1491) b/i],[h,[v,"Rotor"],[p,m]],[/(shield[\w ]+) b/i],[h,[v,"Nvidia"],[p,m]],[/(sprint) (\w+)/i],[v,h,[p,A]],[/(kin\.[onetw]{3})/i],[[h,/\./g," "],[v,M],[p,A]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[h,[v,H],[p,m]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[h,[v,H],[p,A]],[/smart-tv.+(samsung)/i],[v,[p,T]],[/hbbtv.+maple;(\d+)/i],[[h,/^/,"SmartTV"],[v,k],[p,T]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[v,N],[p,T]],[/(apple) ?tv/i],[v,[h,R+" TV"],[p,T]],[/crkey/i],[[h,x+"cast"],[v,D],[p,T]],[/droid.+aft(\w+)( bui|\))/i],[h,[v,w],[p,T]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[h,[v,F],[p,T]],[/(bravia[\w ]+)( bui|\))/i],[h,[v,B],[p,T]],[/(mitv-\w{5}) bui/i],[h,[v,G],[p,T]],[/Hbbtv.*(technisat) (.*);/i],[v,h,[p,T]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[v,K],[h,K],[p,T]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[p,T]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[v,h,[p,E]],[/droid.+; (shield) bui/i],[h,[v,"Nvidia"],[p,E]],[/(playstation [345portablevi]+)/i],[h,[v,B],[p,E]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[h,[v,M],[p,E]],[/((pebble))app/i],[v,h,[p,_]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[h,[v,R],[p,_]],[/droid.+; (glass) \d/i],[h,[v,D],[p,_]],[/droid.+; (wt63?0{2,3})\)/i],[h,[v,H],[p,_]],[/(quest( \d| pro)?)/i],[h,[v,j],[p,_]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[v,[p,b]],[/(aeobc)\b/i],[h,[v,w],[p,b]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[h,[p,A]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[h,[p,m]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[p,m]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[p,A]],[/(android[-\w\. ]{0,9});.+buil/i],[h,[v,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[g,[d,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[g,[d,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[d,g],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[g,d]],os:[[/microsoft (windows) (vista|xp)/i],[d,g],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[d,[g,Q,$]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[g,Q,$],[d,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[g,/_/g,"."],[d,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[d,Y],[g,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[g,d],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[d,g],[/\(bb(10);/i],[g,[d,I]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[g,[d,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[g,[d,O+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[g,[d,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[g,[d,"watchOS"]],[/crkey\/([\d\.]+)/i],[g,[d,x+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[d,V],g],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[d,g],[/(sunos) ?([\w\.\d]*)/i],[[d,"Solaris"],g],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[d,g]]},J=function(t,e){if(i(t)===c&&(e=t,t=a),!(this instanceof J))return new J(t,e).getResult();var r=i(o)!==s&&o.navigator?o.navigator:a,n=t||(r&&r.userAgent?r.userAgent:""),E=r&&r.userAgentData?r.userAgentData:a,T=e?function(t,e){var r={};for(var n in t)e[n]&&e[n].length%2==0?r[n]=e[n].concat(t[n]):r[n]=t[n];return r}(Z,e):Z,_=r&&r.userAgent==n;return this.getBrowser=function(){var t,e={};return e[d]=a,e[g]=a,X.call(e,n,T.browser),e[l]=(t=e[g],i(t)===f?t.replace(/[^\d\.]/g,"").split(".")[0]:a),_&&r&&r.brave&&i(r.brave.isBrave)==u&&(e[d]="Brave"),e},this.getCPU=function(){var t={};return t[y]=a,X.call(t,n,T.cpu),t},this.getDevice=function(){var t={};return t[v]=a,t[h]=a,t[p]=a,X.call(t,n,T.device),_&&!t[p]&&E&&E.mobile&&(t[p]=A),_&&"Macintosh"==t[h]&&r&&i(r.standalone)!==s&&r.maxTouchPoints&&r.maxTouchPoints>2&&(t[h]="iPad",t[p]=m),t},this.getEngine=function(){var t={};return t[d]=a,t[g]=a,X.call(t,n,T.engine),t},this.getOS=function(){var t={};return t[d]=a,t[g]=a,X.call(t,n,T.os),_&&!t[d]&&E&&E.platform&&"Unknown"!=E.platform&&(t[d]=E.platform.replace(/chrome os/i,V).replace(/macos/i,Y)),t},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(t){return n=i(t)===f&&t.length>500?K(t,500):t,this},this.setUA(n),this};J.VERSION="1.0.38",J.BROWSER=W([d,g,l]),J.CPU=W([y]),J.DEVICE=W([h,v,p,E,A,T,m,_,b]),J.ENGINE=J.OS=W([d,g]),i(e)!==s?(i(t)!==s&&t.exports&&(e=t.exports=J),e.UAParser=J):i(r.amdD)===u&&r.amdO?(n=function(){return J}.call(e,r,e,t))===a||(t.exports=n):i(o)!==s&&(o.UAParser=J);var tt=i(o)!==s&&(o.jQuery||o.Zepto);if(tt&&!tt.ua){var et=new J;tt.ua=et.getResult(),tt.ua.get=function(){return et.getUA()},tt.ua.set=function(t){et.setUA(t);var e=et.getResult();for(var r in e)tt.ua[r]=e[r]}}}("object"===("undefined"==typeof window?"undefined":i(window))?window:this)},9306:function(t,e,r){"use strict";var n=r(4901),i=r(6823),o=TypeError;t.exports=function(t){if(n(t))return t;throw new o(i(t)+" is not a function")}},5548:function(t,e,r){"use strict";var n=r(3517),i=r(6823),o=TypeError;t.exports=function(t){if(n(t))return t;throw new o(i(t)+" is not a constructor")}},3506:function(t,e,r){"use strict";var n=r(3925),i=String,o=TypeError;t.exports=function(t){if(n(t))return t;throw new o("Can't set "+i(t)+" as a prototype")}},6469:function(t,e,r){"use strict";var n=r(8227),i=r(2360),o=r(4913).f,a=n("unscopables"),u=Array.prototype;void 0===u[a]&&o(u,a,{configurable:!0,value:i(null)}),t.exports=function(t){u[a][t]=!0}},7829:function(t,e,r){"use strict";var n=r(8183).charAt;t.exports=function(t,e,r){return e+(r?n(t,e).length:1)}},679:function(t,e,r){"use strict";var n=r(1625),i=TypeError;t.exports=function(t,e){if(n(e,t))return t;throw new i("Incorrect invocation")}},8551:function(t,e,r){"use strict";var n=r(34),i=String,o=TypeError;t.exports=function(t){if(n(t))return t;throw new o(i(t)+" is not an object")}},7811:function(t){"use strict";t.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},7394:function(t,e,r){"use strict";var n=r(4576),i=r(6706),o=r(2195),a=n.ArrayBuffer,u=n.TypeError;t.exports=a&&i(a.prototype,"byteLength","get")||function(t){if("ArrayBuffer"!==o(t))throw new u("ArrayBuffer expected");return t.byteLength}},3238:function(t,e,r){"use strict";var n=r(4576),i=r(7476),o=r(7394),a=n.ArrayBuffer,u=a&&a.prototype,s=u&&i(u.slice);t.exports=function(t){if(0!==o(t))return!1;if(!s)return!1;try{return s(t,0,0),!1}catch(t){return!0}}},5169:function(t,e,r){"use strict";var n=r(3238),i=TypeError;t.exports=function(t){if(n(t))throw new i("ArrayBuffer is detached");return t}},5636:function(t,e,r){"use strict";var n=r(4576),i=r(9504),o=r(6706),a=r(7696),u=r(5169),s=r(7394),c=r(4483),f=r(1548),l=n.structuredClone,h=n.ArrayBuffer,d=n.DataView,p=Math.min,v=h.prototype,g=d.prototype,y=i(v.slice),E=o(v,"resizable","get"),A=o(v,"maxByteLength","get"),m=i(g.getInt8),T=i(g.setInt8);t.exports=(f||c)&&function(t,e,r){var n,i=s(t),o=void 0===e?i:a(e),v=!E||!E(t);if(u(t),f&&(t=l(t,{transfer:[t]}),i===o&&(r||v)))return t;if(i>=o&&(!r||v))n=y(t,0,o);else{var g=r&&!v&&A?{maxByteLength:A(t)}:void 0;n=new h(o,g);for(var _=new d(t),b=new d(n),w=p(o,i),R=0;R<w;R++)T(b,R,m(_,R))}return f||c(t),n}},4644:function(t,e,r){"use strict";var n,i,o,a=r(7811),u=r(3724),s=r(4576),c=r(4901),f=r(34),l=r(9297),h=r(6955),d=r(6823),p=r(6699),v=r(6840),g=r(2106),y=r(1625),E=r(2787),A=r(2967),m=r(8227),T=r(3392),_=r(1181),b=_.enforce,w=_.get,R=s.Int8Array,S=R&&R.prototype,I=s.Uint8ClampedArray,C=I&&I.prototype,x=R&&E(R),O=S&&E(S),D=Object.prototype,L=s.TypeError,N=m("toStringTag"),M=T("TYPED_ARRAY_TAG"),P="TypedArrayConstructor",U=a&&!!A&&"Opera"!==h(s.opera),k=!1,F={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},B={BigInt64Array:8,BigUint64Array:8},G=function(t){var e=E(t);if(f(e)){var r=w(e);return r&&l(r,P)?r[P]:G(e)}},H=function(t){if(!f(t))return!1;var e=h(t);return l(F,e)||l(B,e)};for(n in F)(o=(i=s[n])&&i.prototype)?b(o)[P]=i:U=!1;for(n in B)(o=(i=s[n])&&i.prototype)&&(b(o)[P]=i);if((!U||!c(x)||x===Function.prototype)&&(x=function(){throw new L("Incorrect invocation")},U))for(n in F)s[n]&&A(s[n],x);if((!U||!O||O===D)&&(O=x.prototype,U))for(n in F)s[n]&&A(s[n].prototype,O);if(U&&E(C)!==O&&A(C,O),u&&!l(O,N))for(n in k=!0,g(O,N,{configurable:!0,get:function(){return f(this)?this[M]:void 0}}),F)s[n]&&p(s[n],M,n);t.exports={NATIVE_ARRAY_BUFFER_VIEWS:U,TYPED_ARRAY_TAG:k&&M,aTypedArray:function(t){if(H(t))return t;throw new L("Target is not a typed array")},aTypedArrayConstructor:function(t){if(c(t)&&(!A||y(x,t)))return t;throw new L(d(t)+" is not a typed array constructor")},exportTypedArrayMethod:function(t,e,r,n){if(u){if(r)for(var i in F){var o=s[i];if(o&&l(o.prototype,t))try{delete o.prototype[t]}catch(r){try{o.prototype[t]=e}catch(t){}}}O[t]&&!r||v(O,t,r?e:U&&S[t]||e,n)}},exportTypedArrayStaticMethod:function(t,e,r){var n,i;if(u){if(A){if(r)for(n in F)if((i=s[n])&&l(i,t))try{delete i[t]}catch(t){}if(x[t]&&!r)return;try{return v(x,t,r?e:U&&x[t]||e)}catch(t){}}for(n in F)!(i=s[n])||i[t]&&!r||v(i,t,e)}},getTypedArrayConstructor:G,isView:function(t){if(!f(t))return!1;var e=h(t);return"DataView"===e||l(F,e)||l(B,e)},isTypedArray:H,TypedArray:x,TypedArrayPrototype:O}},6346:function(t,e,r){"use strict";var n=r(4576),i=r(9504),o=r(3724),a=r(7811),u=r(350),s=r(6699),c=r(2106),f=r(6279),l=r(9039),h=r(679),d=r(1291),p=r(8014),v=r(7696),g=r(5617),y=r(8490),E=r(2787),A=r(2967),m=r(4373),T=r(7680),_=r(3167),b=r(7740),w=r(687),R=r(1181),S=u.PROPER,I=u.CONFIGURABLE,C="ArrayBuffer",x="DataView",O="prototype",D="Wrong index",L=R.getterFor(C),N=R.getterFor(x),M=R.set,P=n[C],U=P,k=U&&U[O],F=n[x],B=F&&F[O],G=Object.prototype,H=n.Array,j=n.RangeError,V=i(m),Y=i([].reverse),W=y.pack,z=y.unpack,q=function(t){return[255&t]},K=function(t){return[255&t,t>>8&255]},X=function(t){return[255&t,t>>8&255,t>>16&255,t>>24&255]},Q=function(t){return t[3]<<24|t[2]<<16|t[1]<<8|t[0]},$=function(t){return W(g(t),23,4)},Z=function(t){return W(t,52,8)},J=function(t,e,r){c(t[O],e,{configurable:!0,get:function(){return r(this)[e]}})},tt=function(t,e,r,n){var i=N(t),o=v(r),a=!!n;if(o+e>i.byteLength)throw new j(D);var u=i.bytes,s=o+i.byteOffset,c=T(u,s,s+e);return a?c:Y(c)},et=function(t,e,r,n,i,o){var a=N(t),u=v(r),s=n(+i),c=!!o;if(u+e>a.byteLength)throw new j(D);for(var f=a.bytes,l=u+a.byteOffset,h=0;h<e;h++)f[l+h]=s[c?h:e-h-1]};if(a){var rt=S&&P.name!==C;l((function(){P(1)}))&&l((function(){new P(-1)}))&&!l((function(){return new P,new P(1.5),new P(NaN),1!==P.length||rt&&!I}))?rt&&I&&s(P,"name",C):((U=function(t){return h(this,k),_(new P(v(t)),this,U)})[O]=k,k.constructor=U,b(U,P)),A&&E(B)!==G&&A(B,G);var nt=new F(new U(2)),it=i(B.setInt8);nt.setInt8(0,2147483648),nt.setInt8(1,2147483649),!nt.getInt8(0)&&nt.getInt8(1)||f(B,{setInt8:function(t,e){it(this,t,e<<24>>24)},setUint8:function(t,e){it(this,t,e<<24>>24)}},{unsafe:!0})}else k=(U=function(t){h(this,k);var e=v(t);M(this,{type:C,bytes:V(H(e),0),byteLength:e}),o||(this.byteLength=e,this.detached=!1)})[O],B=(F=function(t,e,r){h(this,B),h(t,k);var n=L(t),i=n.byteLength,a=d(e);if(a<0||a>i)throw new j("Wrong offset");if(a+(r=void 0===r?i-a:p(r))>i)throw new j("Wrong length");M(this,{type:x,buffer:t,byteLength:r,byteOffset:a,bytes:n.bytes}),o||(this.buffer=t,this.byteLength=r,this.byteOffset=a)})[O],o&&(J(U,"byteLength",L),J(F,"buffer",N),J(F,"byteLength",N),J(F,"byteOffset",N)),f(B,{getInt8:function(t){return tt(this,1,t)[0]<<24>>24},getUint8:function(t){return tt(this,1,t)[0]},getInt16:function(t){var e=tt(this,2,t,arguments.length>1&&arguments[1]);return(e[1]<<8|e[0])<<16>>16},getUint16:function(t){var e=tt(this,2,t,arguments.length>1&&arguments[1]);return e[1]<<8|e[0]},getInt32:function(t){return Q(tt(this,4,t,arguments.length>1&&arguments[1]))},getUint32:function(t){return Q(tt(this,4,t,arguments.length>1&&arguments[1]))>>>0},getFloat32:function(t){return z(tt(this,4,t,arguments.length>1&&arguments[1]),23)},getFloat64:function(t){return z(tt(this,8,t,arguments.length>1&&arguments[1]),52)},setInt8:function(t,e){et(this,1,t,q,e)},setUint8:function(t,e){et(this,1,t,q,e)},setInt16:function(t,e){et(this,2,t,K,e,arguments.length>2&&arguments[2])},setUint16:function(t,e){et(this,2,t,K,e,arguments.length>2&&arguments[2])},setInt32:function(t,e){et(this,4,t,X,e,arguments.length>2&&arguments[2])},setUint32:function(t,e){et(this,4,t,X,e,arguments.length>2&&arguments[2])},setFloat32:function(t,e){et(this,4,t,$,e,arguments.length>2&&arguments[2])},setFloat64:function(t,e){et(this,8,t,Z,e,arguments.length>2&&arguments[2])}});w(U,C),w(F,x),t.exports={ArrayBuffer:U,DataView:F}},7029:function(t,e,r){"use strict";var n=r(8981),i=r(5610),o=r(6198),a=r(4606),u=Math.min;t.exports=[].copyWithin||function(t,e){var r=n(this),s=o(r),c=i(t,s),f=i(e,s),l=arguments.length>2?arguments[2]:void 0,h=u((void 0===l?s:i(l,s))-f,s-c),d=1;for(f<c&&c<f+h&&(d=-1,f+=h-1,c+=h-1);h-- >0;)f in r?r[c]=r[f]:a(r,c),c+=d,f+=d;return r}},4373:function(t,e,r){"use strict";var n=r(8981),i=r(5610),o=r(6198);t.exports=function(t){for(var e=n(this),r=o(e),a=arguments.length,u=i(a>1?arguments[1]:void 0,r),s=a>2?arguments[2]:void 0,c=void 0===s?r:i(s,r);c>u;)e[u++]=t;return e}},235:function(t,e,r){"use strict";var n=r(9213).forEach,i=r(4598)("forEach");t.exports=i?[].forEach:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}},5370:function(t,e,r){"use strict";var n=r(6198);t.exports=function(t,e,r){for(var i=0,o=arguments.length>2?r:n(e),a=new t(o);o>i;)a[i]=e[i++];return a}},7916:function(t,e,r){"use strict";var n=r(6080),i=r(9565),o=r(8981),a=r(6319),u=r(4209),s=r(3517),c=r(6198),f=r(4659),l=r(81),h=r(851),d=Array;t.exports=function(t){var e=o(t),r=s(this),p=arguments.length,v=p>1?arguments[1]:void 0,g=void 0!==v;g&&(v=n(v,p>2?arguments[2]:void 0));var y,E,A,m,T,_,b=h(e),w=0;if(!b||this===d&&u(b))for(y=c(e),E=r?new this(y):d(y);y>w;w++)_=g?v(e[w],w):e[w],f(E,w,_);else for(E=r?new this:[],T=(m=l(e,b)).next;!(A=i(T,m)).done;w++)_=g?a(m,v,[A.value,w],!0):A.value,f(E,w,_);return E.length=w,E}},9617:function(t,e,r){"use strict";var n=r(5397),i=r(5610),o=r(6198),a=function(t){return function(e,r,a){var u=n(e),s=o(u);if(0===s)return!t&&-1;var c,f=i(a,s);if(t&&r!=r){for(;s>f;)if((c=u[f++])!=c)return!0}else for(;s>f;f++)if((t||f in u)&&u[f]===r)return t||f||0;return!t&&-1}};t.exports={includes:a(!0),indexOf:a(!1)}},3839:function(t,e,r){"use strict";var n=r(6080),i=r(7055),o=r(8981),a=r(6198),u=function(t){var e=1===t;return function(r,u,s){for(var c,f=o(r),l=i(f),h=a(l),d=n(u,s);h-- >0;)if(d(c=l[h],h,f))switch(t){case 0:return c;case 1:return h}return e?-1:void 0}};t.exports={findLast:u(0),findLastIndex:u(1)}},9213:function(t,e,r){"use strict";var n=r(6080),i=r(9504),o=r(7055),a=r(8981),u=r(6198),s=r(1469),c=i([].push),f=function(t){var e=1===t,r=2===t,i=3===t,f=4===t,l=6===t,h=7===t,d=5===t||l;return function(p,v,g,y){for(var E,A,m=a(p),T=o(m),_=u(T),b=n(v,g),w=0,R=y||s,S=e?R(p,_):r||h?R(p,0):void 0;_>w;w++)if((d||w in T)&&(A=b(E=T[w],w,m),t))if(e)S[w]=A;else if(A)switch(t){case 3:return!0;case 5:return E;case 6:return w;case 2:c(S,E)}else switch(t){case 4:return!1;case 7:c(S,E)}return l?-1:i||f?f:S}};t.exports={forEach:f(0),map:f(1),filter:f(2),some:f(3),every:f(4),find:f(5),findIndex:f(6),filterReject:f(7)}},8379:function(t,e,r){"use strict";var n=r(8745),i=r(5397),o=r(1291),a=r(6198),u=r(4598),s=Math.min,c=[].lastIndexOf,f=!!c&&1/[1].lastIndexOf(1,-0)<0,l=u("lastIndexOf"),h=f||!l;t.exports=h?function(t){if(f)return n(c,this,arguments)||0;var e=i(this),r=a(e);if(0===r)return-1;var u=r-1;for(arguments.length>1&&(u=s(u,o(arguments[1]))),u<0&&(u=r+u);u>=0;u--)if(u in e&&e[u]===t)return u||0;return-1}:c},597:function(t,e,r){"use strict";var n=r(9039),i=r(8227),o=r(9519),a=i("species");t.exports=function(t){return o>=51||!n((function(){var e=[];return(e.constructor={})[a]=function(){return{foo:1}},1!==e[t](Boolean).foo}))}},4598:function(t,e,r){"use strict";var n=r(9039);t.exports=function(t,e){var r=[][t];return!!r&&n((function(){r.call(null,e||function(){return 1},1)}))}},926:function(t,e,r){"use strict";var n=r(9306),i=r(8981),o=r(7055),a=r(6198),u=TypeError,s="Reduce of empty array with no initial value",c=function(t){return function(e,r,c,f){var l=i(e),h=o(l),d=a(l);if(n(r),0===d&&c<2)throw new u(s);var p=t?d-1:0,v=t?-1:1;if(c<2)for(;;){if(p in h){f=h[p],p+=v;break}if(p+=v,t?p<0:d<=p)throw new u(s)}for(;t?p>=0:d>p;p+=v)p in h&&(f=r(f,h[p],p,l));return f}};t.exports={left:c(!1),right:c(!0)}},4527:function(t,e,r){"use strict";var n=r(3724),i=r(4376),o=TypeError,a=Object.getOwnPropertyDescriptor,u=n&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}();t.exports=u?function(t,e){if(i(t)&&!a(t,"length").writable)throw new o("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e}},7680:function(t,e,r){"use strict";var n=r(9504);t.exports=n([].slice)},4488:function(t,e,r){"use strict";var n=r(7680),i=Math.floor,o=function(t,e){var r=t.length;if(r<8)for(var a,u,s=1;s<r;){for(u=s,a=t[s];u&&e(t[u-1],a)>0;)t[u]=t[--u];u!==s++&&(t[u]=a)}else for(var c=i(r/2),f=o(n(t,0,c),e),l=o(n(t,c),e),h=f.length,d=l.length,p=0,v=0;p<h||v<d;)t[p+v]=p<h&&v<d?e(f[p],l[v])<=0?f[p++]:l[v++]:p<h?f[p++]:l[v++];return t};t.exports=o},7433:function(t,e,r){"use strict";var n=r(4376),i=r(3517),o=r(34),a=r(8227)("species"),u=Array;t.exports=function(t){var e;return n(t)&&(e=t.constructor,(i(e)&&(e===u||n(e.prototype))||o(e)&&null===(e=e[a]))&&(e=void 0)),void 0===e?u:e}},1469:function(t,e,r){"use strict";var n=r(7433);t.exports=function(t,e){return new(n(t))(0===e?0:e)}},7628:function(t,e,r){"use strict";var n=r(6198);t.exports=function(t,e){for(var r=n(t),i=new e(r),o=0;o<r;o++)i[o]=t[r-o-1];return i}},9928:function(t,e,r){"use strict";var n=r(6198),i=r(1291),o=RangeError;t.exports=function(t,e,r,a){var u=n(t),s=i(r),c=s<0?u+s:s;if(c>=u||c<0)throw new o("Incorrect index");for(var f=new e(u),l=0;l<u;l++)f[l]=l===c?a:t[l];return f}},6319:function(t,e,r){"use strict";var n=r(8551),i=r(9539);t.exports=function(t,e,r,o){try{return o?e(n(r)[0],r[1]):e(r)}catch(e){i(t,"throw",e)}}},4428:function(t,e,r){"use strict";var n=r(8227)("iterator"),i=!1;try{var o=0,a={next:function(){return{done:!!o++}},return:function(){i=!0}};a[n]=function(){return this},Array.from(a,(function(){throw 2}))}catch(t){}t.exports=function(t,e){try{if(!e&&!i)return!1}catch(t){return!1}var r=!1;try{var o={};o[n]=function(){return{next:function(){return{done:r=!0}}}},t(o)}catch(t){}return r}},2195:function(t,e,r){"use strict";var n=r(9504),i=n({}.toString),o=n("".slice);t.exports=function(t){return o(i(t),8,-1)}},6955:function(t,e,r){"use strict";var n=r(2140),i=r(4901),o=r(2195),a=r(8227)("toStringTag"),u=Object,s="Arguments"===o(function(){return arguments}());t.exports=n?o:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=u(t),a))?r:s?o(e):"Object"===(n=o(e))&&i(e.callee)?"Arguments":n}},7740:function(t,e,r){"use strict";var n=r(9297),i=r(5031),o=r(7347),a=r(4913);t.exports=function(t,e,r){for(var u=i(e),s=a.f,c=o.f,f=0;f<u.length;f++){var l=u[f];n(t,l)||r&&n(r,l)||s(t,l,c(e,l))}}},1436:function(t,e,r){"use strict";var n=r(8227)("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(r){try{return e[n]=!1,"/./"[t](e)}catch(t){}}return!1}},2211:function(t,e,r){"use strict";var n=r(9039);t.exports=!n((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))},2529:function(t){"use strict";t.exports=function(t,e){return{value:t,done:e}}},6699:function(t,e,r){"use strict";var n=r(3724),i=r(4913),o=r(6980);t.exports=n?function(t,e,r){return i.f(t,e,o(1,r))}:function(t,e,r){return t[e]=r,t}},6980:function(t){"use strict";t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},4659:function(t,e,r){"use strict";var n=r(3724),i=r(4913),o=r(6980);t.exports=function(t,e,r){n?i.f(t,e,o(0,r)):t[e]=r}},3640:function(t,e,r){"use strict";var n=r(8551),i=r(4270),o=TypeError;t.exports=function(t){if(n(this),"string"===t||"default"===t)t="string";else if("number"!==t)throw new o("Incorrect hint");return i(this,t)}},2106:function(t,e,r){"use strict";var n=r(283),i=r(4913);t.exports=function(t,e,r){return r.get&&n(r.get,e,{getter:!0}),r.set&&n(r.set,e,{setter:!0}),i.f(t,e,r)}},6840:function(t,e,r){"use strict";var n=r(4901),i=r(4913),o=r(283),a=r(9433);t.exports=function(t,e,r,u){u||(u={});var s=u.enumerable,c=void 0!==u.name?u.name:e;if(n(r)&&o(r,c,u),u.global)s?t[e]=r:a(e,r);else{try{u.unsafe?t[e]&&(s=!0):delete t[e]}catch(t){}s?t[e]=r:i.f(t,e,{value:r,enumerable:!1,configurable:!u.nonConfigurable,writable:!u.nonWritable})}return t}},6279:function(t,e,r){"use strict";var n=r(6840);t.exports=function(t,e,r){for(var i in e)n(t,i,e[i],r);return t}},9433:function(t,e,r){"use strict";var n=r(4576),i=Object.defineProperty;t.exports=function(t,e){try{i(n,t,{value:e,configurable:!0,writable:!0})}catch(r){n[t]=e}return e}},4606:function(t,e,r){"use strict";var n=r(6823),i=TypeError;t.exports=function(t,e){if(!delete t[e])throw new i("Cannot delete property "+n(e)+" of "+n(t))}},3724:function(t,e,r){"use strict";var n=r(9039);t.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},4483:function(t,e,r){"use strict";var n,i,o,a,u=r(4576),s=r(9429),c=r(1548),f=u.structuredClone,l=u.ArrayBuffer,h=u.MessageChannel,d=!1;if(c)d=function(t){f(t,{transfer:[t]})};else if(l)try{h||(n=s("worker_threads"))&&(h=n.MessageChannel),h&&(i=new h,o=new l(2),a=function(t){i.port1.postMessage(null,[t])},2===o.byteLength&&(a(o),0===o.byteLength&&(d=a)))}catch(t){}t.exports=d},4055:function(t,e,r){"use strict";var n=r(4576),i=r(34),o=n.document,a=i(o)&&i(o.createElement);t.exports=function(t){return a?o.createElement(t):{}}},6837:function(t){"use strict";var e=TypeError;t.exports=function(t){if(t>9007199254740991)throw e("Maximum allowed index exceeded");return t}},7400:function(t){"use strict";t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},9296:function(t,e,r){"use strict";var n=r(4055)("span").classList,i=n&&n.constructor&&n.constructor.prototype;t.exports=i===Object.prototype?void 0:i},8727:function(t){"use strict";t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},3709:function(t,e,r){"use strict";var n=r(2839).match(/firefox\/(\d+)/i);t.exports=!!n&&+n[1]},3763:function(t,e,r){"use strict";var n=r(2839);t.exports=/MSIE|Trident/.test(n)},8574:function(t,e,r){"use strict";var n=r(4215);t.exports="NODE"===n},2839:function(t,e,r){"use strict";var n=r(4576).navigator,i=n&&n.userAgent;t.exports=i?String(i):""},9519:function(t,e,r){"use strict";var n,i,o=r(4576),a=r(2839),u=o.process,s=o.Deno,c=u&&u.versions||s&&s.version,f=c&&c.v8;f&&(i=(n=f.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!i&&a&&(!(n=a.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=a.match(/Chrome\/(\d+)/))&&(i=+n[1]),t.exports=i},3607:function(t,e,r){"use strict";var n=r(2839).match(/AppleWebKit\/(\d+)\./);t.exports=!!n&&+n[1]},4215:function(t,e,r){"use strict";var n=r(4576),i=r(2839),o=r(2195),a=function(t){return i.slice(0,t.length)===t};t.exports=a("Bun/")?"BUN":a("Cloudflare-Workers")?"CLOUDFLARE":a("Deno/")?"DENO":a("Node.js/")?"NODE":n.Bun&&"string"==typeof Bun.version?"BUN":n.Deno&&"object"==typeof Deno.version?"DENO":"process"===o(n.process)?"NODE":n.window&&n.document?"BROWSER":"REST"},6193:function(t,e,r){"use strict";var n=r(9504),i=Error,o=n("".replace),a=String(new i("zxcasd").stack),u=/\n\s*at [^:]*:[^\n]*/,s=u.test(a);t.exports=function(t,e){if(s&&"string"==typeof t&&!i.prepareStackTrace)for(;e--;)t=o(t,u,"");return t}},747:function(t,e,r){"use strict";var n=r(6699),i=r(6193),o=r(6249),a=Error.captureStackTrace;t.exports=function(t,e,r,u){o&&(a?a(t,e):n(t,"stack",i(r,u)))}},6249:function(t,e,r){"use strict";var n=r(9039),i=r(6980);t.exports=!n((function(){var t=new Error("a");return!("stack"in t)||(Object.defineProperty(t,"stack",i(1,7)),7!==t.stack)}))},6518:function(t,e,r){"use strict";var n=r(4576),i=r(7347).f,o=r(6699),a=r(6840),u=r(9433),s=r(7740),c=r(2796);t.exports=function(t,e){var r,f,l,h,d,p=t.target,v=t.global,g=t.stat;if(r=v?n:g?n[p]||u(p,{}):n[p]&&n[p].prototype)for(f in e){if(h=e[f],l=t.dontCallGetSet?(d=i(r,f))&&d.value:r[f],!c(v?f:p+(g?".":"#")+f,t.forced)&&void 0!==l){if(typeof h==typeof l)continue;s(h,l)}(t.sham||l&&l.sham)&&o(h,"sham",!0),a(r,f,h,t)}}},9039:function(t){"use strict";t.exports=function(t){try{return!!t()}catch(t){return!0}}},9228:function(t,e,r){"use strict";r(7495);var n=r(9565),i=r(6840),o=r(7323),a=r(9039),u=r(8227),s=r(6699),c=u("species"),f=RegExp.prototype;t.exports=function(t,e,r,l){var h=u(t),d=!a((function(){var e={};return e[h]=function(){return 7},7!==""[t](e)})),p=d&&!a((function(){var e=!1,r=/a/;return"split"===t&&((r={}).constructor={},r.constructor[c]=function(){return r},r.flags="",r[h]=/./[h]),r.exec=function(){return e=!0,null},r[h](""),!e}));if(!d||!p||r){var v=/./[h],g=e(h,""[t],(function(t,e,r,i,a){var u=e.exec;return u===o||u===f.exec?d&&!a?{done:!0,value:n(v,e,r,i)}:{done:!0,value:n(t,r,e,i)}:{done:!1}}));i(String.prototype,t,g[0]),i(f,h,g[1])}l&&s(f[h],"sham",!0)}},8745:function(t,e,r){"use strict";var n=r(616),i=Function.prototype,o=i.apply,a=i.call;t.exports="object"==typeof Reflect&&Reflect.apply||(n?a.bind(o):function(){return a.apply(o,arguments)})},6080:function(t,e,r){"use strict";var n=r(7476),i=r(9306),o=r(616),a=n(n.bind);t.exports=function(t,e){return i(t),void 0===e?t:o?a(t,e):function(){return t.apply(e,arguments)}}},616:function(t,e,r){"use strict";var n=r(9039);t.exports=!n((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))},566:function(t,e,r){"use strict";var n=r(9504),i=r(9306),o=r(34),a=r(9297),u=r(7680),s=r(616),c=Function,f=n([].concat),l=n([].join),h={};t.exports=s?c.bind:function(t){var e=i(this),r=e.prototype,n=u(arguments,1),s=function(){var r=f(n,u(arguments));return this instanceof s?function(t,e,r){if(!a(h,e)){for(var n=[],i=0;i<e;i++)n[i]="a["+i+"]";h[e]=c("C,a","return new C("+l(n,",")+")")}return h[e](t,r)}(e,r.length,r):e.apply(t,r)};return o(r)&&(s.prototype=r),s}},9565:function(t,e,r){"use strict";var n=r(616),i=Function.prototype.call;t.exports=n?i.bind(i):function(){return i.apply(i,arguments)}},350:function(t,e,r){"use strict";var n=r(3724),i=r(9297),o=Function.prototype,a=n&&Object.getOwnPropertyDescriptor,u=i(o,"name"),s=u&&"something"===function(){}.name,c=u&&(!n||n&&a(o,"name").configurable);t.exports={EXISTS:u,PROPER:s,CONFIGURABLE:c}},6706:function(t,e,r){"use strict";var n=r(9504),i=r(9306);t.exports=function(t,e,r){try{return n(i(Object.getOwnPropertyDescriptor(t,e)[r]))}catch(t){}}},7476:function(t,e,r){"use strict";var n=r(2195),i=r(9504);t.exports=function(t){if("Function"===n(t))return i(t)}},9504:function(t,e,r){"use strict";var n=r(616),i=Function.prototype,o=i.call,a=n&&i.bind.bind(o,o);t.exports=n?a:function(t){return function(){return o.apply(t,arguments)}}},9429:function(t,e,r){"use strict";var n=r(4576),i=r(8574);t.exports=function(t){if(i){try{return n.process.getBuiltinModule(t)}catch(t){}try{return Function('return require("'+t+'")')()}catch(t){}}}},7751:function(t,e,r){"use strict";var n=r(4576),i=r(4901);t.exports=function(t,e){return arguments.length<2?(r=n[t],i(r)?r:void 0):n[t]&&n[t][e];var r}},851:function(t,e,r){"use strict";var n=r(6955),i=r(5966),o=r(4117),a=r(6269),u=r(8227)("iterator");t.exports=function(t){if(!o(t))return i(t,u)||i(t,"@@iterator")||a[n(t)]}},81:function(t,e,r){"use strict";var n=r(9565),i=r(9306),o=r(8551),a=r(6823),u=r(851),s=TypeError;t.exports=function(t,e){var r=arguments.length<2?u(t):e;if(i(r))return o(n(r,t));throw new s(a(t)+" is not iterable")}},6933:function(t,e,r){"use strict";var n=r(9504),i=r(4376),o=r(4901),a=r(2195),u=r(655),s=n([].push);t.exports=function(t){if(o(t))return t;if(i(t)){for(var e=t.length,r=[],n=0;n<e;n++){var c=t[n];"string"==typeof c?s(r,c):"number"!=typeof c&&"Number"!==a(c)&&"String"!==a(c)||s(r,u(c))}var f=r.length,l=!0;return function(t,e){if(l)return l=!1,e;if(i(this))return e;for(var n=0;n<f;n++)if(r[n]===t)return e}}}},5966:function(t,e,r){"use strict";var n=r(9306),i=r(4117);t.exports=function(t,e){var r=t[e];return i(r)?void 0:n(r)}},2478:function(t,e,r){"use strict";var n=r(9504),i=r(8981),o=Math.floor,a=n("".charAt),u=n("".replace),s=n("".slice),c=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,f=/\$([$&'`]|\d{1,2})/g;t.exports=function(t,e,r,n,l,h){var d=r+t.length,p=n.length,v=f;return void 0!==l&&(l=i(l),v=c),u(h,v,(function(i,u){var c;switch(a(u,0)){case"$":return"$";case"&":return t;case"`":return s(e,0,r);case"'":return s(e,d);case"<":c=l[s(u,1,-1)];break;default:var f=+u;if(0===f)return i;if(f>p){var h=o(f/10);return 0===h?i:h<=p?void 0===n[h-1]?a(u,1):n[h-1]+a(u,1):i}c=n[f-1]}return void 0===c?"":c}))}},4576:function(t,e,r){"use strict";var n=function(t){return t&&t.Math===Math&&t};t.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof r.g&&r.g)||n("object"==typeof this&&this)||function(){return this}()||Function("return this")()},9297:function(t,e,r){"use strict";var n=r(9504),i=r(8981),o=n({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,e){return o(i(t),e)}},421:function(t){"use strict";t.exports={}},397:function(t,e,r){"use strict";var n=r(7751);t.exports=n("document","documentElement")},5917:function(t,e,r){"use strict";var n=r(3724),i=r(9039),o=r(4055);t.exports=!n&&!i((function(){return 7!==Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a}))},8490:function(t){"use strict";var e=Array,r=Math.abs,n=Math.pow,i=Math.floor,o=Math.log,a=Math.LN2;t.exports={pack:function(t,u,s){var c,f,l,h=e(s),d=8*s-u-1,p=(1<<d)-1,v=p>>1,g=23===u?n(2,-24)-n(2,-77):0,y=t<0||0===t&&1/t<0?1:0,E=0;for((t=r(t))!=t||t===1/0?(f=t!=t?1:0,c=p):(c=i(o(t)/a),t*(l=n(2,-c))<1&&(c--,l*=2),(t+=c+v>=1?g/l:g*n(2,1-v))*l>=2&&(c++,l/=2),c+v>=p?(f=0,c=p):c+v>=1?(f=(t*l-1)*n(2,u),c+=v):(f=t*n(2,v-1)*n(2,u),c=0));u>=8;)h[E++]=255&f,f/=256,u-=8;for(c=c<<u|f,d+=u;d>0;)h[E++]=255&c,c/=256,d-=8;return h[E-1]|=128*y,h},unpack:function(t,e){var r,i=t.length,o=8*i-e-1,a=(1<<o)-1,u=a>>1,s=o-7,c=i-1,f=t[c--],l=127&f;for(f>>=7;s>0;)l=256*l+t[c--],s-=8;for(r=l&(1<<-s)-1,l>>=-s,s+=e;s>0;)r=256*r+t[c--],s-=8;if(0===l)l=1-u;else{if(l===a)return r?NaN:f?-1/0:1/0;r+=n(2,e),l-=u}return(f?-1:1)*r*n(2,l-e)}}},7055:function(t,e,r){"use strict";var n=r(9504),i=r(9039),o=r(2195),a=Object,u=n("".split);t.exports=i((function(){return!a("z").propertyIsEnumerable(0)}))?function(t){return"String"===o(t)?u(t,""):a(t)}:a},3167:function(t,e,r){"use strict";var n=r(4901),i=r(34),o=r(2967);t.exports=function(t,e,r){var a,u;return o&&n(a=e.constructor)&&a!==r&&i(u=a.prototype)&&u!==r.prototype&&o(t,u),t}},3706:function(t,e,r){"use strict";var n=r(9504),i=r(4901),o=r(7629),a=n(Function.toString);i(o.inspectSource)||(o.inspectSource=function(t){return a(t)}),t.exports=o.inspectSource},7584:function(t,e,r){"use strict";var n=r(34),i=r(6699);t.exports=function(t,e){n(e)&&"cause"in e&&i(t,"cause",e.cause)}},1181:function(t,e,r){"use strict";var n,i,o,a=r(8622),u=r(4576),s=r(34),c=r(6699),f=r(9297),l=r(7629),h=r(6119),d=r(421),p="Object already initialized",v=u.TypeError,g=u.WeakMap;if(a||l.state){var y=l.state||(l.state=new g);y.get=y.get,y.has=y.has,y.set=y.set,n=function(t,e){if(y.has(t))throw new v(p);return e.facade=t,y.set(t,e),e},i=function(t){return y.get(t)||{}},o=function(t){return y.has(t)}}else{var E=h("state");d[E]=!0,n=function(t,e){if(f(t,E))throw new v(p);return e.facade=t,c(t,E,e),e},i=function(t){return f(t,E)?t[E]:{}},o=function(t){return f(t,E)}}t.exports={set:n,get:i,has:o,enforce:function(t){return o(t)?i(t):n(t,{})},getterFor:function(t){return function(e){var r;if(!s(e)||(r=i(e)).type!==t)throw new v("Incompatible receiver, "+t+" required");return r}}}},4209:function(t,e,r){"use strict";var n=r(8227),i=r(6269),o=n("iterator"),a=Array.prototype;t.exports=function(t){return void 0!==t&&(i.Array===t||a[o]===t)}},4376:function(t,e,r){"use strict";var n=r(2195);t.exports=Array.isArray||function(t){return"Array"===n(t)}},1108:function(t,e,r){"use strict";var n=r(6955);t.exports=function(t){var e=n(t);return"BigInt64Array"===e||"BigUint64Array"===e}},4901:function(t){"use strict";var e="object"==typeof document&&document.all;t.exports=void 0===e&&void 0!==e?function(t){return"function"==typeof t||t===e}:function(t){return"function"==typeof t}},3517:function(t,e,r){"use strict";var n=r(9504),i=r(9039),o=r(4901),a=r(6955),u=r(7751),s=r(3706),c=function(){},f=u("Reflect","construct"),l=/^\s*(?:class|function)\b/,h=n(l.exec),d=!l.test(c),p=function(t){if(!o(t))return!1;try{return f(c,[],t),!0}catch(t){return!1}},v=function(t){if(!o(t))return!1;switch(a(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return d||!!h(l,s(t))}catch(t){return!0}};v.sham=!0,t.exports=!f||i((function(){var t;return p(p.call)||!p(Object)||!p((function(){t=!0}))||t}))?v:p},2796:function(t,e,r){"use strict";var n=r(9039),i=r(4901),o=/#|\.prototype\./,a=function(t,e){var r=s[u(t)];return r===f||r!==c&&(i(e)?n(e):!!e)},u=a.normalize=function(t){return String(t).replace(o,".").toLowerCase()},s=a.data={},c=a.NATIVE="N",f=a.POLYFILL="P";t.exports=a},2087:function(t,e,r){"use strict";var n=r(34),i=Math.floor;t.exports=Number.isInteger||function(t){return!n(t)&&isFinite(t)&&i(t)===t}},4117:function(t){"use strict";t.exports=function(t){return null==t}},34:function(t,e,r){"use strict";var n=r(4901);t.exports=function(t){return"object"==typeof t?null!==t:n(t)}},3925:function(t,e,r){"use strict";var n=r(34);t.exports=function(t){return n(t)||null===t}},6395:function(t){"use strict";t.exports=!1},788:function(t,e,r){"use strict";var n=r(34),i=r(2195),o=r(8227)("match");t.exports=function(t){var e;return n(t)&&(void 0!==(e=t[o])?!!e:"RegExp"===i(t))}},757:function(t,e,r){"use strict";var n=r(7751),i=r(4901),o=r(1625),a=r(7040),u=Object;t.exports=a?function(t){return"symbol"==typeof t}:function(t){var e=n("Symbol");return i(e)&&o(e.prototype,u(t))}},9539:function(t,e,r){"use strict";var n=r(9565),i=r(8551),o=r(5966);t.exports=function(t,e,r){var a,u;i(t);try{if(!(a=o(t,"return"))){if("throw"===e)throw r;return r}a=n(a,t)}catch(t){u=!0,a=t}if("throw"===e)throw r;if(u)throw a;return i(a),r}},3994:function(t,e,r){"use strict";var n=r(7657).IteratorPrototype,i=r(2360),o=r(6980),a=r(687),u=r(6269),s=function(){return this};t.exports=function(t,e,r,c){var f=e+" Iterator";return t.prototype=i(n,{next:o(+!c,r)}),a(t,f,!1,!0),u[f]=s,t}},1088:function(t,e,r){"use strict";var n=r(6518),i=r(9565),o=r(6395),a=r(350),u=r(4901),s=r(3994),c=r(2787),f=r(2967),l=r(687),h=r(6699),d=r(6840),p=r(8227),v=r(6269),g=r(7657),y=a.PROPER,E=a.CONFIGURABLE,A=g.IteratorPrototype,m=g.BUGGY_SAFARI_ITERATORS,T=p("iterator"),_="keys",b="values",w="entries",R=function(){return this};t.exports=function(t,e,r,a,p,g,S){s(r,e,a);var I,C,x,O=function(t){if(t===p&&P)return P;if(!m&&t&&t in N)return N[t];switch(t){case _:case b:case w:return function(){return new r(this,t)}}return function(){return new r(this)}},D=e+" Iterator",L=!1,N=t.prototype,M=N[T]||N["@@iterator"]||p&&N[p],P=!m&&M||O(p),U="Array"===e&&N.entries||M;if(U&&(I=c(U.call(new t)))!==Object.prototype&&I.next&&(o||c(I)===A||(f?f(I,A):u(I[T])||d(I,T,R)),l(I,D,!0,!0),o&&(v[D]=R)),y&&p===b&&M&&M.name!==b&&(!o&&E?h(N,"name",b):(L=!0,P=function(){return i(M,this)})),p)if(C={values:O(b),keys:g?P:O(_),entries:O(w)},S)for(x in C)(m||L||!(x in N))&&d(N,x,C[x]);else n({target:e,proto:!0,forced:m||L},C);return o&&!S||N[T]===P||d(N,T,P,{name:p}),v[e]=P,C}},7657:function(t,e,r){"use strict";var n,i,o,a=r(9039),u=r(4901),s=r(34),c=r(2360),f=r(2787),l=r(6840),h=r(8227),d=r(6395),p=h("iterator"),v=!1;[].keys&&("next"in(o=[].keys())?(i=f(f(o)))!==Object.prototype&&(n=i):v=!0),!s(n)||a((function(){var t={};return n[p].call(t)!==t}))?n={}:d&&(n=c(n)),u(n[p])||l(n,p,(function(){return this})),t.exports={IteratorPrototype:n,BUGGY_SAFARI_ITERATORS:v}},6269:function(t){"use strict";t.exports={}},6198:function(t,e,r){"use strict";var n=r(8014);t.exports=function(t){return n(t.length)}},283:function(t,e,r){"use strict";var n=r(9504),i=r(9039),o=r(4901),a=r(9297),u=r(3724),s=r(350).CONFIGURABLE,c=r(3706),f=r(1181),l=f.enforce,h=f.get,d=String,p=Object.defineProperty,v=n("".slice),g=n("".replace),y=n([].join),E=u&&!i((function(){return 8!==p((function(){}),"length",{value:8}).length})),A=String(String).split("String"),m=t.exports=function(t,e,r){"Symbol("===v(d(e),0,7)&&(e="["+g(d(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(e="get "+e),r&&r.setter&&(e="set "+e),(!a(t,"name")||s&&t.name!==e)&&(u?p(t,"name",{value:e,configurable:!0}):t.name=e),E&&r&&a(r,"arity")&&t.length!==r.arity&&p(t,"length",{value:r.arity});try{r&&a(r,"constructor")&&r.constructor?u&&p(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var n=l(t);return a(n,"source")||(n.source=y(A,"string"==typeof e?e:"")),t};Function.prototype.toString=m((function(){return o(this)&&h(this).source||c(this)}),"toString")},3164:function(t,e,r){"use strict";var n=r(7782),i=Math.abs,o=2220446049250313e-31,a=1/o;t.exports=function(t,e,r,u){var s=+t,c=i(s),f=n(s);if(c<u)return f*function(t){return t+a-a}(c/u/e)*u*e;var l=(1+e/o)*c,h=l-(l-c);return h>r||h!=h?f*(1/0):f*h}},5617:function(t,e,r){"use strict";var n=r(3164);t.exports=Math.fround||function(t){return n(t,1.1920928955078125e-7,34028234663852886e22,11754943508222875e-54)}},7782:function(t){"use strict";t.exports=Math.sign||function(t){var e=+t;return 0===e||e!=e?e:e<0?-1:1}},741:function(t){"use strict";var e=Math.ceil,r=Math.floor;t.exports=Math.trunc||function(t){var n=+t;return(n>0?r:e)(n)}},2603:function(t,e,r){"use strict";var n=r(655);t.exports=function(t,e){return void 0===t?arguments.length<2?"":e:n(t)}},5749:function(t,e,r){"use strict";var n=r(788),i=TypeError;t.exports=function(t){if(n(t))throw new i("The method doesn't accept regular expressions");return t}},4213:function(t,e,r){"use strict";var n=r(3724),i=r(9504),o=r(9565),a=r(9039),u=r(1072),s=r(3717),c=r(8773),f=r(8981),l=r(7055),h=Object.assign,d=Object.defineProperty,p=i([].concat);t.exports=!h||a((function(){if(n&&1!==h({b:1},h(d({},"a",{enumerable:!0,get:function(){d(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},r=Symbol("assign detection"),i="abcdefghijklmnopqrst";return t[r]=7,i.split("").forEach((function(t){e[t]=t})),7!==h({},t)[r]||u(h({},e)).join("")!==i}))?function(t,e){for(var r=f(t),i=arguments.length,a=1,h=s.f,d=c.f;i>a;)for(var v,g=l(arguments[a++]),y=h?p(u(g),h(g)):u(g),E=y.length,A=0;E>A;)v=y[A++],n&&!o(d,g,v)||(r[v]=g[v]);return r}:h},2360:function(t,e,r){"use strict";var n,i=r(8551),o=r(6801),a=r(8727),u=r(421),s=r(397),c=r(4055),f=r(6119),l="prototype",h="script",d=f("IE_PROTO"),p=function(){},v=function(t){return"<"+h+">"+t+"</"+h+">"},g=function(t){t.write(v("")),t.close();var e=t.parentWindow.Object;return t=null,e},y=function(){try{n=new ActiveXObject("htmlfile")}catch(t){}var t,e,r;y="undefined"!=typeof document?document.domain&&n?g(n):(e=c("iframe"),r="java"+h+":",e.style.display="none",s.appendChild(e),e.src=String(r),(t=e.contentWindow.document).open(),t.write(v("document.F=Object")),t.close(),t.F):g(n);for(var i=a.length;i--;)delete y[l][a[i]];return y()};u[d]=!0,t.exports=Object.create||function(t,e){var r;return null!==t?(p[l]=i(t),r=new p,p[l]=null,r[d]=t):r=y(),void 0===e?r:o.f(r,e)}},6801:function(t,e,r){"use strict";var n=r(3724),i=r(8686),o=r(4913),a=r(8551),u=r(5397),s=r(1072);e.f=n&&!i?Object.defineProperties:function(t,e){a(t);for(var r,n=u(e),i=s(e),c=i.length,f=0;c>f;)o.f(t,r=i[f++],n[r]);return t}},4913:function(t,e,r){"use strict";var n=r(3724),i=r(5917),o=r(8686),a=r(8551),u=r(6969),s=TypeError,c=Object.defineProperty,f=Object.getOwnPropertyDescriptor,l="enumerable",h="configurable",d="writable";e.f=n?o?function(t,e,r){if(a(t),e=u(e),a(r),"function"==typeof t&&"prototype"===e&&"value"in r&&d in r&&!r[d]){var n=f(t,e);n&&n[d]&&(t[e]=r.value,r={configurable:h in r?r[h]:n[h],enumerable:l in r?r[l]:n[l],writable:!1})}return c(t,e,r)}:c:function(t,e,r){if(a(t),e=u(e),a(r),i)try{return c(t,e,r)}catch(t){}if("get"in r||"set"in r)throw new s("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},7347:function(t,e,r){"use strict";var n=r(3724),i=r(9565),o=r(8773),a=r(6980),u=r(5397),s=r(6969),c=r(9297),f=r(5917),l=Object.getOwnPropertyDescriptor;e.f=n?l:function(t,e){if(t=u(t),e=s(e),f)try{return l(t,e)}catch(t){}if(c(t,e))return a(!i(o.f,t,e),t[e])}},298:function(t,e,r){"use strict";var n=r(2195),i=r(5397),o=r(8480).f,a=r(7680),u="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];t.exports.f=function(t){return u&&"Window"===n(t)?function(t){try{return o(t)}catch(t){return a(u)}}(t):o(i(t))}},8480:function(t,e,r){"use strict";var n=r(1828),i=r(8727).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return n(t,i)}},3717:function(t,e){"use strict";e.f=Object.getOwnPropertySymbols},2787:function(t,e,r){"use strict";var n=r(9297),i=r(4901),o=r(8981),a=r(6119),u=r(2211),s=a("IE_PROTO"),c=Object,f=c.prototype;t.exports=u?c.getPrototypeOf:function(t){var e=o(t);if(n(e,s))return e[s];var r=e.constructor;return i(r)&&e instanceof r?r.prototype:e instanceof c?f:null}},1625:function(t,e,r){"use strict";var n=r(9504);t.exports=n({}.isPrototypeOf)},1828:function(t,e,r){"use strict";var n=r(9504),i=r(9297),o=r(5397),a=r(9617).indexOf,u=r(421),s=n([].push);t.exports=function(t,e){var r,n=o(t),c=0,f=[];for(r in n)!i(u,r)&&i(n,r)&&s(f,r);for(;e.length>c;)i(n,r=e[c++])&&(~a(f,r)||s(f,r));return f}},1072:function(t,e,r){"use strict";var n=r(1828),i=r(8727);t.exports=Object.keys||function(t){return n(t,i)}},8773:function(t,e){"use strict";var r={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,i=n&&!r.call({1:2},1);e.f=i?function(t){var e=n(this,t);return!!e&&e.enumerable}:r},2967:function(t,e,r){"use strict";var n=r(6706),i=r(34),o=r(7750),a=r(3506);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=n(Object.prototype,"__proto__","set"))(r,[]),e=r instanceof Array}catch(t){}return function(r,n){return o(r),a(n),i(r)?(e?t(r,n):r.__proto__=n,r):r}}():void 0)},3179:function(t,e,r){"use strict";var n=r(2140),i=r(6955);t.exports=n?{}.toString:function(){return"[object "+i(this)+"]"}},4270:function(t,e,r){"use strict";var n=r(9565),i=r(4901),o=r(34),a=TypeError;t.exports=function(t,e){var r,u;if("string"===e&&i(r=t.toString)&&!o(u=n(r,t)))return u;if(i(r=t.valueOf)&&!o(u=n(r,t)))return u;if("string"!==e&&i(r=t.toString)&&!o(u=n(r,t)))return u;throw new a("Can't convert object to primitive value")}},5031:function(t,e,r){"use strict";var n=r(7751),i=r(9504),o=r(8480),a=r(3717),u=r(8551),s=i([].concat);t.exports=n("Reflect","ownKeys")||function(t){var e=o.f(u(t)),r=a.f;return r?s(e,r(t)):e}},9167:function(t,e,r){"use strict";var n=r(4576);t.exports=n},1056:function(t,e,r){"use strict";var n=r(4913).f;t.exports=function(t,e,r){r in t||n(t,r,{configurable:!0,get:function(){return e[r]},set:function(t){e[r]=t}})}},6682:function(t,e,r){"use strict";var n=r(9565),i=r(8551),o=r(4901),a=r(2195),u=r(7323),s=TypeError;t.exports=function(t,e){var r=t.exec;if(o(r)){var c=n(r,t,e);return null!==c&&i(c),c}if("RegExp"===a(t))return n(u,t,e);throw new s("RegExp#exec called on incompatible receiver")}},7323:function(t,e,r){"use strict";var n,i,o=r(9565),a=r(9504),u=r(655),s=r(7979),c=r(8429),f=r(5745),l=r(2360),h=r(1181).get,d=r(3635),p=r(8814),v=f("native-string-replace",String.prototype.replace),g=RegExp.prototype.exec,y=g,E=a("".charAt),A=a("".indexOf),m=a("".replace),T=a("".slice),_=(i=/b*/g,o(g,n=/a/,"a"),o(g,i,"a"),0!==n.lastIndex||0!==i.lastIndex),b=c.BROKEN_CARET,w=void 0!==/()??/.exec("")[1];(_||w||b||d||p)&&(y=function(t){var e,r,n,i,a,c,f,d=this,p=h(d),R=u(t),S=p.raw;if(S)return S.lastIndex=d.lastIndex,e=o(y,S,R),d.lastIndex=S.lastIndex,e;var I=p.groups,C=b&&d.sticky,x=o(s,d),O=d.source,D=0,L=R;if(C&&(x=m(x,"y",""),-1===A(x,"g")&&(x+="g"),L=T(R,d.lastIndex),d.lastIndex>0&&(!d.multiline||d.multiline&&"\n"!==E(R,d.lastIndex-1))&&(O="(?: "+O+")",L=" "+L,D++),r=new RegExp("^(?:"+O+")",x)),w&&(r=new RegExp("^"+O+"$(?!\\s)",x)),_&&(n=d.lastIndex),i=o(g,C?r:d,L),C?i?(i.input=T(i.input,D),i[0]=T(i[0],D),i.index=d.lastIndex,d.lastIndex+=i[0].length):d.lastIndex=0:_&&i&&(d.lastIndex=d.global?i.index+i[0].length:n),w&&i&&i.length>1&&o(v,i[0],r,(function(){for(a=1;a<arguments.length-2;a++)void 0===arguments[a]&&(i[a]=void 0)})),i&&I)for(i.groups=c=l(null),a=0;a<I.length;a++)c[(f=I[a])[0]]=i[f[1]];return i}),t.exports=y},7979:function(t,e,r){"use strict";var n=r(8551);t.exports=function(){var t=n(this),e="";return t.hasIndices&&(e+="d"),t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.unicodeSets&&(e+="v"),t.sticky&&(e+="y"),e}},1034:function(t,e,r){"use strict";var n=r(9565),i=r(9297),o=r(1625),a=r(7979),u=RegExp.prototype;t.exports=function(t){var e=t.flags;return void 0!==e||"flags"in u||i(t,"flags")||!o(u,t)?e:n(a,t)}},8429:function(t,e,r){"use strict";var n=r(9039),i=r(4576).RegExp,o=n((function(){var t=i("a","y");return t.lastIndex=2,null!==t.exec("abcd")})),a=o||n((function(){return!i("a","y").sticky})),u=o||n((function(){var t=i("^r","gy");return t.lastIndex=2,null!==t.exec("str")}));t.exports={BROKEN_CARET:u,MISSED_STICKY:a,UNSUPPORTED_Y:o}},3635:function(t,e,r){"use strict";var n=r(9039),i=r(4576).RegExp;t.exports=n((function(){var t=i(".","s");return!(t.dotAll&&t.test("\n")&&"s"===t.flags)}))},8814:function(t,e,r){"use strict";var n=r(9039),i=r(4576).RegExp;t.exports=n((function(){var t=i("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")}))},7750:function(t,e,r){"use strict";var n=r(4117),i=TypeError;t.exports=function(t){if(n(t))throw new i("Can't call method on "+t);return t}},3389:function(t,e,r){"use strict";var n=r(4576),i=r(3724),o=Object.getOwnPropertyDescriptor;t.exports=function(t){if(!i)return n[t];var e=o(n,t);return e&&e.value}},3470:function(t){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e}},7633:function(t,e,r){"use strict";var n=r(7751),i=r(2106),o=r(8227),a=r(3724),u=o("species");t.exports=function(t){var e=n(t);a&&e&&!e[u]&&i(e,u,{configurable:!0,get:function(){return this}})}},687:function(t,e,r){"use strict";var n=r(4913).f,i=r(9297),o=r(8227)("toStringTag");t.exports=function(t,e,r){t&&!r&&(t=t.prototype),t&&!i(t,o)&&n(t,o,{configurable:!0,value:e})}},6119:function(t,e,r){"use strict";var n=r(5745),i=r(3392),o=n("keys");t.exports=function(t){return o[t]||(o[t]=i(t))}},7629:function(t,e,r){"use strict";var n=r(6395),i=r(4576),o=r(9433),a="__core-js_shared__",u=t.exports=i[a]||o(a,{});(u.versions||(u.versions=[])).push({version:"3.39.0",mode:n?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.39.0/LICENSE",source:"https://github.com/zloirock/core-js"})},5745:function(t,e,r){"use strict";var n=r(7629);t.exports=function(t,e){return n[t]||(n[t]=e||{})}},8183:function(t,e,r){"use strict";var n=r(9504),i=r(1291),o=r(655),a=r(7750),u=n("".charAt),s=n("".charCodeAt),c=n("".slice),f=function(t){return function(e,r){var n,f,l=o(a(e)),h=i(r),d=l.length;return h<0||h>=d?t?"":void 0:(n=s(l,h))<55296||n>56319||h+1===d||(f=s(l,h+1))<56320||f>57343?t?u(l,h):n:t?c(l,h,h+2):f-56320+(n-55296<<10)+65536}};t.exports={codeAt:f(!1),charAt:f(!0)}},6098:function(t,e,r){"use strict";var n=r(9504),i=**********,o=/[^\0-\u007E]/,a=/[.\u3002\uFF0E\uFF61]/g,u="Overflow: input needs wider integers to process",s=RangeError,c=n(a.exec),f=Math.floor,l=String.fromCharCode,h=n("".charCodeAt),d=n([].join),p=n([].push),v=n("".replace),g=n("".split),y=n("".toLowerCase),E=function(t){return t+22+75*(t<26)},A=function(t,e,r){var n=0;for(t=r?f(t/700):t>>1,t+=f(t/e);t>455;)t=f(t/35),n+=36;return f(n+36*t/(t+38))},m=function(t){var e=[];t=function(t){for(var e=[],r=0,n=t.length;r<n;){var i=h(t,r++);if(i>=55296&&i<=56319&&r<n){var o=h(t,r++);56320==(64512&o)?p(e,((1023&i)<<10)+(1023&o)+65536):(p(e,i),r--)}else p(e,i)}return e}(t);var r,n,o=t.length,a=128,c=0,v=72;for(r=0;r<t.length;r++)(n=t[r])<128&&p(e,l(n));var g=e.length,y=g;for(g&&p(e,"-");y<o;){var m=i;for(r=0;r<t.length;r++)(n=t[r])>=a&&n<m&&(m=n);var T=y+1;if(m-a>f((i-c)/T))throw new s(u);for(c+=(m-a)*T,a=m,r=0;r<t.length;r++){if((n=t[r])<a&&++c>i)throw new s(u);if(n===a){for(var _=c,b=36;;){var w=b<=v?1:b>=v+26?26:b-v;if(_<w)break;var R=_-w,S=36-w;p(e,l(E(w+R%S))),_=f(R/S),b+=36}p(e,l(E(_))),v=A(c,T,y===g),c=0,y++}}c++,a++}return d(e,"")};t.exports=function(t){var e,r,n=[],i=g(v(y(t),a,"."),".");for(e=0;e<i.length;e++)r=i[e],p(n,c(o,r)?"xn--"+m(r):r);return d(n,".")}},706:function(t,e,r){"use strict";var n=r(350).PROPER,i=r(9039),o=r(7452);t.exports=function(t){return i((function(){return!!o[t]()||"​᠎"!=="​᠎"[t]()||n&&o[t].name!==t}))}},3802:function(t,e,r){"use strict";var n=r(9504),i=r(7750),o=r(655),a=r(7452),u=n("".replace),s=RegExp("^["+a+"]+"),c=RegExp("(^|[^"+a+"])["+a+"]+$"),f=function(t){return function(e){var r=o(i(e));return 1&t&&(r=u(r,s,"")),2&t&&(r=u(r,c,"$1")),r}};t.exports={start:f(1),end:f(2),trim:f(3)}},1548:function(t,e,r){"use strict";var n=r(4576),i=r(9039),o=r(9519),a=r(4215),u=n.structuredClone;t.exports=!!u&&!i((function(){if("DENO"===a&&o>92||"NODE"===a&&o>94||"BROWSER"===a&&o>97)return!1;var t=new ArrayBuffer(8),e=u(t,{transfer:[t]});return 0!==t.byteLength||8!==e.byteLength}))},4495:function(t,e,r){"use strict";var n=r(9519),i=r(9039),o=r(4576).String;t.exports=!!Object.getOwnPropertySymbols&&!i((function(){var t=Symbol("symbol detection");return!o(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},8242:function(t,e,r){"use strict";var n=r(9565),i=r(7751),o=r(8227),a=r(6840);t.exports=function(){var t=i("Symbol"),e=t&&t.prototype,r=e&&e.valueOf,u=o("toPrimitive");e&&!e[u]&&a(e,u,(function(t){return n(r,this)}),{arity:1})}},1296:function(t,e,r){"use strict";var n=r(4495);t.exports=n&&!!Symbol.for&&!!Symbol.keyFor},1240:function(t,e,r){"use strict";var n=r(9504);t.exports=n(1..valueOf)},5610:function(t,e,r){"use strict";var n=r(1291),i=Math.max,o=Math.min;t.exports=function(t,e){var r=n(t);return r<0?i(r+e,0):o(r,e)}},5854:function(t,e,r){"use strict";var n=r(2777),i=TypeError;t.exports=function(t){var e=n(t,"number");if("number"==typeof e)throw new i("Can't convert number to bigint");return BigInt(e)}},7696:function(t,e,r){"use strict";var n=r(1291),i=r(8014),o=RangeError;t.exports=function(t){if(void 0===t)return 0;var e=n(t),r=i(e);if(e!==r)throw new o("Wrong length or index");return r}},5397:function(t,e,r){"use strict";var n=r(7055),i=r(7750);t.exports=function(t){return n(i(t))}},1291:function(t,e,r){"use strict";var n=r(741);t.exports=function(t){var e=+t;return e!=e||0===e?0:n(e)}},8014:function(t,e,r){"use strict";var n=r(1291),i=Math.min;t.exports=function(t){var e=n(t);return e>0?i(e,9007199254740991):0}},8981:function(t,e,r){"use strict";var n=r(7750),i=Object;t.exports=function(t){return i(n(t))}},8229:function(t,e,r){"use strict";var n=r(9590),i=RangeError;t.exports=function(t,e){var r=n(t);if(r%e)throw new i("Wrong offset");return r}},9590:function(t,e,r){"use strict";var n=r(1291),i=RangeError;t.exports=function(t){var e=n(t);if(e<0)throw new i("The argument can't be less than 0");return e}},2777:function(t,e,r){"use strict";var n=r(9565),i=r(34),o=r(757),a=r(5966),u=r(4270),s=r(8227),c=TypeError,f=s("toPrimitive");t.exports=function(t,e){if(!i(t)||o(t))return t;var r,s=a(t,f);if(s){if(void 0===e&&(e="default"),r=n(s,t,e),!i(r)||o(r))return r;throw new c("Can't convert object to primitive value")}return void 0===e&&(e="number"),u(t,e)}},6969:function(t,e,r){"use strict";var n=r(2777),i=r(757);t.exports=function(t){var e=n(t,"string");return i(e)?e:e+""}},2140:function(t,e,r){"use strict";var n={};n[r(8227)("toStringTag")]="z",t.exports="[object z]"===String(n)},655:function(t,e,r){"use strict";var n=r(6955),i=String;t.exports=function(t){if("Symbol"===n(t))throw new TypeError("Cannot convert a Symbol value to a string");return i(t)}},8319:function(t){"use strict";var e=Math.round;t.exports=function(t){var r=e(t);return r<0?0:r>255?255:255&r}},6823:function(t){"use strict";var e=String;t.exports=function(t){try{return e(t)}catch(t){return"Object"}}},5823:function(t,e,r){"use strict";var n=r(6518),i=r(4576),o=r(9565),a=r(3724),u=r(2805),s=r(4644),c=r(6346),f=r(679),l=r(6980),h=r(6699),d=r(2087),p=r(8014),v=r(7696),g=r(8229),y=r(8319),E=r(6969),A=r(9297),m=r(6955),T=r(34),_=r(757),b=r(2360),w=r(1625),R=r(2967),S=r(8480).f,I=r(3251),C=r(9213).forEach,x=r(7633),O=r(2106),D=r(4913),L=r(7347),N=r(5370),M=r(1181),P=r(3167),U=M.get,k=M.set,F=M.enforce,B=D.f,G=L.f,H=i.RangeError,j=c.ArrayBuffer,V=j.prototype,Y=c.DataView,W=s.NATIVE_ARRAY_BUFFER_VIEWS,z=s.TYPED_ARRAY_TAG,q=s.TypedArray,K=s.TypedArrayPrototype,X=s.isTypedArray,Q="BYTES_PER_ELEMENT",$="Wrong length",Z=function(t,e){O(t,e,{configurable:!0,get:function(){return U(this)[e]}})},J=function(t){var e;return w(V,t)||"ArrayBuffer"===(e=m(t))||"SharedArrayBuffer"===e},tt=function(t,e){return X(t)&&!_(e)&&e in t&&d(+e)&&e>=0},et=function(t,e){return e=E(e),tt(t,e)?l(2,t[e]):G(t,e)},rt=function(t,e,r){return e=E(e),!(tt(t,e)&&T(r)&&A(r,"value"))||A(r,"get")||A(r,"set")||r.configurable||A(r,"writable")&&!r.writable||A(r,"enumerable")&&!r.enumerable?B(t,e,r):(t[e]=r.value,t)};a?(W||(L.f=et,D.f=rt,Z(K,"buffer"),Z(K,"byteOffset"),Z(K,"byteLength"),Z(K,"length")),n({target:"Object",stat:!0,forced:!W},{getOwnPropertyDescriptor:et,defineProperty:rt}),t.exports=function(t,e,r){var a=t.match(/\d+/)[0]/8,s=t+(r?"Clamped":"")+"Array",c="get"+t,l="set"+t,d=i[s],E=d,A=E&&E.prototype,m={},_=function(t,e){B(t,e,{get:function(){return function(t,e){var r=U(t);return r.view[c](e*a+r.byteOffset,!0)}(this,e)},set:function(t){return function(t,e,n){var i=U(t);i.view[l](e*a+i.byteOffset,r?y(n):n,!0)}(this,e,t)},enumerable:!0})};W?u&&(E=e((function(t,e,r,n){return f(t,A),P(T(e)?J(e)?void 0!==n?new d(e,g(r,a),n):void 0!==r?new d(e,g(r,a)):new d(e):X(e)?N(E,e):o(I,E,e):new d(v(e)),t,E)})),R&&R(E,q),C(S(d),(function(t){t in E||h(E,t,d[t])})),E.prototype=A):(E=e((function(t,e,r,n){f(t,A);var i,u,s,c=0,l=0;if(T(e)){if(!J(e))return X(e)?N(E,e):o(I,E,e);i=e,l=g(r,a);var h=e.byteLength;if(void 0===n){if(h%a)throw new H($);if((u=h-l)<0)throw new H($)}else if((u=p(n)*a)+l>h)throw new H($);s=u/a}else s=v(e),i=new j(u=s*a);for(k(t,{buffer:i,byteOffset:l,byteLength:u,length:s,view:new Y(i)});c<s;)_(t,c++)})),R&&R(E,q),A=E.prototype=b(K)),A.constructor!==E&&h(A,"constructor",E),F(A).TypedArrayConstructor=E,z&&h(A,z,s);var w=E!==d;m[s]=E,n({global:!0,constructor:!0,forced:w,sham:!W},m),Q in E||h(E,Q,a),Q in A||h(A,Q,a),x(s)}):t.exports=function(){}},2805:function(t,e,r){"use strict";var n=r(4576),i=r(9039),o=r(4428),a=r(4644).NATIVE_ARRAY_BUFFER_VIEWS,u=n.ArrayBuffer,s=n.Int8Array;t.exports=!a||!i((function(){s(1)}))||!i((function(){new s(-1)}))||!o((function(t){new s,new s(null),new s(1.5),new s(t)}),!0)||i((function(){return 1!==new s(new u(2),1,void 0).length}))},9948:function(t,e,r){"use strict";var n=r(5370),i=r(4644).getTypedArrayConstructor;t.exports=function(t,e){return n(i(t),e)}},3251:function(t,e,r){"use strict";var n=r(6080),i=r(9565),o=r(5548),a=r(8981),u=r(6198),s=r(81),c=r(851),f=r(4209),l=r(1108),h=r(4644).aTypedArrayConstructor,d=r(5854);t.exports=function(t){var e,r,p,v,g,y,E,A,m=o(this),T=a(t),_=arguments.length,b=_>1?arguments[1]:void 0,w=void 0!==b,R=c(T);if(R&&!f(R))for(A=(E=s(T,R)).next,T=[];!(y=i(A,E)).done;)T.push(y.value);for(w&&_>2&&(b=n(b,arguments[2])),r=u(T),p=new(h(m))(r),v=l(p),e=0;r>e;e++)g=w?b(T[e],e):T[e],p[e]=v?d(g):+g;return p}},3392:function(t,e,r){"use strict";var n=r(9504),i=0,o=Math.random(),a=n(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+a(++i+o,36)}},7416:function(t,e,r){"use strict";var n=r(9039),i=r(8227),o=r(3724),a=r(6395),u=i("iterator");t.exports=!n((function(){var t=new URL("b?a=1&b=2&c=3","https://a"),e=t.searchParams,r=new URLSearchParams("a=1&a=2&b=3"),n="";return t.pathname="c%20d",e.forEach((function(t,r){e.delete("b"),n+=r+t})),r.delete("a",2),r.delete("b",void 0),a&&(!t.toJSON||!r.has("a",1)||r.has("a",2)||!r.has("a",void 0)||r.has("b"))||!e.size&&(a||!o)||!e.sort||"https://a/c%20d?a=1&c=3"!==t.href||"3"!==e.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!e[u]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("https://тест").host||"#%D0%B1"!==new URL("https://a#б").hash||"a1c3"!==n||"x"!==new URL("https://x",void 0).host}))},7040:function(t,e,r){"use strict";var n=r(4495);t.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},8686:function(t,e,r){"use strict";var n=r(3724),i=r(9039);t.exports=n&&i((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},2812:function(t){"use strict";var e=TypeError;t.exports=function(t,r){if(t<r)throw new e("Not enough arguments");return t}},8622:function(t,e,r){"use strict";var n=r(4576),i=r(4901),o=n.WeakMap;t.exports=i(o)&&/native code/.test(String(o))},511:function(t,e,r){"use strict";var n=r(9167),i=r(9297),o=r(1951),a=r(4913).f;t.exports=function(t){var e=n.Symbol||(n.Symbol={});i(e,t)||a(e,t,{value:o.f(t)})}},1951:function(t,e,r){"use strict";var n=r(8227);e.f=n},8227:function(t,e,r){"use strict";var n=r(4576),i=r(5745),o=r(9297),a=r(3392),u=r(4495),s=r(7040),c=n.Symbol,f=i("wks"),l=s?c.for||c:c&&c.withoutSetter||a;t.exports=function(t){return o(f,t)||(f[t]=u&&o(c,t)?c[t]:l("Symbol."+t)),f[t]}},7452:function(t){"use strict";t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},4601:function(t,e,r){"use strict";var n=r(7751),i=r(9297),o=r(6699),a=r(1625),u=r(2967),s=r(7740),c=r(1056),f=r(3167),l=r(2603),h=r(7584),d=r(747),p=r(3724),v=r(6395);t.exports=function(t,e,r,g){var y="stackTraceLimit",E=g?2:1,A=t.split("."),m=A[A.length-1],T=n.apply(null,A);if(T){var _=T.prototype;if(!v&&i(_,"cause")&&delete _.cause,!r)return T;var b=n("Error"),w=e((function(t,e){var r=l(g?e:t,void 0),n=g?new T(t):new T;return void 0!==r&&o(n,"message",r),d(n,w,n.stack,2),this&&a(_,this)&&f(n,this,w),arguments.length>E&&h(n,arguments[E]),n}));if(w.prototype=_,"Error"!==m?u?u(w,b):s(w,b,{name:!0}):p&&y in T&&(c(w,T,y),c(w,T,"prepareStackTrace")),s(w,T),!v)try{_.name!==m&&o(_,"name",m),_.constructor=w}catch(t){}return w}}},4743:function(t,e,r){"use strict";var n=r(6518),i=r(4576),o=r(6346),a=r(7633),u="ArrayBuffer",s=o[u];n({global:!0,constructor:!0,forced:i[u]!==s},{ArrayBuffer:s}),a(u)},6573:function(t,e,r){"use strict";var n=r(3724),i=r(2106),o=r(3238),a=ArrayBuffer.prototype;n&&!("detached"in a)&&i(a,"detached",{configurable:!0,get:function(){return o(this)}})},7936:function(t,e,r){"use strict";var n=r(6518),i=r(5636);i&&n({target:"ArrayBuffer",proto:!0},{transferToFixedLength:function(){return i(this,arguments.length?arguments[0]:void 0,!1)}})},8100:function(t,e,r){"use strict";var n=r(6518),i=r(5636);i&&n({target:"ArrayBuffer",proto:!0},{transfer:function(){return i(this,arguments.length?arguments[0]:void 0,!0)}})},8706:function(t,e,r){"use strict";var n=r(6518),i=r(9039),o=r(4376),a=r(34),u=r(8981),s=r(6198),c=r(6837),f=r(4659),l=r(1469),h=r(597),d=r(8227),p=r(9519),v=d("isConcatSpreadable"),g=p>=51||!i((function(){var t=[];return t[v]=!1,t.concat()[0]!==t})),y=function(t){if(!a(t))return!1;var e=t[v];return void 0!==e?!!e:o(t)};n({target:"Array",proto:!0,arity:1,forced:!g||!h("concat")},{concat:function(t){var e,r,n,i,o,a=u(this),h=l(a,0),d=0;for(e=-1,n=arguments.length;e<n;e++)if(y(o=-1===e?a:arguments[e]))for(i=s(o),c(d+i),r=0;r<i;r++,d++)r in o&&f(h,d,o[r]);else c(d+1),f(h,d++,o);return h.length=d,h}})},2008:function(t,e,r){"use strict";var n=r(6518),i=r(9213).filter;n({target:"Array",proto:!0,forced:!r(597)("filter")},{filter:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},3418:function(t,e,r){"use strict";var n=r(6518),i=r(7916);n({target:"Array",stat:!0,forced:!r(4428)((function(t){Array.from(t)}))},{from:i})},4423:function(t,e,r){"use strict";var n=r(6518),i=r(9617).includes,o=r(9039),a=r(6469);n({target:"Array",proto:!0,forced:o((function(){return!Array(1).includes()}))},{includes:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}}),a("includes")},3792:function(t,e,r){"use strict";var n=r(5397),i=r(6469),o=r(6269),a=r(1181),u=r(4913).f,s=r(1088),c=r(2529),f=r(6395),l=r(3724),h="Array Iterator",d=a.set,p=a.getterFor(h);t.exports=s(Array,"Array",(function(t,e){d(this,{type:h,target:n(t),index:0,kind:e})}),(function(){var t=p(this),e=t.target,r=t.index++;if(!e||r>=e.length)return t.target=null,c(void 0,!0);switch(t.kind){case"keys":return c(r,!1);case"values":return c(e[r],!1)}return c([r,e[r]],!1)}),"values");var v=o.Arguments=o.Array;if(i("keys"),i("values"),i("entries"),!f&&l&&"values"!==v.name)try{u(v,"name",{value:"values"})}catch(t){}},8598:function(t,e,r){"use strict";var n=r(6518),i=r(9504),o=r(7055),a=r(5397),u=r(4598),s=i([].join);n({target:"Array",proto:!0,forced:o!==Object||!u("join",",")},{join:function(t){return s(a(this),void 0===t?",":t)}})},2062:function(t,e,r){"use strict";var n=r(6518),i=r(9213).map;n({target:"Array",proto:!0,forced:!r(597)("map")},{map:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},4114:function(t,e,r){"use strict";var n=r(6518),i=r(8981),o=r(6198),a=r(4527),u=r(6837);n({target:"Array",proto:!0,arity:1,forced:r(9039)((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(t){return t instanceof TypeError}}()},{push:function(t){var e=i(this),r=o(e),n=arguments.length;u(r+n);for(var s=0;s<n;s++)e[r]=arguments[s],r++;return a(e,r),r}})},4782:function(t,e,r){"use strict";var n=r(6518),i=r(4376),o=r(3517),a=r(34),u=r(5610),s=r(6198),c=r(5397),f=r(4659),l=r(8227),h=r(597),d=r(7680),p=h("slice"),v=l("species"),g=Array,y=Math.max;n({target:"Array",proto:!0,forced:!p},{slice:function(t,e){var r,n,l,h=c(this),p=s(h),E=u(t,p),A=u(void 0===e?p:e,p);if(i(h)&&(r=h.constructor,(o(r)&&(r===g||i(r.prototype))||a(r)&&null===(r=r[v]))&&(r=void 0),r===g||void 0===r))return d(h,E,A);for(n=new(void 0===r?g:r)(y(A-E,0)),l=0;E<A;E++,l++)E in h&&f(n,l,h[E]);return n.length=l,n}})},4554:function(t,e,r){"use strict";var n=r(6518),i=r(8981),o=r(5610),a=r(1291),u=r(6198),s=r(4527),c=r(6837),f=r(1469),l=r(4659),h=r(4606),d=r(597)("splice"),p=Math.max,v=Math.min;n({target:"Array",proto:!0,forced:!d},{splice:function(t,e){var r,n,d,g,y,E,A=i(this),m=u(A),T=o(t,m),_=arguments.length;for(0===_?r=n=0:1===_?(r=0,n=m-T):(r=_-2,n=v(p(a(e),0),m-T)),c(m+r-n),d=f(A,n),g=0;g<n;g++)(y=T+g)in A&&l(d,g,A[y]);if(d.length=n,r<n){for(g=T;g<m-n;g++)E=g+r,(y=g+n)in A?A[E]=A[y]:h(A,E);for(g=m;g>m-n+r;g--)h(A,g-1)}else if(r>n)for(g=m-n;g>T;g--)E=g+r-1,(y=g+n-1)in A?A[E]=A[y]:h(A,E);for(g=0;g<r;g++)A[g+T]=arguments[g+2];return s(A,m-n+r),d}})},9572:function(t,e,r){"use strict";var n=r(9297),i=r(6840),o=r(3640),a=r(8227)("toPrimitive"),u=Date.prototype;n(u,a)||i(u,a,o)},6280:function(t,e,r){"use strict";var n=r(6518),i=r(4576),o=r(8745),a=r(4601),u="WebAssembly",s=i[u],c=7!==new Error("e",{cause:7}).cause,f=function(t,e){var r={};r[t]=a(t,e,c),n({global:!0,constructor:!0,arity:1,forced:c},r)},l=function(t,e){if(s&&s[t]){var r={};r[t]=a(u+"."+t,e,c),n({target:u,stat:!0,constructor:!0,arity:1,forced:c},r)}};f("Error",(function(t){return function(e){return o(t,this,arguments)}})),f("EvalError",(function(t){return function(e){return o(t,this,arguments)}})),f("RangeError",(function(t){return function(e){return o(t,this,arguments)}})),f("ReferenceError",(function(t){return function(e){return o(t,this,arguments)}})),f("SyntaxError",(function(t){return function(e){return o(t,this,arguments)}})),f("TypeError",(function(t){return function(e){return o(t,this,arguments)}})),f("URIError",(function(t){return function(e){return o(t,this,arguments)}})),l("CompileError",(function(t){return function(e){return o(t,this,arguments)}})),l("LinkError",(function(t){return function(e){return o(t,this,arguments)}})),l("RuntimeError",(function(t){return function(e){return o(t,this,arguments)}}))},2010:function(t,e,r){"use strict";var n=r(3724),i=r(350).EXISTS,o=r(9504),a=r(2106),u=Function.prototype,s=o(u.toString),c=/function\b(?:\s|\/\*[\S\s]*?\*\/|\/\/[^\n\r]*[\n\r]+)*([^\s(/]*)/,f=o(c.exec);n&&!i&&a(u,"name",{configurable:!0,get:function(){try{return f(c,s(this))[1]}catch(t){return""}}})},3110:function(t,e,r){"use strict";var n=r(6518),i=r(7751),o=r(8745),a=r(9565),u=r(9504),s=r(9039),c=r(4901),f=r(757),l=r(7680),h=r(6933),d=r(4495),p=String,v=i("JSON","stringify"),g=u(/./.exec),y=u("".charAt),E=u("".charCodeAt),A=u("".replace),m=u(1..toString),T=/[\uD800-\uDFFF]/g,_=/^[\uD800-\uDBFF]$/,b=/^[\uDC00-\uDFFF]$/,w=!d||s((function(){var t=i("Symbol")("stringify detection");return"[null]"!==v([t])||"{}"!==v({a:t})||"{}"!==v(Object(t))})),R=s((function(){return'"\\udf06\\ud834"'!==v("\udf06\ud834")||'"\\udead"'!==v("\udead")})),S=function(t,e){var r=l(arguments),n=h(e);if(c(n)||void 0!==t&&!f(t))return r[1]=function(t,e){if(c(n)&&(e=a(n,this,p(t),e)),!f(e))return e},o(v,null,r)},I=function(t,e,r){var n=y(r,e-1),i=y(r,e+1);return g(_,t)&&!g(b,i)||g(b,t)&&!g(_,n)?"\\u"+m(E(t,0),16):t};v&&n({target:"JSON",stat:!0,arity:3,forced:w||R},{stringify:function(t,e,r){var n=l(arguments),i=o(w?S:v,null,n);return R&&"string"==typeof i?A(i,T,I):i}})},2892:function(t,e,r){"use strict";var n=r(6518),i=r(6395),o=r(3724),a=r(4576),u=r(9167),s=r(9504),c=r(2796),f=r(9297),l=r(3167),h=r(1625),d=r(757),p=r(2777),v=r(9039),g=r(8480).f,y=r(7347).f,E=r(4913).f,A=r(1240),m=r(3802).trim,T="Number",_=a[T],b=u[T],w=_.prototype,R=a.TypeError,S=s("".slice),I=s("".charCodeAt),C=c(T,!_(" 0o1")||!_("0b1")||_("+0x1")),x=function(t){var e,r=arguments.length<1?0:_(function(t){var e=p(t,"number");return"bigint"==typeof e?e:function(t){var e,r,n,i,o,a,u,s,c=p(t,"number");if(d(c))throw new R("Cannot convert a Symbol value to a number");if("string"==typeof c&&c.length>2)if(c=m(c),43===(e=I(c,0))||45===e){if(88===(r=I(c,2))||120===r)return NaN}else if(48===e){switch(I(c,1)){case 66:case 98:n=2,i=49;break;case 79:case 111:n=8,i=55;break;default:return+c}for(a=(o=S(c,2)).length,u=0;u<a;u++)if((s=I(o,u))<48||s>i)return NaN;return parseInt(o,n)}return+c}(e)}(t));return h(w,e=this)&&v((function(){A(e)}))?l(Object(r),this,x):r};x.prototype=w,C&&!i&&(w.constructor=x),n({global:!0,constructor:!0,wrap:!0,forced:C},{Number:x});var O=function(t,e){for(var r,n=o?g(e):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),i=0;n.length>i;i++)f(e,r=n[i])&&!f(t,r)&&E(t,r,y(e,r))};i&&b&&O(u[T],b),(C||i)&&O(u[T],_)},9773:function(t,e,r){"use strict";var n=r(6518),i=r(4495),o=r(9039),a=r(3717),u=r(8981);n({target:"Object",stat:!0,forced:!i||o((function(){a.f(1)}))},{getOwnPropertySymbols:function(t){var e=a.f;return e?e(u(t)):[]}})},875:function(t,e,r){"use strict";var n=r(6518),i=r(9039),o=r(8981),a=r(2787),u=r(2211);n({target:"Object",stat:!0,forced:i((function(){a(1)})),sham:!u},{getPrototypeOf:function(t){return a(o(t))}})},9432:function(t,e,r){"use strict";var n=r(6518),i=r(8981),o=r(1072);n({target:"Object",stat:!0,forced:r(9039)((function(){o(1)}))},{keys:function(t){return o(i(t))}})},6099:function(t,e,r){"use strict";var n=r(2140),i=r(6840),o=r(3179);n||i(Object.prototype,"toString",o,{unsafe:!0})},825:function(t,e,r){"use strict";var n=r(6518),i=r(7751),o=r(8745),a=r(566),u=r(5548),s=r(8551),c=r(34),f=r(2360),l=r(9039),h=i("Reflect","construct"),d=Object.prototype,p=[].push,v=l((function(){function t(){}return!(h((function(){}),[],t)instanceof t)})),g=!l((function(){h((function(){}))})),y=v||g;n({target:"Reflect",stat:!0,forced:y,sham:y},{construct:function(t,e){u(t),s(e);var r=arguments.length<3?t:u(arguments[2]);if(g&&!v)return h(t,e,r);if(t===r){switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3])}var n=[null];return o(p,n,e),new(o(a,t,n))}var i=r.prototype,l=f(c(i)?i:d),y=o(t,l,e);return c(y)?y:l}})},4864:function(t,e,r){"use strict";var n=r(3724),i=r(4576),o=r(9504),a=r(2796),u=r(3167),s=r(6699),c=r(2360),f=r(8480).f,l=r(1625),h=r(788),d=r(655),p=r(1034),v=r(8429),g=r(1056),y=r(6840),E=r(9039),A=r(9297),m=r(1181).enforce,T=r(7633),_=r(8227),b=r(3635),w=r(8814),R=_("match"),S=i.RegExp,I=S.prototype,C=i.SyntaxError,x=o(I.exec),O=o("".charAt),D=o("".replace),L=o("".indexOf),N=o("".slice),M=/^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/,P=/a/g,U=/a/g,k=new S(P)!==P,F=v.MISSED_STICKY,B=v.UNSUPPORTED_Y;if(a("RegExp",n&&(!k||F||b||w||E((function(){return U[R]=!1,S(P)!==P||S(U)===U||"/a/i"!==String(S(P,"i"))}))))){for(var G=function(t,e){var r,n,i,o,a,f,v=l(I,this),g=h(t),y=void 0===e,E=[],T=t;if(!v&&g&&y&&t.constructor===G)return t;if((g||l(I,t))&&(t=t.source,y&&(e=p(T))),t=void 0===t?"":d(t),e=void 0===e?"":d(e),T=t,b&&"dotAll"in P&&(n=!!e&&L(e,"s")>-1)&&(e=D(e,/s/g,"")),r=e,F&&"sticky"in P&&(i=!!e&&L(e,"y")>-1)&&B&&(e=D(e,/y/g,"")),w&&(o=function(t){for(var e,r=t.length,n=0,i="",o=[],a=c(null),u=!1,s=!1,f=0,l="";n<=r;n++){if("\\"===(e=O(t,n)))e+=O(t,++n);else if("]"===e)u=!1;else if(!u)switch(!0){case"["===e:u=!0;break;case"("===e:if(i+=e,"?:"===N(t,n+1,n+3))continue;x(M,N(t,n+1))&&(n+=2,s=!0),f++;continue;case">"===e&&s:if(""===l||A(a,l))throw new C("Invalid capture group name");a[l]=!0,o[o.length]=[l,f],s=!1,l="";continue}s?l+=e:i+=e}return[i,o]}(t),t=o[0],E=o[1]),a=u(S(t,e),v?this:I,G),(n||i||E.length)&&(f=m(a),n&&(f.dotAll=!0,f.raw=G(function(t){for(var e,r=t.length,n=0,i="",o=!1;n<=r;n++)"\\"!==(e=O(t,n))?o||"."!==e?("["===e?o=!0:"]"===e&&(o=!1),i+=e):i+="[\\s\\S]":i+=e+O(t,++n);return i}(t),r)),i&&(f.sticky=!0),E.length&&(f.groups=E)),t!==T)try{s(a,"source",""===T?"(?:)":T)}catch(t){}return a},H=f(S),j=0;H.length>j;)g(G,S,H[j++]);I.constructor=G,G.prototype=I,y(i,"RegExp",G,{constructor:!0})}T("RegExp")},7465:function(t,e,r){"use strict";var n=r(3724),i=r(3635),o=r(2195),a=r(2106),u=r(1181).get,s=RegExp.prototype,c=TypeError;n&&i&&a(s,"dotAll",{configurable:!0,get:function(){if(this!==s){if("RegExp"===o(this))return!!u(this).dotAll;throw new c("Incompatible receiver, RegExp required")}}})},7495:function(t,e,r){"use strict";var n=r(6518),i=r(7323);n({target:"RegExp",proto:!0,forced:/./.exec!==i},{exec:i})},7745:function(t,e,r){"use strict";var n=r(3724),i=r(8429).MISSED_STICKY,o=r(2195),a=r(2106),u=r(1181).get,s=RegExp.prototype,c=TypeError;n&&i&&a(s,"sticky",{configurable:!0,get:function(){if(this!==s){if("RegExp"===o(this))return!!u(this).sticky;throw new c("Incompatible receiver, RegExp required")}}})},906:function(t,e,r){"use strict";r(7495);var n,i,o=r(6518),a=r(9565),u=r(4901),s=r(8551),c=r(655),f=(n=!1,(i=/[ac]/).exec=function(){return n=!0,/./.exec.apply(this,arguments)},!0===i.test("abc")&&n),l=/./.test;o({target:"RegExp",proto:!0,forced:!f},{test:function(t){var e=s(this),r=c(t),n=e.exec;if(!u(n))return a(l,e,r);var i=a(n,e,r);return null!==i&&(s(i),!0)}})},8781:function(t,e,r){"use strict";var n=r(350).PROPER,i=r(6840),o=r(8551),a=r(655),u=r(9039),s=r(1034),c="toString",f=RegExp.prototype,l=f[c],h=u((function(){return"/a/b"!==l.call({source:"a",flags:"b"})})),d=n&&l.name!==c;(h||d)&&i(f,c,(function(){var t=o(this);return"/"+a(t.source)+"/"+a(s(t))}),{unsafe:!0})},7337:function(t,e,r){"use strict";var n=r(6518),i=r(9504),o=r(5610),a=RangeError,u=String.fromCharCode,s=String.fromCodePoint,c=i([].join);n({target:"String",stat:!0,arity:1,forced:!!s&&1!==s.length},{fromCodePoint:function(t){for(var e,r=[],n=arguments.length,i=0;n>i;){if(e=+arguments[i++],o(e,1114111)!==e)throw new a(e+" is not a valid code point");r[i]=e<65536?u(e):u(55296+((e-=65536)>>10),e%1024+56320)}return c(r,"")}})},1699:function(t,e,r){"use strict";var n=r(6518),i=r(9504),o=r(5749),a=r(7750),u=r(655),s=r(1436),c=i("".indexOf);n({target:"String",proto:!0,forced:!s("includes")},{includes:function(t){return!!~c(u(a(this)),u(o(t)),arguments.length>1?arguments[1]:void 0)}})},7764:function(t,e,r){"use strict";var n=r(8183).charAt,i=r(655),o=r(1181),a=r(1088),u=r(2529),s="String Iterator",c=o.set,f=o.getterFor(s);a(String,"String",(function(t){c(this,{type:s,string:i(t),index:0})}),(function(){var t,e=f(this),r=e.string,i=e.index;return i>=r.length?u(void 0,!0):(t=n(r,i),e.index+=t.length,u(t,!1))}))},1761:function(t,e,r){"use strict";var n=r(9565),i=r(9228),o=r(8551),a=r(4117),u=r(8014),s=r(655),c=r(7750),f=r(5966),l=r(7829),h=r(6682);i("match",(function(t,e,r){return[function(e){var r=c(this),i=a(e)?void 0:f(e,t);return i?n(i,e,r):new RegExp(e)[t](s(r))},function(t){var n=o(this),i=s(t),a=r(e,n,i);if(a.done)return a.value;if(!n.global)return h(n,i);var c=n.unicode;n.lastIndex=0;for(var f,d=[],p=0;null!==(f=h(n,i));){var v=s(f[0]);d[p]=v,""===v&&(n.lastIndex=l(i,u(n.lastIndex),c)),p++}return 0===p?null:d}]}))},5440:function(t,e,r){"use strict";var n=r(8745),i=r(9565),o=r(9504),a=r(9228),u=r(9039),s=r(8551),c=r(4901),f=r(4117),l=r(1291),h=r(8014),d=r(655),p=r(7750),v=r(7829),g=r(5966),y=r(2478),E=r(6682),A=r(8227)("replace"),m=Math.max,T=Math.min,_=o([].concat),b=o([].push),w=o("".indexOf),R=o("".slice),S="$0"==="a".replace(/./,"$0"),I=!!/./[A]&&""===/./[A]("a","$0");a("replace",(function(t,e,r){var o=I?"$":"$0";return[function(t,r){var n=p(this),o=f(t)?void 0:g(t,A);return o?i(o,t,n,r):i(e,d(n),t,r)},function(t,i){var a=s(this),u=d(t);if("string"==typeof i&&-1===w(i,o)&&-1===w(i,"$<")){var f=r(e,a,u,i);if(f.done)return f.value}var p=c(i);p||(i=d(i));var g,A=a.global;A&&(g=a.unicode,a.lastIndex=0);for(var S,I=[];null!==(S=E(a,u))&&(b(I,S),A);)""===d(S[0])&&(a.lastIndex=v(u,h(a.lastIndex),g));for(var C,x="",O=0,D=0;D<I.length;D++){for(var L,N=d((S=I[D])[0]),M=m(T(l(S.index),u.length),0),P=[],U=1;U<S.length;U++)b(P,void 0===(C=S[U])?C:String(C));var k=S.groups;if(p){var F=_([N],P,M,u);void 0!==k&&b(F,k),L=d(n(i,void 0,F))}else L=y(N,u,M,P,k,i);M>=O&&(x+=R(u,O,M)+L,O=M+N.length)}return x+R(u,O)}]}),!!u((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!S||I)},5746:function(t,e,r){"use strict";var n=r(9565),i=r(9228),o=r(8551),a=r(4117),u=r(7750),s=r(3470),c=r(655),f=r(5966),l=r(6682);i("search",(function(t,e,r){return[function(e){var r=u(this),i=a(e)?void 0:f(e,t);return i?n(i,e,r):new RegExp(e)[t](c(r))},function(t){var n=o(this),i=c(t),a=r(e,n,i);if(a.done)return a.value;var u=n.lastIndex;s(u,0)||(n.lastIndex=0);var f=l(n,i);return s(n.lastIndex,u)||(n.lastIndex=u),null===f?-1:f.index}]}))},2762:function(t,e,r){"use strict";var n=r(6518),i=r(3802).trim;n({target:"String",proto:!0,forced:r(706)("trim")},{trim:function(){return i(this)}})},6761:function(t,e,r){"use strict";var n=r(6518),i=r(4576),o=r(9565),a=r(9504),u=r(6395),s=r(3724),c=r(4495),f=r(9039),l=r(9297),h=r(1625),d=r(8551),p=r(5397),v=r(6969),g=r(655),y=r(6980),E=r(2360),A=r(1072),m=r(8480),T=r(298),_=r(3717),b=r(7347),w=r(4913),R=r(6801),S=r(8773),I=r(6840),C=r(2106),x=r(5745),O=r(6119),D=r(421),L=r(3392),N=r(8227),M=r(1951),P=r(511),U=r(8242),k=r(687),F=r(1181),B=r(9213).forEach,G=O("hidden"),H="Symbol",j="prototype",V=F.set,Y=F.getterFor(H),W=Object[j],z=i.Symbol,q=z&&z[j],K=i.RangeError,X=i.TypeError,Q=i.QObject,$=b.f,Z=w.f,J=T.f,tt=S.f,et=a([].push),rt=x("symbols"),nt=x("op-symbols"),it=x("wks"),ot=!Q||!Q[j]||!Q[j].findChild,at=function(t,e,r){var n=$(W,e);n&&delete W[e],Z(t,e,r),n&&t!==W&&Z(W,e,n)},ut=s&&f((function(){return 7!==E(Z({},"a",{get:function(){return Z(this,"a",{value:7}).a}})).a}))?at:Z,st=function(t,e){var r=rt[t]=E(q);return V(r,{type:H,tag:t,description:e}),s||(r.description=e),r},ct=function(t,e,r){t===W&&ct(nt,e,r),d(t);var n=v(e);return d(r),l(rt,n)?(r.enumerable?(l(t,G)&&t[G][n]&&(t[G][n]=!1),r=E(r,{enumerable:y(0,!1)})):(l(t,G)||Z(t,G,y(1,E(null))),t[G][n]=!0),ut(t,n,r)):Z(t,n,r)},ft=function(t,e){d(t);var r=p(e),n=A(r).concat(pt(r));return B(n,(function(e){s&&!o(lt,r,e)||ct(t,e,r[e])})),t},lt=function(t){var e=v(t),r=o(tt,this,e);return!(this===W&&l(rt,e)&&!l(nt,e))&&(!(r||!l(this,e)||!l(rt,e)||l(this,G)&&this[G][e])||r)},ht=function(t,e){var r=p(t),n=v(e);if(r!==W||!l(rt,n)||l(nt,n)){var i=$(r,n);return!i||!l(rt,n)||l(r,G)&&r[G][n]||(i.enumerable=!0),i}},dt=function(t){var e=J(p(t)),r=[];return B(e,(function(t){l(rt,t)||l(D,t)||et(r,t)})),r},pt=function(t){var e=t===W,r=J(e?nt:p(t)),n=[];return B(r,(function(t){!l(rt,t)||e&&!l(W,t)||et(n,rt[t])})),n};c||(z=function(){if(h(q,this))throw new X("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?g(arguments[0]):void 0,e=L(t),r=function(t){var n=void 0===this?i:this;n===W&&o(r,nt,t),l(n,G)&&l(n[G],e)&&(n[G][e]=!1);var a=y(1,t);try{ut(n,e,a)}catch(t){if(!(t instanceof K))throw t;at(n,e,a)}};return s&&ot&&ut(W,e,{configurable:!0,set:r}),st(e,t)},I(q=z[j],"toString",(function(){return Y(this).tag})),I(z,"withoutSetter",(function(t){return st(L(t),t)})),S.f=lt,w.f=ct,R.f=ft,b.f=ht,m.f=T.f=dt,_.f=pt,M.f=function(t){return st(N(t),t)},s&&(C(q,"description",{configurable:!0,get:function(){return Y(this).description}}),u||I(W,"propertyIsEnumerable",lt,{unsafe:!0}))),n({global:!0,constructor:!0,wrap:!0,forced:!c,sham:!c},{Symbol:z}),B(A(it),(function(t){P(t)})),n({target:H,stat:!0,forced:!c},{useSetter:function(){ot=!0},useSimple:function(){ot=!1}}),n({target:"Object",stat:!0,forced:!c,sham:!s},{create:function(t,e){return void 0===e?E(t):ft(E(t),e)},defineProperty:ct,defineProperties:ft,getOwnPropertyDescriptor:ht}),n({target:"Object",stat:!0,forced:!c},{getOwnPropertyNames:dt}),U(),k(z,H),D[G]=!0},9463:function(t,e,r){"use strict";var n=r(6518),i=r(3724),o=r(4576),a=r(9504),u=r(9297),s=r(4901),c=r(1625),f=r(655),l=r(2106),h=r(7740),d=o.Symbol,p=d&&d.prototype;if(i&&s(d)&&(!("description"in p)||void 0!==d().description)){var v={},g=function(){var t=arguments.length<1||void 0===arguments[0]?void 0:f(arguments[0]),e=c(p,this)?new d(t):void 0===t?d():d(t);return""===t&&(v[e]=!0),e};h(g,d),g.prototype=p,p.constructor=g;var y="Symbol(description detection)"===String(d("description detection")),E=a(p.valueOf),A=a(p.toString),m=/^Symbol\((.*)\)[^)]+$/,T=a("".replace),_=a("".slice);l(p,"description",{configurable:!0,get:function(){var t=E(this);if(u(v,t))return"";var e=A(t),r=y?_(e,7,-1):T(e,m,"$1");return""===r?void 0:r}}),n({global:!0,constructor:!0,forced:!0},{Symbol:g})}},1510:function(t,e,r){"use strict";var n=r(6518),i=r(7751),o=r(9297),a=r(655),u=r(5745),s=r(1296),c=u("string-to-symbol-registry"),f=u("symbol-to-string-registry");n({target:"Symbol",stat:!0,forced:!s},{for:function(t){var e=a(t);if(o(c,e))return c[e];var r=i("Symbol")(e);return c[e]=r,f[r]=e,r}})},2259:function(t,e,r){"use strict";r(511)("iterator")},2675:function(t,e,r){"use strict";r(6761),r(1510),r(7812),r(3110),r(9773)},7812:function(t,e,r){"use strict";var n=r(6518),i=r(9297),o=r(757),a=r(6823),u=r(5745),s=r(1296),c=u("symbol-to-string-registry");n({target:"Symbol",stat:!0,forced:!s},{keyFor:function(t){if(!o(t))throw new TypeError(a(t)+" is not a symbol");if(i(c,t))return c[t]}})},5700:function(t,e,r){"use strict";var n=r(511),i=r(8242);n("toPrimitive"),i()},8140:function(t,e,r){"use strict";var n=r(4644),i=r(6198),o=r(1291),a=n.aTypedArray;(0,n.exportTypedArrayMethod)("at",(function(t){var e=a(this),r=i(e),n=o(t),u=n>=0?n:r+n;return u<0||u>=r?void 0:e[u]}))},1630:function(t,e,r){"use strict";var n=r(9504),i=r(4644),o=n(r(7029)),a=i.aTypedArray;(0,i.exportTypedArrayMethod)("copyWithin",(function(t,e){return o(a(this),t,e,arguments.length>2?arguments[2]:void 0)}))},2170:function(t,e,r){"use strict";var n=r(4644),i=r(9213).every,o=n.aTypedArray;(0,n.exportTypedArrayMethod)("every",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},5044:function(t,e,r){"use strict";var n=r(4644),i=r(4373),o=r(5854),a=r(6955),u=r(9565),s=r(9504),c=r(9039),f=n.aTypedArray,l=n.exportTypedArrayMethod,h=s("".slice);l("fill",(function(t){var e=arguments.length;f(this);var r="Big"===h(a(this),0,3)?o(t):+t;return u(i,this,r,e>1?arguments[1]:void 0,e>2?arguments[2]:void 0)}),c((function(){var t=0;return new Int8Array(2).fill({valueOf:function(){return t++}}),1!==t})))},1920:function(t,e,r){"use strict";var n=r(4644),i=r(9213).filter,o=r(9948),a=n.aTypedArray;(0,n.exportTypedArrayMethod)("filter",(function(t){var e=i(a(this),t,arguments.length>1?arguments[1]:void 0);return o(this,e)}))},9955:function(t,e,r){"use strict";var n=r(4644),i=r(9213).findIndex,o=n.aTypedArray;(0,n.exportTypedArrayMethod)("findIndex",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},1134:function(t,e,r){"use strict";var n=r(4644),i=r(3839).findLastIndex,o=n.aTypedArray;(0,n.exportTypedArrayMethod)("findLastIndex",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},1903:function(t,e,r){"use strict";var n=r(4644),i=r(3839).findLast,o=n.aTypedArray;(0,n.exportTypedArrayMethod)("findLast",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},1694:function(t,e,r){"use strict";var n=r(4644),i=r(9213).find,o=n.aTypedArray;(0,n.exportTypedArrayMethod)("find",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},3206:function(t,e,r){"use strict";var n=r(4644),i=r(9213).forEach,o=n.aTypedArray;(0,n.exportTypedArrayMethod)("forEach",(function(t){i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},4496:function(t,e,r){"use strict";var n=r(4644),i=r(9617).includes,o=n.aTypedArray;(0,n.exportTypedArrayMethod)("includes",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},6651:function(t,e,r){"use strict";var n=r(4644),i=r(9617).indexOf,o=n.aTypedArray;(0,n.exportTypedArrayMethod)("indexOf",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},2887:function(t,e,r){"use strict";var n=r(4576),i=r(9039),o=r(9504),a=r(4644),u=r(3792),s=r(8227)("iterator"),c=n.Uint8Array,f=o(u.values),l=o(u.keys),h=o(u.entries),d=a.aTypedArray,p=a.exportTypedArrayMethod,v=c&&c.prototype,g=!i((function(){v[s].call([1])})),y=!!v&&v.values&&v[s]===v.values&&"values"===v.values.name,E=function(){return f(d(this))};p("entries",(function(){return h(d(this))}),g),p("keys",(function(){return l(d(this))}),g),p("values",E,g||!y,{name:"values"}),p(s,E,g||!y,{name:"values"})},9369:function(t,e,r){"use strict";var n=r(4644),i=r(9504),o=n.aTypedArray,a=n.exportTypedArrayMethod,u=i([].join);a("join",(function(t){return u(o(this),t)}))},6812:function(t,e,r){"use strict";var n=r(4644),i=r(8745),o=r(8379),a=n.aTypedArray;(0,n.exportTypedArrayMethod)("lastIndexOf",(function(t){var e=arguments.length;return i(o,a(this),e>1?[t,arguments[1]]:[t])}))},8995:function(t,e,r){"use strict";var n=r(4644),i=r(9213).map,o=n.aTypedArray,a=n.getTypedArrayConstructor;(0,n.exportTypedArrayMethod)("map",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0,(function(t,e){return new(a(t))(e)}))}))},6072:function(t,e,r){"use strict";var n=r(4644),i=r(926).right,o=n.aTypedArray;(0,n.exportTypedArrayMethod)("reduceRight",(function(t){var e=arguments.length;return i(o(this),t,e,e>1?arguments[1]:void 0)}))},1575:function(t,e,r){"use strict";var n=r(4644),i=r(926).left,o=n.aTypedArray;(0,n.exportTypedArrayMethod)("reduce",(function(t){var e=arguments.length;return i(o(this),t,e,e>1?arguments[1]:void 0)}))},8747:function(t,e,r){"use strict";var n=r(4644),i=n.aTypedArray,o=n.exportTypedArrayMethod,a=Math.floor;o("reverse",(function(){for(var t,e=this,r=i(e).length,n=a(r/2),o=0;o<n;)t=e[o],e[o++]=e[--r],e[r]=t;return e}))},8845:function(t,e,r){"use strict";var n=r(4576),i=r(9565),o=r(4644),a=r(6198),u=r(8229),s=r(8981),c=r(9039),f=n.RangeError,l=n.Int8Array,h=l&&l.prototype,d=h&&h.set,p=o.aTypedArray,v=o.exportTypedArrayMethod,g=!c((function(){var t=new Uint8ClampedArray(2);return i(d,t,{length:1,0:3},1),3!==t[1]})),y=g&&o.NATIVE_ARRAY_BUFFER_VIEWS&&c((function(){var t=new l(2);return t.set(1),t.set("2",1),0!==t[0]||2!==t[1]}));v("set",(function(t){p(this);var e=u(arguments.length>1?arguments[1]:void 0,1),r=s(t);if(g)return i(d,this,r,e);var n=this.length,o=a(r),c=0;if(o+e>n)throw new f("Wrong length");for(;c<o;)this[e+c]=r[c++]}),!g||y)},9423:function(t,e,r){"use strict";var n=r(4644),i=r(9039),o=r(7680),a=n.aTypedArray,u=n.getTypedArrayConstructor;(0,n.exportTypedArrayMethod)("slice",(function(t,e){for(var r=o(a(this),t,e),n=u(this),i=0,s=r.length,c=new n(s);s>i;)c[i]=r[i++];return c}),i((function(){new Int8Array(1).slice()})))},7301:function(t,e,r){"use strict";var n=r(4644),i=r(9213).some,o=n.aTypedArray;(0,n.exportTypedArrayMethod)("some",(function(t){return i(o(this),t,arguments.length>1?arguments[1]:void 0)}))},373:function(t,e,r){"use strict";var n=r(4576),i=r(7476),o=r(9039),a=r(9306),u=r(4488),s=r(4644),c=r(3709),f=r(3763),l=r(9519),h=r(3607),d=s.aTypedArray,p=s.exportTypedArrayMethod,v=n.Uint16Array,g=v&&i(v.prototype.sort),y=!(!g||o((function(){g(new v(2),null)}))&&o((function(){g(new v(2),{})}))),E=!!g&&!o((function(){if(l)return l<74;if(c)return c<67;if(f)return!0;if(h)return h<602;var t,e,r=new v(516),n=Array(516);for(t=0;t<516;t++)e=t%4,r[t]=515-t,n[t]=t-2*e+3;for(g(r,(function(t,e){return(t/4|0)-(e/4|0)})),t=0;t<516;t++)if(r[t]!==n[t])return!0}));p("sort",(function(t){return void 0!==t&&a(t),E?g(this,t):u(d(this),function(t){return function(e,r){return void 0!==t?+t(e,r)||0:r!=r?-1:e!=e?1:0===e&&0===r?1/e>0&&1/r<0?1:-1:e>r}}(t))}),!E||y)},6614:function(t,e,r){"use strict";var n=r(4644),i=r(8014),o=r(5610),a=n.aTypedArray,u=n.getTypedArrayConstructor;(0,n.exportTypedArrayMethod)("subarray",(function(t,e){var r=a(this),n=r.length,s=o(t,n);return new(u(r))(r.buffer,r.byteOffset+s*r.BYTES_PER_ELEMENT,i((void 0===e?n:o(e,n))-s))}))},1405:function(t,e,r){"use strict";var n=r(4576),i=r(8745),o=r(4644),a=r(9039),u=r(7680),s=n.Int8Array,c=o.aTypedArray,f=o.exportTypedArrayMethod,l=[].toLocaleString,h=!!s&&a((function(){l.call(new s(1))}));f("toLocaleString",(function(){return i(l,h?u(c(this)):c(this),u(arguments))}),a((function(){return[1,2].toLocaleString()!==new s([1,2]).toLocaleString()}))||!a((function(){s.prototype.toLocaleString.call([1,2])})))},7467:function(t,e,r){"use strict";var n=r(7628),i=r(4644),o=i.aTypedArray,a=i.exportTypedArrayMethod,u=i.getTypedArrayConstructor;a("toReversed",(function(){return n(o(this),u(this))}))},4732:function(t,e,r){"use strict";var n=r(4644),i=r(9504),o=r(9306),a=r(5370),u=n.aTypedArray,s=n.getTypedArrayConstructor,c=n.exportTypedArrayMethod,f=i(n.TypedArrayPrototype.sort);c("toSorted",(function(t){void 0!==t&&o(t);var e=u(this),r=a(s(e),e);return f(r,t)}))},3684:function(t,e,r){"use strict";var n=r(4644).exportTypedArrayMethod,i=r(9039),o=r(4576),a=r(9504),u=o.Uint8Array,s=u&&u.prototype||{},c=[].toString,f=a([].join);i((function(){c.call({})}))&&(c=function(){return f(this)});var l=s.toString!==c;n("toString",c,l)},1740:function(t,e,r){"use strict";r(5823)("Uint32",(function(t){return function(e,r,n){return t(this,e,r,n)}}))},1489:function(t,e,r){"use strict";r(5823)("Uint8",(function(t){return function(e,r,n){return t(this,e,r,n)}}))},9577:function(t,e,r){"use strict";var n=r(9928),i=r(4644),o=r(1108),a=r(1291),u=r(5854),s=i.aTypedArray,c=i.getTypedArrayConstructor,f=i.exportTypedArrayMethod,l=!!function(){try{new Int8Array(1).with(2,{valueOf:function(){throw 8}})}catch(t){return 8===t}}();f("with",{with:function(t,e){var r=s(this),i=a(t),f=o(r)?u(e):+e;return n(r,c(r),i,f)}}.with,!l)},3500:function(t,e,r){"use strict";var n=r(4576),i=r(7400),o=r(9296),a=r(235),u=r(6699),s=function(t){if(t&&t.forEach!==a)try{u(t,"forEach",a)}catch(e){t.forEach=a}};for(var c in i)i[c]&&s(n[c]&&n[c].prototype);s(o)},2953:function(t,e,r){"use strict";var n=r(4576),i=r(7400),o=r(9296),a=r(3792),u=r(6699),s=r(687),c=r(8227)("iterator"),f=a.values,l=function(t,e){if(t){if(t[c]!==f)try{u(t,c,f)}catch(e){t[c]=f}if(s(t,e,!0),i[e])for(var r in a)if(t[r]!==a[r])try{u(t,r,a[r])}catch(e){t[r]=a[r]}}};for(var h in i)l(n[h]&&n[h].prototype,h);l(o,"DOMTokenList")},8406:function(t,e,r){"use strict";r(3792),r(7337);var n=r(6518),i=r(4576),o=r(3389),a=r(7751),u=r(9565),s=r(9504),c=r(3724),f=r(7416),l=r(6840),h=r(2106),d=r(6279),p=r(687),v=r(3994),g=r(1181),y=r(679),E=r(4901),A=r(9297),m=r(6080),T=r(6955),_=r(8551),b=r(34),w=r(655),R=r(2360),S=r(6980),I=r(81),C=r(851),x=r(2529),O=r(2812),D=r(8227),L=r(4488),N=D("iterator"),M="URLSearchParams",P=M+"Iterator",U=g.set,k=g.getterFor(M),F=g.getterFor(P),B=o("fetch"),G=o("Request"),H=o("Headers"),j=G&&G.prototype,V=H&&H.prototype,Y=i.TypeError,W=i.encodeURIComponent,z=String.fromCharCode,q=a("String","fromCodePoint"),K=parseInt,X=s("".charAt),Q=s([].join),$=s([].push),Z=s("".replace),J=s([].shift),tt=s([].splice),et=s("".split),rt=s("".slice),nt=s(/./.exec),it=/\+/g,ot=/^[0-9a-f]+$/i,at=function(t,e){var r=rt(t,e,e+2);return nt(ot,r)?K(r,16):NaN},ut=function(t){for(var e=0,r=128;r>0&&t&r;r>>=1)e++;return e},st=function(t){var e=null;switch(t.length){case 1:e=t[0];break;case 2:e=(31&t[0])<<6|63&t[1];break;case 3:e=(15&t[0])<<12|(63&t[1])<<6|63&t[2];break;case 4:e=(7&t[0])<<18|(63&t[1])<<12|(63&t[2])<<6|63&t[3]}return e>1114111?null:e},ct=function(t){for(var e=(t=Z(t,it," ")).length,r="",n=0;n<e;){var i=X(t,n);if("%"===i){if("%"===X(t,n+1)||n+3>e){r+="%",n++;continue}var o=at(t,n+1);if(o!=o){r+=i,n++;continue}n+=2;var a=ut(o);if(0===a)i=z(o);else{if(1===a||a>4){r+="�",n++;continue}for(var u=[o],s=1;s<a&&!(3+ ++n>e||"%"!==X(t,n));){var c=at(t,n+1);if(c!=c){n+=3;break}if(c>191||c<128)break;$(u,c),n+=2,s++}if(u.length!==a){r+="�";continue}var f=st(u);null===f?r+="�":i=q(f)}}r+=i,n++}return r},ft=/[!'()~]|%20/g,lt={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},ht=function(t){return lt[t]},dt=function(t){return Z(W(t),ft,ht)},pt=v((function(t,e){U(this,{type:P,target:k(t).entries,index:0,kind:e})}),M,(function(){var t=F(this),e=t.target,r=t.index++;if(!e||r>=e.length)return t.target=null,x(void 0,!0);var n=e[r];switch(t.kind){case"keys":return x(n.key,!1);case"values":return x(n.value,!1)}return x([n.key,n.value],!1)}),!0),vt=function(t){this.entries=[],this.url=null,void 0!==t&&(b(t)?this.parseObject(t):this.parseQuery("string"==typeof t?"?"===X(t,0)?rt(t,1):t:w(t)))};vt.prototype={type:M,bindURL:function(t){this.url=t,this.update()},parseObject:function(t){var e,r,n,i,o,a,s,c=this.entries,f=C(t);if(f)for(r=(e=I(t,f)).next;!(n=u(r,e)).done;){if(o=(i=I(_(n.value))).next,(a=u(o,i)).done||(s=u(o,i)).done||!u(o,i).done)throw new Y("Expected sequence with length 2");$(c,{key:w(a.value),value:w(s.value)})}else for(var l in t)A(t,l)&&$(c,{key:l,value:w(t[l])})},parseQuery:function(t){if(t)for(var e,r,n=this.entries,i=et(t,"&"),o=0;o<i.length;)(e=i[o++]).length&&(r=et(e,"="),$(n,{key:ct(J(r)),value:ct(Q(r,"="))}))},serialize:function(){for(var t,e=this.entries,r=[],n=0;n<e.length;)t=e[n++],$(r,dt(t.key)+"="+dt(t.value));return Q(r,"&")},update:function(){this.entries.length=0,this.parseQuery(this.url.query)},updateURL:function(){this.url&&this.url.update()}};var gt=function(){y(this,yt);var t=U(this,new vt(arguments.length>0?arguments[0]:void 0));c||(this.size=t.entries.length)},yt=gt.prototype;if(d(yt,{append:function(t,e){var r=k(this);O(arguments.length,2),$(r.entries,{key:w(t),value:w(e)}),c||this.length++,r.updateURL()},delete:function(t){for(var e=k(this),r=O(arguments.length,1),n=e.entries,i=w(t),o=r<2?void 0:arguments[1],a=void 0===o?o:w(o),u=0;u<n.length;){var s=n[u];if(s.key!==i||void 0!==a&&s.value!==a)u++;else if(tt(n,u,1),void 0!==a)break}c||(this.size=n.length),e.updateURL()},get:function(t){var e=k(this).entries;O(arguments.length,1);for(var r=w(t),n=0;n<e.length;n++)if(e[n].key===r)return e[n].value;return null},getAll:function(t){var e=k(this).entries;O(arguments.length,1);for(var r=w(t),n=[],i=0;i<e.length;i++)e[i].key===r&&$(n,e[i].value);return n},has:function(t){for(var e=k(this).entries,r=O(arguments.length,1),n=w(t),i=r<2?void 0:arguments[1],o=void 0===i?i:w(i),a=0;a<e.length;){var u=e[a++];if(u.key===n&&(void 0===o||u.value===o))return!0}return!1},set:function(t,e){var r=k(this);O(arguments.length,1);for(var n,i=r.entries,o=!1,a=w(t),u=w(e),s=0;s<i.length;s++)(n=i[s]).key===a&&(o?tt(i,s--,1):(o=!0,n.value=u));o||$(i,{key:a,value:u}),c||(this.size=i.length),r.updateURL()},sort:function(){var t=k(this);L(t.entries,(function(t,e){return t.key>e.key?1:-1})),t.updateURL()},forEach:function(t){for(var e,r=k(this).entries,n=m(t,arguments.length>1?arguments[1]:void 0),i=0;i<r.length;)n((e=r[i++]).value,e.key,this)},keys:function(){return new pt(this,"keys")},values:function(){return new pt(this,"values")},entries:function(){return new pt(this,"entries")}},{enumerable:!0}),l(yt,N,yt.entries,{name:"entries"}),l(yt,"toString",(function(){return k(this).serialize()}),{enumerable:!0}),c&&h(yt,"size",{get:function(){return k(this).entries.length},configurable:!0,enumerable:!0}),p(gt,M),n({global:!0,constructor:!0,forced:!f},{URLSearchParams:gt}),!f&&E(H)){var Et=s(V.has),At=s(V.set),mt=function(t){if(b(t)){var e,r=t.body;if(T(r)===M)return e=t.headers?new H(t.headers):new H,Et(e,"content-type")||At(e,"content-type","application/x-www-form-urlencoded;charset=UTF-8"),R(t,{body:S(0,w(r)),headers:S(0,e)})}return t};if(E(B)&&n({global:!0,enumerable:!0,dontCallGetSet:!0,forced:!0},{fetch:function(t){return B(t,arguments.length>1?mt(arguments[1]):{})}}),E(G)){var Tt=function(t){return y(this,j),new G(t,arguments.length>1?mt(arguments[1]):{})};j.constructor=Tt,Tt.prototype=j,n({global:!0,constructor:!0,dontCallGetSet:!0,forced:!0},{Request:Tt})}}t.exports={URLSearchParams:gt,getState:k}},4603:function(t,e,r){"use strict";var n=r(6840),i=r(9504),o=r(655),a=r(2812),u=URLSearchParams,s=u.prototype,c=i(s.append),f=i(s.delete),l=i(s.forEach),h=i([].push),d=new u("a=1&a=2&b=3");d.delete("a",1),d.delete("b",void 0),d+""!="a=2"&&n(s,"delete",(function(t){var e=arguments.length,r=e<2?void 0:arguments[1];if(e&&void 0===r)return f(this,t);var n=[];l(this,(function(t,e){h(n,{key:e,value:t})})),a(e,1);for(var i,u=o(t),s=o(r),d=0,p=0,v=!1,g=n.length;d<g;)i=n[d++],v||i.key===u?(v=!0,f(this,i.key)):p++;for(;p<g;)(i=n[p++]).key===u&&i.value===s||c(this,i.key,i.value)}),{enumerable:!0,unsafe:!0})},7566:function(t,e,r){"use strict";var n=r(6840),i=r(9504),o=r(655),a=r(2812),u=URLSearchParams,s=u.prototype,c=i(s.getAll),f=i(s.has),l=new u("a=1");!l.has("a",2)&&l.has("a",void 0)||n(s,"has",(function(t){var e=arguments.length,r=e<2?void 0:arguments[1];if(e&&void 0===r)return f(this,t);var n=c(this,t);a(e,1);for(var i=o(r),u=0;u<n.length;)if(n[u++]===i)return!0;return!1}),{enumerable:!0,unsafe:!0})},8408:function(t,e,r){"use strict";r(8406)},8721:function(t,e,r){"use strict";var n=r(3724),i=r(9504),o=r(2106),a=URLSearchParams.prototype,u=i(a.forEach);n&&!("size"in a)&&o(a,"size",{get:function(){var t=0;return u(this,(function(){t++})),t},configurable:!0,enumerable:!0})},5806:function(t,e,r){"use strict";r(7764);var n,i=r(6518),o=r(3724),a=r(7416),u=r(4576),s=r(6080),c=r(9504),f=r(6840),l=r(2106),h=r(679),d=r(9297),p=r(4213),v=r(7916),g=r(7680),y=r(8183).codeAt,E=r(6098),A=r(655),m=r(687),T=r(2812),_=r(8406),b=r(1181),w=b.set,R=b.getterFor("URL"),S=_.URLSearchParams,I=_.getState,C=u.URL,x=u.TypeError,O=u.parseInt,D=Math.floor,L=Math.pow,N=c("".charAt),M=c(/./.exec),P=c([].join),U=c(1..toString),k=c([].pop),F=c([].push),B=c("".replace),G=c([].shift),H=c("".split),j=c("".slice),V=c("".toLowerCase),Y=c([].unshift),W="Invalid scheme",z="Invalid host",q="Invalid port",K=/[a-z]/i,X=/[\d+-.a-z]/i,Q=/\d/,$=/^0x/i,Z=/^[0-7]+$/,J=/^\d+$/,tt=/^[\da-f]+$/i,et=/[\0\t\n\r #%/:<>?@[\\\]^|]/,rt=/[\0\t\n\r #/:<>?@[\\\]^|]/,nt=/^[\u0000-\u0020]+/,it=/(^|[^\u0000-\u0020])[\u0000-\u0020]+$/,ot=/[\t\n\r]/g,at=function(t){var e,r,n,i;if("number"==typeof t){for(e=[],r=0;r<4;r++)Y(e,t%256),t=D(t/256);return P(e,".")}if("object"==typeof t){for(e="",n=function(t){for(var e=null,r=1,n=null,i=0,o=0;o<8;o++)0!==t[o]?(i>r&&(e=n,r=i),n=null,i=0):(null===n&&(n=o),++i);return i>r?n:e}(t),r=0;r<8;r++)i&&0===t[r]||(i&&(i=!1),n===r?(e+=r?":":"::",i=!0):(e+=U(t[r],16),r<7&&(e+=":")));return"["+e+"]"}return t},ut={},st=p({},ut,{" ":1,'"':1,"<":1,">":1,"`":1}),ct=p({},st,{"#":1,"?":1,"{":1,"}":1}),ft=p({},ct,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),lt=function(t,e){var r=y(t,0);return r>32&&r<127&&!d(e,t)?t:encodeURIComponent(t)},ht={ftp:21,file:null,http:80,https:443,ws:80,wss:443},dt=function(t,e){var r;return 2===t.length&&M(K,N(t,0))&&(":"===(r=N(t,1))||!e&&"|"===r)},pt=function(t){var e;return t.length>1&&dt(j(t,0,2))&&(2===t.length||"/"===(e=N(t,2))||"\\"===e||"?"===e||"#"===e)},vt=function(t){return"."===t||"%2e"===V(t)},gt={},yt={},Et={},At={},mt={},Tt={},_t={},bt={},wt={},Rt={},St={},It={},Ct={},xt={},Ot={},Dt={},Lt={},Nt={},Mt={},Pt={},Ut={},kt=function(t,e,r){var n,i,o,a=A(t);if(e){if(i=this.parse(a))throw new x(i);this.searchParams=null}else{if(void 0!==r&&(n=new kt(r,!0)),i=this.parse(a,null,n))throw new x(i);(o=I(new S)).bindURL(this),this.searchParams=o}};kt.prototype={type:"URL",parse:function(t,e,r){var i,o,a,u,s,c=this,f=e||gt,l=0,h="",p=!1,y=!1,E=!1;for(t=A(t),e||(c.scheme="",c.username="",c.password="",c.host=null,c.port=null,c.path=[],c.query=null,c.fragment=null,c.cannotBeABaseURL=!1,t=B(t,nt,""),t=B(t,it,"$1")),t=B(t,ot,""),i=v(t);l<=i.length;){switch(o=i[l],f){case gt:if(!o||!M(K,o)){if(e)return W;f=Et;continue}h+=V(o),f=yt;break;case yt:if(o&&(M(X,o)||"+"===o||"-"===o||"."===o))h+=V(o);else{if(":"!==o){if(e)return W;h="",f=Et,l=0;continue}if(e&&(c.isSpecial()!==d(ht,h)||"file"===h&&(c.includesCredentials()||null!==c.port)||"file"===c.scheme&&!c.host))return;if(c.scheme=h,e)return void(c.isSpecial()&&ht[c.scheme]===c.port&&(c.port=null));h="","file"===c.scheme?f=xt:c.isSpecial()&&r&&r.scheme===c.scheme?f=At:c.isSpecial()?f=bt:"/"===i[l+1]?(f=mt,l++):(c.cannotBeABaseURL=!0,F(c.path,""),f=Mt)}break;case Et:if(!r||r.cannotBeABaseURL&&"#"!==o)return W;if(r.cannotBeABaseURL&&"#"===o){c.scheme=r.scheme,c.path=g(r.path),c.query=r.query,c.fragment="",c.cannotBeABaseURL=!0,f=Ut;break}f="file"===r.scheme?xt:Tt;continue;case At:if("/"!==o||"/"!==i[l+1]){f=Tt;continue}f=wt,l++;break;case mt:if("/"===o){f=Rt;break}f=Nt;continue;case Tt:if(c.scheme=r.scheme,o===n)c.username=r.username,c.password=r.password,c.host=r.host,c.port=r.port,c.path=g(r.path),c.query=r.query;else if("/"===o||"\\"===o&&c.isSpecial())f=_t;else if("?"===o)c.username=r.username,c.password=r.password,c.host=r.host,c.port=r.port,c.path=g(r.path),c.query="",f=Pt;else{if("#"!==o){c.username=r.username,c.password=r.password,c.host=r.host,c.port=r.port,c.path=g(r.path),c.path.length--,f=Nt;continue}c.username=r.username,c.password=r.password,c.host=r.host,c.port=r.port,c.path=g(r.path),c.query=r.query,c.fragment="",f=Ut}break;case _t:if(!c.isSpecial()||"/"!==o&&"\\"!==o){if("/"!==o){c.username=r.username,c.password=r.password,c.host=r.host,c.port=r.port,f=Nt;continue}f=Rt}else f=wt;break;case bt:if(f=wt,"/"!==o||"/"!==N(h,l+1))continue;l++;break;case wt:if("/"!==o&&"\\"!==o){f=Rt;continue}break;case Rt:if("@"===o){p&&(h="%40"+h),p=!0,a=v(h);for(var m=0;m<a.length;m++){var T=a[m];if(":"!==T||E){var _=lt(T,ft);E?c.password+=_:c.username+=_}else E=!0}h=""}else if(o===n||"/"===o||"?"===o||"#"===o||"\\"===o&&c.isSpecial()){if(p&&""===h)return"Invalid authority";l-=v(h).length+1,h="",f=St}else h+=o;break;case St:case It:if(e&&"file"===c.scheme){f=Dt;continue}if(":"!==o||y){if(o===n||"/"===o||"?"===o||"#"===o||"\\"===o&&c.isSpecial()){if(c.isSpecial()&&""===h)return z;if(e&&""===h&&(c.includesCredentials()||null!==c.port))return;if(u=c.parseHost(h))return u;if(h="",f=Lt,e)return;continue}"["===o?y=!0:"]"===o&&(y=!1),h+=o}else{if(""===h)return z;if(u=c.parseHost(h))return u;if(h="",f=Ct,e===It)return}break;case Ct:if(!M(Q,o)){if(o===n||"/"===o||"?"===o||"#"===o||"\\"===o&&c.isSpecial()||e){if(""!==h){var b=O(h,10);if(b>65535)return q;c.port=c.isSpecial()&&b===ht[c.scheme]?null:b,h=""}if(e)return;f=Lt;continue}return q}h+=o;break;case xt:if(c.scheme="file","/"===o||"\\"===o)f=Ot;else{if(!r||"file"!==r.scheme){f=Nt;continue}switch(o){case n:c.host=r.host,c.path=g(r.path),c.query=r.query;break;case"?":c.host=r.host,c.path=g(r.path),c.query="",f=Pt;break;case"#":c.host=r.host,c.path=g(r.path),c.query=r.query,c.fragment="",f=Ut;break;default:pt(P(g(i,l),""))||(c.host=r.host,c.path=g(r.path),c.shortenPath()),f=Nt;continue}}break;case Ot:if("/"===o||"\\"===o){f=Dt;break}r&&"file"===r.scheme&&!pt(P(g(i,l),""))&&(dt(r.path[0],!0)?F(c.path,r.path[0]):c.host=r.host),f=Nt;continue;case Dt:if(o===n||"/"===o||"\\"===o||"?"===o||"#"===o){if(!e&&dt(h))f=Nt;else if(""===h){if(c.host="",e)return;f=Lt}else{if(u=c.parseHost(h))return u;if("localhost"===c.host&&(c.host=""),e)return;h="",f=Lt}continue}h+=o;break;case Lt:if(c.isSpecial()){if(f=Nt,"/"!==o&&"\\"!==o)continue}else if(e||"?"!==o)if(e||"#"!==o){if(o!==n&&(f=Nt,"/"!==o))continue}else c.fragment="",f=Ut;else c.query="",f=Pt;break;case Nt:if(o===n||"/"===o||"\\"===o&&c.isSpecial()||!e&&("?"===o||"#"===o)){if(".."===(s=V(s=h))||"%2e."===s||".%2e"===s||"%2e%2e"===s?(c.shortenPath(),"/"===o||"\\"===o&&c.isSpecial()||F(c.path,"")):vt(h)?"/"===o||"\\"===o&&c.isSpecial()||F(c.path,""):("file"===c.scheme&&!c.path.length&&dt(h)&&(c.host&&(c.host=""),h=N(h,0)+":"),F(c.path,h)),h="","file"===c.scheme&&(o===n||"?"===o||"#"===o))for(;c.path.length>1&&""===c.path[0];)G(c.path);"?"===o?(c.query="",f=Pt):"#"===o&&(c.fragment="",f=Ut)}else h+=lt(o,ct);break;case Mt:"?"===o?(c.query="",f=Pt):"#"===o?(c.fragment="",f=Ut):o!==n&&(c.path[0]+=lt(o,ut));break;case Pt:e||"#"!==o?o!==n&&("'"===o&&c.isSpecial()?c.query+="%27":c.query+="#"===o?"%23":lt(o,ut)):(c.fragment="",f=Ut);break;case Ut:o!==n&&(c.fragment+=lt(o,st))}l++}},parseHost:function(t){var e,r,n;if("["===N(t,0)){if("]"!==N(t,t.length-1))return z;if(e=function(t){var e,r,n,i,o,a,u,s=[0,0,0,0,0,0,0,0],c=0,f=null,l=0,h=function(){return N(t,l)};if(":"===h()){if(":"!==N(t,1))return;l+=2,f=++c}for(;h();){if(8===c)return;if(":"!==h()){for(e=r=0;r<4&&M(tt,h());)e=16*e+O(h(),16),l++,r++;if("."===h()){if(0===r)return;if(l-=r,c>6)return;for(n=0;h();){if(i=null,n>0){if(!("."===h()&&n<4))return;l++}if(!M(Q,h()))return;for(;M(Q,h());){if(o=O(h(),10),null===i)i=o;else{if(0===i)return;i=10*i+o}if(i>255)return;l++}s[c]=256*s[c]+i,2!=++n&&4!==n||c++}if(4!==n)return;break}if(":"===h()){if(l++,!h())return}else if(h())return;s[c++]=e}else{if(null!==f)return;l++,f=++c}}if(null!==f)for(a=c-f,c=7;0!==c&&a>0;)u=s[c],s[c--]=s[f+a-1],s[f+--a]=u;else if(8!==c)return;return s}(j(t,1,-1)),!e)return z;this.host=e}else if(this.isSpecial()){if(t=E(t),M(et,t))return z;if(e=function(t){var e,r,n,i,o,a,u,s=H(t,".");if(s.length&&""===s[s.length-1]&&s.length--,(e=s.length)>4)return t;for(r=[],n=0;n<e;n++){if(""===(i=s[n]))return t;if(o=10,i.length>1&&"0"===N(i,0)&&(o=M($,i)?16:8,i=j(i,8===o?1:2)),""===i)a=0;else{if(!M(10===o?J:8===o?Z:tt,i))return t;a=O(i,o)}F(r,a)}for(n=0;n<e;n++)if(a=r[n],n===e-1){if(a>=L(256,5-e))return null}else if(a>255)return null;for(u=k(r),n=0;n<r.length;n++)u+=r[n]*L(256,3-n);return u}(t),null===e)return z;this.host=e}else{if(M(rt,t))return z;for(e="",r=v(t),n=0;n<r.length;n++)e+=lt(r[n],ut);this.host=e}},cannotHaveUsernamePasswordPort:function(){return!this.host||this.cannotBeABaseURL||"file"===this.scheme},includesCredentials:function(){return""!==this.username||""!==this.password},isSpecial:function(){return d(ht,this.scheme)},shortenPath:function(){var t=this.path,e=t.length;!e||"file"===this.scheme&&1===e&&dt(t[0],!0)||t.length--},serialize:function(){var t=this,e=t.scheme,r=t.username,n=t.password,i=t.host,o=t.port,a=t.path,u=t.query,s=t.fragment,c=e+":";return null!==i?(c+="//",t.includesCredentials()&&(c+=r+(n?":"+n:"")+"@"),c+=at(i),null!==o&&(c+=":"+o)):"file"===e&&(c+="//"),c+=t.cannotBeABaseURL?a[0]:a.length?"/"+P(a,"/"):"",null!==u&&(c+="?"+u),null!==s&&(c+="#"+s),c},setHref:function(t){var e=this.parse(t);if(e)throw new x(e);this.searchParams.update()},getOrigin:function(){var t=this.scheme,e=this.port;if("blob"===t)try{return new Ft(t.path[0]).origin}catch(t){return"null"}return"file"!==t&&this.isSpecial()?t+"://"+at(this.host)+(null!==e?":"+e:""):"null"},getProtocol:function(){return this.scheme+":"},setProtocol:function(t){this.parse(A(t)+":",gt)},getUsername:function(){return this.username},setUsername:function(t){var e=v(A(t));if(!this.cannotHaveUsernamePasswordPort()){this.username="";for(var r=0;r<e.length;r++)this.username+=lt(e[r],ft)}},getPassword:function(){return this.password},setPassword:function(t){var e=v(A(t));if(!this.cannotHaveUsernamePasswordPort()){this.password="";for(var r=0;r<e.length;r++)this.password+=lt(e[r],ft)}},getHost:function(){var t=this.host,e=this.port;return null===t?"":null===e?at(t):at(t)+":"+e},setHost:function(t){this.cannotBeABaseURL||this.parse(t,St)},getHostname:function(){var t=this.host;return null===t?"":at(t)},setHostname:function(t){this.cannotBeABaseURL||this.parse(t,It)},getPort:function(){var t=this.port;return null===t?"":A(t)},setPort:function(t){this.cannotHaveUsernamePasswordPort()||(""===(t=A(t))?this.port=null:this.parse(t,Ct))},getPathname:function(){var t=this.path;return this.cannotBeABaseURL?t[0]:t.length?"/"+P(t,"/"):""},setPathname:function(t){this.cannotBeABaseURL||(this.path=[],this.parse(t,Lt))},getSearch:function(){var t=this.query;return t?"?"+t:""},setSearch:function(t){""===(t=A(t))?this.query=null:("?"===N(t,0)&&(t=j(t,1)),this.query="",this.parse(t,Pt)),this.searchParams.update()},getSearchParams:function(){return this.searchParams.facade},getHash:function(){var t=this.fragment;return t?"#"+t:""},setHash:function(t){""!==(t=A(t))?("#"===N(t,0)&&(t=j(t,1)),this.fragment="",this.parse(t,Ut)):this.fragment=null},update:function(){this.query=this.searchParams.serialize()||null}};var Ft=function(t){var e=h(this,Bt),r=T(arguments.length,1)>1?arguments[1]:void 0,n=w(e,new kt(t,!1,r));o||(e.href=n.serialize(),e.origin=n.getOrigin(),e.protocol=n.getProtocol(),e.username=n.getUsername(),e.password=n.getPassword(),e.host=n.getHost(),e.hostname=n.getHostname(),e.port=n.getPort(),e.pathname=n.getPathname(),e.search=n.getSearch(),e.searchParams=n.getSearchParams(),e.hash=n.getHash())},Bt=Ft.prototype,Gt=function(t,e){return{get:function(){return R(this)[t]()},set:e&&function(t){return R(this)[e](t)},configurable:!0,enumerable:!0}};if(o&&(l(Bt,"href",Gt("serialize","setHref")),l(Bt,"origin",Gt("getOrigin")),l(Bt,"protocol",Gt("getProtocol","setProtocol")),l(Bt,"username",Gt("getUsername","setUsername")),l(Bt,"password",Gt("getPassword","setPassword")),l(Bt,"host",Gt("getHost","setHost")),l(Bt,"hostname",Gt("getHostname","setHostname")),l(Bt,"port",Gt("getPort","setPort")),l(Bt,"pathname",Gt("getPathname","setPathname")),l(Bt,"search",Gt("getSearch","setSearch")),l(Bt,"searchParams",Gt("getSearchParams")),l(Bt,"hash",Gt("getHash","setHash"))),f(Bt,"toJSON",(function(){return R(this).serialize()}),{enumerable:!0}),f(Bt,"toString",(function(){return R(this).serialize()}),{enumerable:!0}),C){var Ht=C.createObjectURL,jt=C.revokeObjectURL;Ht&&f(Ft,"createObjectURL",s(Ht,C)),jt&&f(Ft,"revokeObjectURL",s(jt,C))}m(Ft,"URL"),i({global:!0,constructor:!0,forced:!a,sham:!o},{URL:Ft})},3296:function(t,e,r){"use strict";r(5806)},7208:function(t,e,r){"use strict";var n=r(6518),i=r(9565);n({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return i(URL.prototype.toString,this)}})},5429:function(t,e,r){function n(e){return t.exports=n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,n(e)}r(2675),r(9463),r(2259),r(3792),r(6099),r(7764),r(2953),t.exports=n,t.exports.__esModule=!0,t.exports.default=t.exports},1238:function(t,e,r){"use strict";function n(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}r.d(e,{A:function(){return n}})},2166:function(t,e,r){"use strict";function n(t){if(Array.isArray(t))return t}r.d(e,{A:function(){return n}})},2130:function(t,e,r){"use strict";r.d(e,{A:function(){return i}});var n=r(1238);function i(t){if(Array.isArray(t))return(0,n.A)(t)}},7056:function(t,e,r){"use strict";function n(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}r.d(e,{A:function(){return n}}),r(6280)},7718:function(t,e,r){"use strict";function n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}r.d(e,{A:function(){return n}}),r(6280)},5952:function(t,e,r){"use strict";r.d(e,{A:function(){return o}});var n=r(7791);function i(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,(0,n.A)(i.key),i)}}function o(t,e,r){return e&&i(t.prototype,e),r&&i(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}},544:function(t,e,r){"use strict";r.d(e,{A:function(){return i}});var n=r(7791);function i(t,e,r){return(e=(0,n.A)(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}},4597:function(t,e,r){"use strict";function n(t){return n=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},n(t)}r.d(e,{A:function(){return n}}),r(875)},8766:function(t,e,r){"use strict";r.d(e,{A:function(){return i}}),r(6280);var n=r(3497);function i(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&(0,n.A)(t,e)}},3056:function(t,e,r){"use strict";function n(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}r.d(e,{A:function(){return n}}),r(2675),r(9463),r(2259),r(3418),r(3792),r(6099),r(7764),r(2953)},5729:function(t,e,r){"use strict";function n(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],s=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);s=!0);}catch(t){c=!0,i=t}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw i}}return u}}r.d(e,{A:function(){return n}}),r(2675),r(9463),r(2259),r(3792),r(4114),r(6099),r(7764),r(2953)},4031:function(t,e,r){"use strict";function n(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}r.d(e,{A:function(){return n}}),r(6280)},2658:function(t,e,r){"use strict";function n(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}r.d(e,{A:function(){return n}}),r(6280)},8591:function(t,e,r){"use strict";r.d(e,{A:function(){return o}}),r(6280);var n=r(4299),i=r(7056);function o(t,e){if(e&&("object"==(0,n.A)(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return(0,i.A)(t)}},3497:function(t,e,r){"use strict";function n(t,e){return n=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},n(t,e)}r.d(e,{A:function(){return n}})},8508:function(t,e,r){"use strict";r.d(e,{A:function(){return u}});var n=r(2166),i=r(5729),o=r(132),a=r(4031);function u(t,e){return(0,n.A)(t)||(0,i.A)(t,e)||(0,o.A)(t,e)||(0,a.A)()}},2958:function(t,e,r){"use strict";r.d(e,{A:function(){return u}});var n=r(2166),i=r(3056),o=r(132),a=r(4031);function u(t){return(0,n.A)(t)||(0,i.A)(t)||(0,o.A)(t)||(0,a.A)()}},1975:function(t,e,r){"use strict";r.d(e,{A:function(){return u}});var n=r(2130),i=r(3056),o=r(132),a=r(2658);function u(t){return(0,n.A)(t)||(0,i.A)(t)||(0,o.A)(t)||(0,a.A)()}},6714:function(t,e,r){"use strict";r.d(e,{A:function(){return i}}),r(5700),r(6280),r(9572),r(2892);var n=r(4299);function i(t,e){if("object"!=(0,n.A)(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var i=r.call(t,e||"default");if("object"!=(0,n.A)(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}},7791:function(t,e,r){"use strict";r.d(e,{A:function(){return o}});var n=r(4299),i=r(6714);function o(t){var e=(0,i.A)(t,"string");return"symbol"==(0,n.A)(e)?e:e+""}},4299:function(t,e,r){"use strict";function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}r.d(e,{A:function(){return n}}),r(2675),r(9463),r(2259),r(3792),r(6099),r(7764),r(2953)},132:function(t,e,r){"use strict";r.d(e,{A:function(){return i}}),r(3418),r(4782),r(2010),r(6099),r(7495),r(906),r(8781),r(7764);var n=r(1238);function i(t,e){if(t){if("string"==typeof t)return(0,n.A)(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?(0,n.A)(t,e):void 0}}},649:function(t,e,r){"use strict";r(8706),r(6099),r(3500);var n=r(8850),i=r(3621),o=r(138),a=1,u=2,s=3,c=4,f=5;function l(t){t=t||{};var e,r,o,l,h=this.context,d=(0,n.A)(h).getInstance(),p=t.settings,v=[];function g(t){return t&&t.bind?t.bind(window.console):window.console.log.bind(window.console)}function y(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];_.apply(void 0,[a,this].concat(e))}function E(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];_.apply(void 0,[u,this].concat(e))}function A(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];_.apply(void 0,[s,this].concat(e))}function m(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];_.apply(void 0,[c,this].concat(e))}function T(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];_.apply(void 0,[f,this].concat(e))}function _(t,e){var n="",a=null;r&&(a=(new Date).getTime(),n+="["+(a-l)+"]"),o&&e&&e.getClassName&&(n+="["+e.getClassName()+"]",e.getType&&(n+="["+e.getType()+"]")),n.length>0&&(n+=" ");for(var u=arguments.length,s=new Array(u>2?u-2:0),c=2;c<u;c++)s[c-2]=arguments[c];Array.apply(null,s).forEach((function(t){n+=t+" "})),v[t]&&p&&p.get().debug.logLevel>=t&&v[t](n),p&&p.get().debug.dispatchEvent&&d.trigger(i.A.LOG,{message:n,level:t})}return e={getLogger:function(t){return{fatal:y.bind(t),error:E.bind(t),warn:A.bind(t),info:m.bind(t),debug:T.bind(t)}},setLogTimestampVisible:function(t){r=t},setCalleeNameVisible:function(t){o=t}},r=!0,o=!0,l=(new Date).getTime(),"undefined"!=typeof window&&window.console&&(v[a]=g(window.console.error),v[u]=g(window.console.error),v[s]=g(window.console.warn),v[c]=g(window.console.info),v[f]=g(window.console.debug)),e}l.__dashjs_factory_name="Debug";var h=o.A.getSingletonFactory(l);h.LOG_LEVEL_NONE=0,h.LOG_LEVEL_FATAL=a,h.LOG_LEVEL_ERROR=u,h.LOG_LEVEL_WARNING=s,h.LOG_LEVEL_INFO=c,h.LOG_LEVEL_DEBUG=f,o.A.updateSingletonFactory(l.__dashjs_factory_name,h),e.A=h},8850:function(t,e,r){"use strict";r(6280),r(2008),r(4114),r(4554),r(6099),r(3500);var n=r(138),i=r(1191);function o(){var t={};function e(e,r,i){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(!e)throw new Error("event type cannot be null or undefined");if(!r||"function"!=typeof r)throw new Error("listener must be a function: "+r);var u=o.priority||0;if(!(n(e,r,i)>=0)){t[e]=t[e]||[];var s={callback:r,scope:i,priority:u,executeOnlyOnce:a};i&&i.getStreamId&&(s.streamId=i.getStreamId()),i&&i.getType&&(s.mediaType=i.getType()),o&&o.mode&&(s.mode=o.mode),t[e].some((function(r,n){if(r&&u>r.priority)return t[e].splice(n,0,s),!0}))||t[e].push(s)}}function r(e,r,i){if(e&&r&&t[e]){var o=n(e,r,i);o<0||(t[e][o]=null)}}function n(e,r,n){var i=-1;return t[e]?(t[e].some((function(t,e){if(t&&t.callback===r&&(!n||n===t.scope))return i=e,!0})),i):i}var o={on:function(t,r,n){e(t,r,n,arguments.length>3&&void 0!==arguments[3]?arguments[3]:{})},once:function(t,r,n){e(t,r,n,arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},!0)},off:r,trigger:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e&&t[e]){if((n=n||{}).hasOwnProperty("type"))throw new Error("'type' is a reserved word for event dispatching");n.type=e,o.streamId&&(n.streamId=o.streamId),o.mediaType&&(n.mediaType=o.mediaType);var a=[];t[e].filter((function(t){return!(!t||o.streamId&&t.streamId&&t.streamId!==o.streamId||o.mediaType&&t.mediaType&&t.mediaType!==o.mediaType||o.mode&&t.mode&&t.mode!==o.mode||!t.mode&&o.mode&&o.mode===i.A.EVENT_MODE_ON_RECEIVE)})).forEach((function(t){t&&t.callback.call(t.scope,n),t.executeOnlyOnce&&a.push(t)})),a.forEach((function(t){r(e,t.callback,t.scope)}))}},reset:function(){t={}}};return o}o.__dashjs_factory_name="EventBus";var a=n.A.getSingletonFactory(o);a.EVENT_PRIORITY_LOW=0,a.EVENT_PRIORITY_HIGH=5e3,n.A.updateSingletonFactory(o.__dashjs_factory_name,a),e.A=a},138:function(t,e,r){"use strict";r(2008),r(4114),r(2010),r(6099);var n=function(){var t,e=[],r={},n={};function i(t,r){for(var n in e){var i=e[n];if(i.context===t&&i.name===r)return i.instance}return null}function o(t,e){return e[t]}function a(t,e,r){t in r&&(r[t]=e)}function u(e,r,n){var i,o=e.__dashjs_factory_name,a=r[o];if(a){var u=a.instance;if(!a.override)return u.apply({context:r,factory:t},n);for(var s in i=e.apply({context:r},n),u=u.apply({context:r,factory:t,parent:i},n))i.hasOwnProperty(s)&&(i[s]=u[s])}else i=e.apply({context:r},n);return i.getClassName=function(){return o},i}return t={extend:function(t,e,r,n){!n[t]&&e&&(n[t]={instance:e,override:r})},getSingletonInstance:i,setSingletonInstance:function(t,r,n){for(var i in e){var o=e[i];if(o.context===t&&o.name===r)return void(e[i].instance=n)}e.push({name:r,context:t,instance:n})},deleteSingletonInstances:function(t){e=e.filter((function(e){return e.context!==t}))},getSingletonFactory:function(t){var n=o(t.__dashjs_factory_name,r);return n||(n=function(r){var n;return void 0===r&&(r={}),{getInstance:function(){return n||(n=i(r,t.__dashjs_factory_name)),n||(n=u(t,r,arguments),e.push({name:t.__dashjs_factory_name,context:r,instance:n})),n}}},r[t.__dashjs_factory_name]=n),n},getSingletonFactoryByName:function(t){return o(t,r)},updateSingletonFactory:function(t,e){a(t,e,r)},getClassFactory:function(t){var e=o(t.__dashjs_factory_name,n);return e||(e=function(e){return void 0===e&&(e={}),{create:function(){return u(t,e,arguments)}}},n[t.__dashjs_factory_name]=e),e},getClassFactoryByName:function(t){return o(t,n)},updateClassFactory:function(t,e){a(t,e,n)}},t}();e.A=n},8261:function(t,e,r){"use strict";var n=r(4299),i=r(544),o=r(1975),a=(r(8706),r(2062),r(4782),r(4864),r(7465),r(7495),r(7745),r(8781),r(138)),u=r(7263),s=r(649),c=r(5212),f=r(7568),l=r(8850),h=r(3621);function d(){var t=this.context,e=(0,l.A)(t).getInstance(),r={"streaming.delay.liveDelay":h.A.SETTING_UPDATED_LIVE_DELAY,"streaming.delay.liveDelayFragmentCount":h.A.SETTING_UPDATED_LIVE_DELAY_FRAGMENT_COUNT,"streaming.liveCatchup.enabled":h.A.SETTING_UPDATED_CATCHUP_ENABLED,"streaming.liveCatchup.playbackRate.min":h.A.SETTING_UPDATED_PLAYBACK_RATE_MIN,"streaming.liveCatchup.playbackRate.max":h.A.SETTING_UPDATED_PLAYBACK_RATE_MAX,"streaming.abr.rules.throughputRule.active":h.A.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.bolaRule.active":h.A.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.insufficientBufferRule.active":h.A.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.switchHistoryRule.active":h.A.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.droppedFramesRule.active":h.A.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.abandonRequestsRule.active":h.A.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.l2ARule.active":h.A.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.loLPRule.active":h.A.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.maxBitrate.video":h.A.SETTING_UPDATED_MAX_BITRATE,"streaming.abr.maxBitrate.audio":h.A.SETTING_UPDATED_MAX_BITRATE,"streaming.abr.minBitrate.video":h.A.SETTING_UPDATED_MIN_BITRATE,"streaming.abr.minBitrate.audio":h.A.SETTING_UPDATED_MIN_BITRATE},a={debug:{logLevel:s.A.LOG_LEVEL_WARNING,dispatchEvent:!1},streaming:{abandonLoadTimeout:1e4,wallclockTimeUpdateInterval:100,manifestUpdateRetryInterval:100,liveUpdateTimeThresholdInMilliseconds:0,cacheInitSegments:!1,applyServiceDescription:!0,applyProducerReferenceTime:!0,applyContentSteering:!0,enableManifestDurationMismatchFix:!0,parseInbandPrft:!1,enableManifestTimescaleMismatchFix:!1,capabilities:{filterUnsupportedEssentialProperties:!0,supportedEssentialProperties:[{schemeIdUri:c.A.FONT_DOWNLOAD_DVB_SCHEME},{schemeIdUri:c.A.COLOUR_PRIMARIES_SCHEME_ID_URI,value:/1|5|6|7/},{schemeIdUri:c.A.URL_QUERY_INFO_SCHEME},{schemeIdUri:c.A.EXT_URL_QUERY_INFO_SCHEME},{schemeIdUri:c.A.MATRIX_COEFFICIENTS_SCHEME_ID_URI,value:/0|1|5|6/},{schemeIdUri:c.A.TRANSFER_CHARACTERISTICS_SCHEME_ID_URI,value:/1|6|13|14|15/}].concat((0,o.A)(c.A.THUMBNAILS_SCHEME_ID_URIS.map((function(t){return{schemeIdUri:t}})))),useMediaCapabilitiesApi:!0,filterVideoColorimetryEssentialProperties:!1,filterHDRMetadataFormatEssentialProperties:!1},events:{eventControllerRefreshDelay:100,deleteEventMessageDataTimeout:1e4},timeShiftBuffer:{calcFromSegmentTimeline:!1,fallbackToSegmentTimeline:!0},metrics:{maxListDepth:100},delay:{liveDelayFragmentCount:NaN,liveDelay:NaN,useSuggestedPresentationDelay:!0},protection:{keepProtectionMediaKeys:!1,ignoreEmeEncryptedEvent:!1,detectPlayreadyMessageFormat:!0,ignoreKeyStatuses:!1},buffer:{enableSeekDecorrelationFix:!1,fastSwitchEnabled:null,flushBufferAtTrackSwitch:!1,reuseExistingSourceBuffers:!0,bufferPruningInterval:10,bufferToKeep:20,bufferTimeAtTopQuality:30,bufferTimeAtTopQualityLongForm:60,initialBufferLevel:NaN,bufferTimeDefault:18,longFormContentDurationThreshold:600,stallThreshold:.3,lowLatencyStallThreshold:.3,useAppendWindow:!0,setStallState:!0,avoidCurrentTimeRangePruning:!1,useChangeType:!0,mediaSourceDurationInfinity:!0,resetSourceBuffersForTrackSwitch:!1,syntheticStallEvents:{enabled:!1,ignoreReadyState:!1}},gaps:{jumpGaps:!0,jumpLargeGaps:!0,smallGapLimit:1.5,threshold:.3,enableSeekFix:!0,enableStallFix:!1,stallSeek:.1},utcSynchronization:{enabled:!0,useManifestDateHeaderTimeSource:!0,backgroundAttempts:2,timeBetweenSyncAttempts:30,maximumTimeBetweenSyncAttempts:600,minimumTimeBetweenSyncAttempts:2,timeBetweenSyncAttemptsAdjustmentFactor:2,maximumAllowedDrift:100,enableBackgroundSyncAfterSegmentDownloadError:!0,defaultTimingSource:{scheme:"urn:mpeg:dash:utc:http-xsdate:2014",value:"https://time.akamai.com/?iso&ms"}},scheduling:{defaultTimeout:500,lowLatencyTimeout:0,scheduleWhilePaused:!0},text:{defaultEnabled:!0,dispatchForManualRendering:!1,extendSegmentedCues:!0,imsc:{displayForcedOnlyMode:!1,enableRollUp:!0},webvtt:{customRenderingEnabled:!1}},liveCatchup:{maxDrift:NaN,playbackRate:{min:NaN,max:NaN},playbackBufferMin:.5,enabled:null,mode:c.A.LIVE_CATCHUP_MODE_DEFAULT},lastBitrateCachingInfo:{enabled:!0,ttl:36e4},lastMediaSettingsCachingInfo:{enabled:!0,ttl:36e4},saveLastMediaSettingsForCurrentStreamingSession:!0,cacheLoadThresholds:{video:10,audio:5},trackSwitchMode:{audio:c.A.TRACK_SWITCH_MODE_ALWAYS_REPLACE,video:c.A.TRACK_SWITCH_MODE_NEVER_REPLACE},ignoreSelectionPriority:!1,prioritizeRoleMain:!0,assumeDefaultRoleAsMain:!0,selectionModeForInitialTrack:c.A.TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY,fragmentRequestTimeout:2e4,fragmentRequestProgressTimeout:-1,manifestRequestTimeout:1e4,retryIntervals:(0,i.A)((0,i.A)((0,i.A)((0,i.A)((0,i.A)((0,i.A)((0,i.A)((0,i.A)((0,i.A)((0,i.A)({},f.G.MPD_TYPE,500),f.G.XLINK_EXPANSION_TYPE,500),f.G.MEDIA_SEGMENT_TYPE,1e3),f.G.INIT_SEGMENT_TYPE,1e3),f.G.BITSTREAM_SWITCHING_SEGMENT_TYPE,1e3),f.G.INDEX_SEGMENT_TYPE,1e3),f.G.MSS_FRAGMENT_INFO_SEGMENT_TYPE,1e3),f.G.LICENSE,1e3),f.G.OTHER_TYPE,1e3),"lowLatencyReductionFactor",10),retryAttempts:(0,i.A)((0,i.A)((0,i.A)((0,i.A)((0,i.A)((0,i.A)((0,i.A)((0,i.A)((0,i.A)((0,i.A)({},f.G.MPD_TYPE,3),f.G.XLINK_EXPANSION_TYPE,1),f.G.MEDIA_SEGMENT_TYPE,3),f.G.INIT_SEGMENT_TYPE,3),f.G.BITSTREAM_SWITCHING_SEGMENT_TYPE,3),f.G.INDEX_SEGMENT_TYPE,3),f.G.MSS_FRAGMENT_INFO_SEGMENT_TYPE,3),f.G.LICENSE,3),f.G.OTHER_TYPE,3),"lowLatencyMultiplyFactor",5),abr:{limitBitrateByPortal:!1,usePixelRatioInLimitBitrateByPortal:!1,enableSupplementalPropertyAdaptationSetSwitching:!0,rules:{throughputRule:{active:!0},bolaRule:{active:!0},insufficientBufferRule:{active:!0,parameters:{throughputSafetyFactor:.7,segmentIgnoreCount:2}},switchHistoryRule:{active:!0,parameters:{sampleSize:8,switchPercentageThreshold:.075}},droppedFramesRule:{active:!1,parameters:{minimumSampleSize:375,droppedFramesPercentageThreshold:.15}},abandonRequestsRule:{active:!0,parameters:{abandonDurationMultiplier:1.8,minSegmentDownloadTimeThresholdInMs:500,minThroughputSamplesThreshold:6}},l2ARule:{active:!1},loLPRule:{active:!1}},throughput:{averageCalculationMode:c.A.THROUGHPUT_CALCULATION_MODES.EWMA,lowLatencyDownloadTimeCalculationMode:c.A.LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE.MOOF_PARSING,useResourceTimingApi:!0,useNetworkInformationApi:{xhr:!1,fetch:!1},useDeadTimeLatency:!0,bandwidthSafetyFactor:.9,sampleSettings:{live:3,vod:4,enableSampleSizeAdjustment:!0,decreaseScale:.7,increaseScale:1.3,maxMeasurementsToKeep:20,averageLatencySampleAmount:4},ewma:{throughputSlowHalfLifeSeconds:8,throughputFastHalfLifeSeconds:3,latencySlowHalfLifeCount:2,latencyFastHalfLifeCount:1,weightDownloadTimeMultiplicationFactor:.0015}},maxBitrate:{audio:-1,video:-1},minBitrate:{audio:-1,video:-1},initialBitrate:{audio:-1,video:-1},autoSwitchBitrate:{audio:!0,video:!0}},cmcd:{applyParametersFromMpd:!0,enabled:!1,sid:null,cid:null,rtp:null,rtpSafetyFactor:5,mode:c.A.CMCD_MODE_QUERY,enabledKeys:c.A.CMCD_AVAILABLE_KEYS,includeInRequests:["segment","mpd"],version:1},cmsd:{enabled:!1,abr:{applyMb:!1,etpWeightRatio:0}},defaultSchemeIdUri:{viewpoint:"",audioChannelConfiguration:"urn:mpeg:mpegB:cicp:ChannelConfiguration",role:"urn:mpeg:dash:role:2011",accessibility:"urn:mpeg:dash:role:2011"}},errors:{recoverAttempts:{mediaErrorDecode:5}}},d=u.A.clone(a);function p(t,i,o){for(var a in t)t.hasOwnProperty(a)&&(i.hasOwnProperty(a)?"object"!==(0,n.A)(t[a])||t[a]instanceof RegExp||t[a]instanceof Array||null===t[a]?(i[a]=u.A.clone(t[a]),r[o+a]&&e.trigger(r[o+a])):p(t[a],i[a],o.slice()+a+"."):console.error("Settings parameter "+o+a+" is not supported"))}return{get:function(){return d},update:function(t){"object"===(0,n.A)(t)&&p(t,d,"")},reset:function(){d=u.A.clone(a)}}}d.__dashjs_factory_name="Settings";var p=a.A.getSingletonFactory(d);e.A=p},7263:function(t,e,r){"use strict";var n=r(2958),i=r(8508),o=r(4299),a=r(7718),u=r(5952),s=(r(2675),r(9463),r(2259),r(6280),r(8706),r(3418),r(4423),r(3792),r(8598),r(2062),r(4114),r(4782),r(4743),r(6573),r(8100),r(7936),r(2010),r(6099),r(4864),r(7465),r(7495),r(7745),r(906),r(8781),r(1699),r(7764),r(5440),r(5746),r(2762),r(1489),r(8140),r(1630),r(2170),r(5044),r(1920),r(1694),r(9955),r(1903),r(1134),r(3206),r(4496),r(6651),r(2887),r(9369),r(6812),r(8995),r(1575),r(6072),r(8747),r(8845),r(9423),r(7301),r(373),r(6614),r(1405),r(7467),r(4732),r(3684),r(9577),r(3500),r(2953),r(3296),r(7208),r(8408),r(4603),r(7566),r(8721),r(3282)),c=r(8571),f=r(5212);function l(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return h(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?h(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){u=!0,o=t},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw o}}}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var d=function(){function t(){(0,a.A)(this,t)}return(0,u.A)(t,null,[{key:"mixin",value:function(e,r,n){var i,a={};if(e)for(var u in r)r.hasOwnProperty(u)&&(i=r[u],u in e&&(e[u]===i||u in a&&a[u]===i)||("object"===(0,o.A)(e[u])&&null!==e[u]?e[u]=t.mixin(e[u],i,n):e[u]=n(i)));return e}},{key:"clone",value:function(e){if(!e||"object"!==(0,o.A)(e))return e;if(e instanceof RegExp)return new RegExp(e);var r;if(e instanceof Array){r=[];for(var n=0,i=e.length;n<i;++n)n in e&&r.push(t.clone(e[n]))}else r={};return t.mixin(r,e,t.clone)}},{key:"addAdditionalQueryParameterToUrl",value:function(t,e){try{if(!e||0===e.length)return t;var r=t;return e.forEach((function(t){var e=t.key,n=t.value,i=r.includes("?")?"&":"?";r+="".concat(i).concat(encodeURIComponent(e),"=").concat(encodeURIComponent(n))})),r}catch(e){return t}}},{key:"removeQueryParameterFromUrl",value:function(t,e){if(!t||!e)return t;var r=new URL(t),n=new URLSearchParams(r.search);if(!n||0===n.size||!n.has(e))return t;n.delete(e);var o=Array.from(n.entries()).map((function(t){var e=(0,i.A)(t,2),r=e[0],n=e[1];return"".concat(r,"=").concat(n)})).join("&"),a="".concat(r.origin).concat(r.pathname);return o?"".concat(a,"?").concat(o):a}},{key:"parseHttpHeaders",value:function(t){var e={};if(!t)return e;for(var r=t.trim().split("\r\n"),n=0,i=r.length;n<i;n++){var o=r[n],a=o.indexOf(": ");a>0&&(e[o.substring(0,a)]=o.substring(a+2))}return e}},{key:"parseQueryParams",value:function(t){var e,r=[],n=l(new URLSearchParams(t).entries());try{for(n.s();!(e=n.n()).done;){var o=(0,i.A)(e.value,2),a=o[0],u=o[1];r.push({key:decodeURIComponent(a),value:decodeURIComponent(u)})}}catch(t){n.e(t)}finally{n.f()}return r}},{key:"generateUuid",value:function(){var t=(new Date).getTime();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var r=(t+16*Math.random())%16|0;return t=Math.floor(t/16),("x"==e?r:3&r|8).toString(16)}))}},{key:"generateHashCode",value:function(t){var e=0;if(0===t.length)return e;for(var r=0;r<t.length;r++)e=(e<<5)-e+t.charCodeAt(r),e|=0;return e}},{key:"getRelativeUrl",value:function(t,e){try{var r=new URL(t),n=new URL(e);if(r.protocol=n.protocol,r.origin!==n.origin)return e;var i=s.relative(r.pathname.substr(0,r.pathname.lastIndexOf("/")),n.pathname.substr(0,n.pathname.lastIndexOf("/"))),o=0===i.length?1:0;return i+=n.pathname.substr(n.pathname.lastIndexOf("/")+o,n.pathname.length-1),n.pathname.length<i.length?n.pathname:i}catch(t){return e}}},{key:"getHostFromUrl",value:function(t){try{return new URL(t).host}catch(t){return null}}},{key:"parseUserAgent",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;try{var e=null===t&&"undefined"!=typeof navigator?navigator.userAgent.toLowerCase():"";return(0,c.UAParser)(e)}catch(t){return{}}}},{key:"stringHasProtocol",value:function(t){return/(http(s?)):\/\//i.test(t)}},{key:"bufferSourceToDataView",value:function(e){return t.toDataView(e,DataView)}},{key:"bufferSourceToInt8",value:function(e){return t.toDataView(e,Uint8Array)}},{key:"uint8ArrayToString",value:function(t){return new TextDecoder("utf-8").decode(t)}},{key:"bufferSourceToHex",value:function(e){var r,n="",i=l(t.bufferSourceToInt8(e));try{for(i.s();!(r=i.n()).done;){var o=r.value;1===(o=o.toString(16)).length&&(o="0"+o),n+=o}}catch(t){i.e(t)}finally{i.f()}return n}},{key:"toDataView",value:function(e,r){var n=t.getArrayBuffer(e),i=1;"BYTES_PER_ELEMENT"in DataView&&(i=DataView.BYTES_PER_ELEMENT);var o=((e.byteOffset||0)+e.byteLength)/i,a=(e.byteOffset||0)/i,u=Math.floor(Math.max(0,Math.min(a,o)));return new r(n,u,Math.floor(Math.min(u+Math.max(1/0,0),o))-u)}},{key:"getArrayBuffer",value:function(t){return t instanceof ArrayBuffer?t:t.buffer}},{key:"getCodecFamily",value:function(e){var r=t._getCodecParts(e),n=r.base,i=r.profile;switch(n){case"mp4a":switch(i){case"69":case"6b":case"40.34":return f.A.CODEC_FAMILIES.MP3;case"66":case"67":case"68":case"40.2":case"40.02":case"40.5":case"40.05":case"40.29":case"40.42":return f.A.CODEC_FAMILIES.AAC;case"a5":return f.A.CODEC_FAMILIES.AC3;case"e6":return f.A.CODEC_FAMILIES.EC3;case"b2":return f.A.CODEC_FAMILIES.DTSX;case"a9":return f.A.CODEC_FAMILIES.DTSC}break;case"avc1":case"avc3":return f.A.CODEC_FAMILIES.AVC;case"hvc1":case"hvc3":return f.A.CODEC_FAMILIES.HEVC;default:return n}return n}},{key:"_getCodecParts",value:function(t){var e=t.split("."),r=(0,n.A)(e);return{base:r[0],profile:r.slice(1).join(".")}}}])}();e.A=d},5734:function(t,e,r){"use strict";r(825);var n=r(5952),i=r(7718),o=r(8591),a=r(4597),u=r(8766);function s(t,e,r){return e=(0,a.A)(e),(0,o.A)(t,c()?Reflect.construct(e,r||[],(0,a.A)(t).constructor):e.apply(t,r))}function c(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(c=function(){return!!t})()}var f=function(t){function e(){var t;return(0,i.A)(this,e),(t=s(this,e)).ATTEMPT_BACKGROUND_SYNC="attemptBackgroundSync",t.BUFFERING_COMPLETED="bufferingCompleted",t.BUFFER_CLEARED="bufferCleared",t.BYTES_APPENDED_END_FRAGMENT="bytesAppendedEndFragment",t.BUFFER_REPLACEMENT_STARTED="bufferReplacementStarted",t.CHECK_FOR_EXISTENCE_COMPLETED="checkForExistenceCompleted",t.CMSD_STATIC_HEADER="cmsdStaticHeader",t.CURRENT_TRACK_CHANGED="currentTrackChanged",t.DATA_UPDATE_COMPLETED="dataUpdateCompleted",t.INBAND_EVENTS="inbandEvents",t.INITIAL_STREAM_SWITCH="initialStreamSwitch",t.INIT_FRAGMENT_LOADED="initFragmentLoaded",t.INIT_FRAGMENT_NEEDED="initFragmentNeeded",t.INTERNAL_MANIFEST_LOADED="internalManifestLoaded",t.ORIGINAL_MANIFEST_LOADED="originalManifestLoaded",t.LOADING_COMPLETED="loadingCompleted",t.LOADING_PROGRESS="loadingProgress",t.LOADING_DATA_PROGRESS="loadingDataProgress",t.LOADING_ABANDONED="loadingAborted",t.MANIFEST_UPDATED="manifestUpdated",t.MEDIA_FRAGMENT_LOADED="mediaFragmentLoaded",t.MEDIA_FRAGMENT_NEEDED="mediaFragmentNeeded",t.MEDIAINFO_UPDATED="mediaInfoUpdated",t.QUOTA_EXCEEDED="quotaExceeded",t.SEGMENT_LOCATION_BLACKLIST_ADD="segmentLocationBlacklistAdd",t.SEGMENT_LOCATION_BLACKLIST_CHANGED="segmentLocationBlacklistChanged",t.SERVICE_LOCATION_BASE_URL_BLACKLIST_ADD="serviceLocationBlacklistAdd",t.SERVICE_LOCATION_BASE_URL_BLACKLIST_CHANGED="serviceLocationBlacklistChanged",t.SERVICE_LOCATION_LOCATION_BLACKLIST_ADD="serviceLocationLocationBlacklistAdd",t.SERVICE_LOCATION_LOCATION_BLACKLIST_CHANGED="serviceLocationLocationBlacklistChanged",t.SET_FRAGMENTED_TEXT_AFTER_DISABLED="setFragmentedTextAfterDisabled",t.SET_NON_FRAGMENTED_TEXT="setNonFragmentedText",t.SOURCE_BUFFER_ERROR="sourceBufferError",t.STREAMS_COMPOSED="streamsComposed",t.STREAM_BUFFERING_COMPLETED="streamBufferingCompleted",t.STREAM_REQUESTING_COMPLETED="streamRequestingCompleted",t.TEXT_TRACKS_QUEUE_INITIALIZED="textTracksQueueInitialized",t.TIME_SYNCHRONIZATION_COMPLETED="timeSynchronizationComplete",t.UPDATE_TIME_SYNC_OFFSET="updateTimeSyncOffset",t.URL_RESOLUTION_FAILED="urlResolutionFailed",t.VIDEO_CHUNK_RECEIVED="videoChunkReceived",t.WALLCLOCK_TIME_UPDATED="wallclockTimeUpdated",t.XLINK_ELEMENT_LOADED="xlinkElementLoaded",t.XLINK_READY="xlinkReady",t.SEEK_TARGET="seekTarget",t.SETTING_UPDATED_LIVE_DELAY="settingUpdatedLiveDelay",t.SETTING_UPDATED_LIVE_DELAY_FRAGMENT_COUNT="settingUpdatedLiveDelayFragmentCount",t.SETTING_UPDATED_CATCHUP_ENABLED="settingUpdatedCatchupEnabled",t.SETTING_UPDATED_PLAYBACK_RATE_MIN="settingUpdatedPlaybackRateMin",t.SETTING_UPDATED_PLAYBACK_RATE_MAX="settingUpdatedPlaybackRateMax",t.SETTING_UPDATED_ABR_ACTIVE_RULES="settingUpdatedAbrActiveRules",t.SETTING_UPDATED_MAX_BITRATE="settingUpdatedMaxBitrate",t.SETTING_UPDATED_MIN_BITRATE="settingUpdatedMinBitrate",t}return(0,u.A)(e,t),(0,n.A)(e)}(r(7252).A);e.A=f},3621:function(t,e,r){"use strict";r(825);var n=r(5952),i=r(7718),o=r(8591),a=r(4597),u=r(8766);function s(t,e,r){return e=(0,a.A)(e),(0,o.A)(t,c()?Reflect.construct(e,r||[],(0,a.A)(t).constructor):e.apply(t,r))}function c(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(c=function(){return!!t})()}var f=function(t){function e(){return(0,i.A)(this,e),s(this,e,arguments)}return(0,u.A)(e,t),(0,n.A)(e)}(r(5734).A),l=new f;e.A=l},7252:function(t,e,r){"use strict";var n=r(7718),i=r(5952),o=function(){return(0,i.A)((function t(){(0,n.A)(this,t)}),[{key:"extend",value:function(t,e){if(t){var r=!!e&&e.override,n=!!e&&e.publicOnly;for(var i in t)!t.hasOwnProperty(i)||this[i]&&!r||n&&-1===t[i].indexOf("public_")||(this[i]=t[i])}}}])}();e.A=o},5717:function(t,e,r){"use strict";var n=r(5952),i=r(7718),o=(0,n.A)((function t(){(0,i.A)(this,t),this.schemeIdUri="",this.value=""}));e.A=o},1191:function(t,e,r){"use strict";r(825);var n=r(5952),i=r(7718),o=r(8591),a=r(4597),u=r(8766);function s(t,e,r){return e=(0,a.A)(e),(0,o.A)(t,c()?Reflect.construct(e,r||[],(0,a.A)(t).constructor):e.apply(t,r))}function c(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(c=function(){return!!t})()}var f=new(function(t){function e(){var t;return(0,i.A)(this,e),(t=s(this,e)).AST_IN_FUTURE="astInFuture",t.BASE_URLS_UPDATED="baseUrlsUpdated",t.BUFFER_EMPTY="bufferStalled",t.BUFFER_LOADED="bufferLoaded",t.BUFFER_LEVEL_STATE_CHANGED="bufferStateChanged",t.BUFFER_LEVEL_UPDATED="bufferLevelUpdated",t.DVB_FONT_DOWNLOAD_ADDED="dvbFontDownloadAdded",t.DVB_FONT_DOWNLOAD_COMPLETE="dvbFontDownloadComplete",t.DVB_FONT_DOWNLOAD_FAILED="dvbFontDownloadFailed",t.DYNAMIC_TO_STATIC="dynamicToStatic",t.ERROR="error",t.FRAGMENT_LOADING_COMPLETED="fragmentLoadingCompleted",t.FRAGMENT_LOADING_PROGRESS="fragmentLoadingProgress",t.FRAGMENT_LOADING_STARTED="fragmentLoadingStarted",t.FRAGMENT_LOADING_ABANDONED="fragmentLoadingAbandoned",t.LOG="log",t.MANIFEST_LOADING_STARTED="manifestLoadingStarted",t.MANIFEST_LOADING_FINISHED="manifestLoadingFinished",t.MANIFEST_LOADED="manifestLoaded",t.METRICS_CHANGED="metricsChanged",t.METRIC_CHANGED="metricChanged",t.METRIC_ADDED="metricAdded",t.METRIC_UPDATED="metricUpdated",t.PERIOD_SWITCH_STARTED="periodSwitchStarted",t.PERIOD_SWITCH_COMPLETED="periodSwitchCompleted",t.QUALITY_CHANGE_REQUESTED="qualityChangeRequested",t.QUALITY_CHANGE_RENDERED="qualityChangeRendered",t.NEW_TRACK_SELECTED="newTrackSelected",t.TRACK_CHANGE_RENDERED="trackChangeRendered",t.STREAM_INITIALIZING="streamInitializing",t.STREAM_UPDATED="streamUpdated",t.STREAM_ACTIVATED="streamActivated",t.STREAM_DEACTIVATED="streamDeactivated",t.STREAM_INITIALIZED="streamInitialized",t.STREAM_TEARDOWN_COMPLETE="streamTeardownComplete",t.TEXT_TRACKS_ADDED="allTextTracksAdded",t.TEXT_TRACK_ADDED="textTrackAdded",t.CUE_ENTER="cueEnter",t.CUE_EXIT="cueExit",t.THROUGHPUT_MEASUREMENT_STORED="throughputMeasurementStored",t.TTML_PARSED="ttmlParsed",t.TTML_TO_PARSE="ttmlToParse",t.CAPTION_RENDERED="captionRendered",t.CAPTION_CONTAINER_RESIZE="captionContainerResize",t.CAN_PLAY="canPlay",t.CAN_PLAY_THROUGH="canPlayThrough",t.PLAYBACK_ENDED="playbackEnded",t.PLAYBACK_ERROR="playbackError",t.PLAYBACK_INITIALIZED="playbackInitialized",t.PLAYBACK_NOT_ALLOWED="playbackNotAllowed",t.PLAYBACK_METADATA_LOADED="playbackMetaDataLoaded",t.PLAYBACK_LOADED_DATA="playbackLoadedData",t.PLAYBACK_PAUSED="playbackPaused",t.PLAYBACK_PLAYING="playbackPlaying",t.PLAYBACK_PROGRESS="playbackProgress",t.PLAYBACK_RATE_CHANGED="playbackRateChanged",t.PLAYBACK_SEEKED="playbackSeeked",t.PLAYBACK_SEEKING="playbackSeeking",t.PLAYBACK_STALLED="playbackStalled",t.PLAYBACK_STARTED="playbackStarted",t.PLAYBACK_TIME_UPDATED="playbackTimeUpdated",t.PLAYBACK_VOLUME_CHANGED="playbackVolumeChanged",t.PLAYBACK_WAITING="playbackWaiting",t.MANIFEST_VALIDITY_CHANGED="manifestValidityChanged",t.EVENT_MODE_ON_START="eventModeOnStart",t.EVENT_MODE_ON_RECEIVE="eventModeOnReceive",t.CONFORMANCE_VIOLATION="conformanceViolation",t.REPRESENTATION_SWITCH="representationSwitch",t.ADAPTATION_SET_REMOVED_NO_CAPABILITIES="adaptationSetRemovedNoCapabilities",t.CONTENT_STEERING_REQUEST_COMPLETED="contentSteeringRequestCompleted",t.INBAND_PRFT="inbandPrft",t.MANAGED_MEDIA_SOURCE_START_STREAMING="managedMediaSourceStartStreaming",t.MANAGED_MEDIA_SOURCE_END_STREAMING="managedMediaSourceEndStreaming",t}return(0,u.A)(e,t),(0,n.A)(e)}(r(7252).A));e.A=f},5212:function(t,e){"use strict";e.A={STREAM:"stream",VIDEO:"video",AUDIO:"audio",TEXT:"text",MUXED:"muxed",IMAGE:"image",STPP:"stpp",TTML:"ttml",VTT:"vtt",WVTT:"wvtt",CONTENT_STEERING:"contentSteering",LIVE_CATCHUP_MODE_DEFAULT:"liveCatchupModeDefault",LIVE_CATCHUP_MODE_LOLP:"liveCatchupModeLoLP",MOVING_AVERAGE_SLIDING_WINDOW:"slidingWindow",MOVING_AVERAGE_EWMA:"ewma",BAD_ARGUMENT_ERROR:"Invalid Arguments",MISSING_CONFIG_ERROR:"Missing config parameter(s)",TRACK_SWITCH_MODE_ALWAYS_REPLACE:"alwaysReplace",TRACK_SWITCH_MODE_NEVER_REPLACE:"neverReplace",TRACK_SELECTION_MODE_FIRST_TRACK:"firstTrack",TRACK_SELECTION_MODE_HIGHEST_BITRATE:"highestBitrate",TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY:"highestEfficiency",TRACK_SELECTION_MODE_WIDEST_RANGE:"widestRange",CMCD_QUERY_KEY:"CMCD",CMCD_MODE_QUERY:"query",CMCD_MODE_HEADER:"header",CMCD_AVAILABLE_KEYS:["br","d","ot","tb","bl","dl","mtp","nor","nrr","su","bs","rtp","cid","pr","sf","sid","st","v"],CMCD_V2_AVAILABLE_KEYS:["msd","ltc"],CMCD_AVAILABLE_REQUESTS:["segment","mpd","xlink","steering","other"],INITIALIZE:"initialize",TEXT_SHOWING:"showing",TEXT_HIDDEN:"hidden",TEXT_DISABLED:"disabled",ACCESSIBILITY_CEA608_SCHEME:"urn:scte:dash:cc:cea-608:2015",CC1:"CC1",CC3:"CC3",UTF8:"utf-8",SCHEME_ID_URI:"schemeIdUri",START_TIME:"starttime",SERVICE_DESCRIPTION_DVB_LL_SCHEME:"urn:dvb:dash:lowlatency:scope:2019",SUPPLEMENTAL_PROPERTY_DVB_LL_SCHEME:"urn:dvb:dash:lowlatency:critical:2019",CTA_5004_2023_SCHEME:"urn:mpeg:dash:cta-5004:2023",THUMBNAILS_SCHEME_ID_URIS:["http://dashif.org/thumbnail_tile","http://dashif.org/guidelines/thumbnail_tile"],FONT_DOWNLOAD_DVB_SCHEME:"urn:dvb:dash:fontdownload:2014",COLOUR_PRIMARIES_SCHEME_ID_URI:"urn:mpeg:mpegB:cicp:ColourPrimaries",URL_QUERY_INFO_SCHEME:"urn:mpeg:dash:urlparam:2014",EXT_URL_QUERY_INFO_SCHEME:"urn:mpeg:dash:urlparam:2016",MATRIX_COEFFICIENTS_SCHEME_ID_URI:"urn:mpeg:mpegB:cicp:MatrixCoefficients",TRANSFER_CHARACTERISTICS_SCHEME_ID_URI:"urn:mpeg:mpegB:cicp:TransferCharacteristics",HDR_METADATA_FORMAT_SCHEME_ID_URI:"urn:dvb:dash:hdr-dmi",HDR_METADATA_FORMAT_VALUES:{ST2094_10:"ST2094-10",SL_HDR2:"SL-HDR2",ST2094_40:"ST2094-40"},MEDIA_CAPABILITIES_API:{COLORGAMUT:{SRGB:"srgb",P3:"p3",REC2020:"rec2020"},TRANSFERFUNCTION:{SRGB:"srgb",PQ:"pq",HLG:"hlg"},HDR_METADATATYPE:{SMPTE_ST_2094_10:"smpteSt2094-10",SLHDR2:"slhdr2",SMPTE_ST_2094_40:"smpteSt2094-40"}},XML:"XML",ARRAY_BUFFER:"ArrayBuffer",DVB_REPORTING_URL:"dvb:reportingUrl",DVB_PROBABILITY:"dvb:probability",OFF_MIMETYPE:"application/font-sfnt",WOFF_MIMETYPE:"application/font-woff",VIDEO_ELEMENT_READY_STATES:{HAVE_NOTHING:0,HAVE_METADATA:1,HAVE_CURRENT_DATA:2,HAVE_FUTURE_DATA:3,HAVE_ENOUGH_DATA:4},FILE_LOADER_TYPES:{FETCH:"fetch_loader",XHR:"xhr_loader"},THROUGHPUT_TYPES:{LATENCY:"throughput_type_latency",BANDWIDTH:"throughput_type_bandwidth"},THROUGHPUT_CALCULATION_MODES:{EWMA:"throughputCalculationModeEwma",ZLEMA:"throughputCalculationModeZlema",ARITHMETIC_MEAN:"throughputCalculationModeArithmeticMean",BYTE_SIZE_WEIGHTED_ARITHMETIC_MEAN:"throughputCalculationModeByteSizeWeightedArithmeticMean",DATE_WEIGHTED_ARITHMETIC_MEAN:"throughputCalculationModeDateWeightedArithmeticMean",HARMONIC_MEAN:"throughputCalculationModeHarmonicMean",BYTE_SIZE_WEIGHTED_HARMONIC_MEAN:"throughputCalculationModeByteSizeWeightedHarmonicMean",DATE_WEIGHTED_HARMONIC_MEAN:"throughputCalculationModeDateWeightedHarmonicMean"},LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE:{MOOF_PARSING:"lowLatencyDownloadTimeCalculationModeMoofParsing",DOWNLOADED_DATA:"lowLatencyDownloadTimeCalculationModeDownloadedData",AAST:"lowLatencyDownloadTimeCalculationModeAast"},RULES_TYPES:{QUALITY_SWITCH_RULES:"qualitySwitchRules",ABANDON_FRAGMENT_RULES:"abandonFragmentRules"},QUALITY_SWITCH_RULES:{BOLA_RULE:"BolaRule",THROUGHPUT_RULE:"ThroughputRule",INSUFFICIENT_BUFFER_RULE:"InsufficientBufferRule",SWITCH_HISTORY_RULE:"SwitchHistoryRule",DROPPED_FRAMES_RULE:"DroppedFramesRule",LEARN_TO_ADAPT_RULE:"L2ARule",LOL_PLUS_RULE:"LoLPRule"},ABANDON_FRAGMENT_RULES:{ABANDON_REQUEST_RULE:"AbandonRequestsRule"},ID3_SCHEME_ID_URI:"https://aomedia.org/emsg/ID3",COMMON_ACCESS_TOKEN_HEADER:"common-access-token",DASH_ROLE_SCHEME_ID:"urn:mpeg:dash:role:2011",CODEC_FAMILIES:{MP3:"mp3",AAC:"aac",AC3:"ac3",EC3:"ec3",DTSX:"dtsx",DTSC:"dtsc",AVC:"avc",HEVC:"hevc"}}},913:function(t,e,r){"use strict";r(825);var n=r(5952),i=r(7718),o=r(8591),a=r(4597),u=r(8766);function s(t,e,r){return e=(0,a.A)(e),(0,o.A)(t,c()?Reflect.construct(e,r||[],(0,a.A)(t).constructor):e.apply(t,r))}function c(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(c=function(){return!!t})()}var f=new(function(t){function e(){var t;return(0,i.A)(this,e),(t=s(this,e)).METRICS_INITIALISATION_COMPLETE="internal_metricsReportingInitialized",t.BECAME_REPORTING_PLAYER="internal_becameReportingPlayer",t.CMCD_DATA_GENERATED="cmcdDataGenerated",t}return(0,u.A)(e,t),(0,n.A)(e)}(r(7252).A));e.A=f},1926:function(t,e,r){"use strict";r(4114),r(4554),r(9432),r(6099),r(3500);var n=r(5717),i=r(138),o=r(8261),a=r(656),u=r(5212);function s(){var t,e,r,i,s,c,f,l,h,d,p=this.context,v=(0,o.A)(p).getInstance();function g(){i=[],s=[],c=[],f=[],l=[],d=[],h=null,e=[]}function y(t,e){var r=-1;t.some((function(t,n){if(t===e)return r=n,!0})),r<0||t.splice(r,1)}function E(t){var e;for(e=0;e<d.length;e++)if(d[e].rulename===t)return e;return-1}function A(t,r){m(t,r);var i=new n.A;i.schemeIdUri=t,i.value=r,e.push(i)}function m(t,r){(0,a.sq)(t,"string"),(0,a.sq)(r,"string"),e.forEach((function(n,i){n.schemeIdUri===t&&n.value===r&&e.splice(i,1)}))}return t={addAbrCustomRule:function(t,e,r){if("string"!=typeof t||t!==u.A.RULES_TYPES.ABANDON_FRAGMENT_RULES&&t!==u.A.RULES_TYPES.QUALITY_SWITCH_RULES||"string"!=typeof e)throw u.A.BAD_ARGUMENT_ERROR;var n=E(e);-1===n?d.push({type:t,rulename:e,rule:r}):(d[n].type=t,d[n].rule=r)},addRequestInterceptor:function(t){i.push(t)},addResponseInterceptor:function(t){s.push(t)},addUTCTimingSource:A,clearDefaultUTCTimingSources:function(){e=[]},getAbrCustomRules:function(){return d},getCustomCapabilitiesFilters:function(){return l},getCustomInitialTrackSelectionFunction:function(){return h},getLicenseRequestFilters:function(){return c},getLicenseResponseFilters:function(){return f},getRequestInterceptors:function(){return i},getResponseInterceptors:function(){return s},getUTCTimingSources:function(){return e},getXHRWithCredentialsForType:function(t){var e=r[t];return void 0===e?r.default:e},registerCustomCapabilitiesFilter:function(t){l.push(t)},registerLicenseRequestFilter:function(t){c.push(t)},registerLicenseResponseFilter:function(t){f.push(t)},removeAbrCustomRule:function(t){if(t){var e=E(t);-1!==e&&d.splice(e,1)}else d=[]},removeAllAbrCustomRule:function(){d=[]},removeRequestInterceptor:function(t){y(i,t)},removeResponseInterceptor:function(t){y(s,t)},removeUTCTimingSource:m,reset:function(){g()},resetCustomInitialTrackSelectionFunction:function(){h=null},restoreDefaultUTCTimingSources:function(){var t=v.get().streaming.utcSynchronization.defaultTimingSource;A(t.scheme,t.value)},setConfig:function(){},setCustomInitialTrackSelectionFunction:function(t){h=t},setXHRWithCredentialsForType:function t(e,n){e?r[e]=!!n:Object.keys(r).forEach((function(e){t(e,n)}))},unregisterCustomCapabilitiesFilter:function(t){y(l,t)},unregisterLicenseRequestFilter:function(t){y(c,t)},unregisterLicenseResponseFilter:function(t){y(f,t)}},r={default:!1},g(),t}s.__dashjs_factory_name="CustomParametersModel",e.A=i.A.getSingletonFactory(s)},7377:function(t,e,r){"use strict";r(4554);var n=r(138),i=r(656);function o(){return{customTimeRangeArray:[],length:0,add:function(t,e){var r;for(r=0;r<this.customTimeRangeArray.length&&t>this.customTimeRangeArray[r].start;r++);for(this.customTimeRangeArray.splice(r,0,{start:t,end:e}),r=0;r<this.customTimeRangeArray.length-1;r++)this.mergeRanges(r,r+1)&&r--;this.length=this.customTimeRangeArray.length},clear:function(){this.customTimeRangeArray=[],this.length=0},remove:function(t,e){for(var r=0;r<this.customTimeRangeArray.length;r++)if(t<=this.customTimeRangeArray[r].start&&e>=this.customTimeRangeArray[r].end)this.customTimeRangeArray.splice(r,1),r--;else{if(t>this.customTimeRangeArray[r].start&&e<this.customTimeRangeArray[r].end){this.customTimeRangeArray.splice(r+1,0,{start:e,end:this.customTimeRangeArray[r].end}),this.customTimeRangeArray[r].end=t;break}t>this.customTimeRangeArray[r].start&&t<this.customTimeRangeArray[r].end?this.customTimeRangeArray[r].end=t:e>this.customTimeRangeArray[r].start&&e<this.customTimeRangeArray[r].end&&(this.customTimeRangeArray[r].start=e)}this.length=this.customTimeRangeArray.length},mergeRanges:function(t,e){var r=this.customTimeRangeArray[t],n=this.customTimeRangeArray[e];return r.start<=n.start&&n.start<=r.end&&r.end<=n.end?(r.end=n.end,this.customTimeRangeArray.splice(e,1),!0):n.start<=r.start&&r.start<=n.end&&n.end<=r.end?(r.start=n.start,this.customTimeRangeArray.splice(e,1),!0):n.start<=r.start&&r.start<=n.end&&r.end<=n.end?(this.customTimeRangeArray.splice(t,1),!0):r.start<=n.start&&n.start<=r.end&&n.end<=r.end&&(this.customTimeRangeArray.splice(e,1),!0)},start:function(t){return(0,i.zQ)(t),t>=this.customTimeRangeArray.length||t<0?NaN:this.customTimeRangeArray[t].start},end:function(t){return(0,i.zQ)(t),t>=this.customTimeRangeArray.length||t<0?NaN:this.customTimeRangeArray[t].end}}}o.__dashjs_factory_name="CustomTimeRanges",e.A=n.A.getClassFactory(o)},656:function(t,e,r){"use strict";r.d(e,{sq:function(){return o},zQ:function(){return a}});var n=r(4299),i=r(5212);function o(t,e){if((0,n.A)(t)!==e)throw i.A.BAD_ARGUMENT_ERROR}function a(t){if(null===t||isNaN(t)||t%1!=0)throw i.A.BAD_ARGUMENT_ERROR+" : argument is not an integer"}},7568:function(t,e,r){"use strict";r.d(e,{G:function(){return o}});var n=r(5952),i=r(7718),o=(0,n.A)((function t(){(0,i.A)(this,t),this.tcpid=null,this.type=null,this.url=null,this.actualurl=null,this.range=null,this.trequest=null,this.tresponse=null,this.responsecode=null,this.interval=null,this.trace=[],this.cmsd=null,this._stream=null,this._tfinish=null,this._mediaduration=null,this._responseHeaders=null,this._serviceLocation=null,this._fileLoaderType=null,this._resourceTimingValues=null}));o.GET="GET",o.HEAD="HEAD",o.MPD_TYPE="MPD",o.XLINK_EXPANSION_TYPE="XLinkExpansion",o.INIT_SEGMENT_TYPE="InitializationSegment",o.INDEX_SEGMENT_TYPE="IndexSegment",o.MEDIA_SEGMENT_TYPE="MediaSegment",o.BITSTREAM_SWITCHING_SEGMENT_TYPE="BitstreamSwitchingSegment",o.MSS_FRAGMENT_INFO_SEGMENT_TYPE="FragmentInfoSegment",o.DVB_REPORTING_TYPE="DVBReporting",o.LICENSE="license",o.CONTENT_STEERING_TYPE="ContentSteering",o.OTHER_TYPE="other"}},e={};function r(n){var i=e[n];if(void 0!==i)return i.exports;var o=e[n]={id:n,loaded:!1,exports:{}};return t[n].call(o.exports,o,o.exports,r),o.loaded=!0,o.exports}r.amdD=function(){throw new Error("define cannot be used indirect")},r.amdO={},r.d=function(t,e){for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.nmd=function(t){return t.paths=[],t.children||(t.children=[]),t};var n={};return function(){"use strict";r.d(n,{default:function(){return K}});var t=r(5952),e=r(7718),i=(0,t.A)((function t(){(0,e.A)(this,t),this.mpdurl=null,this.errorcode=null,this.terror=null,this.url=null,this.ipaddress=null,this.servicelocation=null}));i.SSL_CONNECTION_FAILED_PREFIX="SSL",i.DNS_RESOLUTION_FAILED="C00",i.HOST_UNREACHABLE="C01",i.CONNECTION_REFUSED="C02",i.CONNECTION_ERROR="C03",i.CORRUPT_MEDIA_ISOBMFF="M00",i.CORRUPT_MEDIA_OTHER="M01",i.BASE_URL_CHANGED="F00",i.BECAME_REPORTER="S00";var o=i,a=r(913),u=r(138);function s(t){var e,r,n=(t=t||{}).eventBus,i=t.dashMetrics,u=t.metricsConstants,s=t.events;function c(t){var e=new o;if(r){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);e.mpdurl||(e.mpdurl=r.originalUrl||r.url),e.terror||(e.terror=new Date),i.addDVBErrors(e)}}function f(t){t.error||(r=t.manifest)}function l(t){c({errorcode:o.BASE_URL_CHANGED,servicelocation:t.entry})}function h(){c({errorcode:o.BECAME_REPORTER})}function d(t){var e;t.metric===u.HTTP_REQUEST&&(0===(e=t.value).responsecode||null==e.responsecode||e.responsecode>=400||e.responsecode<100||e.responsecode>=600)&&c({errorcode:e.responsecode||o.CONNECTION_ERROR,url:e.url,terror:e.tresponse,servicelocation:e._serviceLocation})}function p(t){var e;switch(t.error?t.error.code:0){case MediaError.MEDIA_ERR_NETWORK:e=o.CONNECTION_ERROR;break;case MediaError.MEDIA_ERR_DECODE:e=o.CORRUPT_MEDIA_OTHER;break;default:return}c({errorcode:e})}return e={initialize:function(){n.on(s.MANIFEST_UPDATED,f,e),n.on(s.SERVICE_LOCATION_BASE_URL_BLACKLIST_CHANGED,l,e),n.on(s.METRIC_ADDED,d,e),n.on(s.METRIC_UPDATED,d,e),n.on(s.PLAYBACK_ERROR,p,e),n.on(a.A.BECAME_REPORTING_PLAYER,h,e)},reset:function(){n.off(s.MANIFEST_UPDATED,f,e),n.off(s.SERVICE_LOCATION_BASE_URL_BLACKLIST_CHANGED,l,e),n.off(s.METRIC_ADDED,d,e),n.off(s.METRIC_UPDATED,d,e),n.off(s.PLAYBACK_ERROR,p,e),n.off(a.A.BECAME_REPORTING_PLAYER,h,e)}}}s.__dashjs_factory_name="DVBErrorsTranslator";var c=u.A.getSingletonFactory(s),f=(r(4554),r(3110),r(9432),r(6099),r(3500),r(7377));function l(t){t=t||{};var e,r,n=!1,i=this.context,o=t.mediaElement;return e={initialize:function(t){t&&t.length&&(t.forEach((function(t){var e=t.starttime,n=e+t.duration;r.add(e,n)})),n=!!t[0]._useWallClockTime)},reset:function(){r.clear()},isEnabled:function(){var t,e=r.length;if(!e)return!0;t=n?(new Date).getTime()/1e3:o.currentTime;for(var i=0;i<e;i+=1){var a=r.start(i),u=r.end(i);if(a<=t&&t<u)return!0}return!1}},r=(0,f.A)(i).create(),e}l.__dashjs_factory_name="RangeController";var h=u.A.getClassFactory(l);function d(){return{serialise:function t(e){var r,n,i=[],o=[];for(r in e)if(e.hasOwnProperty(r)&&0!==r.indexOf("_")){if(null==(n=e[r])&&(n=""),Array.isArray(n)){if(!n.length)continue;o=[],n.forEach((function(e){var r="Object"!==Object.prototype.toString.call(e).slice(8,-1);o.push(r?e:t(e))})),n=o.map(encodeURIComponent).join(",")}else"string"==typeof n?n=encodeURIComponent(n):n instanceof Date?n=n.toISOString():"number"==typeof n&&(n=Math.round(n));i.push(r+"="+n)}return i.join("&")}}}r(4114),r(8706),r(6280),r(8598),r(2062),r(4782),r(8781),d.__dashjs_factory_name="MetricSerialiser";var p=u.A.getSingletonFactory(d);function v(){var t,e,r,n=window.crypto||window.msCrypto,i=Uint32Array,o=Math.pow(2,8*i.BYTES_PER_ELEMENT)-1;function a(){n&&(t||(t=new i(10)),n.getRandomValues(t),e=0)}return r={random:function(r,i){var u;return r||(r=0),i||(i=1),n?(e===t.length&&a(),u=t[e]/o,e+=1):u=Math.random(),u*(i-r)+r}},a(),r}r(3792),r(4743),r(6573),r(8100),r(7936),r(1740),r(8140),r(1630),r(2170),r(5044),r(1920),r(1694),r(9955),r(1903),r(1134),r(3206),r(4496),r(6651),r(2887),r(9369),r(6812),r(8995),r(1575),r(6072),r(8747),r(8845),r(9423),r(7301),r(373),r(6614),r(1405),r(7467),r(4732),r(3684),r(9577),v.__dashjs_factory_name="RNG";var g=u.A.getSingletonFactory(v),y=r(1926);function E(t){var e;t=t||{};var r,n,i,o,a,u,s,c=this.context,f=[],l=t.metricsConstants;function h(){o=!1,a=!1,u=null,s=null}return e={report:function(t,e){Array.isArray(e)||(e=[e]),a&&s.isEnabled()&&e.forEach((function(e){var i=r.serialise(e);t!==l.DVB_ERRORS&&(i="metricname=".concat(t,"&").concat(i)),function(t,e,r){var i=new XMLHttpRequest;i.withCredentials=n.getXHRWithCredentialsForType(l.HTTP_REQUEST_DVB_REPORTING_TYPE);var o=function(){var t=f.indexOf(i);-1!==t&&(f.splice(t,1),!(i.status>=200&&i.status<300)&&(r&&r()))};f.push(i);try{i.open("GET",t),i.onloadend=o,i.onerror=o,i.send()}catch(t){i.onerror()}}(i="".concat(u,"?").concat(i),0,(function(){a=!1}))}))},initialize:function(t,e){var r;if(s=e,!(u=t.dvbReportingUrl))throw new Error("required parameter missing (dvb:reportingUrl)");o||((r=t.dvbProbability)&&(1e3===r||r/1e3>=i.random())&&(a=!0),o=!0)},reset:function(){h()}},r=p(c).getInstance(),i=g(c).getInstance(),n=(0,y.A)(c).getInstance(),h(),e}E.__dashjs_factory_name="DVBReporting";var A=u.A.getClassFactory(E);function m(t){t=t||{};var e,r={"urn:dvb:dash:reporting:2014":A},n=this.context,i=t.debug?t.debug.getLogger(e):{},o=t.metricsConstants,a=t.mediaPlayerModel||{};return{create:function(t,e){var u;try{(u=r[t.schemeIdUri](n).create({metricsConstants:o,mediaPlayerModel:a})).initialize(t,e)}catch(e){u=null,i.error("ReportingFactory: could not create Reporting with schemeIdUri ".concat(t.schemeIdUri," (").concat(e.message,")"))}return u},register:function(t,e){r[t]=e},unregister:function(t){delete r[t]}}}m.__dashjs_factory_name="ReportingFactory";var T=u.A.getSingletonFactory(m);function _(t){var e=[],r=T(this.context).getInstance(t);return{initialize:function(t,n){t.some((function(t){var i=r.create(t,n);if(i)return e.push(i),!0}))},reset:function(){e.forEach((function(t){return t.reset()})),e=[]},report:function(t,r){e.forEach((function(e){return e.report(t,r)}))}}}_.__dashjs_factory_name="ReportingController";var b=u.A.getClassFactory(_);function w(){return{reconstructFullMetricName:function(t,e,r){var n=t;return e&&(n+="("+e,r&&r.length&&(n+=","+r),n+=")"),n},validateN:function(t){if(!t)throw new Error("missing n");if(isNaN(t))throw new Error("n is NaN");if(t<0)throw new Error("n must be positive");return t}}}r(7495),r(1761),w.__dashjs_factory_name="HandlerHelpers";var R=u.A.getSingletonFactory(w);function S(t){var e,r,n,i,o;t=t||{};var a=this.context,u=R(a).getInstance(),s=[],c=t.metricsConstants;function f(){var t=function(){try{return Object.keys(s).map((function(t){return s[t]})).reduce((function(t,e){return t.level<e.level?t:e}))}catch(t){return}}();t&&o!==t.t&&(o=t.t,e.report(n,t))}return{initialize:function(t,o,a){o&&(r=u.validateN(a),e=o,n=u.reconstructFullMetricName(t,a),i=setInterval(f,r))},reset:function(){clearInterval(i),i=null,r=0,e=null,o=null},handleNewMetric:function(t,e,r){t===c.BUFFER_LEVEL&&(s[r]=e)}}}S.__dashjs_factory_name="BufferLevelHandler";var I=u.A.getClassFactory(S),C=u.A.getClassFactory((function(t){var e,r=(t=t||{}).eventBus,n=t.metricsConstants;function i(){r.off(a.A.METRICS_INITIALISATION_COMPLETE,i,this),r.trigger(a.A.BECAME_REPORTING_PLAYER)}return{initialize:function(t,n){n&&(e=n,r.on(a.A.METRICS_INITIALISATION_COMPLETE,i,this))},reset:function(){e=null},handleNewMetric:function(t,r){t===n.DVB_ERRORS&&e&&e.report(t,r)}}}));function x(t){var e,r,n,i,o;t=t||{};var a=[],u=R(this.context).getInstance(),s=t.metricsConstants;function c(){var t=a;t.length&&e&&e.report(i,t),a=[]}return{initialize:function(t,a,s,f){a&&(r=u.validateN(s),e=a,f&&f.length&&(n=f),i=u.reconstructFullMetricName(t,s,f),o=setInterval(c,r))},reset:function(){clearInterval(o),o=null,r=null,n=null,a=[],e=null},handleNewMetric:function(t,e){t===s.HTTP_REQUEST&&(n&&n!==e.type||a.push(e))}}}x.__dashjs_factory_name="HttpListHandler";var O=u.A.getClassFactory(x);function D(){var t,e;return{initialize:function(r,n){t=r,e=n},reset:function(){e=null,t=void 0},handleNewMetric:function(r,n){r===t&&e&&e.report(t,n)}}}D.__dashjs_factory_name="GenericMetricHandler";var L=u.A.getClassFactory(D);function N(t){var e,r=(t=t||{}).debug?t.debug.getLogger(e):{},n=/([a-zA-Z]*)(\(([0-9]*)(\,\s*([a-zA-Z]*))?\))?/,i=this.context,o={BufferLevel:I,DVBErrors:C,HttpList:O,PlayList:L,RepSwitchList:L,TcpList:L};return{create:function(e,a){var u,s=e.match(n);if(s){try{(u=o[s[1]](i).create({eventBus:t.eventBus,metricsConstants:t.metricsConstants})).initialize(s[1],a,s[3],s[5])}catch(t){u=null,r.error("MetricsHandlerFactory: Could not create handler for type ".concat(s[1]," with args ").concat(s[3],", ").concat(s[5]," (").concat(t.message,")"))}return u}},register:function(t,e){o[t]=e},unregister:function(t){delete o[t]}}}N.__dashjs_factory_name="MetricsHandlerFactory";var M=u.A.getSingletonFactory(N);function P(t){t=t||{};var e,r=[],n=this.context,i=t.eventBus,o=t.events,a=M(n).getInstance({debug:t.debug,eventBus:t.eventBus,metricsConstants:t.metricsConstants});function u(t){r.forEach((function(e){e.handleNewMetric(t.metric,t.value,t.mediaType)}))}return e={initialize:function(t,n){t.split(",").forEach((function(t,e,i){var o;if(-1!==t.indexOf("(")&&-1===t.indexOf(")")){var u=i[e+1];u&&-1===u.indexOf("(")&&-1!==u.indexOf(")")&&(t+=","+u,delete i[e+1])}(o=a.create(t,n))&&r.push(o)})),i.on(o.METRIC_ADDED,u,e),i.on(o.METRIC_UPDATED,u,e)},reset:function(){i.off(o.METRIC_ADDED,u,e),i.off(o.METRIC_UPDATED,u,e),r.forEach((function(t){return t.reset()})),r=[]}}}P.__dashjs_factory_name="MetricsHandlersController";var U=u.A.getClassFactory(P);function k(t){var e,r,n;t=t||{};var i=this.context;function o(){e&&e.reset(),r&&r.reset(),n&&n.reset()}return{initialize:function(a){try{(n=h(i).create({mediaElement:t.mediaElement})).initialize(a.Range),(r=b(i).create({debug:t.debug,metricsConstants:t.metricsConstants,mediaPlayerModel:t.mediaPlayerModel})).initialize(a.Reporting,n),(e=U(i).create({debug:t.debug,eventBus:t.eventBus,metricsConstants:t.metricsConstants,events:t.events})).initialize(a.metrics,r)}catch(t){throw o(),t}},reset:o}}k.__dashjs_factory_name="MetricsController";var F=u.A.getClassFactory(k),B=(0,t.A)((function t(){(0,e.A)(this,t),this.metrics="",this.Range=[],this.Reporting=[]})),G=(0,t.A)((function t(){(0,e.A)(this,t),this.starttime=0,this.duration=1/0,this._useWallClockTime=!1})),H=(0,t.A)((function t(){(0,e.A)(this,t),this.schemeIdUri="",this.value="",this.dvbReportingUrl="",this.dvbProbability=1e3}));function j(t){var e=(t=t||{}).adapter,r=t.constants;return{getMetrics:function(t){var n=[];return t&&t.Metrics&&t.Metrics.forEach((function(i){var o=new B,a=e.getIsDynamic(t);i.hasOwnProperty("metrics")&&(o.metrics=i.metrics,i.Range&&i.Range.forEach((function(n){var i=new G;i.starttime=function(t,n,i){var o,a,u=0;return n?u=e.getAvailabilityStartTime(t)/1e3:(o=e.getRegularPeriods(t)).length&&(u=o[0].start),a=u,i&&i.hasOwnProperty(r.START_TIME)&&(a+=i.starttime),a}(t,a,n),n.hasOwnProperty("duration")?i.duration=n.duration:i.duration=e.getDuration(t),i._useWallClockTime=a,o.Range.push(i)})),i.Reporting&&(i.Reporting.forEach((function(t){var e=new H;t.hasOwnProperty(r.SCHEME_ID_URI)&&(e.schemeIdUri=t.schemeIdUri,t.hasOwnProperty("value")&&(e.value=t.value),t.hasOwnProperty(r.DVB_REPORTING_URL)&&(e.dvbReportingUrl=t[r.DVB_REPORTING_URL]),t.hasOwnProperty(r.DVB_PROBABILITY)&&(e.dvbProbability=t[r.DVB_PROBABILITY]),o.Reporting.push(e))})),n.push(o)))})),n}}}j.__dashjs_factory_name="ManifestParsing";var V=u.A.getSingletonFactory(j);function Y(t){var e;t=t||{};var r={},n=this.context,i=t.eventBus,o=t.events;function u(e){if(!e.error){var o=Object.keys(r);V(n).getInstance({adapter:t.adapter,constants:t.constants}).getMetrics(e.manifest).forEach((function(e){var i=JSON.stringify(e);if(r.hasOwnProperty(i))o.splice(i,1);else try{var a=F(n).create(t);a.initialize(e),r[i]=a}catch(t){}})),o.forEach((function(t){r[t].reset(),delete r[t]})),i.trigger(a.A.METRICS_INITIALISATION_COMPLETE)}}function s(){Object.keys(r).forEach((function(t){r[t].reset()})),r={}}return e={reset:function(){i.off(o.MANIFEST_UPDATED,u,e),i.off(o.STREAM_TEARDOWN_COMPLETE,s,e)}},i.on(o.MANIFEST_UPDATED,u,e),i.on(o.STREAM_TEARDOWN_COMPLETE,s,e),e}Y.__dashjs_factory_name="MetricsCollectionController";var W=u.A.getClassFactory(Y);function z(){var t=this.context;return{createMetricsReporting:function(e){return c(t).getInstance({eventBus:e.eventBus,dashMetrics:e.dashMetrics,metricsConstants:e.metricsConstants,events:e.events}).initialize(),W(t).create(e)},getReportingFactory:function(){return T(t).getInstance()},getMetricsHandlerFactory:function(){return M(t).getInstance()}}}z.__dashjs_factory_name="MetricsReporting";var q=dashjs.FactoryMaker.getClassFactory(z);q.events=a.A,dashjs.FactoryMaker.updateClassFactory(z.__dashjs_factory_name,q);var K=q}(),n.default}()}));
//# sourceMappingURL=dash.reporting.min.js.map