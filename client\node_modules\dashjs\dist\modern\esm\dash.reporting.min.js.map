{"version": 3, "file": "dash.reporting.min.js", "mappings": "wBA0BA,SAASA,EAAWC,GAClB,GAAoB,iBAATA,EACT,MAAM,IAAIC,UAAU,mCAAqCC,KAAKC,UAAUH,GAE5E,CAGA,SAASI,EAAqBJ,EAAMK,GAMlC,IALA,IAIIC,EAJAC,EAAM,GACNC,EAAoB,EACpBC,GAAa,EACbC,EAAO,EAEFC,EAAI,EAAGA,GAAKX,EAAKY,SAAUD,EAAG,CACrC,GAAIA,EAAIX,EAAKY,OACXN,EAAON,EAAKa,WAAWF,OACpB,IAAa,KAATL,EACP,MAEAA,EAAO,EAAQ,CACjB,GAAa,KAATA,EAAmB,CACrB,GAAIG,IAAcE,EAAI,GAAc,IAATD,QAEpB,GAAID,IAAcE,EAAI,GAAc,IAATD,EAAY,CAC5C,GAAIH,EAAIK,OAAS,GAA2B,IAAtBJ,GAA8D,KAAnCD,EAAIM,WAAWN,EAAIK,OAAS,IAAsD,KAAnCL,EAAIM,WAAWN,EAAIK,OAAS,GAC1H,GAAIL,EAAIK,OAAS,EAAG,CAClB,IAAIE,EAAiBP,EAAIQ,YAAY,KACrC,GAAID,IAAmBP,EAAIK,OAAS,EAAG,EACb,IAApBE,GACFP,EAAM,GACNC,EAAoB,GAGpBA,GADAD,EAAMA,EAAIS,MAAM,EAAGF,IACKF,OAAS,EAAIL,EAAIQ,YAAY,KAEvDN,EAAYE,EACZD,EAAO,EACP,QACF,CACF,MAAO,GAAmB,IAAfH,EAAIK,QAA+B,IAAfL,EAAIK,OAAc,CAC/CL,EAAM,GACNC,EAAoB,EACpBC,EAAYE,EACZD,EAAO,EACP,QACF,CAEEL,IACEE,EAAIK,OAAS,EACfL,GAAO,MAEPA,EAAM,KACRC,EAAoB,EAExB,MACMD,EAAIK,OAAS,EACfL,GAAO,IAAMP,EAAKgB,MAAMP,EAAY,EAAGE,GAEvCJ,EAAMP,EAAKgB,MAAMP,EAAY,EAAGE,GAClCH,EAAoBG,EAAIF,EAAY,EAEtCA,EAAYE,EACZD,EAAO,CACT,MAAoB,KAATJ,IAA+B,IAAVI,IAC5BA,EAEFA,GAAQ,CAEZ,CACA,OAAOH,CACT,CAcA,IAAIU,EAAQ,CAEVC,QAAS,WAKP,IAJA,IAEIC,EAFAC,EAAe,GACfC,GAAmB,EAGdV,EAAIW,UAAUV,OAAS,EAAGD,IAAM,IAAMU,EAAkBV,IAAK,CACpE,IAAIX,EACAW,GAAK,EACPX,EAAOsB,UAAUX,SAELY,IAARJ,IACFA,EAAMK,QAAQL,OAChBnB,EAAOmB,GAGTpB,EAAWC,GAGS,IAAhBA,EAAKY,SAITQ,EAAepB,EAAO,IAAMoB,EAC5BC,EAA0C,KAAvBrB,EAAKa,WAAW,GACrC,CAQA,OAFAO,EAAehB,EAAqBgB,GAAeC,GAE/CA,EACED,EAAaR,OAAS,EACjB,IAAMQ,EAEN,IACAA,EAAaR,OAAS,EACxBQ,EAEA,GAEX,EAEAK,UAAW,SAAmBzB,GAG5B,GAFAD,EAAWC,GAES,IAAhBA,EAAKY,OAAc,MAAO,IAE9B,IAAIc,EAAoC,KAAvB1B,EAAKa,WAAW,GAC7Bc,EAAyD,KAArC3B,EAAKa,WAAWb,EAAKY,OAAS,GAQtD,OAHoB,KAFpBZ,EAAOI,EAAqBJ,GAAO0B,IAE1Bd,QAAiBc,IAAY1B,EAAO,KACzCA,EAAKY,OAAS,GAAKe,IAAmB3B,GAAQ,KAE9C0B,EAAmB,IAAM1B,EACtBA,CACT,EAEA0B,WAAY,SAAoB1B,GAE9B,OADAD,EAAWC,GACJA,EAAKY,OAAS,GAA4B,KAAvBZ,EAAKa,WAAW,EAC5C,EAEAe,KAAM,WACJ,GAAyB,IAArBN,UAAUV,OACZ,MAAO,IAET,IADA,IAAIiB,EACKlB,EAAI,EAAGA,EAAIW,UAAUV,SAAUD,EAAG,CACzC,IAAImB,EAAMR,UAAUX,GACpBZ,EAAW+B,GACPA,EAAIlB,OAAS,SACAW,IAAXM,EACFA,EAASC,EAETD,GAAU,IAAMC,EAEtB,CACA,YAAeP,IAAXM,EACK,IACFZ,EAAMQ,UAAUI,EACzB,EAEAE,SAAU,SAAkBC,EAAMC,GAIhC,GAHAlC,EAAWiC,GACXjC,EAAWkC,GAEPD,IAASC,EAAI,MAAO,GAKxB,IAHAD,EAAOf,EAAMC,QAAQc,OACrBC,EAAKhB,EAAMC,QAAQe,IAEF,MAAO,GAIxB,IADA,IAAIC,EAAY,EACTA,EAAYF,EAAKpB,QACa,KAA/BoB,EAAKnB,WAAWqB,KADYA,GASlC,IALA,IAAIC,EAAUH,EAAKpB,OACfwB,EAAUD,EAAUD,EAGpBG,EAAU,EACPA,EAAUJ,EAAGrB,QACa,KAA3BqB,EAAGpB,WAAWwB,KADUA,GAW9B,IAPA,IACIC,EADQL,EAAGrB,OACKyB,EAGhBzB,EAASwB,EAAUE,EAAQF,EAAUE,EACrCC,GAAiB,EACjB5B,EAAI,EACDA,GAAKC,IAAUD,EAAG,CACvB,GAAIA,IAAMC,EAAQ,CAChB,GAAI0B,EAAQ1B,EAAQ,CAClB,GAAmC,KAA/BqB,EAAGpB,WAAWwB,EAAU1B,GAG1B,OAAOsB,EAAGjB,MAAMqB,EAAU1B,EAAI,GACzB,GAAU,IAANA,EAGT,OAAOsB,EAAGjB,MAAMqB,EAAU1B,EAE9B,MAAWyB,EAAUxB,IACoB,KAAnCoB,EAAKnB,WAAWqB,EAAYvB,GAG9B4B,EAAgB5B,EACD,IAANA,IAGT4B,EAAgB,IAGpB,KACF,CACA,IAAIC,EAAWR,EAAKnB,WAAWqB,EAAYvB,GAE3C,GAAI6B,IADSP,EAAGpB,WAAWwB,EAAU1B,GAEnC,MACoB,KAAb6B,IACPD,EAAgB5B,EACpB,CAEA,IAAI8B,EAAM,GAGV,IAAK9B,EAAIuB,EAAYK,EAAgB,EAAG5B,GAAKwB,IAAWxB,EAClDA,IAAMwB,GAAkC,KAAvBH,EAAKnB,WAAWF,KAChB,IAAf8B,EAAI7B,OACN6B,GAAO,KAEPA,GAAO,OAMb,OAAIA,EAAI7B,OAAS,EACR6B,EAAMR,EAAGjB,MAAMqB,EAAUE,IAEhCF,GAAWE,EACoB,KAA3BN,EAAGpB,WAAWwB,MACdA,EACGJ,EAAGjB,MAAMqB,GAEpB,EAEAK,UAAW,SAAmB1C,GAC5B,OAAOA,CACT,EAEA2C,QAAS,SAAiB3C,GAExB,GADAD,EAAWC,GACS,IAAhBA,EAAKY,OAAc,MAAO,IAK9B,IAJA,IAAIN,EAAON,EAAKa,WAAW,GACvB+B,EAAmB,KAATtC,EACVuC,GAAO,EACPC,GAAe,EACVnC,EAAIX,EAAKY,OAAS,EAAGD,GAAK,IAAKA,EAEtC,GAAa,MADbL,EAAON,EAAKa,WAAWF,KAEnB,IAAKmC,EAAc,CACjBD,EAAMlC,EACN,KACF,OAGFmC,GAAe,EAInB,OAAa,IAATD,EAAmBD,EAAU,IAAM,IACnCA,GAAmB,IAARC,EAAkB,KAC1B7C,EAAKgB,MAAM,EAAG6B,EACvB,EAEAE,SAAU,SAAkB/C,EAAMgD,GAChC,QAAYzB,IAARyB,GAAoC,iBAARA,EAAkB,MAAM,IAAI/C,UAAU,mCACtEF,EAAWC,GAEX,IAGIW,EAHAsC,EAAQ,EACRJ,GAAO,EACPC,GAAe,EAGnB,QAAYvB,IAARyB,GAAqBA,EAAIpC,OAAS,GAAKoC,EAAIpC,QAAUZ,EAAKY,OAAQ,CACpE,GAAIoC,EAAIpC,SAAWZ,EAAKY,QAAUoC,IAAQhD,EAAM,MAAO,GACvD,IAAIkD,EAASF,EAAIpC,OAAS,EACtBuC,GAAoB,EACxB,IAAKxC,EAAIX,EAAKY,OAAS,EAAGD,GAAK,IAAKA,EAAG,CACrC,IAAIL,EAAON,EAAKa,WAAWF,GAC3B,GAAa,KAATL,GAGA,IAAKwC,EAAc,CACjBG,EAAQtC,EAAI,EACZ,KACF,OAEwB,IAAtBwC,IAGFL,GAAe,EACfK,EAAmBxC,EAAI,GAErBuC,GAAU,IAER5C,IAAS0C,EAAInC,WAAWqC,IACR,KAAZA,IAGJL,EAAMlC,IAKRuC,GAAU,EACVL,EAAMM,GAId,CAGA,OADIF,IAAUJ,EAAKA,EAAMM,GAAmC,IAATN,IAAYA,EAAM7C,EAAKY,QACnEZ,EAAKgB,MAAMiC,EAAOJ,EAC3B,CACE,IAAKlC,EAAIX,EAAKY,OAAS,EAAGD,GAAK,IAAKA,EAClC,GAA2B,KAAvBX,EAAKa,WAAWF,IAGhB,IAAKmC,EAAc,CACjBG,EAAQtC,EAAI,EACZ,KACF,OACkB,IAATkC,IAGXC,GAAe,EACfD,EAAMlC,EAAI,GAId,OAAa,IAATkC,EAAmB,GAChB7C,EAAKgB,MAAMiC,EAAOJ,EAE7B,EAEAO,QAAS,SAAiBpD,GACxBD,EAAWC,GAQX,IAPA,IAAIqD,GAAY,EACZC,EAAY,EACZT,GAAO,EACPC,GAAe,EAGfS,EAAc,EACT5C,EAAIX,EAAKY,OAAS,EAAGD,GAAK,IAAKA,EAAG,CACzC,IAAIL,EAAON,EAAKa,WAAWF,GAC3B,GAAa,KAATL,GASS,IAATuC,IAGFC,GAAe,EACfD,EAAMlC,EAAI,GAEC,KAATL,GAEkB,IAAd+C,EACFA,EAAW1C,EACY,IAAhB4C,IACPA,EAAc,IACK,IAAdF,IAGTE,GAAe,QArBb,IAAKT,EAAc,CACjBQ,EAAY3C,EAAI,EAChB,KACF,CAoBN,CAEA,OAAkB,IAAd0C,IAA4B,IAATR,GAEH,IAAhBU,GAEgB,IAAhBA,GAAqBF,IAAaR,EAAM,GAAKQ,IAAaC,EAAY,EACjE,GAEFtD,EAAKgB,MAAMqC,EAAUR,EAC9B,EAEAW,OAAQ,SAAgBC,GACtB,GAAmB,OAAfA,GAA6C,iBAAfA,EAChC,MAAM,IAAIxD,UAAU,0EAA4EwD,GAElG,OAvVJ,SAAiBC,EAAKD,GACpB,IAAIE,EAAMF,EAAWE,KAAOF,EAAWG,KACnCC,EAAOJ,EAAWI,OAASJ,EAAWK,MAAQ,KAAOL,EAAWT,KAAO,IAC3E,OAAKW,EAGDA,IAAQF,EAAWG,KACdD,EAAME,EAERF,EA8UU,IA9UEE,EALVA,CAMX,CA6UWE,CAAQ,EAAKN,EACtB,EAEAO,MAAO,SAAehE,GACpBD,EAAWC,GAEX,IAAIiE,EAAM,CAAEL,KAAM,GAAID,IAAK,GAAIE,KAAM,GAAIb,IAAK,GAAIc,KAAM,IACxD,GAAoB,IAAhB9D,EAAKY,OAAc,OAAOqD,EAC9B,IAEIhB,EAFA3C,EAAON,EAAKa,WAAW,GACvBa,EAAsB,KAATpB,EAEboB,GACFuC,EAAIL,KAAO,IACXX,EAAQ,GAERA,EAAQ,EAaV,IAXA,IAAII,GAAY,EACZC,EAAY,EACZT,GAAO,EACPC,GAAe,EACfnC,EAAIX,EAAKY,OAAS,EAIlB2C,EAAc,EAGX5C,GAAKsC,IAAStC,EAEnB,GAAa,MADbL,EAAON,EAAKa,WAAWF,KAUV,IAATkC,IAGFC,GAAe,EACfD,EAAMlC,EAAI,GAEC,KAATL,GAEkB,IAAd+C,EAAiBA,EAAW1C,EAA2B,IAAhB4C,IAAmBA,EAAc,IACrD,IAAdF,IAGXE,GAAe,QAlBb,IAAKT,EAAc,CACjBQ,EAAY3C,EAAI,EAChB,KACF,CAwCN,OArBkB,IAAd0C,IAA4B,IAATR,GAEP,IAAhBU,GAEgB,IAAhBA,GAAqBF,IAAaR,EAAM,GAAKQ,IAAaC,EAAY,GACvD,IAATT,IACiCoB,EAAIJ,KAAOI,EAAIH,KAAhC,IAAdR,GAAmB5B,EAAkC1B,EAAKgB,MAAM,EAAG6B,GAAgC7C,EAAKgB,MAAMsC,EAAWT,KAG7G,IAAdS,GAAmB5B,GACrBuC,EAAIH,KAAO9D,EAAKgB,MAAM,EAAGqC,GACzBY,EAAIJ,KAAO7D,EAAKgB,MAAM,EAAG6B,KAEzBoB,EAAIH,KAAO9D,EAAKgB,MAAMsC,EAAWD,GACjCY,EAAIJ,KAAO7D,EAAKgB,MAAMsC,EAAWT,IAEnCoB,EAAIjB,IAAMhD,EAAKgB,MAAMqC,EAAUR,IAG7BS,EAAY,EAAGW,EAAIN,IAAM3D,EAAKgB,MAAM,EAAGsC,EAAY,GAAY5B,IAAYuC,EAAIN,IAAM,KAElFM,CACT,EAEAP,IAAK,IACLQ,UAAW,IACXC,MAAO,KACPlD,MAAO,MAGTA,EAAMA,MAAQA,EAEdmD,EAAOC,QAAUpD,C,uBChhBjB,OAUA,SAAWqD,EAAQ/C,GASf,IAGIgD,EAAc,WACdC,EAAc,YACdC,EAAc,SACdC,EAAc,SACdC,EAAc,QACdC,EAAc,QACdC,EAAc,OACdC,EAAc,OACdC,EAAc,SACdC,EAAc,UACdC,EAAc,eACdC,EAAc,UACdC,EAAc,SACdC,EAAc,SACdC,EAAc,UACdC,EAAc,WACdC,EAAc,WAGdC,EAAU,SACVC,EAAU,QACVC,EAAU,OACVC,EAAa,aACbC,EAAU,UACVC,EAAU,SAEVC,EAAU,UACVC,EAAU,SACVC,EAAU,SACVC,EAAU,KACVC,EAAY,YACZC,EAAY,WACZC,EAAU,QACVC,EAAU,UACVC,EAAU,QACVC,EAAU,OACVC,EAAU,SACVC,EAAU,QACVC,EAAc,WACdC,EAAc,cACdC,EAAU,SAiBVC,EAAY,SAAUC,GAElB,IADA,IAAIC,EAAQ,CAAC,EACJpG,EAAE,EAAGA,EAAEmG,EAAIlG,OAAQD,IACxBoG,EAAMD,EAAInG,GAAGqG,eAAiBF,EAAInG,GAEtC,OAAOoG,CACX,EACAE,EAAM,SAAUC,EAAMC,GAClB,cAAcD,IAASxC,IAAuD,IAA5C0C,EAASD,GAAME,QAAQD,EAASF,GACtE,EACAE,EAAW,SAAUE,GACjB,OAAOA,EAAIC,aACf,EAIAC,EAAO,SAAUF,EAAKG,GAClB,UAAWH,IAAS5C,EAEhB,OADA4C,EAAMA,EAAII,QAAQ,SA7EZ,WA8EQD,IAASjD,EAAa8C,EAAMA,EAAIK,UAAU,EA3DhD,IA6DpB,EAMIC,EAAY,SAAUC,EAAIC,GAKtB,IAHA,IAAWC,EAAGC,EAAGC,EAAGC,EAAGC,EAASC,EAA5BzH,EAAI,EAGDA,EAAImH,EAAOlH,SAAWuH,GAAS,CAElC,IAAIE,EAAQP,EAAOnH,GACf2H,EAAQR,EAAOnH,EAAI,GAIvB,IAHAoH,EAAIC,EAAI,EAGDD,EAAIM,EAAMzH,SAAWuH,GAEnBE,EAAMN,IAGX,GAFAI,EAAUE,EAAMN,KAAKQ,KAAKV,GAGtB,IAAKI,EAAI,EAAGA,EAAIK,EAAM1H,OAAQqH,IAC1BG,EAAQD,IAAUH,UAClBE,EAAII,EAAML,MAEOxD,GAAYyD,EAAEtH,OAAS,EACnB,IAAbsH,EAAEtH,cACSsH,EAAE,IAAM3D,EAEfiE,KAAKN,EAAE,IAAMA,EAAE,GAAGO,KAAKD,KAAMJ,GAG7BI,KAAKN,EAAE,IAAMA,EAAE,GAEC,IAAbA,EAAEtH,cAEEsH,EAAE,KAAO3D,GAAe2D,EAAE,GAAGK,MAAQL,EAAE,GAAGQ,KAKjDF,KAAKN,EAAE,IAAME,EAAQA,EAAMV,QAAQQ,EAAE,GAAIA,EAAE,IAAM3G,EAHjDiH,KAAKN,EAAE,IAAME,EAAQF,EAAE,GAAGO,KAAKD,KAAMJ,EAAOF,EAAE,IAAM3G,EAKpC,IAAb2G,EAAEtH,SACL4H,KAAKN,EAAE,IAAME,EAAQF,EAAE,GAAGO,KAAKD,KAAMJ,EAAMV,QAAQQ,EAAE,GAAIA,EAAE,KAAO3G,GAG1EiH,KAAKN,GAAKE,GAAgB7G,EAK1CZ,GAAK,CACT,CACJ,EAEAgI,EAAY,SAAUrB,EAAKsB,GAEvB,IAAK,IAAIjI,KAAKiI,EAEV,UAAWA,EAAIjI,KAAO8D,GAAYmE,EAAIjI,GAAGC,OAAS,GAC9C,IAAK,IAAImH,EAAI,EAAGA,EAAIa,EAAIjI,GAAGC,OAAQmH,IAC/B,GAAId,EAAI2B,EAAIjI,GAAGoH,GAAIT,GACf,MAjJN,MAiJc3G,EAAiBY,EAAYZ,OAG1C,GAAIsG,EAAI2B,EAAIjI,GAAI2G,GACnB,MArJE,MAqJM3G,EAAiBY,EAAYZ,EAG7C,OAAO2G,CACf,EAiBIuB,EAAoB,CAChB,GAAc,OACd,UAAc,SACd,SAAc,QACd,IAAc,SACd,GAAc,CAAC,SAAU,UACzB,MAAc,SACd,EAAc,SACd,EAAc,SACd,IAAc,SACd,GAAc,CAAC,SAAU,WACzB,GAAc,OAOlBC,EAAU,CAEVC,QAAU,CAAC,CAEP,gCACG,CAAC/D,EAAS,CAACH,EAAM,WAAY,CAChC,+BACG,CAACG,EAAS,CAACH,EAAM,SAAU,CAG9B,4BACA,mDACA,2CACG,CAACA,EAAMG,GAAU,CACpB,yBACG,CAACA,EAAS,CAACH,EAAMuB,EAAM,UAAW,CACrC,4BACG,CAACpB,EAAS,CAACH,EAAMuB,EAAM,QAAS,CACnC,qBACG,CAACpB,EAAS,CAACH,EAAMuB,IAAS,CAG7B,0DACG,CAACpB,EAAS,CAACH,EAAM,UAAW,CAC/B,uBACA,8DAEA,uDACA,2BAGA,+LAEA,kCACA,uBACG,CAACA,EAAMG,GAAU,CACpB,qBACG,CAACA,EAAS,CAACH,EAAM,eAAgB,CACpC,qDACG,CAACG,EAAS,CAACH,EAAM,KAAKe,IAAW,CACpC,+BACA,+BACA,8BACG,CAACZ,EAAS,CAACH,EAAM,WAAY,CAChC,yBACG,CAACG,EAAS,CAACH,EAAM,cAAe,CACnC,+CACG,CAACG,EAAS,CAACH,EAAM,OAAQ,CAC5B,oCACG,CAACG,EAAS,CAACH,EAAM,WAAY,CAChC,yBACG,CAACG,EAAS,CAACH,EAAM,gBAAgBe,IAAW,CAC/C,2BACG,CAAC,CAACf,EAAM,OAAQ,aAAae,GAAUZ,GAAU,CACpD,uBACG,CAACA,EAAS,CAACH,EAAMiB,EAAQ,WAAY,CACxC,qBACG,CAACd,EAAS,CAACH,EAAMuB,EAAM,WAAY,CACtC,0BACG,CAACpB,EAAS,CAACH,EAAM,YAAa,CACjC,sBACG,CAACG,EAAS,CAACH,EAAM,YAAa,CACjC,qBACG,CAACG,EAAS,CAACH,EAAMuB,EAAM,WAAY,CACtC,2BACG,CAACpB,EAAS,CAACH,EAAM,QAAQe,IAAW,CACvC,sBACG,CAACZ,EAAS,CAACH,EAAMiB,IAAW,CAC/B,iCACG,CAAC,CAACjB,EAAM,OAASe,IAAW,CAC/B,oDACG,CAAC,CAACf,EAAM,OAAQ,MAAQe,GAAUZ,GAAU,CAC/C,8BACG,CAACA,EAAS,CAACH,EAAMwB,EAAU,cAAe,CAC7C,+BACG,CAAC,CAACxB,EAAM,KAAM,KAAMG,GAAU,CACjC,0BACG,CAACA,EAAS,CAACH,EAAM,mBAAoB,CACxC,4BACG,CAAC,CAACA,EAAM,gBAAiBG,GAAU,CACtC,gCACA,iDACA,8CACG,CAACH,EAAMG,GAAU,CACpB,eACA,sBACG,CAACH,GAAO,CAGX,+DACG,CAAC,CAACA,EAAM6B,GAAW1B,GAAU,CAChC,uBACA,uCACA,kCACA,4BACA,4BACA,6BACA,qCACA,iDACG,CAACH,EAAMG,GAAU,CACpB,gCACG,CAACA,EAAS,CAACH,EAAM,QAAS,CAC7B,8CACG,CAACG,EAAS,CAACH,EAAM,WAAY,CAEhC,oCACG,CAACG,EAAS,CAACH,EAAMgB,EAAO,cAAe,CAE1C,+BACG,CAAC,CAAChB,EAAMgB,EAAO,YAAab,GAAU,CAEzC,2DACG,CAACA,EAAS,CAACH,EAAM,WAAWe,IAAW,CAE1C,+DACG,CAACf,EAAMG,GAAU,CAEpB,gDACG,CAACA,EAAS,CAACH,EAAM,kBAAmB,CACvC,sDACG,CAACG,EAASH,GAAO,CACpB,gDACG,CAACA,EAAM,CAACG,EAAS2D,EAtJT,CACX,MAAU,KACV,IAAU,KACV,IAAU,KACV,MAAU,OACV,QAAU,OACV,QAAU,OACV,QAAU,OACV,IAAU,OA8IqC,CAE/C,8BACG,CAAC9D,EAAMG,GAAU,CAGpB,wCACG,CAAC,CAACH,EAAM,YAAaG,GAAU,CAClC,uCACG,CAACA,EAAS,CAACH,EAAMiB,EAAQ,aAAc,CAC1C,6BACA,cACA,mGAEA,+FAEA,wBACA,2CAGA,wHAEA,uBACA,sBACG,CAACjB,EAAMG,GAAU,CAEpB,wBACG,CAACH,EAAM,CAACG,EAAS,eAAgB,MAGxCgE,IAAM,CAAC,CAEH,iDACG,CAAC,CAAC/D,EAAc,UAAW,CAE9B,gBACG,CAAC,CAACA,EAAcmC,IAAY,CAE/B,0BACG,CAAC,CAACnC,EAAc,SAAU,CAE7B,oCACG,CAAC,CAACA,EAAc,UAAW,CAE9B,mCACG,CAAC,CAACA,EAAc,UAAW,CAG9B,8BACG,CAAC,CAACA,EAAc,QAAS,CAE5B,0CACG,CAAC,CAACA,EAAc,OA3WT,GA2WwBmC,IAAY,CAE9C,kBACG,CAAC,CAACnC,EAAc,UAAW,CAE9B,2HAEG,CAAC,CAACA,EAAcmC,KAGvB6B,OAAS,CAAC,CAON,mFACG,CAACrE,EAAO,CAACG,EAAQsB,GAAU,CAACvB,EAAMM,IAAU,CAC/C,yDACA,uBACA,iBACG,CAACR,EAAO,CAACG,EAAQsB,GAAU,CAACvB,EAAMK,IAAU,CAG/C,4CACG,CAACP,EAAO,CAACG,EAAQU,GAAQ,CAACX,EAAMK,IAAU,CAC7C,6BACA,oCACA,kCACG,CAACP,EAAO,CAACG,EAAQU,GAAQ,CAACX,EAAMM,IAAU,CAC7C,iBACG,CAACR,EAAO,CAACG,EAAQU,IAAS,CAG7B,iCACG,CAACb,EAAO,CAACG,EAAQuB,GAAQ,CAACxB,EAAMK,IAAU,CAG7C,+DACG,CAACP,EAAO,CAACG,EAAQiB,GAAS,CAAClB,EAAMM,IAAU,CAC9C,kCACA,sEACG,CAACR,EAAO,CAACG,EAAQiB,GAAS,CAAClB,EAAMK,IAAU,CAG9C,kDACA,yBACA,uCACA,iDACA,4DACA,yGACG,CAAC,CAACP,EAAO,KAAM,KAAM,CAACG,EAAQyB,GAAS,CAAC1B,EAAMK,IAAU,CAC3D,+CACA,8CACE,CAAC,CAACP,EAAO,KAAM,KAAM,CAACG,EAAQyB,GAAS,CAAC1B,EAAMM,IAAU,CAG1D,sBACA,mEACG,CAACR,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMK,IAAU,CAC9C,wBACG,CAACP,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMM,IAAU,CAG9C,yBACA,oCACG,CAACR,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMK,IAAU,CAG9C,mCACG,CAACP,EAAO,CAACG,EAAQ,UAAW,CAACD,EAAMK,IAAU,CAGhD,iFACA,4BACA,sDACG,CAACP,EAAO,CAACG,EAAQoB,GAAW,CAACrB,EAAMK,IAAU,CAChD,qCACG,CAACP,EAAO,CAACG,EAAQoB,GAAW,CAACrB,EAAMM,IAAU,CAGhD,iEACG,CAACR,EAAO,CAACG,EAAQkB,GAAK,CAACnB,EAAMM,IAAU,CAC1C,sDACA,oDACA,wBACG,CAACR,EAAO,CAACG,EAAQkB,GAAK,CAACnB,EAAMK,IAAU,CAG1C,oBACA,qEACG,CAACP,EAAO,CAACG,EAAQ,UAAW,CAACD,EAAMM,IAAU,CAGhD,qCACA,0BACG,CAAC,CAACR,EAAO,KAAM,KAAM,CAACG,EAAQ,SAAU,CAACD,EAAMK,IAAU,CAG5D,gBACG,CAACP,EAAO,CAACG,EAAQgB,GAAS,CAACjB,EAAMM,IAAU,CAC9C,6CACG,CAACR,EAAO,CAACG,EAAQgB,GAAS,CAACjB,EAAMK,IAAU,CAG9C,0GACG,CAACP,EAAO,CAACG,EAAQwB,GAAO,CAACzB,EAAMK,IAAU,CAC5C,oBACA,iCACG,CAAC,CAACP,EAAO,iBAAkB,CAACG,EAAQwB,GAAO,CAACzB,EAAMM,IAAU,CAG/D,sCACA,0CACG,CAACR,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMK,IAAU,CAGjD,eACA,uCACA,gCACG,CAACP,EAAO,CAACG,EAAQS,GAAS,CAACV,EAAMM,IAAU,CAC9C,iDACG,CAAC,CAACR,EAAO,QAAS,iBAAkB,CAACG,EAAQS,GAAS,CAACV,EAAMK,IAAU,CAG1E,gCACG,CAACP,EAAOG,EAAQ,CAACD,EAAMM,IAAU,CACpC,gCACA,kBACG,CAACR,EAAO,CAACG,EAAQY,GAAa,CAACb,EAAMK,IAAU,CAGlD,qFACG,CAACP,EAAO,CAACG,EAAQW,GAAO,CAACZ,EAAMM,IAAU,CAC5C,iDACG,CAACR,EAAO,CAACG,EAAQW,GAAO,CAACZ,EAAMK,IAAU,CAG5C,cACG,CAACP,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMM,IAAU,CAC7C,2CAGA,oCACA,iFACG,CAACL,EAAQ,CAACH,EAAO,KAAM,KAAM,CAACE,EAAMK,IAAU,CAGjD,uCACG,CAACP,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMM,IAAU,CAG9C,8BACA,qBACG,CAACR,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMK,IAAU,CAG/C,kDACG,CAACP,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMK,IAAU,CAGjD,gHAEA,mBACA,iBACA,8BACA,0BACA,WACA,yBACG,CAACJ,EAAQH,EAAO,CAACE,EAAMK,IAAU,CAEpC,2BACA,wBACA,uCACA,uBACA,4BACA,iCACA,kCACA,8BACA,gCACA,mCACG,CAACJ,EAAQH,EAAO,CAACE,EAAMM,IAAU,CAEpC,kBACG,CAACR,EAAO,CAACG,EAAQmB,GAAY,CAACpB,EAAMM,IAAU,CACjD,qCACG,CAACR,EAAO,CAACG,EAAQ,aAAc,CAACD,EAAMK,IAAU,CACnD,aACG,CAACP,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMK,IAAU,CAC9C,gBACG,CAACP,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMK,IAAU,CACjD,iBACG,CAACP,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMM,IAAU,CAC7C,0BACG,CAACR,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMM,IAAU,CAC9C,wBACG,CAACR,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMM,IAAU,CACjD,+CACG,CAACR,EAAO,CAACG,EAAQ,kBAAmB,CAACD,EAAMM,IAAU,CACxD,qBACG,CAACR,EAAO,CAACG,EAAQ,YAAa,CAACD,EAAMM,IAAU,CAClD,cACG,CAACR,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMM,IAAU,CAC7C,mBACG,CAACR,EAAO,CAACG,EAAQ,OAAQ,CAACD,EAAMK,IAAU,CAC7C,wBACG,CAACP,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMK,IAAU,CAC/C,mBACG,CAACP,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMM,IAAU,CAC/C,wBACG,CAACR,EAAO,CAACG,EAAQ,QAAS,CAACD,EAAMM,IAAU,CAC9C,mBACA,sCACG,CAAC,CAACL,EAAQ,gBAAiBH,EAAO,CAACE,EAAMM,IAAU,CACtD,sBACG,CAACR,EAAO,CAACG,EAAQ,YAAa,CAACD,EAAMM,IAAU,CAClD,8BACG,CAACR,EAAO,CAACG,EAAQ,YAAa,CAACD,EAAMM,IAAU,CAClD,oDACG,CAAC,CAACL,EAAQ,SAAUH,EAAO,CAACE,EAAMK,IAAU,CAC/C,2BACG,CAAC,CAACJ,EAAQ,SAAUH,EAAO,CAACE,EAAMK,IAAU,CAC/C,cACG,CAACP,EAAO,CAACG,EAAQ,aAAc,CAACD,EAAMK,IAAU,CACnD,uCACG,CAACP,EAAO,CAACG,EAAQ,WAAY,CAACD,EAAMM,IAAU,CACjD,wBACG,CAACR,EAAO,CAACG,EAAQ,aAAc,CAACD,EAAMM,IAAU,CACnD,kBACG,CAACR,EAAO,CAACG,EAAQ,SAAU,CAACD,EAAMM,IAAU,CAC/C,qBACG,CAACR,EAAO,CAACG,EAAQ,UAAW,CAACD,EAAMM,IAAU,CAChD,mBACG,CAACL,EAAQH,EAAO,CAACE,EAAMK,IAAU,CACpC,sBACG,CAAC,CAACP,EAAO,MAAO,KAAM,CAACG,EAAQmB,GAAY,CAACpB,EAAMK,IAAU,CAC/D,yDACG,CAACP,EAAO,CAACG,EAAQ0B,GAAQ,CAAC3B,EAAMM,IAAU,CAC7C,yCACG,CAACR,EAAO,CAACG,EAAQ0B,GAAQ,CAAC3B,EAAMK,IAAU,CAM7C,wBACG,CAACJ,EAAQ,CAACD,EAAMO,IAAW,CAC9B,uBACG,CAAC,CAACT,EAAO,IAAK,WAAY,CAACG,EAAQsB,GAAU,CAACvB,EAAMO,IAAW,CAClE,8DACG,CAAC,CAACN,EAAQkB,GAAK,CAACnB,EAAMO,IAAW,CACpC,gBACG,CAACN,EAAQ,CAACH,EAAOa,EAAM,OAAQ,CAACX,EAAMO,IAAW,CACpD,UACG,CAAC,CAACT,EAAOiB,EAAO,QAAS,CAACd,EAAQgB,GAAS,CAACjB,EAAMO,IAAW,CAChE,6BACG,CAACT,EAAO,CAACG,EAAQS,GAAS,CAACV,EAAMO,IAAW,CAC/C,uBACA,uBACG,CAACT,EAAO,CAACG,EAAQuB,GAAQ,CAACxB,EAAMO,IAAU,CAC7C,4BACG,CAACT,EAAO,CAACG,EAAQwB,GAAO,CAACzB,EAAMO,IAAW,CAC7C,qBACG,CAACT,EAAO,CAACG,EAAQyB,GAAS,CAAC1B,EAAMO,IAAW,CAC/C,6BACG,CAACN,EAAQH,EAAO,CAACE,EAAMO,IAAW,CACrC,0CACA,6DACG,CAAC,CAACN,EAAQyC,GAAO,CAAC5C,EAAO4C,GAAO,CAAC1C,EAAMO,IAAW,CACrD,mDACG,CAAC,CAACP,EAAMO,IAAW,CAMtB,UACA,8BACG,CAACN,EAAQH,EAAO,CAACE,EAAMI,IAAW,CACrC,0BACG,CAACN,EAAO,CAACG,EAAQ,UAAW,CAACD,EAAMI,IAAW,CACjD,mCACG,CAACN,EAAO,CAACG,EAAQwB,GAAO,CAACzB,EAAMI,IAAW,CAC7C,sCACG,CAACN,EAAO,CAACG,EAAQmB,GAAY,CAACpB,EAAMI,IAAW,CAMlD,kBACG,CAACH,EAAQH,EAAO,CAACE,EAAMQ,IAAY,CACtC,wCACG,CAACV,EAAO,CAACG,EAAQU,GAAQ,CAACX,EAAMQ,IAAY,CAC/C,wBACG,CAACV,EAAO,CAACG,EAAQgB,GAAS,CAACjB,EAAMQ,IAAY,CAChD,6BACG,CAACV,EAAO,CAACG,EAAQ0B,GAAQ,CAAC3B,EAAMQ,IAAY,CAC/C,uBACG,CAACV,EAAO,CAACG,EAAQ2B,GAAW,CAAC5B,EAAMQ,IAAY,CAMlD,wCACG,CAACP,EAAQ,CAACD,EAAMS,IAAY,CAC/B,cACG,CAACX,EAAO,CAACG,EAAQS,GAAS,CAACV,EAAMS,IAAY,CAMhD,kEACG,CAACX,EAAO,CAACE,EAAMK,IAAU,CAC5B,+DACG,CAACP,EAAO,CAACE,EAAMM,IAAU,CAC5B,gDACG,CAAC,CAACN,EAAMM,IAAU,CACrB,kEACG,CAAC,CAACN,EAAMK,IAAU,CACrB,kCACG,CAACP,EAAO,CAACG,EAAQ,aAGxBmE,OAAS,CAAC,CAEN,8BACG,CAAClE,EAAS,CAACH,EAAMsE,aAAe,CAEnC,6CACG,CAACnE,EAAS,CAACH,EAAM,UAAW,CAE/B,uBACA,sEACA,0BACA,yCACA,8BACA,eACG,CAACA,EAAMG,GAAU,CAEpB,iCACG,CAACA,EAASH,IAGjBuE,GAAK,CAAC,CAGF,mCACG,CAACvE,EAAMG,GAAU,CACpB,yDACG,CAACH,EAAM,CAACG,EAAS2D,EAAWE,IAAqB,CACpD,0BACA,2CACA,wCACG,CAAC,CAAC7D,EAAS2D,EAAWE,GAAoB,CAAChE,EAAM,YAAa,CAGjE,sDACA,4CACA,wBACG,CAAC,CAACG,EAAS,KAAM,KAAM,CAACH,EAAM,QAAS,CAC1C,0BACA,yCACG,CAAC,CAACA,EAAM+B,GAAS,CAAC5B,EAAS,KAAM,MAAO,CAG3C,kDACG,CAACA,EAASH,GAAO,CACpB,+EACA,8BACA,+BACA,kBACG,CAACA,EAAMG,GAAU,CACpB,cACG,CAACA,EAAS,CAACH,EAAMc,IAAc,CAClC,6DACG,CAACX,EAAS,CAACH,EAAM,YAAa,CACjC,mFACG,CAACG,EAAS,CAACH,EAAMiB,EAAQ,QAAS,CACrC,kBACA,wCACG,CAACd,EAAS,CAACH,EAAM,UAAW,CAC/B,wCACG,CAACG,EAAS,CAACH,EAAM,YAAa,CAGjC,qBACG,CAACG,EAAS,CAACH,EAAMgB,EAAO,SAAU,CACrC,oCACG,CAAC,CAAChB,EAAM8B,GAAc3B,GAAS,CAGlC,qBACA,iBACA,2BAGA,mDACA,2BAGA,wCACA,yBACA,4BACA,8SAEA,2BACA,oBACA,6EACA,kBACG,CAACH,EAAMG,GAAU,CACpB,yBACG,CAAC,CAACH,EAAM,WAAYG,GAAU,CACjC,sCACA,kCACA,mEACA,sBACG,CAACH,EAAMG,KAQdqE,EAAW,SAAUxB,EAAIyB,GAOzB,UALWzB,IAAOpD,IACd6E,EAAazB,EACbA,EAAKtG,KAGHiH,gBAAgBa,GAClB,OAAO,IAAIA,EAASxB,EAAIyB,GAAYC,YAGxC,IAAIC,SAAqBlF,IAAWE,GAAcF,EAAOmF,UAAanF,EAAOmF,UAAYlI,EACrFmI,EAAM7B,IAAQ2B,GAAcA,EAAWG,UAAaH,EAAWG,UAnyBrD,IAoyBVC,EAASJ,GAAcA,EAAWK,cAAiBL,EAAWK,cAAgBtI,EAC9EuI,EAAUR,EArvBL,SAAUR,EAASQ,GACxB,IAAIS,EAAgB,CAAC,EACrB,IAAK,IAAIpJ,KAAKmI,EACNQ,EAAW3I,IAAM2I,EAAW3I,GAAGC,OAAS,GAAM,EAC9CmJ,EAAcpJ,GAAK2I,EAAW3I,GAAGqJ,OAAOlB,EAAQnI,IAEhDoJ,EAAcpJ,GAAKmI,EAAQnI,GAGnC,OAAOoJ,CACX,CA2uB2BE,CAAOnB,EAASQ,GAAcR,EACrDoB,EAAaV,GAAcA,EAAWG,WAAaD,EAyEvD,OAvEAlB,KAAK2B,WAAa,WACd,IAjuBiBC,EAiuBbC,EAAW,CAAC,EAShB,OARAA,EAASxF,GAAQtD,EACjB8I,EAASrF,GAAWzD,EACpBqG,EAAUa,KAAK4B,EAAUX,EAAKI,EAAQf,SACtCsB,EAAS1F,UAruBQyF,EAquBUC,EAASrF,MApuBTN,EAAW0F,EAAQ1C,QAAQ,WAzE5C,IAyE+D4C,MAAM,KAAK,GAAK/I,EAsuBrF2I,GAAcV,GAAcA,EAAWe,cAAgBf,EAAWe,MAAMC,SAAWjG,IACnF8F,EAASxF,GAAQ,SAEdwF,CACX,EACA7B,KAAKiC,OAAS,WACV,IAAIC,EAAO,CAAC,EAGZ,OAFAA,EAAKzF,GAAgB1D,EACrBqG,EAAUa,KAAKiC,EAAMhB,EAAKI,EAAQd,KAC3B0B,CACX,EACAlC,KAAKmC,UAAY,WACb,IAAIC,EAAU,CAAC,EAaf,OAZAA,EAAQ7F,GAAUxD,EAClBqJ,EAAQhG,GAASrD,EACjBqJ,EAAQ9F,GAAQvD,EAChBqG,EAAUa,KAAKmC,EAASlB,EAAKI,EAAQb,QACjCiB,IAAeU,EAAQ9F,IAAS8E,GAASA,EAAMiB,SAC/CD,EAAQ9F,GAAQK,GAGhB+E,GAAgC,aAAlBU,EAAQhG,IAAyB4E,UAAqBA,EAAWsB,aAAetG,GAAcgF,EAAWuB,gBAAkBvB,EAAWuB,eAAiB,IACrKH,EAAQhG,GAAS,OACjBgG,EAAQ9F,GAAQM,GAEbwF,CACX,EACApC,KAAKwC,UAAY,WACb,IAAIC,EAAU,CAAC,EAIf,OAHAA,EAAQpG,GAAQtD,EAChB0J,EAAQjG,GAAWzD,EACnBqG,EAAUa,KAAKwC,EAASvB,EAAKI,EAAQZ,QAC9B+B,CACX,EACAzC,KAAK0C,MAAQ,WACT,IAAIC,EAAM,CAAC,EASX,OARAA,EAAItG,GAAQtD,EACZ4J,EAAInG,GAAWzD,EACfqG,EAAUa,KAAK0C,EAAKzB,EAAKI,EAAQV,IAC7Bc,IAAeiB,EAAItG,IAAS+E,GAASA,EAAMwB,UAA8B,WAAlBxB,EAAMwB,WAC7DD,EAAItG,GAAQ+E,EAAMwB,SACG1D,QAAQ,aAAcf,GACtBe,QAAQ,SAAUd,IAEpCuE,CACX,EACA3C,KAAKe,UAAY,WACb,MAAO,CACH1B,GAAUW,KAAK6C,QACftC,QAAUP,KAAK2B,aACfjB,OAAUV,KAAKwC,YACf5B,GAAUZ,KAAK0C,QACfjC,OAAUT,KAAKmC,YACf3B,IAAUR,KAAKiC,SAEvB,EACAjC,KAAK6C,MAAQ,WACT,OAAO3B,CACX,EACAlB,KAAK8C,MAAQ,SAAUzD,GAEnB,OADA6B,SAAc7B,IAAOnD,GAAYmD,EAAGjH,OAx1BxB,IAw1BkD4G,EAAKK,EAx1BvD,KAw1B4EA,EACjFW,IACX,EACAA,KAAK8C,MAAM5B,GACJlB,IACX,EAEAa,EAASrE,QAn3BS,SAo3BlBqE,EAASzD,QAAWiB,EAAU,CAAChC,EAAMG,EAASL,IAC9C0E,EAASkC,IAAM1E,EAAU,CAAC5B,IAC1BoE,EAASmC,OAAS3E,EAAU,CAACjC,EAAOG,EAAQD,EAAMI,EAASC,EAAQE,EAASD,EAAQE,EAAUC,IAC9F8D,EAASoC,OAASpC,EAASqC,GAAK7E,EAAU,CAAChC,EAAMG,WAOtCX,IAAaG,GAEgBJ,EAAOC,UACvCA,EAAUD,EAAOC,QAAUgF,GAE/BhF,EAAQgF,SAAWA,GAGiBsC,EAAAA,MAChCA,EAAAA,WACI,OAAOtC,CACV,2CACa/E,IAAWE,IAEzBF,EAAO+E,SAAWA,GAS1B,IAAIuC,SAAWtH,IAAWE,IAAeF,EAAOuH,QAAUvH,EAAOwH,OACjE,GAAIF,IAAMA,EAAE/D,GAAI,CACZ,IAAIkE,GAAS,IAAI1C,EACjBuC,EAAE/D,GAAKkE,GAAOxC,YACdqC,EAAE/D,GAAGmE,IAAM,WACP,OAAOD,GAAOV,OAClB,EACAO,EAAE/D,GAAGoE,IAAM,SAAUpE,GACjBkE,GAAOT,MAAMzD,GACb,IAAIqE,EAASH,GAAOxC,YACpB,IAAK,IAAI4C,KAAQD,EACbN,EAAE/D,GAAGsE,GAAQD,EAAOC,EAE5B,CACJ,CAEH,CA96BD,CA86BqB,iBAAX7H,OAAsBA,OAASkE,K,8DC14BzC,SAAS4D,EAAMC,GAEXA,EAASA,GAAU,CAAC,EACpB,MAAMC,EAAU9D,KAAK8D,QACfC,GAAWC,EAAAA,EAAAA,SAASF,GAASG,cAC7BC,EAAWL,EAAOK,SAElBC,EAAQ,GAEd,IAAIC,EACAC,EACAC,EACAC,EAgBJ,SAASC,EAASC,GACd,OAAIA,GAAMA,EAAGC,KACFD,EAAGC,KAAK5I,OAAO6I,SAGnB7I,OAAO6I,QAAQC,IAAIF,KAAK5I,OAAO6I,QAC1C,CA0CA,SAASE,IAAiB,QAAAC,EAAAhM,UAAAV,OAAR2M,EAAM,IAAAC,MAAAF,GAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAANF,EAAME,GAAAnM,UAAAmM,GACpBC,EAxFgB,EAwFOlF,QAAS+E,EACpC,CAEA,SAASI,IAAiB,QAAAC,EAAAtM,UAAAV,OAAR2M,EAAM,IAAAC,MAAAI,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAANN,EAAMM,GAAAvM,UAAAuM,GACpBH,EA3FgB,EA2FOlF,QAAS+E,EACpC,CAEA,SAASO,IAAgB,QAAAC,EAAAzM,UAAAV,OAAR2M,EAAM,IAAAC,MAAAO,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAANT,EAAMS,GAAA1M,UAAA0M,GACnBN,EA9FkB,EA8FOlF,QAAS+E,EACtC,CAEA,SAASU,IAAgB,QAAAC,EAAA5M,UAAAV,OAAR2M,EAAM,IAAAC,MAAAU,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAANZ,EAAMY,GAAA7M,UAAA6M,GACnBT,EAjGe,EAiGOlF,QAAS+E,EACnC,CAEA,SAASa,IAAiB,QAAAC,EAAA/M,UAAAV,OAAR2M,EAAM,IAAAC,MAAAa,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAANf,EAAMe,GAAAhN,UAAAgN,GACpBZ,EApGgB,EAoGOlF,QAAS+E,EACpC,CAEA,SAASG,EAAMa,EAAOC,GAClB,IAAIC,EAAU,GACVC,EAAU,KAEV7B,IACA6B,GAAU,IAAIC,MAAOC,UACrBH,GAAW,KAAOC,EAAU3B,GAAa,KAGzCD,GAAkB0B,GAASA,EAAMK,eACjCJ,GAAW,IAAMD,EAAMK,eAAiB,IACpCL,EAAMM,UACNL,GAAW,IAAMD,EAAMM,UAAY,MAIvCL,EAAQ7N,OAAS,IACjB6N,GAAW,KACd,QAAAM,EAAAzN,UAAAV,OAlB2B2M,EAAM,IAAAC,MAAAuB,EAAA,EAAAA,EAAA,KAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAANzB,EAAMyB,EAAA,GAAA1N,UAAA0N,GAoBlCxB,MAAMyB,MAAM,KAAM1B,GAAQ2B,SAAQ,SAAUC,GACxCV,GAAWU,EAAO,GACtB,IAGIxC,EAAM4B,IAAU7B,GAAYA,EAASV,MAAMoC,MAAMgB,UAAYb,GAC7D5B,EAAM4B,GAAOE,GAIb/B,GAAYA,EAASV,MAAMoC,MAAMiB,eACjC9C,EAAS+C,QAAQC,EAAAA,QAAOC,IAAK,CAAEf,QAASA,EAASF,MAAOA,GAEhE,CAUA,OARA3B,EAAW,CACP6C,UAxFJ,SAAmB7C,GACf,MAAO,CACHS,MAAOA,EAAMH,KAAKN,GAClBe,MAAOA,EAAMT,KAAKN,GAClBkB,KAAMA,EAAKZ,KAAKN,GAChBqB,KAAMA,EAAKf,KAAKN,GAChBwB,MAAOA,EAAMlB,KAAKN,GAE1B,EAiFI8C,uBAxEJ,SAAgCC,GAC5B9C,EAAmB8C,CACvB,EAuEIC,qBA9DJ,SAA8BD,GAC1B7C,EAAiB6C,CACrB,GA3DI9C,GAAmB,EACnBC,GAAiB,EACjBC,GAAY,IAAI4B,MAAOC,UAED,oBAAXtK,QAA0BA,OAAO6I,UACxCR,EA/BY,GA+BaK,EAAS1I,OAAO6I,QAAQQ,OACjDhB,EA/BY,GA+BaK,EAAS1I,OAAO6I,QAAQQ,OACjDhB,EA/Bc,GA+BaK,EAAS1I,OAAO6I,QAAQW,MACnDnB,EA/BW,GA+BaK,EAAS1I,OAAO6I,QAAQc,MAChDtB,EA/BY,GA+BaK,EAAS1I,OAAO6I,QAAQiB,QAmHlDxB,CACX,CAEAR,EAAMyD,sBAAwB,QAE9B,MAAMC,EAAUC,EAAAA,QAAaC,oBAAoB5D,GACjD0D,EAAQG,eA7Je,EA8JvBH,EAAQI,gBA7JgB,EA8JxBJ,EAAQK,gBA7JgB,EA8JxBL,EAAQM,kBA7JkB,EA8J1BN,EAAQO,eA7Je,EA8JvBP,EAAQQ,gBA7JgB,EA8JxBP,EAAAA,QAAaQ,uBAAuBnE,EAAMyD,sBAAuBC,GACjE,W,qDClKA,SAAStD,IAEL,IAAIgE,EAAW,CAAC,EAEhB,SAASC,EAAUC,EAAMC,EAAUC,GAA8C,IAAvCC,EAAOvP,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,EAAGwP,EAAexP,UAAAV,OAAA,QAAAW,IAAAD,UAAA,IAAAA,UAAA,GAEnE,IAAKoP,EACD,MAAM,IAAIK,MAAM,0CAEpB,IAAKJ,GAAkC,mBAAdA,EACrB,MAAM,IAAII,MAAM,gCAAkCJ,GAGtD,IAAIK,EAAWH,EAAQG,UAhBJ,EAkBnB,GAAIC,EAAcP,EAAMC,EAAUC,IAAU,EACxC,OAGJJ,EAASE,GAAQF,EAASE,IAAS,GAEnC,MAAMQ,EAAU,CACZC,SAAUR,EACVC,QACAI,WACAF,mBAGAF,GAASA,EAAMQ,cACfF,EAAQG,SAAWT,EAAMQ,eAEzBR,GAASA,EAAM9B,UACfoC,EAAQI,UAAYV,EAAM9B,WAE1B+B,GAAWA,EAAQU,OACnBL,EAAQK,KAAOV,EAAQU,MAGVf,EAASE,GAAMc,MAAK,CAACrC,EAAMsC,KACxC,GAAItC,GAAQ6B,EAAW7B,EAAK6B,SAExB,OADAR,EAASE,GAAMgB,OAAOD,EAAK,EAAGP,IACvB,CACX,KAIAV,EAASE,GAAMiB,KAAKT,EAE5B,CAUA,SAASU,EAAIlB,EAAMC,EAAUC,GACzB,IAAKF,IAASC,IAAaH,EAASE,GAChC,OAEJ,MAAMe,EAAMR,EAAcP,EAAMC,EAAUC,GACtCa,EAAM,IAGVjB,EAASE,GAAMe,GAAO,KAC1B,CAoDA,SAASR,EAAcP,EAAMC,EAAUC,GAEnC,IAAIa,GAAO,EAEX,OAAKjB,EAASE,IAIdF,EAASE,GAAMc,MAAK,CAACrC,EAAM0C,KACvB,GAAI1C,GAAQA,EAAKgC,WAAaR,KAAcC,GAASA,IAAUzB,EAAKyB,OAEhE,OADAa,EAAMI,GACC,CACX,IAEGJ,GATIA,CAUf,CAMA,MAAM7E,EAAW,CACbkF,GA3FJ,SAAYpB,EAAMC,EAAUC,GACxBH,EAAUC,EAAMC,EAAUC,EADYtP,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,EAE9C,EA0FIyQ,KAxFJ,SAAcrB,EAAMC,EAAUC,GAC1BH,EAAUC,EAAMC,EAAUC,EADctP,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,GACF,EAC9C,EAuFIsQ,MACAtC,QA3EJ,SAAiBoB,GAAkC,IAA5BsB,EAAO1Q,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,EAAG2Q,EAAO3Q,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,EAC5C,IAAKoP,IAASF,EAASE,GACnB,OAKJ,GAFAsB,EAAUA,GAAW,CAAC,EAElBA,EAAQE,eAAe,QACvB,MAAM,IAAInB,MAAM,mDAGpBiB,EAAQtB,KAAOA,EAEXuB,EAAQZ,WACRW,EAAQX,SAAWY,EAAQZ,UAE3BY,EAAQX,YACRU,EAAQV,UAAYW,EAAQX,WAGhC,MAAMa,EAAmB,GACzB3B,EAASE,GACJ0B,QAAQlB,MACAA,GAGDe,EAAQZ,UAAYH,EAAQG,UAAYH,EAAQG,WAAaY,EAAQZ,UAGrEY,EAAQX,WAAaJ,EAAQI,WAAaJ,EAAQI,YAAcW,EAAQX,WAIvEW,EAAQV,MAAQL,EAAQK,MAAQL,EAAQK,OAASU,EAAQV,OAAWL,EAAQK,MAAQU,EAAQV,MAAQU,EAAQV,OAASc,EAAAA,QAAkBC,yBAK/IpD,SAASgC,IACNA,GAAWA,EAAQC,SAAS1I,KAAKyI,EAAQN,MAAOoB,GAC5Cd,EAAQJ,iBACRqB,EAAiBR,KAAKT,EAC1B,IAGRiB,EAAiBjD,SAASgC,IACtBU,EAAIlB,EAAMQ,EAAQC,SAAUD,EAAQN,MAAM,GAElD,EA4BI2B,MATJ,WACI/B,EAAW,CAAC,CAChB,GAUA,OAAO5D,CACX,CAEAJ,EAASqD,sBAAwB,WACjC,MAAMC,EAAUC,EAAAA,QAAaC,oBAAoBxD,GACjDsD,EAAQ0C,mBA5JmB,EA6J3B1C,EAAQ2C,oBA5JoB,IA6J5B1C,EAAAA,QAAaQ,uBAAuB/D,EAASqD,sBAAuBC,GACpE,W,6BC9JA,MAAMC,EAAgB,WAElB,IAAInD,EACA8F,EAAoB,GACxB,MAAMC,EAAqB,CAAC,EACtBC,EAAiB,CAAC,EAuBxB,SAASC,EAAqBvG,EAASwG,GACnC,IAAK,MAAMnS,KAAK+R,EAAmB,CAC/B,MAAMK,EAAML,EAAkB/R,GAC9B,GAAIoS,EAAIzG,UAAYA,GAAWyG,EAAIjP,OAASgP,EACxC,OAAOC,EAAInG,QAEnB,CACA,OAAO,IACX,CA2CA,SAASoG,EAAiBlP,EAAMmP,GAC5B,OAAOA,EAAenP,EAC1B,CAEA,SAASoP,EAAcpP,EAAMgM,EAASmD,GAC9BnP,KAAQmP,IACRA,EAAenP,GAAQgM,EAE/B,CAmFA,SAASqD,EAAMC,EAAkB9G,EAAS+G,GAEtC,IAAIC,EACJ,MAAMR,EAAYM,EAAiBvD,sBAC7B0D,EAAkBjH,EAAQwG,GAEhC,GAAIS,EAAiB,CAEjB,IAAIC,EAAYD,EAAgB3G,SAEhC,IAAI2G,EAAgBE,SAiBhB,OAAOD,EAAUvE,MAAM,CACnB3C,UACAwD,QAASlD,GACVyG,GAlBHC,EAAgBF,EAAiBnE,MAAM,CAAC3C,WAAU+G,GAClDG,EAAYA,EAAUvE,MAAM,CACxB3C,UACAwD,QAASlD,EACT8G,OAAQJ,GACTD,GAEH,IAAK,MAAMlH,KAAQqH,EACXF,EAAcpB,eAAe/F,KAC7BmH,EAAcnH,GAAQqH,EAAUrH,GAYhD,MAEImH,EAAgBF,EAAiBnE,MAAM,CAAC3C,WAAU+G,GAMtD,OAFAC,EAAczE,aAAe,WAAa,OAAOiE,CAAU,EAEpDQ,CACX,CAeA,OAbA1G,EAAW,CACP3C,OAhNJ,SAAgBnG,EAAM6P,EAAeF,EAAUnH,IACtCA,EAAQxI,IAAS6P,IAClBrH,EAAQxI,GAAQ,CACZ8I,SAAU+G,EACVF,SAAUA,GAGtB,EA0MIZ,qBAAsBA,EACtBe,qBA1KJ,SAA8BtH,EAASwG,EAAWlG,GAC9C,IAAK,MAAMjM,KAAK+R,EAAmB,CAC/B,MAAMK,EAAML,EAAkB/R,GAC9B,GAAIoS,EAAIzG,UAAYA,GAAWyG,EAAIjP,OAASgP,EAExC,YADAJ,EAAkB/R,GAAGiM,SAAWA,EAGxC,CACA8F,EAAkBf,KAAK,CACnB7N,KAAMgP,EACNxG,QAASA,EACTM,SAAUA,GAElB,EA8JIiH,yBArJJ,SAAkCvH,GAC9BoG,EAAoBA,EAAkBN,QAAO0B,GAAKA,EAAExH,UAAYA,GACpE,EAoJI0D,oBAlFJ,SAA6BoD,GACzB,IAAItD,EAAUkD,EAAiBI,EAAiBvD,sBAAuB8C,GA6BvE,OA5BK7C,IACDA,EAAU,SAAUxD,GAChB,IAAIM,EAIJ,YAHgBrL,IAAZ+K,IACAA,EAAU,CAAC,GAER,CACHG,YAAa,WAcT,OAZKG,IACDA,EAAWiG,EAAqBvG,EAAS8G,EAAiBvD,wBAGzDjD,IACDA,EAAWuG,EAAMC,EAAkB9G,EAAShL,WAC5CoR,EAAkBf,KAAK,CACnB7N,KAAMsP,EAAiBvD,sBACvBvD,QAASA,EACTM,SAAUA,KAGXA,CACX,EAER,EACA+F,EAAmBS,EAAiBvD,uBAAyBC,GAG1DA,CACX,EAoDIiE,0BAvFJ,SAAmCjQ,GAC/B,OAAOkP,EAAiBlP,EAAM6O,EAClC,EAsFIpC,uBA5FJ,SAAgCzM,EAAMgM,GAClCoD,EAAcpP,EAAMgM,EAAS6C,EACjC,EA2FIqB,gBAvHJ,SAAyBZ,GACrB,IAAItD,EAAUkD,EAAiBI,EAAiBvD,sBAAuB+C,GAgBvE,OAdK9C,IACDA,EAAU,SAAUxD,GAIhB,YAHgB/K,IAAZ+K,IACAA,EAAU,CAAC,GAER,CACH2H,OAAQ,WACJ,OAAOd,EAAMC,EAAkB9G,EAAShL,UAC5C,EAER,EAEAsR,EAAeQ,EAAiBvD,uBAAyBC,GAEtDA,CACX,EAsGIoE,sBA5HJ,SAA+BpQ,GAC3B,OAAOkP,EAAiBlP,EAAM8O,EAClC,EA2HIuB,mBAjIJ,SAA4BrQ,EAAMgM,GAC9BoD,EAAcpP,EAAMgM,EAAS8C,EACjC,GAkIOhG,CAEX,CArOsB,GAuOtB,W,sGCiyBA,SAASwH,IACL,IAAIxH,EACJ,MAAMN,EAAU9D,KAAK8D,QACfC,GAAWC,EAAAA,EAAAA,SAASF,GAASG,cAC7B4H,EAAmB,CACrB,4BAA6B9E,EAAAA,QAAO+E,2BACpC,yCAA0C/E,EAAAA,QAAOgF,0CACjD,gCAAiChF,EAAAA,QAAOiF,gCACxC,yCAA0CjF,EAAAA,QAAOkF,kCACjD,yCAA0ClF,EAAAA,QAAOmF,kCACjD,4CAA6CnF,EAAAA,QAAOoF,iCACpD,sCAAuCpF,EAAAA,QAAOoF,iCAC9C,oDAAqDpF,EAAAA,QAAOoF,iCAC5D,+CAAgDpF,EAAAA,QAAOoF,iCACvD,+CAAgDpF,EAAAA,QAAOoF,iCACvD,iDAAkDpF,EAAAA,QAAOoF,iCACzD,qCAAsCpF,EAAAA,QAAOoF,iCAC7C,sCAAuCpF,EAAAA,QAAOoF,iCAC9C,iCAAkCpF,EAAAA,QAAOqF,4BACzC,iCAAkCrF,EAAAA,QAAOqF,4BACzC,iCAAkCrF,EAAAA,QAAOsF,4BACzC,iCAAkCtF,EAAAA,QAAOsF,6BAOvCC,EAAkB,CACpB1G,MAAO,CACHgB,SAAUhD,EAAAA,QAAMgE,kBAChBf,eAAe,GAEnB0F,UAAW,CACPC,mBAAoB,IACpBC,4BAA6B,IAC7BC,4BAA6B,IAC7BC,sCAAuC,EACvCC,mBAAmB,EACnBC,yBAAyB,EACzBC,4BAA4B,EAC5BC,sBAAsB,EACtBC,mCAAmC,EACnCC,iBAAiB,EACjBC,oCAAoC,EACpCC,aAAc,CACVC,sCAAsC,EACtCC,6BAA8B,CAC1B,CAAEC,YAAaC,EAAAA,QAAUC,0BACzB,CAAEF,YAAaC,EAAAA,QAAUE,+BAAgCtG,MAAO,WAChE,CAAEmG,YAAaC,EAAAA,QAAUG,uBACzB,CAAEJ,YAAaC,EAAAA,QAAUI,2BACzB,CAAEL,YAAaC,EAAAA,QAAUK,kCAAmCzG,MAAO,WACnE,CAAEmG,YAAaC,EAAAA,QAAUM,uCAAwC1G,MAAO,mBACrEoG,EAAAA,QAAUO,0BAA0B1N,KAAI2N,IAChC,CAAE,YAAeA,OAGhCC,yBAAyB,EACzBC,2CAA2C,EAC3CC,4CAA4C,GAEhDC,OAAQ,CACJC,4BAA6B,IAC7BC,8BAA+B,KAEnCC,gBAAiB,CACbC,yBAAyB,EACzBC,2BAA2B,GAE/BC,QAAS,CACLC,aAAc,KAElBC,MAAO,CACHC,uBAAwBC,IACxBC,UAAWD,IACXE,+BAA+B,GAEnCC,WAAY,CACRC,yBAAyB,EACzBC,yBAAyB,EACzBC,8BAA8B,EAC9BC,mBAAmB,GAEvBC,OAAQ,CACJC,4BAA4B,EAC5BC,kBAAmB,KACnBC,0BAA0B,EAC1BC,4BAA4B,EAC5BC,sBAAuB,GACvBC,aAAc,GACdC,uBAAwB,GACxBC,+BAAgC,GAChCC,mBAAoBjB,IACpBkB,kBAAmB,GACnBC,iCAAkC,IAClCC,eAAgB,GAChBC,yBAA0B,GAC1BC,iBAAiB,EACjBC,eAAe,EACfC,8BAA8B,EAC9BC,eAAe,EACfC,6BAA6B,EAC7BC,kCAAkC,EAClCC,qBAAsB,CAClBC,SAAS,EACTC,kBAAkB,IAG1BC,KAAM,CACFC,UAAU,EACVC,eAAe,EACfC,cAAe,IACfC,UAAW,GACXC,eAAe,EACfC,gBAAgB,EAChBC,UAAW,IAEfC,mBAAoB,CAChBV,SAAS,EACTW,iCAAiC,EACjCC,mBAAoB,EACpBC,wBAAyB,GACzBC,+BAAgC,IAChCC,+BAAgC,EAChCC,wCAAyC,EACzCC,oBAAqB,IACrBC,+CAA+C,EAC/CC,oBAAqB,CACjBC,OAAQ,qCACR3K,MAAO,oCAGf4K,WAAY,CACRC,eAAgB,IAChBC,kBAAmB,EACnBC,qBAAqB,GAEzBC,KAAM,CACFC,gBAAgB,EAChBC,4BAA4B,EAC5BC,qBAAqB,EACrBC,KAAM,CACFC,uBAAuB,EACvBC,cAAc,GAElBC,OAAQ,CACJC,wBAAwB,IAGhCC,YAAa,CACTC,SAAUhE,IACViE,aAAc,CACVC,IAAKlE,IACLmE,IAAKnE,KAEToE,kBAAmB,GACnBvC,QAAS,KACT3H,KAAMwE,EAAAA,QAAU2F,2BAEpBC,uBAAwB,CACpBzC,SAAS,EACT0C,IAAK,MAETC,6BAA8B,CAC1B3C,SAAS,EACT0C,IAAK,MAETE,iDAAiD,EACjDC,oBAAqB,CACjBC,MAAO,GACPC,MAAO,GAEXC,gBAAiB,CACbD,MAAOlG,EAAAA,QAAUoG,iCACjBH,MAAOjG,EAAAA,QAAUqG,iCAErBC,yBAAyB,EACzBC,oBAAoB,EACpBC,yBAAyB,EACzBC,6BAA8BzG,EAAAA,QAAU0G,wCACxCC,uBAAwB,IACxBC,gCAAiC,EACjCC,uBAAwB,IACxBC,eAAgB,CACZ,CAACC,EAAAA,YAAYC,UAAW,IACxB,CAACD,EAAAA,YAAYE,sBAAuB,IACpC,CAACF,EAAAA,YAAYG,oBAAqB,IAClC,CAACH,EAAAA,YAAYI,mBAAoB,IACjC,CAACJ,EAAAA,YAAYK,kCAAmC,IAChD,CAACL,EAAAA,YAAYM,oBAAqB,IAClC,CAACN,EAAAA,YAAYO,gCAAiC,IAC9C,CAACP,EAAAA,YAAYQ,SAAU,IACvB,CAACR,EAAAA,YAAYS,YAAa,IAC1BC,0BAA2B,IAE/BC,cAAe,CACX,CAACX,EAAAA,YAAYC,UAAW,EACxB,CAACD,EAAAA,YAAYE,sBAAuB,EACpC,CAACF,EAAAA,YAAYG,oBAAqB,EAClC,CAACH,EAAAA,YAAYI,mBAAoB,EACjC,CAACJ,EAAAA,YAAYK,kCAAmC,EAChD,CAACL,EAAAA,YAAYM,oBAAqB,EAClC,CAACN,EAAAA,YAAYO,gCAAiC,EAC9C,CAACP,EAAAA,YAAYQ,SAAU,EACvB,CAACR,EAAAA,YAAYS,YAAa,EAC1BG,yBAA0B,GAE9BC,IAAK,CACDC,sBAAsB,EACtBC,qCAAqC,EACrCC,kDAAkD,EAClDC,MAAO,CACHC,eAAgB,CACZC,QAAQ,GAEZC,SAAU,CACND,QAAQ,GAEZE,uBAAwB,CACpBF,QAAQ,EACRG,WAAY,CACRC,uBAAwB,GACxBC,mBAAoB,IAG5BC,kBAAmB,CACfN,QAAQ,EACRG,WAAY,CACRI,WAAY,EACZC,0BAA2B,OAGnCC,kBAAmB,CACfT,QAAQ,EACRG,WAAY,CACRO,kBAAmB,IACnBC,iCAAkC,MAG1CC,oBAAqB,CACjBZ,QAAQ,EACRG,WAAY,CACRU,0BAA2B,IAC3BC,oCAAqC,IACrCC,8BAA+B,IAGvCC,QAAS,CACLhB,QAAQ,GAEZiB,SAAU,CACNjB,QAAQ,IAGhBkB,WAAY,CACRC,uBAAwBrJ,EAAAA,QAAUsJ,6BAA6BC,KAC/DC,sCAAuCxJ,EAAAA,QAAUyJ,2CAA2CC,aAC5FC,sBAAsB,EACtBC,yBAA0B,CACtBC,KAAK,EACLC,OAAO,GAEXC,oBAAoB,EACpBC,sBAAuB,GACvBC,eAAgB,CACZC,KAAM,EACNC,IAAK,EACLC,4BAA4B,EAC5BC,cAAe,GACfC,cAAe,IACfC,sBAAuB,GACvBC,2BAA4B,GAEhCC,KAAM,CACFC,8BAA+B,EAC/BC,8BAA+B,EAC/BC,yBAA0B,EAC1BC,yBAA0B,EAC1BC,uCAAwC,QAGhDC,WAAY,CACR7E,OAAQ,EACRD,OAAQ,GAEZ+E,WAAY,CACR9E,OAAQ,EACRD,OAAQ,GAEZgF,eAAgB,CACZ/E,OAAQ,EACRD,OAAQ,GAEZiF,kBAAmB,CACfhF,OAAO,EACPD,OAAO,IAGfkF,KAAM,CACFC,wBAAwB,EACxBjI,SAAS,EACTkI,IAAK,KACLC,IAAK,KACLC,IAAK,KACLC,gBAAiB,EACjBhQ,KAAMwE,EAAAA,QAAUyL,gBAChBC,YAAa1L,EAAAA,QAAU2L,oBACvBC,kBAAmB,CAAC,UAAW,OAC/BvX,QAAS,GAEbwX,KAAM,CACF1I,SAAS,EACTyE,IAAK,CACDkE,SAAS,EACTC,eAAgB,IAGxBC,mBAAoB,CAChBC,UAAW,GACXC,0BAA2B,2CAC3BC,KAAM,0BACNC,cAAe,4BAGvBC,OAAQ,CACJC,gBAAiB,CACbC,iBAAkB,KAK9B,IAAI5V,EAAW6V,EAAAA,QAAMC,MAAM1N,GAI3B,SAAS2N,EAAcC,EAAQC,EAAM3iB,GACjC,IAAK,IAAI4iB,KAAKF,EACNA,EAAOxQ,eAAe0Q,KAClBD,EAAKzQ,eAAe0Q,GACK,iBAAdF,EAAOE,IAAqBF,EAAOE,aAAcC,QAAaH,EAAOE,aAAcpV,OAAwB,OAAdkV,EAAOE,IAG3GD,EAAKC,GAAKL,EAAAA,QAAMC,MAAME,EAAOE,IACzBvO,EAAiBrU,EAAO4iB,IACxBrW,EAAS+C,QAAQ+E,EAAiBrU,EAAO4iB,KAJ7CH,EAAcC,EAAOE,GAAID,EAAKC,GAAI5iB,EAAKgB,QAAU4hB,EAAI,KAQzDzV,QAAQQ,MAAM,sBAAwB3N,EAAO4iB,EAAI,qBAIjE,CA4CA,OANAhW,EAAW,CACPZ,IAhCJ,WACI,OAAOU,CACX,EA+BIoW,OAlBJ,SAAgBC,GACe,iBAAhBA,GACPN,EAAcM,EAAarW,EAAU,GAE7C,EAeI6F,MAPJ,WACI7F,EAAW6V,EAAAA,QAAMC,MAAM1N,EAC3B,GAQOlI,CACX,CAGAwH,EAASvE,sBAAwB,WACjC,IAAIC,EAAUC,EAAAA,QAAaC,oBAAoBoE,GAC/C,W,gECr5CA,MAAMmO,EACF,YAAOS,CAAML,EAAMD,EAAQO,GACvB,IAAIC,EACAC,EAAQ,CAAC,EACb,GAAIR,EACA,IAAK,IAAI7e,KAAQ4e,EACTA,EAAOxQ,eAAepO,KACtBof,EAAIR,EAAO5e,GACLA,KAAQ6e,IAAUA,EAAK7e,KAAUof,GAAQpf,KAAQqf,GAAUA,EAAMrf,KAAUof,KACnD,iBAAfP,EAAK7e,IAAqC,OAAf6e,EAAK7e,GACvC6e,EAAK7e,GAAQye,EAAMS,MAAML,EAAK7e,GAAOof,EAAGD,GAExCN,EAAK7e,GAAQmf,EAAKC,KAMtC,OAAOP,CACX,CAEA,YAAOH,CAAMY,GACT,IAAKA,GAAsB,iBAARA,EACf,OAAOA,EAEX,GAAIA,aAAeP,OACf,OAAO,IAAIA,OAAOO,GAEtB,IAAIC,EACJ,GAAID,aAAe5V,MAAO,CAEtB6V,EAAI,GACJ,IAAK,IAAI1iB,EAAI,EAAG2iB,EAAIF,EAAIxiB,OAAQD,EAAI2iB,IAAK3iB,EACjCA,KAAKyiB,GACLC,EAAE1R,KAAK4Q,EAAMC,MAAMY,EAAIziB,IAGnC,MACI0iB,EAAI,CAAC,EAET,OAAOd,EAAMS,MAAMK,EAAGD,EAAKb,EAAMC,MACrC,CAEA,uCAAOe,CAAiCC,EAAKjW,GACzC,IACI,IAAKA,GAA4B,IAAlBA,EAAO3M,OAClB,OAAO4iB,EAGX,IAAIC,EAAaD,EAKjB,OAJAjW,EAAO2B,SAAQwU,IAAoB,IAAnB,IAAEC,EAAG,MAAEhU,GAAO+T,EAC1B,MAAME,EAAYH,EAAWI,SAAS,KAAO,IAAM,IACnDJ,GAAc,GAAGG,IAAaE,mBAAmBH,MAAUG,mBAAmBnU,IAAS,IAEpF8T,CACX,CAAE,MAAOM,GACL,OAAOP,CACX,CACJ,CAEA,kCAAOQ,CAA4BR,EAAKS,GACpC,IAAKT,IAAQS,EACT,OAAOT,EAGX,MAAMU,EAAY,IAAIC,IAAIX,GAGpBjW,EAAS,IAAI6W,gBAAgBF,EAAUG,QAE7C,IAAK9W,GAA0B,IAAhBA,EAAO+W,OAAe/W,EAAOtG,IAAIgd,GAC5C,OAAOT,EAIXjW,EAAOgX,OAAON,GAGd,MAAMO,EAAchX,MAAMxL,KAAKuL,EAAOkX,WACjC7b,KAAI8b,IAAA,IAAEf,EAAKhU,GAAM+U,EAAA,MAAK,GAAGf,KAAOhU,GAAO,IACvC/N,KAAK,KAGJ+iB,EAAU,GAAGT,EAAUU,SAASV,EAAUW,WAChD,OAAOL,EAAc,GAAGG,KAAWH,IAAgBG,CACvD,CAEA,uBAAOG,CAAiBC,GACpB,IAAIC,EAAU,CAAC,EACf,IAAKD,EACD,OAAOC,EAKX,IAAIC,EAAcF,EAAUvd,OAAO8C,MAAM,QACzC,IAAK,IAAI3J,EAAI,EAAGukB,EAAOD,EAAYrkB,OAAQD,EAAIukB,EAAMvkB,IAAK,CACtD,IAAIwkB,EAAaF,EAAYtkB,GACzBkR,EAAQsT,EAAW9d,QAAQ,MAC3BwK,EAAQ,IACRmT,EAAQG,EAAWxd,UAAU,EAAGkK,IAAUsT,EAAWxd,UAAUkK,EAAQ,GAE/E,CACA,OAAOmT,CACX,CAOA,uBAAOI,CAAiBC,GACpB,MAAM9X,EAAS,GACT+X,EAAe,IAAIlB,gBAAgBiB,GACzC,IAAK,MAAO1B,EAAKhU,KAAU2V,EAAab,UACpClX,EAAOoE,KAAK,CAAEgS,IAAK4B,mBAAmB5B,GAAMhU,MAAO4V,mBAAmB5V,KAE1E,OAAOpC,CACX,CAEA,mBAAOiY,GACH,IAAIC,GAAK,IAAI9W,MAAOC,UAMpB,MALa,uCAAuClH,QAAQ,SAAS,SAAUge,GAC3E,MAAMrC,GAAKoC,EAAqB,GAAhBE,KAAKC,UAAiB,GAAK,EAE3C,OADAH,EAAKE,KAAKE,MAAMJ,EAAK,KACR,KAALC,EAAWrC,EAAS,EAAJA,EAAU,GAAMyC,SAAS,GACrD,GAEJ,CAEA,uBAAOC,CAAiBC,GACpB,IAAIC,EAAO,EAEX,GAAsB,IAAlBD,EAAOplB,OACP,OAAOqlB,EAGX,IAAK,IAAItlB,EAAI,EAAGA,EAAIqlB,EAAOplB,OAAQD,IAE/BslB,GAASA,GAAQ,GAAKA,EADVD,EAAOnlB,WAAWF,GAE9BslB,GAAQ,EAEZ,OAAOA,CACX,CAQA,qBAAOC,CAAeC,EAAaC,GAC/B,IACI,MAAMC,EAAW,IAAIlC,IAAIgC,GACnBG,EAAS,IAAInC,IAAIiC,GAIvB,GADAC,EAASE,SAAWD,EAAOC,SACvBF,EAASzB,SAAW0B,EAAO1B,OAC3B,OAAOwB,EAIX,IAAII,EAAexmB,EAAAA,SAAcqmB,EAASxB,SAAS4B,OAAO,EAAGJ,EAASxB,SAAS9jB,YAAY,MAAOulB,EAAOzB,SAAS4B,OAAO,EAAGH,EAAOzB,SAAS9jB,YAAY,OAGxJ,MAAM2lB,EAA2C,IAAxBF,EAAa5lB,OAAe,EAAI,EAIzD,OAHA4lB,GAAgBF,EAAOzB,SAAS4B,OAAOH,EAAOzB,SAAS9jB,YAAY,KAAO2lB,EAAkBJ,EAAOzB,SAASjkB,OAAS,GAGjH0lB,EAAOzB,SAASjkB,OAAS4lB,EAAa5lB,OAC/B0lB,EAAOzB,SAEX2B,CACX,CAAE,MAAOzC,GACL,OAAOqC,CACX,CACJ,CAEA,qBAAOO,CAAeC,GAClB,IAGI,OAFY,IAAIzC,IAAIyC,GAETC,IACf,CAAE,MAAO9C,GACL,OAAO,IACX,CACJ,CAEA,qBAAO+C,GAA0B,IAAXjf,EAAEvG,UAAAV,OAAA,QAAAW,IAAAD,UAAA,GAAAA,UAAA,GAAG,KACvB,IACI,MAAMylB,EAAkB,OAAPlf,GAAmC,oBAAd4B,UAA4BA,UAAUE,UAAUpC,cAAqB,GAE3G,OAAO8B,EAAAA,EAAAA,UAAS0d,EACpB,CAAE,MAAOhD,GACL,MAAO,CAAC,CACZ,CACJ,CAOA,wBAAOiD,CAAkBhB,GACrB,MAAQ,mBAAmBtd,KAAKsd,EACpC,CAEA,6BAAOiB,CAAuBC,GAC1B,OAAO3E,EAAM4E,WAAWD,EAAcE,SAC1C,CAEA,yBAAOC,CAAmBH,GACtB,OAAO3E,EAAM4E,WAAWD,EAAcI,WAC1C,CAEA,yBAAOC,CAAmBC,GAEtB,OADgB,IAAIC,YAAY,SACjBC,OAAOF,EAC1B,CAEA,wBAAOG,CAAkBC,GACrB,MAAM9gB,EAAMyb,EAAM8E,mBAAmBO,GACrC,IAAIC,EAAM,GACV,IAAK,IAAIlY,KAAS7I,EACd6I,EAAQA,EAAMmW,SAAS,IACF,IAAjBnW,EAAM/O,SACN+O,EAAQ,IAAMA,GAElBkY,GAAOlY,EAEX,OAAOkY,CACX,CAEA,iBAAOV,CAAWD,EAAcY,GAC5B,MAAMjQ,EAAS0K,EAAMwF,eAAeb,GACpC,IAAIc,EAAkB,EAClB,sBAAuBZ,WACvBY,EAAkBZ,SAASa,mBAG/B,MAAMC,IAAYhB,EAAaiB,YAAc,GAAKjB,EAAakB,YAC3DJ,EACEK,GAAanB,EAAaiB,YAAc,GAAMH,EAC9C/kB,EAAQ0iB,KAAKE,MAAMF,KAAKnK,IAAI,EAAGmK,KAAKpK,IAAI8M,EAAUH,KAExD,OAAO,IAAIJ,EAAKjQ,EAAQ5U,EADZ0iB,KAAKE,MAAMF,KAAKpK,IAAItY,EAAQ0iB,KAAKnK,IAAI8M,IAAU,GAAIJ,IAC1BjlB,EACzC,CAEA,qBAAO8kB,CAAeQ,GAClB,OAAIA,aAAgBC,YACTD,EAEAA,EAAK1Q,MAEpB,CAEA,qBAAO4Q,CAAeC,GAClB,MAAM,KAAE7kB,EAAI,QAAE8kB,GAAYpG,EAAMqG,eAAeF,GAE/C,OAAQ7kB,GACJ,IAAK,OACD,OAAQ8kB,GACJ,IAAK,KACL,IAAK,KACL,IAAK,QACD,OAAO5S,EAAAA,QAAU8S,eAAeC,IACpC,IAAK,KACL,IAAK,KACL,IAAK,KACL,IAAK,OACL,IAAK,QACL,IAAK,OACL,IAAK,QACL,IAAK,QACL,IAAK,QACD,OAAO/S,EAAAA,QAAU8S,eAAeE,IACpC,IAAK,KACD,OAAOhT,EAAAA,QAAU8S,eAAeG,IACpC,IAAK,KACD,OAAOjT,EAAAA,QAAU8S,eAAeI,IACpC,IAAK,KACD,OAAOlT,EAAAA,QAAU8S,eAAeK,KACpC,IAAK,KACD,OAAOnT,EAAAA,QAAU8S,eAAeM,KAExC,MACJ,IAAK,OACL,IAAK,OACD,OAAOpT,EAAAA,QAAU8S,eAAeO,IACpC,IAAK,OACL,IAAK,OACD,OAAOrT,EAAAA,QAAU8S,eAAeQ,KACpC,QACI,OAAOxlB,EAGf,OAAOA,CACX,CAEA,qBAAO+kB,CAAeF,GAClB,MAAO7kB,KAASylB,GAAQZ,EAAYpe,MAAM,KAE1C,MAAO,CAAEzG,OAAM8kB,QADCW,EAAK1nB,KAAK,KAE9B,EAIJ,W,4CCrTA,MAAM2nB,UAAmBC,EAAAA,QACrBC,WAAAA,GACIC,QACAlhB,KAAKmhB,wBAA0B,wBAC/BnhB,KAAKohB,oBAAsB,qBAC3BphB,KAAKqhB,eAAiB,gBACtBrhB,KAAKshB,4BAA8B,2BACnCthB,KAAKuhB,2BAA6B,2BAClCvhB,KAAKwhB,8BAAgC,6BACrCxhB,KAAKyhB,mBAAqB,mBAC1BzhB,KAAK0hB,sBAAwB,sBAC7B1hB,KAAK2hB,sBAAwB,sBAC7B3hB,KAAK4hB,cAAgB,eACrB5hB,KAAK6hB,sBAAwB,sBAC7B7hB,KAAK8hB,qBAAuB,qBAC5B9hB,KAAK+hB,qBAAuB,qBAC5B/hB,KAAKgiB,yBAA2B,yBAChChiB,KAAKiiB,yBAA2B,yBAChCjiB,KAAKkiB,kBAAoB,mBACzBliB,KAAKmiB,iBAAmB,kBACxBniB,KAAKoiB,sBAAwB,sBAC7BpiB,KAAKqiB,kBAAoB,iBACzBriB,KAAKsiB,iBAAmB,kBACxBtiB,KAAKuiB,sBAAwB,sBAC7BviB,KAAKwiB,sBAAwB,sBAC7BxiB,KAAKyiB,kBAAoB,mBACzBziB,KAAK0iB,eAAiB,gBACtB1iB,KAAK2iB,+BAAiC,8BACtC3iB,KAAK4iB,mCAAqC,kCAC1C5iB,KAAK6iB,wCAA0C,8BAC/C7iB,KAAK8iB,4CAA8C,kCACnD9iB,KAAK+iB,wCAA0C,sCAC/C/iB,KAAKgjB,4CAA8C,0CACnDhjB,KAAKijB,mCAAqC,iCAC1CjjB,KAAKkjB,wBAA0B,uBAC/BljB,KAAKmjB,oBAAsB,oBAC3BnjB,KAAKojB,iBAAmB,kBACxBpjB,KAAKqjB,2BAA6B,2BAClCrjB,KAAKsjB,4BAA8B,4BACnCtjB,KAAKujB,8BAAgC,6BACrCvjB,KAAKwjB,+BAAiC,8BACtCxjB,KAAKyjB,wBAA0B,uBAC/BzjB,KAAK0jB,sBAAwB,sBAC7B1jB,KAAK2jB,qBAAuB,qBAC5B3jB,KAAK4jB,uBAAyB,uBAC9B5jB,KAAK6jB,qBAAuB,qBAC5B7jB,KAAK8jB,YAAc,aACnB9jB,KAAK+jB,YAAc,aACnB/jB,KAAK8L,2BAA6B,0BAClC9L,KAAK+L,0CAA4C,uCACjD/L,KAAKgM,gCAAkC,+BACvChM,KAAKiM,kCAAoC,gCACzCjM,KAAKkM,kCAAoC,gCACzClM,KAAKmM,iCAAmC,+BACxCnM,KAAKoM,4BAA8B,2BACnCpM,KAAKqM,4BAA8B,0BACvC,EAGJ,W,4CC9DA,MAAMtF,UAAega,EAAAA,SAGrB,IAAI5S,EAAS,IAAIpH,EACjB,W,8BCiBA,UAvBA,MACItF,MAAAA,CAAO0M,EAAQtK,GACX,IAAKsK,EACD,OAGJ,IAAIlD,IAAWpH,GAASA,EAAOoH,SAC3B+Y,IAAangB,GAASA,EAAOmgB,WAGjC,IAAK,MAAMC,KAAO9V,GACTA,EAAOzE,eAAeua,IAASjkB,KAAKikB,KAAShZ,GAG9C+Y,IAAkD,IAApC7V,EAAO8V,GAAKplB,QAAQ,aAGtCmB,KAAKikB,GAAO9V,EAAO8V,GAG3B,E,8BCZJ,UARA,MACIhD,WAAAA,GAEIjhB,KAAKsN,YAAc,GACnBtN,KAAKmH,MAAQ,EACjB,E,4CCHJ,MAAM0C,UAA0BmX,EAAAA,QAK5BC,WAAAA,GACIC,QAOAlhB,KAAKkkB,cAAgB,cAMrBlkB,KAAKmkB,kBAAoB,kBAOzBnkB,KAAKokB,aAAe,gBAOpBpkB,KAAKqkB,cAAgB,eAMrBrkB,KAAKskB,2BAA6B,qBAMlCtkB,KAAKukB,qBAAuB,qBAM5BvkB,KAAKwkB,wBAA0B,uBAM/BxkB,KAAKykB,2BAA6B,0BAMlCzkB,KAAK0kB,yBAA2B,wBAMhC1kB,KAAK2kB,kBAAoB,kBAMzB3kB,KAAK4kB,MAAQ,QAKb5kB,KAAK6kB,2BAA6B,2BAMlC7kB,KAAK8kB,0BAA4B,0BAKjC9kB,KAAK+kB,yBAA2B,yBAMhC/kB,KAAKglB,2BAA6B,2BAMlChlB,KAAKgH,IAAM,MAMXhH,KAAKilB,yBAA2B,yBAMhCjlB,KAAKklB,0BAA4B,0BAMjCllB,KAAKmlB,gBAAkB,iBAMvBnlB,KAAKolB,gBAAkB,iBAMvBplB,KAAKqlB,eAAiB,gBAMtBrlB,KAAKslB,aAAe,cAMpBtlB,KAAKulB,eAAiB,gBAMtBvlB,KAAKwlB,sBAAwB,sBAM7BxlB,KAAKylB,wBAA0B,wBAM/BzlB,KAAK0lB,yBAA2B,yBAMhC1lB,KAAK2lB,wBAA0B,wBAM/B3lB,KAAK4lB,mBAAqB,mBAM1B5lB,KAAK6lB,sBAAwB,sBAM7B7lB,KAAK8lB,oBAAsB,qBAM3B9lB,KAAK+lB,eAAiB,gBAMtB/lB,KAAKgmB,iBAAmB,kBAMxBhmB,KAAKimB,mBAAqB,oBAM1BjmB,KAAKkmB,mBAAqB,oBAM1BlmB,KAAKmmB,yBAA2B,yBAMhCnmB,KAAKomB,kBAAoB,qBAMzBpmB,KAAKqmB,iBAAmB,iBAMxBrmB,KAAKsmB,UAAY,WAMjBtmB,KAAKumB,SAAW,UAMhBvmB,KAAKwmB,8BAAgC,8BAMrCxmB,KAAKymB,YAAc,aAMnBzmB,KAAK0mB,cAAgB,cAMrB1mB,KAAK2mB,iBAAmB,kBAMxB3mB,KAAK4mB,yBAA2B,yBAQhC5mB,KAAK6mB,SAAW,UAMhB7mB,KAAK8mB,iBAAmB,iBAMxB9mB,KAAK+mB,eAAiB,gBAOtB/mB,KAAKgnB,eAAiB,gBAOtBhnB,KAAKinB,qBAAuB,sBAM5BjnB,KAAKknB,qBAAuB,qBAO5BlnB,KAAKmnB,yBAA2B,yBAOhCnnB,KAAKonB,qBAAuB,qBAM5BpnB,KAAKqnB,gBAAkB,iBAQvBrnB,KAAKsnB,iBAAmB,kBAQxBtnB,KAAKunB,kBAAoB,mBAMzBvnB,KAAKwnB,sBAAwB,sBAM7BxnB,KAAKynB,gBAAkB,iBAMvBznB,KAAK0nB,iBAAmB,kBAMxB1nB,KAAK2nB,iBAAmB,kBAQxB3nB,KAAK4nB,iBAAmB,kBAMxB5nB,KAAK6nB,sBAAwB,sBAM7B7nB,KAAK8nB,wBAA0B,wBAO/B9nB,KAAK+nB,iBAAmB,kBAMxB/nB,KAAKgoB,0BAA4B,0BAMjChoB,KAAKioB,oBAAsB,mBAM3BjoB,KAAK8J,sBAAwB,qBAM7B9J,KAAKkoB,sBAAwB,uBAM7BloB,KAAKmoB,sBAAwB,uBAM7BnoB,KAAKooB,uCAAyC,qCAM9CpoB,KAAKqoB,mCAAqC,kCAM1CroB,KAAKsoB,YAAc,aAMnBtoB,KAAKuoB,qCAAuC,mCAM5CvoB,KAAKwoB,mCAAqC,gCAC9C,EAGJ,IAAIC,EAAoB,IAAI5e,EAC5B,W,8BCjdA,WAMI6e,OAAQ,SAORC,MAAO,QAOPC,MAAO,QAOPC,KAAM,OAONC,MAAO,QAOPC,MAAO,QAOPC,KAAM,OAONC,KAAM,OAONC,IAAK,MAOLC,KAAM,OAONC,iBAAkB,kBAOlBlW,0BAA2B,yBAO3BmW,uBAAwB,sBAOxBC,8BAA+B,gBAO/BC,oBAAqB,OAOrBC,mBAAoB,oBAOpBC,qBAAsB,8BAOtB9V,iCAAkC,gBAOlCC,gCAAiC,eAOjC8V,iCAAkC,aAOlCC,qCAAsC,iBAOtC1V,wCAAyC,oBAOzC2V,kCAAmC,cAOnCC,eAAgB,OAOhB7Q,gBAAiB,QAOjB8Q,iBAAkB,SAOlB5Q,oBAAqB,CAAC,KAAM,IAAK,KAAM,KAAM,KAAM,KAAM,MAAO,MAAO,MAAO,KAAM,KAAM,MAAO,MAAO,KAAM,KAAM,MAAO,KAAM,KAMjI6Q,uBAAwB,CAAC,MAAO,OAOhCC,wBAAyB,CAAC,UAAW,MAAO,QAAS,WAAY,SAGjEC,WAAY,aACZC,aAAc,UACdC,YAAa,SACbC,cAAe,WACfC,4BAA6B,gCAC7BC,IAAK,MACLC,IAAK,MACLC,KAAM,QACNC,cAAe,cACfC,WAAY,YACZC,kCAAmC,qCACnCC,oCAAqC,wCACrCC,qBAAsB,8BACtB/c,0BAA2B,CAAC,mCAAoC,+CAChEN,yBAA0B,iCAC1BC,+BAAgC,sCAChCC,sBAAuB,8BACvBC,0BAA2B,8BAC3BC,kCAAmC,yCACnCC,uCAAwC,8CACxCid,kCAAmC,uBACnCC,2BAA4B,CACxBC,UAAW,YACXC,QAAS,UACTC,UAAW,aAEfC,uBAAwB,CACpBC,WAAY,CACRC,KAAM,OACNC,GAAI,KACJC,QAAS,WAEbC,iBAAkB,CACdH,KAAM,OACNI,GAAI,KACJC,IAAK,OAETC,iBAAkB,CACdC,iBAAkB,iBAClBC,OAAQ,SACRC,iBAAkB,mBAG1BC,IAAK,MACLC,aAAc,cACdC,kBAAmB,mBACnBC,gBAAiB,kBACjBC,aAAc,wBACdC,cAAe,wBACfC,2BAA4B,CACxBC,aAAc,EACdC,cAAe,EACfC,kBAAmB,EACnBC,iBAAkB,EAClBC,iBAAkB,GAEtBC,kBAAmB,CACfC,MAAO,eACPC,IAAK,cAETC,iBAAkB,CACdC,QAAS,0BACTC,UAAW,6BAEfnW,6BAA8B,CAC1BC,KAAM,gCACNmW,MAAO,iCACPC,gBAAiB,0CACjBC,mCAAoC,0DACpCC,8BAA+B,sDAC/BC,cAAe,wCACfC,iCAAkC,wDAClCC,4BAA6B,qDAEjCvW,2CAA4C,CACxCC,aAAc,mDACduW,gBAAiB,sDACjBC,KAAM,6CAEVC,YAAa,CACTC,qBAAsB,qBACtBC,uBAAwB,wBAE5BD,qBAAsB,CAClBE,UAAW,WACXC,gBAAiB,iBACjBC,yBAA0B,yBAC1BC,oBAAqB,oBACrBC,oBAAqB,oBACrBC,oBAAqB,UACrBC,cAAe,YAEnBP,uBAAwB,CACpBQ,qBAAsB,uBAQ1BC,kBAAmB,+BACnBC,2BAA4B,sBAC5BC,oBAAsB,0BACtBlO,eAAgB,CACZC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,KAAM,OACNC,KAAM,OACNC,IAAK,MACLC,KAAM,Q,2CC1Td,MAAM2N,UAA+BxN,EAAAA,QACjCC,WAAAA,GACIC,QAEAlhB,KAAKyuB,gCAAkC,uCACvCzuB,KAAK0uB,wBAA0B,iCAM/B1uB,KAAK2uB,oBAAsB,mBAC/B,EAGJ,IAAIC,EAAyB,IAAIJ,EACjC,W,kFCdA,SAASK,IAEL,IAAIzqB,EACA0qB,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAEJ,MAAMxrB,EAAU9D,KAAK8D,QACfI,GAAW0H,EAAAA,EAAAA,SAAS9H,GAASG,cASnC,SAASsrB,IACLP,EAAsB,GACtBC,EAAuB,GACvBC,EAAwB,GACxBC,EAAyB,GACzBC,EAA4B,GAC5BE,EAAiB,GACjBD,EAAsC,KACtCP,EAAmB,EACvB,CAsHA,SAASU,EAAkB/lB,EAASG,GAChC,IAAIP,GAAS,EACbI,EAAQT,MAAK,CAACrC,EAAMxO,KAChB,GAAIwO,IAASiD,EAET,OADAP,EAAQlR,GACD,CACX,IAEAkR,EAAQ,GAGZI,EAAQP,OAAOG,EAAO,EAC1B,CAOA,SAASomB,EAAwBC,GAC7B,IAAIv3B,EACJ,IAAKA,EAAI,EAAGA,EAAIm3B,EAAel3B,OAAQD,IACnC,GAAIm3B,EAAen3B,GAAGu3B,WAAaA,EAC/B,OAAOv3B,EAGf,OAAQ,CACZ,CA0HA,SAASw3B,EAAmBriB,EAAanG,GACrCyoB,EAAsBtiB,EAAanG,GACnC,IAAI0oB,EAAK,IAAIC,EAAAA,QACbD,EAAGviB,YAAcA,EACjBuiB,EAAG1oB,MAAQA,EACX2nB,EAAiB3lB,KAAK0mB,EAC1B,CAeA,SAASD,EAAsBtiB,EAAanG,IACxC4oB,EAAAA,EAAAA,oBAAmBziB,EAAa,WAChCyiB,EAAAA,EAAAA,oBAAmB5oB,EAAO,UAC1B2nB,EAAiBpoB,SAAQ,SAAU6D,EAAKtB,GAChCsB,EAAI+C,cAAgBA,GAAe/C,EAAIpD,QAAUA,GACjD2nB,EAAiB5lB,OAAOD,EAAK,EAErC,GACJ,CAqEA,OApCA7E,EAAW,CACP4rB,iBA9KJ,SAA0B9nB,EAAMwnB,EAAUO,GACtC,GAAoB,iBAAT/nB,GAAsBA,IAASqF,EAAAA,QAAUmgB,YAAYE,wBAA0B1lB,IAASqF,EAAAA,QAAUmgB,YAAYC,sBACjG,iBAAb+B,EACP,MAAMniB,EAAAA,QAAUic,mBAEpB,IAAIngB,EAAQomB,EAAwBC,IACrB,IAAXrmB,EAEAimB,EAAenmB,KAAK,CAChBjB,KAAMA,EACNwnB,SAAUA,EACVO,KAAMA,KAIVX,EAAejmB,GAAOnB,KAAOA,EAC7BonB,EAAejmB,GAAO4mB,KAAOA,EAErC,EA6JIC,sBAnHJ,SAA+BC,GAC3BnB,EAAoB7lB,KAAKgnB,EAC7B,EAkHIC,uBA1GJ,SAAgCD,GAC5BlB,EAAqB9lB,KAAKgnB,EAC9B,EAyGIR,qBACAU,6BAjCJ,WACIvB,EAAmB,EACvB,EAgCIwB,kBAjIJ,WACI,OAAOhB,CACX,EAgIIiB,6BAtPJ,WACI,OAAOnB,CACX,EAqPIoB,uCAnTJ,WACI,OAAOnB,CACX,EAkTIoB,yBA5SJ,WACI,OAAOvB,CACX,EA2SIwB,0BArSJ,WACI,OAAOvB,CACX,EAoSIwB,uBA1FJ,WACI,OAAO3B,CACX,EAyFI4B,wBAnFJ,WACI,OAAO3B,CACX,EAkFI4B,oBA/DJ,WACI,OAAO/B,CACX,EA8DIgC,6BApBJ,SAAsC5oB,GAClC,MAAM6oB,EAAWhC,EAAmB7mB,GAEpC,YAAoBnP,IAAbg4B,EAAyBhC,EAAmBiC,QAAUD,CACjE,EAiBIE,iCApPJ,SAA0CrnB,GACtCwlB,EAA0BjmB,KAAKS,EACnC,EAmPIsnB,6BAjSJ,SAAsCtnB,GAClCslB,EAAsB/lB,KAAKS,EAC/B,EAgSIunB,8BAxRJ,SAAuCvnB,GACnCulB,EAAuBhmB,KAAKS,EAChC,EAuRIwnB,oBAtKJ,SAA6B1B,GACzB,GAAIA,EAAU,CACV,IAAIrmB,EAAQomB,EAAwBC,IAErB,IAAXrmB,GAEAimB,EAAepmB,OAAOG,EAAO,EAErC,MAEIimB,EAAiB,EAEzB,EA2JI+B,uBAtJJ,WACI/B,EAAiB,EACrB,EAqJIgC,yBAnHJ,SAAkCnB,GAC9BX,EAAkBR,EAAqBmB,EAC3C,EAkHIoB,0BA5GJ,SAAmCpB,GAC/BX,EAAkBP,EAAsBkB,EAC5C,EA2GIP,wBACA7lB,MA7VJ,WACIwlB,GACJ,EA4VIiC,yCA3UJ,WACInC,EAAsC,IAC1C,EA0UIoC,+BA9CJ,WACI,IAAIC,EAAyBxtB,EAASV,MAAM+I,UAAU6E,mBAAmBS,oBACzE8d,EAAmB+B,EAAuB5f,OAAQ4f,EAAuBvqB,MAC7E,EA4CIwqB,UA5VJ,WAEA,EA2VIC,uCArVJ,SAAgDC,GAC5CxC,EAAsCwC,CAC1C,EAoVIC,6BA5CJ,SAASA,EAA6B5pB,EAAMf,GACnCe,EAKD6mB,EAAmB7mB,KAAUf,EAJ7B4qB,OAAOC,KAAKjD,GAAoBroB,SAAQyU,IACpC2W,EAA6B3W,EAAKhU,EAAM,GAKpD,EAqCI8qB,mCA1PJ,SAA4CroB,GACxC4lB,EAAkBJ,EAA2BxlB,EACjD,EAyPIsoB,+BA7RJ,SAAwCtoB,GACpC4lB,EAAkBN,EAAuBtlB,EAC7C,EA4RIuoB,gCAtRJ,SAAyCvoB,GACrC4lB,EAAkBL,EAAwBvlB,EAC9C,GAnGImlB,EAAqB,CACjBiC,SApByB,GAsB7BzB,IAyXGnrB,CACX,CAEAyqB,EAAsBxnB,sBAAwB,wBAC9C,UAAeE,EAAAA,QAAaC,oBAAoBqnB,E,oDCtZhD,SAASuD,IAoHL,MAAO,CACHC,qBApHuB,GAqHvBj6B,OApHS,EAqHTk6B,IAnHJ,SAAa73B,EAAOJ,GAChB,IAAIlC,EAGJ,IAAKA,EAAI,EAAIA,EAAI6H,KAAKqyB,qBAAqBj6B,QAAYqC,EAAQuF,KAAKqyB,qBAAqBl6B,GAAGsC,MAAQtC,KAIpG,IAFA6H,KAAKqyB,qBAAqBnpB,OAAO/Q,EAAG,EAAG,CAAEsC,MAAOA,EAAOJ,IAAKA,IAEvDlC,EAAI,EAAGA,EAAI6H,KAAKqyB,qBAAqBj6B,OAAS,EAAGD,IAC9C6H,KAAKuyB,YAAYp6B,EAAGA,EAAI,IACxBA,IAGR6H,KAAK5H,OAAS4H,KAAKqyB,qBAAqBj6B,MAC5C,EAsGIo6B,MApGJ,WACIxyB,KAAKqyB,qBAAuB,GAC5BryB,KAAK5H,OAAS,CAClB,EAkGIq6B,OAhGJ,SAAgBh4B,EAAOJ,GACnB,IAAK,IAAIlC,EAAI,EAAGA,EAAI6H,KAAKqyB,qBAAqBj6B,OAAQD,IAClD,GAAIsC,GAASuF,KAAKqyB,qBAAqBl6B,GAAGsC,OAASJ,GAAO2F,KAAKqyB,qBAAqBl6B,GAAGkC,IASnF2F,KAAKqyB,qBAAqBnpB,OAAO/Q,EAAG,GACpCA,QAEG,IAAIsC,EAAQuF,KAAKqyB,qBAAqBl6B,GAAGsC,OAASJ,EAAM2F,KAAKqyB,qBAAqBl6B,GAAGkC,IAAK,CAG7F2F,KAAKqyB,qBAAqBnpB,OAAO/Q,EAAI,EAAG,EAAG,CAAEsC,MAAOJ,EAAKA,IAAK2F,KAAKqyB,qBAAqBl6B,GAAGkC,MAC3F2F,KAAKqyB,qBAAqBl6B,GAAGkC,IAAMI,EACnC,KACJ,CAAWA,EAAQuF,KAAKqyB,qBAAqBl6B,GAAGsC,OAASA,EAAQuF,KAAKqyB,qBAAqBl6B,GAAGkC,IAM1F2F,KAAKqyB,qBAAqBl6B,GAAGkC,IAAMI,EAC5BJ,EAAM2F,KAAKqyB,qBAAqBl6B,GAAGsC,OAASJ,EAAM2F,KAAKqyB,qBAAqBl6B,GAAGkC,MAMtF2F,KAAKqyB,qBAAqBl6B,GAAGsC,MAAQJ,EACzC,CAGJ2F,KAAK5H,OAAS4H,KAAKqyB,qBAAqBj6B,MAC5C,EA2DIm6B,YAzDJ,SAAqBG,EAAaC,GAC9B,IAAIC,EAAS5yB,KAAKqyB,qBAAqBK,GACnCG,EAAS7yB,KAAKqyB,qBAAqBM,GAEvC,OAAIC,EAAOn4B,OAASo4B,EAAOp4B,OAASo4B,EAAOp4B,OAASm4B,EAAOv4B,KAAOu4B,EAAOv4B,KAAOw4B,EAAOx4B,KAGnFu4B,EAAOv4B,IAAMw4B,EAAOx4B,IACpB2F,KAAKqyB,qBAAqBnpB,OAAOypB,EAAa,IACvC,GAEAE,EAAOp4B,OAASm4B,EAAOn4B,OAASm4B,EAAOn4B,OAASo4B,EAAOx4B,KAAOw4B,EAAOx4B,KAAOu4B,EAAOv4B,KAG1Fu4B,EAAOn4B,MAAQo4B,EAAOp4B,MACtBuF,KAAKqyB,qBAAqBnpB,OAAOypB,EAAa,IACvC,GACAE,EAAOp4B,OAASm4B,EAAOn4B,OAASm4B,EAAOn4B,OAASo4B,EAAOx4B,KAAOu4B,EAAOv4B,KAAOw4B,EAAOx4B,KAG1F2F,KAAKqyB,qBAAqBnpB,OAAOwpB,EAAa,IACvC,GACAE,EAAOn4B,OAASo4B,EAAOp4B,OAASo4B,EAAOp4B,OAASm4B,EAAOv4B,KAAOw4B,EAAOx4B,KAAOu4B,EAAOv4B,MAG1F2F,KAAKqyB,qBAAqBnpB,OAAOypB,EAAa,IACvC,EAGf,EA6BIl4B,MA3BJ,SAAe4O,GAGX,OAFAypB,EAAAA,EAAAA,cAAazpB,GAETA,GAASrJ,KAAKqyB,qBAAqBj6B,QAAUiR,EAAQ,EAC9CwF,IAGJ7O,KAAKqyB,qBAAqBhpB,GAAO5O,KAC5C,EAoBIJ,IAlBJ,SAAagP,GAGT,OAFAypB,EAAAA,EAAAA,cAAazpB,GAETA,GAASrJ,KAAKqyB,qBAAqBj6B,QAAUiR,EAAQ,EAC9CwF,IAGJ7O,KAAKqyB,qBAAqBhpB,GAAOhP,GAC5C,EAYJ,CAEA+3B,EAAiB/qB,sBAAwB,mBACzC,UAAeE,EAAAA,QAAaiE,gBAAgB4mB,E,2MClIrC,SAASrC,EAAmBgD,EAAW7qB,GAC1C,UAAW6qB,IAAc7qB,EACrB,MAAMqF,EAAAA,QAAUic,kBAExB,CAEO,SAASsJ,EAAaC,GAGzB,GAF4B,OAAdA,GAAuBC,MAAMD,IAAeA,EAAY,GAAM,EAGxE,MAAMxlB,EAAAA,QAAUic,mBAAqB,+BAE7C,CAEO,SAASyJ,EAAWF,EAAWhgB,EAAKC,GACvC,GAAI+f,EAAYhgB,GAAOggB,EAAY/f,EAC/B,MAAMzF,EAAAA,QAAUic,mBAAqB,0BAE7C,CAEO,SAAS0J,EAAwBhrB,GACpC,GAAoB,iBAATA,GAAsBA,IAASqF,EAAAA,QAAUqb,OAAS1gB,IAASqF,EAAAA,QAAUob,MAC5E,MAAMpb,EAAAA,QAAUic,kBAExB,C,8GCpBA,MAAMlV,EAIF2M,WAAAA,GAKIjhB,KAAKmzB,MAAQ,KAabnzB,KAAKkI,KAAO,KAKZlI,KAAKgb,IAAM,KAKXhb,KAAKozB,UAAY,KAKjBpzB,KAAKqzB,MAAQ,KAKbrzB,KAAKszB,SAAW,KAKhBtzB,KAAKuzB,UAAY,KAKjBvzB,KAAKwzB,aAAe,KAKpBxzB,KAAKyzB,SAAW,KAKhBzzB,KAAK0zB,MAAQ,GAKb1zB,KAAKoZ,KAAO,KAMZpZ,KAAK2zB,QAAU,KAKf3zB,KAAK4zB,SAAW,KAKhB5zB,KAAK6zB,eAAiB,KAKtB7zB,KAAK8zB,iBAAmB,KAKxB9zB,KAAK+zB,iBAAmB,KAIxB/zB,KAAKg0B,gBAAkB,KAIvBh0B,KAAKi0B,sBAAwB,IACjC,EAOJ,MAAMC,EAIFjT,WAAAA,GAKIjhB,KAAK0a,EAAI,KAKT1a,KAAKm0B,EAAI,KAKTn0B,KAAKo0B,EAAI,EACb,EAGJ9f,EAAY+f,IAAM,MAClB/f,EAAYggB,KAAO,OACnBhgB,EAAYC,SAAW,MACvBD,EAAYE,qBAAuB,iBACnCF,EAAYI,kBAAoB,wBAChCJ,EAAYM,mBAAqB,eACjCN,EAAYG,mBAAqB,eACjCH,EAAYK,iCAAmC,4BAC/CL,EAAYO,+BAAiC,sBAC7CP,EAAYigB,mBAAqB,eACjCjgB,EAAYQ,QAAU,UACtBR,EAAYkgB,sBAAwB,kBACpClgB,EAAYS,WAAa,O,GCnLrB0f,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqB57B,IAAjB67B,EACH,OAAOA,EAAa/4B,QAGrB,IAAID,EAAS64B,EAAyBE,GAAY,CAGjD94B,QAAS,CAAC,GAOX,OAHAg5B,EAAoBF,GAAU10B,KAAKrE,EAAOC,QAASD,EAAQA,EAAOC,QAAS64B,GAGpE94B,EAAOC,OACf,CCtBA64B,EAAoBI,KAAO,CAAC,ECC5BJ,EAAoBP,EAAI,SAASt4B,EAASk5B,GACzC,IAAI,IAAI5Z,KAAO4Z,EACXL,EAAoBM,EAAED,EAAY5Z,KAASuZ,EAAoBM,EAAEn5B,EAASsf,IAC5E4W,OAAOkD,eAAep5B,EAASsf,EAAK,CAAE+Z,YAAY,EAAM1xB,IAAKuxB,EAAW5Z,IAG3E,ECPAuZ,EAAoBM,EAAI,SAASzqB,EAAK5G,GAAQ,OAAOouB,OAAOoD,UAAUzrB,eAAezJ,KAAKsK,EAAK5G,EAAO,ECCtG+wB,EAAoB7Z,EAAI,SAAShf,GACX,oBAAXu5B,QAA0BA,OAAOC,aAC1CtD,OAAOkD,eAAep5B,EAASu5B,OAAOC,YAAa,CAAEluB,MAAO,WAE7D4qB,OAAOkD,eAAep5B,EAAS,aAAc,CAAEsL,OAAO,GACvD,E,sDC4BA,MAAMmuB,EACFrU,WAAAA,GACIjhB,KAAKu1B,OAAS,KAIdv1B,KAAKw1B,UAAY,KAoBjBx1B,KAAKy1B,OAAS,KAIdz1B,KAAKgb,IAAM,KAOXhb,KAAK01B,UAAY,KAOjB11B,KAAK21B,gBAAkB,IAK3B,EAGJL,EAAUM,6BAA+B,MACzCN,EAAUO,sBAAwB,MAClCP,EAAUQ,iBAAmB,MAC7BR,EAAUS,mBAAqB,MAC/BT,EAAUU,iBAAmB,MAC7BV,EAAUW,sBAAwB,MAClCX,EAAUY,oBAAsB,MAChCZ,EAAUa,iBAAmB,MAC7Bb,EAAUc,gBAAkB,MAE5B,Q,kBC7DA,SAASC,EAAoBxyB,GAGzB,IAAIO,EACAkyB,EACJ,MAAMvyB,GAHNF,EAASA,GAAU,CAAC,GAGIE,SAClBwyB,EAAc1yB,EAAO0yB,YACrBC,EAAmB3yB,EAAO2yB,iBAE1BzvB,EAASlD,EAAOsK,OAEtB,SAASsoB,EAAO5G,GACZ,IAAImF,EAAI,IAAIM,EAEZ,GAAKgB,EAAL,CAIA,IAAK,MAAMnb,KAAO0U,EACVA,EAAGnmB,eAAeyR,KAClB6Z,EAAE7Z,GAAO0U,EAAG1U,IAIf6Z,EAAEO,SACHP,EAAEO,OAASe,EAAI3Y,aAAe2Y,EAAItb,KAGjCga,EAAES,SACHT,EAAES,OAAS,IAAItvB,MAGnBowB,EAAYG,aAAa1B,EAhBzB,CAiBJ,CAEA,SAAS2B,EAAiBpb,GAClBA,EAAEpW,QAINmxB,EAAM/a,EAAEqb,SACZ,CAEA,SAASC,EAAyBtb,GAC9Bkb,EAAO,CACHjB,UAAWF,EAAUa,iBACrBR,gBAAiBpa,EAAEub,OAE3B,CAEA,SAASC,IACLN,EAAO,CACHjB,UAAWF,EAAUc,iBAE7B,CAiBA,SAASY,EAAczb,GAfvB,IAA0BsU,EAgBdtU,EAAE0b,SACDT,EAAiBU,eAhBD,KADHrH,EAkBGtU,EAAEpU,OAjBnBqsB,cACgB,MAAnB3D,EAAG2D,cACH3D,EAAG2D,cAAgB,KACnB3D,EAAG2D,aAAe,KAClB3D,EAAG2D,cAAgB,MACpBiD,EAAO,CACHjB,UAAW3F,EAAG2D,cAAgB8B,EAAUU,iBACxChb,IAAK6U,EAAG7U,IACRya,OAAQ5F,EAAG0D,UACXoC,gBAAiB9F,EAAGkE,kBAahC,CAEA,SAASoD,EAAgB5b,GACrB,IACIia,EAEJ,OAHaja,EAAEpW,MAAQoW,EAAEpW,MAAMrN,KAAO,GAIlC,KAAKs/B,WAAWC,kBACZ7B,EAAYF,EAAUU,iBACtB,MACJ,KAAKoB,WAAWE,iBACZ9B,EAAYF,EAAUY,oBACtB,MACJ,QACI,OAGRO,EAAO,CACHjB,UAAWA,GAEnB,CAyCA,OALApxB,EAAW,CACPmzB,WAnCJ,WACIxzB,EAASuF,GAAGvC,EAAOub,iBAAkBqU,EAAkBvyB,GACvDL,EAASuF,GACLvC,EAAO+b,4CACP+T,EACAzyB,GAEJL,EAASuF,GAAGvC,EAAOue,aAAc0R,EAAe5yB,GAChDL,EAASuF,GAAGvC,EAAOwe,eAAgByR,EAAe5yB,GAClDL,EAASuF,GAAGvC,EAAOigB,eAAgBmQ,EAAiB/yB,GACpDL,EAASuF,GACLklB,EAAAA,QAAuBE,wBACvBqI,EACA3yB,EAER,EAqBI2F,MAnBJ,WACIhG,EAASqF,IAAIrC,EAAOub,iBAAkBqU,EAAkBvyB,GACxDL,EAASqF,IACLrC,EAAO+b,4CACP+T,EACAzyB,GAEJL,EAASqF,IAAIrC,EAAOue,aAAc0R,EAAe5yB,GACjDL,EAASqF,IAAIrC,EAAOwe,eAAgByR,EAAe5yB,GACnDL,EAASqF,IAAIrC,EAAOigB,eAAgBmQ,EAAiB/yB,GACrDL,EAASqF,IACLolB,EAAAA,QAAuBE,wBACvBqI,EACA3yB,EAER,GAOOA,CACX,CAEAiyB,EAAoBhvB,sBAAwB,sBAC5C,MAAeE,EAAAA,QAAaC,oBAAoB6uB,G,UCjJhD,SAASmB,EAAgB3zB,GAErBA,EAASA,GAAU,CAAC,EACpB,IAEIO,EACAqzB,EAHAC,GAAmB,EACnB5zB,EAAU9D,KAAK8D,QAIf6zB,EAAe9zB,EAAO8zB,aAyD1B,OARAvzB,EAAW,CACPmzB,WAhDJ,SAAoBK,GACZA,GAAMA,EAAGx/B,SACTw/B,EAAGlxB,SAAQmU,IACP,IAAIpgB,EAAQogB,EAAEgd,UACVx9B,EAAMI,EAAQogB,EAAEid,SAEpBL,EAAOnF,IAAI73B,EAAOJ,EAAI,IAG1Bq9B,IAAqBE,EAAG,GAAGG,kBAEnC,EAsCIhuB,MApCJ,WACI0tB,EAAOjF,OACX,EAmCIwF,UA7BJ,WACI,IACIC,EADAC,EAAYT,EAAOr/B,OAGvB,IAAK8/B,EACD,OAAO,EAKXD,EAAOP,GACF,IAAIvxB,MAAOC,UAAY,IACxBuxB,EAAaQ,YAEjB,IAAK,IAAIhgC,EAAI,EAAGA,EAAI+/B,EAAW//B,GAAK,EAAG,CACnC,IAAIsC,EAAQg9B,EAAOh9B,MAAMtC,GACrBkC,EAAMo9B,EAAOp9B,IAAIlC,GAErB,GAAKsC,GAASw9B,GAAUA,EAAO59B,EAC3B,OAAO,CAEf,CAEA,OAAO,CACX,GA3BIo9B,GAASrF,EAAAA,EAAAA,SAAiBtuB,GAAS2H,SAqChCrH,CACX,CAEAozB,EAAgBnwB,sBAAwB,kBACxC,MAAeE,EAAAA,QAAaiE,gBAAgBgsB,GCnE5C,SAASY,IA2DL,MAAO,CACHC,UAvDJ,SAASA,EAAUpB,GACf,IAEI9b,EACAhU,EAHAmxB,EAAQ,GACR/tB,EAAM,GASV,IAAK4Q,KAAO8b,EACR,GAAIA,EAAOvtB,eAAeyR,IAA8B,IAArBA,EAAItc,QAAQ,KAAa,CAUxD,GATAsI,EAAQ8vB,EAAO9b,GAIVhU,UACDA,EAAQ,IAIRnC,MAAMuzB,QAAQpxB,GAAQ,CAEtB,IAAKA,EAAM/O,OACP,SAGJmS,EAAM,GAENpD,EAAMT,SAAQ,SAAU8xB,GACpB,IAAIC,EAA+D,WAAnD1G,OAAOoD,UAAU7X,SAASrd,KAAKu4B,GAAGhgC,MAAM,GAAI,GAE5D+R,EAAIpB,KAAKsvB,EAAYD,EAAIH,EAAUG,GACvC,IAEArxB,EAAQoD,EAAInK,IAAIkb,oBAAoBliB,KAAK,IAC7C,KAA4B,iBAAV+N,EACdA,EAAQmU,mBAAmBnU,GACpBA,aAAiBhB,KACxBgB,EAAQA,EAAMuxB,cACU,iBAAVvxB,IACdA,EAAQgW,KAAKwb,MAAMxxB,IAGvBmxB,EAAMnvB,KAAKgS,EAAM,IAAMhU,EAC3B,CAKJ,OAAOmxB,EAAMl/B,KAAK,IACtB,EAKJ,CAEAg/B,EAAiB/wB,sBAAwB,mBACzC,MAAeE,EAAAA,QAAaC,oBAAoB4wB,GCjEhD,SAASQ,IAIL,IAWIC,EACAxvB,EACAjF,EAbA00B,EAASh9B,OAAOg9B,QAAUh9B,OAAOi9B,SAGjCC,EAAYC,YACZC,EAAY/b,KAAKgc,IAAI,EAAiC,EAA9BH,EAAUvZ,mBAAyB,EAW/D,SAAS8X,IACDuB,IACKD,IACDA,EAAgB,IAAIG,EATP,KAWjBF,EAAOM,gBAAgBP,GACvBxvB,EAAQ,EAEhB,CAiCA,OANAjF,EAAW,CACPgZ,OA1BJ,SAAcrK,EAAKC,GACf,IAAI6H,EAqBJ,OAnBK9H,IACDA,EAAM,GAGLC,IACDA,EAAM,GAGN8lB,GACIzvB,IAAUwvB,EAAczgC,QACxBm/B,IAGJ1c,EAAIge,EAAcxvB,GAAS6vB,EAC3B7vB,GAAS,GAETwR,EAAIsC,KAAKC,SAGLvC,GAAK7H,EAAMD,GAAQA,CAC/B,GAMAwkB,IAEOnzB,CACX,CAEAw0B,EAAIvxB,sBAAwB,MAC5B,MAAeE,EAAAA,QAAaC,oBAAoBoxB,G,UChEhD,SAASS,EAAax1B,GAElB,IAAIO,EADJP,EAASA,GAAU,CAAC,EAGpB,IACIy1B,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAPA91B,EAAU9D,KAAK8D,QAWf+1B,EAAkB,GAEtB,MAAMrD,EAAmB3yB,EAAO2yB,iBAuHhC,SAASsD,IACLL,GAA+B,EAC/BC,GAAoB,EACpBC,EAAe,KACfC,EAAkB,IACtB,CAmBA,OARAx1B,EAAW,CACPqyB,OA3FJ,SAAgBvuB,EAAM6xB,GACb/0B,MAAMuzB,QAAQwB,KACfA,EAAM,CAACA,IAOPL,GAAqBE,EAAgB5B,aAIrC+B,EAAIrzB,SAAQ,SAAUmpB,GAClB,IAAI7U,EAAMse,EAAiBjB,UAAUxI,GAGV3nB,IAASsuB,EAAiBwD,aACjDhf,EAAM,cAAc9S,KAAQ8S,KAMhCA,EAAM,GAAG2e,KAAgB3e,IA3DrC,SAAsBA,EAAKif,EAAWC,GAClC,IAAIC,EAAM,IAAIC,eACdD,EAAIE,gBAAkBd,EAAsBzI,6BAA6B0F,EAAiB8D,iCAC1F,MAAMC,EAAa,WACf,IAAIC,EAAWX,EAAgBh7B,QAAQs7B,IAErB,IAAdK,IAGAX,EAAgB3wB,OAAOsxB,EAAU,KAGhCL,EAAIM,QAAU,KAASN,EAAIM,OAAS,OAKjCP,GACAA,KAGZ,EAEAL,EAAgB1wB,KAAKgxB,GAErB,IACIA,EAAIO,KAAK,MAAO1f,GAChBmf,EAAIQ,UAAYJ,EAChBJ,EAAIS,QAAUL,EACdJ,EAAIU,MACR,CAAE,MAAOtf,GACL4e,EAAIS,SACR,CACJ,CA8BYE,CAAa9f,EAAK,GAAM,WAOpB0e,GAAoB,CACxB,GACJ,GAER,EAqDInC,WAnDJ,SAAoBT,EAAOiE,GACvB,IAAIC,EAQJ,GANApB,EAAkBmB,EAElBpB,EAAe7C,EAAMmE,iBAIhBtB,EACD,MAAM,IAAIpxB,MACN,iDAOHkxB,IACDuB,EAAclE,EAAMoE,eAMhBF,IAAgC,MAAhBA,GAA0BA,EAAc,KAASxB,EAAsBpc,YACvFsc,GAAoB,GAGxBD,GAA+B,EAEvC,EAqBI1vB,MAZJ,WAMI+vB,GACJ,GAlIIR,EAAmBlB,EAAiBt0B,GAASG,cAC7Cu1B,EAAwBZ,EAAI90B,GAASG,cACrCs1B,GAAwB1K,EAAAA,EAAAA,SAAsB/qB,GAASG,cAEvD61B,IAwIG11B,CACX,CAEAi1B,EAAahyB,sBAAwB,eACrC,MAAeE,EAAAA,QAAaiE,gBAAgB6tB,GCtK5C,SAAS8B,EAAiBt3B,GACtBA,EAASA,GAAU,CAAC,EAEpB,MAAMu3B,EAA6B,CAC/B,8BAA+B/B,GAG7Bv1B,EAAU9D,KAAK8D,QACrB,IAAIM,EACJ,MAAMi3B,EAASx3B,EAAO+B,MAAQ/B,EAAO+B,MAAMqB,UAAU7C,GAAY,CAAC,EAC5DoyB,EAAmB3yB,EAAO2yB,iBAC1B8E,EAAmBz3B,EAAOy3B,kBAAoB,CAAC,EAkCrD,OANAl3B,EAAW,CACPqH,OA3BJ,SAAgBqrB,EAAO8C,GACnB,IAAI2B,EAEJ,IACIA,EAAYH,EAA2BtE,EAAMxpB,aAAaxJ,GAAS2H,OAAO,CACtE+qB,iBAAkBA,EAClB8E,iBAAkBA,IAGtBC,EAAUhE,WAAWT,EAAO8C,EAChC,CAAE,MAAOre,GACLggB,EAAY,KACZF,EAAOl2B,MAAM,iEAAiE2xB,EAAMxpB,gBAAgBiO,EAAEtV,WAC1G,CAEA,OAAOs1B,CACX,EAYIC,SAVJ,SAAkBluB,EAAamuB,GAC3BL,EAA2B9tB,GAAemuB,CAC9C,EASIC,WAPJ,SAAoBpuB,UACT8tB,EAA2B9tB,EACtC,GAQOlJ,CACX,CAEA+2B,EAAiB9zB,sBAAwB,mBACzC,MAAeE,EAAAA,QAAaC,oBAAoB2zB,GCjDhD,SAASQ,EAAoB93B,GAEzB,IACIO,EADAw3B,EAAY,GAGhB,MAAMC,EAAmBV,EAAiBn7B,KAAK8D,SAASG,YAAYJ,GAgCpE,OANAO,EAAW,CACPmzB,WAzBJ,SAAoBgE,EAAW3B,GAK3B2B,EAAUvyB,MAAK6R,IACX,IAAIihB,EAAWD,EAAiBpwB,OAAOoP,EAAG+e,GAE1C,GAAIkC,EAEA,OADAF,EAAUzyB,KAAK2yB,IACR,CACX,GAER,EAaI/xB,MAXJ,WACI6xB,EAAUl1B,SAAQmU,GAAKA,EAAE9Q,UACzB6xB,EAAY,EAChB,EASInF,OAPJ,SAAgBvuB,EAAM6xB,GAClB6B,EAAUl1B,SAAQmU,GAAKA,EAAE4b,OAAOvuB,EAAM6xB,IAC1C,GAQO31B,CACX,CAEAu3B,EAAoBt0B,sBAAwB,sBAC5C,MAAeE,EAAAA,QAAaiE,gBAAgBmwB,GCvC5C,SAASI,IACL,MAAO,CACHC,0BAA2B,SAAU7gB,EAAKf,EAAGlS,GACzC,IAAI+zB,EAAK9gB,EAYT,OAVIf,IACA6hB,GAAM,IAAM7hB,EAERlS,GAAQA,EAAK9P,SACb6jC,GAAM,IAAM/zB,GAGhB+zB,GAAM,KAGHA,CACX,EAEAC,UAAW,SAAUC,GACjB,IAAKA,EACD,MAAM,IAAI5zB,MAAM,aAGpB,GAAIyqB,MAAMmJ,GACN,MAAM,IAAI5zB,MAAM,YAKpB,GAAI4zB,EAAO,EACP,MAAM,IAAI5zB,MAAM,sBAGpB,OAAO4zB,CACX,EAER,CAEAJ,EAAe10B,sBAAwB,iBACvC,MAAeE,EAAAA,QAAaC,oBAAoBu0B,GCzChD,SAASK,EAAmBv4B,GAGxB,IAAIO,EACAi4B,EACAjiB,EACA9e,EACAm4B,EACA6I,EANJz4B,EAASA,GAAU,CAAC,EAQpB,IAAIC,EAAU9D,KAAK8D,QACfy4B,EAAiBR,EAAej4B,GAASG,cAEzCu4B,EAAY,GAEhB,MAAMhG,EAAmB3yB,EAAO2yB,iBAgBhC,SAASiG,IACL,IAAI5M,EAfR,WACI,IACI,OAAOkC,OAAOC,KAAKwK,GAAWp8B,KAC1B+a,GAAOqhB,EAAUrhB,KACnBuhB,QACE,CAACC,EAAGvI,IACQuI,EAAE52B,MAAQquB,EAAEruB,MAAS42B,EAAIvI,GAG7C,CAAE,MAAO7Y,GACL,MACJ,CACJ,CAGaqhB,GAEL/M,GACIyM,IAAqBzM,EAAGgN,IACxBP,EAAmBzM,EAAGgN,EACtBR,EAAoB5F,OAAOn7B,EAAMu0B,GAG7C,CAiCA,OANAzrB,EAAW,CACPmzB,WA1BJ,SAAoBh9B,EAAUwgC,EAAIoB,GAC1BpB,IAGA3gB,EAAImiB,EAAeL,UAAUC,GAC7BE,EAAsBtB,EACtBz/B,EAAOihC,EAAeP,0BAA0BzhC,EAAU4hC,GAC1D1I,EAAWqJ,YAAYL,EAAkBriB,GAEjD,EAkBIrQ,MAhBJ,WACIgzB,cAActJ,GACdA,EAAW,KACXrZ,EAAI,EACJiiB,EAAsB,KACtBC,EAAmB,IACvB,EAWIU,gBATJ,SAAyB/F,EAAQpH,EAAI3nB,GAC7B+uB,IAAWT,EAAiByG,eAC5BT,EAAUt0B,GAAQ2nB,EAE1B,GAQOzrB,CACX,CAEAg4B,EAAmB/0B,sBAAwB,qBAC3C,MAAeE,EAAAA,QAAaiE,gBAAgB4wB,GCrB5C,EAAe70B,EAAAA,QAAaiE,iBAxD5B,SAA0B3H,GAGtB,IAAIO,EACAi4B,EAEAt4B,GAJJF,EAASA,GAAU,CAAC,GAIEE,SACtB,MAAMyyB,EAAmB3yB,EAAO2yB,iBAEhC,SAAS0G,IAELn5B,EAASqF,IACLolB,EAAAA,QAAuBC,gCACvByO,EACAl9B,MAKJ+D,EAAS+C,QAAQ0nB,EAAAA,QAAuBE,wBAC5C,CAiCA,OANAtqB,EAAW,CACPmzB,WA1BJ,SAAoB4F,EAAQpC,GACpBA,IACAsB,EAAsBtB,EAEtBh3B,EAASuF,GACLklB,EAAAA,QAAuBC,gCACvByO,EACAl9B,MAGZ,EAiBI+J,MAfJ,WACIsyB,EAAsB,IAC1B,EAcIW,gBAZJ,SAAyB/F,EAAQpH,GAEzBoH,IAAWT,EAAiBwD,YACxBqC,GACAA,EAAoB5F,OAAOQ,EAAQpH,EAG/C,GAQOzrB,CACX,ICtDA,SAASg5B,EAAgBv5B,GAGrB,IAAIO,EACAi4B,EACAjiB,EACAlS,EACA5M,EACAm4B,EANJ5vB,EAASA,GAAU,CAAC,EAQpB,IAAIw5B,EAAY,GAEZd,EAAiBR,EAAe/7B,KAAK8D,SAASG,cAElD,MAAMuyB,EAAmB3yB,EAAO2yB,iBAEhC,SAASiG,IACL,IAAI1C,EAAMsD,EAENtD,EAAI3hC,QACAikC,GACAA,EAAoB5F,OAAOn7B,EAAMy+B,GAIzCsD,EAAY,EAChB,CAgDA,OANAj5B,EAAW,CACPmzB,WAzCJ,SAAoBh9B,EAAUwgC,EAAIoB,EAAMmB,GAChCvC,IAIA3gB,EAAImiB,EAAeL,UAAUC,GAE7BE,EAAsBtB,EAElBuC,GAAeA,EAAYllC,SAC3B8P,EAAOo1B,GAGXhiC,EAAOihC,EAAeP,0BAClBzhC,EACA4hC,EACAmB,GAGJ7J,EAAWqJ,YAAYL,EAAkBriB,GAEjD,EAqBIrQ,MAnBJ,WACIgzB,cAActJ,GACdA,EAAW,KACXrZ,EAAI,KACJlS,EAAO,KACPm1B,EAAY,GACZhB,EAAsB,IAC1B,EAaIW,gBAXJ,SAAyB/F,EAAQpH,GACzBoH,IAAWT,EAAiBU,eACvBhvB,GAASA,IAAS2nB,EAAG3nB,MACtBm1B,EAAUl0B,KAAK0mB,GAG3B,GAQOzrB,CACX,CAEAg5B,EAAgB/1B,sBAAwB,kBACxC,MAAeE,EAAAA,QAAaiE,gBAAgB4xB,GC5E5C,SAASG,IAEL,IAAIn5B,EACAo5B,EACAnB,EA2BJ,OANAj4B,EAAW,CACPmzB,WApBJ,SAAoBj8B,EAAMy/B,GACtByC,EAAaliC,EACb+gC,EAAsBtB,CAC1B,EAkBIhxB,MAhBJ,WACIsyB,EAAsB,KACtBmB,OAAazkC,CACjB,EAcIikC,gBAZJ,SAAyB/F,EAAQpH,GAEzBoH,IAAWuG,GACPnB,GACAA,EAAoB5F,OAAO+G,EAAY3N,EAGnD,GAQOzrB,CACX,CAEAm5B,EAAqBl2B,sBAAwB,uBAC7C,MAAeE,EAAAA,QAAaiE,gBAAgB+xB,GClC5C,SAASE,EAAsB55B,GAG3B,IAAIO,EACJ,MAAMi3B,GAFNx3B,EAASA,GAAU,CAAC,GAEE+B,MAAQ/B,EAAO+B,MAAMqB,UAAU7C,GAAY,CAAC,EAGlE,IAAIs5B,EAAW,gDAEf,MAAM55B,EAAU9D,KAAK8D,QACrB,IAAI65B,EAAuB,CACvBC,YAAgBA,EAChBtI,UAAgBA,EAChBuI,SAAgBA,EAChBC,SAAgBP,EAChBQ,cAAgBR,EAChBS,QAAgBT,GA6CpB,OANAn5B,EAAW,CACPqH,OArCJ,SAAgBwyB,EAAU5B,GACtB,IACI3zB,EADA/I,EAAUs+B,EAASr+B,MAAM89B,GAG7B,GAAK/9B,EAAL,CAIA,KACI+I,EAAUi1B,EAAqBh+B,EAAQ,IAAImE,GAAS2H,OAAO,CACvD1H,SAAUF,EAAOE,SACjByyB,iBAAkB3yB,EAAO2yB,oBAGrBe,WACJ53B,EAAQ,GACR08B,EACA18B,EAAQ,GACRA,EAAQ,GAEhB,CAAE,MAAO4b,GACL7S,EAAU,KACV2yB,EAAOl2B,MAAM,4DAA4DxF,EAAQ,gBAAgBA,EAAQ,OAAOA,EAAQ,OAAO4b,EAAEtV,WACrI,CAEA,OAAOyC,CAnBP,CAoBJ,EAYI8yB,SAVJ,SAAkBrgB,EAAKzS,GACnBi1B,EAAqBxiB,GAAOzS,CAChC,EASIgzB,WAPJ,SAAoBvgB,UACTwiB,EAAqBxiB,EAChC,GAQO/W,CACX,CAEAq5B,EAAsBp2B,sBAAwB,wBAC9C,MAAeE,EAAAA,QAAaC,oBAAoBi2B,GCpEhD,SAASS,EAA0Br6B,GAE/BA,EAASA,GAAU,CAAC,EACpB,IAEIO,EAFA4D,EAAW,GAGf,MAAMlE,EAAU9D,KAAK8D,QACfC,EAAWF,EAAOE,SAClBgD,EAASlD,EAAOsK,OAEtB,IAAIgwB,EAAwBV,EAAsB35B,GAASG,YAAY,CACnE2B,MAAO/B,EAAO+B,MACd7B,SAAUF,EAAOE,SACjByyB,iBAAkB3yB,EAAO2yB,mBAG7B,SAAS4H,EAAO7iB,GACZvT,EAAStB,SAAQgC,IACbA,EAAQs0B,gBAAgBzhB,EAAE0b,OAAQ1b,EAAEpU,MAAOoU,EAAEzS,UAAU,GAE/D,CAsEA,OALA1E,EAAW,CACPmzB,WAhEJ,SAAoB9oB,EAAS4tB,GACzB5tB,EAAQ3M,MAAM,KAAK4E,SACf,CAAC23B,EAAGC,EAAMC,KACN,IAAI71B,EAKJ,IAAyB,IAApB21B,EAAEx/B,QAAQ,OAAoC,IAApBw/B,EAAEx/B,QAAQ,KAAa,CAClD,IAAI2/B,EAAQD,EAAGD,EAAO,GAElBE,IAC6B,IAAxBA,EAAM3/B,QAAQ,OACU,IAAxB2/B,EAAM3/B,QAAQ,OACnBw/B,GAAK,IAAMG,SAGJD,EAAGD,EAAO,GAEzB,CAEA51B,EAAUy1B,EAAsB1yB,OAC5B4yB,EACAhC,GAGA3zB,GACAV,EAASmB,KAAKT,EAClB,IAIR3E,EAASuF,GACLvC,EAAOue,aACP8Y,EACAh6B,GAGJL,EAASuF,GACLvC,EAAOwe,eACP6Y,EACAh6B,EAER,EAsBI2F,MApBJ,WACIhG,EAASqF,IACLrC,EAAOue,aACP8Y,EACAh6B,GAGJL,EAASqF,IACLrC,EAAOwe,eACP6Y,EACAh6B,GAGJ4D,EAAStB,SAAQgC,GAAWA,EAAQqB,UAEpC/B,EAAW,EACf,GAOO5D,CACX,CAEA85B,EAA0B72B,sBAAwB,4BAClD,MAAeE,EAAAA,QAAaiE,gBAAgB0yB,GC5F5C,SAASO,EAAkB56B,GAGvB,IAAI66B,EACArC,EACAzC,EACAx1B,EAJJP,EAASA,GAAU,CAAC,EAMpB,IAAIC,EAAU9D,KAAK8D,QAgCnB,SAASiG,IACD20B,GACAA,EAA0B30B,QAG1BsyB,GACAA,EAAoBtyB,QAGpB6vB,GACAA,EAAgB7vB,OAExB,CAOA,OALA3F,EAAW,CACPmzB,WA7CJ,SAAoBoH,GAChB,IACI/E,EAAkBpC,EAAgB1zB,GAAS2H,OAAO,CAC9CksB,aAAc9zB,EAAO8zB,eAGzBiC,EAAgBrC,WAAWoH,EAAaC,OAExCvC,EAAsBV,EAAoB73B,GAAS2H,OAAO,CACtD7F,MAAO/B,EAAO+B,MACd4wB,iBAAkB3yB,EAAO2yB,iBACzB8E,iBAAkBz3B,EAAOy3B,mBAG7Be,EAAoB9E,WAAWoH,EAAaE,UAAWjF,GAEvD8E,EAA4BR,EAA0Bp6B,GAAS2H,OAAO,CAClE7F,MAAO/B,EAAO+B,MACd7B,SAAUF,EAAOE,SACjByyB,iBAAkB3yB,EAAO2yB,iBACzBroB,OAAQtK,EAAOsK,SAGnBuwB,EAA0BnH,WAAWoH,EAAalwB,QAAS4tB,EAC/D,CAAE,MAAO9gB,GAEL,MADAxR,IACMwR,CACV,CACJ,EAkBIxR,MAAYA,GAGT3F,CACX,CAEAq6B,EAAkBp3B,sBAAwB,oBAC1C,MAAeE,EAAAA,QAAaiE,gBAAgBizB,GCxD5C,EATA,MACIxd,WAAAA,GAEIjhB,KAAKyO,QAAU,GACfzO,KAAK4+B,MAAQ,GACb5+B,KAAK6+B,UAAY,EACrB,GCMJ,EAZA,MACI5d,WAAAA,GAGIjhB,KAAK63B,UAAY,EACjB73B,KAAK83B,SAAWhY,IAGhB9f,KAAK+3B,mBAAoB,CAC7B,GCOJ,EAZA,MACI9W,WAAAA,GAEIjhB,KAAKsN,YAAc,GACnBtN,KAAKmH,MAAQ,GAGbnH,KAAKi7B,gBAAkB,GACvBj7B,KAAKk7B,eAVmB,GAW5B,GC1CJ,SAAS4D,EAAiBj7B,GAEtB,IAAIO,EACA26B,GAFJl7B,EAASA,GAAU,CAAC,GAECk7B,QACrB,MAAMC,EAAYn7B,EAAOm7B,UAgHzB,OAJA56B,EAAW,CACP66B,WAzEJ,SAAoBrI,GAChB,IAAInoB,EAAU,GAoEd,OAlEImoB,GAAYA,EAASsI,SACrBtI,EAASsI,QAAQx4B,SAAQuwB,IACrB,IAAIkI,EAAc,IAAID,EAClBE,EAAYL,EAAQM,aAAazI,GAEjCK,EAAOvtB,eAAe,aACtBy1B,EAAY1wB,QAAUwoB,EAAOxoB,QAK7BwoB,EAAO2H,OACP3H,EAAO2H,MAAMl4B,SAAQ2sB,IACjB,IAAIiM,EAAa,IAAIV,EAErBU,EAAWzH,UApD/B,SAAkCjB,EAAU2I,EAASlM,GACjD,IAAImM,EACAC,EACAC,EAAwB,EA4B5B,OA1BIH,EAKAG,EAAwBX,EAAQY,yBAAyB/I,GAAY,KAKrE4I,EAAYT,EAAQa,kBAAkBhJ,GAElC4I,EAAUpnC,SACVsnC,EAAwBF,EAAU,GAAG/kC,QAO7CglC,EAAqBC,EAEjBrM,GAASA,EAAM3pB,eAAes1B,EAAUtU,cACxC+U,GAAsBpM,EAAMwE,WAGzB4H,CACX,CAqBwBI,CAAyBjJ,EAAUwI,EAAW/L,GAE9CA,EAAM3pB,eAAe,YACrB41B,EAAWxH,SAAWzE,EAAMyE,SAI5BwH,EAAWxH,SAAWiH,EAAQe,YAAYlJ,GAG9C0I,EAAWvH,kBAAoBqH,EAE/BD,EAAYP,MAAMz1B,KAAKm2B,EAAW,IAItCrI,EAAO4H,YACP5H,EAAO4H,UAAUn4B,SAAQ60B,IACrB,IAAIwE,EAAiB,IAAIlB,EAErBtD,EAAU7xB,eAAes1B,EAAUvU,iBACnCsV,EAAezyB,YAAciuB,EAAUjuB,YAMvCiuB,EAAU7xB,eAAe,WACzBq2B,EAAe54B,MAAQo0B,EAAUp0B,OAGjCo0B,EAAU7xB,eAAes1B,EAAU/S,qBACnC8T,EAAe9E,gBAAkBM,EAAUyD,EAAU/S,oBAGrDsP,EAAU7xB,eAAes1B,EAAU9S,mBACnC6T,EAAe7E,eAAiBK,EAAUyD,EAAU9S,kBAGxDiT,EAAYN,UAAU11B,KAAK42B,GAAe,IAOlDtxB,EAAQtF,KAAKg2B,IAAY,IAI1B1wB,CACX,GAMOrK,CACX,CAEA06B,EAAgBz3B,sBAAwB,kBACxC,MAAeE,EAAAA,QAAaC,oBAAoBs3B,GCzFhD,SAASkB,EAA4Bn8B,GAGjC,IAAIO,EADJP,EAASA,GAAU,CAAC,EAEpB,IAAIo8B,EAAqB,CAAC,EACtBn8B,EAAU9D,KAAK8D,QACfC,EAAWF,EAAOE,SACtB,MAAMoK,EAAStK,EAAOsK,OAEtB,SAASmM,EAAOiB,GACZ,GAAIA,EAAEpW,MACF,OAIJ,IAAI+6B,EAAsBnO,OAAOC,KAAKiO,GAEtBnB,EAAgBh7B,GAASG,YAAY,CACjD86B,QAASl7B,EAAOk7B,QAChBC,UAAWn7B,EAAOm7B,YACnBC,WAAW1jB,EAAEqb,UAERlwB,SAAQ23B,IACZ,MAAMljB,EAAMzjB,KAAKC,UAAU0mC,GAE3B,GAAK4B,EAAmBv2B,eAAeyR,GAUnC+kB,EAAoBh3B,OAAOiS,EAAK,QAThC,IACI,IAAIglB,EAAa1B,EAAkB36B,GAAS2H,OAAO5H,GACnDs8B,EAAW5I,WAAW8G,GACtB4B,EAAmB9kB,GAAOglB,CAC9B,CAAE,MAAO5kB,GACL,CAKR,IAIJ2kB,EAAoBx5B,SAAQwW,IACxB+iB,EAAmB/iB,GAAGnT,eACfk2B,EAAmB/iB,EAAE,IAGhCnZ,EAAS+C,QAAQ0nB,EAAAA,QAAuBC,gCAC5C,CAEA,SAAS2R,IACLrO,OAAOC,KAAKiO,GAAoBv5B,SAAQyU,IACpC8kB,EAAmB9kB,GAAKpR,OAAO,IAGnCk2B,EAAqB,CAAC,CAC1B,CAiBA,OALA77B,EAAW,CACP2F,MANJ,WACIhG,EAASqF,IAAI+E,EAAOmU,iBAAkBhI,EAAQlW,GAC9CL,EAASqF,IAAI+E,EAAOgY,yBAA0Bia,EAAyBh8B,EAC3E,GAPIL,EAASuF,GAAG6E,EAAOmU,iBAAkBhI,EAAQlW,GAC7CL,EAASuF,GAAG6E,EAAOgY,yBAA0Bia,EAAyBh8B,GAanEA,CACX,CAEA47B,EAA4B34B,sBAAwB,8BACpD,MAAeE,EAAAA,QAAaiE,gBAAgBw0B,GC1E5C,SAASK,IAEL,IACIj8B,EACAk8B,EAFAx8B,EAAU9D,KAAK8D,QA0CnB,OANAM,EAAW,CACPm8B,uBA5BJ,SAAgC18B,GAQ5B,OAPAy8B,EAAsBjK,EAAoBvyB,GAASG,YAAY,CAC3DF,SAAUF,EAAOE,SACjBwyB,YAAa1yB,EAAO0yB,YACpBC,iBAAkB3yB,EAAO2yB,iBACzBroB,OAAQtK,EAAOsK,SAEnBmyB,EAAoB/I,aACbyI,EAA4Bl8B,GAAS2H,OAAO5H,EACvD,EAoBI28B,oBAdJ,WACI,OAAOrF,EAAiBr3B,GAASG,aACrC,EAaIw8B,yBAPJ,WACI,OAAOhD,EAAsB35B,GAASG,aAC1C,GAQOG,CACX,CAEAi8B,EAAiBh5B,sBAAwB,mBACzC,MAAMC,EAAUo5B,OAAOn5B,aAAaiE,gBAAgB60B,GACpD/4B,EAAQ6G,OAASqgB,EAAAA,QACjBkS,OAAOn5B,aAAaoE,mBAAmB00B,EAAiBh5B,sBAAuBC,GAC/E,Q", "sources": ["webpack://dashjs/./node_modules/path-browserify/index.js", "webpack://dashjs/./node_modules/ua-parser-js/src/ua-parser.js", "webpack://dashjs/./src/core/Debug.js", "webpack://dashjs/./src/core/EventBus.js", "webpack://dashjs/./src/core/FactoryMaker.js", "webpack://dashjs/./src/core/Settings.js", "webpack://dashjs/./src/core/Utils.js", "webpack://dashjs/./src/core/events/CoreEvents.js", "webpack://dashjs/./src/core/events/Events.js", "webpack://dashjs/./src/core/events/EventsBase.js", "webpack://dashjs/./src/dash/vo/UTCTiming.js", "webpack://dashjs/./src/streaming/MediaPlayerEvents.js", "webpack://dashjs/./src/streaming/constants/Constants.js", "webpack://dashjs/./src/streaming/metrics/MetricsReportingEvents.js", "webpack://dashjs/./src/streaming/models/CustomParametersModel.js", "webpack://dashjs/./src/streaming/utils/CustomTimeRanges.js", "webpack://dashjs/./src/streaming/utils/SupervisorTools.js", "webpack://dashjs/./src/streaming/vo/metrics/HTTPRequest.js", "webpack://dashjs/webpack/bootstrap", "webpack://dashjs/webpack/runtime/amd options", "webpack://dashjs/webpack/runtime/define property getters", "webpack://dashjs/webpack/runtime/hasOwnProperty shorthand", "webpack://dashjs/webpack/runtime/make namespace object", "webpack://dashjs/./src/streaming/metrics/vo/DVBErrors.js", "webpack://dashjs/./src/streaming/metrics/utils/DVBErrorsTranslator.js", "webpack://dashjs/./src/streaming/metrics/controllers/RangeController.js", "webpack://dashjs/./src/streaming/metrics/utils/MetricSerialiser.js", "webpack://dashjs/./src/streaming/metrics/utils/RNG.js", "webpack://dashjs/./src/streaming/metrics/reporting/reporters/DVBReporting.js", "webpack://dashjs/./src/streaming/metrics/reporting/ReportingFactory.js", "webpack://dashjs/./src/streaming/metrics/controllers/ReportingController.js", "webpack://dashjs/./src/streaming/metrics/utils/HandlerHelpers.js", "webpack://dashjs/./src/streaming/metrics/metrics/handlers/BufferLevelHandler.js", "webpack://dashjs/./src/streaming/metrics/metrics/handlers/DVBErrorsHandler.js", "webpack://dashjs/./src/streaming/metrics/metrics/handlers/HttpListHandler.js", "webpack://dashjs/./src/streaming/metrics/metrics/handlers/GenericMetricHandler.js", "webpack://dashjs/./src/streaming/metrics/metrics/MetricsHandlerFactory.js", "webpack://dashjs/./src/streaming/metrics/controllers/MetricsHandlersController.js", "webpack://dashjs/./src/streaming/metrics/controllers/MetricsController.js", "webpack://dashjs/./src/streaming/metrics/vo/Metrics.js", "webpack://dashjs/./src/streaming/metrics/vo/Range.js", "webpack://dashjs/./src/streaming/metrics/vo/Reporting.js", "webpack://dashjs/./src/streaming/metrics/utils/ManifestParsing.js", "webpack://dashjs/./src/streaming/metrics/controllers/MetricsCollectionController.js", "webpack://dashjs/./src/streaming/metrics/MetricsReporting.js"], "sourcesContent": ["// 'path' module extracted from Node.js v8.11.1 (only the posix part)\n// transplited with Babel\n\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nfunction assertPath(path) {\n  if (typeof path !== 'string') {\n    throw new TypeError('Path must be a string. Received ' + JSON.stringify(path));\n  }\n}\n\n// Resolves . and .. elements in a path with directory names\nfunction normalizeStringPosix(path, allowAboveRoot) {\n  var res = '';\n  var lastSegmentLength = 0;\n  var lastSlash = -1;\n  var dots = 0;\n  var code;\n  for (var i = 0; i <= path.length; ++i) {\n    if (i < path.length)\n      code = path.charCodeAt(i);\n    else if (code === 47 /*/*/)\n      break;\n    else\n      code = 47 /*/*/;\n    if (code === 47 /*/*/) {\n      if (lastSlash === i - 1 || dots === 1) {\n        // NOOP\n      } else if (lastSlash !== i - 1 && dots === 2) {\n        if (res.length < 2 || lastSegmentLength !== 2 || res.charCodeAt(res.length - 1) !== 46 /*.*/ || res.charCodeAt(res.length - 2) !== 46 /*.*/) {\n          if (res.length > 2) {\n            var lastSlashIndex = res.lastIndexOf('/');\n            if (lastSlashIndex !== res.length - 1) {\n              if (lastSlashIndex === -1) {\n                res = '';\n                lastSegmentLength = 0;\n              } else {\n                res = res.slice(0, lastSlashIndex);\n                lastSegmentLength = res.length - 1 - res.lastIndexOf('/');\n              }\n              lastSlash = i;\n              dots = 0;\n              continue;\n            }\n          } else if (res.length === 2 || res.length === 1) {\n            res = '';\n            lastSegmentLength = 0;\n            lastSlash = i;\n            dots = 0;\n            continue;\n          }\n        }\n        if (allowAboveRoot) {\n          if (res.length > 0)\n            res += '/..';\n          else\n            res = '..';\n          lastSegmentLength = 2;\n        }\n      } else {\n        if (res.length > 0)\n          res += '/' + path.slice(lastSlash + 1, i);\n        else\n          res = path.slice(lastSlash + 1, i);\n        lastSegmentLength = i - lastSlash - 1;\n      }\n      lastSlash = i;\n      dots = 0;\n    } else if (code === 46 /*.*/ && dots !== -1) {\n      ++dots;\n    } else {\n      dots = -1;\n    }\n  }\n  return res;\n}\n\nfunction _format(sep, pathObject) {\n  var dir = pathObject.dir || pathObject.root;\n  var base = pathObject.base || (pathObject.name || '') + (pathObject.ext || '');\n  if (!dir) {\n    return base;\n  }\n  if (dir === pathObject.root) {\n    return dir + base;\n  }\n  return dir + sep + base;\n}\n\nvar posix = {\n  // path.resolve([from ...], to)\n  resolve: function resolve() {\n    var resolvedPath = '';\n    var resolvedAbsolute = false;\n    var cwd;\n\n    for (var i = arguments.length - 1; i >= -1 && !resolvedAbsolute; i--) {\n      var path;\n      if (i >= 0)\n        path = arguments[i];\n      else {\n        if (cwd === undefined)\n          cwd = process.cwd();\n        path = cwd;\n      }\n\n      assertPath(path);\n\n      // Skip empty entries\n      if (path.length === 0) {\n        continue;\n      }\n\n      resolvedPath = path + '/' + resolvedPath;\n      resolvedAbsolute = path.charCodeAt(0) === 47 /*/*/;\n    }\n\n    // At this point the path should be resolved to a full absolute path, but\n    // handle relative paths to be safe (might happen when process.cwd() fails)\n\n    // Normalize the path\n    resolvedPath = normalizeStringPosix(resolvedPath, !resolvedAbsolute);\n\n    if (resolvedAbsolute) {\n      if (resolvedPath.length > 0)\n        return '/' + resolvedPath;\n      else\n        return '/';\n    } else if (resolvedPath.length > 0) {\n      return resolvedPath;\n    } else {\n      return '.';\n    }\n  },\n\n  normalize: function normalize(path) {\n    assertPath(path);\n\n    if (path.length === 0) return '.';\n\n    var isAbsolute = path.charCodeAt(0) === 47 /*/*/;\n    var trailingSeparator = path.charCodeAt(path.length - 1) === 47 /*/*/;\n\n    // Normalize the path\n    path = normalizeStringPosix(path, !isAbsolute);\n\n    if (path.length === 0 && !isAbsolute) path = '.';\n    if (path.length > 0 && trailingSeparator) path += '/';\n\n    if (isAbsolute) return '/' + path;\n    return path;\n  },\n\n  isAbsolute: function isAbsolute(path) {\n    assertPath(path);\n    return path.length > 0 && path.charCodeAt(0) === 47 /*/*/;\n  },\n\n  join: function join() {\n    if (arguments.length === 0)\n      return '.';\n    var joined;\n    for (var i = 0; i < arguments.length; ++i) {\n      var arg = arguments[i];\n      assertPath(arg);\n      if (arg.length > 0) {\n        if (joined === undefined)\n          joined = arg;\n        else\n          joined += '/' + arg;\n      }\n    }\n    if (joined === undefined)\n      return '.';\n    return posix.normalize(joined);\n  },\n\n  relative: function relative(from, to) {\n    assertPath(from);\n    assertPath(to);\n\n    if (from === to) return '';\n\n    from = posix.resolve(from);\n    to = posix.resolve(to);\n\n    if (from === to) return '';\n\n    // Trim any leading backslashes\n    var fromStart = 1;\n    for (; fromStart < from.length; ++fromStart) {\n      if (from.charCodeAt(fromStart) !== 47 /*/*/)\n        break;\n    }\n    var fromEnd = from.length;\n    var fromLen = fromEnd - fromStart;\n\n    // Trim any leading backslashes\n    var toStart = 1;\n    for (; toStart < to.length; ++toStart) {\n      if (to.charCodeAt(toStart) !== 47 /*/*/)\n        break;\n    }\n    var toEnd = to.length;\n    var toLen = toEnd - toStart;\n\n    // Compare paths to find the longest common path from root\n    var length = fromLen < toLen ? fromLen : toLen;\n    var lastCommonSep = -1;\n    var i = 0;\n    for (; i <= length; ++i) {\n      if (i === length) {\n        if (toLen > length) {\n          if (to.charCodeAt(toStart + i) === 47 /*/*/) {\n            // We get here if `from` is the exact base path for `to`.\n            // For example: from='/foo/bar'; to='/foo/bar/baz'\n            return to.slice(toStart + i + 1);\n          } else if (i === 0) {\n            // We get here if `from` is the root\n            // For example: from='/'; to='/foo'\n            return to.slice(toStart + i);\n          }\n        } else if (fromLen > length) {\n          if (from.charCodeAt(fromStart + i) === 47 /*/*/) {\n            // We get here if `to` is the exact base path for `from`.\n            // For example: from='/foo/bar/baz'; to='/foo/bar'\n            lastCommonSep = i;\n          } else if (i === 0) {\n            // We get here if `to` is the root.\n            // For example: from='/foo'; to='/'\n            lastCommonSep = 0;\n          }\n        }\n        break;\n      }\n      var fromCode = from.charCodeAt(fromStart + i);\n      var toCode = to.charCodeAt(toStart + i);\n      if (fromCode !== toCode)\n        break;\n      else if (fromCode === 47 /*/*/)\n        lastCommonSep = i;\n    }\n\n    var out = '';\n    // Generate the relative path based on the path difference between `to`\n    // and `from`\n    for (i = fromStart + lastCommonSep + 1; i <= fromEnd; ++i) {\n      if (i === fromEnd || from.charCodeAt(i) === 47 /*/*/) {\n        if (out.length === 0)\n          out += '..';\n        else\n          out += '/..';\n      }\n    }\n\n    // Lastly, append the rest of the destination (`to`) path that comes after\n    // the common path parts\n    if (out.length > 0)\n      return out + to.slice(toStart + lastCommonSep);\n    else {\n      toStart += lastCommonSep;\n      if (to.charCodeAt(toStart) === 47 /*/*/)\n        ++toStart;\n      return to.slice(toStart);\n    }\n  },\n\n  _makeLong: function _makeLong(path) {\n    return path;\n  },\n\n  dirname: function dirname(path) {\n    assertPath(path);\n    if (path.length === 0) return '.';\n    var code = path.charCodeAt(0);\n    var hasRoot = code === 47 /*/*/;\n    var end = -1;\n    var matchedSlash = true;\n    for (var i = path.length - 1; i >= 1; --i) {\n      code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          if (!matchedSlash) {\n            end = i;\n            break;\n          }\n        } else {\n        // We saw the first non-path separator\n        matchedSlash = false;\n      }\n    }\n\n    if (end === -1) return hasRoot ? '/' : '.';\n    if (hasRoot && end === 1) return '//';\n    return path.slice(0, end);\n  },\n\n  basename: function basename(path, ext) {\n    if (ext !== undefined && typeof ext !== 'string') throw new TypeError('\"ext\" argument must be a string');\n    assertPath(path);\n\n    var start = 0;\n    var end = -1;\n    var matchedSlash = true;\n    var i;\n\n    if (ext !== undefined && ext.length > 0 && ext.length <= path.length) {\n      if (ext.length === path.length && ext === path) return '';\n      var extIdx = ext.length - 1;\n      var firstNonSlashEnd = -1;\n      for (i = path.length - 1; i >= 0; --i) {\n        var code = path.charCodeAt(i);\n        if (code === 47 /*/*/) {\n            // If we reached a path separator that was not part of a set of path\n            // separators at the end of the string, stop now\n            if (!matchedSlash) {\n              start = i + 1;\n              break;\n            }\n          } else {\n          if (firstNonSlashEnd === -1) {\n            // We saw the first non-path separator, remember this index in case\n            // we need it if the extension ends up not matching\n            matchedSlash = false;\n            firstNonSlashEnd = i + 1;\n          }\n          if (extIdx >= 0) {\n            // Try to match the explicit extension\n            if (code === ext.charCodeAt(extIdx)) {\n              if (--extIdx === -1) {\n                // We matched the extension, so mark this as the end of our path\n                // component\n                end = i;\n              }\n            } else {\n              // Extension does not match, so our result is the entire path\n              // component\n              extIdx = -1;\n              end = firstNonSlashEnd;\n            }\n          }\n        }\n      }\n\n      if (start === end) end = firstNonSlashEnd;else if (end === -1) end = path.length;\n      return path.slice(start, end);\n    } else {\n      for (i = path.length - 1; i >= 0; --i) {\n        if (path.charCodeAt(i) === 47 /*/*/) {\n            // If we reached a path separator that was not part of a set of path\n            // separators at the end of the string, stop now\n            if (!matchedSlash) {\n              start = i + 1;\n              break;\n            }\n          } else if (end === -1) {\n          // We saw the first non-path separator, mark this as the end of our\n          // path component\n          matchedSlash = false;\n          end = i + 1;\n        }\n      }\n\n      if (end === -1) return '';\n      return path.slice(start, end);\n    }\n  },\n\n  extname: function extname(path) {\n    assertPath(path);\n    var startDot = -1;\n    var startPart = 0;\n    var end = -1;\n    var matchedSlash = true;\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    var preDotState = 0;\n    for (var i = path.length - 1; i >= 0; --i) {\n      var code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          // If we reached a path separator that was not part of a set of path\n          // separators at the end of the string, stop now\n          if (!matchedSlash) {\n            startPart = i + 1;\n            break;\n          }\n          continue;\n        }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === 46 /*.*/) {\n          // If this is our first dot, mark it as the start of our extension\n          if (startDot === -1)\n            startDot = i;\n          else if (preDotState !== 1)\n            preDotState = 1;\n      } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n\n    if (startDot === -1 || end === -1 ||\n        // We saw a non-dot character immediately before the dot\n        preDotState === 0 ||\n        // The (right-most) trimmed path component is exactly '..'\n        preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n      return '';\n    }\n    return path.slice(startDot, end);\n  },\n\n  format: function format(pathObject) {\n    if (pathObject === null || typeof pathObject !== 'object') {\n      throw new TypeError('The \"pathObject\" argument must be of type Object. Received type ' + typeof pathObject);\n    }\n    return _format('/', pathObject);\n  },\n\n  parse: function parse(path) {\n    assertPath(path);\n\n    var ret = { root: '', dir: '', base: '', ext: '', name: '' };\n    if (path.length === 0) return ret;\n    var code = path.charCodeAt(0);\n    var isAbsolute = code === 47 /*/*/;\n    var start;\n    if (isAbsolute) {\n      ret.root = '/';\n      start = 1;\n    } else {\n      start = 0;\n    }\n    var startDot = -1;\n    var startPart = 0;\n    var end = -1;\n    var matchedSlash = true;\n    var i = path.length - 1;\n\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    var preDotState = 0;\n\n    // Get non-dir info\n    for (; i >= start; --i) {\n      code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          // If we reached a path separator that was not part of a set of path\n          // separators at the end of the string, stop now\n          if (!matchedSlash) {\n            startPart = i + 1;\n            break;\n          }\n          continue;\n        }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === 46 /*.*/) {\n          // If this is our first dot, mark it as the start of our extension\n          if (startDot === -1) startDot = i;else if (preDotState !== 1) preDotState = 1;\n        } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n\n    if (startDot === -1 || end === -1 ||\n    // We saw a non-dot character immediately before the dot\n    preDotState === 0 ||\n    // The (right-most) trimmed path component is exactly '..'\n    preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n      if (end !== -1) {\n        if (startPart === 0 && isAbsolute) ret.base = ret.name = path.slice(1, end);else ret.base = ret.name = path.slice(startPart, end);\n      }\n    } else {\n      if (startPart === 0 && isAbsolute) {\n        ret.name = path.slice(1, startDot);\n        ret.base = path.slice(1, end);\n      } else {\n        ret.name = path.slice(startPart, startDot);\n        ret.base = path.slice(startPart, end);\n      }\n      ret.ext = path.slice(startDot, end);\n    }\n\n    if (startPart > 0) ret.dir = path.slice(0, startPart - 1);else if (isAbsolute) ret.dir = '/';\n\n    return ret;\n  },\n\n  sep: '/',\n  delimiter: ':',\n  win32: null,\n  posix: null\n};\n\nposix.posix = posix;\n\nmodule.exports = posix;\n", "/////////////////////////////////////////////////////////////////////////////////\n/* UAParser.js v1.0.38\n   Copyright © 2012-2021 F<PERSON><PERSON> <<EMAIL>>\n   MIT License *//*\n   Detect Browser, Engine, OS, CPU, and Device type/model from User-Agent data.\n   Supports browser & node.js environment. \n   Demo   : https://faisalman.github.io/ua-parser-js\n   Source : https://github.com/faisalman/ua-parser-js */\n/////////////////////////////////////////////////////////////////////////////////\n\n(function (window, undefined) {\n\n    'use strict';\n\n    //////////////\n    // Constants\n    /////////////\n\n\n    var LIBVERSION  = '1.0.38',\n        EMPTY       = '',\n        UNKNOWN     = '?',\n        FUNC_TYPE   = 'function',\n        UNDEF_TYPE  = 'undefined',\n        OBJ_TYPE    = 'object',\n        STR_TYPE    = 'string',\n        MAJOR       = 'major',\n        MODEL       = 'model',\n        NAME        = 'name',\n        TYPE        = 'type',\n        VENDOR      = 'vendor',\n        VERSION     = 'version',\n        ARCHITECTURE= 'architecture',\n        CONSOLE     = 'console',\n        MOBILE      = 'mobile',\n        TABLET      = 'tablet',\n        SMARTTV     = 'smarttv',\n        WEARABLE    = 'wearable',\n        EMBEDDED    = 'embedded',\n        UA_MAX_LENGTH = 500;\n\n    var AMAZON  = 'Amazon',\n        APPLE   = 'Apple',\n        ASUS    = 'ASUS',\n        BLACKBERRY = 'BlackBerry',\n        BROWSER = 'Browser',\n        CHROME  = 'Chrome',\n        EDGE    = 'Edge',\n        FIREFOX = 'Firefox',\n        GOOGLE  = 'Google',\n        HUAWEI  = 'Huawei',\n        LG      = 'LG',\n        MICROSOFT = 'Microsoft',\n        MOTOROLA  = 'Motorola',\n        OPERA   = 'Opera',\n        SAMSUNG = 'Samsung',\n        SHARP   = 'Sharp',\n        SONY    = 'Sony',\n        XIAOMI  = 'Xiaomi',\n        ZEBRA   = 'Zebra',\n        FACEBOOK    = 'Facebook',\n        CHROMIUM_OS = 'Chromium OS',\n        MAC_OS  = 'Mac OS';\n\n    ///////////\n    // Helper\n    //////////\n\n    var extend = function (regexes, extensions) {\n            var mergedRegexes = {};\n            for (var i in regexes) {\n                if (extensions[i] && extensions[i].length % 2 === 0) {\n                    mergedRegexes[i] = extensions[i].concat(regexes[i]);\n                } else {\n                    mergedRegexes[i] = regexes[i];\n                }\n            }\n            return mergedRegexes;\n        },\n        enumerize = function (arr) {\n            var enums = {};\n            for (var i=0; i<arr.length; i++) {\n                enums[arr[i].toUpperCase()] = arr[i];\n            }\n            return enums;\n        },\n        has = function (str1, str2) {\n            return typeof str1 === STR_TYPE ? lowerize(str2).indexOf(lowerize(str1)) !== -1 : false;\n        },\n        lowerize = function (str) {\n            return str.toLowerCase();\n        },\n        majorize = function (version) {\n            return typeof(version) === STR_TYPE ? version.replace(/[^\\d\\.]/g, EMPTY).split('.')[0] : undefined;\n        },\n        trim = function (str, len) {\n            if (typeof(str) === STR_TYPE) {\n                str = str.replace(/^\\s\\s*/, EMPTY);\n                return typeof(len) === UNDEF_TYPE ? str : str.substring(0, UA_MAX_LENGTH);\n            }\n    };\n\n    ///////////////\n    // Map helper\n    //////////////\n\n    var rgxMapper = function (ua, arrays) {\n\n            var i = 0, j, k, p, q, matches, match;\n\n            // loop through all regexes maps\n            while (i < arrays.length && !matches) {\n\n                var regex = arrays[i],       // even sequence (0,2,4,..)\n                    props = arrays[i + 1];   // odd sequence (1,3,5,..)\n                j = k = 0;\n\n                // try matching uastring with regexes\n                while (j < regex.length && !matches) {\n\n                    if (!regex[j]) { break; }\n                    matches = regex[j++].exec(ua);\n\n                    if (!!matches) {\n                        for (p = 0; p < props.length; p++) {\n                            match = matches[++k];\n                            q = props[p];\n                            // check if given property is actually array\n                            if (typeof q === OBJ_TYPE && q.length > 0) {\n                                if (q.length === 2) {\n                                    if (typeof q[1] == FUNC_TYPE) {\n                                        // assign modified match\n                                        this[q[0]] = q[1].call(this, match);\n                                    } else {\n                                        // assign given value, ignore regex match\n                                        this[q[0]] = q[1];\n                                    }\n                                } else if (q.length === 3) {\n                                    // check whether function or regex\n                                    if (typeof q[1] === FUNC_TYPE && !(q[1].exec && q[1].test)) {\n                                        // call function (usually string mapper)\n                                        this[q[0]] = match ? q[1].call(this, match, q[2]) : undefined;\n                                    } else {\n                                        // sanitize match using given regex\n                                        this[q[0]] = match ? match.replace(q[1], q[2]) : undefined;\n                                    }\n                                } else if (q.length === 4) {\n                                        this[q[0]] = match ? q[3].call(this, match.replace(q[1], q[2])) : undefined;\n                                }\n                            } else {\n                                this[q] = match ? match : undefined;\n                            }\n                        }\n                    }\n                }\n                i += 2;\n            }\n        },\n\n        strMapper = function (str, map) {\n\n            for (var i in map) {\n                // check if current value is array\n                if (typeof map[i] === OBJ_TYPE && map[i].length > 0) {\n                    for (var j = 0; j < map[i].length; j++) {\n                        if (has(map[i][j], str)) {\n                            return (i === UNKNOWN) ? undefined : i;\n                        }\n                    }\n                } else if (has(map[i], str)) {\n                    return (i === UNKNOWN) ? undefined : i;\n                }\n            }\n            return str;\n    };\n\n    ///////////////\n    // String map\n    //////////////\n\n    // Safari < 3.0\n    var oldSafariMap = {\n            '1.0'   : '/8',\n            '1.2'   : '/1',\n            '1.3'   : '/3',\n            '2.0'   : '/412',\n            '2.0.2' : '/416',\n            '2.0.3' : '/417',\n            '2.0.4' : '/419',\n            '?'     : '/'\n        },\n        windowsVersionMap = {\n            'ME'        : '4.90',\n            'NT 3.11'   : 'NT3.51',\n            'NT 4.0'    : 'NT4.0',\n            '2000'      : 'NT 5.0',\n            'XP'        : ['NT 5.1', 'NT 5.2'],\n            'Vista'     : 'NT 6.0',\n            '7'         : 'NT 6.1',\n            '8'         : 'NT 6.2',\n            '8.1'       : 'NT 6.3',\n            '10'        : ['NT 6.4', 'NT 10.0'],\n            'RT'        : 'ARM'\n    };\n\n    //////////////\n    // Regex map\n    /////////////\n\n    var regexes = {\n\n        browser : [[\n\n            /\\b(?:crmo|crios)\\/([\\w\\.]+)/i                                      // Chrome for Android/iOS\n            ], [VERSION, [NAME, 'Chrome']], [\n            /edg(?:e|ios|a)?\\/([\\w\\.]+)/i                                       // Microsoft Edge\n            ], [VERSION, [NAME, 'Edge']], [\n\n            // Presto based\n            /(opera mini)\\/([-\\w\\.]+)/i,                                        // Opera Mini\n            /(opera [mobiletab]{3,6})\\b.+version\\/([-\\w\\.]+)/i,                 // Opera Mobi/Tablet\n            /(opera)(?:.+version\\/|[\\/ ]+)([\\w\\.]+)/i                           // Opera\n            ], [NAME, VERSION], [\n            /opios[\\/ ]+([\\w\\.]+)/i                                             // Opera mini on iphone >= 8.0\n            ], [VERSION, [NAME, OPERA+' Mini']], [\n            /\\bop(?:rg)?x\\/([\\w\\.]+)/i                                          // Opera GX\n            ], [VERSION, [NAME, OPERA+' GX']], [\n            /\\bopr\\/([\\w\\.]+)/i                                                 // Opera Webkit\n            ], [VERSION, [NAME, OPERA]], [\n\n            // Mixed\n            /\\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\\/ ]?([\\w\\.]+)/i            // Baidu\n            ], [VERSION, [NAME, 'Baidu']], [\n            /(kindle)\\/([\\w\\.]+)/i,                                             // Kindle\n            /(lunascape|maxthon|netfront|jasmine|blazer)[\\/ ]?([\\w\\.]*)/i,      // Lunascape/Maxthon/Netfront/Jasmine/Blazer\n            // Trident based\n            /(avant|iemobile|slim)\\s?(?:browser)?[\\/ ]?([\\w\\.]*)/i,             // Avant/IEMobile/SlimBrowser\n            /(?:ms|\\()(ie) ([\\w\\.]+)/i,                                         // Internet Explorer\n\n            // Webkit/KHTML based                                               // Flock/RockMelt/Midori/Epiphany/Silk/Skyfire/Bolt/Iron/Iridium/PhantomJS/Bowser/QupZilla/Falkon\n            /(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\\/([-\\w\\.]+)/i,\n                                                                                // Rekonq/Puffin/Brave/Whale/QQBrowserLite/QQ, aka ShouQ\n            /(heytap|ovi)browser\\/([\\d\\.]+)/i,                                  // Heytap/Ovi\n            /(weibo)__([\\d\\.]+)/i                                               // Weibo\n            ], [NAME, VERSION], [\n            /\\bddg\\/([\\w\\.]+)/i                                                 // DuckDuckGo\n            ], [VERSION, [NAME, 'DuckDuckGo']], [\n            /(?:\\buc? ?browser|(?:juc.+)ucweb)[\\/ ]?([\\w\\.]+)/i                 // UCBrowser\n            ], [VERSION, [NAME, 'UC'+BROWSER]], [\n            /microm.+\\bqbcore\\/([\\w\\.]+)/i,                                     // WeChat Desktop for Windows Built-in Browser\n            /\\bqbcore\\/([\\w\\.]+).+microm/i,\n            /micromessenger\\/([\\w\\.]+)/i                                        // WeChat\n            ], [VERSION, [NAME, 'WeChat']], [\n            /konqueror\\/([\\w\\.]+)/i                                             // Konqueror\n            ], [VERSION, [NAME, 'Konqueror']], [\n            /trident.+rv[: ]([\\w\\.]{1,9})\\b.+like gecko/i                       // IE11\n            ], [VERSION, [NAME, 'IE']], [\n            /ya(?:search)?browser\\/([\\w\\.]+)/i                                  // Yandex\n            ], [VERSION, [NAME, 'Yandex']], [\n            /slbrowser\\/([\\w\\.]+)/i                                             // Smart Lenovo Browser\n            ], [VERSION, [NAME, 'Smart Lenovo '+BROWSER]], [\n            /(avast|avg)\\/([\\w\\.]+)/i                                           // Avast/AVG Secure Browser\n            ], [[NAME, /(.+)/, '$1 Secure '+BROWSER], VERSION], [\n            /\\bfocus\\/([\\w\\.]+)/i                                               // Firefox Focus\n            ], [VERSION, [NAME, FIREFOX+' Focus']], [\n            /\\bopt\\/([\\w\\.]+)/i                                                 // Opera Touch\n            ], [VERSION, [NAME, OPERA+' Touch']], [\n            /coc_coc\\w+\\/([\\w\\.]+)/i                                            // Coc Coc Browser\n            ], [VERSION, [NAME, 'Coc Coc']], [\n            /dolfin\\/([\\w\\.]+)/i                                                // Dolphin\n            ], [VERSION, [NAME, 'Dolphin']], [\n            /coast\\/([\\w\\.]+)/i                                                 // Opera Coast\n            ], [VERSION, [NAME, OPERA+' Coast']], [\n            /miuibrowser\\/([\\w\\.]+)/i                                           // MIUI Browser\n            ], [VERSION, [NAME, 'MIUI '+BROWSER]], [\n            /fxios\\/([-\\w\\.]+)/i                                                // Firefox for iOS\n            ], [VERSION, [NAME, FIREFOX]], [\n            /\\bqihu|(qi?ho?o?|360)browser/i                                     // 360\n            ], [[NAME, '360 ' + BROWSER]], [\n            /(oculus|sailfish|huawei|vivo)browser\\/([\\w\\.]+)/i\n            ], [[NAME, /(.+)/, '$1 ' + BROWSER], VERSION], [                    // Oculus/Sailfish/HuaweiBrowser/VivoBrowser\n            /samsungbrowser\\/([\\w\\.]+)/i                                        // Samsung Internet\n            ], [VERSION, [NAME, SAMSUNG + ' Internet']], [\n            /(comodo_dragon)\\/([\\w\\.]+)/i                                       // Comodo Dragon\n            ], [[NAME, /_/g, ' '], VERSION], [\n            /metasr[\\/ ]?([\\d\\.]+)/i                                            // Sogou Explorer\n            ], [VERSION, [NAME, 'Sogou Explorer']], [\n            /(sogou)mo\\w+\\/([\\d\\.]+)/i                                          // Sogou Mobile\n            ], [[NAME, 'Sogou Mobile'], VERSION], [\n            /(electron)\\/([\\w\\.]+) safari/i,                                    // Electron-based App\n            /(tesla)(?: qtcarbrowser|\\/(20\\d\\d\\.[-\\w\\.]+))/i,                   // Tesla\n            /m?(qqbrowser|2345Explorer)[\\/ ]?([\\w\\.]+)/i                        // QQBrowser/2345 Browser\n            ], [NAME, VERSION], [\n            /(lbbrowser)/i,                                                     // LieBao Browser\n            /\\[(linkedin)app\\]/i                                                // LinkedIn App for iOS & Android\n            ], [NAME], [\n\n            // WebView\n            /((?:fban\\/fbios|fb_iab\\/fb4a)(?!.+fbav)|;fbav\\/([\\w\\.]+);)/i       // Facebook App for iOS & Android\n            ], [[NAME, FACEBOOK], VERSION], [\n            /(Klarna)\\/([\\w\\.]+)/i,                                             // Klarna Shopping Browser for iOS & Android\n            /(kakao(?:talk|story))[\\/ ]([\\w\\.]+)/i,                             // Kakao App\n            /(naver)\\(.*?(\\d+\\.[\\w\\.]+).*\\)/i,                                  // Naver InApp\n            /safari (line)\\/([\\w\\.]+)/i,                                        // Line App for iOS\n            /\\b(line)\\/([\\w\\.]+)\\/iab/i,                                        // Line App for Android\n            /(alipay)client\\/([\\w\\.]+)/i,                                       // Alipay\n            /(twitter)(?:and| f.+e\\/([\\w\\.]+))/i,                               // Twitter\n            /(chromium|instagram|snapchat)[\\/ ]([-\\w\\.]+)/i                     // Chromium/Instagram/Snapchat\n            ], [NAME, VERSION], [\n            /\\bgsa\\/([\\w\\.]+) .*safari\\//i                                      // Google Search Appliance on iOS\n            ], [VERSION, [NAME, 'GSA']], [\n            /musical_ly(?:.+app_?version\\/|_)([\\w\\.]+)/i                        // TikTok\n            ], [VERSION, [NAME, 'TikTok']], [\n\n            /headlesschrome(?:\\/([\\w\\.]+)| )/i                                  // Chrome Headless\n            ], [VERSION, [NAME, CHROME+' Headless']], [\n\n            / wv\\).+(chrome)\\/([\\w\\.]+)/i                                       // Chrome WebView\n            ], [[NAME, CHROME+' WebView'], VERSION], [\n\n            /droid.+ version\\/([\\w\\.]+)\\b.+(?:mobile safari|safari)/i           // Android Browser\n            ], [VERSION, [NAME, 'Android '+BROWSER]], [\n\n            /(chrome|omniweb|arora|[tizenoka]{5} ?browser)\\/v?([\\w\\.]+)/i       // Chrome/OmniWeb/Arora/Tizen/Nokia\n            ], [NAME, VERSION], [\n\n            /version\\/([\\w\\.\\,]+) .*mobile\\/\\w+ (safari)/i                      // Mobile Safari\n            ], [VERSION, [NAME, 'Mobile Safari']], [\n            /version\\/([\\w(\\.|\\,)]+) .*(mobile ?safari|safari)/i                // Safari & Safari Mobile\n            ], [VERSION, NAME], [\n            /webkit.+?(mobile ?safari|safari)(\\/[\\w\\.]+)/i                      // Safari < 3.0\n            ], [NAME, [VERSION, strMapper, oldSafariMap]], [\n\n            /(webkit|khtml)\\/([\\w\\.]+)/i\n            ], [NAME, VERSION], [\n\n            // Gecko based\n            /(navigator|netscape\\d?)\\/([-\\w\\.]+)/i                              // Netscape\n            ], [[NAME, 'Netscape'], VERSION], [\n            /mobile vr; rv:([\\w\\.]+)\\).+firefox/i                               // Firefox Reality\n            ], [VERSION, [NAME, FIREFOX+' Reality']], [\n            /ekiohf.+(flow)\\/([\\w\\.]+)/i,                                       // Flow\n            /(swiftfox)/i,                                                      // Swiftfox\n            /(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\\/ ]?([\\w\\.\\+]+)/i,\n                                                                                // IceDragon/Iceweasel/Camino/Chimera/Fennec/Maemo/Minimo/Conkeror/Klar\n            /(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\\/([-\\w\\.]+)$/i,\n                                                                                // Firefox/SeaMonkey/K-Meleon/IceCat/IceApe/Firebird/Phoenix\n            /(firefox)\\/([\\w\\.]+)/i,                                            // Other Firefox-based\n            /(mozilla)\\/([\\w\\.]+) .+rv\\:.+gecko\\/\\d+/i,                         // Mozilla\n\n            // Other\n            /(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\\. ]?browser)[-\\/ ]?v?([\\w\\.]+)/i,\n                                                                                // Polaris/Lynx/Dillo/iCab/Doris/Amaya/w3m/NetSurf/Sleipnir/Obigo/Mosaic/Go/ICE/UP.Browser\n            /(links) \\(([\\w\\.]+)/i,                                             // Links\n            /panasonic;(viera)/i                                                // Panasonic Viera\n            ], [NAME, VERSION], [\n            \n            /(cobalt)\\/([\\w\\.]+)/i                                              // Cobalt\n            ], [NAME, [VERSION, /master.|lts./, \"\"]]\n        ],\n\n        cpu : [[\n\n            /(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\\)]/i                     // AMD64 (x64)\n            ], [[ARCHITECTURE, 'amd64']], [\n\n            /(ia32(?=;))/i                                                      // IA32 (quicktime)\n            ], [[ARCHITECTURE, lowerize]], [\n\n            /((?:i[346]|x)86)[;\\)]/i                                            // IA32 (x86)\n            ], [[ARCHITECTURE, 'ia32']], [\n\n            /\\b(aarch64|arm(v?8e?l?|_?64))\\b/i                                 // ARM64\n            ], [[ARCHITECTURE, 'arm64']], [\n\n            /\\b(arm(?:v[67])?ht?n?[fl]p?)\\b/i                                   // ARMHF\n            ], [[ARCHITECTURE, 'armhf']], [\n\n            // PocketPC mistakenly identified as PowerPC\n            /windows (ce|mobile); ppc;/i\n            ], [[ARCHITECTURE, 'arm']], [\n\n            /((?:ppc|powerpc)(?:64)?)(?: mac|;|\\))/i                            // PowerPC\n            ], [[ARCHITECTURE, /ower/, EMPTY, lowerize]], [\n\n            /(sun4\\w)[;\\)]/i                                                    // SPARC\n            ], [[ARCHITECTURE, 'sparc']], [\n\n            /((?:avr32|ia64(?=;))|68k(?=\\))|\\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\\b|pa-risc)/i\n                                                                                // IA64, 68K, ARM/64, AVR/32, IRIX/64, MIPS/64, SPARC/64, PA-RISC\n            ], [[ARCHITECTURE, lowerize]]\n        ],\n\n        device : [[\n\n            //////////////////////////\n            // MOBILES & TABLETS\n            /////////////////////////\n\n            // Samsung\n            /\\b(sch-i[89]0\\d|shw-m380s|sm-[ptx]\\w{2,4}|gt-[pn]\\d{2,4}|sgh-t8[56]9|nexus 10)/i\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, TABLET]], [\n            /\\b((?:s[cgp]h|gt|sm)-\\w+|sc[g-]?[\\d]+a?|galaxy nexus)/i,\n            /samsung[- ]([-\\w]+)/i,\n            /sec-(sgh\\w+)/i\n            ], [MODEL, [VENDOR, SAMSUNG], [TYPE, MOBILE]], [\n\n            // Apple\n            /(?:\\/|\\()(ip(?:hone|od)[\\w, ]*)(?:\\/|;)/i                          // iPod/iPhone\n            ], [MODEL, [VENDOR, APPLE], [TYPE, MOBILE]], [\n            /\\((ipad);[-\\w\\),; ]+apple/i,                                       // iPad\n            /applecoremedia\\/[\\w\\.]+ \\((ipad)/i,\n            /\\b(ipad)\\d\\d?,\\d\\d?[;\\]].+ios/i\n            ], [MODEL, [VENDOR, APPLE], [TYPE, TABLET]], [\n            /(macintosh);/i\n            ], [MODEL, [VENDOR, APPLE]], [\n\n            // Sharp\n            /\\b(sh-?[altvz]?\\d\\d[a-ekm]?)/i\n            ], [MODEL, [VENDOR, SHARP], [TYPE, MOBILE]], [\n\n            // Huawei\n            /\\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\\d{2})\\b(?!.+d\\/s)/i\n            ], [MODEL, [VENDOR, HUAWEI], [TYPE, TABLET]], [\n            /(?:huawei|honor)([-\\w ]+)[;\\)]/i,\n            /\\b(nexus 6p|\\w{2,4}e?-[atu]?[ln][\\dx][012359c][adn]?)\\b(?!.+d\\/s)/i\n            ], [MODEL, [VENDOR, HUAWEI], [TYPE, MOBILE]], [\n\n            // Xiaomi\n            /\\b(poco[\\w ]+|m2\\d{3}j\\d\\d[a-z]{2})(?: bui|\\))/i,                  // Xiaomi POCO\n            /\\b; (\\w+) build\\/hm\\1/i,                                           // Xiaomi Hongmi 'numeric' models\n            /\\b(hm[-_ ]?note?[_ ]?(?:\\d\\w)?) bui/i,                             // Xiaomi Hongmi\n            /\\b(redmi[\\-_ ]?(?:note|k)?[\\w_ ]+)(?: bui|\\))/i,                   // Xiaomi Redmi\n            /oid[^\\)]+; (m?[12][0-389][01]\\w{3,6}[c-y])( bui|; wv|\\))/i,        // Xiaomi Redmi 'numeric' models\n            /\\b(mi[-_ ]?(?:a\\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\\d?\\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\\))/i // Xiaomi Mi\n            ], [[MODEL, /_/g, ' '], [VENDOR, XIAOMI], [TYPE, MOBILE]], [\n            /oid[^\\)]+; (2\\d{4}(283|rpbf)[cgl])( bui|\\))/i,                     // Redmi Pad\n            /\\b(mi[-_ ]?(?:pad)(?:[\\w_ ]+))(?: bui|\\))/i                        // Mi Pad tablets\n            ],[[MODEL, /_/g, ' '], [VENDOR, XIAOMI], [TYPE, TABLET]], [\n\n            // OPPO\n            /; (\\w+) bui.+ oppo/i,\n            /\\b(cph[12]\\d{3}|p(?:af|c[al]|d\\w|e[ar])[mt]\\d0|x9007|a101op)\\b/i\n            ], [MODEL, [VENDOR, 'OPPO'], [TYPE, MOBILE]], [\n            /\\b(opd2\\d{3}a?) bui/i\n            ], [MODEL, [VENDOR, 'OPPO'], [TYPE, TABLET]], [\n\n            // Vivo\n            /vivo (\\w+)(?: bui|\\))/i,\n            /\\b(v[12]\\d{3}\\w?[at])(?: bui|;)/i\n            ], [MODEL, [VENDOR, 'Vivo'], [TYPE, MOBILE]], [\n\n            // Realme\n            /\\b(rmx[1-3]\\d{3})(?: bui|;|\\))/i\n            ], [MODEL, [VENDOR, 'Realme'], [TYPE, MOBILE]], [\n\n            // Motorola\n            /\\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\\b[\\w ]+build\\//i,\n            /\\bmot(?:orola)?[- ](\\w*)/i,\n            /((?:moto[\\w\\(\\) ]+|xt\\d{3,4}|nexus 6)(?= bui|\\)))/i\n            ], [MODEL, [VENDOR, MOTOROLA], [TYPE, MOBILE]], [\n            /\\b(mz60\\d|xoom[2 ]{0,2}) build\\//i\n            ], [MODEL, [VENDOR, MOTOROLA], [TYPE, TABLET]], [\n\n            // LG\n            /((?=lg)?[vl]k\\-?\\d{3}) bui| 3\\.[-\\w; ]{10}lg?-([06cv9]{3,4})/i\n            ], [MODEL, [VENDOR, LG], [TYPE, TABLET]], [\n            /(lm(?:-?f100[nv]?|-[\\w\\.]+)(?= bui|\\))|nexus [45])/i,\n            /\\blg[-e;\\/ ]+((?!browser|netcast|android tv)\\w+)/i,\n            /\\blg-?([\\d\\w]+) bui/i\n            ], [MODEL, [VENDOR, LG], [TYPE, MOBILE]], [\n\n            // Lenovo\n            /(ideatab[-\\w ]+)/i,\n            /lenovo ?(s[56]000[-\\w]+|tab(?:[\\w ]+)|yt[-\\d\\w]{6}|tb[-\\d\\w]{6})/i\n            ], [MODEL, [VENDOR, 'Lenovo'], [TYPE, TABLET]], [\n\n            // Nokia\n            /(?:maemo|nokia).*(n900|lumia \\d+)/i,\n            /nokia[-_ ]?([-\\w\\.]*)/i\n            ], [[MODEL, /_/g, ' '], [VENDOR, 'Nokia'], [TYPE, MOBILE]], [\n\n            // Google\n            /(pixel c)\\b/i                                                      // Google Pixel C\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, TABLET]], [\n            /droid.+; (pixel[\\daxl ]{0,6})(?: bui|\\))/i                         // Google Pixel\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, MOBILE]], [\n\n            // Sony\n            /droid.+ (a?\\d[0-2]{2}so|[c-g]\\d{4}|so[-gl]\\w+|xq-a\\w[4-7][12])(?= bui|\\).+chrome\\/(?![1-6]{0,1}\\d\\.))/i\n            ], [MODEL, [VENDOR, SONY], [TYPE, MOBILE]], [\n            /sony tablet [ps]/i,\n            /\\b(?:sony)?sgp\\w+(?: bui|\\))/i\n            ], [[MODEL, 'Xperia Tablet'], [VENDOR, SONY], [TYPE, TABLET]], [\n\n            // OnePlus\n            / (kb2005|in20[12]5|be20[12][59])\\b/i,\n            /(?:one)?(?:plus)? (a\\d0\\d\\d)(?: b|\\))/i\n            ], [MODEL, [VENDOR, 'OnePlus'], [TYPE, MOBILE]], [\n\n            // Amazon\n            /(alexa)webm/i,\n            /(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\\))/i,                             // Kindle Fire without Silk / Echo Show\n            /(kf[a-z]+)( bui|\\)).+silk\\//i                                      // Kindle Fire HD\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, TABLET]], [\n            /((?:sd|kf)[0349hijorstuw]+)( bui|\\)).+silk\\//i                     // Fire Phone\n            ], [[MODEL, /(.+)/g, 'Fire Phone $1'], [VENDOR, AMAZON], [TYPE, MOBILE]], [\n\n            // BlackBerry\n            /(playbook);[-\\w\\),; ]+(rim)/i                                      // BlackBerry PlayBook\n            ], [MODEL, VENDOR, [TYPE, TABLET]], [\n            /\\b((?:bb[a-f]|st[hv])100-\\d)/i,\n            /\\(bb10; (\\w+)/i                                                    // BlackBerry 10\n            ], [MODEL, [VENDOR, BLACKBERRY], [TYPE, MOBILE]], [\n\n            // Asus\n            /(?:\\b|asus_)(transfo[prime ]{4,10} \\w+|eeepc|slider \\w+|nexus 7|padfone|p00[cj])/i\n            ], [MODEL, [VENDOR, ASUS], [TYPE, TABLET]], [\n            / (z[bes]6[027][012][km][ls]|zenfone \\d\\w?)\\b/i\n            ], [MODEL, [VENDOR, ASUS], [TYPE, MOBILE]], [\n\n            // HTC\n            /(nexus 9)/i                                                        // HTC Nexus 9\n            ], [MODEL, [VENDOR, 'HTC'], [TYPE, TABLET]], [\n            /(htc)[-;_ ]{1,2}([\\w ]+(?=\\)| bui)|\\w+)/i,                         // HTC\n\n            // ZTE\n            /(zte)[- ]([\\w ]+?)(?: bui|\\/|\\))/i,\n            /(alcatel|geeksphone|nexian|panasonic(?!(?:;|\\.))|sony(?!-bra))[-_ ]?([-\\w]*)/i         // Alcatel/GeeksPhone/Nexian/Panasonic/Sony\n            ], [VENDOR, [MODEL, /_/g, ' '], [TYPE, MOBILE]], [\n\n            // Acer\n            /droid.+; ([ab][1-7]-?[0178a]\\d\\d?)/i\n            ], [MODEL, [VENDOR, 'Acer'], [TYPE, TABLET]], [\n\n            // Meizu\n            /droid.+; (m[1-5] note) bui/i,\n            /\\bmz-([-\\w]{2,})/i\n            ], [MODEL, [VENDOR, 'Meizu'], [TYPE, MOBILE]], [\n                \n            // Ulefone\n            /; ((?:power )?armor(?:[\\w ]{0,8}))(?: bui|\\))/i\n            ], [MODEL, [VENDOR, 'Ulefone'], [TYPE, MOBILE]], [\n\n            // MIXED\n            /(blackberry|benq|palm(?=\\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno)[-_ ]?([-\\w]*)/i,\n                                                                                // BlackBerry/BenQ/Palm/Sony-Ericsson/Acer/Asus/Dell/Meizu/Motorola/Polytron\n            /(hp) ([\\w ]+\\w)/i,                                                 // HP iPAQ\n            /(asus)-?(\\w+)/i,                                                   // Asus\n            /(microsoft); (lumia[\\w ]+)/i,                                      // Microsoft Lumia\n            /(lenovo)[-_ ]?([-\\w]+)/i,                                          // Lenovo\n            /(jolla)/i,                                                         // Jolla\n            /(oppo) ?([\\w ]+) bui/i                                             // OPPO\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n\n            /(kobo)\\s(ereader|touch)/i,                                         // Kobo\n            /(archos) (gamepad2?)/i,                                            // Archos\n            /(hp).+(touchpad(?!.+tablet)|tablet)/i,                             // HP TouchPad\n            /(kindle)\\/([\\w\\.]+)/i,                                             // Kindle\n            /(nook)[\\w ]+build\\/(\\w+)/i,                                        // Nook\n            /(dell) (strea[kpr\\d ]*[\\dko])/i,                                   // Dell Streak\n            /(le[- ]+pan)[- ]+(\\w{1,9}) bui/i,                                  // Le Pan Tablets\n            /(trinity)[- ]*(t\\d{3}) bui/i,                                      // Trinity Tablets\n            /(gigaset)[- ]+(q\\w{1,9}) bui/i,                                    // Gigaset Tablets\n            /(vodafone) ([\\w ]+)(?:\\)| bui)/i                                   // Vodafone\n            ], [VENDOR, MODEL, [TYPE, TABLET]], [\n\n            /(surface duo)/i                                                    // Surface Duo\n            ], [MODEL, [VENDOR, MICROSOFT], [TYPE, TABLET]], [\n            /droid [\\d\\.]+; (fp\\du?)(?: b|\\))/i                                 // Fairphone\n            ], [MODEL, [VENDOR, 'Fairphone'], [TYPE, MOBILE]], [\n            /(u304aa)/i                                                         // AT&T\n            ], [MODEL, [VENDOR, 'AT&T'], [TYPE, MOBILE]], [\n            /\\bsie-(\\w*)/i                                                      // Siemens\n            ], [MODEL, [VENDOR, 'Siemens'], [TYPE, MOBILE]], [\n            /\\b(rct\\w+) b/i                                                     // RCA Tablets\n            ], [MODEL, [VENDOR, 'RCA'], [TYPE, TABLET]], [\n            /\\b(venue[\\d ]{2,7}) b/i                                            // Dell Venue Tablets\n            ], [MODEL, [VENDOR, 'Dell'], [TYPE, TABLET]], [\n            /\\b(q(?:mv|ta)\\w+) b/i                                              // Verizon Tablet\n            ], [MODEL, [VENDOR, 'Verizon'], [TYPE, TABLET]], [\n            /\\b(?:barnes[& ]+noble |bn[rt])([\\w\\+ ]*) b/i                       // Barnes & Noble Tablet\n            ], [MODEL, [VENDOR, 'Barnes & Noble'], [TYPE, TABLET]], [\n            /\\b(tm\\d{3}\\w+) b/i\n            ], [MODEL, [VENDOR, 'NuVision'], [TYPE, TABLET]], [\n            /\\b(k88) b/i                                                        // ZTE K Series Tablet\n            ], [MODEL, [VENDOR, 'ZTE'], [TYPE, TABLET]], [\n            /\\b(nx\\d{3}j) b/i                                                   // ZTE Nubia\n            ], [MODEL, [VENDOR, 'ZTE'], [TYPE, MOBILE]], [\n            /\\b(gen\\d{3}) b.+49h/i                                              // Swiss GEN Mobile\n            ], [MODEL, [VENDOR, 'Swiss'], [TYPE, MOBILE]], [\n            /\\b(zur\\d{3}) b/i                                                   // Swiss ZUR Tablet\n            ], [MODEL, [VENDOR, 'Swiss'], [TYPE, TABLET]], [\n            /\\b((zeki)?tb.*\\b) b/i                                              // Zeki Tablets\n            ], [MODEL, [VENDOR, 'Zeki'], [TYPE, TABLET]], [\n            /\\b([yr]\\d{2}) b/i,\n            /\\b(dragon[- ]+touch |dt)(\\w{5}) b/i                                // Dragon Touch Tablet\n            ], [[VENDOR, 'Dragon Touch'], MODEL, [TYPE, TABLET]], [\n            /\\b(ns-?\\w{0,9}) b/i                                                // Insignia Tablets\n            ], [MODEL, [VENDOR, 'Insignia'], [TYPE, TABLET]], [\n            /\\b((nxa|next)-?\\w{0,9}) b/i                                        // NextBook Tablets\n            ], [MODEL, [VENDOR, 'NextBook'], [TYPE, TABLET]], [\n            /\\b(xtreme\\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i                  // Voice Xtreme Phones\n            ], [[VENDOR, 'Voice'], MODEL, [TYPE, MOBILE]], [\n            /\\b(lvtel\\-)?(v1[12]) b/i                                           // LvTel Phones\n            ], [[VENDOR, 'LvTel'], MODEL, [TYPE, MOBILE]], [\n            /\\b(ph-1) /i                                                        // Essential PH-1\n            ], [MODEL, [VENDOR, 'Essential'], [TYPE, MOBILE]], [\n            /\\b(v(100md|700na|7011|917g).*\\b) b/i                               // Envizen Tablets\n            ], [MODEL, [VENDOR, 'Envizen'], [TYPE, TABLET]], [\n            /\\b(trio[-\\w\\. ]+) b/i                                              // MachSpeed Tablets\n            ], [MODEL, [VENDOR, 'MachSpeed'], [TYPE, TABLET]], [\n            /\\btu_(1491) b/i                                                    // Rotor Tablets\n            ], [MODEL, [VENDOR, 'Rotor'], [TYPE, TABLET]], [\n            /(shield[\\w ]+) b/i                                                 // Nvidia Shield Tablets\n            ], [MODEL, [VENDOR, 'Nvidia'], [TYPE, TABLET]], [\n            /(sprint) (\\w+)/i                                                   // Sprint Phones\n            ], [VENDOR, MODEL, [TYPE, MOBILE]], [\n            /(kin\\.[onetw]{3})/i                                                // Microsoft Kin\n            ], [[MODEL, /\\./g, ' '], [VENDOR, MICROSOFT], [TYPE, MOBILE]], [\n            /droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\\)/i             // Zebra\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, TABLET]], [\n            /droid.+; (ec30|ps20|tc[2-8]\\d[kx])\\)/i\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, MOBILE]], [\n\n            ///////////////////\n            // SMARTTVS\n            ///////////////////\n\n            /smart-tv.+(samsung)/i                                              // Samsung\n            ], [VENDOR, [TYPE, SMARTTV]], [\n            /hbbtv.+maple;(\\d+)/i\n            ], [[MODEL, /^/, 'SmartTV'], [VENDOR, SAMSUNG], [TYPE, SMARTTV]], [\n            /(nux; netcast.+smarttv|lg (netcast\\.tv-201\\d|android tv))/i        // LG SmartTV\n            ], [[VENDOR, LG], [TYPE, SMARTTV]], [\n            /(apple) ?tv/i                                                      // Apple TV\n            ], [VENDOR, [MODEL, APPLE+' TV'], [TYPE, SMARTTV]], [\n            /crkey/i                                                            // Google Chromecast\n            ], [[MODEL, CHROME+'cast'], [VENDOR, GOOGLE], [TYPE, SMARTTV]], [\n            /droid.+aft(\\w+)( bui|\\))/i                                         // Fire TV\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, SMARTTV]], [\n            /\\(dtv[\\);].+(aquos)/i,\n            /(aquos-tv[\\w ]+)\\)/i                                               // Sharp\n            ], [MODEL, [VENDOR, SHARP], [TYPE, SMARTTV]],[\n            /(bravia[\\w ]+)( bui|\\))/i                                              // Sony\n            ], [MODEL, [VENDOR, SONY], [TYPE, SMARTTV]], [\n            /(mitv-\\w{5}) bui/i                                                 // Xiaomi\n            ], [MODEL, [VENDOR, XIAOMI], [TYPE, SMARTTV]], [\n            /Hbbtv.*(technisat) (.*);/i                                         // TechniSAT\n            ], [VENDOR, MODEL, [TYPE, SMARTTV]], [\n            /\\b(roku)[\\dx]*[\\)\\/]((?:dvp-)?[\\d\\.]*)/i,                          // Roku\n            /hbbtv\\/\\d+\\.\\d+\\.\\d+ +\\([\\w\\+ ]*; *([\\w\\d][^;]*);([^;]*)/i         // HbbTV devices\n            ], [[VENDOR, trim], [MODEL, trim], [TYPE, SMARTTV]], [\n            /\\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\\b/i                   // SmartTV from Unidentified Vendors\n            ], [[TYPE, SMARTTV]], [\n\n            ///////////////////\n            // CONSOLES\n            ///////////////////\n\n            /(ouya)/i,                                                          // Ouya\n            /(nintendo) ([wids3utch]+)/i                                        // Nintendo\n            ], [VENDOR, MODEL, [TYPE, CONSOLE]], [\n            /droid.+; (shield) bui/i                                            // Nvidia\n            ], [MODEL, [VENDOR, 'Nvidia'], [TYPE, CONSOLE]], [\n            /(playstation [345portablevi]+)/i                                   // Playstation\n            ], [MODEL, [VENDOR, SONY], [TYPE, CONSOLE]], [\n            /\\b(xbox(?: one)?(?!; xbox))[\\); ]/i                                // Microsoft Xbox\n            ], [MODEL, [VENDOR, MICROSOFT], [TYPE, CONSOLE]], [\n\n            ///////////////////\n            // WEARABLES\n            ///////////////////\n\n            /((pebble))app/i                                                    // Pebble\n            ], [VENDOR, MODEL, [TYPE, WEARABLE]], [\n            /(watch)(?: ?os[,\\/]|\\d,\\d\\/)[\\d\\.]+/i                              // Apple Watch\n            ], [MODEL, [VENDOR, APPLE], [TYPE, WEARABLE]], [\n            /droid.+; (glass) \\d/i                                              // Google Glass\n            ], [MODEL, [VENDOR, GOOGLE], [TYPE, WEARABLE]], [\n            /droid.+; (wt63?0{2,3})\\)/i\n            ], [MODEL, [VENDOR, ZEBRA], [TYPE, WEARABLE]], [\n            /(quest( \\d| pro)?)/i                                               // Oculus Quest\n            ], [MODEL, [VENDOR, FACEBOOK], [TYPE, WEARABLE]], [\n\n            ///////////////////\n            // EMBEDDED\n            ///////////////////\n\n            /(tesla)(?: qtcarbrowser|\\/[-\\w\\.]+)/i                              // Tesla\n            ], [VENDOR, [TYPE, EMBEDDED]], [\n            /(aeobc)\\b/i                                                        // Echo Dot\n            ], [MODEL, [VENDOR, AMAZON], [TYPE, EMBEDDED]], [\n\n            ////////////////////\n            // MIXED (GENERIC)\n            ///////////////////\n\n            /droid .+?; ([^;]+?)(?: bui|; wv\\)|\\) applew).+? mobile safari/i    // Android Phones from Unidentified Vendors\n            ], [MODEL, [TYPE, MOBILE]], [\n            /droid .+?; ([^;]+?)(?: bui|\\) applew).+?(?! mobile) safari/i       // Android Tablets from Unidentified Vendors\n            ], [MODEL, [TYPE, TABLET]], [\n            /\\b((tablet|tab)[;\\/]|focus\\/\\d(?!.+mobile))/i                      // Unidentifiable Tablet\n            ], [[TYPE, TABLET]], [\n            /(phone|mobile(?:[;\\/]| [ \\w\\/\\.]*safari)|pda(?=.+windows ce))/i    // Unidentifiable Mobile\n            ], [[TYPE, MOBILE]], [\n            /(android[-\\w\\. ]{0,9});.+buil/i                                    // Generic Android Device\n            ], [MODEL, [VENDOR, 'Generic']]\n        ],\n\n        engine : [[\n\n            /windows.+ edge\\/([\\w\\.]+)/i                                       // EdgeHTML\n            ], [VERSION, [NAME, EDGE+'HTML']], [\n\n            /webkit\\/537\\.36.+chrome\\/(?!27)([\\w\\.]+)/i                         // Blink\n            ], [VERSION, [NAME, 'Blink']], [\n\n            /(presto)\\/([\\w\\.]+)/i,                                             // Presto\n            /(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\\/([\\w\\.]+)/i, // WebKit/Trident/NetFront/NetSurf/Amaya/Lynx/w3m/Goanna\n            /ekioh(flow)\\/([\\w\\.]+)/i,                                          // Flow\n            /(khtml|tasman|links)[\\/ ]\\(?([\\w\\.]+)/i,                           // KHTML/Tasman/Links\n            /(icab)[\\/ ]([23]\\.[\\d\\.]+)/i,                                      // iCab\n            /\\b(libweb)/i\n            ], [NAME, VERSION], [\n\n            /rv\\:([\\w\\.]{1,9})\\b.+(gecko)/i                                     // Gecko\n            ], [VERSION, NAME]\n        ],\n\n        os : [[\n\n            // Windows\n            /microsoft (windows) (vista|xp)/i                                   // Windows (iTunes)\n            ], [NAME, VERSION], [\n            /(windows (?:phone(?: os)?|mobile))[\\/ ]?([\\d\\.\\w ]*)/i             // Windows Phone\n            ], [NAME, [VERSION, strMapper, windowsVersionMap]], [\n            /windows nt 6\\.2; (arm)/i,                                        // Windows RT\n            /windows[\\/ ]?([ntce\\d\\. ]+\\w)(?!.+xbox)/i,\n            /(?:win(?=3|9|n)|win 9x )([nt\\d\\.]+)/i\n            ], [[VERSION, strMapper, windowsVersionMap], [NAME, 'Windows']], [\n\n            // iOS/macOS\n            /ip[honead]{2,4}\\b(?:.*os ([\\w]+) like mac|; opera)/i,              // iOS\n            /(?:ios;fbsv\\/|iphone.+ios[\\/ ])([\\d\\.]+)/i,\n            /cfnetwork\\/.+darwin/i\n            ], [[VERSION, /_/g, '.'], [NAME, 'iOS']], [\n            /(mac os x) ?([\\w\\. ]*)/i,\n            /(macintosh|mac_powerpc\\b)(?!.+haiku)/i                             // Mac OS\n            ], [[NAME, MAC_OS], [VERSION, /_/g, '.']], [\n\n            // Mobile OSes\n            /droid ([\\w\\.]+)\\b.+(android[- ]x86|harmonyos)/i                    // Android-x86/HarmonyOS\n            ], [VERSION, NAME], [                                               // Android/WebOS/QNX/Bada/RIM/Maemo/MeeGo/Sailfish OS\n            /(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\\/ ]?([\\w\\.]*)/i,\n            /(blackberry)\\w*\\/([\\w\\.]*)/i,                                      // Blackberry\n            /(tizen|kaios)[\\/ ]([\\w\\.]+)/i,                                     // Tizen/KaiOS\n            /\\((series40);/i                                                    // Series 40\n            ], [NAME, VERSION], [\n            /\\(bb(10);/i                                                        // BlackBerry 10\n            ], [VERSION, [NAME, BLACKBERRY]], [\n            /(?:symbian ?os|symbos|s60(?=;)|series60)[-\\/ ]?([\\w\\.]*)/i         // Symbian\n            ], [VERSION, [NAME, 'Symbian']], [\n            /mozilla\\/[\\d\\.]+ \\((?:mobile|tablet|tv|mobile; [\\w ]+); rv:.+ gecko\\/([\\w\\.]+)/i // Firefox OS\n            ], [VERSION, [NAME, FIREFOX+' OS']], [\n            /web0s;.+rt(tv)/i,\n            /\\b(?:hp)?wos(?:browser)?\\/([\\w\\.]+)/i                              // WebOS\n            ], [VERSION, [NAME, 'webOS']], [\n            /watch(?: ?os[,\\/]|\\d,\\d\\/)([\\d\\.]+)/i                              // watchOS\n            ], [VERSION, [NAME, 'watchOS']], [\n\n            // Google Chromecast\n            /crkey\\/([\\d\\.]+)/i                                                 // Google Chromecast\n            ], [VERSION, [NAME, CHROME+'cast']], [\n            /(cros) [\\w]+(?:\\)| ([\\w\\.]+)\\b)/i                                  // Chromium OS\n            ], [[NAME, CHROMIUM_OS], VERSION],[\n\n            // Smart TVs\n            /panasonic;(viera)/i,                                               // Panasonic Viera\n            /(netrange)mmh/i,                                                   // Netrange\n            /(nettv)\\/(\\d+\\.[\\w\\.]+)/i,                                         // NetTV\n\n            // Console\n            /(nintendo|playstation) ([wids345portablevuch]+)/i,                 // Nintendo/Playstation\n            /(xbox); +xbox ([^\\);]+)/i,                                         // Microsoft Xbox (360, One, X, S, Series X, Series S)\n\n            // Other\n            /\\b(joli|palm)\\b ?(?:os)?\\/?([\\w\\.]*)/i,                            // Joli/Palm\n            /(mint)[\\/\\(\\) ]?(\\w*)/i,                                           // Mint\n            /(mageia|vectorlinux)[; ]/i,                                        // Mageia/VectorLinux\n            /([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\\/ ]?(?!chrom|package)([-\\w\\.]*)/i,\n                                                                                // Ubuntu/Debian/SUSE/Gentoo/Arch/Slackware/Fedora/Mandriva/CentOS/PCLinuxOS/RedHat/Zenwalk/Linpus/Raspbian/Plan9/Minix/RISCOS/Contiki/Deepin/Manjaro/elementary/Sabayon/Linspire\n            /(hurd|linux) ?([\\w\\.]*)/i,                                         // Hurd/Linux\n            /(gnu) ?([\\w\\.]*)/i,                                                // GNU\n            /\\b([-frentopcghs]{0,5}bsd|dragonfly)[\\/ ]?(?!amd|[ix346]{1,2}86)([\\w\\.]*)/i, // FreeBSD/NetBSD/OpenBSD/PC-BSD/GhostBSD/DragonFly\n            /(haiku) (\\w+)/i                                                    // Haiku\n            ], [NAME, VERSION], [\n            /(sunos) ?([\\w\\.\\d]*)/i                                             // Solaris\n            ], [[NAME, 'Solaris'], VERSION], [\n            /((?:open)?solaris)[-\\/ ]?([\\w\\.]*)/i,                              // Solaris\n            /(aix) ((\\d)(?=\\.|\\)| )[\\w\\.])*/i,                                  // AIX\n            /\\b(beos|os\\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i, // BeOS/OS2/AmigaOS/MorphOS/OpenVMS/Fuchsia/HP-UX/SerenityOS\n            /(unix) ?([\\w\\.]*)/i                                                // UNIX\n            ], [NAME, VERSION]\n        ]\n    };\n\n    /////////////////\n    // Constructor\n    ////////////////\n\n    var UAParser = function (ua, extensions) {\n\n        if (typeof ua === OBJ_TYPE) {\n            extensions = ua;\n            ua = undefined;\n        }\n\n        if (!(this instanceof UAParser)) {\n            return new UAParser(ua, extensions).getResult();\n        }\n\n        var _navigator = (typeof window !== UNDEF_TYPE && window.navigator) ? window.navigator : undefined;\n        var _ua = ua || ((_navigator && _navigator.userAgent) ? _navigator.userAgent : EMPTY);\n        var _uach = (_navigator && _navigator.userAgentData) ? _navigator.userAgentData : undefined;\n        var _rgxmap = extensions ? extend(regexes, extensions) : regexes;\n        var _isSelfNav = _navigator && _navigator.userAgent == _ua;\n\n        this.getBrowser = function () {\n            var _browser = {};\n            _browser[NAME] = undefined;\n            _browser[VERSION] = undefined;\n            rgxMapper.call(_browser, _ua, _rgxmap.browser);\n            _browser[MAJOR] = majorize(_browser[VERSION]);\n            // Brave-specific detection\n            if (_isSelfNav && _navigator && _navigator.brave && typeof _navigator.brave.isBrave == FUNC_TYPE) {\n                _browser[NAME] = 'Brave';\n            }\n            return _browser;\n        };\n        this.getCPU = function () {\n            var _cpu = {};\n            _cpu[ARCHITECTURE] = undefined;\n            rgxMapper.call(_cpu, _ua, _rgxmap.cpu);\n            return _cpu;\n        };\n        this.getDevice = function () {\n            var _device = {};\n            _device[VENDOR] = undefined;\n            _device[MODEL] = undefined;\n            _device[TYPE] = undefined;\n            rgxMapper.call(_device, _ua, _rgxmap.device);\n            if (_isSelfNav && !_device[TYPE] && _uach && _uach.mobile) {\n                _device[TYPE] = MOBILE;\n            }\n            // iPadOS-specific detection: identified as Mac, but has some iOS-only properties\n            if (_isSelfNav && _device[MODEL] == 'Macintosh' && _navigator && typeof _navigator.standalone !== UNDEF_TYPE && _navigator.maxTouchPoints && _navigator.maxTouchPoints > 2) {\n                _device[MODEL] = 'iPad';\n                _device[TYPE] = TABLET;\n            }\n            return _device;\n        };\n        this.getEngine = function () {\n            var _engine = {};\n            _engine[NAME] = undefined;\n            _engine[VERSION] = undefined;\n            rgxMapper.call(_engine, _ua, _rgxmap.engine);\n            return _engine;\n        };\n        this.getOS = function () {\n            var _os = {};\n            _os[NAME] = undefined;\n            _os[VERSION] = undefined;\n            rgxMapper.call(_os, _ua, _rgxmap.os);\n            if (_isSelfNav && !_os[NAME] && _uach && _uach.platform && _uach.platform != 'Unknown') {\n                _os[NAME] = _uach.platform  \n                                    .replace(/chrome os/i, CHROMIUM_OS)\n                                    .replace(/macos/i, MAC_OS);           // backward compatibility\n            }\n            return _os;\n        };\n        this.getResult = function () {\n            return {\n                ua      : this.getUA(),\n                browser : this.getBrowser(),\n                engine  : this.getEngine(),\n                os      : this.getOS(),\n                device  : this.getDevice(),\n                cpu     : this.getCPU()\n            };\n        };\n        this.getUA = function () {\n            return _ua;\n        };\n        this.setUA = function (ua) {\n            _ua = (typeof ua === STR_TYPE && ua.length > UA_MAX_LENGTH) ? trim(ua, UA_MAX_LENGTH) : ua;\n            return this;\n        };\n        this.setUA(_ua);\n        return this;\n    };\n\n    UAParser.VERSION = LIBVERSION;\n    UAParser.BROWSER =  enumerize([NAME, VERSION, MAJOR]);\n    UAParser.CPU = enumerize([ARCHITECTURE]);\n    UAParser.DEVICE = enumerize([MODEL, VENDOR, TYPE, CONSOLE, MOBILE, SMARTTV, TABLET, WEARABLE, EMBEDDED]);\n    UAParser.ENGINE = UAParser.OS = enumerize([NAME, VERSION]);\n\n    ///////////\n    // Export\n    //////////\n\n    // check js environment\n    if (typeof(exports) !== UNDEF_TYPE) {\n        // nodejs env\n        if (typeof module !== UNDEF_TYPE && module.exports) {\n            exports = module.exports = UAParser;\n        }\n        exports.UAParser = UAParser;\n    } else {\n        // requirejs env (optional)\n        if (typeof(define) === FUNC_TYPE && define.amd) {\n            define(function () {\n                return UAParser;\n            });\n        } else if (typeof window !== UNDEF_TYPE) {\n            // browser env\n            window.UAParser = UAParser;\n        }\n    }\n\n    // jQuery/Zepto specific (optional)\n    // Note:\n    //   In AMD env the global scope should be kept clean, but jQuery is an exception.\n    //   jQuery always exports to global scope, unless jQuery.noConflict(true) is used,\n    //   and we should catch that.\n    var $ = typeof window !== UNDEF_TYPE && (window.jQuery || window.Zepto);\n    if ($ && !$.ua) {\n        var parser = new UAParser();\n        $.ua = parser.getResult();\n        $.ua.get = function () {\n            return parser.getUA();\n        };\n        $.ua.set = function (ua) {\n            parser.setUA(ua);\n            var result = parser.getResult();\n            for (var prop in result) {\n                $.ua[prop] = result[prop];\n            }\n        };\n    }\n\n})(typeof window === 'object' ? window : this);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport EventBus from './EventBus.js';\nimport Events from './events/Events.js';\nimport FactoryMaker from './FactoryMaker.js';\n\nconst LOG_LEVEL_NONE = 0;\nconst LOG_LEVEL_FATAL = 1;\nconst LOG_LEVEL_ERROR = 2;\nconst LOG_LEVEL_WARNING = 3;\nconst LOG_LEVEL_INFO = 4;\nconst LOG_LEVEL_DEBUG = 5;\n\n/**\n * @module Debug\n * @param {object} config\n * @ignore\n */\nfunction Debug(config) {\n\n    config = config || {};\n    const context = this.context;\n    const eventBus = EventBus(context).getInstance();\n    const settings = config.settings;\n\n    const logFn = [];\n\n    let instance,\n        showLogTimestamp,\n        showCalleeName,\n        startTime;\n\n    function setup() {\n        showLogTimestamp = true;\n        showCalleeName = true;\n        startTime = new Date().getTime();\n\n        if (typeof window !== 'undefined' && window.console) {\n            logFn[LOG_LEVEL_FATAL] = getLogFn(window.console.error);\n            logFn[LOG_LEVEL_ERROR] = getLogFn(window.console.error);\n            logFn[LOG_LEVEL_WARNING] = getLogFn(window.console.warn);\n            logFn[LOG_LEVEL_INFO] = getLogFn(window.console.info);\n            logFn[LOG_LEVEL_DEBUG] = getLogFn(window.console.debug);\n        }\n    }\n\n    function getLogFn(fn) {\n        if (fn && fn.bind) {\n            return fn.bind(window.console);\n        }\n        // if not define, return the default function for reporting logs\n        return window.console.log.bind(window.console);\n    }\n\n    /**\n     * Retrieves a logger which can be used to write logging information in browser console.\n     * @param {object} instance Object for which the logger is created. It is used\n     * to include calle object information in log messages.\n     * @memberof module:Debug\n     * @returns {Logger}\n     * @instance\n     */\n    function getLogger(instance) {\n        return {\n            fatal: fatal.bind(instance),\n            error: error.bind(instance),\n            warn: warn.bind(instance),\n            info: info.bind(instance),\n            debug: debug.bind(instance)\n        };\n    }\n\n    /**\n     * Prepends a timestamp in milliseconds to each log message.\n     * @param {boolean} value Set to true if you want to see a timestamp in each log message.\n     * @default LOG_LEVEL_WARNING\n     * @memberof module:Debug\n     * @instance\n     */\n    function setLogTimestampVisible(value) {\n        showLogTimestamp = value;\n    }\n\n    /**\n     * Prepends the callee object name, and media type if available, to each log message.\n     * @param {boolean} value Set to true if you want to see the callee object name and media type in each log message.\n     * @default true\n     * @memberof module:Debug\n     * @instance\n     */\n    function setCalleeNameVisible(value) {\n        showCalleeName = value;\n    }\n\n    function fatal(...params) {\n        doLog(LOG_LEVEL_FATAL, this, ...params);\n    }\n\n    function error(...params) {\n        doLog(LOG_LEVEL_ERROR, this, ...params);\n    }\n\n    function warn(...params) {\n        doLog(LOG_LEVEL_WARNING, this, ...params);\n    }\n\n    function info(...params) {\n        doLog(LOG_LEVEL_INFO, this, ...params);\n    }\n\n    function debug(...params) {\n        doLog(LOG_LEVEL_DEBUG, this, ...params);\n    }\n\n    function doLog(level, _this, ...params) {\n        let message = '';\n        let logTime = null;\n\n        if (showLogTimestamp) {\n            logTime = new Date().getTime();\n            message += '[' + (logTime - startTime) + ']';\n        }\n\n        if (showCalleeName && _this && _this.getClassName) {\n            message += '[' + _this.getClassName() + ']';\n            if (_this.getType) {\n                message += '[' + _this.getType() + ']';\n            }\n        }\n\n        if (message.length > 0) {\n            message += ' ';\n        }\n\n        Array.apply(null, params).forEach(function (item) {\n            message += item + ' ';\n        });\n\n        // log to console if the log level is high enough\n        if (logFn[level] && settings && settings.get().debug.logLevel >= level) {\n            logFn[level](message);\n        }\n\n        // send log event regardless of log level\n        if (settings && settings.get().debug.dispatchEvent) {\n            eventBus.trigger(Events.LOG, { message: message, level: level });\n        }\n    }\n\n    instance = {\n        getLogger: getLogger,\n        setLogTimestampVisible: setLogTimestampVisible,\n        setCalleeNameVisible: setCalleeNameVisible\n    };\n\n    setup();\n\n    return instance;\n}\n\nDebug.__dashjs_factory_name = 'Debug';\n\nconst factory = FactoryMaker.getSingletonFactory(Debug);\nfactory.LOG_LEVEL_NONE = LOG_LEVEL_NONE;\nfactory.LOG_LEVEL_FATAL = LOG_LEVEL_FATAL;\nfactory.LOG_LEVEL_ERROR = LOG_LEVEL_ERROR;\nfactory.LOG_LEVEL_WARNING = LOG_LEVEL_WARNING;\nfactory.LOG_LEVEL_INFO = LOG_LEVEL_INFO;\nfactory.LOG_LEVEL_DEBUG = LOG_LEVEL_DEBUG;\nFactoryMaker.updateSingletonFactory(Debug.__dashjs_factory_name, factory);\nexport default factory;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport FactoryMaker from './FactoryMaker.js';\nimport MediaPlayerEvents from '../streaming/MediaPlayerEvents.js';\n\nconst EVENT_PRIORITY_LOW = 0;\nconst EVENT_PRIORITY_HIGH = 5000;\n\nfunction EventBus() {\n\n    let handlers = {};\n\n    function _commonOn(type, listener, scope, options = {}, executeOnlyOnce = false) {\n\n        if (!type) {\n            throw new Error('event type cannot be null or undefined');\n        }\n        if (!listener || typeof (listener) !== 'function') {\n            throw new Error('listener must be a function: ' + listener);\n        }\n\n        let priority = options.priority || EVENT_PRIORITY_LOW;\n\n        if (getHandlerIdx(type, listener, scope) >= 0) {\n            return;\n        }\n\n        handlers[type] = handlers[type] || [];\n\n        const handler = {\n            callback: listener,\n            scope,\n            priority,\n            executeOnlyOnce\n        };\n\n        if (scope && scope.getStreamId) {\n            handler.streamId = scope.getStreamId();\n        }\n        if (scope && scope.getType) {\n            handler.mediaType = scope.getType();\n        }\n        if (options && options.mode) {\n            handler.mode = options.mode;\n        }\n\n        const inserted = handlers[type].some((item, idx) => {\n            if (item && priority > item.priority) {\n                handlers[type].splice(idx, 0, handler);\n                return true;\n            }\n        });\n\n        if (!inserted) {\n            handlers[type].push(handler);\n        }\n    }\n\n    function on(type, listener, scope, options = {}) {\n        _commonOn(type, listener, scope, options);\n    }\n\n    function once(type, listener, scope, options = {}) {\n        _commonOn(type, listener, scope, options, true)\n    }\n\n    function off(type, listener, scope) {\n        if (!type || !listener || !handlers[type]) {\n            return;\n        }\n        const idx = getHandlerIdx(type, listener, scope);\n        if (idx < 0) {\n            return;\n        }\n        handlers[type][idx] = null;\n    }\n\n    function trigger(type, payload = {}, filters = {}) {\n        if (!type || !handlers[type]) {\n            return;\n        }\n\n        payload = payload || {};\n\n        if (payload.hasOwnProperty('type')) {\n            throw new Error('\\'type\\' is a reserved word for event dispatching');\n        }\n\n        payload.type = type;\n\n        if (filters.streamId) {\n            payload.streamId = filters.streamId;\n        }\n        if (filters.mediaType) {\n            payload.mediaType = filters.mediaType;\n        }\n\n        const handlersToRemove = [];\n        handlers[type]\n            .filter((handler) => {\n                if (!handler) {\n                    return false;\n                }\n                if (filters.streamId && handler.streamId && handler.streamId !== filters.streamId) {\n                    return false;\n                }\n                if (filters.mediaType && handler.mediaType && handler.mediaType !== filters.mediaType) {\n                    return false;\n                }\n                // This is used for dispatching DASH events. By default we use the onStart mode. Consequently we filter everything that has a non matching mode and the onReceive events for handlers that did not specify a mode.\n                if ((filters.mode && handler.mode && handler.mode !== filters.mode) || (!handler.mode && filters.mode && filters.mode === MediaPlayerEvents.EVENT_MODE_ON_RECEIVE)) {\n                    return false;\n                }\n                return true;\n            })\n            .forEach((handler) => {\n                handler && handler.callback.call(handler.scope, payload);\n                if (handler.executeOnlyOnce) {\n                    handlersToRemove.push(handler);\n                }\n            });\n\n        handlersToRemove.forEach((handler) => {\n            off(type, handler.callback, handler.scope);\n        })\n    }\n\n    function getHandlerIdx(type, listener, scope) {\n\n        let idx = -1;\n\n        if (!handlers[type]) {\n            return idx;\n        }\n\n        handlers[type].some((item, index) => {\n            if (item && item.callback === listener && (!scope || scope === item.scope)) {\n                idx = index;\n                return true;\n            }\n        });\n        return idx;\n    }\n\n    function reset() {\n        handlers = {};\n    }\n\n    const instance = {\n        on,\n        once,\n        off,\n        trigger,\n        reset\n    };\n\n    return instance;\n}\n\nEventBus.__dashjs_factory_name = 'EventBus';\nconst factory = FactoryMaker.getSingletonFactory(EventBus);\nfactory.EVENT_PRIORITY_LOW = EVENT_PRIORITY_LOW;\nfactory.EVENT_PRIORITY_HIGH = EVENT_PRIORITY_HIGH;\nFactoryMaker.updateSingletonFactory(EventBus.__dashjs_factory_name, factory);\nexport default factory;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @module FactoryMaker\n * @ignore\n */\nconst FactoryMaker = (function () {\n\n    let instance;\n    let singletonContexts = [];\n    const singletonFactories = {};\n    const classFactories = {};\n\n    function extend(name, childInstance, override, context) {\n        if (!context[name] && childInstance) {\n            context[name] = {\n                instance: childInstance,\n                override: override\n            };\n        }\n    }\n\n    /**\n     * Use this method from your extended object.  this.factory is injected into your object.\n     * this.factory.getSingletonInstance(this.context, 'VideoModel')\n     * will return the video model for use in the extended object.\n     *\n     * @param {Object} context - injected into extended object as this.context\n     * @param {string} className - string name found in all dash.js objects\n     * with name __dashjs_factory_name Will be at the bottom. Will be the same as the object's name.\n     * @returns {*} Context aware instance of specified singleton name.\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function getSingletonInstance(context, className) {\n        for (const i in singletonContexts) {\n            const obj = singletonContexts[i];\n            if (obj.context === context && obj.name === className) {\n                return obj.instance;\n            }\n        }\n        return null;\n    }\n\n    /**\n     * Use this method to add an singleton instance to the system.  Useful for unit testing to mock objects etc.\n     *\n     * @param {Object} context\n     * @param {string} className\n     * @param {Object} instance\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function setSingletonInstance(context, className, instance) {\n        for (const i in singletonContexts) {\n            const obj = singletonContexts[i];\n            if (obj.context === context && obj.name === className) {\n                singletonContexts[i].instance = instance;\n                return;\n            }\n        }\n        singletonContexts.push({\n            name: className,\n            context: context,\n            instance: instance\n        });\n    }\n\n    /**\n     * Use this method to remove all singleton instances associated with a particular context.\n     *\n     * @param {Object} context\n     * @memberof module:FactoryMaker\n     * @instance\n     */\n    function deleteSingletonInstances(context) {\n        singletonContexts = singletonContexts.filter(x => x.context !== context);\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Factories storage Management\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function getFactoryByName(name, factoriesArray) {\n        return factoriesArray[name];\n    }\n\n    function updateFactory(name, factory, factoriesArray) {\n        if (name in factoriesArray) {\n            factoriesArray[name] = factory;\n        }\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Class Factories Management\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function updateClassFactory(name, factory) {\n        updateFactory(name, factory, classFactories);\n    }\n\n    function getClassFactoryByName(name) {\n        return getFactoryByName(name, classFactories);\n    }\n\n    function getClassFactory(classConstructor) {\n        let factory = getFactoryByName(classConstructor.__dashjs_factory_name, classFactories);\n\n        if (!factory) {\n            factory = function (context) {\n                if (context === undefined) {\n                    context = {};\n                }\n                return {\n                    create: function () {\n                        return merge(classConstructor, context, arguments);\n                    }\n                };\n            };\n\n            classFactories[classConstructor.__dashjs_factory_name] = factory; // store factory\n        }\n        return factory;\n    }\n\n    /*------------------------------------------------------------------------------------------*/\n\n    // Singleton Factory MAangement\n\n    /*------------------------------------------------------------------------------------------*/\n\n    function updateSingletonFactory(name, factory) {\n        updateFactory(name, factory, singletonFactories);\n    }\n\n    function getSingletonFactoryByName(name) {\n        return getFactoryByName(name, singletonFactories);\n    }\n\n    function getSingletonFactory(classConstructor) {\n        let factory = getFactoryByName(classConstructor.__dashjs_factory_name, singletonFactories);\n        if (!factory) {\n            factory = function (context) {\n                let instance;\n                if (context === undefined) {\n                    context = {};\n                }\n                return {\n                    getInstance: function () {\n                        // If we don't have an instance yet check for one on the context\n                        if (!instance) {\n                            instance = getSingletonInstance(context, classConstructor.__dashjs_factory_name);\n                        }\n                        // If there's no instance on the context then create one\n                        if (!instance) {\n                            instance = merge(classConstructor, context, arguments);\n                            singletonContexts.push({\n                                name: classConstructor.__dashjs_factory_name,\n                                context: context,\n                                instance: instance\n                            });\n                        }\n                        return instance;\n                    }\n                };\n            };\n            singletonFactories[classConstructor.__dashjs_factory_name] = factory; // store factory\n        }\n\n        return factory;\n    }\n\n    function merge(classConstructor, context, args) {\n\n        let classInstance;\n        const className = classConstructor.__dashjs_factory_name;\n        const extensionObject = context[className];\n\n        if (extensionObject) {\n\n            let extension = extensionObject.instance;\n\n            if (extensionObject.override) { //Override public methods in parent but keep parent.\n\n                classInstance = classConstructor.apply({context}, args);\n                extension = extension.apply({\n                    context,\n                    factory: instance,\n                    parent: classInstance\n                }, args);\n\n                for (const prop in extension) {\n                    if (classInstance.hasOwnProperty(prop)) {\n                        classInstance[prop] = extension[prop];\n                    }\n                }\n\n            } else { //replace parent object completely with new object. Same as dijon.\n\n                return extension.apply({\n                    context,\n                    factory: instance\n                }, args);\n\n            }\n        } else {\n            // Create new instance of the class\n            classInstance = classConstructor.apply({context}, args);\n        }\n\n        // Add getClassName function to class instance prototype (used by Debug)\n        classInstance.getClassName = function () {return className;};\n\n        return classInstance;\n    }\n\n    instance = {\n        extend: extend,\n        getSingletonInstance: getSingletonInstance,\n        setSingletonInstance: setSingletonInstance,\n        deleteSingletonInstances: deleteSingletonInstances,\n        getSingletonFactory: getSingletonFactory,\n        getSingletonFactoryByName: getSingletonFactoryByName,\n        updateSingletonFactory: updateSingletonFactory,\n        getClassFactory: getClassFactory,\n        getClassFactoryByName: getClassFactoryByName,\n        updateClassFactory: updateClassFactory\n    };\n\n    return instance;\n\n}());\n\nexport default FactoryMaker;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport FactoryMaker from './FactoryMaker.js';\nimport Utils from './Utils.js';\nimport Debug from '../core/Debug.js';\nimport Constants from '../streaming/constants/Constants.js';\nimport {HTTPRequest} from '../streaming/vo/metrics/HTTPRequest.js';\nimport EventBus from './EventBus.js';\nimport Events from './events/Events.js';\n\n/** @module Settings\n * @description Define the configuration parameters of Dash.js MediaPlayer.\n * @see {@link module:Settings~PlayerSettings PlayerSettings} for further information about the supported configuration properties.\n */\n\n\n/**\n * @typedef {Object} PlayerSettings\n * @property {module:Settings~DebugSettings} [debug]\n * Debug related settings.\n * @property {module:Settings~ErrorSettings} [errors]\n * Error related settings\n * @property {module:Settings~StreamingSettings} [streaming]\n * Streaming related settings.\n * @example\n *\n * // Full settings object\n * settings = {\n *        debug: {\n *            logLevel: Debug.LOG_LEVEL_WARNING,\n *            dispatchEvent: false\n *        },\n *        streaming: {\n *            abandonLoadTimeout: 10000,\n *            wallclockTimeUpdateInterval: 100,\n *            manifestUpdateRetryInterval: 100,\n *            liveUpdateTimeThresholdInMilliseconds: 0,\n *            cacheInitSegments: false,\n *            applyServiceDescription: true,\n *            applyProducerReferenceTime: true,\n *            applyContentSteering: true,\n *            enableManifestDurationMismatchFix: true,\n *            parseInbandPrft: false,\n *            enableManifestTimescaleMismatchFix: false,\n *            capabilities: {\n *               filterUnsupportedEssentialProperties: true,\n *               supportedEssentialProperties: [\n *                   { schemeIdUri: Constants.FONT_DOWNLOAD_DVB_SCHEME },\n *                   { schemeIdUri: Constants.COLOUR_PRIMARIES_SCHEME_ID_URI, value: /1|5|6|7/ },\n *                   { schemeIdUri: Constants.URL_QUERY_INFO_SCHEME },\n *                   { schemeIdUri: Constants.EXT_URL_QUERY_INFO_SCHEME },\n *                   { schemeIdUri: Constants.MATRIX_COEFFICIENTS_SCHEME_ID_URI, value: /0|1|5|6/ },\n *                   { schemeIdUri: Constants.TRANSFER_CHARACTERISTICS_SCHEME_ID_URI, value: /1|6|13|14|15/ },\n *                   ...Constants.THUMBNAILS_SCHEME_ID_URIS.map(ep => { return { 'schemeIdUri': ep }; })\n *               ],\n *               useMediaCapabilitiesApi: true,\n *               filterVideoColorimetryEssentialProperties: false,\n *               filterHDRMetadataFormatEssentialProperties: false\n *            },\n *            events: {\n *              eventControllerRefreshDelay: 100,\n *              deleteEventMessageDataTimeout: 10000\n *            }\n *            timeShiftBuffer: {\n *                calcFromSegmentTimeline: false,\n *                fallbackToSegmentTimeline: true\n *            },\n *            metrics: {\n *              maxListDepth: 100\n *            },\n *            delay: {\n *                liveDelayFragmentCount: NaN,\n *                liveDelay: NaN,\n *                useSuggestedPresentationDelay: true\n *            },\n *            protection: {\n *                keepProtectionMediaKeys: false,\n *                ignoreEmeEncryptedEvent: false,\n *                detectPlayreadyMessageFormat: true,\n *                ignoreKeyStatuses: false\n *            },\n *            buffer: {\n *                enableSeekDecorrelationFix: false,\n *                fastSwitchEnabled: true,\n *                flushBufferAtTrackSwitch: false,\n *                reuseExistingSourceBuffers: true,\n *                bufferPruningInterval: 10,\n *                bufferToKeep: 20,\n *                bufferTimeAtTopQuality: 30,\n *                bufferTimeAtTopQualityLongForm: 60,\n *                initialBufferLevel: NaN,\n *                bufferTimeDefault: 18,\n *                longFormContentDurationThreshold: 600,\n *                stallThreshold: 0.3,\n *                lowLatencyStallThreshold: 0.3,\n *                useAppendWindow: true,\n *                setStallState: true,\n *                avoidCurrentTimeRangePruning: false,\n *                useChangeType: true,\n *                mediaSourceDurationInfinity: true,\n *                resetSourceBuffersForTrackSwitch: false,\n *                syntheticStallEvents: {\n *                    enabled: false,\n *                    ignoreReadyState: false\n *                }\n *            },\n *            gaps: {\n *                jumpGaps: true,\n *                jumpLargeGaps: true,\n *                smallGapLimit: 1.5,\n *                threshold: 0.3,\n *                enableSeekFix: true,\n *                enableStallFix: false,\n *                stallSeek: 0.1\n *            },\n *            utcSynchronization: {\n *                enabled: true,\n *                useManifestDateHeaderTimeSource: true,\n *                backgroundAttempts: 2,\n *                timeBetweenSyncAttempts: 30,\n *                maximumTimeBetweenSyncAttempts: 600,\n *                minimumTimeBetweenSyncAttempts: 2,\n *                timeBetweenSyncAttemptsAdjustmentFactor: 2,\n *                maximumAllowedDrift: 100,\n *                enableBackgroundSyncAfterSegmentDownloadError: true,\n *                defaultTimingSource: {\n *                    scheme: 'urn:mpeg:dash:utc:http-xsdate:2014',\n *                    value: 'http://time.akamai.com/?iso&ms'\n *                }\n *            },\n *            scheduling: {\n *                defaultTimeout: 500,\n *                lowLatencyTimeout: 0,\n *                scheduleWhilePaused: true\n *            },\n *            text: {\n *                defaultEnabled: true,\n *                dispatchForManualRendering: false,\n *                extendSegmentedCues: true,\n *                imsc: {\n *                    displayForcedOnlyMode: false,\n *                    enableRollUp: true\n *                },\n *                webvtt: {\n *                    customRenderingEnabled: false\n *                }\n *            },\n *            liveCatchup: {\n *                maxDrift: NaN,\n *                playbackRate: {min: NaN, max: NaN},\n *                playbackBufferMin: 0.5,\n *                enabled: null,\n *                mode: Constants.LIVE_CATCHUP_MODE_DEFAULT\n *            },\n *            lastBitrateCachingInfo: { enabled: true, ttl: 360000 },\n *            lastMediaSettingsCachingInfo: { enabled: true, ttl: 360000 },\n *            saveLastMediaSettingsForCurrentStreamingSession: true,\n *            cacheLoadThresholds: { video: 10, audio: 5 },\n *            trackSwitchMode: {\n *                audio: Constants.TRACK_SWITCH_MODE_ALWAYS_REPLACE,\n *                video: Constants.TRACK_SWITCH_MODE_NEVER_REPLACE\n *            },\n *            ignoreSelectionPriority: false,\n *            prioritizeRoleMain: true,\n *            assumeDefaultRoleAsMain: true,\n *            selectionModeForInitialTrack: Constants.TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY,\n *            fragmentRequestTimeout: 20000,\n *            fragmentRequestProgressTimeout: -1,\n *            manifestRequestTimeout: 10000,\n *            retryIntervals: {\n *                [HTTPRequest.MPD_TYPE]: 500,\n *                [HTTPRequest.XLINK_EXPANSION_TYPE]: 500,\n *                [HTTPRequest.MEDIA_SEGMENT_TYPE]: 1000,\n *                [HTTPRequest.INIT_SEGMENT_TYPE]: 1000,\n *                [HTTPRequest.BITSTREAM_SWITCHING_SEGMENT_TYPE]: 1000,\n *                [HTTPRequest.INDEX_SEGMENT_TYPE]: 1000,\n *                [HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE]: 1000,\n *                [HTTPRequest.LICENSE]: 1000,\n *                [HTTPRequest.OTHER_TYPE]: 1000,\n *                lowLatencyReductionFactor: 10\n *            },\n *            retryAttempts: {\n *                [HTTPRequest.MPD_TYPE]: 3,\n *                [HTTPRequest.XLINK_EXPANSION_TYPE]: 1,\n *                [HTTPRequest.MEDIA_SEGMENT_TYPE]: 3,\n *                [HTTPRequest.INIT_SEGMENT_TYPE]: 3,\n *                [HTTPRequest.BITSTREAM_SWITCHING_SEGMENT_TYPE]: 3,\n *                [HTTPRequest.INDEX_SEGMENT_TYPE]: 3,\n *                [HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE]: 3,\n *                [HTTPRequest.LICENSE]: 3,\n *                [HTTPRequest.OTHER_TYPE]: 3,\n *                lowLatencyMultiplyFactor: 5\n *            },\n *             abr: {\n *                 limitBitrateByPortal: false,\n *                 usePixelRatioInLimitBitrateByPortal: false,\n *                rules: {\n *                     throughputRule: {\n *                         active: true\n *                     },\n *                     bolaRule: {\n *                         active: true\n *                     },\n *                     insufficientBufferRule: {\n *                         active: true,\n *                         parameters: {\n *                             throughputSafetyFactor: 0.7,\n *                             segmentIgnoreCount: 2\n *                         }\n *                     },\n *                     switchHistoryRule: {\n *                         active: true,\n *                         parameters: {\n *                             sampleSize: 8,\n *                             switchPercentageThreshold: 0.075\n *                         }\n *                     },\n *                     droppedFramesRule: {\n *                         active: true,\n *                         parameters: {\n *                             minimumSampleSize: 375,\n *                             droppedFramesPercentageThreshold: 0.15\n *                         }\n *                     },\n *                     abandonRequestsRule: {\n *                         active: true,\n *                         parameters: {\n *                             abandonDurationMultiplier: 1.8,\n *                             minSegmentDownloadTimeThresholdInMs: 500,\n *                             minThroughputSamplesThreshold: 6\n *                         }\n *                     },\n *                     l2ARule: {\n *                         active: false\n *                     },\n *                     loLPRule: {\n *                         active: false\n *                     }\n *                 },\n *                 throughput: {\n *                     averageCalculationMode: Constants.THROUGHPUT_CALCULATION_MODES.EWMA,\n *                     lowLatencyDownloadTimeCalculationMode: Constants.LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE.MOOF_PARSING,\n *                     useResourceTimingApi: true,\n *                     useNetworkInformationApi: {\n *                         xhr: false,\n *                         fetch: false\n *                     },\n *                     useDeadTimeLatency: true,\n *                     bandwidthSafetyFactor: 0.9,\n *                     sampleSettings: {\n *                         live: 3,\n *                         vod: 4,\n *                         enableSampleSizeAdjustment: true,\n *                         decreaseScale: 0.7,\n *                         increaseScale: 1.3,\n *                         maxMeasurementsToKeep: 20,\n *                         averageLatencySampleAmount: 4,\n *                     },\n *                     ewma: {\n *                         throughputSlowHalfLifeSeconds: 8,\n *                         throughputFastHalfLifeSeconds: 3,\n *                         latencySlowHalfLifeCount: 2,\n *                         latencyFastHalfLifeCount: 1,\n *                         weightDownloadTimeMultiplicationFactor: 0.0015\n *                     }\n *                 },\n *                 maxBitrate: {\n *                     audio: -1,\n *                     video: -1\n *                 },\n *                 minBitrate: {\n *                     audio: -1,\n *                     video: -1\n *                 },\n *                 initialBitrate: {\n *                     audio: -1,\n *                     video: -1\n *                 },\n *                 autoSwitchBitrate: {\n *                     audio: true,\n *                     video: true\n *                 }\n *             },\n *            cmcd: {\n *                enabled: false,\n *                sid: null,\n *                cid: null,\n *                rtp: null,\n *                rtpSafetyFactor: 5,\n *                mode: Constants.CMCD_MODE_QUERY,\n *                enabledKeys: ['br', 'd', 'ot', 'tb' , 'bl', 'dl', 'mtp', 'nor', 'nrr', 'su' , 'bs', 'rtp' , 'cid', 'pr', 'sf', 'sid', 'st', 'v']\n *                includeInRequests: ['segment', 'mpd'],\n *                version: 1\n *            },\n *            cmsd: {\n *                enabled: false,\n *                abr: {\n *                    applyMb: false,\n *                    etpWeightRatio: 0\n *                }\n *            },\n *            defaultSchemeIdUri: {\n *                viewpoint: '',\n *                audioChannelConfiguration: 'urn:mpeg:mpegB:cicp:ChannelConfiguration',\n *                role: 'urn:mpeg:dash:role:2011',\n *                accessibility: 'urn:mpeg:dash:role:2011'\n *            }\n *          },\n *          errors: {\n *            recoverAttempts: {\n *                mediaErrorDecode: 5\n *             }\n *          }\n * }\n */\n\n/**\n * @typedef {Object} TimeShiftBuffer\n * @property {boolean} [calcFromSegmentTimeline=false]\n * Enable calculation of the DVR window for SegmentTimeline manifests based on the entries in \\<SegmentTimeline\\>.\n *  * @property {boolean} [fallbackToSegmentTimeline=true]\n * In case the MPD uses \\<SegmentTimeline\\ and no segment is found within the DVR window the DVR window is calculated based on the entries in \\<SegmentTimeline\\>.\n */\n\n/**\n * @typedef {Object} EventSettings\n * @property {number} [eventControllerRefreshDelay=100]\n * Interval timer used by the EventController to check if events need to be triggered or removed.\n * @property {number} [deleteEventMessageDataTimeout=10000]\n * If this value is larger than -1 the EventController will delete the message data attributes of events after they have been started and dispatched to the application.\n * This is to save memory in case events have a long duration and need to be persisted in the EventController.\n * This parameter defines the time in milliseconds between the start of an event and when the message data is deleted.\n * If an event is dispatched for the second time (e.g. when the user seeks back) the message data will not be included in the dispatched event if it has been deleted already.\n * Set this value to -1 to not delete any message data.\n */\n\n/**\n * @typedef {Object} LiveDelay\n * @property {number} [liveDelayFragmentCount=NaN]\n * Changing this value will lower or increase live stream latency.\n *\n * The detected segment duration will be multiplied by this value to define a time in seconds to delay a live stream from the live edge.\n *\n * Lowering this value will lower latency but may decrease the player's ability to build a stable buffer.\n * @property {number} [liveDelay=NaN]\n * Equivalent in seconds of setLiveDelayFragmentCount.\n *\n * Lowering this value will lower latency but may decrease the player's ability to build a stable buffer.\n *\n * This value should be less than the manifest duration by a couple of segment durations to avoid playback issues.\n *\n * If set, this parameter will take precedence over setLiveDelayFragmentCount and manifest info.\n * @property {boolean} [useSuggestedPresentationDelay=true]\n * Set to true if you would like to overwrite the default live delay and honor the SuggestedPresentationDelay attribute in by the manifest.\n */\n\n/**\n * @typedef {Object} Buffer\n * @property {boolean} [enableSeekDecorrelationFix=false]\n * Enables a workaround for playback start on some devices, e.g. WebOS 4.9.\n * It is necessary because some browsers do not support setting currentTime on video element to a value that is outside of current buffer.\n *\n * If you experience unexpected seeking triggered by BufferController, you can try setting this value to false.\n\n * @property {boolean} [fastSwitchEnabled=true]\n * When enabled, after an ABR up-switch in quality, instead of requesting and appending the next fragment at the end of the current buffer range it is requested and appended closer to the current time.\n *\n * When enabled, The maximum time to render a higher quality is current time + (1.5 * fragment duration).\n *\n * Note, When ABR down-switch is detected, we appended the lower quality at the end of the buffer range to preserve the\n * higher quality media for as long as possible.\n *\n * If enabled, it should be noted there are a few cases when the client will not replace inside buffer range but rather just append at the end.\n * 1. When the buffer level is less than one fragment duration.\n * 2. The client is in an Abandonment State due to recent fragment abandonment event.\n *\n * Known issues:\n * 1. In IE11 with auto switching off, if a user switches to a quality they can not download in time the fragment may be appended in the same range as the playhead or even in the past, in IE11 it may cause a stutter or stall in playback.\n * @property {boolean} [flushBufferAtTrackSwitch=false]\n * When enabled, after a track switch and in case buffer is being replaced, the video element is flushed (seek at current playback time) once a segment of the new track is appended in buffer in order to force video decoder to play new track.\n *\n * This can be required on some devices like GoogleCast devices to make track switching functional.\n *\n * Otherwise, track switching will be effective only once after previous buffered track is fully consumed.\n * @property {boolean} [reuseExistingSourceBuffers=true]\n * Enable reuse of existing MediaSource Sourcebuffers during period transition.\n * @property {number} [bufferPruningInterval=10]\n * The interval of pruning buffer in seconds.\n * @property {number} [bufferToKeep=20]\n * This value influences the buffer pruning logic.\n *\n * Allows you to modify the buffer that is kept in source buffer in seconds.\n * 0|-----------bufferToPrune-----------|-----bufferToKeep-----|currentTime|\n * @property {number} [bufferTimeDefault=18]\n * The time that the internal buffer target will be set to when not playing at the top quality.\n * @property {number} [bufferTimeAtTopQuality=30]\n * The time that the internal buffer target will be set to once playing the top quality.\n *\n * If there are multiple bitrates in your adaptation, and the media is playing at the highest bitrate, then we try to build a larger buffer at the top quality to increase stability and to maintain media quality.\n * @property {number} [bufferTimeAtTopQualityLongForm=60]\n * The time that the internal buffer target will be set to once playing the top quality for long form content.\n * @property {number} [longFormContentDurationThreshold=600]\n * The threshold which defines if the media is considered long form content.\n *\n * This will directly affect the buffer targets when playing back at the top quality.\n * @property {number} [initialBufferLevel=NaN]\n * Initial buffer level before playback starts\n * @property {number} [stallThreshold=0.3]\n * Stall threshold used in BufferController.js to determine whether a track should still be changed and which buffer range to prune.\n * @property {number} [lowLatencyStallThreshold=0.3]\n * Low Latency stall threshold used in BufferController.js to determine whether a track should still be changed and which buffer range to prune.\n * @property {boolean} [useAppendWindow=true]\n * Specifies if the appendWindow attributes of the MSE SourceBuffers should be set according to content duration from manifest.\n * @property {boolean} [setStallState=true]\n * Specifies if we fire manual waiting events once the stall threshold is reached.\n * @property {module:Settings~SyntheticStallSettings} [syntheticStallEvents]\n * Specifies if manual stall events are to be fired once the stall threshold is reached.\n * @property {boolean} [avoidCurrentTimeRangePruning=false]\n * Avoids pruning of the buffered range that contains the current playback time.\n *\n * That buffered range is likely to have been enqueued for playback. Pruning it causes a flush and reenqueue in WPE and WebKitGTK based browsers. This stresses the video decoder and can cause stuttering on embedded platforms.\n * @property {boolean} [useChangeType=true]\n * If this flag is set to true then dash.js will use the MSE v.2 API call \"changeType()\" before switching to a different codec family.\n * Note that some platforms might not implement the changeType function. dash.js is checking for the availability before trying to call it.\n * @property {boolean} [mediaSourceDurationInfinity=true]\n * If this flag is set to true then dash.js will allow `Infinity` to be set as the MediaSource duration otherwise the duration will be set to `Math.pow(2,32)` instead of `Infinity` to allow appending segments indefinitely.\n * Some platforms such as WebOS 4.x have issues with seeking when duration is set to `Infinity`, setting this flag to false resolve this.\n * @property {boolean} [resetSourceBuffersForTrackSwitch=false]\n * When switching to a track that is not compatible with the currently active MSE SourceBuffers, MSE will be reset. This happens when we switch codecs on a system\n * that does not properly implement \"changeType()\", such as webOS 4.0 and before.\n */\n\n/**\n * @typedef {Object} module:Settings~AudioVideoSettings\n * @property {number|boolean|string} [audio]\n * Configuration for audio media type of tracks.\n * @property {number|boolean|string} [video]\n * Configuration for video media type of tracks.\n */\n\n/**\n * @typedef {Object} module:Settings~SyntheticStallSettings\n * @property {boolean} [enabled]\n * Enables manual stall events and sets the playback rate to 0 once the stall threshold is reached.\n * @property {boolean} [ignoreReadyState]\n * Ignore the media element's ready state when entering or exiting a stall.\n * Enable this when either of these scenarios still occur with synthetic stalls enabled:\n * - If the buffer is empty, but playback is not stalled.\n * - If playback resumes, but a playing event isn't reported.\n */\n\n/**\n * @typedef {Object} DebugSettings\n * @property {number} [logLevel=dashjs.Debug.LOG_LEVEL_WARNING]\n * Sets up the log level. The levels are cumulative.\n *\n * For example, if you set the log level to dashjs.Debug.LOG_LEVEL_WARNING all warnings, errors and fatals will be logged.\n *\n * Possible values.\n *\n * - dashjs.Debug.LOG_LEVEL_NONE\n * No message is written in the browser console.\n *\n * - dashjs.Debug.LOG_LEVEL_FATAL\n * Log fatal errors.\n * An error is considered fatal when it causes playback to fail completely.\n *\n * - dashjs.Debug.LOG_LEVEL_ERROR\n * Log error messages.\n *\n * - dashjs.Debug.LOG_LEVEL_WARNING\n * Log warning messages.\n *\n * - dashjs.Debug.LOG_LEVEL_INFO\n * Log info messages.\n *\n * - dashjs.Debug.LOG_LEVEL_DEBUG\n * Log debug messages.\n * @property {boolean} [dispatchEvent=false]\n * Enable to trigger a Events.LOG event whenever log output is generated.\n *\n * Note this will be dispatched regardless of log level.\n */\n\n/**\n * @typedef {Object} module:Settings~ErrorSettings\n * @property {object} [recoverAttempts={mediaErrorDecode: 5}]\n * Defines the maximum number of recover attempts for specific media errors.\n *\n * For mediaErrorDecode the player will reset the MSE and skip the blacklisted segment that caused the decode error. The resulting gap will be handled by the GapController.\n */\n\n/**\n * @typedef {Object} CachingInfoSettings\n * @property {boolean} [enable]\n * Enable or disable the caching feature.\n * @property {number} [ttl]\n * Time to live.\n *\n * A value defined in milliseconds representing how log to cache the settings for.\n */\n\n/**\n * @typedef {Object} Gaps\n * @property {boolean} [jumpGaps=true]\n * Sets whether player should jump small gaps (discontinuities) in the buffer.\n * @property {boolean} [jumpLargeGaps=true]\n * Sets whether player should jump large gaps (discontinuities) in the buffer.\n * @property {number} [smallGapLimit=1.5]\n * Time in seconds for a gap to be considered small.\n * @property {number} [threshold=0.3]\n * Threshold at which the gap handling is executed. If currentRangeEnd - currentTime < threshold the gap jump will be triggered.\n * For live stream the jump might be delayed to keep a consistent live edge.\n * Note that the amount of buffer at which platforms automatically stall might differ.\n * @property {boolean} [enableSeekFix=true]\n * Enables the adjustment of the seek target once no valid segment request could be generated for a specific seek time. This can happen if the user seeks to a position for which there is a gap in the timeline.\n * @property {boolean} [enableStallFix=false]\n * If playback stalled in a buffered range this fix will perform a seek by the value defined in stallSeek to trigger playback again.\n * @property {number} [stallSeek=0.1]\n * Value to be used in case enableStallFix is set to true\n */\n\n/**\n * @typedef {Object} UtcSynchronizationSettings\n * @property {boolean} [enabled=true]\n * Enables or disables the UTC clock synchronization\n * @property {boolean} [useManifestDateHeaderTimeSource=true]\n * Allows you to enable the use of the Date Header, if exposed with CORS, as a timing source for live edge detection.\n *\n * The use of the date header will happen only after the other timing source that take precedence fail or are omitted as described.\n * @property {number} [backgroundAttempts=2]\n * Number of synchronization attempts to perform in the background after an initial synchronization request has been done. This is used to verify that the derived client-server offset is correct.\n *\n * The background requests are async and done in parallel to the start of the playback.\n *\n * This value is also used to perform a resync after 404 errors on segments.\n * @property {number} [timeBetweenSyncAttempts=30]\n * The time in seconds between two consecutive sync attempts.\n *\n * Note: This value is used as an initial starting value. The internal value of the TimeSyncController is adjusted during playback based on the drift between two consecutive synchronization attempts.\n *\n * Note: A sync is only performed after an MPD update. In case the @minimumUpdatePeriod is larger than this value the sync will be delayed until the next MPD update.\n * @property {number} [maximumTimeBetweenSyncAttempts=600]\n * The maximum time in seconds between two consecutive sync attempts.\n *\n * @property {number} [minimumTimeBetweenSyncAttempts=2]\n * The minimum time in seconds between two consecutive sync attempts.\n *\n * @property {number} [timeBetweenSyncAttemptsAdjustmentFactor=2]\n * The factor used to multiply or divide the timeBetweenSyncAttempts parameter after a sync. The maximumAllowedDrift defines whether this value is used as a factor or a dividend.\n *\n * @property {number} [maximumAllowedDrift=100]\n * The maximum allowed drift specified in milliseconds between two consecutive synchronization attempts.\n *\n * @property {boolean} [enableBackgroundSyncAfterSegmentDownloadError=true]\n * Enables or disables the background sync after the player ran into a segment download error.\n *\n * @property {object} [defaultTimingSource={scheme:'urn:mpeg:dash:utc:http-xsdate:2014',value: 'http://time.akamai.com/?iso&ms'}]\n * The default timing source to be used. The timing sources in the MPD take precedence over this one.\n */\n\n/**\n * @typedef {Object} Scheduling\n * @property {number} [defaultTimeout=500]\n * Default timeout between two consecutive segment scheduling attempts\n * @property {number} [lowLatencyTimeout=0]\n * Default timeout between two consecutive low-latency segment scheduling attempts\n * @property {boolean} [scheduleWhilePaused=true]\n * Set to true if you would like dash.js to keep downloading fragments in the background when the video element is paused.\n */\n\n/**\n * @typedef {Object} Text\n * @property {boolean} [defaultEnabled=true]\n * Enable/disable subtitle rendering by default.\n * @property {boolean} [dispatchForManualRendering=false]\n * Enable/disable firing of CueEnter/CueExt events. This will disable the display of subtitles and should be used when you want to have full control about rendering them.\n * @property {boolean} [extendSegmentedCues=true]\n * Enable/disable patching of segmented cues in order to merge as a single cue by extending cue end time.\n * @property {boolean} [imsc.displayForcedOnlyMode=false]\n * Enable/disable forced only mode in IMSC captions.\n * When true, only those captions where itts:forcedDisplay=\"true\" will be displayed.\n * @property {boolean} [imsc.enableRollUp=true]\n * Enable/disable rollUp style display of IMSC captions.\n * @property {object} [webvtt.customRenderingEnabled=false]\n * Enables the custom rendering for WebVTT captions. For details refer to the \"Subtitles and Captions\" sample section of dash.js.\n * Custom WebVTT rendering requires the external library vtt.js that can be found in the contrib folder.\n */\n\n/**\n * @typedef {Object} LiveCatchupSettings\n * @property {number} [maxDrift=NaN]\n * Use this method to set the maximum latency deviation allowed before dash.js to do a seeking to live position.\n *\n * In low latency mode, when the difference between the measured latency and the target one, as an absolute number, is higher than the one sets with this method, then dash.js does a seek to live edge position minus the target live delay.\n *\n * LowLatencyMaxDriftBeforeSeeking should be provided in seconds.\n *\n * If 0, then seeking operations won't be used for fixing latency deviations.\n *\n * Note: Catch-up mechanism is only applied when playing low latency live streams.\n * @property {number} [playbackRate={min: NaN, max: NaN}]\n * Use this parameter to set the minimum and maximum catch up rates, as percentages, for low latency live streams.\n *\n * In low latency mode, when measured latency is higher/lower than the target one, dash.js increases/decreases playback rate respectively up to (+/-) the percentage defined with this method until target is reached.\n *\n * Valid values for min catch up rate are in the range -0.5 to 0 (-50% to 0% playback rate decrease)\n *\n * Valid values for max catch up rate are in the range 0 to 1 (0% to 100% playback rate increase).\n *\n * Set min and max to NaN to turn off live catch up feature.\n *\n * These playback rate limits take precedence over any PlaybackRate values in ServiceDescription elements in an MPD. If only one of the min/max properties is given a value, the property without a value will not fall back to a ServiceDescription value. Its default value of NaN will be used.\n *\n * Note: Catch-up mechanism is only applied when playing low latency live streams.\n * @property {number} [playbackBufferMin=0.5]\n * Use this parameter to specify the minimum buffer which is used for LoL+ based playback rate reduction.\n *\n *\n * @property {boolean} [enabled=null]\n * Use this parameter to enable the catchup mode for non low-latency streams.\n *\n * @property {string} [mode=\"liveCatchupModeDefault\"]\n * Use this parameter to switch between different catchup modes.\n *\n * Options: \"liveCatchupModeDefault\" or \"liveCatchupModeLOLP\".\n *\n * Note: Catch-up mechanism is automatically applied when playing low latency live streams.\n */\n\n/**\n * @typedef {Object} RequestTypeSettings\n * @property {number} [MPD]\n * Manifest type of requests.\n * @property {number} [XLinkExpansion]\n * XLink expansion type of requests.\n * @property {number} [InitializationSegment]\n * Request to retrieve an initialization segment.\n * @property {number} [IndexSegment]\n * Request to retrieve an index segment (SegmentBase).\n * @property {number} [MediaSegment]\n * Request to retrieve a media segment (video/audio/image/text chunk).\n * @property {number} [BitstreamSwitchingSegment]\n * Bitrate stream switching type of request.\n * @property {number} [FragmentInfoSegment]\n * Request to retrieve a FragmentInfo segment (specific to Smooth Streaming live streams).\n * @property {number} [other]\n * Other type of request.\n * @property {number} [lowLatencyReductionFactor]\n * For low latency mode, values of type of request are divided by lowLatencyReductionFactor.\n *\n * Note: It's not type of request.\n * @property {number} [lowLatencyMultiplyFactor]\n * For low latency mode, values of type of request are multiplied by lowLatencyMultiplyFactor.\n *\n * Note: It's not type of request.\n */\n\n\n/**\n * @typedef {Object} Protection\n * @property {boolean} [keepProtectionMediaKeys=false]\n * Set the value for the ProtectionController and MediaKeys life cycle.\n *\n * If true, the ProtectionController and then created MediaKeys and MediaKeySessions will be preserved during the MediaPlayer lifetime.\n * @property {boolean} [ignoreEmeEncryptedEvent=false]\n * If set to true the player will ignore \"encrypted\" and \"needkey\" events thrown by the EME.\n *\n * @property {boolean} [detectPlayreadyMessageFormat=true]\n * If set to true the player will use the raw unwrapped message from the Playready CDM\n *\n * @property {boolean} [ignoreKeyStatuses=false]\n * If set to true the player will ignore the status of a key and try to play the corresponding track regardless whether the key is usable or not.\n */\n\n/**\n * @typedef {Object} Capabilities\n * @property {boolean} [filterUnsupportedEssentialProperties=true]\n * Enable to filter all the AdaptationSets and Representations which contain an unsupported \\<EssentialProperty\\> element.\n * @property {Array.<string>} [supportedEssentialProperties]\n * List of supported \\<EssentialProperty\\> elements\n * @property {boolean} [useMediaCapabilitiesApi=true]\n * Enable to use the MediaCapabilities API to check whether codecs are supported. If disabled MSE.isTypeSupported will be used instead.\n * @property {boolean} [filterVideoColorimetryEssentialProperties=false]\n * Enable dash.js to query MediaCapabilities API for signalled Colorimetry EssentialProperties (per schemeIdUris: 'urn:mpeg:mpegB:cicp:ColourPrimaries', 'urn:mpeg:mpegB:cicp:TransferCharacteristics').\n * If disabled, registered properties per supportedEssentialProperties will be allowed without any further checking (including 'urn:mpeg:mpegB:cicp:MatrixCoefficients').\n * @property {boolean} [filterHDRMetadataFormatEssentialProperties=false]\n * Enable dash.js to query MediaCapabilities API for signalled HDR-MetadataFormat EssentialProperty (per schemeIdUri:'urn:dvb:dash:hdr-dmi').\n */\n\n/**\n * @typedef {Object} AbrSettings\n * @property {boolean} [limitBitrateByPortal=false]\n * If true, the size of the video portal will limit the max chosen video resolution.\n * @property {boolean} [usePixelRatioInLimitBitrateByPortal=false]\n * Sets whether to take into account the device's pixel ratio when defining the portal dimensions.\n *\n * Useful on, for example, retina displays.\n * @property {module:Settings~AbrRules} [rules]\n * Enable/Disable individual ABR rules. Note that if the throughputRule and the bolaRule are activated at the same time we switch to a dynamic mode.\n * In the dynamic mode either ThroughputRule or BolaRule are active but not both at the same time.\n *\n * l2ARule and loLPRule are ABR rules that are designed for low latency streams. They are tested as standalone rules meaning the other rules should be deactivated when choosing these rules.\n * @property {module:Settings~ThroughputSettings} [throughput]\n * Settings related to throughput calculation\n * @property {module:Settings~AudioVideoSettings} [maxBitrate={audio: -1, video: -1}]\n * The maximum bitrate that the ABR algorithms will choose. This value is specified in kbps.\n *\n * Use -1 for no limit.\n * @property {module:Settings~AudioVideoSettings} [minBitrate={audio: -1, video: -1}]\n * The minimum bitrate that the ABR algorithms will choose. This value is specified in kbps.\n *\n * Use -1 for no limit.\n * @property {module:Settings~AudioVideoSettings} [initialBitrate={audio: -1, video: -1}]\n * Explicitly set the starting bitrate for audio or video. This value is specified in kbps.\n *\n * Use -1 to let the player decide.\n * @property {module:Settings~AudioVideoSettings} [autoSwitchBitrate={audio: true, video: true}]\n * Indicates whether the player should enable ABR algorithms to switch the bitrate.\n */\n\n/**\n * @typedef {Object} AbrRules\n * @property {module:Settings~ThroughputRule} [throughputRule]\n * Configuration of the Throughput rule\n * @property {module:Settings~BolaRule} [bolaRule]\n * Configuration of the BOLA rule\n * @property {module:Settings~InsufficientBufferRule} [insufficientBufferRule]\n * Configuration of the Insufficient Buffer rule\n * @property {module:Settings~SwitchHistoryRule} [switchHistoryRule]\n * Configuration of the Switch History rule\n * @property {module:Settings~DroppedFramesRule} [droppedFramesRule]\n * Configuration of the Dropped Frames rule\n * @property {module:Settings~AbandonRequestsRule} [abandonRequestsRule]\n * Configuration of the Abandon Requests rule\n * @property {module:Settings~L2ARule} [l2ARule]\n * Configuration of the L2A rule\n * @property {module:Settings~LoLPRule} [loLPRule]\n * Configuration of the LoLP rule\n */\n\n/**\n * @typedef {Object} ThroughputRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n */\n\n/**\n * @typedef {Object} BolaRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n */\n\n/**\n * @typedef {Object} InsufficientBufferRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n * @property {object} [parameters={throughputSafetyFactor=0.7, segmentIgnoreCount=2}]\n * Configures the rule specific parameters.\n *\n * - `throughputSafetyFactor`: The safety factor that is applied to the derived throughput, see example in the Description.\n * - `segmentIgnoreCount`: This rule is not taken into account until the first segmentIgnoreCount media segments have been appended to the buffer.\n */\n\n/**\n * @typedef {Object} SwitchHistoryRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n * @property {object} [parameters={sampleSize=8, switchPercentageThreshold=0.075}]\n * Configures the rule specific parameters.\n *\n * - `sampleSize`: Number of switch requests (\"no switch\", because of the selected Representation is already playing or \"actual switches\") required before the rule is applied\n * - `switchPercentageThreshold`: Ratio of actual quality drops compared to no drops before a quality down-switch is triggered\n */\n\n/**\n * @typedef {Object} DroppedFramesRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n * @property {object} [parameters={minimumSampleSize=375, droppedFramesPercentageThreshold=0.15}]\n * Configures the rule specific parameters.\n *\n * - `minimumSampleSize`: Sum of rendered and dropped frames required for each Representation before the rule kicks in.\n * - `droppedFramesPercentageThreshold`: Minimum percentage of dropped frames to trigger a quality down switch. Values are defined in the range of 0 - 1.\n */\n\n/**\n * @typedef {Object} AbandonRequestsRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n * @property {object} [parameters={abandonDurationMultiplier=1.8, minSegmentDownloadTimeThresholdInMs=500, minThroughputSamplesThreshold=6}]\n * Configures the rule specific parameters.\n *\n * - `abandonDurationMultiplier`: Factor to multiply with the segment duration to compare against the estimated remaining download time of the current segment. See code example above.\n * - `minSegmentDownloadTimeThresholdInMs`: The AbandonRequestRule only kicks if the download time of the current segment exceeds this value.\n * - `minThroughputSamplesThreshold`: Minimum throughput samples (equivalent to number of progress events) required before the AbandonRequestRule kicks in.\n */\n\n/**\n * @typedef {Object} L2ARule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n */\n\n/**\n * @typedef {Object} LoLPRule\n * @property {boolean} [active=true]\n * Enable or disable the rule\n */\n\n/**\n * @typedef {Object} ThroughputSettings\n * @property {string} [averageCalculationMode=Constants.THROUGHPUT_CALCULATION_MODES.EWMA]\n * Defines the default mode for calculating the throughput based on the samples collected during playback.\n *\n * For arithmetic and harmonic mean calculations we use a sliding window with the values defined in \"sampleSettings\"\n *\n * For exponential weighted moving average calculation the default values can be changed in \"ewma\"\n * @property {string} [lowLatencyDownloadTimeCalculationMode=Constants.LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE.MOOF_PARSING]\n * Defines the effective download time estimation method we use for low latency streams that utilize the Fetch API and chunked transfer coding\n * @property {boolean} [useResourceTimingApi=true]\n * If set to true the ResourceTimingApi is used to derive the download time and the number of downloaded bytes.\n * This option has no effect for low latency streaming as the download time equals the segment duration in most of the cases and therefor does not provide reliable values\n * @property {object} [useNetworkInformationApi = { xhr=false, fetch=false}]\n * If set to true the NetworkInformationApi is used to derive the current throughput. Browser support is limited, only available in Chrome and Edge.\n * Applies to standard (XHR requests) and/or low latency streaming (Fetch API requests).\n * @property {boolean} [useDeadTimeLatency=true]\n * If true, only the download portion will be considered part of the download bitrate and latency will be regarded as static.\n *\n * If false, the reciprocal of the whole transfer time will be used.\n * @property {number} [bandwidthSafetyFactor=0.9]\n * Standard ABR throughput rules multiply the throughput by this value.\n *\n * It should be between 0 and 1, with lower values giving less rebuffering (but also lower quality)\n * @property {object} [sampleSettings = {live=3,vod=4,enableSampleSizeAdjustment=true,decreaseScale=0.7,increaseScale=1.3,maxMeasurementsToKeep=20,averageLatencySampleAmount=4}]\n * When deriving the throughput based on the arithmetic or harmonic mean these settings define:\n * - `live`: Number of throughput samples to use (sample size) for live streams\n * - `vod`: Number of throughput samples to use (sample size) for VoD streams\n * - `enableSampleSizeAdjustment`: Adjust the sample sizes if throughput samples vary a lot\n * - `decreaseScale`: Increase sample size by one if the ratio of current and previous sample is below or equal this value\n * - `increaseScale`: Increase sample size by one if the ratio of current and previous sample is higher or equal this value\n * - `maxMeasurementsToKeep`: Number of samples to keep before sliding samples out of the window\n * - `averageLatencySampleAmount`: Number of latency samples to use (sample size)\n * @property {object} [ewma={throughputSlowHalfLifeSeconds=8,throughputFastHalfLifeSeconds=3,latencySlowHalfLifeCount=2,latencyFastHalfLifeCount=1, weightDownloadTimeMultiplicationFactor=0.0015}]\n * When deriving the throughput based on the exponential weighted moving average these settings define:\n * - `throughputSlowHalfLifeSeconds`: Number by which the weight of the current throughput measurement is divided, see ThroughputModel._updateEwmaValues\n * - `throughputFastHalfLifeSeconds`: Number by which the weight of the current throughput measurement is divided, see ThroughputModel._updateEwmaValues\n * - `latencySlowHalfLifeCount`: Number by which the weight of the current latency is divided, see ThroughputModel._updateEwmaValues\n * - `latencyFastHalfLifeCount`: Number by which the weight of the current latency is divided, see ThroughputModel._updateEwmaValues\n * - `weightDownloadTimeMultiplicationFactor`: This value is multiplied with the download time in milliseconds to derive the weight for the EWMA calculation.\n */\n\n/**\n * @typedef {Object} CmcdSettings\n * @property {boolean} [applyParametersFromMpd=true]\n * Set to true if dash.js should use the CMCD parameters defined in the MPD.\n * @property {boolean} [enable=false]\n * Enable or disable the CMCD reporting.\n * @property {string} [sid]\n * GUID identifying the current playback session.\n *\n * Should be in UUID format.\n *\n * If not specified a UUID will be automatically generated.\n * @property {string} [cid]\n * A unique string to identify the current content.\n *\n * If not specified it will be a hash of the MPD url.\n * @property {number} [rtp]\n * The requested maximum throughput that the client considers sufficient for delivery of the asset.\n *\n * If not specified this value will be dynamically calculated in the CMCDModel based on the current buffer level.\n * @property {number} [rtpSafetyFactor=5]\n * This value is used as a factor for the rtp value calculation: rtp = minBandwidth * rtpSafetyFactor\n *\n * If not specified this value defaults to 5. Note that this value is only used when no static rtp value is defined.\n * @property {number} [mode=\"query\"]\n * The method to use to attach cmcd metrics to the requests. 'query' to use query parameters, 'header' to use http headers.\n *\n * If not specified this value defaults to 'query'.\n * @property {Array.<string>} [enabledKeys]\n * This value is used to specify the desired CMCD parameters. Parameters not included in this list are not reported.\n * @property {Array.<string>} [includeInRequests]\n * Specifies which HTTP GET requests shall carry parameters.\n *\n * If not specified this value defaults to ['segment', 'mpd].\n * @property {number} [version=1]\n * The version of the CMCD to use.\n *\n * If not specified this value defaults to 1.\n */\n\n/**\n * @typedef {Object} module:Settings~CmsdSettings\n * @property {boolean} [enabled=false]\n * Enable or disable the CMSD response headers parsing.\n * @property {module:Settings~CmsdAbrSettings} [abr]\n * Sets additional ABR rules based on CMSD response headers.\n */\n\n/**\n * @typedef {Object} CmsdAbrSettings\n * @property {boolean} [applyMb=false]\n * Set to true if dash.js should apply CMSD maximum suggested bitrate in ABR logic.\n * @property {number} [etpWeightRatio=0]\n * Sets the weight ratio (between 0 and 1) that shall be applied on CMSD estimated throuhgput compared to measured throughput when calculating throughput.\n */\n\n/**\n * @typedef {Object} Metrics\n * @property {number} [metricsMaxListDepth=100]\n * Maximum number of metrics that are persisted per type.\n */\n\n/**\n * @typedef {Object} StreamingSettings\n * @property {number} [abandonLoadTimeout=10000]\n * A timeout value in seconds, which during the ABRController will block switch-up events.\n *\n * This will only take effect after an abandoned fragment event occurs.\n * @property {number} [wallclockTimeUpdateInterval=100]\n * How frequently the wallclockTimeUpdated internal event is triggered (in milliseconds).\n * @property {number} [manifestUpdateRetryInterval=100]\n * For live streams, set the interval-frequency in milliseconds at which dash.js will check if the current manifest is still processed before downloading the next manifest once the minimumUpdatePeriod time has.\n * @property {number} [liveUpdateTimeThresholdInMilliseconds=0]\n * For live streams, postpone syncing time updates until the threshold is passed. Increase if problems occurs during live streams on low end devices.\n * @property {boolean} [cacheInitSegments=false]\n * Enables the caching of init segments to avoid requesting the init segments before each representation switch.\n * @property {boolean} [applyServiceDescription=true]\n * Set to true if dash.js should use the parameters defined in ServiceDescription elements\n * @property {boolean} [applyProducerReferenceTime=true]\n * Set to true if dash.js should use the parameters defined in ProducerReferenceTime elements in combination with ServiceDescription elements.\n * @property {boolean} [applyContentSteering=true]\n * Set to true if dash.js should apply content steering during playback.\n * @property {boolean} [enableManifestDurationMismatchFix=true]\n * For multi-period streams, overwrite the manifest mediaPresentationDuration attribute with the sum of period durations if the manifest mediaPresentationDuration is greater than the sum of period durations\n * @property {boolean} [enableManifestTimescaleMismatchFix=false]\n * Overwrite the manifest segments base information timescale attributes with the timescale set in initialization segments\n * @property {boolean} [parseInbandPrft=false]\n * Set to true if dash.js should parse inband prft boxes (ProducerReferenceTime) and trigger events.\n * @property {module:Settings~Metrics} metrics Metric settings\n * @property {module:Settings~LiveDelay} delay Live Delay settings\n * @property {module:Settings~EventSettings} events Event settings\n * @property {module:Settings~TimeShiftBuffer} timeShiftBuffer TimeShiftBuffer settings\n * @property {module:Settings~Protection} protection DRM related settings\n * @property {module:Settings~Capabilities} capabilities Capability related settings\n * @property {module:Settings~Buffer}  buffer Buffer related settings\n * @property {module:Settings~Gaps}  gaps Gap related settings\n * @property {module:Settings~UtcSynchronizationSettings} utcSynchronization Settings related to UTC clock synchronization\n * @property {module:Settings~Scheduling} scheduling Settings related to segment scheduling\n * @property {module:Settings~Text} text Settings related to Subtitles and captions\n * @property {module:Settings~LiveCatchupSettings} liveCatchup  Settings related to live catchup.\n * @property {module:Settings~CachingInfoSettings} [lastBitrateCachingInfo={enabled: true, ttl: 360000}]\n * Set to false if you would like to disable the last known bit rate from being stored during playback and used to set the initial bit rate for subsequent playback within the expiration window.\n *\n * The default expiration is one hour, defined in milliseconds.\n *\n * If expired, the default initial bit rate (closest to 1000 kbps) will be used for that session and a new bit rate will be stored during that session.\n * @property {module:Settings~CachingInfoSettings} [lastMediaSettingsCachingInfo={enabled: true, ttl: 360000}]\n * Set to false if you would like to disable the last media settings from being stored to localStorage during playback and used to set the initial track for subsequent playback within the expiration window.\n *\n * The default expiration is one hour, defined in milliseconds.\n * @property {boolean} [saveLastMediaSettingsForCurrentStreamingSession=true]\n * Set to true if dash.js should save media settings from last selected track for incoming track selection during current streaming session.\n * @property {module:Settings~AudioVideoSettings} [cacheLoadThresholds={video: 10, audio: 5}]\n * For a given media type, the threshold which defines if the response to a fragment request is coming from browser cache or not.\n * @property {module:Settings~AudioVideoSettings} [trackSwitchMode={video: \"neverReplace\", audio: \"alwaysReplace\"}]\n * For a given media type defines if existing segments in the buffer should be overwritten once the track is switched. For instance if the user switches the audio language the existing segments in the audio buffer will be replaced when setting this value to \"alwaysReplace\".\n *\n * Possible values\n *\n * - Constants.TRACK_SWITCH_MODE_ALWAYS_REPLACE\n * Replace existing segments in the buffer\n *\n * - Constants.TRACK_SWITCH_MODE_NEVER_REPLACE\n * Do not replace existing segments in the buffer\n *\n * @property {} [ignoreSelectionPriority: false]\n * provides the option to disregard any signalled selectionPriority attribute. If disabled and if no initial media settings are set, track selection is accomplished as defined by selectionModeForInitialTrack.\n *\n * @property {} [prioritizeRoleMain: true]\n * provides the option to disable prioritization of AdaptationSets with their Role set to Main\n *\n * @property {} [assumeDefaultRoleAsMain: true]\n * when no Role descriptor is present, assume main per default\n * \n * @property {string} [selectionModeForInitialTrack=\"highestEfficiency\"]\n * Sets the selection mode for the initial track. This mode defines how the initial track will be selected if no initial media settings are set. If initial media settings are set this parameter will be ignored. Available options are:\n *\n * Possible values\n *\n * - Constants.TRACK_SELECTION_MODE_HIGHEST_BITRATE\n * This mode makes the player select the track with a highest bitrate.\n *\n * - Constants.TRACK_SELECTION_MODE_FIRST_TRACK\n * This mode makes the player select the first track found in the manifest.\n *\n * - Constants.TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY\n * This mode makes the player select the track with the lowest bitrate per pixel average.\n *\n * - Constants.TRACK_SELECTION_MODE_WIDEST_RANGE\n * This mode makes the player select the track with a widest range of bitrates.\n *\n *\n * @property {number} [fragmentRequestTimeout=20000]\n * Time in milliseconds before timing out on loading a media fragment.\n *\n * @property {number} [fragmentRequestProgressTimeout=-1]\n * Time in milliseconds before timing out on loading progress of a media fragment.\n *\n * @property {number} [manifestRequestTimeout=10000]\n * Time in milliseconds before timing out on loading a manifest.\n *\n * Fragments that timeout are retried as if they failed.\n * @property {module:Settings~RequestTypeSettings} [retryIntervals]\n * Time in milliseconds of which to reload a failed file load attempt.\n *\n * For low latency mode these values are divided by lowLatencyReductionFactor.\n * @property {module:Settings~RequestTypeSettings} [retryAttempts]\n * Total number of retry attempts that will occur on a file load before it fails.\n *\n * For low latency mode these values are multiplied by lowLatencyMultiplyFactor.\n * @property {module:Settings~AbrSettings} abr\n * Adaptive Bitrate algorithm related settings.\n * @property {module:Settings~CmcdSettings} cmcd\n * Settings related to Common Media Client Data reporting.\n * @property {module:Settings~CmsdSettings} cmsd\n * Settings related to Common Media Server Data parsing.\n * @property {module:Settings~defaultSchemeIdUri} defaultSchemeIdUri\n * Default schemeIdUri for descriptor type elements\n * These strings are used when not provided with setInitialMediaSettingsFor()\n */\n\n\n/**\n * @class\n * @ignore\n */\nfunction Settings() {\n    let instance;\n    const context = this.context;\n    const eventBus = EventBus(context).getInstance();\n    const DISPATCH_KEY_MAP = {\n        'streaming.delay.liveDelay': Events.SETTING_UPDATED_LIVE_DELAY,\n        'streaming.delay.liveDelayFragmentCount': Events.SETTING_UPDATED_LIVE_DELAY_FRAGMENT_COUNT,\n        'streaming.liveCatchup.enabled': Events.SETTING_UPDATED_CATCHUP_ENABLED,\n        'streaming.liveCatchup.playbackRate.min': Events.SETTING_UPDATED_PLAYBACK_RATE_MIN,\n        'streaming.liveCatchup.playbackRate.max': Events.SETTING_UPDATED_PLAYBACK_RATE_MAX,\n        'streaming.abr.rules.throughputRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.bolaRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.insufficientBufferRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.switchHistoryRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.droppedFramesRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.abandonRequestsRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.l2ARule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.rules.loLPRule.active': Events.SETTING_UPDATED_ABR_ACTIVE_RULES,\n        'streaming.abr.maxBitrate.video': Events.SETTING_UPDATED_MAX_BITRATE,\n        'streaming.abr.maxBitrate.audio': Events.SETTING_UPDATED_MAX_BITRATE,\n        'streaming.abr.minBitrate.video': Events.SETTING_UPDATED_MIN_BITRATE,\n        'streaming.abr.minBitrate.audio': Events.SETTING_UPDATED_MIN_BITRATE,\n    };\n\n    /**\n     * @const {PlayerSettings} defaultSettings\n     * @ignore\n     */\n    const defaultSettings = {\n        debug: {\n            logLevel: Debug.LOG_LEVEL_WARNING,\n            dispatchEvent: false\n        },\n        streaming: {\n            abandonLoadTimeout: 10000,\n            wallclockTimeUpdateInterval: 100,\n            manifestUpdateRetryInterval: 100,\n            liveUpdateTimeThresholdInMilliseconds: 0,\n            cacheInitSegments: false,\n            applyServiceDescription: true,\n            applyProducerReferenceTime: true,\n            applyContentSteering: true,\n            enableManifestDurationMismatchFix: true,\n            parseInbandPrft: false,\n            enableManifestTimescaleMismatchFix: false,\n            capabilities: {\n                filterUnsupportedEssentialProperties: true,\n                supportedEssentialProperties: [\n                    { schemeIdUri: Constants.FONT_DOWNLOAD_DVB_SCHEME },\n                    { schemeIdUri: Constants.COLOUR_PRIMARIES_SCHEME_ID_URI, value: /1|5|6|7/ },\n                    { schemeIdUri: Constants.URL_QUERY_INFO_SCHEME },\n                    { schemeIdUri: Constants.EXT_URL_QUERY_INFO_SCHEME },\n                    { schemeIdUri: Constants.MATRIX_COEFFICIENTS_SCHEME_ID_URI, value: /0|1|5|6/ },\n                    { schemeIdUri: Constants.TRANSFER_CHARACTERISTICS_SCHEME_ID_URI, value: /1|6|13|14|15/ },\n                    ...Constants.THUMBNAILS_SCHEME_ID_URIS.map(ep => {\n                        return { 'schemeIdUri': ep };\n                    })\n                ],\n                useMediaCapabilitiesApi: true,\n                filterVideoColorimetryEssentialProperties: false,\n                filterHDRMetadataFormatEssentialProperties: false\n            },\n            events: {\n                eventControllerRefreshDelay: 100,\n                deleteEventMessageDataTimeout: 10000\n            },\n            timeShiftBuffer: {\n                calcFromSegmentTimeline: false,\n                fallbackToSegmentTimeline: true\n            },\n            metrics: {\n                maxListDepth: 100\n            },\n            delay: {\n                liveDelayFragmentCount: NaN,\n                liveDelay: NaN,\n                useSuggestedPresentationDelay: true\n            },\n            protection: {\n                keepProtectionMediaKeys: false,\n                ignoreEmeEncryptedEvent: false,\n                detectPlayreadyMessageFormat: true,\n                ignoreKeyStatuses: false\n            },\n            buffer: {\n                enableSeekDecorrelationFix: false,\n                fastSwitchEnabled: null,\n                flushBufferAtTrackSwitch: false,\n                reuseExistingSourceBuffers: true,\n                bufferPruningInterval: 10,\n                bufferToKeep: 20,\n                bufferTimeAtTopQuality: 30,\n                bufferTimeAtTopQualityLongForm: 60,\n                initialBufferLevel: NaN,\n                bufferTimeDefault: 18,\n                longFormContentDurationThreshold: 600,\n                stallThreshold: 0.3,\n                lowLatencyStallThreshold: 0.3,\n                useAppendWindow: true,\n                setStallState: true,\n                avoidCurrentTimeRangePruning: false,\n                useChangeType: true,\n                mediaSourceDurationInfinity: true,\n                resetSourceBuffersForTrackSwitch: false,\n                syntheticStallEvents: {\n                    enabled: false,\n                    ignoreReadyState: false\n                }\n            },\n            gaps: {\n                jumpGaps: true,\n                jumpLargeGaps: true,\n                smallGapLimit: 1.5,\n                threshold: 0.3,\n                enableSeekFix: true,\n                enableStallFix: false,\n                stallSeek: 0.1\n            },\n            utcSynchronization: {\n                enabled: true,\n                useManifestDateHeaderTimeSource: true,\n                backgroundAttempts: 2,\n                timeBetweenSyncAttempts: 30,\n                maximumTimeBetweenSyncAttempts: 600,\n                minimumTimeBetweenSyncAttempts: 2,\n                timeBetweenSyncAttemptsAdjustmentFactor: 2,\n                maximumAllowedDrift: 100,\n                enableBackgroundSyncAfterSegmentDownloadError: true,\n                defaultTimingSource: {\n                    scheme: 'urn:mpeg:dash:utc:http-xsdate:2014',\n                    value: 'https://time.akamai.com/?iso&ms'\n                }\n            },\n            scheduling: {\n                defaultTimeout: 500,\n                lowLatencyTimeout: 0,\n                scheduleWhilePaused: true\n            },\n            text: {\n                defaultEnabled: true,\n                dispatchForManualRendering: false,\n                extendSegmentedCues: true,\n                imsc: {\n                    displayForcedOnlyMode: false,\n                    enableRollUp: true\n                },\n                webvtt: {\n                    customRenderingEnabled: false\n                }\n            },\n            liveCatchup: {\n                maxDrift: NaN,\n                playbackRate: {\n                    min: NaN,\n                    max: NaN\n                },\n                playbackBufferMin: 0.5,\n                enabled: null,\n                mode: Constants.LIVE_CATCHUP_MODE_DEFAULT\n            },\n            lastBitrateCachingInfo: {\n                enabled: true,\n                ttl: 360000\n            },\n            lastMediaSettingsCachingInfo: {\n                enabled: true,\n                ttl: 360000\n            },\n            saveLastMediaSettingsForCurrentStreamingSession: true,\n            cacheLoadThresholds: {\n                video: 10,\n                audio: 5\n            },\n            trackSwitchMode: {\n                audio: Constants.TRACK_SWITCH_MODE_ALWAYS_REPLACE,\n                video: Constants.TRACK_SWITCH_MODE_NEVER_REPLACE\n            },\n            ignoreSelectionPriority: false,\n            prioritizeRoleMain: true,\n            assumeDefaultRoleAsMain: true,\n            selectionModeForInitialTrack: Constants.TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY,\n            fragmentRequestTimeout: 20000,\n            fragmentRequestProgressTimeout: -1,\n            manifestRequestTimeout: 10000,\n            retryIntervals: {\n                [HTTPRequest.MPD_TYPE]: 500,\n                [HTTPRequest.XLINK_EXPANSION_TYPE]: 500,\n                [HTTPRequest.MEDIA_SEGMENT_TYPE]: 1000,\n                [HTTPRequest.INIT_SEGMENT_TYPE]: 1000,\n                [HTTPRequest.BITSTREAM_SWITCHING_SEGMENT_TYPE]: 1000,\n                [HTTPRequest.INDEX_SEGMENT_TYPE]: 1000,\n                [HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE]: 1000,\n                [HTTPRequest.LICENSE]: 1000,\n                [HTTPRequest.OTHER_TYPE]: 1000,\n                lowLatencyReductionFactor: 10\n            },\n            retryAttempts: {\n                [HTTPRequest.MPD_TYPE]: 3,\n                [HTTPRequest.XLINK_EXPANSION_TYPE]: 1,\n                [HTTPRequest.MEDIA_SEGMENT_TYPE]: 3,\n                [HTTPRequest.INIT_SEGMENT_TYPE]: 3,\n                [HTTPRequest.BITSTREAM_SWITCHING_SEGMENT_TYPE]: 3,\n                [HTTPRequest.INDEX_SEGMENT_TYPE]: 3,\n                [HTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE]: 3,\n                [HTTPRequest.LICENSE]: 3,\n                [HTTPRequest.OTHER_TYPE]: 3,\n                lowLatencyMultiplyFactor: 5\n            },\n            abr: {\n                limitBitrateByPortal: false,\n                usePixelRatioInLimitBitrateByPortal: false,\n                enableSupplementalPropertyAdaptationSetSwitching: true,\n                rules: {\n                    throughputRule: {\n                        active: true\n                    },\n                    bolaRule: {\n                        active: true\n                    },\n                    insufficientBufferRule: {\n                        active: true,\n                        parameters: {\n                            throughputSafetyFactor: 0.7,\n                            segmentIgnoreCount: 2\n                        }\n                    },\n                    switchHistoryRule: {\n                        active: true,\n                        parameters: {\n                            sampleSize: 8,\n                            switchPercentageThreshold: 0.075\n                        }\n                    },\n                    droppedFramesRule: {\n                        active: false,\n                        parameters: {\n                            minimumSampleSize: 375,\n                            droppedFramesPercentageThreshold: 0.15\n                        }\n                    },\n                    abandonRequestsRule: {\n                        active: true,\n                        parameters: {\n                            abandonDurationMultiplier: 1.8,\n                            minSegmentDownloadTimeThresholdInMs: 500,\n                            minThroughputSamplesThreshold: 6\n                        }\n                    },\n                    l2ARule: {\n                        active: false\n                    },\n                    loLPRule: {\n                        active: false\n                    }\n                },\n                throughput: {\n                    averageCalculationMode: Constants.THROUGHPUT_CALCULATION_MODES.EWMA,\n                    lowLatencyDownloadTimeCalculationMode: Constants.LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE.MOOF_PARSING,\n                    useResourceTimingApi: true,\n                    useNetworkInformationApi: {\n                        xhr: false,\n                        fetch: false\n                    },\n                    useDeadTimeLatency: true,\n                    bandwidthSafetyFactor: 0.9,\n                    sampleSettings: {\n                        live: 3,\n                        vod: 4,\n                        enableSampleSizeAdjustment: true,\n                        decreaseScale: 0.7,\n                        increaseScale: 1.3,\n                        maxMeasurementsToKeep: 20,\n                        averageLatencySampleAmount: 4,\n                    },\n                    ewma: {\n                        throughputSlowHalfLifeSeconds: 8,\n                        throughputFastHalfLifeSeconds: 3,\n                        latencySlowHalfLifeCount: 2,\n                        latencyFastHalfLifeCount: 1,\n                        weightDownloadTimeMultiplicationFactor: 0.0015\n                    }\n                },\n                maxBitrate: {\n                    audio: -1,\n                    video: -1\n                },\n                minBitrate: {\n                    audio: -1,\n                    video: -1\n                },\n                initialBitrate: {\n                    audio: -1,\n                    video: -1\n                },\n                autoSwitchBitrate: {\n                    audio: true,\n                    video: true\n                }\n            },\n            cmcd: {\n                applyParametersFromMpd: true,\n                enabled: false,\n                sid: null,\n                cid: null,\n                rtp: null,\n                rtpSafetyFactor: 5,\n                mode: Constants.CMCD_MODE_QUERY,\n                enabledKeys: Constants.CMCD_AVAILABLE_KEYS,\n                includeInRequests: ['segment', 'mpd'],\n                version: 1\n            },\n            cmsd: {\n                enabled: false,\n                abr: {\n                    applyMb: false,\n                    etpWeightRatio: 0\n                }\n            },\n            defaultSchemeIdUri: {\n                viewpoint: '',\n                audioChannelConfiguration: 'urn:mpeg:mpegB:cicp:ChannelConfiguration',\n                role: 'urn:mpeg:dash:role:2011',\n                accessibility: 'urn:mpeg:dash:role:2011'\n            }\n        },\n        errors: {\n            recoverAttempts: {\n                mediaErrorDecode: 5\n            }\n        }\n    };\n\n    let settings = Utils.clone(defaultSettings);\n\n    //Merge in the settings. If something exists in the new config that doesn't match the schema of the default config,\n    //regard it as an error and log it.\n    function mixinSettings(source, dest, path) {\n        for (let n in source) {\n            if (source.hasOwnProperty(n)) {\n                if (dest.hasOwnProperty(n)) {\n                    if (typeof source[n] === 'object' && !(source[n] instanceof RegExp) && !(source[n] instanceof Array) && source[n] !== null) {\n                        mixinSettings(source[n], dest[n], path.slice() + n + '.');\n                    } else {\n                        dest[n] = Utils.clone(source[n]);\n                        if (DISPATCH_KEY_MAP[path + n]) {\n                            eventBus.trigger(DISPATCH_KEY_MAP[path + n]);\n                        }\n                    }\n                } else {\n                    console.error('Settings parameter ' + path + n + ' is not supported');\n                }\n            }\n        }\n    }\n\n    /**\n     * Return the settings object. Don't copy/store this object, you won't get updates.\n     * @func\n     * @instance\n     */\n    function get() {\n        return settings;\n    }\n\n    /**\n     * @func\n     * @instance\n     * @param {object} settingsObj - This should be a partial object of the Settings.Schema type. That is, fields defined should match the path (e.g.\n     * settingsObj.streaming.abr.autoSwitchBitrate.audio -> defaultSettings.streaming.abr.autoSwitchBitrate.audio). Where an element's path does\n     * not match it is ignored, and a warning is logged.\n     *\n     * Use to change the settings object. Any new values defined will overwrite the settings and anything undefined will not change.\n     * Implementers of new settings should add it in an approriate namespace to the defaultSettings object and give it a default value (that is not undefined).\n     *\n     */\n    function update(settingsObj) {\n        if (typeof settingsObj === 'object') {\n            mixinSettings(settingsObj, settings, '');\n        }\n    }\n\n    /**\n     * Resets the settings object. Everything is set to its default value.\n     * @func\n     * @instance\n     *\n     */\n    function reset() {\n        settings = Utils.clone(defaultSettings);\n    }\n\n    instance = {\n        get,\n        update,\n        reset\n    };\n\n    return instance;\n}\n\n\nSettings.__dashjs_factory_name = 'Settings';\nlet factory = FactoryMaker.getSingletonFactory(Settings);\nexport default factory;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * @class\n * @ignore\n */\n\nimport path from 'path-browserify'\nimport {UAParser} from 'ua-parser-js'\nimport Constants from '../streaming/constants/Constants.js';\n\nclass Utils {\n    static mixin(dest, source, copy) {\n        let s;\n        let empty = {};\n        if (dest) {\n            for (let name in source) {\n                if (source.hasOwnProperty(name)) {\n                    s = source[name];\n                    if (!(name in dest) || (dest[name] !== s && (!(name in empty) || empty[name] !== s))) {\n                        if (typeof dest[name] === 'object' && dest[name] !== null) {\n                            dest[name] = Utils.mixin(dest[name], s, copy);\n                        } else {\n                            dest[name] = copy(s);\n                        }\n                    }\n                }\n            }\n        }\n        return dest;\n    }\n\n    static clone(src) {\n        if (!src || typeof src !== 'object') {\n            return src; // anything\n        }\n        if (src instanceof RegExp) {\n            return new RegExp(src);\n        }\n        let r;\n        if (src instanceof Array) {\n            // array\n            r = [];\n            for (let i = 0, l = src.length; i < l; ++i) {\n                if (i in src) {\n                    r.push(Utils.clone(src[i]));\n                }\n            }\n        } else {\n            r = {};\n        }\n        return Utils.mixin(r, src, Utils.clone);\n    }\n\n    static addAdditionalQueryParameterToUrl(url, params) {\n        try {\n            if (!params || params.length === 0) {\n                return url;\n            }\n\n            let updatedUrl = url;\n            params.forEach(({ key, value }) => {\n                const separator = updatedUrl.includes('?') ? '&' : '?';\n                updatedUrl += `${separator}${(encodeURIComponent(key))}=${(encodeURIComponent(value))}`;\n            });\n            return updatedUrl;\n        } catch (e) {\n            return url;\n        }\n    }\n\n    static removeQueryParameterFromUrl(url, queryParameter) {\n        if (!url || !queryParameter) {\n            return url;\n        }\n        // Parse the URL\n        const parsedUrl = new URL(url);\n\n        // Get the search parameters\n        const params = new URLSearchParams(parsedUrl.search);\n\n        if (!params || params.size === 0 || !params.has(queryParameter)) {\n            return url;\n        }\n\n        // Remove the queryParameter\n        params.delete(queryParameter);\n\n        // Manually reconstruct the query string without re-encoding\n        const queryString = Array.from(params.entries())\n            .map(([key, value]) => `${key}=${value}`)\n            .join('&');\n\n        // Reconstruct the URL\n        const baseUrl = `${parsedUrl.origin}${parsedUrl.pathname}`;\n        return queryString ? `${baseUrl}?${queryString}` : baseUrl;\n    }\n\n    static parseHttpHeaders(headerStr) {\n        let headers = {};\n        if (!headerStr) {\n            return headers;\n        }\n\n        // Trim headerStr to fix a MS Edge bug with xhr.getAllResponseHeaders method\n        // which send a string starting with a \"\\n\" character\n        let headerPairs = headerStr.trim().split('\\u000d\\u000a');\n        for (let i = 0, ilen = headerPairs.length; i < ilen; i++) {\n            let headerPair = headerPairs[i];\n            let index = headerPair.indexOf('\\u003a\\u0020');\n            if (index > 0) {\n                headers[headerPair.substring(0, index)] = headerPair.substring(index + 2);\n            }\n        }\n        return headers;\n    }\n\n    /**\n     * Parses query parameters from a string and returns them as an array of key-value pairs.\n     * @param {string} queryParamString - A string containing the query parameters.\n     * @return {Array<{key: string, value: string}>} An array of objects representing the query parameters.\n     */\n    static parseQueryParams(queryParamString) {\n        const params = [];\n        const searchParams = new URLSearchParams(queryParamString);\n        for (const [key, value] of searchParams.entries()) {\n            params.push({ key: decodeURIComponent(key), value: decodeURIComponent(value) });\n        }\n        return params;\n    }\n\n    static generateUuid() {\n        let dt = new Date().getTime();\n        const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n            const r = (dt + Math.random() * 16) % 16 | 0;\n            dt = Math.floor(dt / 16);\n            return (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16);\n        });\n        return uuid;\n    }\n\n    static generateHashCode(string) {\n        let hash = 0;\n\n        if (string.length === 0) {\n            return hash;\n        }\n\n        for (let i = 0; i < string.length; i++) {\n            const chr = string.charCodeAt(i);\n            hash = ((hash << 5) - hash) + chr;\n            hash |= 0;\n        }\n        return hash;\n    }\n\n    /**\n     * Compares both urls and returns a relative url (target relative to original)\n     * @param {string} originalUrl\n     * @param {string} targetUrl\n     * @return {string|*}\n     */\n    static getRelativeUrl(originalUrl, targetUrl) {\n        try {\n            const original = new URL(originalUrl);\n            const target = new URL(targetUrl);\n\n            // Unify the protocol to compare the origins\n            original.protocol = target.protocol;\n            if (original.origin !== target.origin) {\n                return targetUrl;\n            }\n\n            // Use the relative path implementation of the path library. We need to cut off the actual filename in the end to get the relative path\n            let relativePath = path.relative(original.pathname.substr(0, original.pathname.lastIndexOf('/')), target.pathname.substr(0, target.pathname.lastIndexOf('/')));\n\n            // In case the relative path is empty (both path are equal) return the filename only. Otherwise add a slash in front of the filename\n            const startIndexOffset = relativePath.length === 0 ? 1 : 0;\n            relativePath += target.pathname.substr(target.pathname.lastIndexOf('/') + startIndexOffset, target.pathname.length - 1);\n\n            // Build the other candidate, e.g. the 'host relative' path that starts with \"/\", and return the shortest of the two candidates.\n            if (target.pathname.length < relativePath.length) {\n                return target.pathname;\n            }\n            return relativePath;\n        } catch (e) {\n            return targetUrl\n        }\n    }\n\n    static getHostFromUrl(urlString) {\n        try {\n            const url = new URL(urlString);\n\n            return url.host\n        } catch (e) {\n            return null\n        }\n    }\n\n    static parseUserAgent(ua = null) {\n        try {\n            const uaString = ua === null ? typeof navigator !== 'undefined' ? navigator.userAgent.toLowerCase() : '' : '';\n\n            return UAParser(uaString);\n        } catch (e) {\n            return {};\n        }\n    }\n\n    /**\n     * Checks for existence of \"http\" or \"https\" in a string\n     * @param string\n     * @returns {boolean}\n     */\n    static stringHasProtocol(string) {\n        return (/(http(s?)):\\/\\//i.test(string))\n    }\n\n    static bufferSourceToDataView(bufferSource) {\n        return Utils.toDataView(bufferSource, DataView);\n    }\n\n    static bufferSourceToInt8(bufferSource) {\n        return Utils.toDataView(bufferSource, Uint8Array)\n    }\n\n    static uint8ArrayToString(uint8Array) {\n        const decoder = new TextDecoder('utf-8');\n        return decoder.decode(uint8Array);\n    }\n\n    static bufferSourceToHex(data) {\n        const arr = Utils.bufferSourceToInt8(data)\n        let hex = '';\n        for (let value of arr) {\n            value = value.toString(16);\n            if (value.length === 1) {\n                value = '0' + value;\n            }\n            hex += value;\n        }\n        return hex;\n    }\n\n    static toDataView(bufferSource, Type) {\n        const buffer = Utils.getArrayBuffer(bufferSource);\n        let bytesPerElement = 1;\n        if ('BYTES_PER_ELEMENT' in DataView) {\n            bytesPerElement = DataView.BYTES_PER_ELEMENT;\n        }\n\n        const dataEnd = ((bufferSource.byteOffset || 0) + bufferSource.byteLength) /\n            bytesPerElement;\n        const rawStart = ((bufferSource.byteOffset || 0)) / bytesPerElement;\n        const start = Math.floor(Math.max(0, Math.min(rawStart, dataEnd)));\n        const end = Math.floor(Math.min(start + Math.max(Infinity, 0), dataEnd));\n        return new Type(buffer, start, end - start);\n    }\n\n    static getArrayBuffer(view) {\n        if (view instanceof ArrayBuffer) {\n            return view;\n        } else {\n            return view.buffer;\n        }\n    }\n\n    static getCodecFamily(codecString) {\n        const { base, profile } = Utils._getCodecParts(codecString)\n\n        switch (base) {\n            case 'mp4a':\n                switch (profile) {\n                    case '69':\n                    case '6b':\n                    case '40.34':\n                        return Constants.CODEC_FAMILIES.MP3\n                    case '66':\n                    case '67':\n                    case '68':\n                    case '40.2':\n                    case '40.02':\n                    case '40.5':\n                    case '40.05':\n                    case '40.29':\n                    case '40.42':\n                        return Constants.CODEC_FAMILIES.AAC\n                    case 'a5':\n                        return Constants.CODEC_FAMILIES.AC3\n                    case 'e6':\n                        return Constants.CODEC_FAMILIES.EC3\n                    case 'b2':\n                        return Constants.CODEC_FAMILIES.DTSX\n                    case 'a9':\n                        return Constants.CODEC_FAMILIES.DTSC\n                }\n                break;\n            case 'avc1':\n            case 'avc3':\n                return Constants.CODEC_FAMILIES.AVC\n            case 'hvc1':\n            case 'hvc3':\n                return Constants.CODEC_FAMILIES.HEVC\n            default:\n                return base\n        }\n\n        return base;\n    }\n\n    static _getCodecParts(codecString) {\n        const [base, ...rest] = codecString.split('.');\n        const profile = rest.join('.');\n        return { base, profile };\n    }\n\n}\n\nexport default Utils;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport EventsBase from './EventsBase.js';\n\n/**\n * These are internal events that should not be needed at the player level.\n * If you find and event in here that you would like access to from MediaPlayer level\n * please add an issue at https://github.com/Dash-Industry-Forum/dash.js/issues/new\n * @class\n * @ignore\n */\nclass CoreEvents extends EventsBase {\n    constructor () {\n        super();\n        this.ATTEMPT_BACKGROUND_SYNC = 'attemptBackgroundSync';\n        this.BUFFERING_COMPLETED = 'bufferingCompleted';\n        this.BUFFER_CLEARED = 'bufferCleared';\n        this.BYTES_APPENDED_END_FRAGMENT = 'bytesAppendedEndFragment';\n        this.BUFFER_REPLACEMENT_STARTED = 'bufferReplacementStarted';\n        this.CHECK_FOR_EXISTENCE_COMPLETED = 'checkForExistenceCompleted';\n        this.CMSD_STATIC_HEADER = 'cmsdStaticHeader';\n        this.CURRENT_TRACK_CHANGED = 'currentTrackChanged';\n        this.DATA_UPDATE_COMPLETED = 'dataUpdateCompleted';\n        this.INBAND_EVENTS = 'inbandEvents';\n        this.INITIAL_STREAM_SWITCH = 'initialStreamSwitch';\n        this.INIT_FRAGMENT_LOADED = 'initFragmentLoaded';\n        this.INIT_FRAGMENT_NEEDED = 'initFragmentNeeded';\n        this.INTERNAL_MANIFEST_LOADED = 'internalManifestLoaded';\n        this.ORIGINAL_MANIFEST_LOADED = 'originalManifestLoaded';\n        this.LOADING_COMPLETED = 'loadingCompleted';\n        this.LOADING_PROGRESS = 'loadingProgress';\n        this.LOADING_DATA_PROGRESS = 'loadingDataProgress';\n        this.LOADING_ABANDONED = 'loadingAborted';\n        this.MANIFEST_UPDATED = 'manifestUpdated';\n        this.MEDIA_FRAGMENT_LOADED = 'mediaFragmentLoaded';\n        this.MEDIA_FRAGMENT_NEEDED = 'mediaFragmentNeeded';\n        this.MEDIAINFO_UPDATED = 'mediaInfoUpdated';\n        this.QUOTA_EXCEEDED = 'quotaExceeded';\n        this.SEGMENT_LOCATION_BLACKLIST_ADD = 'segmentLocationBlacklistAdd';\n        this.SEGMENT_LOCATION_BLACKLIST_CHANGED = 'segmentLocationBlacklistChanged';\n        this.SERVICE_LOCATION_BASE_URL_BLACKLIST_ADD = 'serviceLocationBlacklistAdd';\n        this.SERVICE_LOCATION_BASE_URL_BLACKLIST_CHANGED = 'serviceLocationBlacklistChanged';\n        this.SERVICE_LOCATION_LOCATION_BLACKLIST_ADD = 'serviceLocationLocationBlacklistAdd';\n        this.SERVICE_LOCATION_LOCATION_BLACKLIST_CHANGED = 'serviceLocationLocationBlacklistChanged';\n        this.SET_FRAGMENTED_TEXT_AFTER_DISABLED = 'setFragmentedTextAfterDisabled';\n        this.SET_NON_FRAGMENTED_TEXT = 'setNonFragmentedText';\n        this.SOURCE_BUFFER_ERROR = 'sourceBufferError';\n        this.STREAMS_COMPOSED = 'streamsComposed';\n        this.STREAM_BUFFERING_COMPLETED = 'streamBufferingCompleted';\n        this.STREAM_REQUESTING_COMPLETED = 'streamRequestingCompleted';\n        this.TEXT_TRACKS_QUEUE_INITIALIZED = 'textTracksQueueInitialized';\n        this.TIME_SYNCHRONIZATION_COMPLETED = 'timeSynchronizationComplete';\n        this.UPDATE_TIME_SYNC_OFFSET = 'updateTimeSyncOffset';\n        this.URL_RESOLUTION_FAILED = 'urlResolutionFailed';\n        this.VIDEO_CHUNK_RECEIVED = 'videoChunkReceived';\n        this.WALLCLOCK_TIME_UPDATED = 'wallclockTimeUpdated';\n        this.XLINK_ELEMENT_LOADED = 'xlinkElementLoaded';\n        this.XLINK_READY = 'xlinkReady';\n        this.SEEK_TARGET = 'seekTarget';\n        this.SETTING_UPDATED_LIVE_DELAY = 'settingUpdatedLiveDelay';\n        this.SETTING_UPDATED_LIVE_DELAY_FRAGMENT_COUNT = 'settingUpdatedLiveDelayFragmentCount';\n        this.SETTING_UPDATED_CATCHUP_ENABLED = 'settingUpdatedCatchupEnabled';\n        this.SETTING_UPDATED_PLAYBACK_RATE_MIN = 'settingUpdatedPlaybackRateMin';\n        this.SETTING_UPDATED_PLAYBACK_RATE_MAX = 'settingUpdatedPlaybackRateMax';\n        this.SETTING_UPDATED_ABR_ACTIVE_RULES = 'settingUpdatedAbrActiveRules';\n        this.SETTING_UPDATED_MAX_BITRATE = 'settingUpdatedMaxBitrate';\n        this.SETTING_UPDATED_MIN_BITRATE = 'settingUpdatedMinBitrate';\n    }\n}\n\nexport default CoreEvents;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nimport CoreEvents from './CoreEvents.js';\n\nclass Events extends CoreEvents {\n}\n\nlet events = new Events();\nexport default events;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass EventsBase {\n    extend(events, config) {\n        if (!events) {\n            return;\n        }\n\n        let override = config ? config.override : false;\n        let publicOnly = config ? config.publicOnly : false;\n\n\n        for (const evt in events) {\n            if (!events.hasOwnProperty(evt) || (this[evt] && !override)) {\n                continue;\n            }\n            if (publicOnly && events[evt].indexOf('public_') === -1) {\n                continue;\n            }\n            this[evt] = events[evt];\n\n        }\n    }\n}\n\nexport default EventsBase;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass UTCTiming {\n    constructor() {\n        // UTCTiming is a DescriptorType and doesn't have any additional fields\n        this.schemeIdUri = '';\n        this.value = '';\n    }\n}\n\nexport default UTCTiming;", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport EventsBase from '../core/events/EventsBase.js';\n\n/**\n * @class\n * @implements EventsBase\n */\nclass MediaPlayerEvents extends EventsBase {\n\n    /**\n     * @description Public facing external events to be used when developing a player that implements dash.js.\n     */\n    constructor() {\n        super();\n        /**\n         * Triggered when playback will not start yet\n         * as the MPD's availabilityStartTime is in the future.\n         * Check delay property in payload to determine time before playback will start.\n         * @event MediaPlayerEvents#AST_IN_FUTURE\n         */\n        this.AST_IN_FUTURE = 'astInFuture';\n\n        /**\n         * Triggered when the BaseURLs have been updated.\n         * @event MediaPlayerEvents#BASE_URLS_UPDATED\n         */\n        this.BASE_URLS_UPDATED = 'baseUrlsUpdated';\n\n        /**\n         * Triggered when the video element's buffer state changes to stalled.\n         * Check mediaType in payload to determine type (Video, Audio, FragmentedText).\n         * @event MediaPlayerEvents#BUFFER_EMPTY\n         */\n        this.BUFFER_EMPTY = 'bufferStalled';\n\n        /**\n         * Triggered when the video element's buffer state changes to loaded.\n         * Check mediaType in payload to determine type (Video, Audio, FragmentedText).\n         * @event MediaPlayerEvents#BUFFER_LOADED\n         */\n        this.BUFFER_LOADED = 'bufferLoaded';\n\n        /**\n         * Triggered when the video element's buffer state changes, either stalled or loaded. Check payload for state.\n         * @event MediaPlayerEvents#BUFFER_LEVEL_STATE_CHANGED\n         */\n        this.BUFFER_LEVEL_STATE_CHANGED = 'bufferStateChanged';\n\n        /**\n         * Triggered when the buffer level of a media type has been updated\n         * @event MediaPlayerEvents#BUFFER_LEVEL_UPDATED\n         */\n        this.BUFFER_LEVEL_UPDATED = 'bufferLevelUpdated';\n\n        /**\n         * Triggered when a font signalled by a DVB Font Download has been added to the document FontFaceSet interface.\n         * @event MediaPlayerEvents#DVB_FONT_DOWNLOAD_ADDED\n         */\n        this.DVB_FONT_DOWNLOAD_ADDED = 'dvbFontDownloadAdded';\n\n        /**\n         * Triggered when a font signalled by a DVB Font Download has successfully downloaded and the FontFace can be used.\n         * @event MediaPlayerEvents#DVB_FONT_DOWNLOAD_COMPLETE\n         */\n        this.DVB_FONT_DOWNLOAD_COMPLETE = 'dvbFontDownloadComplete';\n\n        /**\n         * Triggered when a font signalled by a DVB Font Download could not be successfully downloaded, so the FontFace will not be used.\n         * @event MediaPlayerEvents#DVB_FONT_DOWNLOAD_FAILED\n         */\n        this.DVB_FONT_DOWNLOAD_FAILED = 'dvbFontDownloadFailed';\n\n        /**\n         * Triggered when a dynamic stream changed to static (transition phase between Live and On-Demand).\n         * @event MediaPlayerEvents#DYNAMIC_TO_STATIC\n         */\n        this.DYNAMIC_TO_STATIC = 'dynamicToStatic';\n\n        /**\n         * Triggered when there is an error from the element or MSE source buffer.\n         * @event MediaPlayerEvents#ERROR\n         */\n        this.ERROR = 'error';\n        /**\n         * Triggered when a fragment download has completed.\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_COMPLETED\n         */\n        this.FRAGMENT_LOADING_COMPLETED = 'fragmentLoadingCompleted';\n\n        /**\n         * Triggered when a partial fragment download has completed.\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_PROGRESS\n         */\n        this.FRAGMENT_LOADING_PROGRESS = 'fragmentLoadingProgress';\n        /**\n         * Triggered when a fragment download has started.\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_STARTED\n         */\n        this.FRAGMENT_LOADING_STARTED = 'fragmentLoadingStarted';\n\n        /**\n         * Triggered when a fragment download is abandoned due to detection of slow download base on the ABR abandon rule..\n         * @event MediaPlayerEvents#FRAGMENT_LOADING_ABANDONED\n         */\n        this.FRAGMENT_LOADING_ABANDONED = 'fragmentLoadingAbandoned';\n\n        /**\n         * Triggered when {@link module:Debug} logger methods are called.\n         * @event MediaPlayerEvents#LOG\n         */\n        this.LOG = 'log';\n\n        /**\n         * Triggered when the manifest load is started\n         * @event MediaPlayerEvents#MANIFEST_LOADING_STARTED\n         */\n        this.MANIFEST_LOADING_STARTED = 'manifestLoadingStarted';\n\n        /**\n         * Triggered when the manifest loading is finished, providing the request object information\n         * @event MediaPlayerEvents#MANIFEST_LOADING_FINISHED\n         */\n        this.MANIFEST_LOADING_FINISHED = 'manifestLoadingFinished';\n\n        /**\n         * Triggered when the manifest load is complete, providing the payload\n         * @event MediaPlayerEvents#MANIFEST_LOADED\n         */\n        this.MANIFEST_LOADED = 'manifestLoaded';\n\n        /**\n         * Triggered anytime there is a change to the overall metrics.\n         * @event MediaPlayerEvents#METRICS_CHANGED\n         */\n        this.METRICS_CHANGED = 'metricsChanged';\n\n        /**\n         * Triggered when an individual metric is added, updated or cleared.\n         * @event MediaPlayerEvents#METRIC_CHANGED\n         */\n        this.METRIC_CHANGED = 'metricChanged';\n\n        /**\n         * Triggered every time a new metric is added.\n         * @event MediaPlayerEvents#METRIC_ADDED\n         */\n        this.METRIC_ADDED = 'metricAdded';\n\n        /**\n         * Triggered every time a metric is updated.\n         * @event MediaPlayerEvents#METRIC_UPDATED\n         */\n        this.METRIC_UPDATED = 'metricUpdated';\n\n        /**\n         * Triggered when a new stream (period) starts.\n         * @event MediaPlayerEvents#PERIOD_SWITCH_STARTED\n         */\n        this.PERIOD_SWITCH_STARTED = 'periodSwitchStarted';\n\n        /**\n         * Triggered at the stream end of a period.\n         * @event MediaPlayerEvents#PERIOD_SWITCH_COMPLETED\n         */\n        this.PERIOD_SWITCH_COMPLETED = 'periodSwitchCompleted';\n\n        /**\n         * Triggered when an ABR up /down switch is initiated; either by user in manual mode or auto mode via ABR rules.\n         * @event MediaPlayerEvents#QUALITY_CHANGE_REQUESTED\n         */\n        this.QUALITY_CHANGE_REQUESTED = 'qualityChangeRequested';\n\n        /**\n         * Triggered when the new ABR quality is being rendered on-screen.\n         * @event MediaPlayerEvents#QUALITY_CHANGE_RENDERED\n         */\n        this.QUALITY_CHANGE_RENDERED = 'qualityChangeRendered';\n\n        /**\n         * Triggered when the new track is being selected\n         * @event MediaPlayerEvents#NEW_TRACK_SELECTED\n         */\n        this.NEW_TRACK_SELECTED = 'newTrackSelected';\n\n        /**\n         * Triggered when the new track is being rendered.\n         * @event MediaPlayerEvents#TRACK_CHANGE_RENDERED\n         */\n        this.TRACK_CHANGE_RENDERED = 'trackChangeRendered';\n\n        /**\n         * Triggered when a stream (period) is being loaded\n         * @event MediaPlayerEvents#STREAM_INITIALIZING\n         */\n        this.STREAM_INITIALIZING = 'streamInitializing';\n\n        /**\n         * Triggered when a stream (period) is loaded\n         * @event MediaPlayerEvents#STREAM_UPDATED\n         */\n        this.STREAM_UPDATED = 'streamUpdated';\n\n        /**\n         * Triggered when a stream (period) is activated\n         * @event MediaPlayerEvents#STREAM_ACTIVATED\n         */\n        this.STREAM_ACTIVATED = 'streamActivated';\n\n        /**\n         * Triggered when a stream (period) is deactivated\n         * @event MediaPlayerEvents#STREAM_DEACTIVATED\n         */\n        this.STREAM_DEACTIVATED = 'streamDeactivated';\n\n        /**\n         * Triggered when a stream (period) is activated\n         * @event MediaPlayerEvents#STREAM_INITIALIZED\n         */\n        this.STREAM_INITIALIZED = 'streamInitialized';\n\n        /**\n         * Triggered when the player has been reset.\n         * @event MediaPlayerEvents#STREAM_TEARDOWN_COMPLETE\n         */\n        this.STREAM_TEARDOWN_COMPLETE = 'streamTeardownComplete';\n\n        /**\n         * Triggered once all text tracks detected in the MPD are added to the video element.\n         * @event MediaPlayerEvents#TEXT_TRACKS_ADDED\n         */\n        this.TEXT_TRACKS_ADDED = 'allTextTracksAdded';\n\n        /**\n         * Triggered when a text track is added to the video element's TextTrackList\n         * @event MediaPlayerEvents#TEXT_TRACK_ADDED\n         */\n        this.TEXT_TRACK_ADDED = 'textTrackAdded';\n\n        /**\n         * Triggered when a text track should be shown\n         * @event MediaPlayerEvents#CUE_ENTER\n         */\n        this.CUE_ENTER = 'cueEnter'\n\n        /**\n         * Triggered when a text track should be hidden\n         * @event MediaPlayerEvents#CUE_ENTER\n         */\n        this.CUE_EXIT = 'cueExit'\n\n        /**\n         * Triggered when a throughput measurement based on the last segment request has been stored\n         * @event MediaPlayerEvents#THROUGHPUT_MEASUREMENT_STORED\n         */\n        this.THROUGHPUT_MEASUREMENT_STORED = 'throughputMeasurementStored';\n\n        /**\n         * Triggered when a ttml chunk is parsed.\n         * @event MediaPlayerEvents#TTML_PARSED\n         */\n        this.TTML_PARSED = 'ttmlParsed';\n\n        /**\n         * Triggered when a ttml chunk has to be parsed.\n         * @event MediaPlayerEvents#TTML_TO_PARSE\n         */\n        this.TTML_TO_PARSE = 'ttmlToParse';\n\n        /**\n         * Triggered when a caption is rendered.\n         * @event MediaPlayerEvents#CAPTION_RENDERED\n         */\n        this.CAPTION_RENDERED = 'captionRendered';\n\n        /**\n         * Triggered when the caption container is resized.\n         * @event MediaPlayerEvents#CAPTION_CONTAINER_RESIZE\n         */\n        this.CAPTION_CONTAINER_RESIZE = 'captionContainerResize';\n\n        /**\n         * Sent when enough data is available that the media can be played,\n         * at least for a couple of frames.  This corresponds to the\n         * HAVE_ENOUGH_DATA readyState.\n         * @event MediaPlayerEvents#CAN_PLAY\n         */\n        this.CAN_PLAY = 'canPlay';\n\n        /**\n         * This corresponds to the CAN_PLAY_THROUGH readyState.\n         * @event MediaPlayerEvents#CAN_PLAY_THROUGH\n         */\n        this.CAN_PLAY_THROUGH = 'canPlayThrough';\n\n        /**\n         * Sent when playback completes.\n         * @event MediaPlayerEvents#PLAYBACK_ENDED\n         */\n        this.PLAYBACK_ENDED = 'playbackEnded';\n\n        /**\n         * Sent when an error occurs.  The element's error\n         * attribute contains more information.\n         * @event MediaPlayerEvents#PLAYBACK_ERROR\n         */\n        this.PLAYBACK_ERROR = 'playbackError';\n\n        /**\n         * This event is fired once the playback has been initialized by MediaPlayer.js.\n         * After that event methods such as setTextTrack() can be used.\n         * @event MediaPlayerEvents#PLAYBACK_INITIALIZED\n         */\n        this.PLAYBACK_INITIALIZED = 'playbackInitialized';\n\n        /**\n         * Sent when playback is not allowed (for example if user gesture is needed).\n         * @event MediaPlayerEvents#PLAYBACK_NOT_ALLOWED\n         */\n        this.PLAYBACK_NOT_ALLOWED = 'playbackNotAllowed';\n\n        /**\n         * The media's metadata has finished loading; all attributes now\n         * contain as much useful information as they're going to.\n         * @event MediaPlayerEvents#PLAYBACK_METADATA_LOADED\n         */\n        this.PLAYBACK_METADATA_LOADED = 'playbackMetaDataLoaded';\n\n        /**\n         * The event is fired when the frame at the current playback position of the media has finished loading;\n         * often the first frame\n         * @event MediaPlayerEvents#PLAYBACK_LOADED_DATA\n         */\n        this.PLAYBACK_LOADED_DATA = 'playbackLoadedData';\n\n        /**\n         * Sent when playback is paused.\n         * @event MediaPlayerEvents#PLAYBACK_PAUSED\n         */\n        this.PLAYBACK_PAUSED = 'playbackPaused';\n\n        /**\n         * Sent when the media begins to play (either for the first time, after having been paused,\n         * or after ending and then restarting).\n         *\n         * @event MediaPlayerEvents#PLAYBACK_PLAYING\n         */\n        this.PLAYBACK_PLAYING = 'playbackPlaying';\n\n        /**\n         * Sent periodically to inform interested parties of progress downloading\n         * the media. Information about the current amount of the media that has\n         * been downloaded is available in the media element's buffered attribute.\n         * @event MediaPlayerEvents#PLAYBACK_PROGRESS\n         */\n        this.PLAYBACK_PROGRESS = 'playbackProgress';\n\n        /**\n         * Sent when the playback speed changes.\n         * @event MediaPlayerEvents#PLAYBACK_RATE_CHANGED\n         */\n        this.PLAYBACK_RATE_CHANGED = 'playbackRateChanged';\n\n        /**\n         * Sent when a seek operation completes.\n         * @event MediaPlayerEvents#PLAYBACK_SEEKED\n         */\n        this.PLAYBACK_SEEKED = 'playbackSeeked';\n\n        /**\n         * Sent when a seek operation begins.\n         * @event MediaPlayerEvents#PLAYBACK_SEEKING\n         */\n        this.PLAYBACK_SEEKING = 'playbackSeeking';\n\n        /**\n         * Sent when the video element reports stalled\n         * @event MediaPlayerEvents#PLAYBACK_STALLED\n         */\n        this.PLAYBACK_STALLED = 'playbackStalled';\n\n        /**\n         * Sent when playback of the media starts after having been paused;\n         * that is, when playback is resumed after a prior pause event.\n         *\n         * @event MediaPlayerEvents#PLAYBACK_STARTED\n         */\n        this.PLAYBACK_STARTED = 'playbackStarted';\n\n        /**\n         * The time indicated by the element's currentTime attribute has changed.\n         * @event MediaPlayerEvents#PLAYBACK_TIME_UPDATED\n         */\n        this.PLAYBACK_TIME_UPDATED = 'playbackTimeUpdated';\n\n        /**\n         * Sent when the video element reports that the volume has changed\n         * @event MediaPlayerEvents#PLAYBACK_VOLUME_CHANGED\n         */\n        this.PLAYBACK_VOLUME_CHANGED = 'playbackVolumeChanged';\n\n        /**\n         * Sent when the media playback has stopped because of a temporary lack of data.\n         *\n         * @event MediaPlayerEvents#PLAYBACK_WAITING\n         */\n        this.PLAYBACK_WAITING = 'playbackWaiting';\n\n        /**\n         * Manifest validity changed - As a result of an MPD validity expiration event.\n         * @event MediaPlayerEvents#MANIFEST_VALIDITY_CHANGED\n         */\n        this.MANIFEST_VALIDITY_CHANGED = 'manifestValidityChanged';\n\n        /**\n         * Dash events are triggered at their respective start points on the timeline.\n         * @event MediaPlayerEvents#EVENT_MODE_ON_START\n         */\n        this.EVENT_MODE_ON_START = 'eventModeOnStart';\n\n        /**\n         * Dash events are triggered as soon as they were parsed.\n         * @event MediaPlayerEvents#EVENT_MODE_ON_RECEIVE\n         */\n        this.EVENT_MODE_ON_RECEIVE = 'eventModeOnReceive';\n\n        /**\n         * Event that is dispatched whenever the player encounters a potential conformance validation that might lead to unexpected/not optimal behavior\n         * @event MediaPlayerEvents#CONFORMANCE_VIOLATION\n         */\n        this.CONFORMANCE_VIOLATION = 'conformanceViolation';\n\n        /**\n         * Event that is dispatched whenever the player switches to a different representation\n         * @event MediaPlayerEvents#REPRESENTATION_SWITCH\n         */\n        this.REPRESENTATION_SWITCH = 'representationSwitch';\n\n        /**\n         * Event that is dispatched whenever an adaptation set is removed due to all representations not being supported.\n         * @event MediaPlayerEvents#ADAPTATION_SET_REMOVED_NO_CAPABILITIES\n         */\n        this.ADAPTATION_SET_REMOVED_NO_CAPABILITIES = 'adaptationSetRemovedNoCapabilities';\n\n        /**\n         * Triggered when a content steering request has completed.\n         * @event MediaPlayerEvents#CONTENT_STEERING_REQUEST_COMPLETED\n         */\n        this.CONTENT_STEERING_REQUEST_COMPLETED = 'contentSteeringRequestCompleted';\n\n        /**\n         * Triggered when an inband prft (ProducerReferenceTime) boxes has been received.\n         * @event MediaPlayerEvents#INBAND_PRFT\n         */\n        this.INBAND_PRFT = 'inbandPrft';\n\n        /**\n         * The streaming attribute of the Managed Media Source is true\n         * @type {string}\n         */\n        this.MANAGED_MEDIA_SOURCE_START_STREAMING = 'managedMediaSourceStartStreaming';\n\n        /**\n         * The streaming attribute of the Managed Media Source is false\n         * @type {string}\n         */\n        this.MANAGED_MEDIA_SOURCE_END_STREAMING = 'managedMediaSourceEndStreaming';\n    }\n}\n\nlet mediaPlayerEvents = new MediaPlayerEvents();\nexport default mediaPlayerEvents;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\n/**\n * Constants declaration\n */\nexport default {\n    /**\n     *  @constant {string} STREAM Stream media type. Mainly used to report metrics relative to the full stream\n     *  @memberof Constants#\n     *  @static\n     */\n    STREAM: 'stream',\n\n    /**\n     *  @constant {string} VIDEO Video media type\n     *  @memberof Constants#\n     *  @static\n     */\n    VIDEO: 'video',\n\n    /**\n     *  @constant {string} AUDIO Audio media type\n     *  @memberof Constants#\n     *  @static\n     */\n    AUDIO: 'audio',\n\n    /**\n     *  @constant {string} TEXT Text media type\n     *  @memberof Constants#\n     *  @static\n     */\n    TEXT: 'text',\n\n    /**\n     *  @constant {string} MUXED Muxed (video/audio in the same chunk) media type\n     *  @memberof Constants#\n     *  @static\n     */\n    MUXED: 'muxed',\n\n    /**\n     *  @constant {string} IMAGE Image media type\n     *  @memberof Constants#\n     *  @static\n     */\n    IMAGE: 'image',\n\n    /**\n     *  @constant {string} STPP STTP Subtitles format\n     *  @memberof Constants#\n     *  @static\n     */\n    STPP: 'stpp',\n\n    /**\n     *  @constant {string} TTML STTP Subtitles format\n     *  @memberof Constants#\n     *  @static\n     */\n    TTML: 'ttml',\n\n    /**\n     *  @constant {string} VTT STTP Subtitles format\n     *  @memberof Constants#\n     *  @static\n     */\n    VTT: 'vtt',\n\n    /**\n     *  @constant {string} WVTT STTP Subtitles format\n     *  @memberof Constants#\n     *  @static\n     */\n    WVTT: 'wvtt',\n\n    /**\n     *  @constant {string} Content Steering\n     *  @memberof Constants#\n     *  @static\n     */\n    CONTENT_STEERING: 'contentSteering',\n\n    /**\n     *  @constant {string} LIVE_CATCHUP_MODE_DEFAULT Throughput calculation based on moof parsing\n     *  @memberof Constants#\n     *  @static\n     */\n    LIVE_CATCHUP_MODE_DEFAULT: 'liveCatchupModeDefault',\n\n    /**\n     *  @constant {string} LIVE_CATCHUP_MODE_LOLP Throughput calculation based on moof parsing\n     *  @memberof Constants#\n     *  @static\n     */\n    LIVE_CATCHUP_MODE_LOLP: 'liveCatchupModeLoLP',\n\n    /**\n     *  @constant {string} MOVING_AVERAGE_SLIDING_WINDOW Moving average sliding window\n     *  @memberof Constants#\n     *  @static\n     */\n    MOVING_AVERAGE_SLIDING_WINDOW: 'slidingWindow',\n\n    /**\n     *  @constant {string} EWMA Exponential moving average\n     *  @memberof Constants#\n     *  @static\n     */\n    MOVING_AVERAGE_EWMA: 'ewma',\n\n    /**\n     *  @constant {string} BAD_ARGUMENT_ERROR Invalid Arguments type of error\n     *  @memberof Constants#\n     *  @static\n     */\n    BAD_ARGUMENT_ERROR: 'Invalid Arguments',\n\n    /**\n     *  @constant {string} MISSING_CONFIG_ERROR Missing configuration parameters type of error\n     *  @memberof Constants#\n     *  @static\n     */\n    MISSING_CONFIG_ERROR: 'Missing config parameter(s)',\n\n    /**\n     *  @constant {string} TRACK_SWITCH_MODE_ALWAYS_REPLACE used to clear the buffered data (prior to current playback position) after track switch. Default for audio\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SWITCH_MODE_ALWAYS_REPLACE: 'alwaysReplace',\n\n    /**\n     *  @constant {string} TRACK_SWITCH_MODE_NEVER_REPLACE used to forbid clearing the buffered data (prior to current playback position) after track switch. Defers to fastSwitchEnabled for placement of new data. Default for video\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SWITCH_MODE_NEVER_REPLACE: 'neverReplace',\n\n    /**\n     *  @constant {string} TRACK_SELECTION_MODE_FIRST_TRACK makes the player select the first track found in the manifest.\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SELECTION_MODE_FIRST_TRACK: 'firstTrack',\n\n    /**\n     *  @constant {string} TRACK_SELECTION_MODE_HIGHEST_BITRATE makes the player select the track with a highest bitrate. This mode is a default mode.\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SELECTION_MODE_HIGHEST_BITRATE: 'highestBitrate',\n\n    /**\n     *  @constant {string} TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY makes the player select the track with the lowest bitrate per pixel average.\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY: 'highestEfficiency',\n\n    /**\n     *  @constant {string} TRACK_SELECTION_MODE_WIDEST_RANGE makes the player select the track with a widest range of bitrates.\n     *  @memberof Constants#\n     *  @static\n     */\n    TRACK_SELECTION_MODE_WIDEST_RANGE: 'widestRange',\n\n    /**\n     *  @constant {string} CMCD_QUERY_KEY specifies the key that is used for the CMCD query parameter.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_QUERY_KEY: 'CMCD',\n\n    /**\n     *  @constant {string} CMCD_MODE_QUERY specifies to attach CMCD metrics as query parameters.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_MODE_QUERY: 'query',\n\n    /**\n     *  @constant {string} CMCD_MODE_HEADER specifies to attach CMCD metrics as HTTP headers.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_MODE_HEADER: 'header',\n\n    /**\n     *  @constant {string} CMCD_AVAILABLE_KEYS specifies all the available keys for CMCD metrics.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_AVAILABLE_KEYS: ['br', 'd', 'ot', 'tb', 'bl', 'dl', 'mtp', 'nor', 'nrr', 'su', 'bs', 'rtp', 'cid', 'pr', 'sf', 'sid', 'st', 'v'],\n    /**\n     *  @constant {string} CMCD_AVAILABLE_KEYS_V2 specifies all the available keys for CMCD version 2 metrics.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_V2_AVAILABLE_KEYS: ['msd', 'ltc'],\n\n    /**\n     *  @constant {string} CMCD_AVAILABLE_REQUESTS specifies all the available requests type for CMCD metrics.\n     *  @memberof Constants#\n     *  @static\n     */\n    CMCD_AVAILABLE_REQUESTS: ['segment', 'mpd', 'xlink', 'steering', 'other'],\n\n\n    INITIALIZE: 'initialize',\n    TEXT_SHOWING: 'showing',\n    TEXT_HIDDEN: 'hidden',\n    TEXT_DISABLED: 'disabled',\n    ACCESSIBILITY_CEA608_SCHEME: 'urn:scte:dash:cc:cea-608:2015',\n    CC1: 'CC1',\n    CC3: 'CC3',\n    UTF8: 'utf-8',\n    SCHEME_ID_URI: 'schemeIdUri',\n    START_TIME: 'starttime',\n    SERVICE_DESCRIPTION_DVB_LL_SCHEME: 'urn:dvb:dash:lowlatency:scope:2019',\n    SUPPLEMENTAL_PROPERTY_DVB_LL_SCHEME: 'urn:dvb:dash:lowlatency:critical:2019',\n    CTA_5004_2023_SCHEME: 'urn:mpeg:dash:cta-5004:2023',\n    THUMBNAILS_SCHEME_ID_URIS: ['http://dashif.org/thumbnail_tile', 'http://dashif.org/guidelines/thumbnail_tile'],\n    FONT_DOWNLOAD_DVB_SCHEME: 'urn:dvb:dash:fontdownload:2014',\n    COLOUR_PRIMARIES_SCHEME_ID_URI: 'urn:mpeg:mpegB:cicp:ColourPrimaries',\n    URL_QUERY_INFO_SCHEME: 'urn:mpeg:dash:urlparam:2014',\n    EXT_URL_QUERY_INFO_SCHEME: 'urn:mpeg:dash:urlparam:2016',\n    MATRIX_COEFFICIENTS_SCHEME_ID_URI: 'urn:mpeg:mpegB:cicp:MatrixCoefficients',\n    TRANSFER_CHARACTERISTICS_SCHEME_ID_URI: 'urn:mpeg:mpegB:cicp:TransferCharacteristics',\n    HDR_METADATA_FORMAT_SCHEME_ID_URI: 'urn:dvb:dash:hdr-dmi',\n    HDR_METADATA_FORMAT_VALUES: {\n        ST2094_10: 'ST2094-10',\n        SL_HDR2: 'SL-HDR2',\n        ST2094_40: 'ST2094-40'\n    },\n    MEDIA_CAPABILITIES_API: {\n        COLORGAMUT: {\n            SRGB: 'srgb',\n            P3: 'p3',\n            REC2020: 'rec2020'\n        },\n        TRANSFERFUNCTION: {\n            SRGB: 'srgb',\n            PQ: 'pq',\n            HLG: 'hlg'\n        },\n        HDR_METADATATYPE: {\n            SMPTE_ST_2094_10: 'smpteSt2094-10',\n            SLHDR2: 'slhdr2',\n            SMPTE_ST_2094_40: 'smpteSt2094-40'\n        }\n    },\n    XML: 'XML',\n    ARRAY_BUFFER: 'ArrayBuffer',\n    DVB_REPORTING_URL: 'dvb:reportingUrl',\n    DVB_PROBABILITY: 'dvb:probability',\n    OFF_MIMETYPE: 'application/font-sfnt',\n    WOFF_MIMETYPE: 'application/font-woff',\n    VIDEO_ELEMENT_READY_STATES: {\n        HAVE_NOTHING: 0,\n        HAVE_METADATA: 1,\n        HAVE_CURRENT_DATA: 2,\n        HAVE_FUTURE_DATA: 3,\n        HAVE_ENOUGH_DATA: 4\n    },\n    FILE_LOADER_TYPES: {\n        FETCH: 'fetch_loader',\n        XHR: 'xhr_loader'\n    },\n    THROUGHPUT_TYPES: {\n        LATENCY: 'throughput_type_latency',\n        BANDWIDTH: 'throughput_type_bandwidth'\n    },\n    THROUGHPUT_CALCULATION_MODES: {\n        EWMA: 'throughputCalculationModeEwma',\n        ZLEMA: 'throughputCalculationModeZlema',\n        ARITHMETIC_MEAN: 'throughputCalculationModeArithmeticMean',\n        BYTE_SIZE_WEIGHTED_ARITHMETIC_MEAN: 'throughputCalculationModeByteSizeWeightedArithmeticMean',\n        DATE_WEIGHTED_ARITHMETIC_MEAN: 'throughputCalculationModeDateWeightedArithmeticMean',\n        HARMONIC_MEAN: 'throughputCalculationModeHarmonicMean',\n        BYTE_SIZE_WEIGHTED_HARMONIC_MEAN: 'throughputCalculationModeByteSizeWeightedHarmonicMean',\n        DATE_WEIGHTED_HARMONIC_MEAN: 'throughputCalculationModeDateWeightedHarmonicMean',\n    },\n    LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE: {\n        MOOF_PARSING: 'lowLatencyDownloadTimeCalculationModeMoofParsing',\n        DOWNLOADED_DATA: 'lowLatencyDownloadTimeCalculationModeDownloadedData',\n        AAST: 'lowLatencyDownloadTimeCalculationModeAast',\n    },\n    RULES_TYPES: {\n        QUALITY_SWITCH_RULES: 'qualitySwitchRules',\n        ABANDON_FRAGMENT_RULES: 'abandonFragmentRules'\n    },\n    QUALITY_SWITCH_RULES: {\n        BOLA_RULE: 'BolaRule',\n        THROUGHPUT_RULE: 'ThroughputRule',\n        INSUFFICIENT_BUFFER_RULE: 'InsufficientBufferRule',\n        SWITCH_HISTORY_RULE: 'SwitchHistoryRule',\n        DROPPED_FRAMES_RULE: 'DroppedFramesRule',\n        LEARN_TO_ADAPT_RULE: 'L2ARule',\n        LOL_PLUS_RULE: 'LoLPRule'\n    },\n    ABANDON_FRAGMENT_RULES: {\n        ABANDON_REQUEST_RULE: 'AbandonRequestsRule'\n    },\n\n    /**\n     *  @constant {string} ID3_SCHEME_ID_URI specifies scheme ID URI for ID3 timed metadata\n     *  @memberof Constants#\n     *  @static\n     */\n    ID3_SCHEME_ID_URI: 'https://aomedia.org/emsg/ID3',\n    COMMON_ACCESS_TOKEN_HEADER: 'common-access-token',\n    DASH_ROLE_SCHEME_ID : 'urn:mpeg:dash:role:2011',\n    CODEC_FAMILIES: {\n        MP3: 'mp3',\n        AAC: 'aac',\n        AC3: 'ac3',\n        EC3: 'ec3',\n        DTSX: 'dtsx',\n        DTSC: 'dtsc',\n        AVC: 'avc',\n        HEVC: 'hevc'\n    }\n}\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport EventsBase from '../../core/events/EventsBase.js';\n\n/**\n * @class\n * @implements EventsBase\n */\nclass MetricsReportingEvents extends EventsBase {\n    constructor () {\n        super();\n\n        this.METRICS_INITIALISATION_COMPLETE = 'internal_metricsReportingInitialized';\n        this.BECAME_REPORTING_PLAYER = 'internal_becameReportingPlayer';\n\n        /**\n         * Triggered when CMCD data was generated for a HTTP request\n         * @event MetricsReportingEvents#CMCD_DATA_GENERATED\n         */\n        this.CMCD_DATA_GENERATED = 'cmcdDataGenerated';\n    }\n}\n\nlet metricsReportingEvents = new MetricsReportingEvents();\nexport default metricsReportingEvents;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport UTCTiming from '../../dash/vo/UTCTiming.js';\nimport FactoryMaker from '../../core/FactoryMaker.js';\nimport Settings from '../../core/Settings.js';\nimport {checkParameterType} from '../utils/SupervisorTools.js';\nimport Constants from '../constants/Constants.js';\n\nconst DEFAULT_XHR_WITH_CREDENTIALS = false;\n\nfunction CustomParametersModel() {\n\n    let instance,\n        utcTimingSources,\n        xhrWithCredentials,\n        requestInterceptors,\n        responseInterceptors,\n        licenseRequestFilters,\n        licenseResponseFilters,\n        customCapabilitiesFilters,\n        customInitialTrackSelectionFunction,\n        customAbrRules;\n\n    const context = this.context;\n    const settings = Settings(context).getInstance();\n\n    function setup() {\n        xhrWithCredentials = {\n            default: DEFAULT_XHR_WITH_CREDENTIALS\n        };\n        _resetInitialSettings();\n    }\n\n    function _resetInitialSettings() {\n        requestInterceptors = [];\n        responseInterceptors = [];\n        licenseRequestFilters = [];\n        licenseResponseFilters = [];\n        customCapabilitiesFilters = [];\n        customAbrRules = [];\n        customInitialTrackSelectionFunction = null;\n        utcTimingSources = [];\n    }\n\n\n    function reset() {\n        _resetInitialSettings();\n    }\n\n    function setConfig() {\n\n    }\n\n    /**\n     * Registers a custom initial track selection function. Only one function is allowed. Calling this method will overwrite a potentially existing function.\n     * @param {function} customFunc - the custom function that returns the initial track\n     */\n    function setCustomInitialTrackSelectionFunction(customFunc) {\n        customInitialTrackSelectionFunction = customFunc;\n    }\n\n    /**\n     * Resets the custom initial track selection\n     */\n    function resetCustomInitialTrackSelectionFunction() {\n        customInitialTrackSelectionFunction = null;\n    }\n\n    /**\n     * Returns the initial track selection function\n     * @return {function}\n     */\n    function getCustomInitialTrackSelectionFunction() {\n        return customInitialTrackSelectionFunction;\n    }\n\n    /**\n     * Returns all license request filters\n     * @return {array}\n     */\n    function getLicenseRequestFilters() {\n        return licenseRequestFilters;\n    }\n\n    /**\n     * Returns all license response filters\n     * @return {array}\n     */\n    function getLicenseResponseFilters() {\n        return licenseResponseFilters;\n    }\n\n    /**\n     * Registers a license request filter. This enables application to manipulate/overwrite any request parameter and/or request data.\n     * The provided callback function shall return a promise that shall be resolved once the filter process is completed.\n     * The filters are applied in the order they are registered.\n     * @param {function} filter - the license request filter callback\n     */\n    function registerLicenseRequestFilter(filter) {\n        licenseRequestFilters.push(filter);\n    }\n\n    /**\n     * Registers a license response filter. This enables application to manipulate/overwrite the response data\n     * The provided callback function shall return a promise that shall be resolved once the filter process is completed.\n     * The filters are applied in the order they are registered.\n     * @param {function} filter - the license response filter callback\n     */\n    function registerLicenseResponseFilter(filter) {\n        licenseResponseFilters.push(filter);\n    }\n\n    /**\n     * Unregisters a license request filter.\n     * @param {function} filter - the license request filter callback\n     */\n    function unregisterLicenseRequestFilter(filter) {\n        _unregisterFilter(licenseRequestFilters, filter);\n    }\n\n    /**\n     * Unregisters a license response filter.\n     * @param {function} filter - the license response filter callback\n     */\n    function unregisterLicenseResponseFilter(filter) {\n        _unregisterFilter(licenseResponseFilters, filter);\n    }\n\n    /**\n     * Returns all custom capabilities filter\n     * @return {array}\n     */\n    function getCustomCapabilitiesFilters() {\n        return customCapabilitiesFilters;\n    }\n\n    /**\n     * Registers a custom capabilities filter. This enables application to filter representations to use.\n     * The provided callback function shall return a boolean or promise resolving to a boolean based on whether or not to use the representation.\n     * The filters are applied in the order they are registered.\n     * @param {function} filter - the custom capabilities filter callback\n     */\n    function registerCustomCapabilitiesFilter(filter) {\n        customCapabilitiesFilters.push(filter);\n    }\n\n    /**\n     * Unregisters a custom capabilities filter.\n     * @param {function} filter - the custom capabilities filter callback\n     */\n    function unregisterCustomCapabilitiesFilter(filter) {\n        _unregisterFilter(customCapabilitiesFilters, filter);\n    }\n\n    /**\n     * Unregister a filter from the list of existing filers.\n     * @param {array} filters\n     * @param {function} filter\n     * @private\n     */\n    function _unregisterFilter(filters, filter) {\n        let index = -1;\n        filters.some((item, i) => {\n            if (item === filter) {\n                index = i;\n                return true;\n            }\n        });\n        if (index < 0) {\n            return;\n        }\n        filters.splice(index, 1);\n    }\n\n    /**\n     * Iterate through the list of custom ABR rules and find the right rule by name\n     * @param {string} rulename\n     * @return {number} rule number\n     */\n    function _findAbrCustomRuleIndex(rulename) {\n        let i;\n        for (i = 0; i < customAbrRules.length; i++) {\n            if (customAbrRules[i].rulename === rulename) {\n                return i;\n            }\n        }\n        return -1;\n    }\n\n    /**\n     * Add a custom ABR Rule\n     * Rule will be apply on next stream if a stream is being played\n     *\n     * @param {string} type - rule type (one of ['qualitySwitchRules','abandonFragmentRules'])\n     * @param {string} rulename - name of rule (used to identify custom rule). If one rule of same name has been added, then existing rule will be updated\n     * @param {object} rule - the rule object instance\n     * @throws {@link Constants#BAD_ARGUMENT_ERROR BAD_ARGUMENT_ERROR} if called with invalid arguments.\n     */\n    function addAbrCustomRule(type, rulename, rule) {\n        if (typeof type !== 'string' || (type !== Constants.RULES_TYPES.ABANDON_FRAGMENT_RULES && type !== Constants.RULES_TYPES.QUALITY_SWITCH_RULES) ||\n            typeof rulename !== 'string') {\n            throw Constants.BAD_ARGUMENT_ERROR;\n        }\n        let index = _findAbrCustomRuleIndex(rulename);\n        if (index === -1) {\n            // add rule\n            customAbrRules.push({\n                type: type,\n                rulename: rulename,\n                rule: rule\n            });\n        } else {\n            // update rule\n            customAbrRules[index].type = type;\n            customAbrRules[index].rule = rule;\n        }\n    }\n\n    /**\n     * Remove a custom ABR Rule\n     *\n     * @param {string} rulename - name of the rule to be removed\n     */\n    function removeAbrCustomRule(rulename) {\n        if (rulename) {\n            let index = _findAbrCustomRuleIndex(rulename);\n            //if no rulename custom rule has been found, do nothing\n            if (index !== -1) {\n                // remove rule\n                customAbrRules.splice(index, 1);\n            }\n        } else {\n            //if no rulename is defined, remove all ABR custome rules\n            customAbrRules = [];\n        }\n    }\n\n    /**\n     * Remove all custom rules\n     */\n    function removeAllAbrCustomRule() {\n        customAbrRules = [];\n    }\n\n    /**\n     * Return all ABR custom rules\n     * @return {array}\n     */\n    function getAbrCustomRules() {\n        return customAbrRules;\n    }\n\n    /**\n     * Adds a request interceptor. This enables application to monitor, manipulate, overwrite any request parameter and/or request data.\n     * The provided callback function shall return a promise with updated request that shall be resolved once the process of the request is completed.\n     * The interceptors are applied in the order they are added.\n     * @param {function} interceptor - the request interceptor callback\n     */\n    function addRequestInterceptor(interceptor) {\n        requestInterceptors.push(interceptor);\n    }\n\n    /**\n     * Adds a response interceptor. This enables application to monitor, manipulate, overwrite the response data\n     * The provided callback function shall return a promise with updated response that shall be resolved once the process of the response is completed.\n     * The interceptors are applied in the order they are added.\n     * @param {function} interceptor - the response interceptor callback\n     */\n    function addResponseInterceptor(interceptor) {\n        responseInterceptors.push(interceptor);\n    }\n\n    /**\n     * Unregisters a request interceptor.\n     * @param {function} interceptor - the request interceptor callback\n     */\n    function removeRequestInterceptor(interceptor) {\n        _unregisterFilter(requestInterceptors, interceptor);\n    }\n\n    /**\n     * Unregisters a response interceptor.\n     * @param {function} interceptor - the request interceptor callback\n     */\n    function removeResponseInterceptor(interceptor) {\n        _unregisterFilter(responseInterceptors, interceptor);\n    }\n\n    /**\n     * Returns all request interceptors\n     * @return {array}\n     */\n    function getRequestInterceptors() {\n        return requestInterceptors;\n    }\n\n    /**\n     * Returns all response interceptors\n     * @return {array}\n     */\n    function getResponseInterceptors() {\n        return responseInterceptors;\n    }\n\n    /**\n     * Add a UTC timing source at the top of the list\n     * @param {string} schemeIdUri\n     * @param {string} value\n     */\n    function addUTCTimingSource(schemeIdUri, value) {\n        removeUTCTimingSource(schemeIdUri, value); //check if it already exists and remove if so.\n        let vo = new UTCTiming();\n        vo.schemeIdUri = schemeIdUri;\n        vo.value = value;\n        utcTimingSources.push(vo);\n    }\n\n    /**\n     * Return all UTC timing sources\n     * @return {array}\n     */\n    function getUTCTimingSources() {\n        return utcTimingSources;\n    }\n\n    /**\n     * Remove a specific timing source from the array\n     * @param {string} schemeIdUri\n     * @param {string} value\n     */\n    function removeUTCTimingSource(schemeIdUri, value) {\n        checkParameterType(schemeIdUri, 'string');\n        checkParameterType(value, 'string');\n        utcTimingSources.forEach(function (obj, idx) {\n            if (obj.schemeIdUri === schemeIdUri && obj.value === value) {\n                utcTimingSources.splice(idx, 1);\n            }\n        });\n    }\n\n    /**\n     * Remove all timing sources\n     */\n    function clearDefaultUTCTimingSources() {\n        utcTimingSources = [];\n    }\n\n    /**\n     * Add the default timing source to the list\n     */\n    function restoreDefaultUTCTimingSources() {\n        let defaultUtcTimingSource = settings.get().streaming.utcSynchronization.defaultTimingSource;\n        addUTCTimingSource(defaultUtcTimingSource.scheme, defaultUtcTimingSource.value);\n    }\n\n    function setXHRWithCredentialsForType(type, value) {\n        if (!type) {\n            Object.keys(xhrWithCredentials).forEach(key => {\n                setXHRWithCredentialsForType(key, value);\n            });\n        } else {\n            xhrWithCredentials[type] = !!value;\n        }\n    }\n\n    function getXHRWithCredentialsForType(type) {\n        const useCreds = xhrWithCredentials[type];\n\n        return useCreds === undefined ? xhrWithCredentials.default : useCreds;\n    }\n\n    instance = {\n        addAbrCustomRule,\n        addRequestInterceptor,\n        addResponseInterceptor,\n        addUTCTimingSource,\n        clearDefaultUTCTimingSources,\n        getAbrCustomRules,\n        getCustomCapabilitiesFilters,\n        getCustomInitialTrackSelectionFunction,\n        getLicenseRequestFilters,\n        getLicenseResponseFilters,\n        getRequestInterceptors,\n        getResponseInterceptors,\n        getUTCTimingSources,\n        getXHRWithCredentialsForType,\n        registerCustomCapabilitiesFilter,\n        registerLicenseRequestFilter,\n        registerLicenseResponseFilter,\n        removeAbrCustomRule,\n        removeAllAbrCustomRule,\n        removeRequestInterceptor,\n        removeResponseInterceptor,\n        removeUTCTimingSource,\n        reset,\n        resetCustomInitialTrackSelectionFunction,\n        restoreDefaultUTCTimingSources,\n        setConfig,\n        setCustomInitialTrackSelectionFunction,\n        setXHRWithCredentialsForType,\n        unregisterCustomCapabilitiesFilter,\n        unregisterLicenseRequestFilter,\n        unregisterLicenseResponseFilter,\n    };\n\n    setup();\n\n    return instance;\n}\n\nCustomParametersModel.__dashjs_factory_name = 'CustomParametersModel';\nexport default FactoryMaker.getSingletonFactory(CustomParametersModel);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport FactoryMaker from '../../core/FactoryMaker.js';\nimport {checkInteger} from './SupervisorTools.js';\n\nfunction CustomTimeRanges(/*config*/) {\n    let customTimeRangeArray = [];\n    let length = 0;\n\n    function add(start, end) {\n        let i;\n\n        // eslint-disable-next-line curly\n        for (i = 0; (i < this.customTimeRangeArray.length) && (start > this.customTimeRangeArray[i].start); i++) ;\n\n        this.customTimeRangeArray.splice(i, 0, { start: start, end: end });\n\n        for (i = 0; i < this.customTimeRangeArray.length - 1; i++) {\n            if (this.mergeRanges(i, i + 1)) {\n                i--;\n            }\n        }\n        this.length = this.customTimeRangeArray.length;\n    }\n\n    function clear() {\n        this.customTimeRangeArray = [];\n        this.length = 0;\n    }\n\n    function remove(start, end) {\n        for (let i = 0; i < this.customTimeRangeArray.length; i++) {\n            if (start <= this.customTimeRangeArray[i].start && end >= this.customTimeRangeArray[i].end) {\n                //      |--------------Range i-------|\n                //|---------------Range to remove ---------------|\n                //    or\n                //|--------------Range i-------|\n                //|--------------Range to remove ---------------|\n                //    or\n                //                 |--------------Range i-------|\n                //|--------------Range to remove ---------------|\n                this.customTimeRangeArray.splice(i, 1);\n                i--;\n\n            } else if (start > this.customTimeRangeArray[i].start && end < this.customTimeRangeArray[i].end) {\n                //|-----------------Range i----------------|\n                //        |-------Range to remove -----|\n                this.customTimeRangeArray.splice(i + 1, 0, { start: end, end: this.customTimeRangeArray[i].end });\n                this.customTimeRangeArray[i].end = start;\n                break;\n            } else if (start > this.customTimeRangeArray[i].start && start < this.customTimeRangeArray[i].end) {\n                //|-----------Range i----------|\n                //                    |---------Range to remove --------|\n                //    or\n                //|-----------------Range i----------------|\n                //            |-------Range to remove -----|\n                this.customTimeRangeArray[i].end = start;\n            } else if (end > this.customTimeRangeArray[i].start && end < this.customTimeRangeArray[i].end) {\n                //                     |-----------Range i----------|\n                //|---------Range to remove --------|\n                //            or\n                //|-----------------Range i----------------|\n                //|-------Range to remove -----|\n                this.customTimeRangeArray[i].start = end;\n            }\n        }\n\n        this.length = this.customTimeRangeArray.length;\n    }\n\n    function mergeRanges(rangeIndex1, rangeIndex2) {\n        let range1 = this.customTimeRangeArray[rangeIndex1];\n        let range2 = this.customTimeRangeArray[rangeIndex2];\n\n        if (range1.start <= range2.start && range2.start <= range1.end && range1.end <= range2.end) {\n            //|-----------Range1----------|\n            //                    |-----------Range2----------|\n            range1.end = range2.end;\n            this.customTimeRangeArray.splice(rangeIndex2, 1);\n            return true;\n\n        } else if (range2.start <= range1.start && range1.start <= range2.end && range2.end <= range1.end) {\n            //                |-----------Range1----------|\n            //|-----------Range2----------|\n            range1.start = range2.start;\n            this.customTimeRangeArray.splice(rangeIndex2, 1);\n            return true;\n        } else if (range2.start <= range1.start && range1.start <= range2.end && range1.end <= range2.end) {\n            //      |--------Range1-------|\n            //|---------------Range2--------------|\n            this.customTimeRangeArray.splice(rangeIndex1, 1);\n            return true;\n        } else if (range1.start <= range2.start && range2.start <= range1.end && range2.end <= range1.end) {\n            //|-----------------Range1--------------|\n            //        |-----------Range2----------|\n            this.customTimeRangeArray.splice(rangeIndex2, 1);\n            return true;\n        }\n        return false;\n    }\n\n    function start(index) {\n        checkInteger(index);\n\n        if (index >= this.customTimeRangeArray.length || index < 0) {\n            return NaN;\n        }\n\n        return this.customTimeRangeArray[index].start;\n    }\n\n    function end(index) {\n        checkInteger(index);\n\n        if (index >= this.customTimeRangeArray.length || index < 0) {\n            return NaN;\n        }\n\n        return this.customTimeRangeArray[index].end;\n    }\n\n    return {\n        customTimeRangeArray: customTimeRangeArray,\n        length: length,\n        add: add,\n        clear: clear,\n        remove: remove,\n        mergeRanges: mergeRanges,\n        start: start,\n        end: end\n    };\n}\n\nCustomTimeRanges.__dashjs_factory_name = 'CustomTimeRanges';\nexport default FactoryMaker.getClassFactory(CustomTimeRanges);\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\nimport Constants from '../constants/Constants.js';\n\nexport function checkParameterType(parameter, type) {\n    if (typeof parameter !== type) {\n        throw Constants.BAD_ARGUMENT_ERROR;\n    }\n}\n\nexport function checkInteger(parameter) {\n    const isInt = parameter !== null && !isNaN(parameter) && (parameter % 1 === 0);\n\n    if (!isInt) {\n        throw Constants.BAD_ARGUMENT_ERROR + ' : argument is not an integer';\n    }\n}\n\nexport function checkRange(parameter, min, max) {\n    if (parameter < min || parameter > max) {\n        throw Constants.BAD_ARGUMENT_ERROR + ' : argument out of range';\n    }\n}\n\nexport function checkIsVideoOrAudioType(type) {\n    if (typeof type !== 'string' || (type !== Constants.AUDIO && type !== Constants.VIDEO)) {\n        throw Constants.BAD_ARGUMENT_ERROR;\n    }\n}\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @classdesc This Object holds reference to the HTTPRequest for manifest, fragment and xlink loading.\n * Members which are not defined in ISO23009-1 Annex D should be prefixed by a _ so that they are ignored\n * by Metrics Reporting code.\n * @ignore\n */\nclass HTTPRequest {\n    /**\n     * @class\n     */\n    constructor() {\n        /**\n         * Identifier of the TCP connection on which the HTTP request was sent.\n         * @public\n         */\n        this.tcpid = null;\n        /**\n         * This is an optional parameter and should not be included in HTTP request/response transactions for progressive download.\n         * The type of the request:\n         * - MPD\n         * - XLink expansion\n         * - Initialization Fragment\n         * - Index Fragment\n         * - Media Fragment\n         * - Bitstream Switching Fragment\n         * - other\n         * @public\n         */\n        this.type = null;\n        /**\n         * The original URL (before any redirects or failures)\n         * @public\n         */\n        this.url = null;\n        /**\n         * The actual URL requested, if different from above\n         * @public\n         */\n        this.actualurl = null;\n        /**\n         * The contents of the byte-range-spec part of the HTTP Range header.\n         * @public\n         */\n        this.range = null;\n        /**\n         * Real-Time | The real time at which the request was sent.\n         * @public\n         */\n        this.trequest = null;\n        /**\n         * Real-Time | The real time at which the first byte of the response was received.\n         * @public\n         */\n        this.tresponse = null;\n        /**\n         * The HTTP response code.\n         * @public\n         */\n        this.responsecode = null;\n        /**\n         * The duration of the throughput trace intervals (ms), for successful requests only.\n         * @public\n         */\n        this.interval = null;\n        /**\n         * Throughput traces, for successful requests only.\n         * @public\n         */\n        this.trace = [];\n        /**\n         * The CMSD static and dynamic values retrieved from CMSD response headers.\n         * @public\n         */\n        this.cmsd = null;\n\n        /**\n         * Type of stream (\"audio\" | \"video\" etc..)\n         * @public\n         */\n        this._stream = null;\n        /**\n         * Real-Time | The real time at which the request finished.\n         * @public\n         */\n        this._tfinish = null;\n        /**\n         * The duration of the media requests, if available, in seconds.\n         * @public\n         */\n        this._mediaduration = null;\n        /**\n         * all the response headers from request.\n         * @public\n         */\n        this._responseHeaders = null;\n        /**\n         * The selected service location for the request. string.\n         * @public\n         */\n        this._serviceLocation = null;\n        /**\n         * The type of the loader that was used. Distinguish between fetch loader and xhr loader\n         */\n        this._fileLoaderType = null;\n        /**\n         * The values derived from the ResourceTimingAPI.\n         */\n        this._resourceTimingValues = null;\n    }\n}\n\n/**\n * @classdesc This Object holds reference to the progress of the HTTPRequest.\n * @ignore\n */\nclass HTTPRequestTrace {\n    /**\n     * @class\n     */\n    constructor() {\n        /**\n         * Real-Time | Measurement stream start.\n         * @public\n         */\n        this.s = null;\n        /**\n         * Measurement stream duration (ms).\n         * @public\n         */\n        this.d = null;\n        /**\n         * List of integers counting the bytes received in each trace interval within the measurement stream.\n         * @public\n         */\n        this.b = [];\n    }\n}\n\nHTTPRequest.GET = 'GET';\nHTTPRequest.HEAD = 'HEAD';\nHTTPRequest.MPD_TYPE = 'MPD';\nHTTPRequest.XLINK_EXPANSION_TYPE = 'XLinkExpansion';\nHTTPRequest.INIT_SEGMENT_TYPE = 'InitializationSegment';\nHTTPRequest.INDEX_SEGMENT_TYPE = 'IndexSegment';\nHTTPRequest.MEDIA_SEGMENT_TYPE = 'MediaSegment';\nHTTPRequest.BITSTREAM_SWITCHING_SEGMENT_TYPE = 'BitstreamSwitchingSegment';\nHTTPRequest.MSS_FRAGMENT_INFO_SEGMENT_TYPE = 'FragmentInfoSegment';\nHTTPRequest.DVB_REPORTING_TYPE = 'DVBReporting';\nHTTPRequest.LICENSE = 'license';\nHTTPRequest.CONTENT_STEERING_TYPE = 'ContentSteering';\nHTTPRequest.OTHER_TYPE = 'other';\n\nexport {HTTPRequest, HTTPRequestTrace};\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "__webpack_require__.amdO = {};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass DVBErrors {\n    constructor() {\n        this.mpdurl = null;\n        // String - Absolute URL from which the MPD was originally\n        // retrieved (MPD updates will not change this value).\n\n        this.errorcode = null;\n        // String - The value of errorcode depends upon the type\n        // of error being reported. For an error listed in the\n        // ErrorType column below the value is as described in the\n        // Value column.\n        //\n        // ErrorType                                            Value\n        // ---------                                            -----\n        // HTTP error status code                               HTTP status code\n        // Unknown HTTP status code                             HTTP status code\n        // SSL connection failed                                \"SSL\" followed by SSL alert value\n        // DNS resolution failed                                \"C00\"\n        // Host unreachable                                     \"C01\"\n        // Connection refused                                   \"C02\"\n        // Connection error – Not otherwise specified           \"C03\"\n        // Corrupt media – ISO BMFF container cannot be parsed  \"M00\"\n        // Corrupt media – Not otherwise specified              \"M01\"\n        // Changing Base URL in use due to errors               \"F00\"\n        // Becoming an error reporting Player                   \"S00\"\n\n        this.terror = null;\n        // Real-Time - Date and time at which error occurred in UTC,\n        // formatted as a combined date and time according to ISO 8601.\n\n        this.url = null;\n        // String - Absolute URL from which data was being requested\n        // when this error occurred. If the error report is in relation\n        // to corrupt media or changing BaseURL, this may be a null\n        // string if the URL from which the media was obtained or\n        // which led to the change of BaseURL is no longer known.\n\n        this.ipaddress = null;\n        // String - IP Address which the host name in \"url\" resolved to.\n        // If the error report is in relation to corrupt media or\n        // changing BaseURL, this may be a null string if the URL\n        // from which the media was obtained or which led to the\n        // change of BaseURL is no longer known.\n\n        this.servicelocation = null;\n        // String - The value of the serviceLocation field in the\n        // BaseURL being used. In the event of this report indicating\n        // a change of BaseURL this is the value from the BaseURL\n        // being moved from.\n    }\n}\n\nDVBErrors.SSL_CONNECTION_FAILED_PREFIX = 'SSL';\nDVBErrors.DNS_RESOLUTION_FAILED = 'C00';\nDVBErrors.HOST_UNREACHABLE = 'C01';\nDVBErrors.CONNECTION_REFUSED = 'C02';\nDVBErrors.CONNECTION_ERROR = 'C03';\nDVBErrors.CORRUPT_MEDIA_ISOBMFF = 'M00';\nDVBErrors.CORRUPT_MEDIA_OTHER = 'M01';\nDVBErrors.BASE_URL_CHANGED = 'F00';\nDVBErrors.BECAME_REPORTER = 'S00';\n\nexport default DVBErrors;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport DVBErrors from '../vo/DVBErrors.js';\nimport MetricsReportingEvents from '../MetricsReportingEvents.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction DVBErrorsTranslator(config) {\n\n    config = config || {};\n    let instance,\n        mpd;\n    const eventBus = config.eventBus;\n    const dashMetrics = config.dashMetrics;\n    const metricsConstants = config.metricsConstants;\n    //MediaPlayerEvents have been added to Events in MediaPlayer class\n    const Events = config.events;\n\n    function report(vo) {\n        let o = new DVBErrors();\n\n        if (!mpd) {\n            return;\n        }\n\n        for (const key in vo) {\n            if (vo.hasOwnProperty(key)) {\n                o[key] = vo[key];\n            }\n        }\n\n        if (!o.mpdurl) {\n            o.mpdurl = mpd.originalUrl || mpd.url;\n        }\n\n        if (!o.terror) {\n            o.terror = new Date();\n        }\n\n        dashMetrics.addDVBErrors(o);\n    }\n\n    function onManifestUpdate(e) {\n        if (e.error) {\n            return;\n        }\n\n        mpd = e.manifest;\n    }\n\n    function onServiceLocationChanged(e) {\n        report({\n            errorcode: DVBErrors.BASE_URL_CHANGED,\n            servicelocation: e.entry\n        });\n    }\n\n    function onBecameReporter() {\n        report({\n            errorcode: DVBErrors.BECAME_REPORTER\n        });\n    }\n\n    function handleHttpMetric(vo) {\n        if ((vo.responsecode === 0) || // connection failure - unknown\n            (vo.responsecode == null) || // Generated on .catch() and when uninitialized\n            (vo.responsecode >= 400) || // HTTP error status code\n            (vo.responsecode < 100) || // unknown status codes\n            (vo.responsecode >= 600)) { // unknown status codes\n            report({\n                errorcode: vo.responsecode || DVBErrors.CONNECTION_ERROR,\n                url: vo.url,\n                terror: vo.tresponse,\n                servicelocation: vo._serviceLocation\n            });\n        }\n    }\n\n    function onMetricEvent(e) {\n        switch (e.metric) {\n            case metricsConstants.HTTP_REQUEST:\n                handleHttpMetric(e.value);\n                break;\n            default:\n                break;\n        }\n    }\n\n    function onPlaybackError(e) {\n        let reason = e.error ? e.error.code : 0;\n        let errorcode;\n\n        switch (reason) {\n            case MediaError.MEDIA_ERR_NETWORK:\n                errorcode = DVBErrors.CONNECTION_ERROR;\n                break;\n            case MediaError.MEDIA_ERR_DECODE:\n                errorcode = DVBErrors.CORRUPT_MEDIA_OTHER;\n                break;\n            default:\n                return;\n        }\n\n        report({\n            errorcode: errorcode\n        });\n    }\n\n    function initialize() {\n        eventBus.on(Events.MANIFEST_UPDATED, onManifestUpdate, instance);\n        eventBus.on(\n            Events.SERVICE_LOCATION_BASE_URL_BLACKLIST_CHANGED,\n            onServiceLocationChanged,\n            instance\n        );\n        eventBus.on(Events.METRIC_ADDED, onMetricEvent, instance);\n        eventBus.on(Events.METRIC_UPDATED, onMetricEvent, instance);\n        eventBus.on(Events.PLAYBACK_ERROR, onPlaybackError, instance);\n        eventBus.on(\n            MetricsReportingEvents.BECAME_REPORTING_PLAYER,\n            onBecameReporter,\n            instance\n        );\n    }\n\n    function reset() {\n        eventBus.off(Events.MANIFEST_UPDATED, onManifestUpdate, instance);\n        eventBus.off(\n            Events.SERVICE_LOCATION_BASE_URL_BLACKLIST_CHANGED,\n            onServiceLocationChanged,\n            instance\n        );\n        eventBus.off(Events.METRIC_ADDED, onMetricEvent, instance);\n        eventBus.off(Events.METRIC_UPDATED, onMetricEvent, instance);\n        eventBus.off(Events.PLAYBACK_ERROR, onPlaybackError, instance);\n        eventBus.off(\n            MetricsReportingEvents.BECAME_REPORTING_PLAYER,\n            onBecameReporter,\n            instance\n        );\n    }\n\n    instance = {\n        initialize,\n        reset\n    };\n\n    return instance;\n}\n\nDVBErrorsTranslator.__dashjs_factory_name = 'DVBErrorsTranslator';\nexport default FactoryMaker.getSingletonFactory(DVBErrorsTranslator); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport CustomTimeRanges from '../../utils/CustomTimeRanges.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction RangeController(config) {\n\n    config = config || {};\n    let useWallClockTime = false;\n    let context = this.context;\n    let instance,\n        ranges;\n\n    let mediaElement = config.mediaElement;\n\n    function initialize(rs) {\n        if (rs && rs.length) {\n            rs.forEach(r => {\n                let start = r.starttime;\n                let end = start + r.duration;\n\n                ranges.add(start, end);\n            });\n\n            useWallClockTime = !!rs[0]._useWallClockTime;\n        }\n    }\n\n    function reset() {\n        ranges.clear();\n    }\n\n    function setup() {\n        ranges = CustomTimeRanges(context).create();\n    }\n\n    function isEnabled() {\n        let numRanges = ranges.length;\n        let time;\n\n        if (!numRanges) {\n            return true;\n        }\n\n        // When not present, DASH Metrics reporting is requested\n        // for the whole duration of the content.\n        time = useWallClockTime ?\n            (new Date().getTime() / 1000) :\n            mediaElement.currentTime;\n\n        for (let i = 0; i < numRanges; i += 1) {\n            let start = ranges.start(i);\n            let end = ranges.end(i);\n\n            if ((start <= time) && (time < end)) {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    instance = {\n        initialize: initialize,\n        reset: reset,\n        isEnabled: isEnabled\n    };\n\n    setup();\n\n    return instance;\n}\n\nRangeController.__dashjs_factory_name = 'RangeController';\nexport default FactoryMaker.getClassFactory(RangeController); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\n/**\n * @ignore\n */\nfunction MetricSerialiser() {\n\n    // For each entry in the top level list within the metric (in the case\n    // of the DVBErrors metric each entry corresponds to an \"error event\"\n    // described in clause 10.8.4) the Player shall:\n    function serialise(metric) {\n        let pairs = [];\n        let obj = [];\n        let key,\n            value;\n\n        // Take each (key, value) pair from the metric entry and create a\n        // string consisting of the name of the key, followed by an equals\n        // ('=') character, followed by the string representation of the\n        // value. The string representation of the value is created based\n        // on the type of the value following the instructions in Table 22.\n        for (key in metric) {\n            if (metric.hasOwnProperty(key) && (key.indexOf('_') !== 0)) {\n                value = metric[key];\n\n                // we want to ensure that keys still end up in the report\n                // even if there is no value\n                if ((value === undefined) || (value === null)) {\n                    value = '';\n                }\n\n                // DVB A168 10.12.4 Table 22\n                if (Array.isArray(value)) {\n                    // if trace or similar is null, do not include in output\n                    if (!value.length) {\n                        continue;\n                    }\n\n                    obj = [];\n\n                    value.forEach(function (v) {\n                        let isBuiltIn = Object.prototype.toString.call(v).slice(8, -1) !== 'Object';\n\n                        obj.push(isBuiltIn ? v : serialise(v));\n                    });\n\n                    value = obj.map(encodeURIComponent).join(',');\n                } else if (typeof value === 'string') {\n                    value = encodeURIComponent(value);\n                } else if (value instanceof Date) {\n                    value = value.toISOString();\n                } else if (typeof value === 'number') {\n                    value = Math.round(value);\n                }\n\n                pairs.push(key + '=' + value);\n            }\n        }\n\n        // Concatenate the strings created in the previous step with an\n        // ampersand ('&') character between each one.\n        return pairs.join('&');\n    }\n\n    return {\n        serialise: serialise\n    };\n}\n\nMetricSerialiser.__dashjs_factory_name = 'MetricSerialiser';\nexport default FactoryMaker.getSingletonFactory(MetricSerialiser); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\n/**\n * @ignore\n */\nfunction RNG() {\n\n    // check whether secure random numbers are available. if not, revert to\n    // using Math.random\n    let crypto = window.crypto || window.msCrypto;\n\n    // could just as easily use any other array type by changing line below\n    let ArrayType = Uint32Array;\n    let MAX_VALUE = Math.pow(2, ArrayType.BYTES_PER_ELEMENT * 8) - 1;\n\n    // currently there is only one client for this code, and that only uses\n    // a single random number per initialisation. may want to increase this\n    // number if more consumers in the future\n    let NUM_RANDOM_NUMBERS = 10;\n\n    let randomNumbers,\n        index,\n        instance;\n\n    function initialize() {\n        if (crypto) {\n            if (!randomNumbers) {\n                randomNumbers = new ArrayType(NUM_RANDOM_NUMBERS);\n            }\n            crypto.getRandomValues(randomNumbers);\n            index = 0;\n        }\n    }\n\n    function rand(min, max) {\n        let r;\n\n        if (!min) {\n            min = 0;\n        }\n\n        if (!max) {\n            max = 1;\n        }\n\n        if (crypto) {\n            if (index === randomNumbers.length) {\n                initialize();\n            }\n\n            r = randomNumbers[index] / MAX_VALUE;\n            index += 1;\n        } else {\n            r = Math.random();\n        }\n\n        return (r * (max - min)) + min;\n    }\n\n    instance = {\n        random: rand\n    };\n\n    initialize();\n\n    return instance;\n}\n\nRNG.__dashjs_factory_name = 'RNG';\nexport default FactoryMaker.getSingletonFactory(RNG); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport MetricSerialiser from '../../utils/MetricSerialiser.js';\nimport RNG from '../../utils/RNG.js';\nimport CustomParametersModel from '../../../models/CustomParametersModel.js';\nimport FactoryMaker from '../../../../core/FactoryMaker.js';\n\nfunction DVBReporting(config) {\n    config = config || {};\n    let instance;\n\n    let context = this.context;\n    let metricSerialiser,\n        customParametersModel,\n        randomNumberGenerator,\n        reportingPlayerStatusDecided,\n        isReportingPlayer,\n        reportingUrl,\n        rangeController;\n\n    let USE_DRAFT_DVB_SPEC = true;\n    let allowPendingRequestsToCompleteOnReset = true;\n    let pendingRequests = [];\n\n    const metricsConstants = config.metricsConstants;\n\n    function setup() {\n        metricSerialiser = MetricSerialiser(context).getInstance();\n        randomNumberGenerator = RNG(context).getInstance();\n        customParametersModel = CustomParametersModel(context).getInstance();\n\n        resetInitialSettings();\n    }\n\n    function doGetRequest(url, successCB, failureCB) {\n        let req = new XMLHttpRequest();\n        req.withCredentials = customParametersModel.getXHRWithCredentialsForType(metricsConstants.HTTP_REQUEST_DVB_REPORTING_TYPE);\n        const oncomplete = function () {\n            let reqIndex = pendingRequests.indexOf(req);\n\n            if (reqIndex === -1) {\n                return;\n            } else {\n                pendingRequests.splice(reqIndex, 1);\n            }\n\n            if ((req.status >= 200) && (req.status < 300)) {\n                if (successCB) {\n                    successCB();\n                }\n            } else {\n                if (failureCB) {\n                    failureCB();\n                }\n            }\n        };\n\n        pendingRequests.push(req);\n\n        try {\n            req.open('GET', url);\n            req.onloadend = oncomplete;\n            req.onerror = oncomplete;\n            req.send();\n        } catch (e) {\n            req.onerror();\n        }\n    }\n\n    function report(type, vos) {\n        if (!Array.isArray(vos)) {\n            vos = [vos];\n        }\n\n        // If the Player is not a reporting Player, then the Player shall\n        // not report any errors.\n        // ... In addition to any time restrictions specified by a Range\n        // element within the Metrics element.\n        if (isReportingPlayer && rangeController.isEnabled()) {\n\n            // This reporting mechanism operates by creating one HTTP GET\n            // request for every entry in the top level list of the metric.\n            vos.forEach(function (vo) {\n                let url = metricSerialiser.serialise(vo);\n\n                // this has been proposed for errata\n                if (USE_DRAFT_DVB_SPEC && (type !== metricsConstants.DVB_ERRORS)) {\n                    url = `metricname=${type}&${url}`;\n                }\n\n                // Take the value of the @reportingUrl attribute, append a\n                // question mark ('?') character and then append the string\n                // created in the previous step.\n                url = `${reportingUrl}?${url}`;\n\n                // Make an HTTP GET request to the URL contained within the\n                // string created in the previous step.\n                doGetRequest(url, null, function () {\n                    // If the Player is unable to make the report, for\n                    // example because the @reportingUrl is invalid, the\n                    // host cannot be reached, or an HTTP status code other\n                    // than one in the 200 series is received, the Player\n                    // shall cease being a reporting Player for the\n                    // duration of the MPD.\n                    isReportingPlayer = false;\n                });\n            });\n        }\n    }\n\n    function initialize(entry, rc) {\n        let probability;\n\n        rangeController = rc;\n\n        reportingUrl = entry.dvbReportingUrl;\n\n        // If a required attribute is missing, the Reporting descriptor may\n        // be ignored by the Player\n        if (!reportingUrl) {\n            throw new Error(\n                'required parameter missing (dvb:reportingUrl)'\n            );\n        }\n\n        // A Player's status, as a reporting Player or not, shall remain\n        // static for the duration of the MPD, regardless of MPD updates.\n        // (i.e. only calling reset (or failure) changes this state)\n        if (!reportingPlayerStatusDecided) {\n            probability = entry.dvbProbability;\n            // TS 103 285 Clause *********\n            // If the @probability attribute is set to 1000, it shall be a reporting Player.\n            // If the @probability attribute is absent it will take the default value of 1000.\n            // For any other value of the @probability attribute, it shall decide at random whether to be a\n            // reporting Player, such that the probability of being one is @probability/1000.\n            if (probability && (probability === 1000 || ((probability / 1000) >= randomNumberGenerator.random()))) {\n                isReportingPlayer = true;\n            }\n\n            reportingPlayerStatusDecided = true;\n        }\n    }\n\n    function resetInitialSettings() {\n        reportingPlayerStatusDecided = false;\n        isReportingPlayer = false;\n        reportingUrl = null;\n        rangeController = null;\n    }\n\n    function reset() {\n        if (!allowPendingRequestsToCompleteOnReset) {\n            pendingRequests.forEach(req => req.abort());\n            pendingRequests = [];\n        }\n\n        resetInitialSettings();\n    }\n\n    instance = {\n        report:     report,\n        initialize: initialize,\n        reset:      reset\n    };\n\n    setup();\n\n    return instance;\n}\n\nDVBReporting.__dashjs_factory_name = 'DVBReporting';\nexport default FactoryMaker.getClassFactory(DVBReporting); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport DVBReporting from './reporters/DVBReporting.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction ReportingFactory(config) {\n    config = config || {};\n\n    const knownReportingSchemeIdUris = {\n        'urn:dvb:dash:reporting:2014': DVBReporting\n    };\n\n    const context = this.context;\n    let instance;\n    const logger = config.debug ? config.debug.getLogger(instance) : {};\n    const metricsConstants = config.metricsConstants;\n    const mediaPlayerModel = config.mediaPlayerModel || {};\n\n    function create(entry, rangeController) {\n        let reporting;\n\n        try {\n            reporting = knownReportingSchemeIdUris[entry.schemeIdUri](context).create({\n                metricsConstants: metricsConstants,\n                mediaPlayerModel: mediaPlayerModel\n            });\n\n            reporting.initialize(entry, rangeController);\n        } catch (e) {\n            reporting = null;\n            logger.error(`ReportingFactory: could not create Reporting with schemeIdUri ${entry.schemeIdUri} (${e.message})`);\n        }\n\n        return reporting;\n    }\n\n    function register(schemeIdUri, moduleName) {\n        knownReportingSchemeIdUris[schemeIdUri] = moduleName;\n    }\n\n    function unregister(schemeIdUri) {\n        delete knownReportingSchemeIdUris[schemeIdUri];\n    }\n\n    instance = {\n        create:     create,\n        register:   register,\n        unregister: unregister\n    };\n\n    return instance;\n}\n\nReportingFactory.__dashjs_factory_name = 'ReportingFactory';\nexport default FactoryMaker.getSingletonFactory(ReportingFactory); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport ReportingFactory from '../reporting/ReportingFactory.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction ReportingController(config) {\n\n    let reporters = [];\n    let instance;\n\n    const reportingFactory = ReportingFactory(this.context).getInstance(config);\n\n    function initialize(reporting, rangeController) {\n        // \"if multiple Reporting elements are present, it is expected that\n        // the client processes one of the recognized reporting schemes.\"\n        // to ignore this, and support multiple Reporting per Metric,\n        // simply change the 'some' below to 'forEach'\n        reporting.some(r => {\n            let reporter = reportingFactory.create(r, rangeController);\n\n            if (reporter) {\n                reporters.push(reporter);\n                return true;\n            }\n        });\n    }\n\n    function reset() {\n        reporters.forEach(r => r.reset());\n        reporters = [];\n    }\n\n    function report(type, vos) {\n        reporters.forEach(r => r.report(type, vos));\n    }\n\n    instance = {\n        initialize: initialize,\n        reset:      reset,\n        report:     report\n    };\n\n    return instance;\n}\n\nReportingController.__dashjs_factory_name = 'ReportingController';\nexport default FactoryMaker.getClassFactory(ReportingController); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\n/**\n * @ignore\n */\nfunction HandlerHelpers() {\n    return {\n        reconstructFullMetricName: function (key, n, type) {\n            let mn = key;\n\n            if (n) {\n                mn += '(' + n;\n\n                if (type && type.length) {\n                    mn += ',' + type;\n                }\n\n                mn += ')';\n            }\n\n            return mn;\n        },\n\n        validateN: function (n_ms) {\n            if (!n_ms) {\n                throw new Error('missing n');\n            }\n\n            if (isNaN(n_ms)) {\n                throw new Error('n is NaN');\n            }\n\n            // n is a positive integer is defined to refer to the metric\n            // in which the buffer level is recorded every n ms.\n            if (n_ms < 0) {\n                throw new Error('n must be positive');\n            }\n\n            return n_ms;\n        }\n    };\n}\n\nHandlerHelpers.__dashjs_factory_name = 'HandlerHelpers';\nexport default FactoryMaker.getSingletonFactory(HandlerHelpers); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport HandlerHelpers from '../../utils/HandlerHelpers.js';\nimport FactoryMaker from '../../../../core/FactoryMaker.js';\n\nfunction BufferLevelHandler(config) {\n\n    config = config || {};\n    let instance,\n        reportingController,\n        n,\n        name,\n        interval,\n        lastReportedTime;\n\n    let context = this.context;\n    let handlerHelpers = HandlerHelpers(context).getInstance();\n\n    let storedVOs = [];\n\n    const metricsConstants = config.metricsConstants;\n\n    function getLowestBufferLevelVO() {\n        try {\n            return Object.keys(storedVOs).map(\n                key => storedVOs[key]\n            ).reduce(\n                (a, b) => {\n                    return (a.level < b.level) ? a : b;\n                }\n            );\n        } catch (e) {\n            return;\n        }\n    }\n\n    function intervalCallback() {\n        let vo = getLowestBufferLevelVO();\n\n        if (vo) {\n            if (lastReportedTime !== vo.t) {\n                lastReportedTime = vo.t;\n                reportingController.report(name, vo);\n            }\n        }\n    }\n\n    function initialize(basename, rc, n_ms) {\n        if (rc) {\n            // this will throw if n is invalid, to be\n            // caught by the initialize caller.\n            n = handlerHelpers.validateN(n_ms);\n            reportingController = rc;\n            name = handlerHelpers.reconstructFullMetricName(basename, n_ms);\n            interval = setInterval(intervalCallback, n);\n        }\n    }\n\n    function reset() {\n        clearInterval(interval);\n        interval = null;\n        n = 0;\n        reportingController = null;\n        lastReportedTime = null;\n    }\n\n    function handleNewMetric(metric, vo, type) {\n        if (metric === metricsConstants.BUFFER_LEVEL) {\n            storedVOs[type] = vo;\n        }\n    }\n\n    instance = {\n        initialize:         initialize,\n        reset:              reset,\n        handleNewMetric:    handleNewMetric\n    };\n\n    return instance;\n}\n\nBufferLevelHandler.__dashjs_factory_name = 'BufferLevelHandler';\nexport default FactoryMaker.getClassFactory(BufferLevelHandler); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport MetricsReportingEvents from '../../MetricsReportingEvents.js';\nimport FactoryMaker from '../../../../core/FactoryMaker.js';\n\nfunction DVBErrorsHandler(config) {\n\n    config = config || {};\n    let instance,\n        reportingController;\n\n    let eventBus = config.eventBus;\n    const metricsConstants = config.metricsConstants;\n\n    function onInitialisationComplete() {\n        // we only want to report this once per call to initialize\n        eventBus.off(\n            MetricsReportingEvents.METRICS_INITIALISATION_COMPLETE,\n            onInitialisationComplete,\n            this\n        );\n\n        // Note: A Player becoming a reporting Player is itself\n        // something which is recorded by the DVBErrors metric.\n        eventBus.trigger(MetricsReportingEvents.BECAME_REPORTING_PLAYER);\n    }\n\n    function initialize(unused, rc) {\n        if (rc) {\n            reportingController = rc;\n\n            eventBus.on(\n                MetricsReportingEvents.METRICS_INITIALISATION_COMPLETE,\n                onInitialisationComplete,\n                this\n            );\n        }\n    }\n\n    function reset() {\n        reportingController = null;\n    }\n\n    function handleNewMetric(metric, vo) {\n        // simply pass metric straight through\n        if (metric === metricsConstants.DVB_ERRORS) {\n            if (reportingController) {\n                reportingController.report(metric, vo);\n            }\n        }\n    }\n\n    instance = {\n        initialize:         initialize,\n        reset:              reset,\n        handleNewMetric:    handleNewMetric\n    };\n\n    return instance;\n}\n\nexport default FactoryMaker.getClassFactory(DVBErrorsHandler); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport HandlerHelpers from '../../utils/HandlerHelpers.js';\nimport FactoryMaker from '../../../../core/FactoryMaker.js';\n\nfunction HttpListHandler(config) {\n\n    config = config || {};\n    let instance,\n        reportingController,\n        n,\n        type,\n        name,\n        interval;\n\n    let storedVos = [];\n\n    let handlerHelpers = HandlerHelpers(this.context).getInstance();\n\n    const metricsConstants = config.metricsConstants;\n\n    function intervalCallback() {\n        var vos = storedVos;\n\n        if (vos.length) {\n            if (reportingController) {\n                reportingController.report(name, vos);\n            }\n        }\n\n        storedVos = [];\n    }\n\n    function initialize(basename, rc, n_ms, requestType) {\n        if (rc) {\n\n            // this will throw if n is invalid, to be\n            // caught by the initialize caller.\n            n = handlerHelpers.validateN(n_ms);\n\n            reportingController = rc;\n\n            if (requestType && requestType.length) {\n                type = requestType;\n            }\n\n            name = handlerHelpers.reconstructFullMetricName(\n                basename,\n                n_ms,\n                requestType\n            );\n\n            interval = setInterval(intervalCallback, n);\n        }\n    }\n\n    function reset() {\n        clearInterval(interval);\n        interval = null;\n        n = null;\n        type = null;\n        storedVos = [];\n        reportingController = null;\n    }\n\n    function handleNewMetric(metric, vo) {\n        if (metric === metricsConstants.HTTP_REQUEST) {\n            if (!type || (type === vo.type)) {\n                storedVos.push(vo);\n            }\n        }\n    }\n\n    instance = {\n        initialize:         initialize,\n        reset:              reset,\n        handleNewMetric:    handleNewMetric\n    };\n\n    return instance;\n}\n\nHttpListHandler.__dashjs_factory_name = 'HttpListHandler';\nexport default FactoryMaker.getClassFactory(HttpListHandler); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport FactoryMaker from '../../../../core/FactoryMaker.js';\n\n/**\n * @ignore\n */\nfunction GenericMetricHandler() {\n\n    let instance,\n        metricName,\n        reportingController;\n\n    function initialize(name, rc) {\n        metricName = name;\n        reportingController = rc;\n    }\n\n    function reset() {\n        reportingController = null;\n        metricName = undefined;\n    }\n\n    function handleNewMetric(metric, vo) {\n        // simply pass metric straight through\n        if (metric === metricName) {\n            if (reportingController) {\n                reportingController.report(metricName, vo);\n            }\n        }\n    }\n\n    instance = {\n        initialize: initialize,\n        reset: reset,\n        handleNewMetric: handleNewMetric\n    };\n\n    return instance;\n}\n\nGenericMetricHandler.__dashjs_factory_name = 'GenericMetricHandler';\nexport default FactoryMaker.getClassFactory(GenericMetricHandler); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport BufferLevel from './handlers/BufferLevelHandler.js';\nimport DVBErrors from './handlers/DVBErrorsHandler.js';\nimport HttpList from './handlers/HttpListHandler.js';\nimport GenericMetricHandler from './handlers/GenericMetricHandler.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction MetricsHandlerFactory(config) {\n\n    config = config || {};\n    let instance;\n    const logger = config.debug ? config.debug.getLogger(instance) : {};\n\n    // group 1: key, [group 3: n [, group 5: type]]\n    let keyRegex = /([a-zA-Z]*)(\\(([0-9]*)(\\,\\s*([a-zA-Z]*))?\\))?/;\n\n    const context = this.context;\n    let knownFactoryProducts = {\n        BufferLevel:    BufferLevel,\n        DVBErrors:      DVBErrors,\n        HttpList:       HttpList,\n        PlayList:       GenericMetricHandler,\n        RepSwitchList:  GenericMetricHandler,\n        TcpList:        GenericMetricHandler\n    };\n\n    function create(listType, reportingController) {\n        var matches = listType.match(keyRegex);\n        var handler;\n\n        if (!matches) {\n            return;\n        }\n\n        try {\n            handler = knownFactoryProducts[matches[1]](context).create({\n                eventBus: config.eventBus,\n                metricsConstants: config.metricsConstants\n            });\n\n            handler.initialize(\n                matches[1],\n                reportingController,\n                matches[3],\n                matches[5]\n            );\n        } catch (e) {\n            handler = null;\n            logger.error(`MetricsHandlerFactory: Could not create handler for type ${matches[1]} with args ${matches[3]}, ${matches[5]} (${e.message})`);\n        }\n\n        return handler;\n    }\n\n    function register(key, handler) {\n        knownFactoryProducts[key] = handler;\n    }\n\n    function unregister(key) {\n        delete knownFactoryProducts[key];\n    }\n\n    instance = {\n        create:     create,\n        register:   register,\n        unregister: unregister\n    };\n\n    return instance;\n}\n\nMetricsHandlerFactory.__dashjs_factory_name = 'MetricsHandlerFactory';\nexport default FactoryMaker.getSingletonFactory(MetricsHandlerFactory); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport MetricsHandlerFactory from '../metrics/MetricsHandlerFactory.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction MetricsHandlersController(config) {\n\n    config = config || {};\n    let handlers = [];\n\n    let instance;\n    const context = this.context;\n    const eventBus = config.eventBus;\n    const Events = config.events;\n\n    let metricsHandlerFactory = MetricsHandlerFactory(context).getInstance({\n        debug: config.debug,\n        eventBus: config.eventBus,\n        metricsConstants: config.metricsConstants\n    });\n\n    function handle(e) {\n        handlers.forEach(handler => {\n            handler.handleNewMetric(e.metric, e.value, e.mediaType);\n        });\n    }\n\n    function initialize(metrics, reportingController) {\n        metrics.split(',').forEach(\n            (m, midx, ms) => {\n                let handler;\n\n                // there is a bug in ISO23009-1 where the metrics attribute\n                // is a comma-separated list but HttpList key can contain a\n                // comma enclosed by ().\n                if ((m.indexOf('(') !== -1) && m.indexOf(')') === -1) {\n                    let nextm = ms[midx + 1];\n\n                    if (nextm &&\n                            (nextm.indexOf('(') === -1) &&\n                            (nextm.indexOf(')') !== -1)) {\n                        m += ',' + nextm;\n\n                        // delete the next metric so forEach does not visit.\n                        delete ms[midx + 1];\n                    }\n                }\n\n                handler = metricsHandlerFactory.create(\n                    m,\n                    reportingController\n                );\n\n                if (handler) {\n                    handlers.push(handler);\n                }\n            }\n        );\n\n        eventBus.on(\n            Events.METRIC_ADDED,\n            handle,\n            instance\n        );\n\n        eventBus.on(\n            Events.METRIC_UPDATED,\n            handle,\n            instance\n        );\n    }\n\n    function reset() {\n        eventBus.off(\n            Events.METRIC_ADDED,\n            handle,\n            instance\n        );\n\n        eventBus.off(\n            Events.METRIC_UPDATED,\n            handle,\n            instance\n        );\n\n        handlers.forEach(handler => handler.reset());\n\n        handlers = [];\n    }\n\n    instance = {\n        initialize: initialize,\n        reset:      reset\n    };\n\n    return instance;\n}\n\nMetricsHandlersController.__dashjs_factory_name = 'MetricsHandlersController';\nexport default FactoryMaker.getClassFactory(MetricsHandlersController); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport RangeController from './RangeController.js';\nimport ReportingController from './ReportingController.js';\nimport MetricsHandlersController from './MetricsHandlersController.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction MetricsController(config) {\n\n    config = config || {};\n    let metricsHandlersController,\n        reportingController,\n        rangeController,\n        instance;\n\n    let context = this.context;\n\n    function initialize(metricsEntry) {\n        try {\n            rangeController = RangeController(context).create({\n                mediaElement: config.mediaElement\n            });\n\n            rangeController.initialize(metricsEntry.Range);\n\n            reportingController = ReportingController(context).create({\n                debug: config.debug,\n                metricsConstants: config.metricsConstants,\n                mediaPlayerModel: config.mediaPlayerModel\n            });\n\n            reportingController.initialize(metricsEntry.Reporting, rangeController);\n\n            metricsHandlersController = MetricsHandlersController(context).create({\n                debug: config.debug,\n                eventBus: config.eventBus,\n                metricsConstants: config.metricsConstants,\n                events: config.events\n            });\n\n            metricsHandlersController.initialize(metricsEntry.metrics, reportingController);\n        } catch (e) {\n            reset();\n            throw e;\n        }\n    }\n\n    function reset() {\n        if (metricsHandlersController) {\n            metricsHandlersController.reset();\n        }\n\n        if (reportingController) {\n            reportingController.reset();\n        }\n\n        if (rangeController) {\n            rangeController.reset();\n        }\n    }\n\n    instance = {\n        initialize: initialize,\n        reset:      reset\n    };\n\n    return instance;\n}\n\nMetricsController.__dashjs_factory_name = 'MetricsController';\nexport default FactoryMaker.getClassFactory(MetricsController); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass Metrics {\n    constructor() {\n\n        this.metrics = '';\n        this.Range = [];\n        this.Reporting = [];\n    }\n}\n\nexport default Metrics;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\nclass Range {\n    constructor() {\n\n        // as defined in ISO23009-1\n        this.starttime = 0;\n        this.duration = Infinity;\n\n        // for internal use\n        this._useWallClockTime = false;\n    }\n}\n\nexport default Range;\n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n/**\n * @class\n * @ignore\n */\n\n// TS 103 285 Clause 10.12.3.3\nconst DEFAULT_DVB_PROBABILITY = 1000;\n\nclass Reporting {\n    constructor() {\n\n        this.schemeIdUri = '';\n        this.value = '';\n\n        // DVB Extensions\n        this.dvbReportingUrl = '';\n        this.dvbProbability = DEFAULT_DVB_PROBABILITY;\n    }\n}\n\nexport default Reporting;\n", "import Metrics from '../vo/Metrics.js';\nimport Range from '../vo/Range.js';\nimport Reporting from '../vo/Reporting.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction ManifestParsing (config) {\n    config = config || {};\n    let instance;\n    let adapter = config.adapter;\n    const constants = config.constants;\n\n    function getMetricsRangeStartTime(manifest, dynamic, range) {\n        let voPeriods,\n            reportingStartTime;\n        let presentationStartTime = 0;\n\n        if (dynamic) {\n            // For services with MPD@type='dynamic', the start time is\n            // indicated in wall clock time by adding the value of this\n            // attribute to the value of the MPD@availabilityStartTime\n            // attribute.\n            presentationStartTime = adapter.getAvailabilityStartTime(manifest) / 1000;\n        } else {\n            // For services with MPD@type='static', the start time is indicated\n            // in Media Presentation time and is relative to the PeriodStart\n            // time of the first Period in this MPD.\n            voPeriods = adapter.getRegularPeriods(manifest);\n\n            if (voPeriods.length) {\n                presentationStartTime = voPeriods[0].start;\n            }\n        }\n\n        // When not present, DASH Metrics collection is\n        // requested from the beginning of content\n        // consumption.\n        reportingStartTime = presentationStartTime;\n\n        if (range && range.hasOwnProperty(constants.START_TIME)) {\n            reportingStartTime += range.starttime;\n        }\n\n        return reportingStartTime;\n    }\n\n    function getMetrics(manifest) {\n        let metrics = [];\n\n        if (manifest && manifest.Metrics) {\n            manifest.Metrics.forEach(metric => {\n                var metricEntry = new Metrics();\n                var isDynamic = adapter.getIsDynamic(manifest);\n\n                if (metric.hasOwnProperty('metrics')) {\n                    metricEntry.metrics = metric.metrics;\n                } else {\n                    return;\n                }\n\n                if (metric.Range) {\n                    metric.Range.forEach(range => {\n                        var rangeEntry = new Range();\n\n                        rangeEntry.starttime =\n                            getMetricsRangeStartTime(manifest, isDynamic, range);\n\n                        if (range.hasOwnProperty('duration')) {\n                            rangeEntry.duration = range.duration;\n                        } else {\n                            // if not present, the value is identical to the\n                            // Media Presentation duration.\n                            rangeEntry.duration = adapter.getDuration(manifest);\n                        }\n\n                        rangeEntry._useWallClockTime = isDynamic;\n\n                        metricEntry.Range.push(rangeEntry);\n                    });\n                }\n\n                if (metric.Reporting) {\n                    metric.Reporting.forEach(reporting => {\n                        var reportingEntry = new Reporting();\n\n                        if (reporting.hasOwnProperty(constants.SCHEME_ID_URI)) {\n                            reportingEntry.schemeIdUri = reporting.schemeIdUri;\n                        } else {\n                            // Invalid Reporting. schemeIdUri must be set. Ignore.\n                            return;\n                        }\n\n                        if (reporting.hasOwnProperty('value')) {\n                            reportingEntry.value = reporting.value;\n                        }\n\n                        if (reporting.hasOwnProperty(constants.DVB_REPORTING_URL)) {\n                            reportingEntry.dvbReportingUrl = reporting[constants.DVB_REPORTING_URL];\n                        }\n\n                        if (reporting.hasOwnProperty(constants.DVB_PROBABILITY)) {\n                            reportingEntry.dvbProbability = reporting[constants.DVB_PROBABILITY];\n                        }\n\n                        metricEntry.Reporting.push(reportingEntry);\n                    });\n                } else {\n                    // Invalid Metrics. At least one reporting must be present. Ignore\n                    return;\n                }\n\n                metrics.push(metricEntry);\n            });\n        }\n\n        return metrics;\n    }\n\n    instance = {\n        getMetrics: getMetrics\n    };\n\n    return instance;\n}\n\nManifestParsing.__dashjs_factory_name = 'ManifestParsing';\nexport default FactoryMaker.getSingletonFactory(ManifestParsing); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport MetricsController from './MetricsController.js';\nimport ManifestParsing from '../utils/ManifestParsing.js';\nimport MetricsReportingEvents from '../MetricsReportingEvents.js';\nimport FactoryMaker from '../../../core/FactoryMaker.js';\n\nfunction MetricsCollectionController(config) {\n\n    config = config || {};\n    let instance;\n    let metricsControllers = {};\n    let context = this.context;\n    let eventBus = config.eventBus;\n    const events = config.events;\n\n    function update(e) {\n        if (e.error) {\n            return;\n        }\n\n        // start by assuming all existing controllers need removing\n        let controllersToRemove = Object.keys(metricsControllers);\n\n        const metrics = ManifestParsing(context).getInstance({\n            adapter: config.adapter,\n            constants: config.constants\n        }).getMetrics(e.manifest);\n\n        metrics.forEach(m => {\n            const key = JSON.stringify(m);\n\n            if (!metricsControllers.hasOwnProperty(key)) {\n                try {\n                    let controller = MetricsController(context).create(config);\n                    controller.initialize(m);\n                    metricsControllers[key] = controller;\n                } catch (e) {\n                    // fail quietly\n                }\n            } else {\n                // we still need this controller - delete from removal list\n                controllersToRemove.splice(key, 1);\n            }\n        });\n\n        // now remove the unwanted controllers\n        controllersToRemove.forEach(c => {\n            metricsControllers[c].reset();\n            delete metricsControllers[c];\n        });\n\n        eventBus.trigger(MetricsReportingEvents.METRICS_INITIALISATION_COMPLETE);\n    }\n\n    function resetMetricsControllers() {\n        Object.keys(metricsControllers).forEach(key => {\n            metricsControllers[key].reset();\n        });\n\n        metricsControllers = {};\n    }\n\n    function setup() {\n        eventBus.on(events.MANIFEST_UPDATED, update, instance);\n        eventBus.on(events.STREAM_TEARDOWN_COMPLETE, resetMetricsControllers, instance);\n    }\n\n    function reset() {\n        eventBus.off(events.MANIFEST_UPDATED, update, instance);\n        eventBus.off(events.STREAM_TEARDOWN_COMPLETE, resetMetricsControllers, instance);\n    }\n\n    instance = {\n        reset: reset\n    };\n\n    setup();\n    return instance;\n}\n\nMetricsCollectionController.__dashjs_factory_name = 'MetricsCollectionController';\nexport default FactoryMaker.getClassFactory(MetricsCollectionController); \n", "/**\n * The copyright in this software is being made available under the BSD License,\n * included below. This software may be subject to other third party and contributor\n * rights, including patent rights, and no such rights are granted under this license.\n *\n * Copyright (c) 2013, Dash Industry Forum.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *  * Redistributions of source code must retain the above copyright notice, this\n *  list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *  this list of conditions and the following disclaimer in the documentation and/or\n *  other materials provided with the distribution.\n *  * Neither the name of Dash Industry Forum nor the names of its\n *  contributors may be used to endorse or promote products derived from this software\n *  without specific prior written permission.\n *\n *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS AS IS AND ANY\n *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n *  IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,\n *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n *  NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n *  WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n *  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n *  POSSIBILITY OF SUCH DAMAGE.\n */\n\nimport DVBErrorsTranslator from './utils/DVBErrorsTranslator.js';\nimport MetricsReportingEvents from './MetricsReportingEvents.js';\nimport MetricsCollectionController from './controllers/MetricsCollectionController.js';\nimport MetricsHandlerFactory from './metrics/MetricsHandlerFactory.js';\nimport ReportingFactory from './reporting/ReportingFactory.js';\n\nfunction MetricsReporting() {\n\n    let context = this.context;\n    let instance,\n        dvbErrorsTranslator;\n\n    /**\n     * Create a MetricsCollectionController, and a DVBErrorsTranslator\n     * @param {Object} config - dependancies from owner\n     * @return {MetricsCollectionController} Metrics Collection Controller\n     */\n    function createMetricsReporting(config) {\n        dvbErrorsTranslator = DVBErrorsTranslator(context).getInstance({\n            eventBus: config.eventBus,\n            dashMetrics: config.dashMetrics,\n            metricsConstants: config.metricsConstants,\n            events: config.events\n        });\n        dvbErrorsTranslator.initialize();\n        return MetricsCollectionController(context).create(config);\n    }\n\n    /**\n     * Get the ReportingFactory to allow new reporters to be registered\n     * @return {ReportingFactory} Reporting Factory\n     */\n    function getReportingFactory() {\n        return ReportingFactory(context).getInstance();\n    }\n\n    /**\n     * Get the MetricsHandlerFactory to allow new handlers to be registered\n     * @return {MetricsHandlerFactory} Metrics Handler Factory\n     */\n    function getMetricsHandlerFactory() {\n        return MetricsHandlerFactory(context).getInstance();\n    }\n\n    instance = {\n        createMetricsReporting:     createMetricsReporting,\n        getReportingFactory:        getReportingFactory,\n        getMetricsHandlerFactory:   getMetricsHandlerFactory\n    };\n\n    return instance;\n}\n\nMetricsReporting.__dashjs_factory_name = 'MetricsReporting';\nconst factory = dashjs.FactoryMaker.getClassFactory(MetricsReporting); \nfactory.events = MetricsReportingEvents;\ndashjs.FactoryMaker.updateClassFactory(MetricsReporting.__dashjs_factory_name, factory); \nexport default factory;\n"], "names": ["assertPath", "path", "TypeError", "JSON", "stringify", "normalizeStringPosix", "allowAboveRoot", "code", "res", "lastSegmentLength", "lastSlash", "dots", "i", "length", "charCodeAt", "lastSlashIndex", "lastIndexOf", "slice", "posix", "resolve", "cwd", "<PERSON><PERSON><PERSON>", "resolvedAbsolute", "arguments", "undefined", "process", "normalize", "isAbsolute", "trailingSeparator", "join", "joined", "arg", "relative", "from", "to", "fromStart", "fromEnd", "fromLen", "toStart", "toLen", "lastCommonSep", "fromCode", "out", "_makeLong", "dirname", "hasRoot", "end", "matchedSlash", "basename", "ext", "start", "extIdx", "firstNonSlashEnd", "extname", "startDot", "startPart", "preDotState", "format", "pathObject", "sep", "dir", "root", "base", "name", "_format", "parse", "ret", "delimiter", "win32", "module", "exports", "window", "FUNC_TYPE", "UNDEF_TYPE", "OBJ_TYPE", "STR_TYPE", "MAJOR", "MODEL", "NAME", "TYPE", "VENDOR", "VERSION", "ARCHITECTURE", "CONSOLE", "MOBILE", "TABLET", "SMARTTV", "WEARABLE", "EMBEDDED", "AMAZON", "APPLE", "ASUS", "BLACKBERRY", "BROWSER", "CHROME", "FIREFOX", "GOOGLE", "HUAWEI", "LG", "MICROSOFT", "MOTOROLA", "OPERA", "SAMSUNG", "SHARP", "SONY", "XIAOMI", "ZEBRA", "FACEBOOK", "CHROMIUM_OS", "MAC_OS", "enumerize", "arr", "enums", "toUpperCase", "has", "str1", "str2", "lowerize", "indexOf", "str", "toLowerCase", "trim", "len", "replace", "substring", "rgxMapper", "ua", "arrays", "j", "k", "p", "q", "matches", "match", "regex", "props", "exec", "this", "call", "test", "strMapper", "map", "windowsVersionMap", "regexes", "browser", "cpu", "device", "engine", "EDGE", "os", "<PERSON><PERSON><PERSON><PERSON>", "extensions", "getResult", "_navigator", "navigator", "_ua", "userAgent", "_uach", "userAgentData", "_rgxmap", "mergedRegexes", "concat", "extend", "_isSelfNav", "<PERSON><PERSON><PERSON><PERSON>", "version", "_browser", "split", "brave", "isBrave", "getCPU", "_cpu", "getDevice", "_device", "mobile", "standalone", "maxTouchPoints", "getEngine", "_engine", "getOS", "_os", "platform", "getUA", "setUA", "CPU", "DEVICE", "ENGINE", "OS", "define", "$", "j<PERSON><PERSON><PERSON>", "Zepto", "parser", "get", "set", "result", "prop", "Debug", "config", "context", "eventBus", "EventBus", "getInstance", "settings", "logFn", "instance", "showLogTimestamp", "showCalleeName", "startTime", "getLogFn", "fn", "bind", "console", "log", "fatal", "_len", "params", "Array", "_key", "doLog", "error", "_len2", "_key2", "warn", "_len3", "_key3", "info", "_len4", "_key4", "debug", "_len5", "_key5", "level", "_this", "message", "logTime", "Date", "getTime", "getClassName", "getType", "_len6", "_key6", "apply", "for<PERSON>ach", "item", "logLevel", "dispatchEvent", "trigger", "Events", "LOG", "<PERSON><PERSON><PERSON><PERSON>", "setLogTimestampVisible", "value", "setCalleeNameVisible", "__dashjs_factory_name", "factory", "FactoryMaker", "getSingletonFactory", "LOG_LEVEL_NONE", "LOG_LEVEL_FATAL", "LOG_LEVEL_ERROR", "LOG_LEVEL_WARNING", "LOG_LEVEL_INFO", "LOG_LEVEL_DEBUG", "updateSingletonFactory", "handlers", "_commonOn", "type", "listener", "scope", "options", "executeOnlyOnce", "Error", "priority", "getHandlerIdx", "handler", "callback", "getStreamId", "streamId", "mediaType", "mode", "some", "idx", "splice", "push", "off", "index", "on", "once", "payload", "filters", "hasOwnProperty", "handlersToRemove", "filter", "MediaPlayerEvents", "EVENT_MODE_ON_RECEIVE", "reset", "EVENT_PRIORITY_LOW", "EVENT_PRIORITY_HIGH", "singletonContexts", "singletonFactories", "classFactories", "getSingletonInstance", "className", "obj", "getFactoryByName", "factoriesArray", "updateFactory", "merge", "classConstructor", "args", "classInstance", "extensionObject", "extension", "override", "parent", "childInstance", "setSingletonInstance", "deleteSingletonInstances", "x", "getSingletonFactoryByName", "getClassFactory", "create", "getClassFactoryByName", "updateClassFactory", "Settings", "DISPATCH_KEY_MAP", "SETTING_UPDATED_LIVE_DELAY", "SETTING_UPDATED_LIVE_DELAY_FRAGMENT_COUNT", "SETTING_UPDATED_CATCHUP_ENABLED", "SETTING_UPDATED_PLAYBACK_RATE_MIN", "SETTING_UPDATED_PLAYBACK_RATE_MAX", "SETTING_UPDATED_ABR_ACTIVE_RULES", "SETTING_UPDATED_MAX_BITRATE", "SETTING_UPDATED_MIN_BITRATE", "defaultSettings", "streaming", "abandonLoadTimeout", "wallclockTimeUpdateInterval", "manifestUpdateRetryInterval", "liveUpdateTimeThresholdInMilliseconds", "cacheInitSegments", "applyServiceDescription", "applyProducerReferenceTime", "applyContentSteering", "enableManifestDurationMismatchFix", "parseInbandPrft", "enableManifestTimescaleMismatchFix", "capabilities", "filterUnsupportedEssentialProperties", "supportedEssentialProperties", "schemeIdUri", "Constants", "FONT_DOWNLOAD_DVB_SCHEME", "COLOUR_PRIMARIES_SCHEME_ID_URI", "URL_QUERY_INFO_SCHEME", "EXT_URL_QUERY_INFO_SCHEME", "MATRIX_COEFFICIENTS_SCHEME_ID_URI", "TRANSFER_CHARACTERISTICS_SCHEME_ID_URI", "THUMBNAILS_SCHEME_ID_URIS", "ep", "useMediaCapabilitiesApi", "filterVideoColorimetryEssentialProperties", "filterHDRMetadataFormatEssentialProperties", "events", "eventControllerRefreshDelay", "deleteEventMessageDataTimeout", "timeShiftBuffer", "calcFromSegmentTimeline", "fallbackToSegmentTimeline", "metrics", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "delay", "liveDelayFragmentCount", "NaN", "liveDelay", "useSuggestedPresentationDelay", "protection", "keepProtectionMediaKeys", "ignoreEmeEncryptedEvent", "detectPlayreadyMessageFormat", "ignoreKeyStatuses", "buffer", "enableSeekDecorrelationFix", "fastSwitchEnabled", "flushBufferAtTrackSwitch", "reuseExistingSourceBuffers", "bufferPruningInterval", "bufferToKeep", "bufferTimeAtTopQuality", "bufferTimeAtTopQualityLongForm", "initialBufferLevel", "bufferTimeDefault", "longFormContentDurationThreshold", "stallThreshold", "lowLatencyStallThreshold", "useAppendWindow", "setStallState", "avoidCurrentTimeRangePruning", "useChangeType", "mediaSourceDurationInfinity", "resetSourceBuffersForTrackSwitch", "syntheticStallEvents", "enabled", "ignoreReadyState", "gaps", "jumpGaps", "jumpLargeGaps", "smallGapLimit", "threshold", "enableSeekFix", "enableStallFix", "stallSeek", "utcSynchronization", "useManifestDateHeaderTimeSource", "backgroundAttempts", "timeBetweenSyncAttempts", "maximumTimeBetweenSyncAttempts", "minimumTimeBetweenSyncAttempts", "timeBetweenSyncAttemptsAdjustmentFactor", "maximumAllowedDrift", "enableBackgroundSyncAfterSegmentDownloadError", "defaultTimingSource", "scheme", "scheduling", "defaultTimeout", "lowLatencyTimeout", "scheduleWhilePaused", "text", "defaultEnabled", "dispatchForManualRendering", "extendSegmentedCues", "imsc", "displayForcedOnlyMode", "enableRollUp", "webvtt", "customRenderingEnabled", "liveCatchup", "maxDrift", "playbackRate", "min", "max", "playbackBufferMin", "LIVE_CATCHUP_MODE_DEFAULT", "lastBitrateCachingInfo", "ttl", "lastMediaSettingsCachingInfo", "saveLastMediaSettingsForCurrentStreamingSession", "cacheLoadThresholds", "video", "audio", "trackSwitchMode", "TRACK_SWITCH_MODE_ALWAYS_REPLACE", "TRACK_SWITCH_MODE_NEVER_REPLACE", "ignoreSelectionPriority", "prioritizeRoleMain", "assumeDefaultRoleAsMain", "selectionModeForInitialTrack", "TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY", "fragmentRequestTimeout", "fragmentRequestProgressTimeout", "manifestRequestTimeout", "retryIntervals", "HTTPRequest", "MPD_TYPE", "XLINK_EXPANSION_TYPE", "MEDIA_SEGMENT_TYPE", "INIT_SEGMENT_TYPE", "BITSTREAM_SWITCHING_SEGMENT_TYPE", "INDEX_SEGMENT_TYPE", "MSS_FRAGMENT_INFO_SEGMENT_TYPE", "LICENSE", "OTHER_TYPE", "lowLatencyReductionFactor", "retryAttempts", "lowLatencyMultiplyFactor", "abr", "limitBitrateByPortal", "usePixelRatioInLimitBitrateByPortal", "enableSupplementalPropertyAdaptationSetSwitching", "rules", "throughputRule", "active", "bolaRule", "insufficientBufferRule", "parameters", "throughputSafetyFactor", "segmentIgnoreCount", "switchHistoryRule", "sampleSize", "switchPercentageThreshold", "droppedFramesRule", "minimumSampleSize", "droppedFramesPercentageThreshold", "abandonRequestsRule", "abandonDurationMultiplier", "minSegmentDownloadTimeThresholdInMs", "minThroughputSamplesThreshold", "l2ARule", "loLPRule", "throughput", "averageCalculationMode", "THROUGHPUT_CALCULATION_MODES", "EWMA", "lowLatencyDownloadTimeCalculationMode", "LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE", "MOOF_PARSING", "useResourceTimingApi", "useNetworkInformationApi", "xhr", "fetch", "useDeadTimeLatency", "bandwidthSafetyFactor", "sampleSettings", "live", "vod", "enableSampleSizeAdjustment", "decreaseScale", "increaseScale", "maxMeasurementsToKeep", "averageLatencySampleAmount", "ewma", "throughputSlowHalfLifeSeconds", "throughputFastHalfLifeSeconds", "latencySlowHalfLifeCount", "latencyFastHalfLifeCount", "weightDownloadTimeMultiplicationFactor", "maxBitrate", "minBitrate", "initialBitrate", "autoSwitchBitrate", "cmcd", "applyParametersFromMpd", "sid", "cid", "rtp", "rtpSafetyFactor", "CMCD_MODE_QUERY", "<PERSON><PERSON><PERSON><PERSON>", "CMCD_AVAILABLE_KEYS", "includeInRequests", "cmsd", "applyMb", "etpWeightRatio", "defaultSchemeIdUri", "viewpoint", "audioChannelConfiguration", "role", "accessibility", "errors", "recoverAttempts", "mediaErrorDecode", "Utils", "clone", "mixinSettings", "source", "dest", "n", "RegExp", "update", "settingsObj", "mixin", "copy", "s", "empty", "src", "r", "l", "addAdditionalQueryParameterToUrl", "url", "updatedUrl", "_ref", "key", "separator", "includes", "encodeURIComponent", "e", "removeQueryParameterFromUrl", "queryParameter", "parsedUrl", "URL", "URLSearchParams", "search", "size", "delete", "queryString", "entries", "_ref2", "baseUrl", "origin", "pathname", "parseHttpHeaders", "headerStr", "headers", "headerPairs", "ilen", "headerPair", "parseQueryParams", "queryParamString", "searchParams", "decodeURIComponent", "generateUuid", "dt", "c", "Math", "random", "floor", "toString", "generateHashCode", "string", "hash", "getRelativeUrl", "originalUrl", "targetUrl", "original", "target", "protocol", "relativePath", "substr", "startIndexOffset", "getHostFromUrl", "urlString", "host", "parseUserAgent", "uaString", "stringHasProtocol", "bufferSourceToDataView", "bufferSource", "toDataView", "DataView", "bufferSourceToInt8", "Uint8Array", "uint8ArrayToString", "uint8Array", "TextDecoder", "decode", "bufferSourceToHex", "data", "hex", "Type", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bytesPerElement", "BYTES_PER_ELEMENT", "dataEnd", "byteOffset", "byteLength", "rawStart", "Infinity", "view", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getCodecFamily", "codecString", "profile", "_getCodecParts", "CODEC_FAMILIES", "MP3", "AAC", "AC3", "EC3", "DTSX", "DTSC", "AVC", "HEVC", "rest", "CoreEvents", "EventsBase", "constructor", "super", "ATTEMPT_BACKGROUND_SYNC", "BUFFERING_COMPLETED", "BUFFER_CLEARED", "BYTES_APPENDED_END_FRAGMENT", "BUFFER_REPLACEMENT_STARTED", "CHECK_FOR_EXISTENCE_COMPLETED", "CMSD_STATIC_HEADER", "CURRENT_TRACK_CHANGED", "DATA_UPDATE_COMPLETED", "INBAND_EVENTS", "INITIAL_STREAM_SWITCH", "INIT_FRAGMENT_LOADED", "INIT_FRAGMENT_NEEDED", "INTERNAL_MANIFEST_LOADED", "ORIGINAL_MANIFEST_LOADED", "LOADING_COMPLETED", "LOADING_PROGRESS", "LOADING_DATA_PROGRESS", "LOADING_ABANDONED", "MANIFEST_UPDATED", "MEDIA_FRAGMENT_LOADED", "MEDIA_FRAGMENT_NEEDED", "MEDIAINFO_UPDATED", "QUOTA_EXCEEDED", "SEGMENT_LOCATION_BLACKLIST_ADD", "SEGMENT_LOCATION_BLACKLIST_CHANGED", "SERVICE_LOCATION_BASE_URL_BLACKLIST_ADD", "SERVICE_LOCATION_BASE_URL_BLACKLIST_CHANGED", "SERVICE_LOCATION_LOCATION_BLACKLIST_ADD", "SERVICE_LOCATION_LOCATION_BLACKLIST_CHANGED", "SET_FRAGMENTED_TEXT_AFTER_DISABLED", "SET_NON_FRAGMENTED_TEXT", "SOURCE_BUFFER_ERROR", "STREAMS_COMPOSED", "STREAM_BUFFERING_COMPLETED", "STREAM_REQUESTING_COMPLETED", "TEXT_TRACKS_QUEUE_INITIALIZED", "TIME_SYNCHRONIZATION_COMPLETED", "UPDATE_TIME_SYNC_OFFSET", "URL_RESOLUTION_FAILED", "VIDEO_CHUNK_RECEIVED", "WALLCLOCK_TIME_UPDATED", "XLINK_ELEMENT_LOADED", "XLINK_READY", "SEEK_TARGET", "publicOnly", "evt", "AST_IN_FUTURE", "BASE_URLS_UPDATED", "BUFFER_EMPTY", "BUFFER_LOADED", "BUFFER_LEVEL_STATE_CHANGED", "BUFFER_LEVEL_UPDATED", "DVB_FONT_DOWNLOAD_ADDED", "DVB_FONT_DOWNLOAD_COMPLETE", "DVB_FONT_DOWNLOAD_FAILED", "DYNAMIC_TO_STATIC", "ERROR", "FRAGMENT_LOADING_COMPLETED", "FRAGMENT_LOADING_PROGRESS", "FRAGMENT_LOADING_STARTED", "FRAGMENT_LOADING_ABANDONED", "MANIFEST_LOADING_STARTED", "MANIFEST_LOADING_FINISHED", "MANIFEST_LOADED", "METRICS_CHANGED", "METRIC_CHANGED", "METRIC_ADDED", "METRIC_UPDATED", "PERIOD_SWITCH_STARTED", "PERIOD_SWITCH_COMPLETED", "QUALITY_CHANGE_REQUESTED", "QUALITY_CHANGE_RENDERED", "NEW_TRACK_SELECTED", "TRACK_CHANGE_RENDERED", "STREAM_INITIALIZING", "STREAM_UPDATED", "STREAM_ACTIVATED", "STREAM_DEACTIVATED", "STREAM_INITIALIZED", "STREAM_TEARDOWN_COMPLETE", "TEXT_TRACKS_ADDED", "TEXT_TRACK_ADDED", "CUE_ENTER", "CUE_EXIT", "THROUGHPUT_MEASUREMENT_STORED", "TTML_PARSED", "TTML_TO_PARSE", "CAPTION_RENDERED", "CAPTION_CONTAINER_RESIZE", "CAN_PLAY", "CAN_PLAY_THROUGH", "PLAYBACK_ENDED", "PLAYBACK_ERROR", "PLAYBACK_INITIALIZED", "PLAYBACK_NOT_ALLOWED", "PLAYBACK_METADATA_LOADED", "PLAYBACK_LOADED_DATA", "PLAYBACK_PAUSED", "PLAYBACK_PLAYING", "PLAYBACK_PROGRESS", "PLAYBACK_RATE_CHANGED", "PLAYBACK_SEEKED", "PLAYBACK_SEEKING", "PLAYBACK_STALLED", "PLAYBACK_STARTED", "PLAYBACK_TIME_UPDATED", "PLAYBACK_VOLUME_CHANGED", "PLAYBACK_WAITING", "MANIFEST_VALIDITY_CHANGED", "EVENT_MODE_ON_START", "CONFORMANCE_VIOLATION", "REPRESENTATION_SWITCH", "ADAPTATION_SET_REMOVED_NO_CAPABILITIES", "CONTENT_STEERING_REQUEST_COMPLETED", "INBAND_PRFT", "MANAGED_MEDIA_SOURCE_START_STREAMING", "MANAGED_MEDIA_SOURCE_END_STREAMING", "mediaPlayerEvents", "STREAM", "VIDEO", "AUDIO", "TEXT", "MUXED", "IMAGE", "STPP", "TTML", "VTT", "WVTT", "CONTENT_STEERING", "LIVE_CATCHUP_MODE_LOLP", "MOVING_AVERAGE_SLIDING_WINDOW", "MOVING_AVERAGE_EWMA", "BAD_ARGUMENT_ERROR", "MISSING_CONFIG_ERROR", "TRACK_SELECTION_MODE_FIRST_TRACK", "TRACK_SELECTION_MODE_HIGHEST_BITRATE", "TRACK_SELECTION_MODE_WIDEST_RANGE", "CMCD_QUERY_KEY", "CMCD_MODE_HEADER", "CMCD_V2_AVAILABLE_KEYS", "CMCD_AVAILABLE_REQUESTS", "INITIALIZE", "TEXT_SHOWING", "TEXT_HIDDEN", "TEXT_DISABLED", "ACCESSIBILITY_CEA608_SCHEME", "CC1", "CC3", "UTF8", "SCHEME_ID_URI", "START_TIME", "SERVICE_DESCRIPTION_DVB_LL_SCHEME", "SUPPLEMENTAL_PROPERTY_DVB_LL_SCHEME", "CTA_5004_2023_SCHEME", "HDR_METADATA_FORMAT_SCHEME_ID_URI", "HDR_METADATA_FORMAT_VALUES", "ST2094_10", "SL_HDR2", "ST2094_40", "MEDIA_CAPABILITIES_API", "COLORGAMUT", "SRGB", "P3", "REC2020", "TRANSFERFUNCTION", "PQ", "HLG", "HDR_METADATATYPE", "SMPTE_ST_2094_10", "SLHDR2", "SMPTE_ST_2094_40", "XML", "ARRAY_BUFFER", "DVB_REPORTING_URL", "DVB_PROBABILITY", "OFF_MIMETYPE", "WOFF_MIMETYPE", "VIDEO_ELEMENT_READY_STATES", "HAVE_NOTHING", "HAVE_METADATA", "HAVE_CURRENT_DATA", "HAVE_FUTURE_DATA", "HAVE_ENOUGH_DATA", "FILE_LOADER_TYPES", "FETCH", "XHR", "THROUGHPUT_TYPES", "LATENCY", "BANDWIDTH", "ZLEMA", "ARITHMETIC_MEAN", "BYTE_SIZE_WEIGHTED_ARITHMETIC_MEAN", "DATE_WEIGHTED_ARITHMETIC_MEAN", "HARMONIC_MEAN", "BYTE_SIZE_WEIGHTED_HARMONIC_MEAN", "DATE_WEIGHTED_HARMONIC_MEAN", "DOWNLOADED_DATA", "AAST", "RULES_TYPES", "QUALITY_SWITCH_RULES", "ABANDON_FRAGMENT_RULES", "BOLA_RULE", "THROUGHPUT_RULE", "INSUFFICIENT_BUFFER_RULE", "SWITCH_HISTORY_RULE", "DROPPED_FRAMES_RULE", "LEARN_TO_ADAPT_RULE", "LOL_PLUS_RULE", "ABANDON_REQUEST_RULE", "ID3_SCHEME_ID_URI", "COMMON_ACCESS_TOKEN_HEADER", "DASH_ROLE_SCHEME_ID", "MetricsReportingEvents", "METRICS_INITIALISATION_COMPLETE", "BECAME_REPORTING_PLAYER", "CMCD_DATA_GENERATED", "metricsReportingEvents", "CustomParametersModel", "utcTimingSources", "xhrWithCredentials", "requestInterceptors", "responseInterceptors", "licenseRequestFilters", "licenseResponseFilters", "customCapabilitiesFilters", "customInitialTrackSelectionFunction", "customAbrRules", "_resetInitialSettings", "_unregisterFilter", "_findAbrCustomRuleIndex", "rulename", "addUTCTimingSource", "removeUTCTimingSource", "vo", "UTCTiming", "checkParameterType", "addAbrCustomRule", "rule", "addRequestInterceptor", "interceptor", "addResponseInterceptor", "clearDefaultUTCTimingSources", "getAbrCustomRules", "getCustomCapabilitiesFilters", "getCustomInitialTrackSelectionFunction", "getLicenseRequestFilters", "getLicenseResponseFilters", "getRequestInterceptors", "getResponseInterceptors", "getUTCTimingSources", "getXHRWithCredentialsForType", "useCreds", "default", "registerCustomCapabilitiesFilter", "registerLicenseRequestFilter", "registerLicenseResponseFilter", "removeAbrCustomRule", "removeAllAbrCustomRule", "removeRequestInterceptor", "removeResponseInterceptor", "resetCustomInitialTrackSelectionFunction", "restoreDefaultUTCTimingSources", "defaultUtcTimingSource", "setConfig", "setCustomInitialTrackSelectionFunction", "customFunc", "setXHRWithCredentialsForType", "Object", "keys", "unregisterCustomCapabilitiesFilter", "unregisterLicenseRequestFilter", "unregisterLicenseResponseFilter", "CustomTimeRanges", "customTimeRangeArray", "add", "mergeRanges", "clear", "remove", "rangeIndex1", "rangeIndex2", "range1", "range2", "checkInteger", "parameter", "isNaN", "checkRange", "checkIsVideoOrAudioType", "tcpid", "<PERSON><PERSON><PERSON>", "range", "trequest", "tresponse", "responsecode", "interval", "trace", "_stream", "_tfinish", "_mediaduration", "_responseHeaders", "_serviceLocation", "_fileLoaderType", "_resourceTimingValues", "HTTPRequestTrace", "d", "b", "GET", "HEAD", "DVB_REPORTING_TYPE", "CONTENT_STEERING_TYPE", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "amdO", "definition", "o", "defineProperty", "enumerable", "prototype", "Symbol", "toStringTag", "DVBErrors", "mpdurl", "errorcode", "terror", "ipaddress", "servicelocation", "SSL_CONNECTION_FAILED_PREFIX", "DNS_RESOLUTION_FAILED", "HOST_UNREACHABLE", "CONNECTION_REFUSED", "CONNECTION_ERROR", "CORRUPT_MEDIA_ISOBMFF", "CORRUPT_MEDIA_OTHER", "BASE_URL_CHANGED", "BECAME_REPORTER", "DVBErrorsTranslator", "mpd", "dashMetrics", "metricsConstants", "report", "addDVBErrors", "onManifestUpdate", "manifest", "onServiceLocationChanged", "entry", "onBecameReporter", "onMetricEvent", "metric", "HTTP_REQUEST", "onPlaybackError", "MediaError", "MEDIA_ERR_NETWORK", "MEDIA_ERR_DECODE", "initialize", "RangeController", "ranges", "useWallClockTime", "mediaElement", "rs", "starttime", "duration", "_useWallClockTime", "isEnabled", "time", "numRanges", "currentTime", "MetricSerialiser", "serialise", "pairs", "isArray", "v", "isBuiltIn", "toISOString", "round", "RNG", "randomNumbers", "crypto", "msCrypto", "ArrayType", "Uint32Array", "MAX_VALUE", "pow", "getRandomValues", "DVBReporting", "metricSerialiser", "customParametersModel", "randomNumberGenerator", "reportingPlayerStatusDecided", "isReportingPlayer", "reportingUrl", "rangeController", "pendingRequests", "resetInitialSettings", "vos", "DVB_ERRORS", "successCB", "failureCB", "req", "XMLHttpRequest", "withCredentials", "HTTP_REQUEST_DVB_REPORTING_TYPE", "oncomplete", "reqIndex", "status", "open", "onloadend", "onerror", "send", "doGetRequest", "rc", "probability", "dvbReportingUrl", "dvbProbability", "ReportingFactory", "knownReportingSchemeIdUris", "logger", "mediaPlayerModel", "reporting", "register", "moduleName", "unregister", "ReportingController", "reporters", "reportingFactory", "reporter", "HandlerHelpers", "reconstructFullMetricName", "mn", "validateN", "n_ms", "BufferLevelHandler", "reportingController", "lastReportedTime", "handlerHelpers", "storedVOs", "intervalCallback", "reduce", "a", "getLowestBufferLevelVO", "t", "setInterval", "clearInterval", "handleNewMetric", "BUFFER_LEVEL", "onInitialisationComplete", "unused", "HttpListHandler", "storedVos", "requestType", "GenericMetricHandler", "metricName", "MetricsHandlerFactory", "keyRegex", "knownFactoryProducts", "BufferLevel", "HttpList", "PlayList", "RepSwitchList", "TcpList", "listType", "MetricsHandlersController", "metricsHandlerFactory", "handle", "m", "midx", "ms", "nextm", "MetricsController", "metricsHandlersController", "metricsEntry", "Range", "Reporting", "ManifestParsing", "adapter", "constants", "getMetrics", "Metrics", "metricEntry", "isDynamic", "getIsDynamic", "rangeEntry", "dynamic", "voPeriods", "reportingStartTime", "presentationStartTime", "getAvailabilityStartTime", "getRegularPeriods", "getMetricsRangeStartTime", "getDuration", "reportingEntry", "MetricsCollectionController", "metricsControllers", "controllersToRemove", "controller", "resetMetricsControllers", "MetricsReporting", "dvbErrorsTranslator", "createMetricsReporting", "getReportingFactory", "getMetricsHandlerFactory", "dashjs"], "sourceRoot": ""}