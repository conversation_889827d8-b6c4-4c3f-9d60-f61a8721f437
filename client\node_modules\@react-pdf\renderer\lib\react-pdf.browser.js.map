{"version": 3, "file": "react-pdf.browser.js", "sources": ["../src/utils.js", "../src/renderer.js", "../src/index.js", "../src/dom/usePDF.js", "../src/dom/PDFViewer.js", "../src/dom/BlobProvider.js", "../src/dom/PDFDownloadLink.js", "../src/dom/index.js"], "sourcesContent": ["export const omitNils = (object) =>\n  Object.fromEntries(\n    Object.entries(object).filter(([, value]) => value !== undefined),\n  );\n", "import Reconciler from '@react-pdf/reconciler';\n\nconst createInstance = (type, { style, children, ...props }) => ({\n  type,\n  box: {},\n  style: style || {},\n  props: props || {},\n  children: [],\n});\n\nconst createTextInstance = (text) => ({ type: 'TEXT_INSTANCE', value: text });\n\nconst appendChild = (parent, child) => {\n  const isParentText =\n    parent.type === 'TEXT' ||\n    parent.type === 'LINK' ||\n    parent.type === 'TSPAN' ||\n    parent.type === 'NOTE';\n\n  const isChildTextInstance = child.type === 'TEXT_INSTANCE';\n  const isOrphanTextInstance = isChildTextInstance && !isParentText;\n\n  // Ignore orphan text instances.\n  // Caused by cases such as <>{name && <Text>{name}</Text>}</>\n  if (isOrphanTextInstance) {\n    console.warn(\n      `Invalid '${child.value}' string child outside <Text> component`,\n    );\n    return;\n  }\n\n  parent.children.push(child);\n};\n\nconst appendChildToContainer = (parentInstance, child) => {\n  if (parentInstance.type === 'ROOT') {\n    parentInstance.document = child;\n  } else {\n    appendChild(parentInstance, child);\n  }\n};\n\nconst insertBefore = (parentInstance, child, beforeChild) => {\n  const index = parentInstance.children?.indexOf(beforeChild);\n\n  if (index === undefined) return;\n\n  if (index !== -1 && child) parentInstance.children.splice(index, 0, child);\n};\n\nconst removeChild = (parentInstance, child) => {\n  const index = parentInstance.children?.indexOf(child);\n\n  if (index === undefined) return;\n\n  if (index !== -1) parentInstance.children.splice(index, 1);\n};\n\nconst removeChildFromContainer = (parentInstance, child) => {\n  const index = parentInstance.children?.indexOf(child);\n\n  if (index === undefined) return;\n\n  if (index !== -1) parentInstance.children.splice(index, 1);\n};\n\nconst commitTextUpdate = (textInstance, oldText, newText) => {\n  textInstance.value = newText;\n};\n\nconst commitUpdate = (instance, updatePayload, type, oldProps, newProps) => {\n  const { style, ...props } = newProps;\n  instance.props = props;\n  instance.style = style;\n};\n\nconst createRenderer = ({ onChange = () => {} }) =>\n  Reconciler({\n    appendChild,\n    appendChildToContainer,\n    commitTextUpdate,\n    commitUpdate,\n    createInstance,\n    createTextInstance,\n    insertBefore,\n    removeChild,\n    removeChildFromContainer,\n    resetAfterCommit: onChange,\n  });\n\nexport default createRenderer;\n", "import FontStore from '@react-pdf/font';\nimport renderPDF from '@react-pdf/render';\nimport PDFDocument from '@react-pdf/pdfkit';\nimport layoutDocument from '@react-pdf/layout';\nimport { upperFirst } from '@react-pdf/fns';\n\nimport { omitNils } from './utils';\nimport createRenderer from './renderer';\nimport packageJson from '../package.json';\n\nconst { version } = packageJson;\n\nconst fontStore = new FontStore();\n\n// We must keep a single renderer instance, otherwise <PERSON><PERSON> will complain\nlet renderer;\n\n// The pdf instance acts as an event emitter for DOM usage.\n// We only want to trigger an update when PDF content changes\nconst events = {};\n\nconst pdf = (initialValue) => {\n  const onChange = () => {\n    const listeners = events.change?.slice() || [];\n    for (let i = 0; i < listeners.length; i += 1) listeners[i]();\n  };\n\n  const container = { type: 'ROOT', document: null };\n  renderer = renderer || createRenderer({ onChange });\n  const mountNode = renderer.createContainer(container);\n\n  const updateContainer = (doc, callback) => {\n    renderer.updateContainer(doc, mountNode, null, callback);\n  };\n\n  if (initialValue) updateContainer(initialValue);\n\n  const render = async (compress = true) => {\n    const props = container.document.props || {};\n    const {\n      pdfVersion,\n      language,\n      pageLayout,\n      pageMode,\n      title,\n      author,\n      subject,\n      keyboards,\n      creator = 'react-pdf',\n      producer = 'react-pdf',\n      creationDate = new Date(),\n      modificationDate,\n    } = props;\n\n    const ctx = new PDFDocument({\n      compress,\n      pdfVersion,\n      lang: language,\n      displayTitle: true,\n      autoFirstPage: false,\n      info: omitNils({\n        Title: title,\n        Author: author,\n        Subject: subject,\n        Keywords: keyboards,\n        Creator: creator,\n        Producer: producer,\n        CreationDate: creationDate,\n        ModificationDate: modificationDate,\n      }),\n    });\n\n    if (pageLayout) {\n      ctx._root.data.PageLayout = upperFirst(pageLayout);\n    }\n\n    if (pageMode) {\n      ctx._root.data.PageMode = upperFirst(pageMode);\n    }\n\n    const layout = await layoutDocument(container.document, fontStore);\n    const fileStream = renderPDF(ctx, layout);\n    return { layout, fileStream };\n  };\n\n  const callOnRender = (params = {}) => {\n    if (container.document.props.onRender) {\n      container.document.props.onRender(params);\n    }\n  };\n\n  const toBlob = async () => {\n    const chunks = [];\n    const { layout: _INTERNAL__LAYOUT__DATA_, fileStream: instance } =\n      await render();\n\n    return new Promise((resolve, reject) => {\n      instance.on('data', (chunk) => {\n        chunks.push(\n          chunk instanceof Uint8Array ? chunk : new Uint8Array(chunk),\n        );\n      });\n\n      instance.on('end', () => {\n        try {\n          const blob = new Blob(chunks, { type: 'application/pdf' });\n          callOnRender({ blob, _INTERNAL__LAYOUT__DATA_ });\n          resolve(blob);\n        } catch (error) {\n          reject(error);\n        }\n      });\n    });\n  };\n\n  // TODO: rename this method to `toStream` in next major release, because it return stream not a buffer\n  const toBuffer = async () => {\n    const { layout: _INTERNAL__LAYOUT__DATA_, fileStream } = await render();\n    callOnRender({ _INTERNAL__LAYOUT__DATA_ });\n\n    return fileStream;\n  };\n\n  /*\n   * TODO: remove this method in next major release. it is buggy\n   * see\n   * - https://github.com/diegomura/react-pdf/issues/2112\n   * - https://github.com/diegomura/react-pdf/issues/2095\n   */\n  const toString = async () => {\n    if (process.env.NODE_ENV === 'development') {\n      console.warn(\n        '`toString` is deprecated and will be removed in next major release',\n      );\n    }\n\n    let result = '';\n    const { fileStream: instance } = await render(false); // For some reason, when rendering to string if compress=true the document is blank\n\n    return new Promise((resolve, reject) => {\n      try {\n        instance.on('data', (buffer) => {\n          result += buffer;\n        });\n\n        instance.on('end', () => {\n          callOnRender();\n          resolve(result);\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  };\n\n  const on = (event, listener) => {\n    if (!events[event]) events[event] = [];\n    events[event].push(listener);\n  };\n\n  const removeListener = (event, listener) => {\n    if (!events[event]) return;\n    const idx = events[event].indexOf(listener);\n    if (idx > -1) events[event].splice(idx, 1);\n  };\n\n  return {\n    on,\n    container,\n    toBlob,\n    toBuffer,\n    toString,\n    removeListener,\n    updateContainer,\n  };\n};\n\nconst Font = fontStore;\n\nconst StyleSheet = {\n  create: (s) => s,\n};\n\nexport { version, Font, StyleSheet, pdf, createRenderer };\n", "import queue from 'queue';\nimport { useState, useRef, useEffect, useCallback } from 'react';\n\nimport { pdf } from '../index';\n\n/**\n * PDF hook\n *\n * @param {Object} [options] hook options\n * @returns {[Object, Function]} pdf state and update function\n */\nexport const usePDF = ({ document } = {}) => {\n  const pdfInstance = useRef(null);\n\n  const [state, setState] = useState({\n    url: null,\n    blob: null,\n    error: null,\n    loading: !!document,\n  });\n\n  // Setup rendering queue\n  useEffect(() => {\n    const renderQueue = queue({ autostart: true, concurrency: 1 });\n\n    const queueDocumentRender = () => {\n      setState((prev) => ({ ...prev, loading: true }));\n\n      renderQueue.splice(0, renderQueue.length, () =>\n        state.error ? Promise.resolve() : pdfInstance.current.toBlob(),\n      );\n    };\n\n    const onRenderFailed = (error) => {\n      console.error(error);\n      setState((prev) => ({ ...prev, loading: false, error }));\n    };\n\n    const onRenderSuccessful = (blob) => {\n      setState({\n        blob,\n        error: null,\n        loading: false,\n        url: URL.createObjectURL(blob),\n      });\n    };\n\n    pdfInstance.current = pdf();\n    pdfInstance.current.on('change', queueDocumentRender);\n    if (document) {\n      pdfInstance.current.updateContainer(document);\n    }\n\n    renderQueue.on('error', onRenderFailed);\n    renderQueue.on('success', onRenderSuccessful);\n\n    return () => {\n      renderQueue.end();\n      pdfInstance.current.removeListener('change', queueDocumentRender);\n    };\n  }, []);\n\n  // Revoke old unused url instances\n  useEffect(() => {\n    return () => {\n      if (state.url) {\n        URL.revokeObjectURL(state.url);\n      }\n    };\n  }, [state.url]);\n\n  const update = useCallback((newDoc) => {\n    pdfInstance.current.updateContainer(newDoc);\n  }, []);\n\n  return [state, update];\n};\n\nexport default usePDF;\n", "import { useEffect } from 'react';\n\nimport usePDF from './usePDF';\n\nexport const PDFViewer = ({\n  title,\n  style,\n  className,\n  children,\n  innerRef,\n  showToolbar = true,\n  ...props\n}) => {\n  const [instance, updateInstance] = usePDF();\n\n  useEffect(() => updateInstance(children), [children]);\n\n  const src = instance.url\n    ? `${instance.url}#toolbar=${showToolbar ? 1 : 0}`\n    : null;\n\n  return (\n    <iframe\n      src={src}\n      title={title}\n      ref={innerRef}\n      style={style}\n      className={className}\n      {...props}\n    />\n  );\n};\n\nexport default PDFViewer;\n", "import { useEffect } from 'react';\n\nimport usePDF from './usePDF';\n\nexport const BlobProvider = ({ document: doc, children }) => {\n  const [instance, updateInstance] = usePDF();\n\n  useEffect(() => updateInstance(doc), [doc]);\n\n  if (!doc) {\n    console.warn('You should pass a valid document to BlobProvider');\n    return null;\n  }\n\n  return children(instance);\n};\n\nexport default BlobProvider;\n", "import { useEffect } from 'react';\n\nimport usePDF from './usePDF';\n\nexport const PDFDownloadLink = ({\n  fileName = 'document.pdf',\n  document: doc,\n  children,\n  onClick,\n  href,\n  ...rest\n}) => {\n  const [instance, updateInstance] = usePDF();\n\n  useEffect(() => updateInstance(doc), [doc]);\n\n  if (!doc) {\n    console.warn('You should pass a valid document to PDFDownloadLink');\n    return null;\n  }\n\n  const handleDownloadIE = () => {\n    if (instance && window.navigator.msSaveBlob) {\n      // IE\n      window.navigator.msSaveBlob(instance.blob, fileName);\n    }\n  };\n\n  const handleClick = (event) => {\n    handleDownloadIE();\n    if (typeof onClick === 'function') onClick(event, instance);\n  };\n\n  return (\n    <a href={instance.url} download={fileName} onClick={handleClick} {...rest}>\n      {typeof children === 'function' ? children(instance) : children}\n    </a>\n  );\n};\n\nexport default PDFDownloadLink;\n", "import * as primitives from '@react-pdf/primitives';\n\nimport usePDF from './usePDF';\nimport PDFViewer from './PDFViewer';\nimport BlobProvider from './BlobProvider';\nimport PDFDownloadLink from './PDFDownloadLink';\nimport { pdf, version, Font, StyleSheet } from '../index';\n\nconst throwEnvironmentError = (name) => {\n  throw new Error(\n    `${name} is a Node specific API. You're either using this method in a browser, or your bundler is not loading react-pdf from the appropriate web build.`,\n  );\n};\n\nexport const renderToStream = () => {\n  throwEnvironmentError('renderToStream');\n};\n\nexport const renderToBuffer = () => {\n  throwEnvironmentError('renderToBuffer');\n};\n\nexport const renderToString = () => {\n  throwEnvironmentError('renderToString');\n};\n\nexport const renderToFile = () => {\n  throwEnvironmentError('renderToFile');\n};\n\nexport const render = () => {\n  throwEnvironmentError('render');\n};\n\nexport * from '../index';\n\nexport * from './usePDF';\n\nexport * from './PDFViewer';\n\nexport * from './BlobProvider';\n\nexport * from './PDFDownloadLink';\n\nexport * from '@react-pdf/primitives';\n\n// TODO: remove this default export in next major release because it breaks tree-shacking\nexport default {\n  pdf,\n  usePDF,\n  Font,\n  version,\n  StyleSheet,\n  PDFViewer,\n  BlobProvider,\n  PDFDownloadLink,\n  renderToStream,\n  renderToString,\n  renderToFile,\n  render,\n  ...primitives,\n};\n"], "names": ["omitNils", "object", "Object", "fromEntries", "entries", "filter", "_ref", "value", "undefined", "createInstance", "type", "style", "children", "props", "box", "createTextInstance", "text", "append<PERSON><PERSON><PERSON>", "parent", "child", "isParentText", "isChildTextInstance", "isOrphanTextInstance", "console", "warn", "push", "append<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parentInstance", "document", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "_parentInstance$child", "index", "indexOf", "splice", "<PERSON><PERSON><PERSON><PERSON>", "_parentInstance$child2", "<PERSON><PERSON><PERSON><PERSON><PERSON>rom<PERSON><PERSON><PERSON>", "_parentInstance$child3", "commitTextUpdate", "textInstance", "oldText", "newText", "commitUpdate", "instance", "updatePayload", "oldProps", "newProps", "<PERSON><PERSON><PERSON><PERSON>", "_ref2", "onChange", "<PERSON><PERSON><PERSON><PERSON>", "resetAfterCommit", "version", "packageJson", "fontStore", "FontStore", "renderer", "events", "pdf", "initialValue", "_events$change", "listeners", "change", "slice", "i", "length", "container", "mountNode", "createContainer", "updateContainer", "doc", "callback", "render", "compress", "pdfVersion", "language", "pageLayout", "pageMode", "title", "author", "subject", "keyboards", "creator", "producer", "creationDate", "Date", "modificationDate", "ctx", "PDFDocument", "lang", "displayTitle", "autoFirstPage", "info", "Title", "Author", "Subject", "Keywords", "Creator", "Producer", "CreationDate", "ModificationDate", "_root", "data", "PageLayout", "upperFirst", "PageMode", "layout", "layoutDocument", "fileStream", "renderPDF", "callOnRender", "params", "onRender", "toBlob", "chunks", "_INTERNAL__LAYOUT__DATA_", "Promise", "resolve", "reject", "on", "chunk", "Uint8Array", "blob", "Blob", "error", "<PERSON><PERSON><PERSON><PERSON>", "toString", "process", "env", "NODE_ENV", "result", "buffer", "event", "listener", "removeListener", "idx", "Font", "StyleSheet", "create", "s", "usePDF", "_temp", "pdfInstance", "useRef", "state", "setState", "useState", "url", "loading", "useEffect", "renderQueue", "queue", "autostart", "concurrency", "queueDocumentRender", "prev", "current", "onRenderFailed", "onRenderSuccessful", "URL", "createObjectURL", "end", "revokeObjectURL", "update", "useCallback", "newDoc", "PDFViewer", "className", "innerRef", "showToolbar", "updateInstance", "src", "_jsx", "ref", "BlobProvider", "PDFDownloadLink", "fileName", "onClick", "href", "rest", "handleDownloadIE", "window", "navigator", "msSaveBlob", "handleClick", "download", "throwEnvironmentError", "name", "Error", "renderToStream", "renderToBuffer", "renderToString", "renderToFile", "primitives"], "mappings": ";;;;;;;;;;;;AAAO,MAAMA,QAAQ,GAAIC,MAAM,IAC7BC,MAAM,CAACC,WAAW,CAChBD,MAAM,CAACE,OAAO,CAACH,MAAM,CAAC,CAACI,MAAM,CAACC,IAAA,IAAA;AAAA,EAAA,IAAC,GAAGC,KAAK,CAAC,GAAAD,IAAA;EAAA,OAAKC,KAAK,KAAKC,SAAS;AAAA,CAAA,CAClE,CAAC;;ACDH,MAAMC,cAAc,GAAGA,CAACC,IAAI,EAAAJ,IAAA,KAAA;EAAA,IAAE;IAAEK,KAAK;IAAEC,QAAQ;IAAE,GAAGC;AAAM,GAAC,GAAAP,IAAA;EAAA,OAAM;IAC/DI,IAAI;IACJI,GAAG,EAAE,EAAE;AACPH,IAAAA,KAAK,EAAEA,KAAK,IAAI,EAAE;AAClBE,IAAAA,KAAK,EAAEA,KAAK,IAAI,EAAE;AAClBD,IAAAA,QAAQ,EAAE;GACX;AAAA,CAAC;AAEF,MAAMG,kBAAkB,GAAIC,IAAI,KAAM;AAAEN,EAAAA,IAAI,EAAE,eAAe;AAAEH,EAAAA,KAAK,EAAES;AAAK,CAAC,CAAC;AAE7E,MAAMC,WAAW,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EACrC,MAAMC,YAAY,GAChBF,MAAM,CAACR,IAAI,KAAK,MAAM,IACtBQ,MAAM,CAACR,IAAI,KAAK,MAAM,IACtBQ,MAAM,CAACR,IAAI,KAAK,OAAO,IACvBQ,MAAM,CAACR,IAAI,KAAK,MAAM;AAExB,EAAA,MAAMW,mBAAmB,GAAGF,KAAK,CAACT,IAAI,KAAK,eAAe;AAC1D,EAAA,MAAMY,oBAAoB,GAAGD,mBAAmB,IAAI,CAACD,YAAY;;AAEjE;AACA;AACA,EAAA,IAAIE,oBAAoB,EAAE;IACxBC,OAAO,CAACC,IAAI,CACT,CAAA,SAAA,EAAWL,KAAK,CAACZ,KAAM,yCAC1B,CAAC;AACD,IAAA;AACF;AAEAW,EAAAA,MAAM,CAACN,QAAQ,CAACa,IAAI,CAACN,KAAK,CAAC;AAC7B,CAAC;AAED,MAAMO,sBAAsB,GAAGA,CAACC,cAAc,EAAER,KAAK,KAAK;AACxD,EAAA,IAAIQ,cAAc,CAACjB,IAAI,KAAK,MAAM,EAAE;IAClCiB,cAAc,CAACC,QAAQ,GAAGT,KAAK;AACjC,GAAC,MAAM;AACLF,IAAAA,WAAW,CAACU,cAAc,EAAER,KAAK,CAAC;AACpC;AACF,CAAC;AAED,MAAMU,YAAY,GAAGA,CAACF,cAAc,EAAER,KAAK,EAAEW,WAAW,KAAK;AAAA,EAAA,IAAAC,qBAAA;AAC3D,EAAA,MAAMC,KAAK,GAAA,CAAAD,qBAAA,GAAGJ,cAAc,CAACf,QAAQ,MAAAmB,IAAAA,IAAAA,qBAAA,uBAAvBA,qBAAA,CAAyBE,OAAO,CAACH,WAAW,CAAC;EAE3D,IAAIE,KAAK,KAAKxB,SAAS,EAAE;AAEzB,EAAA,IAAIwB,KAAK,KAAK,EAAE,IAAIb,KAAK,EAAEQ,cAAc,CAACf,QAAQ,CAACsB,MAAM,CAACF,KAAK,EAAE,CAAC,EAAEb,KAAK,CAAC;AAC5E,CAAC;AAED,MAAMgB,WAAW,GAAGA,CAACR,cAAc,EAAER,KAAK,KAAK;AAAA,EAAA,IAAAiB,sBAAA;AAC7C,EAAA,MAAMJ,KAAK,GAAA,CAAAI,sBAAA,GAAGT,cAAc,CAACf,QAAQ,MAAAwB,IAAAA,IAAAA,sBAAA,uBAAvBA,sBAAA,CAAyBH,OAAO,CAACd,KAAK,CAAC;EAErD,IAAIa,KAAK,KAAKxB,SAAS,EAAE;AAEzB,EAAA,IAAIwB,KAAK,KAAK,EAAE,EAAEL,cAAc,CAACf,QAAQ,CAACsB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;AAC5D,CAAC;AAED,MAAMK,wBAAwB,GAAGA,CAACV,cAAc,EAAER,KAAK,KAAK;AAAA,EAAA,IAAAmB,sBAAA;AAC1D,EAAA,MAAMN,KAAK,GAAA,CAAAM,sBAAA,GAAGX,cAAc,CAACf,QAAQ,MAAA0B,IAAAA,IAAAA,sBAAA,uBAAvBA,sBAAA,CAAyBL,OAAO,CAACd,KAAK,CAAC;EAErD,IAAIa,KAAK,KAAKxB,SAAS,EAAE;AAEzB,EAAA,IAAIwB,KAAK,KAAK,EAAE,EAAEL,cAAc,CAACf,QAAQ,CAACsB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;AAC5D,CAAC;AAED,MAAMO,gBAAgB,GAAGA,CAACC,YAAY,EAAEC,OAAO,EAAEC,OAAO,KAAK;EAC3DF,YAAY,CAACjC,KAAK,GAAGmC,OAAO;AAC9B,CAAC;AAED,MAAMC,YAAY,GAAGA,CAACC,QAAQ,EAAEC,aAAa,EAAEnC,IAAI,EAAEoC,QAAQ,EAAEC,QAAQ,KAAK;EAC1E,MAAM;IAAEpC,KAAK;IAAE,GAAGE;AAAM,GAAC,GAAGkC,QAAQ;EACpCH,QAAQ,CAAC/B,KAAK,GAAGA,KAAK;EACtB+B,QAAQ,CAACjC,KAAK,GAAGA,KAAK;AACxB,CAAC;AAEKqC,MAAAA,cAAc,GAAGC,KAAA,IAAA;EAAA,IAAC;IAAEC,QAAQ,GAAGA,MAAM;AAAG,GAAC,GAAAD,KAAA;AAAA,EAAA,OAC7CE,UAAU,CAAC;IACTlC,WAAW;IACXS,sBAAsB;IACtBa,gBAAgB;IAChBI,YAAY;IACZlC,cAAc;IACdM,kBAAkB;IAClBc,YAAY;IACZM,WAAW;IACXE,wBAAwB;AACxBe,IAAAA,gBAAgB,EAAEF;AACpB,GAAC,CAAC;AAAA;;;;;;AC9EE,MAAA;AAAEG,EAAAA;AAAQ,CAAC,GAAGC;AAEpB,MAAMC,SAAS,GAAG,IAAIC,SAAS,EAAE;;AAEjC;AACA,IAAIC,QAAQ;;AAEZ;AACA;AACA,MAAMC,MAAM,GAAG,EAAE;AAEXC,MAAAA,GAAG,GAAIC,YAAY,IAAK;EAC5B,MAAMV,QAAQ,GAAGA,MAAM;AAAA,IAAA,IAAAW,cAAA;AACrB,IAAA,MAAMC,SAAS,GAAG,CAAA,CAAAD,cAAA,GAAAH,MAAM,CAACK,MAAM,MAAAF,IAAAA,IAAAA,cAAA,uBAAbA,cAAA,CAAeG,KAAK,EAAE,KAAI,EAAE;IAC9C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACI,MAAM,EAAED,CAAC,IAAI,CAAC,EAAEH,SAAS,CAACG,CAAC,CAAC,EAAE;GAC7D;AAED,EAAA,MAAME,SAAS,GAAG;AAAEzD,IAAAA,IAAI,EAAE,MAAM;AAAEkB,IAAAA,QAAQ,EAAE;GAAM;AAClD6B,EAAAA,QAAQ,GAAGA,QAAQ,IAAIT,cAAc,CAAC;AAAEE,IAAAA;AAAS,GAAC,CAAC;AACnD,EAAA,MAAMkB,SAAS,GAAGX,QAAQ,CAACY,eAAe,CAACF,SAAS,CAAC;AAErD,EAAA,MAAMG,eAAe,GAAGA,CAACC,GAAG,EAAEC,QAAQ,KAAK;IACzCf,QAAQ,CAACa,eAAe,CAACC,GAAG,EAAEH,SAAS,EAAE,IAAI,EAAEI,QAAQ,CAAC;GACzD;AAED,EAAA,IAAIZ,YAAY,EAAEU,eAAe,CAACV,YAAY,CAAC;AAE/C,EAAA,MAAMa,MAAM,GAAG,gBAAOC,QAAQ,EAAY;AAAA,IAAA,IAApBA,QAAQ,KAAA,MAAA,EAAA;AAARA,MAAAA,QAAQ,GAAG,IAAI;AAAA;IACnC,MAAM7D,KAAK,GAAGsD,SAAS,CAACvC,QAAQ,CAACf,KAAK,IAAI,EAAE;IAC5C,MAAM;MACJ8D,UAAU;MACVC,QAAQ;MACRC,UAAU;MACVC,QAAQ;MACRC,KAAK;MACLC,MAAM;MACNC,OAAO;MACPC,SAAS;AACTC,MAAAA,OAAO,GAAG,WAAW;AACrBC,MAAAA,QAAQ,GAAG,WAAW;AACtBC,MAAAA,YAAY,GAAG,IAAIC,IAAI,EAAE;AACzBC,MAAAA;AACF,KAAC,GAAG1E,KAAK;AAET,IAAA,MAAM2E,GAAG,GAAG,IAAIC,WAAW,CAAC;MAC1Bf,QAAQ;MACRC,UAAU;AACVe,MAAAA,IAAI,EAAEd,QAAQ;AACde,MAAAA,YAAY,EAAE,IAAI;AAClBC,MAAAA,aAAa,EAAE,KAAK;MACpBC,IAAI,EAAE7F,QAAQ,CAAC;AACb8F,QAAAA,KAAK,EAAEf,KAAK;AACZgB,QAAAA,MAAM,EAAEf,MAAM;AACdgB,QAAAA,OAAO,EAAEf,OAAO;AAChBgB,QAAAA,QAAQ,EAAEf,SAAS;AACnBgB,QAAAA,OAAO,EAAEf,OAAO;AAChBgB,QAAAA,QAAQ,EAAEf,QAAQ;AAClBgB,QAAAA,YAAY,EAAEf,YAAY;AAC1BgB,QAAAA,gBAAgB,EAAEd;OACnB;AACH,KAAC,CAAC;AAEF,IAAA,IAAIV,UAAU,EAAE;MACdW,GAAG,CAACc,KAAK,CAACC,IAAI,CAACC,UAAU,GAAGC,UAAU,CAAC5B,UAAU,CAAC;AACpD;AAEA,IAAA,IAAIC,QAAQ,EAAE;MACZU,GAAG,CAACc,KAAK,CAACC,IAAI,CAACG,QAAQ,GAAGD,UAAU,CAAC3B,QAAQ,CAAC;AAChD;IAEA,MAAM6B,MAAM,GAAG,MAAMC,cAAc,CAACzC,SAAS,CAACvC,QAAQ,EAAE2B,SAAS,CAAC;AAClE,IAAA,MAAMsD,UAAU,GAAGC,SAAS,CAACtB,GAAG,EAAEmB,MAAM,CAAC;IACzC,OAAO;MAAEA,MAAM;AAAEE,MAAAA;KAAY;GAC9B;AAED,EAAA,MAAME,YAAY,GAAG,UAACC,MAAM,EAAU;AAAA,IAAA,IAAhBA,MAAM,KAAA,MAAA,EAAA;MAANA,MAAM,GAAG,EAAE;AAAA;AAC/B,IAAA,IAAI7C,SAAS,CAACvC,QAAQ,CAACf,KAAK,CAACoG,QAAQ,EAAE;MACrC9C,SAAS,CAACvC,QAAQ,CAACf,KAAK,CAACoG,QAAQ,CAACD,MAAM,CAAC;AAC3C;GACD;AAED,EAAA,MAAME,MAAM,GAAG,YAAY;IACzB,MAAMC,MAAM,GAAG,EAAE;IACjB,MAAM;AAAER,MAAAA,MAAM,EAAES,wBAAwB;AAAEP,MAAAA,UAAU,EAAEjE;AAAS,KAAC,GAC9D,MAAM6B,MAAM,EAAE;AAEhB,IAAA,OAAO,IAAI4C,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;AACtC3E,MAAAA,QAAQ,CAAC4E,EAAE,CAAC,MAAM,EAAGC,KAAK,IAAK;AAC7BN,QAAAA,MAAM,CAAC1F,IAAI,CACTgG,KAAK,YAAYC,UAAU,GAAGD,KAAK,GAAG,IAAIC,UAAU,CAACD,KAAK,CAC5D,CAAC;AACH,OAAC,CAAC;AAEF7E,MAAAA,QAAQ,CAAC4E,EAAE,CAAC,KAAK,EAAE,MAAM;QACvB,IAAI;AACF,UAAA,MAAMG,IAAI,GAAG,IAAIC,IAAI,CAACT,MAAM,EAAE;AAAEzG,YAAAA,IAAI,EAAE;AAAkB,WAAC,CAAC;AAC1DqG,UAAAA,YAAY,CAAC;YAAEY,IAAI;AAAEP,YAAAA;AAAyB,WAAC,CAAC;UAChDE,OAAO,CAACK,IAAI,CAAC;SACd,CAAC,OAAOE,KAAK,EAAE;UACdN,MAAM,CAACM,KAAK,CAAC;AACf;AACF,OAAC,CAAC;AACJ,KAAC,CAAC;GACH;;AAED;AACA,EAAA,MAAMC,QAAQ,GAAG,YAAY;IAC3B,MAAM;AAAEnB,MAAAA,MAAM,EAAES,wBAAwB;AAAEP,MAAAA;AAAW,KAAC,GAAG,MAAMpC,MAAM,EAAE;AACvEsC,IAAAA,YAAY,CAAC;AAAEK,MAAAA;AAAyB,KAAC,CAAC;AAE1C,IAAA,OAAOP,UAAU;GAClB;;AAED;AACF;AACA;AACA;AACA;AACA;AACE,EAAA,MAAMkB,QAAQ,GAAG,YAAY;AAC3B,IAAA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;AAC1C3G,MAAAA,OAAO,CAACC,IAAI,CACV,oEACF,CAAC;AACH;IAEA,IAAI2G,MAAM,GAAG,EAAE;IACf,MAAM;AAAEtB,MAAAA,UAAU,EAAEjE;AAAS,KAAC,GAAG,MAAM6B,MAAM,CAAC,KAAK,CAAC,CAAC;;AAErD,IAAA,OAAO,IAAI4C,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,IAAI;AACF3E,QAAAA,QAAQ,CAAC4E,EAAE,CAAC,MAAM,EAAGY,MAAM,IAAK;AAC9BD,UAAAA,MAAM,IAAIC,MAAM;AAClB,SAAC,CAAC;AAEFxF,QAAAA,QAAQ,CAAC4E,EAAE,CAAC,KAAK,EAAE,MAAM;AACvBT,UAAAA,YAAY,EAAE;UACdO,OAAO,CAACa,MAAM,CAAC;AACjB,SAAC,CAAC;OACH,CAAC,OAAON,KAAK,EAAE;QACdN,MAAM,CAACM,KAAK,CAAC;AACf;AACF,KAAC,CAAC;GACH;AAED,EAAA,MAAML,EAAE,GAAGA,CAACa,KAAK,EAAEC,QAAQ,KAAK;IAC9B,IAAI,CAAC5E,MAAM,CAAC2E,KAAK,CAAC,EAAE3E,MAAM,CAAC2E,KAAK,CAAC,GAAG,EAAE;AACtC3E,IAAAA,MAAM,CAAC2E,KAAK,CAAC,CAAC5G,IAAI,CAAC6G,QAAQ,CAAC;GAC7B;AAED,EAAA,MAAMC,cAAc,GAAGA,CAACF,KAAK,EAAEC,QAAQ,KAAK;AAC1C,IAAA,IAAI,CAAC5E,MAAM,CAAC2E,KAAK,CAAC,EAAE;IACpB,MAAMG,GAAG,GAAG9E,MAAM,CAAC2E,KAAK,CAAC,CAACpG,OAAO,CAACqG,QAAQ,CAAC;AAC3C,IAAA,IAAIE,GAAG,GAAG,EAAE,EAAE9E,MAAM,CAAC2E,KAAK,CAAC,CAACnG,MAAM,CAACsG,GAAG,EAAE,CAAC,CAAC;GAC3C;EAED,OAAO;IACLhB,EAAE;IACFrD,SAAS;IACT+C,MAAM;IACNY,QAAQ;IACRC,QAAQ;IACRQ,cAAc;AACdjE,IAAAA;GACD;AACH;AAEMmE,MAAAA,IAAI,GAAGlF;AAEb,MAAMmF,UAAU,GAAG;EACjBC,MAAM,EAAGC,CAAC,IAAKA;AACjB;;AChLA;AACA;AACA;AACA;AACA;AACA;MACaC,MAAM,GAAG,UAAAC,KAAA,EAAuB;EAAA,IAAtB;AAAElH,IAAAA;AAAS,GAAC,GAAAkH,KAAA,KAAA,MAAA,GAAG,EAAE,GAAAA,KAAA;AACtC,EAAA,MAAMC,WAAW,GAAGC,MAAM,CAAC,IAAI,CAAC;AAEhC,EAAA,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGC,QAAQ,CAAC;AACjCC,IAAAA,GAAG,EAAE,IAAI;AACTzB,IAAAA,IAAI,EAAE,IAAI;AACVE,IAAAA,KAAK,EAAE,IAAI;IACXwB,OAAO,EAAE,CAAC,CAACzH;AACb,GAAC,CAAC;;AAEF;AACA0H,EAAAA,SAAS,CAAC,MAAM;IACd,MAAMC,WAAW,GAAGC,KAAK,CAAC;AAAEC,MAAAA,SAAS,EAAE,IAAI;AAAEC,MAAAA,WAAW,EAAE;AAAE,KAAC,CAAC;IAE9D,MAAMC,mBAAmB,GAAGA,MAAM;MAChCT,QAAQ,CAAEU,IAAI,KAAM;AAAE,QAAA,GAAGA,IAAI;AAAEP,QAAAA,OAAO,EAAE;AAAK,OAAC,CAAC,CAAC;MAEhDE,WAAW,CAACrH,MAAM,CAAC,CAAC,EAAEqH,WAAW,CAACrF,MAAM,EAAE,MACxC+E,KAAK,CAACpB,KAAK,GAAGR,OAAO,CAACC,OAAO,EAAE,GAAGyB,WAAW,CAACc,OAAO,CAAC3C,MAAM,EAC9D,CAAC;KACF;IAED,MAAM4C,cAAc,GAAIjC,KAAK,IAAK;AAChCtG,MAAAA,OAAO,CAACsG,KAAK,CAACA,KAAK,CAAC;MACpBqB,QAAQ,CAAEU,IAAI,KAAM;AAAE,QAAA,GAAGA,IAAI;AAAEP,QAAAA,OAAO,EAAE,KAAK;AAAExB,QAAAA;AAAM,OAAC,CAAC,CAAC;KACzD;IAED,MAAMkC,kBAAkB,GAAIpC,IAAI,IAAK;AACnCuB,MAAAA,QAAQ,CAAC;QACPvB,IAAI;AACJE,QAAAA,KAAK,EAAE,IAAI;AACXwB,QAAAA,OAAO,EAAE,KAAK;AACdD,QAAAA,GAAG,EAAEY,GAAG,CAACC,eAAe,CAACtC,IAAI;AAC/B,OAAC,CAAC;KACH;AAEDoB,IAAAA,WAAW,CAACc,OAAO,GAAGlG,GAAG,EAAE;IAC3BoF,WAAW,CAACc,OAAO,CAACrC,EAAE,CAAC,QAAQ,EAAEmC,mBAAmB,CAAC;AACrD,IAAA,IAAI/H,QAAQ,EAAE;AACZmH,MAAAA,WAAW,CAACc,OAAO,CAACvF,eAAe,CAAC1C,QAAQ,CAAC;AAC/C;AAEA2H,IAAAA,WAAW,CAAC/B,EAAE,CAAC,OAAO,EAAEsC,cAAc,CAAC;AACvCP,IAAAA,WAAW,CAAC/B,EAAE,CAAC,SAAS,EAAEuC,kBAAkB,CAAC;AAE7C,IAAA,OAAO,MAAM;MACXR,WAAW,CAACW,GAAG,EAAE;MACjBnB,WAAW,CAACc,OAAO,CAACtB,cAAc,CAAC,QAAQ,EAAEoB,mBAAmB,CAAC;KAClE;GACF,EAAE,EAAE,CAAC;;AAEN;AACAL,EAAAA,SAAS,CAAC,MAAM;AACd,IAAA,OAAO,MAAM;MACX,IAAIL,KAAK,CAACG,GAAG,EAAE;AACbY,QAAAA,GAAG,CAACG,eAAe,CAAClB,KAAK,CAACG,GAAG,CAAC;AAChC;KACD;AACH,GAAC,EAAE,CAACH,KAAK,CAACG,GAAG,CAAC,CAAC;AAEf,EAAA,MAAMgB,MAAM,GAAGC,WAAW,CAAEC,MAAM,IAAK;AACrCvB,IAAAA,WAAW,CAACc,OAAO,CAACvF,eAAe,CAACgG,MAAM,CAAC;GAC5C,EAAE,EAAE,CAAC;AAEN,EAAA,OAAO,CAACrB,KAAK,EAAEmB,MAAM,CAAC;AACxB;;ACxEaG,MAAAA,SAAS,GAAGjK,IAAA,IAQnB;EAAA,IARoB;IACxByE,KAAK;IACLpE,KAAK;IACL6J,SAAS;IACT5J,QAAQ;IACR6J,QAAQ;AACRC,IAAAA,WAAW,GAAG,IAAI;IAClB,GAAG7J;AACL,GAAC,GAAAP,IAAA;EACC,MAAM,CAACsC,QAAQ,EAAE+H,cAAc,CAAC,GAAG9B,MAAM,EAAE;EAE3CS,SAAS,CAAC,MAAMqB,cAAc,CAAC/J,QAAQ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;AAErD,EAAA,MAAMgK,GAAG,GAAGhI,QAAQ,CAACwG,GAAG,GACnB,GAAExG,QAAQ,CAACwG,GAAI,CAAA,SAAA,EAAWsB,WAAW,GAAG,CAAC,GAAG,CAAE,CAAA,CAAC,GAChD,IAAI;AAER,EAAA,oBACEG,GAAA,CAAA,QAAA,EAAA;AACED,IAAAA,GAAG,EAAEA,GAAI;AACT7F,IAAAA,KAAK,EAAEA,KAAM;AACb+F,IAAAA,GAAG,EAAEL,QAAS;AACd9J,IAAAA,KAAK,EAAEA,KAAM;AACb6J,IAAAA,SAAS,EAAEA,SAAU;IAAA,GACjB3J;AAAK,GACV,CAAC;AAEN;;AC3BakK,MAAAA,YAAY,GAAGzK,IAAA,IAAiC;EAAA,IAAhC;AAAEsB,IAAAA,QAAQ,EAAE2C,GAAG;AAAE3D,IAAAA;AAAS,GAAC,GAAAN,IAAA;EACtD,MAAM,CAACsC,QAAQ,EAAE+H,cAAc,CAAC,GAAG9B,MAAM,EAAE;EAE3CS,SAAS,CAAC,MAAMqB,cAAc,CAACpG,GAAG,CAAC,EAAE,CAACA,GAAG,CAAC,CAAC;EAE3C,IAAI,CAACA,GAAG,EAAE;AACRhD,IAAAA,OAAO,CAACC,IAAI,CAAC,kDAAkD,CAAC;AAChE,IAAA,OAAO,IAAI;AACb;EAEA,OAAOZ,QAAQ,CAACgC,QAAQ,CAAC;AAC3B;;ACXaoI,MAAAA,eAAe,GAAG1K,IAAA,IAOzB;EAAA,IAP0B;AAC9B2K,IAAAA,QAAQ,GAAG,cAAc;AACzBrJ,IAAAA,QAAQ,EAAE2C,GAAG;IACb3D,QAAQ;IACRsK,OAAO;IACPC,IAAI;IACJ,GAAGC;AACL,GAAC,GAAA9K,IAAA;EACC,MAAM,CAACsC,QAAQ,EAAE+H,cAAc,CAAC,GAAG9B,MAAM,EAAE;EAE3CS,SAAS,CAAC,MAAMqB,cAAc,CAACpG,GAAG,CAAC,EAAE,CAACA,GAAG,CAAC,CAAC;EAE3C,IAAI,CAACA,GAAG,EAAE;AACRhD,IAAAA,OAAO,CAACC,IAAI,CAAC,qDAAqD,CAAC;AACnE,IAAA,OAAO,IAAI;AACb;EAEA,MAAM6J,gBAAgB,GAAGA,MAAM;AAC7B,IAAA,IAAIzI,QAAQ,IAAI0I,MAAM,CAACC,SAAS,CAACC,UAAU,EAAE;AAC3C;MACAF,MAAM,CAACC,SAAS,CAACC,UAAU,CAAC5I,QAAQ,CAAC+E,IAAI,EAAEsD,QAAQ,CAAC;AACtD;GACD;EAED,MAAMQ,WAAW,GAAIpD,KAAK,IAAK;AAC7BgD,IAAAA,gBAAgB,EAAE;IAClB,IAAI,OAAOH,OAAO,KAAK,UAAU,EAAEA,OAAO,CAAC7C,KAAK,EAAEzF,QAAQ,CAAC;GAC5D;AAED,EAAA,oBACEiI,GAAA,CAAA,GAAA,EAAA;IAAGM,IAAI,EAAEvI,QAAQ,CAACwG,GAAI;AAACsC,IAAAA,QAAQ,EAAET,QAAS;AAACC,IAAAA,OAAO,EAAEO,WAAY;AAAA,IAAA,GAAKL,IAAI;IAAAxK,QAAA,EACtE,OAAOA,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACgC,QAAQ,CAAC,GAAGhC;AAAQ,GAC9D,CAAC;AAER;;AC9BA,MAAM+K,qBAAqB,GAAIC,IAAI,IAAK;AACtC,EAAA,MAAM,IAAIC,KAAK,CACZ,CAAED,EAAAA,IAAK,iJACV,CAAC;AACH,CAAC;AAEYE,MAAAA,cAAc,GAAGA,MAAM;EAClCH,qBAAqB,CAAC,gBAAgB,CAAC;AACzC;AAEaI,MAAAA,cAAc,GAAGA,MAAM;EAClCJ,qBAAqB,CAAC,gBAAgB,CAAC;AACzC;AAEaK,MAAAA,cAAc,GAAGA,MAAM;EAClCL,qBAAqB,CAAC,gBAAgB,CAAC;AACzC;AAEaM,MAAAA,YAAY,GAAGA,MAAM;EAChCN,qBAAqB,CAAC,cAAc,CAAC;AACvC;AAEalH,MAAAA,MAAM,GAAGA,MAAM;EAC1BkH,qBAAqB,CAAC,QAAQ,CAAC;AACjC;;AAcA;AACA,YAAe;EACbhI,GAAG;EACHkF,MAAM;EACNJ,IAAI;EACJpF,OAAO;EACPqF,UAAU;EACV6B,SAAS;EACTQ,YAAY;EACZC,eAAe;EACfc,cAAc;EACdE,cAAc;EACdC,YAAY;EACZxH,MAAM;EACN,GAAGyH;AACL,CAAC;;;;"}