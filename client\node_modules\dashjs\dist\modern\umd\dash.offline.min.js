/*! For license information please see dash.offline.min.js.LICENSE.txt */
!function(t,n){"object"==typeof exports&&"object"==typeof module?module.exports=n():"function"==typeof define&&define.amd?define([],n):"object"==typeof exports?exports.dashjs=n():t.dashjs=n()}(self,(function(){return function(){var t={7316:function(t){"use strict";t.exports=function t(n,a){if(n===a)return!0;if(n&&a&&"object"==typeof n&&"object"==typeof a){if(n.constructor!==a.constructor)return!1;var e,r,l;if(Array.isArray(n)){if((e=n.length)!=a.length)return!1;for(r=e;0!=r--;)if(!t(n[r],a[r]))return!1;return!0}if(n.constructor===RegExp)return n.source===a.source&&n.flags===a.flags;if(n.valueOf!==Object.prototype.valueOf)return n.valueOf()===a.valueOf();if(n.toString!==Object.prototype.toString)return n.toString()===a.toString();if((e=(l=Object.keys(n)).length)!==Object.keys(a).length)return!1;for(r=e;0!=r--;)if(!Object.prototype.hasOwnProperty.call(a,l[r]))return!1;for(r=e;0!=r--;){var i=l[r];if(!t(n[i],a[i]))return!1}return!0}return n!=n&&a!=a}},4441:function(t,n,a){"use strict";var e=this&&this.__assign||function(){return e=Object.assign||function(t){for(var n,a=1,e=arguments.length;a<e;a++)for(var r in n=arguments[a])Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r]);return t},e.apply(this,arguments)};Object.defineProperty(n,"__esModule",{value:!0});var r=a(2279),l=a(9131),i=a(9055),o=e(e({},r.namedReferences),{all:r.namedReferences.html5});function s(t,n,a){n.lastIndex=0;var e,r=n.exec(t);if(r){e="";var l=0;do{l!==r.index&&(e+=t.substring(l,r.index));var i=r[0];e+=a(i),l=r.index+i.length}while(r=n.exec(t));l!==t.length&&(e+=t.substring(l))}else e=t;return e}var u={specialChars:/[<>'"&]/g,nonAscii:/[<>'"&\u0080-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/g,nonAsciiPrintable:/[<>'"&\x01-\x08\x11-\x15\x17-\x1F\x7f-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/g,nonAsciiPrintableOnly:/[\x01-\x08\x11-\x15\x17-\x1F\x7f-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/g,extensive:/[\x01-\x0c\x0e-\x1f\x21-\x2c\x2e-\x2f\x3a-\x40\x5b-\x60\x7b-\x7d\x7f-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/g},c={mode:"specialChars",level:"all",numeric:"decimal"};n.encode=function(t,n){var a=void 0===n?c:n,e=a.mode,r=void 0===e?"specialChars":e,l=a.numeric,d=void 0===l?"decimal":l,m=a.level;if(!t)return"";var g=u[r],f=o[void 0===m?"all":m].characters,p="hexadecimal"===d;return s(t,g,(function(t){var n=f[t];if(!n){var a=t.length>1?i.getCodePoint(t,0):t.charCodeAt(0);n=(p?"&#x"+a.toString(16):"&#"+a)+";"}return n}))};var d={scope:"body",level:"all"},m=/&(?:#\d+|#[xX][\da-fA-F]+|[0-9a-zA-Z]+);/g,g=/&(?:#\d+|#[xX][\da-fA-F]+|[0-9a-zA-Z]+)[;=]?/g,f={xml:{strict:m,attribute:g,body:r.bodyRegExps.xml},html4:{strict:m,attribute:g,body:r.bodyRegExps.html4},html5:{strict:m,attribute:g,body:r.bodyRegExps.html5}},p=e(e({},f),{all:f.html5}),b=String.fromCharCode,h=b(65533),v={level:"all"};function y(t,n,a,e){var r=t,o=t[t.length-1];if(a&&"="===o)r=t;else if(e&&";"!==o)r=t;else{var s=n[t];if(s)r=s;else if("&"===t[0]&&"#"===t[1]){var u=t[2],c="x"==u||"X"==u?parseInt(t.substr(3),16):parseInt(t.substr(2));r=c>=1114111?h:c>65535?i.fromCodePoint(c):b(l.numericUnicodeMap[c]||c)}}return r}n.decodeEntity=function(t,n){var a=(void 0===n?v:n).level;return t?y(t,o[void 0===a?"all":a].entities,!1,!1):""},n.decode=function(t,n){var a=void 0===n?d:n,e=a.level,r=void 0===e?"all":e,l=a.scope,i=void 0===l?"xml"===r?"strict":"body":l;if(!t)return"";var u=p[r][i],c=o[r].entities,m="attribute"===i,g="strict"===i;return s(t,u,(function(t){return y(t,c,m,g)}))}},2279:function(t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.bodyRegExps={xml:/&(?:#\d+|#[xX][\da-fA-F]+|[0-9a-zA-Z]+);?/g,html4:/&notin;|&(?:nbsp|iexcl|cent|pound|curren|yen|brvbar|sect|uml|copy|ordf|laquo|not|shy|reg|macr|deg|plusmn|sup2|sup3|acute|micro|para|middot|cedil|sup1|ordm|raquo|frac14|frac12|frac34|iquest|Agrave|Aacute|Acirc|Atilde|Auml|Aring|AElig|Ccedil|Egrave|Eacute|Ecirc|Euml|Igrave|Iacute|Icirc|Iuml|ETH|Ntilde|Ograve|Oacute|Ocirc|Otilde|Ouml|times|Oslash|Ugrave|Uacute|Ucirc|Uuml|Yacute|THORN|szlig|agrave|aacute|acirc|atilde|auml|aring|aelig|ccedil|egrave|eacute|ecirc|euml|igrave|iacute|icirc|iuml|eth|ntilde|ograve|oacute|ocirc|otilde|ouml|divide|oslash|ugrave|uacute|ucirc|uuml|yacute|thorn|yuml|quot|amp|lt|gt|#\d+|#[xX][\da-fA-F]+|[0-9a-zA-Z]+);?/g,html5:/&centerdot;|&copysr;|&divideontimes;|&gtcc;|&gtcir;|&gtdot;|&gtlPar;|&gtquest;|&gtrapprox;|&gtrarr;|&gtrdot;|&gtreqless;|&gtreqqless;|&gtrless;|&gtrsim;|&ltcc;|&ltcir;|&ltdot;|&lthree;|&ltimes;|&ltlarr;|&ltquest;|&ltrPar;|&ltri;|&ltrie;|&ltrif;|&notin;|&notinE;|&notindot;|&notinva;|&notinvb;|&notinvc;|&notni;|&notniva;|&notnivb;|&notnivc;|&parallel;|&timesb;|&timesbar;|&timesd;|&(?:AElig|AMP|Aacute|Acirc|Agrave|Aring|Atilde|Auml|COPY|Ccedil|ETH|Eacute|Ecirc|Egrave|Euml|GT|Iacute|Icirc|Igrave|Iuml|LT|Ntilde|Oacute|Ocirc|Ograve|Oslash|Otilde|Ouml|QUOT|REG|THORN|Uacute|Ucirc|Ugrave|Uuml|Yacute|aacute|acirc|acute|aelig|agrave|amp|aring|atilde|auml|brvbar|ccedil|cedil|cent|copy|curren|deg|divide|eacute|ecirc|egrave|eth|euml|frac12|frac14|frac34|gt|iacute|icirc|iexcl|igrave|iquest|iuml|laquo|lt|macr|micro|middot|nbsp|not|ntilde|oacute|ocirc|ograve|ordf|ordm|oslash|otilde|ouml|para|plusmn|pound|quot|raquo|reg|sect|shy|sup1|sup2|sup3|szlig|thorn|times|uacute|ucirc|ugrave|uml|uuml|yacute|yen|yuml|#\d+|#[xX][\da-fA-F]+|[0-9a-zA-Z]+);?/g},n.namedReferences={xml:{entities:{"&lt;":"<","&gt;":">","&quot;":'"',"&apos;":"'","&amp;":"&"},characters:{"<":"&lt;",">":"&gt;",'"':"&quot;","'":"&apos;","&":"&amp;"}},html4:{entities:{"&apos;":"'","&nbsp":" ","&nbsp;":" ","&iexcl":"¡","&iexcl;":"¡","&cent":"¢","&cent;":"¢","&pound":"£","&pound;":"£","&curren":"¤","&curren;":"¤","&yen":"¥","&yen;":"¥","&brvbar":"¦","&brvbar;":"¦","&sect":"§","&sect;":"§","&uml":"¨","&uml;":"¨","&copy":"©","&copy;":"©","&ordf":"ª","&ordf;":"ª","&laquo":"«","&laquo;":"«","&not":"¬","&not;":"¬","&shy":"­","&shy;":"­","&reg":"®","&reg;":"®","&macr":"¯","&macr;":"¯","&deg":"°","&deg;":"°","&plusmn":"±","&plusmn;":"±","&sup2":"²","&sup2;":"²","&sup3":"³","&sup3;":"³","&acute":"´","&acute;":"´","&micro":"µ","&micro;":"µ","&para":"¶","&para;":"¶","&middot":"·","&middot;":"·","&cedil":"¸","&cedil;":"¸","&sup1":"¹","&sup1;":"¹","&ordm":"º","&ordm;":"º","&raquo":"»","&raquo;":"»","&frac14":"¼","&frac14;":"¼","&frac12":"½","&frac12;":"½","&frac34":"¾","&frac34;":"¾","&iquest":"¿","&iquest;":"¿","&Agrave":"À","&Agrave;":"À","&Aacute":"Á","&Aacute;":"Á","&Acirc":"Â","&Acirc;":"Â","&Atilde":"Ã","&Atilde;":"Ã","&Auml":"Ä","&Auml;":"Ä","&Aring":"Å","&Aring;":"Å","&AElig":"Æ","&AElig;":"Æ","&Ccedil":"Ç","&Ccedil;":"Ç","&Egrave":"È","&Egrave;":"È","&Eacute":"É","&Eacute;":"É","&Ecirc":"Ê","&Ecirc;":"Ê","&Euml":"Ë","&Euml;":"Ë","&Igrave":"Ì","&Igrave;":"Ì","&Iacute":"Í","&Iacute;":"Í","&Icirc":"Î","&Icirc;":"Î","&Iuml":"Ï","&Iuml;":"Ï","&ETH":"Ð","&ETH;":"Ð","&Ntilde":"Ñ","&Ntilde;":"Ñ","&Ograve":"Ò","&Ograve;":"Ò","&Oacute":"Ó","&Oacute;":"Ó","&Ocirc":"Ô","&Ocirc;":"Ô","&Otilde":"Õ","&Otilde;":"Õ","&Ouml":"Ö","&Ouml;":"Ö","&times":"×","&times;":"×","&Oslash":"Ø","&Oslash;":"Ø","&Ugrave":"Ù","&Ugrave;":"Ù","&Uacute":"Ú","&Uacute;":"Ú","&Ucirc":"Û","&Ucirc;":"Û","&Uuml":"Ü","&Uuml;":"Ü","&Yacute":"Ý","&Yacute;":"Ý","&THORN":"Þ","&THORN;":"Þ","&szlig":"ß","&szlig;":"ß","&agrave":"à","&agrave;":"à","&aacute":"á","&aacute;":"á","&acirc":"â","&acirc;":"â","&atilde":"ã","&atilde;":"ã","&auml":"ä","&auml;":"ä","&aring":"å","&aring;":"å","&aelig":"æ","&aelig;":"æ","&ccedil":"ç","&ccedil;":"ç","&egrave":"è","&egrave;":"è","&eacute":"é","&eacute;":"é","&ecirc":"ê","&ecirc;":"ê","&euml":"ë","&euml;":"ë","&igrave":"ì","&igrave;":"ì","&iacute":"í","&iacute;":"í","&icirc":"î","&icirc;":"î","&iuml":"ï","&iuml;":"ï","&eth":"ð","&eth;":"ð","&ntilde":"ñ","&ntilde;":"ñ","&ograve":"ò","&ograve;":"ò","&oacute":"ó","&oacute;":"ó","&ocirc":"ô","&ocirc;":"ô","&otilde":"õ","&otilde;":"õ","&ouml":"ö","&ouml;":"ö","&divide":"÷","&divide;":"÷","&oslash":"ø","&oslash;":"ø","&ugrave":"ù","&ugrave;":"ù","&uacute":"ú","&uacute;":"ú","&ucirc":"û","&ucirc;":"û","&uuml":"ü","&uuml;":"ü","&yacute":"ý","&yacute;":"ý","&thorn":"þ","&thorn;":"þ","&yuml":"ÿ","&yuml;":"ÿ","&quot":'"',"&quot;":'"',"&amp":"&","&amp;":"&","&lt":"<","&lt;":"<","&gt":">","&gt;":">","&OElig;":"Œ","&oelig;":"œ","&Scaron;":"Š","&scaron;":"š","&Yuml;":"Ÿ","&circ;":"ˆ","&tilde;":"˜","&ensp;":" ","&emsp;":" ","&thinsp;":" ","&zwnj;":"‌","&zwj;":"‍","&lrm;":"‎","&rlm;":"‏","&ndash;":"–","&mdash;":"—","&lsquo;":"‘","&rsquo;":"’","&sbquo;":"‚","&ldquo;":"“","&rdquo;":"”","&bdquo;":"„","&dagger;":"†","&Dagger;":"‡","&permil;":"‰","&lsaquo;":"‹","&rsaquo;":"›","&euro;":"€","&fnof;":"ƒ","&Alpha;":"Α","&Beta;":"Β","&Gamma;":"Γ","&Delta;":"Δ","&Epsilon;":"Ε","&Zeta;":"Ζ","&Eta;":"Η","&Theta;":"Θ","&Iota;":"Ι","&Kappa;":"Κ","&Lambda;":"Λ","&Mu;":"Μ","&Nu;":"Ν","&Xi;":"Ξ","&Omicron;":"Ο","&Pi;":"Π","&Rho;":"Ρ","&Sigma;":"Σ","&Tau;":"Τ","&Upsilon;":"Υ","&Phi;":"Φ","&Chi;":"Χ","&Psi;":"Ψ","&Omega;":"Ω","&alpha;":"α","&beta;":"β","&gamma;":"γ","&delta;":"δ","&epsilon;":"ε","&zeta;":"ζ","&eta;":"η","&theta;":"θ","&iota;":"ι","&kappa;":"κ","&lambda;":"λ","&mu;":"μ","&nu;":"ν","&xi;":"ξ","&omicron;":"ο","&pi;":"π","&rho;":"ρ","&sigmaf;":"ς","&sigma;":"σ","&tau;":"τ","&upsilon;":"υ","&phi;":"φ","&chi;":"χ","&psi;":"ψ","&omega;":"ω","&thetasym;":"ϑ","&upsih;":"ϒ","&piv;":"ϖ","&bull;":"•","&hellip;":"…","&prime;":"′","&Prime;":"″","&oline;":"‾","&frasl;":"⁄","&weierp;":"℘","&image;":"ℑ","&real;":"ℜ","&trade;":"™","&alefsym;":"ℵ","&larr;":"←","&uarr;":"↑","&rarr;":"→","&darr;":"↓","&harr;":"↔","&crarr;":"↵","&lArr;":"⇐","&uArr;":"⇑","&rArr;":"⇒","&dArr;":"⇓","&hArr;":"⇔","&forall;":"∀","&part;":"∂","&exist;":"∃","&empty;":"∅","&nabla;":"∇","&isin;":"∈","&notin;":"∉","&ni;":"∋","&prod;":"∏","&sum;":"∑","&minus;":"−","&lowast;":"∗","&radic;":"√","&prop;":"∝","&infin;":"∞","&ang;":"∠","&and;":"∧","&or;":"∨","&cap;":"∩","&cup;":"∪","&int;":"∫","&there4;":"∴","&sim;":"∼","&cong;":"≅","&asymp;":"≈","&ne;":"≠","&equiv;":"≡","&le;":"≤","&ge;":"≥","&sub;":"⊂","&sup;":"⊃","&nsub;":"⊄","&sube;":"⊆","&supe;":"⊇","&oplus;":"⊕","&otimes;":"⊗","&perp;":"⊥","&sdot;":"⋅","&lceil;":"⌈","&rceil;":"⌉","&lfloor;":"⌊","&rfloor;":"⌋","&lang;":"〈","&rang;":"〉","&loz;":"◊","&spades;":"♠","&clubs;":"♣","&hearts;":"♥","&diams;":"♦"},characters:{"'":"&apos;"," ":"&nbsp;","¡":"&iexcl;","¢":"&cent;","£":"&pound;","¤":"&curren;","¥":"&yen;","¦":"&brvbar;","§":"&sect;","¨":"&uml;","©":"&copy;","ª":"&ordf;","«":"&laquo;","¬":"&not;","­":"&shy;","®":"&reg;","¯":"&macr;","°":"&deg;","±":"&plusmn;","²":"&sup2;","³":"&sup3;","´":"&acute;","µ":"&micro;","¶":"&para;","·":"&middot;","¸":"&cedil;","¹":"&sup1;","º":"&ordm;","»":"&raquo;","¼":"&frac14;","½":"&frac12;","¾":"&frac34;","¿":"&iquest;","À":"&Agrave;","Á":"&Aacute;","Â":"&Acirc;","Ã":"&Atilde;","Ä":"&Auml;","Å":"&Aring;","Æ":"&AElig;","Ç":"&Ccedil;","È":"&Egrave;","É":"&Eacute;","Ê":"&Ecirc;","Ë":"&Euml;","Ì":"&Igrave;","Í":"&Iacute;","Î":"&Icirc;","Ï":"&Iuml;","Ð":"&ETH;","Ñ":"&Ntilde;","Ò":"&Ograve;","Ó":"&Oacute;","Ô":"&Ocirc;","Õ":"&Otilde;","Ö":"&Ouml;","×":"&times;","Ø":"&Oslash;","Ù":"&Ugrave;","Ú":"&Uacute;","Û":"&Ucirc;","Ü":"&Uuml;","Ý":"&Yacute;","Þ":"&THORN;","ß":"&szlig;","à":"&agrave;","á":"&aacute;","â":"&acirc;","ã":"&atilde;","ä":"&auml;","å":"&aring;","æ":"&aelig;","ç":"&ccedil;","è":"&egrave;","é":"&eacute;","ê":"&ecirc;","ë":"&euml;","ì":"&igrave;","í":"&iacute;","î":"&icirc;","ï":"&iuml;","ð":"&eth;","ñ":"&ntilde;","ò":"&ograve;","ó":"&oacute;","ô":"&ocirc;","õ":"&otilde;","ö":"&ouml;","÷":"&divide;","ø":"&oslash;","ù":"&ugrave;","ú":"&uacute;","û":"&ucirc;","ü":"&uuml;","ý":"&yacute;","þ":"&thorn;","ÿ":"&yuml;",'"':"&quot;","&":"&amp;","<":"&lt;",">":"&gt;","Œ":"&OElig;","œ":"&oelig;","Š":"&Scaron;","š":"&scaron;","Ÿ":"&Yuml;","ˆ":"&circ;","˜":"&tilde;"," ":"&ensp;"," ":"&emsp;"," ":"&thinsp;","‌":"&zwnj;","‍":"&zwj;","‎":"&lrm;","‏":"&rlm;","–":"&ndash;","—":"&mdash;","‘":"&lsquo;","’":"&rsquo;","‚":"&sbquo;","“":"&ldquo;","”":"&rdquo;","„":"&bdquo;","†":"&dagger;","‡":"&Dagger;","‰":"&permil;","‹":"&lsaquo;","›":"&rsaquo;","€":"&euro;","ƒ":"&fnof;","Α":"&Alpha;","Β":"&Beta;","Γ":"&Gamma;","Δ":"&Delta;","Ε":"&Epsilon;","Ζ":"&Zeta;","Η":"&Eta;","Θ":"&Theta;","Ι":"&Iota;","Κ":"&Kappa;","Λ":"&Lambda;","Μ":"&Mu;","Ν":"&Nu;","Ξ":"&Xi;","Ο":"&Omicron;","Π":"&Pi;","Ρ":"&Rho;","Σ":"&Sigma;","Τ":"&Tau;","Υ":"&Upsilon;","Φ":"&Phi;","Χ":"&Chi;","Ψ":"&Psi;","Ω":"&Omega;","α":"&alpha;","β":"&beta;","γ":"&gamma;","δ":"&delta;","ε":"&epsilon;","ζ":"&zeta;","η":"&eta;","θ":"&theta;","ι":"&iota;","κ":"&kappa;","λ":"&lambda;","μ":"&mu;","ν":"&nu;","ξ":"&xi;","ο":"&omicron;","π":"&pi;","ρ":"&rho;","ς":"&sigmaf;","σ":"&sigma;","τ":"&tau;","υ":"&upsilon;","φ":"&phi;","χ":"&chi;","ψ":"&psi;","ω":"&omega;","ϑ":"&thetasym;","ϒ":"&upsih;","ϖ":"&piv;","•":"&bull;","…":"&hellip;","′":"&prime;","″":"&Prime;","‾":"&oline;","⁄":"&frasl;","℘":"&weierp;","ℑ":"&image;","ℜ":"&real;","™":"&trade;","ℵ":"&alefsym;","←":"&larr;","↑":"&uarr;","→":"&rarr;","↓":"&darr;","↔":"&harr;","↵":"&crarr;","⇐":"&lArr;","⇑":"&uArr;","⇒":"&rArr;","⇓":"&dArr;","⇔":"&hArr;","∀":"&forall;","∂":"&part;","∃":"&exist;","∅":"&empty;","∇":"&nabla;","∈":"&isin;","∉":"&notin;","∋":"&ni;","∏":"&prod;","∑":"&sum;","−":"&minus;","∗":"&lowast;","√":"&radic;","∝":"&prop;","∞":"&infin;","∠":"&ang;","∧":"&and;","∨":"&or;","∩":"&cap;","∪":"&cup;","∫":"&int;","∴":"&there4;","∼":"&sim;","≅":"&cong;","≈":"&asymp;","≠":"&ne;","≡":"&equiv;","≤":"&le;","≥":"&ge;","⊂":"&sub;","⊃":"&sup;","⊄":"&nsub;","⊆":"&sube;","⊇":"&supe;","⊕":"&oplus;","⊗":"&otimes;","⊥":"&perp;","⋅":"&sdot;","⌈":"&lceil;","⌉":"&rceil;","⌊":"&lfloor;","⌋":"&rfloor;","〈":"&lang;","〉":"&rang;","◊":"&loz;","♠":"&spades;","♣":"&clubs;","♥":"&hearts;","♦":"&diams;"}},html5:{entities:{"&AElig":"Æ","&AElig;":"Æ","&AMP":"&","&AMP;":"&","&Aacute":"Á","&Aacute;":"Á","&Abreve;":"Ă","&Acirc":"Â","&Acirc;":"Â","&Acy;":"А","&Afr;":"𝔄","&Agrave":"À","&Agrave;":"À","&Alpha;":"Α","&Amacr;":"Ā","&And;":"⩓","&Aogon;":"Ą","&Aopf;":"𝔸","&ApplyFunction;":"⁡","&Aring":"Å","&Aring;":"Å","&Ascr;":"𝒜","&Assign;":"≔","&Atilde":"Ã","&Atilde;":"Ã","&Auml":"Ä","&Auml;":"Ä","&Backslash;":"∖","&Barv;":"⫧","&Barwed;":"⌆","&Bcy;":"Б","&Because;":"∵","&Bernoullis;":"ℬ","&Beta;":"Β","&Bfr;":"𝔅","&Bopf;":"𝔹","&Breve;":"˘","&Bscr;":"ℬ","&Bumpeq;":"≎","&CHcy;":"Ч","&COPY":"©","&COPY;":"©","&Cacute;":"Ć","&Cap;":"⋒","&CapitalDifferentialD;":"ⅅ","&Cayleys;":"ℭ","&Ccaron;":"Č","&Ccedil":"Ç","&Ccedil;":"Ç","&Ccirc;":"Ĉ","&Cconint;":"∰","&Cdot;":"Ċ","&Cedilla;":"¸","&CenterDot;":"·","&Cfr;":"ℭ","&Chi;":"Χ","&CircleDot;":"⊙","&CircleMinus;":"⊖","&CirclePlus;":"⊕","&CircleTimes;":"⊗","&ClockwiseContourIntegral;":"∲","&CloseCurlyDoubleQuote;":"”","&CloseCurlyQuote;":"’","&Colon;":"∷","&Colone;":"⩴","&Congruent;":"≡","&Conint;":"∯","&ContourIntegral;":"∮","&Copf;":"ℂ","&Coproduct;":"∐","&CounterClockwiseContourIntegral;":"∳","&Cross;":"⨯","&Cscr;":"𝒞","&Cup;":"⋓","&CupCap;":"≍","&DD;":"ⅅ","&DDotrahd;":"⤑","&DJcy;":"Ђ","&DScy;":"Ѕ","&DZcy;":"Џ","&Dagger;":"‡","&Darr;":"↡","&Dashv;":"⫤","&Dcaron;":"Ď","&Dcy;":"Д","&Del;":"∇","&Delta;":"Δ","&Dfr;":"𝔇","&DiacriticalAcute;":"´","&DiacriticalDot;":"˙","&DiacriticalDoubleAcute;":"˝","&DiacriticalGrave;":"`","&DiacriticalTilde;":"˜","&Diamond;":"⋄","&DifferentialD;":"ⅆ","&Dopf;":"𝔻","&Dot;":"¨","&DotDot;":"⃜","&DotEqual;":"≐","&DoubleContourIntegral;":"∯","&DoubleDot;":"¨","&DoubleDownArrow;":"⇓","&DoubleLeftArrow;":"⇐","&DoubleLeftRightArrow;":"⇔","&DoubleLeftTee;":"⫤","&DoubleLongLeftArrow;":"⟸","&DoubleLongLeftRightArrow;":"⟺","&DoubleLongRightArrow;":"⟹","&DoubleRightArrow;":"⇒","&DoubleRightTee;":"⊨","&DoubleUpArrow;":"⇑","&DoubleUpDownArrow;":"⇕","&DoubleVerticalBar;":"∥","&DownArrow;":"↓","&DownArrowBar;":"⤓","&DownArrowUpArrow;":"⇵","&DownBreve;":"̑","&DownLeftRightVector;":"⥐","&DownLeftTeeVector;":"⥞","&DownLeftVector;":"↽","&DownLeftVectorBar;":"⥖","&DownRightTeeVector;":"⥟","&DownRightVector;":"⇁","&DownRightVectorBar;":"⥗","&DownTee;":"⊤","&DownTeeArrow;":"↧","&Downarrow;":"⇓","&Dscr;":"𝒟","&Dstrok;":"Đ","&ENG;":"Ŋ","&ETH":"Ð","&ETH;":"Ð","&Eacute":"É","&Eacute;":"É","&Ecaron;":"Ě","&Ecirc":"Ê","&Ecirc;":"Ê","&Ecy;":"Э","&Edot;":"Ė","&Efr;":"𝔈","&Egrave":"È","&Egrave;":"È","&Element;":"∈","&Emacr;":"Ē","&EmptySmallSquare;":"◻","&EmptyVerySmallSquare;":"▫","&Eogon;":"Ę","&Eopf;":"𝔼","&Epsilon;":"Ε","&Equal;":"⩵","&EqualTilde;":"≂","&Equilibrium;":"⇌","&Escr;":"ℰ","&Esim;":"⩳","&Eta;":"Η","&Euml":"Ë","&Euml;":"Ë","&Exists;":"∃","&ExponentialE;":"ⅇ","&Fcy;":"Ф","&Ffr;":"𝔉","&FilledSmallSquare;":"◼","&FilledVerySmallSquare;":"▪","&Fopf;":"𝔽","&ForAll;":"∀","&Fouriertrf;":"ℱ","&Fscr;":"ℱ","&GJcy;":"Ѓ","&GT":">","&GT;":">","&Gamma;":"Γ","&Gammad;":"Ϝ","&Gbreve;":"Ğ","&Gcedil;":"Ģ","&Gcirc;":"Ĝ","&Gcy;":"Г","&Gdot;":"Ġ","&Gfr;":"𝔊","&Gg;":"⋙","&Gopf;":"𝔾","&GreaterEqual;":"≥","&GreaterEqualLess;":"⋛","&GreaterFullEqual;":"≧","&GreaterGreater;":"⪢","&GreaterLess;":"≷","&GreaterSlantEqual;":"⩾","&GreaterTilde;":"≳","&Gscr;":"𝒢","&Gt;":"≫","&HARDcy;":"Ъ","&Hacek;":"ˇ","&Hat;":"^","&Hcirc;":"Ĥ","&Hfr;":"ℌ","&HilbertSpace;":"ℋ","&Hopf;":"ℍ","&HorizontalLine;":"─","&Hscr;":"ℋ","&Hstrok;":"Ħ","&HumpDownHump;":"≎","&HumpEqual;":"≏","&IEcy;":"Е","&IJlig;":"Ĳ","&IOcy;":"Ё","&Iacute":"Í","&Iacute;":"Í","&Icirc":"Î","&Icirc;":"Î","&Icy;":"И","&Idot;":"İ","&Ifr;":"ℑ","&Igrave":"Ì","&Igrave;":"Ì","&Im;":"ℑ","&Imacr;":"Ī","&ImaginaryI;":"ⅈ","&Implies;":"⇒","&Int;":"∬","&Integral;":"∫","&Intersection;":"⋂","&InvisibleComma;":"⁣","&InvisibleTimes;":"⁢","&Iogon;":"Į","&Iopf;":"𝕀","&Iota;":"Ι","&Iscr;":"ℐ","&Itilde;":"Ĩ","&Iukcy;":"І","&Iuml":"Ï","&Iuml;":"Ï","&Jcirc;":"Ĵ","&Jcy;":"Й","&Jfr;":"𝔍","&Jopf;":"𝕁","&Jscr;":"𝒥","&Jsercy;":"Ј","&Jukcy;":"Є","&KHcy;":"Х","&KJcy;":"Ќ","&Kappa;":"Κ","&Kcedil;":"Ķ","&Kcy;":"К","&Kfr;":"𝔎","&Kopf;":"𝕂","&Kscr;":"𝒦","&LJcy;":"Љ","&LT":"<","&LT;":"<","&Lacute;":"Ĺ","&Lambda;":"Λ","&Lang;":"⟪","&Laplacetrf;":"ℒ","&Larr;":"↞","&Lcaron;":"Ľ","&Lcedil;":"Ļ","&Lcy;":"Л","&LeftAngleBracket;":"⟨","&LeftArrow;":"←","&LeftArrowBar;":"⇤","&LeftArrowRightArrow;":"⇆","&LeftCeiling;":"⌈","&LeftDoubleBracket;":"⟦","&LeftDownTeeVector;":"⥡","&LeftDownVector;":"⇃","&LeftDownVectorBar;":"⥙","&LeftFloor;":"⌊","&LeftRightArrow;":"↔","&LeftRightVector;":"⥎","&LeftTee;":"⊣","&LeftTeeArrow;":"↤","&LeftTeeVector;":"⥚","&LeftTriangle;":"⊲","&LeftTriangleBar;":"⧏","&LeftTriangleEqual;":"⊴","&LeftUpDownVector;":"⥑","&LeftUpTeeVector;":"⥠","&LeftUpVector;":"↿","&LeftUpVectorBar;":"⥘","&LeftVector;":"↼","&LeftVectorBar;":"⥒","&Leftarrow;":"⇐","&Leftrightarrow;":"⇔","&LessEqualGreater;":"⋚","&LessFullEqual;":"≦","&LessGreater;":"≶","&LessLess;":"⪡","&LessSlantEqual;":"⩽","&LessTilde;":"≲","&Lfr;":"𝔏","&Ll;":"⋘","&Lleftarrow;":"⇚","&Lmidot;":"Ŀ","&LongLeftArrow;":"⟵","&LongLeftRightArrow;":"⟷","&LongRightArrow;":"⟶","&Longleftarrow;":"⟸","&Longleftrightarrow;":"⟺","&Longrightarrow;":"⟹","&Lopf;":"𝕃","&LowerLeftArrow;":"↙","&LowerRightArrow;":"↘","&Lscr;":"ℒ","&Lsh;":"↰","&Lstrok;":"Ł","&Lt;":"≪","&Map;":"⤅","&Mcy;":"М","&MediumSpace;":" ","&Mellintrf;":"ℳ","&Mfr;":"𝔐","&MinusPlus;":"∓","&Mopf;":"𝕄","&Mscr;":"ℳ","&Mu;":"Μ","&NJcy;":"Њ","&Nacute;":"Ń","&Ncaron;":"Ň","&Ncedil;":"Ņ","&Ncy;":"Н","&NegativeMediumSpace;":"​","&NegativeThickSpace;":"​","&NegativeThinSpace;":"​","&NegativeVeryThinSpace;":"​","&NestedGreaterGreater;":"≫","&NestedLessLess;":"≪","&NewLine;":"\n","&Nfr;":"𝔑","&NoBreak;":"⁠","&NonBreakingSpace;":" ","&Nopf;":"ℕ","&Not;":"⫬","&NotCongruent;":"≢","&NotCupCap;":"≭","&NotDoubleVerticalBar;":"∦","&NotElement;":"∉","&NotEqual;":"≠","&NotEqualTilde;":"≂̸","&NotExists;":"∄","&NotGreater;":"≯","&NotGreaterEqual;":"≱","&NotGreaterFullEqual;":"≧̸","&NotGreaterGreater;":"≫̸","&NotGreaterLess;":"≹","&NotGreaterSlantEqual;":"⩾̸","&NotGreaterTilde;":"≵","&NotHumpDownHump;":"≎̸","&NotHumpEqual;":"≏̸","&NotLeftTriangle;":"⋪","&NotLeftTriangleBar;":"⧏̸","&NotLeftTriangleEqual;":"⋬","&NotLess;":"≮","&NotLessEqual;":"≰","&NotLessGreater;":"≸","&NotLessLess;":"≪̸","&NotLessSlantEqual;":"⩽̸","&NotLessTilde;":"≴","&NotNestedGreaterGreater;":"⪢̸","&NotNestedLessLess;":"⪡̸","&NotPrecedes;":"⊀","&NotPrecedesEqual;":"⪯̸","&NotPrecedesSlantEqual;":"⋠","&NotReverseElement;":"∌","&NotRightTriangle;":"⋫","&NotRightTriangleBar;":"⧐̸","&NotRightTriangleEqual;":"⋭","&NotSquareSubset;":"⊏̸","&NotSquareSubsetEqual;":"⋢","&NotSquareSuperset;":"⊐̸","&NotSquareSupersetEqual;":"⋣","&NotSubset;":"⊂⃒","&NotSubsetEqual;":"⊈","&NotSucceeds;":"⊁","&NotSucceedsEqual;":"⪰̸","&NotSucceedsSlantEqual;":"⋡","&NotSucceedsTilde;":"≿̸","&NotSuperset;":"⊃⃒","&NotSupersetEqual;":"⊉","&NotTilde;":"≁","&NotTildeEqual;":"≄","&NotTildeFullEqual;":"≇","&NotTildeTilde;":"≉","&NotVerticalBar;":"∤","&Nscr;":"𝒩","&Ntilde":"Ñ","&Ntilde;":"Ñ","&Nu;":"Ν","&OElig;":"Œ","&Oacute":"Ó","&Oacute;":"Ó","&Ocirc":"Ô","&Ocirc;":"Ô","&Ocy;":"О","&Odblac;":"Ő","&Ofr;":"𝔒","&Ograve":"Ò","&Ograve;":"Ò","&Omacr;":"Ō","&Omega;":"Ω","&Omicron;":"Ο","&Oopf;":"𝕆","&OpenCurlyDoubleQuote;":"“","&OpenCurlyQuote;":"‘","&Or;":"⩔","&Oscr;":"𝒪","&Oslash":"Ø","&Oslash;":"Ø","&Otilde":"Õ","&Otilde;":"Õ","&Otimes;":"⨷","&Ouml":"Ö","&Ouml;":"Ö","&OverBar;":"‾","&OverBrace;":"⏞","&OverBracket;":"⎴","&OverParenthesis;":"⏜","&PartialD;":"∂","&Pcy;":"П","&Pfr;":"𝔓","&Phi;":"Φ","&Pi;":"Π","&PlusMinus;":"±","&Poincareplane;":"ℌ","&Popf;":"ℙ","&Pr;":"⪻","&Precedes;":"≺","&PrecedesEqual;":"⪯","&PrecedesSlantEqual;":"≼","&PrecedesTilde;":"≾","&Prime;":"″","&Product;":"∏","&Proportion;":"∷","&Proportional;":"∝","&Pscr;":"𝒫","&Psi;":"Ψ","&QUOT":'"',"&QUOT;":'"',"&Qfr;":"𝔔","&Qopf;":"ℚ","&Qscr;":"𝒬","&RBarr;":"⤐","&REG":"®","&REG;":"®","&Racute;":"Ŕ","&Rang;":"⟫","&Rarr;":"↠","&Rarrtl;":"⤖","&Rcaron;":"Ř","&Rcedil;":"Ŗ","&Rcy;":"Р","&Re;":"ℜ","&ReverseElement;":"∋","&ReverseEquilibrium;":"⇋","&ReverseUpEquilibrium;":"⥯","&Rfr;":"ℜ","&Rho;":"Ρ","&RightAngleBracket;":"⟩","&RightArrow;":"→","&RightArrowBar;":"⇥","&RightArrowLeftArrow;":"⇄","&RightCeiling;":"⌉","&RightDoubleBracket;":"⟧","&RightDownTeeVector;":"⥝","&RightDownVector;":"⇂","&RightDownVectorBar;":"⥕","&RightFloor;":"⌋","&RightTee;":"⊢","&RightTeeArrow;":"↦","&RightTeeVector;":"⥛","&RightTriangle;":"⊳","&RightTriangleBar;":"⧐","&RightTriangleEqual;":"⊵","&RightUpDownVector;":"⥏","&RightUpTeeVector;":"⥜","&RightUpVector;":"↾","&RightUpVectorBar;":"⥔","&RightVector;":"⇀","&RightVectorBar;":"⥓","&Rightarrow;":"⇒","&Ropf;":"ℝ","&RoundImplies;":"⥰","&Rrightarrow;":"⇛","&Rscr;":"ℛ","&Rsh;":"↱","&RuleDelayed;":"⧴","&SHCHcy;":"Щ","&SHcy;":"Ш","&SOFTcy;":"Ь","&Sacute;":"Ś","&Sc;":"⪼","&Scaron;":"Š","&Scedil;":"Ş","&Scirc;":"Ŝ","&Scy;":"С","&Sfr;":"𝔖","&ShortDownArrow;":"↓","&ShortLeftArrow;":"←","&ShortRightArrow;":"→","&ShortUpArrow;":"↑","&Sigma;":"Σ","&SmallCircle;":"∘","&Sopf;":"𝕊","&Sqrt;":"√","&Square;":"□","&SquareIntersection;":"⊓","&SquareSubset;":"⊏","&SquareSubsetEqual;":"⊑","&SquareSuperset;":"⊐","&SquareSupersetEqual;":"⊒","&SquareUnion;":"⊔","&Sscr;":"𝒮","&Star;":"⋆","&Sub;":"⋐","&Subset;":"⋐","&SubsetEqual;":"⊆","&Succeeds;":"≻","&SucceedsEqual;":"⪰","&SucceedsSlantEqual;":"≽","&SucceedsTilde;":"≿","&SuchThat;":"∋","&Sum;":"∑","&Sup;":"⋑","&Superset;":"⊃","&SupersetEqual;":"⊇","&Supset;":"⋑","&THORN":"Þ","&THORN;":"Þ","&TRADE;":"™","&TSHcy;":"Ћ","&TScy;":"Ц","&Tab;":"\t","&Tau;":"Τ","&Tcaron;":"Ť","&Tcedil;":"Ţ","&Tcy;":"Т","&Tfr;":"𝔗","&Therefore;":"∴","&Theta;":"Θ","&ThickSpace;":"  ","&ThinSpace;":" ","&Tilde;":"∼","&TildeEqual;":"≃","&TildeFullEqual;":"≅","&TildeTilde;":"≈","&Topf;":"𝕋","&TripleDot;":"⃛","&Tscr;":"𝒯","&Tstrok;":"Ŧ","&Uacute":"Ú","&Uacute;":"Ú","&Uarr;":"↟","&Uarrocir;":"⥉","&Ubrcy;":"Ў","&Ubreve;":"Ŭ","&Ucirc":"Û","&Ucirc;":"Û","&Ucy;":"У","&Udblac;":"Ű","&Ufr;":"𝔘","&Ugrave":"Ù","&Ugrave;":"Ù","&Umacr;":"Ū","&UnderBar;":"_","&UnderBrace;":"⏟","&UnderBracket;":"⎵","&UnderParenthesis;":"⏝","&Union;":"⋃","&UnionPlus;":"⊎","&Uogon;":"Ų","&Uopf;":"𝕌","&UpArrow;":"↑","&UpArrowBar;":"⤒","&UpArrowDownArrow;":"⇅","&UpDownArrow;":"↕","&UpEquilibrium;":"⥮","&UpTee;":"⊥","&UpTeeArrow;":"↥","&Uparrow;":"⇑","&Updownarrow;":"⇕","&UpperLeftArrow;":"↖","&UpperRightArrow;":"↗","&Upsi;":"ϒ","&Upsilon;":"Υ","&Uring;":"Ů","&Uscr;":"𝒰","&Utilde;":"Ũ","&Uuml":"Ü","&Uuml;":"Ü","&VDash;":"⊫","&Vbar;":"⫫","&Vcy;":"В","&Vdash;":"⊩","&Vdashl;":"⫦","&Vee;":"⋁","&Verbar;":"‖","&Vert;":"‖","&VerticalBar;":"∣","&VerticalLine;":"|","&VerticalSeparator;":"❘","&VerticalTilde;":"≀","&VeryThinSpace;":" ","&Vfr;":"𝔙","&Vopf;":"𝕍","&Vscr;":"𝒱","&Vvdash;":"⊪","&Wcirc;":"Ŵ","&Wedge;":"⋀","&Wfr;":"𝔚","&Wopf;":"𝕎","&Wscr;":"𝒲","&Xfr;":"𝔛","&Xi;":"Ξ","&Xopf;":"𝕏","&Xscr;":"𝒳","&YAcy;":"Я","&YIcy;":"Ї","&YUcy;":"Ю","&Yacute":"Ý","&Yacute;":"Ý","&Ycirc;":"Ŷ","&Ycy;":"Ы","&Yfr;":"𝔜","&Yopf;":"𝕐","&Yscr;":"𝒴","&Yuml;":"Ÿ","&ZHcy;":"Ж","&Zacute;":"Ź","&Zcaron;":"Ž","&Zcy;":"З","&Zdot;":"Ż","&ZeroWidthSpace;":"​","&Zeta;":"Ζ","&Zfr;":"ℨ","&Zopf;":"ℤ","&Zscr;":"𝒵","&aacute":"á","&aacute;":"á","&abreve;":"ă","&ac;":"∾","&acE;":"∾̳","&acd;":"∿","&acirc":"â","&acirc;":"â","&acute":"´","&acute;":"´","&acy;":"а","&aelig":"æ","&aelig;":"æ","&af;":"⁡","&afr;":"𝔞","&agrave":"à","&agrave;":"à","&alefsym;":"ℵ","&aleph;":"ℵ","&alpha;":"α","&amacr;":"ā","&amalg;":"⨿","&amp":"&","&amp;":"&","&and;":"∧","&andand;":"⩕","&andd;":"⩜","&andslope;":"⩘","&andv;":"⩚","&ang;":"∠","&ange;":"⦤","&angle;":"∠","&angmsd;":"∡","&angmsdaa;":"⦨","&angmsdab;":"⦩","&angmsdac;":"⦪","&angmsdad;":"⦫","&angmsdae;":"⦬","&angmsdaf;":"⦭","&angmsdag;":"⦮","&angmsdah;":"⦯","&angrt;":"∟","&angrtvb;":"⊾","&angrtvbd;":"⦝","&angsph;":"∢","&angst;":"Å","&angzarr;":"⍼","&aogon;":"ą","&aopf;":"𝕒","&ap;":"≈","&apE;":"⩰","&apacir;":"⩯","&ape;":"≊","&apid;":"≋","&apos;":"'","&approx;":"≈","&approxeq;":"≊","&aring":"å","&aring;":"å","&ascr;":"𝒶","&ast;":"*","&asymp;":"≈","&asympeq;":"≍","&atilde":"ã","&atilde;":"ã","&auml":"ä","&auml;":"ä","&awconint;":"∳","&awint;":"⨑","&bNot;":"⫭","&backcong;":"≌","&backepsilon;":"϶","&backprime;":"‵","&backsim;":"∽","&backsimeq;":"⋍","&barvee;":"⊽","&barwed;":"⌅","&barwedge;":"⌅","&bbrk;":"⎵","&bbrktbrk;":"⎶","&bcong;":"≌","&bcy;":"б","&bdquo;":"„","&becaus;":"∵","&because;":"∵","&bemptyv;":"⦰","&bepsi;":"϶","&bernou;":"ℬ","&beta;":"β","&beth;":"ℶ","&between;":"≬","&bfr;":"𝔟","&bigcap;":"⋂","&bigcirc;":"◯","&bigcup;":"⋃","&bigodot;":"⨀","&bigoplus;":"⨁","&bigotimes;":"⨂","&bigsqcup;":"⨆","&bigstar;":"★","&bigtriangledown;":"▽","&bigtriangleup;":"△","&biguplus;":"⨄","&bigvee;":"⋁","&bigwedge;":"⋀","&bkarow;":"⤍","&blacklozenge;":"⧫","&blacksquare;":"▪","&blacktriangle;":"▴","&blacktriangledown;":"▾","&blacktriangleleft;":"◂","&blacktriangleright;":"▸","&blank;":"␣","&blk12;":"▒","&blk14;":"░","&blk34;":"▓","&block;":"█","&bne;":"=⃥","&bnequiv;":"≡⃥","&bnot;":"⌐","&bopf;":"𝕓","&bot;":"⊥","&bottom;":"⊥","&bowtie;":"⋈","&boxDL;":"╗","&boxDR;":"╔","&boxDl;":"╖","&boxDr;":"╓","&boxH;":"═","&boxHD;":"╦","&boxHU;":"╩","&boxHd;":"╤","&boxHu;":"╧","&boxUL;":"╝","&boxUR;":"╚","&boxUl;":"╜","&boxUr;":"╙","&boxV;":"║","&boxVH;":"╬","&boxVL;":"╣","&boxVR;":"╠","&boxVh;":"╫","&boxVl;":"╢","&boxVr;":"╟","&boxbox;":"⧉","&boxdL;":"╕","&boxdR;":"╒","&boxdl;":"┐","&boxdr;":"┌","&boxh;":"─","&boxhD;":"╥","&boxhU;":"╨","&boxhd;":"┬","&boxhu;":"┴","&boxminus;":"⊟","&boxplus;":"⊞","&boxtimes;":"⊠","&boxuL;":"╛","&boxuR;":"╘","&boxul;":"┘","&boxur;":"└","&boxv;":"│","&boxvH;":"╪","&boxvL;":"╡","&boxvR;":"╞","&boxvh;":"┼","&boxvl;":"┤","&boxvr;":"├","&bprime;":"‵","&breve;":"˘","&brvbar":"¦","&brvbar;":"¦","&bscr;":"𝒷","&bsemi;":"⁏","&bsim;":"∽","&bsime;":"⋍","&bsol;":"\\","&bsolb;":"⧅","&bsolhsub;":"⟈","&bull;":"•","&bullet;":"•","&bump;":"≎","&bumpE;":"⪮","&bumpe;":"≏","&bumpeq;":"≏","&cacute;":"ć","&cap;":"∩","&capand;":"⩄","&capbrcup;":"⩉","&capcap;":"⩋","&capcup;":"⩇","&capdot;":"⩀","&caps;":"∩︀","&caret;":"⁁","&caron;":"ˇ","&ccaps;":"⩍","&ccaron;":"č","&ccedil":"ç","&ccedil;":"ç","&ccirc;":"ĉ","&ccups;":"⩌","&ccupssm;":"⩐","&cdot;":"ċ","&cedil":"¸","&cedil;":"¸","&cemptyv;":"⦲","&cent":"¢","&cent;":"¢","&centerdot;":"·","&cfr;":"𝔠","&chcy;":"ч","&check;":"✓","&checkmark;":"✓","&chi;":"χ","&cir;":"○","&cirE;":"⧃","&circ;":"ˆ","&circeq;":"≗","&circlearrowleft;":"↺","&circlearrowright;":"↻","&circledR;":"®","&circledS;":"Ⓢ","&circledast;":"⊛","&circledcirc;":"⊚","&circleddash;":"⊝","&cire;":"≗","&cirfnint;":"⨐","&cirmid;":"⫯","&cirscir;":"⧂","&clubs;":"♣","&clubsuit;":"♣","&colon;":":","&colone;":"≔","&coloneq;":"≔","&comma;":",","&commat;":"@","&comp;":"∁","&compfn;":"∘","&complement;":"∁","&complexes;":"ℂ","&cong;":"≅","&congdot;":"⩭","&conint;":"∮","&copf;":"𝕔","&coprod;":"∐","&copy":"©","&copy;":"©","&copysr;":"℗","&crarr;":"↵","&cross;":"✗","&cscr;":"𝒸","&csub;":"⫏","&csube;":"⫑","&csup;":"⫐","&csupe;":"⫒","&ctdot;":"⋯","&cudarrl;":"⤸","&cudarrr;":"⤵","&cuepr;":"⋞","&cuesc;":"⋟","&cularr;":"↶","&cularrp;":"⤽","&cup;":"∪","&cupbrcap;":"⩈","&cupcap;":"⩆","&cupcup;":"⩊","&cupdot;":"⊍","&cupor;":"⩅","&cups;":"∪︀","&curarr;":"↷","&curarrm;":"⤼","&curlyeqprec;":"⋞","&curlyeqsucc;":"⋟","&curlyvee;":"⋎","&curlywedge;":"⋏","&curren":"¤","&curren;":"¤","&curvearrowleft;":"↶","&curvearrowright;":"↷","&cuvee;":"⋎","&cuwed;":"⋏","&cwconint;":"∲","&cwint;":"∱","&cylcty;":"⌭","&dArr;":"⇓","&dHar;":"⥥","&dagger;":"†","&daleth;":"ℸ","&darr;":"↓","&dash;":"‐","&dashv;":"⊣","&dbkarow;":"⤏","&dblac;":"˝","&dcaron;":"ď","&dcy;":"д","&dd;":"ⅆ","&ddagger;":"‡","&ddarr;":"⇊","&ddotseq;":"⩷","&deg":"°","&deg;":"°","&delta;":"δ","&demptyv;":"⦱","&dfisht;":"⥿","&dfr;":"𝔡","&dharl;":"⇃","&dharr;":"⇂","&diam;":"⋄","&diamond;":"⋄","&diamondsuit;":"♦","&diams;":"♦","&die;":"¨","&digamma;":"ϝ","&disin;":"⋲","&div;":"÷","&divide":"÷","&divide;":"÷","&divideontimes;":"⋇","&divonx;":"⋇","&djcy;":"ђ","&dlcorn;":"⌞","&dlcrop;":"⌍","&dollar;":"$","&dopf;":"𝕕","&dot;":"˙","&doteq;":"≐","&doteqdot;":"≑","&dotminus;":"∸","&dotplus;":"∔","&dotsquare;":"⊡","&doublebarwedge;":"⌆","&downarrow;":"↓","&downdownarrows;":"⇊","&downharpoonleft;":"⇃","&downharpoonright;":"⇂","&drbkarow;":"⤐","&drcorn;":"⌟","&drcrop;":"⌌","&dscr;":"𝒹","&dscy;":"ѕ","&dsol;":"⧶","&dstrok;":"đ","&dtdot;":"⋱","&dtri;":"▿","&dtrif;":"▾","&duarr;":"⇵","&duhar;":"⥯","&dwangle;":"⦦","&dzcy;":"џ","&dzigrarr;":"⟿","&eDDot;":"⩷","&eDot;":"≑","&eacute":"é","&eacute;":"é","&easter;":"⩮","&ecaron;":"ě","&ecir;":"≖","&ecirc":"ê","&ecirc;":"ê","&ecolon;":"≕","&ecy;":"э","&edot;":"ė","&ee;":"ⅇ","&efDot;":"≒","&efr;":"𝔢","&eg;":"⪚","&egrave":"è","&egrave;":"è","&egs;":"⪖","&egsdot;":"⪘","&el;":"⪙","&elinters;":"⏧","&ell;":"ℓ","&els;":"⪕","&elsdot;":"⪗","&emacr;":"ē","&empty;":"∅","&emptyset;":"∅","&emptyv;":"∅","&emsp13;":" ","&emsp14;":" ","&emsp;":" ","&eng;":"ŋ","&ensp;":" ","&eogon;":"ę","&eopf;":"𝕖","&epar;":"⋕","&eparsl;":"⧣","&eplus;":"⩱","&epsi;":"ε","&epsilon;":"ε","&epsiv;":"ϵ","&eqcirc;":"≖","&eqcolon;":"≕","&eqsim;":"≂","&eqslantgtr;":"⪖","&eqslantless;":"⪕","&equals;":"=","&equest;":"≟","&equiv;":"≡","&equivDD;":"⩸","&eqvparsl;":"⧥","&erDot;":"≓","&erarr;":"⥱","&escr;":"ℯ","&esdot;":"≐","&esim;":"≂","&eta;":"η","&eth":"ð","&eth;":"ð","&euml":"ë","&euml;":"ë","&euro;":"€","&excl;":"!","&exist;":"∃","&expectation;":"ℰ","&exponentiale;":"ⅇ","&fallingdotseq;":"≒","&fcy;":"ф","&female;":"♀","&ffilig;":"ﬃ","&fflig;":"ﬀ","&ffllig;":"ﬄ","&ffr;":"𝔣","&filig;":"ﬁ","&fjlig;":"fj","&flat;":"♭","&fllig;":"ﬂ","&fltns;":"▱","&fnof;":"ƒ","&fopf;":"𝕗","&forall;":"∀","&fork;":"⋔","&forkv;":"⫙","&fpartint;":"⨍","&frac12":"½","&frac12;":"½","&frac13;":"⅓","&frac14":"¼","&frac14;":"¼","&frac15;":"⅕","&frac16;":"⅙","&frac18;":"⅛","&frac23;":"⅔","&frac25;":"⅖","&frac34":"¾","&frac34;":"¾","&frac35;":"⅗","&frac38;":"⅜","&frac45;":"⅘","&frac56;":"⅚","&frac58;":"⅝","&frac78;":"⅞","&frasl;":"⁄","&frown;":"⌢","&fscr;":"𝒻","&gE;":"≧","&gEl;":"⪌","&gacute;":"ǵ","&gamma;":"γ","&gammad;":"ϝ","&gap;":"⪆","&gbreve;":"ğ","&gcirc;":"ĝ","&gcy;":"г","&gdot;":"ġ","&ge;":"≥","&gel;":"⋛","&geq;":"≥","&geqq;":"≧","&geqslant;":"⩾","&ges;":"⩾","&gescc;":"⪩","&gesdot;":"⪀","&gesdoto;":"⪂","&gesdotol;":"⪄","&gesl;":"⋛︀","&gesles;":"⪔","&gfr;":"𝔤","&gg;":"≫","&ggg;":"⋙","&gimel;":"ℷ","&gjcy;":"ѓ","&gl;":"≷","&glE;":"⪒","&gla;":"⪥","&glj;":"⪤","&gnE;":"≩","&gnap;":"⪊","&gnapprox;":"⪊","&gne;":"⪈","&gneq;":"⪈","&gneqq;":"≩","&gnsim;":"⋧","&gopf;":"𝕘","&grave;":"`","&gscr;":"ℊ","&gsim;":"≳","&gsime;":"⪎","&gsiml;":"⪐","&gt":">","&gt;":">","&gtcc;":"⪧","&gtcir;":"⩺","&gtdot;":"⋗","&gtlPar;":"⦕","&gtquest;":"⩼","&gtrapprox;":"⪆","&gtrarr;":"⥸","&gtrdot;":"⋗","&gtreqless;":"⋛","&gtreqqless;":"⪌","&gtrless;":"≷","&gtrsim;":"≳","&gvertneqq;":"≩︀","&gvnE;":"≩︀","&hArr;":"⇔","&hairsp;":" ","&half;":"½","&hamilt;":"ℋ","&hardcy;":"ъ","&harr;":"↔","&harrcir;":"⥈","&harrw;":"↭","&hbar;":"ℏ","&hcirc;":"ĥ","&hearts;":"♥","&heartsuit;":"♥","&hellip;":"…","&hercon;":"⊹","&hfr;":"𝔥","&hksearow;":"⤥","&hkswarow;":"⤦","&hoarr;":"⇿","&homtht;":"∻","&hookleftarrow;":"↩","&hookrightarrow;":"↪","&hopf;":"𝕙","&horbar;":"―","&hscr;":"𝒽","&hslash;":"ℏ","&hstrok;":"ħ","&hybull;":"⁃","&hyphen;":"‐","&iacute":"í","&iacute;":"í","&ic;":"⁣","&icirc":"î","&icirc;":"î","&icy;":"и","&iecy;":"е","&iexcl":"¡","&iexcl;":"¡","&iff;":"⇔","&ifr;":"𝔦","&igrave":"ì","&igrave;":"ì","&ii;":"ⅈ","&iiiint;":"⨌","&iiint;":"∭","&iinfin;":"⧜","&iiota;":"℩","&ijlig;":"ĳ","&imacr;":"ī","&image;":"ℑ","&imagline;":"ℐ","&imagpart;":"ℑ","&imath;":"ı","&imof;":"⊷","&imped;":"Ƶ","&in;":"∈","&incare;":"℅","&infin;":"∞","&infintie;":"⧝","&inodot;":"ı","&int;":"∫","&intcal;":"⊺","&integers;":"ℤ","&intercal;":"⊺","&intlarhk;":"⨗","&intprod;":"⨼","&iocy;":"ё","&iogon;":"į","&iopf;":"𝕚","&iota;":"ι","&iprod;":"⨼","&iquest":"¿","&iquest;":"¿","&iscr;":"𝒾","&isin;":"∈","&isinE;":"⋹","&isindot;":"⋵","&isins;":"⋴","&isinsv;":"⋳","&isinv;":"∈","&it;":"⁢","&itilde;":"ĩ","&iukcy;":"і","&iuml":"ï","&iuml;":"ï","&jcirc;":"ĵ","&jcy;":"й","&jfr;":"𝔧","&jmath;":"ȷ","&jopf;":"𝕛","&jscr;":"𝒿","&jsercy;":"ј","&jukcy;":"є","&kappa;":"κ","&kappav;":"ϰ","&kcedil;":"ķ","&kcy;":"к","&kfr;":"𝔨","&kgreen;":"ĸ","&khcy;":"х","&kjcy;":"ќ","&kopf;":"𝕜","&kscr;":"𝓀","&lAarr;":"⇚","&lArr;":"⇐","&lAtail;":"⤛","&lBarr;":"⤎","&lE;":"≦","&lEg;":"⪋","&lHar;":"⥢","&lacute;":"ĺ","&laemptyv;":"⦴","&lagran;":"ℒ","&lambda;":"λ","&lang;":"⟨","&langd;":"⦑","&langle;":"⟨","&lap;":"⪅","&laquo":"«","&laquo;":"«","&larr;":"←","&larrb;":"⇤","&larrbfs;":"⤟","&larrfs;":"⤝","&larrhk;":"↩","&larrlp;":"↫","&larrpl;":"⤹","&larrsim;":"⥳","&larrtl;":"↢","&lat;":"⪫","&latail;":"⤙","&late;":"⪭","&lates;":"⪭︀","&lbarr;":"⤌","&lbbrk;":"❲","&lbrace;":"{","&lbrack;":"[","&lbrke;":"⦋","&lbrksld;":"⦏","&lbrkslu;":"⦍","&lcaron;":"ľ","&lcedil;":"ļ","&lceil;":"⌈","&lcub;":"{","&lcy;":"л","&ldca;":"⤶","&ldquo;":"“","&ldquor;":"„","&ldrdhar;":"⥧","&ldrushar;":"⥋","&ldsh;":"↲","&le;":"≤","&leftarrow;":"←","&leftarrowtail;":"↢","&leftharpoondown;":"↽","&leftharpoonup;":"↼","&leftleftarrows;":"⇇","&leftrightarrow;":"↔","&leftrightarrows;":"⇆","&leftrightharpoons;":"⇋","&leftrightsquigarrow;":"↭","&leftthreetimes;":"⋋","&leg;":"⋚","&leq;":"≤","&leqq;":"≦","&leqslant;":"⩽","&les;":"⩽","&lescc;":"⪨","&lesdot;":"⩿","&lesdoto;":"⪁","&lesdotor;":"⪃","&lesg;":"⋚︀","&lesges;":"⪓","&lessapprox;":"⪅","&lessdot;":"⋖","&lesseqgtr;":"⋚","&lesseqqgtr;":"⪋","&lessgtr;":"≶","&lesssim;":"≲","&lfisht;":"⥼","&lfloor;":"⌊","&lfr;":"𝔩","&lg;":"≶","&lgE;":"⪑","&lhard;":"↽","&lharu;":"↼","&lharul;":"⥪","&lhblk;":"▄","&ljcy;":"љ","&ll;":"≪","&llarr;":"⇇","&llcorner;":"⌞","&llhard;":"⥫","&lltri;":"◺","&lmidot;":"ŀ","&lmoust;":"⎰","&lmoustache;":"⎰","&lnE;":"≨","&lnap;":"⪉","&lnapprox;":"⪉","&lne;":"⪇","&lneq;":"⪇","&lneqq;":"≨","&lnsim;":"⋦","&loang;":"⟬","&loarr;":"⇽","&lobrk;":"⟦","&longleftarrow;":"⟵","&longleftrightarrow;":"⟷","&longmapsto;":"⟼","&longrightarrow;":"⟶","&looparrowleft;":"↫","&looparrowright;":"↬","&lopar;":"⦅","&lopf;":"𝕝","&loplus;":"⨭","&lotimes;":"⨴","&lowast;":"∗","&lowbar;":"_","&loz;":"◊","&lozenge;":"◊","&lozf;":"⧫","&lpar;":"(","&lparlt;":"⦓","&lrarr;":"⇆","&lrcorner;":"⌟","&lrhar;":"⇋","&lrhard;":"⥭","&lrm;":"‎","&lrtri;":"⊿","&lsaquo;":"‹","&lscr;":"𝓁","&lsh;":"↰","&lsim;":"≲","&lsime;":"⪍","&lsimg;":"⪏","&lsqb;":"[","&lsquo;":"‘","&lsquor;":"‚","&lstrok;":"ł","&lt":"<","&lt;":"<","&ltcc;":"⪦","&ltcir;":"⩹","&ltdot;":"⋖","&lthree;":"⋋","&ltimes;":"⋉","&ltlarr;":"⥶","&ltquest;":"⩻","&ltrPar;":"⦖","&ltri;":"◃","&ltrie;":"⊴","&ltrif;":"◂","&lurdshar;":"⥊","&luruhar;":"⥦","&lvertneqq;":"≨︀","&lvnE;":"≨︀","&mDDot;":"∺","&macr":"¯","&macr;":"¯","&male;":"♂","&malt;":"✠","&maltese;":"✠","&map;":"↦","&mapsto;":"↦","&mapstodown;":"↧","&mapstoleft;":"↤","&mapstoup;":"↥","&marker;":"▮","&mcomma;":"⨩","&mcy;":"м","&mdash;":"—","&measuredangle;":"∡","&mfr;":"𝔪","&mho;":"℧","&micro":"µ","&micro;":"µ","&mid;":"∣","&midast;":"*","&midcir;":"⫰","&middot":"·","&middot;":"·","&minus;":"−","&minusb;":"⊟","&minusd;":"∸","&minusdu;":"⨪","&mlcp;":"⫛","&mldr;":"…","&mnplus;":"∓","&models;":"⊧","&mopf;":"𝕞","&mp;":"∓","&mscr;":"𝓂","&mstpos;":"∾","&mu;":"μ","&multimap;":"⊸","&mumap;":"⊸","&nGg;":"⋙̸","&nGt;":"≫⃒","&nGtv;":"≫̸","&nLeftarrow;":"⇍","&nLeftrightarrow;":"⇎","&nLl;":"⋘̸","&nLt;":"≪⃒","&nLtv;":"≪̸","&nRightarrow;":"⇏","&nVDash;":"⊯","&nVdash;":"⊮","&nabla;":"∇","&nacute;":"ń","&nang;":"∠⃒","&nap;":"≉","&napE;":"⩰̸","&napid;":"≋̸","&napos;":"ŉ","&napprox;":"≉","&natur;":"♮","&natural;":"♮","&naturals;":"ℕ","&nbsp":" ","&nbsp;":" ","&nbump;":"≎̸","&nbumpe;":"≏̸","&ncap;":"⩃","&ncaron;":"ň","&ncedil;":"ņ","&ncong;":"≇","&ncongdot;":"⩭̸","&ncup;":"⩂","&ncy;":"н","&ndash;":"–","&ne;":"≠","&neArr;":"⇗","&nearhk;":"⤤","&nearr;":"↗","&nearrow;":"↗","&nedot;":"≐̸","&nequiv;":"≢","&nesear;":"⤨","&nesim;":"≂̸","&nexist;":"∄","&nexists;":"∄","&nfr;":"𝔫","&ngE;":"≧̸","&nge;":"≱","&ngeq;":"≱","&ngeqq;":"≧̸","&ngeqslant;":"⩾̸","&nges;":"⩾̸","&ngsim;":"≵","&ngt;":"≯","&ngtr;":"≯","&nhArr;":"⇎","&nharr;":"↮","&nhpar;":"⫲","&ni;":"∋","&nis;":"⋼","&nisd;":"⋺","&niv;":"∋","&njcy;":"њ","&nlArr;":"⇍","&nlE;":"≦̸","&nlarr;":"↚","&nldr;":"‥","&nle;":"≰","&nleftarrow;":"↚","&nleftrightarrow;":"↮","&nleq;":"≰","&nleqq;":"≦̸","&nleqslant;":"⩽̸","&nles;":"⩽̸","&nless;":"≮","&nlsim;":"≴","&nlt;":"≮","&nltri;":"⋪","&nltrie;":"⋬","&nmid;":"∤","&nopf;":"𝕟","&not":"¬","&not;":"¬","&notin;":"∉","&notinE;":"⋹̸","&notindot;":"⋵̸","&notinva;":"∉","&notinvb;":"⋷","&notinvc;":"⋶","&notni;":"∌","&notniva;":"∌","&notnivb;":"⋾","&notnivc;":"⋽","&npar;":"∦","&nparallel;":"∦","&nparsl;":"⫽⃥","&npart;":"∂̸","&npolint;":"⨔","&npr;":"⊀","&nprcue;":"⋠","&npre;":"⪯̸","&nprec;":"⊀","&npreceq;":"⪯̸","&nrArr;":"⇏","&nrarr;":"↛","&nrarrc;":"⤳̸","&nrarrw;":"↝̸","&nrightarrow;":"↛","&nrtri;":"⋫","&nrtrie;":"⋭","&nsc;":"⊁","&nsccue;":"⋡","&nsce;":"⪰̸","&nscr;":"𝓃","&nshortmid;":"∤","&nshortparallel;":"∦","&nsim;":"≁","&nsime;":"≄","&nsimeq;":"≄","&nsmid;":"∤","&nspar;":"∦","&nsqsube;":"⋢","&nsqsupe;":"⋣","&nsub;":"⊄","&nsubE;":"⫅̸","&nsube;":"⊈","&nsubset;":"⊂⃒","&nsubseteq;":"⊈","&nsubseteqq;":"⫅̸","&nsucc;":"⊁","&nsucceq;":"⪰̸","&nsup;":"⊅","&nsupE;":"⫆̸","&nsupe;":"⊉","&nsupset;":"⊃⃒","&nsupseteq;":"⊉","&nsupseteqq;":"⫆̸","&ntgl;":"≹","&ntilde":"ñ","&ntilde;":"ñ","&ntlg;":"≸","&ntriangleleft;":"⋪","&ntrianglelefteq;":"⋬","&ntriangleright;":"⋫","&ntrianglerighteq;":"⋭","&nu;":"ν","&num;":"#","&numero;":"№","&numsp;":" ","&nvDash;":"⊭","&nvHarr;":"⤄","&nvap;":"≍⃒","&nvdash;":"⊬","&nvge;":"≥⃒","&nvgt;":">⃒","&nvinfin;":"⧞","&nvlArr;":"⤂","&nvle;":"≤⃒","&nvlt;":"<⃒","&nvltrie;":"⊴⃒","&nvrArr;":"⤃","&nvrtrie;":"⊵⃒","&nvsim;":"∼⃒","&nwArr;":"⇖","&nwarhk;":"⤣","&nwarr;":"↖","&nwarrow;":"↖","&nwnear;":"⤧","&oS;":"Ⓢ","&oacute":"ó","&oacute;":"ó","&oast;":"⊛","&ocir;":"⊚","&ocirc":"ô","&ocirc;":"ô","&ocy;":"о","&odash;":"⊝","&odblac;":"ő","&odiv;":"⨸","&odot;":"⊙","&odsold;":"⦼","&oelig;":"œ","&ofcir;":"⦿","&ofr;":"𝔬","&ogon;":"˛","&ograve":"ò","&ograve;":"ò","&ogt;":"⧁","&ohbar;":"⦵","&ohm;":"Ω","&oint;":"∮","&olarr;":"↺","&olcir;":"⦾","&olcross;":"⦻","&oline;":"‾","&olt;":"⧀","&omacr;":"ō","&omega;":"ω","&omicron;":"ο","&omid;":"⦶","&ominus;":"⊖","&oopf;":"𝕠","&opar;":"⦷","&operp;":"⦹","&oplus;":"⊕","&or;":"∨","&orarr;":"↻","&ord;":"⩝","&order;":"ℴ","&orderof;":"ℴ","&ordf":"ª","&ordf;":"ª","&ordm":"º","&ordm;":"º","&origof;":"⊶","&oror;":"⩖","&orslope;":"⩗","&orv;":"⩛","&oscr;":"ℴ","&oslash":"ø","&oslash;":"ø","&osol;":"⊘","&otilde":"õ","&otilde;":"õ","&otimes;":"⊗","&otimesas;":"⨶","&ouml":"ö","&ouml;":"ö","&ovbar;":"⌽","&par;":"∥","&para":"¶","&para;":"¶","&parallel;":"∥","&parsim;":"⫳","&parsl;":"⫽","&part;":"∂","&pcy;":"п","&percnt;":"%","&period;":".","&permil;":"‰","&perp;":"⊥","&pertenk;":"‱","&pfr;":"𝔭","&phi;":"φ","&phiv;":"ϕ","&phmmat;":"ℳ","&phone;":"☎","&pi;":"π","&pitchfork;":"⋔","&piv;":"ϖ","&planck;":"ℏ","&planckh;":"ℎ","&plankv;":"ℏ","&plus;":"+","&plusacir;":"⨣","&plusb;":"⊞","&pluscir;":"⨢","&plusdo;":"∔","&plusdu;":"⨥","&pluse;":"⩲","&plusmn":"±","&plusmn;":"±","&plussim;":"⨦","&plustwo;":"⨧","&pm;":"±","&pointint;":"⨕","&popf;":"𝕡","&pound":"£","&pound;":"£","&pr;":"≺","&prE;":"⪳","&prap;":"⪷","&prcue;":"≼","&pre;":"⪯","&prec;":"≺","&precapprox;":"⪷","&preccurlyeq;":"≼","&preceq;":"⪯","&precnapprox;":"⪹","&precneqq;":"⪵","&precnsim;":"⋨","&precsim;":"≾","&prime;":"′","&primes;":"ℙ","&prnE;":"⪵","&prnap;":"⪹","&prnsim;":"⋨","&prod;":"∏","&profalar;":"⌮","&profline;":"⌒","&profsurf;":"⌓","&prop;":"∝","&propto;":"∝","&prsim;":"≾","&prurel;":"⊰","&pscr;":"𝓅","&psi;":"ψ","&puncsp;":" ","&qfr;":"𝔮","&qint;":"⨌","&qopf;":"𝕢","&qprime;":"⁗","&qscr;":"𝓆","&quaternions;":"ℍ","&quatint;":"⨖","&quest;":"?","&questeq;":"≟","&quot":'"',"&quot;":'"',"&rAarr;":"⇛","&rArr;":"⇒","&rAtail;":"⤜","&rBarr;":"⤏","&rHar;":"⥤","&race;":"∽̱","&racute;":"ŕ","&radic;":"√","&raemptyv;":"⦳","&rang;":"⟩","&rangd;":"⦒","&range;":"⦥","&rangle;":"⟩","&raquo":"»","&raquo;":"»","&rarr;":"→","&rarrap;":"⥵","&rarrb;":"⇥","&rarrbfs;":"⤠","&rarrc;":"⤳","&rarrfs;":"⤞","&rarrhk;":"↪","&rarrlp;":"↬","&rarrpl;":"⥅","&rarrsim;":"⥴","&rarrtl;":"↣","&rarrw;":"↝","&ratail;":"⤚","&ratio;":"∶","&rationals;":"ℚ","&rbarr;":"⤍","&rbbrk;":"❳","&rbrace;":"}","&rbrack;":"]","&rbrke;":"⦌","&rbrksld;":"⦎","&rbrkslu;":"⦐","&rcaron;":"ř","&rcedil;":"ŗ","&rceil;":"⌉","&rcub;":"}","&rcy;":"р","&rdca;":"⤷","&rdldhar;":"⥩","&rdquo;":"”","&rdquor;":"”","&rdsh;":"↳","&real;":"ℜ","&realine;":"ℛ","&realpart;":"ℜ","&reals;":"ℝ","&rect;":"▭","&reg":"®","&reg;":"®","&rfisht;":"⥽","&rfloor;":"⌋","&rfr;":"𝔯","&rhard;":"⇁","&rharu;":"⇀","&rharul;":"⥬","&rho;":"ρ","&rhov;":"ϱ","&rightarrow;":"→","&rightarrowtail;":"↣","&rightharpoondown;":"⇁","&rightharpoonup;":"⇀","&rightleftarrows;":"⇄","&rightleftharpoons;":"⇌","&rightrightarrows;":"⇉","&rightsquigarrow;":"↝","&rightthreetimes;":"⋌","&ring;":"˚","&risingdotseq;":"≓","&rlarr;":"⇄","&rlhar;":"⇌","&rlm;":"‏","&rmoust;":"⎱","&rmoustache;":"⎱","&rnmid;":"⫮","&roang;":"⟭","&roarr;":"⇾","&robrk;":"⟧","&ropar;":"⦆","&ropf;":"𝕣","&roplus;":"⨮","&rotimes;":"⨵","&rpar;":")","&rpargt;":"⦔","&rppolint;":"⨒","&rrarr;":"⇉","&rsaquo;":"›","&rscr;":"𝓇","&rsh;":"↱","&rsqb;":"]","&rsquo;":"’","&rsquor;":"’","&rthree;":"⋌","&rtimes;":"⋊","&rtri;":"▹","&rtrie;":"⊵","&rtrif;":"▸","&rtriltri;":"⧎","&ruluhar;":"⥨","&rx;":"℞","&sacute;":"ś","&sbquo;":"‚","&sc;":"≻","&scE;":"⪴","&scap;":"⪸","&scaron;":"š","&sccue;":"≽","&sce;":"⪰","&scedil;":"ş","&scirc;":"ŝ","&scnE;":"⪶","&scnap;":"⪺","&scnsim;":"⋩","&scpolint;":"⨓","&scsim;":"≿","&scy;":"с","&sdot;":"⋅","&sdotb;":"⊡","&sdote;":"⩦","&seArr;":"⇘","&searhk;":"⤥","&searr;":"↘","&searrow;":"↘","&sect":"§","&sect;":"§","&semi;":";","&seswar;":"⤩","&setminus;":"∖","&setmn;":"∖","&sext;":"✶","&sfr;":"𝔰","&sfrown;":"⌢","&sharp;":"♯","&shchcy;":"щ","&shcy;":"ш","&shortmid;":"∣","&shortparallel;":"∥","&shy":"­","&shy;":"­","&sigma;":"σ","&sigmaf;":"ς","&sigmav;":"ς","&sim;":"∼","&simdot;":"⩪","&sime;":"≃","&simeq;":"≃","&simg;":"⪞","&simgE;":"⪠","&siml;":"⪝","&simlE;":"⪟","&simne;":"≆","&simplus;":"⨤","&simrarr;":"⥲","&slarr;":"←","&smallsetminus;":"∖","&smashp;":"⨳","&smeparsl;":"⧤","&smid;":"∣","&smile;":"⌣","&smt;":"⪪","&smte;":"⪬","&smtes;":"⪬︀","&softcy;":"ь","&sol;":"/","&solb;":"⧄","&solbar;":"⌿","&sopf;":"𝕤","&spades;":"♠","&spadesuit;":"♠","&spar;":"∥","&sqcap;":"⊓","&sqcaps;":"⊓︀","&sqcup;":"⊔","&sqcups;":"⊔︀","&sqsub;":"⊏","&sqsube;":"⊑","&sqsubset;":"⊏","&sqsubseteq;":"⊑","&sqsup;":"⊐","&sqsupe;":"⊒","&sqsupset;":"⊐","&sqsupseteq;":"⊒","&squ;":"□","&square;":"□","&squarf;":"▪","&squf;":"▪","&srarr;":"→","&sscr;":"𝓈","&ssetmn;":"∖","&ssmile;":"⌣","&sstarf;":"⋆","&star;":"☆","&starf;":"★","&straightepsilon;":"ϵ","&straightphi;":"ϕ","&strns;":"¯","&sub;":"⊂","&subE;":"⫅","&subdot;":"⪽","&sube;":"⊆","&subedot;":"⫃","&submult;":"⫁","&subnE;":"⫋","&subne;":"⊊","&subplus;":"⪿","&subrarr;":"⥹","&subset;":"⊂","&subseteq;":"⊆","&subseteqq;":"⫅","&subsetneq;":"⊊","&subsetneqq;":"⫋","&subsim;":"⫇","&subsub;":"⫕","&subsup;":"⫓","&succ;":"≻","&succapprox;":"⪸","&succcurlyeq;":"≽","&succeq;":"⪰","&succnapprox;":"⪺","&succneqq;":"⪶","&succnsim;":"⋩","&succsim;":"≿","&sum;":"∑","&sung;":"♪","&sup1":"¹","&sup1;":"¹","&sup2":"²","&sup2;":"²","&sup3":"³","&sup3;":"³","&sup;":"⊃","&supE;":"⫆","&supdot;":"⪾","&supdsub;":"⫘","&supe;":"⊇","&supedot;":"⫄","&suphsol;":"⟉","&suphsub;":"⫗","&suplarr;":"⥻","&supmult;":"⫂","&supnE;":"⫌","&supne;":"⊋","&supplus;":"⫀","&supset;":"⊃","&supseteq;":"⊇","&supseteqq;":"⫆","&supsetneq;":"⊋","&supsetneqq;":"⫌","&supsim;":"⫈","&supsub;":"⫔","&supsup;":"⫖","&swArr;":"⇙","&swarhk;":"⤦","&swarr;":"↙","&swarrow;":"↙","&swnwar;":"⤪","&szlig":"ß","&szlig;":"ß","&target;":"⌖","&tau;":"τ","&tbrk;":"⎴","&tcaron;":"ť","&tcedil;":"ţ","&tcy;":"т","&tdot;":"⃛","&telrec;":"⌕","&tfr;":"𝔱","&there4;":"∴","&therefore;":"∴","&theta;":"θ","&thetasym;":"ϑ","&thetav;":"ϑ","&thickapprox;":"≈","&thicksim;":"∼","&thinsp;":" ","&thkap;":"≈","&thksim;":"∼","&thorn":"þ","&thorn;":"þ","&tilde;":"˜","&times":"×","&times;":"×","&timesb;":"⊠","&timesbar;":"⨱","&timesd;":"⨰","&tint;":"∭","&toea;":"⤨","&top;":"⊤","&topbot;":"⌶","&topcir;":"⫱","&topf;":"𝕥","&topfork;":"⫚","&tosa;":"⤩","&tprime;":"‴","&trade;":"™","&triangle;":"▵","&triangledown;":"▿","&triangleleft;":"◃","&trianglelefteq;":"⊴","&triangleq;":"≜","&triangleright;":"▹","&trianglerighteq;":"⊵","&tridot;":"◬","&trie;":"≜","&triminus;":"⨺","&triplus;":"⨹","&trisb;":"⧍","&tritime;":"⨻","&trpezium;":"⏢","&tscr;":"𝓉","&tscy;":"ц","&tshcy;":"ћ","&tstrok;":"ŧ","&twixt;":"≬","&twoheadleftarrow;":"↞","&twoheadrightarrow;":"↠","&uArr;":"⇑","&uHar;":"⥣","&uacute":"ú","&uacute;":"ú","&uarr;":"↑","&ubrcy;":"ў","&ubreve;":"ŭ","&ucirc":"û","&ucirc;":"û","&ucy;":"у","&udarr;":"⇅","&udblac;":"ű","&udhar;":"⥮","&ufisht;":"⥾","&ufr;":"𝔲","&ugrave":"ù","&ugrave;":"ù","&uharl;":"↿","&uharr;":"↾","&uhblk;":"▀","&ulcorn;":"⌜","&ulcorner;":"⌜","&ulcrop;":"⌏","&ultri;":"◸","&umacr;":"ū","&uml":"¨","&uml;":"¨","&uogon;":"ų","&uopf;":"𝕦","&uparrow;":"↑","&updownarrow;":"↕","&upharpoonleft;":"↿","&upharpoonright;":"↾","&uplus;":"⊎","&upsi;":"υ","&upsih;":"ϒ","&upsilon;":"υ","&upuparrows;":"⇈","&urcorn;":"⌝","&urcorner;":"⌝","&urcrop;":"⌎","&uring;":"ů","&urtri;":"◹","&uscr;":"𝓊","&utdot;":"⋰","&utilde;":"ũ","&utri;":"▵","&utrif;":"▴","&uuarr;":"⇈","&uuml":"ü","&uuml;":"ü","&uwangle;":"⦧","&vArr;":"⇕","&vBar;":"⫨","&vBarv;":"⫩","&vDash;":"⊨","&vangrt;":"⦜","&varepsilon;":"ϵ","&varkappa;":"ϰ","&varnothing;":"∅","&varphi;":"ϕ","&varpi;":"ϖ","&varpropto;":"∝","&varr;":"↕","&varrho;":"ϱ","&varsigma;":"ς","&varsubsetneq;":"⊊︀","&varsubsetneqq;":"⫋︀","&varsupsetneq;":"⊋︀","&varsupsetneqq;":"⫌︀","&vartheta;":"ϑ","&vartriangleleft;":"⊲","&vartriangleright;":"⊳","&vcy;":"в","&vdash;":"⊢","&vee;":"∨","&veebar;":"⊻","&veeeq;":"≚","&vellip;":"⋮","&verbar;":"|","&vert;":"|","&vfr;":"𝔳","&vltri;":"⊲","&vnsub;":"⊂⃒","&vnsup;":"⊃⃒","&vopf;":"𝕧","&vprop;":"∝","&vrtri;":"⊳","&vscr;":"𝓋","&vsubnE;":"⫋︀","&vsubne;":"⊊︀","&vsupnE;":"⫌︀","&vsupne;":"⊋︀","&vzigzag;":"⦚","&wcirc;":"ŵ","&wedbar;":"⩟","&wedge;":"∧","&wedgeq;":"≙","&weierp;":"℘","&wfr;":"𝔴","&wopf;":"𝕨","&wp;":"℘","&wr;":"≀","&wreath;":"≀","&wscr;":"𝓌","&xcap;":"⋂","&xcirc;":"◯","&xcup;":"⋃","&xdtri;":"▽","&xfr;":"𝔵","&xhArr;":"⟺","&xharr;":"⟷","&xi;":"ξ","&xlArr;":"⟸","&xlarr;":"⟵","&xmap;":"⟼","&xnis;":"⋻","&xodot;":"⨀","&xopf;":"𝕩","&xoplus;":"⨁","&xotime;":"⨂","&xrArr;":"⟹","&xrarr;":"⟶","&xscr;":"𝓍","&xsqcup;":"⨆","&xuplus;":"⨄","&xutri;":"△","&xvee;":"⋁","&xwedge;":"⋀","&yacute":"ý","&yacute;":"ý","&yacy;":"я","&ycirc;":"ŷ","&ycy;":"ы","&yen":"¥","&yen;":"¥","&yfr;":"𝔶","&yicy;":"ї","&yopf;":"𝕪","&yscr;":"𝓎","&yucy;":"ю","&yuml":"ÿ","&yuml;":"ÿ","&zacute;":"ź","&zcaron;":"ž","&zcy;":"з","&zdot;":"ż","&zeetrf;":"ℨ","&zeta;":"ζ","&zfr;":"𝔷","&zhcy;":"ж","&zigrarr;":"⇝","&zopf;":"𝕫","&zscr;":"𝓏","&zwj;":"‍","&zwnj;":"‌"},characters:{"Æ":"&AElig;","&":"&amp;","Á":"&Aacute;","Ă":"&Abreve;","Â":"&Acirc;","А":"&Acy;","𝔄":"&Afr;","À":"&Agrave;","Α":"&Alpha;","Ā":"&Amacr;","⩓":"&And;","Ą":"&Aogon;","𝔸":"&Aopf;","⁡":"&af;","Å":"&angst;","𝒜":"&Ascr;","≔":"&coloneq;","Ã":"&Atilde;","Ä":"&Auml;","∖":"&ssetmn;","⫧":"&Barv;","⌆":"&doublebarwedge;","Б":"&Bcy;","∵":"&because;","ℬ":"&bernou;","Β":"&Beta;","𝔅":"&Bfr;","𝔹":"&Bopf;","˘":"&breve;","≎":"&bump;","Ч":"&CHcy;","©":"&copy;","Ć":"&Cacute;","⋒":"&Cap;","ⅅ":"&DD;","ℭ":"&Cfr;","Č":"&Ccaron;","Ç":"&Ccedil;","Ĉ":"&Ccirc;","∰":"&Cconint;","Ċ":"&Cdot;","¸":"&cedil;","·":"&middot;","Χ":"&Chi;","⊙":"&odot;","⊖":"&ominus;","⊕":"&oplus;","⊗":"&otimes;","∲":"&cwconint;","”":"&rdquor;","’":"&rsquor;","∷":"&Proportion;","⩴":"&Colone;","≡":"&equiv;","∯":"&DoubleContourIntegral;","∮":"&oint;","ℂ":"&complexes;","∐":"&coprod;","∳":"&awconint;","⨯":"&Cross;","𝒞":"&Cscr;","⋓":"&Cup;","≍":"&asympeq;","⤑":"&DDotrahd;","Ђ":"&DJcy;","Ѕ":"&DScy;","Џ":"&DZcy;","‡":"&ddagger;","↡":"&Darr;","⫤":"&DoubleLeftTee;","Ď":"&Dcaron;","Д":"&Dcy;","∇":"&nabla;","Δ":"&Delta;","𝔇":"&Dfr;","´":"&acute;","˙":"&dot;","˝":"&dblac;","`":"&grave;","˜":"&tilde;","⋄":"&diamond;","ⅆ":"&dd;","𝔻":"&Dopf;","¨":"&uml;","⃜":"&DotDot;","≐":"&esdot;","⇓":"&dArr;","⇐":"&lArr;","⇔":"&iff;","⟸":"&xlArr;","⟺":"&xhArr;","⟹":"&xrArr;","⇒":"&rArr;","⊨":"&vDash;","⇑":"&uArr;","⇕":"&vArr;","∥":"&spar;","↓":"&downarrow;","⤓":"&DownArrowBar;","⇵":"&duarr;","̑":"&DownBreve;","⥐":"&DownLeftRightVector;","⥞":"&DownLeftTeeVector;","↽":"&lhard;","⥖":"&DownLeftVectorBar;","⥟":"&DownRightTeeVector;","⇁":"&rightharpoondown;","⥗":"&DownRightVectorBar;","⊤":"&top;","↧":"&mapstodown;","𝒟":"&Dscr;","Đ":"&Dstrok;","Ŋ":"&ENG;","Ð":"&ETH;","É":"&Eacute;","Ě":"&Ecaron;","Ê":"&Ecirc;","Э":"&Ecy;","Ė":"&Edot;","𝔈":"&Efr;","È":"&Egrave;","∈":"&isinv;","Ē":"&Emacr;","◻":"&EmptySmallSquare;","▫":"&EmptyVerySmallSquare;","Ę":"&Eogon;","𝔼":"&Eopf;","Ε":"&Epsilon;","⩵":"&Equal;","≂":"&esim;","⇌":"&rlhar;","ℰ":"&expectation;","⩳":"&Esim;","Η":"&Eta;","Ë":"&Euml;","∃":"&exist;","ⅇ":"&exponentiale;","Ф":"&Fcy;","𝔉":"&Ffr;","◼":"&FilledSmallSquare;","▪":"&squf;","𝔽":"&Fopf;","∀":"&forall;","ℱ":"&Fscr;","Ѓ":"&GJcy;",">":"&gt;","Γ":"&Gamma;","Ϝ":"&Gammad;","Ğ":"&Gbreve;","Ģ":"&Gcedil;","Ĝ":"&Gcirc;","Г":"&Gcy;","Ġ":"&Gdot;","𝔊":"&Gfr;","⋙":"&ggg;","𝔾":"&Gopf;","≥":"&geq;","⋛":"&gtreqless;","≧":"&geqq;","⪢":"&GreaterGreater;","≷":"&gtrless;","⩾":"&ges;","≳":"&gtrsim;","𝒢":"&Gscr;","≫":"&gg;","Ъ":"&HARDcy;","ˇ":"&caron;","^":"&Hat;","Ĥ":"&Hcirc;","ℌ":"&Poincareplane;","ℋ":"&hamilt;","ℍ":"&quaternions;","─":"&boxh;","Ħ":"&Hstrok;","≏":"&bumpeq;","Е":"&IEcy;","Ĳ":"&IJlig;","Ё":"&IOcy;","Í":"&Iacute;","Î":"&Icirc;","И":"&Icy;","İ":"&Idot;","ℑ":"&imagpart;","Ì":"&Igrave;","Ī":"&Imacr;","ⅈ":"&ii;","∬":"&Int;","∫":"&int;","⋂":"&xcap;","⁣":"&ic;","⁢":"&it;","Į":"&Iogon;","𝕀":"&Iopf;","Ι":"&Iota;","ℐ":"&imagline;","Ĩ":"&Itilde;","І":"&Iukcy;","Ï":"&Iuml;","Ĵ":"&Jcirc;","Й":"&Jcy;","𝔍":"&Jfr;","𝕁":"&Jopf;","𝒥":"&Jscr;","Ј":"&Jsercy;","Є":"&Jukcy;","Х":"&KHcy;","Ќ":"&KJcy;","Κ":"&Kappa;","Ķ":"&Kcedil;","К":"&Kcy;","𝔎":"&Kfr;","𝕂":"&Kopf;","𝒦":"&Kscr;","Љ":"&LJcy;","<":"&lt;","Ĺ":"&Lacute;","Λ":"&Lambda;","⟪":"&Lang;","ℒ":"&lagran;","↞":"&twoheadleftarrow;","Ľ":"&Lcaron;","Ļ":"&Lcedil;","Л":"&Lcy;","⟨":"&langle;","←":"&slarr;","⇤":"&larrb;","⇆":"&lrarr;","⌈":"&lceil;","⟦":"&lobrk;","⥡":"&LeftDownTeeVector;","⇃":"&downharpoonleft;","⥙":"&LeftDownVectorBar;","⌊":"&lfloor;","↔":"&leftrightarrow;","⥎":"&LeftRightVector;","⊣":"&dashv;","↤":"&mapstoleft;","⥚":"&LeftTeeVector;","⊲":"&vltri;","⧏":"&LeftTriangleBar;","⊴":"&trianglelefteq;","⥑":"&LeftUpDownVector;","⥠":"&LeftUpTeeVector;","↿":"&upharpoonleft;","⥘":"&LeftUpVectorBar;","↼":"&lharu;","⥒":"&LeftVectorBar;","⋚":"&lesseqgtr;","≦":"&leqq;","≶":"&lg;","⪡":"&LessLess;","⩽":"&les;","≲":"&lsim;","𝔏":"&Lfr;","⋘":"&Ll;","⇚":"&lAarr;","Ŀ":"&Lmidot;","⟵":"&xlarr;","⟷":"&xharr;","⟶":"&xrarr;","𝕃":"&Lopf;","↙":"&swarrow;","↘":"&searrow;","↰":"&lsh;","Ł":"&Lstrok;","≪":"&ll;","⤅":"&Map;","М":"&Mcy;"," ":"&MediumSpace;","ℳ":"&phmmat;","𝔐":"&Mfr;","∓":"&mp;","𝕄":"&Mopf;","Μ":"&Mu;","Њ":"&NJcy;","Ń":"&Nacute;","Ň":"&Ncaron;","Ņ":"&Ncedil;","Н":"&Ncy;","​":"&ZeroWidthSpace;","\n":"&NewLine;","𝔑":"&Nfr;","⁠":"&NoBreak;"," ":"&nbsp;","ℕ":"&naturals;","⫬":"&Not;","≢":"&nequiv;","≭":"&NotCupCap;","∦":"&nspar;","∉":"&notinva;","≠":"&ne;","≂̸":"&nesim;","∄":"&nexists;","≯":"&ngtr;","≱":"&ngeq;","≧̸":"&ngeqq;","≫̸":"&nGtv;","≹":"&ntgl;","⩾̸":"&nges;","≵":"&ngsim;","≎̸":"&nbump;","≏̸":"&nbumpe;","⋪":"&ntriangleleft;","⧏̸":"&NotLeftTriangleBar;","⋬":"&ntrianglelefteq;","≮":"&nlt;","≰":"&nleq;","≸":"&ntlg;","≪̸":"&nLtv;","⩽̸":"&nles;","≴":"&nlsim;","⪢̸":"&NotNestedGreaterGreater;","⪡̸":"&NotNestedLessLess;","⊀":"&nprec;","⪯̸":"&npreceq;","⋠":"&nprcue;","∌":"&notniva;","⋫":"&ntriangleright;","⧐̸":"&NotRightTriangleBar;","⋭":"&ntrianglerighteq;","⊏̸":"&NotSquareSubset;","⋢":"&nsqsube;","⊐̸":"&NotSquareSuperset;","⋣":"&nsqsupe;","⊂⃒":"&vnsub;","⊈":"&nsubseteq;","⊁":"&nsucc;","⪰̸":"&nsucceq;","⋡":"&nsccue;","≿̸":"&NotSucceedsTilde;","⊃⃒":"&vnsup;","⊉":"&nsupseteq;","≁":"&nsim;","≄":"&nsimeq;","≇":"&ncong;","≉":"&napprox;","∤":"&nsmid;","𝒩":"&Nscr;","Ñ":"&Ntilde;","Ν":"&Nu;","Œ":"&OElig;","Ó":"&Oacute;","Ô":"&Ocirc;","О":"&Ocy;","Ő":"&Odblac;","𝔒":"&Ofr;","Ò":"&Ograve;","Ō":"&Omacr;","Ω":"&ohm;","Ο":"&Omicron;","𝕆":"&Oopf;","“":"&ldquo;","‘":"&lsquo;","⩔":"&Or;","𝒪":"&Oscr;","Ø":"&Oslash;","Õ":"&Otilde;","⨷":"&Otimes;","Ö":"&Ouml;","‾":"&oline;","⏞":"&OverBrace;","⎴":"&tbrk;","⏜":"&OverParenthesis;","∂":"&part;","П":"&Pcy;","𝔓":"&Pfr;","Φ":"&Phi;","Π":"&Pi;","±":"&pm;","ℙ":"&primes;","⪻":"&Pr;","≺":"&prec;","⪯":"&preceq;","≼":"&preccurlyeq;","≾":"&prsim;","″":"&Prime;","∏":"&prod;","∝":"&vprop;","𝒫":"&Pscr;","Ψ":"&Psi;",'"':"&quot;","𝔔":"&Qfr;","ℚ":"&rationals;","𝒬":"&Qscr;","⤐":"&drbkarow;","®":"&reg;","Ŕ":"&Racute;","⟫":"&Rang;","↠":"&twoheadrightarrow;","⤖":"&Rarrtl;","Ř":"&Rcaron;","Ŗ":"&Rcedil;","Р":"&Rcy;","ℜ":"&realpart;","∋":"&niv;","⇋":"&lrhar;","⥯":"&duhar;","Ρ":"&Rho;","⟩":"&rangle;","→":"&srarr;","⇥":"&rarrb;","⇄":"&rlarr;","⌉":"&rceil;","⟧":"&robrk;","⥝":"&RightDownTeeVector;","⇂":"&downharpoonright;","⥕":"&RightDownVectorBar;","⌋":"&rfloor;","⊢":"&vdash;","↦":"&mapsto;","⥛":"&RightTeeVector;","⊳":"&vrtri;","⧐":"&RightTriangleBar;","⊵":"&trianglerighteq;","⥏":"&RightUpDownVector;","⥜":"&RightUpTeeVector;","↾":"&upharpoonright;","⥔":"&RightUpVectorBar;","⇀":"&rightharpoonup;","⥓":"&RightVectorBar;","ℝ":"&reals;","⥰":"&RoundImplies;","⇛":"&rAarr;","ℛ":"&realine;","↱":"&rsh;","⧴":"&RuleDelayed;","Щ":"&SHCHcy;","Ш":"&SHcy;","Ь":"&SOFTcy;","Ś":"&Sacute;","⪼":"&Sc;","Š":"&Scaron;","Ş":"&Scedil;","Ŝ":"&Scirc;","С":"&Scy;","𝔖":"&Sfr;","↑":"&uparrow;","Σ":"&Sigma;","∘":"&compfn;","𝕊":"&Sopf;","√":"&radic;","□":"&square;","⊓":"&sqcap;","⊏":"&sqsubset;","⊑":"&sqsubseteq;","⊐":"&sqsupset;","⊒":"&sqsupseteq;","⊔":"&sqcup;","𝒮":"&Sscr;","⋆":"&sstarf;","⋐":"&Subset;","⊆":"&subseteq;","≻":"&succ;","⪰":"&succeq;","≽":"&succcurlyeq;","≿":"&succsim;","∑":"&sum;","⋑":"&Supset;","⊃":"&supset;","⊇":"&supseteq;","Þ":"&THORN;","™":"&trade;","Ћ":"&TSHcy;","Ц":"&TScy;","\t":"&Tab;","Τ":"&Tau;","Ť":"&Tcaron;","Ţ":"&Tcedil;","Т":"&Tcy;","𝔗":"&Tfr;","∴":"&therefore;","Θ":"&Theta;","  ":"&ThickSpace;"," ":"&thinsp;","∼":"&thksim;","≃":"&simeq;","≅":"&cong;","≈":"&thkap;","𝕋":"&Topf;","⃛":"&tdot;","𝒯":"&Tscr;","Ŧ":"&Tstrok;","Ú":"&Uacute;","↟":"&Uarr;","⥉":"&Uarrocir;","Ў":"&Ubrcy;","Ŭ":"&Ubreve;","Û":"&Ucirc;","У":"&Ucy;","Ű":"&Udblac;","𝔘":"&Ufr;","Ù":"&Ugrave;","Ū":"&Umacr;",_:"&lowbar;","⏟":"&UnderBrace;","⎵":"&bbrk;","⏝":"&UnderParenthesis;","⋃":"&xcup;","⊎":"&uplus;","Ų":"&Uogon;","𝕌":"&Uopf;","⤒":"&UpArrowBar;","⇅":"&udarr;","↕":"&varr;","⥮":"&udhar;","⊥":"&perp;","↥":"&mapstoup;","↖":"&nwarrow;","↗":"&nearrow;","ϒ":"&upsih;","Υ":"&Upsilon;","Ů":"&Uring;","𝒰":"&Uscr;","Ũ":"&Utilde;","Ü":"&Uuml;","⊫":"&VDash;","⫫":"&Vbar;","В":"&Vcy;","⊩":"&Vdash;","⫦":"&Vdashl;","⋁":"&xvee;","‖":"&Vert;","∣":"&smid;","|":"&vert;","❘":"&VerticalSeparator;","≀":"&wreath;"," ":"&hairsp;","𝔙":"&Vfr;","𝕍":"&Vopf;","𝒱":"&Vscr;","⊪":"&Vvdash;","Ŵ":"&Wcirc;","⋀":"&xwedge;","𝔚":"&Wfr;","𝕎":"&Wopf;","𝒲":"&Wscr;","𝔛":"&Xfr;","Ξ":"&Xi;","𝕏":"&Xopf;","𝒳":"&Xscr;","Я":"&YAcy;","Ї":"&YIcy;","Ю":"&YUcy;","Ý":"&Yacute;","Ŷ":"&Ycirc;","Ы":"&Ycy;","𝔜":"&Yfr;","𝕐":"&Yopf;","𝒴":"&Yscr;","Ÿ":"&Yuml;","Ж":"&ZHcy;","Ź":"&Zacute;","Ž":"&Zcaron;","З":"&Zcy;","Ż":"&Zdot;","Ζ":"&Zeta;","ℨ":"&zeetrf;","ℤ":"&integers;","𝒵":"&Zscr;","á":"&aacute;","ă":"&abreve;","∾":"&mstpos;","∾̳":"&acE;","∿":"&acd;","â":"&acirc;","а":"&acy;","æ":"&aelig;","𝔞":"&afr;","à":"&agrave;","ℵ":"&aleph;","α":"&alpha;","ā":"&amacr;","⨿":"&amalg;","∧":"&wedge;","⩕":"&andand;","⩜":"&andd;","⩘":"&andslope;","⩚":"&andv;","∠":"&angle;","⦤":"&ange;","∡":"&measuredangle;","⦨":"&angmsdaa;","⦩":"&angmsdab;","⦪":"&angmsdac;","⦫":"&angmsdad;","⦬":"&angmsdae;","⦭":"&angmsdaf;","⦮":"&angmsdag;","⦯":"&angmsdah;","∟":"&angrt;","⊾":"&angrtvb;","⦝":"&angrtvbd;","∢":"&angsph;","⍼":"&angzarr;","ą":"&aogon;","𝕒":"&aopf;","⩰":"&apE;","⩯":"&apacir;","≊":"&approxeq;","≋":"&apid;","'":"&apos;","å":"&aring;","𝒶":"&ascr;","*":"&midast;","ã":"&atilde;","ä":"&auml;","⨑":"&awint;","⫭":"&bNot;","≌":"&bcong;","϶":"&bepsi;","‵":"&bprime;","∽":"&bsim;","⋍":"&bsime;","⊽":"&barvee;","⌅":"&barwedge;","⎶":"&bbrktbrk;","б":"&bcy;","„":"&ldquor;","⦰":"&bemptyv;","β":"&beta;","ℶ":"&beth;","≬":"&twixt;","𝔟":"&bfr;","◯":"&xcirc;","⨀":"&xodot;","⨁":"&xoplus;","⨂":"&xotime;","⨆":"&xsqcup;","★":"&starf;","▽":"&xdtri;","△":"&xutri;","⨄":"&xuplus;","⤍":"&rbarr;","⧫":"&lozf;","▴":"&utrif;","▾":"&dtrif;","◂":"&ltrif;","▸":"&rtrif;","␣":"&blank;","▒":"&blk12;","░":"&blk14;","▓":"&blk34;","█":"&block;","=⃥":"&bne;","≡⃥":"&bnequiv;","⌐":"&bnot;","𝕓":"&bopf;","⋈":"&bowtie;","╗":"&boxDL;","╔":"&boxDR;","╖":"&boxDl;","╓":"&boxDr;","═":"&boxH;","╦":"&boxHD;","╩":"&boxHU;","╤":"&boxHd;","╧":"&boxHu;","╝":"&boxUL;","╚":"&boxUR;","╜":"&boxUl;","╙":"&boxUr;","║":"&boxV;","╬":"&boxVH;","╣":"&boxVL;","╠":"&boxVR;","╫":"&boxVh;","╢":"&boxVl;","╟":"&boxVr;","⧉":"&boxbox;","╕":"&boxdL;","╒":"&boxdR;","┐":"&boxdl;","┌":"&boxdr;","╥":"&boxhD;","╨":"&boxhU;","┬":"&boxhd;","┴":"&boxhu;","⊟":"&minusb;","⊞":"&plusb;","⊠":"&timesb;","╛":"&boxuL;","╘":"&boxuR;","┘":"&boxul;","└":"&boxur;","│":"&boxv;","╪":"&boxvH;","╡":"&boxvL;","╞":"&boxvR;","┼":"&boxvh;","┤":"&boxvl;","├":"&boxvr;","¦":"&brvbar;","𝒷":"&bscr;","⁏":"&bsemi;","\\":"&bsol;","⧅":"&bsolb;","⟈":"&bsolhsub;","•":"&bullet;","⪮":"&bumpE;","ć":"&cacute;","∩":"&cap;","⩄":"&capand;","⩉":"&capbrcup;","⩋":"&capcap;","⩇":"&capcup;","⩀":"&capdot;","∩︀":"&caps;","⁁":"&caret;","⩍":"&ccaps;","č":"&ccaron;","ç":"&ccedil;","ĉ":"&ccirc;","⩌":"&ccups;","⩐":"&ccupssm;","ċ":"&cdot;","⦲":"&cemptyv;","¢":"&cent;","𝔠":"&cfr;","ч":"&chcy;","✓":"&checkmark;","χ":"&chi;","○":"&cir;","⧃":"&cirE;","ˆ":"&circ;","≗":"&cire;","↺":"&olarr;","↻":"&orarr;","Ⓢ":"&oS;","⊛":"&oast;","⊚":"&ocir;","⊝":"&odash;","⨐":"&cirfnint;","⫯":"&cirmid;","⧂":"&cirscir;","♣":"&clubsuit;",":":"&colon;",",":"&comma;","@":"&commat;","∁":"&complement;","⩭":"&congdot;","𝕔":"&copf;","℗":"&copysr;","↵":"&crarr;","✗":"&cross;","𝒸":"&cscr;","⫏":"&csub;","⫑":"&csube;","⫐":"&csup;","⫒":"&csupe;","⋯":"&ctdot;","⤸":"&cudarrl;","⤵":"&cudarrr;","⋞":"&curlyeqprec;","⋟":"&curlyeqsucc;","↶":"&curvearrowleft;","⤽":"&cularrp;","∪":"&cup;","⩈":"&cupbrcap;","⩆":"&cupcap;","⩊":"&cupcup;","⊍":"&cupdot;","⩅":"&cupor;","∪︀":"&cups;","↷":"&curvearrowright;","⤼":"&curarrm;","⋎":"&cuvee;","⋏":"&cuwed;","¤":"&curren;","∱":"&cwint;","⌭":"&cylcty;","⥥":"&dHar;","†":"&dagger;","ℸ":"&daleth;","‐":"&hyphen;","⤏":"&rBarr;","ď":"&dcaron;","д":"&dcy;","⇊":"&downdownarrows;","⩷":"&eDDot;","°":"&deg;","δ":"&delta;","⦱":"&demptyv;","⥿":"&dfisht;","𝔡":"&dfr;","♦":"&diams;","ϝ":"&gammad;","⋲":"&disin;","÷":"&divide;","⋇":"&divonx;","ђ":"&djcy;","⌞":"&llcorner;","⌍":"&dlcrop;",$:"&dollar;","𝕕":"&dopf;","≑":"&eDot;","∸":"&minusd;","∔":"&plusdo;","⊡":"&sdotb;","⌟":"&lrcorner;","⌌":"&drcrop;","𝒹":"&dscr;","ѕ":"&dscy;","⧶":"&dsol;","đ":"&dstrok;","⋱":"&dtdot;","▿":"&triangledown;","⦦":"&dwangle;","џ":"&dzcy;","⟿":"&dzigrarr;","é":"&eacute;","⩮":"&easter;","ě":"&ecaron;","≖":"&eqcirc;","ê":"&ecirc;","≕":"&eqcolon;","э":"&ecy;","ė":"&edot;","≒":"&fallingdotseq;","𝔢":"&efr;","⪚":"&eg;","è":"&egrave;","⪖":"&eqslantgtr;","⪘":"&egsdot;","⪙":"&el;","⏧":"&elinters;","ℓ":"&ell;","⪕":"&eqslantless;","⪗":"&elsdot;","ē":"&emacr;","∅":"&varnothing;"," ":"&emsp13;"," ":"&emsp14;"," ":"&emsp;","ŋ":"&eng;"," ":"&ensp;","ę":"&eogon;","𝕖":"&eopf;","⋕":"&epar;","⧣":"&eparsl;","⩱":"&eplus;","ε":"&epsilon;","ϵ":"&varepsilon;","=":"&equals;","≟":"&questeq;","⩸":"&equivDD;","⧥":"&eqvparsl;","≓":"&risingdotseq;","⥱":"&erarr;","ℯ":"&escr;","η":"&eta;","ð":"&eth;","ë":"&euml;","€":"&euro;","!":"&excl;","ф":"&fcy;","♀":"&female;","ﬃ":"&ffilig;","ﬀ":"&fflig;","ﬄ":"&ffllig;","𝔣":"&ffr;","ﬁ":"&filig;",fj:"&fjlig;","♭":"&flat;","ﬂ":"&fllig;","▱":"&fltns;","ƒ":"&fnof;","𝕗":"&fopf;","⋔":"&pitchfork;","⫙":"&forkv;","⨍":"&fpartint;","½":"&half;","⅓":"&frac13;","¼":"&frac14;","⅕":"&frac15;","⅙":"&frac16;","⅛":"&frac18;","⅔":"&frac23;","⅖":"&frac25;","¾":"&frac34;","⅗":"&frac35;","⅜":"&frac38;","⅘":"&frac45;","⅚":"&frac56;","⅝":"&frac58;","⅞":"&frac78;","⁄":"&frasl;","⌢":"&sfrown;","𝒻":"&fscr;","⪌":"&gtreqqless;","ǵ":"&gacute;","γ":"&gamma;","⪆":"&gtrapprox;","ğ":"&gbreve;","ĝ":"&gcirc;","г":"&gcy;","ġ":"&gdot;","⪩":"&gescc;","⪀":"&gesdot;","⪂":"&gesdoto;","⪄":"&gesdotol;","⋛︀":"&gesl;","⪔":"&gesles;","𝔤":"&gfr;","ℷ":"&gimel;","ѓ":"&gjcy;","⪒":"&glE;","⪥":"&gla;","⪤":"&glj;","≩":"&gneqq;","⪊":"&gnapprox;","⪈":"&gneq;","⋧":"&gnsim;","𝕘":"&gopf;","ℊ":"&gscr;","⪎":"&gsime;","⪐":"&gsiml;","⪧":"&gtcc;","⩺":"&gtcir;","⋗":"&gtrdot;","⦕":"&gtlPar;","⩼":"&gtquest;","⥸":"&gtrarr;","≩︀":"&gvnE;","ъ":"&hardcy;","⥈":"&harrcir;","↭":"&leftrightsquigarrow;","ℏ":"&plankv;","ĥ":"&hcirc;","♥":"&heartsuit;","…":"&mldr;","⊹":"&hercon;","𝔥":"&hfr;","⤥":"&searhk;","⤦":"&swarhk;","⇿":"&hoarr;","∻":"&homtht;","↩":"&larrhk;","↪":"&rarrhk;","𝕙":"&hopf;","―":"&horbar;","𝒽":"&hscr;","ħ":"&hstrok;","⁃":"&hybull;","í":"&iacute;","î":"&icirc;","и":"&icy;","е":"&iecy;","¡":"&iexcl;","𝔦":"&ifr;","ì":"&igrave;","⨌":"&qint;","∭":"&tint;","⧜":"&iinfin;","℩":"&iiota;","ĳ":"&ijlig;","ī":"&imacr;","ı":"&inodot;","⊷":"&imof;","Ƶ":"&imped;","℅":"&incare;","∞":"&infin;","⧝":"&infintie;","⊺":"&intercal;","⨗":"&intlarhk;","⨼":"&iprod;","ё":"&iocy;","į":"&iogon;","𝕚":"&iopf;","ι":"&iota;","¿":"&iquest;","𝒾":"&iscr;","⋹":"&isinE;","⋵":"&isindot;","⋴":"&isins;","⋳":"&isinsv;","ĩ":"&itilde;","і":"&iukcy;","ï":"&iuml;","ĵ":"&jcirc;","й":"&jcy;","𝔧":"&jfr;","ȷ":"&jmath;","𝕛":"&jopf;","𝒿":"&jscr;","ј":"&jsercy;","є":"&jukcy;","κ":"&kappa;","ϰ":"&varkappa;","ķ":"&kcedil;","к":"&kcy;","𝔨":"&kfr;","ĸ":"&kgreen;","х":"&khcy;","ќ":"&kjcy;","𝕜":"&kopf;","𝓀":"&kscr;","⤛":"&lAtail;","⤎":"&lBarr;","⪋":"&lesseqqgtr;","⥢":"&lHar;","ĺ":"&lacute;","⦴":"&laemptyv;","λ":"&lambda;","⦑":"&langd;","⪅":"&lessapprox;","«":"&laquo;","⤟":"&larrbfs;","⤝":"&larrfs;","↫":"&looparrowleft;","⤹":"&larrpl;","⥳":"&larrsim;","↢":"&leftarrowtail;","⪫":"&lat;","⤙":"&latail;","⪭":"&late;","⪭︀":"&lates;","⤌":"&lbarr;","❲":"&lbbrk;","{":"&lcub;","[":"&lsqb;","⦋":"&lbrke;","⦏":"&lbrksld;","⦍":"&lbrkslu;","ľ":"&lcaron;","ļ":"&lcedil;","л":"&lcy;","⤶":"&ldca;","⥧":"&ldrdhar;","⥋":"&ldrushar;","↲":"&ldsh;","≤":"&leq;","⇇":"&llarr;","⋋":"&lthree;","⪨":"&lescc;","⩿":"&lesdot;","⪁":"&lesdoto;","⪃":"&lesdotor;","⋚︀":"&lesg;","⪓":"&lesges;","⋖":"&ltdot;","⥼":"&lfisht;","𝔩":"&lfr;","⪑":"&lgE;","⥪":"&lharul;","▄":"&lhblk;","љ":"&ljcy;","⥫":"&llhard;","◺":"&lltri;","ŀ":"&lmidot;","⎰":"&lmoustache;","≨":"&lneqq;","⪉":"&lnapprox;","⪇":"&lneq;","⋦":"&lnsim;","⟬":"&loang;","⇽":"&loarr;","⟼":"&xmap;","↬":"&rarrlp;","⦅":"&lopar;","𝕝":"&lopf;","⨭":"&loplus;","⨴":"&lotimes;","∗":"&lowast;","◊":"&lozenge;","(":"&lpar;","⦓":"&lparlt;","⥭":"&lrhard;","‎":"&lrm;","⊿":"&lrtri;","‹":"&lsaquo;","𝓁":"&lscr;","⪍":"&lsime;","⪏":"&lsimg;","‚":"&sbquo;","ł":"&lstrok;","⪦":"&ltcc;","⩹":"&ltcir;","⋉":"&ltimes;","⥶":"&ltlarr;","⩻":"&ltquest;","⦖":"&ltrPar;","◃":"&triangleleft;","⥊":"&lurdshar;","⥦":"&luruhar;","≨︀":"&lvnE;","∺":"&mDDot;","¯":"&strns;","♂":"&male;","✠":"&maltese;","▮":"&marker;","⨩":"&mcomma;","м":"&mcy;","—":"&mdash;","𝔪":"&mfr;","℧":"&mho;","µ":"&micro;","⫰":"&midcir;","−":"&minus;","⨪":"&minusdu;","⫛":"&mlcp;","⊧":"&models;","𝕞":"&mopf;","𝓂":"&mscr;","μ":"&mu;","⊸":"&mumap;","⋙̸":"&nGg;","≫⃒":"&nGt;","⇍":"&nlArr;","⇎":"&nhArr;","⋘̸":"&nLl;","≪⃒":"&nLt;","⇏":"&nrArr;","⊯":"&nVDash;","⊮":"&nVdash;","ń":"&nacute;","∠⃒":"&nang;","⩰̸":"&napE;","≋̸":"&napid;","ŉ":"&napos;","♮":"&natural;","⩃":"&ncap;","ň":"&ncaron;","ņ":"&ncedil;","⩭̸":"&ncongdot;","⩂":"&ncup;","н":"&ncy;","–":"&ndash;","⇗":"&neArr;","⤤":"&nearhk;","≐̸":"&nedot;","⤨":"&toea;","𝔫":"&nfr;","↮":"&nleftrightarrow;","⫲":"&nhpar;","⋼":"&nis;","⋺":"&nisd;","њ":"&njcy;","≦̸":"&nleqq;","↚":"&nleftarrow;","‥":"&nldr;","𝕟":"&nopf;","¬":"&not;","⋹̸":"&notinE;","⋵̸":"&notindot;","⋷":"&notinvb;","⋶":"&notinvc;","⋾":"&notnivb;","⋽":"&notnivc;","⫽⃥":"&nparsl;","∂̸":"&npart;","⨔":"&npolint;","↛":"&nrightarrow;","⤳̸":"&nrarrc;","↝̸":"&nrarrw;","𝓃":"&nscr;","⊄":"&nsub;","⫅̸":"&nsubseteqq;","⊅":"&nsup;","⫆̸":"&nsupseteqq;","ñ":"&ntilde;","ν":"&nu;","#":"&num;","№":"&numero;"," ":"&numsp;","⊭":"&nvDash;","⤄":"&nvHarr;","≍⃒":"&nvap;","⊬":"&nvdash;","≥⃒":"&nvge;",">⃒":"&nvgt;","⧞":"&nvinfin;","⤂":"&nvlArr;","≤⃒":"&nvle;","<⃒":"&nvlt;","⊴⃒":"&nvltrie;","⤃":"&nvrArr;","⊵⃒":"&nvrtrie;","∼⃒":"&nvsim;","⇖":"&nwArr;","⤣":"&nwarhk;","⤧":"&nwnear;","ó":"&oacute;","ô":"&ocirc;","о":"&ocy;","ő":"&odblac;","⨸":"&odiv;","⦼":"&odsold;","œ":"&oelig;","⦿":"&ofcir;","𝔬":"&ofr;","˛":"&ogon;","ò":"&ograve;","⧁":"&ogt;","⦵":"&ohbar;","⦾":"&olcir;","⦻":"&olcross;","⧀":"&olt;","ō":"&omacr;","ω":"&omega;","ο":"&omicron;","⦶":"&omid;","𝕠":"&oopf;","⦷":"&opar;","⦹":"&operp;","∨":"&vee;","⩝":"&ord;","ℴ":"&oscr;","ª":"&ordf;","º":"&ordm;","⊶":"&origof;","⩖":"&oror;","⩗":"&orslope;","⩛":"&orv;","ø":"&oslash;","⊘":"&osol;","õ":"&otilde;","⨶":"&otimesas;","ö":"&ouml;","⌽":"&ovbar;","¶":"&para;","⫳":"&parsim;","⫽":"&parsl;","п":"&pcy;","%":"&percnt;",".":"&period;","‰":"&permil;","‱":"&pertenk;","𝔭":"&pfr;","φ":"&phi;","ϕ":"&varphi;","☎":"&phone;","π":"&pi;","ϖ":"&varpi;","ℎ":"&planckh;","+":"&plus;","⨣":"&plusacir;","⨢":"&pluscir;","⨥":"&plusdu;","⩲":"&pluse;","⨦":"&plussim;","⨧":"&plustwo;","⨕":"&pointint;","𝕡":"&popf;","£":"&pound;","⪳":"&prE;","⪷":"&precapprox;","⪹":"&prnap;","⪵":"&prnE;","⋨":"&prnsim;","′":"&prime;","⌮":"&profalar;","⌒":"&profline;","⌓":"&profsurf;","⊰":"&prurel;","𝓅":"&pscr;","ψ":"&psi;"," ":"&puncsp;","𝔮":"&qfr;","𝕢":"&qopf;","⁗":"&qprime;","𝓆":"&qscr;","⨖":"&quatint;","?":"&quest;","⤜":"&rAtail;","⥤":"&rHar;","∽̱":"&race;","ŕ":"&racute;","⦳":"&raemptyv;","⦒":"&rangd;","⦥":"&range;","»":"&raquo;","⥵":"&rarrap;","⤠":"&rarrbfs;","⤳":"&rarrc;","⤞":"&rarrfs;","⥅":"&rarrpl;","⥴":"&rarrsim;","↣":"&rightarrowtail;","↝":"&rightsquigarrow;","⤚":"&ratail;","∶":"&ratio;","❳":"&rbbrk;","}":"&rcub;","]":"&rsqb;","⦌":"&rbrke;","⦎":"&rbrksld;","⦐":"&rbrkslu;","ř":"&rcaron;","ŗ":"&rcedil;","р":"&rcy;","⤷":"&rdca;","⥩":"&rdldhar;","↳":"&rdsh;","▭":"&rect;","⥽":"&rfisht;","𝔯":"&rfr;","⥬":"&rharul;","ρ":"&rho;","ϱ":"&varrho;","⇉":"&rrarr;","⋌":"&rthree;","˚":"&ring;","‏":"&rlm;","⎱":"&rmoustache;","⫮":"&rnmid;","⟭":"&roang;","⇾":"&roarr;","⦆":"&ropar;","𝕣":"&ropf;","⨮":"&roplus;","⨵":"&rotimes;",")":"&rpar;","⦔":"&rpargt;","⨒":"&rppolint;","›":"&rsaquo;","𝓇":"&rscr;","⋊":"&rtimes;","▹":"&triangleright;","⧎":"&rtriltri;","⥨":"&ruluhar;","℞":"&rx;","ś":"&sacute;","⪴":"&scE;","⪸":"&succapprox;","š":"&scaron;","ş":"&scedil;","ŝ":"&scirc;","⪶":"&succneqq;","⪺":"&succnapprox;","⋩":"&succnsim;","⨓":"&scpolint;","с":"&scy;","⋅":"&sdot;","⩦":"&sdote;","⇘":"&seArr;","§":"&sect;",";":"&semi;","⤩":"&tosa;","✶":"&sext;","𝔰":"&sfr;","♯":"&sharp;","щ":"&shchcy;","ш":"&shcy;","­":"&shy;","σ":"&sigma;","ς":"&varsigma;","⩪":"&simdot;","⪞":"&simg;","⪠":"&simgE;","⪝":"&siml;","⪟":"&simlE;","≆":"&simne;","⨤":"&simplus;","⥲":"&simrarr;","⨳":"&smashp;","⧤":"&smeparsl;","⌣":"&ssmile;","⪪":"&smt;","⪬":"&smte;","⪬︀":"&smtes;","ь":"&softcy;","/":"&sol;","⧄":"&solb;","⌿":"&solbar;","𝕤":"&sopf;","♠":"&spadesuit;","⊓︀":"&sqcaps;","⊔︀":"&sqcups;","𝓈":"&sscr;","☆":"&star;","⊂":"&subset;","⫅":"&subseteqq;","⪽":"&subdot;","⫃":"&subedot;","⫁":"&submult;","⫋":"&subsetneqq;","⊊":"&subsetneq;","⪿":"&subplus;","⥹":"&subrarr;","⫇":"&subsim;","⫕":"&subsub;","⫓":"&subsup;","♪":"&sung;","¹":"&sup1;","²":"&sup2;","³":"&sup3;","⫆":"&supseteqq;","⪾":"&supdot;","⫘":"&supdsub;","⫄":"&supedot;","⟉":"&suphsol;","⫗":"&suphsub;","⥻":"&suplarr;","⫂":"&supmult;","⫌":"&supsetneqq;","⊋":"&supsetneq;","⫀":"&supplus;","⫈":"&supsim;","⫔":"&supsub;","⫖":"&supsup;","⇙":"&swArr;","⤪":"&swnwar;","ß":"&szlig;","⌖":"&target;","τ":"&tau;","ť":"&tcaron;","ţ":"&tcedil;","т":"&tcy;","⌕":"&telrec;","𝔱":"&tfr;","θ":"&theta;","ϑ":"&vartheta;","þ":"&thorn;","×":"&times;","⨱":"&timesbar;","⨰":"&timesd;","⌶":"&topbot;","⫱":"&topcir;","𝕥":"&topf;","⫚":"&topfork;","‴":"&tprime;","▵":"&utri;","≜":"&trie;","◬":"&tridot;","⨺":"&triminus;","⨹":"&triplus;","⧍":"&trisb;","⨻":"&tritime;","⏢":"&trpezium;","𝓉":"&tscr;","ц":"&tscy;","ћ":"&tshcy;","ŧ":"&tstrok;","⥣":"&uHar;","ú":"&uacute;","ў":"&ubrcy;","ŭ":"&ubreve;","û":"&ucirc;","у":"&ucy;","ű":"&udblac;","⥾":"&ufisht;","𝔲":"&ufr;","ù":"&ugrave;","▀":"&uhblk;","⌜":"&ulcorner;","⌏":"&ulcrop;","◸":"&ultri;","ū":"&umacr;","ų":"&uogon;","𝕦":"&uopf;","υ":"&upsilon;","⇈":"&uuarr;","⌝":"&urcorner;","⌎":"&urcrop;","ů":"&uring;","◹":"&urtri;","𝓊":"&uscr;","⋰":"&utdot;","ũ":"&utilde;","ü":"&uuml;","⦧":"&uwangle;","⫨":"&vBar;","⫩":"&vBarv;","⦜":"&vangrt;","⊊︀":"&vsubne;","⫋︀":"&vsubnE;","⊋︀":"&vsupne;","⫌︀":"&vsupnE;","в":"&vcy;","⊻":"&veebar;","≚":"&veeeq;","⋮":"&vellip;","𝔳":"&vfr;","𝕧":"&vopf;","𝓋":"&vscr;","⦚":"&vzigzag;","ŵ":"&wcirc;","⩟":"&wedbar;","≙":"&wedgeq;","℘":"&wp;","𝔴":"&wfr;","𝕨":"&wopf;","𝓌":"&wscr;","𝔵":"&xfr;","ξ":"&xi;","⋻":"&xnis;","𝕩":"&xopf;","𝓍":"&xscr;","ý":"&yacute;","я":"&yacy;","ŷ":"&ycirc;","ы":"&ycy;","¥":"&yen;","𝔶":"&yfr;","ї":"&yicy;","𝕪":"&yopf;","𝓎":"&yscr;","ю":"&yucy;","ÿ":"&yuml;","ź":"&zacute;","ž":"&zcaron;","з":"&zcy;","ż":"&zdot;","ζ":"&zeta;","𝔷":"&zfr;","ж":"&zhcy;","⇝":"&zigrarr;","𝕫":"&zopf;","𝓏":"&zscr;","‍":"&zwj;","‌":"&zwnj;"}}}},9131:function(t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.numericUnicodeMap={0:65533,128:8364,130:8218,131:402,132:8222,133:8230,134:8224,135:8225,136:710,137:8240,138:352,139:8249,140:338,142:381,145:8216,146:8217,147:8220,148:8221,149:8226,150:8211,151:8212,152:732,153:8482,154:353,155:8250,156:339,158:382,159:376}},9055:function(t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.fromCodePoint=String.fromCodePoint||function(t){return String.fromCharCode(Math.floor((t-65536)/1024)+55296,(t-65536)%1024+56320)},n.getCodePoint=String.prototype.codePointAt?function(t,n){return t.codePointAt(n)}:function(t,n){return 1024*(t.charCodeAt(n)-55296)+t.charCodeAt(n+1)-56320+65536},n.highSurrogateFrom=55296,n.highSurrogateTo=56319},9809:function(t,n,a){t.exports=function t(n,a,e){function r(i,o){if(!a[i]){if(!n[i]){if(l)return l(i,!0);var s=new Error("Cannot find module '"+i+"'");throw s.code="MODULE_NOT_FOUND",s}var u=a[i]={exports:{}};n[i][0].call(u.exports,(function(t){return r(n[i][1][t]||t)}),u,u.exports,t,n,a,e)}return a[i].exports}for(var l=void 0,i=0;i<e.length;i++)r(e[i]);return r}({1:[function(t,n,e){(function(t){"use strict";var a,e,r=t.MutationObserver||t.WebKitMutationObserver;if(r){var l=0,i=new r(c),o=t.document.createTextNode("");i.observe(o,{characterData:!0}),a=function(){o.data=l=++l%2}}else if(t.setImmediate||void 0===t.MessageChannel)a="document"in t&&"onreadystatechange"in t.document.createElement("script")?function(){var n=t.document.createElement("script");n.onreadystatechange=function(){c(),n.onreadystatechange=null,n.parentNode.removeChild(n),n=null},t.document.documentElement.appendChild(n)}:function(){setTimeout(c,0)};else{var s=new t.MessageChannel;s.port1.onmessage=c,a=function(){s.port2.postMessage(0)}}var u=[];function c(){var t,n;e=!0;for(var a=u.length;a;){for(n=u,u=[],t=-1;++t<a;)n[t]();a=u.length}e=!1}n.exports=function(t){1!==u.push(t)||e||a()}}).call(this,void 0!==a.g?a.g:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],2:[function(t,n,a){"use strict";var e=t(1);function r(){}var l={},i=["REJECTED"],o=["FULFILLED"],s=["PENDING"];function u(t){if("function"!=typeof t)throw new TypeError("resolver must be a function");this.state=s,this.queue=[],this.outcome=void 0,t!==r&&g(this,t)}function c(t,n,a){this.promise=t,"function"==typeof n&&(this.onFulfilled=n,this.callFulfilled=this.otherCallFulfilled),"function"==typeof a&&(this.onRejected=a,this.callRejected=this.otherCallRejected)}function d(t,n,a){e((function(){var e;try{e=n(a)}catch(n){return l.reject(t,n)}e===t?l.reject(t,new TypeError("Cannot resolve promise with itself")):l.resolve(t,e)}))}function m(t){var n=t&&t.then;if(t&&("object"==typeof t||"function"==typeof t)&&"function"==typeof n)return function(){n.apply(t,arguments)}}function g(t,n){var a=!1;function e(n){a||(a=!0,l.reject(t,n))}function r(n){a||(a=!0,l.resolve(t,n))}var i=f((function(){n(r,e)}));"error"===i.status&&e(i.value)}function f(t,n){var a={};try{a.value=t(n),a.status="success"}catch(t){a.status="error",a.value=t}return a}n.exports=u,u.prototype.catch=function(t){return this.then(null,t)},u.prototype.then=function(t,n){if("function"!=typeof t&&this.state===o||"function"!=typeof n&&this.state===i)return this;var a=new this.constructor(r);return this.state!==s?d(a,this.state===o?t:n,this.outcome):this.queue.push(new c(a,t,n)),a},c.prototype.callFulfilled=function(t){l.resolve(this.promise,t)},c.prototype.otherCallFulfilled=function(t){d(this.promise,this.onFulfilled,t)},c.prototype.callRejected=function(t){l.reject(this.promise,t)},c.prototype.otherCallRejected=function(t){d(this.promise,this.onRejected,t)},l.resolve=function(t,n){var a=f(m,n);if("error"===a.status)return l.reject(t,a.value);var e=a.value;if(e)g(t,e);else{t.state=o,t.outcome=n;for(var r=-1,i=t.queue.length;++r<i;)t.queue[r].callFulfilled(n)}return t},l.reject=function(t,n){t.state=i,t.outcome=n;for(var a=-1,e=t.queue.length;++a<e;)t.queue[a].callRejected(n);return t},u.resolve=function(t){return t instanceof this?t:l.resolve(new this(r),t)},u.reject=function(t){var n=new this(r);return l.reject(n,t)},u.all=function(t){var n=this;if("[object Array]"!==Object.prototype.toString.call(t))return this.reject(new TypeError("must be an array"));var a=t.length,e=!1;if(!a)return this.resolve([]);for(var i=new Array(a),o=0,s=-1,u=new this(r);++s<a;)c(t[s],s);return u;function c(t,r){n.resolve(t).then((function(t){i[r]=t,++o!==a||e||(e=!0,l.resolve(u,i))}),(function(t){e||(e=!0,l.reject(u,t))}))}},u.race=function(t){var n=this;if("[object Array]"!==Object.prototype.toString.call(t))return this.reject(new TypeError("must be an array"));var a,e=t.length,i=!1;if(!e)return this.resolve([]);for(var o=-1,s=new this(r);++o<e;)a=t[o],n.resolve(a).then((function(t){i||(i=!0,l.resolve(s,t))}),(function(t){i||(i=!0,l.reject(s,t))}));return s}},{1:1}],3:[function(t,n,e){(function(n){"use strict";"function"!=typeof n.Promise&&(n.Promise=t(2))}).call(this,void 0!==a.g?a.g:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{2:2}],4:[function(t,n,a){"use strict";var e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};var r=function(){try{if("undefined"!=typeof indexedDB)return indexedDB;if("undefined"!=typeof webkitIndexedDB)return webkitIndexedDB;if("undefined"!=typeof mozIndexedDB)return mozIndexedDB;if("undefined"!=typeof OIndexedDB)return OIndexedDB;if("undefined"!=typeof msIndexedDB)return msIndexedDB}catch(t){return}}();function l(t,n){t=t||[],n=n||{};try{return new Blob(t,n)}catch(r){if("TypeError"!==r.name)throw r;for(var a=new("undefined"!=typeof BlobBuilder?BlobBuilder:"undefined"!=typeof MSBlobBuilder?MSBlobBuilder:"undefined"!=typeof MozBlobBuilder?MozBlobBuilder:WebKitBlobBuilder),e=0;e<t.length;e+=1)a.append(t[e]);return a.getBlob(n.type)}}"undefined"==typeof Promise&&t(3);var i=Promise;function o(t,n){n&&t.then((function(t){n(null,t)}),(function(t){n(t)}))}function s(t,n,a){"function"==typeof n&&t.then(n),"function"==typeof a&&t.catch(a)}function u(t){return"string"!=typeof t&&(console.warn(t+" used as a key, but it is not a string."),t=String(t)),t}function c(){if(arguments.length&&"function"==typeof arguments[arguments.length-1])return arguments[arguments.length-1]}var d="local-forage-detect-blob-support",m=void 0,g={},f=Object.prototype.toString,p="readonly",b="readwrite";function h(t){for(var n=t.length,a=new ArrayBuffer(n),e=new Uint8Array(a),r=0;r<n;r++)e[r]=t.charCodeAt(r);return a}function v(t){return"boolean"==typeof m?i.resolve(m):function(t){return new i((function(n){var a=t.transaction(d,b),e=l([""]);a.objectStore(d).put(e,"key"),a.onabort=function(t){t.preventDefault(),t.stopPropagation(),n(!1)},a.oncomplete=function(){var t=navigator.userAgent.match(/Chrome\/(\d+)/),a=navigator.userAgent.match(/Edge\//);n(a||!t||parseInt(t[1],10)>=43)}})).catch((function(){return!1}))}(t).then((function(t){return m=t}))}function y(t){var n=g[t.name],a={};a.promise=new i((function(t,n){a.resolve=t,a.reject=n})),n.deferredOperations.push(a),n.dbReady?n.dbReady=n.dbReady.then((function(){return a.promise})):n.dbReady=a.promise}function k(t){var n=g[t.name].deferredOperations.pop();if(n)return n.resolve(),n.promise}function w(t,n){var a=g[t.name].deferredOperations.pop();if(a)return a.reject(n),a.promise}function E(t,n){return new i((function(a,e){if(g[t.name]=g[t.name]||{forages:[],db:null,dbReady:null,deferredOperations:[]},t.db){if(!n)return a(t.db);y(t),t.db.close()}var l=[t.name];n&&l.push(t.version);var i=r.open.apply(r,l);n&&(i.onupgradeneeded=function(n){var a=i.result;try{a.createObjectStore(t.storeName),n.oldVersion<=1&&a.createObjectStore(d)}catch(a){if("ConstraintError"!==a.name)throw a;console.warn('The database "'+t.name+'" has been upgraded from version '+n.oldVersion+" to version "+n.newVersion+', but the storage "'+t.storeName+'" already exists.')}}),i.onerror=function(t){t.preventDefault(),e(i.error)},i.onsuccess=function(){var n=i.result;n.onversionchange=function(t){t.target.close()},a(n),k(t)}}))}function z(t){return E(t,!1)}function x(t){return E(t,!0)}function T(t,n){if(!t.db)return!0;var a=!t.db.objectStoreNames.contains(t.storeName),e=t.version<t.db.version,r=t.version>t.db.version;if(e&&(t.version!==n&&console.warn('The database "'+t.name+"\" can't be downgraded from version "+t.db.version+" to version "+t.version+"."),t.version=t.db.version),r||a){if(a){var l=t.db.version+1;l>t.version&&(t.version=l)}return!0}return!1}function _(t){return l([h(atob(t.data))],{type:t.type})}function I(t){return t&&t.__local_forage_encoded_blob}function A(t){var n=this,a=n._initReady().then((function(){var t=g[n._dbInfo.name];if(t&&t.dbReady)return t.dbReady}));return s(a,t,t),a}function S(t,n,a,e){void 0===e&&(e=1);try{var r=t.db.transaction(t.storeName,n);a(null,r)}catch(r){if(e>0&&(!t.db||"InvalidStateError"===r.name||"NotFoundError"===r.name))return i.resolve().then((function(){if(!t.db||"NotFoundError"===r.name&&!t.db.objectStoreNames.contains(t.storeName)&&t.version<=t.db.version)return t.db&&(t.version=t.db.version+1),x(t)})).then((function(){return function(t){y(t);for(var n=g[t.name],a=n.forages,e=0;e<a.length;e++){var r=a[e];r._dbInfo.db&&(r._dbInfo.db.close(),r._dbInfo.db=null)}return t.db=null,z(t).then((function(n){return t.db=n,T(t)?x(t):n})).then((function(e){t.db=n.db=e;for(var r=0;r<a.length;r++)a[r]._dbInfo.db=e})).catch((function(n){throw w(t,n),n}))}(t).then((function(){S(t,n,a,e-1)}))})).catch(a);a(r)}}var R={_driver:"asyncStorage",_initStorage:function(t){var n=this,a={db:null};if(t)for(var e in t)a[e]=t[e];var r=g[a.name];r||(r={forages:[],db:null,dbReady:null,deferredOperations:[]},g[a.name]=r),r.forages.push(n),n._initReady||(n._initReady=n.ready,n.ready=A);var l=[];function o(){return i.resolve()}for(var s=0;s<r.forages.length;s++){var u=r.forages[s];u!==n&&l.push(u._initReady().catch(o))}var c=r.forages.slice(0);return i.all(l).then((function(){return a.db=r.db,z(a)})).then((function(t){return a.db=t,T(a,n._defaultConfig.version)?x(a):t})).then((function(t){a.db=r.db=t,n._dbInfo=a;for(var e=0;e<c.length;e++){var l=c[e];l!==n&&(l._dbInfo.db=a.db,l._dbInfo.version=a.version)}}))},_support:function(){try{if(!r||!r.open)return!1;var t="undefined"!=typeof openDatabase&&/(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent)&&!/BlackBerry/.test(navigator.platform),n="function"==typeof fetch&&-1!==fetch.toString().indexOf("[native code");return(!t||n)&&"undefined"!=typeof indexedDB&&"undefined"!=typeof IDBKeyRange}catch(t){return!1}}(),iterate:function(t,n){var a=this,e=new i((function(n,e){a.ready().then((function(){S(a._dbInfo,p,(function(r,l){if(r)return e(r);try{var i=l.objectStore(a._dbInfo.storeName).openCursor(),o=1;i.onsuccess=function(){var a=i.result;if(a){var e=a.value;I(e)&&(e=_(e));var r=t(e,a.key,o++);void 0!==r?n(r):a.continue()}else n()},i.onerror=function(){e(i.error)}}catch(t){e(t)}}))})).catch(e)}));return o(e,n),e},getItem:function(t,n){var a=this;t=u(t);var e=new i((function(n,e){a.ready().then((function(){S(a._dbInfo,p,(function(r,l){if(r)return e(r);try{var i=l.objectStore(a._dbInfo.storeName).get(t);i.onsuccess=function(){var t=i.result;void 0===t&&(t=null),I(t)&&(t=_(t)),n(t)},i.onerror=function(){e(i.error)}}catch(t){e(t)}}))})).catch(e)}));return o(e,n),e},setItem:function(t,n,a){var e=this;t=u(t);var r=new i((function(a,r){var l;e.ready().then((function(){return l=e._dbInfo,"[object Blob]"===f.call(n)?v(l.db).then((function(t){return t?n:(a=n,new i((function(t,n){var e=new FileReader;e.onerror=n,e.onloadend=function(n){var e=btoa(n.target.result||"");t({__local_forage_encoded_blob:!0,data:e,type:a.type})},e.readAsBinaryString(a)})));var a})):n})).then((function(n){S(e._dbInfo,b,(function(l,i){if(l)return r(l);try{var o=i.objectStore(e._dbInfo.storeName);null===n&&(n=void 0);var s=o.put(n,t);i.oncomplete=function(){void 0===n&&(n=null),a(n)},i.onabort=i.onerror=function(){var t=s.error?s.error:s.transaction.error;r(t)}}catch(t){r(t)}}))})).catch(r)}));return o(r,a),r},removeItem:function(t,n){var a=this;t=u(t);var e=new i((function(n,e){a.ready().then((function(){S(a._dbInfo,b,(function(r,l){if(r)return e(r);try{var i=l.objectStore(a._dbInfo.storeName).delete(t);l.oncomplete=function(){n()},l.onerror=function(){e(i.error)},l.onabort=function(){var t=i.error?i.error:i.transaction.error;e(t)}}catch(t){e(t)}}))})).catch(e)}));return o(e,n),e},clear:function(t){var n=this,a=new i((function(t,a){n.ready().then((function(){S(n._dbInfo,b,(function(e,r){if(e)return a(e);try{var l=r.objectStore(n._dbInfo.storeName).clear();r.oncomplete=function(){t()},r.onabort=r.onerror=function(){var t=l.error?l.error:l.transaction.error;a(t)}}catch(t){a(t)}}))})).catch(a)}));return o(a,t),a},length:function(t){var n=this,a=new i((function(t,a){n.ready().then((function(){S(n._dbInfo,p,(function(e,r){if(e)return a(e);try{var l=r.objectStore(n._dbInfo.storeName).count();l.onsuccess=function(){t(l.result)},l.onerror=function(){a(l.error)}}catch(t){a(t)}}))})).catch(a)}));return o(a,t),a},key:function(t,n){var a=this,e=new i((function(n,e){t<0?n(null):a.ready().then((function(){S(a._dbInfo,p,(function(r,l){if(r)return e(r);try{var i=l.objectStore(a._dbInfo.storeName),o=!1,s=i.openKeyCursor();s.onsuccess=function(){var a=s.result;a?0===t||o?n(a.key):(o=!0,a.advance(t)):n(null)},s.onerror=function(){e(s.error)}}catch(t){e(t)}}))})).catch(e)}));return o(e,n),e},keys:function(t){var n=this,a=new i((function(t,a){n.ready().then((function(){S(n._dbInfo,p,(function(e,r){if(e)return a(e);try{var l=r.objectStore(n._dbInfo.storeName).openKeyCursor(),i=[];l.onsuccess=function(){var n=l.result;n?(i.push(n.key),n.continue()):t(i)},l.onerror=function(){a(l.error)}}catch(t){a(t)}}))})).catch(a)}));return o(a,t),a},dropInstance:function(t,n){n=c.apply(this,arguments);var a,e=this.config();if((t="function"!=typeof t&&t||{}).name||(t.name=t.name||e.name,t.storeName=t.storeName||e.storeName),t.name){var l=t.name===e.name&&this._dbInfo.db?i.resolve(this._dbInfo.db):z(t).then((function(n){var a=g[t.name],e=a.forages;a.db=n;for(var r=0;r<e.length;r++)e[r]._dbInfo.db=n;return n}));a=t.storeName?l.then((function(n){if(n.objectStoreNames.contains(t.storeName)){var a=n.version+1;y(t);var e=g[t.name],l=e.forages;n.close();for(var o=0;o<l.length;o++){var s=l[o];s._dbInfo.db=null,s._dbInfo.version=a}var u=new i((function(n,e){var l=r.open(t.name,a);l.onerror=function(t){l.result.close(),e(t)},l.onupgradeneeded=function(){l.result.deleteObjectStore(t.storeName)},l.onsuccess=function(){var t=l.result;t.close(),n(t)}}));return u.then((function(t){e.db=t;for(var n=0;n<l.length;n++){var a=l[n];a._dbInfo.db=t,k(a._dbInfo)}})).catch((function(n){throw(w(t,n)||i.resolve()).catch((function(){})),n}))}})):l.then((function(n){y(t);var a=g[t.name],e=a.forages;n.close();for(var l=0;l<e.length;l++)e[l]._dbInfo.db=null;var o=new i((function(n,a){var e=r.deleteDatabase(t.name);e.onerror=function(){var t=e.result;t&&t.close(),a(e.error)},e.onblocked=function(){console.warn('dropInstance blocked for database "'+t.name+'" until all open connections are closed')},e.onsuccess=function(){var t=e.result;t&&t.close(),n(t)}}));return o.then((function(t){a.db=t;for(var n=0;n<e.length;n++)k(e[n]._dbInfo)})).catch((function(n){throw(w(t,n)||i.resolve()).catch((function(){})),n}))}))}else a=i.reject("Invalid arguments");return o(a,n),a}};var N="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",D=/^~~local_forage_type~([^~]+)~/,j="__lfsc__:",O="arbf",q="blob",C="si08",L="ui08",P="uic8",M="si16",U="si32",F="ur16",B="ui32",G="fl32",H="fl64",V=Object.prototype.toString;function Y(t){var n,a,e,r,l,i=.75*t.length,o=t.length,s=0;"="===t[t.length-1]&&(i--,"="===t[t.length-2]&&i--);var u=new ArrayBuffer(i),c=new Uint8Array(u);for(n=0;n<o;n+=4)a=N.indexOf(t[n]),e=N.indexOf(t[n+1]),r=N.indexOf(t[n+2]),l=N.indexOf(t[n+3]),c[s++]=a<<2|e>>4,c[s++]=(15&e)<<4|r>>2,c[s++]=(3&r)<<6|63&l;return u}function X(t){var n,a=new Uint8Array(t),e="";for(n=0;n<a.length;n+=3)e+=N[a[n]>>2],e+=N[(3&a[n])<<4|a[n+1]>>4],e+=N[(15&a[n+1])<<2|a[n+2]>>6],e+=N[63&a[n+2]];return a.length%3==2?e=e.substring(0,e.length-1)+"=":a.length%3==1&&(e=e.substring(0,e.length-2)+"=="),e}var W={serialize:function(t,n){var a="";if(t&&(a=V.call(t)),t&&("[object ArrayBuffer]"===a||t.buffer&&"[object ArrayBuffer]"===V.call(t.buffer))){var e,r=j;t instanceof ArrayBuffer?(e=t,r+=O):(e=t.buffer,"[object Int8Array]"===a?r+=C:"[object Uint8Array]"===a?r+=L:"[object Uint8ClampedArray]"===a?r+=P:"[object Int16Array]"===a?r+=M:"[object Uint16Array]"===a?r+=F:"[object Int32Array]"===a?r+=U:"[object Uint32Array]"===a?r+=B:"[object Float32Array]"===a?r+=G:"[object Float64Array]"===a?r+=H:n(new Error("Failed to get type for BinaryArray"))),n(r+X(e))}else if("[object Blob]"===a){var l=new FileReader;l.onload=function(){var a="~~local_forage_type~"+t.type+"~"+X(this.result);n(j+q+a)},l.readAsArrayBuffer(t)}else try{n(JSON.stringify(t))}catch(a){console.error("Couldn't convert value into a JSON string: ",t),n(null,a)}},deserialize:function(t){if(t.substring(0,9)!==j)return JSON.parse(t);var n,a=t.substring(13),e=t.substring(9,13);if(e===q&&D.test(a)){var r=a.match(D);n=r[1],a=a.substring(r[0].length)}var i=Y(a);switch(e){case O:return i;case q:return l([i],{type:n});case C:return new Int8Array(i);case L:return new Uint8Array(i);case P:return new Uint8ClampedArray(i);case M:return new Int16Array(i);case F:return new Uint16Array(i);case U:return new Int32Array(i);case B:return new Uint32Array(i);case G:return new Float32Array(i);case H:return new Float64Array(i);default:throw new Error("Unkown type: "+e)}},stringToBuffer:Y,bufferToString:X};function K(t,n,a,e){t.executeSql("CREATE TABLE IF NOT EXISTS "+n.storeName+" (id INTEGER PRIMARY KEY, key unique, value)",[],a,e)}function Q(t,n,a,e,r,l){t.executeSql(a,e,r,(function(t,i){i.code===i.SYNTAX_ERR?t.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name = ?",[n.storeName],(function(t,o){o.rows.length?l(t,i):K(t,n,(function(){t.executeSql(a,e,r,l)}),l)}),l):l(t,i)}),l)}function $(t,n,a,e){var r=this;t=u(t);var l=new i((function(l,i){r.ready().then((function(){void 0===n&&(n=null);var o=n,s=r._dbInfo;s.serializer.serialize(n,(function(n,u){u?i(u):s.db.transaction((function(a){Q(a,s,"INSERT OR REPLACE INTO "+s.storeName+" (key, value) VALUES (?, ?)",[t,n],(function(){l(o)}),(function(t,n){i(n)}))}),(function(n){if(n.code===n.QUOTA_ERR){if(e>0)return void l($.apply(r,[t,o,a,e-1]));i(n)}}))}))})).catch(i)}));return o(l,a),l}var Z={_driver:"webSQLStorage",_initStorage:function(t){var n=this,a={db:null};if(t)for(var e in t)a[e]="string"!=typeof t[e]?t[e].toString():t[e];var r=new i((function(t,e){try{a.db=openDatabase(a.name,String(a.version),a.description,a.size)}catch(t){return e(t)}a.db.transaction((function(r){K(r,a,(function(){n._dbInfo=a,t()}),(function(t,n){e(n)}))}),e)}));return a.serializer=W,r},_support:"function"==typeof openDatabase,iterate:function(t,n){var a=this,e=new i((function(n,e){a.ready().then((function(){var r=a._dbInfo;r.db.transaction((function(a){Q(a,r,"SELECT * FROM "+r.storeName,[],(function(a,e){for(var l=e.rows,i=l.length,o=0;o<i;o++){var s=l.item(o),u=s.value;if(u&&(u=r.serializer.deserialize(u)),void 0!==(u=t(u,s.key,o+1)))return void n(u)}n()}),(function(t,n){e(n)}))}))})).catch(e)}));return o(e,n),e},getItem:function(t,n){var a=this;t=u(t);var e=new i((function(n,e){a.ready().then((function(){var r=a._dbInfo;r.db.transaction((function(a){Q(a,r,"SELECT * FROM "+r.storeName+" WHERE key = ? LIMIT 1",[t],(function(t,a){var e=a.rows.length?a.rows.item(0).value:null;e&&(e=r.serializer.deserialize(e)),n(e)}),(function(t,n){e(n)}))}))})).catch(e)}));return o(e,n),e},setItem:function(t,n,a){return $.apply(this,[t,n,a,1])},removeItem:function(t,n){var a=this;t=u(t);var e=new i((function(n,e){a.ready().then((function(){var r=a._dbInfo;r.db.transaction((function(a){Q(a,r,"DELETE FROM "+r.storeName+" WHERE key = ?",[t],(function(){n()}),(function(t,n){e(n)}))}))})).catch(e)}));return o(e,n),e},clear:function(t){var n=this,a=new i((function(t,a){n.ready().then((function(){var e=n._dbInfo;e.db.transaction((function(n){Q(n,e,"DELETE FROM "+e.storeName,[],(function(){t()}),(function(t,n){a(n)}))}))})).catch(a)}));return o(a,t),a},length:function(t){var n=this,a=new i((function(t,a){n.ready().then((function(){var e=n._dbInfo;e.db.transaction((function(n){Q(n,e,"SELECT COUNT(key) as c FROM "+e.storeName,[],(function(n,a){var e=a.rows.item(0).c;t(e)}),(function(t,n){a(n)}))}))})).catch(a)}));return o(a,t),a},key:function(t,n){var a=this,e=new i((function(n,e){a.ready().then((function(){var r=a._dbInfo;r.db.transaction((function(a){Q(a,r,"SELECT key FROM "+r.storeName+" WHERE id = ? LIMIT 1",[t+1],(function(t,a){var e=a.rows.length?a.rows.item(0).key:null;n(e)}),(function(t,n){e(n)}))}))})).catch(e)}));return o(e,n),e},keys:function(t){var n=this,a=new i((function(t,a){n.ready().then((function(){var e=n._dbInfo;e.db.transaction((function(n){Q(n,e,"SELECT key FROM "+e.storeName,[],(function(n,a){for(var e=[],r=0;r<a.rows.length;r++)e.push(a.rows.item(r).key);t(e)}),(function(t,n){a(n)}))}))})).catch(a)}));return o(a,t),a},dropInstance:function(t,n){n=c.apply(this,arguments);var a=this.config();(t="function"!=typeof t&&t||{}).name||(t.name=t.name||a.name,t.storeName=t.storeName||a.storeName);var e,r=this;return o(e=t.name?new i((function(n){var e;e=t.name===a.name?r._dbInfo.db:openDatabase(t.name,"","",0),t.storeName?n({db:e,storeNames:[t.storeName]}):n(function(t){return new i((function(n,a){t.transaction((function(e){e.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name <> '__WebKitDatabaseInfoTable__'",[],(function(a,e){for(var r=[],l=0;l<e.rows.length;l++)r.push(e.rows.item(l).name);n({db:t,storeNames:r})}),(function(t,n){a(n)}))}),(function(t){a(t)}))}))}(e))})).then((function(t){return new i((function(n,a){t.db.transaction((function(e){function r(t){return new i((function(n,a){e.executeSql("DROP TABLE IF EXISTS "+t,[],(function(){n()}),(function(t,n){a(n)}))}))}for(var l=[],o=0,s=t.storeNames.length;o<s;o++)l.push(r(t.storeNames[o]));i.all(l).then((function(){n()})).catch((function(t){a(t)}))}),(function(t){a(t)}))}))})):i.reject("Invalid arguments"),n),e}};function J(t,n){var a=t.name+"/";return t.storeName!==n.storeName&&(a+=t.storeName+"/"),a}function tt(){return!function(){var t="_localforage_support_test";try{return localStorage.setItem(t,!0),localStorage.removeItem(t),!1}catch(t){return!0}}()||localStorage.length>0}var nt={_driver:"localStorageWrapper",_initStorage:function(t){var n={};if(t)for(var a in t)n[a]=t[a];return n.keyPrefix=J(t,this._defaultConfig),tt()?(this._dbInfo=n,n.serializer=W,i.resolve()):i.reject()},_support:function(){try{return"undefined"!=typeof localStorage&&"setItem"in localStorage&&!!localStorage.setItem}catch(t){return!1}}(),iterate:function(t,n){var a=this,e=a.ready().then((function(){for(var n=a._dbInfo,e=n.keyPrefix,r=e.length,l=localStorage.length,i=1,o=0;o<l;o++){var s=localStorage.key(o);if(0===s.indexOf(e)){var u=localStorage.getItem(s);if(u&&(u=n.serializer.deserialize(u)),void 0!==(u=t(u,s.substring(r),i++)))return u}}}));return o(e,n),e},getItem:function(t,n){var a=this;t=u(t);var e=a.ready().then((function(){var n=a._dbInfo,e=localStorage.getItem(n.keyPrefix+t);return e&&(e=n.serializer.deserialize(e)),e}));return o(e,n),e},setItem:function(t,n,a){var e=this;t=u(t);var r=e.ready().then((function(){void 0===n&&(n=null);var a=n;return new i((function(r,l){var i=e._dbInfo;i.serializer.serialize(n,(function(n,e){if(e)l(e);else try{localStorage.setItem(i.keyPrefix+t,n),r(a)}catch(t){"QuotaExceededError"!==t.name&&"NS_ERROR_DOM_QUOTA_REACHED"!==t.name||l(t),l(t)}}))}))}));return o(r,a),r},removeItem:function(t,n){var a=this;t=u(t);var e=a.ready().then((function(){var n=a._dbInfo;localStorage.removeItem(n.keyPrefix+t)}));return o(e,n),e},clear:function(t){var n=this,a=n.ready().then((function(){for(var t=n._dbInfo.keyPrefix,a=localStorage.length-1;a>=0;a--){var e=localStorage.key(a);0===e.indexOf(t)&&localStorage.removeItem(e)}}));return o(a,t),a},length:function(t){var n=this.keys().then((function(t){return t.length}));return o(n,t),n},key:function(t,n){var a=this,e=a.ready().then((function(){var n,e=a._dbInfo;try{n=localStorage.key(t)}catch(t){n=null}return n&&(n=n.substring(e.keyPrefix.length)),n}));return o(e,n),e},keys:function(t){var n=this,a=n.ready().then((function(){for(var t=n._dbInfo,a=localStorage.length,e=[],r=0;r<a;r++){var l=localStorage.key(r);0===l.indexOf(t.keyPrefix)&&e.push(l.substring(t.keyPrefix.length))}return e}));return o(a,t),a},dropInstance:function(t,n){if(n=c.apply(this,arguments),!(t="function"!=typeof t&&t||{}).name){var a=this.config();t.name=t.name||a.name,t.storeName=t.storeName||a.storeName}var e,r=this;return e=t.name?new i((function(n){t.storeName?n(J(t,r._defaultConfig)):n(t.name+"/")})).then((function(t){for(var n=localStorage.length-1;n>=0;n--){var a=localStorage.key(n);0===a.indexOf(t)&&localStorage.removeItem(a)}})):i.reject("Invalid arguments"),o(e,n),e}},at=function(t,n){for(var a=t.length,e=0;e<a;){if((r=t[e])===(l=n)||"number"==typeof r&&"number"==typeof l&&isNaN(r)&&isNaN(l))return!0;e++}var r,l;return!1},et=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)},rt={},lt={},it={INDEXEDDB:R,WEBSQL:Z,LOCALSTORAGE:nt},ot=[it.INDEXEDDB._driver,it.WEBSQL._driver,it.LOCALSTORAGE._driver],st=["dropInstance"],ut=["clear","getItem","iterate","key","keys","length","removeItem","setItem"].concat(st),ct={description:"",driver:ot.slice(),name:"localforage",size:4980736,storeName:"keyvaluepairs",version:1};function dt(t,n){t[n]=function(){var a=arguments;return t.ready().then((function(){return t[n].apply(t,a)}))}}function mt(){for(var t=1;t<arguments.length;t++){var n=arguments[t];if(n)for(var a in n)n.hasOwnProperty(a)&&(et(n[a])?arguments[0][a]=n[a].slice():arguments[0][a]=n[a])}return arguments[0]}var gt=function(){function t(n){for(var a in function(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}(this,t),it)if(it.hasOwnProperty(a)){var e=it[a],r=e._driver;this[a]=r,rt[r]||this.defineDriver(e)}this._defaultConfig=mt({},ct),this._config=mt({},this._defaultConfig,n),this._driverSet=null,this._initDriver=null,this._ready=!1,this._dbInfo=null,this._wrapLibraryMethodsWithReady(),this.setDriver(this._config.driver).catch((function(){}))}return t.prototype.config=function(t){if("object"===(void 0===t?"undefined":e(t))){if(this._ready)return new Error("Can't call config() after localforage has been used.");for(var n in t){if("storeName"===n&&(t[n]=t[n].replace(/\W/g,"_")),"version"===n&&"number"!=typeof t[n])return new Error("Database version must be a number.");this._config[n]=t[n]}return!("driver"in t)||!t.driver||this.setDriver(this._config.driver)}return"string"==typeof t?this._config[t]:this._config},t.prototype.defineDriver=function(t,n,a){var e=new i((function(n,a){try{var e=t._driver,r=new Error("Custom driver not compliant; see https://mozilla.github.io/localForage/#definedriver");if(!t._driver)return void a(r);for(var l=ut.concat("_initStorage"),s=0,u=l.length;s<u;s++){var c=l[s];if((!at(st,c)||t[c])&&"function"!=typeof t[c])return void a(r)}!function(){for(var n=function(t){return function(){var n=new Error("Method "+t+" is not implemented by the current driver"),a=i.reject(n);return o(a,arguments[arguments.length-1]),a}},a=0,e=st.length;a<e;a++){var r=st[a];t[r]||(t[r]=n(r))}}();var d=function(a){rt[e]&&console.info("Redefining LocalForage driver: "+e),rt[e]=t,lt[e]=a,n()};"_support"in t?t._support&&"function"==typeof t._support?t._support().then(d,a):d(!!t._support):d(!0)}catch(t){a(t)}}));return s(e,n,a),e},t.prototype.driver=function(){return this._driver||null},t.prototype.getDriver=function(t,n,a){var e=rt[t]?i.resolve(rt[t]):i.reject(new Error("Driver not found."));return s(e,n,a),e},t.prototype.getSerializer=function(t){var n=i.resolve(W);return s(n,t),n},t.prototype.ready=function(t){var n=this,a=n._driverSet.then((function(){return null===n._ready&&(n._ready=n._initDriver()),n._ready}));return s(a,t,t),a},t.prototype.setDriver=function(t,n,a){var e=this;et(t)||(t=[t]);var r=this._getSupportedDrivers(t);function l(){e._config.driver=e.driver()}function o(t){return e._extend(t),l(),e._ready=e._initStorage(e._config),e._ready}var u=null!==this._driverSet?this._driverSet.catch((function(){return i.resolve()})):i.resolve();return this._driverSet=u.then((function(){var t=r[0];return e._dbInfo=null,e._ready=null,e.getDriver(t).then((function(t){e._driver=t._driver,l(),e._wrapLibraryMethodsWithReady(),e._initDriver=function(t){return function(){var n=0;return function a(){for(;n<t.length;){var r=t[n];return n++,e._dbInfo=null,e._ready=null,e.getDriver(r).then(o).catch(a)}l();var s=new Error("No available storage method found.");return e._driverSet=i.reject(s),e._driverSet}()}}(r)}))})).catch((function(){l();var t=new Error("No available storage method found.");return e._driverSet=i.reject(t),e._driverSet})),s(this._driverSet,n,a),this._driverSet},t.prototype.supports=function(t){return!!lt[t]},t.prototype._extend=function(t){mt(this,t)},t.prototype._getSupportedDrivers=function(t){for(var n=[],a=0,e=t.length;a<e;a++){var r=t[a];this.supports(r)&&n.push(r)}return n},t.prototype._wrapLibraryMethodsWithReady=function(){for(var t=0,n=ut.length;t<n;t++)dt(this,ut[t])},t.prototype.createInstance=function(n){return new t(n)},t}(),ft=new gt;n.exports=ft},{3:3}]},{},[4])(4)},3282:function(t){"use strict";function n(t){if("string"!=typeof t)throw new TypeError("Path must be a string. Received "+JSON.stringify(t))}function a(t,n){for(var a,e="",r=0,l=-1,i=0,o=0;o<=t.length;++o){if(o<t.length)a=t.charCodeAt(o);else{if(47===a)break;a=47}if(47===a){if(l===o-1||1===i);else if(l!==o-1&&2===i){if(e.length<2||2!==r||46!==e.charCodeAt(e.length-1)||46!==e.charCodeAt(e.length-2))if(e.length>2){var s=e.lastIndexOf("/");if(s!==e.length-1){-1===s?(e="",r=0):r=(e=e.slice(0,s)).length-1-e.lastIndexOf("/"),l=o,i=0;continue}}else if(2===e.length||1===e.length){e="",r=0,l=o,i=0;continue}n&&(e.length>0?e+="/..":e="..",r=2)}else e.length>0?e+="/"+t.slice(l+1,o):e=t.slice(l+1,o),r=o-l-1;l=o,i=0}else 46===a&&-1!==i?++i:i=-1}return e}var e={resolve:function(){for(var t,e="",r=!1,l=arguments.length-1;l>=-1&&!r;l--){var i;l>=0?i=arguments[l]:(void 0===t&&(t=process.cwd()),i=t),n(i),0!==i.length&&(e=i+"/"+e,r=47===i.charCodeAt(0))}return e=a(e,!r),r?e.length>0?"/"+e:"/":e.length>0?e:"."},normalize:function(t){if(n(t),0===t.length)return".";var e=47===t.charCodeAt(0),r=47===t.charCodeAt(t.length-1);return 0!==(t=a(t,!e)).length||e||(t="."),t.length>0&&r&&(t+="/"),e?"/"+t:t},isAbsolute:function(t){return n(t),t.length>0&&47===t.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var t,a=0;a<arguments.length;++a){var r=arguments[a];n(r),r.length>0&&(void 0===t?t=r:t+="/"+r)}return void 0===t?".":e.normalize(t)},relative:function(t,a){if(n(t),n(a),t===a)return"";if((t=e.resolve(t))===(a=e.resolve(a)))return"";for(var r=1;r<t.length&&47===t.charCodeAt(r);++r);for(var l=t.length,i=l-r,o=1;o<a.length&&47===a.charCodeAt(o);++o);for(var s=a.length-o,u=i<s?i:s,c=-1,d=0;d<=u;++d){if(d===u){if(s>u){if(47===a.charCodeAt(o+d))return a.slice(o+d+1);if(0===d)return a.slice(o+d)}else i>u&&(47===t.charCodeAt(r+d)?c=d:0===d&&(c=0));break}var m=t.charCodeAt(r+d);if(m!==a.charCodeAt(o+d))break;47===m&&(c=d)}var g="";for(d=r+c+1;d<=l;++d)d!==l&&47!==t.charCodeAt(d)||(0===g.length?g+="..":g+="/..");return g.length>0?g+a.slice(o+c):(o+=c,47===a.charCodeAt(o)&&++o,a.slice(o))},_makeLong:function(t){return t},dirname:function(t){if(n(t),0===t.length)return".";for(var a=t.charCodeAt(0),e=47===a,r=-1,l=!0,i=t.length-1;i>=1;--i)if(47===(a=t.charCodeAt(i))){if(!l){r=i;break}}else l=!1;return-1===r?e?"/":".":e&&1===r?"//":t.slice(0,r)},basename:function(t,a){if(void 0!==a&&"string"!=typeof a)throw new TypeError('"ext" argument must be a string');n(t);var e,r=0,l=-1,i=!0;if(void 0!==a&&a.length>0&&a.length<=t.length){if(a.length===t.length&&a===t)return"";var o=a.length-1,s=-1;for(e=t.length-1;e>=0;--e){var u=t.charCodeAt(e);if(47===u){if(!i){r=e+1;break}}else-1===s&&(i=!1,s=e+1),o>=0&&(u===a.charCodeAt(o)?-1==--o&&(l=e):(o=-1,l=s))}return r===l?l=s:-1===l&&(l=t.length),t.slice(r,l)}for(e=t.length-1;e>=0;--e)if(47===t.charCodeAt(e)){if(!i){r=e+1;break}}else-1===l&&(i=!1,l=e+1);return-1===l?"":t.slice(r,l)},extname:function(t){n(t);for(var a=-1,e=0,r=-1,l=!0,i=0,o=t.length-1;o>=0;--o){var s=t.charCodeAt(o);if(47!==s)-1===r&&(l=!1,r=o+1),46===s?-1===a?a=o:1!==i&&(i=1):-1!==a&&(i=-1);else if(!l){e=o+1;break}}return-1===a||-1===r||0===i||1===i&&a===r-1&&a===e+1?"":t.slice(a,r)},format:function(t){if(null===t||"object"!=typeof t)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof t);return function(t,n){var a=n.dir||n.root,e=n.base||(n.name||"")+(n.ext||"");return a?a===n.root?a+e:a+"/"+e:e}(0,t)},parse:function(t){n(t);var a={root:"",dir:"",base:"",ext:"",name:""};if(0===t.length)return a;var e,r=t.charCodeAt(0),l=47===r;l?(a.root="/",e=1):e=0;for(var i=-1,o=0,s=-1,u=!0,c=t.length-1,d=0;c>=e;--c)if(47!==(r=t.charCodeAt(c)))-1===s&&(u=!1,s=c+1),46===r?-1===i?i=c:1!==d&&(d=1):-1!==i&&(d=-1);else if(!u){o=c+1;break}return-1===i||-1===s||0===d||1===d&&i===s-1&&i===o+1?-1!==s&&(a.base=a.name=0===o&&l?t.slice(1,s):t.slice(o,s)):(0===o&&l?(a.name=t.slice(1,i),a.base=t.slice(1,s)):(a.name=t.slice(o,i),a.base=t.slice(o,s)),a.ext=t.slice(i,s)),o>0?a.dir=t.slice(0,o-1):l&&(a.dir="/"),a},sep:"/",delimiter:":",win32:null,posix:null};e.posix=e,t.exports=e},8571:function(t,n,a){var e;!function(r,l){"use strict";var i="function",o="undefined",s="object",u="string",c="major",d="model",m="name",g="type",f="vendor",p="version",b="architecture",h="console",v="mobile",y="tablet",k="smarttv",w="wearable",E="embedded",z="Amazon",x="Apple",T="ASUS",_="BlackBerry",I="Browser",A="Chrome",S="Firefox",R="Google",N="Huawei",D="LG",j="Microsoft",O="Motorola",q="Opera",C="Samsung",L="Sharp",P="Sony",M="Xiaomi",U="Zebra",F="Facebook",B="Chromium OS",G="Mac OS",H=function(t){for(var n={},a=0;a<t.length;a++)n[t[a].toUpperCase()]=t[a];return n},V=function(t,n){return typeof t===u&&-1!==Y(n).indexOf(Y(t))},Y=function(t){return t.toLowerCase()},X=function(t,n){if(typeof t===u)return t=t.replace(/^\s\s*/,""),typeof n===o?t:t.substring(0,500)},W=function(t,n){for(var a,e,r,o,u,c,d=0;d<n.length&&!u;){var m=n[d],g=n[d+1];for(a=e=0;a<m.length&&!u&&m[a];)if(u=m[a++].exec(t))for(r=0;r<g.length;r++)c=u[++e],typeof(o=g[r])===s&&o.length>0?2===o.length?typeof o[1]==i?this[o[0]]=o[1].call(this,c):this[o[0]]=o[1]:3===o.length?typeof o[1]!==i||o[1].exec&&o[1].test?this[o[0]]=c?c.replace(o[1],o[2]):l:this[o[0]]=c?o[1].call(this,c,o[2]):l:4===o.length&&(this[o[0]]=c?o[3].call(this,c.replace(o[1],o[2])):l):this[o]=c||l;d+=2}},K=function(t,n){for(var a in n)if(typeof n[a]===s&&n[a].length>0){for(var e=0;e<n[a].length;e++)if(V(n[a][e],t))return"?"===a?l:a}else if(V(n[a],t))return"?"===a?l:a;return t},Q={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},$={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[p,[m,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[p,[m,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[m,p],[/opios[\/ ]+([\w\.]+)/i],[p,[m,q+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[p,[m,q+" GX"]],[/\bopr\/([\w\.]+)/i],[p,[m,q]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[p,[m,"Baidu"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim)\s?(?:browser)?[\/ ]?([\w\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[m,p],[/\bddg\/([\w\.]+)/i],[p,[m,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[p,[m,"UC"+I]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[p,[m,"WeChat"]],[/konqueror\/([\w\.]+)/i],[p,[m,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[p,[m,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[p,[m,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[p,[m,"Smart Lenovo "+I]],[/(avast|avg)\/([\w\.]+)/i],[[m,/(.+)/,"$1 Secure "+I],p],[/\bfocus\/([\w\.]+)/i],[p,[m,S+" Focus"]],[/\bopt\/([\w\.]+)/i],[p,[m,q+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[p,[m,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[p,[m,"Dolphin"]],[/coast\/([\w\.]+)/i],[p,[m,q+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[p,[m,"MIUI "+I]],[/fxios\/([-\w\.]+)/i],[p,[m,S]],[/\bqihu|(qi?ho?o?|360)browser/i],[[m,"360 "+I]],[/(oculus|sailfish|huawei|vivo)browser\/([\w\.]+)/i],[[m,/(.+)/,"$1 "+I],p],[/samsungbrowser\/([\w\.]+)/i],[p,[m,C+" Internet"]],[/(comodo_dragon)\/([\w\.]+)/i],[[m,/_/g," "],p],[/metasr[\/ ]?([\d\.]+)/i],[p,[m,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[m,"Sogou Mobile"],p],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345Explorer)[\/ ]?([\w\.]+)/i],[m,p],[/(lbbrowser)/i,/\[(linkedin)app\]/i],[m],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[m,F],p],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[m,p],[/\bgsa\/([\w\.]+) .*safari\//i],[p,[m,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[p,[m,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[p,[m,A+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[m,A+" WebView"],p],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[p,[m,"Android "+I]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[m,p],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[p,[m,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[p,m],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[m,[p,K,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[m,p],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[m,"Netscape"],p],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[p,[m,S+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[m,p],[/(cobalt)\/([\w\.]+)/i],[m,[p,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[b,"amd64"]],[/(ia32(?=;))/i],[[b,Y]],[/((?:i[346]|x)86)[;\)]/i],[[b,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[b,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[b,"armhf"]],[/windows (ce|mobile); ppc;/i],[[b,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[b,/ower/,"",Y]],[/(sun4\w)[;\)]/i],[[b,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[b,Y]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[d,[f,C],[g,y]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[d,[f,C],[g,v]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[d,[f,x],[g,v]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[d,[f,x],[g,y]],[/(macintosh);/i],[d,[f,x]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[d,[f,L],[g,v]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[d,[f,N],[g,y]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[d,[f,N],[g,v]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[d,/_/g," "],[f,M],[g,v]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[d,/_/g," "],[f,M],[g,y]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[d,[f,"OPPO"],[g,v]],[/\b(opd2\d{3}a?) bui/i],[d,[f,"OPPO"],[g,y]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[d,[f,"Vivo"],[g,v]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[d,[f,"Realme"],[g,v]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[d,[f,O],[g,v]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[d,[f,O],[g,y]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[d,[f,D],[g,y]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[d,[f,D],[g,v]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[d,[f,"Lenovo"],[g,y]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[d,/_/g," "],[f,"Nokia"],[g,v]],[/(pixel c)\b/i],[d,[f,R],[g,y]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[d,[f,R],[g,v]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[d,[f,P],[g,v]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[d,"Xperia Tablet"],[f,P],[g,y]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[d,[f,"OnePlus"],[g,v]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[d,[f,z],[g,y]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[d,/(.+)/g,"Fire Phone $1"],[f,z],[g,v]],[/(playbook);[-\w\),; ]+(rim)/i],[d,f,[g,y]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[d,[f,_],[g,v]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[d,[f,T],[g,y]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[d,[f,T],[g,v]],[/(nexus 9)/i],[d,[f,"HTC"],[g,y]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[f,[d,/_/g," "],[g,v]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[d,[f,"Acer"],[g,y]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[d,[f,"Meizu"],[g,v]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[d,[f,"Ulefone"],[g,v]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[f,d,[g,v]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[f,d,[g,y]],[/(surface duo)/i],[d,[f,j],[g,y]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[d,[f,"Fairphone"],[g,v]],[/(u304aa)/i],[d,[f,"AT&T"],[g,v]],[/\bsie-(\w*)/i],[d,[f,"Siemens"],[g,v]],[/\b(rct\w+) b/i],[d,[f,"RCA"],[g,y]],[/\b(venue[\d ]{2,7}) b/i],[d,[f,"Dell"],[g,y]],[/\b(q(?:mv|ta)\w+) b/i],[d,[f,"Verizon"],[g,y]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[d,[f,"Barnes & Noble"],[g,y]],[/\b(tm\d{3}\w+) b/i],[d,[f,"NuVision"],[g,y]],[/\b(k88) b/i],[d,[f,"ZTE"],[g,y]],[/\b(nx\d{3}j) b/i],[d,[f,"ZTE"],[g,v]],[/\b(gen\d{3}) b.+49h/i],[d,[f,"Swiss"],[g,v]],[/\b(zur\d{3}) b/i],[d,[f,"Swiss"],[g,y]],[/\b((zeki)?tb.*\b) b/i],[d,[f,"Zeki"],[g,y]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[f,"Dragon Touch"],d,[g,y]],[/\b(ns-?\w{0,9}) b/i],[d,[f,"Insignia"],[g,y]],[/\b((nxa|next)-?\w{0,9}) b/i],[d,[f,"NextBook"],[g,y]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[f,"Voice"],d,[g,v]],[/\b(lvtel\-)?(v1[12]) b/i],[[f,"LvTel"],d,[g,v]],[/\b(ph-1) /i],[d,[f,"Essential"],[g,v]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[d,[f,"Envizen"],[g,y]],[/\b(trio[-\w\. ]+) b/i],[d,[f,"MachSpeed"],[g,y]],[/\btu_(1491) b/i],[d,[f,"Rotor"],[g,y]],[/(shield[\w ]+) b/i],[d,[f,"Nvidia"],[g,y]],[/(sprint) (\w+)/i],[f,d,[g,v]],[/(kin\.[onetw]{3})/i],[[d,/\./g," "],[f,j],[g,v]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[d,[f,U],[g,y]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[d,[f,U],[g,v]],[/smart-tv.+(samsung)/i],[f,[g,k]],[/hbbtv.+maple;(\d+)/i],[[d,/^/,"SmartTV"],[f,C],[g,k]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[f,D],[g,k]],[/(apple) ?tv/i],[f,[d,x+" TV"],[g,k]],[/crkey/i],[[d,A+"cast"],[f,R],[g,k]],[/droid.+aft(\w+)( bui|\))/i],[d,[f,z],[g,k]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[d,[f,L],[g,k]],[/(bravia[\w ]+)( bui|\))/i],[d,[f,P],[g,k]],[/(mitv-\w{5}) bui/i],[d,[f,M],[g,k]],[/Hbbtv.*(technisat) (.*);/i],[f,d,[g,k]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[f,X],[d,X],[g,k]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,k]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[f,d,[g,h]],[/droid.+; (shield) bui/i],[d,[f,"Nvidia"],[g,h]],[/(playstation [345portablevi]+)/i],[d,[f,P],[g,h]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[d,[f,j],[g,h]],[/((pebble))app/i],[f,d,[g,w]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[d,[f,x],[g,w]],[/droid.+; (glass) \d/i],[d,[f,R],[g,w]],[/droid.+; (wt63?0{2,3})\)/i],[d,[f,U],[g,w]],[/(quest( \d| pro)?)/i],[d,[f,F],[g,w]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[f,[g,E]],[/(aeobc)\b/i],[d,[f,z],[g,E]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[d,[g,v]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[d,[g,y]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,y]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,v]],[/(android[-\w\. ]{0,9});.+buil/i],[d,[f,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[p,[m,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[p,[m,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[m,p],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[p,m]],os:[[/microsoft (windows) (vista|xp)/i],[m,p],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[m,[p,K,Q]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[p,K,Q],[m,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[p,/_/g,"."],[m,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[m,G],[p,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[p,m],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[m,p],[/\(bb(10);/i],[p,[m,_]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[p,[m,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[p,[m,S+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[p,[m,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[p,[m,"watchOS"]],[/crkey\/([\d\.]+)/i],[p,[m,A+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[m,B],p],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[m,p],[/(sunos) ?([\w\.\d]*)/i],[[m,"Solaris"],p],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[m,p]]},Z=function(t,n){if(typeof t===s&&(n=t,t=l),!(this instanceof Z))return new Z(t,n).getResult();var a=typeof r!==o&&r.navigator?r.navigator:l,e=t||(a&&a.userAgent?a.userAgent:""),h=a&&a.userAgentData?a.userAgentData:l,k=n?function(t,n){var a={};for(var e in t)n[e]&&n[e].length%2==0?a[e]=n[e].concat(t[e]):a[e]=t[e];return a}($,n):$,w=a&&a.userAgent==e;return this.getBrowser=function(){var t,n={};return n[m]=l,n[p]=l,W.call(n,e,k.browser),n[c]=typeof(t=n[p])===u?t.replace(/[^\d\.]/g,"").split(".")[0]:l,w&&a&&a.brave&&typeof a.brave.isBrave==i&&(n[m]="Brave"),n},this.getCPU=function(){var t={};return t[b]=l,W.call(t,e,k.cpu),t},this.getDevice=function(){var t={};return t[f]=l,t[d]=l,t[g]=l,W.call(t,e,k.device),w&&!t[g]&&h&&h.mobile&&(t[g]=v),w&&"Macintosh"==t[d]&&a&&typeof a.standalone!==o&&a.maxTouchPoints&&a.maxTouchPoints>2&&(t[d]="iPad",t[g]=y),t},this.getEngine=function(){var t={};return t[m]=l,t[p]=l,W.call(t,e,k.engine),t},this.getOS=function(){var t={};return t[m]=l,t[p]=l,W.call(t,e,k.os),w&&!t[m]&&h&&h.platform&&"Unknown"!=h.platform&&(t[m]=h.platform.replace(/chrome os/i,B).replace(/macos/i,G)),t},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return e},this.setUA=function(t){return e=typeof t===u&&t.length>500?X(t,500):t,this},this.setUA(e),this};Z.VERSION="1.0.38",Z.BROWSER=H([m,p,c]),Z.CPU=H([b]),Z.DEVICE=H([d,f,g,h,v,k,y,w,E]),Z.ENGINE=Z.OS=H([m,p]),typeof n!==o?(t.exports&&(n=t.exports=Z),n.UAParser=Z):a.amdO?(e=function(){return Z}.call(n,a,n,t))===l||(t.exports=e):typeof r!==o&&(r.UAParser=Z);var J=typeof r!==o&&(r.jQuery||r.Zepto);if(J&&!J.ua){var tt=new Z;J.ua=tt.getResult(),J.ua.get=function(){return tt.getUA()},J.ua.set=function(t){tt.setUA(t);var n=tt.getResult();for(var a in n)J.ua[a]=n[a]}}}("object"==typeof window?window:this)}},n={};function a(e){var r=n[e];if(void 0!==r)return r.exports;var l=n[e]={exports:{}};return t[e].call(l.exports,l,l.exports,a),l.exports}a.amdO={},a.d=function(t,n){for(var e in n)a.o(n,e)&&!a.o(t,e)&&Object.defineProperty(t,e,{enumerable:!0,get:n[e]})},a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),a.o=function(t,n){return Object.prototype.hasOwnProperty.call(t,n)};var e={};return function(){"use strict";a.d(e,{default:function(){return Ie}});var t=new class{init(){this.OFFLINE_SCHEME="offline_indexeddb",this.OFFLINE_URL_REGEX=/^offline_indexeddb:\/\//i,this.OFFLINE_STATUS_CREATED="created",this.OFFLINE_STATUS_STARTED="started",this.OFFLINE_STATUS_STOPPED="stopped",this.OFFLINE_STATUS_FINISHED="finished",this.OFFLINE_STATUS_ERROR="error"}constructor(){this.init()}},n=a(9809),r=a(4441);const l=function(){let t,n=[];const a={},e={};function r(t,a){for(const e in n){const r=n[e];if(r.context===t&&r.name===a)return r.instance}return null}function l(t,n){return n[t]}function i(t,n,a){t in a&&(a[t]=n)}function o(n,a,e){let r;const l=n.__dashjs_factory_name,i=a[l];if(i){let l=i.instance;if(!i.override)return l.apply({context:a,factory:t},e);r=n.apply({context:a},e),l=l.apply({context:a,factory:t,parent:r},e);for(const t in l)r.hasOwnProperty(t)&&(r[t]=l[t])}else r=n.apply({context:a},e);return r.getClassName=function(){return l},r}return t={extend:function(t,n,a,e){!e[t]&&n&&(e[t]={instance:n,override:a})},getSingletonInstance:r,setSingletonInstance:function(t,a,e){for(const r in n){const l=n[r];if(l.context===t&&l.name===a)return void(n[r].instance=e)}n.push({name:a,context:t,instance:e})},deleteSingletonInstances:function(t){n=n.filter((n=>n.context!==t))},getSingletonFactory:function(t){let e=l(t.__dashjs_factory_name,a);return e||(e=function(a){let e;return void 0===a&&(a={}),{getInstance:function(){return e||(e=r(a,t.__dashjs_factory_name)),e||(e=o(t,a,arguments),n.push({name:t.__dashjs_factory_name,context:a,instance:e})),e}}},a[t.__dashjs_factory_name]=e),e},getSingletonFactoryByName:function(t){return l(t,a)},updateSingletonFactory:function(t,n){i(t,n,a)},getClassFactory:function(t){let n=l(t.__dashjs_factory_name,e);return n||(n=function(n){return void 0===n&&(n={}),{create:function(){return o(t,n,arguments)}}},e[t.__dashjs_factory_name]=n),n},getClassFactoryByName:function(t){return l(t,e)},updateClassFactory:function(t,n){i(t,n,e)}},t}();var i=l;function o(){let t,a,e;function l(t){return i().then((function(n){if(n){let a=null;for(let e=0;e<n.manifests.length;e++)n.manifests[e].manifestId===parseInt(t)&&(a=n.manifests[e]);return null!==a?(a.manifest=(0,r.decode)(a.manifest),Promise.resolve(a)):Promise.reject("Cannot found manifest with this manifestId : "+t)}return Promise.reject("Any manifests stored in DB !")})).catch((function(t){return Promise.reject(t)}))}function i(){return a.getItem("manifest").then((function(t){return Promise.resolve(t||{manifests:[]})})).catch((function(t){return Promise.reject(t)}))}function o(t){return i().then((function(n){try{for(let a=0;a<n.manifests.length;a++)n.manifests[a].manifestId===t.manifestId&&(n.manifests[a]=t);return a.setItem("manifest",n)}catch(t){throw new Error("Any results found !")}}))}return e={},"undefined"!=typeof window&&(n.config({driver:n.INDEXEDDB,name:"dash_offline_db"}),a=n.createInstance({driver:n.INDEXEDDB,name:"dash_offline_db",version:1,storeName:"manifest"})),t={dropAll:function(){return n.clear().then((function(){return Promise.resolve()})).catch((function(t){return Promise.reject(t)}))},getFragmentByKey:function(t,n){let a=e[t];return a?a.getItem(n).then((function(t){return Promise.resolve(t)})).catch((function(t){return Promise.reject(t)})):Promise.reject(new Error(`No fragment store found for manifest ${t}`))},getManifestById:l,storeFragment:function(t,n,a){let r=e[t];return r?r.setItem(n,a,(function(){return Promise.resolve()})).catch((function(t){return Promise.reject(t)})):Promise.reject(new Error(`No fragment store found for manifest ${t}`))},storeManifest:function(t){return a.getItem("manifest").then((function(n){let e=n||{manifests:[]};return e.manifests.push(t),a.setItem("manifest",e)}))},updateManifest:o,saveSelectedRepresentations:function(t,n){return l(t).then((function(t){return t.selected||(t.selected={}),t.selected=n,o(t).catch((function(){return Promise.reject("Cannot save selected representations")}))})).catch((function(t){return Promise.reject(t)}))},createFragmentStore:function(t){if(!e[t]){console.log("setStore  "+t);let a=n.createInstance({driver:n.INDEXEDDB,name:"dash_offline_db",version:1,storeName:t});e[t]=a}},setDownloadingStatus:function(t,n){return l(t).then((function(t){return t.status=n,o(t).catch((function(){return Promise.reject("Cannot set status "+n+" for this stream !")}))})).catch((function(t){return Promise.reject(t)}))},setRepresentationCurrentState:function(t,n,a){return l(t).then((function(t){return t.state||(t.state={}),t.state[n]||(t.state[n]={index:-1,downloaded:0}),t.state[n]=a,o(t).catch((function(){return Promise.reject("Cannot set current index for represenation id "+n)}))})).catch((function(t){return Promise.reject(t)}))},getRepresentationCurrentState:function(t,n){return l(t).then((function(t){let a={index:-1,downloaded:0};return t.state&&t.state[n]&&(a=t.state[n]),Promise.resolve(a)})).catch((function(t){return Promise.reject(t)}))},getCurrentHigherManifestId:function(){return i().then((function(t){let n=0;if(t){for(let a=0;a<t.manifests.length;a++)t.manifests[a].manifestId>n&&(n=t.manifests[a].manifestId);return Promise.resolve(n)}return Promise.resolve(n)})).catch((function(t){return Promise.reject(t)}))},getAllManifests:i,dropFragmentStore:function(t){n.dropInstance({driver:n.INDEXEDDB,name:"dash_offline_db",version:1,storeName:t}).then((function(){delete e[t]})).catch((function(t){console.log("dropFragmentStore failed "+t)}))},deleteDownloadById:function(t){return a.getItem("manifest").then((function(r){return r?(l=t,n.createInstance({name:"dash_offline_db",storeName:l}),n.dropInstance({name:"dash_offline_db",storeName:l}).then((function(){return delete e[l],Promise.resolve()})).catch((function(t){return console.log(t),Promise.reject(t)}))).then((function(){for(let n=0;n<r.manifests.length;n++)r.manifests[n].manifestId===parseInt(t)&&r.manifests.splice(n,1);return a.setItem("manifest",r).then((function(){return Promise.resolve("This stream has been successfull removed !")})).catch((function(){return Promise.reject("An error occured when trying to delete this manifest")}))})):Promise.resolve("Nothing to delete !");var l})).catch((function(t){return Promise.reject(t)}))}},t}o.__dashjs_factory_name="IndexDBStore";var s=i.getSingletonFactory(o),u=class{extend(t,n){if(!t)return;let a=!!n&&n.override,e=!!n&&n.publicOnly;for(const n in t)!t.hasOwnProperty(n)||this[n]&&!a||e&&-1===t[n].indexOf("public_")||(this[n]=t[n])}},c=new class extends u{constructor(){super(),this.OFFLINE_ERROR=11e3,this.INDEXEDDB_QUOTA_EXCEED_ERROR=11001,this.INDEXEDDB_INVALID_STATE_ERROR=11002,this.INDEXEDDB_NOT_READABLE_ERROR=11003,this.INDEXEDDB_NOT_FOUND_ERROR=11004,this.INDEXEDDB_NETWORK_ERROR=11005,this.INDEXEDDB_DATA_ERROR=11006,this.INDEXEDDB_TRANSACTION_INACTIVE_ERROR=11007,this.INDEXEDDB_NOT_ALLOWED_ERROR=11008,this.INDEXEDDB_NOT_SUPPORTED_ERROR=11009,this.INDEXEDDB_VERSION_ERROR=11010,this.INDEXEDDB_TIMEOUT_ERROR=11011,this.INDEXEDDB_ABORT_ERROR=11012,this.INDEXEDDB_UNKNOWN_ERROR=11013}};function d(t){t=t||{};const n=this.context,a=t.errHandler;let e,r;function l(t){let n;if(t){switch(t.name){case"QuotaExceededError":n=c.INDEXEDDB_QUOTA_EXCEED_ERROR;break;case"InvalidStateError":n=c.INDEXEDDB_INVALID_STATE_ERROR;break;case"NotFoundError":n=c.INDEXEDDB_NOT_FOUND_ERROR;break;case"VersionError":n=c.INDEXEDDB_VERSION_ERROR}a.error({code:n,message:t.name,data:t})}}return e={storeFragment:function(t,n,a){return r.storeFragment(t,n,a).catch((function(t){l(t)}))},createOfflineManifest:function(t){return r.storeManifest(t).catch((function(t){l(t)}))},updateOfflineManifest:function(t){return r.updateManifest(t).catch((function(t){l(t)}))},getManifestById:function(t){return r.getManifestById(t).catch((function(t){l(t)}))},saveSelectedRepresentations:function(t,n){return r.saveSelectedRepresentations(t,n).catch((function(t){l(t)}))},createFragmentStore:function(t,n){try{r.createFragmentStore(t,n)}catch(t){l(t)}},getCurrentHigherManifestId:function(){return r.getCurrentHigherManifestId().catch((function(t){l(t)}))},getAllManifests:function(){return r.getAllManifests().catch((function(t){l(t)}))},deleteDownloadById:function(t){return r.deleteDownloadById(t).catch((function(t){l(t)}))},setDownloadingStatus:function(t,n){return r.setDownloadingStatus(t,n).catch((function(t){l(t)}))},setRepresentationCurrentState:function(t,n,a){return r.setRepresentationCurrentState(t,n,a).catch((function(t){l(t)}))},getRepresentationCurrentState:function(t,n){return r.getRepresentationCurrentState(t,n).catch((function(t){l(t)}))}},r=s(n).getInstance(),e}d.__dashjs_factory_name="OfflineStoreController";var m=i.getClassFactory(d);class g{constructor(){this.tcpid=null,this.type=null,this.url=null,this.actualurl=null,this.range=null,this.trequest=null,this.tresponse=null,this.responsecode=null,this.interval=null,this.trace=[],this.cmsd=null,this._stream=null,this._tfinish=null,this._mediaduration=null,this._responseHeaders=null,this._serviceLocation=null,this._fileLoaderType=null,this._resourceTimingValues=null}}g.GET="GET",g.HEAD="HEAD",g.MPD_TYPE="MPD",g.XLINK_EXPANSION_TYPE="XLinkExpansion",g.INIT_SEGMENT_TYPE="InitializationSegment",g.INDEX_SEGMENT_TYPE="IndexSegment",g.MEDIA_SEGMENT_TYPE="MediaSegment",g.BITSTREAM_SWITCHING_SEGMENT_TYPE="BitstreamSwitchingSegment",g.MSS_FRAGMENT_INFO_SEGMENT_TYPE="FragmentInfoSegment",g.DVB_REPORTING_TYPE="DVBReporting",g.LICENSE="license",g.CONTENT_STEERING_TYPE="ContentSteering",g.OTHER_TYPE="other";class f{constructor(t){this.action=f.ACTION_DOWNLOAD,this.availabilityEndTime=null,this.availabilityStartTime=null,this.bandwidth=NaN,this.bytesLoaded=NaN,this.bytesTotal=NaN,this.delayLoadingTime=NaN,this.duration=NaN,this.endDate=null,this.firstByteDate=null,this.index=NaN,this.mediaStartTime=NaN,this.mediaType=null,this.range=null,this.representation=null,this.responseType="arraybuffer",this.retryAttempts=0,this.serviceLocation=null,this.startDate=null,this.startTime=NaN,this.timescale=NaN,this.type=null,this.url=t||null,this.wallStartTime=null}isInitializationRequest(){return this.type&&this.type===g.INIT_SEGMENT_TYPE}setInfo(t){this.type=t&&t.init?g.INIT_SEGMENT_TYPE:g.MEDIA_SEGMENT_TYPE,this.url=t&&t.url?t.url:null,this.range=t&&t.range?t.range.start+"-"+t.range.end:null,this.mediaType=t&&t.mediaType?t.mediaType:null,this.representation=t&&t.representation?t.representation:null}}f.ACTION_DOWNLOAD="download",f.ACTION_COMPLETE="complete";var p=f,b=class{extend(t,n){if(!t)return;let a=!!n&&n.override,e=!!n&&n.publicOnly;for(const n in t)!t.hasOwnProperty(n)||this[n]&&!a||e&&-1===t[n].indexOf("public_")||(this[n]=t[n])}},h=new class extends b{constructor(){super(),this.AST_IN_FUTURE="astInFuture",this.BASE_URLS_UPDATED="baseUrlsUpdated",this.BUFFER_EMPTY="bufferStalled",this.BUFFER_LOADED="bufferLoaded",this.BUFFER_LEVEL_STATE_CHANGED="bufferStateChanged",this.BUFFER_LEVEL_UPDATED="bufferLevelUpdated",this.DVB_FONT_DOWNLOAD_ADDED="dvbFontDownloadAdded",this.DVB_FONT_DOWNLOAD_COMPLETE="dvbFontDownloadComplete",this.DVB_FONT_DOWNLOAD_FAILED="dvbFontDownloadFailed",this.DYNAMIC_TO_STATIC="dynamicToStatic",this.ERROR="error",this.FRAGMENT_LOADING_COMPLETED="fragmentLoadingCompleted",this.FRAGMENT_LOADING_PROGRESS="fragmentLoadingProgress",this.FRAGMENT_LOADING_STARTED="fragmentLoadingStarted",this.FRAGMENT_LOADING_ABANDONED="fragmentLoadingAbandoned",this.LOG="log",this.MANIFEST_LOADING_STARTED="manifestLoadingStarted",this.MANIFEST_LOADING_FINISHED="manifestLoadingFinished",this.MANIFEST_LOADED="manifestLoaded",this.METRICS_CHANGED="metricsChanged",this.METRIC_CHANGED="metricChanged",this.METRIC_ADDED="metricAdded",this.METRIC_UPDATED="metricUpdated",this.PERIOD_SWITCH_STARTED="periodSwitchStarted",this.PERIOD_SWITCH_COMPLETED="periodSwitchCompleted",this.QUALITY_CHANGE_REQUESTED="qualityChangeRequested",this.QUALITY_CHANGE_RENDERED="qualityChangeRendered",this.NEW_TRACK_SELECTED="newTrackSelected",this.TRACK_CHANGE_RENDERED="trackChangeRendered",this.STREAM_INITIALIZING="streamInitializing",this.STREAM_UPDATED="streamUpdated",this.STREAM_ACTIVATED="streamActivated",this.STREAM_DEACTIVATED="streamDeactivated",this.STREAM_INITIALIZED="streamInitialized",this.STREAM_TEARDOWN_COMPLETE="streamTeardownComplete",this.TEXT_TRACKS_ADDED="allTextTracksAdded",this.TEXT_TRACK_ADDED="textTrackAdded",this.CUE_ENTER="cueEnter",this.CUE_EXIT="cueExit",this.THROUGHPUT_MEASUREMENT_STORED="throughputMeasurementStored",this.TTML_PARSED="ttmlParsed",this.TTML_TO_PARSE="ttmlToParse",this.CAPTION_RENDERED="captionRendered",this.CAPTION_CONTAINER_RESIZE="captionContainerResize",this.CAN_PLAY="canPlay",this.CAN_PLAY_THROUGH="canPlayThrough",this.PLAYBACK_ENDED="playbackEnded",this.PLAYBACK_ERROR="playbackError",this.PLAYBACK_INITIALIZED="playbackInitialized",this.PLAYBACK_NOT_ALLOWED="playbackNotAllowed",this.PLAYBACK_METADATA_LOADED="playbackMetaDataLoaded",this.PLAYBACK_LOADED_DATA="playbackLoadedData",this.PLAYBACK_PAUSED="playbackPaused",this.PLAYBACK_PLAYING="playbackPlaying",this.PLAYBACK_PROGRESS="playbackProgress",this.PLAYBACK_RATE_CHANGED="playbackRateChanged",this.PLAYBACK_SEEKED="playbackSeeked",this.PLAYBACK_SEEKING="playbackSeeking",this.PLAYBACK_STALLED="playbackStalled",this.PLAYBACK_STARTED="playbackStarted",this.PLAYBACK_TIME_UPDATED="playbackTimeUpdated",this.PLAYBACK_VOLUME_CHANGED="playbackVolumeChanged",this.PLAYBACK_WAITING="playbackWaiting",this.MANIFEST_VALIDITY_CHANGED="manifestValidityChanged",this.EVENT_MODE_ON_START="eventModeOnStart",this.EVENT_MODE_ON_RECEIVE="eventModeOnReceive",this.CONFORMANCE_VIOLATION="conformanceViolation",this.REPRESENTATION_SWITCH="representationSwitch",this.ADAPTATION_SET_REMOVED_NO_CAPABILITIES="adaptationSetRemovedNoCapabilities",this.CONTENT_STEERING_REQUEST_COMPLETED="contentSteeringRequestCompleted",this.INBAND_PRFT="inbandPrft",this.MANAGED_MEDIA_SOURCE_START_STREAMING="managedMediaSourceStartStreaming",this.MANAGED_MEDIA_SOURCE_END_STREAMING="managedMediaSourceEndStreaming"}},v={ACCESSIBILITY:"Accessibility",ADAPTATION_SET:"AdaptationSet",ADAPTATION_SETS:"adaptationSets",ADAPTATION_SET_SWITCHING_SCHEME_ID_URI:"urn:mpeg:dash:adaptation-set-switching:2016",ADD:"add",ASSET_IDENTIFIER:"AssetIdentifier",AUDIO_CHANNEL_CONFIGURATION:"AudioChannelConfiguration",AUDIO_SAMPLING_RATE:"audioSamplingRate",AVAILABILITY_END_TIME:"availabilityEndTime",AVAILABILITY_START_TIME:"availabilityStartTime",AVAILABILITY_TIME_COMPLETE:"availabilityTimeComplete",AVAILABILITY_TIME_OFFSET:"availabilityTimeOffset",BANDWITH:"bandwidth",BASE_URL:"BaseURL",BITSTREAM_SWITCHING:"BitstreamSwitching",BITSTREAM_SWITCHING_MINUS:"bitstreamSwitching",BYTE_RANGE:"byteRange",CAPTION:"caption",CENC_DEFAULT_KID:"cenc:default_KID",CLIENT_DATA_REPORTING:"ClientDataReporting",CLIENT_REQUIREMENT:"clientRequirement",CMCD_PARAMETERS:"CMCDParameters",CODECS:"codecs",CODEC_PRIVATE_DATA:"codecPrivateData",CODING_DEPENDENCY:"codingDependency",CONTENT_COMPONENT:"ContentComponent",CONTENT_PROTECTION:"ContentProtection",CONTENT_STEERING:"ContentSteering",CONTENT_STEERING_RESPONSE:{VERSION:"VERSION",TTL:"TTL",RELOAD_URI:"RELOAD-URI",PATHWAY_PRIORITY:"PATHWAY-PRIORITY",PATHWAY_CLONES:"PATHWAY-CLONES",BASE_ID:"BASE-ID",ID:"ID",URI_REPLACEMENT:"URI-REPLACEMENT",HOST:"HOST",PARAMS:"PARAMS"},CONTENT_TYPE:"contentType",DEFAULT_SERVICE_LOCATION:"defaultServiceLocation",DEPENDENCY_ID:"dependencyId",DURATION:"duration",DVB_PRIORITY:"dvb:priority",DVB_WEIGHT:"dvb:weight",DVB_URL:"dvb:url",DVB_MIMETYPE:"dvb:mimeType",DVB_FONTFAMILY:"dvb:fontFamily",DYNAMIC:"dynamic",END_NUMBER:"endNumber",ESSENTIAL_PROPERTY:"EssentialProperty",EVENT:"Event",EVENT_STREAM:"EventStream",FORCED_SUBTITLE:"forced-subtitle",FRAMERATE:"frameRate",FRAME_PACKING:"FramePacking",GROUP_LABEL:"GroupLabel",HEIGHT:"height",ID:"id",INBAND:"inband",INBAND_EVENT_STREAM:"InbandEventStream",INDEX:"index",INDEX_RANGE:"indexRange",INITIALIZATION:"Initialization",INITIALIZATION_MINUS:"initialization",LA_URL:"Laurl",LA_URL_LOWER_CASE:"laurl",LABEL:"Label",LANG:"lang",LOCATION:"Location",MAIN:"main",MAXIMUM_SAP_PERIOD:"maximumSAPPeriod",MAX_PLAYOUT_RATE:"maxPlayoutRate",MAX_SEGMENT_DURATION:"maxSegmentDuration",MAX_SUBSEGMENT_DURATION:"maxSubsegmentDuration",MEDIA:"media",MEDIA_PRESENTATION_DURATION:"mediaPresentationDuration",MEDIA_RANGE:"mediaRange",MEDIA_STREAM_STRUCTURE_ID:"mediaStreamStructureId",METRICS:"Metrics",METRICS_MINUS:"metrics",MIME_TYPE:"mimeType",MINIMUM_UPDATE_PERIOD:"minimumUpdatePeriod",MIN_BUFFER_TIME:"minBufferTime",MP4_PROTECTION_SCHEME:"urn:mpeg:dash:mp4protection:2011",MPD:"MPD",MPD_TYPE:"mpd",MPD_PATCH_TYPE:"mpdpatch",ORIGINAL_MPD_ID:"mpdId",ORIGINAL_PUBLISH_TIME:"originalPublishTime",PATCH_LOCATION:"PatchLocation",PERIOD:"Period",PRESENTATION_TIME:"presentationTime",PRESENTATION_TIME_OFFSET:"presentationTimeOffset",PRO:"pro",PRODUCER_REFERENCE_TIME:"ProducerReferenceTime",PRODUCER_REFERENCE_TIME_TYPE:{ENCODER:"encoder",CAPTURED:"captured",APPLICATION:"application"},PROFILES:"profiles",PSSH:"pssh",PUBLISH_TIME:"publishTime",QUALITY_RANKING:"qualityRanking",QUERY_BEFORE_START:"queryBeforeStart",QUERY_PART:"$querypart$",RANGE:"range",RATING:"Rating",REF:"ref",REF_ID:"refId",REMOVE:"remove",REPLACE:"replace",REPORTING:"Reporting",REPRESENTATION:"Representation",REPRESENTATION_INDEX:"RepresentationIndex",ROBUSTNESS:"robustness",ROLE:"Role",S:"S",SAR:"sar",SCAN_TYPE:"scanType",SEGMENT_ALIGNMENT:"segmentAlignment",SEGMENT_BASE:"SegmentBase",SEGMENT_LIST:"SegmentList",SEGMENT_PROFILES:"segmentProfiles",SEGMENT_TEMPLATE:"SegmentTemplate",SEGMENT_TIMELINE:"SegmentTimeline",SEGMENT_TYPE:"segment",SEGMENT_URL:"SegmentURL",SERVICE_DESCRIPTION:"ServiceDescription",SERVICE_DESCRIPTION_LATENCY:"Latency",SERVICE_DESCRIPTION_OPERATING_BANDWIDTH:"OperatingBandwidth",SERVICE_DESCRIPTION_OPERATING_QUALITY:"OperatingQuality",SERVICE_DESCRIPTION_PLAYBACK_RATE:"PlaybackRate",SERVICE_DESCRIPTION_SCOPE:"Scope",SERVICE_LOCATION:"serviceLocation",SERVICE_LOCATIONS:"serviceLocations",SOURCE_URL:"sourceURL",START:"start",START_NUMBER:"startNumber",START_WITH_SAP:"startWithSAP",STATIC:"static",STEERING_TYPE:"steering",SUBSET:"Subset",SUBTITLE:"subtitle",SUB_REPRESENTATION:"SubRepresentation",SUB_SEGMENT_ALIGNMENT:"subsegmentAlignment",SUGGESTED_PRESENTATION_DELAY:"suggestedPresentationDelay",SUPPLEMENTAL_PROPERTY:"SupplementalProperty",SUPPLEMENTAL_CODECS:"scte214:supplementalCodecs",TIMESCALE:"timescale",TIMESHIFT_BUFFER_DEPTH:"timeShiftBufferDepth",TTL:"ttl",TYPE:"type",UTC_TIMING:"UTCTiming",VALUE:"value",VIEWPOINT:"Viewpoint",WALL_CLOCK_TIME:"wallClockTime",WIDTH:"width"};const y=/\$(RepresentationID|Number|SubNumber|Bandwidth|Time)?(?:%0([0-9]+)([diouxX]))?\$/g;var k=class{constructor(){this.availabilityEndTime=NaN,this.availabilityStartTime=NaN,this.duration=NaN,this.index=null,this.indexRange=null,this.media=null,this.mediaRange=null,this.mediaStartTime=NaN,this.presentationStartTime=NaN,this.replacementNumber=NaN,this.replacementTime=null,this.representation=null,this.wallStartTime=NaN}};function w(t,n,a,e,r,l,i,o){let s=new k;var u;return s.representation=t,s.duration=n,s.presentationStartTime=a,s.mediaStartTime=e,s.availabilityStartTime=r.calcAvailabilityStartTimeFromPresentationTime(l,t,i),s.availabilityEndTime=r.calcAvailabilityEndTimeFromPresentationTime(l+n,t,i),s.wallStartTime=r.calcWallTimeForSegment(s,i),s.replacementNumber=(u=o,s.representation.startNumber+u),s.index=o,s}function E(t,n,a,e){const r=n.adaptation.period;if(isFinite(r.duration)&&r.start+r.duration<=a.presentationStartTime)return!1;if(e){if("INF"===n.availabilityTimeOffset)return!0;const e=t.getClientReferenceTime();return a.availabilityStartTime.getTime()<=e&&(!isFinite(a.availabilityEndTime)||a.availabilityEndTime.getTime()>=e)}return!0}function z(t,n,a,e,r,l){return t?function(t,n,a,e,r,l){return t.replace(y,((t,i,o,s)=>{let u,c;switch(i){case void 0:return"$";case"RepresentationID":u=n;break;case"Number":u=a;break;case"SubNumber":u=e;break;case"Bandwidth":u=r;break;case"Time":if("string"==typeof l)return l;u=l?Math.round(l):l;break;default:u=null}if(null==u)return t;switch(s){case void 0:case"d":case"i":case"u":c=u.toString();break;case"o":c=u.toString(8);break;case"x":c=u.toString(16);break;case"X":c=u.toString(16).toUpperCase();break;default:c=u.toString()}const d=parseInt(o,10)||1;return c.padStart(d,"0")}))}(t,n,a,e,r,l):t}function x(t,n,a,e){let r,l,i;r=a.segmentDuration,isNaN(r)&&(r=a.adaptation.period.duration),l=parseFloat((a.adaptation.period.start+e*r).toFixed(5)),i=parseFloat((l+r).toFixed(5));const o=w(a,r,l,t.calcMediaTimeFromPresentationTime(l,a),t,i,n,e);return E(t,a,o,n)?o:null}function T(t,n,a,e,r,l,i,o,s,u){const c=e/l,d=r/l;let m,g,f;return m=t.calcPresentationTimeFromMediaTime(c,a),g=m+d,f=w(a,d,m,c,t,g,n,s),E(t,a,f,n)?(f.replacementTime=u||e,f.media=z(i,void 0,f.replacementNumber,void 0,void 0,f.replacementTime),f.mediaRange=o,f):null}function _(t){const n=(t=t||{}).eventBus,a=t.debug,e=t.urlUtils,r=t.type,l=t.streamInfo,i=t.segmentsController,o=t.timelineConverter,s=t.baseURLController;let u,c,d,m,f;function b(){return r}function y(){d=null}function k(t,n,a){const r=s.resolve(a.path);let l,i,o={};return r&&n!==r.url&&e.isRelative(n)?(l=r.url,i=r.serviceLocation,o=r.queryParams,n&&(l=e.resolve(n,l))):l=n,!e.isRelative(l)&&(t.url=l,t.serviceLocation=i,t.queryParams=o,!0)}function w(t,n){if(null==n)return null;const a=new p,e=n.representation,r=e.bandwidth,l=z(n.media,e.id,n.replacementNumber,void 0,r,n.replacementTime);return a.mediaType=b(),a.bandwidth=e.bandwidth,a.type=g.MEDIA_SEGMENT_TYPE,a.range=n.mediaRange,a.startTime=n.presentationStartTime,a.mediaStartTime=n.mediaStartTime,a.duration=n.duration,a.timescale=e.timescale,a.availabilityStartTime=n.availabilityStartTime,a.availabilityEndTime=n.availabilityEndTime,a.availabilityTimeComplete=e.availabilityTimeComplete,a.wallStartTime=n.wallStartTime,a.index=n.index,a.adaptationIndex=e.adaptation.index,a.representation=e,k(a,l,e)?a:void 0}function E(t,n,a){let e=null;if(!n||!n.segmentInfoType)return e;const r=i.getSegmentByTime(n,a);return r&&(d=r,c.debug("Index for time "+a+" is "+r.index),e=w(0,r)),e}function x(t,n,a){let e=null;const r=i.getSegmentByIndex(n,a,d?d.mediaStartTime:-1);if(r)e=w(0,r),d=r;else{if(m&&!f)return c.debug(b()+" No segment found at index: "+a+". Wait for next loop"),null;f=!0}return e}function T(){c.debug("Dynamic stream complete"),f=!0}return u={getCurrentIndex:function(){return d?d.index:-1},getInitRequest:function(t,n){return n?function(t,n,a){const e=new p,r=n.adaptation.period,l=r.start;if(e.mediaType=a,e.type=g.INIT_SEGMENT_TYPE,e.range=n.range,e.availabilityStartTime=o.calcAvailabilityStartTimeFromPresentationTime(l,n,m),e.availabilityEndTime=o.calcAvailabilityEndTimeFromPresentationTime(l+r.duration,n,m),e.representation=n,k(e,n.initialization,n))return e.url=z(e.url,void 0,void 0,void 0,n.bandwidth),e}(0,n,b()):null},getNextSegmentRequest:function(t,n){if(!n||!n.segmentInfoType)return null;let a=d?d.index+1:0;return n&&d&&n.endNumber&&d.replacementNumber&&d.replacementNumber>=n.endNumber?(f=!0,null):x(0,n,a)},getNextSegmentRequestIdempotent:function(t,n){let a=null,e=d?d.index+1:0;const r=i.getSegmentByIndex(n,e,d?d.mediaStartTime:-1);return r?(a=w(0,r),a):null},getSegmentRequestForTime:E,getStreamId:function(){return l.id},getStreamInfo:function(){return l},getType:b,getValidTimeAheadOfTargetTime:function(t,n,a,e){try{if(isNaN(t)||!n||!a)return NaN;if(t<0&&(t=0),isNaN(e)&&(e=.5),E(0,a,t))return t;if(a.adaptation.period.start+a.adaptation.period.duration<t)return NaN;const r=isFinite(a.adaptation.period.duration)?a.adaptation.period.start+a.adaptation.period.duration:t+30;let l=Math.min(t+e,r),i=NaN,o=null;for(;l<=r;){let t=null;if(l<=r&&(t=E(0,a,l)),t){i=l,o=t;break}l+=e}if(o){const n=o.startTime+o.duration;return t>o.startTime&&n-t>e?t:!isNaN(o.startTime)&&t<o.startTime&&i>o.startTime?o.startTime+.001:Math.min(n-e,i)}return i}catch(t){return NaN}},initialize:function(t){m=t,f=!1,i.initialize(t)},isLastSegmentRequested:function(t,n){if(!t||!d)return!1;if(f)return!0;if(!isFinite(t.adaptation.period.duration))return!1;if(d.presentationStartTime+d.duration>n)return!1;if(t.mediaFinishedInformation&&!isNaN(t.mediaFinishedInformation.numberOfSegments)&&!isNaN(d.index)&&d.index>=t.mediaFinishedInformation.numberOfSegments-1){if(!m||t.segmentInfoType===v.SEGMENT_TEMPLATE)return!0;if(m&&t.segmentInfoType===v.SEGMENT_LIST&&t.adaptation.period.nextPeriodId)return!0}return!!(m&&t.adaptation.period.nextPeriodId&&t.segmentInfoType===v.SEGMENT_TIMELINE&&t.mediaFinishedInformation&&!isNaN(t.mediaFinishedInformation.mediaTimeOfLastSignaledSegment)&&d&&!isNaN(d.mediaStartTime)&&!isNaN(d.duration)&&d.mediaStartTime+d.duration>=t.mediaFinishedInformation.mediaTimeOfLastSignaledSegment-.05)},repeatSegmentRequest:function(t,n){return n&&n.segmentInfoType?x(0,n,d?d.index:0):null},reset:function(){y(),n.off(h.DYNAMIC_TO_STATIC,T,u)}},c=a.getLogger(u),y(),n.on(h.DYNAMIC_TO_STATIC,T,u),u}_.__dashjs_factory_name="DashHandler";var I=i.getClassFactory(_),A={STREAM:"stream",VIDEO:"video",AUDIO:"audio",TEXT:"text",MUXED:"muxed",IMAGE:"image",STPP:"stpp",TTML:"ttml",VTT:"vtt",WVTT:"wvtt",CONTENT_STEERING:"contentSteering",LIVE_CATCHUP_MODE_DEFAULT:"liveCatchupModeDefault",LIVE_CATCHUP_MODE_LOLP:"liveCatchupModeLoLP",MOVING_AVERAGE_SLIDING_WINDOW:"slidingWindow",MOVING_AVERAGE_EWMA:"ewma",BAD_ARGUMENT_ERROR:"Invalid Arguments",MISSING_CONFIG_ERROR:"Missing config parameter(s)",TRACK_SWITCH_MODE_ALWAYS_REPLACE:"alwaysReplace",TRACK_SWITCH_MODE_NEVER_REPLACE:"neverReplace",TRACK_SELECTION_MODE_FIRST_TRACK:"firstTrack",TRACK_SELECTION_MODE_HIGHEST_BITRATE:"highestBitrate",TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY:"highestEfficiency",TRACK_SELECTION_MODE_WIDEST_RANGE:"widestRange",CMCD_QUERY_KEY:"CMCD",CMCD_MODE_QUERY:"query",CMCD_MODE_HEADER:"header",CMCD_AVAILABLE_KEYS:["br","d","ot","tb","bl","dl","mtp","nor","nrr","su","bs","rtp","cid","pr","sf","sid","st","v"],CMCD_V2_AVAILABLE_KEYS:["msd","ltc"],CMCD_AVAILABLE_REQUESTS:["segment","mpd","xlink","steering","other"],INITIALIZE:"initialize",TEXT_SHOWING:"showing",TEXT_HIDDEN:"hidden",TEXT_DISABLED:"disabled",ACCESSIBILITY_CEA608_SCHEME:"urn:scte:dash:cc:cea-608:2015",CC1:"CC1",CC3:"CC3",UTF8:"utf-8",SCHEME_ID_URI:"schemeIdUri",START_TIME:"starttime",SERVICE_DESCRIPTION_DVB_LL_SCHEME:"urn:dvb:dash:lowlatency:scope:2019",SUPPLEMENTAL_PROPERTY_DVB_LL_SCHEME:"urn:dvb:dash:lowlatency:critical:2019",CTA_5004_2023_SCHEME:"urn:mpeg:dash:cta-5004:2023",THUMBNAILS_SCHEME_ID_URIS:["http://dashif.org/thumbnail_tile","http://dashif.org/guidelines/thumbnail_tile"],FONT_DOWNLOAD_DVB_SCHEME:"urn:dvb:dash:fontdownload:2014",COLOUR_PRIMARIES_SCHEME_ID_URI:"urn:mpeg:mpegB:cicp:ColourPrimaries",URL_QUERY_INFO_SCHEME:"urn:mpeg:dash:urlparam:2014",EXT_URL_QUERY_INFO_SCHEME:"urn:mpeg:dash:urlparam:2016",MATRIX_COEFFICIENTS_SCHEME_ID_URI:"urn:mpeg:mpegB:cicp:MatrixCoefficients",TRANSFER_CHARACTERISTICS_SCHEME_ID_URI:"urn:mpeg:mpegB:cicp:TransferCharacteristics",HDR_METADATA_FORMAT_SCHEME_ID_URI:"urn:dvb:dash:hdr-dmi",HDR_METADATA_FORMAT_VALUES:{ST2094_10:"ST2094-10",SL_HDR2:"SL-HDR2",ST2094_40:"ST2094-40"},MEDIA_CAPABILITIES_API:{COLORGAMUT:{SRGB:"srgb",P3:"p3",REC2020:"rec2020"},TRANSFERFUNCTION:{SRGB:"srgb",PQ:"pq",HLG:"hlg"},HDR_METADATATYPE:{SMPTE_ST_2094_10:"smpteSt2094-10",SLHDR2:"slhdr2",SMPTE_ST_2094_40:"smpteSt2094-40"}},XML:"XML",ARRAY_BUFFER:"ArrayBuffer",DVB_REPORTING_URL:"dvb:reportingUrl",DVB_PROBABILITY:"dvb:probability",OFF_MIMETYPE:"application/font-sfnt",WOFF_MIMETYPE:"application/font-woff",VIDEO_ELEMENT_READY_STATES:{HAVE_NOTHING:0,HAVE_METADATA:1,HAVE_CURRENT_DATA:2,HAVE_FUTURE_DATA:3,HAVE_ENOUGH_DATA:4},FILE_LOADER_TYPES:{FETCH:"fetch_loader",XHR:"xhr_loader"},THROUGHPUT_TYPES:{LATENCY:"throughput_type_latency",BANDWIDTH:"throughput_type_bandwidth"},THROUGHPUT_CALCULATION_MODES:{EWMA:"throughputCalculationModeEwma",ZLEMA:"throughputCalculationModeZlema",ARITHMETIC_MEAN:"throughputCalculationModeArithmeticMean",BYTE_SIZE_WEIGHTED_ARITHMETIC_MEAN:"throughputCalculationModeByteSizeWeightedArithmeticMean",DATE_WEIGHTED_ARITHMETIC_MEAN:"throughputCalculationModeDateWeightedArithmeticMean",HARMONIC_MEAN:"throughputCalculationModeHarmonicMean",BYTE_SIZE_WEIGHTED_HARMONIC_MEAN:"throughputCalculationModeByteSizeWeightedHarmonicMean",DATE_WEIGHTED_HARMONIC_MEAN:"throughputCalculationModeDateWeightedHarmonicMean"},LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE:{MOOF_PARSING:"lowLatencyDownloadTimeCalculationModeMoofParsing",DOWNLOADED_DATA:"lowLatencyDownloadTimeCalculationModeDownloadedData",AAST:"lowLatencyDownloadTimeCalculationModeAast"},RULES_TYPES:{QUALITY_SWITCH_RULES:"qualitySwitchRules",ABANDON_FRAGMENT_RULES:"abandonFragmentRules"},QUALITY_SWITCH_RULES:{BOLA_RULE:"BolaRule",THROUGHPUT_RULE:"ThroughputRule",INSUFFICIENT_BUFFER_RULE:"InsufficientBufferRule",SWITCH_HISTORY_RULE:"SwitchHistoryRule",DROPPED_FRAMES_RULE:"DroppedFramesRule",LEARN_TO_ADAPT_RULE:"L2ARule",LOL_PLUS_RULE:"LoLPRule"},ABANDON_FRAGMENT_RULES:{ABANDON_REQUEST_RULE:"AbandonRequestsRule"},ID3_SCHEME_ID_URI:"https://aomedia.org/emsg/ID3",COMMON_ACCESS_TOKEN_HEADER:"common-access-token",DASH_ROLE_SCHEME_ID:"urn:mpeg:dash:role:2011",CODEC_FAMILIES:{MP3:"mp3",AAC:"aac",AC3:"ac3",EC3:"ec3",DTSX:"dtsx",DTSC:"dtsc",AVC:"avc",HEVC:"hevc"}};function S(t){const n=(t=t||{}).eventBus,a=t.events,e=t.abrController,r=t.dashMetrics,l=t.playbackController,i=t.timelineConverter,o=t.type,s=t.streamInfo,u=t.segmentsController,c=t.isDynamic;let d,m,g;function f(){return o}function p(){return g}function b(){g=null,m=[]}function v(t){return new Promise(((n,a)=>{const e=t.hasInitialization(),l=t.hasSegments(),o=[];o.push(u.updateInitData(t,e)),o.push(u.updateSegmentData(t,l)),Promise.all(o).then((a=>{var e;a[0]&&!a[0].error&&(t=function(t,n){return n&&!n.error&&n.representation?n.representation:t}(t,a[0])),a[1]&&!a[1].error&&(t=function(t,n){if(!n||n.error)return;const a=n.segments,e=[];let r,l,o,s,u=0;for(r=0,l=a?a.length:0;r<l;r++)o=a[r],s=T(i,c,t,o.startTime,o.duration,o.timescale,o.media,o.mediaRange,u),s&&(e.push(s),s=null,u++);return e.length>0&&(t.segments=e),t}(t,a[1])),t.fragmentDuration=t.segmentDuration?t.segmentDuration:t.segments&&t.segments.length>0?t.segments[0].duration:NaN,(e=t).mediaFinishedInformation=u.getMediaFinishedInformation(e),function(t){let n,a=r.getCurrentManifestUpdate(),e=!1;if(a){for(let r=0;r<a.representationInfo.length;r++)if(n=a.representationInfo[r],n.index===t.index&&n.mediaType===f()){e=!0;break}e||r.addManifestUpdateRepresentationInfo(t,f())}}(t),n()})).catch((t=>{a(t)}))}))}function y(t){if(!m||0===m.length)return null;const n=m.filter((n=>n.id===t));return n.length>0?n[0]:null}function k(t){n.trigger(a.DATA_UPDATE_COMPLETED,{currentRepresentation:g,error:t},{streamId:s.id,mediaType:o})}function w(t){g&&g.id===t.id||function(t){!function(){if(!(e&&r&&l&&i))throw new Error(A.MISSING_CONFIG_ERROR)}();const a=new Date,u=1e3*l.getTime();t&&r.addRepresentationSwitch(t.adaptation.type,a,u,t.id),n.trigger(h.REPRESENTATION_SWITCH,{mediaType:o,streamId:s.id,currentRepresentation:t},{streamId:s.id,mediaType:o})}(t),g=t}function E(t){if(t.newDuration){const n=p();n&&n.adaptation.period&&(n.adaptation.period.duration=t.newDuration)}}return d={getCurrentRepresentation:p,getRepresentationById:y,getStreamId:function(){return s.id},getType:f,prepareQualityChange:function(t){const n=m.filter((n=>n.id===t.id));n.length>0&&w(n[0])},reset:function(){n.off(h.MANIFEST_VALIDITY_CHANGED,E,d),b()},updateData:function(t,n,a){return new Promise(((i,u)=>{if(m=t,w(y(a)),o!==A.VIDEO&&o!==A.AUDIO&&(o!==A.TEXT||!n))return k(),void i();const c=[];for(let t=0,n=m.length;t<n;t++){const n=m[t];c.push(v(n))}Promise.all(c).then((()=>{!function(){e.setPlaybackQuality(o,s,g);const t=r.getCurrentDVRInfo(o);t&&r.updateManifestUpdateInfo({latency:t.range.end-l.getTime()}),k()}(),i()})).catch((t=>{u(t)}))}))}},b(),n.on(h.MANIFEST_VALIDITY_CHANGED,E,d),d}S.__dashjs_factory_name="RepresentationController";var R=i.getClassFactory(S);const N="loading",D="executed",j="failed";function O(t){const n=(t=t||{}).eventBus,a=t.events,e=t.dashMetrics,r=t.fragmentLoader,l=t.debug,i=t.streamInfo,o=t.type;let s,u,c,d;function m(t){return!!t&&function(n){let a=!1;return n.some((n=>{if(e=t,r=n,!isNaN(e.index)&&e.startTime===r.startTime&&e.adaptationIndex===r.adaptationIndex&&e.type===r.type||function(t,n){return t.representation.id===n.representation.id}(t,n)||function(t,n){return t.action===p.ACTION_COMPLETE&&t.action===n.action}(t,n))return a=!0,a;var e,r})),a}(c)}function g(t){return isNaN(t.duration)?.25:Math.min(t.duration/8,.5)}function f(t){c=c.filter((n=>{const a=g(n);return isNaN(n.startTime)||void 0!==t&&n.startTime>=t-a}))}function b(t,n){n<=t+.5||(c=c.filter((a=>{const e=g(a);return isNaN(a.startTime)||a.startTime>=n-e||isNaN(a.duration)||a.startTime+a.duration<=t+e})))}function h(t,n,a){for(let e=t.length-1;e>=0;e--){const r=t[e],l=r.startTime,i=l+r.duration;if(a=isNaN(a)?g(r):a,!isNaN(l)&&!isNaN(i)&&n+a>=l&&n-a<i||isNaN(l)&&isNaN(n))return r}return null}function v(t,n){e.addSchedulingInfo(t,n),e.addRequestsQueue(t.mediaType,d,c)}function y(t){t.sender===r&&(d.splice(d.indexOf(t.request),1),t.response&&!t.error&&c.push(t.request),v(t.request,t.error?j:D),n.trigger(a.FRAGMENT_LOADING_COMPLETED,{request:t.request,response:t.response,error:t.error,sender:this},{streamId:i.id,mediaType:o}))}function k(t){t.sender===r&&n.trigger(a.FRAGMENT_LOADING_PROGRESS,{request:t.request,response:t.response,error:t.error,sender:this},{streamId:i.id,mediaType:o})}function w(t){t.sender===r&&n.trigger(a.FRAGMENT_LOADING_ABANDONED,{request:t.request},{streamId:i.id,mediaType:o})}function E(){c=[],d=[],r&&r.resetInitialSettings()}return s={abortRequests:function(){u.debug("abort requests"),r.abort(),d=[]},executeRequest:function(t){t.action===p.ACTION_DOWNLOAD?(v(t,N),d.push(t),function(t){n.trigger(a.FRAGMENT_LOADING_STARTED,{request:t},{streamId:i.id,mediaType:o}),r.load(t)}(t)):u.warn("Unknown request action.")},getRequests:function(t){const n=t?t.state instanceof Array?t.state:[t.state]:[];let a=[];return n.forEach((n=>{const e=function(t){let n;switch(t){case N:n=d;break;case D:n=c;break;default:n=[]}return n}(n);a=a.concat(function(t,n){return n.hasOwnProperty("time")?[h(t,n.time,n.threshold)]:t.filter((t=>{for(const a in n)if("state"!==a&&n.hasOwnProperty(a)&&t[a]!=n[a])return!1;return!0}))}(e,t))})),a},getStreamId:function(){return i.id},getType:function(){return o},isFragmentLoaded:m,isFragmentLoadedOrPending:function(t){let n,a=!1,e=0;if(a=m(t),!a)for(e=0;e<d.length;e++)n=d[e],t.url===n.url&&t.startTime===n.startTime&&(a=!0);return a},removeExecutedRequestsAfterTime:function(t){c=c.filter((n=>isNaN(n.startTime)||void 0!==t&&n.startTime<t))},removeExecutedRequestsBeforeTime:f,reset:function(){n.off(a.LOADING_COMPLETED,y,this),n.off(a.LOADING_DATA_PROGRESS,k,this),n.off(a.LOADING_ABANDONED,w,this),r&&r.reset(),E()},resetInitialSettings:E,syncExecutedRequestsWithBufferedRange:function(t,n){if(!t||0===t.length)return void f();let a=0;for(let n=0,e=t.length;n<e;n++)b(a,t.start(n)),a=t.end(n);n>0&&b(a,n)}},u=l.getLogger(s),E(),n.on(a.LOADING_COMPLETED,y,s),n.on(a.LOADING_DATA_PROGRESS,k,s),n.on(a.LOADING_ABANDONED,w,s),s}O.__dashjs_factory_name="FragmentModel";const q=i.getClassFactory(O);q.FRAGMENT_MODEL_LOADING=N,q.FRAGMENT_MODEL_EXECUTED=D,q.FRAGMENT_MODEL_CANCELED="canceled",q.FRAGMENT_MODEL_FAILED=j,i.updateClassFactory(O.__dashjs_factory_name,q);var C=q,L=a(3282),P=a(8571);class M{static mixin(t,n,a){let e,r={};if(t)for(let l in n)n.hasOwnProperty(l)&&(e=n[l],l in t&&(t[l]===e||l in r&&r[l]===e)||("object"==typeof t[l]&&null!==t[l]?t[l]=M.mixin(t[l],e,a):t[l]=a(e)));return t}static clone(t){if(!t||"object"!=typeof t)return t;if(t instanceof RegExp)return new RegExp(t);let n;if(t instanceof Array){n=[];for(let a=0,e=t.length;a<e;++a)a in t&&n.push(M.clone(t[a]))}else n={};return M.mixin(n,t,M.clone)}static addAdditionalQueryParameterToUrl(t,n){try{if(!n||0===n.length)return t;let a=t;return n.forEach((t=>{let{key:n,value:e}=t;const r=a.includes("?")?"&":"?";a+=`${r}${encodeURIComponent(n)}=${encodeURIComponent(e)}`})),a}catch(n){return t}}static removeQueryParameterFromUrl(t,n){if(!t||!n)return t;const a=new URL(t),e=new URLSearchParams(a.search);if(!e||0===e.size||!e.has(n))return t;e.delete(n);const r=Array.from(e.entries()).map((t=>{let[n,a]=t;return`${n}=${a}`})).join("&"),l=`${a.origin}${a.pathname}`;return r?`${l}?${r}`:l}static parseHttpHeaders(t){let n={};if(!t)return n;let a=t.trim().split("\r\n");for(let t=0,e=a.length;t<e;t++){let e=a[t],r=e.indexOf(": ");r>0&&(n[e.substring(0,r)]=e.substring(r+2))}return n}static parseQueryParams(t){const n=[],a=new URLSearchParams(t);for(const[t,e]of a.entries())n.push({key:decodeURIComponent(t),value:decodeURIComponent(e)});return n}static generateUuid(){let t=(new Date).getTime();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(n){const a=(t+16*Math.random())%16|0;return t=Math.floor(t/16),("x"==n?a:3&a|8).toString(16)}))}static generateHashCode(t){let n=0;if(0===t.length)return n;for(let a=0;a<t.length;a++)n=(n<<5)-n+t.charCodeAt(a),n|=0;return n}static getRelativeUrl(t,n){try{const a=new URL(t),e=new URL(n);if(a.protocol=e.protocol,a.origin!==e.origin)return n;let r=L.relative(a.pathname.substr(0,a.pathname.lastIndexOf("/")),e.pathname.substr(0,e.pathname.lastIndexOf("/")));const l=0===r.length?1:0;return r+=e.pathname.substr(e.pathname.lastIndexOf("/")+l,e.pathname.length-1),e.pathname.length<r.length?e.pathname:r}catch(t){return n}}static getHostFromUrl(t){try{return new URL(t).host}catch(t){return null}}static parseUserAgent(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;try{const n=null===t&&"undefined"!=typeof navigator?navigator.userAgent.toLowerCase():"";return(0,P.UAParser)(n)}catch(t){return{}}}static stringHasProtocol(t){return/(http(s?)):\/\//i.test(t)}static bufferSourceToDataView(t){return M.toDataView(t,DataView)}static bufferSourceToInt8(t){return M.toDataView(t,Uint8Array)}static uint8ArrayToString(t){return new TextDecoder("utf-8").decode(t)}static bufferSourceToHex(t){const n=M.bufferSourceToInt8(t);let a="";for(let t of n)t=t.toString(16),1===t.length&&(t="0"+t),a+=t;return a}static toDataView(t,n){const a=M.getArrayBuffer(t);let e=1;"BYTES_PER_ELEMENT"in DataView&&(e=DataView.BYTES_PER_ELEMENT);const r=((t.byteOffset||0)+t.byteLength)/e,l=(t.byteOffset||0)/e,i=Math.floor(Math.max(0,Math.min(l,r)));return new n(a,i,Math.floor(Math.min(i+Math.max(1/0,0),r))-i)}static getArrayBuffer(t){return t instanceof ArrayBuffer?t:t.buffer}static getCodecFamily(t){const{base:n,profile:a}=M._getCodecParts(t);switch(n){case"mp4a":switch(a){case"69":case"6b":case"40.34":return A.CODEC_FAMILIES.MP3;case"66":case"67":case"68":case"40.2":case"40.02":case"40.5":case"40.05":case"40.29":case"40.42":return A.CODEC_FAMILIES.AAC;case"a5":return A.CODEC_FAMILIES.AC3;case"e6":return A.CODEC_FAMILIES.EC3;case"b2":return A.CODEC_FAMILIES.DTSX;case"a9":return A.CODEC_FAMILIES.DTSC}break;case"avc1":case"avc3":return A.CODEC_FAMILIES.AVC;case"hvc1":case"hvc3":return A.CODEC_FAMILIES.HEVC;default:return n}return n}static _getCodecParts(t){const[n,...a]=t.split(".");return{base:n,profile:a.join(".")}}}var U=M;function F(){let t,n;function a(){n&&(n.onloadend=n.onerror=n.onprogress=n.onload=null,n.abort(),n=null)}return t={load:function(t,e){if(n=null,n=new XMLHttpRequest,n.open(t.method,t.url,!0),t.responseType&&(n.responseType=t.responseType),t.headers)for(let a in t.headers){let e=t.headers[a];e&&n.setRequestHeader(a,e)}return n.withCredentials="include"===t.credentials,n.timeout=t.timeout,n.onload=function(){e.url=this.responseURL,e.status=this.status,e.statusText=this.statusText,e.headers=U.parseHttpHeaders(this.getAllResponseHeaders()),e.data=this.response},t.customData&&(n.onloadend=t.customData.onloadend,n.onprogress=t.customData.onprogress,n.onabort=t.customData.onabort,n.ontimeout=t.customData.ontimeout),n.send(),t.customData.abort=a.bind(this),!0},abort:a,getXhr:function(){return n},reset:function(){a(),t=null},resetInitialSettings:function(){a()}},t}F.__dashjs_factory_name="XHRLoader";var B=i.getClassFactory(F);function G(){let t={};function n(n,a,r){let l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(!n)throw new Error("event type cannot be null or undefined");if(!a||"function"!=typeof a)throw new Error("listener must be a function: "+a);let o=l.priority||0;if(e(n,a,r)>=0)return;t[n]=t[n]||[];const s={callback:a,scope:r,priority:o,executeOnlyOnce:i};r&&r.getStreamId&&(s.streamId=r.getStreamId()),r&&r.getType&&(s.mediaType=r.getType()),l&&l.mode&&(s.mode=l.mode),t[n].some(((a,e)=>{if(a&&o>a.priority)return t[n].splice(e,0,s),!0}))||t[n].push(s)}function a(n,a,r){if(!n||!a||!t[n])return;const l=e(n,a,r);l<0||(t[n][l]=null)}function e(n,a,e){let r=-1;return t[n]?(t[n].some(((t,n)=>{if(t&&t.callback===a&&(!e||e===t.scope))return r=n,!0})),r):r}const r={on:function(t,a,e){n(t,a,e,arguments.length>3&&void 0!==arguments[3]?arguments[3]:{})},once:function(t,a,e){n(t,a,e,arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},!0)},off:a,trigger:function(n){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!n||!t[n])return;if(e=e||{},e.hasOwnProperty("type"))throw new Error("'type' is a reserved word for event dispatching");e.type=n,r.streamId&&(e.streamId=r.streamId),r.mediaType&&(e.mediaType=r.mediaType);const l=[];t[n].filter((t=>!(!t||r.streamId&&t.streamId&&t.streamId!==r.streamId||r.mediaType&&t.mediaType&&t.mediaType!==r.mediaType||r.mode&&t.mode&&t.mode!==r.mode||!t.mode&&r.mode&&r.mode===h.EVENT_MODE_ON_RECEIVE))).forEach((t=>{t&&t.callback.call(t.scope,e),t.executeOnlyOnce&&l.push(t)})),l.forEach((t=>{a(n,t.callback,t.scope)}))},reset:function(){t={}}};return r}G.__dashjs_factory_name="EventBus";const H=i.getSingletonFactory(G);H.EVENT_PRIORITY_LOW=0,H.EVENT_PRIORITY_HIGH=5e3,i.updateSingletonFactory(G.__dashjs_factory_name,H);var V=H,Y=class extends b{constructor(){super(),this.ATTEMPT_BACKGROUND_SYNC="attemptBackgroundSync",this.BUFFERING_COMPLETED="bufferingCompleted",this.BUFFER_CLEARED="bufferCleared",this.BYTES_APPENDED_END_FRAGMENT="bytesAppendedEndFragment",this.BUFFER_REPLACEMENT_STARTED="bufferReplacementStarted",this.CHECK_FOR_EXISTENCE_COMPLETED="checkForExistenceCompleted",this.CMSD_STATIC_HEADER="cmsdStaticHeader",this.CURRENT_TRACK_CHANGED="currentTrackChanged",this.DATA_UPDATE_COMPLETED="dataUpdateCompleted",this.INBAND_EVENTS="inbandEvents",this.INITIAL_STREAM_SWITCH="initialStreamSwitch",this.INIT_FRAGMENT_LOADED="initFragmentLoaded",this.INIT_FRAGMENT_NEEDED="initFragmentNeeded",this.INTERNAL_MANIFEST_LOADED="internalManifestLoaded",this.ORIGINAL_MANIFEST_LOADED="originalManifestLoaded",this.LOADING_COMPLETED="loadingCompleted",this.LOADING_PROGRESS="loadingProgress",this.LOADING_DATA_PROGRESS="loadingDataProgress",this.LOADING_ABANDONED="loadingAborted",this.MANIFEST_UPDATED="manifestUpdated",this.MEDIA_FRAGMENT_LOADED="mediaFragmentLoaded",this.MEDIA_FRAGMENT_NEEDED="mediaFragmentNeeded",this.MEDIAINFO_UPDATED="mediaInfoUpdated",this.QUOTA_EXCEEDED="quotaExceeded",this.SEGMENT_LOCATION_BLACKLIST_ADD="segmentLocationBlacklistAdd",this.SEGMENT_LOCATION_BLACKLIST_CHANGED="segmentLocationBlacklistChanged",this.SERVICE_LOCATION_BASE_URL_BLACKLIST_ADD="serviceLocationBlacklistAdd",this.SERVICE_LOCATION_BASE_URL_BLACKLIST_CHANGED="serviceLocationBlacklistChanged",this.SERVICE_LOCATION_LOCATION_BLACKLIST_ADD="serviceLocationLocationBlacklistAdd",this.SERVICE_LOCATION_LOCATION_BLACKLIST_CHANGED="serviceLocationLocationBlacklistChanged",this.SET_FRAGMENTED_TEXT_AFTER_DISABLED="setFragmentedTextAfterDisabled",this.SET_NON_FRAGMENTED_TEXT="setNonFragmentedText",this.SOURCE_BUFFER_ERROR="sourceBufferError",this.STREAMS_COMPOSED="streamsComposed",this.STREAM_BUFFERING_COMPLETED="streamBufferingCompleted",this.STREAM_REQUESTING_COMPLETED="streamRequestingCompleted",this.TEXT_TRACKS_QUEUE_INITIALIZED="textTracksQueueInitialized",this.TIME_SYNCHRONIZATION_COMPLETED="timeSynchronizationComplete",this.UPDATE_TIME_SYNC_OFFSET="updateTimeSyncOffset",this.URL_RESOLUTION_FAILED="urlResolutionFailed",this.VIDEO_CHUNK_RECEIVED="videoChunkReceived",this.WALLCLOCK_TIME_UPDATED="wallclockTimeUpdated",this.XLINK_ELEMENT_LOADED="xlinkElementLoaded",this.XLINK_READY="xlinkReady",this.SEEK_TARGET="seekTarget",this.SETTING_UPDATED_LIVE_DELAY="settingUpdatedLiveDelay",this.SETTING_UPDATED_LIVE_DELAY_FRAGMENT_COUNT="settingUpdatedLiveDelayFragmentCount",this.SETTING_UPDATED_CATCHUP_ENABLED="settingUpdatedCatchupEnabled",this.SETTING_UPDATED_PLAYBACK_RATE_MIN="settingUpdatedPlaybackRateMin",this.SETTING_UPDATED_PLAYBACK_RATE_MAX="settingUpdatedPlaybackRateMax",this.SETTING_UPDATED_ABR_ACTIVE_RULES="settingUpdatedAbrActiveRules",this.SETTING_UPDATED_MAX_BITRATE="settingUpdatedMaxBitrate",this.SETTING_UPDATED_MIN_BITRATE="settingUpdatedMinBitrate"}},X=new class extends Y{};function W(t){t=t||{};const n=this.context,a=V(n).getInstance(),e=t.settings,r=[];let l,i,o,s;function u(t){return t&&t.bind?t.bind(window.console):window.console.log.bind(window.console)}function c(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];p(1,this,...n)}function d(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];p(2,this,...n)}function m(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];p(3,this,...n)}function g(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];p(4,this,...n)}function f(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];p(5,this,...n)}function p(t,n){let l="",u=null;i&&(u=(new Date).getTime(),l+="["+(u-s)+"]"),o&&n&&n.getClassName&&(l+="["+n.getClassName()+"]",n.getType&&(l+="["+n.getType()+"]")),l.length>0&&(l+=" ");for(var c=arguments.length,d=new Array(c>2?c-2:0),m=2;m<c;m++)d[m-2]=arguments[m];Array.apply(null,d).forEach((function(t){l+=t+" "})),r[t]&&e&&e.get().debug.logLevel>=t&&r[t](l),e&&e.get().debug.dispatchEvent&&a.trigger(X.LOG,{message:l,level:t})}return l={getLogger:function(t){return{fatal:c.bind(t),error:d.bind(t),warn:m.bind(t),info:g.bind(t),debug:f.bind(t)}},setLogTimestampVisible:function(t){i=t},setCalleeNameVisible:function(t){o=t}},i=!0,o=!0,s=(new Date).getTime(),"undefined"!=typeof window&&window.console&&(r[1]=u(window.console.error),r[2]=u(window.console.error),r[3]=u(window.console.warn),r[4]=u(window.console.info),r[5]=u(window.console.debug)),l}W.__dashjs_factory_name="Debug";const K=i.getSingletonFactory(W);K.LOG_LEVEL_NONE=0,K.LOG_LEVEL_FATAL=1,K.LOG_LEVEL_ERROR=2,K.LOG_LEVEL_WARNING=3,K.LOG_LEVEL_INFO=4,K.LOG_LEVEL_DEBUG=5,i.updateSingletonFactory(W.__dashjs_factory_name,K);var Q=K;function $(){let t;const n=this.context,a=V(n).getInstance(),e={"streaming.delay.liveDelay":X.SETTING_UPDATED_LIVE_DELAY,"streaming.delay.liveDelayFragmentCount":X.SETTING_UPDATED_LIVE_DELAY_FRAGMENT_COUNT,"streaming.liveCatchup.enabled":X.SETTING_UPDATED_CATCHUP_ENABLED,"streaming.liveCatchup.playbackRate.min":X.SETTING_UPDATED_PLAYBACK_RATE_MIN,"streaming.liveCatchup.playbackRate.max":X.SETTING_UPDATED_PLAYBACK_RATE_MAX,"streaming.abr.rules.throughputRule.active":X.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.bolaRule.active":X.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.insufficientBufferRule.active":X.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.switchHistoryRule.active":X.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.droppedFramesRule.active":X.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.abandonRequestsRule.active":X.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.l2ARule.active":X.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.rules.loLPRule.active":X.SETTING_UPDATED_ABR_ACTIVE_RULES,"streaming.abr.maxBitrate.video":X.SETTING_UPDATED_MAX_BITRATE,"streaming.abr.maxBitrate.audio":X.SETTING_UPDATED_MAX_BITRATE,"streaming.abr.minBitrate.video":X.SETTING_UPDATED_MIN_BITRATE,"streaming.abr.minBitrate.audio":X.SETTING_UPDATED_MIN_BITRATE},r={debug:{logLevel:Q.LOG_LEVEL_WARNING,dispatchEvent:!1},streaming:{abandonLoadTimeout:1e4,wallclockTimeUpdateInterval:100,manifestUpdateRetryInterval:100,liveUpdateTimeThresholdInMilliseconds:0,cacheInitSegments:!1,applyServiceDescription:!0,applyProducerReferenceTime:!0,applyContentSteering:!0,enableManifestDurationMismatchFix:!0,parseInbandPrft:!1,enableManifestTimescaleMismatchFix:!1,capabilities:{filterUnsupportedEssentialProperties:!0,supportedEssentialProperties:[{schemeIdUri:A.FONT_DOWNLOAD_DVB_SCHEME},{schemeIdUri:A.COLOUR_PRIMARIES_SCHEME_ID_URI,value:/1|5|6|7/},{schemeIdUri:A.URL_QUERY_INFO_SCHEME},{schemeIdUri:A.EXT_URL_QUERY_INFO_SCHEME},{schemeIdUri:A.MATRIX_COEFFICIENTS_SCHEME_ID_URI,value:/0|1|5|6/},{schemeIdUri:A.TRANSFER_CHARACTERISTICS_SCHEME_ID_URI,value:/1|6|13|14|15/},...A.THUMBNAILS_SCHEME_ID_URIS.map((t=>({schemeIdUri:t})))],useMediaCapabilitiesApi:!0,filterVideoColorimetryEssentialProperties:!1,filterHDRMetadataFormatEssentialProperties:!1},events:{eventControllerRefreshDelay:100,deleteEventMessageDataTimeout:1e4},timeShiftBuffer:{calcFromSegmentTimeline:!1,fallbackToSegmentTimeline:!0},metrics:{maxListDepth:100},delay:{liveDelayFragmentCount:NaN,liveDelay:NaN,useSuggestedPresentationDelay:!0},protection:{keepProtectionMediaKeys:!1,ignoreEmeEncryptedEvent:!1,detectPlayreadyMessageFormat:!0,ignoreKeyStatuses:!1},buffer:{enableSeekDecorrelationFix:!1,fastSwitchEnabled:null,flushBufferAtTrackSwitch:!1,reuseExistingSourceBuffers:!0,bufferPruningInterval:10,bufferToKeep:20,bufferTimeAtTopQuality:30,bufferTimeAtTopQualityLongForm:60,initialBufferLevel:NaN,bufferTimeDefault:18,longFormContentDurationThreshold:600,stallThreshold:.3,lowLatencyStallThreshold:.3,useAppendWindow:!0,setStallState:!0,avoidCurrentTimeRangePruning:!1,useChangeType:!0,mediaSourceDurationInfinity:!0,resetSourceBuffersForTrackSwitch:!1,syntheticStallEvents:{enabled:!1,ignoreReadyState:!1}},gaps:{jumpGaps:!0,jumpLargeGaps:!0,smallGapLimit:1.5,threshold:.3,enableSeekFix:!0,enableStallFix:!1,stallSeek:.1},utcSynchronization:{enabled:!0,useManifestDateHeaderTimeSource:!0,backgroundAttempts:2,timeBetweenSyncAttempts:30,maximumTimeBetweenSyncAttempts:600,minimumTimeBetweenSyncAttempts:2,timeBetweenSyncAttemptsAdjustmentFactor:2,maximumAllowedDrift:100,enableBackgroundSyncAfterSegmentDownloadError:!0,defaultTimingSource:{scheme:"urn:mpeg:dash:utc:http-xsdate:2014",value:"https://time.akamai.com/?iso&ms"}},scheduling:{defaultTimeout:500,lowLatencyTimeout:0,scheduleWhilePaused:!0},text:{defaultEnabled:!0,dispatchForManualRendering:!1,extendSegmentedCues:!0,imsc:{displayForcedOnlyMode:!1,enableRollUp:!0},webvtt:{customRenderingEnabled:!1}},liveCatchup:{maxDrift:NaN,playbackRate:{min:NaN,max:NaN},playbackBufferMin:.5,enabled:null,mode:A.LIVE_CATCHUP_MODE_DEFAULT},lastBitrateCachingInfo:{enabled:!0,ttl:36e4},lastMediaSettingsCachingInfo:{enabled:!0,ttl:36e4},saveLastMediaSettingsForCurrentStreamingSession:!0,cacheLoadThresholds:{video:10,audio:5},trackSwitchMode:{audio:A.TRACK_SWITCH_MODE_ALWAYS_REPLACE,video:A.TRACK_SWITCH_MODE_NEVER_REPLACE},ignoreSelectionPriority:!1,prioritizeRoleMain:!0,assumeDefaultRoleAsMain:!0,selectionModeForInitialTrack:A.TRACK_SELECTION_MODE_HIGHEST_EFFICIENCY,fragmentRequestTimeout:2e4,fragmentRequestProgressTimeout:-1,manifestRequestTimeout:1e4,retryIntervals:{[g.MPD_TYPE]:500,[g.XLINK_EXPANSION_TYPE]:500,[g.MEDIA_SEGMENT_TYPE]:1e3,[g.INIT_SEGMENT_TYPE]:1e3,[g.BITSTREAM_SWITCHING_SEGMENT_TYPE]:1e3,[g.INDEX_SEGMENT_TYPE]:1e3,[g.MSS_FRAGMENT_INFO_SEGMENT_TYPE]:1e3,[g.LICENSE]:1e3,[g.OTHER_TYPE]:1e3,lowLatencyReductionFactor:10},retryAttempts:{[g.MPD_TYPE]:3,[g.XLINK_EXPANSION_TYPE]:1,[g.MEDIA_SEGMENT_TYPE]:3,[g.INIT_SEGMENT_TYPE]:3,[g.BITSTREAM_SWITCHING_SEGMENT_TYPE]:3,[g.INDEX_SEGMENT_TYPE]:3,[g.MSS_FRAGMENT_INFO_SEGMENT_TYPE]:3,[g.LICENSE]:3,[g.OTHER_TYPE]:3,lowLatencyMultiplyFactor:5},abr:{limitBitrateByPortal:!1,usePixelRatioInLimitBitrateByPortal:!1,enableSupplementalPropertyAdaptationSetSwitching:!0,rules:{throughputRule:{active:!0},bolaRule:{active:!0},insufficientBufferRule:{active:!0,parameters:{throughputSafetyFactor:.7,segmentIgnoreCount:2}},switchHistoryRule:{active:!0,parameters:{sampleSize:8,switchPercentageThreshold:.075}},droppedFramesRule:{active:!1,parameters:{minimumSampleSize:375,droppedFramesPercentageThreshold:.15}},abandonRequestsRule:{active:!0,parameters:{abandonDurationMultiplier:1.8,minSegmentDownloadTimeThresholdInMs:500,minThroughputSamplesThreshold:6}},l2ARule:{active:!1},loLPRule:{active:!1}},throughput:{averageCalculationMode:A.THROUGHPUT_CALCULATION_MODES.EWMA,lowLatencyDownloadTimeCalculationMode:A.LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE.MOOF_PARSING,useResourceTimingApi:!0,useNetworkInformationApi:{xhr:!1,fetch:!1},useDeadTimeLatency:!0,bandwidthSafetyFactor:.9,sampleSettings:{live:3,vod:4,enableSampleSizeAdjustment:!0,decreaseScale:.7,increaseScale:1.3,maxMeasurementsToKeep:20,averageLatencySampleAmount:4},ewma:{throughputSlowHalfLifeSeconds:8,throughputFastHalfLifeSeconds:3,latencySlowHalfLifeCount:2,latencyFastHalfLifeCount:1,weightDownloadTimeMultiplicationFactor:.0015}},maxBitrate:{audio:-1,video:-1},minBitrate:{audio:-1,video:-1},initialBitrate:{audio:-1,video:-1},autoSwitchBitrate:{audio:!0,video:!0}},cmcd:{applyParametersFromMpd:!0,enabled:!1,sid:null,cid:null,rtp:null,rtpSafetyFactor:5,mode:A.CMCD_MODE_QUERY,enabledKeys:A.CMCD_AVAILABLE_KEYS,includeInRequests:["segment","mpd"],version:1},cmsd:{enabled:!1,abr:{applyMb:!1,etpWeightRatio:0}},defaultSchemeIdUri:{viewpoint:"",audioChannelConfiguration:"urn:mpeg:mpegB:cicp:ChannelConfiguration",role:"urn:mpeg:dash:role:2011",accessibility:"urn:mpeg:dash:role:2011"}},errors:{recoverAttempts:{mediaErrorDecode:5}}};let l=U.clone(r);function i(t,n,r){for(let l in t)t.hasOwnProperty(l)&&(n.hasOwnProperty(l)?"object"!=typeof t[l]||t[l]instanceof RegExp||t[l]instanceof Array||null===t[l]?(n[l]=U.clone(t[l]),e[r+l]&&a.trigger(e[r+l])):i(t[l],n[l],r.slice()+l+"."):console.error("Settings parameter "+r+l+" is not supported"))}return t={get:function(){return l},update:function(t){"object"==typeof t&&i(t,l,"")},reset:function(){l=U.clone(r)}},t}$.__dashjs_factory_name="Settings";var Z=i.getSingletonFactory($);function J(){const t=this.context,n=Z(t).getInstance();let a,e,r;function l(t,n,a){t.customData.reader.read().then(a).catch((function(){i(t)}))}function i(t){t.customData.onloadend&&t.customData.onloadend()}function o(){return"undefined"!=typeof performance&&"function"==typeof performance.now?performance.now():Date.now()}function s(){if(this.customData.abortController)this.customData.abortController.abort();else if(this.customData.reader)try{this.customData.reader.cancel(),this.onabort()}catch(t){}}function u(t,n){try{if((t=t.filter((a=>a.bytes>n/4/t.length))).length>1){let n=0;const a=(t[t.length-1].timestamp-t[0].timestamp)/t.length;return t.forEach(((e,r)=>{const l=t[r+1];if(l){const t=l.timestamp-e.timestamp;n+=t<a?t:0}})),n}return null}catch(t){return null}}function c(t,n){return 8*t/n}return r=Q(t).getInstance().getLogger(a),a={abort:s,calculateDownloadedTime:u,load:function(t,a){const d=function(t){const n=new Headers;if(t.headers)for(let a in t.headers){let e=t.headers[a];e&&n.append(a,e)}return n}(t),m=function(t){let n;return"function"==typeof window.AbortController&&(n=new AbortController,t.customData.abortController=n,n.signal.onabort=t.customData.onabort),t.customData.abort=s.bind(t),n}(t),g=function(t,n,a){return new Request(t.url,{method:t.method,headers:n,credentials:t.credentials,signal:a?a.signal:void 0})}(t,d,m);fetch(g).then((i=>{!function(t,a,i){(function(t,n){t.status=n.status,t.statusText=n.statusText,t.url=n.url;const a={};for(const t of n.headers.keys())a[t]=n.headers.get(t);t.headers=a})(i,t),t.ok||a.customData.onloadend();let s=0,d=!1,m=new Uint8Array,g=0;a.customData.reader=t.body.getReader();let f=[],p=[],b=[],h=!0;const v=n.get().streaming.abr.throughput.lowLatencyDownloadTimeCalculationMode,y=t=>{let{value:n,done:k}=t;k?function(){if(m){const t=function(){let t=null;return v===A.LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE.MOOF_PARSING?t=function(){const t=function(t,n){try{let a,e;if(a=t.slice(0,-1),e=n.slice(0,-1),a.length!==e.length&&r.warn(`[FetchLoader] Moof and Mdat data arrays have different lengths. Moof: ${a.length}, Mdat: ${e.length}`),a.length<=1)return null;let l=[],i=0,o=0;for(let t=0;t<a.length;t++)if(a[t]&&e[t]){let n=e[t].timestamp-a[t].timestamp;if(n>1){const a=c(e[t].bytes,n);l.push(a),o=0}else{0===o&&(o=a[t].timestamp,i=0);let n=e[t].timestamp-o;if(n>1){i+=e[t].bytes;const a=c(i,n);l.push(a),o=0}else i+=e[t].bytes}}return l.length>0?l.reduce(((t,n)=>t+n),0)/l.length:null}catch(t){return null}}(p,b);return t?8*s/t:null}():v===A.LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE.DOWNLOADED_DATA&&(t=u(f,s)),t}();a.customData.onprogress({loaded:s,total:s,lengthComputable:!0,time:t}),i.data=m.buffer}a.customData.onloadend()}():(n&&n.length>0&&function(t){m=function(t,n){if(0===t.length)return n;const a=new Uint8Array(t.length+n.length);return a.set(t),a.set(n,t.length),a}(m,t),s+=t.length,f.push({timestamp:o(),bytes:t.length}),v===A.LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE.MOOF_PARSING&&h&&e.findLastTopIsoBoxCompleted(["moof"],m,g).found&&(h=!1,p.push({timestamp:o()}));const n=e.findLastTopIsoBoxCompleted(["moov","mdat"],m,g);n.found?function(t){const n=function(t){let n;return t===m.length?(n=m,m=new Uint8Array):(n=new Uint8Array(m.subarray(0,t)),m=m.subarray(t)),n}(t.startOffsetOfLastFoundTargetBox+t.sizeOfLastFoundTargetBox);v!==A.LOW_LATENCY_DOWNLOAD_TIME_CALCULATION_MODE.MOOF_PARSING||h||(h=!0,b.push({timestamp:o(),bytes:n.length})),a.customData.onprogress({data:n.buffer,lengthComputable:!1,noTrace:!0}),g=0}(n):function(t){g=t.startOffsetOfLastCompletedBox+t.sizeOfLastCompletedBox,d||(a.customData.onprogress({lengthComputable:!1,noTrace:!0}),d=!0)}(n)}(n),l(a,0,y))};l(a,0,y)}(i,t,a)})).catch((()=>{i(t)}))},reset:function(){},setConfig:function(t){e=t.boxParser}},a}J.__dashjs_factory_name="FetchLoader";var tt=i.getClassFactory(J),nt=class{constructor(t,n,a){this.code=t||null,this.message=n||null,this.data=a||null}},at=new class extends b{constructor(){super(),this.METRICS_INITIALISATION_COMPLETE="internal_metricsReportingInitialized",this.BECAME_REPORTING_PLAYER="internal_becameReportingPlayer",this.CMCD_DATA_GENERATED="cmcdDataGenerated"}},et=class{constructor(){this.period=null,this.index=-1,this.type=null}};class rt{constructor(t,n,a,e){this.url=t||"",this.serviceLocation=n||t||"",this.dvbPriority=a||1,this.dvbWeight=e||1,this.availabilityTimeOffset=0,this.availabilityTimeComplete=!0,this.queryParams={}}}rt.DEFAULT_DVB_PRIORITY=1,rt.DEFAULT_DVB_WEIGHT=1;var lt=rt,it=class{constructor(){this.schemeIdUri=null,this.value=null,this.id=null}init(t){t&&(this.schemeIdUri=t.schemeIdUri?t.schemeIdUri:null,this.value=null!==t.value&&void 0!==t.value?t.value.toString():null,this.id=t.id?t.id:null,t[v.DVB_URL]&&(this.dvbUrl=t[v.DVB_URL]),t[v.DVB_MIMETYPE]&&(this.dvbMimeType=t[v.DVB_MIMETYPE]),t[v.DVB_FONTFAMILY]&&(this.dvbFontFamily=t[v.DVB_FONTFAMILY]))}inArray(t){return!!t&&t.some((t=>this.schemeIdUri===t.schemeIdUri&&(this.value?this.value.toString().match(t.value):"".match(t.value))))}},ot=class extends it{constructor(){super(),this.version=null,this.sessionID=null,this.contentID=null,this.mode=null,this.keys=null,this.includeInRequests=null}init(t){super.init(t),t&&(this.version=t.version,this.sessionID=t.sessionID,this.contentID=t.contentID,this.mode=t.mode??"query",this.keys=t.keys?t.keys.split(" "):null,this.includeInRequests=t.includeInRequests?t.includeInRequests.split(" "):["segment"],this.schemeIdUri=t.schemeIdUri)}},st=class{constructor(){this.adaptationSets=null,this.adaptationSetsArray=[],this.cmcdParameters=null,this.serviceLocations=null,this.serviceLocationsArray=[]}},ut=class extends it{constructor(){super(),this.ref=null,this.refId=null,this.robustness=null,this.keyId=null,this.cencDefaultKid=null,this.pssh=null,this.pro=null,this.laUrl=null}init(t){super.init(t),t&&(this.ref=t.hasOwnProperty(v.REF)?t[v.REF]:null,this.refId=t.hasOwnProperty(v.REF_ID)?t[v.REF_ID]:null,this.robustness=t.hasOwnProperty(v.ROBUSTNESS)?t[v.ROBUSTNESS]:null,this.cencDefaultKid=t.hasOwnProperty(v.CENC_DEFAULT_KID)?t[v.CENC_DEFAULT_KID]:null,this.pssh=t.hasOwnProperty(v.PSSH)?t[v.PSSH]:null,this.pro=t.hasOwnProperty(v.PRO)?t[v.PRO]:null,this.laUrl=t.hasOwnProperty(v.LA_URL)?t[v.LA_URL]:t.hasOwnProperty(v.LA_URL_LOWER_CASE)?t[v.LA_URL_LOWER_CASE]:null)}mergeAttributesFromReference(t){["schemeIdUri","value","id","robustness","cencDefaultKid","pro","pssh","laUrl"].forEach((n=>{null===this[n]&&(this[n]=t[n])}))}},ct=class{constructor(){this.defaultServiceLocation=null,this.defaultServiceLocationArray=[],this.queryBeforeStart=!1,this.serverUrl=null,this.clientRequirement=!0}},dt=new class extends u{constructor(){super(),this.MANIFEST_LOADER_PARSING_FAILURE_ERROR_CODE=10,this.MANIFEST_LOADER_LOADING_FAILURE_ERROR_CODE=11,this.XLINK_LOADER_LOADING_FAILURE_ERROR_CODE=12,this.SEGMENT_BASE_LOADER_ERROR_CODE=15,this.TIME_SYNC_FAILED_ERROR_CODE=16,this.FRAGMENT_LOADER_LOADING_FAILURE_ERROR_CODE=17,this.FRAGMENT_LOADER_NULL_REQUEST_ERROR_CODE=18,this.URL_RESOLUTION_FAILED_GENERIC_ERROR_CODE=19,this.APPEND_ERROR_CODE=20,this.REMOVE_ERROR_CODE=21,this.DATA_UPDATE_FAILED_ERROR_CODE=22,this.CAPABILITY_MEDIASOURCE_ERROR_CODE=23,this.CAPABILITY_MEDIAKEYS_ERROR_CODE=24,this.DOWNLOAD_ERROR_ID_MANIFEST_CODE=25,this.DOWNLOAD_ERROR_ID_SIDX_CODE=26,this.DOWNLOAD_ERROR_ID_CONTENT_CODE=27,this.DOWNLOAD_ERROR_ID_INITIALIZATION_CODE=28,this.DOWNLOAD_ERROR_ID_XLINK_CODE=29,this.MANIFEST_ERROR_ID_PARSE_CODE=31,this.MANIFEST_ERROR_ID_NOSTREAMS_CODE=32,this.TIMED_TEXT_ERROR_ID_PARSE_CODE=33,this.MANIFEST_ERROR_ID_MULTIPLEXED_CODE=34,this.MEDIASOURCE_TYPE_UNSUPPORTED_CODE=35,this.NO_SUPPORTED_KEY_IDS=36,this.MANIFEST_LOADER_PARSING_FAILURE_ERROR_MESSAGE="parsing failed for ",this.MANIFEST_LOADER_LOADING_FAILURE_ERROR_MESSAGE="Failed loading manifest: ",this.XLINK_LOADER_LOADING_FAILURE_ERROR_MESSAGE="Failed loading Xlink element: ",this.SEGMENTS_UPDATE_FAILED_ERROR_MESSAGE="Segments update failed",this.SEGMENTS_UNAVAILABLE_ERROR_MESSAGE="no segments are available yet",this.SEGMENT_BASE_LOADER_ERROR_MESSAGE="error loading segment ranges from sidx",this.TIME_SYNC_FAILED_ERROR_MESSAGE="Failed to synchronize client and server time",this.FRAGMENT_LOADER_NULL_REQUEST_ERROR_MESSAGE="request is null",this.URL_RESOLUTION_FAILED_GENERIC_ERROR_MESSAGE="Failed to resolve a valid URL",this.APPEND_ERROR_MESSAGE="chunk is not defined",this.REMOVE_ERROR_MESSAGE="Removing data from the SourceBuffer",this.DATA_UPDATE_FAILED_ERROR_MESSAGE="Data update failed",this.CAPABILITY_MEDIASOURCE_ERROR_MESSAGE="mediasource is not supported",this.CAPABILITY_MEDIAKEYS_ERROR_MESSAGE="mediakeys is not supported",this.TIMED_TEXT_ERROR_MESSAGE_PARSE="parsing error :",this.MEDIASOURCE_TYPE_UNSUPPORTED_MESSAGE="Error creating source buffer of type : ",this.NO_SUPPORTED_KEY_IDS_MESSAGE="All possible Adaptation Sets have an invalid key status"}},mt=class{constructor(){this.type="",this.duration=NaN,this.presentationTime=NaN,this.id=NaN,this.messageData="",this.eventStream=null,this.presentationTimeDelta=NaN,this.parsedMessageData=null}},gt=class{constructor(){this.adaptionSet=null,this.representation=null,this.period=null,this.timescale=1,this.value="",this.schemeIdUri="",this.presentationTimeOffset=0}},ft=class{constructor(){this.availabilityEndTime=Number.POSITIVE_INFINITY,this.availabilityStartTime=null,this.manifest=null,this.maxSegmentDuration=Number.POSITIVE_INFINITY,this.mediaPresentationDuration=NaN,this.minimumUpdatePeriod=NaN,this.publishTime=null,this.suggestedPresentationDelay=0,this.timeShiftBufferDepth=Number.POSITIVE_INFINITY}},pt=class{constructor(t,n){this.url=t||"",this.serviceLocation=n||null,this.queryParams={}}},bt=a(7316);function ht(){let t;return t={areEqual:function(t,n){return bt(t,n)}},t}ht.__dashjs_factory_name="ObjectUtils";var vt=i.getSingletonFactory(ht),yt=class{constructor(t,n,a){this.url=t||"",this.serviceLocation=n||null,this.ttl=a||NaN,this.queryParams={}}};class kt{constructor(){this.id=null,this.index=-1,this.duration=NaN,this.start=NaN,this.mpd=null,this.nextPeriodId=null,this.isEncrypted=!1}}kt.DEFAULT_ID="defaultId";var wt=kt,Et=class{constructor(){this.id=null,this.inband=!1,this.type="encoder",this.applicationScheme=null,this.wallClockTime=null,this.presentationTime=NaN,this.UTCTiming=null}},zt=class{constructor(){this.absoluteIndex=NaN,this.adaptation=null,this.availabilityTimeComplete=!0,this.availabilityTimeOffset=0,this.bandwidth=NaN,this.bitrateInKbit=NaN,this.bitsPerPixel=NaN,this.codecFamily=null,this.codecPrivateData=null,this.codecs=null,this.essentialProperties=[],this.fragmentDuration=null,this.frameRate=null,this.height=NaN,this.id=null,this.indexRange=null,this.initialization=null,this.maxPlayoutRate=NaN,this.mediaFinishedInformation={numberOfSegments:0,mediaTimeOfLastSignaledSegment:NaN},this.mediaInfo=null,this.mimeType=null,this.mseTimeOffset=NaN,this.pixelsPerSecond=NaN,this.presentationTimeOffset=0,this.qualityRanking=NaN,this.range=null,this.scanType=null,this.segments=null,this.segmentDuration=NaN,this.segmentInfoType=null,this.supplementalProperties=[],this.startNumber=1,this.timescale=1,this.width=NaN,this.endNumber=null}hasInitialization(){return null!==this.initialization||null!==this.range}hasSegments(){return this.segmentInfoType!==v.BASE_URL&&this.segmentInfoType!==v.SEGMENT_BASE&&!this.indexRange}};function xt(){let t;const n=/^[a-z][a-z0-9+\-_.]*:/i,a=/^https?:\/\//i,e=/^https:\/\//i,r=/^([a-z][a-z0-9+\-_.]*:\/\/[^\/]+)\/?/i,l=(t,n)=>{try{return new window.URL(t,n).toString()}catch(n){return t}},i=(t,n)=>{let a=o;if(!n)return t;if(!c(t))return t;d(t)&&(a=s),m(t)&&(a=u);const e=a(n),r="/"!==e.charAt(e.length-1)&&"/"!==t.charAt(0)?"/":"";return[e,t].join(r)};function o(t){const n=t.indexOf("/"),a=t.lastIndexOf("/");return-1!==n?a===n+1?t:(-1!==t.indexOf("?")&&(t=t.substring(0,t.indexOf("?"))),t.substring(0,a+1)):""}function s(t){const n=t.match(r);return n?n[1]:""}function u(t){const a=t.match(n);return a?a[0]:""}function c(t){return!n.test(t)}function d(t){return c(t)&&"/"===t.charAt(0)}function m(t){return 0===t.indexOf("//")}return function(){try{new window.URL("x","http://y"),t=l}catch(t){}finally{t=t||i}}(),{parseBaseUrl:o,parseOrigin:s,parseScheme:u,isRelative:c,isPathAbsolute:d,isSchemeRelative:m,isHTTPURL:function(t){return a.test(t)},isHTTPS:function(t){return e.test(t)},removeHostname:function(t){return/^(?:\w+\:\/\/)?([^\/]+)(.*)$/.exec(t)[2].substring(1)},resolve:function(n,a){return t(n,a)}}}xt.__dashjs_factory_name="DefaultURLUtils";var Tt=i.getSingletonFactory(xt);function _t(){let t,n,a=[];const e=this.context;function r(t,e,r){let l=function(t){let e;for(e=0;e<a.length;e++)if(a[e].regex.test(t))return a[e].utils;return n}(r||e);return l&&"function"==typeof l[t]?l[t](e,r):n[t](e,r)}return n=Tt(e).getInstance(),t={registerUrlRegex:function(t,n){a.push({regex:t,utils:n})},parseBaseUrl:function(t){return r("parseBaseUrl",t)},parseOrigin:function(t){return r("parseOrigin",t)},parseScheme:function(t){return r("parseScheme",t)},isRelative:function(t){return r("isRelative",t)},isPathAbsolute:function(t){return r("isPathAbsolute",t)},isSchemeRelative:function(t){return r("isSchemeRelative",t)},isHTTPURL:function(t){return r("isHTTPURL",t)},isHTTPS:function(t){return r("isHTTPS",t)},removeHostname:function(t){return r("removeHostname",t)},resolve:function(t,n){return r("resolve",t,n)}},t}_t.__dashjs_factory_name="URLUtils";var It=i.getSingletonFactory(_t),At=class{constructor(){this.schemeIdUri="",this.value=""}};function St(){let t,n,a,e;const r=this.context,l=It(r).getInstance(),i=Number.isInteger||function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t};function o(t,n){if(!t)throw new Error("adaptation is not defined");if(!n)throw new Error("type is not defined");if(t.Representation&&t.Representation.length){const a=z(t.Representation[0]);if(a&&a.some((t=>A.THUMBNAILS_SCHEME_ID_URIS.indexOf(t.schemeIdUri)>=0)))return n===A.IMAGE}if(t.ContentComponent&&t.ContentComponent.length>0){if(t.ContentComponent.length>1)return n===A.MUXED;if(t.ContentComponent[0].contentType===n)return!0}const a=n===A.TEXT?new RegExp("(ttml|vtt|wvtt|stpp)"):new RegExp(n);if(t.Representation&&t.Representation.length){const n=t.Representation[0].codecs;if(a.test(n))return!0}if(t.hasOwnProperty(v.MIME_TYPE))return a.test(t.mimeType);if(t.Representation){let n;for(let e=0;e<t.Representation.length;e++)if(n=t.Representation[e],n.hasOwnProperty(v.MIME_TYPE))return a.test(n.mimeType)}return!1}function s(t){if(!t)throw new Error("adaptation is not defined");if(t.hasOwnProperty(v.SEGMENT_TEMPLATE)||t.hasOwnProperty(v.SEGMENT_TIMELINE)||t.hasOwnProperty(v.SEGMENT_LIST)||t.hasOwnProperty(v.SEGMENT_BASE))return!0;if(t.Representation&&t.Representation.length>0){const n=t.Representation[0];if(n.hasOwnProperty(v.SEGMENT_TEMPLATE)||n.hasOwnProperty(v.SEGMENT_TIMELINE)||n.hasOwnProperty(v.SEGMENT_LIST)||n.hasOwnProperty(v.SEGMENT_BASE))return!0}return!1}function u(t){return o(t,A.VIDEO)}function c(t){return o(t,A.TEXT)}function d(t){return o(t,A.IMAGE)}function m(t){return t&&t.Representation&&t.Representation.sort(((t,n)=>t.bandwidth-n.bandwidth)),t}function g(t,n){return t&&t.Period&&i(n)&&t.Period[n]?t.Period[n].AdaptationSet:[]}function f(t){return t&&t.Period?t.Period:[]}function p(t){const n=b(t);return n&&n.length>0}function b(t){let n=[];if(!t)return n;const a=h(t);return n=n.concat(a),t.hasOwnProperty(v.ADAPTATION_SET)&&t[v.ADAPTATION_SET].length>0&&t[v.ADAPTATION_SET].forEach((t=>{const a=h(t);n=n.concat(a)})),n}function h(t){return t&&t.hasOwnProperty(v.CONTENT_PROTECTION)&&0!==t.ContentProtection.length?t[v.CONTENT_PROTECTION].map((t=>{const n=new ut;return n.init(t),n})):[]}function y(t){let n=!1;return t&&t.hasOwnProperty("type")&&(n=t.type===v.DYNAMIC),n}function k(t){if(!t)return null;const n=t[v.FRAMERATE];if(!n)return null;if("string"==typeof n&&n.includes("/")){const[t,a]=n.split("/").map((t=>parseInt(t,10)));if(!isNaN(t)&&!isNaN(a)&&0!==a)return t/a}return parseInt(n)}function w(t,n){return n&&n.hasOwnProperty(t)&&n[t].length?n[t].map((t=>{const n=new it;return n.init(t),n})):[]}function E(t,n){if(!n)return[];let a=function(t,n){if(!n||!n.length)return[];let a=n[0][t]||[];return 0===a.length?[]:1===n.length?a:a.filter((a=>n.slice(1).every((n=>n.hasOwnProperty(t)&&n[t].some((t=>t.schemeIdUri===a.schemeIdUri&&t.value===a.value))))))}(t,n[v.REPRESENTATION]);return n.hasOwnProperty(t)&&n[t].length&&a.push(...n[t]),a.map((t=>{const n=new it;return n.init(t),n}))}function z(t){return w(v.ESSENTIAL_PROPERTY,t)}function x(t){return w(v.SUPPLEMENTAL_PROPERTY,t)}function T(t){if(!t||!t.S)return NaN;let n=t.S[0],a=t.S[1];return n.hasOwnProperty("d")?n.d:a.t-n.t}function _(t){const n=t.presentationTimeOffset;return t.adaptation.period.start-n}function I(t,n){if(!t)throw new Error("Period cannot be null or undefined");let a=wt.DEFAULT_ID+"_"+n;return t.hasOwnProperty(v.ID)&&t.id.toString().length>0&&"__proto__"!==t.id&&(a=t.id.toString()),a}function S(t,n,a){const e=[];let r;if(!t)return e;for(r=0;r<t.length;r++){const l=new gt;if(l.timescale=1,l.representation=n,!t[r].hasOwnProperty(A.SCHEME_ID_URI))throw new Error("Invalid EventStream. SchemeIdUri has to be set");l.schemeIdUri=t[r].schemeIdUri,t[r].hasOwnProperty(v.TIMESCALE)&&(l.timescale=t[r].timescale),t[r].hasOwnProperty(v.VALUE)&&(l.value=t[r].value),e.push(l),l.period=a}return e}function R(t){const n=[],a=t.BaseURL||[t.baseUri];let e=!1;return a.some((a=>{if(a){const r=new lt;let i=a.__text||a;return l.isRelative(i)&&(e=!0,t.baseUri&&(i=l.resolve(i,t.baseUri))),r.url=i,a.hasOwnProperty(v.SERVICE_LOCATION)&&a.serviceLocation.length?r.serviceLocation=a.serviceLocation:r.serviceLocation=i,a.hasOwnProperty(v.DVB_PRIORITY)&&(r.dvbPriority=a[v.DVB_PRIORITY]),a.hasOwnProperty(v.DVB_WEIGHT)&&(r.dvbWeight=a[v.DVB_WEIGHT]),a.hasOwnProperty(v.AVAILABILITY_TIME_OFFSET)&&(r.availabilityTimeOffset=a[v.AVAILABILITY_TIME_OFFSET]),a.hasOwnProperty(v.AVAILABILITY_TIME_COMPLETE)&&(r.availabilityTimeComplete="false"!==a[v.AVAILABILITY_TIME_COMPLETE]),n.push(r),e}})),n}function N(t){const n=new ct;return n.serverUrl=t.__text,t.hasOwnProperty(v.DEFAULT_SERVICE_LOCATION)&&(n.defaultServiceLocation=t[v.DEFAULT_SERVICE_LOCATION],n.defaultServiceLocationArray=n.defaultServiceLocation.split(" ")),t.hasOwnProperty(v.QUERY_BEFORE_START)&&(n.queryBeforeStart="true"===t[v.QUERY_BEFORE_START].toLowerCase()),t.hasOwnProperty(v.CLIENT_REQUIREMENT)&&(n.clientRequirement="false"!==t[v.CLIENT_REQUIREMENT].toLowerCase()),n}function D(t){const n=new st;return t.hasOwnProperty(v.CMCD_PARAMETERS)&&t[v.CMCD_PARAMETERS].schemeIdUri===A.CTA_5004_2023_SCHEME&&(n.cmcdParameters=new ot,n.cmcdParameters.init(t[v.CMCD_PARAMETERS])),t.hasOwnProperty(v.SERVICE_LOCATIONS)&&""!==t[v.SERVICE_LOCATIONS]&&(n.serviceLocations=t[v.SERVICE_LOCATIONS],n.serviceLocationsArray=n.serviceLocations.toString().split(" ")),t.hasOwnProperty(v.ADAPTATION_SETS)&&""!==t[v.ADAPTATION_SETS]&&(n.adaptationSets=t[v.ADAPTATION_SETS],n.adaptationSetsArray=n.adaptationSets.toString().split(" ")),n}return t={getAccessibilityForAdaptation:function(t){return t&&t.hasOwnProperty(v.ACCESSIBILITY)&&t[v.ACCESSIBILITY].length?t[v.ACCESSIBILITY].map((t=>{const n=new it;return n.init(t),n})):[]},getAdaptationForId:function(t,n,a){const e=g(n,a);let r,l;for(r=0,l=e.length;r<l;r++)if(e[r].hasOwnProperty(v.ID)&&e[r].id===t)return e[r];return null},getAdaptationForIndex:function(t,n,a){const e=g(n,a);return e.length>0&&i(t)?e[t]:null},getAdaptationsForPeriod:function(t){const a=t&&i(t.index)?t.mpd.manifest.Period[t.index]:null,e=[];let r,l,s;if(a&&a.AdaptationSet)for(s=0;s<a.AdaptationSet.length;s++)l=a.AdaptationSet[s],r=new et,l.hasOwnProperty(v.ID)&&(r.id=l.id),r.index=s,r.period=t,o(l,A.MUXED)?r.type=A.MUXED:o(l,A.AUDIO)?r.type=A.AUDIO:u(l)?r.type=A.VIDEO:c(l)?r.type=A.TEXT:d(l)?r.type=A.IMAGE:n.warn("Unknown Adaptation stream type"),e.push(r);return e},getAdaptationsForType:function(t,n,a){const e=g(t,n);let r,l;const i=[];for(r=0,l=e.length;r<l;r++)o(e[r],a)&&i.push(m(e[r]));return i},getAudioChannelConfigurationForAdaptation:function(t){return t&&t.hasOwnProperty(v.AUDIO_CHANNEL_CONFIGURATION)&&t[v.AUDIO_CHANNEL_CONFIGURATION].length?t[v.AUDIO_CHANNEL_CONFIGURATION].map((t=>{const n=new it;return n.init(t),n})):[]},getAudioChannelConfigurationForRepresentation:function(t){return t&&t.hasOwnProperty(v.AUDIO_CHANNEL_CONFIGURATION)&&t[v.AUDIO_CHANNEL_CONFIGURATION].length?t[v.AUDIO_CHANNEL_CONFIGURATION].map((t=>{const n=new it;return n.init(t),n})):[]},getAvailabilityStartTime:function(t){return t&&t.hasOwnProperty(v.AVAILABILITY_START_TIME)&&null!==t.availabilityStartTime?t.availabilityStartTime.getTime():null},getBandwidth:function(t){return t&&t.bandwidth?t.bandwidth:NaN},getBaseURLsFromElement:R,getBitrateListForAdaptation:function(t){const n=m(t);return(n&&n.Representation?n.Representation:[]).map((t=>({bandwidth:t.bandwidth,width:t.width||0,height:t.height||0,scanType:t.scanType||null,id:t.id||null})))},getCodec:function(t,n,a){let e=null;if(t&&t.Representation&&t.Representation.length>0){const r=i(n)&&n>=0&&n<t.Representation.length?t.Representation[n]:t.Representation[0];r&&(e=r.mimeType+';codecs="'+r.codecs+'"',a&&void 0!==r.width&&(e+=';width="'+r.width+'";height="'+r.height+'"'))}return e&&(e=e.replace(/\sprofiles=[^;]*/g,"")),e},getCombinedEssentialPropertiesForAdaptationSet:function(t){return E(v.ESSENTIAL_PROPERTY,t)},getCombinedSupplementalPropertiesForAdaptationSet:function(t){return E(v.SUPPLEMENTAL_PROPERTY,t)},getContentProtectionByAdaptation:function(t){return h(t)},getContentProtectionByManifest:function(t){let n=[];if(!t)return n;const a=h(t);return n=n.concat(a),t.hasOwnProperty(v.PERIOD)&&t[v.PERIOD].length>0&&t[v.PERIOD].forEach((t=>{const a=b(t);n=n.concat(a)})),n},getContentProtectionByPeriod:b,getContentSteering:function(t){if(t&&t.hasOwnProperty(v.CONTENT_STEERING))return N(t[v.CONTENT_STEERING][0])},getDuration:function(t){let n;return n=t&&t.hasOwnProperty(v.MEDIA_PRESENTATION_DURATION)?t.mediaPresentationDuration:t&&"dynamic"==t.type?Number.POSITIVE_INFINITY:Number.MAX_SAFE_INTEGER||Number.MAX_VALUE,n},getEssentialPropertiesForAdaptationSet:function(t){return w(v.ESSENTIAL_PROPERTY,t)},getEssentialPropertiesForRepresentation:z,getEventStreamForAdaptationSet:function(t,n,a){let e,r,l;return t&&t.Period&&n&&n.period&&i(n.period.index)&&(r=t.Period[n.period.index],r&&r.AdaptationSet&&i(n.index)&&(l=r.AdaptationSet[n.index],l&&(e=l.InbandEventStream))),S(e,null,a)},getEventStreamForRepresentation:function(t,n,a){let e,r,l,o;return t&&t.Period&&n&&n.adaptation&&n.adaptation.period&&i(n.adaptation.period.index)&&(r=t.Period[n.adaptation.period.index],r&&r.AdaptationSet&&i(n.adaptation.index)&&(l=r.AdaptationSet[n.adaptation.index],l&&l.Representation&&i(n.index)&&(o=l.Representation[n.index],o&&(e=o.InbandEventStream)))),S(e,n,a)},getEventsForPeriod:function(t){const n=t&&t.mpd&&t.mpd.manifest?t.mpd.manifest:null,a=n?n.Period:null,r=a&&t&&i(t.index)?a[t.index].EventStream:null,l=[];let o,s;if(r)for(o=0;o<r.length;o++){const n=new gt;if(n.period=t,n.timescale=1,!r[o].hasOwnProperty(A.SCHEME_ID_URI))throw new Error("Invalid EventStream. SchemeIdUri has to be set");for(n.schemeIdUri=r[o][A.SCHEME_ID_URI],r[o].hasOwnProperty(v.TIMESCALE)&&(n.timescale=r[o][v.TIMESCALE]),r[o].hasOwnProperty(v.VALUE)&&(n.value=r[o][v.VALUE]),r[o].hasOwnProperty(v.PRESENTATION_TIME_OFFSET)&&(n.presentationTimeOffset=r[o][v.PRESENTATION_TIME_OFFSET]),s=0;r[o].Event&&s<r[o].Event.length;s++){const a=r[o].Event[s],i=new mt;i.presentationTime=0,i.eventStream=n,a.hasOwnProperty(v.PRESENTATION_TIME)&&(i.presentationTime=a.presentationTime);const u=n.presentationTimeOffset?n.presentationTimeOffset/n.timescale:0;i.calculatedPresentationTime=i.presentationTime/n.timescale+t.start-u,a.hasOwnProperty(v.DURATION)&&(i.duration=a.duration/n.timescale),a.hasOwnProperty(v.ID)?i.id=parseInt(a.id):i.id=null,a.Signal&&a.Signal.Binary?i.messageData=e.decodeArray(a.Signal.Binary.toString()):i.messageData=a.messageData||a.__cdata||a.__text,l.push(i)}}return l},getFramerate:k,getId:function(t){return t&&t[v.ID]||null},getIndexForAdaptation:function(t,n,a){if(!t)return-1;const e=g(n,a);for(let n=0;n<e.length;n++)if(vt(r).getInstance().areEqual(e[n],t))return n;return-1},getIsDynamic:y,getIsFragmented:s,getIsText:c,getIsTypeOf:o,getLabelsForAdaptation:function(t){if(!t||!t.Label)return[];const n=[];for(let a=0;a<t.Label.length;a++)n.push({lang:t.Label[a].lang,text:t.Label[a].__text||t.Label[a]});return n},getLanguageForAdaptation:function(t){let n="";return t&&t.hasOwnProperty(v.LANG)&&(n=t.lang),n},getLocation:function(t){return t&&t.hasOwnProperty(v.LOCATION)?t[v.LOCATION].map((t=>{const n=t.__text||t,a=t.hasOwnProperty(v.SERVICE_LOCATION)?t[v.SERVICE_LOCATION]:null;return new pt(n,a)})):[]},getManifestUpdatePeriod:function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=NaN;return t&&t.hasOwnProperty(v.MINIMUM_UPDATE_PERIOD)&&(a=t.minimumUpdatePeriod),isNaN(a)?a:Math.max(a-n,1)},getMimeType:function(t){return t&&t.Representation&&t.Representation.length>0?t.Representation[0].mimeType:null},getMpd:function(t){const n=new ft;return t&&(n.manifest=t,t.hasOwnProperty(v.AVAILABILITY_START_TIME)?n.availabilityStartTime=new Date(t.availabilityStartTime.getTime()):t.loadedTime&&(n.availabilityStartTime=new Date(t.loadedTime.getTime())),t.hasOwnProperty(v.AVAILABILITY_END_TIME)&&(n.availabilityEndTime=new Date(t.availabilityEndTime.getTime())),t.hasOwnProperty(v.MINIMUM_UPDATE_PERIOD)&&(n.minimumUpdatePeriod=t.minimumUpdatePeriod),t.hasOwnProperty(v.MEDIA_PRESENTATION_DURATION)&&(n.mediaPresentationDuration=t.mediaPresentationDuration),t.hasOwnProperty(v.SUGGESTED_PRESENTATION_DELAY)&&(n.suggestedPresentationDelay=t.suggestedPresentationDelay),t.hasOwnProperty(v.TIMESHIFT_BUFFER_DEPTH)&&(n.timeShiftBufferDepth=t.timeShiftBufferDepth),t.hasOwnProperty(v.MAX_SEGMENT_DURATION)&&(n.maxSegmentDuration=t.maxSegmentDuration),t.hasOwnProperty(v.PUBLISH_TIME)&&(n.publishTime=new Date(t.publishTime))),n},getPatchLocation:function(t){return t&&t.hasOwnProperty(v.PATCH_LOCATION)?t[v.PATCH_LOCATION].map((t=>{const n=t.__text||t,a=t.hasOwnProperty(v.SERVICE_LOCATION)?t[v.SERVICE_LOCATION]:null;let e=t.hasOwnProperty(v.TTL)?1e3*parseFloat(t[v.TTL]):NaN;return new yt(n,a,e)})):[]},getProducerReferenceTimesForAdaptation:function(t){const n=t&&t.hasOwnProperty(v.PRODUCER_REFERENCE_TIME)?t[v.PRODUCER_REFERENCE_TIME]:[];(t&&t.hasOwnProperty(v.REPRESENTATION)?t[v.REPRESENTATION]:[]).forEach((t=>{t.hasOwnProperty(v.PRODUCER_REFERENCE_TIME)&&n.push(...t[v.PRODUCER_REFERENCE_TIME])}));const a=[];return n.forEach((t=>{const n=new Et;t.hasOwnProperty(v.ID)&&(n[v.ID]=parseInt(t[v.ID]),t.hasOwnProperty(v.WALL_CLOCK_TIME)&&(n[v.WALL_CLOCK_TIME]=t[v.WALL_CLOCK_TIME],t.hasOwnProperty(v.PRESENTATION_TIME)&&(n[v.PRESENTATION_TIME]=t[v.PRESENTATION_TIME],t.hasOwnProperty(v.INBAND)&&(n[v.INBAND]="false"!==t[v.INBAND]),t.hasOwnProperty(v.TYPE)&&(n[v.TYPE]=t[v.TYPE]),a.push(n))))})),a},getPublishTime:function(t){return t&&t.hasOwnProperty(v.PUBLISH_TIME)?new Date(t[v.PUBLISH_TIME]):null},getRealPeriodForIndex:function(t,n){const a=f(n);return a.length>0&&i(t)?a[t]:null},getRealPeriods:f,getRegularPeriods:function(t){const e=!!t&&y(t.manifest),r=[];let l,i,o=null,s=null,u=null,c=null;for(i=0,l=t&&t.manifest&&t.manifest.Period?t.manifest.Period.length:0;i<l;i++)s=t.manifest.Period[i],s.hasOwnProperty(v.START)?(c=new wt,c.start=s.start):null!==o&&o.hasOwnProperty(v.DURATION)&&null!==u?(c=new wt,c.start=parseFloat((u.start+u.duration).toFixed(5))):0!==i||e||(c=new wt,c.start=0),null!==u&&isNaN(u.duration)&&(null!==c?u.duration=parseFloat((c.start-u.start).toFixed(5)):n.warn("First period duration could not be calculated because lack of start and duration period properties. This will cause timing issues during playback")),null!==c&&(c.id=I(s,i),c.index=i,c.mpd=t,c.isEncrypted=p(s),s.hasOwnProperty(v.DURATION)&&(c.duration=s.duration),u&&(u.nextPeriodId=c.id),r.push(c),o=s,u=c),s=null,c=null;return 0===r.length||null!==u&&isNaN(u.duration)&&(u.duration=parseFloat((function(t){!function(){if(!a||!a.hasOwnProperty("error"))throw new Error(A.MISSING_CONFIG_ERROR)}();const n=y(t.mpd.manifest);let e;return t.mpd.manifest.mediaPresentationDuration?e=t.mpd.manifest.mediaPresentationDuration:t.duration?e=t.duration:n?e=Number.POSITIVE_INFINITY:a.error(new nt(dt.MANIFEST_ERROR_ID_PARSE_CODE,"Must have @mediaPresentationDuration on MPD or an explicit @duration on the last period.",t)),e}(u)-u.start).toFixed(5))),r},getRepresentationCount:function(t){return t&&t.Representation?t.Representation.length:0},getRepresentationFor:function(t,n){return n&&n.Representation&&n.Representation.length>0&&i(t)?n.Representation[t]:null},getRepresentationSortFunction:function(){return(t,n)=>t.bandwidth-n.bandwidth},getRepresentationsForAdaptation:function(t,n){const a=[],e=function(t){if(t&&t.period&&i(t.period.index)){const n=t.period.mpd.manifest.Period[t.period.index];if(n&&n.AdaptationSet&&i(t.index))return m(n.AdaptationSet[t.index])}}(t);let r,l;if(e&&e.Representation){if(t&&t.period&&i(t.period.index)){const n=R(t.period.mpd.manifest);n&&(l=n[0])}for(let i=0,o=e.Representation.length;i<o;++i){const o=e.Representation[i],u=new zt;if(u.index=i,u.adaptation=t,u.mediaInfo=n,o.hasOwnProperty(v.ID)&&(u.id=o.id),o.hasOwnProperty(v.CODECS)&&(u.codecs=o.codecs,u.codecFamily=U.getCodecFamily(u.codecs)),o.hasOwnProperty(v.MIME_TYPE)&&(u.mimeType=o[v.MIME_TYPE]),o.hasOwnProperty(v.CODEC_PRIVATE_DATA)&&(u.codecPrivateData=o.codecPrivateData),o.hasOwnProperty(v.BANDWITH)&&(u.bandwidth=o.bandwidth,u.bitrateInKbit=o.bandwidth/1e3),o.hasOwnProperty(v.WIDTH)&&(u.width=o.width),o.hasOwnProperty(v.HEIGHT)&&(u.height=o.height),o.hasOwnProperty(v.SCAN_TYPE)&&(u.scanType=o.scanType),o.hasOwnProperty(v.FRAMERATE)&&(u.frameRate=k(o)),o.hasOwnProperty(v.QUALITY_RANKING)&&(u.qualityRanking=o[v.QUALITY_RANKING]),o.hasOwnProperty(v.MAX_PLAYOUT_RATE)&&(u.maxPlayoutRate=o.maxPlayoutRate),o.hasOwnProperty(v.SEGMENT_BASE)?(r=o.SegmentBase,u.segmentInfoType=v.SEGMENT_BASE):o.hasOwnProperty(v.SEGMENT_LIST)?(r=o.SegmentList,r.hasOwnProperty(v.SEGMENT_TIMELINE)?u.segmentInfoType=v.SEGMENT_TIMELINE:u.segmentInfoType=v.SEGMENT_LIST):o.hasOwnProperty(v.SEGMENT_TEMPLATE)?(r=o.SegmentTemplate,r.hasOwnProperty(v.SEGMENT_TIMELINE)?u.segmentInfoType=v.SEGMENT_TIMELINE:u.segmentInfoType=v.SEGMENT_TEMPLATE,r.hasOwnProperty(v.INITIALIZATION_MINUS)&&(u.initialization=r.initialization.split("$Bandwidth$").join(o.bandwidth).split("$RepresentationID$").join(o.id))):u.segmentInfoType=v.BASE_URL,u.essentialProperties=z(o),u.supplementalProperties=x(o),r){if(r.hasOwnProperty(v.INITIALIZATION)){const t=r.Initialization;t.hasOwnProperty(v.SOURCE_URL)&&(u.initialization=t.sourceURL),t.hasOwnProperty(v.RANGE)&&(u.range=t.range)}else c(e)&&s(e)&&e.mimeType&&-1===e.mimeType.indexOf("application/mp4")&&(u.range=0);r.hasOwnProperty(v.TIMESCALE)&&(u.timescale=r.timescale),r.hasOwnProperty(v.DURATION)?u.segmentDuration=r.duration/u.timescale:o.hasOwnProperty(v.SEGMENT_TEMPLATE)&&(r=o.SegmentTemplate,r.hasOwnProperty(v.SEGMENT_TIMELINE)&&(u.segmentDuration=T(r.SegmentTimeline)/u.timescale)),r.hasOwnProperty(v.MEDIA)&&(u.media=r.media),r.hasOwnProperty(v.START_NUMBER)&&(u.startNumber=parseInt(r.startNumber)),r.hasOwnProperty(v.INDEX_RANGE)&&(u.indexRange=r.indexRange),r.hasOwnProperty(v.PRESENTATION_TIME_OFFSET)&&(u.presentationTimeOffset=r.presentationTimeOffset/u.timescale),r.hasOwnProperty(v.AVAILABILITY_TIME_OFFSET)?u.availabilityTimeOffset=r.availabilityTimeOffset:l&&void 0!==l.availabilityTimeOffset&&(u.availabilityTimeOffset=l.availabilityTimeOffset),r.hasOwnProperty(v.AVAILABILITY_TIME_COMPLETE)?u.availabilityTimeComplete="false"!==r.availabilityTimeComplete:l&&void 0!==l.availabilityTimeComplete&&(u.availabilityTimeComplete=l.availabilityTimeComplete),r.hasOwnProperty(v.END_NUMBER)&&(u.endNumber=r[v.END_NUMBER])}u.mseTimeOffset=_(u),u.path=[t.period.index,t.index,i],isNaN(u.width)||isNaN(u.height)||isNaN(u.frameRate)||(u.pixelsPerSecond=Math.max(1,u.width*u.height*u.frameRate),isNaN(u.bandwidth)||(u.bitsPerPixel=u.bandwidth/u.pixelsPerSecond)),a.push(u)}}return a},getRolesForAdaptation:function(t){return t&&t.hasOwnProperty(v.ROLE)&&t[v.ROLE].length?t[v.ROLE].map((t=>{t.schemeIdUri===A.DASH_ROLE_SCHEME_ID&&"Main"===t.value&&(t.value=v.MAIN);const n=new it;return n.init(t),n})):[]},getSegmentAlignment:function(t){return!(!t||!t.hasOwnProperty(v.SEGMENT_ALIGNMENT))&&"true"===t[v.SEGMENT_ALIGNMENT]},getSelectionPriority:function(t){try{const n=t&&void 0!==t.selectionPriority?parseInt(t.selectionPriority):1;return isNaN(n)?1:n}catch(t){return 1}},getServiceDescriptions:function(t){const n=[];if(t&&t.hasOwnProperty(v.SERVICE_DESCRIPTION))for(const a of t.ServiceDescription){let t=null,e=null,r=null,l=null,i=null,o=null,s=null,u=null;for(const n in a)if(a.hasOwnProperty(n))if(n===v.ID)t=a[n];else if(n===v.SERVICE_DESCRIPTION_SCOPE)e=a[n].schemeIdUri;else if(n===v.SERVICE_DESCRIPTION_LATENCY)r={target:parseInt(a[n].target),max:parseInt(a[n].max),min:parseInt(a[n].min),referenceId:parseInt(a[n].referenceId)};else if(n===v.SERVICE_DESCRIPTION_PLAYBACK_RATE)l={max:parseFloat(a[n].max),min:parseFloat(a[n].min)};else if(n===v.SERVICE_DESCRIPTION_OPERATING_QUALITY)i={mediaType:a[n].mediaType,max:parseInt(a[n].max),min:parseInt(a[n].min),target:parseInt(a[n].target),type:a[n].type,maxQualityDifference:parseInt(a[n].maxQualityDifference)};else if(n===v.SERVICE_DESCRIPTION_OPERATING_BANDWIDTH)o={mediaType:a[n].mediaType,max:parseInt(a[n].max),min:parseInt(a[n].min),target:parseInt(a[n].target)};else if(n===v.CONTENT_STEERING){let t=a[n];t=Array.isArray(t)?t.at(t.length-1):t,s=N(t)}else n===v.CLIENT_DATA_REPORTING&&(u=D(a[n]));n.push({id:t,schemeIdUri:e,latency:r,playbackRate:l,operatingQuality:i,operatingBandwidth:o,contentSteering:s,clientDataReporting:u})}return n},getSubSegmentAlignment:function(t){return!(!t||!t.hasOwnProperty(v.SUB_SEGMENT_ALIGNMENT))&&"true"===t[v.SUB_SEGMENT_ALIGNMENT]},getSuggestedPresentationDelay:function(t){return t&&t.hasOwnProperty(v.SUGGESTED_PRESENTATION_DELAY)?t.suggestedPresentationDelay:null},getSupplementalPropertiesForAdaptationSet:function(t){return w(v.SUPPLEMENTAL_PROPERTY,t)},getSupplementalPropertiesForRepresentation:x,getUTCTimingSources:function(t){const n=y(t),a=!!t&&t.hasOwnProperty(v.AVAILABILITY_START_TIME),e=t?t.UTCTiming:null,r=[];return(n||a)&&e&&e.forEach((function(t){const n=new At;t.hasOwnProperty(A.SCHEME_ID_URI)&&(n.schemeIdUri=t.schemeIdUri,t.hasOwnProperty(v.VALUE)&&(n.value=t.value.toString(),r.push(n)))})),r},getViewpointForAdaptation:function(t){return t&&t.hasOwnProperty(v.VIEWPOINT)&&t[v.VIEWPOINT].length?t[v.VIEWPOINT].map((t=>{const n=new it;return n.init(t),n})):[]},hasProfile:function(t,n){let a=!1;return t&&t.profiles&&t.profiles.length>0&&(a=-1!==t.profiles.indexOf(n)),a},isPeriodEncrypted:p,setConfig:function(t){t&&(t.errHandler&&(a=t.errHandler),t.BASE64&&(e=t.BASE64))}},n=Q(r).getInstance().getLogger(t),t}St.__dashjs_factory_name="DashManifestModel";var Rt=i.getSingletonFactory(St);const Nt={MANIFEST:"m",AUDIO:"a",VIDEO:"v",MUXED:"av",INIT:"i",CAPTION:"c",TIMED_TEXT:"tt",KEY:"k",OTHER:"o"},Dt=Nt,jt="v",Ot="l",qt="d",Ct="s";class Lt{constructor(t,n){Array.isArray(t)&&(t=t.map((t=>t instanceof Lt?t:new Lt(t)))),this.value=t,this.params=n}}const Pt="Dict";function Mt(t,n,a,e){return new Error(`failed to ${t} "${r=n,Array.isArray(r)?JSON.stringify(r):r instanceof Map?"Map{}":r instanceof Set?"Set{}":"object"==typeof r?JSON.stringify(r):String(r)}" as ${a}`,{cause:e});var r}function Ut(t,n,a){return Mt("serialize",t,n,a)}class Ft{constructor(t){this.description=t}}const Bt="Bare Item",Gt="Boolean",Ht="Byte Sequence";const Vt="Integer";function Yt(t){return t<-999999999999999||999999999999999<t}function Xt(t){if(Yt(t))throw Ut(t,Vt);return t.toString()}function Wt(t,n){if(t<0)return-Wt(-t,n);const a=Math.pow(10,n);if(Math.abs(t*a%1-.5)<Number.EPSILON){const n=Math.floor(t*a);return(n%2==0?n:n+1)/a}return Math.round(t*a)/a}const Kt="Decimal";function Qt(t){const n=Wt(t,3);if(Math.floor(Math.abs(n)).toString().length>12)throw Ut(t,Kt);const a=n.toString();return a.includes(".")?a:`${a}.0`}const $t="String",Zt=/[\x00-\x1f\x7f]+/;function Jt(t){return t.description||t.toString().slice(7,-1)}const tn="Token";function nn(t){const n=Jt(t);if(!1===/^([a-zA-Z*])([!#$%&'*+\-.^_`|~\w:/]*)$/.test(n))throw Ut(n,tn);return n}function an(t){switch(typeof t){case"number":if(!Number.isFinite(t))throw Ut(t,Bt);return Number.isInteger(t)?Xt(t):Qt(t);case"string":return function(t){if(Zt.test(t))throw Ut(t,$t);return`"${t.replace(/\\/g,"\\\\").replace(/"/g,'\\"')}"`}(t);case"symbol":return nn(t);case"boolean":return function(t){if("boolean"!=typeof t)throw Ut(t,Gt);return t?"?1":"?0"}(t);case"object":if(t instanceof Date)return function(t){return`@${Xt(t.getTime()/1e3)}`}(t);if(t instanceof Uint8Array)return function(t){if(!1===ArrayBuffer.isView(t))throw Ut(t,Ht);return`:${n=t,btoa(String.fromCharCode(...n))}:`;var n}(t);if(t instanceof Ft)return nn(t);default:throw Ut(t,Bt)}}const en="Key";function rn(t){if(!1===/^[a-z*][a-z0-9\-_.*]*$/.test(t))throw Ut(t,en);return t}function ln(t){return null==t?"":Object.entries(t).map((t=>{let[n,a]=t;return!0===a?`;${rn(n)}`:`;${rn(n)}=${an(a)}`})).join("")}function on(t){return t instanceof Lt?`${an(t.value)}${ln(t.params)}`:an(t)}const sn=t=>Math.round(t),un=t=>100*sn(t/100),cn={br:sn,d:sn,bl:un,dl:un,mtp:un,nor:(t,n)=>((null==n?void 0:n.baseUrl)&&(t=function(t,n){const a=new URL(t),e=new URL(n);if(a.origin!==e.origin)return t;const r=a.pathname.split("/").slice(1),l=e.pathname.split("/").slice(1,-1);for(;r[0]===l[0];)r.shift(),l.shift();for(;l.length;)l.shift(),r.unshift("..");return r.join("/")}(t,n.baseUrl)),encodeURIComponent(t)),rtp:un,tb:sn};function dn(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t?function(t,n){return function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{whitespace:!0};if("object"!=typeof t)throw Ut(t,Pt);const a=t instanceof Map?t.entries():Object.entries(t),e=(null==n?void 0:n.whitespace)?" ":"";return Array.from(a).map((t=>{let[n,a]=t;a instanceof Lt==0&&(a=new Lt(a));let e=rn(n);var r;return!0===a.value?e+=ln(a.params):(e+="=",Array.isArray(a.value)?e+=`(${(r=a).value.map(on).join(" ")})${ln(r.params)}`:e+=on(a)),e})).join(`,${e}`)}(t,n)}(function(t,n){const a={};if(null==t||"object"!=typeof t)return a;const e=Object.keys(t).sort(),r=Object.assign({},cn,null==n?void 0:n.formatters),l=null==n?void 0:n.filter;return e.forEach((e=>{if(null==l?void 0:l(e))return;let i=t[e];const o=r[e];o&&(i=o(i,n)),"v"===e&&1===i||"pr"==e&&1===i||function(t){return"number"==typeof t?Number.isFinite(t):null!=t&&""!==t&&!1!==t}(i)&&(function(t){return"ot"===t||"sf"===t||"st"===t}(e)&&"string"==typeof i&&(i=new Ft(i)),a[e]=i)})),a}(t,n),Object.assign({whitespace:!1},n)):""}const mn="CMCD-Object",gn="CMCD-Request",fn="CMCD-Session",pn="CMCD-Status",bn={[mn]:["br","d","ot","tb"],[gn]:["bl","dl","mtp","nor","nrr","su"],[fn]:["cid","pr","sf","sid","st","v"],[pn]:["bs","rtp"]},hn="segment";function vn(){let t,n,a,e,r,l,i,o,s,u,c,d,m,f,p,b,v=this.context,y=V(v).getInstance(),k=Z(v).getInstance(),w=Q(v).getInstance();function E(){e={pr:1,nor:null,st:null,sf:null,sid:`${U.generateUuid()}`,cid:null},m={},d={},f={},c=void 0,p=void 0,b=!1,_()}function z(){_()}function x(){p||(p=Date.now())}function T(){p&&!e.msd&&(e.msd=Date.now()-p)}function _(){if(!i)return;const t=i.getStreamController();if(!t)return;if("function"!=typeof t.getActiveStream)return;const n=t.getActiveStream();n&&(u=n.getStreamProcessors())}function I(t){try{const n=R(),a=n.version?n.keys:k.get().streaming.cmcd.enabledKeys;return Object.keys(t).filter((t=>a.includes(t))).reduce(((n,a)=>(n[a]=t[a],n)),{})}catch(n){return t}}function S(){const t=R();return function(t){if(Object.keys(t).length){if(1!==parseInt(t.version))return a.error("version parameter must be defined in 1."),!1;if(!t.keys)return a.error("keys parameter must be defined."),!1}const n=t.version,e=k.get().streaming.cmcd&&k.get().streaming.cmcd.enabled;return n||e}(t)&&function(t){let n=k.get().streaming.cmcd.includeInRequests;t.version&&(n=t.includeInRequests??[hn]);const e=A.CMCD_AVAILABLE_REQUESTS,r=n.filter((t=>!e.includes(t)));return r.length===n.length?(a.error("None of the request types are supported."),!1):(r.map((t=>{a.warn(`request type ${t} is not supported.`)})),!0)}(t)&&function(t){const n=A.CMCD_AVAILABLE_KEYS,e=A.CMCD_V2_AVAILABLE_KEYS,r=t.version?t.keys:k.get().streaming.cmcd.enabledKeys,l=k.get().streaming.cmcd.version,i=r.filter((t=>!(n.includes(t)||2===l&&e.includes(t))));return i.length===r.length&&r.length>0?(a.error(`None of the keys are implemented for CMCD version ${l}.`),!1):(i.map((t=>{a.warn(`key parameter ${t} is not implemented for CMCD version ${l}.`)})),!0)}(t)}function R(){let t={};if(o){const n=o.getServiceDescriptionSettings();k.get().streaming.cmcd.applyParametersFromMpd&&n.clientDataReporting&&n.clientDataReporting.cmcdParameters&&(t=n.clientDataReporting.cmcdParameters)}return t}function N(t){try{let a=null;if(t.type,(n=t.mediaType)!==A.VIDEO&&n!==A.AUDIO||c&&c!=A.AUDIO||(c=n),function(t){const n=R();let a=k.get().streaming.cmcd.includeInRequests;n.version&&(a=n.includeInRequests?n.includeInRequests:[hn]);const e={[g.INIT_SEGMENT_TYPE]:"segment",[g.MEDIA_SEGMENT_TYPE]:"segment",[g.XLINK_EXPANSION_TYPE]:"xlink",[g.MPD_TYPE]:"mpd",[g.CONTENT_STEERING_TYPE]:"steering",[g.OTHER_TYPE]:"other"};return a.some((n=>e[t]===n))}(t.type)){if(t.type===g.MPD_TYPE)return function(){const t=O();return t.ot=Dt.MANIFEST,t}();if(t.type===g.MEDIA_SEGMENT_TYPE)return j(t.mediaType),D(t,t.mediaType);if(t.type===g.INIT_SEGMENT_TYPE)return function(){const t=O();return t.ot=Dt.INIT,t.su=!0,t}();if(t.type===g.OTHER_TYPE||t.type===g.XLINK_EXPANSION_TYPE)return function(){const t=O();return t.ot=Dt.OTHER,t}();if(t.type===g.LICENSE)return function(){const t=O();return t.ot=Dt.KEY,t}();if(t.type===g.CONTENT_STEERING_TYPE)return function(t){const n=c?D(t,c):O();return n.ot=Dt.OTHER,n}(t)}return a}catch(t){return null}var n}function D(t,n){j(n);const a=O(),o=function(t){try{return parseInt(t.bandwidth/1e3)}catch(t){return null}}(t),c=function(t){try{return isNaN(t.duration)?NaN:Math.round(1e3*t.duration)}catch(t){return null}}(t),g=function(t){try{return 100*parseInt(s.getSafeAverageThroughput(t)/100)}catch(t){return null}}(n),p=function(t){try{const n=e.pr,a=l.getCurrentBufferLevel(t);return isNaN(n)||isNaN(a)?null:100*parseInt(a/n*10)}catch(t){return null}}(n),b=q(n),h=function(t){try{const n=r.getPossibleVoRepresentationsFilteredBySettings(t).map((t=>t.bitrateInKbit));return Math.max(...n)}catch(t){return null}}(t.representation?.mediaInfo),v=e.pr,y=function(t){if(u&&0!==u.length)for(let n of u)if(n.getType()===t)return n.probeNextRequest()}(n);let w;n===A.VIDEO&&(w=Dt.VIDEO),n===A.AUDIO&&(w=Dt.AUDIO),n===A.TEXT&&(w="application/mp4"===t.representation.mediaInfo.mimeType?Dt.TIMED_TEXT:Dt.CAPTION);let E=k.get().streaming.cmcd.rtp;return E||(E=function(t){try{let n=i.getPlaybackRate();n||(n=1);let{bandwidth:a,mediaType:e,representation:r,duration:l}=t;if(!r.mediaInfo)return NaN;let o=q(e);0===o&&(o=500);let s=a*l/1e3/(o/n/1e3),u=k.get().streaming.cmcd.rtpSafetyFactor&&!isNaN(k.get().streaming.cmcd.rtpSafetyFactor)?k.get().streaming.cmcd.rtpSafetyFactor:5;return 100*(parseInt(s*u/100)+1)}catch(t){return NaN}}(t)),isNaN(E)||(a.rtp=E),y&&(t.url!==y.url?a.nor=encodeURIComponent(U.getRelativeUrl(t.url,y.url)):y.range&&(a.nrr=y.range)),o&&(a.br=o),w&&(a.ot=w),isNaN(c)||(a.d=c),isNaN(g)||(a.mtp=g),isNaN(p)||(a.dl=p),isNaN(b)||(a.bl=b),isNaN(h)||(a.tb=h),isNaN(v)||1===v||(a.pr=v),m[n]&&(a.bs=!0,m[n]=!1),!d[n]&&f[n]||(a.su=!0,d[n]=!1,f[n]=!0),a}function j(t){f.hasOwnProperty(t)||(f[t]=!1),d.hasOwnProperty(t)||(d[t]=!1),m.hasOwnProperty(t)||(m[t]=!1)}function O(){const t=R(),n={};let a=k.get().streaming.cmcd.cid?k.get().streaming.cmcd.cid:e.cid;if(a=t.contentID?t.contentID:a,n.v=k.get().streaming.cmcd.version??1,n.sid=k.get().streaming.cmcd.sid?k.get().streaming.cmcd.sid:e.sid,n.sid=t.sessionID?t.sessionID:n.sid,n.sid=`${n.sid}`,a&&(n.cid=`${a}`),isNaN(e.pr)||1===e.pr||null===e.pr||(n.pr=e.pr),e.st&&(n.st=e.st),e.sf&&(n.sf=e.sf),2===n.v){let t=1e3*i.getCurrentLiveLatency();isNaN(t)||(n.ltc=t);const a=e.msd;b||isNaN(a)||(n.msd=a,b=!0)}return n}function q(t){try{const n=l.getCurrentBufferLevel(t);return isNaN(n)?null:100*parseInt(10*n)}catch(t){return null}}function C(t){try{e.pr=t.playbackRate}catch(t){}}function L(n){try{const a=t.getIsDynamic(n.data)?Ot:jt,r=n.protocol&&"MSS"===n.protocol?Ct:qt;e.st=`${a}`,e.sf=`${r}`}catch(t){}}function P(t){try{t.state&&t.mediaType&&t.state===h.BUFFER_EMPTY&&(m[t.mediaType]||(m[t.mediaType]=!0),d[t.mediaType]||(d[t.mediaType]=!0))}catch(t){}}function M(){for(let t in m)m.hasOwnProperty(t)&&(m[t]=!0);for(let t in d)d.hasOwnProperty(t)&&(d[t]=!0)}return n={getCmcdData:N,getQueryParameter:function(t){try{if(S()){const n=N(t),a=dn(I(n));return y.trigger(at.CMCD_DATA_GENERATED,{url:t.url,mediaType:t.mediaType,cmcdData:n,cmcdString:a}),{key:"CMCD",value:a}}return null}catch(t){return null}},getHeaderParameters:function(t){try{if(S()){const n=N(t),a=function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const a={};if(!t)return a;const e=Object.entries(t),r=Object.entries(bn).concat(Object.entries((null==n?void 0:n.customHeaderMap)||{})),l=e.reduce(((t,n)=>{var a,e;const[l,i]=n,o=(null===(a=r.find((t=>t[1].includes(l))))||void 0===a?void 0:a[0])||gn;return null!==(e=t[o])&&void 0!==e||(t[o]={}),t[o][l]=i,t}),{});return Object.entries(l).reduce(((t,a)=>{let[e,r]=a;return t[e]=dn(r,n),t}),a)}(I(n),1===k.get().streaming.cmcd.version?{}:{customHeaderMap:{[gn]:["ltc"],[fn]:["msd"]}});return y.trigger(at.CMCD_DATA_GENERATED,{url:t.url,mediaType:t.mediaType,cmcdData:n,headers:a}),a}return null}catch(t){return null}},getCmcdParametersFromManifest:R,setConfig:function(t){t&&(t.abrController&&(r=t.abrController),t.dashMetrics&&(l=t.dashMetrics),t.throughputController&&(s=t.throughputController),t.playbackController&&(i=t.playbackController),t.serviceDescriptionController&&(o=t.serviceDescriptionController))},reset:function(){y.off(h.PLAYBACK_RATE_CHANGED,C,this),y.off(h.MANIFEST_LOADED,L,this),y.off(h.BUFFER_LEVEL_STATE_CHANGED,P,n),y.off(h.PLAYBACK_SEEKED,M,n),y.off(h.PLAYBACK_STARTED,x,n),y.off(h.PLAYBACK_PLAYING,T,n),E()},initialize:function(t){y.on(h.PLAYBACK_RATE_CHANGED,C,n),y.on(h.MANIFEST_LOADED,L,n),y.on(h.BUFFER_LEVEL_STATE_CHANGED,P,n),y.on(h.PLAYBACK_SEEKED,M,n),y.on(h.PERIOD_SWITCH_COMPLETED,z,n),t?y.on(h.MANIFEST_LOADING_STARTED,x,n):y.on(h.PLAYBACK_STARTED,x,n),y.on(h.PLAYBACK_PLAYING,T,n)},isCmcdEnabled:S},t=Rt(v).getInstance(),a=w.getLogger(n),E(),n}vn.__dashjs_factory_name="CmcdModel";var yn=i.getSingletonFactory(vn);const kn="CMSD-Dynamic",wn="CMSD-Static",En=Nt;function zn(t,n,a){return Mt("parse",t,n,a)}const xn="List";function Tn(t,n){return{value:t,src:n}}const _n="Inner List";const In="Date",An=`${Vt} or ${Kt}`;function Sn(t){let n,a=1,e="";const r=zn(t,An);if("-"===t[0]&&(a=-1,t=t.substring(1)),t.length<=0)throw r;const l=/^(\d+)?/g,i=l.exec(t);if(0===i[0].length)throw r;if(e+=i[1],"."===(t=t.substring(l.lastIndex))[0]){if(e.length>12)throw r;const l=/^(\.\d+)?/g,i=l.exec(t);if(t=t.substring(l.lastIndex),0===i[0].length||i[1].length>4)throw r;if(e+=i[1],e.length>16)throw r;n=parseFloat(e)*a}else{if(e.length>15)throw r;if(n=parseInt(e)*a,Yt(n))throw zn(e,An)}return Tn(n,t)}function Rn(t,n){const a=t[0];if('"'===a)return function(t){let n="",a=0;if('"'!==t[a])throw zn(t,$t);for(a++;t.length>a;){if("\\"===t[a]){if(t.length<=a+1)throw zn(t,$t);if(a++,'"'!==t[a]&&"\\"!==t[a])throw zn(t,$t);n+=t[a]}else{if('"'===t[a])return Tn(n,t.substring(++a));if(Zt.test(t[a]))throw zn(t,$t);n+=t[a]}a++}throw zn(t,$t)}(t);if(/^[-0-9]/.test(a))return Sn(t);if("?"===a)return function(t){let n=0;if("?"!==t[n])throw zn(t,Gt);if(n++,"1"===t[n])return Tn(!0,t.substring(++n));if("0"===t[n])return Tn(!1,t.substring(++n));throw zn(t,Gt)}(t);if(":"===a)return function(t){if(":"!==t[0])throw zn(t,Ht);if(!1===(t=t.substring(1)).includes(":"))throw zn(t,Ht);const n=/(^.*?)(:)/g,a=n.exec(t)[1];return t=t.substring(n.lastIndex),Tn(new Uint8Array([...atob(a)].map((t=>t.charCodeAt(0)))),t)}(t);if(/^[a-zA-Z*]/.test(a))return function(t,n){if(!1===/^[a-zA-Z*]$/.test(t[0]))throw zn(t,tn);const a=/^([!#$%&'*+\-.^_`|~\w:/]+)/g,e=a.exec(t)[1];return t=t.substring(a.lastIndex),Tn(!1===(null==n?void 0:n.useSymbol)?new Ft(e):Symbol.for(e),t)}(t,n);if("@"===a)return function(t){let n=0;if("@"!==t[n])throw zn(t,In);n++;const a=Sn(t.substring(n));if(!1===Number.isInteger(a.value))throw zn(t,In);return Tn(new Date(1e3*a.value),a.src)}(t);throw zn(t,Bt)}function Nn(t){let n=0;if(!1===/^[a-z*]$/.test(t[n]))throw zn(t,en);let a="";for(;t.length>n;){if(!1===/^[a-z0-9_\-.*]$/.test(t[n]))return Tn(a,t.substring(n));a+=t[n],n++}return Tn(a,t.substring(n))}function Dn(t,n){let a;for(;t.length>0&&";"===t[0];){const e=Nn(t=t.substring(1).trim()),r=e.value;let l=!0;if("="===(t=e.src)[0]){const a=Rn(t=t.substring(1),n);l=a.value,t=a.src}null==a&&(a={}),a[r]=l}return Tn(a,t)}function jn(t,n){const a=Rn(t,n),e=Dn(t=a.src,n);return t=e.src,Tn(new Lt(a.value,e.value),t)}function On(t,n){return"("===t[0]?function(t,n){if("("!==t[0])throw zn(t,_n);t=t.substring(1);const a=[];for(;t.length>0;){if(")"===(t=t.trim())[0]){const e=Dn(t=t.substring(1),n);return Tn(new Lt(a,e.value),e.src)}const e=jn(t,n);if(a.push(e.value)," "!==(t=e.src)[0]&&")"!==t[0])throw zn(t,_n)}throw zn(t,_n)}(t,n):jn(t,n)}const qn=wn.toLowerCase(),Cn=kn.toLowerCase(),Ln=["mb","st","sf","v"],Pn="stream",Mn={video:En.VIDEO,audio:En.AUDIO,text:En.TIMED_TEXT,stream:Pn};function Un(){const t=this.context,n=V(t).getInstance();let a,e,r,l;function i(){r={},l={}}function o(t){t&&Object.keys(t).forEach((n=>{Ln.includes(n)||delete t[n]}))}function s(t){try{return(n=t)?Object.entries(function(t,n){try{const{src:a,value:e}=function(t,n){const a={};for(;t.length>0;){let e;const r=Nn(t),l=r.value;if("="===(t=r.src)[0]){const a=On(t.substring(1),n);e=a.value,t=a.src}else{const a=Dn(t,n);e=new Lt(!0,a.value),t=a.src}if(a[l]=e,0===(t=t.trim()).length)return Tn(a,t);if(","!==t[0])throw zn(t,Pt);if(0===(t=t.substring(1).trim()).length||","===t[0])throw zn(t,Pt)}return Tn(a,t)}(t.trim(),n);if(""!==a)throw zn(a,Pt);return e}catch(n){throw zn(t,Pt,n)}}(n)).reduce(((t,n)=>{let[a,e]=n;const{value:r}=e;return t[a]="symbol"==typeof r?Jt(r):r,t}),{}):{}}catch(t){e.error("Failed to parse CMSD-Static response header value:",t)}var n}function u(t){try{const a=(n=t)?function(t,n){try{const{src:a,value:e}=function(t,n){const a=[];for(;t.length>0;){const e=On(t,n);if(a.push(e.value),0===(t=e.src.trim()).length)return Tn(a,t);if(","!==t[0])throw zn(t,xn);if(0===(t=t.substring(1).trim()).length||","===t[0])throw zn(t,xn)}return Tn(a,t)}(t.trim(),n);if(""!==a)throw zn(a,xn);return e}catch(n){throw zn(t,xn,n)}}(n):[],e=a[a.length-1];return e?.params||{}}catch(t){return e.error("Failed to parse CMSD-Dynamic response header value:",t),{}}var n}function c(t){return Mn[t]||En.OTHER}function d(t,n,a){const e=t===wn?r:l,i=e[n]||{},o=e[Pn]||{};return i[a]||o[a]}return a={setConfig:function(){},initialize:function(){},reset:function(){i()},parseResponseHeaders:function(t,a){let e=null,i=null;for(const a in t){const r=t[a];switch(a){case qn:e=s(r),n.trigger(X.CMSD_STATIC_HEADER,e);break;case Cn:i||(i=u(r))}}let d=Pn;return e&&e.ot?d=e.ot:a&&(d=c(a)),o(r[d]),o(l[d]),e&&(r[d]=Object.assign(r[d]||{},e)),i&&(l[d]=Object.assign(l[d]||{},i)),{static:e,dynamic:i}},getMaxBitrate:function(t){let n=c(t);return d(kn,n,"mb")||-1},getEstimatedThroughput:function(t){let n=c(t);return d(kn,n,"etp")||null},getResponseDelay:function(t){let n=c(t);return d(kn,n,"rd")||null},getRoundTripTime:function(t){let n=c(t);return d(kn,n,"rtt")||null}},e=Q(t).getInstance().getLogger(a),i(),a}Un.__dashjs_factory_name="CmsdModel";var Fn=i.getSingletonFactory(Un);function Bn(t,n){if(typeof t!==n)throw A.BAD_ARGUMENT_ERROR}function Gn(){let t,n,a,e,r,l,i,o,s,u;const c=this.context,d=Z(c).getInstance();function m(){e=[],r=[],l=[],i=[],o=[],u=[],s=null,n=[]}function g(t,n){let a=-1;t.some(((t,e)=>{if(t===n)return a=e,!0})),a<0||t.splice(a,1)}function f(t){let n;for(n=0;n<u.length;n++)if(u[n].rulename===t)return n;return-1}function p(t,a){b(t,a);let e=new At;e.schemeIdUri=t,e.value=a,n.push(e)}function b(t,a){Bn(t,"string"),Bn(a,"string"),n.forEach((function(e,r){e.schemeIdUri===t&&e.value===a&&n.splice(r,1)}))}return t={addAbrCustomRule:function(t,n,a){if("string"!=typeof t||t!==A.RULES_TYPES.ABANDON_FRAGMENT_RULES&&t!==A.RULES_TYPES.QUALITY_SWITCH_RULES||"string"!=typeof n)throw A.BAD_ARGUMENT_ERROR;let e=f(n);-1===e?u.push({type:t,rulename:n,rule:a}):(u[e].type=t,u[e].rule=a)},addRequestInterceptor:function(t){e.push(t)},addResponseInterceptor:function(t){r.push(t)},addUTCTimingSource:p,clearDefaultUTCTimingSources:function(){n=[]},getAbrCustomRules:function(){return u},getCustomCapabilitiesFilters:function(){return o},getCustomInitialTrackSelectionFunction:function(){return s},getLicenseRequestFilters:function(){return l},getLicenseResponseFilters:function(){return i},getRequestInterceptors:function(){return e},getResponseInterceptors:function(){return r},getUTCTimingSources:function(){return n},getXHRWithCredentialsForType:function(t){const n=a[t];return void 0===n?a.default:n},registerCustomCapabilitiesFilter:function(t){o.push(t)},registerLicenseRequestFilter:function(t){l.push(t)},registerLicenseResponseFilter:function(t){i.push(t)},removeAbrCustomRule:function(t){if(t){let n=f(t);-1!==n&&u.splice(n,1)}else u=[]},removeAllAbrCustomRule:function(){u=[]},removeRequestInterceptor:function(t){g(e,t)},removeResponseInterceptor:function(t){g(r,t)},removeUTCTimingSource:b,reset:function(){m()},resetCustomInitialTrackSelectionFunction:function(){s=null},restoreDefaultUTCTimingSources:function(){let t=d.get().streaming.utcSynchronization.defaultTimingSource;p(t.scheme,t.value)},setConfig:function(){},setCustomInitialTrackSelectionFunction:function(t){s=t},setXHRWithCredentialsForType:function t(n,e){n?a[n]=!!e:Object.keys(a).forEach((n=>{t(n,e)}))},unregisterCustomCapabilitiesFilter:function(t){g(o,t)},unregisterLicenseRequestFilter:function(t){g(l,t)},unregisterLicenseResponseFilter:function(t){g(i,t)}},a={default:!1},m(),t}Gn.__dashjs_factory_name="CustomParametersModel";var Hn=i.getSingletonFactory(Gn);function Vn(){let t,n;function a(){n={}}return t={reset:function(){a()},processResponseHeaders:function(t){if(!(t&&t.headers&&t.request&&t.request.url))return;const a=t.headers[A.COMMON_ACCESS_TOKEN_HEADER];if(a){const e=U.getHostFromUrl(t.request.url);e&&(n[e]=a)}},getCommonAccessTokenForUrl:function(t){if(!t)return null;const a=U.getHostFromUrl(t);return a?n[a]?n[a]:null:void 0}},a(),t}Vn.__dashjs_factory_name="CommonAccessTokenController";var Yn=i.getSingletonFactory(Vn);function Xn(){let t,n;return t={setConfig:function(t){t&&t.serviceDescriptionController&&(n=t.serviceDescriptionController)},isAdaptationsIncluded:function(t){const{adaptationSetsArray:a}=n?.getServiceDescriptionSettings()?.clientDataReporting??{};return!a||0===a?.length||a.includes(t)},isServiceLocationIncluded:function(t,a){if(t===g.CONTENT_STEERING_TYPE)return!0;const{serviceLocationsArray:e}=n?.getServiceDescriptionSettings()?.clientDataReporting??{};return!e||0===e?.length||e.includes(a)}},t}Xn.__dashjs_factory_name="ClientDataReportingController";var Wn=i.getSingletonFactory(Xn);function Kn(){let t,n,a;const e=this.context;function r(t,n,a,e,r){const l=function(t,n){let a=[];return a=n===v.PERIOD?t[v.SUPPLEMENTAL_PROPERTY]||[]:[...t[v.ESSENTIAL_PROPERTY]||[],...t[v.SUPPLEMENTAL_PROPERTY]||[]],a.find((t=>t.schemeIdUri===A.URL_QUERY_INFO_SCHEME&&t.UrlQueryInfo||t.schemeIdUri===A.EXT_URL_QUERY_INFO_SCHEME&&t.ExtUrlQueryInfo))}(n,r);!function(t,n,a,e){a.initialQueryString="";let r="";const l=t?.ExtUrlQueryInfo||t?.UrlQueryInfo;r=l&&l.queryString?n&&n.length>0?n+"&"+l.queryString:l.queryString:n,"true"===l?.useMPDUrlQuery&&e&&(r=r?r+"&"+e:e),a.initialQueryString=r}(l,e.initialQueryString,t,a),function(t,n,a){if(!t)return void(n.finalQueryString=a);const e=t?.ExtUrlQueryInfo?.queryTemplate||t?.UrlQueryInfo?.queryTemplate||"";n.finalQueryString=e===v.QUERY_PART?n?.initialQueryString:""}(l,t,e.finalQueryString),t.sameOriginOnly=l?.ExtUrlQueryInfo?.sameOriginOnly,t.queryParams=U.parseQueryParams(t?.finalQueryString),t.includeInRequests=function(t,n){return t?t.ExtUrlQueryInfo?.includeInRequests?t.ExtUrlQueryInfo.includeInRequests.split(" "):[v.SEGMENT_TYPE]:n}(l,e.includeInRequests)}return n=Q(e).getInstance().getLogger(t),t={getFinalQueryString:function(t){try{if(!a)return null;if(t.type===g.MEDIA_SEGMENT_TYPE||t.type===g.INIT_SEGMENT_TYPE){const n=t.representation;if(!n)return null;const e=n.adaptation,r=e.period,l=a.period[r.index].adaptation[e.index].representation[n.index],i=new URL(t.url),o=!l.sameOriginOnly||a.origin===i.origin;if(l.includeInRequests.includes(v.SEGMENT_TYPE)&&o)return l.queryParams}else if(t.type===g.MPD_TYPE){if([v.MPD_TYPE,v.MPD_PATCH_TYPE].some((t=>a.includeInRequests.includes(t))))return a.queryParams}else if(t.type===g.CONTENT_STEERING_TYPE&&a.includeInRequests.includes(v.STEERING_TYPE))return a.queryParams}catch(t){return n.error(t),null}},createFinalQueryStrings:function(t){a={origin:new URL(t.url).origin,period:[]};const n=t.url.split("?")[1];r(a,t,n,{initialQueryString:"",includeInRequests:[]},v.MPD),t.Period.forEach((t=>{const e={adaptation:[]};r(e,t,n,a,v.PERIOD),t.AdaptationSet.forEach((t=>{const a={representation:[]};r(a,t,n,e,v.ADAPTATION_SET),t.Representation.forEach((t=>{const e={};r(e,t,n,a,v.REPRESENTATION),a.representation.push(e)})),e.adaptation.push(a)})),a.period.push(e)}))}},t}Kn.__dashjs_factory_name="ExtUrlQueryInfoController";var Qn=i.getSingletonFactory(Kn),$n=class{constructor(t){this.url=t.url,this.method=t.method,this.responseType=void 0!==t.responseType?t.responseType:null,this.headers=void 0!==t.headers?t.headers:{},this.credentials=void 0!==t.credentials?t.credentials:null,this.mode=void 0!==t.mode?t.mode:null,this.timeout=void 0!==t.timeout?t.timeout:0,this.cmcd=void 0!==t.cmcd?t.cmcd:null,this.customData=void 0!==t.customData?t.customData:null}},Zn=class{constructor(t){this.request=t.request,this.url=void 0!==t.url?t.url:null,this.redirected=void 0!==t.redirected&&t.redirected,this.status=void 0!==t.status?t.status:null,this.statusText=void 0!==t.statusText?t.statusText:"",this.type=void 0!==t.type?t.type:"",this.headers=void 0!==t.headers?t.headers:{},this.data=void 0!==t.data?t.data:null,this.resourceTiming=void 0!==t.resourceTiming?t.resourceTiming:null}};function Jn(t){t=t||{};const n=this.context,a=t.errHandler,e=t.dashMetrics,r=t.mediaPlayerModel,l=t.boxParser,i=t.errors,o=t.requestTimeout||0,s=V(n).getInstance(),u=Z(n).getInstance();let c,d,m,f,p,b,h,v,y,k,w,E,z,x;function T(t,i){const c=function(){R()},_=function(n){const a=new Date;L&&(L=!1,(!n.lengthComputable||n.lengthComputable&&n.total!==n.loaded)&&(q.firstByteDate=a,V.resourceTiming.responseStart=a.getTime())),n.lengthComputable&&(q.bytesLoaded=V.length=n.loaded,q.bytesTotal=V.resourceTiming.encodedBodySize=n.total,V.length=n.total,V.resourceTiming.encodedBodySize=n.loaded),n.noTrace||(C.push({s:M,d:n.time?n.time:a.getTime()-M.getTime(),b:[n.loaded?n.loaded-F:0],t:n.throughput}),q.traces=C,M=a,F=n.loaded),G&&(clearTimeout(G),G=null),u.get().streaming.fragmentRequestProgressTimeout>0&&(G=setTimeout((function(){x.warn("Abort request "+H.url+" due to progress timeout"),W.abort(H),c()}),u.get().streaming.fragmentRequestProgressTimeout)),t.progress&&n&&t.progress(n)},I=function(){R(!0)},S=function(t){let n;if(t.lengthComputable){let a=t.loaded/t.total*100;n="Request timeout: loaded: "+t.loaded+", out of: "+t.total+" : "+a.toFixed(3)+"% Completed"}else n="Request timeout: non-computable download size";x.warn(n)},R=function(){let n=arguments.length>0&&void 0!==arguments[0]&&arguments[0];-1!==d.indexOf(H)&&d.splice(d.indexOf(H),1),G&&(clearTimeout(G),G=null),w.processResponseHeaders(V),N(),D(),function(t){const n=k.getResponseInterceptors();return n?n.reduce(((t,n)=>t.then((t=>n(t)))),Promise.resolve(t)):Promise.resolve(t)}(V).then((a=>{if(V=a,function(t,n,a){const r=t.customData.request,l=u.get().streaming.cmsd&&u.get().streaming.cmsd.enabled?h.parseResponseHeaders(n.headers,r.mediaType):null;e.addHttpRequest(r,n.url,n.status,n.headers,a,l)}(H,V,C),n)t.abort&&t.abort(q);else if(q.type===g.MPD_TYPE&&(e.addManifestUpdate(q),s.trigger(X.MANIFEST_LOADING_FINISHED,{requestObject:q})),V.status>=200&&V.status<=299&&V.data)t.success&&t.success(V.data,V.statusText,V.url),t.complete&&t.complete(q,V.statusText);else{try{404===V.status&&u.get().streaming.utcSynchronization.enableBackgroundSyncAfterSegmentDownloadError&&q.type===g.MEDIA_SEGMENT_TYPE&&r.getRetryAttemptsForType(g.MEDIA_SEGMENT_TYPE)===i&&s.trigger(X.ATTEMPT_BACKGROUND_SYNC)}catch(t){}O()}}))},N=function(){q.startDate=P,q.endDate=new Date,q.firstByteDate=q.firstByteDate||P},D=function(){V.resourceTiming.responseEnd=Date.now(),function(t,n){if(!u.get().streaming.abr.throughput.useResourceTimingApi)return;if("undefined"==typeof performance||t.range)return;const a=performance.getEntriesByType("resource");if(void 0===a||a.length<=0)return;let e=0,r=null;for(;e<a.length;){if(a[e].name===t.url){r=a[e];break}e+=1}(function(t){return t&&!isNaN(t.responseStart)&&t.responseStart>0&&!isNaN(t.responseEnd)&&t.responseEnd>0&&!isNaN(t.transferSize)&&t.transferSize>0})(r)&&(t.customData.request.resourceTimingValues=r,n.resourceTiming.startTime=r.startTime,n.resourceTiming.encodedBodySize=r.encodedBodySize,n.resourceTiming.responseStart=r.startTime,n.resourceTiming.responseEnd=r.responseEnd,n.resourceTiming.duration=r.duration)}(H,V)},j=function(t,n,a){return new Promise((e=>{(function(t){const n=k.getRequestInterceptors();return n?n.reduce(((t,n)=>t.then((t=>n(t)))),Promise.resolve(t)):Promise.resolve(t)})(n).then((r=>{(n=r).customData.onloadend=c,n.customData.onprogress=_,n.customData.onabort=I,n.customData.ontimeout=S,a.resourceTiming.startTime=Date.now(),t.load(n,a),e()}))}))},O=function(){if(i>0){i--,t&&t.request&&(t.request.retryAttempts+=1);let n={config:t};f.push(n),n.timeout=setTimeout((function(){-1!==f.indexOf(n)&&(f.splice(f.indexOf(n),1),T(t,i))}),r.getRetryIntervalsForType(q.type))}else{if(q.type===g.MSS_FRAGMENT_INFO_SEGMENT_TYPE)return;a.error(new nt(p[q.type],q.url+" is not available",{request:q,response:V})),t.error&&t.error(q,"error",V.statusText,V),t.complete&&t.complete(q,V.statusText)}},q=t.request,C=[];let L,P,M,F,G,H,V;if(q.bytesLoaded=NaN,q.bytesTotal=NaN,q.firstByteDate=null,q.traces=[],L=!0,P=new Date,M=P,F=0,G=null,!e||!a)throw new Error("config object is not correct or missing");const Y=function(t){let a,r;return t.hasOwnProperty("availabilityTimeComplete")&&!1===t.availabilityTimeComplete&&window.fetch&&"arraybuffer"===t.responseType&&t.type===g.MEDIA_SEGMENT_TYPE?(y||(y=tt(n).create(),y.setConfig({dashMetrics:e,boxParser:l})),a=y,r=A.FILE_LOADER_TYPES.FETCH):(v||(v=B(n).create()),a=v,r=A.FILE_LOADER_TYPES.XHR),{loader:a,fileLoaderType:r}}(q),W=Y.loader;var K;q.fileLoaderType=Y.fileLoaderType,q.headers={},function(t){const n=t?.serviceLocation,a=t?.mediaInfo?.id?.toString();if(E.isServiceLocationIncluded(t.type,n)&&E.isAdaptationsIncluded(a)&&b.isCmcdEnabled()){const n=b.getCmcdParametersFromManifest(),a=n.mode?n.mode:u.get().streaming.cmcd.mode;if(a===A.CMCD_MODE_QUERY){t.url=U.removeQueryParameterFromUrl(t.url,A.CMCD_QUERY_KEY);const n=function(t){try{const n=[],a=b.getQueryParameter(t);return a&&n.push(a),n}catch(t){return[]}}(t);t.url=U.addAdditionalQueryParameterToUrl(t.url,n)}else a===A.CMCD_MODE_HEADER&&(t.headers=Object.assign(t.headers,b.getHeaderParameters(t)))}}(K=q),0===K.retryAttempts&&function(t){let n=z.getFinalQueryString(t);n&&(t.url=U.addAdditionalQueryParameterToUrl(t.url,n))}(K),function(t){if(t.queryParams){const n=Object.keys(t.queryParams).map((n=>({key:n,value:t.queryParams[n]})));t.url=U.addAdditionalQueryParameterToUrl(t.url,n)}}(K),function(t){const n=w.getCommonAccessTokenForUrl(t.url);n&&(t.headers[A.COMMON_ACCESS_TOKEN_HEADER]=n)}(K),q.range&&(q.headers.Range="bytes="+q.range);const Q=k.getXHRWithCredentialsForType(q.type);H=new $n({url:q.url,method:g.GET,responseType:q.responseType,headers:q.headers,credentials:Q?"include":"omit",timeout:o,cmcd:b.getCmcdData(q),customData:{request:q}}),V=new Zn({request:H,resourceTiming:{startTime:Date.now(),encodedBodySize:0},status:0});let $=(new Date).getTime();if(isNaN(q.delayLoadingTime)||$>=q.delayLoadingTime)return d.push(H),j(W,H,V);{let t={httpRequest:H,httpResponse:V};return m.push(t),t.delayTimeout=setTimeout((function(){if(-1!==m.indexOf(t)){m.splice(m.indexOf(t),1);try{P=new Date,M=P,d.push(t.httpRequest),j(W,t.httpRequest,t.httpResponse)}catch(n){t.httpRequest.onloadend()}}}),q.delayLoadingTime-$),Promise.resolve()}}return c={abort:function(){f.forEach((t=>{clearTimeout(t.timeout),t.config.request&&t.config.abort&&t.config.abort(t.config.request)})),f=[],m.forEach((t=>clearTimeout(t.delayTimeout))),m=[],d.forEach((t=>{const n=t.customData;n&&(n.request&&n.request.type===g.MSS_FRAGMENT_INFO_SEGMENT_TYPE||(n.onloadend=n.onprogress=void 0,n.abort&&n.abort()))})),d=[]},load:function(t){return t.request?T(t,r.getRetryAttemptsForType(t.request.type)):(t.error&&t.error(t.request,"error"),Promise.resolve())},reset:function(){d=[],m=[],f=[],v&&v.reset(),y&&y.reset(),v=null,y=null},resetInitialSettings:function(){v&&v.resetInitialSettings()},setConfig:function(t){t&&(t.commonAccessTokenController&&(w=t.commonAccessTokenController),t.extUrlQueryInfoController&&(z=t.extUrlQueryInfoController))}},x=Q(n).getInstance().getLogger(c),d=[],m=[],f=[],b=yn(n).getInstance(),E=Wn(n).getInstance(),h=Fn(n).getInstance(),k=Hn(n).getInstance(),w=Yn(n).getInstance(),z=Qn(n).getInstance(),p={[g.MPD_TYPE]:i.DOWNLOAD_ERROR_ID_MANIFEST_CODE,[g.XLINK_EXPANSION_TYPE]:i.DOWNLOAD_ERROR_ID_XLINK_CODE,[g.INIT_SEGMENT_TYPE]:i.DOWNLOAD_ERROR_ID_INITIALIZATION_CODE,[g.MEDIA_SEGMENT_TYPE]:i.DOWNLOAD_ERROR_ID_CONTENT_CODE,[g.INDEX_SEGMENT_TYPE]:i.DOWNLOAD_ERROR_ID_CONTENT_CODE,[g.BITSTREAM_SWITCHING_SEGMENT_TYPE]:i.DOWNLOAD_ERROR_ID_CONTENT_CODE,[g.OTHER_TYPE]:i.DOWNLOAD_ERROR_ID_CONTENT_CODE},c}Jn.__dashjs_factory_name="HTTPLoader";var ta=i.getClassFactory(Jn);function na(){let t,n;function a(){n={}}function e(){a()}return e(),t={getLoader:function(t){for(var a in n)if(n.hasOwnProperty(a)&&t.startsWith(a))return n[a];return ta},registerLoader:function(t,a){n[t]=a},unregisterLoader:function(t){n[t]&&delete n[t]},unregisterAllLoader:a,reset:e},t}na.__dashjs_factory_name="SchemeLoaderFactory";var aa=i.getSingletonFactory(na);function ea(t){t=t||{};const n=this.context;let a,e,r;return e=aa(n).getInstance(),a={abort:function(){r&&r.abort()},load:function(a){if(!r){let l=e.getLoader(a&&a.request?a.request.url:null);r=l(n).create({errHandler:t.errHandler,mediaPlayerModel:t.mediaPlayerModel,dashMetrics:t.dashMetrics,boxParser:t.boxParser?t.boxParser:null,constants:t.constants?t.constants:null,dashConstants:t.dashConstants?t.dashConstants:null,urlUtils:t.urlUtils?t.urlUtils:null,requestTimeout:isNaN(t.requestTimeout)?0:t.requestTimeout,errors:t.errors})}r.load(a)},reset:function(){e&&(e.reset(),e=null),r&&"function"==typeof r.reset&&r.reset(),r=null},resetInitialSettings:function(){r&&"function"==typeof r.resetInitialSettings&&r.resetInitialSettings()}},a}ea.__dashjs_factory_name="URLLoader";var ra=i.getClassFactory(ea),la=class extends p{constructor(t){super(t),this.checkForExistenceOnly=!0}};function ia(t){t=t||{};const n=this.context,a=t.eventBus,e=t.events,r=t.urlUtils,l=t.errors,i=t.streamId;let o,s;return o={abort:function(){s&&s.abort()},checkForExistence:function(t){const n=function(n){a.trigger(e.CHECK_FOR_EXISTENCE_COMPLETED,{request:t,exists:n})};if(t){let a=new la(t.url);s.load({request:a,success:function(){n(!0)},error:function(){n(!1)}})}else n(!1)},load:function(t){const n=function(n,r){a.trigger(e.LOADING_COMPLETED,{request:t,response:n||null,error:r||null,sender:o})};t?s.load({request:t,progress:function(n){a.trigger(e.LOADING_PROGRESS,{request:t,stream:n.stream,streamId:i}),n.data&&a.trigger(e.LOADING_DATA_PROGRESS,{request:t,response:n.data||null,error:null,sender:o})},success:function(t){n(t)},error:function(t,a,e){n(void 0,new nt(l.FRAGMENT_LOADER_LOADING_FAILURE_ERROR_CODE,e,a))},abort:function(t){t&&a.trigger(e.LOADING_ABANDONED,{mediaType:t.mediaType,request:t,sender:o})}}):n(void 0,new nt(l.FRAGMENT_LOADER_NULL_REQUEST_ERROR_CODE,l.FRAGMENT_LOADER_NULL_REQUEST_ERROR_MESSAGE))},reset:function(){s&&(s.abort(),s.reset(),s=null)},resetInitialSettings:function(){s&&s.resetInitialSettings()}},s=ra(n).create({errHandler:t.errHandler,errors:l,dashMetrics:t.dashMetrics,mediaPlayerModel:t.mediaPlayerModel,urlUtils:r,constants:A,boxParser:t.boxParser,dashConstants:t.dashConstants,requestTimeout:t.settings.get().streaming.fragmentRequestTimeout}),o}ia.__dashjs_factory_name="FragmentLoader";var oa=i.getClassFactory(ia);function sa(t,n){const a=(t=t||{}).timelineConverter,e=t.dashMetrics;let r;function l(){if(!a)throw new Error(A.MISSING_CONFIG_ERROR)}function i(t,n){const a=t.adaptation.period.mpd.manifest.Period[t.adaptation.period.index].AdaptationSet[t.adaptation.index].Representation[t.index].SegmentTemplate||t.adaptation.period.mpd.manifest.Period[t.adaptation.period.index].AdaptationSet[t.adaptation.index].Representation[t.index].SegmentList,e=a.SegmentTimeline,r=a.SegmentURL;let l,i,s,u,c,d,m,g=0,f=-1;m=t.timescale,l=e.S;let p=!1;for(s=0,u=l.length;s<u&&!p;s++)for(i=l[s],d=0,i.hasOwnProperty("r")&&(d=i.r),i.hasOwnProperty("t")&&(g=i.t),d<0&&(d=o(t,l[s+1],i,m,g/m)),c=0;c<=d&&!p;c++)f++,p=n(g,a,r,i,m,f,s),p&&(t.segmentDuration=i.d/m),g+=i.d}function o(t,n,r,l,i){let o;if(n&&n.hasOwnProperty("t"))o=n.t/l;else try{let n=0;if(isNaN(t.adaptation.period.start)||isNaN(t.adaptation.period.duration)||!isFinite(t.adaptation.period.duration)){const t=e.getCurrentDVRInfo();n=isNaN(t.end)?0:t.end}else n=t.adaptation.period.start+t.adaptation.period.duration;o=a.calcMediaTimeFromPresentationTime(n,t),t.segmentDuration=r.d/l}catch(t){o=0}return Math.max(Math.ceil((o-i)/(r.d/l))-1,0)}return r={getSegmentByIndex:function(t,e,r){if(l(),!t)return null;let o=null,s=!1;return i(t,(function(e,l,i,u,c,d,m){if(s||r<0){let r=l.media,s=u.mediaRange;return i&&(r=i[m].media||"",s=i[m].mediaRange),o=T(a,n,t,e,u.d,c,r,s,d,u.tManifest),!0}return e>=r*c-.5*u.d&&(s=!0),!1})),o},getSegmentByTime:function(t,e){if(l(),!t)return null;void 0===e&&(e=null);let r=null;const o=a.calcMediaTimeFromPresentationTime(e,t);return i(t,(function(e,l,i,s,u,c,d){const m=parseFloat((o*u).toPrecision(15));if(m<e+s.d&&m>=e){let o=l.media,m=s.mediaRange;return i&&(o=i[d].media||"",m=i[d].mediaRange),r=T(a,n,t,e,s.d,u,o,m,c,s.tManifest),!0}return!1})),r},getMediaFinishedInformation:function(t){if(!t)return 0;const n=(t.adaptation.period.mpd.manifest.Period[t.adaptation.period.index].AdaptationSet[t.adaptation.index].Representation[t.index].SegmentTemplate||t.adaptation.period.mpd.manifest.Period[t.adaptation.period.index].AdaptationSet[t.adaptation.index].Representation[t.index].SegmentList).SegmentTimeline;let a,e,r,l,i,s,u,c=0,d=0,m=0;for(u=t.timescale,a=n.S,l=a.length,r=0;r<l;r++)for(e=a[r],s=0,e.hasOwnProperty("r")&&(s=e.r),e.hasOwnProperty("t")&&(c=e.t,d=c/u),s<0&&(s=o(t,a[r+1],e,u,d)),i=0;i<=s;i++)m++,c+=e.d,d=c/u;return{numberOfSegments:m,mediaTimeOfLastSignaledSegment:d}}},r}sa.__dashjs_factory_name="TimelineSegmentsGetter";var ua=i.getClassFactory(sa);function ca(t,n){const a=(t=t||{}).timelineConverter;let e;function r(){if(!a||!a.hasOwnProperty("calcPeriodRelativeTimeFromMpdRelativeTime"))throw new Error(A.MISSING_CONFIG_ERROR)}function l(t,e){if(r(),!t)return null;const l=t.adaptation.period.mpd.manifest.Period[t.adaptation.period.index].AdaptationSet[t.adaptation.index].Representation[t.index].SegmentTemplate;e=Math.max(e,0);const i=x(a,n,t,e);if(i){if(t.endNumber&&i.replacementNumber>t.endNumber)return null;i.replacementTime=Math.round(e*t.segmentDuration*t.timescale,10),i.media=z(l.media,void 0,i.replacementNumber,void 0,void 0,i.replacementTime)}return i}return e={getSegmentByIndex:l,getSegmentByTime:function(t,n){if(r(),!t)return null;const e=t.segmentDuration;if(isNaN(e))return null;let i=a.calcPeriodRelativeTimeFromMpdRelativeTime(t,n);return l(t,Math.floor(i/e))},getMediaFinishedInformation:function(t){const n={numberOfSegments:0,mediaTimeOfLastSignaledSegment:NaN};if(!t)return n;const a=t.segmentDuration;return isNaN(a)?n.numberOfSegments=1:n.numberOfSegments=Math.ceil(t.adaptation.period.duration/a),n}},e}ca.__dashjs_factory_name="TemplateSegmentsGetter";var da=i.getClassFactory(ca);function ma(t,n){const a=(t=t||{}).timelineConverter;let e;function r(){if(!a||!a.hasOwnProperty("calcPeriodRelativeTimeFromMpdRelativeTime"))throw new Error(A.MISSING_CONFIG_ERROR)}function l(t,e){if(r(),!t)return null;const l=t.adaptation.period.mpd.manifest.Period[t.adaptation.period.index].AdaptationSet[t.adaptation.index].Representation[t.index].SegmentList,i=l.SegmentURL.length,o=t&&!isNaN(t.startNumber)?t.startNumber:1,s=Math.max(o-1,0),u=Math.max(e-s,0);let c=null;if(u<i){const r=l.SegmentURL[u];c=x(a,n,t,e),c&&(c.replacementTime=(o+e-1)*t.segmentDuration,c.media=r.media?r.media:"",c.mediaRange=r.mediaRange,c.indexRange=r.indexRange)}return c}return e={getSegmentByIndex:l,getSegmentByTime:function(t,n){if(r(),!t)return null;const e=t.segmentDuration;if(isNaN(e))return null;const i=a.calcPeriodRelativeTimeFromMpdRelativeTime(t,n);return l(t,Math.floor(i/e))},getMediaFinishedInformation:function(t){const n={numberOfSegments:0,mediaTimeOfLastSignaledSegment:NaN};if(!t)return n;const a=t.adaptation.period.mpd.manifest.Period[t.adaptation.period.index].AdaptationSet[t.adaptation.index].Representation[t.index].SegmentList,e=t&&!isNaN(t.startNumber)?t.startNumber:1,r=Math.max(e-1,0);return n.numberOfSegments=r+a.SegmentURL.length,n}},e}ma.__dashjs_factory_name="ListSegmentsGetter";var ga=i.getClassFactory(ma);function fa(t){const n=(t=t||{}).timelineConverter;let a;function e(){if(!n||!n.hasOwnProperty("calcPeriodRelativeTimeFromMpdRelativeTime"))throw new Error(A.MISSING_CONFIG_ERROR)}function r(t,n){if(e(),!t)return null;const a=t.segments?t.segments.length:-1;let r;if(n<a&&(r=t.segments[n],r&&r.index===n))return r;for(let e=0;e<a;e++)if(r=t.segments[e],r&&r.index===n)return r;return null}return a={getSegmentByIndex:r,getSegmentByTime:function(t,n){e();const a=function(t,n){if(!t)return-1;const a=t.segments,e=a?a.length:null;let r,l,i,o,s,u=-1;if(a&&e>0)for(s=0;s<e;s++)if(l=a[s],i=l.presentationStartTime,o=l.duration,r=o/2,n+r>=i&&n-r<i+o){u=l.index;break}return u}(t,n);return r(t,a)},getMediaFinishedInformation:function(t){const n={numberOfSegments:0,mediaTimeOfLastSignaledSegment:NaN};return t&&t.segments?(n.numberOfSegments=t.segments.length,n):n}},a}fa.__dashjs_factory_name="SegmentBaseGetter";var pa=i.getClassFactory(fa);function ba(t){t=t||{};const n=this.context,a=t.dashConstants,e=t.type,r=t.segmentBaseController;let l,i;function o(t){return t?t.segments?i[a.SEGMENT_BASE]:i[t.segmentInfoType]:null}return l={initialize:function(e){i[a.SEGMENT_TIMELINE]=ua(n).create(t,e),i[a.SEGMENT_TEMPLATE]=da(n).create(t,e),i[a.SEGMENT_LIST]=ga(n).create(t,e),i[a.SEGMENT_BASE]=pa(n).create(t,e)},updateInitData:function(t,n){return n?Promise.resolve():r.getSegmentBaseInitSegment({representation:t,mediaType:e})},updateSegmentData:function(t,n){return n?Promise.resolve():r.getSegmentList({mimeType:t.mimeType,representation:t,mediaType:e})},getSegmentByIndex:function(t,n,a){const e=o(t);return e?e.getSegmentByIndex(t,n,a):null},getSegmentByTime:function(t,n){const a=o(t);return a?a.getSegmentByTime(t,n):null},getMediaFinishedInformation:function(t){const n=o(t);return n?n.getMediaFinishedInformation(t):{numberOfSegments:0,mediaTimeOfLastSignaledSegment:NaN}}},i={},l}ba.__dashjs_factory_name="SegmentsController";var ha=i.getClassFactory(ba);function va(t){t=t||{};const n=this.context,a=t.eventBus,e=t.events,r=t.errors,l=t.debug,i=t.constants,o=t.settings,s=t.dashConstants,u=t.id,c=t.type,d=t.streamInfo,m=t.errHandler,g=t.mediaPlayerModel,f=t.abrController,p=t.playbackController,b=t.adapter,h=t.dashMetrics,v=t.baseURLController,y=t.timelineConverter,k=t.bitrate,w=t.offlineStoreController,E=t.callbacks&&t.callbacks.completed,z=t.callbacks&&t.callbacks.progression;let x,T,_,A,S,N,D,j,O,q,L;function P(t){if(t.sender===N){if(null!==t.request){let n="InitializationSegment"===t.request.type,a=n?"init":t.request.index,e=t.request.representationId+"_"+a;w.storeFragment(u,e,t.response).then((()=>{n||w.setRepresentationCurrentState(u,t.request.representationId,{index:t.request.index,downloaded:j})}))}t.error&&t.request.serviceLocation&&!L?N.executeRequest(t.request):(j++,F())}}function M(t){t.fragmentModel===N&&(T.info(`[${u}] Stream is complete`),U(),E())}function U(){L||(L=!0)}function F(){if(!L&&isNaN(S.getCurrentRepresentation())){let t=null;O?(t=A.getNextSegmentRequest(B(),S.getCurrentRepresentation()),z&&z(x,j,G())):(t=S.getCurrentRepresentation()?A.getInitRequest(B(),S.getCurrentRepresentation()):null,O=!0),t?(T.info(`[${u}] download request : ${t.url}`),N.executeRequest(t)):T.info(`[${u}] no request to be downloaded`)}}function B(){return _}function G(){return S.getCurrentRepresentation().numberOfSegments+1}function H(){O=!1,j=0,D=!1}return x={initialize:function(t){_=t,A.initialize(!1),function(t){D=!0;let n=b.getVoRepresentations(t),a=n.find((t=>t.id===k.id));c===i.VIDEO||c===i.AUDIO||c===i.TEXT?S.updateData(n,t.isFragmented,a.id):D=!1}(_)},getMediaInfo:B,getRepresentationController:function(){return S},removeExecutedRequestsBeforeTime:function(t){N&&N.removeExecutedRequestsBeforeTime(t)},getType:function(){return c},getRepresentationId:function(){return S.getCurrentRepresentation().id},isUpdating:function(){return D},start:function(){if(S){if(!S.getCurrentRepresentation())throw new Error("Start denied to OfflineStreamProcessor");L=!1,w.getRepresentationCurrentState(u,S.getCurrentRepresentation().id).then((t=>{t&&(A.setCurrentIndex(t.index),j=t.downloaded),F()})).catch((()=>{F()}))}},stop:U,getAvailableSegmentsNumber:G,reset:function(){H(),A.reset(),a.off(e.STREAM_REQUESTING_COMPLETED,M,x),a.off(e.FRAGMENT_LOADING_COMPLETED,P,x)}},H(),T=l.getLogger(x),q=ha(n).create({events:e,eventBus:a,streamInfo:d,timelineConverter:y,dashConstants:s,segmentBaseController:t.segmentBaseController,type:c}),A=I(n).create({streamInfo:d,type:c,timelineConverter:y,dashMetrics:h,mediaPlayerModel:g,baseURLController:v,errHandler:m,settings:o,eventBus:a,events:e,debug:l,dashConstants:s,constants:i,segmentsController:q,urlUtils:It(n).getInstance()}),S=R(n).create({streamInfo:d,type:c,abrController:f,dashMetrics:h,playbackController:p,timelineConverter:y,dashConstants:s,events:e,eventBus:a,errors:r,adapter:b,segmentsController:q}),N=C(n).create({streamInfo:d,dashMetrics:h,fragmentLoader:oa(n).create({dashMetrics:h,mediaPlayerModel:g,errHandler:m,settings:o,eventBus:a,events:e,errors:r,constants:i,dashConstants:s,urlUtils:It(n).getInstance()}),debug:l,eventBus:a,events:e}),a.on(e.STREAM_REQUESTING_COMPLETED,M,x),a.on(e.FRAGMENT_LOADING_COMPLETED,P,x),x}va.__dashjs_factory_name="OfflineStreamProcessor";var ya=dashjs.FactoryMaker.getClassFactory(va);function ka(t){t=t||{};const n=this.context,a=t.eventBus,e=t.events,r=t.errors,l=t.constants,i=t.dashConstants,o=t.settings,s=t.debug,u=t.errHandler,c=t.mediaPlayerModel,d=t.abrController,m=t.playbackController,g=t.adapter,f=t.dashMetrics,p=t.baseURLController,b=t.timelineConverter,h=t.segmentBaseController,v=t.offlineStoreController,y=t.id,k=t.callbacks&&t.callbacks.started,w=t.callbacks&&t.callbacks.progression,E=t.callbacks&&t.callbacks.finished,z=t.callbacks&&t.callbacks.updateManifestNeeded;let x,T,_,I,A,S,R,N;function D(){A=null,T=[],_=0,I=0,R=[],S=[],N={}}function j(t,n){let a=g.getAllMediaInfoForType(n,t);return a.forEach((n=>{n.bitrateList=n.bitrateList.filter((n=>!(!R[t]||-1===R[t].indexOf(n.id))))})),a=a.filter((t=>t.bitrateList&&t.bitrateList.length>0)),a.forEach((t=>{t.bitrateList&&t.bitrateList.forEach((n=>{O(t,n)}))})),a}function O(t,k){let w=ya(n).create({id:y,streamInfo:A,debug:s,events:e,errors:r,eventBus:a,constants:l,dashConstants:i,settings:o,type:t.type,mimeType:t.mimeType,bitrate:k,errHandler:u,mediaPlayerModel:c,abrController:d,playbackController:m,adapter:g,dashMetrics:f,baseURLController:p,timelineConverter:b,offlineStoreController:v,segmentBaseController:h,callbacks:{completed:q,progression:C}});T.push(w),w.initialize(t),N[k.id]=null}function q(){I++,I===T.length&&E({sender:this,id:y,message:"Downloading has been successfully completed for this stream !"})}function C(t,n,a){N[t.getRepresentationId()]={downloadedSegments:n,availableSegments:a};let e,r=0,l=0;for(var i in N)N.hasOwnProperty(i)&&(null===N[i]?e=!0:(r+=N[i].downloadedSegments,l+=N[i].availableSegments));!e&&w&&l>0&&w(x,r,l)}function L(t){let n;t.currentRepresentation.segments&&t.currentRepresentation.segments.length>0&&S.push(t.currentRepresentation);for(let a=0;a<T.length;a++)if(T[a].getRepresentationController().getType()===t.mediaType){n=T[a];break}n&&function(){_++,_===T.length&&(k({sender:this,id:y,message:"Downloading started for this stream !"}),S.length>0?z({sender:this,id:y,representations:S}):M())}()}function P(){for(let t=0;t<T.length;t++)T[t].stop()}function M(){for(let t=0;t<T.length;t++)T[t].start()}return x={initialize:function(t){A=t,a.on(e.DATA_UPDATE_COMPLETED,L,x)},getStreamId:function(){return A.id},getMediaInfos:function(){let t=g.getAllMediaInfoForType(A,l.VIDEO);t=t.concat(g.getAllMediaInfoForType(A,l.AUDIO)),t=t.concat(g.getAllMediaInfoForType(A,l.TEXT)),a.trigger(e.OFFLINE_RECORD_LOADEDMETADATA,{id:y,mediaInfos:t})},initializeAllMediasInfoList:function(t){R=t,function(t){j(l.VIDEO,t),j(l.AUDIO,t),j(l.TEXT,t),j(l.MUXED,t),j(l.IMAGE,t)}(A)},getStreamInfo:function(){return A},stopOfflineStreamProcessors:P,startOfflineStreamProcessors:M,reset:function(){P(),function(){let t=T?T.length:0;for(let n=0;n<t;n++)T[n].removeExecutedRequestsBeforeTime((A?A.start:NaN)+(A?A.duration:NaN)),T[n].reset()}(),D(),a.off(e.DATA_UPDATE_COMPLETED,L,x)}},D(),x}ka.__dashjs_factory_name="OfflineStream";var wa=i.getClassFactory(ka);function Ea(t){const n=t.manifestId,a=t.allMediaInfos,e=t.urlUtils,l=t.debug,i=t.dashConstants,o=t.constants;let s,u,c;function d(t){let a,r,l,o=`offline_indexeddb://${n}/`;if(a=t.getElementsByTagName(i.BASE_URL),0===a.length){let n=u.createElement(i.BASE_URL);n.innerHTML=o,t.appendChild(n)}a=t.getElementsByTagName(i.BASE_URL);for(let t=0;t<a.length;t++){let n=a[t].parentNode;if(n.nodeName===i.MPD)a[t].innerHTML=o;else if(n.nodeName===i.REPRESENTATION){let s=n.parentNode;s.nodeName==i.ADAPTATION_SET&&(e.isHTTPS(a[t].innerHTML)||e.isHTTPURL(a[t].innerHTML)?(r=T(a[t].innerHTML),l=x(s),a[t].innerHTML=o+l+"_"+r):"./"===a[t].innerHTML?a[t].innerHTML=o:(r=T(a[t].innerHTML),l=x(s),a[t].innerHTML=l+"_"+r))}else a[t].innerHTML=o}}function m(t,n){let a=t.getElementsByTagName(i.PERIOD);for(let t=0;t<a.length;t++)g(a[t],n)}function g(t,n){let a,e,r,l;a=t.getElementsByTagName(i.ADAPTATION_SET);for(let o=a.length-1;o>=0;o--)if(e=a[o],e)if(r=f(e),l=h(e),z(e,l,r),l=h(e),y(e),0===l.length)t.removeChild(e);else{for(let t=0;t<l.length;t++){let n=l[t],a=n.getElementsByTagName(i.SEGMENT_LIST);a.length>=1&&w(a,n)}let t=v(e);if(t.length>=1&&k(t),n&&n.length>0){let t;for(let a=0;a<l.length;a++){let e=l[a];for(let a=0;n&&a<n.length;a++)if(n[a].id===e.id){t=n[a];break}}E(e,t)}}}function f(t){return p(t,o.MUXED)?o.MUXED:p(t,o.AUDIO)?o.AUDIO:function(t){return p(t,o.VIDEO)}(t)?o.VIDEO:function(t){return p(t,o.TEXT)}(t)?o.TEXT:function(t){return p(t,o.IMAGE)}(t)?o.IMAGE:null}function p(t,n){if(!t)throw new Error("adaptation is not defined");if(!n)throw new Error("type is not defined");return function(t,n){let a=n!==o.TEXT?new RegExp(n):new RegExp("(vtt|ttml)"),e=b(t);if(e)return a.test(e);let r=h(t);if(r)for(let t=0;t<r.length;t++)if(e=b(r[t]),e)return a.test(e);return!1}(t,n)}function b(t){return t.getAttribute(i.MIME_TYPE)}function h(t){return t.getElementsByTagName(i.REPRESENTATION)}function v(t){return t.getElementsByTagName(i.SEGMENT_TEMPLATE)}function y(t){let n=t.getElementsByTagName(i.SEGMENT_BASE);for(let t=0;t<n.length;t++){let a=n[t];a.parentNode.removeChild(a)}}function k(t){for(let n=0;n<t.length;n++){let a=t[n].getAttribute(i.MEDIA);a="$RepresentationID$_$Number$"+a.substring(a.indexOf("."),a.length),t[n].setAttribute(i.START_NUMBER,"0"),t[n].setAttribute(i.MEDIA,a),t[n].setAttribute(i.INITIALIZATION_MINUS,"$RepresentationID$_init")}}function w(t,n){let a=n.getAttribute(i.ID);for(let n=0;n<t.length;n++){let e=t[n],r=e.getElementsByTagName(i.INITIALIZATION);if(r){let t=r[0].getAttribute(i.SOURCE_URL);t=`${a}_init`,r[0].setAttribute(i.SOURCE_URL,t)}let l=e.getElementsByTagName(i.SEGMENT_URL);if(l)for(let t=0;t<l.length;t++){let n=l[t],e=n.getAttribute(i.MEDIA);e=`${a}_${t}`,n.setAttribute(i.MEDIA,e)}}}function E(t,n){let a=u.createElement(i.SEGMENT_TEMPLATE);a.setAttribute(i.START_NUMBER,"0"),a.setAttribute(i.MEDIA,"$RepresentationID$-$Time$"),a.setAttribute(i.INITIALIZATION_MINUS,"$RepresentationID$_init"),function(t,n){let a=u.createElement("S");if(n&&n.segments){let e=u.createElement(i.SEGMENT_TIMELINE),r=function(t){let n=[];n.push(0);for(let a=1;a<t.segments.length;a++)t.segments[a-1].duration!==t.segments[a].duration&&n.push(a);return n}(n);for(let t=0;t<r.length;t++){let l=t+1<r.length?r[t+1]-r[t]-1:0;l>1&&a.setAttribute("r",l),a.setAttribute("d",n.segments[r[t]].duration),e.appendChild(a),a=u.createElement("S")}t.appendChild(e)}}(a,n),t.appendChild(a)}function z(t,n,e){for(var r=n.length-1;r>=0;r--){let l=n[r],o=l.getAttribute(i.ID);a[e]&&-1===a[e].indexOf(o)&&t.removeChild(l)}}function x(t){let n=t.getElementsByTagName(i.REPRESENTATION)[0];return console.log(n.getAttribute(i.ID)),n.getAttribute(i.ID)}function T(t){let n=t.lastIndexOf("/");return t.substring(n,t.length)}return c=l.getLogger(s),s={parse:function(t,n){return new Promise((function(a,e){u=(new DOMParser).parseFromString(t,"application/xml");let l=u.getElementsByTagName(i.MPD)?u.getElementsByTagName(i.MPD):null;for(let t=0;t<l.length;t++)null!==l[t]&&(d(l[t]),m(l[t],n));let o=function(t){return c.info("encodedManifest "+(new XMLSerializer).serializeToString(t)),(new r.XmlEntities).encode((new XMLSerializer).serializeToString(t))}(u);""!==o?a(o):e("Encoded error")}))}},s}Ea.__dashjs_factory_name="OfflineIndexDBManifestParser";var za=i.getClassFactory(Ea);function xa(t){function n(t,n){for(let a in t)n.hasOwnProperty(a)||(n[a]=t[a])}function a(t,a,e){for(let r=0,l=t.length;r<l;++r){const l=t[r];if(a[l.name])if(e[l.name]){if(l.merge){const t=a[l.name],r=e[l.name];"object"==typeof t&&"object"==typeof r?n(t,r):e[l.name]=t+r}}else e[l.name]=a[l.name]}}function e(t,n){for(let r=0,l=t.children.length;r<l;++r){const l=t.children[r],i=n[l.name];if(i)for(let r=0,o=i.length;r<o;++r){const o=i[r];a(t.properties,n,o),e(l,o)}}}return{run:function(n){if(null===n||"object"!=typeof n)return n;if(n.Period&&"period"in t){const a=t.period,r=n.Period;for(let n=0,l=r.length;n<l;++n){const l=r[n];if(e(a,l),"adaptationset"in t){const n=l.AdaptationSet;if(n){const a=t.adaptationset;for(let t=0,r=n.length;t<r;++t)e(a,n[t])}}}}return n}}}xa.__dashjs_factory_name="ObjectIron";var Ta=i.getClassFactory(xa),_a=class{constructor(t,n){this._test=t,this._converter=n}get test(){return this._test}get converter(){return this._converter}};const Ia=/^([-])?P(([\d.]*)Y)?(([\d.]*)M)?(([\d.]*)D)?T?(([\d.]*)H)?(([\d.]*)M)?(([\d.]*)S)?/;var Aa=class extends _a{constructor(){super(((t,n,a)=>{const e=[v.MIN_BUFFER_TIME,v.MEDIA_PRESENTATION_DURATION,v.MINIMUM_UPDATE_PERIOD,v.TIMESHIFT_BUFFER_DEPTH,v.MAX_SEGMENT_DURATION,v.MAX_SUBSEGMENT_DURATION,v.SUGGESTED_PRESENTATION_DELAY,v.START,A.START_TIME,v.DURATION],r=e.length;for(let t=0;t<r;t++)if(n===e[t])return Ia.test(a);return!1}),(t=>{const n=Ia.exec(t);let a=31536e3*parseFloat(n[3]||0)+2592e3*parseFloat(n[5]||0)+86400*parseFloat(n[7]||0)+3600*parseFloat(n[9]||0)+60*parseFloat(n[11]||0)+parseFloat(n[13]||0);return void 0!==n[1]&&(a=-a),a}))}};const Sa=/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2})(?::([0-9]*)(\.[0-9]*)?)?(?:([+-])([0-9]{2})(?::?)([0-9]{2}))?/;var Ra=class extends _a{constructor(){super(((t,n,a)=>Sa.test(a)),(t=>{const n=Sa.exec(t);let a;if(a=Date.UTC(parseInt(n[1],10),parseInt(n[2],10)-1,parseInt(n[3],10),parseInt(n[4],10),parseInt(n[5],10),n[6]&&parseInt(n[6],10)||0,n[7]&&1e3*parseFloat(n[7])||0),n[9]&&n[10]){const t=60*parseInt(n[9],10)+parseInt(n[10],10);a+=("+"===n[8]?-1:1)*t*60*1e3}return new Date(a)}))}};const Na=/^[-+]?[0-9]+[.]?[0-9]*([eE][-+]?[0-9]+)?$/,Da=[v.ID];var ja=class extends _a{constructor(){super(((t,n,a)=>Na.test(a)&&-1===Da.indexOf(n)),(t=>parseFloat(t)))}};function Oa(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=[];if(t.irregular)return t.irregular;if(t.regular)return t.regular;if(t.language){n=n.concat(t.language,t.extendedLanguageSubtags||[],t.script||[],t.region||[],t.variants||[]);const a=t.extensions||[];let e=-1;for(;++e<a.length;){const t=a[e];t.singleton&&t.extensions&&t.extensions.length>0&&n.push(t.singleton,...t.extensions)}}return t.privateuse&&t.privateuse.length>0&&n.push("x",...t.privateuse),n.join("-")}function qa(t){const n="string"==typeof t?t.charCodeAt(0):t;return n>=97&&n<=122||n>=65&&n<=90}function Ca(t){const n="string"==typeof t?t.charCodeAt(0):t;return n>=48&&n<=57}function La(t){return qa(t)||Ca(t)}const Pa=["art-lojban","cel-gaulish","no-bok","no-nyn","zh-guoyu","zh-hakka","zh-min","zh-min-nan","zh-xiang"],Ma={"en-gb-oed":"en-GB-oxendict","i-ami":"ami","i-bnn":"bnn","i-default":null,"i-enochian":null,"i-hak":"hak","i-klingon":"tlh","i-lux":"lb","i-mingo":null,"i-navajo":"nv","i-pwn":"pwn","i-tao":"tao","i-tay":"tay","i-tsu":"tsu","sgn-be-fr":"sfb","sgn-be-nl":"vgt","sgn-ch-de":"sgg","art-lojban":"jbo","cel-gaulish":null,"no-bok":"nb","no-nyn":"nn","zh-guoyu":"cmn","zh-hakka":"hak","zh-min":null,"zh-min-nan":"nan","zh-xiang":"hsn"},Ua={}.hasOwnProperty;function Fa(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const a={language:null,extendedLanguageSubtags:[],script:null,region:null,variants:[],extensions:[],privateuse:[],irregular:null,regular:null},e=String(t),r=e.toLowerCase();let l=0;if(null==t)throw new Error("Expected string, got `"+t+"`");if(Ua.call(Ma,r)){const t=Ma[r];return void 0!==n.normalize&&null!==n.normalize&&!n.normalize||"string"!=typeof t?(a[Pa.includes(r)?"regular":"irregular"]=e,a):Fa(t)}for(;qa(r.charCodeAt(l))&&l<9;)l++;if(l>1&&l<9){if(a.language=e.slice(0,l),l<4){let t=0;for(;45===r.charCodeAt(l)&&qa(r.charCodeAt(l+1))&&qa(r.charCodeAt(l+2))&&qa(r.charCodeAt(l+3))&&!qa(r.charCodeAt(l+4));){if(t>2)return i(l,3,"Too many extended language subtags, expected at most 3 subtags");a.extendedLanguageSubtags.push(e.slice(l+1,l+4)),l+=4,t++}}for(45===r.charCodeAt(l)&&qa(r.charCodeAt(l+1))&&qa(r.charCodeAt(l+2))&&qa(r.charCodeAt(l+3))&&qa(r.charCodeAt(l+4))&&!qa(r.charCodeAt(l+5))&&(a.script=e.slice(l+1,l+5),l+=5),45===r.charCodeAt(l)&&(qa(r.charCodeAt(l+1))&&qa(r.charCodeAt(l+2))&&!qa(r.charCodeAt(l+3))?(a.region=e.slice(l+1,l+3),l+=3):Ca(r.charCodeAt(l+1))&&Ca(r.charCodeAt(l+2))&&Ca(r.charCodeAt(l+3))&&!Ca(r.charCodeAt(l+4))&&(a.region=e.slice(l+1,l+4),l+=4));45===r.charCodeAt(l);){const t=l+1;let n=t;for(;La(r.charCodeAt(n));){if(n-t>7)return i(n,1,"Too long variant, expected at most 8 characters");n++}if(!(n-t>4||n-t>3&&Ca(r.charCodeAt(t))))break;a.variants.push(e.slice(t,n)),l=n}for(;45===r.charCodeAt(l)&&120!==r.charCodeAt(l+1)&&La(r.charCodeAt(l+1))&&45===r.charCodeAt(l+2)&&La(r.charCodeAt(l+3));){let t=l+2,n=0;for(;45===r.charCodeAt(t)&&La(r.charCodeAt(t+1))&&La(r.charCodeAt(t+2));){const a=t+1;for(t=a+2,n++;La(r.charCodeAt(t));){if(t-a>7)return i(t,2,"Too long extension, expected at most 8 characters");t++}}if(!n)return i(t,4,"Empty extension, extensions must have at least 2 characters of content");a.extensions.push({singleton:e.charAt(l+1),extensions:e.slice(l+3,t).split("-")}),l=t}}else l=0;if(0===l&&120===r.charCodeAt(l)||45===r.charCodeAt(l)&&120===r.charCodeAt(l+1)){l=l?l+2:1;let t=l;for(;45===r.charCodeAt(t)&&La(r.charCodeAt(t+1));){const n=l+1;for(t=n;La(r.charCodeAt(t));){if(t-n>7)return i(t,5,"Too long private-use area, expected at most 8 characters");t++}a.privateuse.push(e.slice(l+1,t)),l=t}}return l!==e.length?i(l,6,"Found superfluous content after tag"):a;function i(t,e,r){return n.warning&&n.warning(r,e,t),n.forgiving?a:{language:null,extendedLanguageSubtags:[],script:null,region:null,variants:[],extensions:[],privateuse:[],irregular:null,regular:null}}}function Ba(t,n){return function(a,e){let r=Ha(a,"tag");const l=Ha(null==e?"*":e,"range"),i=[];let o=-1;for(;++o<l.length;){const a=l[o].toLowerCase();if(!n&&"*"===a)continue;let e=-1;const s=[];for(;++e<r.length;)if(t(r[e].toLowerCase(),a)){if(!n)return r[e];i.push(r[e])}else s.push(r[e]);r=s}return n?i:void 0}}Ba((function(t,n){return"*"===n||t===n||t.includes(n+"-")}),!0);const Ga=Ba((function(t,n){const a=t.split("-"),e=n.split("-");let r=0,l=0;if("*"!==e[l]&&a[r]!==e[l])return!1;for(r++,l++;l<e.length;)if("*"!==e[l]){if(!a[r])return!1;if(a[r]!==e[l]){if(1===a[r].length)return!1;r++}else r++,l++}else l++;return!0}),!0);function Ha(t,n){const a=t&&"string"==typeof t?[t]:t;if(!a||"object"!=typeof a||!("length"in a))throw new Error("Invalid "+n+" `"+a+"`, expected non-empty string");return a}Ba((function(t,n){let a=n;for(;;){if("*"===a||t===a)return!0;let n=a.lastIndexOf("-");if(n<0)return!1;"-"===a.charAt(n-2)&&(n-=2),a=a.slice(0,n)}}),!1);const Va=[{from:"in",to:"id"},{from:"iw",to:"he"},{from:"ji",to:"yi"},{from:"jw",to:"jv"},{from:"mo",to:"ro"},{from:"scc",to:"sr"},{from:"scr",to:"hr"},{from:"aam",to:"aas"},{from:"adp",to:"dz"},{from:"aue",to:"ktz"},{from:"ayx",to:"nun"},{from:"bgm",to:"bcg"},{from:"bjd",to:"drl"},{from:"ccq",to:"rki"},{from:"cjr",to:"mom"},{from:"cka",to:"cmr"},{from:"cmk",to:"xch"},{from:"coy",to:"pij"},{from:"cqu",to:"quh"},{from:"drh",to:"mn"},{from:"drw",to:"fa-af"},{from:"gav",to:"dev"},{from:"gfx",to:"vaj"},{from:"ggn",to:"gvr"},{from:"gti",to:"nyc"},{from:"guv",to:"duz"},{from:"hrr",to:"jal"},{from:"ibi",to:"opa"},{from:"ilw",to:"gal"},{from:"jeg",to:"oyb"},{from:"kgc",to:"tdf"},{from:"kgh",to:"kml"},{from:"koj",to:"kwv"},{from:"krm",to:"bmf"},{from:"ktr",to:"dtp"},{from:"kvs",to:"gdj"},{from:"kwq",to:"yam"},{from:"kxe",to:"tvd"},{from:"kzj",to:"dtp"},{from:"kzt",to:"dtp"},{from:"lii",to:"raq"},{from:"lmm",to:"rmx"},{from:"meg",to:"cir"},{from:"mst",to:"mry"},{from:"mwj",to:"vaj"},{from:"myt",to:"mry"},{from:"nad",to:"xny"},{from:"ncp",to:"kdz"},{from:"nnx",to:"ngv"},{from:"nts",to:"pij"},{from:"oun",to:"vaj"},{from:"pcr",to:"adx"},{from:"pmc",to:"huw"},{from:"pmu",to:"phr"},{from:"ppa",to:"bfy"},{from:"ppr",to:"lcq"},{from:"pry",to:"prt"},{from:"puz",to:"pub"},{from:"sca",to:"hle"},{from:"skk",to:"oyb"},{from:"tdu",to:"dtp"},{from:"thc",to:"tpo"},{from:"thx",to:"oyb"},{from:"tie",to:"ras"},{from:"tkk",to:"twm"},{from:"tlw",to:"weo"},{from:"tmp",to:"tyj"},{from:"tne",to:"kak"},{from:"tnf",to:"fa-af"},{from:"tsf",to:"taj"},{from:"uok",to:"ema"},{from:"xba",to:"cax"},{from:"xia",to:"acn"},{from:"xkh",to:"waw"},{from:"xsj",to:"suj"},{from:"ybd",to:"rki"},{from:"yma",to:"lrr"},{from:"ymt",to:"mtm"},{from:"yos",to:"zom"},{from:"yuu",to:"yug"},{from:"asd",to:"snz"},{from:"dit",to:"dif"},{from:"llo",to:"ngt"},{from:"myd",to:"aog"},{from:"nns",to:"nbr"},{from:"agp",to:"apf"},{from:"ais",to:"ami"},{from:"ajt",to:"aeb"},{from:"baz",to:"nvo"},{from:"bhk",to:"fbl"},{from:"bic",to:"bir"},{from:"bjq",to:"bzc"},{from:"bkb",to:"ebk"},{from:"blg",to:"iba"},{from:"btb",to:"beb"},{from:"daf",to:"dnj"},{from:"dap",to:"njz"},{from:"djl",to:"dze"},{from:"dkl",to:"aqd"},{from:"drr",to:"kzk"},{from:"dud",to:"uth"},{from:"duj",to:"dwu"},{from:"dwl",to:"dbt"},{from:"elp",to:"amq"},{from:"gbc",to:"wny"},{from:"ggo",to:"esg"},{from:"ggr",to:"gtu"},{from:"gio",to:"aou"},{from:"gli",to:"kzk"},{from:"ill",to:"ilm"},{from:"izi",to:"eza"},{from:"jar",to:"jgk"},{from:"kdv",to:"zkd"},{from:"kgd",to:"ncq"},{from:"kpp",to:"jkm"},{from:"kxl",to:"kru"},{from:"kzh",to:"dgl"},{from:"lak",to:"ksp"},{from:"leg",to:"enl"},{from:"mgx",to:"jbk"},{from:"mnt",to:"wnn"},{from:"mof",to:"xnt"},{from:"mwd",to:"dmw"},{from:"nbf",to:"nru"},{from:"nbx",to:"ekc"},{from:"nln",to:"azd"},{from:"nlr",to:"nrk"},{from:"noo",to:"dtd"},{from:"nxu",to:"bpp"},{from:"pat",to:"kxr"},{from:"rmr",to:"emx"},{from:"sap",to:"aqt"},{from:"sgl",to:"isk"},{from:"smd",to:"kmb"},{from:"snb",to:"iba"},{from:"sul",to:"sgd"},{from:"sum",to:"ulw"},{from:"tgg",to:"bjp"},{from:"thw",to:"ola"},{from:"tid",to:"itd"},{from:"unp",to:"wro"},{from:"wgw",to:"wgb"},{from:"wit",to:"nol"},{from:"wiw",to:"nwo"},{from:"xrq",to:"dmw"},{from:"yen",to:"ynq"},{from:"yiy",to:"yrm"},{from:"zir",to:"scv"},{from:"sgn-br",to:"bzs"},{from:"sgn-co",to:"csn"},{from:"sgn-de",to:"gsg"},{from:"sgn-dk",to:"dsl"},{from:"sgn-fr",to:"fsl"},{from:"sgn-gb",to:"bfi"},{from:"sgn-gr",to:"gss"},{from:"sgn-ie",to:"isg"},{from:"sgn-it",to:"ise"},{from:"sgn-jp",to:"jsl"},{from:"sgn-mx",to:"mfs"},{from:"sgn-ni",to:"ncs"},{from:"sgn-nl",to:"dse"},{from:"sgn-no",to:"nsi"},{from:"sgn-pt",to:"psr"},{from:"sgn-se",to:"swl"},{from:"sgn-us",to:"ase"},{from:"sgn-za",to:"sfs"},{from:"sgn-es",to:"ssp"},{from:"zh-cmn",to:"zh"},{from:"zh-cmn-hans",to:"zh-hans"},{from:"zh-cmn-hant",to:"zh-hant"},{from:"zh-gan",to:"gan"},{from:"zh-wuu",to:"wuu"},{from:"zh-yue",to:"yue"},{from:"no-bokmal",to:"nb"},{from:"no-nynorsk",to:"nn"},{from:"aa-saaho",to:"ssy"},{from:"sh",to:"sr-latn"},{from:"cnr",to:"sr-me"},{from:"tl",to:"fil"},{from:"aju",to:"jrb"},{from:"als",to:"sq"},{from:"arb",to:"ar"},{from:"ayr",to:"ay"},{from:"azj",to:"az"},{from:"bcc",to:"bal"},{from:"bcl",to:"bik"},{from:"bxk",to:"luy"},{from:"bxr",to:"bua"},{from:"cld",to:"syr"},{from:"cmn",to:"zh"},{from:"cwd",to:"cr"},{from:"dgo",to:"doi"},{from:"dhd",to:"mwr"},{from:"dik",to:"din"},{from:"diq",to:"zza"},{from:"lbk",to:"bnc"},{from:"ekk",to:"et"},{from:"emk",to:"man"},{from:"esk",to:"ik"},{from:"fat",to:"ak"},{from:"fuc",to:"ff"},{from:"gaz",to:"om"},{from:"gbo",to:"grb"},{from:"gno",to:"gon"},{from:"gug",to:"gn"},{from:"gya",to:"gba"},{from:"hdn",to:"hai"},{from:"hea",to:"hmn"},{from:"ike",to:"iu"},{from:"kmr",to:"ku"},{from:"knc",to:"kr"},{from:"kng",to:"kg"},{from:"knn",to:"kok"},{from:"kpv",to:"kv"},{from:"lvs",to:"lv"},{from:"mhr",to:"chm"},{from:"mup",to:"raj"},{from:"khk",to:"mn"},{from:"npi",to:"ne"},{from:"ojg",to:"oj"},{from:"ory",to:"or"},{from:"pbu",to:"ps"},{from:"pes",to:"fa"},{from:"plt",to:"mg"},{from:"pnb",to:"lah"},{from:"quz",to:"qu"},{from:"rmy",to:"rom"},{from:"spy",to:"kln"},{from:"src",to:"sc"},{from:"swh",to:"sw"},{from:"ttq",to:"tmh"},{from:"tw",to:"ak"},{from:"umu",to:"del"},{from:"uzn",to:"uz"},{from:"xpe",to:"kpe"},{from:"xsl",to:"den"},{from:"ydd",to:"yi"},{from:"zai",to:"zap"},{from:"zsm",to:"ms"},{from:"zyb",to:"za"},{from:"him",to:"srx"},{from:"mnk",to:"man"},{from:"bh",to:"bho"},{from:"prs",to:"fa-af"},{from:"swc",to:"sw-cd"},{from:"aar",to:"aa"},{from:"abk",to:"ab"},{from:"ave",to:"ae"},{from:"afr",to:"af"},{from:"aka",to:"ak"},{from:"amh",to:"am"},{from:"arg",to:"an"},{from:"ara",to:"ar"},{from:"asm",to:"as"},{from:"ava",to:"av"},{from:"aym",to:"ay"},{from:"aze",to:"az"},{from:"bak",to:"ba"},{from:"bel",to:"be"},{from:"bul",to:"bg"},{from:"bih",to:"bho"},{from:"bis",to:"bi"},{from:"bam",to:"bm"},{from:"ben",to:"bn"},{from:"bod",to:"bo"},{from:"bre",to:"br"},{from:"bos",to:"bs"},{from:"cat",to:"ca"},{from:"che",to:"ce"},{from:"cha",to:"ch"},{from:"cos",to:"co"},{from:"cre",to:"cr"},{from:"ces",to:"cs"},{from:"chu",to:"cu"},{from:"chv",to:"cv"},{from:"cym",to:"cy"},{from:"dan",to:"da"},{from:"deu",to:"de"},{from:"div",to:"dv"},{from:"dzo",to:"dz"},{from:"ewe",to:"ee"},{from:"ell",to:"el"},{from:"eng",to:"en"},{from:"epo",to:"eo"},{from:"spa",to:"es"},{from:"est",to:"et"},{from:"eus",to:"eu"},{from:"fas",to:"fa"},{from:"ful",to:"ff"},{from:"fin",to:"fi"},{from:"fij",to:"fj"},{from:"fao",to:"fo"},{from:"fra",to:"fr"},{from:"fry",to:"fy"},{from:"gle",to:"ga"},{from:"gla",to:"gd"},{from:"glg",to:"gl"},{from:"grn",to:"gn"},{from:"guj",to:"gu"},{from:"glv",to:"gv"},{from:"hau",to:"ha"},{from:"heb",to:"he"},{from:"hin",to:"hi"},{from:"hmo",to:"ho"},{from:"hrv",to:"hr"},{from:"hat",to:"ht"},{from:"hun",to:"hu"},{from:"hye",to:"hy"},{from:"her",to:"hz"},{from:"ina",to:"ia"},{from:"ind",to:"id"},{from:"ile",to:"ie"},{from:"ibo",to:"ig"},{from:"iii",to:"ii"},{from:"ipk",to:"ik"},{from:"ido",to:"io"},{from:"isl",to:"is"},{from:"ita",to:"it"},{from:"iku",to:"iu"},{from:"jpn",to:"ja"},{from:"jav",to:"jv"},{from:"kat",to:"ka"},{from:"kon",to:"kg"},{from:"kik",to:"ki"},{from:"kua",to:"kj"},{from:"kaz",to:"kk"},{from:"kal",to:"kl"},{from:"khm",to:"km"},{from:"kan",to:"kn"},{from:"kor",to:"ko"},{from:"kau",to:"kr"},{from:"kas",to:"ks"},{from:"kur",to:"ku"},{from:"kom",to:"kv"},{from:"cor",to:"kw"},{from:"kir",to:"ky"},{from:"lat",to:"la"},{from:"ltz",to:"lb"},{from:"lug",to:"lg"},{from:"lim",to:"li"},{from:"lin",to:"ln"},{from:"lao",to:"lo"},{from:"lit",to:"lt"},{from:"lub",to:"lu"},{from:"lav",to:"lv"},{from:"mlg",to:"mg"},{from:"mah",to:"mh"},{from:"mri",to:"mi"},{from:"mkd",to:"mk"},{from:"mal",to:"ml"},{from:"mon",to:"mn"},{from:"mol",to:"ro"},{from:"mar",to:"mr"},{from:"msa",to:"ms"},{from:"mlt",to:"mt"},{from:"mya",to:"my"},{from:"nau",to:"na"},{from:"nob",to:"nb"},{from:"nde",to:"nd"},{from:"nep",to:"ne"},{from:"ndo",to:"ng"},{from:"nld",to:"nl"},{from:"nno",to:"nn"},{from:"nor",to:"no"},{from:"nbl",to:"nr"},{from:"nav",to:"nv"},{from:"nya",to:"ny"},{from:"oci",to:"oc"},{from:"oji",to:"oj"},{from:"orm",to:"om"},{from:"ori",to:"or"},{from:"oss",to:"os"},{from:"pan",to:"pa"},{from:"pli",to:"pi"},{from:"pol",to:"pl"},{from:"pus",to:"ps"},{from:"por",to:"pt"},{from:"que",to:"qu"},{from:"roh",to:"rm"},{from:"run",to:"rn"},{from:"ron",to:"ro"},{from:"rus",to:"ru"},{from:"kin",to:"rw"},{from:"san",to:"sa"},{from:"srd",to:"sc"},{from:"snd",to:"sd"},{from:"sme",to:"se"},{from:"sag",to:"sg"},{from:"hbs",to:"sr-latn"},{from:"sin",to:"si"},{from:"slk",to:"sk"},{from:"slv",to:"sl"},{from:"smo",to:"sm"},{from:"sna",to:"sn"},{from:"som",to:"so"},{from:"sqi",to:"sq"},{from:"srp",to:"sr"},{from:"ssw",to:"ss"},{from:"sot",to:"st"},{from:"sun",to:"su"},{from:"swe",to:"sv"},{from:"swa",to:"sw"},{from:"tam",to:"ta"},{from:"tel",to:"te"},{from:"tgk",to:"tg"},{from:"tha",to:"th"},{from:"tir",to:"ti"},{from:"tuk",to:"tk"},{from:"tgl",to:"fil"},{from:"tsn",to:"tn"},{from:"ton",to:"to"},{from:"tur",to:"tr"},{from:"tso",to:"ts"},{from:"tat",to:"tt"},{from:"twi",to:"ak"},{from:"tah",to:"ty"},{from:"uig",to:"ug"},{from:"ukr",to:"uk"},{from:"urd",to:"ur"},{from:"uzb",to:"uz"},{from:"ven",to:"ve"},{from:"vie",to:"vi"},{from:"vol",to:"vo"},{from:"wln",to:"wa"},{from:"wol",to:"wo"},{from:"xho",to:"xh"},{from:"yid",to:"yi"},{from:"yor",to:"yo"},{from:"zha",to:"za"},{from:"zho",to:"zh"},{from:"zul",to:"zu"},{from:"alb",to:"sq"},{from:"arm",to:"hy"},{from:"baq",to:"eu"},{from:"bur",to:"my"},{from:"chi",to:"zh"},{from:"cze",to:"cs"},{from:"dut",to:"nl"},{from:"fre",to:"fr"},{from:"geo",to:"ka"},{from:"ger",to:"de"},{from:"gre",to:"el"},{from:"ice",to:"is"},{from:"mac",to:"mk"},{from:"mao",to:"mi"},{from:"may",to:"ms"},{from:"per",to:"fa"},{from:"rum",to:"ro"},{from:"slo",to:"sk"},{from:"tib",to:"bo"},{from:"wel",to:"cy"},{from:"und-aaland",to:"und-ax"},{from:"hy-arevmda",to:"hyw"},{from:"und-arevmda",to:"und"},{from:"und-arevela",to:"und"},{from:"und-lojban",to:"und"},{from:"und-saaho",to:"und"},{from:"und-bokmal",to:"und"},{from:"und-nynorsk",to:"und"},{from:"und-hakka",to:"und"},{from:"und-xiang",to:"und"},{from:"und-hepburn-heploc",to:"und-alalc97"}],Ya=[{from:{field:"script",value:"qaai"},to:{field:"script",value:"zinh"}},{from:{field:"region",value:"bu"},to:{field:"region",value:"mm"}},{from:{field:"region",value:"ct"},to:{field:"region",value:"ki"}},{from:{field:"region",value:"dd"},to:{field:"region",value:"de"}},{from:{field:"region",value:"dy"},to:{field:"region",value:"bj"}},{from:{field:"region",value:"fx"},to:{field:"region",value:"fr"}},{from:{field:"region",value:"hv"},to:{field:"region",value:"bf"}},{from:{field:"region",value:"jt"},to:{field:"region",value:"um"}},{from:{field:"region",value:"mi"},to:{field:"region",value:"um"}},{from:{field:"region",value:"nh"},to:{field:"region",value:"vu"}},{from:{field:"region",value:"nq"},to:{field:"region",value:"aq"}},{from:{field:"region",value:"pu"},to:{field:"region",value:"um"}},{from:{field:"region",value:"pz"},to:{field:"region",value:"pa"}},{from:{field:"region",value:"qu"},to:{field:"region",value:"eu"}},{from:{field:"region",value:"rh"},to:{field:"region",value:"zw"}},{from:{field:"region",value:"tp"},to:{field:"region",value:"tl"}},{from:{field:"region",value:"uk"},to:{field:"region",value:"gb"}},{from:{field:"region",value:"vd"},to:{field:"region",value:"vn"}},{from:{field:"region",value:"wk"},to:{field:"region",value:"um"}},{from:{field:"region",value:"yd"},to:{field:"region",value:"ye"}},{from:{field:"region",value:"zr"},to:{field:"region",value:"cd"}},{from:{field:"region",value:"230"},to:{field:"region",value:"et"}},{from:{field:"region",value:"280"},to:{field:"region",value:"de"}},{from:{field:"region",value:"736"},to:{field:"region",value:"sd"}},{from:{field:"region",value:"886"},to:{field:"region",value:"ye"}},{from:{field:"region",value:"958"},to:{field:"region",value:"aa"}},{from:{field:"region",value:"020"},to:{field:"region",value:"ad"}},{from:{field:"region",value:"784"},to:{field:"region",value:"ae"}},{from:{field:"region",value:"004"},to:{field:"region",value:"af"}},{from:{field:"region",value:"028"},to:{field:"region",value:"ag"}},{from:{field:"region",value:"660"},to:{field:"region",value:"ai"}},{from:{field:"region",value:"008"},to:{field:"region",value:"al"}},{from:{field:"region",value:"051"},to:{field:"region",value:"am"}},{from:{field:"region",value:"024"},to:{field:"region",value:"ao"}},{from:{field:"region",value:"010"},to:{field:"region",value:"aq"}},{from:{field:"region",value:"032"},to:{field:"region",value:"ar"}},{from:{field:"region",value:"016"},to:{field:"region",value:"as"}},{from:{field:"region",value:"040"},to:{field:"region",value:"at"}},{from:{field:"region",value:"036"},to:{field:"region",value:"au"}},{from:{field:"region",value:"533"},to:{field:"region",value:"aw"}},{from:{field:"region",value:"248"},to:{field:"region",value:"ax"}},{from:{field:"region",value:"031"},to:{field:"region",value:"az"}},{from:{field:"region",value:"070"},to:{field:"region",value:"ba"}},{from:{field:"region",value:"052"},to:{field:"region",value:"bb"}},{from:{field:"region",value:"050"},to:{field:"region",value:"bd"}},{from:{field:"region",value:"056"},to:{field:"region",value:"be"}},{from:{field:"region",value:"854"},to:{field:"region",value:"bf"}},{from:{field:"region",value:"100"},to:{field:"region",value:"bg"}},{from:{field:"region",value:"048"},to:{field:"region",value:"bh"}},{from:{field:"region",value:"108"},to:{field:"region",value:"bi"}},{from:{field:"region",value:"204"},to:{field:"region",value:"bj"}},{from:{field:"region",value:"652"},to:{field:"region",value:"bl"}},{from:{field:"region",value:"060"},to:{field:"region",value:"bm"}},{from:{field:"region",value:"096"},to:{field:"region",value:"bn"}},{from:{field:"region",value:"068"},to:{field:"region",value:"bo"}},{from:{field:"region",value:"535"},to:{field:"region",value:"bq"}},{from:{field:"region",value:"076"},to:{field:"region",value:"br"}},{from:{field:"region",value:"044"},to:{field:"region",value:"bs"}},{from:{field:"region",value:"064"},to:{field:"region",value:"bt"}},{from:{field:"region",value:"104"},to:{field:"region",value:"mm"}},{from:{field:"region",value:"074"},to:{field:"region",value:"bv"}},{from:{field:"region",value:"072"},to:{field:"region",value:"bw"}},{from:{field:"region",value:"112"},to:{field:"region",value:"by"}},{from:{field:"region",value:"084"},to:{field:"region",value:"bz"}},{from:{field:"region",value:"124"},to:{field:"region",value:"ca"}},{from:{field:"region",value:"166"},to:{field:"region",value:"cc"}},{from:{field:"region",value:"180"},to:{field:"region",value:"cd"}},{from:{field:"region",value:"140"},to:{field:"region",value:"cf"}},{from:{field:"region",value:"178"},to:{field:"region",value:"cg"}},{from:{field:"region",value:"756"},to:{field:"region",value:"ch"}},{from:{field:"region",value:"384"},to:{field:"region",value:"ci"}},{from:{field:"region",value:"184"},to:{field:"region",value:"ck"}},{from:{field:"region",value:"152"},to:{field:"region",value:"cl"}},{from:{field:"region",value:"120"},to:{field:"region",value:"cm"}},{from:{field:"region",value:"156"},to:{field:"region",value:"cn"}},{from:{field:"region",value:"170"},to:{field:"region",value:"co"}},{from:{field:"region",value:"188"},to:{field:"region",value:"cr"}},{from:{field:"region",value:"192"},to:{field:"region",value:"cu"}},{from:{field:"region",value:"132"},to:{field:"region",value:"cv"}},{from:{field:"region",value:"531"},to:{field:"region",value:"cw"}},{from:{field:"region",value:"162"},to:{field:"region",value:"cx"}},{from:{field:"region",value:"196"},to:{field:"region",value:"cy"}},{from:{field:"region",value:"203"},to:{field:"region",value:"cz"}},{from:{field:"region",value:"278"},to:{field:"region",value:"de"}},{from:{field:"region",value:"276"},to:{field:"region",value:"de"}},{from:{field:"region",value:"262"},to:{field:"region",value:"dj"}},{from:{field:"region",value:"208"},to:{field:"region",value:"dk"}},{from:{field:"region",value:"212"},to:{field:"region",value:"dm"}},{from:{field:"region",value:"214"},to:{field:"region",value:"do"}},{from:{field:"region",value:"012"},to:{field:"region",value:"dz"}},{from:{field:"region",value:"218"},to:{field:"region",value:"ec"}},{from:{field:"region",value:"233"},to:{field:"region",value:"ee"}},{from:{field:"region",value:"818"},to:{field:"region",value:"eg"}},{from:{field:"region",value:"732"},to:{field:"region",value:"eh"}},{from:{field:"region",value:"232"},to:{field:"region",value:"er"}},{from:{field:"region",value:"724"},to:{field:"region",value:"es"}},{from:{field:"region",value:"231"},to:{field:"region",value:"et"}},{from:{field:"region",value:"246"},to:{field:"region",value:"fi"}},{from:{field:"region",value:"242"},to:{field:"region",value:"fj"}},{from:{field:"region",value:"238"},to:{field:"region",value:"fk"}},{from:{field:"region",value:"583"},to:{field:"region",value:"fm"}},{from:{field:"region",value:"234"},to:{field:"region",value:"fo"}},{from:{field:"region",value:"250"},to:{field:"region",value:"fr"}},{from:{field:"region",value:"249"},to:{field:"region",value:"fr"}},{from:{field:"region",value:"266"},to:{field:"region",value:"ga"}},{from:{field:"region",value:"826"},to:{field:"region",value:"gb"}},{from:{field:"region",value:"308"},to:{field:"region",value:"gd"}},{from:{field:"region",value:"268"},to:{field:"region",value:"ge"}},{from:{field:"region",value:"254"},to:{field:"region",value:"gf"}},{from:{field:"region",value:"831"},to:{field:"region",value:"gg"}},{from:{field:"region",value:"288"},to:{field:"region",value:"gh"}},{from:{field:"region",value:"292"},to:{field:"region",value:"gi"}},{from:{field:"region",value:"304"},to:{field:"region",value:"gl"}},{from:{field:"region",value:"270"},to:{field:"region",value:"gm"}},{from:{field:"region",value:"324"},to:{field:"region",value:"gn"}},{from:{field:"region",value:"312"},to:{field:"region",value:"gp"}},{from:{field:"region",value:"226"},to:{field:"region",value:"gq"}},{from:{field:"region",value:"300"},to:{field:"region",value:"gr"}},{from:{field:"region",value:"239"},to:{field:"region",value:"gs"}},{from:{field:"region",value:"320"},to:{field:"region",value:"gt"}},{from:{field:"region",value:"316"},to:{field:"region",value:"gu"}},{from:{field:"region",value:"624"},to:{field:"region",value:"gw"}},{from:{field:"region",value:"328"},to:{field:"region",value:"gy"}},{from:{field:"region",value:"344"},to:{field:"region",value:"hk"}},{from:{field:"region",value:"334"},to:{field:"region",value:"hm"}},{from:{field:"region",value:"340"},to:{field:"region",value:"hn"}},{from:{field:"region",value:"191"},to:{field:"region",value:"hr"}},{from:{field:"region",value:"332"},to:{field:"region",value:"ht"}},{from:{field:"region",value:"348"},to:{field:"region",value:"hu"}},{from:{field:"region",value:"360"},to:{field:"region",value:"id"}},{from:{field:"region",value:"372"},to:{field:"region",value:"ie"}},{from:{field:"region",value:"376"},to:{field:"region",value:"il"}},{from:{field:"region",value:"833"},to:{field:"region",value:"im"}},{from:{field:"region",value:"356"},to:{field:"region",value:"in"}},{from:{field:"region",value:"086"},to:{field:"region",value:"io"}},{from:{field:"region",value:"368"},to:{field:"region",value:"iq"}},{from:{field:"region",value:"364"},to:{field:"region",value:"ir"}},{from:{field:"region",value:"352"},to:{field:"region",value:"is"}},{from:{field:"region",value:"380"},to:{field:"region",value:"it"}},{from:{field:"region",value:"832"},to:{field:"region",value:"je"}},{from:{field:"region",value:"388"},to:{field:"region",value:"jm"}},{from:{field:"region",value:"400"},to:{field:"region",value:"jo"}},{from:{field:"region",value:"392"},to:{field:"region",value:"jp"}},{from:{field:"region",value:"404"},to:{field:"region",value:"ke"}},{from:{field:"region",value:"417"},to:{field:"region",value:"kg"}},{from:{field:"region",value:"116"},to:{field:"region",value:"kh"}},{from:{field:"region",value:"296"},to:{field:"region",value:"ki"}},{from:{field:"region",value:"174"},to:{field:"region",value:"km"}},{from:{field:"region",value:"659"},to:{field:"region",value:"kn"}},{from:{field:"region",value:"408"},to:{field:"region",value:"kp"}},{from:{field:"region",value:"410"},to:{field:"region",value:"kr"}},{from:{field:"region",value:"414"},to:{field:"region",value:"kw"}},{from:{field:"region",value:"136"},to:{field:"region",value:"ky"}},{from:{field:"region",value:"398"},to:{field:"region",value:"kz"}},{from:{field:"region",value:"418"},to:{field:"region",value:"la"}},{from:{field:"region",value:"422"},to:{field:"region",value:"lb"}},{from:{field:"region",value:"662"},to:{field:"region",value:"lc"}},{from:{field:"region",value:"438"},to:{field:"region",value:"li"}},{from:{field:"region",value:"144"},to:{field:"region",value:"lk"}},{from:{field:"region",value:"430"},to:{field:"region",value:"lr"}},{from:{field:"region",value:"426"},to:{field:"region",value:"ls"}},{from:{field:"region",value:"440"},to:{field:"region",value:"lt"}},{from:{field:"region",value:"442"},to:{field:"region",value:"lu"}},{from:{field:"region",value:"428"},to:{field:"region",value:"lv"}},{from:{field:"region",value:"434"},to:{field:"region",value:"ly"}},{from:{field:"region",value:"504"},to:{field:"region",value:"ma"}},{from:{field:"region",value:"492"},to:{field:"region",value:"mc"}},{from:{field:"region",value:"498"},to:{field:"region",value:"md"}},{from:{field:"region",value:"499"},to:{field:"region",value:"me"}},{from:{field:"region",value:"663"},to:{field:"region",value:"mf"}},{from:{field:"region",value:"450"},to:{field:"region",value:"mg"}},{from:{field:"region",value:"584"},to:{field:"region",value:"mh"}},{from:{field:"region",value:"807"},to:{field:"region",value:"mk"}},{from:{field:"region",value:"466"},to:{field:"region",value:"ml"}},{from:{field:"region",value:"496"},to:{field:"region",value:"mn"}},{from:{field:"region",value:"446"},to:{field:"region",value:"mo"}},{from:{field:"region",value:"580"},to:{field:"region",value:"mp"}},{from:{field:"region",value:"474"},to:{field:"region",value:"mq"}},{from:{field:"region",value:"478"},to:{field:"region",value:"mr"}},{from:{field:"region",value:"500"},to:{field:"region",value:"ms"}},{from:{field:"region",value:"470"},to:{field:"region",value:"mt"}},{from:{field:"region",value:"480"},to:{field:"region",value:"mu"}},{from:{field:"region",value:"462"},to:{field:"region",value:"mv"}},{from:{field:"region",value:"454"},to:{field:"region",value:"mw"}},{from:{field:"region",value:"484"},to:{field:"region",value:"mx"}},{from:{field:"region",value:"458"},to:{field:"region",value:"my"}},{from:{field:"region",value:"508"},to:{field:"region",value:"mz"}},{from:{field:"region",value:"516"},to:{field:"region",value:"na"}},{from:{field:"region",value:"540"},to:{field:"region",value:"nc"}},{from:{field:"region",value:"562"},to:{field:"region",value:"ne"}},{from:{field:"region",value:"574"},to:{field:"region",value:"nf"}},{from:{field:"region",value:"566"},to:{field:"region",value:"ng"}},{from:{field:"region",value:"558"},to:{field:"region",value:"ni"}},{from:{field:"region",value:"528"},to:{field:"region",value:"nl"}},{from:{field:"region",value:"578"},to:{field:"region",value:"no"}},{from:{field:"region",value:"524"},to:{field:"region",value:"np"}},{from:{field:"region",value:"520"},to:{field:"region",value:"nr"}},{from:{field:"region",value:"570"},to:{field:"region",value:"nu"}},{from:{field:"region",value:"554"},to:{field:"region",value:"nz"}},{from:{field:"region",value:"512"},to:{field:"region",value:"om"}},{from:{field:"region",value:"591"},to:{field:"region",value:"pa"}},{from:{field:"region",value:"604"},to:{field:"region",value:"pe"}},{from:{field:"region",value:"258"},to:{field:"region",value:"pf"}},{from:{field:"region",value:"598"},to:{field:"region",value:"pg"}},{from:{field:"region",value:"608"},to:{field:"region",value:"ph"}},{from:{field:"region",value:"586"},to:{field:"region",value:"pk"}},{from:{field:"region",value:"616"},to:{field:"region",value:"pl"}},{from:{field:"region",value:"666"},to:{field:"region",value:"pm"}},{from:{field:"region",value:"612"},to:{field:"region",value:"pn"}},{from:{field:"region",value:"630"},to:{field:"region",value:"pr"}},{from:{field:"region",value:"275"},to:{field:"region",value:"ps"}},{from:{field:"region",value:"620"},to:{field:"region",value:"pt"}},{from:{field:"region",value:"585"},to:{field:"region",value:"pw"}},{from:{field:"region",value:"600"},to:{field:"region",value:"py"}},{from:{field:"region",value:"634"},to:{field:"region",value:"qa"}},{from:{field:"region",value:"959"},to:{field:"region",value:"qm"}},{from:{field:"region",value:"960"},to:{field:"region",value:"qn"}},{from:{field:"region",value:"962"},to:{field:"region",value:"qp"}},{from:{field:"region",value:"963"},to:{field:"region",value:"qq"}},{from:{field:"region",value:"964"},to:{field:"region",value:"qr"}},{from:{field:"region",value:"965"},to:{field:"region",value:"qs"}},{from:{field:"region",value:"966"},to:{field:"region",value:"qt"}},{from:{field:"region",value:"967"},to:{field:"region",value:"eu"}},{from:{field:"region",value:"968"},to:{field:"region",value:"qv"}},{from:{field:"region",value:"969"},to:{field:"region",value:"qw"}},{from:{field:"region",value:"970"},to:{field:"region",value:"qx"}},{from:{field:"region",value:"971"},to:{field:"region",value:"qy"}},{from:{field:"region",value:"972"},to:{field:"region",value:"qz"}},{from:{field:"region",value:"638"},to:{field:"region",value:"re"}},{from:{field:"region",value:"642"},to:{field:"region",value:"ro"}},{from:{field:"region",value:"688"},to:{field:"region",value:"rs"}},{from:{field:"region",value:"643"},to:{field:"region",value:"ru"}},{from:{field:"region",value:"646"},to:{field:"region",value:"rw"}},{from:{field:"region",value:"682"},to:{field:"region",value:"sa"}},{from:{field:"region",value:"090"},to:{field:"region",value:"sb"}},{from:{field:"region",value:"690"},to:{field:"region",value:"sc"}},{from:{field:"region",value:"729"},to:{field:"region",value:"sd"}},{from:{field:"region",value:"752"},to:{field:"region",value:"se"}},{from:{field:"region",value:"702"},to:{field:"region",value:"sg"}},{from:{field:"region",value:"654"},to:{field:"region",value:"sh"}},{from:{field:"region",value:"705"},to:{field:"region",value:"si"}},{from:{field:"region",value:"744"},to:{field:"region",value:"sj"}},{from:{field:"region",value:"703"},to:{field:"region",value:"sk"}},{from:{field:"region",value:"694"},to:{field:"region",value:"sl"}},{from:{field:"region",value:"674"},to:{field:"region",value:"sm"}},{from:{field:"region",value:"686"},to:{field:"region",value:"sn"}},{from:{field:"region",value:"706"},to:{field:"region",value:"so"}},{from:{field:"region",value:"740"},to:{field:"region",value:"sr"}},{from:{field:"region",value:"728"},to:{field:"region",value:"ss"}},{from:{field:"region",value:"678"},to:{field:"region",value:"st"}},{from:{field:"region",value:"222"},to:{field:"region",value:"sv"}},{from:{field:"region",value:"534"},to:{field:"region",value:"sx"}},{from:{field:"region",value:"760"},to:{field:"region",value:"sy"}},{from:{field:"region",value:"748"},to:{field:"region",value:"sz"}},{from:{field:"region",value:"796"},to:{field:"region",value:"tc"}},{from:{field:"region",value:"148"},to:{field:"region",value:"td"}},{from:{field:"region",value:"260"},to:{field:"region",value:"tf"}},{from:{field:"region",value:"768"},to:{field:"region",value:"tg"}},{from:{field:"region",value:"764"},to:{field:"region",value:"th"}},{from:{field:"region",value:"762"},to:{field:"region",value:"tj"}},{from:{field:"region",value:"772"},to:{field:"region",value:"tk"}},{from:{field:"region",value:"626"},to:{field:"region",value:"tl"}},{from:{field:"region",value:"795"},to:{field:"region",value:"tm"}},{from:{field:"region",value:"788"},to:{field:"region",value:"tn"}},{from:{field:"region",value:"776"},to:{field:"region",value:"to"}},{from:{field:"region",value:"792"},to:{field:"region",value:"tr"}},{from:{field:"region",value:"780"},to:{field:"region",value:"tt"}},{from:{field:"region",value:"798"},to:{field:"region",value:"tv"}},{from:{field:"region",value:"158"},to:{field:"region",value:"tw"}},{from:{field:"region",value:"834"},to:{field:"region",value:"tz"}},{from:{field:"region",value:"804"},to:{field:"region",value:"ua"}},{from:{field:"region",value:"800"},to:{field:"region",value:"ug"}},{from:{field:"region",value:"581"},to:{field:"region",value:"um"}},{from:{field:"region",value:"840"},to:{field:"region",value:"us"}},{from:{field:"region",value:"858"},to:{field:"region",value:"uy"}},{from:{field:"region",value:"860"},to:{field:"region",value:"uz"}},{from:{field:"region",value:"336"},to:{field:"region",value:"va"}},{from:{field:"region",value:"670"},to:{field:"region",value:"vc"}},{from:{field:"region",value:"862"},to:{field:"region",value:"ve"}},{from:{field:"region",value:"092"},to:{field:"region",value:"vg"}},{from:{field:"region",value:"850"},to:{field:"region",value:"vi"}},{from:{field:"region",value:"704"},to:{field:"region",value:"vn"}},{from:{field:"region",value:"548"},to:{field:"region",value:"vu"}},{from:{field:"region",value:"876"},to:{field:"region",value:"wf"}},{from:{field:"region",value:"882"},to:{field:"region",value:"ws"}},{from:{field:"region",value:"973"},to:{field:"region",value:"xa"}},{from:{field:"region",value:"974"},to:{field:"region",value:"xb"}},{from:{field:"region",value:"975"},to:{field:"region",value:"xc"}},{from:{field:"region",value:"976"},to:{field:"region",value:"xd"}},{from:{field:"region",value:"977"},to:{field:"region",value:"xe"}},{from:{field:"region",value:"978"},to:{field:"region",value:"xf"}},{from:{field:"region",value:"979"},to:{field:"region",value:"xg"}},{from:{field:"region",value:"980"},to:{field:"region",value:"xh"}},{from:{field:"region",value:"981"},to:{field:"region",value:"xi"}},{from:{field:"region",value:"982"},to:{field:"region",value:"xj"}},{from:{field:"region",value:"983"},to:{field:"region",value:"xk"}},{from:{field:"region",value:"984"},to:{field:"region",value:"xl"}},{from:{field:"region",value:"985"},to:{field:"region",value:"xm"}},{from:{field:"region",value:"986"},to:{field:"region",value:"xn"}},{from:{field:"region",value:"987"},to:{field:"region",value:"xo"}},{from:{field:"region",value:"988"},to:{field:"region",value:"xp"}},{from:{field:"region",value:"989"},to:{field:"region",value:"xq"}},{from:{field:"region",value:"990"},to:{field:"region",value:"xr"}},{from:{field:"region",value:"991"},to:{field:"region",value:"xs"}},{from:{field:"region",value:"992"},to:{field:"region",value:"xt"}},{from:{field:"region",value:"993"},to:{field:"region",value:"xu"}},{from:{field:"region",value:"994"},to:{field:"region",value:"xv"}},{from:{field:"region",value:"995"},to:{field:"region",value:"xw"}},{from:{field:"region",value:"996"},to:{field:"region",value:"xx"}},{from:{field:"region",value:"997"},to:{field:"region",value:"xy"}},{from:{field:"region",value:"998"},to:{field:"region",value:"xz"}},{from:{field:"region",value:"720"},to:{field:"region",value:"ye"}},{from:{field:"region",value:"887"},to:{field:"region",value:"ye"}},{from:{field:"region",value:"175"},to:{field:"region",value:"yt"}},{from:{field:"region",value:"710"},to:{field:"region",value:"za"}},{from:{field:"region",value:"894"},to:{field:"region",value:"zm"}},{from:{field:"region",value:"716"},to:{field:"region",value:"zw"}},{from:{field:"region",value:"999"},to:{field:"region",value:"zz"}},{from:{field:"variants",value:"polytoni"},to:{field:"variants",value:"polyton"}},{from:{field:"variants",value:"heploc"},to:{field:"variants",value:"alalc97"}}],Xa={region:{172:["ru","am","az","by","ge","kg","kz","md","tj","tm","ua","uz"],200:["cz","sk"],530:["cw","sx","bq"],532:["cw","sx","bq"],536:["sa","iq"],582:["fm","mh","mp","pw"],810:["ru","am","az","by","ee","ge","kz","kg","lv","lt","md","tj","tm","ua","uz"],830:["je","gg"],890:["rs","me","si","hr","mk","ba"],891:["rs","me"],an:["cw","sx","bq"],cs:["rs","me"],fq:["aq","tf"],nt:["sa","iq"],pc:["fm","mh","mp","pw"],su:["ru","am","az","by","ee","ge","kz","kg","lv","lt","md","tj","tm","ua","uz"],yu:["rs","me"],"062":["034","143"],ant:["cw","sx","bq"],scg:["rs","me"],ntz:["sa","iq"],sun:["ru","am","az","by","ee","ge","kz","kg","lv","lt","md","tj","tm","ua","uz"],yug:["rs","me"]}},Wa={aa:"aa-latn-et",aaa:"aaa-latn-ng",aab:"aab-latn-ng",aac:"aac-latn-pg",aad:"aad-latn-pg",aae:"aae-latn-it","aae-grek":"aae-grek-it",aaf:"aaf-mlym-in","aaf-arab":"aaf-arab-in",aag:"aag-latn-pg",aah:"aah-latn-pg",aai:"aai-latn-zz",aak:"aak-latn-zz",aal:"aal-latn-cm",aan:"aan-latn-br",aao:"aao-arab-dz",aap:"aap-latn-br",aaq:"aaq-latn-us",aas:"aas-latn-tz",aat:"aat-grek-gr",aau:"aau-latn-zz",aaw:"aaw-latn-pg",aax:"aax-latn-id",aaz:"aaz-latn-id",ab:"ab-cyrl-ge",aba:"aba-latn-ci",abb:"abb-latn-cm",abc:"abc-latn-ph",abd:"abd-latn-ph",abe:"abe-latn-ca",abf:"abf-latn-my",abg:"abg-latn-pg",abh:"abh-arab-tj",abi:"abi-latn-zz",abl:"abl-rjng-id","abl-latn":"abl-latn-id",abm:"abm-latn-ng",abn:"abn-latn-ng",abo:"abo-latn-ng",abp:"abp-latn-ph",abq:"abq-cyrl-zz",abr:"abr-latn-gh",abs:"abs-latn-id",abt:"abt-latn-zz",abu:"abu-latn-ci",abv:"abv-arab-bh",abw:"abw-latn-pg",abx:"abx-latn-ph",aby:"aby-latn-zz",abz:"abz-latn-id",aca:"aca-latn-co",acb:"acb-latn-ng",acd:"acd-latn-zz",ace:"ace-latn-id",acf:"acf-latn-lc",ach:"ach-latn-ug",acm:"acm-arab-iq",acn:"acn-latn-cn",acp:"acp-latn-ng",acq:"acq-arab-ye",acr:"acr-latn-gt",acs:"acs-latn-br",act:"act-latn-nl",acu:"acu-latn-ec",acv:"acv-latn-us",acw:"acw-arab-sa",acx:"acx-arab-om",acy:"acy-latn-cy","acy-arab":"acy-arab-cy","acy-grek":"acy-grek-cy",acz:"acz-latn-sd",ada:"ada-latn-gh",adb:"adb-latn-tl",add:"add-latn-cm",ade:"ade-latn-zz",adf:"adf-arab-om",adg:"adg-latn-au",adh:"adh-latn-ug",adi:"adi-latn-in","adi-tibt":"adi-tibt-cn",adj:"adj-latn-zz",adl:"adl-latn-in",adn:"adn-latn-id",ado:"ado-latn-pg",adp:"adp-tibt-bt",adq:"adq-latn-gh",adr:"adr-latn-id",adt:"adt-latn-au",adu:"adu-latn-ng",adw:"adw-latn-br",adx:"adx-tibt-cn",ady:"ady-cyrl-ru",adz:"adz-latn-zz",ae:"ae-avst-ir",aea:"aea-latn-au",aeb:"aeb-arab-tn",aec:"aec-arab-eg",aee:"aee-arab-af",aek:"aek-latn-nc",ael:"ael-latn-cm",aem:"aem-latn-vn",aeq:"aeq-arab-pk",aer:"aer-latn-au",aeu:"aeu-latn-cn",aew:"aew-latn-pg",aey:"aey-latn-zz",aez:"aez-latn-pg",af:"af-latn-za",afb:"afb-arab-kw",afd:"afd-latn-pg",afe:"afe-latn-ng",afh:"afh-latn-gh",afi:"afi-latn-pg",afk:"afk-latn-pg",afn:"afn-latn-ng",afo:"afo-latn-ng",afp:"afp-latn-pg",afs:"afs-latn-mx",afu:"afu-latn-gh",afz:"afz-latn-id",aga:"aga-latn-pe",agb:"agb-latn-ng",agc:"agc-latn-zz",agd:"agd-latn-zz",age:"age-latn-pg",agf:"agf-latn-id",agg:"agg-latn-zz",agh:"agh-latn-cd",agi:"agi-deva-in",agj:"agj-ethi-et","agj-arab":"agj-arab-et",agk:"agk-latn-ph",agl:"agl-latn-pg",agm:"agm-latn-zz",agn:"agn-latn-ph",ago:"ago-latn-zz",agq:"agq-latn-cm",agr:"agr-latn-pe",ags:"ags-latn-cm",agt:"agt-latn-ph",agu:"agu-latn-gt",agv:"agv-latn-ph",agw:"agw-latn-sb",agx:"agx-cyrl-ru",agy:"agy-latn-ph",agz:"agz-latn-ph",aha:"aha-latn-zz",ahb:"ahb-latn-vu",ahg:"ahg-ethi-et",ahh:"ahh-latn-id",ahi:"ahi-latn-ci",ahk:"ahk-latn-mm","ahk-mymr":"ahk-mymr-mm","ahk-th":"ahk-latn-th","ahk-thai":"ahk-thai-th",ahl:"ahl-latn-zz",ahm:"ahm-latn-ci",ahn:"ahn-latn-ng",aho:"aho-ahom-in",ahp:"ahp-latn-ci",ahr:"ahr-deva-in",ahs:"ahs-latn-ng",aht:"aht-latn-us",aia:"aia-latn-sb",aib:"aib-arab-cn",aic:"aic-latn-pg",aid:"aid-latn-au",aie:"aie-latn-pg",aif:"aif-latn-pg",aig:"aig-latn-ag",aij:"aij-hebr-il",aik:"aik-latn-ng",ail:"ail-latn-pg",aim:"aim-latn-in",ain:"ain-kana-jp","ain-latn":"ain-latn-jp",aio:"aio-mymr-in",aip:"aip-latn-id",aiq:"aiq-arab-af",air:"air-latn-id",ait:"ait-latn-br",aiw:"aiw-latn-et","aiw-arab":"aiw-arab-et","aiw-ethi":"aiw-ethi-et",aix:"aix-latn-pg",aiy:"aiy-latn-cf",aja:"aja-latn-ss",ajg:"ajg-latn-zz",aji:"aji-latn-nc",ajn:"ajn-latn-au",ajp:"ajp-arab-jo",ajt:"ajt-arab-tn",ajw:"ajw-latn-ng",ajz:"ajz-latn-in",ak:"ak-latn-gh",akb:"akb-latn-id","akb-batk":"akb-batk-id",akc:"akc-latn-id",akd:"akd-latn-ng",ake:"ake-latn-gy",akf:"akf-latn-ng",akg:"akg-latn-id",akh:"akh-latn-pg",aki:"aki-latn-pg",akk:"akk-xsux-iq",akl:"akl-latn-ph",ako:"ako-latn-sr",akp:"akp-latn-gh",akq:"akq-latn-pg",akr:"akr-latn-vu",aks:"aks-latn-tg",akt:"akt-latn-pg",aku:"aku-latn-cm",akv:"akv-cyrl-ru",akw:"akw-latn-cg",akz:"akz-latn-us",ala:"ala-latn-zz",alc:"alc-latn-cl",ald:"ald-latn-ci",ale:"ale-latn-us",alf:"alf-latn-ng",alh:"alh-latn-au",ali:"ali-latn-zz",alj:"alj-latn-ph",alk:"alk-laoo-la",all:"all-mlym-in",alm:"alm-latn-vu",aln:"aln-latn-xk",alo:"alo-latn-id",alp:"alp-latn-id",alq:"alq-latn-ca",alr:"alr-cyrl-ru",alt:"alt-cyrl-ru",alu:"alu-latn-sb",alw:"alw-ethi-et",alx:"alx-latn-pg",aly:"aly-latn-au",alz:"alz-latn-cd",am:"am-ethi-et",ama:"ama-latn-br",amb:"amb-latn-ng",amc:"amc-latn-pe",ame:"ame-latn-pe",amf:"amf-latn-et","amf-ethi":"amf-ethi-et",amg:"amg-latn-au",ami:"ami-latn-tw",amj:"amj-latn-td",amk:"amk-latn-id",amm:"amm-latn-zz",amn:"amn-latn-zz",amo:"amo-latn-ng",amp:"amp-latn-zz",amq:"amq-latn-id",amr:"amr-latn-pe",ams:"ams-jpan-jp",amt:"amt-latn-pg",amu:"amu-latn-mx",amv:"amv-latn-id",amw:"amw-syrc-sy","amw-arab":"amw-arab-sy","amw-armi":"amw-armi-sy","amw-latn":"amw-latn-sy",amx:"amx-latn-au",amy:"amy-latn-au",amz:"amz-latn-au",an:"an-latn-es",ana:"ana-latn-co",anb:"anb-latn-pe",anc:"anc-latn-zz",and:"and-latn-id",ane:"ane-latn-nc",anf:"anf-latn-gh",ang:"ang-latn-gb",anh:"anh-latn-pg",ani:"ani-cyrl-ru",anj:"anj-latn-pg",ank:"ank-latn-zz",anl:"anl-latn-mm",anm:"anm-latn-in",ann:"ann-latn-ng",ano:"ano-latn-co",anp:"anp-deva-in",anr:"anr-deva-in",ans:"ans-latn-co",ant:"ant-latn-au",anu:"anu-ethi-et","anu-arab":"anu-arab-ss","anu-latn":"anu-latn-ss",anv:"anv-latn-cm",anw:"anw-latn-ng",anx:"anx-latn-pg",any:"any-latn-zz",anz:"anz-latn-pg",aoa:"aoa-latn-st",aob:"aob-latn-pg",aoc:"aoc-latn-ve",aod:"aod-latn-pg",aoe:"aoe-latn-pg",aof:"aof-latn-pg",aog:"aog-latn-pg",aoi:"aoi-latn-au",aoj:"aoj-latn-zz",aok:"aok-latn-nc",aol:"aol-latn-id",aom:"aom-latn-zz",aon:"aon-latn-pg",aor:"aor-latn-vu",aos:"aos-latn-id",aot:"aot-beng-bd","aot-latn":"aot-latn-in",aox:"aox-latn-gy",aoz:"aoz-latn-id",apb:"apb-latn-sb",apc:"apc-arab-sy",apd:"apd-arab-tg",ape:"ape-latn-zz",apf:"apf-latn-ph",apg:"apg-latn-id",aph:"aph-deva-np",api:"api-latn-br",apj:"apj-latn-us",apk:"apk-latn-us",apl:"apl-latn-us",apm:"apm-latn-us",apn:"apn-latn-br",apo:"apo-latn-pg",app:"app-latn-vu",apr:"apr-latn-zz",aps:"aps-latn-zz",apt:"apt-latn-in",apu:"apu-latn-br",apv:"apv-latn-br",apw:"apw-latn-us",apx:"apx-latn-id",apy:"apy-latn-br",apz:"apz-latn-zz",aqc:"aqc-cyrl-ru",aqd:"aqd-latn-ml",aqg:"aqg-latn-ng",aqk:"aqk-latn-ng",aqm:"aqm-latn-id",aqn:"aqn-latn-ph",aqr:"aqr-latn-nc",aqt:"aqt-latn-py",aqz:"aqz-latn-br",ar:"ar-arab-eg",arc:"arc-armi-ir","arc-nbat":"arc-nbat-jo","arc-palm":"arc-palm-sy",ard:"ard-latn-au",are:"are-latn-au",arh:"arh-latn-zz",ari:"ari-latn-us",arj:"arj-latn-br",ark:"ark-latn-br",arl:"arl-latn-pe",arn:"arn-latn-cl",aro:"aro-latn-bo",arp:"arp-latn-us",arq:"arq-arab-dz",arr:"arr-latn-br",ars:"ars-arab-sa",aru:"aru-latn-br",arw:"arw-latn-sr",arx:"arx-latn-br",ary:"ary-arab-ma",arz:"arz-arab-eg",as:"as-beng-in",asa:"asa-latn-tz",asb:"asb-latn-ca",asc:"asc-latn-id",ase:"ase-sgnw-us",asg:"asg-latn-zz",ash:"ash-latn-pe",asi:"asi-latn-id",asj:"asj-latn-cm",ask:"ask-arab-af",asl:"asl-latn-id",asn:"asn-latn-br",aso:"aso-latn-zz",ass:"ass-latn-cm",ast:"ast-latn-es",asu:"asu-latn-br",asv:"asv-latn-cd",asx:"asx-latn-pg",asy:"asy-latn-id",asz:"asz-latn-id",ata:"ata-latn-zz",atb:"atb-latn-cn","atb-lisu":"atb-lisu-cn",atc:"atc-latn-pe",atd:"atd-latn-ph",ate:"ate-latn-pg",atg:"atg-latn-zz",ati:"ati-latn-ci",atj:"atj-latn-ca",atk:"atk-latn-ph",atl:"atl-latn-ph",atm:"atm-latn-ph",atn:"atn-arab-ir",ato:"ato-latn-cm",atp:"atp-latn-ph",atq:"atq-latn-id",atr:"atr-latn-br",ats:"ats-latn-us",att:"att-latn-ph",atu:"atu-latn-ss",atv:"atv-cyrl-ru",atw:"atw-latn-us",atx:"atx-latn-br",aty:"aty-latn-vu",atz:"atz-latn-ph",aua:"aua-latn-sb",auc:"auc-latn-ec",aud:"aud-latn-sb",aug:"aug-latn-bj",auh:"auh-latn-zm",aui:"aui-latn-pg",auj:"auj-arab-ly","auj-latn":"auj-latn-ly","auj-tfng":"auj-tfng-ly",auk:"auk-latn-pg",aul:"aul-latn-vu",aum:"aum-latn-ng",aun:"aun-latn-pg",auo:"auo-latn-ng",aup:"aup-latn-pg",auq:"auq-latn-id",aur:"aur-latn-pg",aut:"aut-latn-pf",auu:"auu-latn-id",auw:"auw-latn-id",auy:"auy-latn-zz",auz:"auz-arab-uz",av:"av-cyrl-ru",avb:"avb-latn-pg",avd:"avd-arab-ir",avi:"avi-latn-ci",avk:"avk-latn-001",avl:"avl-arab-zz",avm:"avm-latn-au",avn:"avn-latn-zz",avo:"avo-latn-br",avs:"avs-latn-pe",avt:"avt-latn-zz",avu:"avu-latn-zz",avv:"avv-latn-br",awa:"awa-deva-in",awb:"awb-latn-zz",awc:"awc-latn-ng",awe:"awe-latn-br",awg:"awg-latn-au",awh:"awh-latn-id",awi:"awi-latn-pg",awk:"awk-latn-au",awm:"awm-latn-pg",awn:"awn-ethi-et",awo:"awo-latn-zz",awr:"awr-latn-id",aws:"aws-latn-id",awt:"awt-latn-br",awu:"awu-latn-id",awv:"awv-latn-id",aww:"aww-latn-pg",awx:"awx-latn-zz",awy:"awy-latn-id",axb:"axb-latn-ar",axe:"axe-latn-au",axg:"axg-latn-br",axk:"axk-latn-cf",axl:"axl-latn-au",axm:"axm-armn-am",axx:"axx-latn-nc",ay:"ay-latn-bo",aya:"aya-latn-pg",ayb:"ayb-latn-zz",ayc:"ayc-latn-pe",ayd:"ayd-latn-au",aye:"aye-latn-ng",ayg:"ayg-latn-tg",ayh:"ayh-arab-ye",ayi:"ayi-latn-ng",ayk:"ayk-latn-ng",ayl:"ayl-arab-ly",ayn:"ayn-arab-ye",ayo:"ayo-latn-py",ayp:"ayp-arab-iq",ayq:"ayq-latn-pg",ays:"ays-latn-ph",ayt:"ayt-latn-ph",ayu:"ayu-latn-ng",ayz:"ayz-latn-id",az:"az-latn-az","az-arab":"az-arab-ir","az-iq":"az-arab-iq","az-ir":"az-arab-ir","az-ru":"az-cyrl-ru",azb:"azb-arab-ir","azb-cyrl":"azb-cyrl-az","azb-latn":"azb-latn-az",azd:"azd-latn-mx",azg:"azg-latn-mx",azm:"azm-latn-mx",azn:"azn-latn-mx",azo:"azo-latn-cm",azt:"azt-latn-ph",azz:"azz-latn-mx",ba:"ba-cyrl-ru",baa:"baa-latn-sb",bab:"bab-latn-gw",bac:"bac-latn-id",bae:"bae-latn-ve",baf:"baf-latn-cm",bag:"bag-latn-cm",bah:"bah-latn-bs",baj:"baj-latn-id",bal:"bal-arab-pk",ban:"ban-latn-id",bao:"bao-latn-co",bap:"bap-deva-np",bar:"bar-latn-at",bas:"bas-latn-cm",bau:"bau-latn-ng",bav:"bav-latn-zz",baw:"baw-latn-cm",bax:"bax-bamu-cm",bay:"bay-latn-id",bba:"bba-latn-zz",bbb:"bbb-latn-zz",bbc:"bbc-latn-id",bbd:"bbd-latn-zz",bbe:"bbe-latn-cd",bbf:"bbf-latn-pg",bbg:"bbg-latn-ga",bbi:"bbi-latn-cm",bbj:"bbj-latn-cm",bbk:"bbk-latn-cm",bbl:"bbl-geor-ge",bbm:"bbm-latn-cd",bbn:"bbn-latn-pg",bbo:"bbo-latn-bf",bbp:"bbp-latn-zz",bbq:"bbq-latn-cm",bbr:"bbr-latn-zz",bbs:"bbs-latn-ng",bbt:"bbt-latn-ng",bbu:"bbu-latn-ng",bbv:"bbv-latn-pg",bbw:"bbw-latn-cm",bbx:"bbx-latn-cm",bby:"bby-latn-cm",bca:"bca-latn-cn","bca-hani":"bca-hani-cn",bcb:"bcb-latn-sn",bcd:"bcd-latn-id",bce:"bce-latn-cm",bcf:"bcf-latn-zz",bcg:"bcg-latn-gn",bch:"bch-latn-zz",bci:"bci-latn-ci",bcj:"bcj-latn-au",bck:"bck-latn-au",bcm:"bcm-latn-zz",bcn:"bcn-latn-zz",bco:"bco-latn-zz",bcp:"bcp-latn-cd",bcq:"bcq-ethi-zz",bcr:"bcr-latn-ca",bcs:"bcs-latn-ng",bct:"bct-latn-cd",bcu:"bcu-latn-zz",bcv:"bcv-latn-ng",bcw:"bcw-latn-cm",bcy:"bcy-latn-ng",bcz:"bcz-latn-sn",bda:"bda-latn-sn",bdb:"bdb-latn-id",bdc:"bdc-latn-co",bdd:"bdd-latn-zz",bde:"bde-latn-ng",bdf:"bdf-latn-pg",bdg:"bdg-latn-my",bdh:"bdh-latn-ss",bdi:"bdi-latn-sd",bdj:"bdj-latn-ss",bdk:"bdk-latn-az",bdl:"bdl-latn-id",bdm:"bdm-latn-td",bdn:"bdn-latn-cm",bdo:"bdo-latn-td",bdp:"bdp-latn-tz",bdq:"bdq-latn-vn",bdr:"bdr-latn-my",bds:"bds-latn-tz",bdt:"bdt-latn-cf",bdu:"bdu-latn-cm",bdv:"bdv-orya-in",bdw:"bdw-latn-id",bdx:"bdx-latn-id",bdy:"bdy-latn-au",bdz:"bdz-arab-pk",be:"be-cyrl-by",bea:"bea-latn-ca","bea-cans":"bea-cans-ca",beb:"beb-latn-cm",bec:"bec-latn-cm",bed:"bed-latn-id",bee:"bee-deva-in",bef:"bef-latn-zz",beh:"beh-latn-zz",bei:"bei-latn-id",bej:"bej-arab-sd",bek:"bek-latn-pg",bem:"bem-latn-zm",beo:"beo-latn-pg",bep:"bep-latn-id",beq:"beq-latn-cg",bes:"bes-latn-td",bet:"bet-latn-zz",beu:"beu-latn-id",bev:"bev-latn-ci",bew:"bew-latn-id",bex:"bex-latn-zz",bey:"bey-latn-pg",bez:"bez-latn-tz",bfa:"bfa-latn-ss","bfa-arab":"bfa-arab-ss",bfb:"bfb-deva-in",bfc:"bfc-latn-cn",bfd:"bfd-latn-cm",bfe:"bfe-latn-id",bff:"bff-latn-cf",bfg:"bfg-latn-id",bfh:"bfh-latn-pg",bfj:"bfj-latn-cm",bfl:"bfl-latn-cf",bfm:"bfm-latn-cm",bfn:"bfn-latn-tl",bfo:"bfo-latn-bf",bfp:"bfp-latn-cm",bfq:"bfq-taml-in",bfs:"bfs-latn-cn","bfs-hani":"bfs-hani-cn",bft:"bft-arab-pk",bfu:"bfu-tibt-in","bfu-takr":"bfu-takr-in",bfw:"bfw-orya-in",bfx:"bfx-latn-ph",bfy:"bfy-deva-in",bfz:"bfz-deva-in",bg:"bg-cyrl-bg",bga:"bga-latn-ng",bgb:"bgb-latn-id",bgc:"bgc-deva-in",bgd:"bgd-deva-in",bgf:"bgf-latn-cm",bgg:"bgg-latn-in",bgi:"bgi-latn-ph",bgj:"bgj-latn-cm",bgn:"bgn-arab-pk",bgo:"bgo-latn-gn",bgp:"bgp-arab-pk",bgq:"bgq-deva-in",bgr:"bgr-latn-in",bgs:"bgs-latn-ph",bgt:"bgt-latn-sb",bgu:"bgu-latn-ng",bgv:"bgv-latn-id",bgw:"bgw-deva-in",bgx:"bgx-grek-tr",bgy:"bgy-latn-id",bgz:"bgz-latn-id",bha:"bha-deva-in",bhb:"bhb-deva-in",bhc:"bhc-latn-id",bhd:"bhd-deva-in","bhd-arab":"bhd-arab-in","bhd-takr":"bhd-takr-in",bhe:"bhe-arab-pk",bhf:"bhf-latn-pg",bhg:"bhg-latn-zz",bhh:"bhh-cyrl-il","bhh-hebr":"bhh-hebr-il","bhh-latn":"bhh-latn-il",bhi:"bhi-deva-in",bhj:"bhj-deva-np",bhl:"bhl-latn-zz",bhm:"bhm-arab-om",bhn:"bhn-syrc-ge",bho:"bho-deva-in",bhp:"bhp-latn-id",bhq:"bhq-latn-id",bhr:"bhr-latn-mg",bhs:"bhs-latn-cm",bht:"bht-takr-in","bht-deva":"bht-deva-in","bht-latn":"bht-latn-in",bhu:"bhu-deva-in",bhv:"bhv-latn-id",bhw:"bhw-latn-id",bhy:"bhy-latn-zz",bhz:"bhz-latn-id",bi:"bi-latn-vu",bia:"bia-latn-au",bib:"bib-latn-zz",bid:"bid-latn-td",bie:"bie-latn-pg",bif:"bif-latn-gw",big:"big-latn-zz",bik:"bik-latn-ph",bil:"bil-latn-ng",bim:"bim-latn-zz",bin:"bin-latn-ng",bio:"bio-latn-zz",bip:"bip-latn-cd",biq:"biq-latn-zz",bir:"bir-latn-pg",bit:"bit-latn-pg",biu:"biu-latn-in",biv:"biv-latn-gh",biw:"biw-latn-cm",biy:"biy-deva-in",biz:"biz-latn-cd",bja:"bja-latn-cd",bjb:"bjb-latn-au",bjc:"bjc-latn-pg",bjf:"bjf-syrc-il",bjg:"bjg-latn-gw",bjh:"bjh-latn-zz",bji:"bji-ethi-zz",bjj:"bjj-deva-in",bjk:"bjk-latn-pg",bjl:"bjl-latn-pg",bjm:"bjm-arab-iq",bjn:"bjn-latn-id",bjo:"bjo-latn-zz",bjp:"bjp-latn-pg",bjr:"bjr-latn-zz",bjs:"bjs-latn-bb",bjt:"bjt-latn-sn",bju:"bju-latn-cm",bjv:"bjv-latn-td",bjw:"bjw-latn-ci",bjx:"bjx-latn-ph",bjy:"bjy-latn-au",bjz:"bjz-latn-zz",bka:"bka-latn-ng",bkc:"bkc-latn-zz",bkd:"bkd-latn-ph",bkf:"bkf-latn-cd",bkg:"bkg-latn-cf",bkh:"bkh-latn-cm",bki:"bki-latn-vu",bkj:"bkj-latn-cf",bkl:"bkl-latn-id",bkm:"bkm-latn-cm",bkn:"bkn-latn-id",bko:"bko-latn-cm",bkp:"bkp-latn-cd",bkq:"bkq-latn-zz",bkr:"bkr-latn-id",bks:"bks-latn-ph",bkt:"bkt-latn-cd",bku:"bku-latn-ph",bkv:"bkv-latn-zz",bkw:"bkw-latn-cg",bkx:"bkx-latn-tl",bky:"bky-latn-ng",bkz:"bkz-latn-id",bla:"bla-latn-ca",blb:"blb-latn-sb",blc:"blc-latn-ca",bld:"bld-latn-id",ble:"ble-latn-gw",blf:"blf-latn-id",blg:"blg-latn-my",blh:"blh-latn-lr",bli:"bli-latn-cd",blj:"blj-latn-id",blk:"blk-mymr-mm",blm:"blm-latn-ss",bln:"bln-latn-ph",blo:"blo-latn-bj",blp:"blp-latn-sb",blq:"blq-latn-pg",blr:"blr-latn-cn","blr-tale":"blr-tale-cn","blr-thai":"blr-thai-th",bls:"bls-latn-id",blt:"blt-tavt-vn",blv:"blv-latn-ao",blw:"blw-latn-ph",blx:"blx-latn-ph",bly:"bly-latn-bj",blz:"blz-latn-id",bm:"bm-latn-ml",bma:"bma-latn-ng",bmb:"bmb-latn-cd",bmc:"bmc-latn-pg",bmd:"bmd-latn-gn",bme:"bme-latn-cf",bmf:"bmf-latn-sl",bmg:"bmg-latn-cd",bmh:"bmh-latn-zz",bmi:"bmi-latn-td",bmj:"bmj-deva-np",bmk:"bmk-latn-zz",bml:"bml-latn-cd",bmm:"bmm-latn-mg",bmn:"bmn-latn-pg",bmo:"bmo-latn-cm",bmp:"bmp-latn-pg",bmq:"bmq-latn-ml",bmr:"bmr-latn-co",bms:"bms-latn-ne",bmu:"bmu-latn-zz",bmv:"bmv-latn-cm",bmw:"bmw-latn-cg",bmx:"bmx-latn-pg",bmz:"bmz-latn-pg",bn:"bn-beng-bd",bna:"bna-latn-id",bnb:"bnb-latn-my",bnc:"bnc-latn-ph",bnd:"bnd-latn-id",bne:"bne-latn-id",bnf:"bnf-latn-id",bng:"bng-latn-zz",bni:"bni-latn-cd",bnj:"bnj-latn-ph",bnk:"bnk-latn-vu",bnm:"bnm-latn-zz",bnn:"bnn-latn-tw",bno:"bno-latn-ph",bnp:"bnp-latn-zz",bnq:"bnq-latn-id",bnr:"bnr-latn-vu",bns:"bns-deva-in",bnu:"bnu-latn-id",bnv:"bnv-latn-id",bnw:"bnw-latn-pg",bnx:"bnx-latn-cd",bny:"bny-latn-my",bnz:"bnz-latn-cm",bo:"bo-tibt-cn",boa:"boa-latn-pe",bob:"bob-latn-ke",boe:"boe-latn-cm",bof:"bof-latn-bf",boh:"boh-latn-cd",boj:"boj-latn-zz",bok:"bok-latn-cg",bol:"bol-latn-ng",bom:"bom-latn-zz",bon:"bon-latn-zz",boo:"boo-latn-ml",bop:"bop-latn-pg",boq:"boq-latn-pg",bor:"bor-latn-br",bot:"bot-latn-ss",bou:"bou-latn-tz",bov:"bov-latn-gh",bow:"bow-latn-pg",box:"box-latn-bf",boy:"boy-latn-cf",boz:"boz-latn-ml","boz-arab":"boz-arab-ml",bpa:"bpa-latn-vu",bpc:"bpc-latn-cm",bpd:"bpd-latn-cf",bpe:"bpe-latn-pg",bpg:"bpg-latn-id",bph:"bph-cyrl-ru",bpi:"bpi-latn-pg",bpj:"bpj-latn-cd",bpk:"bpk-latn-nc",bpl:"bpl-latn-au",bpm:"bpm-latn-pg",bpo:"bpo-latn-id",bpp:"bpp-latn-id",bpq:"bpq-latn-id",bpr:"bpr-latn-ph",bps:"bps-latn-ph",bpt:"bpt-latn-au",bpu:"bpu-latn-pg",bpv:"bpv-latn-id",bpw:"bpw-latn-pg",bpx:"bpx-deva-in",bpy:"bpy-beng-in",bpz:"bpz-latn-id",bqa:"bqa-latn-bj",bqb:"bqb-latn-id",bqc:"bqc-latn-zz",bqd:"bqd-latn-cm",bqf:"bqf-latn-gn","bqf-arab":"bqf-arab-gn",bqg:"bqg-latn-tg",bqi:"bqi-arab-ir",bqj:"bqj-latn-sn",bqk:"bqk-latn-cf",bql:"bql-latn-pg",bqm:"bqm-latn-cm",bqo:"bqo-latn-cm",bqp:"bqp-latn-zz",bqq:"bqq-latn-id",bqr:"bqr-latn-id",bqs:"bqs-latn-pg",bqt:"bqt-latn-cm",bqu:"bqu-latn-cd",bqv:"bqv-latn-ci",bqw:"bqw-latn-ng",bqx:"bqx-latn-ng",bqz:"bqz-latn-cm",br:"br-latn-fr",bra:"bra-deva-in",brb:"brb-khmr-kh","brb-laoo":"brb-laoo-la","brb-latn":"brb-latn-vn",brc:"brc-latn-gy",brd:"brd-deva-np",brf:"brf-latn-cd",brg:"brg-latn-bo",brh:"brh-arab-pk",bri:"bri-latn-cm",brj:"brj-latn-vu",brk:"brk-arab-sd",brl:"brl-latn-bw",brm:"brm-latn-cd",brn:"brn-latn-cr",brp:"brp-latn-id",brq:"brq-latn-pg",brr:"brr-latn-sb",brs:"brs-latn-id",brt:"brt-latn-ng",bru:"bru-latn-vn","bru-laoo":"bru-laoo-la","bru-thai":"bru-thai-la",brv:"brv-laoo-la",brx:"brx-deva-in",bry:"bry-latn-pg",brz:"brz-latn-zz",bs:"bs-latn-ba",bsa:"bsa-latn-id",bsb:"bsb-latn-bn",bsc:"bsc-latn-sn",bse:"bse-latn-cm",bsf:"bsf-latn-ng",bsh:"bsh-arab-af",bsi:"bsi-latn-cm",bsj:"bsj-latn-zz",bsk:"bsk-arab-pk","bsk-latn":"bsk-latn-pk",bsl:"bsl-latn-ng",bsm:"bsm-latn-id",bsn:"bsn-latn-co",bso:"bso-latn-td",bsp:"bsp-latn-gn",bsq:"bsq-bass-lr",bsr:"bsr-latn-ng",bss:"bss-latn-cm",bst:"bst-ethi-zz",bsu:"bsu-latn-id",bsv:"bsv-latn-gn","bsv-arab":"bsv-arab-gn",bsw:"bsw-latn-et","bsw-ethi":"bsw-ethi-et",bsx:"bsx-latn-ng",bsy:"bsy-latn-my",bta:"bta-latn-ng",btc:"btc-latn-cm",btd:"btd-batk-id",bte:"bte-latn-ng",btf:"btf-latn-td",btg:"btg-latn-ci",bth:"bth-latn-my",bti:"bti-latn-id",btj:"btj-latn-id",btm:"btm-batk-id",btn:"btn-latn-ph",bto:"bto-latn-ph",btp:"btp-latn-pg",btq:"btq-latn-my",btr:"btr-latn-vu",bts:"bts-latn-id","bts-batk":"bts-batk-id",btt:"btt-latn-zz",btu:"btu-latn-ng",btv:"btv-deva-pk",btw:"btw-latn-ph",btx:"btx-latn-id","btx-batk":"btx-batk-id",bty:"bty-latn-id",btz:"btz-latn-id",bua:"bua-cyrl-ru",bub:"bub-latn-td",buc:"buc-latn-yt",bud:"bud-latn-zz",bue:"bue-latn-ca",buf:"buf-latn-cd",bug:"bug-latn-id",buh:"buh-latn-cn",bui:"bui-latn-cg",buj:"buj-latn-ng",buk:"buk-latn-zz",bum:"bum-latn-cm",bun:"bun-latn-sl",buo:"buo-latn-zz",bup:"bup-latn-id",buq:"buq-latn-pg",bus:"bus-latn-zz",but:"but-latn-pg",buu:"buu-latn-zz",buv:"buv-latn-pg",buw:"buw-latn-ga",bux:"bux-latn-ng",buy:"buy-latn-sl",buz:"buz-latn-ng",bva:"bva-latn-td",bvb:"bvb-latn-gq",bvc:"bvc-latn-sb",bvd:"bvd-latn-sb",bve:"bve-latn-id",bvf:"bvf-latn-td",bvg:"bvg-latn-cm",bvh:"bvh-latn-ng",bvi:"bvi-latn-ss",bvj:"bvj-latn-ng",bvk:"bvk-latn-id",bvm:"bvm-latn-cm",bvn:"bvn-latn-pg",bvo:"bvo-latn-td",bvq:"bvq-latn-cf",bvr:"bvr-latn-au",bvt:"bvt-latn-id",bvu:"bvu-latn-id",bvv:"bvv-latn-ve",bvw:"bvw-latn-ng",bvx:"bvx-latn-cg",bvy:"bvy-latn-ph",bvz:"bvz-latn-id",bwa:"bwa-latn-nc",bwb:"bwb-latn-fj",bwc:"bwc-latn-zm",bwd:"bwd-latn-zz",bwe:"bwe-mymr-mm","bwe-latn":"bwe-latn-mm",bwf:"bwf-latn-pg",bwg:"bwg-latn-mz",bwh:"bwh-latn-cm",bwi:"bwi-latn-ve",bwj:"bwj-latn-bf",bwk:"bwk-latn-pg",bwl:"bwl-latn-cd",bwm:"bwm-latn-pg",bwo:"bwo-latn-et","bwo-ethi":"bwo-ethi-et",bwp:"bwp-latn-id",bwq:"bwq-latn-bf",bwr:"bwr-latn-zz",bws:"bws-latn-cd",bwt:"bwt-latn-cm",bwu:"bwu-latn-gh",bww:"bww-latn-cd",bwx:"bwx-latn-cn",bwy:"bwy-latn-bf",bwz:"bwz-latn-cg",bxa:"bxa-latn-sb",bxb:"bxb-latn-ss",bxc:"bxc-latn-gq",bxf:"bxf-latn-pg",bxg:"bxg-latn-cd",bxh:"bxh-latn-zz",bxi:"bxi-latn-au",bxj:"bxj-latn-au",bxl:"bxl-latn-bf",bxm:"bxm-cyrl-mn","bxm-latn":"bxm-latn-mn","bxm-mong":"bxm-mong-mn",bxn:"bxn-latn-au",bxo:"bxo-latn-ng",bxp:"bxp-latn-cm",bxq:"bxq-latn-ng",bxs:"bxs-latn-cm",bxu:"bxu-mong-cn","bxu-cyrl":"bxu-cyrl-cn","bxu-latn":"bxu-latn-cn",bxv:"bxv-latn-td",bxw:"bxw-latn-ml",bxz:"bxz-latn-pg",bya:"bya-latn-ph",byb:"byb-latn-cm",byc:"byc-latn-ng",byd:"byd-latn-id",bye:"bye-latn-zz",byf:"byf-latn-ng",byh:"byh-deva-np",byi:"byi-latn-cd",byj:"byj-latn-ng",byk:"byk-latn-cn",byl:"byl-latn-id",bym:"bym-latn-au",byn:"byn-ethi-er",byp:"byp-latn-ng",byr:"byr-latn-zz",bys:"bys-latn-zz",byv:"byv-latn-cm",byw:"byw-deva-np",byx:"byx-latn-zz",byz:"byz-latn-pg",bza:"bza-latn-zz",bzb:"bzb-latn-id",bzc:"bzc-latn-mg",bzd:"bzd-latn-cr",bze:"bze-latn-ml",bzf:"bzf-latn-zz",bzh:"bzh-latn-zz",bzi:"bzi-thai-th",bzj:"bzj-latn-bz",bzk:"bzk-latn-ni",bzl:"bzl-latn-id",bzm:"bzm-latn-cd",bzn:"bzn-latn-id",bzo:"bzo-latn-cd",bzp:"bzp-latn-id",bzq:"bzq-latn-id",bzr:"bzr-latn-au",bzt:"bzt-latn-001",bzu:"bzu-latn-id",bzv:"bzv-latn-cm",bzw:"bzw-latn-zz",bzx:"bzx-latn-ml",bzy:"bzy-latn-ng",bzz:"bzz-latn-ng",ca:"ca-latn-es",caa:"caa-latn-gt",cab:"cab-latn-hn",cac:"cac-latn-gt",cad:"cad-latn-us",cae:"cae-latn-sn",caf:"caf-latn-ca","caf-cans":"caf-cans-ca",cag:"cag-latn-py",cah:"cah-latn-pe",caj:"caj-latn-ar",cak:"cak-latn-gt",cal:"cal-latn-mp",cam:"cam-latn-nc",can:"can-latn-zz",cao:"cao-latn-bo",cap:"cap-latn-bo",caq:"caq-latn-in",car:"car-latn-ve",cas:"cas-latn-bo",cav:"cav-latn-bo",caw:"caw-latn-bo",cax:"cax-latn-bo",cay:"cay-latn-ca",caz:"caz-latn-bo",cbb:"cbb-latn-co",cbc:"cbc-latn-co",cbd:"cbd-latn-co",cbg:"cbg-latn-co",cbi:"cbi-latn-ec",cbj:"cbj-latn-zz",cbk:"cbk-latn-ph","cbk-brai":"cbk-brai-ph",cbl:"cbl-latn-mm",cbn:"cbn-thai-th",cbo:"cbo-latn-ng",cbq:"cbq-latn-ng",cbr:"cbr-latn-pe",cbs:"cbs-latn-pe",cbt:"cbt-latn-pe",cbu:"cbu-latn-pe",cbv:"cbv-latn-co",cbw:"cbw-latn-ph",cby:"cby-latn-co",ccc:"ccc-latn-pe",ccd:"ccd-latn-br",cce:"cce-latn-mz",ccg:"ccg-latn-ng",cch:"cch-latn-ng",ccj:"ccj-latn-gw",ccl:"ccl-latn-tz",ccm:"ccm-latn-my",cco:"cco-latn-mx",ccp:"ccp-cakm-bd",ccr:"ccr-latn-sv",cde:"cde-telu-in",cdf:"cdf-latn-in","cdf-beng":"cdf-beng-in",cdh:"cdh-deva-in","cdh-takr":"cdh-takr-in",cdi:"cdi-gujr-in",cdj:"cdj-deva-in",cdm:"cdm-deva-np","cdm-latn":"cdm-latn-np",cdo:"cdo-hans-cn","cdo-hant":"cdo-hant-cn","cdo-latn":"cdo-latn-cn",cdr:"cdr-latn-ng",cdz:"cdz-beng-in",ce:"ce-cyrl-ru",cea:"cea-latn-us",ceb:"ceb-latn-ph",ceg:"ceg-latn-py",cek:"cek-latn-mm",cen:"cen-latn-ng",cet:"cet-latn-ng",cey:"cey-latn-mm",cfa:"cfa-latn-zz",cfd:"cfd-latn-ng",cfg:"cfg-latn-ng",cfm:"cfm-latn-mm","cfm-beng":"cfm-beng-in",cga:"cga-latn-pg",cgc:"cgc-latn-ph",cgg:"cgg-latn-ug",cgk:"cgk-tibt-bt",ch:"ch-latn-gu",chb:"chb-latn-co",chd:"chd-latn-mx",chf:"chf-latn-mx",chg:"chg-arab-tm",chh:"chh-latn-us",chj:"chj-latn-mx",chk:"chk-latn-fm",chl:"chl-latn-us",chm:"chm-cyrl-ru",chn:"chn-latn-us","chn-dupl":"chn-dupl-us",cho:"cho-latn-us",chp:"chp-latn-ca",chq:"chq-latn-mx",chr:"chr-cher-us",cht:"cht-latn-pe",chw:"chw-latn-mz",chx:"chx-deva-np",chy:"chy-latn-us",chz:"chz-latn-mx",cia:"cia-latn-id","cia-arab":"cia-arab-id","cia-hang":"cia-hang-id",cib:"cib-latn-bj",cic:"cic-latn-us",cie:"cie-latn-ng",cih:"cih-deva-in",cim:"cim-latn-it",cin:"cin-latn-br",cip:"cip-latn-mx",cir:"cir-latn-nc",ciw:"ciw-latn-us","ciw-cans":"ciw-cans-us",ciy:"ciy-latn-ve",cja:"cja-arab-kh",cje:"cje-latn-vn",cjh:"cjh-latn-us",cji:"cji-cyrl-ru",cjk:"cjk-latn-ao",cjm:"cjm-cham-vn",cjn:"cjn-latn-pg",cjo:"cjo-latn-pe",cjp:"cjp-latn-cr",cjs:"cjs-latn-ru","cjs-cyrl":"cjs-cyrl-ru",cjv:"cjv-latn-zz",cjy:"cjy-hans-cn","cjy-hant":"cjy-hant-cn",ckb:"ckb-arab-iq",ckl:"ckl-latn-zz",ckm:"ckm-latn-hr","ckm-glag":"ckm-glag-hr",ckn:"ckn-latn-mm",cko:"cko-latn-zz",ckq:"ckq-latn-td",ckr:"ckr-latn-pg",cks:"cks-latn-nc",ckt:"ckt-cyrl-ru",cku:"cku-latn-us",ckv:"ckv-latn-tw",ckx:"ckx-latn-cm",cky:"cky-latn-zz",ckz:"ckz-latn-gt",cla:"cla-latn-zz",clc:"clc-latn-ca",cle:"cle-latn-mx",clh:"clh-arab-pk",cli:"cli-latn-gh",clj:"clj-latn-mm",clk:"clk-latn-in","clk-tibt":"clk-tibt-cn",cll:"cll-latn-gh",clm:"clm-latn-us",clo:"clo-latn-mx",clt:"clt-latn-mm",clu:"clu-latn-ph",clw:"clw-cyrl-ru",cly:"cly-latn-mx",cma:"cma-latn-vn",cme:"cme-latn-zz",cmg:"cmg-soyo-mn",cmi:"cmi-latn-co",cml:"cml-latn-id",cmo:"cmo-latn-vn","cmo-kh":"cmo-latn-kh","cmo-khmr":"cmo-khmr-kh",cmr:"cmr-latn-mm",cms:"cms-latn-it",cmt:"cmt-latn-za",cna:"cna-tibt-in",cnb:"cnb-latn-mm",cnc:"cnc-latn-vn",cng:"cng-latn-cn",cnh:"cnh-latn-mm",cni:"cni-latn-pe",cnk:"cnk-latn-mm",cnl:"cnl-latn-mx",cnp:"cnp-hans-cn","cnp-hant":"cnp-hant-cn",cnq:"cnq-latn-cm",cns:"cns-latn-id",cnt:"cnt-latn-mx",cnw:"cnw-latn-mm",cnx:"cnx-latn-gb",co:"co-latn-fr",coa:"coa-latn-au",cob:"cob-latn-mx",coc:"coc-latn-mx",cod:"cod-latn-pe",coe:"coe-latn-co",cof:"cof-latn-ec",cog:"cog-thai-th",coh:"coh-latn-ke",coj:"coj-latn-mx",cok:"cok-latn-mx",col:"col-latn-us",com:"com-latn-us",coo:"coo-latn-ca",cop:"cop-copt-eg",coq:"coq-latn-us",cot:"cot-latn-pe",cou:"cou-latn-sn",cox:"cox-latn-pe",coz:"coz-latn-mx",cpa:"cpa-latn-mx",cpb:"cpb-latn-pe",cpc:"cpc-latn-pe",cpg:"cpg-grek-gr",cpi:"cpi-latn-nr",cpn:"cpn-latn-gh",cpo:"cpo-latn-bf",cps:"cps-latn-ph",cpu:"cpu-latn-pe",cpx:"cpx-latn-cn",cpy:"cpy-latn-pe",cqd:"cqd-latn-cn",cr:"cr-cans-ca",crb:"crb-latn-vc",crc:"crc-latn-vu",crd:"crd-latn-us",crf:"crf-latn-co",crg:"crg-latn-ca",crh:"crh-cyrl-ua",cri:"cri-latn-st",crj:"crj-cans-ca","crj-latn":"crj-latn-ca",crk:"crk-cans-ca",crl:"crl-cans-ca",crm:"crm-cans-ca",crn:"crn-latn-mx",cro:"cro-latn-us",crq:"crq-latn-ar",crs:"crs-latn-sc",crt:"crt-latn-ar",crv:"crv-latn-in",crw:"crw-latn-vn",crx:"crx-latn-ca","crx-cans":"crx-cans-ca",cry:"cry-latn-ng",crz:"crz-latn-us",cs:"cs-latn-cz",csa:"csa-latn-mx",csb:"csb-latn-pl",csh:"csh-mymr-mm","csh-latn":"csh-latn-mm",csj:"csj-latn-mm",csk:"csk-latn-sn",csm:"csm-latn-us",cso:"cso-latn-mx",csp:"csp-hans-cn","csp-hant":"csp-hant-cn",css:"css-latn-us",cst:"cst-latn-us",csv:"csv-latn-mm",csw:"csw-cans-ca",csy:"csy-latn-mm",csz:"csz-latn-us",cta:"cta-latn-mx",ctc:"ctc-latn-us",ctd:"ctd-pauc-mm",cte:"cte-latn-mx",ctg:"ctg-beng-bd","ctg-arab":"ctg-arab-bd","ctg-latn":"ctg-latn-bd",cth:"cth-latn-mm",ctl:"ctl-latn-mx",ctm:"ctm-latn-us",ctn:"ctn-deva-np",cto:"cto-latn-co",ctp:"ctp-latn-mx",cts:"cts-latn-ph",ctt:"ctt-taml-in",ctu:"ctu-latn-mx",ctz:"ctz-latn-mx",cu:"cu-cyrl-ru","cu-glag":"cu-glag-bg",cua:"cua-latn-vn",cub:"cub-latn-co",cuc:"cuc-latn-mx",cuh:"cuh-latn-ke",cui:"cui-latn-co",cuj:"cuj-latn-pe",cuk:"cuk-latn-pa",cul:"cul-latn-br",cuo:"cuo-latn-ve",cup:"cup-latn-us",cut:"cut-latn-mx",cuu:"cuu-lana-cn",cuv:"cuv-latn-cm",cux:"cux-latn-mx",cv:"cv-cyrl-ru",cvg:"cvg-latn-in","cvg-tibt":"cvg-tibt-in",cvn:"cvn-latn-mx",cwa:"cwa-latn-tz",cwb:"cwb-latn-mz",cwe:"cwe-latn-tz",cwg:"cwg-latn-my",cwt:"cwt-latn-sn",cy:"cy-latn-gb",cya:"cya-latn-mx",cyb:"cyb-latn-bo",cyo:"cyo-latn-ph",czh:"czh-hans-cn","czh-hant":"czh-hant-cn",czk:"czk-hebr-cz",czn:"czn-latn-mx",czt:"czt-latn-mm",da:"da-latn-dk",daa:"daa-latn-td",dac:"dac-latn-pg",dad:"dad-latn-zz",dae:"dae-latn-cm",daf:"daf-latn-ci",dag:"dag-latn-zz",dah:"dah-latn-zz",dai:"dai-latn-td",daj:"daj-latn-sd",dak:"dak-latn-us",dal:"dal-latn-ke",dam:"dam-latn-ng",dao:"dao-latn-mm",daq:"daq-deva-in",dar:"dar-cyrl-ru",das:"das-latn-ci",dau:"dau-latn-td",dav:"dav-latn-ke",daw:"daw-latn-ph",dax:"dax-latn-au",daz:"daz-latn-id",dba:"dba-latn-ml",dbb:"dbb-latn-ng",dbd:"dbd-latn-zz",dbe:"dbe-latn-id",dbf:"dbf-latn-id",dbg:"dbg-latn-ml",dbi:"dbi-latn-ng",dbj:"dbj-latn-my","dbj-arab":"dbj-arab-my",dbl:"dbl-latn-au",dbm:"dbm-latn-ng",dbn:"dbn-latn-id",dbo:"dbo-latn-ng",dbp:"dbp-latn-ng",dbq:"dbq-latn-zz",dbt:"dbt-latn-ml",dbu:"dbu-latn-ml",dbv:"dbv-latn-ng",dbw:"dbw-latn-ml",dby:"dby-latn-pg",dcc:"dcc-arab-in",dcr:"dcr-latn-vi",dda:"dda-latn-au",ddd:"ddd-latn-ss",dde:"dde-latn-cg",ddg:"ddg-latn-tl",ddi:"ddi-latn-pg",ddj:"ddj-latn-au",ddn:"ddn-latn-zz",ddo:"ddo-cyrl-ru",ddr:"ddr-latn-au",dds:"dds-latn-ml",ddw:"ddw-latn-id",de:"de-latn-de",dec:"dec-latn-sd",ded:"ded-latn-zz",dee:"dee-latn-lr",def:"def-arab-ir",deg:"deg-latn-ng",deh:"deh-arab-pk",dei:"dei-latn-id",dek:"dek-latn-cm",del:"del-latn-us",dem:"dem-latn-id",den:"den-latn-ca",deq:"deq-latn-cf",der:"der-beng-in","der-latn":"der-latn-in",des:"des-latn-br",dev:"dev-latn-pg",dez:"dez-latn-cd",dga:"dga-latn-zz",dgb:"dgb-latn-ml",dgc:"dgc-latn-ph",dgd:"dgd-latn-bf",dge:"dge-latn-pg",dgg:"dgg-latn-pg",dgh:"dgh-latn-zz",dgi:"dgi-latn-zz",dgk:"dgk-latn-cf",dgl:"dgl-arab-zz",dgn:"dgn-latn-au",dgr:"dgr-latn-ca",dgs:"dgs-latn-bf",dgt:"dgt-latn-au",dgw:"dgw-latn-au",dgx:"dgx-latn-pg",dgz:"dgz-latn-zz",dhg:"dhg-latn-au",dhi:"dhi-deva-np",dhl:"dhl-latn-au",dhm:"dhm-latn-ao",dhn:"dhn-gujr-in",dho:"dho-deva-in",dhr:"dhr-latn-au",dhs:"dhs-latn-tz",dhu:"dhu-latn-au",dhv:"dhv-latn-nc",dhw:"dhw-deva-np",dhx:"dhx-latn-au",dia:"dia-latn-zz",dib:"dib-latn-ss",dic:"dic-latn-ci",did:"did-latn-ss",dif:"dif-latn-au",dig:"dig-latn-ke",dih:"dih-latn-mx",dii:"dii-latn-cm",dij:"dij-latn-id",dil:"dil-latn-sd",din:"din-latn-ss","din-arab":"din-arab-ss",dio:"dio-latn-ng",dip:"dip-latn-ss",dir:"dir-latn-ng",dis:"dis-latn-in","dis-beng":"dis-beng-in",diu:"diu-latn-na",diw:"diw-latn-ss",dix:"dix-latn-vu",diy:"diy-latn-id",diz:"diz-latn-cd",dja:"dja-latn-au",djb:"djb-latn-au",djc:"djc-latn-td",djd:"djd-latn-au",dje:"dje-latn-ne",djf:"djf-latn-au",dji:"dji-latn-au",djj:"djj-latn-au",djk:"djk-latn-sr",djm:"djm-latn-ml",djn:"djn-latn-au",djo:"djo-latn-id",djr:"djr-latn-au",dju:"dju-latn-pg",djw:"djw-latn-au",dka:"dka-tibt-bt",dkg:"dkg-latn-ng",dkk:"dkk-latn-id",dkr:"dkr-latn-my",dks:"dks-latn-ss",dkx:"dkx-latn-cm",dlg:"dlg-cyrl-ru",dlm:"dlm-latn-hr",dln:"dln-latn-in",dma:"dma-latn-ga",dmb:"dmb-latn-ml",dmc:"dmc-latn-pg",dmd:"dmd-latn-au",dme:"dme-latn-cm",dmf:"dmf-medf-ng",dmg:"dmg-latn-my",dmk:"dmk-arab-pk",dml:"dml-arab-pk",dmm:"dmm-latn-cm",dmo:"dmo-latn-cm",dmr:"dmr-latn-id",dms:"dms-latn-id",dmu:"dmu-latn-id",dmv:"dmv-latn-my",dmw:"dmw-latn-au",dmx:"dmx-latn-mz",dmy:"dmy-latn-id",dna:"dna-latn-id",dnd:"dnd-latn-pg",dne:"dne-latn-tz",dng:"dng-cyrl-kg","dng-arab":"dng-arab-kg",dni:"dni-latn-id",dnj:"dnj-latn-ci",dnk:"dnk-latn-id",dnn:"dnn-latn-bf",dno:"dno-latn-cd",dnr:"dnr-latn-pg",dnt:"dnt-latn-id",dnu:"dnu-mymr-mm",dnv:"dnv-mymr-mm",dnw:"dnw-latn-id",dny:"dny-latn-br",doa:"doa-latn-pg",dob:"dob-latn-zz",doc:"doc-latn-cn",doe:"doe-latn-tz",dof:"dof-latn-pg",doh:"doh-latn-ng",doi:"doi-deva-in",dok:"dok-latn-id",dol:"dol-latn-pg",don:"don-latn-pg",doo:"doo-latn-cd",dop:"dop-latn-zz",dor:"dor-latn-sb",dos:"dos-latn-bf",dot:"dot-latn-ng",dov:"dov-latn-zw",dow:"dow-latn-zz",dox:"dox-ethi-et",doy:"doy-latn-gh",dpp:"dpp-latn-my",drc:"drc-latn-pt",dre:"dre-tibt-np",drg:"drg-latn-my",drh:"drh-mong-cn",dri:"dri-latn-zz",drl:"drl-latn-au",drn:"drn-latn-id",dro:"dro-latn-my",drq:"drq-deva-np",drs:"drs-ethi-zz",drt:"drt-latn-nl",dru:"dru-latn-tw",dry:"dry-deva-np",dsb:"dsb-latn-de",dsh:"dsh-latn-ke",dsi:"dsi-latn-td",dsn:"dsn-latn-id",dso:"dso-orya-in",dsq:"dsq-latn-ml","dsq-arab":"dsq-arab-ml",dta:"dta-latn-cn","dta-cyrl":"dta-cyrl-cn","dta-hans":"dta-hans-cn",dtb:"dtb-latn-my",dtd:"dtd-latn-ca",dth:"dth-latn-au",dti:"dti-latn-ml",dtk:"dtk-latn-ml",dtm:"dtm-latn-ml",dto:"dto-latn-ml",dtp:"dtp-latn-my",dtr:"dtr-latn-my",dts:"dts-latn-zz",dtt:"dtt-latn-ml",dtu:"dtu-latn-ml",dty:"dty-deva-np",dua:"dua-latn-cm",dub:"dub-gujr-in",duc:"duc-latn-zz",dud:"dud-latn-zz",due:"due-latn-ph",duf:"duf-latn-nc",dug:"dug-latn-zz",duh:"duh-deva-in","duh-gujr":"duh-gujr-in",dui:"dui-latn-pg",duk:"duk-latn-pg",dul:"dul-latn-ph",dum:"dum-latn-nl",dun:"dun-latn-id",duo:"duo-latn-ph",dup:"dup-latn-id",duq:"duq-latn-id",dur:"dur-latn-cm",dus:"dus-deva-np",duu:"duu-latn-cn",duv:"duv-latn-id",duw:"duw-latn-id",dux:"dux-latn-ml",duy:"duy-latn-ph",duz:"duz-latn-cm",dv:"dv-thaa-mv",dva:"dva-latn-zz",dwa:"dwa-latn-ng",dwk:"dwk-orya-in",dwr:"dwr-latn-et","dwr-ethi":"dwr-ethi-et",dws:"dws-latn-001",dwu:"dwu-latn-au",dww:"dww-latn-zz",dwy:"dwy-latn-au",dwz:"dwz-deva-np",dya:"dya-latn-bf",dyb:"dyb-latn-au",dyd:"dyd-latn-au",dyg:"dyg-latn-ph",dyi:"dyi-latn-ci",dym:"dym-latn-ml",dyn:"dyn-latn-au",dyo:"dyo-latn-sn",dyu:"dyu-latn-bf",dyy:"dyy-latn-au",dz:"dz-tibt-bt",dza:"dza-latn-ng",dze:"dze-latn-au",dzg:"dzg-latn-zz",dzl:"dzl-tibt-bt",dzn:"dzn-latn-cd",eaa:"eaa-latn-au",ebc:"ebc-latn-id",ebg:"ebg-latn-ng",ebk:"ebk-latn-ph",ebo:"ebo-latn-cg",ebr:"ebr-latn-ci",ebu:"ebu-latn-ke",ecr:"ecr-grek-gr",ecy:"ecy-cprt-cy",ee:"ee-latn-gh",efa:"efa-latn-ng",efe:"efe-latn-cd",efi:"efi-latn-ng",ega:"ega-latn-ci",egl:"egl-latn-it",egm:"egm-latn-tz",ego:"ego-latn-ng",egy:"egy-egyp-eg",ehu:"ehu-latn-ng",eip:"eip-latn-id",eit:"eit-latn-pg",eiv:"eiv-latn-pg",eja:"eja-latn-gw",eka:"eka-latn-zz",eke:"eke-latn-ng",ekg:"ekg-latn-id",eki:"eki-latn-ng",ekl:"ekl-latn-bd",ekm:"ekm-latn-cm",eko:"eko-latn-mz","eko-arab":"eko-arab-mz",ekp:"ekp-latn-ng",ekr:"ekr-latn-ng",eky:"eky-kali-mm",el:"el-grek-gr",ele:"ele-latn-pg",elk:"elk-latn-pg",elm:"elm-latn-ng",elo:"elo-latn-ke",elu:"elu-latn-pg",ema:"ema-latn-zz",emb:"emb-latn-id",eme:"eme-latn-gf",emg:"emg-deva-np",emi:"emi-latn-zz",emm:"emm-latn-mx",emn:"emn-latn-cm",emp:"emp-latn-pa",ems:"ems-latn-us","ems-cyrl":"ems-cyrl-us",emu:"emu-deva-in",emw:"emw-latn-id",emx:"emx-latn-fr",emz:"emz-latn-cm",en:"en-latn-us","en-shaw":"en-shaw-gb",ena:"ena-latn-pg",enb:"enb-latn-ke",enc:"enc-latn-vn",end:"end-latn-id",enf:"enf-cyrl-ru",enh:"enh-cyrl-ru",enl:"enl-latn-py",enm:"enm-latn-gb",enn:"enn-latn-zz",eno:"eno-latn-id",enq:"enq-latn-zz",enr:"enr-latn-id",env:"env-latn-ng",enw:"enw-latn-ng",enx:"enx-latn-py",eo:"eo-latn-001",eot:"eot-latn-ci",epi:"epi-latn-ng",era:"era-taml-in",erg:"erg-latn-vu",erh:"erh-latn-ng",eri:"eri-latn-zz",erk:"erk-latn-vu",err:"err-latn-au",ert:"ert-latn-id",erw:"erw-latn-id",es:"es-latn-es",ese:"ese-latn-bo",esg:"esg-gonm-in",esh:"esh-arab-ir",esi:"esi-latn-us",esm:"esm-latn-ci",ess:"ess-latn-us","ess-cyrl":"ess-cyrl-us",esu:"esu-latn-us",esy:"esy-latn-ph",et:"et-latn-ee",etb:"etb-latn-ng",etn:"etn-latn-vu",eto:"eto-latn-cm",etr:"etr-latn-zz",ets:"ets-latn-ng",ett:"ett-ital-it",etu:"etu-latn-zz",etx:"etx-latn-zz",etz:"etz-latn-id",eu:"eu-latn-es",eve:"eve-cyrl-ru",evh:"evh-latn-ng",evn:"evn-cyrl-ru","evn-latn":"evn-latn-cn","evn-mong":"evn-mong-cn",ewo:"ewo-latn-cm",ext:"ext-latn-es",eya:"eya-latn-us",eyo:"eyo-latn-ke",eza:"eza-latn-zz",eze:"eze-latn-ng",fa:"fa-arab-ir",faa:"faa-latn-zz",fab:"fab-latn-zz",fad:"fad-latn-pg",faf:"faf-latn-sb",fag:"fag-latn-zz",fah:"fah-latn-ng",fai:"fai-latn-zz",faj:"faj-latn-pg",fak:"fak-latn-cm",fal:"fal-latn-cm",fam:"fam-latn-ng",fan:"fan-latn-gq",fap:"fap-latn-sn",far:"far-latn-sb",fau:"fau-latn-id",fax:"fax-latn-es",fay:"fay-arab-ir",faz:"faz-arab-ir",fbl:"fbl-latn-ph",fer:"fer-latn-ss",ff:"ff-latn-sn","ff-adlm":"ff-adlm-gn",ffi:"ffi-latn-zz",ffm:"ffm-latn-ml",fgr:"fgr-latn-td",fi:"fi-latn-fi",fia:"fia-arab-sd",fie:"fie-latn-ng",fif:"fif-latn-sa",fil:"fil-latn-ph",fip:"fip-latn-tz",fir:"fir-latn-ng",fit:"fit-latn-se",fiw:"fiw-latn-pg",fj:"fj-latn-fj",fkk:"fkk-latn-ng",fkv:"fkv-latn-no",fla:"fla-latn-us",flh:"flh-latn-id",fli:"fli-latn-ng",fll:"fll-latn-cm",fln:"fln-latn-au",flr:"flr-latn-zz",fly:"fly-latn-za",fmp:"fmp-latn-zz",fmu:"fmu-deva-in",fnb:"fnb-latn-vu",fng:"fng-latn-za",fni:"fni-latn-td",fo:"fo-latn-fo",fod:"fod-latn-zz",foi:"foi-latn-pg",fom:"fom-latn-cd",fon:"fon-latn-bj",for:"for-latn-zz",fos:"fos-latn-tw",fpe:"fpe-latn-zz",fqs:"fqs-latn-zz",fr:"fr-latn-fr",frc:"frc-latn-us",frd:"frd-latn-id",frk:"frk-latn-de",frm:"frm-latn-fr",fro:"fro-latn-fr",frp:"frp-latn-fr",frq:"frq-latn-pg",frr:"frr-latn-de",frs:"frs-latn-de",frt:"frt-latn-vu",fub:"fub-arab-cm",fud:"fud-latn-wf",fue:"fue-latn-zz",fuf:"fuf-latn-gn",fuh:"fuh-latn-zz",fui:"fui-latn-td",fum:"fum-latn-ng",fun:"fun-latn-br",fuq:"fuq-latn-ne",fur:"fur-latn-it",fut:"fut-latn-vu",fuu:"fuu-latn-cd",fuv:"fuv-latn-ng",fuy:"fuy-latn-zz",fvr:"fvr-latn-sd",fwa:"fwa-latn-nc",fwe:"fwe-latn-na",fy:"fy-latn-nl",ga:"ga-latn-ie",gaa:"gaa-latn-gh",gab:"gab-latn-td",gac:"gac-latn-in","gac-deva":"gac-deva-in",gad:"gad-latn-ph",gae:"gae-latn-ve",gaf:"gaf-latn-zz",gag:"gag-latn-md",gah:"gah-latn-zz",gai:"gai-latn-pg",gaj:"gaj-latn-zz",gak:"gak-latn-id",gal:"gal-latn-tl",gam:"gam-latn-zz",gan:"gan-hans-cn",gao:"gao-latn-pg",gap:"gap-latn-pg",gaq:"gaq-orya-in",gar:"gar-latn-pg",gas:"gas-gujr-in",gat:"gat-latn-pg",gau:"gau-telu-in",gaw:"gaw-latn-zz",gax:"gax-latn-et","gax-ethi":"gax-ethi-et",gay:"gay-latn-id",gba:"gba-latn-zz",gbb:"gbb-latn-au",gbd:"gbd-latn-au",gbe:"gbe-latn-pg",gbf:"gbf-latn-zz",gbg:"gbg-latn-cf",gbh:"gbh-latn-bj",gbi:"gbi-latn-id",gbj:"gbj-orya-in",gbk:"gbk-deva-in","gbk-takr":"gbk-takr-in",gbl:"gbl-gujr-in","gbl-deva":"gbl-deva-in",gbm:"gbm-deva-in",gbn:"gbn-latn-ss",gbp:"gbp-latn-cf",gbq:"gbq-latn-cf",gbr:"gbr-latn-ng",gbs:"gbs-latn-bj",gbu:"gbu-latn-au",gbv:"gbv-latn-cf",gbw:"gbw-latn-au",gbx:"gbx-latn-bj",gby:"gby-latn-zz",gbz:"gbz-arab-ir",gcc:"gcc-latn-pg",gcd:"gcd-latn-au",gcf:"gcf-latn-gp",gcl:"gcl-latn-gd",gcn:"gcn-latn-pg",gcr:"gcr-latn-gf",gct:"gct-latn-ve",gd:"gd-latn-gb",gdb:"gdb-orya-in","gdb-telu":"gdb-telu-in",gdc:"gdc-latn-au",gdd:"gdd-latn-pg",gde:"gde-latn-zz",gdf:"gdf-latn-ng",gdg:"gdg-latn-ph",gdh:"gdh-latn-au",gdi:"gdi-latn-cf",gdj:"gdj-latn-au",gdk:"gdk-latn-td",gdl:"gdl-latn-et","gdl-ethi":"gdl-ethi-et",gdm:"gdm-latn-td",gdn:"gdn-latn-zz",gdo:"gdo-cyrl-ru",gdq:"gdq-latn-ye",gdr:"gdr-latn-zz",gdt:"gdt-latn-au",gdu:"gdu-latn-ng",gdx:"gdx-deva-in",gea:"gea-latn-ng",geb:"geb-latn-zz",gec:"gec-latn-lr",ged:"ged-latn-ng",gef:"gef-latn-id",geg:"geg-latn-ng",geh:"geh-latn-ca",gei:"gei-latn-id",gej:"gej-latn-zz",gek:"gek-latn-ng",gel:"gel-latn-zz",geq:"geq-latn-cf",ges:"ges-latn-id",gev:"gev-latn-ga",gew:"gew-latn-ng",gex:"gex-latn-so",gey:"gey-latn-cd",gez:"gez-ethi-et",gfk:"gfk-latn-zz",gga:"gga-latn-sb",ggb:"ggb-latn-lr",ggd:"ggd-latn-au",gge:"gge-latn-au",ggg:"ggg-arab-pk",ggk:"ggk-latn-au",ggl:"ggl-latn-pg",ggn:"ggn-deva-np",ggt:"ggt-latn-pg",ggu:"ggu-latn-ci",ggw:"ggw-latn-pg",gha:"gha-arab-ly","gha-latn":"gha-latn-ly","gha-tfng":"gha-tfng-ly",ghc:"ghc-latn-gb",ghe:"ghe-deva-np",ghk:"ghk-latn-mm",ghn:"ghn-latn-sb",ghr:"ghr-arab-pk",ghs:"ghs-latn-zz",ght:"ght-tibt-np",gia:"gia-latn-au",gib:"gib-latn-ng",gic:"gic-latn-za",gid:"gid-latn-cm",gie:"gie-latn-ci",gig:"gig-arab-pk",gih:"gih-latn-au",gil:"gil-latn-ki",gim:"gim-latn-zz",gin:"gin-cyrl-ru",gip:"gip-latn-pg",giq:"giq-latn-vn",gir:"gir-latn-vn",gis:"gis-latn-cm",git:"git-latn-ca",gix:"gix-latn-cd",giy:"giy-latn-au",giz:"giz-latn-cm",gjk:"gjk-arab-pk",gjm:"gjm-latn-au",gjn:"gjn-latn-zz",gjr:"gjr-latn-au",gju:"gju-arab-pk",gka:"gka-latn-pg",gkd:"gkd-latn-pg",gke:"gke-latn-cm",gkn:"gkn-latn-zz",gko:"gko-latn-au",gkp:"gkp-latn-zz",gku:"gku-latn-za",gl:"gl-latn-es",glb:"glb-latn-ng",glc:"glc-latn-td",gld:"gld-cyrl-ru",glh:"glh-arab-af",glj:"glj-latn-td",glk:"glk-arab-ir",gll:"gll-latn-au",glo:"glo-latn-ng",glr:"glr-latn-lr",glu:"glu-latn-td",glw:"glw-latn-ng",gma:"gma-latn-au",gmb:"gmb-latn-sb",gmd:"gmd-latn-ng",gmg:"gmg-latn-pg",gmh:"gmh-latn-de",gmm:"gmm-latn-zz",gmn:"gmn-latn-cm",gmr:"gmr-latn-au",gmu:"gmu-latn-pg",gmv:"gmv-ethi-zz",gmx:"gmx-latn-tz",gmy:"gmy-linb-gr",gmz:"gmz-latn-ng",gn:"gn-latn-py",gna:"gna-latn-bf",gnb:"gnb-latn-in",gnc:"gnc-latn-es",gnd:"gnd-latn-zz",gne:"gne-latn-ng",gng:"gng-latn-zz",gnh:"gnh-latn-ng",gni:"gni-latn-au",gnj:"gnj-latn-ci",gnk:"gnk-latn-bw",gnl:"gnl-latn-au",gnm:"gnm-latn-pg",gnn:"gnn-latn-au",gnq:"gnq-latn-my",gnr:"gnr-latn-au",gnt:"gnt-latn-pg",gnu:"gnu-latn-pg",gnw:"gnw-latn-bo",gnz:"gnz-latn-cf",goa:"goa-latn-ci",gob:"gob-latn-co",goc:"goc-latn-pg",god:"god-latn-zz",goe:"goe-tibt-bt",gof:"gof-ethi-zz",gog:"gog-latn-tz",goh:"goh-latn-de",goi:"goi-latn-zz",gok:"gok-deva-in",gol:"gol-latn-lr",gom:"gom-deva-in",gon:"gon-telu-in",goo:"goo-latn-fj",gop:"gop-latn-id",goq:"goq-latn-id",gor:"gor-latn-id",gos:"gos-latn-nl",got:"got-goth-ua",gou:"gou-latn-cm",gov:"gov-latn-ci",gow:"gow-latn-tz",gox:"gox-latn-cd",goy:"goy-latn-td",gpa:"gpa-latn-ng",gpe:"gpe-latn-gh",gpn:"gpn-latn-pg",gqa:"gqa-latn-ng",gqn:"gqn-latn-br",gqr:"gqr-latn-td",gra:"gra-deva-in","gra-gujr":"gra-gujr-in",grb:"grb-latn-zz",grc:"grc-cprt-cy","grc-linb":"grc-linb-gr",grd:"grd-latn-ng",grg:"grg-latn-pg",grh:"grh-latn-ng",gri:"gri-latn-sb",grj:"grj-latn-lr",grm:"grm-latn-my",grq:"grq-latn-pg",grs:"grs-latn-id",grt:"grt-beng-in",gru:"gru-ethi-et","gru-latn":"gru-latn-et",grv:"grv-latn-lr",grw:"grw-latn-zz",grx:"grx-latn-pg",gry:"gry-latn-lr",grz:"grz-latn-pg",gsl:"gsl-latn-sn",gsn:"gsn-latn-pg",gso:"gso-latn-cf",gsp:"gsp-latn-pg",gsw:"gsw-latn-ch",gta:"gta-latn-br",gtu:"gtu-latn-au",gu:"gu-gujr-in",gua:"gua-latn-ng",gub:"gub-latn-br",guc:"guc-latn-co",gud:"gud-latn-zz",gue:"gue-latn-au",guf:"guf-latn-au",guh:"guh-latn-co",gui:"gui-latn-bo",guk:"guk-latn-et","guk-ethi":"guk-ethi-et",gul:"gul-latn-us",gum:"gum-latn-co",gun:"gun-latn-br",guo:"guo-latn-co",gup:"gup-latn-au",guq:"guq-latn-py",gur:"gur-latn-gh",gut:"gut-latn-cr",guu:"guu-latn-ve",guw:"guw-latn-zz",gux:"gux-latn-zz",guz:"guz-latn-ke",gv:"gv-latn-im",gva:"gva-latn-py",gvc:"gvc-latn-br",gve:"gve-latn-pg",gvf:"gvf-latn-zz",gvj:"gvj-latn-br",gvl:"gvl-latn-td",gvm:"gvm-latn-ng",gvn:"gvn-latn-au",gvo:"gvo-latn-br",gvp:"gvp-latn-br",gvr:"gvr-deva-np",gvs:"gvs-latn-zz",gvy:"gvy-latn-au",gwa:"gwa-latn-ci",gwb:"gwb-latn-ng",gwc:"gwc-arab-zz",gwd:"gwd-latn-et",gwe:"gwe-latn-tz",gwf:"gwf-arab-pk",gwg:"gwg-latn-ng",gwi:"gwi-latn-ca",gwj:"gwj-latn-bw",gwm:"gwm-latn-au",gwn:"gwn-latn-ng",gwr:"gwr-latn-ug",gwt:"gwt-arab-zz",gwu:"gwu-latn-au",gww:"gww-latn-au",gwx:"gwx-latn-gh",gxx:"gxx-latn-ci",gyb:"gyb-latn-pg",gyd:"gyd-latn-au",gye:"gye-latn-ng",gyf:"gyf-latn-au",gyg:"gyg-latn-cf",gyi:"gyi-latn-zz",gyl:"gyl-latn-et","gyl-ethi":"gyl-ethi-et",gym:"gym-latn-pa",gyn:"gyn-latn-gy",gyo:"gyo-deva-np",gyr:"gyr-latn-bo",gyy:"gyy-latn-au",gyz:"gyz-latn-ng",gza:"gza-latn-sd",gzi:"gzi-arab-ir",gzn:"gzn-latn-id",ha:"ha-latn-ng","ha-cm":"ha-arab-cm","ha-sd":"ha-arab-sd",haa:"haa-latn-us",hac:"hac-arab-ir",had:"had-latn-id",hae:"hae-latn-et",hag:"hag-latn-zz",hah:"hah-latn-pg",hai:"hai-latn-ca",haj:"haj-latn-in","haj-beng":"haj-beng-in",hak:"hak-hans-cn",hal:"hal-latn-vn",ham:"ham-latn-zz",han:"han-latn-tz",hao:"hao-latn-pg",hap:"hap-latn-id",haq:"haq-latn-tz",har:"har-ethi-et","har-arab":"har-arab-et","har-latn":"har-latn-et",has:"has-latn-ca",hav:"hav-latn-cd",haw:"haw-latn-us",hax:"hax-latn-ca",hay:"hay-latn-tz",haz:"haz-arab-af",hba:"hba-latn-cd",hbb:"hbb-latn-zz",hbn:"hbn-latn-sd",hbo:"hbo-hebr-il",hbu:"hbu-latn-tl",hch:"hch-latn-mx",hdy:"hdy-ethi-zz",he:"he-hebr-il",hed:"hed-latn-td",heg:"heg-latn-id",heh:"heh-latn-tz",hei:"hei-latn-ca",hem:"hem-latn-cd",hgm:"hgm-latn-na",hgw:"hgw-latn-pg",hhi:"hhi-latn-pg",hhr:"hhr-latn-sn",hhy:"hhy-latn-zz",hi:"hi-deva-in","hi-latn":"hi-latn-in",hia:"hia-latn-zz",hib:"hib-latn-pe",hid:"hid-latn-us",hif:"hif-latn-fj",hig:"hig-latn-zz",hih:"hih-latn-zz",hii:"hii-takr-in","hii-deva":"hii-deva-in",hij:"hij-latn-cm",hik:"hik-latn-id",hil:"hil-latn-ph",hio:"hio-latn-bw",hir:"hir-latn-br",hit:"hit-xsux-tr",hiw:"hiw-latn-vu",hix:"hix-latn-br",hji:"hji-latn-id",hka:"hka-latn-tz",hke:"hke-latn-cd",hkh:"hkh-arab-in","hkh-deva":"hkh-deva-in","hkh-latn":"hkh-latn-in",hkk:"hkk-latn-pg",hla:"hla-latn-zz",hlb:"hlb-deva-in",hld:"hld-latn-vn",hlt:"hlt-latn-mm",hlu:"hlu-hluw-tr",hma:"hma-latn-cn",hmb:"hmb-latn-ml",hmd:"hmd-plrd-cn",hmf:"hmf-latn-vn",hmj:"hmj-bopo-cn",hmm:"hmm-latn-cn",hmn:"hmn-latn-cn","hmn-bopo":"hmn-bopo-cn","hmn-hmng":"hmn-hmng-cn",hmp:"hmp-latn-cn",hmq:"hmq-bopo-cn",hmr:"hmr-latn-in",hms:"hms-latn-cn",hmt:"hmt-latn-zz",hmu:"hmu-latn-id",hmv:"hmv-latn-vn",hmw:"hmw-latn-cn",hmy:"hmy-latn-cn",hmz:"hmz-latn-cn","hmz-plrd":"hmz-plrd-cn",hna:"hna-latn-cm",hnd:"hnd-arab-pk",hne:"hne-deva-in",hng:"hng-latn-ao",hnh:"hnh-latn-bw",hni:"hni-latn-cn",hnj:"hnj-hmnp-us","hnj-au":"hnj-laoo-au","hnj-cn":"hnj-laoo-cn","hnj-fr":"hnj-laoo-fr","hnj-gf":"hnj-laoo-gf","hnj-la":"hnj-laoo-la","hnj-laoo":"hnj-laoo-la","hnj-mm":"hnj-laoo-mm","hnj-sr":"hnj-laoo-sr","hnj-th":"hnj-laoo-th","hnj-us":"hnj-hmnp-us","hnj-vn":"hnj-laoo-vn",hnn:"hnn-latn-ph",hno:"hno-arab-pk",hns:"hns-latn-sr",ho:"ho-latn-pg",hoa:"hoa-latn-sb",hob:"hob-latn-pg",hoc:"hoc-deva-in",hod:"hod-latn-ng",hoe:"hoe-latn-ng",hoh:"hoh-arab-om",hoi:"hoi-latn-us",hoj:"hoj-deva-in",hol:"hol-latn-ao",hom:"hom-latn-ss",hoo:"hoo-latn-cd",hop:"hop-latn-us",hor:"hor-latn-td",hot:"hot-latn-zz",hov:"hov-latn-id",how:"how-hani-cn",hoy:"hoy-deva-in",hpo:"hpo-mymr-mm",hr:"hr-latn-hr",hra:"hra-latn-in",hrc:"hrc-latn-pg",hre:"hre-latn-vn",hrk:"hrk-latn-id",hrm:"hrm-latn-cn","hrm-hmng":"hrm-hmng-cn",hro:"hro-latn-vn",hrp:"hrp-latn-au",hrt:"hrt-syrc-tr",hru:"hru-latn-in",hrw:"hrw-latn-pg",hrx:"hrx-latn-br",hrz:"hrz-arab-ir",hsb:"hsb-latn-de",hsn:"hsn-hans-cn",hss:"hss-arab-om",ht:"ht-latn-ht",hti:"hti-latn-id",hto:"hto-latn-co",hts:"hts-latn-tz",htu:"htu-latn-id",htx:"htx-xsux-tr",hu:"hu-latn-hu",hub:"hub-latn-pe",huc:"huc-latn-bw",hud:"hud-latn-id",hue:"hue-latn-mx",huf:"huf-latn-pg",hug:"hug-latn-pe",huh:"huh-latn-cl",hui:"hui-latn-zz",huk:"huk-latn-id",hul:"hul-latn-pg",hum:"hum-latn-cd",hup:"hup-latn-us",hur:"hur-latn-ca",hus:"hus-latn-mx",hut:"hut-deva-np","hut-tibt":"hut-tibt-np",huu:"huu-latn-pe",huv:"huv-latn-mx",huw:"huw-latn-id",hux:"hux-latn-pe",huy:"huy-hebr-il",huz:"huz-cyrl-ru",hvc:"hvc-latn-ht",hve:"hve-latn-mx",hvk:"hvk-latn-nc",hvn:"hvn-latn-id",hvv:"hvv-latn-mx",hwa:"hwa-latn-ci",hwc:"hwc-latn-us",hwo:"hwo-latn-ng",hy:"hy-armn-am",hya:"hya-latn-cm",hyw:"hyw-armn-am",hz:"hz-latn-na",ia:"ia-latn-001",iai:"iai-latn-nc",ian:"ian-latn-zz",iar:"iar-latn-zz",iba:"iba-latn-my",ibb:"ibb-latn-ng",ibd:"ibd-latn-au",ibe:"ibe-latn-ng",ibg:"ibg-latn-ph",ibh:"ibh-latn-vn",ibl:"ibl-latn-ph",ibm:"ibm-latn-ng",ibn:"ibn-latn-ng",ibr:"ibr-latn-ng",ibu:"ibu-latn-id",iby:"iby-latn-zz",ica:"ica-latn-zz",ich:"ich-latn-zz",icr:"icr-latn-co",id:"id-latn-id",ida:"ida-latn-ke",idb:"idb-latn-in",idc:"idc-latn-ng",idd:"idd-latn-zz",ide:"ide-latn-ng",idi:"idi-latn-zz",idr:"idr-latn-ss",ids:"ids-latn-ng",idt:"idt-latn-tl",idu:"idu-latn-zz",ie:"ie-latn-001",ifa:"ifa-latn-ph",ifb:"ifb-latn-ph",ife:"ife-latn-tg",iff:"iff-latn-vu",ifk:"ifk-latn-ph",ifm:"ifm-latn-cg",ifu:"ifu-latn-ph",ify:"ify-latn-ph",ig:"ig-latn-ng",igb:"igb-latn-zz",ige:"ige-latn-zz",igg:"igg-latn-pg",igl:"igl-latn-ng",igm:"igm-latn-pg",ign:"ign-latn-bo",igo:"igo-latn-pg",igs:"igs-latn-001","igs-grek":"igs-grek-001",igw:"igw-latn-ng",ihb:"ihb-latn-id",ihi:"ihi-latn-ng",ihp:"ihp-latn-id",ihw:"ihw-latn-au",ii:"ii-yiii-cn",iin:"iin-latn-au",ijc:"ijc-latn-ng",ije:"ije-latn-ng",ijj:"ijj-latn-zz",ijn:"ijn-latn-ng",ijs:"ijs-latn-ng",ik:"ik-latn-us",iki:"iki-latn-ng",ikk:"ikk-latn-zz",ikl:"ikl-latn-ng",iko:"iko-latn-ng",ikp:"ikp-latn-ng",ikr:"ikr-latn-au",ikt:"ikt-latn-ca","ikt-cans":"ikt-cans-ca",ikv:"ikv-latn-ng",ikw:"ikw-latn-zz",ikx:"ikx-latn-zz",ikz:"ikz-latn-tz",ila:"ila-latn-id",ilb:"ilb-latn-zm",ilg:"ilg-latn-au",ili:"ili-latn-cn","ili-arab":"ili-arab-cn","ili-cyrl":"ili-cyrl-kz",ilk:"ilk-latn-ph",ilm:"ilm-latn-my",ilo:"ilo-latn-ph",ilp:"ilp-latn-ph",ilu:"ilu-latn-id",ilv:"ilv-latn-ng",imi:"imi-latn-pg",iml:"iml-latn-us",imn:"imn-latn-pg",imo:"imo-latn-zz",imr:"imr-latn-id",ims:"ims-latn-it",imt:"imt-latn-ss",imy:"imy-lyci-tr",in:"in-latn-id",inb:"inb-latn-co",ing:"ing-latn-us",inh:"inh-cyrl-ru",inj:"inj-latn-co",inn:"inn-latn-ph",ino:"ino-latn-pg",inp:"inp-latn-pe",int:"int-mymr-mm",io:"io-latn-001",ior:"ior-ethi-et",iou:"iou-latn-zz",iow:"iow-latn-us",ipi:"ipi-latn-pg",ipo:"ipo-latn-pg",iqu:"iqu-latn-pe",iqw:"iqw-latn-ng",ire:"ire-latn-id",irh:"irh-latn-id",iri:"iri-latn-zz",irk:"irk-latn-tz",irn:"irn-latn-br",iru:"iru-taml-in","iru-mlym":"iru-mlym-in",irx:"irx-latn-id",iry:"iry-latn-ph",is:"is-latn-is",isa:"isa-latn-pg",isc:"isc-latn-pe",isd:"isd-latn-ph",ish:"ish-latn-ng",isi:"isi-latn-ng",isk:"isk-arab-af","isk-cyrl":"isk-cyrl-tj",ism:"ism-latn-id",isn:"isn-latn-tz",iso:"iso-latn-ng",ist:"ist-latn-hr",isu:"isu-latn-cm",it:"it-latn-it",itb:"itb-latn-ph",itd:"itd-latn-id",ite:"ite-latn-bo",iti:"iti-latn-ph",itk:"itk-hebr-it",itl:"itl-cyrl-ru",itm:"itm-latn-ng",ito:"ito-latn-bo",itr:"itr-latn-pg",its:"its-latn-ng",itt:"itt-latn-ph",itv:"itv-latn-ph",itw:"itw-latn-ng",itx:"itx-latn-id",ity:"ity-latn-ph",itz:"itz-latn-gt",iu:"iu-cans-ca",ium:"ium-latn-cn","ium-hani":"ium-hani-cn","ium-laoo":"ium-laoo-la","ium-thai":"ium-thai-th",ivb:"ivb-latn-ph",ivv:"ivv-latn-ph",iw:"iw-hebr-il",iwk:"iwk-latn-ph",iwm:"iwm-latn-zz",iwo:"iwo-latn-id",iws:"iws-latn-zz",ixc:"ixc-latn-mx",ixl:"ixl-latn-gt",iya:"iya-latn-ng",iyo:"iyo-latn-cm",iyx:"iyx-latn-cg",izh:"izh-latn-ru",izi:"izi-latn-zz",izr:"izr-latn-ng",izz:"izz-latn-ng",ja:"ja-jpan-jp",jaa:"jaa-latn-br",jab:"jab-latn-zz",jac:"jac-latn-gt",jad:"jad-arab-gn",jae:"jae-latn-pg",jaf:"jaf-latn-ng",jah:"jah-latn-my",jaj:"jaj-latn-sb",jak:"jak-latn-my",jal:"jal-latn-id",jam:"jam-latn-jm",jan:"jan-latn-au",jao:"jao-latn-au",jaq:"jaq-latn-id",jar:"jar-latn-zz",jas:"jas-latn-nc",jat:"jat-arab-af",jau:"jau-latn-id",jax:"jax-latn-id",jay:"jay-latn-au",jaz:"jaz-latn-nc",jbe:"jbe-hebr-il",jbi:"jbi-latn-au",jbj:"jbj-latn-id",jbk:"jbk-latn-pg",jbm:"jbm-latn-ng",jbn:"jbn-arab-ly",jbo:"jbo-latn-001",jbr:"jbr-latn-id",jbt:"jbt-latn-br",jbu:"jbu-latn-zz",jbw:"jbw-latn-au",jct:"jct-cyrl-ua","jct-latn":"jct-latn-ua",jda:"jda-tibt-in",jdg:"jdg-arab-pk",jdt:"jdt-cyrl-ru","jdt-hebr":"jdt-hebr-ru","jdt-latn":"jdt-latn-az",jeb:"jeb-latn-pe",jee:"jee-deva-np",jeh:"jeh-latn-vn","jeh-laoo":"jeh-laoo-la",jei:"jei-latn-id",jek:"jek-latn-ci",jel:"jel-latn-id",jen:"jen-latn-zz",jer:"jer-latn-ng",jet:"jet-latn-pg",jeu:"jeu-latn-td",jgb:"jgb-latn-cd",jge:"jge-geor-ge","jge-hebr":"jge-hebr-il",jgk:"jgk-latn-zz",jgo:"jgo-latn-cm",jhi:"jhi-latn-my",ji:"ji-hebr-ua",jia:"jia-latn-cm",jib:"jib-latn-zz",jic:"jic-latn-hn",jid:"jid-latn-ng",jie:"jie-latn-ng",jig:"jig-latn-au",jil:"jil-latn-pg",jim:"jim-latn-cm",jit:"jit-latn-tz",jiu:"jiu-latn-cn",jiv:"jiv-latn-ec",jiy:"jiy-latn-cn",jje:"jje-hang-kr",jjr:"jjr-latn-ng",jka:"jka-latn-id",jkm:"jkm-mymr-mm","jkm-brai":"jkm-brai-mm","jkm-latn":"jkm-latn-mm",jko:"jko-latn-pg",jku:"jku-latn-ng",jle:"jle-latn-sd",jma:"jma-latn-pg",jmb:"jmb-latn-ng",jmc:"jmc-latn-tz",jmd:"jmd-latn-id",jmi:"jmi-latn-ng",jml:"jml-deva-np",jmn:"jmn-latn-mm",jmr:"jmr-latn-gh",jms:"jms-latn-ng",jmw:"jmw-latn-pg",jmx:"jmx-latn-mx",jna:"jna-takr-in",jnd:"jnd-arab-pk",jng:"jng-latn-au",jni:"jni-latn-ng",jnj:"jnj-latn-et","jnj-ethi":"jnj-ethi-et",jnl:"jnl-deva-in",jns:"jns-deva-in","jns-latn":"jns-latn-in","jns-takr":"jns-takr-in",job:"job-latn-cd",jod:"jod-latn-ci",jog:"jog-arab-pk",jor:"jor-latn-bo",jow:"jow-latn-ml",jpa:"jpa-hebr-ps",jpr:"jpr-hebr-il",jqr:"jqr-latn-pe",jra:"jra-latn-zz",jrr:"jrr-latn-ng",jrt:"jrt-latn-ng",jru:"jru-latn-ve",jua:"jua-latn-br",jub:"jub-latn-ng",jud:"jud-latn-ci",juh:"juh-latn-ng",jui:"jui-latn-au",juk:"juk-latn-ng",jul:"jul-deva-np",jum:"jum-latn-sd",jun:"jun-orya-in",juo:"juo-latn-ng",jup:"jup-latn-br",jur:"jur-latn-br",jut:"jut-latn-dk",juu:"juu-latn-ng",juw:"juw-latn-ng",juy:"juy-orya-in",jv:"jv-latn-id",jvd:"jvd-latn-id",jvn:"jvn-latn-sr",jw:"jw-latn-id",jwi:"jwi-latn-gh",jya:"jya-tibt-cn",jye:"jye-hebr-il",jyy:"jyy-latn-td",ka:"ka-geor-ge",kaa:"kaa-cyrl-uz",kab:"kab-latn-dz",kac:"kac-latn-mm",kad:"kad-latn-zz",kag:"kag-latn-my",kah:"kah-latn-cf",kai:"kai-latn-zz",kaj:"kaj-latn-ng",kak:"kak-latn-ph",kam:"kam-latn-ke",kao:"kao-latn-ml",kap:"kap-cyrl-ru",kaq:"kaq-latn-pe",kav:"kav-latn-br",kaw:"kaw-kawi-id",kax:"kax-latn-id",kay:"kay-latn-br",kba:"kba-latn-au",kbb:"kbb-latn-br",kbc:"kbc-latn-br",kbd:"kbd-cyrl-ru",kbe:"kbe-latn-au",kbh:"kbh-latn-co",kbi:"kbi-latn-id",kbj:"kbj-latn-cd",kbk:"kbk-latn-pg",kbl:"kbl-latn-td",kbm:"kbm-latn-zz",kbn:"kbn-latn-cf",kbo:"kbo-latn-ss",kbp:"kbp-latn-zz",kbq:"kbq-latn-zz",kbr:"kbr-latn-et","kbr-ethi":"kbr-ethi-et",kbs:"kbs-latn-ga",kbt:"kbt-latn-pg",kbu:"kbu-arab-pk",kbv:"kbv-latn-id",kbw:"kbw-latn-pg",kbx:"kbx-latn-zz",kby:"kby-arab-ne",kbz:"kbz-latn-ng",kca:"kca-cyrl-ru",kcb:"kcb-latn-pg",kcc:"kcc-latn-ng",kcd:"kcd-latn-id",kce:"kce-latn-ng",kcf:"kcf-latn-ng",kcg:"kcg-latn-ng",kch:"kch-latn-ng",kci:"kci-latn-ng",kcj:"kcj-latn-gw",kck:"kck-latn-zw",kcl:"kcl-latn-zz",kcm:"kcm-latn-cf",kcn:"kcn-latn-ug",kco:"kco-latn-pg",kcp:"kcp-latn-sd",kcq:"kcq-latn-ng",kcs:"kcs-latn-ng",kct:"kct-latn-zz",kcu:"kcu-latn-tz",kcv:"kcv-latn-cd",kcw:"kcw-latn-cd",kcz:"kcz-latn-tz",kda:"kda-latn-au",kdc:"kdc-latn-tz",kdd:"kdd-latn-au",kde:"kde-latn-tz",kdf:"kdf-latn-pg",kdg:"kdg-latn-cd",kdh:"kdh-latn-tg",kdi:"kdi-latn-ug",kdj:"kdj-latn-ug",kdk:"kdk-latn-nc",kdl:"kdl-latn-zz",kdm:"kdm-latn-ng",kdn:"kdn-latn-zw",kdp:"kdp-latn-ng",kdq:"kdq-beng-in",kdr:"kdr-latn-lt","kdr-cyrl":"kdr-cyrl-ua",kdt:"kdt-thai-th",kdw:"kdw-latn-id",kdx:"kdx-latn-ng",kdy:"kdy-latn-id",kdz:"kdz-latn-cm",kea:"kea-latn-cv",keb:"keb-latn-ga",kec:"kec-latn-sd",ked:"ked-latn-tz",kee:"kee-latn-us",kef:"kef-latn-tg",keg:"keg-latn-sd",keh:"keh-latn-pg",kei:"kei-latn-id",kek:"kek-latn-gt",kel:"kel-latn-cd",kem:"kem-latn-tl",ken:"ken-latn-cm",keo:"keo-latn-ug",ker:"ker-latn-td",kes:"kes-latn-ng",ket:"ket-cyrl-ru",keu:"keu-latn-tg",kew:"kew-latn-pg",kex:"kex-deva-in","kex-gujr":"kex-gujr-in",key:"key-telu-in",kez:"kez-latn-zz",kfa:"kfa-knda-in",kfb:"kfb-deva-in",kfc:"kfc-telu-in",kfd:"kfd-knda-in",kfe:"kfe-taml-in",kff:"kff-latn-in","kff-deva":"kff-deva-in","kff-orya":"kff-orya-in","kff-telu":"kff-telu-in",kfh:"kfh-mlym-in",kfi:"kfi-taml-in","kfi-knda":"kfi-knda-in",kfk:"kfk-deva-in","kfk-takr":"kfk-takr-in",kfl:"kfl-latn-cm",kfm:"kfm-arab-ir",kfn:"kfn-latn-cm",kfo:"kfo-latn-ci",kfp:"kfp-deva-in",kfq:"kfq-deva-in",kfr:"kfr-deva-in",kfs:"kfs-deva-in",kfv:"kfv-latn-in",kfw:"kfw-latn-in",kfx:"kfx-deva-in","kfx-takr":"kfx-takr-in",kfy:"kfy-deva-in",kfz:"kfz-latn-bf",kg:"kg-latn-cd",kga:"kga-latn-ci",kgb:"kgb-latn-id",kge:"kge-latn-id",kgf:"kgf-latn-zz",kgj:"kgj-deva-np",kgk:"kgk-latn-br",kgl:"kgl-latn-au",kgm:"kgm-latn-br",kgo:"kgo-latn-sd",kgp:"kgp-latn-br",kgq:"kgq-latn-id",kgr:"kgr-latn-id",kgs:"kgs-latn-au",kgt:"kgt-latn-ng",kgu:"kgu-latn-pg",kgv:"kgv-latn-id",kgw:"kgw-latn-id",kgx:"kgx-latn-id",kgy:"kgy-deva-np",kha:"kha-latn-in",khb:"khb-talu-cn",khc:"khc-latn-id",khd:"khd-latn-id",khe:"khe-latn-id",khf:"khf-thai-la",khg:"khg-tibt-cn",khh:"khh-latn-id",khj:"khj-latn-ng",khl:"khl-latn-pg",khn:"khn-deva-in",khp:"khp-latn-id",khq:"khq-latn-ml",khr:"khr-latn-in","khr-deva":"khr-deva-in",khs:"khs-latn-zz",kht:"kht-mymr-in",khu:"khu-latn-ao",khv:"khv-cyrl-ru",khw:"khw-arab-pk",khx:"khx-latn-cd",khy:"khy-latn-cd",khz:"khz-latn-zz",ki:"ki-latn-ke",kia:"kia-latn-td",kib:"kib-latn-sd",kic:"kic-latn-us",kid:"kid-latn-cm",kie:"kie-latn-td",kif:"kif-deva-np",kig:"kig-latn-id",kih:"kih-latn-pg",kij:"kij-latn-zz",kil:"kil-latn-ng",kim:"kim-cyrl-ru",kio:"kio-latn-us",kip:"kip-deva-np",kiq:"kiq-latn-id",kis:"kis-latn-pg",kit:"kit-latn-pg",kiu:"kiu-latn-tr",kiv:"kiv-latn-tz",kiw:"kiw-latn-zz",kix:"kix-latn-in",kiy:"kiy-latn-id",kiz:"kiz-latn-tz",kj:"kj-latn-na",kja:"kja-latn-id",kjb:"kjb-latn-gt",kjc:"kjc-latn-id",kjd:"kjd-latn-zz",kje:"kje-latn-id",kjg:"kjg-laoo-la",kjh:"kjh-cyrl-ru",kji:"kji-latn-sb",kjj:"kjj-latn-az",kjk:"kjk-latn-id",kjl:"kjl-deva-np",kjm:"kjm-latn-vn",kjn:"kjn-latn-au",kjo:"kjo-deva-in",kjp:"kjp-mymr-mm","kjp-thai":"kjp-thai-th",kjq:"kjq-latn-us",kjr:"kjr-latn-id",kjs:"kjs-latn-zz",kjt:"kjt-thai-th",kju:"kju-latn-us",kjx:"kjx-latn-pg",kjy:"kjy-latn-zz",kk:"kk-cyrl-kz","kk-af":"kk-arab-af","kk-arab":"kk-arab-cn","kk-cn":"kk-arab-cn","kk-ir":"kk-arab-ir","kk-mn":"kk-arab-mn",kka:"kka-latn-ng",kkb:"kkb-latn-id",kkc:"kkc-latn-zz",kkd:"kkd-latn-ng",kke:"kke-latn-gn","kke-arab":"kke-arab-gn",kkf:"kkf-tibt-in",kkg:"kkg-latn-ph",kkh:"kkh-lana-mm",kki:"kki-latn-tz",kkj:"kkj-latn-cm",kkk:"kkk-latn-sb",kkl:"kkl-latn-id",kkm:"kkm-latn-ng",kko:"kko-latn-sd",kkp:"kkp-latn-au",kkq:"kkq-latn-cd",kkr:"kkr-latn-ng",kks:"kks-latn-ng",kkt:"kkt-deva-np",kku:"kku-latn-ng",kkv:"kkv-latn-id",kkw:"kkw-latn-cg",kkx:"kkx-latn-id",kky:"kky-latn-au",kkz:"kkz-latn-ca",kl:"kl-latn-gl",kla:"kla-latn-us",klb:"klb-latn-mx",klc:"klc-latn-cm",kld:"kld-latn-au",kle:"kle-deva-np",klf:"klf-latn-td",klg:"klg-latn-ph",klh:"klh-latn-pg",kli:"kli-latn-id",klj:"klj-arab-ir",klk:"klk-latn-ng",kll:"kll-latn-ph",klm:"klm-latn-pg",kln:"kln-latn-ke",klo:"klo-latn-ng",klp:"klp-latn-pg",klq:"klq-latn-zz",klr:"klr-deva-np",kls:"kls-latn-pk","kls-arab":"kls-arab-pk",klt:"klt-latn-zz",klu:"klu-latn-lr",klv:"klv-latn-vu",klw:"klw-latn-id",klx:"klx-latn-zz",kly:"kly-latn-id",klz:"klz-latn-id",km:"km-khmr-kh",kma:"kma-latn-gh",kmb:"kmb-latn-ao",kmc:"kmc-latn-cn","kmc-hani":"kmc-hani-cn",kmd:"kmd-latn-ph",kme:"kme-latn-cm",kmf:"kmf-latn-pg",kmg:"kmg-latn-pg",kmh:"kmh-latn-zz",kmi:"kmi-latn-ng",kmj:"kmj-deva-in",kmk:"kmk-latn-ph",kml:"kml-latn-ph",kmm:"kmm-latn-in",kmn:"kmn-latn-pg",kmo:"kmo-latn-zz",kmp:"kmp-latn-cm",kmq:"kmq-latn-et",kms:"kms-latn-zz",kmt:"kmt-latn-id",kmu:"kmu-latn-zz",kmv:"kmv-latn-br",kmw:"kmw-latn-zz",kmx:"kmx-latn-pg",kmy:"kmy-latn-ng",kmz:"kmz-arab-ir",kn:"kn-knda-in",kna:"kna-latn-ng",knb:"knb-latn-ph",knd:"knd-latn-id",kne:"kne-latn-ph",knf:"knf-latn-gw",kni:"kni-latn-ng",knj:"knj-latn-gt",knk:"knk-latn-sl","knk-arab":"knk-arab-sl",knl:"knl-latn-id",knm:"knm-latn-br",kno:"kno-latn-sl",knp:"knp-latn-zz",knq:"knq-latn-my",knr:"knr-latn-pg",kns:"kns-latn-my","kns-thai":"kns-thai-th",knt:"knt-latn-br",knu:"knu-latn-gn",knv:"knv-latn-pg",knw:"knw-latn-na",knx:"knx-latn-id",kny:"kny-latn-cd",knz:"knz-latn-bf",ko:"ko-kore-kr",koa:"koa-latn-pg",koc:"koc-latn-ng",kod:"kod-latn-id",koe:"koe-latn-ss",kof:"kof-latn-ng",kog:"kog-latn-co",koh:"koh-latn-cg",koi:"koi-cyrl-ru",kok:"kok-deva-in",kol:"kol-latn-zz",koo:"koo-latn-ug",kop:"kop-latn-pg",koq:"koq-latn-ga",kos:"kos-latn-fm",kot:"kot-latn-cm",kou:"kou-latn-td",kov:"kov-latn-ng",kow:"kow-latn-ng",koy:"koy-latn-us",koz:"koz-latn-zz",kpa:"kpa-latn-ng",kpc:"kpc-latn-co",kpd:"kpd-latn-id",kpe:"kpe-latn-lr",kpf:"kpf-latn-zz",kpg:"kpg-latn-fm",kph:"kph-latn-gh",kpi:"kpi-latn-id",kpj:"kpj-latn-br",kpk:"kpk-latn-ng",kpl:"kpl-latn-cd",kpm:"kpm-latn-vn",kpn:"kpn-latn-br",kpo:"kpo-latn-zz",kpq:"kpq-latn-id",kpr:"kpr-latn-zz",kps:"kps-latn-id",kpt:"kpt-cyrl-ru",kpu:"kpu-latn-id",kpw:"kpw-latn-pg",kpx:"kpx-latn-zz",kpy:"kpy-cyrl-ru",kpz:"kpz-latn-ug",kqa:"kqa-latn-pg",kqb:"kqb-latn-zz",kqc:"kqc-latn-pg",kqd:"kqd-syrc-iq",kqe:"kqe-latn-ph",kqf:"kqf-latn-zz",kqg:"kqg-latn-bf",kqh:"kqh-latn-tz",kqi:"kqi-latn-pg",kqj:"kqj-latn-pg",kqk:"kqk-latn-bj",kql:"kql-latn-pg",kqm:"kqm-latn-ci",kqn:"kqn-latn-zm",kqo:"kqo-latn-lr",kqp:"kqp-latn-td",kqq:"kqq-latn-br",kqr:"kqr-latn-my",kqs:"kqs-latn-zz",kqt:"kqt-latn-my",kqu:"kqu-latn-za",kqv:"kqv-latn-id",kqw:"kqw-latn-pg",kqx:"kqx-latn-cm",kqy:"kqy-ethi-zz",kqz:"kqz-latn-za",kr:"kr-latn-zz",kra:"kra-deva-np",krb:"krb-latn-us",krc:"krc-cyrl-ru",krd:"krd-latn-tl",kre:"kre-latn-br",krf:"krf-latn-vu",krh:"krh-latn-ng",kri:"kri-latn-sl",krj:"krj-latn-ph",krk:"krk-cyrl-ru",krl:"krl-latn-ru",krn:"krn-latn-lr",krp:"krp-latn-ng",krr:"krr-khmr-kh",krs:"krs-latn-zz",krt:"krt-latn-ne",kru:"kru-deva-in",krv:"krv-khmr-kh",krw:"krw-latn-lr",krx:"krx-latn-sn",kry:"kry-latn-az",krz:"krz-latn-id",ks:"ks-arab-in",ksa:"ksa-latn-ng",ksb:"ksb-latn-tz",ksc:"ksc-latn-ph",ksd:"ksd-latn-zz",kse:"kse-latn-pg",ksf:"ksf-latn-cm",ksg:"ksg-latn-sb",ksh:"ksh-latn-de",ksi:"ksi-latn-pg",ksj:"ksj-latn-zz",ksk:"ksk-latn-us",ksl:"ksl-latn-pg",ksm:"ksm-latn-ng",ksn:"ksn-latn-ph",kso:"kso-latn-ng",ksp:"ksp-latn-cf",ksq:"ksq-latn-ng",ksr:"ksr-latn-zz",kss:"kss-latn-lr",kst:"kst-latn-bf",ksu:"ksu-mymr-in",ksv:"ksv-latn-cd",ksw:"ksw-mymr-mm","ksw-latn":"ksw-latn-mm",ksx:"ksx-latn-id",ksz:"ksz-deva-in",kta:"kta-latn-vn",ktb:"ktb-ethi-zz",ktc:"ktc-latn-ng",ktd:"ktd-latn-au",ktf:"ktf-latn-cd",ktg:"ktg-latn-au",kth:"kth-latn-td",kti:"kti-latn-id",ktj:"ktj-latn-ci",ktk:"ktk-latn-pg",ktl:"ktl-arab-ir",ktm:"ktm-latn-zz",ktn:"ktn-latn-br",kto:"kto-latn-zz",ktp:"ktp-plrd-cn",ktq:"ktq-latn-ph",ktr:"ktr-latn-my",kts:"kts-latn-id",ktt:"ktt-latn-id",ktu:"ktu-latn-cd",ktv:"ktv-latn-vn",ktw:"ktw-latn-us",ktx:"ktx-latn-br",kty:"kty-latn-cd",ktz:"ktz-latn-na",ku:"ku-latn-tr","ku-arab":"ku-arab-iq","ku-lb":"ku-arab-lb","ku-yezi":"ku-yezi-ge",kub:"kub-latn-zz",kuc:"kuc-latn-id",kud:"kud-latn-zz",kue:"kue-latn-zz",kuf:"kuf-laoo-la",kug:"kug-latn-ng",kuh:"kuh-latn-ng",kui:"kui-latn-br",kuj:"kuj-latn-zz",kuk:"kuk-latn-id",kul:"kul-latn-ng",kum:"kum-cyrl-ru",kun:"kun-latn-zz",kuo:"kuo-latn-pg",kup:"kup-latn-zz",kuq:"kuq-latn-br",kus:"kus-latn-zz",kut:"kut-latn-ca",kuu:"kuu-latn-us",kuv:"kuv-latn-id",kuw:"kuw-latn-cf",kux:"kux-latn-au",kuy:"kuy-latn-au",kuz:"kuz-latn-cl",kv:"kv-cyrl-ru",kva:"kva-cyrl-ru",kvb:"kvb-latn-id",kvc:"kvc-latn-pg",kvd:"kvd-latn-id",kve:"kve-latn-my",kvf:"kvf-latn-td",kvg:"kvg-latn-zz",kvh:"kvh-latn-id",kvi:"kvi-latn-td",kvj:"kvj-latn-cm",kvl:"kvl-latn-mm",kvm:"kvm-latn-cm",kvn:"kvn-latn-co",kvo:"kvo-latn-id",kvp:"kvp-latn-id",kvq:"kvq-mymr-mm","kvq-latn":"kvq-latn-mm",kvr:"kvr-latn-id",kvt:"kvt-mymr-mm",kvv:"kvv-latn-id",kvw:"kvw-latn-id",kvx:"kvx-arab-pk",kvy:"kvy-kali-mm",kvz:"kvz-latn-id",kw:"kw-latn-gb",kwa:"kwa-latn-br",kwb:"kwb-latn-ng",kwc:"kwc-latn-cg",kwd:"kwd-latn-sb",kwe:"kwe-latn-id",kwf:"kwf-latn-sb",kwg:"kwg-latn-td",kwh:"kwh-latn-id",kwi:"kwi-latn-co",kwj:"kwj-latn-zz",kwk:"kwk-latn-ca",kwl:"kwl-latn-ng",kwm:"kwm-latn-na",kwn:"kwn-latn-na",kwo:"kwo-latn-zz",kwp:"kwp-latn-ci",kwq:"kwq-latn-zz",kwr:"kwr-latn-id",kws:"kws-latn-cd",kwt:"kwt-latn-id",kwu:"kwu-latn-cm",kwv:"kwv-latn-td",kww:"kww-latn-sr",kwy:"kwy-latn-cd",kwz:"kwz-latn-ao",kxa:"kxa-latn-zz",kxb:"kxb-latn-ci",kxc:"kxc-ethi-zz",kxd:"kxd-latn-bn","kxd-arab":"kxd-arab-bn",kxe:"kxe-latn-zz",kxf:"kxf-mymr-mm","kxf-latn":"kxf-latn-mm",kxi:"kxi-latn-my",kxj:"kxj-latn-td",kxk:"kxk-mymr-mm",kxl:"kxl-deva-in",kxm:"kxm-thai-th",kxn:"kxn-latn-my",kxo:"kxo-latn-br",kxp:"kxp-arab-pk",kxq:"kxq-latn-id",kxr:"kxr-latn-pg",kxt:"kxt-latn-pg",kxv:"kxv-orya-in","kxv-latn":"kxv-latn-in","kxv-telu":"kxv-telu-in",kxw:"kxw-latn-zz",kxx:"kxx-latn-cg",kxy:"kxy-latn-vn",kxz:"kxz-latn-zz",ky:"ky-cyrl-kg","ky-arab":"ky-arab-cn","ky-cn":"ky-arab-cn","ky-latn":"ky-latn-tr","ky-tr":"ky-latn-tr",kya:"kya-latn-tz",kyb:"kyb-latn-ph",kyc:"kyc-latn-pg",kyd:"kyd-latn-id",kye:"kye-latn-zz",kyf:"kyf-latn-ci",kyg:"kyg-latn-pg",kyh:"kyh-latn-us",kyi:"kyi-latn-my",kyj:"kyj-latn-ph",kyk:"kyk-latn-ph",kyl:"kyl-latn-us",kym:"kym-latn-cf",kyn:"kyn-latn-ph",kyo:"kyo-latn-id",kyq:"kyq-latn-td",kyr:"kyr-latn-br",kys:"kys-latn-my",kyt:"kyt-latn-id",kyu:"kyu-kali-mm","kyu-latn":"kyu-latn-mm","kyu-mymr":"kyu-mymr-mm",kyv:"kyv-deva-np",kyw:"kyw-deva-in","kyw-beng":"kyw-beng-in","kyw-orya":"kyw-orya-in",kyx:"kyx-latn-zz",kyy:"kyy-latn-pg",kyz:"kyz-latn-br",kza:"kza-latn-bf",kzb:"kzb-latn-id",kzc:"kzc-latn-ci",kzd:"kzd-latn-id",kze:"kze-latn-pg",kzf:"kzf-latn-id",kzh:"kzh-arab-zz",kzi:"kzi-latn-my",kzj:"kzj-latn-my",kzk:"kzk-latn-sb",kzl:"kzl-latn-id",kzm:"kzm-latn-id",kzn:"kzn-latn-mw",kzo:"kzo-latn-ga",kzp:"kzp-latn-id",kzr:"kzr-latn-zz",kzs:"kzs-latn-my",kzt:"kzt-latn-my",kzu:"kzu-latn-id",kzv:"kzv-latn-id",kzw:"kzw-latn-br",kzx:"kzx-latn-id",kzy:"kzy-latn-cd",kzz:"kzz-latn-id",la:"la-latn-va",laa:"laa-latn-ph",lab:"lab-lina-gr",lac:"lac-latn-mx",lad:"lad-hebr-il",lae:"lae-deva-in","lae-tibt":"lae-tibt-in",lag:"lag-latn-tz",lah:"lah-arab-pk",lai:"lai-latn-mw",laj:"laj-latn-ug",lal:"lal-latn-cd",lam:"lam-latn-zm",lan:"lan-latn-ng",lap:"lap-latn-td",laq:"laq-latn-vn",lar:"lar-latn-gh",las:"las-latn-zz",lau:"lau-latn-id",law:"law-latn-id",lax:"lax-latn-in","lax-beng":"lax-beng-in",laz:"laz-latn-pg",lb:"lb-latn-lu",lbb:"lbb-latn-pg",lbc:"lbc-lisu-cn",lbe:"lbe-cyrl-ru",lbf:"lbf-deva-in","lbf-tibt":"lbf-tibt-cn",lbi:"lbi-latn-cm",lbj:"lbj-tibt-in","lbj-arab":"lbj-arab-in",lbl:"lbl-latn-ph",lbm:"lbm-deva-in",lbn:"lbn-latn-la","lbn-laoo":"lbn-laoo-la",lbo:"lbo-laoo-la","lbo-latn":"lbo-latn-us",lbq:"lbq-latn-pg",lbr:"lbr-deva-np",lbt:"lbt-latn-vn",lbu:"lbu-latn-zz",lbv:"lbv-latn-pg",lbw:"lbw-latn-id",lbx:"lbx-latn-id",lby:"lby-latn-au",lbz:"lbz-latn-au",lcc:"lcc-latn-id",lcd:"lcd-latn-id",lce:"lce-latn-id",lcf:"lcf-latn-id",lch:"lch-latn-ao",lcl:"lcl-latn-id",lcm:"lcm-latn-zz",lcp:"lcp-thai-cn",lcq:"lcq-latn-id",lcs:"lcs-latn-id",lda:"lda-latn-ci",ldb:"ldb-latn-zz",ldd:"ldd-latn-ng",ldg:"ldg-latn-ng",ldh:"ldh-latn-ng",ldi:"ldi-latn-cg",ldj:"ldj-latn-ng",ldk:"ldk-latn-ng",ldl:"ldl-latn-ng",ldm:"ldm-latn-gn",ldn:"ldn-latn-001",ldo:"ldo-latn-ng",ldp:"ldp-latn-ng",ldq:"ldq-latn-ng",lea:"lea-latn-cd",leb:"leb-latn-zm",lec:"lec-latn-bo",led:"led-latn-zz",lee:"lee-latn-zz",lef:"lef-latn-gh",leh:"leh-latn-zm",lei:"lei-latn-pg",lej:"lej-latn-cd",lek:"lek-latn-pg",lel:"lel-latn-cd",lem:"lem-latn-zz",len:"len-latn-hn",leo:"leo-latn-cm",lep:"lep-lepc-in",leq:"leq-latn-zz",ler:"ler-latn-pg",les:"les-latn-cd",let:"let-latn-pg",leu:"leu-latn-zz",lev:"lev-latn-id",lew:"lew-latn-id",lex:"lex-latn-id",ley:"ley-latn-id",lez:"lez-cyrl-ru",lfa:"lfa-latn-cm",lfn:"lfn-latn-001","lfn-cyrl":"lfn-cyrl-001",lg:"lg-latn-ug",lga:"lga-latn-sb",lgb:"lgb-latn-sb",lgg:"lgg-latn-zz",lgh:"lgh-latn-vn",lgi:"lgi-latn-id",lgk:"lgk-latn-vu",lgl:"lgl-latn-sb",lgm:"lgm-latn-cd",lgn:"lgn-latn-et",lgo:"lgo-latn-ss",lgq:"lgq-latn-gh",lgr:"lgr-latn-sb",lgt:"lgt-latn-pg",lgu:"lgu-latn-sb",lgz:"lgz-latn-cd",lha:"lha-latn-vn",lhh:"lhh-latn-id",lhi:"lhi-latn-cn",lhm:"lhm-deva-np",lhn:"lhn-latn-my",lhs:"lhs-syrc-sy",lht:"lht-latn-vu",lhu:"lhu-latn-cn",li:"li-latn-nl",lia:"lia-latn-zz",lib:"lib-latn-pg",lic:"lic-latn-cn",lid:"lid-latn-zz",lie:"lie-latn-cd",lif:"lif-deva-np","lif-limb":"lif-limb-in",lig:"lig-latn-zz",lih:"lih-latn-zz",lij:"lij-latn-it",lik:"lik-latn-cd",lil:"lil-latn-ca",lio:"lio-latn-id",lip:"lip-latn-gh",liq:"liq-latn-et",lir:"lir-latn-lr",lis:"lis-lisu-cn",liu:"liu-latn-sd",liv:"liv-latn-lv",liw:"liw-latn-id",lix:"lix-latn-id",liy:"liy-latn-cf",liz:"liz-latn-cd",lja:"lja-latn-au",lje:"lje-latn-id",lji:"lji-latn-id",ljl:"ljl-latn-id",ljp:"ljp-latn-id",ljw:"ljw-latn-au",ljx:"ljx-latn-au",lka:"lka-latn-tl",lkb:"lkb-latn-ke",lkc:"lkc-latn-vn",lkd:"lkd-latn-br",lke:"lke-latn-ug",lkh:"lkh-tibt-bt",lki:"lki-arab-ir",lkj:"lkj-latn-my",lkl:"lkl-latn-pg",lkm:"lkm-latn-au",lkn:"lkn-latn-vu",lko:"lko-latn-ke",lkr:"lkr-latn-ss",lks:"lks-latn-ke",lkt:"lkt-latn-us",lku:"lku-latn-au",lky:"lky-latn-ss",lla:"lla-latn-ng",llb:"llb-latn-mz",llc:"llc-latn-gn",lld:"lld-latn-it",lle:"lle-latn-zz",llf:"llf-latn-pg",llg:"llg-latn-id",lli:"lli-latn-cg",llj:"llj-latn-au",llk:"llk-latn-my",lll:"lll-latn-pg",llm:"llm-latn-id",lln:"lln-latn-zz",llp:"llp-latn-vu",llq:"llq-latn-id",llu:"llu-latn-sb",llx:"llx-latn-fj",lma:"lma-latn-gn",lmb:"lmb-latn-vu",lmc:"lmc-latn-au",lmd:"lmd-latn-sd",lme:"lme-latn-td",lmf:"lmf-latn-id",lmg:"lmg-latn-pg",lmh:"lmh-deva-np",lmi:"lmi-latn-cd",lmj:"lmj-latn-id",lmk:"lmk-latn-in","lmk-mymr":"lmk-mymr-in",lml:"lml-latn-vu",lmn:"lmn-telu-in",lmo:"lmo-latn-it",lmp:"lmp-latn-zz",lmq:"lmq-latn-id",lmr:"lmr-latn-id",lmu:"lmu-latn-vu",lmv:"lmv-latn-fj",lmw:"lmw-latn-us",lmx:"lmx-latn-cm",lmy:"lmy-latn-id",ln:"ln-latn-cd",lna:"lna-latn-cf",lnb:"lnb-latn-na",lnd:"lnd-latn-id",lnh:"lnh-latn-my",lni:"lni-latn-pg",lnj:"lnj-latn-au",lnl:"lnl-latn-cf",lnm:"lnm-latn-pg",lnn:"lnn-latn-vu",lns:"lns-latn-zz",lnu:"lnu-latn-zz",lnw:"lnw-latn-au",lnz:"lnz-latn-cd",lo:"lo-laoo-la",loa:"loa-latn-id",lob:"lob-latn-bf",loc:"loc-latn-ph",loe:"loe-latn-id",log:"log-latn-cd",loh:"loh-latn-ss",loi:"loi-latn-ci",loj:"loj-latn-zz",lok:"lok-latn-zz",lol:"lol-latn-cd",lom:"lom-latn-lr",lon:"lon-latn-mw",loo:"loo-latn-cd",lop:"lop-latn-ng",loq:"loq-latn-cd",lor:"lor-latn-zz",los:"los-latn-zz",lot:"lot-latn-ss","lot-arab":"lot-arab-ss",lou:"lou-latn-us",low:"low-latn-my",lox:"lox-latn-id",loy:"loy-deva-np","loy-tibt":"loy-tibt-np",loz:"loz-latn-zm",lpa:"lpa-latn-vu",lpe:"lpe-latn-id",lpn:"lpn-latn-mm",lpo:"lpo-plrd-cn","lpo-lisu":"lpo-lisu-cn",lpx:"lpx-latn-ss",lqr:"lqr-latn-ss",lra:"lra-latn-my",lrc:"lrc-arab-ir",lrg:"lrg-latn-au",lri:"lri-latn-ke",lrk:"lrk-arab-pk",lrl:"lrl-arab-ir",lrm:"lrm-latn-ke",lrn:"lrn-latn-id",lro:"lro-latn-sd",lrt:"lrt-latn-id",lrv:"lrv-latn-vu",lrz:"lrz-latn-vu",lsa:"lsa-arab-ir",lsd:"lsd-hebr-il",lse:"lse-latn-cd",lsi:"lsi-latn-mm",lsm:"lsm-latn-ug",lsr:"lsr-latn-pg",lss:"lss-arab-pk",lt:"lt-latn-lt",ltg:"ltg-latn-lv",lth:"lth-latn-ug",lti:"lti-latn-id",ltn:"ltn-latn-br",lto:"lto-latn-ke",lts:"lts-latn-ke",ltu:"ltu-latn-id",lu:"lu-latn-cd",lua:"lua-latn-cd",luc:"luc-latn-ug",lud:"lud-latn-ru",lue:"lue-latn-zm",luf:"luf-latn-pg",lui:"lui-latn-us",luj:"luj-latn-cd",luk:"luk-tibt-bt",lul:"lul-latn-ss",lum:"lum-latn-ao",lun:"lun-latn-zm",luo:"luo-latn-ke",lup:"lup-latn-ga",luq:"luq-latn-cu",lur:"lur-latn-id",lus:"lus-latn-in","lus-beng":"lus-beng-bd","lus-brai":"lus-brai-in",lut:"lut-latn-us",luu:"luu-deva-np",luv:"luv-arab-om",luw:"luw-latn-cm",luy:"luy-latn-ke",luz:"luz-arab-ir",lv:"lv-latn-lv",lva:"lva-latn-tl",lvi:"lvi-latn-la",lvk:"lvk-latn-sb",lvu:"lvu-latn-id",lwa:"lwa-latn-cd",lwe:"lwe-latn-id",lwg:"lwg-latn-ke",lwh:"lwh-latn-vn",lwl:"lwl-thai-th",lwm:"lwm-thai-cn",lwo:"lwo-latn-ss","lwo-za":"lwo-latn-za",lwt:"lwt-latn-id",lww:"lww-latn-vu",lxm:"lxm-latn-pg",lya:"lya-tibt-bt",lyn:"lyn-latn-zm",lzh:"lzh-hans-cn",lzl:"lzl-latn-vu",lzn:"lzn-latn-mm",lzz:"lzz-latn-tr",maa:"maa-latn-mx",mab:"mab-latn-mx",mad:"mad-latn-id",mae:"mae-latn-ng",maf:"maf-latn-cm",mag:"mag-deva-in",mai:"mai-deva-in",maj:"maj-latn-mx",mak:"mak-latn-id",mam:"mam-latn-gt",man:"man-latn-gm","man-gn":"man-nkoo-gn","man-nkoo":"man-nkoo-gn",maq:"maq-latn-mx",mas:"mas-latn-ke",mat:"mat-latn-mx",mau:"mau-latn-mx",mav:"mav-latn-br",maw:"maw-latn-zz",max:"max-latn-id",maz:"maz-latn-mx",mba:"mba-latn-ph",mbb:"mbb-latn-ph",mbc:"mbc-latn-br",mbd:"mbd-latn-ph",mbf:"mbf-latn-sg",mbh:"mbh-latn-zz",mbi:"mbi-latn-ph",mbj:"mbj-latn-br",mbk:"mbk-latn-pg",mbl:"mbl-latn-br",mbm:"mbm-latn-cg",mbn:"mbn-latn-co",mbo:"mbo-latn-zz",mbp:"mbp-latn-co",mbq:"mbq-latn-zz",mbr:"mbr-latn-co",mbs:"mbs-latn-ph",mbt:"mbt-latn-ph",mbu:"mbu-latn-zz",mbv:"mbv-latn-gn",mbw:"mbw-latn-zz",mbx:"mbx-latn-pg",mby:"mby-arab-pk",mbz:"mbz-latn-mx",mca:"mca-latn-py",mcb:"mcb-latn-pe",mcc:"mcc-latn-pg",mcd:"mcd-latn-pe",mce:"mce-latn-mx",mcf:"mcf-latn-pe",mcg:"mcg-latn-ve",mch:"mch-latn-ve",mci:"mci-latn-zz",mcj:"mcj-latn-ng",mck:"mck-latn-ao",mcl:"mcl-latn-co",mcm:"mcm-latn-my",mcn:"mcn-latn-td",mco:"mco-latn-mx",mcp:"mcp-latn-zz",mcq:"mcq-latn-zz",mcr:"mcr-latn-zz",mcs:"mcs-latn-cm",mct:"mct-latn-cm",mcu:"mcu-latn-zz",mcv:"mcv-latn-pg",mcw:"mcw-latn-td",mcx:"mcx-latn-cf",mcy:"mcy-latn-pg",mcz:"mcz-latn-pg",mda:"mda-latn-zz",mdb:"mdb-latn-pg",mdc:"mdc-latn-pg",mdd:"mdd-latn-cm",mde:"mde-arab-zz",mdf:"mdf-cyrl-ru",mdg:"mdg-latn-td",mdh:"mdh-latn-ph",mdi:"mdi-latn-cd",mdj:"mdj-latn-zz",mdk:"mdk-latn-cd",mdm:"mdm-latn-cd",mdn:"mdn-latn-cf",mdp:"mdp-latn-cd",mdq:"mdq-latn-cd",mdr:"mdr-latn-id",mds:"mds-latn-pg",mdt:"mdt-latn-cg",mdu:"mdu-latn-cg",mdv:"mdv-latn-mx",mdw:"mdw-latn-cg",mdx:"mdx-ethi-zz",mdy:"mdy-ethi-et","mdy-latn":"mdy-latn-et",mdz:"mdz-latn-br",mea:"mea-latn-cm",meb:"meb-latn-pg",mec:"mec-latn-au",med:"med-latn-zz",mee:"mee-latn-zz",meh:"meh-latn-mx",mej:"mej-latn-id",mek:"mek-latn-zz",mel:"mel-latn-my",mem:"mem-latn-au",men:"men-latn-sl",meo:"meo-latn-my","meo-arab":"meo-arab-my",mep:"mep-latn-au",meq:"meq-latn-cm",mer:"mer-latn-ke",mes:"mes-latn-td",met:"met-latn-zz",meu:"meu-latn-zz",mev:"mev-latn-lr",mew:"mew-latn-ng",mey:"mey-latn-mr","mey-arab":"mey-arab-mr",mez:"mez-latn-us",mfa:"mfa-arab-th",mfb:"mfb-latn-id",mfc:"mfc-latn-cd",mfd:"mfd-latn-cm",mfe:"mfe-latn-mu",mff:"mff-latn-cm",mfg:"mfg-latn-gn","mfg-arab":"mfg-arab-gn",mfh:"mfh-latn-cm",mfi:"mfi-arab-cm","mfi-latn":"mfi-latn-cm",mfj:"mfj-latn-cm",mfk:"mfk-latn-cm",mfl:"mfl-latn-ng",mfm:"mfm-latn-ng",mfn:"mfn-latn-zz",mfo:"mfo-latn-zz",mfp:"mfp-latn-id",mfq:"mfq-latn-zz",mfr:"mfr-latn-au",mft:"mft-latn-pg",mfu:"mfu-latn-ao",mfv:"mfv-latn-gw",mfw:"mfw-latn-pg",mfx:"mfx-latn-et","mfx-ethi":"mfx-ethi-et",mfy:"mfy-latn-mx",mfz:"mfz-latn-ss",mg:"mg-latn-mg",mgb:"mgb-latn-td",mgc:"mgc-latn-ss",mgd:"mgd-latn-ss","mgd-arab":"mgd-arab-ss",mge:"mge-latn-td",mgf:"mgf-latn-id",mgg:"mgg-latn-cm",mgh:"mgh-latn-mz",mgi:"mgi-latn-ng",mgj:"mgj-latn-ng",mgk:"mgk-latn-id",mgl:"mgl-latn-zz",mgm:"mgm-latn-tl",mgn:"mgn-latn-cf",mgo:"mgo-latn-cm",mgp:"mgp-deva-np",mgq:"mgq-latn-tz",mgr:"mgr-latn-zm",mgs:"mgs-latn-tz",mgt:"mgt-latn-pg",mgu:"mgu-latn-pg",mgv:"mgv-latn-tz",mgw:"mgw-latn-tz",mgy:"mgy-latn-tz",mgz:"mgz-latn-tz",mh:"mh-latn-mh",mhb:"mhb-latn-ga",mhc:"mhc-latn-mx",mhd:"mhd-latn-tz",mhe:"mhe-latn-my",mhf:"mhf-latn-pg",mhg:"mhg-latn-au",mhi:"mhi-latn-zz",mhj:"mhj-arab-af",mhk:"mhk-latn-cm",mhl:"mhl-latn-zz",mhm:"mhm-latn-mz",mhn:"mhn-latn-it",mho:"mho-latn-zm",mhp:"mhp-latn-id",mhq:"mhq-latn-us",mhs:"mhs-latn-id",mht:"mht-latn-ve",mhu:"mhu-latn-in",mhw:"mhw-latn-bw",mhx:"mhx-latn-mm",mhy:"mhy-latn-id",mhz:"mhz-latn-id",mi:"mi-latn-nz",mia:"mia-latn-us",mib:"mib-latn-mx",mic:"mic-latn-ca",mid:"mid-mand-iq",mie:"mie-latn-mx",mif:"mif-latn-zz",mig:"mig-latn-mx",mih:"mih-latn-mx",mii:"mii-latn-mx",mij:"mij-latn-cm",mik:"mik-latn-us",mil:"mil-latn-mx",mim:"mim-latn-mx",min:"min-latn-id",mio:"mio-latn-mx",mip:"mip-latn-mx",miq:"miq-latn-ni",mir:"mir-latn-mx",mit:"mit-latn-mx",miu:"miu-latn-mx",miw:"miw-latn-zz",mix:"mix-latn-mx",miy:"miy-latn-mx",miz:"miz-latn-mx",mjb:"mjb-latn-tl",mjc:"mjc-latn-mx",mjd:"mjd-latn-us",mje:"mje-latn-td",mjg:"mjg-latn-cn",mjh:"mjh-latn-tz",mji:"mji-latn-cn",mjj:"mjj-latn-pg",mjk:"mjk-latn-pg",mjl:"mjl-deva-in","mjl-takr":"mjl-takr-in",mjm:"mjm-latn-pg",mjn:"mjn-latn-pg",mjq:"mjq-mlym-in",mjr:"mjr-mlym-in",mjs:"mjs-latn-ng",mjt:"mjt-deva-in","mjt-beng":"mjt-beng-bd",mju:"mju-telu-in",mjv:"mjv-mlym-in",mjw:"mjw-latn-in",mjx:"mjx-latn-bd","mjx-beng":"mjx-beng-bd",mjy:"mjy-latn-us",mjz:"mjz-deva-np",mk:"mk-cyrl-mk",mka:"mka-latn-ci",mkb:"mkb-deva-in",mkc:"mkc-latn-pg",mke:"mke-deva-in",mkf:"mkf-latn-ng",mki:"mki-arab-zz",mkj:"mkj-latn-fm",mkk:"mkk-latn-cm",mkl:"mkl-latn-zz",mkm:"mkm-thai-th",mkn:"mkn-latn-id",mko:"mko-latn-ng",mkp:"mkp-latn-zz",mkr:"mkr-latn-pg",mks:"mks-latn-mx",mkt:"mkt-latn-nc",mku:"mku-latn-gn",mkv:"mkv-latn-vu",mkw:"mkw-latn-zz",mkx:"mkx-latn-ph",mky:"mky-latn-id",mkz:"mkz-latn-tl",ml:"ml-mlym-in",mla:"mla-latn-vu",mlb:"mlb-latn-cm",mlc:"mlc-latn-vn",mle:"mle-latn-zz",mlf:"mlf-thai-la","mlf-latn":"mlf-latn-la",mlh:"mlh-latn-pg",mli:"mli-latn-id",mlj:"mlj-latn-td",mlk:"mlk-latn-ke",mll:"mll-latn-vu",mln:"mln-latn-sb",mlo:"mlo-latn-sn",mlp:"mlp-latn-zz",mlq:"mlq-latn-sn","mlq-arab":"mlq-arab-sn",mlr:"mlr-latn-cm",mls:"mls-latn-sd",mlu:"mlu-latn-sb",mlv:"mlv-latn-vu",mlw:"mlw-latn-cm",mlx:"mlx-latn-vu",mlz:"mlz-latn-ph",mma:"mma-latn-ng",mmb:"mmb-latn-id",mmc:"mmc-latn-mx",mmd:"mmd-latn-cn","mmd-hans":"mmd-hans-cn","mmd-hant":"mmd-hant-cn",mme:"mme-latn-vu",mmf:"mmf-latn-ng",mmg:"mmg-latn-vu",mmh:"mmh-latn-br",mmi:"mmi-latn-pg",mmm:"mmm-latn-vu",mmn:"mmn-latn-ph",mmo:"mmo-latn-zz",mmp:"mmp-latn-pg",mmq:"mmq-latn-pg",mmr:"mmr-latn-cn",mmt:"mmt-latn-pg",mmu:"mmu-latn-zz",mmv:"mmv-latn-br",mmw:"mmw-latn-vu",mmx:"mmx-latn-zz",mmy:"mmy-latn-td",mmz:"mmz-latn-cd",mn:"mn-cyrl-mn","mn-cn":"mn-mong-cn","mn-mong":"mn-mong-cn",mna:"mna-latn-zz",mnb:"mnb-latn-id",mnd:"mnd-latn-br",mne:"mne-latn-td",mnf:"mnf-latn-zz",mng:"mng-latn-vn",mnh:"mnh-latn-cd",mni:"mni-beng-in",mnj:"mnj-arab-af",mnl:"mnl-latn-vu",mnm:"mnm-latn-pg",mnn:"mnn-latn-vn",mnp:"mnp-latn-cn",mnq:"mnq-latn-my",mnr:"mnr-latn-us",mns:"mns-cyrl-ru",mnu:"mnu-latn-id",mnv:"mnv-latn-sb",mnw:"mnw-mymr-mm",mnx:"mnx-latn-id",mny:"mny-latn-mz",mnz:"mnz-latn-id",mo:"mo-latn-ro",moa:"moa-latn-zz",moc:"moc-latn-ar",mod:"mod-latn-us",moe:"moe-latn-ca",mog:"mog-latn-id",moh:"moh-latn-ca",moi:"moi-latn-ng",moj:"moj-latn-cg",mok:"mok-latn-id",mom:"mom-latn-ni",moo:"moo-latn-vn",mop:"mop-latn-bz",moq:"moq-latn-id",mor:"mor-latn-sd",mos:"mos-latn-bf",mot:"mot-latn-co",mou:"mou-latn-td",mov:"mov-latn-us",mow:"mow-latn-cg",mox:"mox-latn-zz",moy:"moy-latn-et","moy-ethi":"moy-ethi-et",moz:"moz-latn-td",mpa:"mpa-latn-tz",mpb:"mpb-latn-au",mpc:"mpc-latn-au",mpd:"mpd-latn-br",mpe:"mpe-latn-et","mpe-ethi":"mpe-ethi-et",mpg:"mpg-latn-td",mph:"mph-latn-au",mpi:"mpi-latn-cm",mpj:"mpj-latn-au",mpk:"mpk-latn-td",mpl:"mpl-latn-pg",mpm:"mpm-latn-mx",mpn:"mpn-latn-pg",mpo:"mpo-latn-pg",mpp:"mpp-latn-zz",mpq:"mpq-latn-br",mpr:"mpr-latn-sb",mps:"mps-latn-zz",mpt:"mpt-latn-zz",mpu:"mpu-latn-br",mpv:"mpv-latn-pg",mpw:"mpw-latn-br",mpx:"mpx-latn-zz",mpy:"mpy-latn-id",mpz:"mpz-thai-th",mqa:"mqa-latn-id",mqb:"mqb-latn-cm",mqc:"mqc-latn-id",mqe:"mqe-latn-pg",mqf:"mqf-latn-id",mqg:"mqg-latn-id",mqh:"mqh-latn-mx",mqi:"mqi-latn-id",mqj:"mqj-latn-id",mqk:"mqk-latn-ph",mql:"mql-latn-zz",mqm:"mqm-latn-pf",mqn:"mqn-latn-id",mqo:"mqo-latn-id",mqp:"mqp-latn-id",mqq:"mqq-latn-my",mqr:"mqr-latn-id",mqs:"mqs-latn-id",mqu:"mqu-latn-ss",mqv:"mqv-latn-pg",mqw:"mqw-latn-pg",mqx:"mqx-latn-id","mqx-bugi":"mqx-bugi-id",mqy:"mqy-latn-id",mqz:"mqz-latn-pg",mr:"mr-deva-in",mra:"mra-thai-th",mrb:"mrb-latn-vu",mrc:"mrc-latn-us",mrd:"mrd-deva-np",mrf:"mrf-latn-id",mrg:"mrg-latn-in","mrg-beng":"mrg-beng-in","mrg-deva":"mrg-deva-in",mrh:"mrh-latn-in",mrj:"mrj-cyrl-ru",mrk:"mrk-latn-nc",mrl:"mrl-latn-fm",mrm:"mrm-latn-vu",mrn:"mrn-latn-sb",mro:"mro-mroo-bd",mrp:"mrp-latn-vu",mrq:"mrq-latn-pf",mrr:"mrr-deva-in",mrs:"mrs-latn-vu",mrt:"mrt-latn-ng",mru:"mru-latn-cm",mrv:"mrv-latn-pf",mrw:"mrw-latn-ph","mrw-arab":"mrw-arab-ph",mrx:"mrx-latn-id",mry:"mry-latn-ph",mrz:"mrz-latn-id",ms:"ms-latn-my","ms-cc":"ms-arab-cc",msb:"msb-latn-ph",msc:"msc-latn-gn",mse:"mse-latn-td",msf:"msf-latn-id",msg:"msg-latn-id",msh:"msh-latn-mg",msi:"msi-latn-my",msj:"msj-latn-cd",msk:"msk-latn-ph",msl:"msl-latn-id",msm:"msm-latn-ph",msn:"msn-latn-vu",mso:"mso-latn-id",msp:"msp-latn-br",msq:"msq-latn-nc",mss:"mss-latn-id",msu:"msu-latn-pg",msv:"msv-latn-cm",msw:"msw-latn-gw",msx:"msx-latn-pg",msy:"msy-latn-pg",msz:"msz-latn-pg",mt:"mt-latn-mt",mta:"mta-latn-ph",mtb:"mtb-latn-ci",mtc:"mtc-latn-zz",mtd:"mtd-latn-id",mte:"mte-latn-sb",mtf:"mtf-latn-zz",mtg:"mtg-latn-id",mth:"mth-latn-id",mti:"mti-latn-zz",mtj:"mtj-latn-id",mtk:"mtk-latn-cm",mtl:"mtl-latn-ng",mtm:"mtm-cyrl-ru",mtn:"mtn-latn-ni",mto:"mto-latn-mx",mtp:"mtp-latn-bo",mtq:"mtq-latn-vn",mtr:"mtr-deva-in",mts:"mts-latn-pe",mtt:"mtt-latn-vu",mtu:"mtu-latn-mx",mtv:"mtv-latn-pg",mtw:"mtw-latn-ph",mtx:"mtx-latn-mx",mty:"mty-latn-pg",mua:"mua-latn-cm",mub:"mub-latn-td",muc:"muc-latn-cm",mud:"mud-cyrl-ru",mue:"mue-latn-ec",mug:"mug-latn-cm",muh:"muh-latn-ss",mui:"mui-latn-id",muj:"muj-latn-td",muk:"muk-tibt-np",mum:"mum-latn-pg",muo:"muo-latn-cm",muq:"muq-latn-cn",mur:"mur-latn-zz",mus:"mus-latn-us",mut:"mut-deva-in",muu:"muu-latn-ke",muv:"muv-taml-in",mux:"mux-latn-pg",muy:"muy-latn-cm",muz:"muz-ethi-et","muz-latn":"muz-latn-et",mva:"mva-latn-zz",mvd:"mvd-latn-id",mvf:"mvf-mong-cn","mvf-phag":"mvf-phag-cn",mvg:"mvg-latn-mx",mvh:"mvh-latn-td",mvk:"mvk-latn-pg",mvl:"mvl-latn-au",mvn:"mvn-latn-zz",mvo:"mvo-latn-sb",mvp:"mvp-latn-id",mvq:"mvq-latn-pg",mvr:"mvr-latn-id",mvs:"mvs-latn-id",mvt:"mvt-latn-vu",mvu:"mvu-latn-td",mvv:"mvv-latn-my",mvw:"mvw-latn-tz",mvx:"mvx-latn-id",mvy:"mvy-arab-pk",mvz:"mvz-ethi-et","mvz-arab":"mvz-arab-et",mwa:"mwa-latn-pg",mwb:"mwb-latn-pg",mwc:"mwc-latn-pg",mwe:"mwe-latn-tz",mwf:"mwf-latn-au",mwg:"mwg-latn-pg",mwh:"mwh-latn-pg",mwi:"mwi-latn-vu",mwk:"mwk-latn-ml",mwl:"mwl-latn-pt",mwm:"mwm-latn-td",mwn:"mwn-latn-zm",mwo:"mwo-latn-vu",mwp:"mwp-latn-au",mwq:"mwq-latn-mm",mwr:"mwr-deva-in",mws:"mws-latn-ke",mwt:"mwt-mymr-mm","mwt-thai":"mwt-thai-th",mwu:"mwu-latn-ss",mwv:"mwv-latn-id",mww:"mww-hmnp-us",mwz:"mwz-latn-cd",mxa:"mxa-latn-mx",mxb:"mxb-latn-mx",mxc:"mxc-latn-zw",mxd:"mxd-latn-id",mxe:"mxe-latn-vu",mxf:"mxf-latn-cm",mxg:"mxg-latn-ao",mxh:"mxh-latn-cd",mxi:"mxi-latn-es",mxj:"mxj-latn-in",mxk:"mxk-latn-pg",mxl:"mxl-latn-bj",mxm:"mxm-latn-zz",mxn:"mxn-latn-id",mxo:"mxo-latn-zm",mxp:"mxp-latn-mx",mxq:"mxq-latn-mx",mxr:"mxr-latn-my",mxs:"mxs-latn-mx",mxt:"mxt-latn-mx",mxu:"mxu-latn-cm",mxv:"mxv-latn-mx",mxw:"mxw-latn-pg",mxx:"mxx-latn-ci",mxy:"mxy-latn-mx",mxz:"mxz-latn-id",my:"my-mymr-mm",myb:"myb-latn-td",myc:"myc-latn-cd",mye:"mye-latn-ga",myf:"myf-latn-et",myg:"myg-latn-cm",myh:"myh-latn-us",myj:"myj-latn-ss",myk:"myk-latn-zz",myl:"myl-latn-id",mym:"mym-ethi-zz",myp:"myp-latn-br",myr:"myr-latn-pe",myu:"myu-latn-br",myv:"myv-cyrl-ru",myw:"myw-latn-zz",myx:"myx-latn-ug",myy:"myy-latn-co",myz:"myz-mand-ir",mza:"mza-latn-mx",mzd:"mzd-latn-cm",mze:"mze-latn-pg",mzh:"mzh-latn-ar",mzi:"mzi-latn-mx",mzj:"mzj-latn-lr",mzk:"mzk-latn-zz",mzl:"mzl-latn-mx",mzm:"mzm-latn-zz",mzn:"mzn-arab-ir",mzo:"mzo-latn-br",mzp:"mzp-latn-zz",mzq:"mzq-latn-id",mzr:"mzr-latn-br",mzt:"mzt-latn-my",mzu:"mzu-latn-pg",mzv:"mzv-latn-cf",mzw:"mzw-latn-zz",mzx:"mzx-latn-gy",mzz:"mzz-latn-zz",na:"na-latn-nr",naa:"naa-latn-id",nab:"nab-latn-br",nac:"nac-latn-zz",nae:"nae-latn-id",naf:"naf-latn-zz",nag:"nag-latn-in",naj:"naj-latn-gn",nak:"nak-latn-zz",nal:"nal-latn-pg",nam:"nam-latn-au",nan:"nan-hans-cn",nao:"nao-deva-np",nap:"nap-latn-it",naq:"naq-latn-na",nar:"nar-latn-ng",nas:"nas-latn-zz",nat:"nat-latn-ng",naw:"naw-latn-gh",nax:"nax-latn-pg",nay:"nay-latn-au",naz:"naz-latn-mx",nb:"nb-latn-no",nba:"nba-latn-ao",nbb:"nbb-latn-ng",nbc:"nbc-latn-in",nbd:"nbd-latn-cd",nbe:"nbe-latn-in",nbh:"nbh-latn-ng",nbi:"nbi-latn-in",nbj:"nbj-latn-au",nbk:"nbk-latn-pg",nbm:"nbm-latn-cf",nbn:"nbn-latn-id",nbo:"nbo-latn-ng",nbp:"nbp-latn-ng",nbq:"nbq-latn-id",nbr:"nbr-latn-ng",nbt:"nbt-latn-in","nbt-deva":"nbt-deva-in",nbu:"nbu-latn-in",nbv:"nbv-latn-cm",nbw:"nbw-latn-cd",nby:"nby-latn-pg",nca:"nca-latn-zz",ncb:"ncb-latn-in","ncb-deva":"ncb-deva-in",ncc:"ncc-latn-pg",ncd:"ncd-deva-np",nce:"nce-latn-zz",ncf:"ncf-latn-zz",ncg:"ncg-latn-ca",nch:"nch-latn-mx",nci:"nci-latn-mx",ncj:"ncj-latn-mx",nck:"nck-latn-au",ncl:"ncl-latn-mx",ncm:"ncm-latn-pg",ncn:"ncn-latn-pg",nco:"nco-latn-zz",ncq:"ncq-laoo-la","ncq-thai":"ncq-thai-la",ncr:"ncr-latn-cm",nct:"nct-latn-in","nct-beng":"nct-beng-in",ncu:"ncu-latn-zz",ncx:"ncx-latn-mx",ncz:"ncz-latn-us",nd:"nd-latn-zw",nda:"nda-latn-cg",ndb:"ndb-latn-cm",ndc:"ndc-latn-mz",ndd:"ndd-latn-ng",ndf:"ndf-cyrl-ru",ndg:"ndg-latn-tz",ndh:"ndh-latn-tz",ndi:"ndi-latn-ng",ndj:"ndj-latn-tz",ndk:"ndk-latn-cd",ndl:"ndl-latn-cd",ndm:"ndm-latn-td",ndn:"ndn-latn-cg",ndp:"ndp-latn-ug",ndq:"ndq-latn-ao",ndr:"ndr-latn-ng",nds:"nds-latn-de",ndt:"ndt-latn-cd",ndu:"ndu-latn-cm",ndv:"ndv-latn-sn",ndw:"ndw-latn-cd",ndx:"ndx-latn-id",ndy:"ndy-latn-cf","ndy-td":"ndy-latn-td",ndz:"ndz-latn-ss",ne:"ne-deva-np",nea:"nea-latn-id",neb:"neb-latn-zz",nec:"nec-latn-id",ned:"ned-latn-ng",nee:"nee-latn-nc",neg:"neg-cyrl-ru",neh:"neh-tibt-bt",nei:"nei-xsux-tr",nej:"nej-latn-pg",nek:"nek-latn-nc",nem:"nem-latn-nc",nen:"nen-latn-nc",neo:"neo-latn-vn",neq:"neq-latn-mx",ner:"ner-latn-id",net:"net-latn-pg",neu:"neu-latn-001",new:"new-deva-np",nex:"nex-latn-zz",ney:"ney-latn-ci",nez:"nez-latn-us",nfa:"nfa-latn-id",nfd:"nfd-latn-ng",nfl:"nfl-latn-sb",nfr:"nfr-latn-zz",nfu:"nfu-latn-cm",ng:"ng-latn-na",nga:"nga-latn-zz",ngb:"ngb-latn-zz",ngc:"ngc-latn-cd",ngd:"ngd-latn-cf",nge:"nge-latn-cm",ngg:"ngg-latn-cf",ngh:"ngh-latn-za",ngi:"ngi-latn-ng",ngj:"ngj-latn-cm",ngk:"ngk-latn-au",ngl:"ngl-latn-mz",ngm:"ngm-latn-fm",ngn:"ngn-latn-cm",ngp:"ngp-latn-tz",ngq:"ngq-latn-tz",ngr:"ngr-latn-sb",ngs:"ngs-latn-ng",ngt:"ngt-laoo-la",ngu:"ngu-latn-mx",ngv:"ngv-latn-cm",ngw:"ngw-latn-ng",ngx:"ngx-latn-ng",ngy:"ngy-latn-cm",ngz:"ngz-latn-cg",nha:"nha-latn-au",nhb:"nhb-latn-zz",nhc:"nhc-latn-mx",nhd:"nhd-latn-py",nhe:"nhe-latn-mx",nhf:"nhf-latn-au",nhg:"nhg-latn-mx",nhi:"nhi-latn-mx",nhk:"nhk-latn-mx",nhm:"nhm-latn-mx",nhn:"nhn-latn-mx",nho:"nho-latn-pg",nhp:"nhp-latn-mx",nhq:"nhq-latn-mx",nhr:"nhr-latn-bw",nht:"nht-latn-mx",nhu:"nhu-latn-cm",nhv:"nhv-latn-mx",nhw:"nhw-latn-mx",nhx:"nhx-latn-mx",nhy:"nhy-latn-mx",nhz:"nhz-latn-mx",nia:"nia-latn-id",nib:"nib-latn-pg",nid:"nid-latn-au",nie:"nie-latn-td",nif:"nif-latn-zz",nig:"nig-latn-au",nih:"nih-latn-tz",nii:"nii-latn-zz",nij:"nij-latn-id",nil:"nil-latn-id",nim:"nim-latn-tz",nin:"nin-latn-zz",nio:"nio-cyrl-ru",niq:"niq-latn-ke",nir:"nir-latn-id",nis:"nis-latn-pg",nit:"nit-telu-in",niu:"niu-latn-nu",niv:"niv-cyrl-ru","niv-latn":"niv-latn-ru",niw:"niw-latn-pg",nix:"nix-latn-cd",niy:"niy-latn-zz",niz:"niz-latn-zz",nja:"nja-latn-ng",njb:"njb-latn-in",njd:"njd-latn-tz",njh:"njh-latn-in",nji:"nji-latn-au",njj:"njj-latn-cm",njl:"njl-latn-ss",njm:"njm-latn-in",njn:"njn-latn-in",njo:"njo-latn-in",njr:"njr-latn-ng",njs:"njs-latn-id",njt:"njt-latn-sr",nju:"nju-latn-au",njx:"njx-latn-cg",njy:"njy-latn-cm",njz:"njz-latn-in","njz-beng":"njz-beng-in",nka:"nka-latn-zm",nkb:"nkb-latn-in",nkc:"nkc-latn-cm",nkd:"nkd-latn-in",nke:"nke-latn-sb",nkf:"nkf-latn-in",nkg:"nkg-latn-zz",nkh:"nkh-latn-in",nki:"nki-latn-in","nki-beng":"nki-beng-in",nkj:"nkj-latn-id",nkk:"nkk-latn-vu",nkm:"nkm-latn-pg",nkn:"nkn-latn-ao",nko:"nko-latn-zz",nkq:"nkq-latn-gh",nkr:"nkr-latn-fm",nks:"nks-latn-id",nkt:"nkt-latn-tz",nku:"nku-latn-ci",nkv:"nkv-latn-mw",nkw:"nkw-latn-cd",nkx:"nkx-latn-ng",nkz:"nkz-latn-ng",nl:"nl-latn-nl",nla:"nla-latn-cm",nlc:"nlc-latn-id",nle:"nle-latn-ke",nlg:"nlg-latn-sb",nli:"nli-arab-af",nlj:"nlj-latn-cd",nlk:"nlk-latn-id",nlm:"nlm-arab-pk",nlo:"nlo-latn-cd",nlq:"nlq-latn-mm",nlu:"nlu-latn-gh",nlv:"nlv-latn-mx",nlw:"nlw-latn-au",nlx:"nlx-deva-in",nly:"nly-latn-au",nlz:"nlz-latn-sb",nma:"nma-latn-in",nmb:"nmb-latn-vu",nmc:"nmc-latn-td",nmd:"nmd-latn-ga",nme:"nme-latn-in",nmf:"nmf-latn-in",nmg:"nmg-latn-cm",nmh:"nmh-latn-in",nmi:"nmi-latn-ng",nmj:"nmj-latn-cf",nmk:"nmk-latn-vu",nml:"nml-latn-cm",nmm:"nmm-deva-np","nmm-tibt":"nmm-tibt-np",nmn:"nmn-latn-bw",nmo:"nmo-latn-in","nmo-beng":"nmo-beng-in",nmp:"nmp-latn-au",nmq:"nmq-latn-zw",nmr:"nmr-latn-cm",nms:"nms-latn-vu",nmt:"nmt-latn-fm",nmu:"nmu-latn-us",nmv:"nmv-latn-au",nmw:"nmw-latn-pg",nmx:"nmx-latn-pg",nmz:"nmz-latn-zz",nn:"nn-latn-no",nna:"nna-latn-au",nnb:"nnb-latn-cd",nnc:"nnc-latn-td",nnd:"nnd-latn-vu",nne:"nne-latn-ao",nnf:"nnf-latn-zz",nng:"nng-latn-in","nng-beng":"nng-beng-in",nnh:"nnh-latn-cm",nni:"nni-latn-id",nnj:"nnj-latn-et",nnk:"nnk-latn-zz",nnl:"nnl-latn-in",nnm:"nnm-latn-zz",nnn:"nnn-latn-td",nnp:"nnp-wcho-in",nnq:"nnq-latn-tz",nnr:"nnr-latn-au",nnt:"nnt-latn-us",nnu:"nnu-latn-gh",nnv:"nnv-latn-au",nnw:"nnw-latn-bf",nny:"nny-latn-au",nnz:"nnz-latn-cm",no:"no-latn-no",noa:"noa-latn-co",noc:"noc-latn-pg",nod:"nod-lana-th",noe:"noe-deva-in",nof:"nof-latn-pg",nog:"nog-cyrl-ru",noh:"noh-latn-pg",noi:"noi-deva-in",noj:"noj-latn-co",nok:"nok-latn-us",nom:"nom-latn-pe",non:"non-runr-se",nop:"nop-latn-zz",noq:"noq-latn-cd",nos:"nos-yiii-cn",not:"not-latn-pe",nou:"nou-latn-zz",nov:"nov-latn-001",now:"now-latn-tz",noy:"noy-latn-td",npb:"npb-tibt-bt",npg:"npg-latn-mm",nph:"nph-latn-in",npl:"npl-latn-mx",npn:"npn-latn-pg",npo:"npo-latn-in",nps:"nps-latn-id",npu:"npu-latn-in",npx:"npx-latn-sb",npy:"npy-latn-id",nqg:"nqg-latn-bj",nqk:"nqk-latn-bj",nql:"nql-latn-ao",nqm:"nqm-latn-id",nqn:"nqn-latn-pg",nqo:"nqo-nkoo-gn",nqq:"nqq-latn-mm",nqt:"nqt-latn-ng",nqy:"nqy-latn-mm",nr:"nr-latn-za",nra:"nra-latn-ga",nrb:"nrb-latn-zz",nre:"nre-latn-in",nrf:"nrf-latn-je",nrg:"nrg-latn-vu",nri:"nri-latn-in",nrk:"nrk-latn-au",nrl:"nrl-latn-au",nrm:"nrm-latn-my",nrp:"nrp-latn-it",nru:"nru-latn-cn","nru-hans":"nru-hans-cn","nru-hant":"nru-hant-cn",nrx:"nrx-latn-au",nrz:"nrz-latn-pg",nsa:"nsa-latn-in",nsb:"nsb-latn-za",nsc:"nsc-latn-ng",nsd:"nsd-yiii-cn",nse:"nse-latn-zm",nsf:"nsf-yiii-cn",nsg:"nsg-latn-tz",nsh:"nsh-latn-cm",nsk:"nsk-cans-ca",nsm:"nsm-latn-in",nsn:"nsn-latn-zz",nso:"nso-latn-za",nsq:"nsq-latn-us",nss:"nss-latn-zz",nst:"nst-tnsa-in",nsu:"nsu-latn-mx",nsv:"nsv-yiii-cn",nsw:"nsw-latn-vu",nsx:"nsx-latn-ao",nsy:"nsy-latn-id",nsz:"nsz-latn-us",ntd:"ntd-latn-my",nte:"nte-latn-mz",ntg:"ntg-latn-au",nti:"nti-latn-bf",ntj:"ntj-latn-au",ntk:"ntk-latn-tz",ntm:"ntm-latn-zz",nto:"nto-latn-cd",ntp:"ntp-latn-mx",ntr:"ntr-latn-zz",ntu:"ntu-latn-sb",ntx:"ntx-latn-mm",nty:"nty-yiii-vn",ntz:"ntz-arab-ir",nua:"nua-latn-nc",nuc:"nuc-latn-br",nud:"nud-latn-pg",nue:"nue-latn-cd",nuf:"nuf-latn-cn",nug:"nug-latn-au",nuh:"nuh-latn-ng",nui:"nui-latn-zz",nuj:"nuj-latn-ug",nuk:"nuk-latn-ca",num:"num-latn-to",nun:"nun-latn-mm",nuo:"nuo-latn-vn",nup:"nup-latn-zz",nuq:"nuq-latn-pg",nur:"nur-latn-pg",nus:"nus-latn-ss",nut:"nut-latn-vn",nuu:"nuu-latn-cd",nuv:"nuv-latn-zz",nuw:"nuw-latn-fm",nux:"nux-latn-zz",nuy:"nuy-latn-au",nuz:"nuz-latn-mx",nv:"nv-latn-us",nvh:"nvh-latn-vu",nvm:"nvm-latn-pg",nvo:"nvo-latn-cm",nwb:"nwb-latn-zz",nwc:"nwc-newa-np","nwc-brah":"nwc-brah-np","nwc-deva":"nwc-deva-np","nwc-sidd":"nwc-sidd-np",nwe:"nwe-latn-cm",nwg:"nwg-latn-au",nwi:"nwi-latn-vu",nwm:"nwm-latn-ss",nwo:"nwo-latn-au",nwr:"nwr-latn-pg",nww:"nww-latn-tz",nwx:"nwx-deva-np",nxa:"nxa-latn-tl",nxd:"nxd-latn-cd",nxe:"nxe-latn-id",nxg:"nxg-latn-id",nxi:"nxi-latn-tz",nxl:"nxl-latn-id",nxn:"nxn-latn-au",nxo:"nxo-latn-ga",nxq:"nxq-latn-cn",nxr:"nxr-latn-zz",nxx:"nxx-latn-id",ny:"ny-latn-mw",nyb:"nyb-latn-gh",nyc:"nyc-latn-cd",nyd:"nyd-latn-ke",nye:"nye-latn-ao",nyf:"nyf-latn-ke",nyg:"nyg-latn-cd",nyh:"nyh-latn-au",nyi:"nyi-latn-sd",nyj:"nyj-latn-cd",nyk:"nyk-latn-ao",nyl:"nyl-thai-th",nym:"nym-latn-tz",nyn:"nyn-latn-ug",nyo:"nyo-latn-ug",nyp:"nyp-latn-ug",nyq:"nyq-arab-ir",nyr:"nyr-latn-mw",nys:"nys-latn-au",nyt:"nyt-latn-au",nyu:"nyu-latn-mz",nyv:"nyv-latn-au",nyx:"nyx-latn-au",nyy:"nyy-latn-tz",nza:"nza-latn-cm",nzb:"nzb-latn-ga",nzd:"nzd-latn-cd",nzi:"nzi-latn-gh",nzk:"nzk-latn-cf",nzm:"nzm-latn-in",nzu:"nzu-latn-cg",nzy:"nzy-latn-td",nzz:"nzz-latn-ml",oaa:"oaa-cyrl-ru",oac:"oac-cyrl-ru",oar:"oar-syrc-sy",oav:"oav-geor-ge",obi:"obi-latn-us",obk:"obk-latn-ph",obl:"obl-latn-cm",obm:"obm-phnx-jo",obo:"obo-latn-ph",obr:"obr-mymr-mm",obt:"obt-latn-fr",obu:"obu-latn-ng",oc:"oc-latn-fr",oca:"oca-latn-pe",oco:"oco-latn-gb",ocu:"ocu-latn-mx",oda:"oda-latn-ng",odk:"odk-arab-pk",odt:"odt-latn-nl",odu:"odu-latn-ng",ofu:"ofu-latn-ng",ogb:"ogb-latn-ng",ogc:"ogc-latn-zz",ogg:"ogg-latn-ng",ogo:"ogo-latn-ng",ogu:"ogu-latn-ng",oht:"oht-xsux-tr",oia:"oia-latn-id",oie:"oie-latn-ss",oin:"oin-latn-pg",oj:"oj-cans-ca",ojb:"ojb-latn-ca","ojb-cans":"ojb-cans-ca",ojc:"ojc-latn-ca",ojs:"ojs-cans-ca",ojv:"ojv-latn-sb",ojw:"ojw-latn-ca","ojw-cans":"ojw-cans-ca",oka:"oka-latn-ca",okb:"okb-latn-ng",okc:"okc-latn-cd",okd:"okd-latn-ng",oke:"oke-latn-ng",okg:"okg-latn-au",oki:"oki-latn-ke",okk:"okk-latn-pg",okm:"okm-hang-kr",oko:"oko-hani-kr",okr:"okr-latn-zz",oks:"oks-latn-ng",oku:"oku-latn-cm",okv:"okv-latn-zz",okx:"okx-latn-ng",okz:"okz-khmr-kh",ola:"ola-deva-np","ola-tibt":"ola-tibt-cn",old:"old-latn-tz",ole:"ole-tibt-bt",olk:"olk-latn-au",olm:"olm-latn-ng",olo:"olo-latn-ru",olr:"olr-latn-vu",olt:"olt-latn-lt",olu:"olu-latn-ao",om:"om-latn-et",oma:"oma-latn-us",omb:"omb-latn-vu",omc:"omc-latn-pe",omg:"omg-latn-pe",omi:"omi-latn-cd",omk:"omk-cyrl-ru",oml:"oml-latn-cd",omo:"omo-latn-pg",omp:"omp-mtei-in",omr:"omr-modi-in",omt:"omt-latn-ke",omu:"omu-latn-pe",omw:"omw-latn-pg",ona:"ona-latn-ar",one:"one-latn-ca",ong:"ong-latn-zz",oni:"oni-latn-id",onj:"onj-latn-pg",onk:"onk-latn-pg",onn:"onn-latn-zz",ono:"ono-latn-ca",onp:"onp-latn-in","onp-deva":"onp-deva-in",onr:"onr-latn-pg",ons:"ons-latn-zz",ont:"ont-latn-pg",onu:"onu-latn-vu",onx:"onx-latn-id",ood:"ood-latn-us",oon:"oon-deva-in",oor:"oor-latn-za",opa:"opa-latn-ng",opk:"opk-latn-id",opm:"opm-latn-zz",opo:"opo-latn-pg",opt:"opt-latn-mx",opy:"opy-latn-br",or:"or-orya-in",ora:"ora-latn-sb",orc:"orc-latn-ke",ore:"ore-latn-pe",org:"org-latn-ng",orn:"orn-latn-my",oro:"oro-latn-zz",orr:"orr-latn-ng",ors:"ors-latn-my",ort:"ort-telu-in",oru:"oru-arab-zz",orv:"orv-cyrl-ru",orw:"orw-latn-br",orx:"orx-latn-ng",orz:"orz-latn-id",os:"os-cyrl-ge",osa:"osa-osge-us",osc:"osc-ital-it","osc-latn":"osc-latn-it",osi:"osi-java-id",oso:"oso-latn-ng",osp:"osp-latn-es",ost:"ost-latn-cm",osu:"osu-latn-pg",osx:"osx-latn-de",ota:"ota-arab-zz",otb:"otb-tibt-cn",otd:"otd-latn-id",ote:"ote-latn-mx",oti:"oti-latn-br",otk:"otk-orkh-mn",otl:"otl-latn-mx",otm:"otm-latn-mx",otn:"otn-latn-mx",otq:"otq-latn-mx",otr:"otr-latn-sd",ots:"ots-latn-mx",ott:"ott-latn-mx",otu:"otu-latn-br",otw:"otw-latn-ca",otx:"otx-latn-mx",oty:"oty-gran-in",otz:"otz-latn-mx",oub:"oub-latn-lr",oue:"oue-latn-pg",oui:"oui-ougr-143",oum:"oum-latn-pg",ovd:"ovd-latn-se",owi:"owi-latn-pg",owl:"owl-latn-gb",oyd:"oyd-latn-et",oym:"oym-latn-br",oyy:"oyy-latn-pg",ozm:"ozm-latn-zz",pa:"pa-guru-in","pa-arab":"pa-arab-pk","pa-pk":"pa-arab-pk",pab:"pab-latn-br",pac:"pac-latn-vn",pad:"pad-latn-br",pae:"pae-latn-cd",paf:"paf-latn-br",pag:"pag-latn-ph",pah:"pah-latn-br",pai:"pai-latn-ng",pak:"pak-latn-br",pal:"pal-phli-ir","pal-phlp":"pal-phlp-cn",pam:"pam-latn-ph",pao:"pao-latn-us",pap:"pap-latn-cw",paq:"paq-cyrl-tj",par:"par-latn-us",pas:"pas-latn-id",pau:"pau-latn-pw",pav:"pav-latn-br",paw:"paw-latn-us",pax:"pax-latn-br",pay:"pay-latn-hn",paz:"paz-latn-br",pbb:"pbb-latn-co",pbc:"pbc-latn-gy",pbe:"pbe-latn-mx",pbf:"pbf-latn-mx",pbg:"pbg-latn-ve",pbh:"pbh-latn-ve",pbi:"pbi-latn-zz",pbl:"pbl-latn-ng",pbm:"pbm-latn-mx",pbn:"pbn-latn-ng",pbo:"pbo-latn-gw",pbp:"pbp-latn-gn",pbr:"pbr-latn-tz",pbs:"pbs-latn-mx",pbt:"pbt-arab-af",pbv:"pbv-latn-in",pby:"pby-latn-pg",pca:"pca-latn-mx",pcb:"pcb-khmr-kh",pcc:"pcc-latn-cn","pcc-hani":"pcc-hani-cn",pcd:"pcd-latn-fr",pce:"pce-mymr-mm","pce-thai":"pce-thai-th",pcf:"pcf-mlym-in",pcg:"pcg-mlym-in","pcg-knda":"pcg-knda-in","pcg-taml":"pcg-taml-in",pch:"pch-deva-in",pci:"pci-deva-in","pci-orya":"pci-orya-in",pcj:"pcj-telu-in",pck:"pck-latn-in",pcm:"pcm-latn-ng",pcn:"pcn-latn-ng",pcp:"pcp-latn-bo",pcw:"pcw-latn-ng",pda:"pda-latn-pg",pdc:"pdc-latn-us",pdn:"pdn-latn-id",pdo:"pdo-latn-id",pdt:"pdt-latn-ca",pdu:"pdu-latn-mm","pdu-mymr":"pdu-mymr-mm",pea:"pea-latn-id",peb:"peb-latn-us",ped:"ped-latn-zz",pee:"pee-latn-id",peg:"peg-orya-in",pei:"pei-latn-mx",pek:"pek-latn-pg",pel:"pel-latn-id",pem:"pem-latn-cd",peo:"peo-xpeo-ir",pep:"pep-latn-pg",peq:"peq-latn-us",pev:"pev-latn-ve",pex:"pex-latn-zz",pey:"pey-latn-id",pez:"pez-latn-my",pfa:"pfa-latn-fm",pfe:"pfe-latn-cm",pfl:"pfl-latn-de",pga:"pga-latn-ss",pgd:"pgd-khar-pk",pgg:"pgg-deva-in",pgi:"pgi-latn-pg",pgk:"pgk-latn-vu",pgl:"pgl-ogam-ie",pgn:"pgn-ital-it",pgs:"pgs-latn-ng",pgu:"pgu-latn-id",phd:"phd-deva-in",phg:"phg-latn-vn",phh:"phh-latn-vn",phk:"phk-mymr-in",phl:"phl-arab-zz",phm:"phm-latn-mz",phn:"phn-phnx-lb",pho:"pho-laoo-la",phr:"phr-arab-pk",pht:"pht-thai-th",phv:"phv-arab-af",phw:"phw-deva-np",pi:"pi-sinh-in","pi-brah":"pi-brah-in","pi-deva":"pi-deva-in","pi-khar":"pi-khar-in","pi-khmr":"pi-khmr-in","pi-mymr":"pi-mymr-in","pi-thai":"pi-thai-in",pia:"pia-latn-mx",pib:"pib-latn-pe",pic:"pic-latn-ga",pid:"pid-latn-ve",pif:"pif-latn-fm",pig:"pig-latn-pe",pih:"pih-latn-nf",pij:"pij-latn-co",pil:"pil-latn-zz",pim:"pim-latn-us",pin:"pin-latn-pg",pio:"pio-latn-co",pip:"pip-latn-zz",pir:"pir-latn-br",pis:"pis-latn-sb",pit:"pit-latn-au",piu:"piu-latn-au",piv:"piv-latn-sb",piw:"piw-latn-tz",pix:"pix-latn-pg",piy:"piy-latn-ng",piz:"piz-latn-nc",pjt:"pjt-latn-au",pka:"pka-brah-in",pkb:"pkb-latn-ke",pkg:"pkg-latn-pg",pkh:"pkh-latn-bd","pkh-deva":"pkh-deva-bd",pkn:"pkn-latn-au",pko:"pko-latn-ke",pkp:"pkp-latn-ck",pkr:"pkr-mlym-in",pku:"pku-latn-id",pl:"pl-latn-pl",pla:"pla-latn-zz",plb:"plb-latn-vu",plc:"plc-latn-ph",pld:"pld-latn-gb",ple:"ple-latn-id",plg:"plg-latn-ar",plh:"plh-latn-id",plj:"plj-latn-ng",plk:"plk-arab-pk",pll:"pll-mymr-mm",pln:"pln-latn-co",plo:"plo-latn-mx",plr:"plr-latn-ci",pls:"pls-latn-mx",plu:"plu-latn-br",plv:"plv-latn-ph",plw:"plw-latn-ph",plz:"plz-latn-my",pma:"pma-latn-vu",pmb:"pmb-latn-cd",pmd:"pmd-latn-au",pme:"pme-latn-nc",pmf:"pmf-latn-id",pmh:"pmh-brah-in",pmi:"pmi-latn-cn",pmj:"pmj-latn-cn",pml:"pml-latn-tn",pmm:"pmm-latn-cm",pmn:"pmn-latn-cm",pmo:"pmo-latn-id",pmq:"pmq-latn-mx",pmr:"pmr-latn-pg",pms:"pms-latn-it",pmt:"pmt-latn-pf",pmw:"pmw-latn-us",pmx:"pmx-latn-in",pmy:"pmy-latn-id",pmz:"pmz-latn-mx",pna:"pna-latn-my",pnc:"pnc-latn-id",pnd:"pnd-latn-ao",pne:"pne-latn-my",png:"png-latn-zz",pnh:"pnh-latn-ck",pni:"pni-latn-id",pnj:"pnj-latn-au",pnk:"pnk-latn-bo",pnl:"pnl-latn-bf",pnm:"pnm-latn-my",pnn:"pnn-latn-zz",pno:"pno-latn-pe",pnp:"pnp-latn-id",pnq:"pnq-latn-bf",pnr:"pnr-latn-pg",pns:"pns-latn-id",pnt:"pnt-grek-gr",pnv:"pnv-latn-au",pnw:"pnw-latn-au",pny:"pny-latn-cm",pnz:"pnz-latn-cf",poc:"poc-latn-gt",poe:"poe-latn-mx",pof:"pof-latn-cd",pog:"pog-latn-br",poh:"poh-latn-gt",poi:"poi-latn-mx",pok:"pok-latn-br",pom:"pom-latn-us",pon:"pon-latn-fm",poo:"poo-latn-us",pop:"pop-latn-nc",poq:"poq-latn-mx",pos:"pos-latn-mx",pot:"pot-latn-us",pov:"pov-latn-gw",pow:"pow-latn-mx",poy:"poy-latn-tz",ppa:"ppa-deva-in",ppe:"ppe-latn-pg",ppi:"ppi-latn-mx",ppk:"ppk-latn-id",ppl:"ppl-latn-sv",ppm:"ppm-latn-id",ppn:"ppn-latn-pg",ppo:"ppo-latn-zz",ppp:"ppp-latn-cd",ppq:"ppq-latn-pg",pps:"pps-latn-mx",ppt:"ppt-latn-pg",pqa:"pqa-latn-ng",pqm:"pqm-latn-ca",pra:"pra-khar-pk",prc:"prc-arab-af",prd:"prd-arab-ir",pre:"pre-latn-st",prf:"prf-latn-ph",prg:"prg-latn-001",prh:"prh-latn-ph",pri:"pri-latn-nc",prk:"prk-latn-mm",prm:"prm-latn-pg",pro:"pro-latn-fr",prp:"prp-gujr-in",prq:"prq-latn-pe",prr:"prr-latn-br",prt:"prt-thai-th",pru:"pru-latn-id",prw:"prw-latn-pg",prx:"prx-arab-in","prx-tibt":"prx-tibt-in",ps:"ps-arab-af",psa:"psa-latn-id",pse:"pse-latn-id",psh:"psh-arab-af",psi:"psi-arab-af",psm:"psm-latn-bo",psn:"psn-latn-id",psq:"psq-latn-pg",pss:"pss-latn-zz",pst:"pst-arab-pk",psw:"psw-latn-vu",pt:"pt-latn-br",pta:"pta-latn-py",pth:"pth-latn-br",pti:"pti-latn-au",ptn:"ptn-latn-id",pto:"pto-latn-br",ptp:"ptp-latn-zz",ptr:"ptr-latn-vu",ptt:"ptt-latn-id",ptu:"ptu-latn-id",ptv:"ptv-latn-vu",pua:"pua-latn-mx",pub:"pub-latn-in",puc:"puc-latn-id",pud:"pud-latn-id",pue:"pue-latn-ar",puf:"puf-latn-id",pug:"pug-latn-bf",pui:"pui-latn-co",puj:"puj-latn-id",pum:"pum-deva-np",puo:"puo-latn-vn",pup:"pup-latn-pg",puq:"puq-latn-pe",pur:"pur-latn-br",put:"put-latn-id",puu:"puu-latn-ga",puw:"puw-latn-fm",pux:"pux-latn-pg",puy:"puy-latn-us",pwa:"pwa-latn-zz",pwb:"pwb-latn-ng",pwg:"pwg-latn-pg",pwm:"pwm-latn-ph",pwn:"pwn-latn-tw",pwo:"pwo-mymr-mm",pwr:"pwr-deva-in",pww:"pww-thai-th",pxm:"pxm-latn-mx",pye:"pye-latn-ci",pym:"pym-latn-ng",pyn:"pyn-latn-br",pyu:"pyu-latn-tw","pyu-hani":"pyu-hani-tw",pyx:"pyx-mymr-mm",pyy:"pyy-latn-mm",pzh:"pzh-latn-tw",pzn:"pzn-latn-mm",qu:"qu-latn-pe",qua:"qua-latn-us",qub:"qub-latn-pe",quc:"quc-latn-gt",qud:"qud-latn-ec",quf:"quf-latn-pe",qug:"qug-latn-ec",qui:"qui-latn-us",quk:"quk-latn-pe",qul:"qul-latn-bo",qum:"qum-latn-gt",qun:"qun-latn-us",qup:"qup-latn-pe",quq:"quq-latn-es",qur:"qur-latn-pe",qus:"qus-latn-ar",quv:"quv-latn-gt",quw:"quw-latn-ec",qux:"qux-latn-pe",quy:"quy-latn-pe",qva:"qva-latn-pe",qvc:"qvc-latn-pe",qve:"qve-latn-pe",qvh:"qvh-latn-pe",qvi:"qvi-latn-ec",qvj:"qvj-latn-ec",qvl:"qvl-latn-pe",qvm:"qvm-latn-pe",qvn:"qvn-latn-pe",qvo:"qvo-latn-pe",qvp:"qvp-latn-pe",qvs:"qvs-latn-pe",qvw:"qvw-latn-pe",qvz:"qvz-latn-ec",qwa:"qwa-latn-pe",qwc:"qwc-latn-pe",qwh:"qwh-latn-pe",qwm:"qwm-latn-ru","qwm-cyrl":"qwm-cyrl-ru","qwm-runr":"qwm-runr-ru",qws:"qws-latn-pe",qwt:"qwt-latn-us",qxa:"qxa-latn-pe",qxc:"qxc-latn-pe",qxh:"qxh-latn-pe",qxl:"qxl-latn-ec",qxn:"qxn-latn-pe",qxo:"qxo-latn-pe",qxp:"qxp-latn-pe",qxq:"qxq-arab-ir",qxr:"qxr-latn-ec",qxt:"qxt-latn-pe",qxu:"qxu-latn-pe",qxw:"qxw-latn-pe",qya:"qya-latn-001",qyp:"qyp-latn-us",raa:"raa-deva-np",rab:"rab-deva-np",rac:"rac-latn-id",rad:"rad-latn-vn",raf:"raf-deva-np",rag:"rag-latn-ke",rah:"rah-beng-in","rah-latn":"rah-latn-in",rai:"rai-latn-zz",raj:"raj-deva-in",rak:"rak-latn-pg",ram:"ram-latn-br",ran:"ran-latn-id",rao:"rao-latn-zz",rap:"rap-latn-cl",rar:"rar-latn-ck",rav:"rav-deva-np",raw:"raw-latn-mm",rax:"rax-latn-ng",ray:"ray-latn-pf",raz:"raz-latn-id",rbb:"rbb-mymr-mm",rbk:"rbk-latn-ph",rbl:"rbl-latn-ph",rbp:"rbp-latn-au",rcf:"rcf-latn-re",rdb:"rdb-arab-ir",rea:"rea-latn-pg",reb:"reb-latn-id",ree:"ree-latn-my",reg:"reg-latn-tz",rei:"rei-orya-in","rei-telu":"rei-telu-in",rej:"rej-latn-id",rel:"rel-latn-zz",rem:"rem-latn-pe",ren:"ren-latn-vn",res:"res-latn-zz",ret:"ret-latn-id",rey:"rey-latn-bo",rga:"rga-latn-vu",rgn:"rgn-latn-it",rgr:"rgr-latn-pe",rgs:"rgs-latn-vn",rgu:"rgu-latn-id",rhg:"rhg-rohg-mm",rhp:"rhp-latn-pg",ria:"ria-latn-in",rif:"rif-latn-ma",ril:"ril-latn-mm",rim:"rim-latn-tz",rin:"rin-latn-ng",rir:"rir-latn-id",rit:"rit-latn-au",riu:"riu-latn-id",rjg:"rjg-latn-id",rji:"rji-deva-np",rjs:"rjs-deva-np",rka:"rka-khmr-kh",rkb:"rkb-latn-br",rkh:"rkh-latn-ck",rki:"rki-mymr-mm",rkm:"rkm-latn-bf",rkt:"rkt-beng-bd",rkw:"rkw-latn-au",rm:"rm-latn-ch",rma:"rma-latn-ni",rmb:"rmb-latn-au",rmc:"rmc-latn-sk",rmd:"rmd-latn-dk",rme:"rme-latn-gb",rmf:"rmf-latn-fi",rmg:"rmg-latn-no",rmh:"rmh-latn-id",rmi:"rmi-armn-am",rmk:"rmk-latn-pg",rml:"rml-latn-pl","rml-cyrl":"rml-cyrl-by",rmm:"rmm-latn-id",rmn:"rmn-latn-rs","rmn-cyrl":"rmn-cyrl-bg","rmn-grek":"rmn-grek-gr",rmo:"rmo-latn-ch",rmp:"rmp-latn-pg",rmq:"rmq-latn-es",rmt:"rmt-arab-ir",rmu:"rmu-latn-se",rmw:"rmw-latn-gb",rmx:"rmx-latn-vn",rmz:"rmz-mymr-in",rn:"rn-latn-bi",rna:"rna-latn-zz",rnd:"rnd-latn-cd",rng:"rng-latn-mz",rnl:"rnl-latn-in",rnn:"rnn-latn-id",rnr:"rnr-latn-au",rnw:"rnw-latn-tz",ro:"ro-latn-ro",rob:"rob-latn-id",roc:"roc-latn-vn",rod:"rod-latn-ng",roe:"roe-latn-pg",rof:"rof-latn-tz",rog:"rog-latn-vn",rol:"rol-latn-ph",rom:"rom-latn-ro","rom-cyrl":"rom-cyrl-ro",roo:"roo-latn-zz",rop:"rop-latn-au",ror:"ror-latn-id",rou:"rou-latn-td",row:"row-latn-id",rpn:"rpn-latn-vu",rpt:"rpt-latn-pg",rri:"rri-latn-sb",rro:"rro-latn-zz",rrt:"rrt-latn-au",rsk:"rsk-cyrl-rs",rtc:"rtc-latn-mm",rth:"rth-latn-id",rtm:"rtm-latn-fj",rtw:"rtw-deva-in",ru:"ru-cyrl-ru",rub:"rub-latn-ug",ruc:"ruc-latn-ug",rue:"rue-cyrl-ua",ruf:"ruf-latn-tz",rug:"rug-latn-sb",rui:"rui-latn-tz",ruk:"ruk-latn-ng",ruo:"ruo-latn-hr",rup:"rup-latn-ro","rup-grek":"rup-grek-gr",ruq:"ruq-latn-gr",rut:"rut-cyrl-ru","rut-latn":"rut-latn-az",ruu:"ruu-latn-my",ruy:"ruy-latn-ng",ruz:"ruz-latn-ng",rw:"rw-latn-rw",rwa:"rwa-latn-pg",rwk:"rwk-latn-tz",rwl:"rwl-latn-tz",rwm:"rwm-latn-ug",rwo:"rwo-latn-zz",rwr:"rwr-deva-in",rxd:"rxd-latn-au",rxw:"rxw-latn-au",ryu:"ryu-kana-jp",sa:"sa-deva-in",saa:"saa-latn-td",sab:"sab-latn-pa",sac:"sac-latn-us",sad:"sad-latn-tz",sae:"sae-latn-br",saf:"saf-latn-gh",sah:"sah-cyrl-ru",saj:"saj-latn-id",sak:"sak-latn-ga",sam:"sam-samr-ps","sam-hebr":"sam-hebr-ps","sam-syrc":"sam-syrc-ps",sao:"sao-latn-id",saq:"saq-latn-ke",sar:"sar-latn-bo",sas:"sas-latn-id",sat:"sat-olck-in",sau:"sau-latn-id",sav:"sav-latn-sn",saw:"saw-latn-id",sax:"sax-latn-vu",say:"say-latn-ng",saz:"saz-saur-in",sba:"sba-latn-zz",sbb:"sbb-latn-sb",sbc:"sbc-latn-pg",sbd:"sbd-latn-bf",sbe:"sbe-latn-zz",sbg:"sbg-latn-id",sbh:"sbh-latn-pg",sbi:"sbi-latn-pg",sbj:"sbj-latn-td",sbk:"sbk-latn-tz",sbl:"sbl-latn-ph",sbm:"sbm-latn-tz",sbn:"sbn-arab-pk",sbo:"sbo-latn-my",sbp:"sbp-latn-tz",sbq:"sbq-latn-pg",sbr:"sbr-latn-id",sbs:"sbs-latn-na",sbt:"sbt-latn-id",sbu:"sbu-tibt-in","sbu-deva":"sbu-deva-in",sbv:"sbv-latn-it",sbw:"sbw-latn-ga",sbx:"sbx-latn-id",sby:"sby-latn-zm",sbz:"sbz-latn-cf",sc:"sc-latn-it",scb:"scb-latn-vn",sce:"sce-latn-cn","sce-arab":"sce-arab-cn",scf:"scf-latn-pa",scg:"scg-latn-id",sch:"sch-latn-in",sci:"sci-latn-lk",sck:"sck-deva-in",scl:"scl-arab-zz",scn:"scn-latn-it",sco:"sco-latn-gb",scp:"scp-deva-np",scs:"scs-latn-ca","scs-cans":"scs-cans-ca",sct:"sct-laoo-la",scu:"scu-takr-in",scv:"scv-latn-ng",scw:"scw-latn-ng",scx:"scx-grek-it",sd:"sd-arab-pk","sd-deva":"sd-deva-in","sd-in":"sd-deva-in","sd-khoj":"sd-khoj-in","sd-sind":"sd-sind-in",sda:"sda-latn-id",sdb:"sdb-arab-iq",sdc:"sdc-latn-it",sde:"sde-latn-ng",sdf:"sdf-arab-iq",sdg:"sdg-arab-af",sdh:"sdh-arab-ir",sdj:"sdj-latn-cg",sdk:"sdk-latn-pg",sdn:"sdn-latn-it",sdo:"sdo-latn-my",sdq:"sdq-latn-id",sds:"sds-arab-tn",sdu:"sdu-latn-id",sdx:"sdx-latn-my",se:"se-latn-no",sea:"sea-latn-my",seb:"seb-latn-ci",sec:"sec-latn-ca",sed:"sed-latn-vn",see:"see-latn-us",sef:"sef-latn-ci",seg:"seg-latn-tz",seh:"seh-latn-mz",sei:"sei-latn-mx",sej:"sej-latn-pg",sek:"sek-latn-ca","sek-cans":"sek-cans-ca",sel:"sel-cyrl-ru",sen:"sen-latn-bf",seo:"seo-latn-pg",sep:"sep-latn-bf",seq:"seq-latn-bf",ser:"ser-latn-us",ses:"ses-latn-ml",set:"set-latn-id",seu:"seu-latn-id",sev:"sev-latn-ci",sew:"sew-latn-pg",sey:"sey-latn-ec",sez:"sez-latn-mm",sfe:"sfe-latn-ph",sfm:"sfm-plrd-cn",sfw:"sfw-latn-gh",sg:"sg-latn-cf",sga:"sga-ogam-ie",sgb:"sgb-latn-ph",sgc:"sgc-latn-ke",sgd:"sgd-latn-ph",sge:"sge-latn-id",sgh:"sgh-cyrl-tj","sgh-arab":"sgh-arab-af","sgh-latn":"sgh-latn-tj",sgi:"sgi-latn-cm",sgj:"sgj-deva-in",sgm:"sgm-latn-ke",sgp:"sgp-latn-in",sgr:"sgr-arab-ir",sgs:"sgs-latn-lt",sgt:"sgt-tibt-bt",sgu:"sgu-latn-id",sgw:"sgw-ethi-zz",sgy:"sgy-arab-af",sgz:"sgz-latn-zz",sha:"sha-latn-ng",shb:"shb-latn-br",shc:"shc-latn-cd",shd:"shd-arab-pk",she:"she-latn-et",shg:"shg-latn-bw",shh:"shh-latn-us",shi:"shi-tfng-ma",shj:"shj-latn-sd",shk:"shk-latn-zz",shm:"shm-arab-ir",shn:"shn-mymr-mm",sho:"sho-latn-ng",shp:"shp-latn-pe",shq:"shq-latn-zm",shr:"shr-latn-cd",shs:"shs-latn-ca",sht:"sht-latn-us",shu:"shu-arab-zz",shv:"shv-arab-om",shw:"shw-latn-sd",shy:"shy-latn-dz","shy-arab":"shy-arab-dz","shy-tfng":"shy-tfng-dz",shz:"shz-latn-ml",si:"si-sinh-lk",sia:"sia-cyrl-ru",sib:"sib-latn-my",sid:"sid-latn-et",sie:"sie-latn-zm",sif:"sif-latn-bf",sig:"sig-latn-zz",sih:"sih-latn-nc",sii:"sii-latn-in",sij:"sij-latn-pg",sik:"sik-latn-br",sil:"sil-latn-zz",sim:"sim-latn-zz",sip:"sip-tibt-in",siq:"siq-latn-pg",sir:"sir-latn-ng",sis:"sis-latn-us",siu:"siu-latn-pg",siv:"siv-latn-pg",siw:"siw-latn-pg",six:"six-latn-pg",siy:"siy-arab-ir",siz:"siz-arab-eg",sja:"sja-latn-co",sjb:"sjb-latn-id",sjd:"sjd-cyrl-ru",sje:"sje-latn-se",sjg:"sjg-latn-td",sjl:"sjl-latn-in",sjm:"sjm-latn-ph",sjp:"sjp-deva-in","sjp-beng":"sjp-beng-in",sjr:"sjr-latn-zz",sjt:"sjt-cyrl-ru",sju:"sju-latn-se",sjw:"sjw-latn-us",sk:"sk-latn-sk",ska:"ska-latn-us",skb:"skb-thai-th",skc:"skc-latn-zz",skd:"skd-latn-us",ske:"ske-latn-vu",skf:"skf-latn-br",skg:"skg-latn-mg",skh:"skh-latn-id",ski:"ski-latn-id",skj:"skj-deva-np",skm:"skm-latn-pg",skn:"skn-latn-ph",sko:"sko-latn-id",skp:"skp-latn-my",skq:"skq-latn-bf",skr:"skr-arab-pk",sks:"sks-latn-zz",skt:"skt-latn-cd",sku:"sku-latn-vu",skv:"skv-latn-id",skw:"skw-latn-gy",skx:"skx-latn-id",sky:"sky-latn-sb",skz:"skz-latn-id",sl:"sl-latn-si",slc:"slc-latn-co",sld:"sld-latn-zz",slg:"slg-latn-id",slh:"slh-latn-us",sli:"sli-latn-pl",slj:"slj-latn-br",sll:"sll-latn-zz",slm:"slm-latn-ph",sln:"sln-latn-us",slp:"slp-latn-id",slq:"slq-arab-ir",slr:"slr-latn-cn",slu:"slu-latn-id",slw:"slw-latn-pg",slx:"slx-latn-cd",sly:"sly-latn-id",slz:"slz-latn-id",sm:"sm-latn-ws",sma:"sma-latn-se",smb:"smb-latn-pg",smc:"smc-latn-pg",smd:"smd-latn-ao",smf:"smf-latn-pg",smg:"smg-latn-pg",smh:"smh-yiii-cn",smj:"smj-latn-se",smk:"smk-latn-ph",sml:"sml-latn-ph",smn:"smn-latn-fi",smp:"smp-samr-il",smq:"smq-latn-zz",smr:"smr-latn-id",sms:"sms-latn-fi",smt:"smt-latn-in",smu:"smu-khmr-kh",smw:"smw-latn-id",smx:"smx-latn-cd",smy:"smy-arab-ir",smz:"smz-latn-pg",sn:"sn-latn-zw",snb:"snb-latn-my",snc:"snc-latn-zz",sne:"sne-latn-my",snf:"snf-latn-sn",sng:"sng-latn-cd","sng-brai":"sng-brai-cd",sni:"sni-latn-pe",snj:"snj-latn-cf",snk:"snk-latn-ml",snl:"snl-latn-ph",snm:"snm-latn-ug",snn:"snn-latn-co",sno:"sno-latn-us",snp:"snp-latn-zz",snq:"snq-latn-ga",snr:"snr-latn-pg",sns:"sns-latn-vu",snu:"snu-latn-id",snv:"snv-latn-my",snw:"snw-latn-gh",snx:"snx-latn-zz",sny:"sny-latn-zz",snz:"snz-latn-pg",so:"so-latn-so",soa:"soa-tavt-th","soa-thai":"soa-thai-th",sob:"sob-latn-id",soc:"soc-latn-cd",sod:"sod-latn-cd",soe:"soe-latn-cd",sog:"sog-sogd-uz",soi:"soi-deva-np",sok:"sok-latn-zz",sol:"sol-latn-pg",soo:"soo-latn-cd",sop:"sop-latn-cd",soq:"soq-latn-zz",sor:"sor-latn-td",sos:"sos-latn-bf",sou:"sou-thai-th",sov:"sov-latn-pw",sow:"sow-latn-pg",sox:"sox-latn-cm",soy:"soy-latn-zz",soz:"soz-latn-tz",spb:"spb-latn-id",spc:"spc-latn-ve",spd:"spd-latn-zz",spe:"spe-latn-pg",spg:"spg-latn-my",spi:"spi-latn-id",spk:"spk-latn-pg",spl:"spl-latn-zz",spm:"spm-latn-pg",spn:"spn-latn-py",spo:"spo-latn-us",spp:"spp-latn-ml",spq:"spq-latn-pe",spr:"spr-latn-id",sps:"sps-latn-zz",spt:"spt-tibt-in",spv:"spv-orya-in",sq:"sq-latn-al",sqa:"sqa-latn-ng",sqh:"sqh-latn-ng",sqm:"sqm-latn-cf",sqo:"sqo-arab-ir",sqq:"sqq-laoo-la",sqt:"sqt-arab-ye","sqt-latn":"sqt-latn-ye",squ:"squ-latn-ca",sr:"sr-cyrl-rs","sr-me":"sr-latn-me","sr-ro":"sr-latn-ro","sr-ru":"sr-latn-ru","sr-tr":"sr-latn-tr",sra:"sra-latn-pg",srb:"srb-sora-in",sre:"sre-latn-id",srf:"srf-latn-pg",srg:"srg-latn-ph",srh:"srh-arab-cn",sri:"sri-latn-co",srk:"srk-latn-my",srl:"srl-latn-id",srm:"srm-latn-sr",srn:"srn-latn-sr",sro:"sro-latn-it",srq:"srq-latn-bo",srr:"srr-latn-sn",srs:"srs-latn-ca",srt:"srt-latn-id",sru:"sru-latn-br",srv:"srv-latn-ph",srw:"srw-latn-id",srx:"srx-deva-in",sry:"sry-latn-pg",srz:"srz-arab-ir",ss:"ss-latn-za",ssb:"ssb-latn-ph",ssc:"ssc-latn-tz",ssd:"ssd-latn-zz",sse:"sse-latn-ph","sse-arab":"sse-arab-ph",ssf:"ssf-latn-tw",ssg:"ssg-latn-zz",ssh:"ssh-arab-ae",ssj:"ssj-latn-pg",ssl:"ssl-latn-gh",ssm:"ssm-latn-my",ssn:"ssn-latn-ke",sso:"sso-latn-pg",ssq:"ssq-latn-id",sss:"sss-laoo-la","sss-thai":"sss-thai-th",sst:"sst-latn-pg",ssu:"ssu-latn-pg",ssv:"ssv-latn-vu",ssx:"ssx-latn-pg",ssy:"ssy-latn-er",ssz:"ssz-latn-pg",st:"st-latn-za",sta:"sta-latn-zm",stb:"stb-latn-ph",ste:"ste-latn-id",stf:"stf-latn-pg",stg:"stg-latn-vn",sth:"sth-latn-ie",sti:"sti-latn-vn","sti-kh":"sti-latn-kh",stj:"stj-latn-bf",stk:"stk-latn-zz",stl:"stl-latn-nl",stm:"stm-latn-pg",stn:"stn-latn-sb",sto:"sto-latn-ca",stp:"stp-latn-mx",stq:"stq-latn-de",str:"str-latn-ca",sts:"sts-arab-af",stt:"stt-latn-vn",stv:"stv-ethi-et","stv-arab":"stv-arab-et",stw:"stw-latn-fm",sty:"sty-cyrl-ru",su:"su-latn-id",sua:"sua-latn-zz",sub:"sub-latn-cd",suc:"suc-latn-ph",sue:"sue-latn-zz",sug:"sug-latn-pg",sui:"sui-latn-pg",suj:"suj-latn-tz",suk:"suk-latn-tz",suo:"suo-latn-pg",suq:"suq-latn-et","suq-ethi":"suq-ethi-et",sur:"sur-latn-zz",sus:"sus-latn-gn",sut:"sut-latn-ni",suv:"suv-latn-in","suv-beng":"suv-beng-in","suv-deva":"suv-deva-in",suw:"suw-latn-tz",suy:"suy-latn-br",suz:"suz-deva-np",sv:"sv-latn-se",sva:"sva-geor-ge","sva-cyrl":"sva-cyrl-ge","sva-latn":"sva-latn-ge",svb:"svb-latn-pg",svc:"svc-latn-vc",sve:"sve-latn-id",svm:"svm-latn-it",svs:"svs-latn-sb",sw:"sw-latn-tz",swb:"swb-arab-yt",swc:"swc-latn-cd",swf:"swf-latn-cd",swg:"swg-latn-de",swi:"swi-hani-cn",swj:"swj-latn-ga",swk:"swk-latn-mw",swm:"swm-latn-pg",swo:"swo-latn-br",swp:"swp-latn-zz",swq:"swq-latn-cm",swr:"swr-latn-id",sws:"sws-latn-id",swt:"swt-latn-id",swu:"swu-latn-id",swv:"swv-deva-in",sww:"sww-latn-vu",swx:"swx-latn-br",swy:"swy-latn-td",sxb:"sxb-latn-ke",sxe:"sxe-latn-ga",sxn:"sxn-latn-id",sxr:"sxr-latn-tw",sxs:"sxs-latn-ng",sxu:"sxu-latn-de","sxu-runr":"sxu-runr-de",sxw:"sxw-latn-zz",sya:"sya-latn-id",syb:"syb-latn-ph",syc:"syc-syrc-tr",syi:"syi-latn-ga",syk:"syk-latn-ng",syl:"syl-beng-bd",sym:"sym-latn-bf",syn:"syn-syrc-ir",syo:"syo-latn-kh",syr:"syr-syrc-iq",sys:"sys-latn-td",syw:"syw-deva-np",syx:"syx-latn-ga",sza:"sza-latn-my",szb:"szb-latn-id",szc:"szc-latn-my",szd:"szd-latn-my",szg:"szg-latn-cd",szl:"szl-latn-pl",szn:"szn-latn-id",szp:"szp-latn-id",szv:"szv-latn-cm",szw:"szw-latn-id",szy:"szy-latn-tw",ta:"ta-taml-in",taa:"taa-latn-us",tab:"tab-cyrl-ru",tac:"tac-latn-mx",tad:"tad-latn-id",tae:"tae-latn-br",taf:"taf-latn-br",tag:"tag-latn-sd",taj:"taj-deva-np",tak:"tak-latn-ng",tal:"tal-latn-zz",tan:"tan-latn-zz",tao:"tao-latn-tw",tap:"tap-latn-cd",taq:"taq-latn-zz",tar:"tar-latn-mx",tas:"tas-latn-vn",tau:"tau-latn-us",tav:"tav-latn-co",taw:"taw-latn-pg",tax:"tax-latn-td",tay:"tay-latn-tw","tay-hans":"tay-hans-tw","tay-hant":"tay-hant-tw",taz:"taz-latn-sd",tba:"tba-latn-br",tbc:"tbc-latn-zz",tbd:"tbd-latn-zz",tbe:"tbe-latn-sb",tbf:"tbf-latn-zz",tbg:"tbg-latn-zz",tbh:"tbh-latn-au",tbi:"tbi-latn-sd",tbj:"tbj-latn-pg",tbk:"tbk-tagb-ph","tbk-hano":"tbk-hano-ph","tbk-latn":"tbk-latn-ph",tbl:"tbl-latn-ph",tbm:"tbm-latn-cd",tbn:"tbn-latn-co",tbo:"tbo-latn-zz",tbp:"tbp-latn-id",tbs:"tbs-latn-pg",tbt:"tbt-latn-cd",tbu:"tbu-latn-mx",tbv:"tbv-latn-pg",tbw:"tbw-latn-ph",tbx:"tbx-latn-pg",tby:"tby-latn-id",tbz:"tbz-latn-zz",tca:"tca-latn-br",tcb:"tcb-latn-us",tcc:"tcc-latn-tz",tcd:"tcd-latn-gh",tce:"tce-latn-ca",tcf:"tcf-latn-mx",tcg:"tcg-latn-id",tch:"tch-latn-tc",tci:"tci-latn-zz",tck:"tck-latn-ga",tcm:"tcm-latn-id",tcn:"tcn-tibt-np",tco:"tco-mymr-mm",tcp:"tcp-latn-mm",tcq:"tcq-latn-id",tcs:"tcs-latn-au",tcu:"tcu-latn-mx",tcw:"tcw-latn-mx",tcx:"tcx-taml-in",tcy:"tcy-knda-in",tcz:"tcz-latn-in",tda:"tda-tfng-ne","tda-arab":"tda-arab-ne","tda-latn":"tda-latn-ne",tdb:"tdb-deva-in","tdb-beng":"tdb-beng-in","tdb-kthi":"tdb-kthi-in",tdc:"tdc-latn-co",tdd:"tdd-tale-cn",tde:"tde-latn-ml",tdg:"tdg-deva-np",tdh:"tdh-deva-np",tdi:"tdi-latn-id",tdj:"tdj-latn-id",tdk:"tdk-latn-ng",tdl:"tdl-latn-ng",tdm:"tdm-latn-gy",tdn:"tdn-latn-id",tdo:"tdo-latn-ng",tdq:"tdq-latn-ng",tdr:"tdr-latn-vn",tds:"tds-latn-id",tdt:"tdt-latn-tl",tdu:"tdu-latn-my",tdv:"tdv-latn-ng",tdx:"tdx-latn-mg",tdy:"tdy-latn-ph",te:"te-telu-in",tea:"tea-latn-my",teb:"teb-latn-ec",tec:"tec-latn-ke",ted:"ted-latn-zz",tee:"tee-latn-mx",teg:"teg-latn-ga",teh:"teh-latn-ar",tei:"tei-latn-pg",tek:"tek-latn-cd",tem:"tem-latn-sl",ten:"ten-latn-co",teo:"teo-latn-ug",tep:"tep-latn-mx",teq:"teq-latn-sd",ter:"ter-latn-br",tes:"tes-java-id",tet:"tet-latn-tl",teu:"teu-latn-ug",tev:"tev-latn-id",tew:"tew-latn-us",tex:"tex-latn-ss",tey:"tey-latn-sd",tfi:"tfi-latn-zz",tfn:"tfn-latn-us",tfo:"tfo-latn-id",tfr:"tfr-latn-pa",tft:"tft-latn-id",tg:"tg-cyrl-tj","tg-arab":"tg-arab-pk","tg-pk":"tg-arab-pk",tga:"tga-latn-ke",tgb:"tgb-latn-my",tgc:"tgc-latn-zz",tgd:"tgd-latn-ng",tge:"tge-deva-np",tgf:"tgf-tibt-bt",tgh:"tgh-latn-tt",tgi:"tgi-latn-pg",tgj:"tgj-latn-in",tgn:"tgn-latn-ph",tgo:"tgo-latn-zz",tgp:"tgp-latn-vu",tgq:"tgq-latn-my",tgs:"tgs-latn-vu",tgt:"tgt-latn-ph","tgt-hano":"tgt-hano-ph","tgt-tagb":"tgt-tagb-ph",tgu:"tgu-latn-zz",tgv:"tgv-latn-br",tgw:"tgw-latn-ci",tgx:"tgx-latn-ca",tgy:"tgy-latn-ss",tgz:"tgz-latn-au",th:"th-thai-th",thd:"thd-latn-au",the:"the-deva-np",thf:"thf-deva-np",thh:"thh-latn-mx",thi:"thi-tale-la",thk:"thk-latn-ke",thl:"thl-deva-np",thm:"thm-thai-th",thp:"thp-latn-ca","thp-dupl":"thp-dupl-ca",thq:"thq-deva-np",thr:"thr-deva-np",ths:"ths-deva-np",tht:"tht-latn-ca",thu:"thu-latn-ss",thv:"thv-latn-dz","thv-arab":"thv-arab-dz","thv-tfng":"thv-tfng-dz",thy:"thy-latn-ng",thz:"thz-latn-ne","thz-tfng":"thz-tfng-ne",ti:"ti-ethi-et",tic:"tic-latn-sd",tif:"tif-latn-zz",tig:"tig-ethi-er",tih:"tih-latn-my",tii:"tii-latn-cd",tij:"tij-deva-np",tik:"tik-latn-zz",til:"til-latn-us",tim:"tim-latn-zz",tin:"tin-cyrl-ru",tio:"tio-latn-zz",tip:"tip-latn-id",tiq:"tiq-latn-bf",tis:"tis-latn-ph",tit:"tit-latn-co",tiu:"tiu-latn-ph",tiv:"tiv-latn-ng",tiw:"tiw-latn-au",tix:"tix-latn-us",tiy:"tiy-latn-ph",tja:"tja-latn-lr",tjg:"tjg-latn-id",tji:"tji-latn-cn",tjj:"tjj-latn-au",tjl:"tjl-mymr-mm",tjn:"tjn-latn-ci",tjo:"tjo-arab-dz",tjp:"tjp-latn-au",tjs:"tjs-latn-cn",tju:"tju-latn-au",tjw:"tjw-latn-au",tk:"tk-latn-tm",tka:"tka-latn-br",tkb:"tkb-deva-in",tkd:"tkd-latn-tl",tke:"tke-latn-mz",tkf:"tkf-latn-br",tkg:"tkg-latn-mg",tkl:"tkl-latn-tk",tkp:"tkp-latn-sb",tkq:"tkq-latn-ng",tkr:"tkr-latn-az",tks:"tks-arab-ir",tkt:"tkt-deva-np",tku:"tku-latn-mx",tkv:"tkv-latn-pg",tkw:"tkw-latn-sb",tkx:"tkx-latn-id",tkz:"tkz-latn-vn",tl:"tl-latn-ph",tla:"tla-latn-mx",tlb:"tlb-latn-id",tlc:"tlc-latn-mx",tld:"tld-latn-id",tlf:"tlf-latn-zz",tlg:"tlg-latn-id",tli:"tli-latn-us","tli-cyrl":"tli-cyrl-us",tlj:"tlj-latn-ug",tlk:"tlk-latn-id",tll:"tll-latn-cd",tlm:"tlm-latn-vu",tln:"tln-latn-id",tlp:"tlp-latn-mx",tlq:"tlq-latn-mm",tlr:"tlr-latn-sb",tls:"tls-latn-vu",tlt:"tlt-latn-id",tlu:"tlu-latn-id",tlv:"tlv-latn-id",tlx:"tlx-latn-zz",tly:"tly-latn-az",tma:"tma-latn-td",tmb:"tmb-latn-vu",tmc:"tmc-latn-td",tmd:"tmd-latn-pg",tme:"tme-latn-br",tmf:"tmf-latn-py",tmg:"tmg-latn-id",tmh:"tmh-latn-ne",tmi:"tmi-latn-vu",tmj:"tmj-latn-id",tmk:"tmk-deva-np",tml:"tml-latn-id",tmm:"tmm-latn-vn",tmn:"tmn-latn-id",tmo:"tmo-latn-my",tmq:"tmq-latn-pg",tmr:"tmr-syrc-il",tmt:"tmt-latn-vu",tmu:"tmu-latn-id",tmv:"tmv-latn-cd",tmw:"tmw-latn-my",tmy:"tmy-latn-zz",tmz:"tmz-latn-ve",tn:"tn-latn-za",tna:"tna-latn-bo",tnb:"tnb-latn-co",tnc:"tnc-latn-co",tnd:"tnd-latn-co",tng:"tng-latn-td",tnh:"tnh-latn-zz",tni:"tni-latn-id",tnk:"tnk-latn-vu",tnl:"tnl-latn-vu",tnm:"tnm-latn-id",tnn:"tnn-latn-vu",tno:"tno-latn-bo",tnp:"tnp-latn-vu",tnq:"tnq-latn-pr",tnr:"tnr-latn-sn",tns:"tns-latn-pg",tnt:"tnt-latn-id",tnv:"tnv-cakm-bd",tnw:"tnw-latn-id",tnx:"tnx-latn-sb",tny:"tny-latn-tz",to:"to-latn-to",tob:"tob-latn-ar",toc:"toc-latn-mx",tod:"tod-latn-gn",tof:"tof-latn-zz",tog:"tog-latn-mw",toh:"toh-latn-mz",toi:"toi-latn-zm",toj:"toj-latn-mx",tok:"tok-latn-001",tol:"tol-latn-us",tom:"tom-latn-id",too:"too-latn-mx",top:"top-latn-mx",toq:"toq-latn-zz",tor:"tor-latn-cd",tos:"tos-latn-mx",tou:"tou-latn-vn",tov:"tov-arab-ir",tow:"tow-latn-us",tox:"tox-latn-pw",toy:"toy-latn-id",toz:"toz-latn-cm",tpa:"tpa-latn-pg",tpc:"tpc-latn-mx",tpe:"tpe-latn-bd","tpe-beng":"tpe-beng-bd",tpf:"tpf-latn-id",tpg:"tpg-latn-id",tpi:"tpi-latn-pg",tpj:"tpj-latn-py",tpk:"tpk-latn-br",tpl:"tpl-latn-mx",tpm:"tpm-latn-zz",tpn:"tpn-latn-br",tpp:"tpp-latn-mx",tpr:"tpr-latn-br",tpt:"tpt-latn-mx",tpu:"tpu-khmr-kh",tpv:"tpv-latn-mp",tpx:"tpx-latn-mx",tpy:"tpy-latn-br",tpz:"tpz-latn-zz",tqb:"tqb-latn-br",tql:"tql-latn-vu",tqm:"tqm-latn-pg",tqn:"tqn-latn-us",tqo:"tqo-latn-zz",tqp:"tqp-latn-pg",tqt:"tqt-latn-mx",tqu:"tqu-latn-sb",tqw:"tqw-latn-us",tr:"tr-latn-tr",tra:"tra-arab-af",trb:"trb-latn-pg",trc:"trc-latn-mx",tre:"tre-latn-id",trf:"trf-latn-tt",trg:"trg-hebr-il",trh:"trh-latn-pg",tri:"tri-latn-sr",trj:"trj-latn-td",trl:"trl-latn-gb",trm:"trm-arab-af",trn:"trn-latn-bo",tro:"tro-latn-in",trp:"trp-latn-in","trp-beng":"trp-beng-in",trq:"trq-latn-mx",trr:"trr-latn-pe",trs:"trs-latn-mx",trt:"trt-latn-id",tru:"tru-latn-tr",trv:"trv-latn-tw",trw:"trw-arab-pk",trx:"trx-latn-my",try:"try-latn-in",trz:"trz-latn-br",ts:"ts-latn-za",tsa:"tsa-latn-cg",tsb:"tsb-latn-et",tsc:"tsc-latn-mz",tsd:"tsd-grek-gr",tsf:"tsf-deva-np",tsg:"tsg-latn-ph",tsh:"tsh-latn-cm",tsi:"tsi-latn-ca",tsj:"tsj-tibt-bt",tsl:"tsl-latn-vn",tsp:"tsp-latn-bf",tsr:"tsr-latn-vu",tst:"tst-latn-ml",tsu:"tsu-latn-tw",tsv:"tsv-latn-ga",tsw:"tsw-latn-zz",tsx:"tsx-latn-pg",tsz:"tsz-latn-mx",tt:"tt-cyrl-ru",ttb:"ttb-latn-ng",ttc:"ttc-latn-gt",ttd:"ttd-latn-zz",tte:"tte-latn-zz",ttf:"ttf-latn-cm",tth:"tth-laoo-la",tti:"tti-latn-id",ttj:"ttj-latn-ug",ttk:"ttk-latn-co",ttl:"ttl-latn-zm",ttm:"ttm-latn-ca",ttn:"ttn-latn-id",tto:"tto-laoo-la",ttp:"ttp-latn-id",ttr:"ttr-latn-zz",tts:"tts-thai-th",ttt:"ttt-latn-az",ttu:"ttu-latn-pg",ttv:"ttv-latn-pg",ttw:"ttw-latn-my",tty:"tty-latn-id",tua:"tua-latn-pg",tub:"tub-latn-us",tuc:"tuc-latn-pg",tud:"tud-latn-br",tue:"tue-latn-co",tuf:"tuf-latn-co",tug:"tug-latn-td",tuh:"tuh-latn-zz",tui:"tui-latn-cm",tuj:"tuj-latn-id",tul:"tul-latn-zz",tum:"tum-latn-mw",tun:"tun-latn-us",tuo:"tuo-latn-br",tuq:"tuq-latn-zz",tus:"tus-latn-ca",tuu:"tuu-latn-us",tuv:"tuv-latn-ke",tux:"tux-latn-br",tuy:"tuy-latn-ke",tuz:"tuz-latn-bf",tva:"tva-latn-sb",tvd:"tvd-latn-zz",tve:"tve-latn-id",tvk:"tvk-latn-vu",tvl:"tvl-latn-tv",tvm:"tvm-latn-id",tvn:"tvn-mymr-mm",tvo:"tvo-latn-id",tvs:"tvs-latn-ke",tvt:"tvt-latn-in",tvu:"tvu-latn-zz",tvw:"tvw-latn-id",tvx:"tvx-latn-tw",twa:"twa-latn-us",twb:"twb-latn-ph",twd:"twd-latn-nl",twe:"twe-latn-id",twf:"twf-latn-us",twg:"twg-latn-id",twh:"twh-latn-zz",twl:"twl-latn-mz",twm:"twm-deva-in",twn:"twn-latn-cm",two:"two-latn-bw",twp:"twp-latn-pg",twq:"twq-latn-ne",twr:"twr-latn-mx",twt:"twt-latn-br",twu:"twu-latn-id",tww:"tww-latn-pg",twx:"twx-latn-mz",twy:"twy-latn-id",txa:"txa-latn-my",txe:"txe-latn-id",txg:"txg-tang-cn",txi:"txi-latn-br",txj:"txj-latn-ng",txm:"txm-latn-id",txn:"txn-latn-id",txo:"txo-toto-in",txq:"txq-latn-id",txs:"txs-latn-id",txt:"txt-latn-id",txu:"txu-latn-br",txx:"txx-latn-my",txy:"txy-latn-mg",ty:"ty-latn-pf",tya:"tya-latn-zz",tye:"tye-latn-ng",tyh:"tyh-latn-vn",tyi:"tyi-latn-cg",tyj:"tyj-latn-vn",tyl:"tyl-latn-vn",tyn:"tyn-latn-id",typ:"typ-latn-au",tyr:"tyr-tavt-vn",tys:"tys-latn-vn",tyt:"tyt-latn-vn","tyt-tavt":"tyt-tavt-vn",tyu:"tyu-latn-bw",tyv:"tyv-cyrl-ru",tyx:"tyx-latn-cg",tyy:"tyy-latn-ng",tyz:"tyz-latn-vn",tzh:"tzh-latn-mx",tzj:"tzj-latn-gt",tzl:"tzl-latn-001",tzm:"tzm-latn-ma",tzn:"tzn-latn-id",tzo:"tzo-latn-mx",tzx:"tzx-latn-pg",uam:"uam-latn-br",uar:"uar-latn-pg",uba:"uba-latn-ng",ubi:"ubi-latn-td",ubl:"ubl-latn-ph",ubr:"ubr-latn-pg",ubu:"ubu-latn-zz",uda:"uda-latn-ng",ude:"ude-cyrl-ru",udg:"udg-mlym-in",udi:"udi-aghb-ru",udj:"udj-latn-id",udl:"udl-latn-cm",udm:"udm-cyrl-ru",udu:"udu-latn-sd",ues:"ues-latn-id",ufi:"ufi-latn-pg",ug:"ug-arab-cn","ug-cyrl":"ug-cyrl-kz","ug-kz":"ug-cyrl-kz","ug-mn":"ug-cyrl-mn",uga:"uga-ugar-sy",ugb:"ugb-latn-au",uge:"uge-latn-sb",ugh:"ugh-cyrl-ru",ugo:"ugo-thai-th",uha:"uha-latn-ng",uhn:"uhn-latn-id",uis:"uis-latn-pg",uiv:"uiv-latn-cm",uji:"uji-latn-ng",uk:"uk-cyrl-ua",uka:"uka-latn-id",ukg:"ukg-latn-pg",ukh:"ukh-latn-cf",uki:"uki-orya-in",ukk:"ukk-latn-mm",ukp:"ukp-latn-ng",ukq:"ukq-latn-ng",uku:"uku-latn-ng",ukv:"ukv-latn-ss",ukw:"ukw-latn-ng",uky:"uky-latn-au",ula:"ula-latn-ng",ulb:"ulb-latn-ng",ulc:"ulc-cyrl-ru",ule:"ule-latn-ar",ulf:"ulf-latn-id",uli:"uli-latn-fm",ulk:"ulk-latn-au",ulm:"ulm-latn-id",uln:"uln-latn-pg",ulu:"ulu-latn-id",ulw:"ulw-latn-ni",uma:"uma-latn-us",umb:"umb-latn-ao",umd:"umd-latn-au",umg:"umg-latn-au",umi:"umi-latn-my",umm:"umm-latn-ng",umn:"umn-latn-mm",umo:"umo-latn-br",ump:"ump-latn-au",umr:"umr-latn-au",ums:"ums-latn-id",una:"una-latn-pg",und:"en-latn-us","und-002":"en-latn-ng","und-003":"en-latn-us","und-005":"pt-latn-br","und-009":"en-latn-au","und-011":"en-latn-ng","und-013":"es-latn-mx","und-014":"sw-latn-tz","und-015":"ar-arab-eg","und-017":"sw-latn-cd","und-018":"en-latn-za","und-019":"en-latn-us","und-021":"en-latn-us","und-029":"es-latn-cu","und-030":"zh-hans-cn","und-034":"hi-deva-in","und-035":"id-latn-id","und-039":"it-latn-it","und-053":"en-latn-au","und-054":"en-latn-pg","und-057":"en-latn-gu","und-061":"sm-latn-ws","und-142":"zh-hans-cn","und-143":"uz-latn-uz","und-145":"ar-arab-sa","und-150":"ru-cyrl-ru","und-151":"ru-cyrl-ru","und-154":"en-latn-gb","und-155":"de-latn-de","und-202":"en-latn-ng","und-419":"es-latn-419","und-ad":"ca-latn-ad","und-adlm":"ff-adlm-gn","und-ae":"ar-arab-ae","und-af":"fa-arab-af","und-aghb":"udi-aghb-ru","und-ahom":"aho-ahom-in","und-al":"sq-latn-al","und-am":"hy-armn-am","und-ao":"pt-latn-ao","und-aq":"und-latn-aq","und-ar":"es-latn-ar","und-arab":"ar-arab-eg","und-arab-cc":"ms-arab-cc","und-arab-cn":"ug-arab-cn","und-arab-gb":"ur-arab-gb","und-arab-id":"ms-arab-id","und-arab-in":"ur-arab-in","und-arab-kh":"cja-arab-kh","und-arab-mm":"rhg-arab-mm","und-arab-mn":"kk-arab-mn","und-arab-mu":"ur-arab-mu","und-arab-ng":"ha-arab-ng","und-arab-pk":"ur-arab-pk","und-arab-tg":"apd-arab-tg","und-arab-th":"mfa-arab-th","und-arab-tj":"fa-arab-tj","und-arab-tr":"apc-arab-tr","und-arab-yt":"swb-arab-yt","und-armi":"arc-armi-ir","und-armn":"hy-armn-am","und-as":"sm-latn-as","und-at":"de-latn-at","und-avst":"ae-avst-ir","und-aw":"nl-latn-aw","und-ax":"sv-latn-ax","und-az":"az-latn-az","und-ba":"bs-latn-ba","und-bali":"ban-bali-id","und-bamu":"bax-bamu-cm","und-bass":"bsq-bass-lr","und-batk":"bbc-batk-id","und-bd":"bn-beng-bd","und-be":"nl-latn-be","und-beng":"bn-beng-bd","und-bf":"fr-latn-bf","und-bg":"bg-cyrl-bg","und-bh":"ar-arab-bh","und-bhks":"sa-bhks-in","und-bi":"rn-latn-bi","und-bj":"fr-latn-bj","und-bl":"fr-latn-bl","und-bn":"ms-latn-bn","und-bo":"es-latn-bo","und-bopo":"zh-bopo-tw","und-bq":"pap-latn-bq","und-br":"pt-latn-br","und-brah":"pka-brah-in","und-brai":"fr-brai-fr","und-bt":"dz-tibt-bt","und-bugi":"bug-bugi-id","und-buhd":"bku-buhd-ph","und-bv":"und-latn-bv","und-by":"be-cyrl-by","und-cakm":"ccp-cakm-bd","und-cans":"iu-cans-ca","und-cari":"xcr-cari-tr","und-cd":"sw-latn-cd","und-cf":"fr-latn-cf","und-cg":"fr-latn-cg","und-ch":"de-latn-ch","und-cham":"cjm-cham-vn","und-cher":"chr-cher-us","und-chrs":"xco-chrs-uz","und-ci":"fr-latn-ci","und-cl":"es-latn-cl","und-cm":"fr-latn-cm","und-cn":"zh-hans-cn","und-co":"es-latn-co","und-copt":"cop-copt-eg","und-cp":"und-latn-cp","und-cpmn":"und-cpmn-cy","und-cpmn-cy":"und-cpmn-cy","und-cprt":"grc-cprt-cy","und-cr":"es-latn-cr","und-cu":"es-latn-cu","und-cv":"pt-latn-cv","und-cw":"pap-latn-cw","und-cy":"el-grek-cy","und-cyrl":"ru-cyrl-ru","und-cyrl-al":"mk-cyrl-al","und-cyrl-ba":"sr-cyrl-ba","und-cyrl-ge":"ab-cyrl-ge","und-cyrl-gr":"mk-cyrl-gr","und-cyrl-md":"uk-cyrl-md","und-cyrl-ro":"bg-cyrl-ro","und-cyrl-sk":"uk-cyrl-sk","und-cyrl-tr":"kbd-cyrl-tr","und-cyrl-xk":"sr-cyrl-xk","und-cz":"cs-latn-cz","und-de":"de-latn-de","und-deva":"hi-deva-in","und-deva-bt":"ne-deva-bt","und-deva-fj":"hif-deva-fj","und-deva-mu":"bho-deva-mu","und-deva-pk":"btv-deva-pk","und-diak":"dv-diak-mv","und-dj":"aa-latn-dj","und-dk":"da-latn-dk","und-do":"es-latn-do","und-dogr":"doi-dogr-in","und-dupl":"fr-dupl-fr","und-dz":"ar-arab-dz","und-ea":"es-latn-ea","und-ec":"es-latn-ec","und-ee":"et-latn-ee","und-eg":"ar-arab-eg","und-egyp":"egy-egyp-eg","und-eh":"ar-arab-eh","und-elba":"sq-elba-al","und-elym":"arc-elym-ir","und-er":"ti-ethi-er","und-es":"es-latn-es","und-et":"am-ethi-et","und-ethi":"am-ethi-et","und-eu":"en-latn-ie","und-ez":"de-latn-ez","und-fi":"fi-latn-fi","und-fo":"fo-latn-fo","und-fr":"fr-latn-fr","und-ga":"fr-latn-ga","und-ge":"ka-geor-ge","und-geor":"ka-geor-ge","und-gf":"fr-latn-gf","und-gh":"ak-latn-gh","und-gl":"kl-latn-gl","und-glag":"cu-glag-bg","und-gn":"fr-latn-gn","und-gong":"wsg-gong-in","und-gonm":"esg-gonm-in","und-goth":"got-goth-ua","und-gp":"fr-latn-gp","und-gq":"es-latn-gq","und-gr":"el-grek-gr","und-gran":"sa-gran-in","und-grek":"el-grek-gr","und-grek-tr":"bgx-grek-tr","und-gs":"und-latn-gs","und-gt":"es-latn-gt","und-gujr":"gu-gujr-in","und-guru":"pa-guru-in","und-gw":"pt-latn-gw","und-hanb":"zh-hanb-tw","und-hang":"ko-hang-kr","und-hani":"zh-hani-cn","und-hano":"hnn-hano-ph","und-hans":"zh-hans-cn","und-hant":"zh-hant-tw","und-hant-ca":"yue-hant-ca","und-hebr":"he-hebr-il","und-hebr-se":"yi-hebr-se","und-hebr-ua":"yi-hebr-ua","und-hebr-us":"yi-hebr-us","und-hira":"ja-hira-jp","und-hk":"zh-hant-hk","und-hluw":"hlu-hluw-tr","und-hm":"und-latn-hm","und-hmng":"hnj-hmng-la","und-hmnp":"hnj-hmnp-us","und-hn":"es-latn-hn","und-hr":"hr-latn-hr","und-ht":"ht-latn-ht","und-hu":"hu-latn-hu","und-hung":"hu-hung-hu","und-ic":"es-latn-ic","und-id":"id-latn-id","und-il":"he-hebr-il","und-in":"hi-deva-in","und-iq":"ar-arab-iq","und-ir":"fa-arab-ir","und-is":"is-latn-is","und-it":"it-latn-it","und-ital":"ett-ital-it","und-jamo":"ko-jamo-kr","und-java":"jv-java-id","und-jo":"ar-arab-jo","und-jp":"ja-jpan-jp","und-jpan":"ja-jpan-jp","und-kali":"eky-kali-mm","und-kana":"ja-kana-jp","und-kawi":"kaw-kawi-id","und-ke":"sw-latn-ke","und-kg":"ky-cyrl-kg","und-kh":"km-khmr-kh","und-khar":"pra-khar-pk","und-khmr":"km-khmr-kh","und-khoj":"sd-khoj-in","und-kits":"zkt-kits-cn","und-km":"ar-arab-km","und-knda":"kn-knda-in","und-kore":"ko-kore-kr","und-kp":"ko-kore-kp","und-kr":"ko-kore-kr","und-kthi":"bho-kthi-in","und-kw":"ar-arab-kw","und-kz":"ru-cyrl-kz","und-la":"lo-laoo-la","und-lana":"nod-lana-th","und-laoo":"lo-laoo-la","und-laoo-au":"hnj-laoo-au","und-laoo-cn":"hnj-laoo-cn","und-laoo-fr":"hnj-laoo-fr","und-laoo-gf":"hnj-laoo-gf","und-laoo-mm":"hnj-laoo-mm","und-laoo-sr":"hnj-laoo-sr","und-laoo-th":"hnj-laoo-th","und-laoo-us":"hnj-laoo-us","und-laoo-vn":"hnj-laoo-vn","und-latn-af":"tk-latn-af","und-latn-am":"ku-latn-am","und-latn-cn":"za-latn-cn","und-latn-cy":"tr-latn-cy","und-latn-dz":"fr-latn-dz","und-latn-et":"en-latn-et","und-latn-ge":"ku-latn-ge","und-latn-ir":"tk-latn-ir","und-latn-km":"fr-latn-km","und-latn-ma":"fr-latn-ma","und-latn-mk":"sq-latn-mk","und-latn-mm":"kac-latn-mm","und-latn-mo":"pt-latn-mo","und-latn-mr":"fr-latn-mr","und-latn-ru":"krl-latn-ru","und-latn-sy":"fr-latn-sy","und-latn-tn":"fr-latn-tn","und-latn-tw":"trv-latn-tw","und-latn-ua":"pl-latn-ua","und-lb":"ar-arab-lb","und-lepc":"lep-lepc-in","und-li":"de-latn-li","und-limb":"lif-limb-in","und-lina":"lab-lina-gr","und-linb":"grc-linb-gr","und-lisu":"lis-lisu-cn","und-lk":"si-sinh-lk","und-ls":"st-latn-ls","und-lt":"lt-latn-lt","und-lu":"fr-latn-lu","und-lv":"lv-latn-lv","und-ly":"ar-arab-ly","und-lyci":"xlc-lyci-tr","und-lydi":"xld-lydi-tr","und-ma":"ar-arab-ma","und-mahj":"hi-mahj-in","und-maka":"mak-maka-id","und-mand":"myz-mand-ir","und-mani":"xmn-mani-cn","und-marc":"bo-marc-cn","und-mc":"fr-latn-mc","und-md":"ro-latn-md","und-me":"sr-latn-me","und-medf":"dmf-medf-ng","und-mend":"men-mend-sl","und-merc":"xmr-merc-sd","und-mero":"xmr-mero-sd","und-mf":"fr-latn-mf","und-mg":"mg-latn-mg","und-mk":"mk-cyrl-mk","und-ml":"bm-latn-ml","und-mlym":"ml-mlym-in","und-mm":"my-mymr-mm","und-mn":"mn-cyrl-mn","und-mo":"zh-hant-mo","und-modi":"mr-modi-in","und-mong":"mn-mong-cn","und-mq":"fr-latn-mq","und-mr":"ar-arab-mr","und-mroo":"mro-mroo-bd","und-mt":"mt-latn-mt","und-mtei":"mni-mtei-in","und-mu":"mfe-latn-mu","und-mult":"skr-mult-pk","und-mv":"dv-thaa-mv","und-mx":"es-latn-mx","und-my":"ms-latn-my","und-mymr":"my-mymr-mm","und-mymr-in":"kht-mymr-in","und-mymr-th":"mnw-mymr-th","und-mz":"pt-latn-mz","und-na":"af-latn-na","und-nagm":"unr-nagm-in","und-nand":"sa-nand-in","und-narb":"xna-narb-sa","und-nbat":"arc-nbat-jo","und-nc":"fr-latn-nc","und-ne":"ha-latn-ne","und-newa":"new-newa-np","und-ni":"es-latn-ni","und-nkoo":"man-nkoo-gn","und-nl":"nl-latn-nl","und-no":"nb-latn-no","und-np":"ne-deva-np","und-nshu":"zhx-nshu-cn","und-ogam":"sga-ogam-ie","und-olck":"sat-olck-in","und-om":"ar-arab-om","und-orkh":"otk-orkh-mn","und-orya":"or-orya-in","und-osge":"osa-osge-us","und-osma":"so-osma-so","und-ougr":"oui-ougr-143","und-pa":"es-latn-pa","und-palm":"arc-palm-sy","und-pauc":"ctd-pauc-mm","und-pe":"es-latn-pe","und-perm":"kv-perm-ru","und-pf":"fr-latn-pf","und-pg":"tpi-latn-pg","und-ph":"fil-latn-ph","und-phag":"lzh-phag-cn","und-phli":"pal-phli-ir","und-phlp":"pal-phlp-cn","und-phnx":"phn-phnx-lb","und-pk":"ur-arab-pk","und-pl":"pl-latn-pl","und-plrd":"hmd-plrd-cn","und-pm":"fr-latn-pm","und-pr":"es-latn-pr","und-prti":"xpr-prti-ir","und-ps":"ar-arab-ps","und-pt":"pt-latn-pt","und-pw":"pau-latn-pw","und-py":"gn-latn-py","und-qa":"ar-arab-qa","und-qo":"en-latn-dg","und-re":"fr-latn-re","und-rjng":"rej-rjng-id","und-ro":"ro-latn-ro","und-rohg":"rhg-rohg-mm","und-rs":"sr-cyrl-rs","und-ru":"ru-cyrl-ru","und-runr":"non-runr-se","und-rw":"rw-latn-rw","und-sa":"ar-arab-sa","und-samr":"smp-samr-il","und-sarb":"xsa-sarb-ye","und-saur":"saz-saur-in","und-sc":"fr-latn-sc","und-sd":"ar-arab-sd","und-se":"sv-latn-se","und-sgnw":"ase-sgnw-us","und-shaw":"en-shaw-gb","und-shrd":"sa-shrd-in","und-si":"sl-latn-si","und-sidd":"sa-sidd-in","und-sind":"sd-sind-in","und-sinh":"si-sinh-lk","und-sj":"nb-latn-sj","und-sk":"sk-latn-sk","und-sm":"it-latn-sm","und-sn":"fr-latn-sn","und-so":"so-latn-so","und-sogd":"sog-sogd-uz","und-sogo":"sog-sogo-uz","und-sora":"srb-sora-in","und-soyo":"cmg-soyo-mn","und-sr":"nl-latn-sr","und-st":"pt-latn-st","und-sund":"su-sund-id","und-sv":"es-latn-sv","und-sy":"ar-arab-sy","und-sylo":"syl-sylo-bd","und-syrc":"syr-syrc-iq","und-tagb":"tbw-tagb-ph","und-takr":"doi-takr-in","und-tale":"tdd-tale-cn","und-talu":"khb-talu-cn","und-taml":"ta-taml-in","und-tang":"txg-tang-cn","und-tavt":"blt-tavt-vn","und-td":"fr-latn-td","und-telu":"te-telu-in","und-tf":"fr-latn-tf","und-tfng":"zgh-tfng-ma","und-tg":"fr-latn-tg","und-tglg":"fil-tglg-ph","und-th":"th-thai-th","und-thaa":"dv-thaa-mv","und-thai":"th-thai-th","und-thai-cn":"lcp-thai-cn","und-thai-kh":"kdt-thai-kh","und-thai-la":"kdt-thai-la","und-tibt":"bo-tibt-cn","und-tirh":"mai-tirh-in","und-tj":"tg-cyrl-tj","und-tk":"tkl-latn-tk","und-tl":"pt-latn-tl","und-tm":"tk-latn-tm","und-tn":"ar-arab-tn","und-tnsa":"nst-tnsa-in","und-to":"to-latn-to","und-toto":"txo-toto-in","und-tr":"tr-latn-tr","und-tv":"tvl-latn-tv","und-tw":"zh-hant-tw","und-tz":"sw-latn-tz","und-ua":"uk-cyrl-ua","und-ug":"sw-latn-ug","und-ugar":"uga-ugar-sy","und-uy":"es-latn-uy","und-uz":"uz-latn-uz","und-va":"it-latn-va","und-vaii":"vai-vaii-lr","und-ve":"es-latn-ve","und-vith":"sq-vith-al","und-vn":"vi-latn-vn","und-vu":"bi-latn-vu","und-wara":"hoc-wara-in","und-wcho":"nnp-wcho-in","und-wf":"fr-latn-wf","und-ws":"sm-latn-ws","und-xk":"sq-latn-xk","und-xpeo":"peo-xpeo-ir","und-xsux":"akk-xsux-iq","und-ye":"ar-arab-ye","und-yezi":"ku-yezi-ge","und-yiii":"ii-yiii-cn","und-yt":"fr-latn-yt","und-zanb":"cmg-zanb-mn","und-zw":"sn-latn-zw",une:"une-latn-ng",ung:"ung-latn-au",uni:"uni-latn-pg",unk:"unk-latn-br",unm:"unm-latn-us",unn:"unn-latn-au",unr:"unr-beng-in","unr-deva":"unr-deva-np","unr-np":"unr-deva-np",unu:"unu-latn-pg",unx:"unx-beng-in",unz:"unz-latn-id",uok:"uok-latn-zz",uon:"uon-latn-tw",upi:"upi-latn-pg",upv:"upv-latn-vu",ur:"ur-arab-pk",ura:"ura-latn-pe",urb:"urb-latn-br",urc:"urc-latn-au",ure:"ure-latn-bo",urf:"urf-latn-au",urg:"urg-latn-pg",urh:"urh-latn-ng",uri:"uri-latn-zz",urk:"urk-thai-th",urm:"urm-latn-pg",urn:"urn-latn-id",uro:"uro-latn-pg",urp:"urp-latn-br",urr:"urr-latn-vu",urt:"urt-latn-zz",uru:"uru-latn-br",urv:"urv-latn-pg",urw:"urw-latn-zz",urx:"urx-latn-pg",ury:"ury-latn-id",urz:"urz-latn-br",usa:"usa-latn-zz",ush:"ush-arab-pk",usi:"usi-latn-bd","usi-beng":"usi-beng-bd",usk:"usk-latn-cm",usp:"usp-latn-gt",uss:"uss-latn-ng",usu:"usu-latn-pg",uta:"uta-latn-ng",ute:"ute-latn-us",uth:"uth-latn-zz",utp:"utp-latn-sb",utr:"utr-latn-zz",utu:"utu-latn-pg",uum:"uum-grek-ge","uum-cyrl":"uum-cyrl-ge",uur:"uur-latn-vu",uve:"uve-latn-nc",uvh:"uvh-latn-zz",uvl:"uvl-latn-zz",uwa:"uwa-latn-au",uya:"uya-latn-ng",uz:"uz-latn-uz","uz-af":"uz-arab-af","uz-arab":"uz-arab-af","uz-cn":"uz-cyrl-cn",uzs:"uzs-arab-af",vaa:"vaa-taml-in",vae:"vae-latn-cf",vaf:"vaf-arab-ir",vag:"vag-latn-zz",vah:"vah-deva-in",vai:"vai-vaii-lr",vaj:"vaj-latn-na",val:"val-latn-pg",vam:"vam-latn-pg",van:"van-latn-zz",vao:"vao-latn-vu",vap:"vap-latn-in",var:"var-latn-mx",vas:"vas-deva-in","vas-gujr":"vas-gujr-in",vau:"vau-latn-cd",vav:"vav-deva-in","vav-gujr":"vav-gujr-in",vay:"vay-deva-np",vbb:"vbb-latn-id",vbk:"vbk-latn-ph",ve:"ve-latn-za",vec:"vec-latn-it",vem:"vem-latn-ng",veo:"veo-latn-us",vep:"vep-latn-ru",ver:"ver-latn-ng",vgr:"vgr-arab-pk",vi:"vi-latn-vn",vic:"vic-latn-sx",vid:"vid-latn-tz",vif:"vif-latn-cg",vig:"vig-latn-bf",vil:"vil-latn-ar",vin:"vin-latn-tz",vit:"vit-latn-ng",viv:"viv-latn-zz",vka:"vka-latn-au",vkj:"vkj-latn-td",vkk:"vkk-latn-id",vkl:"vkl-latn-id",vkm:"vkm-latn-br",vkn:"vkn-latn-ng",vko:"vko-latn-id",vkp:"vkp-latn-in","vkp-deva":"vkp-deva-in",vkt:"vkt-latn-id",vku:"vku-latn-au",vkz:"vkz-latn-ng",vlp:"vlp-latn-vu",vls:"vls-latn-be",vma:"vma-latn-au",vmb:"vmb-latn-au",vmc:"vmc-latn-mx",vmd:"vmd-knda-in",vme:"vme-latn-id",vmf:"vmf-latn-de",vmg:"vmg-latn-pg",vmh:"vmh-arab-ir",vmi:"vmi-latn-au",vmj:"vmj-latn-mx",vmk:"vmk-latn-mz",vml:"vml-latn-au",vmm:"vmm-latn-mx",vmp:"vmp-latn-mx",vmq:"vmq-latn-mx",vmr:"vmr-latn-mz",vms:"vms-latn-id",vmu:"vmu-latn-au",vmw:"vmw-latn-mz",vmx:"vmx-latn-mx",vmy:"vmy-latn-mx",vmz:"vmz-latn-mx",vnk:"vnk-latn-sb",vnm:"vnm-latn-vu",vnp:"vnp-latn-vu",vo:"vo-latn-001",vor:"vor-latn-ng",vot:"vot-latn-ru",vra:"vra-latn-vu",vro:"vro-latn-ee",vrs:"vrs-latn-sb",vrt:"vrt-latn-vu",vto:"vto-latn-id",vum:"vum-latn-ga",vun:"vun-latn-tz",vut:"vut-latn-zz",vwa:"vwa-latn-cn","vwa-mymr":"vwa-mymr-cn",wa:"wa-latn-be",waa:"waa-latn-us",wab:"wab-latn-pg",wac:"wac-latn-us",wad:"wad-latn-id",wae:"wae-latn-ch",waf:"waf-latn-br",wag:"wag-latn-pg",wah:"wah-latn-id",wai:"wai-latn-id",waj:"waj-latn-zz",wal:"wal-ethi-et",wam:"wam-latn-us",wan:"wan-latn-zz",wap:"wap-latn-gy",waq:"waq-latn-au",war:"war-latn-ph",was:"was-latn-us",wat:"wat-latn-pg",wau:"wau-latn-br",wav:"wav-latn-ng",waw:"waw-latn-br",wax:"wax-latn-pg",way:"way-latn-sr",waz:"waz-latn-pg",wba:"wba-latn-ve",wbb:"wbb-latn-id",wbe:"wbe-latn-id",wbf:"wbf-latn-bf",wbh:"wbh-latn-tz",wbi:"wbi-latn-tz",wbj:"wbj-latn-tz",wbk:"wbk-arab-af",wbl:"wbl-latn-pk","wbl-arab":"wbl-arab-af","wbl-cyrl":"wbl-cyrl-tj",wbm:"wbm-latn-cn",wbp:"wbp-latn-au",wbq:"wbq-telu-in",wbr:"wbr-deva-in",wbt:"wbt-latn-au",wbv:"wbv-latn-au",wbw:"wbw-latn-id",wca:"wca-latn-br",wci:"wci-latn-zz",wdd:"wdd-latn-ga",wdg:"wdg-latn-pg",wdj:"wdj-latn-au",wdk:"wdk-latn-au",wdt:"wdt-latn-ca",wdu:"wdu-latn-au",wdy:"wdy-latn-au",wec:"wec-latn-ci",wed:"wed-latn-pg",weg:"weg-latn-au",weh:"weh-latn-cm",wei:"wei-latn-pg",wem:"wem-latn-bj",weo:"weo-latn-id",wep:"wep-latn-de",wer:"wer-latn-zz",wes:"wes-latn-cm",wet:"wet-latn-id",weu:"weu-latn-mm",wew:"wew-latn-id",wfg:"wfg-latn-id",wga:"wga-latn-au",wgb:"wgb-latn-pg",wgg:"wgg-latn-au",wgi:"wgi-latn-zz",wgo:"wgo-latn-id",wgu:"wgu-latn-au",wgy:"wgy-latn-au",wha:"wha-latn-id",whg:"whg-latn-zz",whk:"whk-latn-id",whu:"whu-latn-id",wib:"wib-latn-zz",wic:"wic-latn-us",wie:"wie-latn-au",wif:"wif-latn-au",wig:"wig-latn-au",wih:"wih-latn-au",wii:"wii-latn-pg",wij:"wij-latn-au",wik:"wik-latn-au",wil:"wil-latn-au",wim:"wim-latn-au",win:"win-latn-us",wir:"wir-latn-br",wiu:"wiu-latn-zz",wiv:"wiv-latn-zz",wiy:"wiy-latn-us",wja:"wja-latn-zz",wji:"wji-latn-zz",wka:"wka-latn-tz",wkd:"wkd-latn-id",wkr:"wkr-latn-au",wkw:"wkw-latn-au",wky:"wky-latn-au",wla:"wla-latn-pg",wlg:"wlg-latn-au",wlh:"wlh-latn-tl",wli:"wli-latn-id",wlm:"wlm-latn-gb",wlo:"wlo-arab-id",wlr:"wlr-latn-vu",wls:"wls-latn-wf",wlu:"wlu-latn-au",wlv:"wlv-latn-ar",wlw:"wlw-latn-id",wlx:"wlx-latn-gh",wma:"wma-latn-ng",wmb:"wmb-latn-au",wmc:"wmc-latn-pg",wmd:"wmd-latn-br",wme:"wme-deva-np",wmh:"wmh-latn-tl",wmi:"wmi-latn-au",wmm:"wmm-latn-id",wmn:"wmn-latn-nc",wmo:"wmo-latn-zz",wms:"wms-latn-id",wmt:"wmt-latn-au",wmw:"wmw-latn-mz","wmw-arab":"wmw-arab-mz",wmx:"wmx-latn-pg",wnb:"wnb-latn-pg",wnc:"wnc-latn-zz",wnd:"wnd-latn-au",wne:"wne-arab-pk",wng:"wng-latn-id",wni:"wni-arab-km",wnk:"wnk-latn-id",wnm:"wnm-latn-au",wnn:"wnn-latn-au",wno:"wno-latn-id",wnp:"wnp-latn-pg",wnu:"wnu-latn-zz",wnw:"wnw-latn-us",wny:"wny-latn-au",wo:"wo-latn-sn",woa:"woa-latn-au",wob:"wob-latn-zz",woc:"woc-latn-pg",wod:"wod-latn-id",woe:"woe-latn-fm",wof:"wof-latn-gm","wof-arab":"wof-arab-gm",wog:"wog-latn-pg",woi:"woi-latn-id",wok:"wok-latn-cm",wom:"wom-latn-ng",won:"won-latn-cd",woo:"woo-latn-id",wor:"wor-latn-id",wos:"wos-latn-zz",wow:"wow-latn-id",wpc:"wpc-latn-ve",wrb:"wrb-latn-au",wrg:"wrg-latn-au",wrh:"wrh-latn-au",wri:"wri-latn-au",wrk:"wrk-latn-au",wrl:"wrl-latn-au",wrm:"wrm-latn-au",wro:"wro-latn-au",wrp:"wrp-latn-id",wrr:"wrr-latn-au",wrs:"wrs-latn-zz",wru:"wru-latn-id",wrv:"wrv-latn-pg",wrw:"wrw-latn-au",wrx:"wrx-latn-id",wrz:"wrz-latn-au",wsa:"wsa-latn-id",wsg:"wsg-gong-in",wsi:"wsi-latn-vu",wsk:"wsk-latn-zz",wsr:"wsr-latn-pg",wss:"wss-latn-gh",wsu:"wsu-latn-br",wsv:"wsv-arab-af",wtf:"wtf-latn-pg",wth:"wth-latn-au",wti:"wti-latn-et",wtk:"wtk-latn-pg",wtm:"wtm-deva-in",wtw:"wtw-latn-id","wtw-bugi":"wtw-bugi-id",wua:"wua-latn-au",wub:"wub-latn-au",wud:"wud-latn-tg",wul:"wul-latn-id",wum:"wum-latn-ga",wun:"wun-latn-tz",wur:"wur-latn-au",wut:"wut-latn-pg",wuu:"wuu-hans-cn",wuv:"wuv-latn-zz",wux:"wux-latn-au",wuy:"wuy-latn-id",wwa:"wwa-latn-zz",wwb:"wwb-latn-au",wwo:"wwo-latn-vu",wwr:"wwr-latn-au",www:"www-latn-cm",wxw:"wxw-latn-au",wyb:"wyb-latn-au",wyi:"wyi-latn-au",wym:"wym-latn-pl",wyn:"wyn-latn-us",wyr:"wyr-latn-br",wyy:"wyy-latn-fj",xaa:"xaa-latn-es",xab:"xab-latn-ng",xai:"xai-latn-br",xaj:"xaj-latn-br",xak:"xak-latn-ve",xal:"xal-cyrl-ru",xam:"xam-latn-za",xan:"xan-ethi-et",xao:"xao-latn-vn",xar:"xar-latn-pg",xas:"xas-cyrl-ru",xat:"xat-latn-br",xau:"xau-latn-id",xav:"xav-latn-br",xaw:"xaw-latn-us",xay:"xay-latn-id",xbb:"xbb-latn-au",xbd:"xbd-latn-au",xbe:"xbe-latn-au",xbg:"xbg-latn-au",xbi:"xbi-latn-zz",xbj:"xbj-latn-au",xbm:"xbm-latn-fr",xbn:"xbn-latn-my",xbp:"xbp-latn-au",xbr:"xbr-latn-id",xbw:"xbw-latn-br",xby:"xby-latn-au",xch:"xch-latn-us",xco:"xco-chrs-uz",xcr:"xcr-cari-tr",xda:"xda-latn-au",xdk:"xdk-latn-au",xdo:"xdo-latn-ao",xdq:"xdq-cyrl-ru",xdy:"xdy-latn-id",xed:"xed-latn-cm",xeg:"xeg-latn-za",xem:"xem-latn-id",xer:"xer-latn-br",xes:"xes-latn-zz",xet:"xet-latn-br",xeu:"xeu-latn-pg",xgb:"xgb-latn-ci",xgd:"xgd-latn-au",xgg:"xgg-latn-au",xgi:"xgi-latn-au",xgm:"xgm-latn-au",xgu:"xgu-latn-au",xgw:"xgw-latn-au",xh:"xh-latn-za",xhe:"xhe-arab-pk",xhm:"xhm-khmr-kh",xhv:"xhv-latn-vn",xii:"xii-latn-za",xin:"xin-latn-gt",xir:"xir-latn-br",xis:"xis-orya-in",xiy:"xiy-latn-br",xjb:"xjb-latn-au",xjt:"xjt-latn-au",xka:"xka-arab-pk",xkb:"xkb-latn-bj",xkc:"xkc-arab-ir",xkd:"xkd-latn-id",xke:"xke-latn-id",xkg:"xkg-latn-ml",xkj:"xkj-arab-ir",xkl:"xkl-latn-id",xkn:"xkn-latn-id",xkp:"xkp-arab-ir",xkq:"xkq-latn-id",xkr:"xkr-latn-br",xks:"xks-latn-id",xkt:"xkt-latn-gh",xku:"xku-latn-cg",xkv:"xkv-latn-bw",xkw:"xkw-latn-id",xkx:"xkx-latn-pg",xky:"xky-latn-my",xkz:"xkz-latn-bt",xla:"xla-latn-zz",xlc:"xlc-lyci-tr",xld:"xld-lydi-tr",xly:"xly-elym-ir",xma:"xma-latn-so",xmb:"xmb-latn-cm",xmc:"xmc-latn-mz",xmd:"xmd-latn-cm",xmf:"xmf-geor-ge",xmg:"xmg-latn-cm",xmh:"xmh-latn-au",xmj:"xmj-latn-cm",xmm:"xmm-latn-id",xmn:"xmn-mani-cn",xmo:"xmo-latn-br",xmp:"xmp-latn-au",xmq:"xmq-latn-au",xmr:"xmr-merc-sd",xmt:"xmt-latn-id",xmu:"xmu-latn-au",xmv:"xmv-latn-mg",xmw:"xmw-latn-mg",xmx:"xmx-latn-id",xmy:"xmy-latn-au",xmz:"xmz-latn-id",xna:"xna-narb-sa",xnb:"xnb-latn-tw",xni:"xni-latn-au",xnj:"xnj-latn-tz",xnk:"xnk-latn-au",xnm:"xnm-latn-au",xnn:"xnn-latn-ph",xnq:"xnq-latn-mz",xnr:"xnr-deva-in",xnt:"xnt-latn-us",xnu:"xnu-latn-au",xny:"xny-latn-au",xnz:"xnz-latn-eg","xnz-arab":"xnz-arab-eg",xoc:"xoc-latn-ng",xod:"xod-latn-id",xog:"xog-latn-ug",xoi:"xoi-latn-pg",xok:"xok-latn-br",xom:"xom-latn-sd","xom-ethi":"xom-ethi-et",xon:"xon-latn-zz",xoo:"xoo-latn-br",xop:"xop-latn-pg",xor:"xor-latn-br",xow:"xow-latn-pg",xpa:"xpa-latn-au",xpb:"xpb-latn-au",xpd:"xpd-latn-au",xpf:"xpf-latn-au",xpg:"xpg-grek-tr",xph:"xph-latn-au",xpi:"xpi-ogam-gb",xpj:"xpj-latn-au",xpk:"xpk-latn-br",xpl:"xpl-latn-au",xpm:"xpm-cyrl-ru",xpn:"xpn-latn-br",xpo:"xpo-latn-mx",xpq:"xpq-latn-us",xpr:"xpr-prti-ir",xpt:"xpt-latn-au",xpv:"xpv-latn-au",xpw:"xpw-latn-au",xpx:"xpx-latn-au",xpz:"xpz-latn-au",xra:"xra-latn-br",xrb:"xrb-latn-zz",xrd:"xrd-latn-au",xre:"xre-latn-br",xrg:"xrg-latn-au",xri:"xri-latn-br",xrm:"xrm-cyrl-ru",xrn:"xrn-cyrl-ru",xrr:"xrr-latn-it",xru:"xru-latn-au",xrw:"xrw-latn-pg",xsa:"xsa-sarb-ye",xsb:"xsb-latn-ph",xse:"xse-latn-id",xsh:"xsh-latn-ng",xsi:"xsi-latn-zz",xsm:"xsm-latn-zz",xsn:"xsn-latn-ng",xsp:"xsp-latn-pg",xsq:"xsq-latn-mz",xsr:"xsr-deva-np",xss:"xss-cyrl-ru",xsu:"xsu-latn-ve",xsy:"xsy-latn-tw",xta:"xta-latn-mx",xtb:"xtb-latn-mx",xtc:"xtc-latn-sd",xtd:"xtd-latn-mx",xte:"xte-latn-id",xth:"xth-latn-au",xti:"xti-latn-mx",xtj:"xtj-latn-mx",xtl:"xtl-latn-mx",xtm:"xtm-latn-mx",xtn:"xtn-latn-mx",xtp:"xtp-latn-mx",xts:"xts-latn-mx",xtt:"xtt-latn-mx",xtu:"xtu-latn-mx",xtv:"xtv-latn-au",xtw:"xtw-latn-br",xty:"xty-latn-mx",xub:"xub-taml-in","xub-knda":"xub-knda-in","xub-mlym":"xub-mlym-in",xud:"xud-latn-au",xuj:"xuj-taml-in",xul:"xul-latn-au",xum:"xum-latn-it","xum-ital":"xum-ital-it",xun:"xun-latn-au",xuo:"xuo-latn-td",xut:"xut-latn-au",xuu:"xuu-latn-na",xve:"xve-ital-it",xvi:"xvi-arab-af",xvn:"xvn-latn-es",xvo:"xvo-latn-it",xvs:"xvs-latn-it",xwa:"xwa-latn-br",xwd:"xwd-latn-au",xwe:"xwe-latn-zz",xwj:"xwj-latn-au",xwk:"xwk-latn-au",xwl:"xwl-latn-bj",xwo:"xwo-cyrl-ru",xwr:"xwr-latn-id",xwt:"xwt-latn-au",xww:"xww-latn-au",xxb:"xxb-latn-gh",xxk:"xxk-latn-id",xxm:"xxm-latn-au",xxr:"xxr-latn-br",xxt:"xxt-latn-id",xya:"xya-latn-au",xyb:"xyb-latn-au",xyj:"xyj-latn-au",xyk:"xyk-latn-au",xyl:"xyl-latn-br",xyt:"xyt-latn-au",xyy:"xyy-latn-au",xzh:"xzh-marc-cn",xzp:"xzp-latn-mx",yaa:"yaa-latn-pe",yab:"yab-latn-br",yac:"yac-latn-id",yad:"yad-latn-pe",yae:"yae-latn-ve",yaf:"yaf-latn-cd",yag:"yag-latn-cl",yai:"yai-cyrl-tj",yaj:"yaj-latn-cf",yak:"yak-latn-us",yal:"yal-latn-gn","yal-arab":"yal-arab-gn",yam:"yam-latn-zz",yan:"yan-latn-ni",yao:"yao-latn-mz",yap:"yap-latn-fm",yaq:"yaq-latn-mx",yar:"yar-latn-ve",yas:"yas-latn-zz",yat:"yat-latn-zz",yau:"yau-latn-ve",yav:"yav-latn-cm",yaw:"yaw-latn-br",yax:"yax-latn-ao",yay:"yay-latn-zz",yaz:"yaz-latn-zz",yba:"yba-latn-zz",ybb:"ybb-latn-cm",ybe:"ybe-latn-cn","ybe-ougr":"ybe-ougr-cn",ybh:"ybh-deva-np",ybi:"ybi-deva-np",ybj:"ybj-latn-ng",ybl:"ybl-latn-ng",ybm:"ybm-latn-pg",ybn:"ybn-latn-br",ybo:"ybo-latn-pg",ybx:"ybx-latn-pg",yby:"yby-latn-zz",ycl:"ycl-latn-cn",ycn:"ycn-latn-co",yda:"yda-latn-au",yde:"yde-latn-pg",ydg:"ydg-arab-pk",ydk:"ydk-latn-pg",yea:"yea-mlym-in","yea-knda":"yea-knda-in",yec:"yec-latn-de",yee:"yee-latn-pg",yei:"yei-latn-cm",yej:"yej-grek-il",yel:"yel-latn-cd",yer:"yer-latn-zz",yes:"yes-latn-ng",yet:"yet-latn-id",yeu:"yeu-telu-in",yev:"yev-latn-pg",yey:"yey-latn-bw",yga:"yga-latn-au",ygi:"ygi-latn-au",ygl:"ygl-latn-pg",ygm:"ygm-latn-pg",ygp:"ygp-plrd-cn",ygr:"ygr-latn-zz",ygu:"ygu-latn-au",ygw:"ygw-latn-zz",yhd:"yhd-hebr-il",yi:"yi-hebr-001",yia:"yia-latn-au",yig:"yig-yiii-cn",yih:"yih-hebr-de",yii:"yii-latn-au",yij:"yij-latn-au",yil:"yil-latn-au",yim:"yim-latn-in",yir:"yir-latn-id",yis:"yis-latn-pg",yiv:"yiv-yiii-cn",yka:"yka-latn-ph","yka-arab":"yka-arab-ph",ykg:"ykg-cyrl-ru",yki:"yki-latn-id",ykk:"ykk-latn-pg",ykm:"ykm-latn-pg",yko:"yko-latn-zz",ykr:"ykr-latn-pg",yky:"yky-latn-cf",yla:"yla-latn-pg",ylb:"ylb-latn-pg",yle:"yle-latn-zz",ylg:"ylg-latn-zz",yli:"yli-latn-id",yll:"yll-latn-zz",ylr:"ylr-latn-au",ylu:"ylu-latn-pg",yly:"yly-latn-nc",ymb:"ymb-latn-pg",yme:"yme-latn-pe",ymg:"ymg-latn-cd",ymk:"ymk-latn-mz","ymk-arab":"ymk-arab-mz",yml:"yml-latn-zz",ymm:"ymm-latn-so",ymn:"ymn-latn-id",ymo:"ymo-latn-pg",ymp:"ymp-latn-pg",yna:"yna-plrd-cn",ynd:"ynd-latn-au",yng:"yng-latn-cd",ynk:"ynk-cyrl-ru",ynl:"ynl-latn-pg",ynq:"ynq-latn-ng",yns:"yns-latn-cd",ynu:"ynu-latn-co",yo:"yo-latn-ng",yob:"yob-latn-pg",yog:"yog-latn-ph",yoi:"yoi-jpan-jp",yok:"yok-latn-us",yol:"yol-latn-gb",yom:"yom-latn-cd",yon:"yon-latn-zz",yot:"yot-latn-ng",yoy:"yoy-thai-th",yra:"yra-latn-pg",yrb:"yrb-latn-zz",yre:"yre-latn-zz",yrk:"yrk-cyrl-ru",yrl:"yrl-latn-br",yrm:"yrm-latn-au",yro:"yro-latn-br",yrs:"yrs-latn-id",yrw:"yrw-latn-pg",yry:"yry-latn-au",ysd:"ysd-yiii-cn",ysn:"ysn-yiii-cn",ysp:"ysp-yiii-cn",ysr:"ysr-cyrl-ru",yss:"yss-latn-zz",ysy:"ysy-plrd-cn",ytw:"ytw-latn-pg",yty:"yty-latn-au",yua:"yua-latn-mx",yub:"yub-latn-au",yuc:"yuc-latn-us",yud:"yud-hebr-il",yue:"yue-hant-hk","yue-cn":"yue-hans-cn","yue-hans":"yue-hans-cn",yuf:"yuf-latn-us",yug:"yug-cyrl-ru",yui:"yui-latn-co",yuj:"yuj-latn-zz",yul:"yul-latn-cf",yum:"yum-latn-us",yun:"yun-latn-ng",yup:"yup-latn-co",yuq:"yuq-latn-bo",yur:"yur-latn-us",yut:"yut-latn-zz",yuw:"yuw-latn-zz",yux:"yux-cyrl-ru",yuz:"yuz-latn-bo",yva:"yva-latn-id",yvt:"yvt-latn-ve",ywa:"ywa-latn-pg",ywg:"ywg-latn-au",ywn:"ywn-latn-br",ywq:"ywq-plrd-cn","ywq-yiii":"ywq-yiii-cn",ywr:"ywr-latn-au",ywu:"ywu-plrd-cn","ywu-yiii":"ywu-yiii-cn",yww:"yww-latn-au",yxa:"yxa-latn-au",yxg:"yxg-latn-au",yxl:"yxl-latn-au",yxm:"yxm-latn-au",yxu:"yxu-latn-au",yxy:"yxy-latn-au",yyr:"yyr-latn-au",yyu:"yyu-latn-pg",za:"za-latn-cn",zaa:"zaa-latn-mx",zab:"zab-latn-mx",zac:"zac-latn-mx",zad:"zad-latn-mx",zae:"zae-latn-mx",zaf:"zaf-latn-mx",zag:"zag-latn-sd",zah:"zah-latn-ng",zaj:"zaj-latn-tz",zak:"zak-latn-tz",zam:"zam-latn-mx",zao:"zao-latn-mx",zap:"zap-latn-mx",zaq:"zaq-latn-mx",zar:"zar-latn-mx",zas:"zas-latn-mx",zat:"zat-latn-mx",zau:"zau-tibt-in","zau-arab":"zau-arab-in",zav:"zav-latn-mx",zaw:"zaw-latn-mx",zax:"zax-latn-mx",zay:"zay-latn-et","zay-ethi":"zay-ethi-et",zaz:"zaz-latn-ng",zba:"zba-arab-001",zbc:"zbc-latn-my",zbe:"zbe-latn-my",zbt:"zbt-latn-id",zbu:"zbu-latn-ng",zbw:"zbw-latn-my",zca:"zca-latn-mx",zch:"zch-hani-cn",zdj:"zdj-arab-km",zea:"zea-latn-nl",zeg:"zeg-latn-pg",zeh:"zeh-hani-cn",zen:"zen-tfng-mr","zen-arab":"zen-arab-mr",zga:"zga-latn-tz",zgb:"zgb-hani-cn",zgh:"zgh-tfng-ma",zgm:"zgm-hani-cn",zgn:"zgn-hani-cn",zgr:"zgr-latn-pg",zh:"zh-hans-cn","zh-au":"zh-hant-au","zh-bn":"zh-hant-bn","zh-bopo":"zh-bopo-tw","zh-gb":"zh-hant-gb","zh-gf":"zh-hant-gf","zh-hanb":"zh-hanb-tw","zh-hant":"zh-hant-tw","zh-hk":"zh-hant-hk","zh-id":"zh-hant-id","zh-mo":"zh-hant-mo","zh-pa":"zh-hant-pa","zh-pf":"zh-hant-pf","zh-ph":"zh-hant-ph","zh-sr":"zh-hant-sr","zh-th":"zh-hant-th","zh-tw":"zh-hant-tw","zh-us":"zh-hant-us","zh-vn":"zh-hant-vn",zhd:"zhd-hani-cn","zhd-latn":"zhd-latn-vn",zhi:"zhi-latn-ng",zhn:"zhn-latn-cn","zhn-hani":"zhn-hani-cn",zhw:"zhw-latn-cm",zhx:"zhx-nshu-cn",zia:"zia-latn-zz",zik:"zik-latn-pg",zil:"zil-latn-gn",zim:"zim-latn-td",zin:"zin-latn-tz",ziw:"ziw-latn-tz",ziz:"ziz-latn-ng",zka:"zka-latn-id",zkb:"zkb-cyrl-ru",zkd:"zkd-latn-mm",zko:"zko-cyrl-ru",zkp:"zkp-latn-br",zkt:"zkt-kits-cn",zku:"zku-latn-au",zkz:"zkz-cyrl-ru",zla:"zla-latn-cd",zlj:"zlj-hani-cn","zlj-latn":"zlj-latn-cn",zlm:"zlm-latn-tg",zln:"zln-hani-cn",zlq:"zlq-hani-cn",zma:"zma-latn-au",zmb:"zmb-latn-cd",zmc:"zmc-latn-au",zmd:"zmd-latn-au",zme:"zme-latn-au",zmf:"zmf-latn-cd",zmg:"zmg-latn-au",zmh:"zmh-latn-pg",zmi:"zmi-latn-my",zmj:"zmj-latn-au",zmk:"zmk-latn-au",zml:"zml-latn-au",zmm:"zmm-latn-au",zmn:"zmn-latn-ga",zmo:"zmo-latn-sd",zmp:"zmp-latn-cd",zmq:"zmq-latn-cd",zmr:"zmr-latn-au",zms:"zms-latn-cd",zmt:"zmt-latn-au",zmu:"zmu-latn-au",zmv:"zmv-latn-au",zmw:"zmw-latn-cd",zmx:"zmx-latn-cg",zmy:"zmy-latn-au",zmz:"zmz-latn-cd",zna:"zna-latn-td",zne:"zne-latn-zz",zng:"zng-latn-vn",znk:"znk-latn-au",zns:"zns-latn-ng",zoc:"zoc-latn-mx",zoh:"zoh-latn-mx",zom:"zom-latn-in",zoo:"zoo-latn-mx",zoq:"zoq-latn-mx",zor:"zor-latn-mx",zos:"zos-latn-mx",zpa:"zpa-latn-mx",zpb:"zpb-latn-mx",zpc:"zpc-latn-mx",zpd:"zpd-latn-mx",zpe:"zpe-latn-mx",zpf:"zpf-latn-mx",zpg:"zpg-latn-mx",zph:"zph-latn-mx",zpi:"zpi-latn-mx",zpj:"zpj-latn-mx",zpk:"zpk-latn-mx",zpl:"zpl-latn-mx",zpm:"zpm-latn-mx",zpn:"zpn-latn-mx",zpo:"zpo-latn-mx",zpp:"zpp-latn-mx",zpq:"zpq-latn-mx",zpr:"zpr-latn-mx",zps:"zps-latn-mx",zpt:"zpt-latn-mx",zpu:"zpu-latn-mx",zpv:"zpv-latn-mx",zpw:"zpw-latn-mx",zpx:"zpx-latn-mx",zpy:"zpy-latn-mx",zpz:"zpz-latn-mx",zqe:"zqe-hani-cn","zqe-latn":"zqe-latn-cn",zrn:"zrn-latn-td",zro:"zro-latn-ec",zrp:"zrp-hebr-fr",zrs:"zrs-latn-id",zsa:"zsa-latn-pg",zsr:"zsr-latn-mx",zsu:"zsu-latn-pg",zte:"zte-latn-mx",ztg:"ztg-latn-mx",ztl:"ztl-latn-mx",ztm:"ztm-latn-mx",ztn:"ztn-latn-mx",ztp:"ztp-latn-mx",ztq:"ztq-latn-mx",zts:"zts-latn-mx",ztt:"ztt-latn-mx",ztu:"ztu-latn-mx",ztx:"ztx-latn-mx",zty:"zty-latn-mx",zu:"zu-latn-za",zua:"zua-latn-ng",zuh:"zuh-latn-pg",zum:"zum-arab-om",zun:"zun-latn-us",zuy:"zuy-latn-cm",zyg:"zyg-hani-cn",zyj:"zyj-latn-cn","zyj-hani":"zyj-hani-cn",zyn:"zyn-hani-cn",zyp:"zyp-latn-mm",zza:"zza-latn-tr",zzj:"zzj-hani-cn"},Ka={}.hasOwnProperty;function Qa(t){const{language:n,script:a,region:e}=t;let r;var l,i;return a&&e&&(r=Wa[Oa({language:n,script:a,region:e})])?(t.script=void 0,t.region=void 0):a&&(r=Wa[Oa({language:n,script:a})])?t.script=void 0:e&&(r=Wa[Oa({language:n,region:e})])?t.region=void 0:n&&(r=Wa[n]),r&&(t.language=void 0,l=t,i=Fa(r),l.language||(l.language=i.language),l.script||(l.script=i.script),l.region||(l.region=i.region),i.variants&&l.variants.push(...i.variants)),Oa(t)}function $a(t,n,a){const e=Fa(n),r=Fa(a),l=[],i=e.language;let o;for(o in e)if(Ka.call(e,o)){const n=e[o];n&&Za(t,o,n)&&l.push(o)}for(o in r)if(Ka.call(r,o)){const n=r[o];i&&n&&(l.includes(o)||!t[o])&&Ja(t,o,"language"===o&&"und"===n?i:n)}}function Za(t,n,a){let e,r=!1;if(a){const l=t[n];if(e=l,Array.isArray(l)){e=[];let t=-1;for(;++t<l.length;){const n=l[t];a.includes(n)?r=!0:e.push(n)}}else l===a&&(e=null,r=!0);t[n]=e}return r}function Ja(t,n,a){const e=t[n];if(Array.isArray(e)){const t=Array.isArray(a)?a:[a];let n=-1;for(;++n<t.length;){const a=t[n];e.includes(a)||e.push(a)}}else t[n]=a}function te(t,n){return t.singleton>n.singleton?1:t.singleton<n.singleton?-1:0}var ne=class extends _a{constructor(){super(((t,n)=>{const a={[v.ADAPTATION_SET]:[v.LANG],[v.REPRESENTATION]:[v.LANG],[v.CONTENT_COMPONENT]:[v.LANG],[v.LABEL]:[v.LANG],[v.GROUP_LABEL]:[v.LANG]};if(a.hasOwnProperty(t)){let e=a[t];return void 0!==e&&e.indexOf(n)>=0}return!1}),(t=>function(t,n){const a=n||{},e=Fa(String(t||"").toLowerCase(),a),r=Oa(e);if(!r)return r;let l=-1;for(;++l<Va.length;){let t=Va[l].from;"und-"===t.slice(0,4)&&e.language&&(t=e.language+t.slice(3)),Ga(r,t).length>0&&$a(e,t,Va[l].to)}for(l=-1;++l<Ya.length;)Za(e,Ya[l].from.field,Ya[l].from.value)&&Ja(e,Ya[l].to.field,Ya[l].to.value);if(function(t){Qa(t);const{language:n,script:a,region:e}=t;if(!n)return t;const r=Oa({language:n,script:a,region:e});r===Qa(Fa(n))?(t.script=void 0,t.region=void 0):e&&r===Qa(Fa(n+"-"+e))?t.script=void 0:a&&r===Qa(Fa(n+"-"+a))&&(t.region=void 0)}(e),e.variants.sort(),e.extensions.sort(te),a.warning){let t;for(t in Xa)if(Ka.call(Xa,t)){const n=Xa[t],r=e[t];if(r&&Ka.call(n,r)){const e=n[r];a.warning("Deprecated "+t+" `"+r+"`, expected one of `"+e.join("`, `")+"`",-1,7)}}}return e.script&&(e.script=e.script.charAt(0).toUpperCase()+e.script.slice(1)),e.region&&(e.region=e.region.toUpperCase()),Oa(e)}(t)||String(t)))}},ae=class{constructor(t){var n;this._name=t,this._merge=(n=t)&&n.length&&n.charAt(0)===n.charAt(0).toUpperCase()}get name(){return this._name}get merge(){return this._merge}},ee=class{constructor(t,n,a){this._name=t||"",this._properties=[],this._children=a||[],Array.isArray(n)&&n.forEach((t=>{this._properties.push(new ae(t))}))}get name(){return this._name}get children(){return this._children}get properties(){return this._properties}},re=class extends ee{constructor(){const t=[v.PROFILES,v.WIDTH,v.HEIGHT,v.SAR,v.FRAMERATE,v.AUDIO_SAMPLING_RATE,v.MIME_TYPE,v.SEGMENT_PROFILES,v.CODECS,v.MAXIMUM_SAP_PERIOD,v.START_WITH_SAP,v.MAX_PLAYOUT_RATE,v.CODING_DEPENDENCY,v.SCAN_TYPE,v.FRAME_PACKING,v.AUDIO_CHANNEL_CONFIGURATION,v.CONTENT_PROTECTION,v.INBAND_EVENT_STREAM];super(v.ADAPTATION_SET,t,[new ee(v.REPRESENTATION,t,[new ee(v.SUB_REPRESENTATION,t)])])}},le=class extends ee{constructor(){const t=[v.SEGMENT_BASE,v.SEGMENT_TEMPLATE,v.SEGMENT_LIST];super(v.PERIOD,t,[new ee(v.ADAPTATION_SET,t,[new ee(v.REPRESENTATION,t)])])}};const ie={"&amp;":"&","&gt;":">","&lt;":"<","&quot;":'"',"&apos;":"'"};function oe(t,n){const a=n.split(/(&[#a-zA-Z0-9]+;)/);if(a.length<=1)return n;for(let n=1;n<a.length;n+=2){const e=a[n];if("#"===e.charAt(1)){let t;t="x"===e.charAt(2)?parseInt(e.substring(3,e.length-1),16):parseInt(e.substring(2,e.length-1),10),!isNaN(t)&&t>=0&&t<=1114111&&(a[n]=String.fromCodePoint(t))}else t.hasOwnProperty(e)&&(a[n]=t[e])}return a.join("")}function se(t,n){var a=(n=n||{}).pos||0,e=!!n.keepComments,r=!!n.keepWhitespace,l=n.attrMatchers||[],i=n.nodesAsArray||[],o="<",s="<".charCodeAt(0),u=">",c=">".charCodeAt(0),d="-".charCodeAt(0),m="/".charCodeAt(0),g="!".charCodeAt(0),f="'".charCodeAt(0),p='"'.charCodeAt(0),b="[".charCodeAt(0),h="]".charCodeAt(0);function v(n,l){for(var f=[];t[a];)if(t.charCodeAt(a)==s){if(t.charCodeAt(a+1)===m){var p=a+2;if(a=t.indexOf(u,a),-1==t.substring(p,a).indexOf(n)){var v=t.substring(0,a).split("\n");throw new Error("Unexpected close tag\nLine: "+(v.length-1)+"\nColumn: "+(v[v.length-1].length+1)+"\nChar: "+t[a])}return a+1&&(a+=1),f}if(t.charCodeAt(a+1)===g){if(t.charCodeAt(a+2)==d){const n=a;for(;-1!==a&&(t.charCodeAt(a)!==c||t.charCodeAt(a-1)!=d||t.charCodeAt(a-2)!=d||-1==a);)a=t.indexOf(u,a+1);-1===a&&(a=t.length),e&&f.push(t.substring(n,a+1))}else{if(t.charCodeAt(a+2)===b&&t.charCodeAt(a+8)===b&&"cdata"===t.substr(a+3,5).toLowerCase()){var y=t.indexOf("]]>",a);-1==y?(f.push(t.substr(a+9)),a=t.length):(f.push(t.substring(a+9,y)),a=y+3);continue}{const n=a+1;a+=2;for(var k=!1;(t.charCodeAt(a)!==c||!0===k)&&t[a];)t.charCodeAt(a)===b?k=!0:!0===k&&t.charCodeAt(a)===h&&(k=!1),a++;f.push(t.substring(n,a))}}a++;continue}var w=z();if(f.push(w),"?"===w.tagName[0]&&(f.push(...w.children),w.children=[]),l){let t=w.tagName;-1!==i.indexOf(t)?(l[t]||(l[t]=[]),l[t].push(w)):l[t]=w}}else{var E=(x=void 0,x=a,-2==(a=t.indexOf(o,a)-1)&&(a=t.length),oe(ie,t.slice(x,a+1)));r||(E=E.trim()),l?l.__text=E:f.push(E),a++}var x;return f}function y(t,n,a){if("S"===t)return parseInt(a);let e=oe(ie,a);return l.forEach((r=>{r.test(t,n,a)&&(e=r.converter(a))})),e}var k="\r\n\t>/= ";function w(){for(var n=a;-1===k.indexOf(t[a])&&t[a];)a++;return t.slice(n,a)}var E=n.noChildNodes||["img","br","input","meta","link","hr"];function z(){a++;const n=w();let e=[],r={tagName:n},l=r.tagName.indexOf(":");for(-1!==l&&(r.__prefix=r.tagName.substr(0,l),r.tagName=r.tagName.substr(l+1));t.charCodeAt(a)!==c&&t[a];){var i=t.charCodeAt(a);if(i>64&&i<91||i>96&&i<123){for(var o=w(),s=t.charCodeAt(a);s&&s!==f&&s!==p&&!(s>64&&s<91||s>96&&s<123)&&s!==c;)a++,s=t.charCodeAt(a);if(s===f||s===p){var u=(d=void 0,g=void 0,d=t[a],g=a+1,a=t.indexOf(d,g),t.slice(g,a));if(-1===a)return r}else u=null,a--;u=y(r.tagName,o,u),r[o]=u}a++}var d,g;if(t.charCodeAt(a-1)!==m)if("script"==n){var b=a+1;a=t.indexOf("<\/script>",a),e=[t.slice(b,a)],a+=9}else"style"==n?(b=a+1,a=t.indexOf("</style>",a),e=[t.slice(b,a)],a+=8):-1===E.indexOf(n)?(a++,e=v(n,r)):a++;else a++;return r.__children=e,r}var x,T=null;if(void 0!==n.attrValue)for(n.attrName=n.attrName||"id",T=[];-1!==(void 0,x=new RegExp("\\s"+n.attrName+"\\s*=['\"]"+n.attrValue+"['\"]").exec(t),a=x?x.index:-1);)-1!==(a=t.lastIndexOf("<",a))&&T.push(z()),t=t.substr(a),a=0;else T=n.parseNode?z():v("");return n.filter&&(T=ce(T,n.filter)),n.simplify?ue(Array.isArray(T)?T:[T]):(n.setPos&&(T.pos=a),T)}function ue(t){var n={};if(!t.length)return"";if(1===t.length&&"string"==typeof t[0])return t[0];for(var a in t.forEach((function(t){if("object"==typeof t){n[t.tagName]||(n[t.tagName]=[]);var a=ue(t.children);n[t.tagName].push(a),Object.keys(t.attributes).length&&"string"!=typeof a&&(a._attributes=t.attributes)}})),n)1==n[a].length&&(n[a]=n[a][0]);return n}function ce(t,n){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,e=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";var r=[];return t.forEach((function(t,l){if("object"==typeof t&&n(t,l,a,e)&&r.push(t),t.children){var i=ce(t.children,n,a+1,(e?e+".":"")+l+"."+t.tagName);r=r.concat(i)}})),r}const de=[v.PERIOD,v.BASE_URL,v.ADAPTATION_SET,v.REPRESENTATION,v.CONTENT_PROTECTION,v.ROLE,v.ACCESSIBILITY,v.AUDIO_CHANNEL_CONFIGURATION,v.CONTENT_COMPONENT,v.ESSENTIAL_PROPERTY,v.LABEL,v.S,v.SEGMENT_URL,v.EVENT,v.EVENT_STREAM,v.LOCATION,v.SERVICE_DESCRIPTION,v.SUPPLEMENTAL_PROPERTY,v.METRICS,v.REPORTING,v.PATCH_LOCATION,v.REPLACE,v.ADD,v.REMOVE,v.UTC_TIMING,v.INBAND_EVENT_STREAM,v.PRODUCER_REFERENCE_TIME,v.CONTENT_STEERING];function me(t){t=t||{};const n=this.context,a=t.debug;let e,r,l,i;function o(t){try{let n=se(t,{parseNode:!0,attrMatchers:l,nodesAsArray:de}),a={};if(-1!==n.tagName.toLowerCase().indexOf("xml"))for(let t in n){if(Array.isArray(n[t])){a[t]=n[t][0];break}if("object"==typeof n[t]){a[t]=n[t];break}}else a[n.tagName]=n,delete n.tagName;return a}catch(t){return null}}return e={getIron:function(){return i},parseXml:o,parse:function(t){let n;const a=window.performance.now();if(n=o(t),!n)throw new Error("failed to parse the manifest");n.Patch?(n=n.Patch,n.add&&n.add.forEach((t=>i.run(t))),n.replace&&n.replace.forEach((t=>i.run(t)))):(n=n.MPD,i.run(n));const e=window.performance.now();return r.info("Parsing complete: "+(e-a).toPrecision(3)+"ms"),n.protocol="DASH",n}},r=a.getLogger(e),l=[new Aa,new Ra,new ja,new ne],i=Ta(n).create({adaptationset:new re,period:new le}),e}me.__dashjs_factory_name="DashParser";var ge=i.getClassFactory(me);function fe(n){n=n||{};const a=this.context,e=n.manifestLoader,r=n.mediaPlayerModel,l=n.abrController,i=n.playbackController,o=n.adapter,s=n.dashMetrics,u=n.timelineConverter,d=n.offlineStoreController,m=n.id,g=n.eventBus,f=n.errHandler,p=n.events,b=n.errors,h=n.settings,v=n.debug,y=n.manifestUpdater,k=n.baseURLController,w=n.segmentBaseController,E=n.constants,z=n.dashConstants,x=n.urlUtils;let T,_,I,A,S,R,N,D,j,O,q,C,L,P;function M(){return D}function U(n){if(!j&&!n.error)try{N=n.manifest}catch(n){P=t.OFFLINE_STATUS_ERROR,f.error({code:c.OFFLINE_ERROR,message:n.message,data:{id:m,status:P}})}}function F(n){n.id===m&&(n.error||null===m?(P=t.OFFLINE_STATUS_ERROR,f.error({code:c.OFFLINE_ERROR,message:"Cannot start download ",data:{id:m,status:P,error:n.error}})):(P=t.OFFLINE_STATUS_STARTED,d.setDownloadingStatus(m,P).then((function(){g.trigger(p.OFFLINE_RECORD_STARTED,{id:m,message:"Downloading started for this stream !"})}))))}function B(t,n,a){C[t.getStreamInfo().id]={downloaded:n,available:a};let e,r=0,l=0;for(var i in C)C.hasOwnProperty(i)&&(null===C[i]?e=!0:(r+=C[i].downloaded,l+=C[i].available));e||(L=r/l,d.getManifestById(m).then((t=>(t.progress=L,X(t)))))}function G(n){n.id===m&&(n.error||null===m?(P=t.OFFLINE_STATUS_ERROR,f.error({code:c.OFFLINE_ERROR,message:"Error finishing download ",data:{id:m,status:P,error:n.error}})):(P=t.OFFLINE_STATUS_FINISHED,d.setDownloadingStatus(m,P).then((function(){g.trigger(p.OFFLINE_RECORD_FINISHED,{id:m,message:"Downloading has been successfully completed for this stream !"}),$()}))))}function H(t){t.id===m&&(O=t.representations,O.length>0&&q.parse(S,O).then((function(t){if(null===t||null===m)throw"falling parsing offline manifest";d.getManifestById(m).then((n=>(n.manifest=t,X(n)))).then((function(){for(let t=0,n=R.length;t<n;t++)R[t].startOfflineStreamProcessors()}))})).catch((function(t){throw t})))}function V(){try{o.updatePeriods(N),k.initialize(N);const n=o.getStreamsInfo();0===n.length&&(P=t.OFFLINE_STATUS_ERROR,f.error({code:c.OFFLINE_ERROR,message:"Cannot download - no streams",data:{id:m,status:P}}));for(let t=0,e=n.length;t<e;t++){const e=n[t];let c=wa(a).create({id:m,callbacks:{started:F,progression:B,finished:G,updateManifestNeeded:H},constants:E,dashConstants:z,eventBus:g,events:p,errors:b,settings:h,debug:v,errHandler:f,mediaPlayerModel:r,abrController:l,playbackController:i,dashMetrics:s,baseURLController:k,timelineConverter:u,adapter:o,segmentBaseController:w,offlineStoreController:d});R.push(c),c.initialize(e),C[e.id]=null}j=!0}catch(n){_.info(n),P=t.OFFLINE_STATUS_ERROR,f.error({code:c.OFFLINE_ERROR,message:n.message,data:{id:m,status:P,error:n.error}})}}function Y(t){return d.createFragmentStore(t)}function X(t){return d.updateOfflineManifest(t)}function W(n){return g.off(p.ORIGINAL_MANIFEST_LOADED,W,T),S=n.originalManifest,N.type===z.DYNAMIC?(P=t.OFFLINE_STATUS_ERROR,f.error({code:c.OFFLINE_ERROR,message:"Cannot handle DYNAMIC manifest",data:{id:m,status:P}}),void _.error("Cannot handle DYNAMIC manifest")):N.Period.length>1?(P=t.OFFLINE_STATUS_ERROR,f.error({code:c.OFFLINE_ERROR,message:"MultiPeriod manifest are not yet supported",data:{id:m,status:P}}),void _.error("MultiPeriod manifest are not yet supported")):(V(),R.forEach((t=>{t.getMediaInfos()})),void g.trigger(p.STREAMS_COMPOSED))}function K(t){for(let n=0;n<R.length;n++)R[n].initializeAllMediasInfoList(t)}function Q(){if(null!==m&&M()){for(let t=0,n=R.length;t<n;t++)R[t].stopOfflineStreamProcessors();R=[],j=!1,P=t.OFFLINE_STATUS_STOPPED,d.setDownloadingStatus(m,P).then((function(){g.trigger(p.OFFLINE_RECORD_STOPPED,{sender:this,id:m,status:P,message:"Downloading has been stopped for this stream !"}),D=!1}))}}function $(){for(let t=0,n=R.length;t<n;t++)R[t].reset();q=null,D=!1,R=[],g.off(p.MANIFEST_UPDATED,U,T),g.off(p.ORIGINAL_MANIFEST_LOADED,W,T),g.on(p.ERROR,Z,T)}function Z(t){t.error.code!==c.INDEXEDDB_QUOTA_EXCEED_ERROR&&t.error.code!==c.INDEXEDDB_INVALID_STATE_ERROR||Q()}return T={reset:function(){M()&&$(),k.reset(),y.reset()},getId:function(){return m},getOfflineUrl:function(){return A},getManifestUrl:function(){return I},getStatus:function(){return P},setInitialState:function(t){A=t.url,L=t.progress,I=t.originalUrl,P=t.status},initDownload:function(){e.load(I),D=!0},downloadFromUrl:function(n){return I=n,A=`${t.OFFLINE_SCHEME}://${m}`,P=t.OFFLINE_STATUS_CREATED,g.on(p.MANIFEST_UPDATED,U,T),g.on(p.ORIGINAL_MANIFEST_LOADED,W,T),g.on(p.ERROR,Z,T),a={fragmentStore:m,status:P,manifestId:m,url:A,originalURL:n},d.createOfflineManifest(a);var a},startDownload:function(n){try{let t=function(t){let n={};return n[E.VIDEO]=[],n[E.AUDIO]=[],n[E.TEXT]=[],t.forEach((t=>{t.bitrateList.forEach((a=>{n[t.type].push(a.id)}))})),n}(n);d.saveSelectedRepresentations(m,t).then((()=>Y(m))).then((()=>{return n=t,q=za(a).create({manifestId:m,allMediaInfos:n,debug:v,dashConstants:z,constants:E,urlUtils:x}),q.parse(S).then((function(t){return null!==t?d.getManifestById(m).then((n=>(n.originalURL=N.url,n.originalManifest=S,n.manifest=t,X(n)))):Promise.reject("falling parsing offline manifest")})).catch((function(t){return Promise.reject(t)}));var n})).then((function(){K(t)}))}catch(n){P=t.OFFLINE_STATUS_ERROR,f.error({code:c.OFFLINE_ERROR,message:n.message,data:{id:m,status:P}})}},stopDownload:Q,resumeDownload:function(){if(M())return;let t;D=!0,d.getManifestById(m).then((n=>{let e=ge(a).create({debug:v});return N=e.parse(n.originalManifest),V(),t=n.selected,g.trigger(p.STREAMS_COMPOSED),Y(m)})).then((()=>{K(t)}))},deleteDownload:function(){Q()},getDownloadProgression:function(){return Math.round(100*L)},isDownloading:M,resetDownload:$},_=v.getLogger(T),y.initialize(),R=[],D=!1,j=!1,C={},L=0,P=void 0,T}fe.__dashjs_factory_name="OfflineDownload";var pe=i.getClassFactory(fe);function be(t){t=t||{};const n=this.context,a=t.urlUtils,e=t.constants,r=t.dashConstants;let l,i;return i=s(n).getInstance(),l={load:function(t){if(t.request){let l=(n=t.request.url,a.removeHostname(n).split("/")[0]);if(l%1==0)if(t.request.mediaType===e.AUDIO||t.request.mediaType===e.VIDEO||t.request.mediaType===e.TEXT||t.request.mediaType===e.MUXED||t.request.mediaType===e.IMAGE){let n="InitializationSegment"===t.request.type?"init":t.request.index,a=t.request.representationId+"_"+n;i.getFragmentByKey(l,a).then((function(n){t.success(n,null,t.request.url,e.ARRAY_BUFFER)})).catch((function(n){t.error(n)}))}else t.request.type===r.MPD&&i.getManifestById(l).then((function(n){i.createFragmentStore(n.fragmentStore),t.success(n.manifest,null,t.request.url,e.XML)})).catch((function(n){t.error(t.request,404,n)}));else t.error(t.request,null,"MediaType can not be found")}var n},abort:function(){}},l}be.__dashjs_factory_name="IndexDBOfflineLoader";var he=dashjs.FactoryMaker.getClassFactory(be);function ve(){return{getRegex:function(){return t.OFFLINE_URL_REGEX},isRelative:function(){return!1},removeHostname:function(t){return t.replace(/(^\w+:|^)\/\//,"")},resolve:function(t,n){return"/"!==n.charAt(n.length-1)&&(n=n.concat("/")),n+t}}}ve.__dashjs_factory_name="OfflineUrlUtils";var ye=i.getSingletonFactory(ve),ke=new class extends b{constructor(){super(),this.OFFLINE_RECORD_LOADEDMETADATA="public_offlineRecordLoadedmetadata",this.OFFLINE_RECORD_STARTED="public_offlineRecordStarted",this.OFFLINE_RECORD_STOPPED="public_offlineRecordStopped",this.OFFLINE_RECORD_FINISHED="public_offlineRecordFinished"}},we=class{constructor(){this.id=null,this.url=null,this.originalUrl=null,this.status=null,this.progress=null}};function Ee(n){const a=this.context,e=n.errHandler,r=n.events,l=n.errors,i=n.settings,o=n.eventBus,s=n.debug,u=n.manifestLoader,c=n.manifestModel,d=n.mediaPlayerModel,g=n.abrController,f=n.playbackController,p=n.dashMetrics,b=n.timelineConverter,h=n.segmentBaseController,v=n.adapter,y=n.manifestUpdater,k=n.baseURLController,w=n.schemeLoaderFactory,E=n.constants,z=n.dashConstants,x=n.urlUtils;let T,_,I,A,S;function R(t){return _.find((n=>n.getId()===t))}function N(t){let n;return n=R(t),n||(n=pe(a).create({id:t,eventBus:o,events:r,errors:l,settings:i,manifestLoader:u,manifestModel:c,mediaPlayerModel:d,manifestUpdater:y,baseURLController:k,abrController:g,playbackController:f,adapter:v,dashMetrics:p,timelineConverter:b,errHandler:e,segmentBaseController:h,offlineStoreController:A,debug:s,constants:E,dashConstants:z,urlUtils:x}),_.push(n)),n}function D(t){return new Promise((function(n,a){let e=R(t),l=!1;if(e){if(e.isDownloading()){l=!0;const e=function(){return o.off(r.OFFLINE_RECORD_STOPPED,e,T),A.deleteDownloadById(t).then((function(){n()})).catch((function(t){a(t)}))};o.on(r.OFFLINE_RECORD_STOPPED,e,T)}e.deleteDownload();let i=_.indexOf(e);_.splice(i,1)}l||n()}))}function j(){_.forEach((t=>{t.resetDownload()}))}return T={loadRecordsFromStorage:function(){return new Promise((function(n,a){A.getAllManifests().then((a=>{a.manifests.forEach((n=>{!function(n){let a=R(n.manifestId);if(!a){a=N(n.manifestId);let e=n.status;e===t.OFFLINE_STATUS_STARTED&&(e=t.OFFLINE_STATUS_STOPPED),a.setInitialState({url:n.url,progress:n.progress,originalUrl:n.originalURL,status:e})}}(n)})),n()})).catch((t=>{I.error("Failed to load downloads "+t),a(t)}))}))},createRecord:function(t){return new Promise((function(n,a){let e=(new Date).getTime(),r=N(e);r.downloadFromUrl(t).then((()=>{r.initDownload(),n(e)})).catch((t=>{I.error("Failed to download "+t),D(e).then((function(){a(t)}))}))}))},startRecord:function(t,n){let a=R(t);a&&a.startDownload(n)},stopRecord:function(t){let n=R(t);n&&n.stopDownload()},resumeRecord:function(t){let n=R(t);n&&n.resumeDownload()},deleteRecord:function(t){return D(t).then((function(){return A.deleteDownloadById(t)}))},getRecordProgression:function(t){let n=R(t);return n?n.getDownloadProgression():0},getAllRecords:function(){let t=[];return _.forEach((n=>{const a=new we;a.id=n.getId(),a.progress=n.getDownloadProgression(),a.url=n.getOfflineUrl(),a.originalUrl=n.getManifestUrl(),a.status=n.getStatus(),t.push(a)})),t},resetRecords:j,reset:function(){j(),w.unregisterLoader(t.OFFLINE_SCHEME)}},I=s.getLogger(T),A=m(a).create({eventBus:n.eventBus,errHandler:e}),S=ye(a).getInstance(),x.registerUrlRegex(S.getRegex(),S),w.registerLoader(t.OFFLINE_SCHEME,he),_=[],T}Ee.__dashjs_factory_name="OfflineController";const ze=dashjs.FactoryMaker.getClassFactory(Ee);ze.events=ke,ze.errors=c,dashjs.FactoryMaker.updateClassFactory(Ee.__dashjs_factory_name,ze);var xe=ze,Te="undefined"!=typeof window&&window||global,_e=Te.dashjs;_e||(_e=Te.dashjs={}),_e.OfflineController=xe;var Ie=_e}(),e.default}()}));
//# sourceMappingURL=dash.offline.min.js.map